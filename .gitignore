cJenkinsfile
.idea/*
target/*
Dockerfile
application-dev.yml
application-gray.yml
application-iae.yml
application-prod.yml
application-test.yml
application-tw.yml

HELP.md
logs
target/
!.mvn/wrapper/maven-wrapper.jar
!**/src/main/**/target/
!**/src/test/**/target/
.mvn
mvnw.cmd

### STS ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache

### IntelliJ IDEA ###
.idea
*.iws
*.iml
*.ipr
mvnw

### NetBeans ###
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/
build/
!**/src/main/**/build/
!**/src/test/**/build/

### VS Code ###
.vscode/

*.log
*.md
claude-data/**
*.properties
rebel.xml
Test**
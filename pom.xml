<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.get</groupId>
    <artifactId>hti-java-ais</artifactId>
    <packaging>pom</packaging>
    <version>1.0.RELEASE</version>
    <modules>
        <module>gateway</module>
        <module>sys-service</module>
        <module>sys-service-ap</module>
        <module>biz-service</module>
        <module>biz-service-ap</module>
        <module>authentication</module>
        <module>common</module>
    </modules>

    <properties>
        <get.project.version>1.0.RELEASE</get.project.version>

        <java.version>1.8</java.version>
        <maven.plugin.version>3.8.1</maven.plugin.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

        <flowable.version>6.4.2</flowable.version>

        <spring.boot.version>2.3.12.RELEASE</spring.boot.version>
        <spring.cloud.version>Hoxton.SR11</spring.cloud.version>
        <spring.platform.version>Cairo-SR8</spring.platform.version>

        <!-- 生产环境中使用start-->
        <!-- docker 打包镜像上传配置-->
<!--        <docker-ip>************</docker-ip>-->
<!--        <registry-ip>**************</registry-ip>-->
<!--        <docker-url>http://${docker-ip}:2376</docker-url>-->
<!--        <registry-url>${registry-ip}:30121</registry-url>-->
<!--        <nexus-url>http://${registry-ip}:30125</nexus-url>-->
<!--        <nexus.version>0.0.1-SNAPSHOT</nexus.version>-->
<!--        <nexus.project_name>bms_prod</nexus.project_name>-->
<!--        <nexus.project_name>bms_dev</nexus.project_name>-->
        <docker-ip>************</docker-ip>
        <!--        <docker-url>http://${docker-ip}:2375</docker-url>-->
        <docker-url>http://************:2376</docker-url>
        <!--        <docker-url>http://************:2375</docker-url>-->
        <registry-url>${docker-ip}:5050</registry-url>
        <nexus-url>http://${docker-ip}:8282</nexus-url>
        <!-- 注意：上生产每次升级更新版本号-->
        <nexus.version>0.0.1-SNAPSHOT</nexus.version>
        <nexus.project_name>bms_dev</nexus.project_name>
        <!-- 生产环境中使用end-->

        <!-- 内网环境中使用start-->
        <!-- docker 打包镜像上传配置测试环境-->
        <!--        <docker-ip>************</docker-ip>-->
        <!--        <docker-url>http://${docker-ip}:2375</docker-url>-->
        <!--        <docker-url>http://************:2376</docker-url>-->
        <!--        <docker-url>http://************:2375</docker-url>-->
        <!--        <registry-url>${docker-ip}:5050</registry-url>-->
        <!--        <nexus-url>http://${docker-ip}:8081</nexus-url>-->
        <!-- 注意：上生产每次升级更新版本号-->
        <!--        <nexus.version>0.0.1-SNAPSHOT</nexus.version>-->
        <!--        <nexus.project_name>bms_gray</nexus.project_name>-->
        <!-- 内网环境中使用end-->
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.get.platform</groupId>
                <artifactId>core-pom</artifactId>
                <version>${get.project.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.get</groupId>
                <artifactId>common</artifactId>
                <version>${get.project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring.cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>io.spring.platform</groupId>
                <artifactId>platform-bom</artifactId>
                <version>${spring.platform.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
            <version>1.18.26</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.name}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring.boot.version}</version>
                    <configuration>
                        <fork>true</fork>
                        <finalName>${project.build.finalName}</finalName>
                    </configuration>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>

<!--                <plugin>-->
<!--                    <groupId>com.spotify</groupId>-->
<!--                    <artifactId>docker-maven-plugin</artifactId>-->
<!--                    <version>1.2.2</version>-->
<!--                    <configuration>-->
<!--                        &lt;!&ndash;配置 docker 的地址，当运行 docker build 命令时构建并上传镜像&ndash;&gt;-->
<!--                        <dockerHost>http://************:2375</dockerHost>-->
<!--                        &lt;!&ndash;配置最终生成的镜像名称&ndash;&gt;-->
<!--                        <imageName>************:5000/${project.artifactId}:${project.version}</imageName>-->
<!--                        &lt;!&ndash;配置 Dockerfile 所在的目录，basedir 表示在项目的根目录&ndash;&gt;-->
<!--                        <dockerDirectory>${basedir}/</dockerDirectory>-->
<!--                        <buildArgs>-->
<!--                            &lt;!&ndash;Maven 构建的 jar 包相对路径和名称&ndash;&gt;-->
<!--                            <JAR_FILE>target/${project.build.finalName}.jar</JAR_FILE>-->
<!--                        </buildArgs>-->
<!--                        <resources>-->
<!--                            <resource>-->
<!--                                <targetPath>/</targetPath>-->
<!--                                <directory>${project.build.directory}</directory>-->
<!--                                <include>${project.build.finalName}.jar</include>-->
<!--                            </resource>-->
<!--                        </resources>-->
<!--                    </configuration>-->
<!--                </plugin>-->


                                <plugin>
                                    <groupId>com.spotify</groupId>
                                    <artifactId>docker-maven-plugin</artifactId>
                                    <version>1.2.2</version>
                                    <executions>
                                        <execution>
                                            <id>build-image</id>
                                            <phase>package</phase>
                                            <goals>
                                                <goal>build</goal>
                                            </goals>
                                        </execution>
                                    </executions>
                                    <configuration>
                                        <!--打包docker镜像的docker服务器-->
                                        <dockerHost>${docker-url}</dockerHost>
                                        <!--镜像名，这里用工程名 -->
                                        <imageName>${registry-url}/${nexus.project_name}/${project.artifactId}:${nexus.version}</imageName>
                                        <!--nexus3 hosted 仓库地址-->
                                        <registryUrl>${registry-url}</registryUrl>
                                        <!-- ca认证正书-->
                                        <!--                    <dockerCertPath>./docker/cert-new</dockerCertPath>-->
                                        <!--TAG,这里用工程版本号-->
                                        <imageTags>
                                            <!-- 指定镜像标签,可以排至多个标签 -->
                                            <imageTag>${nexus.version}</imageTag>
                                            <imageTag>latest</imageTag>
                                        </imageTags>
                                        <!--是否强制覆盖已有镜像-->
                                        <forceTags>true</forceTags>
                                        <!--方式一：1、指定Dockerfile文件所在目录，通过文件执行打包上传nexus私服-->
                                        <dockerDirectory>${project.artifactId}/src/main/docker</dockerDirectory>
                                        <!-- 指定docker镜像打包参数，即dockerfile中使用的参数，通过${参数名}取值 -->
                                        <buildArgs>
                                            <JAR_FILE>${project.build.finalName}.jar</JAR_FILE>
                                            <JAR_FILE_NAME>${project.artifactId}.jar</JAR_FILE_NAME>
                                        </buildArgs>
                                        <resources>
                                            <resource>
                                                <targetPath>/</targetPath>
                                                <directory>${project.build.directory}</directory>
                                                <include>${project.build.finalName}.jar</include>
                                            </resource>
                                        </resources>
                                        <serverId>hti</serverId>
                                    </configuration>
                                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-antrun-plugin</artifactId>
                    <executions>
                        <execution>
                            <phase>package</phase>
                            <goals>
                                <goal>run</goal>
                            </goals>
                            <configuration>
                                <tasks>
                                    <!--suppress UnresolvedMavenProperty -->
                                    <copy overwrite="true"
                                          tofile="${session.executionRootDirectory}/target/${project.artifactId}.jar"
                                          file="${project.build.directory}/${project.artifactId}.jar" />
                                </tasks>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>dockerfile-maven-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven.plugin.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>UTF-8</encoding>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <!--        <repository>-->
        <!--            <id>spring-milestones</id>-->
        <!--            <name>Spring Milestones</name>-->
        <!--            <url>https://repo.spring.io/milestone</url>-->
        <!--            <snapshots>-->
        <!--                <enabled>false</enabled>-->
        <!--            </snapshots>-->
        <!--        </repository>-->
        <repository>
            <id>maven-public</id>
            <name>maven-public</name>
            <!--            在生产及灰度环境及分支中使用-->
            <!--            <url>http://172.19.0.3:30501/repository/maven-public/</url>-->
            <!--            在测试及开发环境及分支中使用-->
<!--            <url>http://**************:30125/repository/maven-public/</url>-->
            <url>http://************:8282/repository/maven-public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>aliyun-repos</id>
            <url>https://maven.aliyun.com/nexus/content/groups/public/</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>aliyun-plugin</id>
            <url>https://maven.aliyun.com/nexus/content/groups/public/</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

    <distributionManagement>
        <!--Release类型的托管资源库-->
        <repository>
            <!--id对应nexus仓库的id-->
            <id>hti</id>
            <!--自定义名称-->
            <name>Releases</name>
            <!--仓库对应的URL地址-->
            <url>http://************:8282/repository/maven-releases/</url>
        </repository>
        <!--Snapshot类型的托管资源库-->
        <snapshotRepository>
            <!--id对应nexus仓库的id-->
            <id>hti</id>
            <!--自定义名称-->
            <name>Snapshot</name>
            <!--仓库对应的URL地址-->
            <url>http://************:8282/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
</project>
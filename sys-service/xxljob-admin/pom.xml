<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>sys-service</artifactId>
        <groupId>com.get</groupId>
        <version>1.0.RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>xxljob-admin</artifactId>
    <name>${project.artifactId}</name>
    <version>${get.project.version}</version>
    <packaging>jar</packaging>


    <properties>
<!--        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>-->
<!--        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>-->
<!--        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>-->
<!--        <maven.compiler.source>1.8</maven.compiler.source>-->
<!--        <maven.compiler.target>1.8</maven.compiler.target>-->
<!--        <maven.test.skip>true</maven.test.skip>-->

<!--        <xxl-rpc.version>1.5.0</xxl-rpc.version>-->

<!--        <spring.version>4.3.25.RELEASE</spring.version>-->
<!--        <spring-boot.version>1.5.22.RELEASE</spring-boot.version>-->
<!--        <mybatis-spring-boot-starter.version>1.3.5</mybatis-spring-boot-starter.version>-->
<!--        <mysql-connector-java.version>5.1.48</mysql-connector-java.version>-->

<!--        <slf4j-api.version>1.7.29</slf4j-api.version>-->
<!--        <junit.version>4.12</junit.version>-->

<!--        <groovy.version>2.5.8</groovy.version>-->

<!--        <maven-source-plugin.version>3.2.0</maven-source-plugin.version>-->
<!--        <maven-javadoc-plugin.version>3.1.1</maven-javadoc-plugin.version>-->
<!--        <maven-gpg-plugin.version>1.6</maven-gpg-plugin.version>-->
<!--        <maven-war-plugin.version>3.2.3</maven-war-plugin.version>-->


        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <maven.test.skip>true</maven.test.skip>

        <netty-all.version>4.1.58.Final</netty-all.version>
        <gson.version>2.8.6</gson.version>

        <spring.version>5.3.3</spring.version>
        <spring-boot.version>2.4.2</spring-boot.version>

        <mybatis-spring-boot-starter.version>2.1.4</mybatis-spring-boot-starter.version>
        <mysql-connector-java.version>8.0.23</mysql-connector-java.version>

        <slf4j-api.version>1.7.30</slf4j-api.version>
        <junit.version>5.7.1</junit.version>
        <javax.annotation-api.version>1.3.2</javax.annotation-api.version>

        <groovy.version>3.0.7</groovy.version>

        <maven-source-plugin.version>3.2.1</maven-source-plugin.version>
        <maven-javadoc-plugin.version>3.2.0</maven-javadoc-plugin.version>
        <maven-gpg-plugin.version>1.6</maven-gpg-plugin.version>
        <maven-war-plugin.version>3.3.1</maven-war-plugin.version>
    </properties>


    <dependencies>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>common</artifactId>
            <version>${get.project.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.get</groupId>
                    <artifactId>core-secure</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>core-cloud</artifactId>
            <version>${get.project.version}</version>
        </dependency>
        <!-- freemarker-starter -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-freemarker</artifactId>
        </dependency>

        <!-- mail-starter -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>
        <!-- starter-actuator -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <!-- mybatis-starter：mybatis + mybatis-spring + hikari（default） -->
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>${mybatis-spring-boot-starter.version}</version>
        </dependency>
        <!-- mysql -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <!-- xxl-job-core -->
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
            <version>2.3.0</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.name}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring.boot.version}</version>
                    <configuration>
                        <fork>true</fork>
                        <finalName>${project.build.finalName}</finalName>
                    </configuration>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>com.spotify</groupId>
                    <artifactId>docker-maven-plugin</artifactId>
                    <version>1.2.2</version>
                    <executions>
                        <execution>
                            <id>build-image</id>
                            <phase>package</phase>
                            <goals>
                                <goal>build</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <!--打包docker镜像的docker服务器-->
                        <dockerHost>${docker-url}</dockerHost>
                        <!--镜像名，这里用工程名 -->
                        <imageName>${registry-url}/${nexus.project_name}/${project.artifactId}:${nexus.version}</imageName>
                        <!--nexus3 hosted 仓库地址-->
                        <registryUrl>${registry-url}</registryUrl>
                        <!-- ca认证正书-->
                        <!--                        <dockerCertPath>./docker/cert-new</dockerCertPath>-->
                        <!--TAG,这里用工程版本号-->
                        <imageTags>
                            <!-- 指定镜像标签,可以排至多个标签 -->
                            <imageTag>${nexus.version}</imageTag>
                            <imageTag>latest</imageTag>
                        </imageTags>
                        <!--是否强制覆盖已有镜像-->
                        <forceTags>true</forceTags>
                        <!--方式一：1、指定Dockerfile文件所在目录，通过文件执行打包上传nexus私服-->
                        <dockerDirectory>sys-service/${project.artifactId}/src/main/docker</dockerDirectory>
                        <!-- 指定docker镜像打包参数，即dockerfile中使用的参数，通过${参数名}取值 -->
                        <buildArgs>
                            <JAR_FILE>${project.build.finalName}.jar</JAR_FILE>
                            <JAR_FILE_NAME>${project.artifactId}.jar</JAR_FILE_NAME>
                        </buildArgs>
                        <resources>
                            <resource>
                                <targetPath>/</targetPath>
                                <directory>${project.build.directory}</directory>
                                <include>${project.build.finalName}.jar</include>
                            </resource>
                        </resources>
                        <serverId>nexus-docker-prod</serverId>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-antrun-plugin</artifactId>
                    <executions>
                        <execution>
                            <phase>package</phase>
                            <goals>
                                <goal>run</goal>
                            </goals>
                            <configuration>
                                <tasks>
                                    <!--suppress UnresolvedMavenProperty -->
                                    <copy overwrite="true"
                                          tofile="${session.executionRootDirectory}/target/${project.artifactId}.jar"
                                          file="${project.build.directory}/${project.artifactId}.jar" />
                                </tasks>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>dockerfile-maven-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven.plugin.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>UTF-8</encoding>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
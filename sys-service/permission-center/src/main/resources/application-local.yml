#服务器端口
server:
  port: 8086

#数据源配置
spring:
  datasource:
    url: ********************************************************************************************************************************
    username: root
    password: fzhmysql
  mail:
    host: smtp.exmail.qq.com
    port: 465
    username: <EMAIL>
    password: GZEtLhkf6WzpcPd9
    protocol: smtp
    default-encoding: utf-8
    properties:
      mail.smtp.auth: true
      mail.smtp.starttls.enable: true
      mail.smtp.starttls.required: true
      mail.smtp.ssl.enable: true
      mail.smtp.ssl.socketFactory.port: 465
      mail.smtp.ssl.socketFactory.class: javax.net.ssl.SSLSocketFactory

#系统之间交互认证id和密钥,支持的平台包括：get_issue、get_ib、get_mso、get_bms、get_mp_exam、get_mp_gea_annual、iae_vote，逗号分割
avatarKey:
  fkPlatformTypes: get_issue
  appId: get_2022#@!
  appSecret: 31889f86-69c2-429e-b665-5bd86c7cc58b


#脸部匹配URL
faceMatch:
  url: http://************:5024/face_match/


main:
  allow-circular-references: true
  allow-bean-definition-overriding: true

translation:
  ip: *************
  port: 7860
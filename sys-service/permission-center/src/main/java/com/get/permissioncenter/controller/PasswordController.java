package com.get.permissioncenter.controller;

import com.get.common.result.ResponseBo;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.permissioncenter.dto.ResetPasswordDto;
import com.get.permissioncenter.service.PasswordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @DATE: 2020/11/6
 * @TIME: 14:30
 * @Description: 评论管理
 **/
@Api(tags = "密码管理")
@RestController
@RequestMapping("permission/password")
public class PasswordController {
    @Resource
    private PasswordService passwordService;


    @ApiOperation(value = "重置密码", notes = "重置密码")
    @VerifyLogin(IsVerify = false)
    @VerifyPermission(IsVerify = false)
    @PostMapping("/resetPassword")
    public ResponseBo delete(@RequestBody ResetPasswordDto dto) {
        passwordService.resetPassword(dto);
        return new ResponseBo<>("重置成功");
    }

}

package com.get.permissioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.UpdateResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.permissioncenter.dto.CompanyIdsRequestDto;
import com.get.permissioncenter.dto.DepartmentDto;
import com.get.permissioncenter.service.IDepartmentService;
import com.get.permissioncenter.vo.CompanyVo;
import com.get.permissioncenter.vo.DepartmentVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * <AUTHOR>
 * @DATE: 2020/6/28
 * @TIME: 16:43
 **/


@Slf4j
@Api(tags = "部门管理")
@RestController
@RequestMapping("permission/department")
public class DepartmentController {

    @Resource
    private IDepartmentService departMentService;

    @ApiOperation(value = "列表数据", notes = "keyWord为关键词")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.LIST, description = "权限中心/部门管理/查询部门")
    @PostMapping("datas")
    public ResponseBo<DepartmentVo> datas(@RequestBody DepartmentDto searchBean) {
        log.info("执行查询公司列表");
        List<DepartmentVo> departments = departMentService.getDepartments(searchBean);
        return new ListResponseBo<>(departments);
    }

    /**
     * 详情
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "权限中心/部门管理/参数详情")
    @GetMapping("/{id}")
    public ResponseBo<DepartmentVo> detail(@PathVariable("id") Long id) {
        DepartmentVo departMentVo = departMentService.findDepartmentById(id);
        return new ResponseBo<>(departMentVo);
    }

    /**
     * 新增信息
     *
     * @param departMentDto
     * @return
     */
    @ApiOperation(value = "新增接口")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.ADD, description = "权限中心/部门管理/新增参数")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(DepartmentDto.Add.class) DepartmentDto departMentDto) {
        return SaveResponseBo.ok(departMentService.addDepartment(departMentDto));
    }

    /**
     * 批量新增信息
     *
     * @param departmentDtos
     * @return
     */
    @ApiOperation(value = "批量新增接口")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.ADD, description = "权限中心/部门管理/批量新增")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody @Validated(DepartmentDto.Add.class) ValidList<DepartmentDto> departmentDtos) {
        departMentService.batchAddDepartment(departmentDtos);
        return SaveResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param departMentDto
     * @return
     */
    @ApiOperation(value = "修改接口")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.EDIT, description = "权限中心/部门管理/更新参数")
    @PostMapping("update")
    public ResponseBo<DepartmentVo> update(@RequestBody @Validated(DepartmentDto.Update.class) DepartmentDto departMentDto) {
        return UpdateResponseBo.ok(departMentService.updateDepartment(departMentDto));
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "删除接口", notes = "id为删除数据的ID")
    @OperationLogger(module = LoggerModulesConsts.SYSTEMCENTER, type = LoggerOptTypeConst.DELETE, description = "权限中心/部门管理/删除资源")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        departMentService.delete(id);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 上移下移
     * @Param [departmentDtos]
     * <AUTHOR>
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "权限中心/部门管理/排序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<DepartmentDto> departmentDtos) {
        departMentService.movingOrder(departmentDtos);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 部门下拉框数据
     * @Param [id]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "部门下拉框数据", notes = "id公司id")
    @PostMapping("getDepartmentSelect")
    public ResponseBo<BaseSelectEntity> getDepartmentList(@RequestParam("id") Long companyId) {
        List<BaseSelectEntity> datas = departMentService.getDepartmentSelect(companyId);
        return new ListResponseBo<>(datas);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 批量获取部门下拉框数据
     * @Param [id]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "批量部门下拉框数据", notes = "id公司id")
    @PostMapping("batchObtainDepartmentList")
    public ResponseBo<BaseSelectEntity> batchObtainDepartmentList(@RequestBody CompanyIdsRequestDto companyIds) {
        List<BaseSelectEntity> datas = departMentService.batchObtainDepartmentList(companyIds);
        return new ListResponseBo<>(datas);
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 批量获取部门下拉框数据
     * @Param [id]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "多公司部门下拉框数据", notes = "回显公司下的部门使用")
    @GetMapping("multiCompanyDepartmentList")
    public ResponseBo<CompanyVo> batchObtainDepartmentList() {
        return new ResponseBo<>(departMentService.multiCompanyDepartmentList());
    }

    /**
     * @return java.util.List<java.lang.String>
     * @Description: 销售中心feign调用 部门编号对应的部门名称
     * @Param [departmentNumList]
     * <AUTHOR>
     */
    @ApiIgnore
    @PostMapping("getDepartmentNameList")
    public List<String> getDepartmentNameList(@RequestBody String[] departmentNumList) {
        return departMentService.getDepartmentNameList(departmentNumList);
    }

    /**
     * @return java.lang.String
     * @Description :feign调用 根据部门id查找部门名称
     * @Param [departmentId]
     * <AUTHOR>
     */
    @PostMapping("/getDepartmentNameById")
    public String getDepartmentNameById(@RequestParam(required = false, value = "departmentId") Long departmentId) {
        return departMentService.getDepartmentNameById(departmentId);
    }

    @ApiIgnore
    @PostMapping("getDepartmentNamesByIds")
    public Map<Long, String> getDepartmentNamesByIds(@RequestBody Set<Long> departmentIds) {
        return departMentService.getDepartmentNamesByIds(departmentIds);
    }

    @ApiIgnore
    @PostMapping("getDepartmentIdByNum")
    public Long getDepartmentIdByNum(@RequestParam("num") String num) {
        return departMentService.getDepartmentIdByNum(num);
    }

    @ApiIgnore
    @PostMapping("getDepartmentNumById")
    public String getDepartmentNumById(@RequestParam("id") Long id) {
        return departMentService.getDepartmentNumById(id);
    }
}

package com.get.permissioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.result.UpdateResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.permissioncenter.vo.StaffFollowerVo;
import com.get.permissioncenter.vo.StaffSuperiorVo;
import com.get.permissioncenter.vo.tree.StaffSuperiorTreeVo;
import com.get.permissioncenter.service.IStaffSuperiorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/7/28
 * @TIME: 16:43
 * @Description:
 **/
@Api(tags = "员工业务上司管理")
@RestController
@RequestMapping("permission/staffSuperior")
public class StaffSuperiorController {

    @Resource
    private IStaffSuperiorService superiorService;


    /**
     * 员工业务上司列表 根据用户id查询
     *
     * @param staffId
     * @return
     */
    @ApiOperation(value = "员工业务上司列表")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "权限中心/员工业务上司管理/员工业务上司")
    @GetMapping("/getStaffSuperior")
    public ResponseBo<StaffSuperiorVo> getStaffSuperior(@RequestParam(value = "staffId") Long staffId) {
        List<StaffSuperiorVo> staffSuperior = superiorService.getStaffSuperior(staffId);
        return new ListResponseBo<>(staffSuperior);
    }

    /**
     * 员工业务下属列表 根据用户id查询
     *
     * @param staffId
     * @return
     */
    @ApiOperation(value = "员工业务下属列表")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "权限中心/员工业务上司管理/员工业务下属列表")
    @GetMapping("/getStaffFollower")
    public ResponseBo<StaffFollowerVo> getStaffFollower(@RequestParam(value = "staffId") Long staffId,
                                                        @RequestParam(value = "keyWord", required = false) String keyWord) {
        List<StaffFollowerVo> staffFollower = superiorService.getStaffFollower(staffId, keyWord);
        return new ListResponseBo<>(staffFollower);
    }

    /**
     * 员工业务上司列表 根据用户id查询
     *
     * @param staffId
     * @return
     */
    @ApiOperation(value = "员工业务上司Tree")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "权限中心/员工业务上司管理/员工业务上司树")
    @GetMapping("/datas")
    public ResponseBo<StaffSuperiorTreeVo> detailList(@RequestParam(value = "staffId") Long staffId, @RequestParam(value = "keyWord", required = false) String keyWord) {
        List<StaffSuperiorTreeVo> staffSuperiorTree = superiorService.getStaffSuperiorTree(staffId);
        return new ListResponseBo<>(staffSuperiorTree);
    }

    /**
     * 修改员工上司
     *
     * @param staffId
     * @param superiorIds
     * @return
     */

    @ApiOperation(value = "修改接口")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.EDIT, description = "权限中心/员工业务上司管理/更新参数")
    @PostMapping("update")
    public ResponseBo update(@NotNull(message = "staffId不能为空") @RequestParam("staffId") Long staffId,
                             @RequestParam List<Long> superiorIds) {
        return UpdateResponseBo.ok(superiorService.updateStaffSuperior(staffId, superiorIds));
    }


    @ApiOperation(value = "删除接口")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.EDIT, description = "权限中心/员工业务上司管理/更新参数")
    @PostMapping("delete")
    public ResponseBo delete(@NotNull(message = "staffId不能为空") @RequestParam("staffId") Long staffId,
                             @RequestParam List<Long> superiorIds) {
        superiorService.deleteStaffSuperior(staffId, superiorIds);
        return UpdateResponseBo.ok();
    }

    @GetMapping(value = "getStaffFollowerIds")
    @ApiIgnore
    public List<Long> getStaffFollowerIds(@RequestParam(value = "staffId") Long staffId) {
        return superiorService.getStaffFollowerId(staffId);
    }

    @GetMapping(value = "getBusinessSubordinatesIds")
    @ApiIgnore
    public List<Long> getBusinessSubordinatesIds(@RequestParam(value = "staffId") Long staffId) {
        return superiorService.getBusinessSubordinatesIds(staffId);
    }

    /**
     * 当前登录用户业务下属下拉
     *
     * @return
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "当前登录用户业务下属下拉")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "权限中心/员工业务上司管理/当前登录用户业务下属下拉")
    @GetMapping("/getStaffFollowerSelect")
    public ResponseBo<BaseSelectEntity> getStaffFollowerSelect(@RequestParam(value = "companyId") Long companyId) {
        List<BaseSelectEntity> staffFollowerList = superiorService.getStaffFollowerSelect(companyId);
        return new ListResponseBo<>(staffFollowerList);
    }

    @ApiIgnore
    @GetMapping(value = "getStaffSuperiorByIds")
    public Map<Long, List<Long>> getStaffSuperiorByIds(@RequestBody Set<Long> staffIds) {
        return superiorService.getStaffSuperiorByIds(staffIds);
    }

}

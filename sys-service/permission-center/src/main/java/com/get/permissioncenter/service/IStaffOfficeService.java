package com.get.permissioncenter.service;


import com.get.core.mybatis.base.BaseService;
import com.get.permissioncenter.vo.StaffOfficeVo;
import com.get.permissioncenter.entity.StaffOffice;
import com.get.permissioncenter.dto.StaffOfficeBatchUpdateDto;
import com.get.permissioncenter.dto.StaffOfficeDto;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/7/24
 * @TIME: 15:24
 * @Description: 业务办公室
 **/
public interface IStaffOfficeService extends BaseService<StaffOffice> {

    List<StaffOfficeVo> getStaffOfficeById(Long fkCompanyId, Long staffId, String keyWord);


    Long updateStaffOffice(Long id, List<StaffOfficeDto> staffOfficeDtos);

    /**
     * 批量设置业务办公室
     *
     * @Date 12:41 2023/12/11
     * <AUTHOR>
     */
    void batchUpdateStaffOffice(StaffOfficeBatchUpdateDto staffOfficeBatchUpdateDto);
}

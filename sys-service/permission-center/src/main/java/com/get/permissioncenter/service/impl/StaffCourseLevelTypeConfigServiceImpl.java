package com.get.permissioncenter.service.impl;

import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.TableEnum;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.entity.MajorLevel;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.dao.StaffCourseLevelTypeConfigMapper;
import com.get.permissioncenter.vo.StaffCourseLevelTypeConfigVo;
import com.get.permissioncenter.entity.StaffCourseLevelTypeConfig;
import com.get.permissioncenter.service.StaffCourseLevelTypeConfigService;
import com.get.permissioncenter.dto.StaffCourseLevelTypeConfigDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-18
 */
@Service
public class StaffCourseLevelTypeConfigServiceImpl extends BaseServiceImpl<StaffCourseLevelTypeConfigMapper, StaffCourseLevelTypeConfig> implements StaffCourseLevelTypeConfigService {

    @Resource
    private StaffCourseLevelTypeConfigMapper staffCourseLevelTypeConfigMapper;

    @Resource
    private UtilService utilService;

    @Resource
    private IInstitutionCenterClient institutionCenterClient;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<StaffCourseLevelTypeConfigVo> getConfigs(Integer type){
        String fkTableName = null;
        if(type.equals(1)){
            fkTableName = TableEnum.INSTITUTION_MAJOR_LEVEL.key;
        }else if(type.equals(2)) {
            fkTableName = TableEnum.INSTITUTION_COURSE_TYPE_GROUP.key;
        }
        //查询员工配置
        LambdaQueryWrapper<StaffCourseLevelTypeConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StaffCourseLevelTypeConfig::getFkStaffId,SecureUtil.getStaffId());
        if(GeneralTool.isNotEmpty(fkTableName)){
            wrapper.eq(StaffCourseLevelTypeConfig::getFkTableName,fkTableName);
        }
        wrapper.orderByDesc(StaffCourseLevelTypeConfig::getViewOrder);
        List<StaffCourseLevelTypeConfig> staffCourseLevelTypeConfigs = staffCourseLevelTypeConfigMapper.selectList(wrapper);

        if(GeneralTool.isEmpty(staffCourseLevelTypeConfigs)){
            this.addDefaultConfig(SecureUtil.getStaffId(),fkTableName);
            getConfigs(type);
        }

        List<StaffCourseLevelTypeConfigVo> staffCourseLevelTypeConfigVos = BeanCopyUtils.copyListProperties(staffCourseLevelTypeConfigs, StaffCourseLevelTypeConfigVo::new);


        //课程等级
        if(type.equals(1)){
            Set<Long> majorLecelIds = new HashSet<>();
            staffCourseLevelTypeConfigVos.forEach(s->{
                String fkTableIds = s.getFkTableIds();
                if(GeneralTool.isNotEmpty(fkTableIds)){
                    String[] ids = fkTableIds.split(",");
                    for (String id : ids) {
                        majorLecelIds.add(Long.parseLong(id));
                    }
                }
            });
            Result<Map<Long, MajorLevel>> majorLevelResult = institutionCenterClient.getMajorLevelByIds(majorLecelIds);
            if(!majorLevelResult.isSuccess()){
                throw new GetServiceException(LocaleMessageUtils.getMessage("feign_execution_failed"));
            }
            Map<Long, MajorLevel> majorLevelMap = majorLevelResult.getData();
            staffCourseLevelTypeConfigVos.forEach(s->{
                String fkTableIds = s.getFkTableIds();
                if(GeneralTool.isNotEmpty(fkTableIds)){
                    String[] ids = fkTableIds.split(",");
                    List<String> nameList = new ArrayList<>();
                    for (String id : ids) {
                        MajorLevel majorLevel = majorLevelMap.get(Long.parseLong(id));
                        if(GeneralTool.isNotEmpty(majorLevel)){
                            nameList.add(majorLevel.getLevelNameChn());
                        }
                    }
                    s.setName(String.join("+", nameList));
                }
            });
        }

        //课程类型
        if(type.equals(2)){
            Set<Long> courseTypeGroupIds = new HashSet<>();
            staffCourseLevelTypeConfigVos.forEach(s->{
                String fkTableIds = s.getFkTableIds();
                if(GeneralTool.isNotEmpty(fkTableIds)){
                    String[] ids = fkTableIds.split(",");
                    for (String id : ids) {
                        courseTypeGroupIds.add(Long.parseLong(id));
                    }
                }
            });

            Result<Map<Long, String>> courseGroupTypeNameChnResult = institutionCenterClient.getCourseGroupTypeNameChnByIds(courseTypeGroupIds);
            if(!courseGroupTypeNameChnResult.isSuccess()){
                throw new GetServiceException(LocaleMessageUtils.getMessage("feign_execution_failed"));
            }
            Map<Long, String> courseGroupTypeNameMap = courseGroupTypeNameChnResult.getData();
            staffCourseLevelTypeConfigVos.forEach(s->{
                String fkTableIds = s.getFkTableIds();
                if(GeneralTool.isNotEmpty(fkTableIds)){
                    String[] ids = fkTableIds.split(",");
                    List<String> nameList = new ArrayList<>();
                    for (String id : ids) {
                        String name = courseGroupTypeNameMap.get(Long.parseLong(id));
                        if(GeneralTool.isNotEmpty(name)){
                            nameList.add(name);
                        }
                    }
                    s.setName(String.join("+", nameList));
                }
            });
        }
        return staffCourseLevelTypeConfigVos;
    }

    public void addDefaultConfig(Long fkStaffId,String fkTableName){
        List<StaffCourseLevelTypeConfig> defaultStaffCourseLevelTypeConfigs =  getDefaultConfig(fkTableName);
        //新增当前用户的默认配置
        Integer viewOrder = defaultStaffCourseLevelTypeConfigs.size()-1;
        for(StaffCourseLevelTypeConfig config : defaultStaffCourseLevelTypeConfigs){
            config.setId(null);
            config.setFkStaffId(fkStaffId);
            config.setIsChecked(true);
            config.setViewOrder(viewOrder);
            utilService.setCreateInfo(config);
            staffCourseLevelTypeConfigMapper.insert(config);
            viewOrder -- ;
        }
    }

    public List<StaffCourseLevelTypeConfig> getDefaultConfig(String fkTableName){
        //查询默认配置
        LambdaQueryWrapper<StaffCourseLevelTypeConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StaffCourseLevelTypeConfig::getFkStaffId, 0);
        if(GeneralTool.isNotEmpty(fkTableName)){
            wrapper.eq(StaffCourseLevelTypeConfig::getFkTableName, fkTableName);
        }
        List<StaffCourseLevelTypeConfig> defaultStaffCourseLevelTypeConfigs = staffCourseLevelTypeConfigMapper.selectList(wrapper);
        if(GeneralTool.isEmpty(defaultStaffCourseLevelTypeConfigs)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("default_config_not_found"));
        }
        return defaultStaffCourseLevelTypeConfigs;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long add(StaffCourseLevelTypeConfigDto staffCourseLevelTypeConfigDto){
        StaffCourseLevelTypeConfig config = BeanCopyUtils.objClone(staffCourseLevelTypeConfigDto, StaffCourseLevelTypeConfig::new);
        config.setFkStaffId(SecureUtil.getStaffId());
        if(staffCourseLevelTypeConfigDto.getType().equals(1)){
            config.setFkTableName(TableEnum.INSTITUTION_MAJOR_LEVEL.key);
        }
        if(staffCourseLevelTypeConfigDto.getType().equals(2)){
            config.setFkTableName(TableEnum.INSTITUTION_COURSE_TYPE_GROUP.key);
        }
        if(GeneralTool.isEmpty(staffCourseLevelTypeConfigDto.getIsChecked())){
            config.setIsChecked(true);
        }
        //获取最大排序
        StaffCourseLevelTypeConfig maxViewConfig = staffCourseLevelTypeConfigMapper.selectOne(Wrappers.<StaffCourseLevelTypeConfig>lambdaQuery()
                .eq(StaffCourseLevelTypeConfig::getFkStaffId, SecureUtil.getStaffId())
                .eq(StaffCourseLevelTypeConfig::getFkTableName, config.getFkTableName())
                .select(StaffCourseLevelTypeConfig::getViewOrder)
                .orderByDesc(StaffCourseLevelTypeConfig::getViewOrder).last("limit 1"));
        Integer viewOrder = GeneralTool.isEmpty(maxViewConfig) ? 0 : maxViewConfig.getViewOrder() + 1;
        config.setViewOrder(viewOrder);
        utilService.setCreateInfo(config);
        staffCourseLevelTypeConfigMapper.insert(config);
        return config.getId();
    }

    @Override
    public void delete(Long id){
        staffCourseLevelTypeConfigMapper.deleteById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetDefaultConfig(Integer type){
        String fkTableName = TableEnum.INSTITUTION_MAJOR_LEVEL.key;
        if(type.equals(2)){
            fkTableName = TableEnum.INSTITUTION_COURSE_TYPE_GROUP.key;
        }
        staffCourseLevelTypeConfigMapper.delete(Wrappers.<StaffCourseLevelTypeConfig>lambdaQuery()
                .eq(StaffCourseLevelTypeConfig::getFkStaffId,SecureUtil.getStaffId())
                .eq(StaffCourseLevelTypeConfig::getFkTableName,fkTableName));
        this.addDefaultConfig(SecureUtil.getStaffId(),fkTableName);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void movingOrder(Integer type,Integer start,Integer end){
        LambdaQueryWrapper<StaffCourseLevelTypeConfig> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if(type.equals(1)){
            lambdaQueryWrapper.eq(StaffCourseLevelTypeConfig::getFkTableName,TableEnum.INSTITUTION_MAJOR_LEVEL.key);
        }
        if(type.equals(2)){
            lambdaQueryWrapper.eq(StaffCourseLevelTypeConfig::getFkTableName,TableEnum.INSTITUTION_COURSE_TYPE_GROUP.key);
        }
        if (end > start){
            lambdaQueryWrapper.between(StaffCourseLevelTypeConfig::getViewOrder,start,end).orderByDesc(StaffCourseLevelTypeConfig::getViewOrder);
        }else {
            lambdaQueryWrapper.between(StaffCourseLevelTypeConfig::getViewOrder,end,start).orderByDesc(StaffCourseLevelTypeConfig::getViewOrder);

        }
        List<StaffCourseLevelTypeConfig> configs = list(lambdaQueryWrapper);

        //从下上移 列表倒序排序
        List<StaffCourseLevelTypeConfig> updateList = Lists.newArrayList();
        if (end > start){
            int finalEnd = end;
            List<StaffCourseLevelTypeConfig> sortedList = Lists.newArrayList();
            StaffCourseLevelTypeConfig policy = configs.get(configs.size() - 1);
            sortedList.add(policy);
            configs.remove(configs.size() - 1);
            sortedList.addAll(configs);
            for (StaffCourseLevelTypeConfig config : sortedList) {
                config.setViewOrder(finalEnd);
                finalEnd--;
            }
            updateList.addAll(sortedList);
        }else {
            int finalStart = start;
            List<StaffCourseLevelTypeConfig> sortedList = Lists.newArrayList();
            StaffCourseLevelTypeConfig policy = configs.get(0);
            configs.remove(0);
            sortedList.addAll(configs);
            sortedList.add(policy);
            for (StaffCourseLevelTypeConfig config : sortedList) {
                config.setViewOrder(finalStart);
                finalStart--;
            }
            updateList.addAll(sortedList);
        }

        if (GeneralTool.isNotEmpty(updateList)){
            boolean batch = updateBatchById(updateList);
            if (!batch){
                throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
            }
        }
    }

    @Override
    public void update(StaffCourseLevelTypeConfigDto staffCourseLevelTypeConfigDto){
        if(GeneralTool.isEmpty(staffCourseLevelTypeConfigDto)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
        StaffCourseLevelTypeConfig config = BeanCopyUtils.objClone(staffCourseLevelTypeConfigDto, StaffCourseLevelTypeConfig::new);
        utilService.setUpdateInfo(config);
        int i = staffCourseLevelTypeConfigMapper.updateById(config);
        if(i <= 0){
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
    }


    @Override
    public List<StaffCourseLevelTypeConfig> getStaffCourseLevelTypeConfig(Long fkStaffId,Integer type) {
        LambdaQueryWrapper<StaffCourseLevelTypeConfig> wrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isEmpty(fkStaffId)){
            return Collections.emptyList();
        }
        wrapper.eq(StaffCourseLevelTypeConfig::getFkStaffId, fkStaffId);
        String fkTableName = null;
        if(type.equals(1)){
            fkTableName = TableEnum.INSTITUTION_MAJOR_LEVEL.key;
        }else if(type.equals(2)) {
            fkTableName = TableEnum.INSTITUTION_COURSE_TYPE_GROUP.key;
        }
        if(GeneralTool.isNotEmpty(fkTableName)){
            wrapper.eq(StaffCourseLevelTypeConfig::getFkTableName, fkTableName);
        }
        wrapper.eq(StaffCourseLevelTypeConfig::getIsChecked, true);
        wrapper.orderByDesc(StaffCourseLevelTypeConfig::getViewOrder);
        List<StaffCourseLevelTypeConfig> staffCourseLevelTypeConfigs = staffCourseLevelTypeConfigMapper.selectList(wrapper);

        if(GeneralTool.isEmpty(staffCourseLevelTypeConfigs)){
            this.addDefaultConfig(fkStaffId,fkTableName);

            getStaffCourseLevelTypeConfig(fkStaffId,type);
        }
        return staffCourseLevelTypeConfigs;
    }
}

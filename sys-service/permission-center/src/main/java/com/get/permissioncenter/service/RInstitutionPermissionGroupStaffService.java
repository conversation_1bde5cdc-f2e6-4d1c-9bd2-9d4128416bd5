package com.get.permissioncenter.service;

import com.get.common.result.SearchBean;
import com.get.permissioncenter.dto.RInstitutionPermissionGroupStaffDto;
import com.get.permissioncenter.dto.query.StaffQueryDto;
import com.get.permissioncenter.vo.StaffVo;

import java.util.List;

/**
 * 学校权限组别与员工关联表
 */
public interface RInstitutionPermissionGroupStaffService {
    /**
     * 根据id删除
     * @param id
     */
    void removeById(Long id);

    /**
     * 批量新增学校权限和学校关系
     * @param rInstitutionPermissionGroupStaffDto
     * @return
     */
    List<Long> addRInstitutionPermissionGroupStaff(RInstitutionPermissionGroupStaffDto rInstitutionPermissionGroupStaffDto);

    List<StaffVo> dataList(SearchBean<StaffQueryDto> page);


//    /**
//     * 查询学校权限和学校信息关联信息
//     * @param institutionQueryDto
//     * @return
//     */
//    ResponseBo dataList(SearchBean<InstitutionQueryDto> institutionQueryDto);
}


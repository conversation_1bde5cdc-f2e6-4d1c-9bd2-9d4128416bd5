package com.get.permissioncenter.service.impl;

import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.dao.MInstitutionPermissionGroupMapper;
import com.get.permissioncenter.dao.RInstitutionPermissionGroupInstitutionMapper;
import com.get.permissioncenter.dao.RInstitutionPermissionGroupStaffMapper;
import com.get.permissioncenter.dto.MInstitutionPermissionGroupDto;
import com.get.permissioncenter.dto.MInstitutionPermissionGroupSearchDto;
import com.get.permissioncenter.entity.MInstitutionPermissionGroup;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.service.MInstitutionPermissionGroupService;
import com.get.permissioncenter.vo.MInstitutionPermissionGroupVo;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 学校权限组Service实现类
 */
@Service("permissionMInstitutionPermissionGroupService")
@Log4j2
public class MInstitutionPermissionGroupServiceImpl extends ServiceImpl<MInstitutionPermissionGroupMapper, MInstitutionPermissionGroup> implements MInstitutionPermissionGroupService {

    private final MInstitutionPermissionGroupMapper mInstitutionPermissionGroupMapper;
    private final RInstitutionPermissionGroupInstitutionMapper rInstitutionPermissionGroupInstitutionMapper;
    private final RInstitutionPermissionGroupStaffMapper rInstitutionPermissionGroupStaffMapper;
    @Resource
    private IPermissionCenterClient permissionCenterClient;

    private final UtilService utilService;

    public MInstitutionPermissionGroupServiceImpl(MInstitutionPermissionGroupMapper mInstitutionPermissionGroupMapper, RInstitutionPermissionGroupInstitutionMapper rInstitutionPermissionGroupInstitutionMapper, RInstitutionPermissionGroupStaffMapper rInstitutionPermissionGroupStaffMapper, UtilService utilService) {
        this.mInstitutionPermissionGroupMapper = mInstitutionPermissionGroupMapper;
        this.rInstitutionPermissionGroupInstitutionMapper = rInstitutionPermissionGroupInstitutionMapper;
        this.rInstitutionPermissionGroupStaffMapper = rInstitutionPermissionGroupStaffMapper;
        this.utilService = utilService;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addMInstitutionPermissionGroup(MInstitutionPermissionGroupDto mInstitutionPermissionGroupDto) {
        if (GeneralTool.isEmpty(mInstitutionPermissionGroupDto)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        if (GeneralTool.isEmpty(mInstitutionPermissionGroupDto.getFkCompanyId())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("company_id_null"));
        }
        if (GeneralTool.isEmpty(mInstitutionPermissionGroupDto.getGroupName())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("group_name_null"));
        }
        MInstitutionPermissionGroup mInstitutionPermissionGroup = BeanCopyUtils.objClone(mInstitutionPermissionGroupDto, MInstitutionPermissionGroup::new);
        utilService.setCreateInfo(mInstitutionPermissionGroup);
        Integer viewOrder = mInstitutionPermissionGroupMapper.selectViewOrder();
        mInstitutionPermissionGroup.setViewOrder(viewOrder + 1);
        int i = mInstitutionPermissionGroupMapper.insert(mInstitutionPermissionGroup);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }

        Long id = mInstitutionPermissionGroup.getId();
        String groupCode = String.format("IPG%04d", id);
        mInstitutionPermissionGroup.setGroupNum(groupCode);
        int updateRows = mInstitutionPermissionGroupMapper.updateById(mInstitutionPermissionGroup);
        if (updateRows <= 0) {
            log.error("更新组别编号失败，ID: {}", id);
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
        return mInstitutionPermissionGroup.getId();
    }

    /**
     * 删除
     * @param id
     */
    @Override
    @Transactional
    public void removeById(Long id) {
       if (GeneralTool.isEmpty(id)){
           throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
       }
       MInstitutionPermissionGroup mInstitutionPermissionGroup = mInstitutionPermissionGroupMapper.selectById(id);
       if (GeneralTool.isEmpty(mInstitutionPermissionGroup)){
           throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
       }
       Set<Long> rInstitutionPermissionGroupInstitutionIds = rInstitutionPermissionGroupInstitutionMapper.selectByFkInstitutionPermissionGroupId(id);
       Set<Long> rInstitutionPermissionGroupStaffIds = rInstitutionPermissionGroupStaffMapper.selectByFkInstitutionPermissionGroupId(id);
       if (GeneralTool.isNotEmpty(rInstitutionPermissionGroupInstitutionIds)){
           rInstitutionPermissionGroupInstitutionIds.forEach(rInstitutionPermissionGroupInstitutionMapper::deleteById);
       }
       if (GeneralTool.isNotEmpty(rInstitutionPermissionGroupStaffIds)){
           rInstitutionPermissionGroupStaffIds.forEach(rInstitutionPermissionGroupStaffMapper::deleteById);
       }
       mInstitutionPermissionGroupMapper.deleteById(id);

    }

    /**
     * 修改
     * @param mInstitutionPermissionGroupDto
     * @return
     */
    @Override
    public MInstitutionPermissionGroupVo updateById(MInstitutionPermissionGroupDto mInstitutionPermissionGroupDto) {
        if (GeneralTool.isEmpty(mInstitutionPermissionGroupDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if (GeneralTool.isEmpty(mInstitutionPermissionGroupDto.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        MInstitutionPermissionGroup searchById = mInstitutionPermissionGroupMapper.selectById(mInstitutionPermissionGroupDto.getId());
        if (GeneralTool.isEmpty(searchById)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        MInstitutionPermissionGroup mInstitutionPermissionGroup = BeanCopyUtils.objClone(mInstitutionPermissionGroupDto, MInstitutionPermissionGroup::new);
        utilService.updateUserInfoToEntity(mInstitutionPermissionGroup);
        mInstitutionPermissionGroupMapper.updateById(mInstitutionPermissionGroup);

        return findInstitutionPermissionGroupById(mInstitutionPermissionGroup.getId());
    }

    /**
     * 查询
     * @param mInstitutionPermissionGroupSearchDto
     * @param page
     * @return
     */
    @Override
    public List<MInstitutionPermissionGroupVo> getInstitutionPermissionGroups(MInstitutionPermissionGroupSearchDto mInstitutionPermissionGroupSearchDto, Page page) {
        if (GeneralTool.isEmpty(mInstitutionPermissionGroupSearchDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }

        LambdaQueryWrapper<MInstitutionPermissionGroup> wrapper = new LambdaQueryWrapper();
        IPage<MInstitutionPermissionGroup> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        //获取业务下属
        List<Long> staffFollowerIds = new ArrayList<>();
        Result<List<Long>> longResult = permissionCenterClient.getStaffFollowerIds(SecureUtil.getStaffId());
        if (longResult.isSuccess() && GeneralTool.isNotEmpty(longResult.getData())) {
            staffFollowerIds.addAll(longResult.getData());
        }
        if (GeneralTool.isEmpty(staffFollowerIds)) {
            staffFollowerIds = new ArrayList<>();
        }
        staffFollowerIds.add(SecureUtil.getStaffId());
        List<MInstitutionPermissionGroupVo> institutionPermissionGroupVos = mInstitutionPermissionGroupMapper.getInstitutionPermissionGroups(pages, mInstitutionPermissionGroupSearchDto, SecureUtil.getCompanyIds(), staffFollowerIds);
        page.setAll((int) pages.getTotal());
        return institutionPermissionGroupVos;


//        return Collections.emptyList();
    }

    @Override
    public void sortInstitutionPermissionGroup(List<MInstitutionPermissionGroupDto> mInstitutionPermissionGroupDtos) {
        if (GeneralTool.isEmpty(mInstitutionPermissionGroupDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        MInstitutionPermissionGroup moveDataOne = BeanCopyUtils.objClone(mInstitutionPermissionGroupDtos.get(0), MInstitutionPermissionGroup::new);
        MInstitutionPermissionGroup agentCommissionTypeMoveDataOne = mInstitutionPermissionGroupMapper.selectById(moveDataOne.getId());
        if (GeneralTool.isNotEmpty(agentCommissionTypeMoveDataOne) && !agentCommissionTypeMoveDataOne.getViewOrder().equals(moveDataOne.getViewOrder()) ){
            throw new GetServiceException(LocaleMessageUtils.getMessage("inconsistent_data"));
        }
        Integer oneViewOrder = moveDataOne.getViewOrder();
        MInstitutionPermissionGroup moveDataTwo = BeanCopyUtils.objClone(mInstitutionPermissionGroupDtos.get(1), MInstitutionPermissionGroup::new);
        MInstitutionPermissionGroup agentCommissionTypeMoveDataTwo = mInstitutionPermissionGroupMapper.selectById(moveDataTwo.getId());
        if (GeneralTool.isNotEmpty(agentCommissionTypeMoveDataTwo) && !agentCommissionTypeMoveDataTwo.getViewOrder().equals(moveDataTwo.getViewOrder()) ){
            throw new GetServiceException(LocaleMessageUtils.getMessage("inconsistent_data"));
        }
        Integer twoViewOrder = moveDataTwo.getViewOrder();
        moveDataOne.setViewOrder(twoViewOrder);
        utilService.updateUserInfoToEntity(moveDataOne);
        moveDataTwo.setViewOrder(oneViewOrder);
        utilService.updateUserInfoToEntity(moveDataTwo);
        mInstitutionPermissionGroupMapper.updateById(moveDataOne);
        mInstitutionPermissionGroupMapper.updateById(moveDataTwo);
    }

    /**
     * 权限中心/学校权限管理/拖拽
     * @param start
     * @param end
     */
    @Override
    public void movingOrder(Integer start, Integer end) {
        LambdaQueryWrapper<MInstitutionPermissionGroup> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (end > start){
            lambdaQueryWrapper.between(MInstitutionPermissionGroup::getViewOrder,start,end).orderByDesc(MInstitutionPermissionGroup::getViewOrder);
        }else {
            lambdaQueryWrapper.between(MInstitutionPermissionGroup::getViewOrder,end,start).orderByDesc(MInstitutionPermissionGroup::getViewOrder);

        }
        List<MInstitutionPermissionGroup> institutionPermissionGroups = list(lambdaQueryWrapper);

        //从下上移 列表倒序排序
        List<MInstitutionPermissionGroup> updateList = Lists.newArrayList();
        if (end > start){
            int finalEnd = end;
            List<MInstitutionPermissionGroup> sortedList = Lists.newArrayList();
            MInstitutionPermissionGroup policy = institutionPermissionGroups.get(institutionPermissionGroups.size() - 1);
            sortedList.add(policy);
            institutionPermissionGroups.remove(institutionPermissionGroups.size() - 1);
            sortedList.addAll(institutionPermissionGroups);
            for (MInstitutionPermissionGroup institutionPermissionGroup : sortedList) {
                institutionPermissionGroup.setViewOrder(finalEnd);
                finalEnd--;
            }
            updateList.addAll(sortedList);
        } else {
            int finalStart = start;
            List<MInstitutionPermissionGroup> sortedList = Lists.newArrayList();
            MInstitutionPermissionGroup policy = institutionPermissionGroups.get(0);
            institutionPermissionGroups.remove(0);
            sortedList.addAll(institutionPermissionGroups);
            sortedList.add(policy);
            for (MInstitutionPermissionGroup institutionPermissionGroup : sortedList) {
                institutionPermissionGroup.setViewOrder(finalStart);
                finalStart--;
            }
            updateList.addAll(sortedList);
        }

        if (GeneralTool.isNotEmpty(updateList)){
            boolean batch = updateBatchById(updateList);
            if (!batch){
                throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
            }
        }
    }


    public MInstitutionPermissionGroupVo findInstitutionPermissionGroupById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        MInstitutionPermissionGroup searchInstitutionPermissionGroupById = mInstitutionPermissionGroupMapper.selectById(id);
        if (GeneralTool.isEmpty(searchInstitutionPermissionGroupById)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        MInstitutionPermissionGroupVo mInstitutionPermissionGroupVo = BeanCopyUtils.objClone(searchInstitutionPermissionGroupById, MInstitutionPermissionGroupVo::new);
        return mInstitutionPermissionGroupVo;
    }
}


package com.get.permissioncenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseService;
import com.get.permissioncenter.dto.PositionDto;
import com.get.permissioncenter.vo.CompanyParallelVo;
import com.get.permissioncenter.vo.PositionVo;
import com.get.permissioncenter.entity.Position;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/6/29
 * @TIME: 16:32
 **/
public interface IPositionService extends BaseService<Position> {

    /**
     * 根据公司id 和部门id 查询所有职位
     *
     * @param companyId
     * @param departId
     * @return
     */
    List<PositionVo> getAllPosition(Long companyId, Long departId);


    /**
     * 获取所有公司以及部门、职位信息，无层级关系
     *
     * @return
     */
    List<CompanyParallelVo> getCompanyParallelDto(Boolean showStaff, Long companyId);

    /**
     * 详情
     *
     * @param id
     * @return
     */
    PositionVo findPositionById(Long id);

    Map<Long, String> getPositionNamesByIds(Set<Long> ids);


    /**
     * 批量新增
     *
     * @param positionDtoList
     * @return
     */
    void batchAddPosition(List<PositionDto> positionDtoList);


    /***
     * 修改
     * @param positionDto
     * @return
     *
     */
    PositionVo updatePositionVo(PositionDto positionDto);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 排序
     *
     * @param positionDtos
     */
    void movingOrder(List<PositionDto> positionDtos);


    /**
     * 所有职位，分页
     *
     * @param data
     * @param page
     * @return
     */
    List<PositionVo> getPositions(PositionDto data, Page page);


    /**
     * @return java.util.List<com.get.common.com.get.permissioncenter.vo.entity.BaseSelectEntity>
     * @Description: 下拉
     * @Param [companyId]
     * <AUTHOR>
     */
    List<BaseSelectEntity> getPositionSelect(Long companyId, Long departmentId);

    /**
     * feign调用 通过员工ids 查找对应的职位编号
     *
     * @param ids
     * @return
     */
    Map<Long, String> getPositionNumByIds(Set<Long> ids);

    /**
     * 获取职位下的员工
     *
     * @param ids
     * @return
     */
    Map<String, List<Long>> getPositionNumAndStaffIdMap(Set<Long> ids);
}

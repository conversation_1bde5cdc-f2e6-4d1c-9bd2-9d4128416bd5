package com.get.permissioncenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.permissioncenter.dao.StaffMapper;
import com.get.permissioncenter.dto.ResetPasswordDto;
import com.get.permissioncenter.entity.Staff;
import com.get.permissioncenter.service.PasswordService;
import com.get.permissioncenter.utils.MD5Utils;
import com.get.permissioncenter.vo.ResetPasswordTemplateVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.mail.internet.MimeMessage;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Random;

/**
 * @Author:Oliver
 * @Date: 2025/3/6  16:41
 * @Version 1.0
 */
@Service
@Slf4j
public class PasswordServiceImpl implements PasswordService {

    @Autowired
    private StaffMapper staffMapper;
    @Resource
    private JavaMailSender javaMailSender;
    @Value("${spring.mail.username:<EMAIL>}")
    private String from;

    @Override
    public void resetPassword(ResetPasswordDto passwordDto) {
        Staff staff = staffMapper.selectOne(new LambdaQueryWrapper<Staff>()
                .eq(Staff::getLoginId, passwordDto.getLoginId())
                .eq(Staff::getEmail, passwordDto.getEmail()));
        if (Objects.isNull(staff)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("user_information_does_not_exist"));
        }
        if (!staff.getIsActive()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("user_deactivated"));
        }
        ResetPasswordTemplateVo passwordTemplate = staffMapper.getResetPasswordTemplate();
        if (Objects.isNull(passwordTemplate)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("template_is_empty"));
        }
        String password = generateNumericPassword();
        Map replacements = new HashMap();
        replacements.put("account", passwordDto.getLoginId());
        replacements.put("password", password);
        String content = replaceTemplate(passwordTemplate.getTemplate(), replacements);
        Boolean sent = sendMail(passwordTemplate.getTitle(), content, staff.getEmail());
        if (sent) {
            staff.setLoginPs(MD5Utils.encrypt(password));
            staffMapper.updateById(staff);
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("news_email_send_fail"));
        }
    }

    public static String generateNumericPassword() {
        Random random = new Random();
        int password = 100000 + random.nextInt(900000);
        return String.valueOf(password);
    }

    private String replaceTemplate(String template, Map<String, String> replacements) {
        for (Map.Entry<String, String> entry : replacements.entrySet()) {
            template = template.replace("#{" + entry.getKey() + "}", entry.getValue());
        }
        return template;
    }


    private Boolean sendMail(String subject, String content, String toEmail) {
        try {
            MimeMessage mail = javaMailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(mail);

            helper.setTo(toEmail);
            helper.setSubject(subject);
            helper.setFrom(from);
            helper.setText(content, true);
            javaMailSender.send(mail);
            log.info("邮件发送成功===》邮件：{}", content);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("邮件发送出现异常：{}", e.getMessage());
            return false;
        }
    }
}

package com.get.permissioncenter.service;

import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseService;
import com.get.core.tool.api.Result;
import com.get.permissioncenter.dto.AddUserPreferencesAddDto;
import com.get.permissioncenter.dto.AssignBusinessSchoolDto;
import com.get.permissioncenter.dto.BusinessSchoolDto;
import com.get.permissioncenter.dto.CommentDto;
import com.get.permissioncenter.dto.MediaAndAttachedDto;
import com.get.permissioncenter.dto.StaffByIdsAndCompanyIdsDto;
import com.get.permissioncenter.dto.StaffDto;
import com.get.permissioncenter.dto.StaffEmailDto;
import com.get.permissioncenter.dto.StaffEmailPasswordUpdateDto;
import com.get.permissioncenter.dto.StaffInfoUpdateDto;
import com.get.permissioncenter.dto.StaffInstitutionDto;
import com.get.permissioncenter.dto.query.StaffQueryDto;
import com.get.permissioncenter.entity.Staff;
import com.get.permissioncenter.entity.StaffConfig;
import com.get.permissioncenter.vo.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.servlet.http.HttpServletResponse;

/**
 * @author: jack
 * @create: 2020/6/19
 * @verison: 1.0
 * @description: 员工业务接口
 */
public interface IStaffService extends BaseService<Staff> {

    /**
     * 获取所有员工
     *
     * @param staffVo
     * @param page
     * @return
     */
    List<StaffVo> getStaffDto(StaffQueryDto staffVo, Page page);

    /**
     * 员工列表导出
     * @param staffDto
     * @param response
     */
    void exportStaffList(StaffDto staffDto, HttpServletResponse response);

    /**
     * 根据CompanyID departID positionID 查询所有的员工
     *
     * @param companyId
     * @param departId
     * @param positionId
     * @return
     */
    List<StaffVo> getAllStaffDto(Long companyId, Long departId, Long positionId);


    /**
     * 批量新增
     *
     * @param staffDtoList
     * @return
     */

    List<Long> batchAddStaff(List<StaffDto> staffDtoList);

    /**
     * 详情
     *
     * @param id
     * @return
     */
    StaffVo findStaffById(Long id);

    /***
     * 修改
     * @param staffDto
     * @return
     *
     */
    StaffVo updateStaff(StaffDto staffDto);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);


    /**
     * 通过员工id 查找对应员工姓名
     *
     * @param id
     * @return
     */
    String getStaffNameById(Long id);

    List<Long> getReStaffIdByKey(String key,List<Long> staffIds);

    /**
     * 通过员工id 更新登录员工的基本信息（缓存）
     *
     * @param staffId
     * @return
     */
    Boolean updateLoginStaffInfoByStaffId(Long staffId);

    /**
     * @return java.lang.String
     * @Description :feign调用 通过员工ids 查找对应的员工姓名map
     * @Param [ids]
     * <AUTHOR>
     */
    Map<Long, String> getStaffNamesByIds(Set<Long> ids);

    /**
     * @return java.lang.String
     * @Description :feign调用 通过员工ids 获取员工登录账号
     * @Param [ids]
     * <AUTHOR>
     */
    Map<Long, String> getStaffByIdsAndCompanyIds(StaffByIdsAndCompanyIdsDto staffByIdsAndCompanyIdsDto);

    /**
     * 根据员工ids获取员工LoginIds
     *
     * @Date 18:31 2021/6/24
     * <AUTHOR>
     */
    Map<Long, String> getStaffLoginIdByIds(Set<Long> ids);

    /**
     * 保存上传的文件
     *
     * @param mediaAttachedVo
     * @return
     */
    List<MediaAndAttachedVo> addStaffMedia(List<MediaAndAttachedDto> mediaAttachedVo);

    /**
     * 员工下拉框数据
     *
     * @return
     */
    List<BaseSelectEntity> getStaffList(Long companyId, List<String> departNums);

    List<BaseSelectEntity> getOnDutyStaffList(Long companyId);


    /**
     * 员工登出
     *
     * @return
     */
    void loginOut();

    /**
     * 获取员工业务国家
     *
     * @param staffId
     * @return
     */
    List<String> getStaffCountry(Long staffId);


    /**
     * 获取员工业务办公室
     *
     * @param staffId
     * @return
     */
    List<String> getStaffOffice(Long staffId);


    /**
     * 查询员工附件
     *
     * @param data
     * @param page
     * @return
     */
    List<MediaAndAttachedVo> getStaffMedia(MediaAndAttachedDto data, Page page);

    /**
     * @return java.util.List<com.get.permissioncenter.com.get.permissioncenter.vo.StaffVo>
     * @Description: 获取所有员工 不分页
     * @Param [staffVo]
     * <AUTHOR>
     */
    List<StaffVo> getAllStaffDtoList(StaffQueryDto staffVo);

//    /**
//     * @return void
//     * @Description:更新用户sessionid
//     * @Param [id, sessionid]
//     * <AUTHOR>
//     **/
//    void updateStaffSessionId(Long id, Serializable sessionid);

    /**
     * @return java.lang.Long
     * @Description: 增加或者更新评论
     * @Param [commentDto]
     * <AUTHOR>
     **/
    Long editComment(CommentDto commentDto);

    /**
     * @return java.util.List<com.get.salecenter.com.get.permissioncenter.vo.CommentVo>
     * @Description: 获取所有评论
     * @Param [commentDto, page]
     * <AUTHOR>
     */
    List<CommentVo> getComments(CommentDto commentDto, Page page);

    /**
     * @return void
     * @Description: 修改密码
     * @Param [staffDto]
     * <AUTHOR>
     **/
    void updateStaffPwd(StaffDto staffDto);


    /**
     * 获取验证码
     * @param username
     * @return
     */
    ResponseBo getVerifyCode(String username);

    /**
     * 验证用户是否存在
     * @param username
     * @return
     */
    ResponseBo verifyUser(String username);
    /**
     * @return java.util.List<java.lang.Long>
     * @Description :feign调用 通过员工姓名关键字查找对应ids
     * @Param [staffNameKey]
     * <AUTHOR>
     */
    List<Long> getStaffIdsByNameKey(String staffNameKey);

    /**
     * feign调用 通过员工姓名或者英文名关键字查找对应ids
     *
     * @param staffNameKeyOrEnNameKey
     * @return
     */
    List<Long> getStaffIdsByNameKeyOrEnNameKey(String staffNameKeyOrEnNameKey);

    /**
     * feign调用 通过员工考勤号码匹配员工ID
     * @param attendanceNums
     * @return
     */
    Map<String,Long> getStaffIdsByAttendanceNum(Set<String> attendanceNums,Long fkCompanyId);

    /**
     * feign调用 通过员工中文以及英文名匹配员工ID
     * @param entities
     * @return
     */
    Map<String,BaseSelectEntity> getStaffIdsByNameAndEnName(List<BaseSelectEntity> entities);

    /**
     * @return void
     * @Description: 保存提交简历
     * @Param [staffNameKey]
     * <AUTHOR>
     **/
    void saveResume(String guid);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :feign调用 根据公司id查找该公司下的员工ids
     * @Param [companyId]
     * <AUTHOR>
     */
    List<Long> getStaffIdsByCompanyId(Long companyId);

    /**
     * @return void
     * @Description: 保存简历Guid
     * @Param [guid]
     * <AUTHOR>
     **/
    Boolean saveResumeGuid(String guid);

    /**
     * @Description :feign调用 通过员工ids查找对应公司名称map
     * @Param [staffIds]
     * <AUTHOR>
     */
    Map<Long, String> getCompanyNamesByStaffIds(Set<Long> staffIds);

    /**
     * @return void
     * @Description: 删除简历guid
     * @Param [guid]
     * <AUTHOR>
     **/
    boolean deleteGuid(String guid);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :feign调用 获取部门的所有员工ids
     * @Param [companyId, departmentId]
     * <AUTHOR>
     */
    List<Long> getAllDepartmentStaffIds(Long companyId, Long departmentId);

    /**
     * 根据部门id获取员工下拉框数据
     *
     * @Date 16:27 2021/7/6
     * <AUTHOR>
     */
    List<BaseSelectEntity> getStaffByDepartmentIds(List<Long> departmentIds);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :feign调用 获取部门最高职位员工ids
     * @Param [companyId, departmentId]
     * <AUTHOR>
     */
    List<Long> getTopPositionStaffIds(Long companyId, Long departmentId);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :获取职位的所有员工ids
     * @Param [companyId, positionIdList]
     * <AUTHOR>
     */
    List<Long> getAllPositionStaffIds(Long companyId, List<Long> positionIdList);

    /**
     * @ Description :根据职位编号获取人
     * @ Param []
     * @ return com.get.permissioncenter.com.get.permissioncenter.vo.StaffVo
     * @ author LEO
     */
    List<Long> getPositionByNum(List<String> num);

    /**
     * @ Description :fegin调用，根据员工id查询对应公司
     * @ Param [staffId]
     * @ return java.lang.Long
     * @ author LEO
     */
    StaffVo getCompanyIdByCreateUser(String createUser);


    /**
     * fegin调用，根据员工ids查询对应公司
     *
     * @param createUsers
     * @return
     */
    List<StaffVo> getCompanyIdByCreateUsers(Set<String> createUsers);

    /**
     * @ Description :fegin调用，根据员工id找出属于的部门
     * @ Param [StaffId]
     * @ return long
     * @ author LEO
     */
    StaffVo getCompanyIdByStaffId(Long staffId);

    /**
     * @return void
     * @Description :fegin调用，更新年假
     * @Param [id, annualLeaveBase]
     * <AUTHOR>
     */
    Boolean updateAnnualLeaveBase(Long id, BigDecimal annualLeaveBase);

    /**
     * @return void
     * @Description :fegin调用，更新补休
     * @Param [id, compensatoryLeaveBase]
     * <AUTHOR>
     */
    Boolean updateCompensatoryLeaveBase(Long id, BigDecimal compensatoryLeaveBase);

    /**
     * feign 调用根据员工ids获取对应的公司id
     *
     * @Date 10:24 2021/7/29
     * <AUTHOR>
     */
    Map<Long, Long> getCompanyIdByStaffIds(Set<Long> staffIds);

    /**
     * fei调用根据员工id获取员工下拉框数据
     *
     * @Date 14:58 2021/8/20
     * <AUTHOR>
     */
    List<BaseSelectEntity> getStaffByStaffIds(Set<Long> staffIds);

    Map<Long, String> getStaffNameMap(Set<Long> staffId);

    /**
     * 【部门】名字（离职）（英文名）
     * @param staffId
     * @return
     */
    Map<Long, String> getStaffNameDepartMentMap(Set<Long> staffId);

    /**
     * 根据员工id获取员工信息
     * @param staffId
     * @return
     */
    Map<Long, Staff> getStaffMapByStaffIds(Set<Long> staffId);

    List<Long> getStaffIdByName(String startStaffName);

    /**
     * 获取员工上司id
     *
     * @param staffId
     * @return
     */
    Long getStaffSupervisorIdByStaffId(Long staffId);

    /**
     * 员工基本信息修改接口
     *
     * @Date 16:07 2021/11/22
     * <AUTHOR>
     */
    void updateBasicStaffInfo(StaffInfoUpdateDto staffInfoUpdateDto);

    /**
     * 根据loginId获取员工信息
     *
     * @Date 12:04 2021/11/25
     * <AUTHOR>
     */
    Staff findStaffByLoginId(String loginId);

    /**
     * 登录使用：根据用户名和密码获取员工信息
     */
    StaffInfoVo staffInfo(String userName, String password, String isAvatarLogin, String avatarLogin);

    /**
     * 根据公司ID获取员工列表
     *
     * @param fkCompanyId
     * @return
     */
    List<Staff> getStaffs(Long fkCompanyId);

    /**
     * 根据公司ID获取员工DTO
     * @param fkCompanyId
     * @return
     */
    List<StaffVo> getStaffDtoByFkCompanyId(Long fkCompanyId);

    /**
     * 根据公司ID或者部门ID获取所有员工
     * @param fkCompanyId
     * @param fkDepartmentId
     * @return
     */
    List<StaffVo> getStaffDtos(Long fkCompanyId, Long fkDepartmentId, String staffNameKeyOrEnNameKey);

    /**
     * 获取员工所有的直属下属包括下属的下属的ids
     */
    List<Long> getAllSubordinateIds(Long staffId);

    List<StaffVo> getStaffByIds(Set<Long> staffIds);

    StaffVo getStaffById(Long staffId);

    /**
     * @Description: 获取当天生日的员工
     * @Author: Jerry
     * @Date:14:24 2021/12/1
     */
    void getDayOfStaffBirthday();

    /**
     * 根据职位编号positionNums获取员工ids
     *
     * @param positionNums
     * @return
     */
    List<Long> getStaffIdsByPositionNums(Set<String> positionNums);

    /**
     * 模糊查询员工Ids
     *
     * @param keyWord
     * @return
     * @
     */
    List<Long> getStaffIdsBykeyWord(String keyWord);

    /**
     * 根据公司Id获取员工下拉
     *
     * @param fkCompanyId
     * @return
     * @
     */
    List<BaseSelectEntity> getStaffByCompanyId(Long fkCompanyId);

    /**
     * 根据输入关键字模糊查询员工列表
     *
     * @Date 2:20 2022/03/11
     * <AUTHOR>
     */
    List<Long> getStaffListByStaffName(String staffName);

    /**
     * 所有ids
     *
     * @return
     */
    Set<Long> getAllStaffIds();

    /**
     * 获取员工在职状态
     * @param staffIds
     * @return
     */
    Result<Map<Long,Boolean>> doGetStaffIsOnDuty(Set<Long> staffIds);

    Map<Long, String> getStaffEnNameByIds(Set<Long> ids);

    Map<Long, String> getStaffChnNameByIds(Set<Long> ids);


    /**
     * feign调用获取员工和部门信息
     *
     * @param staffIds
     * @return
     */
    List<DepartmentAndStaffVo> getDepartmentAndStaffDtoByStaffIds(Set<Long> staffIds);

    /**
     * feign调用，根据员工Ids获取员工信息
     * <AUTHOR>
     * @DateTime 2022/12/6 14:56
     */
    List<StaffVo> getStaffDtoByIds(Set<Long> staffIds);

    /**
     * 获取配置
     * @param type
     * @return
     */
    StaffConfig getStaffConfigByType(String type);

    /**
     * 保存配置
     * @param type
     * @param keys
     */
    void saveStaffConfigByType(String type,List<String> keys);


    List<StaffListByDepartmentVo> getStaffListByDepartmentId(Long companyId, Long departmentId);

    List<BaseSelectEntity> getStaffByStaffName(List<Long> companyIds, String staffName);

    /**
     * 用户偏好设置保存(自定义列)
     *
     * @Date 16:37 2022/6/1
     * <AUTHOR>
     */
    void addCustomColumnJson(AddUserPreferencesAddDto addUserPreferencesAddDto);

    /**
     * 企业微信登录
     *
     * @param code
     * @return
     */
    StaffInfoVo wxCpLogin(String code, String platformType);

    /**
     * 新增员工邮箱
     *
     * @Date 17:14 2022/11/21
     * <AUTHOR>
     */
    void addStaffEmail(StaffEmailDto staffEmailDto);

    /**
     * 员工邮箱列表
     *
     * @Date 17:21 2022/11/21
     * <AUTHOR>
     */
    List<StaffEmailVo> staffEmailDatas(Long staffId);

    /**
     * 删除员工邮箱
     *
     * @Date 17:47 2022/11/21
     * <AUTHOR>
     */
    void staffEmailDelete(Long id);

    /**
     * 标记员工主邮箱
     *
     * @Date 17:50 2022/11/21
     * <AUTHOR>
     */
    void mainStaffEmailMark(Long id);

    /**
     * 编辑员工邮箱
     *
     * @Date 15:46 2022/11/22
     * <AUTHOR>
     */
    void staffEmailUpdate(StaffEmailDto staffEmailDto);

    /**
     * 根据部门编号获取员工
     * @param nums
     * @return
     */
    List<StaffVo> getStaffDtosByDepartmentNums(Long countryId, Set<String> nums);

    StaffVo getStaffByLoginId(String loginId);

    List<BaseSelectEntity> getStaffByDepartmentIdsAndCountryNum(List<Long> departmentIds, String fkCountryNum);

    /**
     * 模糊匹配名称
     * @param gmtCreateUser
     * @return
     */
    List<Long> getStaffIdsByLikeCondition(String gmtCreateUser);

    List<Staff> getStaffByLoginIds(List<String> staffGmtCreate);

    List<StaffVo> getAllSubordinateInfo();

    /**
     * 获取拥有resourceKey权限的所有用户ids
     * @param resourceKey
     * @param isContainAdmin
     * @return
     */
    List<Long> getStaffIdsByResourceKey(String resourceKey, Boolean isContainAdmin);

    /**
     * 员工email password update
     *
     * @Date 16:08 2023/12/19
     * <AUTHOR>
     */
    void updateStaffEmailPassword(StaffEmailPasswordUpdateDto staffEmailPasswordUpdateDto);

    void assignSchool(List<StaffInstitutionDto> staffInstitutionDtos);

    List<BusinessSchoolVo> getBusinessSchoolList(BusinessSchoolDto businessSchoolDto, Page page);

    void deleteBusinessSchool(Long staffId, Long id);

    List<AssignBusinessSchoolVo> getAssignBusinessSchoolList(AssignBusinessSchoolDto assignBusinessSchoolDto, Page page);

    Set<Long> getStaffDepartmentsById(Long staffId);
    LocalDate getBirthdayByIDCard(String idCard);

    Boolean getIsStaffBusiness(Long fkStaffId);

    /**
     * 根据登录ids，获取对应的员工DTO
     *
     * @param loginIds 登录ids
     * @return
     */
    Map<String, StaffVo> getStaffDtoMapByLoginIds(Set<String> loginIds);

    List<Long> getStaffIdsByCompanyIds(List<Long> companyIds);

    /**
     * 获取每个员工的所有上司（直到最顶级）id集合
     *
     * @param staffIds 员工ids
     * @return key：员工id，value：所有上司（直到最顶级）id集合
     */
    Map<Long, Set<Long>> getAllStaffSuperiorByStaffIds(Set<Long> staffIds);

    /**
     * 获取推送部门人员的邮箱
     * @param fkCountryId
     * @param fkInstitutionId
     * @param departmentList
     * @return
     */
    List<String> getPushDepartmentStaffEmail(Long fkCountryId, Long fkInstitutionId, List<String> departmentList);

    /**
     * 获取指定权限资源key的员工id集合
     *
     * @param resourceKey 权限资源key
     * @return
     */
    List<Long> getAuthorizedStaffIdsByResourceKey(String resourceKey);

    /**
     * 获取直属下属ids
     * @param staffId
     * @return
     */
    List<Long> getObtainDirectSubordinatesIds(Long staffId);

    Set<Long> getStaffSupervisorIds(Set<Long> ids);

    List<StaffVo> getStaffDatas(StaffQueryDto staffVo, Page page);

    CompanyVo batchObtainStaffList();

    List<Staff> getAllStaff();
}


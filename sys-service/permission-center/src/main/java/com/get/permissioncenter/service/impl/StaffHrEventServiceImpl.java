package com.get.permissioncenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.dao.StaffHrEventMapper;
import com.get.permissioncenter.dao.StaffMapper;
import com.get.permissioncenter.dto.MediaAndAttachedDto;
import com.get.permissioncenter.dto.StaffHrEventDto;
import com.get.permissioncenter.vo.*;
import com.get.permissioncenter.vo.CommentVo;
import com.get.permissioncenter.entity.Staff;
import com.get.permissioncenter.entity.StaffHrEvent;
import com.get.permissioncenter.service.ICommentService;
import com.get.permissioncenter.service.IDepartmentService;
import com.get.permissioncenter.service.IMediaAndAttachedService;
import com.get.permissioncenter.service.IOfficeService;
import com.get.permissioncenter.service.IPositionService;
import com.get.permissioncenter.service.IStaffHrEventService;
import com.get.permissioncenter.dto.CommentDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/7/13
 * @TIME: 10:27
 * @Description: 人事记录业务接口实现类
 **/
@Service
public class StaffHrEventServiceImpl extends BaseServiceImpl<StaffHrEventMapper, StaffHrEvent> implements IStaffHrEventService {
    @Resource
    private StaffHrEventMapper hrEventMapper;
    @Resource
    private IPositionService positionService;
    @Resource
    private IOfficeService officeService;
    @Resource
    private IDepartmentService departmentService;
    @Resource
    private UtilService utilService;
    @Resource
    private IMediaAndAttachedService attachedService;
    @Resource
    private ICommentService commentService;
    @Resource
    private StaffMapper staffMapper;


    @Override
    public StaffHrEventVo getStaffHrEventDtoById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
//        StaffHrEvent staffHrEvent = hrEventMapper.selectByPrimaryKey(id);
        StaffHrEvent staffHrEvent = hrEventMapper.selectById(id);
        StaffHrEventVo hrEventDto = BeanCopyUtils.objClone(staffHrEvent, StaffHrEventVo::new);
        if (GeneralTool.isEmpty(hrEventDto)) {
            return null;
        }
        setDepartmentName(hrEventDto);
        setPositionName(hrEventDto);
        setOfficeName(hrEventDto);
        if (GeneralTool.isNotEmpty(staffHrEvent.getFkStaffId())) {
            Staff staff = staffMapper.selectById(staffHrEvent.getFkStaffId());
            if (GeneralTool.isNotEmpty(staff)) {
                hrEventDto.setPassProbationDate(staff.getPassProbationDate());
            }
        }

        return hrEventDto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addStaffHrEvent(StaffHrEventDto staffHrEventDto) {
        if (GeneralTool.isEmpty(staffHrEventDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        StaffHrEvent staffHrEvent = BeanCopyUtils.objClone(staffHrEventDto, StaffHrEvent::new);
        if (staffHrEventDto.getEventType().contains("试用期延期")) {
            if (GeneralTool.isEmpty(staffHrEventDto.getPassProbationDate())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
            }
            Staff staff = staffMapper.selectById(staffHrEventDto.getFkStaffId());
            staff.setPassProbationDate(staffHrEventDto.getPassProbationDate());
            utilService.updateUserInfoToEntity(staff);
            staffMapper.updateById(staff);
        }
        utilService.updateUserInfoToEntity(staffHrEvent);
        int i = hrEventMapper.insertSelective(staffHrEvent);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
        Staff staff = staffMapper.selectById(staffHrEventDto.getFkStaffId());
        if (GeneralTool.isNotEmpty(staffHrEventDto.getFkOfficeIdTo())) {
            staff.setFkOfficeId(staffHrEventDto.getFkOfficeIdTo());
        }
        if (GeneralTool.isNotEmpty(staffHrEventDto.getFkDepartmentIdTo())) {
            staff.setFkDepartmentId(staffHrEventDto.getFkDepartmentIdTo());
        }
        if (GeneralTool.isNotEmpty(staffHrEventDto.getFkPositionIdTo())) {
            staff.setFkPositionId(staffHrEventDto.getFkPositionIdTo());
        }
        if (GeneralTool.isNotEmpty(staffHrEventDto.getToSalaryBase())) {
            staff.setSalaryBase(staffHrEventDto.getToSalaryBase());
        }
        if (GeneralTool.isNotEmpty(staffHrEventDto.getToSalaryPerformance())) {
            staff.setSalaryPerformance(staffHrEventDto.getToSalaryPerformance());
        }
        if (GeneralTool.isNotEmpty(staffHrEventDto.getToAllowancePosition())) {
            staff.setAllowancePosition(staffHrEventDto.getToAllowancePosition());
        }
        if (GeneralTool.isNotEmpty(staffHrEventDto.getToAllowanceCatering())) {
            staff.setAllowanceCatering(staffHrEventDto.getToAllowanceCatering());
        }
        if (GeneralTool.isNotEmpty(staffHrEventDto.getToAllowanceTransportation())) {
            staff.setAllowanceTransportation(staffHrEventDto.getToAllowanceTransportation());
        }
        if (GeneralTool.isNotEmpty(staffHrEventDto.getToAllowanceTelecom())) {
            staff.setAllowanceTelecom(staffHrEventDto.getToAllowanceTelecom());
        }
        if (GeneralTool.isNotEmpty(staffHrEventDto.getToAllowanceOther())) {
            staff.setAllowanceOther(staffHrEventDto.getToAllowanceOther());
        }
        if (GeneralTool.isNotEmpty(staffHrEventDto.getEffectiveDate())) {
            staff.setSalaryEffectiveDate(staffHrEventDto.getEffectiveDate());
        }
        utilService.updateUserInfoToEntity(staff);
        staffMapper.updateById(staff);
        return staffHrEvent.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public StaffHrEventVo updateStaffHrEventVo(StaffHrEventDto staffHrEventDto) {
        if (GeneralTool.isEmpty(staffHrEventDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        StaffHrEvent staffHrEvent = BeanCopyUtils.objClone(staffHrEventDto, StaffHrEvent::new);
        if (staffHrEventDto.getEventType().contains("试用期延期")) {
            if (GeneralTool.isEmpty(staffHrEventDto.getPassProbationDate())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
            }
            Staff staff = staffMapper.selectById(staffHrEventDto.getFkStaffId());
            staff.setPassProbationDate(staffHrEventDto.getPassProbationDate());
            utilService.updateUserInfoToEntity(staff);
            staffMapper.updateById(staff);
        }
//        utilService.setUpdateInfo(staffHrEvent);
//        int i = hrEventMapper.updateByPrimaryKeySelective(staffHrEvent);
        utilService.updateUserInfoToEntity(staffHrEvent);
        int i = hrEventMapper.updateByIdWithNull(staffHrEvent);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
        Staff staff = staffMapper.selectById(staffHrEventDto.getFkStaffId());
        if (GeneralTool.isNotEmpty(staffHrEventDto.getFkOfficeIdTo())) {
            staff.setFkOfficeId(staffHrEventDto.getFkOfficeIdTo());
        }
        if (GeneralTool.isNotEmpty(staffHrEventDto.getFkDepartmentIdTo())) {
            staff.setFkDepartmentId(staffHrEventDto.getFkDepartmentIdTo());
        }
        if (GeneralTool.isNotEmpty(staffHrEventDto.getFkPositionIdTo())) {
            staff.setFkPositionId(staffHrEventDto.getFkPositionIdTo());
        }
        if (GeneralTool.isNotEmpty(staffHrEventDto.getToSalaryBase())) {
            staff.setSalaryBase(staffHrEventDto.getToSalaryBase());
        }
        if (GeneralTool.isNotEmpty(staffHrEventDto.getToSalaryPerformance())) {
            staff.setSalaryPerformance(staffHrEventDto.getToSalaryPerformance());
        }
        if (GeneralTool.isNotEmpty(staffHrEventDto.getToAllowancePosition())) {
            staff.setAllowancePosition(staffHrEventDto.getToAllowancePosition());
        }
        if (GeneralTool.isNotEmpty(staffHrEventDto.getToAllowanceCatering())) {
            staff.setAllowanceCatering(staffHrEventDto.getToAllowanceCatering());
        }
        if (GeneralTool.isNotEmpty(staffHrEventDto.getToAllowanceTransportation())) {
            staff.setAllowanceTransportation(staffHrEventDto.getToAllowanceTransportation());
        }
        if (GeneralTool.isNotEmpty(staffHrEventDto.getToAllowanceTelecom())) {
            staff.setAllowanceTelecom(staffHrEventDto.getToAllowanceTelecom());
        }
        if (GeneralTool.isNotEmpty(staffHrEventDto.getToAllowanceOther())) {
            staff.setAllowanceOther(staffHrEventDto.getToAllowanceOther());
        }
        if (GeneralTool.isNotEmpty(staffHrEventDto.getEffectiveDate())) {
            staff.setSalaryEffectiveDate(staffHrEventDto.getEffectiveDate());
        }
        utilService.updateUserInfoToEntity(staff);
        staffMapper.updateById(staff);
        return getStaffHrEventDtoById(staffHrEvent.getId());
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
//        int i = hrEventMapper.deleteByPrimaryKey(id);
        int i = hrEventMapper.deleteById(id);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }

    @Override
    public List<MediaAndAttachedVo> addHrEventMedia(List<MediaAndAttachedDto> mediaAttachedVo) {
        if (GeneralTool.isEmpty(mediaAttachedVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        List<MediaAndAttachedVo> mediaAndAttachedVos = new ArrayList<>();
        for (MediaAndAttachedDto mediaAndAttachedDto : mediaAttachedVo) {
            //设置插入的表
            mediaAndAttachedDto.setFkTableName(TableEnum.PERMISSION_EVENT.key);
            mediaAndAttachedVos.add(attachedService.addMediaAndAttached(mediaAndAttachedDto));
        }
        return mediaAndAttachedVos;
    }

    @Override
    public List<MediaAndAttachedVo> getHrEventMedia(MediaAndAttachedDto attachedVo, Page page) {
        if (GeneralTool.isEmpty(attachedVo.getFkTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        attachedVo.setFkTableName(TableEnum.PERMISSION_EVENT.key);
        return attachedService.getMediaAndAttachedDto(attachedVo, page);
    }

    @Override
    public List<StaffHrEventVo> getStaffHrEventDto(StaffHrEventDto staffHrEventDto, Page page) {
        if (GeneralTool.isEmpty(staffHrEventDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        if (GeneralTool.isEmpty(staffHrEventDto.getFkStaffId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("staff_id_null"));
        }

//        Example example = new Example(StaffHrEvent.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkStaffId", staffHrEventDto.getFkStaffId());
//        if (GeneralTool.isNotEmpty(staffHrEventDto.getKeyWord())) {
//            criteria.andLike("event_type", "%" + staffHrEventDto.getKeyWord() + "%");
//        }
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        List<StaffHrEvent> staffHrEvents = hrEventMapper.selectByExample(example);
//        page.restPage(staffHrEvents);
//        List<StaffHrEventVo> collect =
//                staffHrEvents.stream().map(staffHrEvent -> Tools.objClone(staffHrEvent, StaffHrEventVo.class)).collect(Collectors.toList());
        LambdaQueryWrapper<StaffHrEvent> wrapper = new LambdaQueryWrapper();

        if (GeneralTool.isNotEmpty(staffHrEventDto.getFkStaffId())) {
            wrapper.eq(StaffHrEvent::getFkStaffId, staffHrEventDto.getFkStaffId());
        }
        if (GeneralTool.isNotEmpty(staffHrEventDto.getKeyWord())) {
            wrapper.and(wrapper_ ->
                    wrapper_.like(StaffHrEvent::getEventType, staffHrEventDto.getKeyWord()));
        }
        IPage<StaffHrEvent> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<StaffHrEvent> staffHrEvents = pages.getRecords();
        List<StaffHrEventVo> collect = BeanCopyUtils.copyListProperties(staffHrEvents, StaffHrEventVo::new);
        for (StaffHrEventVo staffHrEventVo : collect) {
            setDepartmentName(staffHrEventVo);
            setPositionName(staffHrEventVo);
            setOfficeName(staffHrEventVo);
        }
        return collect;
    }

    @Override
    public Long editComment(CommentDto commentDto) {
        if (GeneralTool.isNotEmpty(commentDto)) {
            if (GeneralTool.isNotEmpty(commentDto.getId())) {
                commentDto.setFkTableName(TableEnum.PERMISSION_EVENT.key);
                commentService.updateComment(commentDto);
            } else {
                commentDto.setFkTableName(TableEnum.PERMISSION_EVENT.key);
                commentService.addComment(commentDto);
            }
        }
        return commentDto.getId();
    }

    @Override
    public List<CommentVo> getComments(CommentDto commentDto, Page page) {
        commentDto.setFkTableName(TableEnum.PERMISSION_EVENT.key);
        return commentService.datas(commentDto, page);
    }

    private void setDepartmentName(StaffHrEventVo hrEventDto) {
        if (GeneralTool.isNotEmpty(hrEventDto.getFkDepartmentIdFrom())) {
            DepartmentVo departMentVo = departmentService.findDepartmentById(hrEventDto.getFkDepartmentIdFrom());
            if (GeneralTool.isNotEmpty(departMentVo)) {
                hrEventDto.setFromDepartmentName(departMentVo.getName());
            }
        }

        if (GeneralTool.isNotEmpty(hrEventDto.getFkDepartmentIdTo())) {
            DepartmentVo departMentVo = departmentService.findDepartmentById(hrEventDto.getFkDepartmentIdTo());
            if (GeneralTool.isNotEmpty(departMentVo)) {
                hrEventDto.setToDepartmentName(departMentVo.getName());
            }
        }
    }

    private void setPositionName(StaffHrEventVo hrEventDto) {
        if (GeneralTool.isNotEmpty(hrEventDto.getFkPositionIdFrom())) {
            PositionVo positionVo = positionService.findPositionById(hrEventDto.getFkPositionIdFrom());
            if (GeneralTool.isNotEmpty(positionVo)) {
                hrEventDto.setFromPositionName(positionVo.getName());
            }
        }
        if (GeneralTool.isNotEmpty(hrEventDto.getFkPositionIdTo())) {
            PositionVo positionVo = positionService.findPositionById(hrEventDto.getFkPositionIdTo());
            if (GeneralTool.isNotEmpty(positionVo)) {
                hrEventDto.setToPositionName(positionVo.getName());
            }
        }

    }

    private void setOfficeName(StaffHrEventVo hrEventDto) {
        if (GeneralTool.isNotEmpty(hrEventDto.getFkOfficeIdFrom())) {
            OfficeVo officeVo = officeService.findOfficeById(hrEventDto.getFkOfficeIdFrom());
            if (GeneralTool.isNotEmpty(officeVo)) {
                hrEventDto.setFromOfficeName(officeVo.getName());
            }
        }
        if (GeneralTool.isNotEmpty(hrEventDto.getFkOfficeIdTo())) {
            OfficeVo officeVo = officeService.findOfficeById(hrEventDto.getFkOfficeIdTo());
            if (GeneralTool.isNotEmpty(officeVo)) {
                hrEventDto.setToOfficeName(officeVo.getName());
            }
        }
    }

}

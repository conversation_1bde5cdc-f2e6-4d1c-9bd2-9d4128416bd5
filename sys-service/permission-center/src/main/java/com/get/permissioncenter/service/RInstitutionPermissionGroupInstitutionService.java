package com.get.permissioncenter.service;

import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.institutioncenter.dto.query.InstitutionQueryDto;
import com.get.permissioncenter.dto.RInstitutionPermissionGroupInstitutionDto;

import java.util.List;

/**
 * 学校权限和学校关系表
 */
public interface RInstitutionPermissionGroupInstitutionService{
    /**
     * 根据id删除
     * @param id
     */
    void removeById(Long id);

    /**
     * 批量新增学校权限和学校关系
     * @param rInstitutionPermissionGroupInstitutionDto
     * @return
     */
    List<Long> addMInstitutionPermissionGroupAndInstitution(RInstitutionPermissionGroupInstitutionDto rInstitutionPermissionGroupInstitutionDto);

    /**
     * 查询学校权限和学校信息关联信息
     * @param institutionQueryDto
     * @return
     */
    ResponseBo dataList(SearchBean<InstitutionQueryDto> institutionQueryDto);
}


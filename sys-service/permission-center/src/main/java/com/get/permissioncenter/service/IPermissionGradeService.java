package com.get.permissioncenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseService;
import com.get.permissioncenter.dto.PermissionGradeDto;
import com.get.permissioncenter.vo.PermissionGradeVo;
import com.get.permissioncenter.entity.PermissionGrade;

import java.util.List;

/**
 * @author: jack
 * @create: 2020/7/2
 * @verison: 1.0
 * @description: 权限等级管理业务接口
 */
public interface IPermissionGradeService extends BaseService<PermissionGrade> {
    /**
     * 详情
     *
     * @param id
     * @return
     */
    PermissionGradeVo findPermissionGradeById(Long id);

    /**
     * 列表数据
     *
     * @param permissionGradeDto
     * @param page
     * @return
     */
    List<PermissionGradeVo> getPermissionGrades(PermissionGradeDto permissionGradeDto, Page page);

    /**
     * 修改
     *
     * @param permissionGradeDto
     * @return
     */
    PermissionGradeVo updatePermissionGrade(PermissionGradeDto permissionGradeDto);

    /**
     * 保存
     *
     * @param permissionGradeDto
     * @return
     */
    Long addPermissionGrade(PermissionGradeDto permissionGradeDto);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 批量保存
     *
     * @param permissionGradeDtos
     * @return
     */
    void batchAdd(List<PermissionGradeDto> permissionGradeDtos);

    /**
     * 上移下移
     *
     * @param permissionGradeDtos
     * @return
     */
    void movingOrder(List<PermissionGradeDto> permissionGradeDtos);
}

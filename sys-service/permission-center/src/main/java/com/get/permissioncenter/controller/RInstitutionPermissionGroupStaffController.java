package com.get.permissioncenter.controller;


import com.baomidou.mybatisplus.extension.api.ApiController;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.permissioncenter.dto.RInstitutionPermissionGroupStaffDto;
import com.get.permissioncenter.dto.query.StaffQueryDto;
import com.get.permissioncenter.service.RInstitutionPermissionGroupStaffService;
import com.get.permissioncenter.vo.StaffVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 学校权限组人员表(RInstitutionPermissionGroupStaff)表控制层
 */
@RestController
@RequestMapping("permission/getInstitutionPermissionGroupStaff")
public class RInstitutionPermissionGroupStaffController extends ApiController {
    /**
     * 服务对象
     */
    @Resource
    private RInstitutionPermissionGroupStaffService rInstitutionPermissionGroupStaffService;


    @ApiOperation(value = "查询接口", notes = "查询学校权限和员工")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.LIST, description = "权限中心/学校权限管理/查询学校权限组别与员工关系")
    @PostMapping("selectInstitutionPermissionGroupStaff")
    public ResponseBo getInstitutionPermissionGroupStaff(@RequestBody SearchBean<StaffQueryDto> page) {
        List<StaffVo> datas  = rInstitutionPermissionGroupStaffService.dataList(page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ResponseBo<>(datas, p);
    }

    /**
     * 新增数据
     *
     * @param rInstitutionPermissionGroupStaffDto 实体对象
     * @return 新增结果
     */
    @ApiOperation(value = "批量新增数据", notes = "新增数据")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.ADD, description = "权限中心/学校权限管理/新增学校权限和员工关系")
    @PostMapping("add")
    public ResponseBo insert(@RequestBody RInstitutionPermissionGroupStaffDto rInstitutionPermissionGroupStaffDto) {
        return new ResponseBo<>(rInstitutionPermissionGroupStaffService.addRInstitutionPermissionGroupStaff(rInstitutionPermissionGroupStaffDto));
    }

    /**
     * 删除数据
     *
     * @param id 主键
     * @return 删除结果
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.DELETE, description = "权限中心/学校权限管理/删除学校权限和员工")
    @GetMapping("delete")
    public ResponseBo delete(@RequestParam("id") Long id) {
        rInstitutionPermissionGroupStaffService.removeById(id);
        return ResponseBo.ok();
    }
}


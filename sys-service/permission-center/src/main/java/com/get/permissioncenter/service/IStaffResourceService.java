package com.get.permissioncenter.service;

import com.get.core.mybatis.base.BaseService;
import com.get.permissioncenter.dto.StaffResourceDto;
import com.get.permissioncenter.vo.StaffResourceVo;
import com.get.permissioncenter.entity.StaffResource;

import java.util.List;

/**
 * @author: jack
 * @create: 2020/7/21
 * @verison: 1.0
 * @description: 员工允许禁止权限业务接口
 */
public interface IStaffResourceService extends BaseService<StaffResource> {
    /**
     * 获取员工允许禁止的权限
     *
     * @param staffResourceDto
     * @return
     */
    List<StaffResourceVo> getStaffResources(StaffResourceDto staffResourceDto);

    /**
     * 保存员工允许禁止的权限
     *
     * @param staffResourceDto
     * @return
     */
    void updateStaffResources(StaffResourceDto staffResourceDto);

    /**
     * 用户权限清单
     *
     * @param staffId
     * @return
     */
    StaffResourceVo getStaffResourceKeys(Long staffId);
}

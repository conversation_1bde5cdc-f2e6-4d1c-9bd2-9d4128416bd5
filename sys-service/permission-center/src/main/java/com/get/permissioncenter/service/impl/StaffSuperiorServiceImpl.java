package com.get.permissioncenter.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.cache.utils.CacheUtil;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.dao.StaffSuperiorMapper;
import com.get.permissioncenter.entity.StaffSuperior;
import com.get.permissioncenter.service.IDepartmentService;
import com.get.permissioncenter.service.IPositionService;
import com.get.permissioncenter.service.IStaffSuperiorService;
import com.get.permissioncenter.vo.DepartmentVo;
import com.get.permissioncenter.vo.PositionVo;
import com.get.permissioncenter.vo.StaffFollowerVo;
import com.get.permissioncenter.vo.StaffSuperiorVo;
import com.get.permissioncenter.vo.tree.DepartmentTreeVo;
import com.get.permissioncenter.vo.tree.PositionTreeVo;
import com.get.permissioncenter.vo.tree.StaffSuperiorTreeVo;
import com.get.permissioncenter.vo.tree.StaffTreeVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.get.common.cache.CacheNames.STAFF_FOLLOWER_IDS_CACHE;

/**
 * <AUTHOR>
 * @DATE: 2020/7/27
 * @TIME: 17:26
 * @Description: 员工业务上司
 **/
@Service
public class StaffSuperiorServiceImpl extends BaseServiceImpl<StaffSuperiorMapper, StaffSuperior> implements IStaffSuperiorService {
    @Resource
    private StaffSuperiorMapper superiorMapper;
    @Autowired
    private IDepartmentService departMentService;
    @Lazy
    @Autowired
    private IPositionService positionService;
    @Autowired
    private UtilService utilService;

    @Override
    public List<StaffSuperiorVo> getStaffSuperior(Long staffId) {
        if (GeneralTool.isEmpty(staffId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("staff_id_null"));
        }
        return superiorMapper.getStaffSuperior(staffId);
    }

    @Override
    public List<StaffFollowerVo> getStaffFollower(Long staffId, String keyWord) {
        if (GeneralTool.isEmpty(staffId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("staff_id_null"));
        }

        return superiorMapper.getStaffFollower(staffId, keyWord);
    }

    @Override
    public List<StaffSuperiorTreeVo> getStaffSuperiorTree(Long staffId) {
        if (GeneralTool.isEmpty(staffId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("staff_id_null"));
        }
        //查询上司公司的上级公司
        List<Long> parentCompanyIds = superiorMapper.getSuperiorParentCompanyIds(staffId);
        if (GeneralTool.isEmpty(parentCompanyIds)) {
            return null;
        }
        List<StaffSuperiorTreeVo> result = null;
        for (Long companyId : parentCompanyIds) {
            //查询单个上司
            List<StaffSuperiorTreeVo> staffSuperiorTree = superiorMapper.getStaffSuperiorTree(staffId, companyId);
            List<StaffSuperiorTreeVo> treeList = getTreeList(staffSuperiorTree);
            result = mergeTreeData(result, treeList);

        }

        return result;
    }

    //    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = YException.class)
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long updateStaffSuperior(Long staffId, List<Long> superiorIds) {
        if (GeneralTool.isEmpty(staffId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("staff_id_null"));
        }
        //传一个id查出所有这些id的业务下属 与staffId对比，若相等则不能添加
        List<Long> followerIds = superiorMapper.getAllFollowerIds(String.valueOf(staffId));
        if (!Collections.disjoint(followerIds, superiorIds)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("staff_bind_superior_error"));
        }
        if (GeneralTool.isNotEmpty(superiorIds)) {
            //将记录插入
            for (Long superiorId : superiorIds) {
                if (superiorId.equals(staffId)) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("superior_bind_error"));
                }
                StaffSuperior superior = new StaffSuperior();
                superior.setFkStaffId(staffId);
                superior.setFkStaffSuperiorId(superiorId);
//                utilService.setUpdateInfo(superior);
//                superiorMapper.insertSelective(superior);
                utilService.updateUserInfoToEntity(superior);
                superiorMapper.insert(superior);
            }
        }
        //清理缓存
        CacheUtil.clear(STAFF_FOLLOWER_IDS_CACHE);
        return staffId;
    }

    @Override
    public void deleteStaffSuperior(Long staffId, List<Long> superiorIds) {
        if (GeneralTool.isEmpty(staffId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("staff_id_null"));
        }
        if (GeneralTool.isNotEmpty(superiorIds)) {
            for (Long superiorId : superiorIds) {
                superiorMapper.delete(Wrappers.<StaffSuperior>query().lambda().eq(StaffSuperior::getFkStaffId, staffId).eq(StaffSuperior::getFkStaffSuperiorId, superiorId));
            }
        }
        CacheUtil.clear(STAFF_FOLLOWER_IDS_CACHE);
    }

    @Override
    public List<Long> getStaffFollowerId(Long staffId) {
        if (GeneralTool.isEmpty(staffId)) {
            return null;
        }
//        List<Long> staffFollowerIds = superiorMapper.getStaffFollowerId(staffId);
        System.out.println("==========================================================================>>>获取staffFollowerIds-1");
        List<Long> staffFollowerIds = CacheUtil.get(
                STAFF_FOLLOWER_IDS_CACHE,
                "staffId:",
                staffId,
                ()->superiorMapper.getStaffFollowerId(staffId)
        );        System.out.println("==========================================================================>>>获取staffFollowerIds-2");
        if (GeneralTool.isEmpty(staffFollowerIds)) {
            staffFollowerIds = new ArrayList<>();
            return staffFollowerIds;
        } else {
            return staffFollowerIds.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        }
    }


    /**
     * 获取业务下属id（一层）
     * @param staffId
     * @return
     */
    @Override
    public List<Long> getBusinessSubordinatesIds(Long staffId) {
        if (GeneralTool.isEmpty(staffId)) {
            return null;
        }
        List<Long> staffFollowerIds = superiorMapper.getBusinessSubordinatesIds(staffId);
        if (GeneralTool.isEmpty(staffFollowerIds)) {
            staffFollowerIds = new ArrayList<>();
            return staffFollowerIds;
        } else {
            return staffFollowerIds.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        }
    }


    private List<StaffSuperiorTreeVo> mergeTreeData(List<StaffSuperiorTreeVo> masterTree, List<StaffSuperiorTreeVo> slaveTree) {
        //合并树,去重
        if (GeneralTool.isEmpty(masterTree)) {
            return slaveTree;
        }
        if (GeneralTool.isEmpty(slaveTree)) {
            return masterTree;
        }
        //获取slave 的公司id
        String slaveCompanyId = slaveTree.get(0).getChildList().get(0).getCompanyId();
        //获取slave 的部门id

        ListIterator<StaffSuperiorTreeVo> iterator = masterTree.listIterator();
        while (iterator.hasNext()) {
            StaffSuperiorTreeVo topTree = iterator.next();
            ListIterator<StaffSuperiorTreeVo> childrenTreeList = topTree.getChildList().listIterator();
            //遍历获取子公司
            while (childrenTreeList.hasNext()) {
                StaffSuperiorTreeVo childrenTree = childrenTreeList.next();
                //获取master 的公司id
                String masterCompanyId = childrenTree.getCompanyId();
                if (masterCompanyId.equals(slaveCompanyId)) {
                    //移除原先的分支
                    slaveTree.remove(0);
                }
            }
        }
        return masterTree;
    }

    private List<StaffSuperiorTreeVo> getTreeList(List<StaffSuperiorTreeVo> entityList) {
        List<StaffSuperiorTreeVo> resultList = new ArrayList<>();
        //创建顶层元素
        String parentId;
        for (StaffSuperiorTreeVo entity : entityList) {
            parentId = String.valueOf(entity.getFkParentCompanyId());
            if (parentId == null || "0".equals(parentId)) {
                resultList.add(entity);
            }

        }
        for (StaffSuperiorTreeVo entity : resultList) {
            //查询部门
            if (GeneralTool.isNotEmpty(entity.getStaffId())) {
                List<DepartmentTreeVo> departmentTree = getDepartmentTree(entity);
                entity.setDepartmentTree(departmentTree);
            }
            entity.setChildList(getSubList(entity.getCompanyId(), entityList));
        }
        return resultList;
    }

    private List<StaffSuperiorTreeVo> getSubList(String id, List<StaffSuperiorTreeVo> entityList) {
        List<StaffSuperiorTreeVo> childList = new ArrayList<>();
        String parentId;
        // 子集的直接子对象
        for (StaffSuperiorTreeVo entity : entityList) {
            parentId = String.valueOf(entity.getFkParentCompanyId());
            if (id.equals(parentId)) {
                childList.add(entity);
            }
        }
        //子集的间接子对象
        for (StaffSuperiorTreeVo entity : childList) {
            //查询部门
            if (GeneralTool.isNotEmpty(entity.getStaffId())) {
                List<DepartmentTreeVo> departmentTree = getDepartmentTree(entity);
                entity.setDepartmentTree(departmentTree);
            }
            entity.setChildList(getSubList(entity.getCompanyId(), entityList));
        }
        // 递归退出条件
        if (childList.size() == 0) {
            return null;
        }
        return childList;
    }

    //根据获取部门
    private List<DepartmentTreeVo> getDepartmentTree(StaffSuperiorTreeVo entity) {
        Long departId = entity.getDepartmentId();

        List<DepartmentTreeVo> departmentTreeVos = new ArrayList<>();
        DepartmentVo departMent = departMentService.findDepartmentById(departId);
        DepartmentTreeVo departmentTreeVo = BeanCopyUtils.objClone(departMent, DepartmentTreeVo::new);
        //当部门不为空时
        if (GeneralTool.isNotEmpty(departmentTreeVo)) {
            //查询当前的部门的职位
            List<PositionTreeVo> positionTree = getPositionTree(entity);
            departmentTreeVo.setPositionTreeList(positionTree);
        }
        departmentTreeVos.add(departmentTreeVo);
        return departmentTreeVos;
    }

    //获取职位
    private List<PositionTreeVo> getPositionTree(StaffSuperiorTreeVo entity) {
        //返回listTree
        List<PositionTreeVo> positionTreeVos = new ArrayList<>();
        //职位id
        Long positionId = entity.getPositionId();
        //查询职位信息
        PositionVo positionVo = positionService.findPositionById(positionId);
        PositionTreeVo positionTreeVo = BeanCopyUtils.objClone(positionVo, PositionTreeVo::new);

        if (GeneralTool.isNotEmpty(positionTreeVo)) {
            StaffTreeVo staffTreeVo = convert(entity);
            List<StaffTreeVo> staffTreeVos = new ArrayList<>();
            staffTreeVos.add(staffTreeVo);
            //将员工放入职位
            positionTreeVo.setStaffTreeList(staffTreeVos);
        }
        positionTreeVos.add(positionTreeVo);
        return positionTreeVos;
    }

    private StaffTreeVo convert(StaffSuperiorTreeVo entity) {
        StaffTreeVo staffTreeVo = new StaffTreeVo();
        if (GeneralTool.isNotEmpty(entity)) {
            staffTreeVo.setId(Long.valueOf(entity.getStaffId()));
            staffTreeVo.setName(entity.getStaffName());
            staffTreeVo.setNum(entity.getStaffNum());
            staffTreeVo.setFkCompanyId(Long.valueOf(entity.getCompanyId()));
            staffTreeVo.setFkDepartmentId(entity.getDepartmentId());
            staffTreeVo.setFkPositionId(entity.getPositionId());
            staffTreeVo.setSuperior(true);
            //清除多余的属性
            clearProperties(entity);
        }
        return staffTreeVo;
    }

    //清除属性
    private void clearProperties(StaffSuperiorTreeVo entity) {
        entity.setStaffId(null);
        entity.setStaffName(null);
        entity.setStaffNum(null);
        entity.setDepartmentId(null);
        entity.setPositionId(null);
        entity.setDepartmentName(null);
        entity.setPositionName(null);
    }

    @Override
    public List<BaseSelectEntity> getStaffFollowerSelect(Long companyId) {
        if (GeneralTool.isEmpty(companyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("company_id_null"));
        }
        Long staffId = SecureUtil.getStaffId();
        return superiorMapper.getStaffFollowerSelect(staffId, companyId);
    }

    @Override
    public Map<Long, List<Long>> getStaffSuperiorByIds(Set<Long> staffIds) {
        List<StaffSuperiorVo> staffSuperiorVos = superiorMapper.getStaffSuperiorByIds(staffIds);
        Map<Long, List<StaffSuperiorVo>> collect = staffSuperiorVos.stream().collect(Collectors.groupingBy(StaffSuperiorVo::getFkStaffId));
        Map<Long, List<Long>> result = new HashMap<>();
        for (Long aLong : collect.keySet()) {
            result.put(aLong, collect.get(aLong).stream().map(StaffSuperiorVo::getFkStaffSuperiorId).collect(Collectors.toList()));
        }
        return result;
    }

}

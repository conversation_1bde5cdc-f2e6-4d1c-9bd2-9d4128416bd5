package com.get.permissioncenter.service;


import com.get.core.mybatis.base.BaseService;
import com.get.permissioncenter.dto.ResourceDto;
import com.get.permissioncenter.vo.ResourceVo;
import com.get.permissioncenter.entity.Resource;

import java.util.List;

/**
 * @author: jack
 * @create: 2020/6/8
 * @verison: 1.0
 * @description: 系统资源业务接口
 */
public interface IResourceService extends BaseService<Resource> {
    /**
     * 详情
     *
     * @param id
     * @return
     */
    ResourceVo findResourceById(Long id);

    /**
     * 列表数据
     *
     * @param resourceDto
     * @return
     */
    List<ResourceVo> getResources(ResourceDto resourceDto);

    /**
     * 修改
     *
     * @param resourceDto
     * @return
     */
    ResourceVo updateResource(ResourceDto resourceDto);

    /**
     * 保存
     *
     * @param resourceDto
     * @return
     */
    Long addResource(ResourceDto resourceDto);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);
    /**
     * 更新表单
     * @param resourcevos
     * @return
     */
    /*void batchUpdate(List<ResourceDto> resourcevos);*/

    /**
     * 批量保存
     *
     * @param resourcevos
     * @return
     */
    void batchAdd(List<ResourceDto> resourcevos);

    /**
     * 上移下移
     *
     * @param resourcevos
     * @return
     */
    void movingOrder(List<ResourceDto> resourcevos);

    /**
     * @param keyword
     * @return
     */
    List<Resource> getResources(String keyword);

    /**
     * 获取后端权限
     *
     * @param resourcekeys
     * @return
     */
    List<String> getApiKeysByResourceKeys(List<String> resourcekeys);

    /**
     * 资源树
     *
     * @return
     */
    List<ResourceVo> getResourceTree();


    void updateTranslationConfig(String type);
}

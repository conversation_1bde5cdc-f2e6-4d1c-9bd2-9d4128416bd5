package com.get.permissioncenter.service.impl;

import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.dao.PermissionGroupGradeResourceMapper;
import com.get.permissioncenter.dao.StaffResourceMapper;
import com.get.permissioncenter.dto.StaffResourceDto;
import com.get.permissioncenter.vo.StaffResourceVo;
import com.get.permissioncenter.entity.StaffResource;
import com.get.permissioncenter.service.IStaffResourceService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/7/21
 * @TIME: 11:01
 * @Description:用户允许禁止权限业务
 **/
@Service
public class StaffResourceServiceImpl extends BaseServiceImpl<StaffResourceMapper, StaffResource> implements IStaffResourceService {
    @Resource
    private StaffResourceMapper staffResourceMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private PermissionGroupGradeResourceMapper permissionGroupGradeResourceMapper;

    @Override
    public List<StaffResourceVo> getStaffResources(StaffResourceDto staffResourceDto) {
        List<StaffResourceVo> staffResourceVos = new ArrayList<>();
        List<StaffResource> staffResources = staffResourceMapper.findStaffResourcesByVo(staffResourceDto);
        for (StaffResource staffResource : staffResources) {
            StaffResourceVo staffResourceVo = BeanCopyUtils.objClone(staffResource, StaffResourceVo::new);
            staffResourceVos.add(staffResourceVo);
        }
        return staffResourceVos;
    }

    //    @Transactional(propagation = Propagation.SUPPORTS, readOnly = true, rollbackFor = YException.class)
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateStaffResources(StaffResourceDto staffResourceDto) {
        if (GeneralTool.isNotEmpty(staffResourceDto) && GeneralTool.isNotEmpty(staffResourceDto.getFkStaffId())) {
            staffResourceMapper.deleteByStaffId(staffResourceDto.getFkStaffId());
        }
        if (GeneralTool.isNotEmpty(staffResourceDto.getStaffResources())) {
            for (StaffResource sr : staffResourceDto.getStaffResources()) {
                StaffResource staffResource = new StaffResource();
                staffResource.setFkResourceKey(sr.getFkResourceKey());
                staffResource.setFkStaffId(sr.getFkStaffId());
                staffResource.setPermission(sr.getPermission());
                utilService.updateUserInfoToEntity(staffResource);
                staffResourceMapper.insert(staffResource);
            }
        }
        /*Subject subject = LoginUtil.getSubject();
        StaffVo staffDto = (StaffVo) subject.getPrincipal();
        if(staffResourceDto.getFkStaffId().equals(staffDto.getId())){
            Set<String> apikeys = groupGradeResourceService.getStaffApiKeys(staffDto.getId());
            staffDto.setApiKeys(apikeys);
            LoginUtil.setStaff(staffDto);
            StaffContext.setStaff(staffDto);
        }*/
        List<Long> staffids = new ArrayList<>();
        staffids.add(staffResourceDto.getFkStaffId());
        /*groupGradeResourceService.updateStaffSession(staffids);*/
    }

    @Override
    public StaffResourceVo getStaffResourceKeys(Long staffId) {
        if (GeneralTool.isEmpty(staffId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        StaffResourceVo staffResourceVo = new StaffResourceVo();
        List<String> allowResourcekeys = permissionGroupGradeResourceMapper.findResourcesByStaffId(staffId);
        List<String> notAllowResourcekeys = new ArrayList<>();
        List<StaffResource> staffResources = staffResourceMapper.findStaffResourcesByStaffId(staffId);
        if (GeneralTool.isNotEmpty(staffResources)) {
            for (StaffResource staffResource : staffResources) {
                if (staffResource.getPermission() == 1 && !allowResourcekeys.contains(staffResource.getFkResourceKey())) {
                    allowResourcekeys.add(staffResource.getFkResourceKey());
                } else if (staffResource.getPermission() == 0) {
                    if (allowResourcekeys.contains(staffResource.getFkResourceKey())) {
                        allowResourcekeys.remove(staffResource.getFkResourceKey());
                    }
                    notAllowResourcekeys.add(staffResource.getFkResourceKey());
                }
            }
        }
        staffResourceVo.setAllowResources(allowResourcekeys);
        staffResourceVo.setNotAllowResources(notAllowResourcekeys);
        return staffResourceVo;
    }
}

package com.get.permissioncenter.service.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.secure.StaffInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.dao.StaffAreaCountryMapper;
import com.get.permissioncenter.dao.StaffMapper;
import com.get.permissioncenter.dto.StaffAreaCountryDto;
import com.get.permissioncenter.vo.AreaCountryVo;
import com.get.permissioncenter.vo.StaffAreaCountryVo;
import com.get.permissioncenter.entity.Staff;
import com.get.permissioncenter.entity.StaffAreaCountry;
import com.get.permissioncenter.service.IStaffAreaCountryService;
import com.get.permissioncenter.dto.StaffAreaCountryBatchUpdateDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/7/22
 * @TIME: 12:53
 * @Description:
 **/

@Service
public class StaffAreaCountryImpl extends BaseServiceImpl<StaffAreaCountryMapper, StaffAreaCountry> implements IStaffAreaCountryService {
    @Resource
    private StaffAreaCountryMapper areaCountryMapper;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    private StaffMapper staffMapper;
    @Resource
    private UtilService<Object> utilService;
    @Resource
    private StaffAreaCountryMapper staffAreaCountryMapper;

    //    @Resource
//    private FeignInstitutionService feignInstitutionService;
    @Override
    public List<StaffAreaCountryVo> getStaffAreaCountryById(Long id, String keyWord) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        return getStaffCountry(id, keyWord);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long updateStaffAreaCountry(Long id, List<StaffAreaCountryDto> staffAreaCountryDto) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
//        //删除相关记录
//        Example example = new Example(StaffAreaCountry.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkStaffId", id);
//        areaCountryMapper.deleteByExample(example);
//        //获取选中的国家
//        List<StaffAreaCountryDto> collect =
//                staffAreaCountryDto.stream().filter(StaffAreaCountryDto::getIsSelect).collect(Collectors.toList());
//        //将数据插入中间表
//        if (GeneralTool.isNotEmpty(staffAreaCountryDto)) {
//            for (StaffAreaCountryDto countryVo : collect) {
//                StaffAreaCountry areaCountry = new StaffAreaCountry();
//                areaCountry.setFkStaffId(id);
//                areaCountry.setFkAreaCountryKey(countryVo.getNum());
//                areaCountryMapper.insertSelective(areaCountry);
//            }
//        }
        //删除相关记录
//        Example example = new Example(StaffAreaCountry.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkStaffId", id);
//        areaCountryMapper.deleteByExample(example);

        areaCountryMapper.delete(Wrappers.<StaffAreaCountry>query().lambda()
                .eq(StaffAreaCountry::getFkStaffId, id));
        //获取选中的国家
        List<StaffAreaCountryDto> collect =
                staffAreaCountryDto.stream().filter(StaffAreaCountryDto::getIsSelect).collect(Collectors.toList());
        //将数据插入中间表
        List<StaffAreaCountry> addList = new ArrayList<>();
        if (GeneralTool.isNotEmpty(staffAreaCountryDto)) {
            for (StaffAreaCountryDto countryVo : collect) {
                StaffAreaCountry areaCountry = new StaffAreaCountry();
                areaCountry.setFkStaffId(id);
                areaCountry.setFkAreaCountryKey(countryVo.getNum());
                utilService.setCreateInfo(areaCountry);
                addList.add(areaCountry);
            }
        }
        if (GeneralTool.isNotEmpty(addList)) {
            saveBatch(addList);
        }
        updateStaffAreaCountry(id);
        return id;
    }

    @Override
    public Boolean isExistByStaffId(Long staffId) {
        return areaCountryMapper.isExistByStaffId(staffId);
    }

    @Override
    public List<String> getStaffAreaCountryKeysByfkStaffId(Long staffId) {
        return areaCountryMapper.getStaffAreaCountryKeysByfkStaffId(staffId);
    }

    @Override
    public Set<Long> getStaffAreaCountryByfkStaffId(Long staffId) {
        return areaCountryMapper.getStaffAreaCountryIdByfkStaffId(staffId);
    }

    @Override
    public List<Long> getStaffByAreaCountryKeys(List<Long> staffIds, List<String> areaCountryKeys) {
        return areaCountryMapper.getStaffByAreaCountryKeys(staffIds, areaCountryKeys);
    }


    //获取业务国家
    private ArrayList<StaffAreaCountryVo> getStaffCountry(Long id, String keyWord) {
        //定义员工业务国家
        ArrayList<StaffAreaCountryVo> staffAreaCountryVos = null;
        //查询中间表的数据
//        Example example = new Example(StaffAreaCountry.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkStaffId", id);
//        获取所有的业务国家
        List<StaffAreaCountry> areaCountries = areaCountryMapper.selectList(Wrappers.<StaffAreaCountry>lambdaQuery().eq(StaffAreaCountry::getFkStaffId, id));
        //服务调用查询所有的国家
        Result<List<com.get.institutioncenter.vo.AreaCountryVo>> areaCountryResult = institutionCenterClient.getAreaCountrys(keyWord);
        if (areaCountryResult.isSuccess() && GeneralTool.isNotEmpty(areaCountryResult.getData())) {
            JSONArray objects = JSONUtil.parseArray(areaCountryResult.getData());
            List<AreaCountryVo> areaCountryFeign = JSONUtil.toList(objects, AreaCountryVo.class);
            //当调用学校中心的业务国家有返回值时进行转换
            if (GeneralTool.isNotEmpty(areaCountryFeign)) {
                staffAreaCountryVos = ConvertCountryList(areaCountryFeign);
            }
            if (GeneralTool.isNotEmpty(areaCountries) && GeneralTool.isNotEmpty(areaCountryFeign)) {
                //遍历判断是否选中
                for (StaffAreaCountryVo areaCountryDto : staffAreaCountryVos) {
                    for (StaffAreaCountry areaCountry : areaCountries) {
                        boolean flag = areaCountry.getFkAreaCountryKey().equals(areaCountryDto.getNum());
                        if (flag) {
                            areaCountryDto.setIsSelect(true);
                        }
                    }
                }
            }
        }
        return staffAreaCountryVos;
    }

    //国家list转换
    private ArrayList<StaffAreaCountryVo> ConvertCountryList(List<AreaCountryVo> areaCountryFeign) {
        ArrayList<StaffAreaCountryVo> staffAreaCountryVos = new ArrayList<>();
        for (AreaCountryVo countryDto : areaCountryFeign) {
            StaffAreaCountryVo staffAreaCountry = new StaffAreaCountryVo();
            staffAreaCountry.setName(countryDto.getName());
            staffAreaCountry.setNum(countryDto.getNum());
            staffAreaCountry.setNameChn(countryDto.getNameChn());
            staffAreaCountryVos.add(staffAreaCountry);
        }
        return staffAreaCountryVos;
    }

    public void updateStaffAreaCountry(Long staffId) {
        StaffInfo staffInfo = SecureUtil.getStaffInfoByStaffId(staffId);
        if (staffInfo != null) {
            Staff staff = staffMapper.selectById(staffId);
            List<String> keys = new ArrayList<>();
            List<StaffAreaCountry> staffAreaCountrys = staffAreaCountryMapper.getStaffAreaCountrysByfkStaffId(staff.getId());
            for (StaffAreaCountry staffAreaCountry : staffAreaCountrys) {
                keys.add(staffAreaCountry.getFkAreaCountryKey());
            }
            Result<List<Long>> countryIdByKeyResult = institutionCenterClient.getCountryIdByKey(keys);
            if (countryIdByKeyResult.isSuccess() && GeneralTool.isNotEmpty(countryIdByKeyResult.getData())) {
                List<Long> ids = (List<Long>) countryIdByKeyResult.getData();
                SecureUtil.updateCountryIdsByStaffId(staffId, ids);
            }
        }
    }

    @Override
    public List<com.get.institutioncenter.vo.AreaCountryVo> getStaffAreaCountryKeysSelect() {
        Long staffId = SecureUtil.getStaffId();
        List<String> keys = areaCountryMapper.getStaffAreaCountryKeysByfkStaffId(staffId);
        Result<List<com.get.institutioncenter.vo.AreaCountryVo>> result = institutionCenterClient.getCountryByKey(keys);
        if (!result.isSuccess()) {
            throw new GetServiceException(result.getMessage());
        }
        return result.getData();
    }

    /**
     * 批量修改业务国家
     *
     * @Date 11:53 2023/12/11
     * <AUTHOR>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateStaffAreaCountry(StaffAreaCountryBatchUpdateDto staffAreaCountryBatchUpdateDto) {
        if (!staffAreaCountryBatchUpdateDto.getCountryFlag()) {
            areaCountryMapper.delete(Wrappers.<StaffAreaCountry>query().lambda()
                    .in(StaffAreaCountry::getFkStaffId, staffAreaCountryBatchUpdateDto.getFkStaffIds()));
        }
        List<StaffAreaCountry> staffAreaCountryList = new ArrayList<>();
        for (Long fkStaffId : staffAreaCountryBatchUpdateDto.getFkStaffIds()) {
            List<StaffAreaCountry> staffAreaCountries = areaCountryMapper.selectList(Wrappers.<StaffAreaCountry>query().lambda().eq(StaffAreaCountry::getFkStaffId, fkStaffId));
            for (String fkAreaCountryKey : staffAreaCountryBatchUpdateDto.getFkAreaCountryKey()) {
                boolean flag = false;
                for (StaffAreaCountry staffAreaCountry : staffAreaCountries) {
                    if (staffAreaCountry.getFkAreaCountryKey().equals(fkAreaCountryKey)) {
                        flag = true;
                    }
                }
                if (flag) {
                    continue;
                }
                StaffAreaCountry staffAreaCountry = new StaffAreaCountry();
                staffAreaCountry.setFkAreaCountryKey(fkAreaCountryKey);
                staffAreaCountry.setFkStaffId(fkStaffId);
                utilService.setCreateInfo(staffAreaCountry);
                staffAreaCountryList.add(staffAreaCountry);
            }
        }
        if (GeneralTool.isNotEmpty(staffAreaCountryList)) {
            areaCountryMapper.insertBatchSomeColumn(staffAreaCountryList);
        }
    }


    @Override
    public List<StaffAreaCountry> getStaffAreaCountryByStaffIds(List<Long> fkStaffIds){
        if(GeneralTool.isEmpty(fkStaffIds)){
            return Collections.emptyList();
        }
        List<StaffAreaCountry> staffAreaCountries = areaCountryMapper.selectList(Wrappers.<StaffAreaCountry>lambdaQuery().in(StaffAreaCountry::getFkStaffId, fkStaffIds));
        return staffAreaCountries;

    }

}

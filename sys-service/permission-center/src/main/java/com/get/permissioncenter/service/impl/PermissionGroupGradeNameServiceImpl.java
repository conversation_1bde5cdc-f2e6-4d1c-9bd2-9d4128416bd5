package com.get.permissioncenter.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.dao.PermissionGroupGradeNameMapper;
import com.get.permissioncenter.entity.PermissionGroupGradeName;
import com.get.permissioncenter.service.IPermissionGroupGradeNameService;
import com.get.permissioncenter.dto.PermissionGroupGradeNameDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @DATE: 2021/4/30
 * @TIME: 17:17
 * @Description:
 **/
@Service
public class PermissionGroupGradeNameServiceImpl extends ServiceImpl<PermissionGroupGradeNameMapper, PermissionGroupGradeName> implements IPermissionGroupGradeNameService {
    @Resource
    private PermissionGroupGradeNameMapper permissionGroupGradeNameMapper;
    @Resource
    private UtilService utilService;

    @Override
    public void updateGroupGradeName(PermissionGroupGradeNameDto permissionGroupGradeNameDto) {
        if (GeneralTool.isEmpty(permissionGroupGradeNameDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        PermissionGroupGradeName permissionGroupGradeName = permissionGroupGradeNameMapper.selectByGroupAndGrade(permissionGroupGradeNameDto.getFkPermissionGroupId(), permissionGroupGradeNameDto.getFkPermissionGradeId());
        PermissionGroupGradeName pn = BeanCopyUtils.objClone(permissionGroupGradeNameDto, PermissionGroupGradeName::new);
        if (GeneralTool.isEmpty(permissionGroupGradeName)) {
//            utilService.setCreateInfo(permissionGroupGradeName);
            utilService.updateUserInfoToEntity(pn);
            permissionGroupGradeNameMapper.insertSelective(pn);
        } else {
            permissionGroupGradeName.setPermissionName(permissionGroupGradeNameDto.getPermissionName());
//            utilService.setUpdateInfo(permissionGroupGradeName);
//            permissionGroupGradeNameMapper.updateByPrimaryKey(permissionGroupGradeName);
            utilService.updateUserInfoToEntity(permissionGroupGradeName);
            permissionGroupGradeNameMapper.updateById(permissionGroupGradeName);
        }
    }
}

package com.get.permissioncenter.service;

import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseService;
import com.get.permissioncenter.dto.OfficeDto;
import com.get.permissioncenter.vo.OfficeVo;
import com.get.permissioncenter.entity.Office;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/6/24
 * @TIME: 18:28
 **/
public interface IOfficeService extends BaseService<Office> {


    /**
     * @return java.util.List<com.get.permissioncenter.com.get.permissioncenter.vo.OfficeVo>
     * @Description: 分页
     * @Param [data, page]
     * <AUTHOR>
     */
    List<OfficeVo> getOfficeDtos(OfficeDto data);


    /**
     * @return java.util.List<com.get.permissioncenter.com.get.permissioncenter.vo.OfficeVo>
     * @Description: 不分页
     * @Param [officeDto]
     * <AUTHOR>
     */
    List<OfficeVo> getOffice(OfficeDto officeDto);

    /**
     * 根据用户id查询业务办公室
     *
     * @param staffId
     * @return
     */
    List<OfficeVo> getStaffOffice(Long staffId);


    /**
     * 详情
     *
     * @param id
     * @return
     */
    OfficeVo findOfficeById(Long id);


    /**
     * 新增
     *
     * @param officeDto
     * @return
     */
    Long addOffice(OfficeDto officeDto);

    /**
     * 修改
     *
     * @param officeDto
     * @return
     */

    OfficeVo updateOfficeVo(OfficeDto officeDto);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 排序
     *
     * @param officeDtos
     */
    void movingOrder(List<OfficeDto> officeDtos);

    /**
     * 批量新增
     *
     * @param officeDtos
     */
    void batchAddOffice(List<OfficeDto> officeDtos);


    /**
     * @return java.util.List<com.get.common.com.get.permissioncenter.vo.entity.BaseSelectEntity>
     * @Description:
     * @Param [companyId]
     * <AUTHOR>
     */
    List<BaseSelectEntity> getOfficeSelect(Long companyId);

    /**
     * @return java.lang.String
     * @Description :根据办公室id查找办公室名称
     * @Param [officeId]
     * <AUTHOR>
     */
    String getOfficeNameById(Long officeId);


    OfficeVo getOfficeById(Long officeId);

    Map<Long, String> getofficeNamesByIds(Set<Long> officeIds);
}

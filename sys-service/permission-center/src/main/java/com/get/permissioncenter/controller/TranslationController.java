package com.get.permissioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.translation.baidu.result.TransVo;
import com.get.permissioncenter.service.ITranslationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.context.ApplicationContext;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.sql.Time;

/**
 * @author: Hardy
 * @create: 2021/6/7 17:13
 * @verison: 1.0
 * @description:
 */
@Api(tags = "翻译管理")
@RestController
@RequestMapping("system/translation")
public class TranslationController {

    @Resource
    private ITranslationService translationService;

//    @Resource
//    private TranslationTool translationTool;

    /**
     * 翻译
     *
     * @param transVo
     * @return
     */
    @ApiOperation(value = "翻译接口")
    @OperationLogger(module = LoggerModulesConsts.SYSTEMCENTER, type = LoggerOptTypeConst.ADD, description = "系统中心/翻译管理/翻译")
    @PostMapping("getTranslation")
    public ResponseBo<String> getTranslation(@RequestBody TransVo transVo) {

        String from = transVo.getFrom();
        String to= transVo.getTo();
        String fromto = from + "-" + to;

//        String result = translationTool.translate(transVo.getQuery(),to);

        String result = translationService.getTranslation(transVo);

        return new ResponseBo<>(result);
    }


}

package com.get.permissioncenter.service;

import com.get.core.mybatis.base.BaseService;
import com.get.institutioncenter.vo.AreaCountryVo;
import com.get.permissioncenter.dto.StaffAreaCountryDto;
import com.get.permissioncenter.vo.StaffAreaCountryVo;
import com.get.permissioncenter.entity.StaffAreaCountry;
import com.get.permissioncenter.dto.StaffAreaCountryBatchUpdateDto;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/7/22
 * @TIME: 12:34
 * @Description: 业务国家
 **/

public interface IStaffAreaCountryService extends BaseService<StaffAreaCountry> {

    /**
     * 根据用户id查询所有的业务国家
     *
     * @param id
     * @return
     */
    List<StaffAreaCountryVo> getStaffAreaCountryById(Long id, String keyWord);

    /**
     * 修改用户的业务国家
     *
     * @return
     */
    Long updateStaffAreaCountry(Long id, List<StaffAreaCountryDto> staffAreaCountryDto);


    /**
     * @return java.lang.Boolean
     * @Description: 是否有关联数据
     * @Param [staffId]
     * <AUTHOR>
     */
    Boolean isExistByStaffId(Long staffId);

    /**
     * 员工业务国家
     *
     * @param staffId
     * @return
     */
    List<String> getStaffAreaCountryKeysByfkStaffId(Long staffId);

    /**
     * 员工业务国家ids
     *
     * @param staffId
     * @return
     */
    Set<Long> getStaffAreaCountryByfkStaffId(Long staffId);


    /**
     * 根据业务国家keys和员工ids获取对应员工
     *
     * @param staffIds
     * @param areaCountryKeys
     * @return
     * @
     */
    List<Long> getStaffByAreaCountryKeys(List<Long> staffIds, List<String> areaCountryKeys);

    /**
     * 根据登录用户获取业务国家下拉
     *
     * @return
     * @
     */
    List<AreaCountryVo> getStaffAreaCountryKeysSelect();

    /**
     * 批量修改业务国家
     *
     * @Date 11:53 2023/12/11
     * <AUTHOR>
     */
    void batchUpdateStaffAreaCountry(StaffAreaCountryBatchUpdateDto staffAreaCountryBatchUpdateDto);

    /**
     * 根据员工ids获取对应的业务国家
     * <AUTHOR>
     * @DateTime 2024/4/23 17:54
     */
    List<StaffAreaCountry> getStaffAreaCountryByStaffIds(List<Long> fkStaffIds);
}

package com.get.permissioncenter.service;

import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseService;
import com.get.permissioncenter.vo.StaffFollowerVo;
import com.get.permissioncenter.vo.StaffSuperiorVo;
import com.get.permissioncenter.vo.tree.StaffSuperiorTreeVo;
import com.get.permissioncenter.entity.StaffSuperior;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/7/27
 * @TIME: 17:25
 * @Description: 员工上司interface
 **/
public interface IStaffSuperiorService extends BaseService<StaffSuperior> {

    /**
     * 获取员工上司集合对象
     *
     * @param staffId
     * @return
     */
    List<StaffSuperiorVo> getStaffSuperior(Long staffId);


    /**
     * 获取业务下级
     *
     * @param staffId
     * @return
     */
    List<StaffFollowerVo> getStaffFollower(Long staffId, String keyWord);


    /**
     * 获取业务上司树形结构数据
     *
     * @return
     */
    List<StaffSuperiorTreeVo> getStaffSuperiorTree(Long staffId);

    /**
     * 修改员工的业务上司
     *
     * @param superiorIds
     * @param staffId
     * @return
     */
    Long updateStaffSuperior(Long staffId, List<Long> superiorIds);

    /**
     * @return void
     * @Description: 删除
     * @Param [staffId, superiorIds]
     * <AUTHOR>
     */
    void deleteStaffSuperior(Long staffId, List<Long> superiorIds);


    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 获取下属ids集合
     * @Param [staffId]
     * <AUTHOR>
     */
    List<Long> getStaffFollowerId(Long staffId);

    /**
     * 当前登录用户业务下属下拉
     *
     * @return
     */
    List<BaseSelectEntity> getStaffFollowerSelect(Long companyId);

    /**
     * 批量获取业务上司
     *
     * @param staffIds
     * @return
     */
    Map<Long, List<Long>> getStaffSuperiorByIds(Set<Long> staffIds);


    /**
     * 获取一层业务下属ids
     * @param staffId
     * @return
     */
    List<Long> getBusinessSubordinatesIds(Long staffId);
}

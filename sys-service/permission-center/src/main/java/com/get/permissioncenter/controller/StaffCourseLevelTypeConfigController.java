package com.get.permissioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.UpdateResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseVoEntity;
import com.get.permissioncenter.vo.StaffCourseLevelTypeConfigVo;
import com.get.permissioncenter.service.StaffCourseLevelTypeConfigService;
import com.get.permissioncenter.dto.StaffCourseLevelTypeConfigDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 *  前端接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-18
 */

@Api(tags = "用户课程等级类型管理")
@RestController
@RequestMapping("permission/staffCourseLevelTypeConfig")
public class StaffCourseLevelTypeConfigController {

    @Resource
    private StaffCourseLevelTypeConfigService staffCourseLevelTypeConfigService;

    @ApiOperation(value = "列表数据")
    @OperationLogger(module = LoggerModulesConsts.SYSTEMCENTER, type = LoggerOptTypeConst.LIST, description = "权限中心/用户课程等级类型管理/列表")
    @PostMapping("datas")
    public ResponseBo<StaffCourseLevelTypeConfigVo> datas(@RequestParam("type") Integer type) {
        List<StaffCourseLevelTypeConfigVo> datas = staffCourseLevelTypeConfigService.getConfigs(type);
        return new ListResponseBo<>(datas);
    }


    @ApiOperation(value = "新增")
    @OperationLogger(module = LoggerModulesConsts.SYSTEMCENTER, type = LoggerOptTypeConst.ADD, description = "权限中心/用户课程等级类型管理/新增")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(StaffCourseLevelTypeConfigDto.Add.class) StaffCourseLevelTypeConfigDto staffCourseLevelTypeConfigDto){
        return SaveResponseBo.ok(staffCourseLevelTypeConfigService.add(staffCourseLevelTypeConfigDto));
    }

    @ApiOperation(value = "删除", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SYSTEMCENTER, type = LoggerOptTypeConst.DELETE, description = "权限中心/用户课程等级类型管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        staffCourseLevelTypeConfigService.delete(id);
        return DeleteResponseBo.ok();
    }
    @ApiOperation(value = "修改是否应用")
    @OperationLogger(module = LoggerModulesConsts.SYSTEMCENTER, type = LoggerOptTypeConst.EDIT, description = "权限中心/用户课程等级类型管理/修改是否应用")
    @PostMapping("update")
    public ResponseBo update(@RequestBody @Validated(BaseVoEntity.Update.class) StaffCourseLevelTypeConfigDto staffCourseLevelTypeConfigDto){
        staffCourseLevelTypeConfigService.update(staffCourseLevelTypeConfigDto);
        return UpdateResponseBo.ok();
    }


    @ApiOperation(value = "重置为默认值")
    @OperationLogger(module = LoggerModulesConsts.SYSTEMCENTER, type = LoggerOptTypeConst.ADD, description = "权限中心/用户课程等级类型管理/重置为默认值")
    @PostMapping("resetDefault")
    public ResponseBo resetDefaultConfig(@RequestParam("type") Integer type){
        staffCourseLevelTypeConfigService.resetDefaultConfig(type);
        return ResponseBo.ok();
    }


    @ApiOperation(value = "拖拽", notes = "")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type",value = "类型：1-课程等级；2-课程类型"),
            @ApiImplicitParam(name = "start",value = "拖拽起始位置"),
            @ApiImplicitParam(name = "end",value = "拖拽结束位置")
    })
    @OperationLogger(module = LoggerModulesConsts.SYSTEMCENTER, type = LoggerOptTypeConst.EDIT, description = "权限中心/用户课程等级类型管理/拖拽")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestParam("type")Integer type, @RequestParam("start")Integer start, @RequestParam("end")Integer end) {
        staffCourseLevelTypeConfigService.movingOrder(type,start,end);
        return ResponseBo.ok();
    }

}

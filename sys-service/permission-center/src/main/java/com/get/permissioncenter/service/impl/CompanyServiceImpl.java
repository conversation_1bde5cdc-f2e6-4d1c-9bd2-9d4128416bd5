package com.get.permissioncenter.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.CollectionUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.dao.CompanyMapper;
import com.get.permissioncenter.dao.ConfigMapper;
import com.get.permissioncenter.dao.StaffCompanyMapper;
import com.get.permissioncenter.dao.StaffMapper;
import com.get.permissioncenter.dto.CompanyDto;
import com.get.permissioncenter.dto.MediaAndAttachedDto;
import com.get.permissioncenter.vo.CompanyConfigAnalysisVo;
import com.get.permissioncenter.vo.CompanyVo;
import com.get.permissioncenter.vo.MediaAndAttachedVo;
import com.get.permissioncenter.vo.tree.CompanyTreeVo;
import com.get.permissioncenter.entity.Company;
import com.get.permissioncenter.entity.Config;
import com.get.permissioncenter.entity.Staff;
import com.get.permissioncenter.entity.StaffCompany;
import com.get.permissioncenter.service.ICompanyService;
import com.get.permissioncenter.service.IConfigService;
import com.get.permissioncenter.service.IDeleteService;
import com.get.permissioncenter.service.IMediaAndAttachedService;
import com.get.permissioncenter.dto.query.CompanyQueryDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/6/24
 * @TIME: 12:58
 **/
@Slf4j
@Service
public class CompanyServiceImpl extends BaseServiceImpl<CompanyMapper, Company> implements ICompanyService {
    @Resource
    private CompanyMapper companyMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private StaffMapper staffMapper;
    @Resource
    private IDeleteService deleteService;
    @Resource
    private IMediaAndAttachedService attachedService;
    @Resource
    @Lazy
    private IConfigService configService;
    @Resource
    private ConfigMapper configMapper;
    @Resource
    private StaffCompanyMapper staffCompanyMapper;


    @Override
    public List<CompanyVo> getCompanys(CompanyQueryDto companyVo) {
//        Example example = new Example(Company.class);
//        Example.Criteria criteria = example.createCriteria();
//        Example.Criteria criteria1 = example.createCriteria();
//        List<Long> companyIds = getCompanyIds();
//
//        criteria.andIn("id", companyIds);
//        if (GeneralTool.isNotEmpty(companyVo.getKeyWord())) {
//            criteria1.orLike("name", "%" + companyVo.getKeyWord() + "%");
//            criteria1.orLike("shortName", "%" + companyVo.getKeyWord() + "%");
//            criteria1.orLike("nameChn", "%" + companyVo.getKeyWord() + "%");
//            example.and(criteria1);
//        }
//        if (GeneralTool.isNotEmpty(companyVo.getFkParentCompanyId())) {
//            criteria.andEqualTo("fkParentCompanyId", companyVo.getFkParentCompanyId());
//        }
//        example.orderBy("viewOrder").asc();
//        //获取分页数据
//        List<Company> companies = companyMapper.selectByExample(example);
//        return companies.stream().map(company -> Tools.objClone(company, CompanyVo.class)).collect(Collectors.toList());

        LambdaQueryWrapper<Company> wrapper = new LambdaQueryWrapper();
        if (CollectionUtil.isNotEmpty(SecureUtil.getCompanyIdsByStaffId(GetAuthInfo.getStaffId()))) {
            wrapper.in(Company::getId, SecureUtil.getCompanyIdsByStaffId(GetAuthInfo.getStaffId()));
        }
        if (GeneralTool.isNotEmpty(companyVo.getKeyWord())) {
            wrapper.and(wrapper_ ->
                    wrapper_.like(Company::getName, companyVo.getKeyWord()).or()
                            .like(Company::getShortName, companyVo.getKeyWord()).or()
                            .like(Company::getShortNameChn, companyVo.getKeyWord()));
        }
        if (GeneralTool.isNotEmpty(companyVo.getFkParentCompanyId())) {
            wrapper.eq(Company::getFkParentCompanyId, companyVo.getFkParentCompanyId());
        }
        wrapper.orderByDesc(Company::getViewOrder);

        List<Company> companies = this.list(wrapper);
        List<CompanyVo> companyVos = BeanCopyUtils.copyListProperties(companies, CompanyVo::new);
        return companyVos;
    }

    @Override
    public List<CompanyVo> getAllCompanys(Long companyId) {
//        Example example = new Example(Company.class);
//        Example.Criteria criteria = example.createCriteria();
//        //获取公司
//        if (GeneralTool.isEmpty(companyId)) {
//            List<Long> companyIds = getCompanyIds();
//            criteria.andIn("id", companyIds);
//        } else {
//            SecureUtil.validateCompany(companyId);
//            criteria.andEqualTo("id", companyId);
//        }
//        example.orderBy("viewOrder").asc();
//        List<Company> companies = companyMapper.selectByExample(example);
//        return companies.stream().map(company -> Tools.objClone(company, CompanyVo.class)).collect(Collectors.toList());
        LambdaQueryWrapper<Company> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isEmpty(companyId)) {
            wrapper.in(Company::getId, SecureUtil.getCompanyIdsByStaffId(GetAuthInfo.getStaffId()));
        } else {
            if (!SecureUtil.validateCompany(companyId)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
            }
            wrapper.eq(Company::getId, companyId);
        }
        wrapper.orderByDesc(Company::getViewOrder);
        List<Company> companies = this.list(wrapper);
        List<CompanyVo> companyVos = BeanCopyUtils.copyListProperties(companies, CompanyVo::new);
        return companyVos;
    }

    @Override
    public CompanyVo findCompanyById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
//        if(!SecureUtil.validateCompany(id))
//        {
//            throw new GetServiceException("没有权限查看此条数据");
//        }
//        Company company = companyMapper.selectByPrimaryKey(id);
        Company company = companyMapper.selectById(id);
//        CompanyVo companyVo = Tools.objClone(company, CompanyVo.class);
        CompanyVo companyVo = BeanCopyUtils.objClone(company, CompanyVo::new);//待测试
        if (GeneralTool.isNotEmpty(companyVo)) {
            MediaAndAttachedVo companyIconImg = getCompanyIconImg(companyVo.getId());
            companyVo.setCompanyIcon(companyIconImg);
        }

        return companyVo;
    }

    @Override
    public MediaAndAttachedVo getCompanyLogo(Long id) {
        if (Objects.isNull(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        return getCompanyIconImg(id);
    }

    @Override
    public CompanyVo findStaffIdSupervisorCompanyById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        Company company = companyMapper.selectById(id);
        CompanyVo companyVo = BeanCopyUtils.objClone(company, CompanyVo::new);
        if (GeneralTool.isNotEmpty(companyVo)) {
            MediaAndAttachedVo companyIconImg = getCompanyIconImg(companyVo.getId());
            companyVo.setCompanyIcon(companyIconImg);
        }
        return companyVo;
    }

    @Override
    public List<CompanyVo> findCompanyByParentId(Long parentId) {
        if (GeneralTool.isEmpty(parentId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parent_id_null"));
        }
//        Example example = new Example(Company.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkParentCompanyId", parentId);
//        List<Company> companyList = companyMapper.selectByExample(example);
//        return companyList.stream().map(company -> Tools.objClone(company, CompanyVo.class)).collect(Collectors.toList());
        List<Company> companyList = this.list(Wrappers.<Company>query().lambda().eq(Company::getFkParentCompanyId, parentId));
        return BeanCopyUtils.copyListProperties(companyList, CompanyVo::new);
    }

    @Override
    public Long addCompany(CompanyDto companyDto) {
        if (GeneralTool.isEmpty(companyDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        if (!validateChange(companyDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("num_exist"));
        }
        if (companyDto.getFkParentCompanyId() == 0) {
            Boolean topCompany = companyMapper.getTopCompany();
            if (topCompany) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("topCompany_exist"));
            }
        }
//        Company company = Tools.objClone(companyDto, Company.class);
        Company company = BeanCopyUtils.objClone(companyDto, Company::new);
        Integer maxViewOrder = companyMapper.getMaxViewOrder();
        company.setViewOrder(maxViewOrder);

        utilService.updateUserInfoToEntity(company);
        int i = companyMapper.insertSelective(company);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
        //查询出所有父公司旗下的员工ids,并且更新缓存信息 因为父公司的员工都可以看得到新增的这家公司
        List<Long> ids = staffMapper.getStaffIdsByCompanyId(companyMapper.getParentCompanyIds(companyDto.getFkParentCompanyId()));
        if (GeneralTool.isNotEmpty(ids)) {
            updateStaffCompanyIds(ids);
        }
        //设置公司图标
        setMediaTableId(companyDto.getFkMediaId(), company.getId());
        return company.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CompanyVo updateCompanyVo(CompanyDto companyDto) {
        if (GeneralTool.isEmpty(companyDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if (GeneralTool.isEmpty(companyDto.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        Long aLong = validateUpdate(companyDto);
        if (!companyDto.getId().equals(aLong)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("num_exist"));
        }
//        Company company = Tools.objClone(companyDto, Company.class);
//        utilService.setUpdateInfo(company);
//        companyMapper.updateByPrimaryKeySelective(company);
        Company company = BeanCopyUtils.objClone(companyDto, Company::new);
        utilService.updateUserInfoToEntity(company);
        this.updateById(company);

        setMediaTableId(companyDto.getFkMediaId(), companyDto.getId());
        return findCompanyById(companyDto.getId());
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        deleteService.deleteValidateCompany(id);
//        int i = companyMapper.deleteByPrimaryKey(id);
        int i = companyMapper.deleteById(id);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
    }

    @Override
    public List<Long> getChildCompany(Long id) {
        List<Long> ids = new ArrayList<>();
        List<Long> companyIds = new ArrayList<>();
        ids.add(id);
        companyIds.add(id);
        //判断是否需要过滤该权限
        List<Long> comcanyLimit = getCompanyLimit(id);
        Long staffId = SecureUtil.getStaffId();
        if (GeneralTool.isNotEmpty(staffId) && GeneralTool.isNotEmpty(comcanyLimit) && comcanyLimit.contains(staffId)){
            return ids;
        }
        getAllChildCompany(ids, companyIds);
        return ids;
    }

    @Override
    public List<Long> getHtiAndChildCompany() {
        List<Long> ids = new ArrayList<>();
        LambdaQueryWrapper<Company>queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Company::getNum, "HTI");
        Company company = companyMapper.selectOne(queryWrapper);
        if (GeneralTool.isNotEmpty(company)){
            LambdaQueryWrapper<Company> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(Company::getFkParentCompanyId, company.getId());
           List<Company> companyList = companyMapper.selectList(wrapper);
           Set<Long> companyIdSet = companyList.stream().map(Company::getId).collect(Collectors.toSet());
           ids.addAll(companyIdSet);
        }
        ids.add(company.getId());
        return ids;
    }

    private void getAllChildCompany(List<Long> ids, List<Long> companyIds) {
        companyIds = companyMapper.getAllChildCompanyId(companyIds);
        if (GeneralTool.isNotEmpty(companyIds)) {
            if (Collections.disjoint(ids, companyIds)) {
                ids.addAll(companyIds);
                getAllChildCompany(ids, companyIds);
            }
        }
    }

    @Override
    public List<CompanyTreeVo> getAllCompanyDto() {
        List<Company> companies = this.list();
        companies.sort(Comparator.comparing(Company::getViewOrder).reversed());
//        return companies.stream().map(company -> Tools.objClone(company, CompanyTreeVo.class)).collect(Collectors.toList());
        return BeanCopyUtils.copyListProperties(companies, CompanyTreeVo::new);
    }

    @Override
    public List<CompanyTreeVo> getAllCompanyDto(Long companyId) {
        List<CompanyVo> companys = getAllCompanys(companyId);
//        List<CompanyTreeVo> collect =
//                companys.stream().map(companyDto -> Tools.objClone(companyDto, CompanyTreeVo.class)).collect(Collectors.toList());

//        List<CompanyTreeVo> collect =
//                BeanCopyUtils.copyListProperties(companys,CompanyTreeVo::new);

        List<CompanyTreeVo> collect = BeanCopyUtils.copyListProperties(companys, CompanyTreeVo::new, (companyDto, companyTreeDto) -> {
            // 这里可以定义特定的转换规则
            companyTreeDto.setId(companyDto.getId());
        });

        return collect;
    }


    @Override
    public CompanyVo getChildCompanyDto() {
        Company company = companyMapper.selectById(SecureUtil.getFkCompanyId());
        if (GeneralTool.isEmpty(company)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
//        CompanyVo companyVo = Tools.objClone(company, CompanyVo.class);
        CompanyVo companyVo = BeanCopyUtils.objClone(company, CompanyVo::new);
        List<Long> comcanyLimit = getCompanyLimit(company.getId());
        if (GeneralTool.isNotEmpty(comcanyLimit) &&comcanyLimit.contains(SecureUtil.getStaffId())){
            return companyVo;
        }
        setChildCompanyDto(companyVo);
        List<StaffCompany> staffCompanies = staffCompanyMapper.selectList(Wrappers.<StaffCompany>lambdaQuery().eq(StaffCompany::getFkStaffId, SecureUtil.getStaffId()));
        if (GeneralTool.isNotEmpty(staffCompanies)) {
            StaffCompany parentStaffCompany = staffCompanies.stream().filter(staffCompany -> staffCompany.getNodeMode().equals(0)).findFirst().orElse(null);
            List<StaffCompany> childCompanyList = staffCompanies.stream().filter(staffCompany -> staffCompany.getNodeMode().equals(1)).collect(Collectors.toList());
            if (parentStaffCompany != null) {
                CompanyVo parentCompanyVo = BeanCopyUtils.objClone(companyMapper.selectById(parentStaffCompany.getFkCompanyId()), CompanyVo::new);
                List<CompanyVo> childCompanyVo = new ArrayList<>();
                childCompanyVo.add(companyVo);
                if (GeneralTool.isNotEmpty(childCompanyList)) {
                    List<Company> companyList = companyMapper.selectBatchIds(childCompanyList.stream().map(StaffCompany::getFkCompanyId).collect(Collectors.toList()));
                    companyList.forEach(childCompany -> {
                        CompanyVo companyVo12 = BeanCopyUtils.objClone(childCompany, CompanyVo::new);
                        childCompanyVo.add(companyVo12);
                    });
                }
                parentCompanyVo.setChildCompanyDto(childCompanyVo);
                return parentCompanyVo;
            } else if (GeneralTool.isNotEmpty(childCompanyList)) {
                List<Company> companyList = companyMapper.selectBatchIds(childCompanyList.stream().map(StaffCompany::getFkCompanyId).collect(Collectors.toList()));
                List<CompanyVo> childCompanyVoList = companyVo.getChildCompanyDto();
                companyList.forEach(childCompany -> {
                    CompanyVo companyVo12 = BeanCopyUtils.objClone(childCompany, CompanyVo::new);
                    childCompanyVoList.add(companyVo12);
                });
                companyVo.setChildCompanyDto(childCompanyVoList);
            }

        }
        return companyVo;
    }

    private CompanyVo setChildCompanyDto(CompanyVo companyVo) {
        List<CompanyVo> companyVos = companyMapper.getChildCompany(companyVo.getId());
        companyVo.setChildCompanyDto(companyVos);
        if (GeneralTool.isNotEmpty(companyVos)) {
            for (CompanyVo cd : companyVos) {
                setChildCompanyDto(cd);
            }
        }
        return companyVo;
    }

    @Override
    public String getCompanyNameById(Long id) {
//        Company company = companyMapper.selectByPrimaryKey(id);
        Company company = companyMapper.selectById(id);
        if (GeneralTool.isEmpty(company)) {
            return null;
        }
        return company.getShortName();
    }

    @Override
    public Map<Long, String> getCompanyNamesByIds(Set<Long> companyIds) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(companyIds)) {
            return map;
        }
//        Example example = new Example(Company.class);
//        example.createCriteria().andIn("id",companyIds);
//        List<Company> companies = companyMapper.selectByExample(example);
        List<Company> companies = this.list(Wrappers.<Company>query().lambda().in(Company::getId, companyIds));
        companies.sort(Comparator.comparing(Company::getViewOrder).reversed());
        if (GeneralTool.isEmpty(companies)) {
            return map;
        }
        for (Company company : companies) {
            map.put(company.getId(), company.getShortName());
        }
        return map;
    }

    @Override
    public List<String> getCompanyNamesByIdsDESC(Set<Long> companyIds) {
        if (GeneralTool.isEmpty(companyIds)) {
            return Collections.emptyList();
        }
        List<Company> companies = this.list(Wrappers.<Company>query().lambda().in(Company::getId, companyIds).orderByDesc(Company::getViewOrder));
        if (GeneralTool.isEmpty(companies)) {
            return Collections.emptyList();
        }
        List<String> companyNames = companies.stream().map(Company::getShortName).collect(Collectors.toList());
        return companyNames;
    }

    @Override
    public Map<Long, String> getCompanyFullNamesByIds(Set<Long> companyIds) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(companyIds)) {
            return map;
        }
        List<Company> companies = this.list(Wrappers.<Company>query().lambda().in(Company::getId, companyIds));
        if (GeneralTool.isEmpty(companies)) {
            return map;
        }
        for (Company company : companies) {
            map.put(company.getId(), company.getName());
        }
        return map;
    }

    @Override
    public List<Long> getCompanyIdByName(String keyWord) {
        if (GeneralTool.isNotEmpty(keyWord)) {
            return companyMapper.getCompanyIdByName(keyWord);
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void movingOrder(List<CompanyDto> companyDtoList) {
        if (GeneralTool.isEmpty(companyDtoList)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
//        Company company1 = Tools.objClone(companyDtoList.get(0), Company.class);
//        Company company2 = Tools.objClone(companyDtoList.get(1), Company.class);
        Company company1 = BeanCopyUtils.objClone(companyDtoList.get(0), Company::new);
        Company company2 = BeanCopyUtils.objClone(companyDtoList.get(1), Company::new);
        Integer viewOrder = company1.getViewOrder();
        company1.setViewOrder(company2.getViewOrder());
        company2.setViewOrder(viewOrder);
//        utilService.setUpdateInfo(company1);
//        utilService.setUpdateInfo(company2);
        utilService.updateUserInfoToEntity(company1);
        utilService.updateUserInfoToEntity(company2);
//        companyMapper.updateByPrimaryKeySelective(company1);
//        companyMapper.updateByPrimaryKeySelective(company2);
        companyMapper.updateById(company1);
        companyMapper.updateById(company2);
    }

    @Override
    public List<CompanyTreeVo> getParentCompany(Long companyId) {
        if (GeneralTool.isEmpty(companyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("company_id_null"));
        }
        if (!SecureUtil.validateCompany(companyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
        }
        List<Long> parentCompanyIds = companyMapper.getParentCompany(companyId);

//        Example example = new Example(Company.class);
//        Example.Criteria criteria = example.createCriteria();
//        parentCompanyIds.add(companyId);
//
//        criteria.andNotIn("id", parentCompanyIds);
//
//        example.orderBy("viewOrder").asc();
//        List<Company> companies = companyMapper.selectByExample(example);

        parentCompanyIds.add(companyId);
        List<Company> companies = this.list(Wrappers.<Company>query().lambda().in(Company::getId, parentCompanyIds).orderByDesc(Company::getViewOrder));
        List<CompanyTreeVo> collect = BeanCopyUtils.copyListProperties(companies, CompanyTreeVo::new);
        if (GeneralTool.isEmpty(collect)) {
            return null;
        }
        return getTreeList(collect);
    }

    @Override
    public List<MediaAndAttachedVo> addCompanyMedia(ValidList<MediaAndAttachedDto> mediaAttachedVos) {
        if (GeneralTool.isEmpty(mediaAttachedVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        if (mediaAttachedVos.get(0).getTypeKey().equals(FileTypeEnum.PERMISSION_COMPANY_ICON.key)) {
            if (GeneralTool.isNotEmpty(mediaAttachedVos.get(0).getFkTableId())) {
                MediaAndAttachedDto mediaAndAttached = new MediaAndAttachedDto();
                mediaAndAttached.setFkTableName(mediaAttachedVos.get(0).getFkTableName());
                mediaAndAttached.setFkTableId(mediaAttachedVos.get(0).getFkTableId());
                mediaAndAttached.setTypeKey(mediaAttachedVos.get(0).getTypeKey());
                attachedService.delete(mediaAndAttached);
            }
        }
        List<MediaAndAttachedVo> mediaAndAttachedVos = new ArrayList<>();
        for (MediaAndAttachedDto mediaAndAttachedDto : mediaAttachedVos) {
            //设置插入的表
            mediaAndAttachedDto.setFkTableName(TableEnum.PERMISSION_COMPANY.key);
            mediaAndAttachedVos.add(attachedService.addMediaAndAttached(mediaAndAttachedDto));
        }
        return mediaAndAttachedVos;
    }

    @Override
    public MediaAndAttachedVo getCompanyIcon(Long fkCompanyId) {
        if (GeneralTool.isEmpty(fkCompanyId)) {
            return null;
        }
        MediaAndAttachedDto attachedVo = new MediaAndAttachedDto();
        attachedVo.setFkTableName(TableEnum.PERMISSION_COMPANY.key);
        attachedVo.setFkTableId(fkCompanyId);
        attachedVo.setTypeKey(FileTypeEnum.PERMISSION_COMPANY_ICON.key);

        List<MediaAndAttachedVo> mediaAndAttachedVo = attachedService.getMediaAndAttachedDto(attachedVo, new Page());
        if (GeneralTool.isEmpty(mediaAndAttachedVo)) {
            return null;
        }
        return mediaAndAttachedVo.get(0);
    }


    @Override
    public List<CompanyTreeVo> getCompanyTreeDto(Long companyId) {
        return getTreeList(getAllCompanyDto(companyId));
    }

    @Override
    public List<CompanyTreeVo> getFilterCompanyTree(Long companyId) {
        if (GeneralTool.isEmpty(companyId)) {
            return null;
        }
        List<CompanyTreeVo> entityList = getAllCompanyDto(null);
        if (GeneralTool.isEmpty(entityList)) {
            return null;
        }
        //构造树结构
        List<CompanyTreeVo> resultList = new ArrayList<>();
        // 获取顶层元素集合
        String parentId;
        for (CompanyTreeVo entity : entityList) {
            parentId = String.valueOf(entity.getFkParentCompanyId());
            if (parentId == null || "0".equals(parentId)) {
                //最顶级的公司节点
                resultList.add(entity);
            }
        }
        //假如没有父节点，就是说 所有公司都是处于平级的情况，没有父级公司
        if (GeneralTool.isEmpty(resultList)) {
            //获取最小节点，就是最小的公司id 当成最大的公司级别
            CompanyTreeVo minTreeNode = entityList.stream().min(Comparator.comparing(CompanyTreeVo::getFkParentCompanyId)).get();
            resultList.add(minTreeNode);
            if (GeneralTool.isNotEmpty(minTreeNode)) {
                //获取相同的最小节点，把平级的公司都放进去List.然后分别获取各个平级公司的子公司
                List<CompanyTreeVo> minTreeNodes = entityList.stream().filter(treeDto ->
                        treeDto.getFkParentCompanyId().equals(minTreeNode.getFkParentCompanyId()) &&
                                treeDto.getId().longValue() != (minTreeNode.getId().longValue())).distinct().collect(Collectors.toList());
                resultList.addAll(minTreeNodes);
            }
        }
        Iterator<CompanyTreeVo> iterator = resultList.iterator();
        while (iterator.hasNext()) {
            CompanyTreeVo dto = iterator.next();
            if (companyId.equals(dto.getId())) {
                iterator.remove();
            }
        }
        if (GeneralTool.isNotEmpty(resultList)) {
            // 获取每个顶层元素的子数据集合
            for (CompanyTreeVo entity : resultList) {
                entity.setChildList(getSubList(entity.getId(), entityList, companyId));
            }
        }
        return resultList;
    }

    @Override
    public List<BaseSelectEntity> fuzzySearchCompanyName(String keyword,Long companyId) {
        if (StringUtils.isNotBlank(keyword)) {
            return companyMapper.fuzzGetCompanyInfo(keyword.replace(" ", "").trim(),companyId);
        }
        return null;
    }

    @Override
    public List<Long> getCompanyLimit(Long companyId){
        Map<Long, String> companyConfigMap = getCompanyConfigMap(ProjectKeyEnum.COMPANY_LIMIT.key, 1);
        String configValue1 = companyConfigMap.get(companyId);
        return new ArrayList<>(JSON.parseArray(configValue1, Long.class));
//        ConfigVo configByKey = configService.getConfigByKey(ProjectKeyEnum.COMPANY_LIMIT.key);
//        if (GeneralTool.isNotEmpty(configByKey) && GeneralTool.isNotEmpty(configByKey.getValue1())){
//            JSONObject jsonObject = JSONObject.parseObject(configByKey.getValue1());
//            JSONArray otherConfigs = jsonObject.getJSONArray("OTHER");
//            JSONArray iaeConfigs = jsonObject.getJSONArray("IAE");
//            List<Long> otherConfigList = otherConfigs.toJavaList(Long.class);
//            List<Long> iaeConfigList = iaeConfigs.toJavaList(Long.class);
//            if (companyId.equals(3L)){
//                return iaeConfigList;
//            }else{
//                return otherConfigList;
//            }
//        }
//        return null;
    }

    /**
     * 获取系统参数Map 如果返回为空，只表示不生效
     * @param configKey 系统参数key
     * @param value int类型，  1 则取value1的值
     * @return 公司id - 参数
     */
    @Override
    public Map<Long, String> getCompanyConfigMap(String configKey, int value) {
        Config config = configMapper.selectOne(Wrappers.<Config>query().lambda().eq(Config::getConfigKey, configKey));
        String valueStr = null;
        if (value == 1) {
            valueStr = config.getValue1();
        } else if (value == 2) {
            valueStr = config.getValue2();
        } else if (value == 3) {
            valueStr = config.getValue3();
        } else if (value == 4) {
            valueStr = config.getValue4();
        }

        Map<Long, String> configMap = new HashMap<>();
        cn.hutool.json.JSONObject configJSON = JSONUtil.parseObj(valueStr);
        List<CompanyTreeVo> allCompanyDto = getAllCompanyDto();
        for (CompanyTreeVo companyTreeVo : allCompanyDto) {
            String configValue = configJSON.getStr(companyTreeVo.getNum());
            if (companyTreeVo.getId()==3 && GeneralTool.isNotEmpty(configJSON.getStr("IAE"))) {
                configValue = configJSON.getStr("IAE");
            }
            if (GeneralTool.isEmpty(configValue)) {
                configValue = configJSON.getStr("OTHER");
            }
            configMap.put(companyTreeVo.getId(), configValue);
        }
        return configMap;
    }

    /**
     * 获取公司配置key解析对象
     *
     * @Date 12:53 2024/5/13
     * <AUTHOR>
     */
    @Override
    public Map<Long, CompanyConfigAnalysisVo> getCompanyConfigAnalysis(String configKey) {

        Map<Long, CompanyConfigAnalysisVo> map = new HashMap<>();

        List<CompanyTreeVo> allCompanyDto = getAllCompanyDto();

        Config config = configMapper.selectOne(Wrappers.<Config>query().lambda().eq(Config::getConfigKey, configKey));
        String value1 = config.getValue1();
        String value2 = config.getValue2();
        String value3 = config.getValue3();
        String value4 = config.getValue4();

        for (CompanyTreeVo companyTreeVo : allCompanyDto) {
            CompanyConfigAnalysisVo companyConfigAnalysisVo = new CompanyConfigAnalysisVo();
            if (GeneralTool.isNotEmpty(value1)) {
                String configValue = getConfigValue(companyTreeVo, value1);
                companyConfigAnalysisVo.setValue1(configValue);
            }
            if (GeneralTool.isNotEmpty(value2)) {
                String configValue = getConfigValue(companyTreeVo, value2);
                companyConfigAnalysisVo.setValue2(configValue);
            }
            if (GeneralTool.isNotEmpty(value3)) {
                String configValue = getConfigValue(companyTreeVo, value3);
                companyConfigAnalysisVo.setValue3(configValue);
            }
            if (GeneralTool.isNotEmpty(value4)) {
                String configValue = getConfigValue(companyTreeVo, value4);
                companyConfigAnalysisVo.setValue4(configValue);
            }
            map.put(companyTreeVo.getId(), companyConfigAnalysisVo);
        }
        return map;
    }

    /**
     * 获取对应公司的系统配置value
     *
     * @Date 18:24 2024/5/15
     * <AUTHOR>
     */
    private static String getConfigValue(CompanyTreeVo companyTreeVo, String value) {

        // 验证输入是否为有效的JSON格式
        if (!StrUtil.isEmpty(value) && !JSONUtil.isJson(value)) {
            return value;
        }
        cn.hutool.json.JSONObject configJSON = JSONUtil.parseObj(value);
        String configValue = configJSON.getStr(companyTreeVo.getNum());
        if (companyTreeVo.getId()==3 && GeneralTool.isNotEmpty(configJSON.getStr("IAE"))) {
            configValue = configJSON.getStr("IAE");
        }
        if (GeneralTool.isEmpty(configValue)) {
            configValue = configJSON.getStr("OTHER");
        }
        return configValue;
    }

    private List<CompanyTreeVo> getTreeList(List<CompanyTreeVo> entityList) {
        if (GeneralTool.isEmpty(entityList)) {
            return null;
        }
        List<CompanyTreeVo> resultList = new ArrayList<>();
        // 获取顶层元素集合
        String parentId;
        for (CompanyTreeVo entity : entityList) {
            parentId = String.valueOf(entity.getFkParentCompanyId());
            if (parentId == null || "0".equals(parentId)) {
                //最顶级的公司节点
                resultList.add(entity);
            }
        }
        //假如没有父节点，就是说 所有公司都是处于平级的情况，没有父级公司
        if (GeneralTool.isEmpty(resultList)) {
            //获取最小节点，就是最小的公司id 当成最大的公司级别
            CompanyTreeVo minTreeNode = entityList.stream().min(Comparator.comparing(CompanyTreeVo::getFkParentCompanyId)).get();
            resultList.add(minTreeNode);
            if (GeneralTool.isNotEmpty(minTreeNode)) {
                //获取相同的最小节点，把平级的公司都放进去List.然后分别获取各个平级公司的子公司
                List<CompanyTreeVo> minTreeNodes = entityList.stream().filter(treeDto ->
                        treeDto.getFkParentCompanyId().equals(minTreeNode.getFkParentCompanyId()) &&
                                treeDto.getId().longValue() != (minTreeNode.getId().longValue())).distinct().collect(Collectors.toList());
                resultList.addAll(minTreeNodes);
            }
        }
        // 获取每个顶层元素的子数据集合
        for (CompanyTreeVo entity : resultList) {
            entity.setChildList(getSubList(entity.getId(), entityList, null));
        }
        return resultList;
    }

    private List<CompanyTreeVo> getSubList(Long id, List<CompanyTreeVo> entityList, Long companyId) {
        List<CompanyTreeVo> childList = new ArrayList<>();
//        String parentId;
        // 子集的直接子对象
        for (CompanyTreeVo entity : entityList) {
//            parentId = String.valueOf(entity.getFkParentCompanyId());
            if (id.longValue() == entity.getFkParentCompanyId()) {
                //获取子节点的部门信息
                childList.add(entity);
            }
        }
        if (GeneralTool.isNotEmpty(companyId)) {
            Iterator<CompanyTreeVo> iterator = childList.iterator();
            while (iterator.hasNext()) {
                CompanyTreeVo dto = iterator.next();
                if (companyId.equals(dto.getId())) {
                    iterator.remove();
                }
            }

        }
        // 子集的间接子对象
        for (CompanyTreeVo entity : childList) {
            //递归调用
            entity.setChildList(getSubList(entity.getId(), entityList, companyId));
        }
        // 递归退出条件
        if (childList.size() == 0) {
            return null;
        }
        return childList;
    }


    //判断新增是否存在
    private boolean validateChange(CompanyDto companyDto) {
//        Example example = new Example(Company.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("num", companyDto.getNum());
//        List<Company> companies = companyMapper.selectByExample(example);
        List<Company> companies = this.list(Wrappers.<Company>query().lambda().eq(Company::getNum, companyDto.getNum()));
        return GeneralTool.isEmpty(companies);
    }

    //编辑校验是否存在
    private Long validateUpdate(CompanyDto companyDto) {
//        Example example = new Example(Company.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("num", companyDto.getNum());
//        criteria.andEqualTo("fkParentCompanyId", companyDto.getFkParentCompanyId());
//        List<Company> companies = companyMapper.selectByExample(example);
        List<Company> companies = this.list(Wrappers.<Company>query().lambda().eq(Company::getNum, companyDto.getNum()).eq(Company::getId, companyDto.getId()));
        return GeneralTool.isNotEmpty(companies) ? companies.get(0).getId() : companyDto.getId();
    }

    private void updateStaffCompanyIds(List<Long> staffIds) {
        for (Long staffId : staffIds) {
            Staff staff = staffMapper.selectById(staffId);
            if (staff != null) {
                List<Long> companyIds = this.getChildCompany(staff.getFkCompanyId());
                //更新当前用户的redis缓存
                SecureUtil.updateCompanyIdsByStaffId(staff.getId(), companyIds);
            }
        }
    }

    private void setMediaTableId(Long fkMediaId, Long returnId) {
        if (GeneralTool.isNotEmpty(fkMediaId)) {
            attachedService.updateTableId(fkMediaId, returnId);
        }
    }

    //获取头像下载链接
    private MediaAndAttachedVo getCompanyIconImg(Long companyId) {
        MediaAndAttachedDto attachedVo = new MediaAndAttachedDto();
        attachedVo.setFkTableName(TableEnum.PERMISSION_COMPANY.key);
        attachedVo.setFkTableId(companyId);
        attachedVo.setTypeKey(FileTypeEnum.PERMISSION_COMPANY_ICON.key);

        List<MediaAndAttachedVo> headIcon = attachedService.getMediaAndAttachedDto(attachedVo);
        if (GeneralTool.isNotEmpty(headIcon)) {
            return headIcon.get(0);
        }
        return null;
    }
}

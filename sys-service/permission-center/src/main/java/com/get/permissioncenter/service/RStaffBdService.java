package com.get.permissioncenter.service;

import com.get.common.result.SearchBean;
import com.get.permissioncenter.dto.RStaffBdDto;
import com.get.salecenter.dto.StaffBdCodeDto;
import com.get.salecenter.vo.StaffBdCodeVo;
import java.util.List;

/**
 * 员工绑定bd
 */
public interface RStaffBdService {

    List<Long> addStaffBd(RStaffBdDto rStaffBdDto);

    /**
     * 根据id删除
     * @param id
     */
    void removeById(Long id);

    /**
     * @param rStaffBdDto
     * 批量删除
     */
    void batchDelete(RStaffBdDto rStaffBdDto);

    List<StaffBdCodeVo> dataList(SearchBean<StaffBdCodeDto> page);
}


package com.get.permissioncenter.service;

import com.get.common.result.Page;
import com.get.permissioncenter.dto.workbench.WorkbenchApprovalDto;
import com.get.permissioncenter.vo.workbench.WorkbenchApprovalVo;

import java.util.List;

/**
 * @Author:<PERSON>
 * @Date: 2025/4/14
 * @Version 1.0
 * @apiNote:
 */
public interface WorkbenchService {

    List<WorkbenchApprovalVo> getWorkbenchApprovalList(WorkbenchApprovalDto params, Page page);
}

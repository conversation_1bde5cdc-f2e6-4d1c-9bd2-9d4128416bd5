package com.get.permissioncenter.controller;


import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.eunms.TableEnum;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.UpdateResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.dto.CompanyDto;
import com.get.permissioncenter.dto.MediaAndAttachedDto;
import com.get.permissioncenter.vo.CompanyVo;
import com.get.permissioncenter.vo.MediaAndAttachedVo;
import com.get.permissioncenter.vo.tree.CompanyTreeVo;
import com.get.permissioncenter.service.ICompanyService;
import com.get.permissioncenter.dto.query.CompanyQueryDto;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;


@Api(tags = "公司管理")
@RestController
@RequestMapping("permission/company")
public class CompanyController {
    @Resource
    private ICompanyService companyService;

    //    @VerifyPermission(IsVerify = false)
//    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "列表数据", notes = "keyWord为关键词")
    @ApiOperationSupport(order = 1)
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.LIST, description = "权限中心/公司管理/查询公司")
    @PostMapping("datas")
    public ResponseBo<CompanyVo> datas(@RequestBody CompanyQueryDto companyVo) {
        List<CompanyVo> companys = companyService.getCompanys(companyVo);
        return new ListResponseBo<>(companys);
    }

    /**
     * 详情
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "权限中心/公司管理/参数详情")
    @GetMapping("/{id}")
    public ResponseBo<CompanyVo> detail(@PathVariable("id") Long id) {
        CompanyVo companyVo = companyService.findCompanyById(id);
        return new ResponseBo<>(companyVo);
    }


    @ApiOperation(value = "获取公司logo", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "权限中心/公司管理/获取公司logo")
    @VerifyLogin(IsVerify = false)
    @VerifyPermission(IsVerify = false)
    @GetMapping("/getCompanyLogo")
    public ResponseBo<MediaAndAttachedVo> getCompanyLogo(@RequestParam("companyId") Long companyId){
        return new ResponseBo<>(companyService.getCompanyLogo(companyId));
    }

    /**
     * 新增信息
     *
     * @param companyDto
     * @return
     */
    @ApiOperation(value = "新增接口")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.ADD, description = "权限中心/公司管理/新增参数")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(CompanyDto.Add.class) CompanyDto companyDto) {
        return SaveResponseBo.ok(companyService.addCompany(companyDto));
    }

    /**
     * 修改信息
     *
     * @param companyDto
     * @return
     */
    @ApiOperation(value = "修改接口")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.EDIT, description = "权限中心/公司管理/更新参数")
    @PostMapping("update")
    public ResponseBo<CompanyVo> update(@RequestBody @Validated(CompanyDto.Update.class) CompanyDto companyDto) {
        return UpdateResponseBo.ok(companyService.updateCompanyVo(companyDto));
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "删除接口", notes = "id为删除数据的ID")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.DELETE, description = "权限中心/公司管理/删除资源")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        companyService.delete(id);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 上移下移
     * @Param [companyDtoList]
     * <AUTHOR>
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "权限中心/公司管理/排序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<CompanyDto> companyDtoList) {
        companyService.movingOrder(companyDtoList);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "保存公司附件接口")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.ADD, description = "权限中心/公司管理/保存附件")
    @PostMapping("upload")
    public ResponseBo upload(@RequestBody @Validated(MediaAndAttachedDto.Add.class) ValidList<MediaAndAttachedDto> mediaAttachedVo) {
        return new ListResponseBo<>(companyService.addCompanyMedia(mediaAttachedVo));
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "公司树形数据结构")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.LIST, description = "权限中心/公司管理/公司结构树")
    @GetMapping("/getCompanyTree")
    public ResponseBo<CompanyTreeVo> getCompanyTreeDto(@RequestParam(name = "companyId", required = false) Long companyId) {
        return new ListResponseBo<>(companyService.getCompanyTreeDto(companyId));
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "公司树形数据结构(排除当前公司，以及其子级)")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.LIST, description = "权限中心/公司管理/公司结构树(排除当前公司)")
    @GetMapping("/getFilterCompanyTree")
    public ResponseBo<CompanyTreeVo> getFilterCompanyTree(@RequestParam(name = "companyId", required = false) Long companyId) {
        return new ListResponseBo<>(companyService.getFilterCompanyTree(companyId));
    }

    @ApiOperation(value = "获取所有公司")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.LIST, description = "权限中心/公司管理/公司列表")
    @ApiIgnore
    @GetMapping("/getAllCompanyDto")
    public ResponseBo<CompanyTreeVo> getAllCompanyDto() {
        return new ListResponseBo<>(companyService.getAllCompanyDto());
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "根据公司Id查询公司名称")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.LIST, description = "权限中心/公司管理/公司列表")
    @GetMapping("/getCompanyNameById")
    public ResponseBo getCompanyName(@RequestParam(name = "companyId") Long companyId) {
        return new ResponseBo<>(companyService.getCompanyNameById(companyId));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 通过公司id查询上级公司（不包含自己级下级）
     * @Param [companyId]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "通过公司id查询上级公司")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.LIST, description = "权限中心/公司管理/通过公司id查询上级公司")
    @PostMapping("getParentCompany")
    public ResponseBo<CompanyTreeVo> getParentCompany(@RequestParam(name = "companyId") Long companyId) {
        return new ListResponseBo<>(companyService.getParentCompany(companyId));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.permissioncenter.vo.CompanyVo>
     * @Description: 获取所属公司下拉框
     * @Param []
     * <AUTHOR>
     **/
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "获取所属公司下拉框")
    @GetMapping("/getCompanySelectDto")
    public ResponseBo<CompanyVo> getCompanySelectDto() {
        return new ResponseBo<>(companyService.getChildCompanyDto());
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "获取大区配置所属公司下拉框")
    @GetMapping("/getCompanySelectDtos")
    public ResponseBo<List<CompanyVo>> getCompanySelectDtos() {
        List<CompanyVo> list =new ArrayList<>();
        list.add(companyService.getChildCompanyDto());
        CompanyVo dto = new CompanyVo();
        dto.setName(TableEnum.GEOGRAPHICAL_DIVISION.value);
        dto.setNameChn(TableEnum.GEOGRAPHICAL_DIVISION.value);
        dto.setShortName(TableEnum.GEOGRAPHICAL_DIVISION.value);
        dto.setShortNameChn(TableEnum.GEOGRAPHICAL_DIVISION.value);
        dto.setViewOrder(1);
        dto.setId(-1L);
        list.add(dto);
        return new ResponseBo<>(list);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 通过公司id获取自己及下属公司id数组
     * @Param [companyId]
     * <AUTHOR>
     **/
    @ApiIgnore
    @PostMapping("getChildCompanyIds")
    public List<Long> getChildCompanyIds(@RequestParam(name = "companyId") Long companyId) {
        return companyService.getChildCompany(companyId);
    }

    /**
     * @return java.lang.String
     * @Description: 根据id查询公司名称
     * @Param [companyId]
     * <AUTHOR>
     **/
    @ApiIgnore
    @PostMapping("getCompanyName")
    public String getCompanyNameById(@RequestParam(name = "companyId") Long companyId) {
        return companyService.getCompanyNameById(companyId);
    }

    /**
     * @return java.util.Map<java.lang.Long, java.lang.String>
     * @Description :feign调用 根据ids查询公司名称map
     * @Param [companyIds]
     * <AUTHOR>
     */
    @ApiIgnore
    @PostMapping("getCompanyNamesByIds")
    public Map<Long, String> getCompanyNamesByIds(@RequestBody Set<Long> companyIds) {
        return companyService.getCompanyNamesByIds(companyIds);
    }

    @ApiIgnore
    @GetMapping("getCompanyIdByName")
    public List<Long> getCompanyIdByName(@RequestParam("keyWord") String keyWord) {
        return companyService.getCompanyIdByName(keyWord);
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.permissioncenter.vo.MediaAndAttachedDto>
     * @Description: 获取公司logo
     * @Param [voSearchBean]
     * <AUTHOR>
     */
    @ApiIgnore
    @GetMapping("getCompanyIcon")
    public ResponseBo<MediaAndAttachedVo> getCompanyIcon(@RequestParam("fkCompanyId") Long fkCompanyId) {
        MediaAndAttachedVo staffMedia = companyService.getCompanyIcon(fkCompanyId);
        return new ResponseBo<>(staffMedia);
    }

    @ApiOperation(value = "获取公司配置key解析对象")
    @VerifyPermission(IsVerify = false)
    @GetMapping("getCompanyConfigAnalysis")
    public ResponseBo getCompanyConfigAnalysis(@RequestParam("configKey") String configKey, @RequestParam(value = "fkCompanyId", required = false) Long fkCompanyId) {
        if (GeneralTool.isEmpty(fkCompanyId)) {
            fkCompanyId = SecureUtil.getFkCompanyId();
        }
        return new ResponseBo<>(companyService.getCompanyConfigAnalysis(configKey).get(fkCompanyId));
    }

}

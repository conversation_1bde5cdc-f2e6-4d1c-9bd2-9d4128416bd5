package com.get.permissioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.utils.ValidList;
import com.get.permissioncenter.dto.MediaAndAttachedDto;
import com.get.permissioncenter.dto.StaffHrEventDto;
import com.get.permissioncenter.vo.CommentVo;
import com.get.permissioncenter.vo.MediaAndAttachedVo;
import com.get.permissioncenter.vo.StaffHrEventVo;
import com.get.permissioncenter.service.IStaffHrEventService;
import com.get.permissioncenter.dto.CommentDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/7/13
 * @TIME: 16:23
 * @Description: 员工人事记录控制层
 **/

@Api(tags = "员工人事记录管理")
@RestController
@RequestMapping("permission/staffhrevent")
public class StaffHrEventController {
    @Resource
    private IStaffHrEventService hrEventService;

    /**
     * @return com.get.common.result.ResponseBo<com.get.permissioncenter.vo.StaffHrEventVo>
     * @Description: 员工人事记录列表
     * @Param [voSearchBean]
     * <AUTHOR>
     */
    @ApiOperation(value = "员工人事记录列表")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "权限中心/合同管理/员工人事记录")
    @PostMapping("/datas")
    public ResponseBo<StaffHrEventVo> datas(@RequestBody SearchBean<StaffHrEventDto> voSearchBean) {
        List<StaffHrEventVo> hrEventDtos = hrEventService.getStaffHrEventDto(voSearchBean.getData(), voSearchBean);
        return new ListResponseBo<>(hrEventDtos);
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.permissioncenter.vo.StaffHrEventVo>
     * @Description: 人事记录详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "人事记录详情", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "权限中心/人事记录管理/人事记录详情")
    @GetMapping("/{id}")
    public ResponseBo<StaffHrEventVo> detail(@PathVariable("id") Long id) {
        StaffHrEventVo staffHrEventVo = hrEventService.getStaffHrEventDtoById(id);
        return new ResponseBo<>(staffHrEventVo);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 新增信息
     * @Param [staffHrEventDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增接口")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.ADD, description = "权限中心/人事记录管理/新增参数")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(StaffHrEventDto.Add.class) StaffHrEventDto staffHrEventDto) {
        return SaveResponseBo.ok(hrEventService.addStaffHrEvent(staffHrEventDto));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.permissioncenter.vo.StaffHrEventVo>
     * @Description: 修改信息
     * @Param [staffHrEventDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.EDIT, description = "权限中心/人事记录管理/更新参数")
    @PostMapping("update")
    public ResponseBo<StaffHrEventVo> update(@RequestBody @Validated(StaffHrEventDto.Update.class) StaffHrEventDto staffHrEventDto) {
        return UpdateResponseBo.ok(hrEventService.updateStaffHrEventVo(staffHrEventDto));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 删除
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为删除数据的ID")
    @OperationLogger(module = LoggerModulesConsts.SYSTEMCENTER, type = LoggerOptTypeConst.DELETE, description = "权限中心/人事记录管理/删除资源")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        hrEventService.delete(id);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.permissioncenter.vo.MediaAndAttachedDto>
     * @Description: 保存人事事件附件接口
     * @Param [mediaAttachedVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "保存人事事件附件接口")
    @OperationLogger(module = LoggerModulesConsts.SYSTEMCENTER, type = LoggerOptTypeConst.ADD, description = "权限中心/人事记录管理/保存事件附件")
    @PostMapping("upload")
    public ResponseBo<MediaAndAttachedVo> upload(@RequestBody @Validated(MediaAndAttachedDto.Add.class) ValidList<MediaAndAttachedDto> mediaAttachedVo) {
        return UpdateResponseBo.ok(hrEventService.addHrEventMedia(mediaAttachedVo));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.permissioncenter.vo.MediaAndAttachedDto>
     * @Description: 查询人事事件附件接口
     * @Param [voSearchBean]
     * <AUTHOR>
     */
    @ApiOperation(value = "查询人事事件附件接口", notes = "keyWord为关键词")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.LIST, description = "权限中心/人事记录管理/查询附件")
    @PostMapping("getHrEventMedia")
    public ResponseBo<MediaAndAttachedVo> getHrEventMedia(@RequestBody SearchBean<MediaAndAttachedDto> voSearchBean) {
        List<MediaAndAttachedVo> staffMedia = hrEventService.getHrEventMedia(voSearchBean.getData(), voSearchBean);
        Page page = BeanCopyUtils.objClone(voSearchBean, Page::new);
        return new ListResponseBo<>(staffMedia, page);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 编辑评论
     * @Param [commentDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "编辑评论")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.EDIT, description = "权限中心/人事记录管理/编辑评论")
    @PostMapping("editComment")
    public ResponseBo editComment(@RequestBody @Validated(CommentDto.Add.class) CommentDto commentDto) {
        return SaveResponseBo.ok(hrEventService.editComment(commentDto));
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.permissioncenter.vo.CommentVo>
     * @Description: 评论列表数据
     * @Param [searchBean]
     * <AUTHOR>
     */
    @ApiOperation(value = "评论列表数据")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.LIST, description = "权限中心/人事记录管理/评论查询")
    @PostMapping("getComments")
    public ResponseBo<CommentVo> getComment(@RequestBody SearchBean<CommentDto> searchBean) {
        List<CommentVo> datas = hrEventService.getComments(searchBean.getData(), searchBean);
        Page p = BeanCopyUtils.objClone(searchBean, Page::new);
        return new ListResponseBo<>(datas, p);
    }


}

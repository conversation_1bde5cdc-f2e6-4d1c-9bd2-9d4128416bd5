package com.get.permissioncenter.controller;


import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.result.UpdateResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.permissioncenter.vo.StaffOfficeVo;
import com.get.permissioncenter.service.IStaffOfficeService;
import com.get.permissioncenter.dto.StaffOfficeBatchUpdateDto;
import com.get.permissioncenter.dto.StaffOfficeDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/7/24
 * @TIME: 15:59
 * @Description:
 **/


@Api(tags = "员工业务办公室管理")
@RestController
@RequestMapping("permission/staffOffice")
public class StaffOfficeController {

    @Autowired
    private IStaffOfficeService staffOfficeService;

    /**
     * 业务办公室列表 根据用户id查询
     *
     * @param staffId
     * @return
     */
    @ApiOperation(value = "员工业务办公室列表")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "权限中心/业务办公室管理/业务办公室列表")
    @GetMapping("/datas")
    public ResponseBo<StaffOfficeVo> detailList(@RequestParam(value = "fkCompanyId") Long fkCompanyId,
                                                @RequestParam(value = "staffId") Long staffId,
                                                @RequestParam(value = "keyWord", required = false) String keyWord) {
        List<StaffOfficeVo> staffOfficeById = staffOfficeService.getStaffOfficeById(fkCompanyId, staffId, keyWord);
        return new ListResponseBo<>(staffOfficeById);
    }

    /**
     * 修改信息
     *
     * @param staffId
     * @param staffAreaCountryVo
     * @return
     */
    @ApiOperation(value = "修改接口")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.EDIT, description = "权限中心/业务办公室管理/更新参数")
    @PostMapping("update")
    public ResponseBo update(@NotNull(message = "staffId不能为空") @RequestParam("staffId") Long staffId, @RequestBody List<StaffOfficeDto> staffAreaCountryVo) {
        return UpdateResponseBo.ok(staffOfficeService.updateStaffOffice(staffId, staffAreaCountryVo));
    }

    @ApiOperation(value = "批量设置业务办公室")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.EDIT, description = "权限中心/业务办公室管理/批量设置业务办公室")
    @PostMapping("batchUpdateStaffOffice")
    public ResponseBo batchUpdateStaffOffice(@RequestBody StaffOfficeBatchUpdateDto staffOfficeBatchUpdateDto) {
        staffOfficeService.batchUpdateStaffOffice(staffOfficeBatchUpdateDto);
        return ResponseBo.ok();
    }

}

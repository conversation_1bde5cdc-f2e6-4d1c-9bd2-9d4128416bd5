package com.get.permissioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.utils.ValidList;
import com.get.permissioncenter.vo.PermissionGradeVo;
import com.get.permissioncenter.vo.PermissionGroupGradeStaffVo;
import com.get.permissioncenter.vo.PermissionGroupVo;
import com.get.permissioncenter.vo.PermissionGroupGradeResourceVo;
import com.get.permissioncenter.entity.PermissionGrade;
import com.get.permissioncenter.entity.PermissionGroup;
import com.get.permissioncenter.service.IGroupGradeResourceService;
import com.get.permissioncenter.service.IGroupGradeStaffService;
import com.get.permissioncenter.service.IPermissionGradeService;
import com.get.permissioncenter.service.IPermissionGroupGradeNameService;
import com.get.permissioncenter.service.IPermissionGroupService;
import com.get.permissioncenter.service.IStaffResourceService;
import com.get.permissioncenter.dto.*;
import com.get.permissioncenter.dto.PermissionGradeDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "权限管理")
@RestController
@RequestMapping("permission")
public class PermissionController {
    @Resource
    IPermissionGradeService permissionGradeService;
    @Resource
    IPermissionGroupService permissionGroupService;
    @Resource
    IGroupGradeResourceService groupGradeResourceService;
    @Resource
    IGroupGradeStaffService groupGradeStaffService;
    @Resource
    IStaffResourceService staffResourceService;
    @Resource
    IPermissionGroupGradeNameService permissionGroupGradeNameService;


//    /**
//     * Demo
//     * @param id
//     * @return
//     *
//     */
//    @ApiOperation(value = "测试demo接口",notes = "id为此条数据id")
//    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER,type = LoggerOptTypeConst.DETAIL,description = "权限中心/权限等级管理/权限等级详情")
//    @GetMapping("demo/{id}")
//    public ResponseBo demoDetail(@PathVariable("id") String id) {
//        Result<List<String>> listResult =  filesCenterClient.getDemoList(id);
//        ResponseBo responseBo = new ResponseBo();
//        responseBo.put("data", listResult.getData());
//        return responseBo;
//    }


    /**
     * 列表数据
     *
     * @param page
     * @return
     */
    @ApiOperation(value = "权限等级列表数据", notes = "keyWord为关键词")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.LIST, description = "权限中心/权限等级管理/查询权限等级")
    @PostMapping("permissiongrade/datas")
    public ResponseBo gradeDatas(@RequestBody SearchBean<PermissionGradeDto> page) {
        List<PermissionGradeVo> datas = permissionGradeService.getPermissionGrades(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 详情
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "权限等级详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "权限中心/权限等级管理/权限等级详情")
    @GetMapping("permissiongrade/{id}")
    public ResponseBo gradeDetail(@PathVariable("id") Long id) {
        //TODO 改过
        //PermissionGrade data = permissionGradeService.findPermissionGradeById(id);
        PermissionGradeVo data = permissionGradeService.findPermissionGradeById(id);
        PermissionGradeVo permissionGradeVo = BeanCopyUtils.objClone(data, PermissionGradeVo::new);
        ResponseBo responseBo = new ResponseBo();
        responseBo.put("data", permissionGradeVo);
        return responseBo;
    }


    /**
     * 新增信息
     *
     * @param permissionGradeDto
     * @return
     */
    @ApiOperation(value = "权限等级新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.ADD, description = "权限中心/权限等级管理/新增权限等级")
    @PostMapping("permissiongrade/add")
    public ResponseBo gradeDdd(@RequestBody @Validated(PermissionGradeDto.Add.class) PermissionGradeDto permissionGradeDto) {
        return SaveResponseBo.ok(this.permissionGradeService.addPermissionGrade(permissionGradeDto));
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "权限等级删除接口", notes = "id为删除数据的ID")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.DELETE, description = "权限中心/权限等级管理/删除权限等级")
    @PostMapping("permissiongrade/delete/{id}")
    public ResponseBo gradeDelete(@PathVariable("id") Long id) {
        this.permissionGradeService.delete(id);
        return ResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param permissionGradeDto
     * @return
     */
    @ApiOperation(value = "权限等级修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.EDIT, description = "权限中心/权限等级管理/修改权限等级")
    @PostMapping("permissiongrade/update")
    public ResponseBo gradeUpdate(@RequestBody @Validated(PermissionGradeDto.Update.class) PermissionGradeDto permissionGradeDto) {
        return UpdateResponseBo.ok(permissionGradeService.updatePermissionGrade(permissionGradeDto));
    }


    /**
     * 批量新增信息
     *
     * @param permissionGradeDtos
     * @return
     */
    @ApiOperation(value = "权限等级批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.ADD, description = "权限中心/权限等级管理/批量新增权限等级")
    @PostMapping("permissiongrade/batchAdd")
    public ResponseBo gradeBatchAdd(@RequestBody @Validated(PermissionGradeDto.Add.class) ValidList<PermissionGradeDto> permissionGradeDtos) {
        permissionGradeService.batchAdd(permissionGradeDtos);
        return ResponseBo.ok();
    }

    /**
     * 上移下移
     *
     * @param permissionGradeDtos
     * @return
     */
    @ApiOperation(value = "权限等级上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.EDIT, description = "权限中心/权限等级管理/移动权限等级顺序")
    @PostMapping("permissiongrade/movingOrder")
    public ResponseBo gradeMovingOrder(@RequestBody List<PermissionGradeDto> permissionGradeDtos) {
        permissionGradeService.movingOrder(permissionGradeDtos);
        return ResponseBo.ok();
    }


    /**
     * 列表数据
     *
     * @param page
     * @return
     */
    @ApiOperation(value = "权限组别列表数据", notes = "keyWord为关键词")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.LIST, description = "权限中心/权限组别管理/查询权限组别")
    @PostMapping("permissiongroup/datas")
    public ResponseBo groupDatas(@RequestBody SearchBean<PermissionGroupDto> page) {
        List<PermissionGroupVo> datas = permissionGroupService.getPermissionGroups(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 详情
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "权限组别详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "权限中心/权限组别管理/权限组别详情")
    @GetMapping("permissiongroup/{id}")
    public ResponseBo groupDetail(@PathVariable("id") Long id) {
        //TODO 改过
        //PermissionGroup data = permissionGroupService.findPermissionGroupById(id);
        PermissionGroupVo data = permissionGroupService.findPermissionGroupById(id);
        PermissionGroupVo permissionGroupVo = BeanCopyUtils.objClone(data, PermissionGroupVo::new);
        ResponseBo responseBo = new ResponseBo();
        responseBo.put("data", permissionGroupVo);
        return responseBo;
    }


    /**
     * 新增信息
     *
     * @param permissionGroupDto
     * @return
     */
    @ApiOperation(value = "权限组别新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.ADD, description = "权限中心/权限组别管理/新增权限组别")
    @PostMapping("permissiongroup/add")
    public ResponseBo groupAdd(@RequestBody @Validated(PermissionGroupDto.Add.class) PermissionGroupDto permissionGroupDto) {
        return SaveResponseBo.ok(this.permissionGroupService.addPermissionGroup(permissionGroupDto));
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "权限组别删除接口", notes = "id为删除数据的ID")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.DELETE, description = "权限中心/权限组别管理/删除权限组别")
    @PostMapping("permissiongroup/delete/{id}")
    public ResponseBo groupDelete(@PathVariable("id") Long id) {
        this.permissionGroupService.delete(id);
        return ResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param permissionGroupDto
     * @return
     */
    @ApiOperation(value = "权限组别修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.EDIT, description = "权限中心/权限组别管理/修改权限组别")
    @PostMapping("permissiongroup/update")
    public ResponseBo groupUpdate(@RequestBody @Validated(PermissionGroupDto.Update.class) PermissionGroupDto permissionGroupDto) {
        return UpdateResponseBo.ok(permissionGroupService.updatePermissionGroup(permissionGroupDto));
    }


    /**
     * 批量新增信息
     *
     * @param permissionGroupDtos
     * @return
     */
    @ApiOperation(value = "权限组别批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.ADD, description = "权限中心/权限组别管理/批量新增权限组别")
    @PostMapping("permissiongroup/batchAdd")
    public ResponseBo groupBatchAdd(@RequestBody @Validated(PermissionGroupDto.Add.class) ValidList<PermissionGroupDto> permissionGroupDtos) {
        permissionGroupService.batchAdd(permissionGroupDtos);
        return ResponseBo.ok();
    }

    /**
     * 上移下移
     *
     * @param permissionGroupDtos
     * @return
     */
    @ApiOperation(value = "权限组别上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.EDIT, description = "权限中心/权限组别管理/移动权限组别顺序")
    @PostMapping("permissiongroup/movingOrder")
    public ResponseBo groupMovingOrder(@RequestBody List<PermissionGroupDto> permissionGroupDtos) {
        permissionGroupService.movingOrder(permissionGroupDtos);
        return ResponseBo.ok();
    }

    /**
     * 资源配置列表数据
     *
     * @return
     */
    @ApiOperation(value = "权限系统资源配置列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.LIST, description = "权限中心/权限管理/权限系统资源配置列表")
    @PostMapping("groupgraderesource/datas")
    public ResponseBo groupGradeResourceDatas(@RequestParam(name = "companyId", required = false) Long companyId) {
        PermissionGroupGradeResourceVo permissionGroupGradeResourceVo = groupGradeResourceService.getGroupGradeResource(companyId);
        return new ResponseBo<>(permissionGroupGradeResourceVo);
    }

    /**
     * 组别等级对应的资源
     *
     * @return
     */
    @ApiOperation(value = "组别等级对应的资源", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.LIST, description = "权限中心/权限管理/查询组别等级相关权限")
    @PostMapping("groupgraderesource/getresources")
    public ResponseBo getResources(@RequestBody PermissionGroupGradeResourceDto permissionGroupGradeResourceDto) {
        List<String> datas = groupGradeResourceService.getGroupGradeResources(permissionGroupGradeResourceDto);
        return new ListResponseBo<>(datas);
    }

    /**
     * 保存组别等级权限
     *
     * @return
     */
    @ApiOperation(value = "保存组别等级权限", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.EDIT, description = "权限中心/权限管理/保存组别等级权限")
    @PostMapping("groupgraderesource/updateresource")
    public ResponseBo updateresource(@RequestBody PermissionGroupGradeResourceDto permissionGroupGradeResourceDto) {
        groupGradeResourceService.updateGroupGradeResources(permissionGroupGradeResourceDto);
        return ResponseBo.ok();
    }

    /**
     * 增加组别等级权限
     * @return
     *
     */
/*    @ApiOperation(value = "增加组别等级权限",notes = "")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER,type = LoggerOptTypeConst.ADD,description = "权限中心/权限管理/保存组别等级权限")
    @PostMapping("groupgraderesource/addresource")
    public ResponseBo addresource(@RequestBody PermissionGroupGradeResourceDto permissionGroupGradeResourceVo) {
        groupGradeResourceService.addGroupGradeResources(permissionGroupGradeResourceVo);
        return ResponseBo.ok();
    }*/
    /**
     * 删除组别等级权限
     * @return
     *
     */
/*    @ApiOperation(value = "删除组别等级权限",notes = "")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER,type = LoggerOptTypeConst.DELETE,description = "权限中心/权限管理/删除组别等级权限")
    @PostMapping("groupgraderesource/deleteresource")
    public ResponseBo deleteresource(@RequestBody PermissionGroupGradeResourceDto permissionGroupGradeResourceVo) {
        groupGradeResourceService.deleteGroupGradeResources(permissionGroupGradeResourceVo);
        return ResponseBo.ok();
    }*/

    /**
     * 权限人员配置列表数据
     *
     * @return
     */
    @ApiOperation(value = "权限人员配置列表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.LIST, description = "权限中心/权限管理/权限人员配置列表")
    @PostMapping("groupgradestaff/datas")
    public ResponseBo groupgradestaffDatas(@RequestParam(name = "companyId", required = false) Long companyId) {
        PermissionGroupGradeStaffVo permissionGroupGradeStaffVo = groupGradeStaffService.getGroupGradeStaff(companyId);
        return new ResponseBo<>(permissionGroupGradeStaffVo);
    }

    /**
     * 组别等级相关人员
     *
     * @return
     */
    @ApiOperation(value = "组别等级相关人员", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.LIST, description = "权限中心/权限管理/查询组别等级相关人员")
    @PostMapping("groupgradestaff/getstaffs")
    public ResponseBo getstaffs(@RequestBody PermissionGroupGradeStaffDto permissionGroupGradeStaffDto) {
        List<Long> datas = groupGradeStaffService.getGroupGradeStaffs(permissionGroupGradeStaffDto);
        return new ListResponseBo<>(datas);
    }

    /**
     * 保存组别等级人员
     *
     * @return
     */
    @ApiOperation(value = "保存组别等级人员", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.EDIT, description = "权限中心/权限管理/保存组别等级人员")
    @PostMapping("groupgradestaff/update")
    public ResponseBo updatestaff(@RequestBody PermissionGroupGradeStaffDto permissionGroupGradeStaffDto) {
        groupGradeStaffService.updateGroupGradeStaffs(permissionGroupGradeStaffDto);
        return ResponseBo.ok();
    }

    /**
     * 查询用户允许禁止权限
     *
     * @return
     */
    @ApiOperation(value = "查询用户允许禁止权限", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.LIST, description = "权限中心/权限管理/查询用户允许禁止权限")
    @PostMapping("staffresource/datas")
    public ResponseBo getStaffResources(@RequestBody StaffResourceDto staffResourceDto) {
        return new ListResponseBo<>(staffResourceService.getStaffResources(staffResourceDto));
    }

    /**
     * 保存用户允许禁止权限
     *
     * @return
     */
    @ApiOperation(value = "保存用户允许禁止权限", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.EDIT, description = "权限中心/权限管理/保存用户允许禁止权限")
    @PostMapping("staffresource/update")
    public ResponseBo updateStaffResources(@RequestBody @Validated(StaffResourceDto.Add.class) StaffResourceDto staffResourceDto) {
        staffResourceService.updateStaffResources(staffResourceDto);
        return ResponseBo.ok();
    }

    /**
     * 用户权限清单
     *
     * @return
     */
    @ApiOperation(value = "用户权限清单", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.LIST, description = "权限中心/权限管理/用户权限清单")
    @GetMapping("staffresource/getresourcekeys/{staffId}")
    public ResponseBo getStaffResourceKeys(@PathVariable("staffId") Long staffId) {
        return new ResponseBo<>(staffResourceService.getStaffResourceKeys(staffId));
    }

    /**
     * @return java.lang.Boolean
     * @Description：这里是Feign,判断权限有没有用户
     * @Param [resourceKey]
     * <AUTHOR>
     **/
    @ApiIgnore
    @PostMapping("staffresource/getGroupGradeResourcesByResource")
    public Boolean getGroupGradeResourcesByResource(@RequestParam String resourceKey) {
        return groupGradeResourceService.getGroupGradeResourcesByResource(resourceKey);
    }


    /**
     * @return java.lang.Boolean
     * @Description：这里是Feign,更新资源key
     * @Param [resourceKey]
     * <AUTHOR>
     **/
    @ApiIgnore
    @ApiOperation(value = "系统中心调用更新权限", notes = "")
    @PostMapping("staffresource/updateGroupGradeResourcesByResource")
    public ResponseBo updateGroupGradeResourcesByResource(@RequestBody ResourceDto resourceKey) {
        return new ResponseBo<>(groupGradeResourceService.updateGroupGradeResourcesByResource(resourceKey));
    }

    /**
     * 删除
     *
     * @param permissionGroupGradeNameDto
     * @return
     */
    @ApiOperation(value = "权限组别等级修改名称", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.EDIT, description = "权限中心/权限管理/修改组别等级权限")
    @PostMapping("permissionGroupGrade/updateGroupGradeName")
    public ResponseBo updateGroupGradeName(@RequestBody PermissionGroupGradeNameDto permissionGroupGradeNameDto) {
        this.permissionGroupGradeNameService.updateGroupGradeName(permissionGroupGradeNameDto);
        return ResponseBo.ok();
    }



    /**
     * 移动网点资源
     *
     * @param permissionGroupGradeForMovingAndCopyingDto
     * @return
     */
    @ApiOperation(value = "移动网点资源", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.EDIT, description = "权限中心/权限管理/移动网点资源")
    @PostMapping("groupgraderesource/movePermissionGroupGrade")
    public ResponseBo movePermissionGroupGrade(@RequestBody @Validated(PermissionGroupGradeForMovingAndCopyingDto.Update.class) PermissionGroupGradeForMovingAndCopyingDto permissionGroupGradeForMovingAndCopyingDto) {
        groupGradeResourceService.movePermissionGroupGrade(permissionGroupGradeForMovingAndCopyingDto);
        return ResponseBo.ok();
    }

    /**
     * 复制网点资源
     *
     * @param groupGradeForMovingAndCopyingVo
     * @return
     */
    @ApiOperation(value = "复制网点资源", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.EDIT, description = "权限中心/权限管理/复制网点资源")
    @PostMapping("groupgraderesource/copyPermissionGroupGrade")
    public ResponseBo copyPermissionGroupGrade(@RequestBody @Validated(PermissionGroupGradeForMovingAndCopyingDto.Add.class) PermissionGroupGradeForMovingAndCopyingDto groupGradeForMovingAndCopyingVo) {
        groupGradeResourceService.copyPermissionGroupGrade(groupGradeForMovingAndCopyingVo);
        return ResponseBo.ok();
    }

    /**
     * 移动网点人员
     *
     * @param permissionGroupGradeForMovingAndCopyingDto
     * @return
     */
    @ApiOperation(value = "移动网点人员", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.EDIT, description = "权限中心/权限管理/移动网点人员")
    @PostMapping("groupgraderestaff/movePermissionGroupGradeStaff")
    public ResponseBo movePermissionGroupGradeStaff(@RequestBody @Validated(PermissionGroupGradeForMovingAndCopyingDto.Update.class) PermissionGroupGradeForMovingAndCopyingDto permissionGroupGradeForMovingAndCopyingDto) {
        groupGradeStaffService.movePermissionGroupGradeStaff(permissionGroupGradeForMovingAndCopyingDto);
        return ResponseBo.ok();
    }

    /**
     * 复制网点人员
     *
     * @param
     * @return
     */
    @ApiOperation(value = "复制网点资源人员", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.EDIT, description = "权限中心/权限管理/复制网点人员")
    @PostMapping("groupgraderestaff/copyPermissionGroupGradeStaff")
    public ResponseBo copyPermissionGroupGradeStaff(@RequestBody @Validated(PermissionGroupGradeForMovingAndCopyingDto.Add.class) PermissionGroupGradeForMovingAndCopyingDto permissionGroupGradeForMovingAndCopyingDto) {
        groupGradeStaffService.copyPermissionGroupGradeStaff(permissionGroupGradeForMovingAndCopyingDto);
        return ResponseBo.ok();
    }

}

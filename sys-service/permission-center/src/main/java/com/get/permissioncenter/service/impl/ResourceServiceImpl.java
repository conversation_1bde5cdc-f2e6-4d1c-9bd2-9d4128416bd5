package com.get.permissioncenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.redis.cache.GetRedis;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.dao.ResourceMapper;
import com.get.permissioncenter.vo.ResourceVo;
import com.get.permissioncenter.entity.Resource;
import com.get.permissioncenter.service.IGroupGradeResourceService;
import com.get.permissioncenter.service.IResourceService;
import com.get.permissioncenter.dto.ResourceDto;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: jack
 * @create: 2020/6/8
 * @verison: 1.0
 * @description: 系统资源业务实现类
 */

@Service
public class ResourceServiceImpl extends BaseServiceImpl<ResourceMapper, Resource> implements IResourceService {
    @javax.annotation.Resource
    @Lazy
    IGroupGradeResourceService groupGradeResourceService;
    @javax.annotation.Resource
    private ResourceMapper resourceMapper;
    @javax.annotation.Resource
    private UtilService utilService;
    @javax.annotation.Resource
    private GetRedis getRedis;

    @Override
    public List<ResourceVo> getResources(ResourceDto resourceDto) {
//        Example example = new Example(Resource.class);
//        Example.Criteria criteria = example.createCriteria();
//        if(GeneralTool.isNotEmpty(resourceDto)){
//            if(GeneralTool.isNotEmpty(resourceDto.getFkParentResourceId())){
//                criteria.andEqualTo("fkParentResourceId",resourceDto.getFkParentResourceId());
//            }
//            if(GeneralTool.isNotEmpty(resourceDto.getKeyWord())){
//                criteria.andLike("resourceName","%"+resourceDto.getKeyWord()+"%");
//            }
//        }
//        example.orderBy("viewOrder").asc();
//        List<Resource> resources = resourceMapper.selectByExample(example);
        LambdaQueryWrapper<Resource> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isNotEmpty(resourceDto)) {
            if (GeneralTool.isNotEmpty(resourceDto.getFkParentResourceId())) {
                wrapper.eq(Resource::getFkParentResourceId, resourceDto.getFkParentResourceId());
            }
            if (GeneralTool.isNotEmpty(resourceDto.getKeyWord())) {
                wrapper.and(wrapper_ ->
                        wrapper_.like(Resource::getResourceName, resourceDto.getKeyWord().trim()));
            }
        }
        wrapper.orderByAsc(Resource::getViewOrder);
        List<Resource> resources = resourceMapper.selectList(wrapper);
        List<ResourceVo> convertDatas = new ArrayList<>();
        convertDatas = BeanCopyUtils.copyListProperties(resources, ResourceVo::new);
        return convertDatas;
    }

    @Override
    public ResourceVo findResourceById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        Resource resource = resourceMapper.selectById(id);
        if (GeneralTool.isEmpty(resource)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        ResourceVo resourceVo = BeanCopyUtils.objClone(resource, ResourceVo::new);
        return resourceVo;
    }

    @Override
    public ResourceVo updateResource(ResourceDto resourceDto) {
        if (resourceDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        if (GeneralTool.isEmpty(resourceDto.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        Resource rs = this.resourceMapper.selectById(resourceDto.getId());
        if (rs == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        /*resourceDto.setOldResourceKey(rs.getResourceKey());*/
        Resource resource = BeanCopyUtils.objClone(resourceDto, Resource::new);
        if (validateUpdate(resourceDto)) {
            utilService.updateUserInfoToEntity(resource);
            resourceMapper.updateById(resource);
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
        }
        /*   groupGradeResourceService.updateGroupGradeResourcesByResource(resourceDto);*/
        return findResourceById(resource.getId());
    }

    @Override
    public Long addResource(ResourceDto resourceDto) {
        Resource resource = BeanCopyUtils.objClone(resourceDto, Resource::new);
        if (validateAdd(resourceDto)) {
            resource.setViewOrder(resourceMapper.getMaxViewOrder());
            utilService.updateUserInfoToEntity(resource);
            resourceMapper.insert(resource);
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
        }
        return resource.getId();
    }

    @Override
    public void delete(Long id) {
        ResourceVo resource = findResourceById(id);
        if (resource == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        Boolean success = groupGradeResourceService.getGroupGradeResourcesByResource(resource.getResourceKey());
        if (success) {
            int i = resourceMapper.deleteById(BeanCopyUtils.objClone(resource, Resource::new).getId());
            if (i <= 0) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
            }
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("resource_groupGrade_data_association"));
        }
    }

    /*    @Override
        public void batchUpdate(List<ResourceDto> resources) {
             for (ResourceDto resourceVo:resources){
                 Resource rs = selectByKey(resourceVo.getId());
                 if(rs==null){
                     throw  new GetServiceException(ErrorCodeEnum.INVALID_PARAM.getCode(),ErrorCodeEnum.INVALID_PARAM.getMessage());
                 }
                 Resource resource = BeanCopyUtils.objClone(resourceVo, Resource.class);
                 resourceMapper.updateByPrimaryKeySelective(resource);
             }
        }*/
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(List<ResourceDto> resources) {
        if (validateAdd(resources)) {
            for (ResourceDto resourceDto : resources) {
                if (validateAdd(resourceDto)) {
                    Resource resource = BeanCopyUtils.objClone(resourceDto, Resource::new);
                    resource.setViewOrder(resourceMapper.getMaxViewOrder());
                    utilService.updateUserInfoToEntity(resource);
                    resourceMapper.insert(resource);
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
                }
            }
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
        }
    }

    @Override
    public void movingOrder(List<ResourceDto> resourcevos) {
        if (GeneralTool.isEmpty(resourcevos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
//        Resource ro = BeanCopyUtils.objClone(resourcevos.get(0), Resource.class);
//        Integer oneorder = ro.getViewOrder();
//        Resource rt = BeanCopyUtils.objClone(resourcevos.get(1), Resource.class);
        Resource ro = BeanCopyUtils.objClone(resourcevos.get(0), Resource::new);
        Integer oneorder = ro.getViewOrder();
        Resource rt = BeanCopyUtils.objClone(resourcevos.get(1), Resource::new);
        Integer twoorder = rt.getViewOrder();
        ro.setViewOrder(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setViewOrder(oneorder);
        utilService.updateUserInfoToEntity(rt);
        resourceMapper.updateById(ro);
        resourceMapper.updateById(rt);
    }

    @Override
    public List<Resource> getResources(String keyword) {
        List<Resource> resources = resourceMapper.getResources(keyword);
        return resources;
    }

    @Override
    public List<String> getApiKeysByResourceKeys(List<String> resourcekeys) {
        return resourceMapper.getApiKeysByResourceKeys(resourcekeys);
    }

    @Override
    public List<ResourceVo> getResourceTree() {
        Long fkCompanyId = SecureUtil.getFkCompanyId();
        List<ResourceVo> resourceVos = resourceMapper.getTopLevelResources(fkCompanyId);
        return getTreeList(resourceVos);
    }

    @Override
    public void updateTranslationConfig(String type) {
//        getRedis.set("translation:"+StaffContext.getStaff().getLoginId(),type);
//        CacheUtil.put(USER_CACHE, TRANSLATION_CODE, GetAuthInfo.getStaffId(), type);
        System.out.println("登录员工id:" + GetAuthInfo.getStaffId());
//        SecureUtil.putStaffTranslationLocaleByStaffId(GetAuthInfo.getStaffId(),type);
    }

    private boolean validateAdd(ResourceDto resourceDto) {
//        Example example = new Example(Resource.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("resourceKey",resourceDto.getResourceKey());
//        List<Resource> list = this.resourceMapper.selectByExample(example);
        List<Resource> list = this.resourceMapper.selectList(Wrappers.<Resource>query().lambda().eq(Resource::getResourceKey, resourceDto.getResourceKey()));
        return GeneralTool.isEmpty(list);
    }

    private boolean validateAdd(List<ResourceDto> resourceDtos) {
        boolean success = true;
        for (ResourceDto resourceDto : resourceDtos) {
//            Example example = new Example(Resource.class);
//            Example.Criteria criteria = example.createCriteria();
//            criteria.andEqualTo("resourceKey",resourceDto.getResourceKey());
//            List<Resource> list = this.resourceMapper.selectByExample(example);
            List<Resource> list = this.resourceMapper.selectList(Wrappers.<Resource>query().lambda().eq(Resource::getResourceKey, resourceDto.getResourceKey()));
            if (!GeneralTool.isEmpty(list)) {
                success = false;
            }
        }
        return success;
    }

    private boolean validateUpdate(ResourceDto resourceDto) {
//        Example example = new Example(Resource.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("resourceKey",resourceDto.getResourceKey());
//        List<Resource> list = this.resourceMapper.selectByExample(example);

        List<Resource> list = this.resourceMapper.selectList(Wrappers.<Resource>query().lambda().eq(Resource::getResourceKey, resourceDto.getResourceKey()));
        return list.size() <= 0 || list.get(0).getId().equals(resourceDto.getId());
    }

    private List<ResourceVo> getTreeList(List<ResourceVo> entityList) {
        getChildList(entityList);
        return entityList;
    }

    private List<ResourceVo> getChildList(List<ResourceVo> entityList) {
        List<ResourceVo> resourceVos = new ArrayList<>();
        Long fkCompanyId = SecureUtil.getFkCompanyId();
        for (ResourceVo entity : entityList) {
            //获取父节点的子节点
            resourceVos = resourceMapper.getChildResources(entity.getId(),fkCompanyId);
            entity.setResourceDtos(resourceVos);
            if (GeneralTool.isNotEmpty(resourceVos)) {
                getChildList(resourceVos);
            }
        }
        return resourceVos;
    }


}

package com.get.permissioncenter.service;

import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseService;
import com.get.core.mybatis.utils.ValidList;
import com.get.permissioncenter.dto.CompanyDto;
import com.get.permissioncenter.dto.MediaAndAttachedDto;
import com.get.permissioncenter.vo.CompanyConfigAnalysisVo;
import com.get.permissioncenter.vo.CompanyVo;
import com.get.permissioncenter.vo.MediaAndAttachedVo;
import com.get.permissioncenter.vo.tree.CompanyTreeVo;
import com.get.permissioncenter.entity.Company;
import com.get.permissioncenter.dto.query.CompanyQueryDto;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/6/24
 * @TIME: 12:57
 **/
public interface ICompanyService extends BaseService<Company> {

    /**
     * 列表数据
     *
     * @param companyVo
     * @param
     * @return
     */
    List<CompanyVo> getCompanys(CompanyQueryDto companyVo);


    /**
     * 查询条件 所有数据
     */
    List<CompanyVo> getAllCompanys(Long companyId);


    /**
     * 详情
     *
     * @param id
     * @return
     */
    CompanyVo findCompanyById(Long id);


    /**
     * 获取公司logo
     * @param id
     * @return
     */
    MediaAndAttachedVo getCompanyLogo(Long id);

    /**
     * 详情(直属上司使用)
     *
     * @param id
     * @return
     */
    CompanyVo findStaffIdSupervisorCompanyById(Long id);

    /**
     * 查询上级
     *
     * @param parentId
     * @return
     */
    List<CompanyVo> findCompanyByParentId(Long parentId);


    /**
     * 新增
     *
     * @param CompanyDto
     * @return
     */
    Long addCompany(CompanyDto CompanyDto);

    /**
     * 修改
     *
     * @param companyDto
     * @return
     */

    CompanyVo updateCompanyVo(CompanyDto companyDto);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 递归查询所有子公司
     *
     * @param id
     */
    List<Long> getChildCompany(Long id);

    /**
     * 获取HTI和HTI及其下属公司
     * @return
     */
    List<Long> getHtiAndChildCompany();


    /**
     * 获取公司树形层次结构图
     *
     * @return
     */
    List<CompanyTreeVo> getCompanyTreeDto(Long companyId);

    /**
     * 获取公司树形层次结构图(过滤当前公司)
     *
     * @return
     */
    List<CompanyTreeVo> getFilterCompanyTree(Long companyId);


    /**
     * 获取所有公司
     *
     * @return
     */
    List<CompanyTreeVo> getAllCompanyDto();

    /**
     * 获取所有公司
     *
     * @return
     */
    List<CompanyTreeVo> getAllCompanyDto(Long companyId);


    /**
     * 获取所属公司下拉框
     *
     * @return
     */
    CompanyVo getChildCompanyDto();

    /**
     * @return java.lang.String
     * @Description:根据id查询公司名称
     * @Param [id]
     * <AUTHOR>
     **/
    String getCompanyNameById(Long id);

    /**
     * @return java.util.Map<java.lang.Long, java.lang.String>
     * @Description :
     * @Param [companyIds]
     * <AUTHOR>
     */
    Map<Long, String> getCompanyNamesByIds(Set<Long> companyIds);

    /**
     * @author: Neil
     * @description:
     * @date: 2022/4/29 15:30
     * @return
     * 获取公司全称Map
     */
    Map<Long, String> getCompanyFullNamesByIds(Set<Long> companyIds);

    /**
     * 根据关键字模糊查询公司ids
     *
     * @param keyWord
     * @return
     */
    List<Long> getCompanyIdByName(String keyWord);

    /**
     * @return void
     * @Description: 排序
     * @Param [companyDtoList]
     * <AUTHOR>
     */
    void movingOrder(List<CompanyDto> companyDtoList);

    /**
     * @return java.util.List<com.get.permissioncenter.com.get.permissioncenter.vo.tree.CompanyTreeVo>
     * @Description: 根据公司id查询上级公司
     * @Param [companyId]
     * <AUTHOR>
     */
    List<CompanyTreeVo> getParentCompany(Long companyId);


    /**
     * @return java.util.List<com.get.permissioncenter.com.get.permissioncenter.vo.MediaAndAttachedDto>
     * @Description: 保存附件
     * @Param [mediaAttachedVo]
     * <AUTHOR>
     */
    List<MediaAndAttachedVo> addCompanyMedia(ValidList<MediaAndAttachedDto> mediaAttachedVo);

    /**
     * @return com.get.permissioncenter.com.get.permissioncenter.vo.MediaAndAttachedDto
     * @Description: 获取公司logo
     * @Param [fkCompanyId]
     * <AUTHOR>
     */
    MediaAndAttachedVo getCompanyIcon(Long fkCompanyId);

    /**
     * 模糊搜索公司名称
     * @param keyword
     * @return
     */
    List<BaseSelectEntity> fuzzySearchCompanyName(String keyword,Long companyId);

    /**
     * 获取限制子公司查询的员工
     * @param companyId
     * @return
     */
    List<Long> getCompanyLimit(Long companyId);

    /**
     * 获取系统参数Map
     * @param configKey 系统参数key
     * @param value int类型，  1 则取value1的值
     * @return 公司id - 参数
     */
    Map<Long, String> getCompanyConfigMap(String configKey, int value);

    /**
     * 获取公司配置key解析对象
     *
     * @Date 12:53 2024/5/13
     * <AUTHOR>
     */
    Map<Long, CompanyConfigAnalysisVo> getCompanyConfigAnalysis(String configKey);

    List<String> getCompanyNamesByIdsDESC(Set<Long> companyIds);
}

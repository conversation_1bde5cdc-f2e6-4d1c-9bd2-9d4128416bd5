package com.get.permissioncenter.service.impl;

import com.alibaba.nacos.common.utils.Objects;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.dao.RInstitutionPermissionGroupStaffMapper;
import com.get.permissioncenter.dto.RInstitutionPermissionGroupStaffDto;
import com.get.permissioncenter.dto.query.StaffQueryDto;
import com.get.permissioncenter.entity.RInstitutionPermissionGroupStaff;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.service.IStaffService;
import com.get.permissioncenter.service.RInstitutionPermissionGroupStaffService;
import com.get.permissioncenter.vo.StaffVo;
import com.get.salecenter.feign.ISaleCenterClient;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * 学校权限组别与员工关联service层
 */
@Service("permissionRInstitutionPermissionGroupStaffService")
public class RInstitutionPermissionGroupStaffServiceImpl extends ServiceImpl<RInstitutionPermissionGroupStaffMapper, RInstitutionPermissionGroupStaff> implements RInstitutionPermissionGroupStaffService {
    @Resource
    private RInstitutionPermissionGroupStaffMapper rInstitutionPermissionGroupStaffMapper;
    @Resource
    private IStaffService staffService;
    @Resource
    private UtilService utilService;
    @Resource
    private ISaleCenterClient saleCenterClient;
    @Resource
    private IPermissionCenterClient permissionCenterClient;


    @Override
    public List<StaffVo> dataList(SearchBean<StaffQueryDto> page) {
        StaffQueryDto staffQueryDto = page.getData();
        if (GeneralTool.isEmpty(staffQueryDto.getIsBind())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("is_bind_null"));
        }
        if (GeneralTool.isEmpty(staffQueryDto.getFkInstitutionPermissionGroupId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("institution_permission_group_id"));
        }
        List<StaffVo> staffVos = staffService.getStaffDto(page.getData(), page);
        if (GeneralTool.isEmpty(staffVos)) {
            return null;
        }
        for (StaffVo staffVo : staffVos) {
            String staffNames ="";
            if (GeneralTool.isNotEmpty(staffVo.getStaffBdStaffIds())){
                String staffBdStaffIds = staffVo.getStaffBdStaffIds();
                Set<Long> staffIds = Arrays.stream(staffBdStaffIds.trim().split(","))
                        .map(String::trim)
                        .filter(s -> !s.isEmpty())
                        .map(s -> {
                            try {
                                return Long.parseLong(s);
                            } catch (NumberFormatException e) {
                                return null;
                            }
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());
                Map<Long, String> staffNameMap = permissionCenterClient.getStaffNameMap(staffIds).getData();

                staffNames = Arrays.stream(staffBdStaffIds.trim().split(","))
                        .map(String::trim)
                        .filter(s -> !s.isEmpty())
                        .map(s -> {
                            try {
                                return Long.parseLong(s);
                            } catch (NumberFormatException e) {
                                return null;
                            }
                        })
                        .filter(Objects::nonNull)
                        .map(staffNameMap::get)
                        .filter(Objects::nonNull)
                        .collect(Collectors.joining(","));

            }
            staffVo.setBdNames(staffNames);
        }
        return staffVos;
    }


    /**
     * 批量新增
     *
     * @param rInstitutionPermissionGroupStaffDto
     * @return
     */
    @Override
    public List<Long> addRInstitutionPermissionGroupStaff(RInstitutionPermissionGroupStaffDto rInstitutionPermissionGroupStaffDto) {
        if (GeneralTool.isEmpty(rInstitutionPermissionGroupStaffDto.getFkInstitutionPermissionGroupId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("institution_permission_group_id"));
        }
        if (GeneralTool.isEmpty(rInstitutionPermissionGroupStaffDto.getFkStaffIds())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("staffId_is_null"));
        }
        // 构建批量插入数据
        List<RInstitutionPermissionGroupStaff> entityList = rInstitutionPermissionGroupStaffDto.getFkStaffIds().stream()
                .map(staffId -> {
                    RInstitutionPermissionGroupStaff rInstitutionPermissionGroupStaff = BeanCopyUtils.objClone(rInstitutionPermissionGroupStaffDto, RInstitutionPermissionGroupStaff::new);
                    rInstitutionPermissionGroupStaff.setFkStaffId(staffId);
                    utilService.setCreateInfo(rInstitutionPermissionGroupStaff);
                    return rInstitutionPermissionGroupStaff;
                })
                .collect(Collectors.toList());

        // 批量插入
        boolean success = saveBatch(entityList);
        if (!success) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }

        // 返回id
        return entityList.stream()
                .map(RInstitutionPermissionGroupStaff::getId)
                .collect(Collectors.toList());
    }


    /**
     * 根据id删除
     *
     * @param id
     */
    @Override
    public void removeById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        RInstitutionPermissionGroupStaff rInstitutionPermissionGroupStaff = rInstitutionPermissionGroupStaffMapper.selectById(id);
        if (GeneralTool.isEmpty(rInstitutionPermissionGroupStaff)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        rInstitutionPermissionGroupStaffMapper.deleteById(id);

    }

}


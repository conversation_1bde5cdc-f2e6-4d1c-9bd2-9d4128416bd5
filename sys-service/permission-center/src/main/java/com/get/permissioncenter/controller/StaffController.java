package com.get.permissioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.api.Result;
import com.get.permissioncenter.dto.AddUserPreferencesAddDto;
import com.get.permissioncenter.dto.AssignBusinessSchoolDto;
import com.get.permissioncenter.dto.BusinessSchoolDto;
import com.get.permissioncenter.dto.CommentDto;
import com.get.permissioncenter.dto.MediaAndAttachedDto;
import com.get.permissioncenter.dto.StaffByIdsAndCompanyIdsDto;
import com.get.permissioncenter.dto.StaffByNameDto;
import com.get.permissioncenter.dto.StaffDto;
import com.get.permissioncenter.dto.StaffEmailDto;
import com.get.permissioncenter.dto.StaffEmailPasswordUpdateDto;
import com.get.permissioncenter.dto.StaffInfoUpdateDto;
import com.get.permissioncenter.dto.StaffInstitutionDto;
import com.get.permissioncenter.dto.query.StaffQueryDto;
import com.get.permissioncenter.entity.Staff;
import com.get.permissioncenter.service.IGroupGradeStaffService;
import com.get.permissioncenter.service.IStaffService;
import com.get.permissioncenter.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * @author: jack
 * @create: 2020/6/19
 * @verison: 1.0
 * @description: 员工管理控制器
 */
@Api(tags = "员工管理")
@RestController
@RequestMapping("permission/staff")
public class StaffController {

    @Resource
    private IStaffService staffService;
    @Resource
    private IGroupGradeStaffService groupGradeStaffService;

//    /**
//     * 员工登录
//     *
//     * @param staffVo
//     * @return
//     *
//     */
//    @LoginLogger
//    @ApiOperation(value = "员工登录", notes = "")
//    @PostMapping("login")
//    public ResponseBo<StaffVo> login(@RequestBody StaffDto staffVo, HttpServletRequest request, HttpServletResponse response) {
//        if (GeneralTool.isEmpty(staffVo.getLoginId()) || GeneralTool.isEmpty(staffVo.getLoginPs())) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
//        }
//        //shiro登录操作
//        UsernamePasswordToken token = new UsernamePasswordToken(staffVo.getLoginId(), MD5Utils.encrypt(staffVo.getLoginPs()), staffVo.isRememberMe());
//        Subject subject = LoginUtil.getSubject();
//        if (subject != null) {
//            subject.logout();
//        }
//        LoginUtil.login(token);
//        StaffVo staffDto = (StaffVo) subject.getPrincipal();
//        staffService.updateStaffSessionId(staffDto.getId(), subject.getSession().getId());
//        return new ResponseBo<>(staffDto);
//    }

//    /**
//     * avatar员工登录
//     *
//     * @param staffVo
//     * @return
//     *
//     */
//    @LoginLogger
//    @ApiOperation(value = "员工登录", notes = "")
//    @PostMapping("avatarLogin")
//    public ResponseBo<StaffVo> avatarLogin(@RequestBody StaffDto staffVo) {
//        if (GeneralTool.isEmpty(staffVo.getLoginId()) || GeneralTool.isEmpty(staffVo.getLoginPs())) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
//        }
//        //shiro登录操作
//        UsernamePasswordToken token = new UsernamePasswordToken(staffVo.getLoginId(), MD5Utils.encrypt(staffVo.getLoginPs()), staffVo.isRememberMe());
//        Subject subject = LoginUtil.getSubject();
//        if (subject != null) {
//            subject.logout();
//        }
//        LoginUtil.login(token);
//        StaffVo staffDto = (StaffVo) subject.getPrincipal();
//        if (GeneralTool.isNotEmpty(staffVo.getTargetLoginId()) && "admin".equals(staffDto.getLoginId())) {
//            Staff staff = staffService.findStaffByLoginId(staffVo.getTargetLoginId());
//            token = new UsernamePasswordToken(staff.getLoginId(), staff.getLoginPs(), staffVo.isRememberMe());
//            subject = LoginUtil.getSubject();
//            if (subject != null) {
//                subject.logout();
//            }
//            LoginUtil.login(token);
//            staffDto = (StaffVo) subject.getPrincipal();
//        }
//        staffService.updateStaffSessionId(staffDto.getId(), subject.getSession().getId());
//        return new ResponseBo<>(staffDto);
//    }

//    @ApiOperation(value = "查看用户信息", notes = "")
//    @PostMapping(value = "checkLogin")
//    public ResponseBo<StaffVo> checkLogin() {
//        Subject subject = LoginUtil.getSubject();
//        StaffVo staffDto = (StaffVo) subject.getPrincipal();
//        return new ResponseBo<>(staffDto);
//    }


//    @ApiOperation(value = "登出", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.LIST, description = "权限中心/员工管理/登出")
//    @PostMapping(value = "logout")
//    public ResponseBo logout() {
//        try {
//            staffService.loginOut();
////            StaffContext.staffRemove();
//            Subject subject = LoginUtil.getSubject();
//            subject.logout();
//        } catch (Exception e) {
//            System.out.println("e = " + e);
//            throw new GetServiceException("登出失败");
//        }
//        return ResponseBo.ok();
//    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 员工业务国家
     * @Param [staffId]
     * <AUTHOR>
     */
    @ApiOperation(value = "员工业务国家", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "权限中心/员工管理/员工业务国家")
    @GetMapping("getStaffCountry/{staffId}")
    public ResponseBo getStaffCountry(@PathVariable("staffId") Long staffId) {
        List<String> staffCountry = staffService.getStaffCountry(staffId);
        return new ListResponseBo<>(staffCountry);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 员工业务办公室
     * @Param [staffId]
     * <AUTHOR>
     */
    @ApiOperation(value = "员工业务办公室", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "权限中心/员工管理/员工业务办公室")
    @GetMapping("getStaffOffice/{staffId}")
    public ResponseBo getStaffOffice(@PathVariable("staffId") Long staffId) {
        List<String> staffCountry = staffService.getStaffOffice(staffId);
        return new ListResponseBo<>(staffCountry);
    }


    @ApiOperation(value = "列表数据", notes = "keyWord为关键词")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.LIST, description = "权限中心/员工管理/查询")
    @PostMapping("datas")
    public ResponseBo<StaffVo> datas(@RequestBody SearchBean<StaffQueryDto> voSearchBean) {
        List<StaffVo> staffVos = staffService.getStaffDto(voSearchBean.getData(), voSearchBean);
        Page page = BeanCopyUtils.objClone(voSearchBean, Page::new);
        return new ListResponseBo<>(staffVos, page);
    }


    @ApiOperation(value = "员工列表数据", notes = "keyWord为关键词")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.LIST, description = "权限中心/员工管理/CRM使用的查询")
    @PostMapping("staffDatas")
    public ResponseBo<StaffVo> getStaffDatas(@RequestBody SearchBean<StaffQueryDto> voSearchBean) {
        List<StaffVo> staffVos = staffService.getStaffDatas(voSearchBean.getData(), voSearchBean);
        Page page = BeanCopyUtils.objClone(voSearchBean, Page::new);
        return new ListResponseBo<>(staffVos, page);
    }

    @ApiOperation(value = "员工列表导出", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.LIST, description = "权限中心/员工管理/员工列表导出")
    @PostMapping("exportStaffList")
    public void exportStaffList(@RequestBody StaffDto staffDto, HttpServletResponse response) {
        staffService.exportStaffList(staffDto,response);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.permissioncenter.vo.StaffVo>
     * @Description: 详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "权限中心/员工管理/参数详情")
    @GetMapping("/{id}")
    @VerifyPermission(IsVerify = false)
    public ResponseBo<StaffVo> detail(@PathVariable("id") Long id) {
        StaffVo staff = staffService.findStaffById(id);
        return new ResponseBo<>(staff);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.permissioncenter.vo.StaffVo>
     * @Description: 详情
     * @Param [id]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "简历新增，员工配置详情接口", notes = "id为此条数据id")
    @GetMapping("staffResumeDetail/{id}")
    public ResponseBo<StaffVo> staffResumeDetail(@PathVariable("id") Long id) {
        StaffVo staff = staffService.findStaffById(id);
        return new ResponseBo<>(staff);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 批量新增信息
     * @Param [staffDtos]
     * <AUTHOR>
     */
    @ApiOperation(value = "批量新增接口")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.ADD, description = "权限中心/员工管理/新增参数")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(StaffDto.Add.class) ValidList<StaffDto> staffDtos) {
        return new ListResponseBo(staffService.batchAddStaff(staffDtos));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.permissioncenter.vo.StaffVo>
     * @Description: 修改信息
     * @Param [staffDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.EDIT, description = "权限中心/员工管理/更新参数")
    @PostMapping("update")
    public ResponseBo<StaffVo> update(@RequestBody @Validated(StaffDto.Update.class) StaffDto staffDto) {
        return UpdateResponseBo.ok(staffService.updateStaff(staffDto));
    }

    /**
     * 员工基本信息修改接口
     *
     * @Date 16:07 2021/11/22
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "员工基本信息修改接口")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.EDIT, description = "权限中心/首页个人信息/员工基本信息修改接口")
    @PostMapping("updateBasicStaffInfo")
    public ResponseBo updateBasicStaffInfo(@RequestBody @Validated(StaffInfoUpdateDto.Update.class) StaffInfoUpdateDto staffInfoUpdateDto) {
        staffService.updateBasicStaffInfo(staffInfoUpdateDto);
        return UpdateResponseBo.ok();
    }

    @ApiOperation(value = "员工email password update")
    @VerifyPermission(IsVerify = false)
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.EDIT, description = "权限中心/首页个人信息/员工email password update")
    @PostMapping("updateStaffEmailPassword")
    public ResponseBo updateStaffEmailPassword(@RequestBody StaffEmailPasswordUpdateDto staffEmailPasswordUpdateDto) {
        staffService.updateStaffEmailPassword(staffEmailPasswordUpdateDto);
        return UpdateResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 删除
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为删除数据的ID")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.DELETE, description = "权限中心/员工管理/删除资源")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        staffService.delete(id);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.permissioncenter.vo.StaffVo>
     * @Description: 所有员工数据
     * @Param [staffVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "获取所有员工数据")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.LIST, description = "权限中心/员工管理/查询")
    @PostMapping("getAllStaff")
    public ResponseBo<StaffVo> getAllStaff(@RequestBody StaffQueryDto staffVo) {
        List<StaffVo> staffVos = staffService.getAllStaffDtoList(staffVo);
        return new ListResponseBo<>(staffVos);
    }

    /**
     * 查询员工附件
     *
     * @param voSearchBean
     * @return
     */
    @ApiOperation(value = "查询员工附件", notes = "keyWord为关键词")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.LIST, description = "权限中心/员工管理/查询员工附件")
    @PostMapping("getStaffMedia")
    public ResponseBo<MediaAndAttachedVo> getStaffMedia(@RequestBody SearchBean<MediaAndAttachedDto> voSearchBean) {
        List<MediaAndAttachedVo> staffMedia = staffService.getStaffMedia(voSearchBean.getData(), voSearchBean);
        Page page = BeanCopyUtils.objClone(voSearchBean, Page::new);
        return new ListResponseBo<>(staffMedia, page);
    }

    /**
     * 保存员工附件接口
     *
     * @param mediaAttachedVo
     * @return
     */
    @ApiOperation(value = "保存员工附件接口")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.ADD, description = "权限中心/员工管理/保存员工附件")
    @PostMapping("upload")
    public ResponseBo upload(@RequestBody @Validated(MediaAndAttachedDto.Add.class) ValidList<MediaAndAttachedDto> mediaAttachedVo) {
        return new ListResponseBo<>(staffService.addStaffMedia(mediaAttachedVo));
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 编辑评论
     * @Param [commentDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "编辑评论")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.EDIT, description = "权限中心/员工管理/编辑评论")
    @PostMapping("editComment")
    public ResponseBo editComment(@RequestBody @Validated(CommentDto.Add.class) CommentDto commentDto) {
        return SaveResponseBo.ok(staffService.editComment(commentDto));
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.permissioncenter.vo.CommentVo>
     * @Description: 评论列表数据
     * @Param [searchBean]
     * <AUTHOR>
     */
    @ApiOperation(value = "评论列表数据")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.LIST, description = "权限中心/员工管理/评论查询")
    @PostMapping("getComments")
    public ResponseBo<CommentVo> getComment(@RequestBody SearchBean<CommentDto> searchBean) {
        List<CommentVo> datas = staffService.getComments(searchBean.getData(), searchBean);
        Page p = BeanCopyUtils.objClone(searchBean, Page::new);
        return new ListResponseBo<>(datas, p);
    }


    /**
     * @return java.lang.String
     * @Description :feign调用 通过员工id 查找对应的员工姓名
     * @Param [id]
     * <AUTHOR>
     */
    @ApiIgnore
    @GetMapping("getStaffName")
    public String getStaffName(@RequestParam(required = false) Long id) {
        return staffService.getStaffNameById(id);
    }

    /**
     * @Description :feign调用 通过员工ids 查找对应的员工姓名map
     * @Param [ids]
     * <AUTHOR>
     */
    @ApiIgnore
    @PostMapping("getStaffNamesByIds")
    public Map<Long, String> getStaffNamesByIds(@RequestBody Set<Long> ids) {
        return staffService.getStaffNamesByIds(ids);
    }

    @ApiIgnore
    @PostMapping("getStaffByIdsAndCompanyIds")
    public Map<Long, String> getStaffByIdsAndCompanyIds(@RequestBody StaffByIdsAndCompanyIdsDto staffByIdsAndCompanyIdsDto) {
        return staffService.getStaffByIdsAndCompanyIds(staffByIdsAndCompanyIdsDto);
    }

    /**
     * 根据员工ids获取员工LoginIds
     *
     * @Date 18:31 2021/6/24
     * <AUTHOR>
     */
    @ApiIgnore
    @PostMapping("getStaffLoginIdByIds")
    public Map<Long, String> getStaffLoginIdByIds(@RequestBody Set<Long> ids) {
        return staffService.getStaffLoginIdByIds(ids);
    }

    /**
     * 员工下拉框数据
     *
     * @return
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "员工下拉框数据", notes = "")
    @GetMapping("getStaffList")
    public ResponseBo<BaseSelectEntity> getStaffList(@RequestParam(value = "companyId", required = false) Long companyId,
                                                     @RequestParam(value = "departNums", required = false) List<String> departNums) {
        List<BaseSelectEntity> datas = staffService.getStaffList(companyId, departNums);
        return new ListResponseBo<>(datas);
    }

    /**
     * 员工下拉框数据
     *
     * @return
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "在职员工下拉框数据", notes = "")
    @GetMapping("getOnDutyStaffList")
    public ResponseBo<BaseSelectEntity> getOnDutyStaffList(@RequestParam(value = "companyId") Long companyId) {
        return new ListResponseBo<>(staffService.getOnDutyStaffList(companyId));
    }

    /**
     * 员工下拉框数据(百度式搜索)公司改成多选
     *
     * @return
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "员工下拉框数据(百度式搜索)", notes = "")
    @PostMapping("getStaffByStaffName")
    public ResponseBo<BaseSelectEntity> getStaffByStaffName(@RequestBody StaffByNameDto staffByNameDto) {
        List<BaseSelectEntity> datas = staffService.getStaffByStaffName(staffByNameDto.getCompanyIds(), staffByNameDto.getStaffName());
        return new ListResponseBo<>(datas);
    }


    /**
     * fei调用 根据部门id获取员工下拉框数据
     *
     * @Date 16:27 2021/7/6
     * <AUTHOR>
     */
    @ApiIgnore
    @PostMapping("getStaffByDepartmentIds")
    public List<BaseSelectEntity> getStaffByDepartmentIds(@RequestBody List<Long> departmentIds) {
        return staffService.getStaffByDepartmentIds(departmentIds);
    }

    /**
     * fei调用 根据员工id获取员工下拉框数据
     *
     * @Date 14:57 2021/8/20
     * <AUTHOR>
     */
    @ApiIgnore
    @PostMapping("getStaffByStaffIds")
    public List<BaseSelectEntity> getStaffByStaffIds(@RequestBody Set<Long> staffIds) {
        return staffService.getStaffByStaffIds(staffIds);
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 获取员工权限组别等级
     * @Param [id]
     * <AUTHOR>
     **/
    @ApiOperation(value = "获取员工权限组别等级", notes = "")
    @GetMapping("getPermissionGroupGradeStaffDtosByStaffId/{id}")
    public ResponseBo getPermissionGroupGradeStaffDtosByStaffId(@PathVariable("id") Long id) {
        //获取员工权限组别与等级
        List<PermissionGroupGradeStaffVo> permissionGroupGradeStaffVos = groupGradeStaffService.getPermissionGroupGradeStaffDtosByStaffId(id);
        return new ListResponseBo<>(permissionGroupGradeStaffVos);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 修改密码
     * @Param [staffDto]
     * <AUTHOR>
     **/
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "修改密码", notes = "")
    @PostMapping("updateStaffPwd")
    public ResponseBo updateStaffPwd(@RequestBody StaffDto staffDto) {
        staffService.updateStaffPwd(staffDto);
        return ResponseBo.ok();
    }

    @GetMapping("/getVerifyCode")
    @VerifyLogin(IsVerify = false)
    public ResponseBo getVerifyCode(@RequestParam(value = "username",required = true)String username){
        return staffService.getVerifyCode(username);
    }

    @GetMapping("/verifyUser")
    @VerifyLogin(IsVerify = false)
    public ResponseBo verifyUser(@RequestParam(value = "username",required = true)String username){
        return staffService.verifyUser(username);
    }

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :feign调用 通过员工姓名关键字查找对应ids
     * @Param [staffNameKey]
     * <AUTHOR>
     */
    @ApiIgnore
    @GetMapping(value = "getStaffIdsByNameKey")
    public List<Long> getStaffIdsByNameKey(@RequestParam("staffNameKey") String staffNameKey) {
        return staffService.getStaffIdsByNameKey(staffNameKey);
    }

    /**
     * feign调用 通过员工姓名或者英文名关键字查找对应ids
     *
     * @param staffNameKeyOrEnNameKey
     * @return
     */
    @ApiIgnore
    @GetMapping(value = "getStaffIdsByNameKeyOrEnNameKey")
    public List<Long> getStaffIdsByNameKeyOrEnNameKey(@RequestParam("staffNameKeyOrEnNameKey") String staffNameKeyOrEnNameKey) {
        return staffService.getStaffIdsByNameKeyOrEnNameKey(staffNameKeyOrEnNameKey);
    }

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :feign调用根据公司id查找该公司下的员工ids
     * @Param [companyId]
     * <AUTHOR>
     */
    @ApiIgnore
    @GetMapping(value = "getStaffIdsByCompanyId")
    public List<Long> getStaffIdsByCompanyId(@RequestParam("companyId") Long companyId) {
        return staffService.getStaffIdsByCompanyId(companyId);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 保存提交简历
     * @Param [guid]
     * <AUTHOR>
     **/
    @ApiOperation(value = "保存提交简历", notes = "")
    @PostMapping("saveResume")
    public ResponseBo saveResume(@RequestParam("guid") String guid) {
        staffService.saveResume(guid);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 保存简历guid
     * @Param [guid]
     * <AUTHOR>
     **/
    @ApiIgnore
    @PostMapping("saveResumeGuid")
    public ResponseBo saveResumeGuid(@RequestParam("guid") String guid) {
        staffService.saveResumeGuid(guid);
        return ResponseBo.ok();
    }

    /**
     * @Description :feign调用 通过员工ids查找对应公司名称map
     * @Param [staffIds]
     * <AUTHOR>
     */
    @ApiIgnore
    @PostMapping("getCompanyNamesByStaffIds")
    public Map<Long, String> getCompanyNamesByStaffIds(@RequestBody Set<Long> staffIds) {
        return staffService.getCompanyNamesByStaffIds(staffIds);
    }

    /**
     * feign 调用根据员工ids获取对应的公司id
     *
     * @Date 10:24 2021/7/29
     * <AUTHOR>
     */
    @ApiIgnore
    @PostMapping("getCompanyIdByStaffIds")
    public Map<Long, Long> getCompanyIdByStaffIds(@RequestBody Set<Long> staffIds) {
        return staffService.getCompanyIdByStaffIds(staffIds);
    }

    /**
     * @Description :feign调用 删除简历guid
     * @Param [staffIds]
     * <AUTHOR>
     */
    @ApiIgnore
    @PostMapping("deleteGuid")
    public ResponseBo deleteGuid(@RequestParam("guid") String guid) {
        staffService.deleteGuid(guid);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :feign调用 获取部门的所有员工ids
     * @Param [companyId, departmentId]
     * <AUTHOR>
     */
    @ApiIgnore
    @PostMapping("getAllDepartmentStaffIds")
    public ResponseBo getAllDepartmentStaffIds(@RequestParam(value = "companyId") Long companyId, @RequestParam("departmentId") Long departmentId) {
        List<Long> datas = staffService.getAllDepartmentStaffIds(companyId, departmentId);
        return new ListResponseBo<>(datas);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :feign调用 获取部门最高职位员工ids
     * @Param [companyId, departmentId]
     * <AUTHOR>
     */
    @ApiIgnore
    @PostMapping("getTopPositionStaffIds")
    public ResponseBo getTopPositionStaffIds(@RequestParam(value = "companyId") Long companyId, @RequestParam(value = "departmentId") Long departmentId) {
        List<Long> datas = staffService.getTopPositionStaffIds(companyId, departmentId);
        return new ListResponseBo<>(datas);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :feign调用 获取对应职位全部员工得id集合
     * @Param [companyId, positionIdList]
     * <AUTHOR>
     */
    @ApiIgnore
    @PostMapping(value = "getAllPositionStaffIds")
    public ResponseBo getAllPositionStaffIds(@RequestParam(value = "companyId") Long companyId, @RequestBody List<Long> positionIdList) {
        List<Long> datas = staffService.getAllPositionStaffIds(companyId, positionIdList);
        return new ListResponseBo<>(datas);
    }

    /**
     * @ Description :fegin调用，根据职位编号获取人
     * @ Param [num, companyId]
     * @ return com.get.common.result.ResponseBo
     * @ author LEO
     */
    @ApiIgnore
    @ApiOperation("fegin调用，根据职位编号获取人")
    @GetMapping("getPositionByNum")
    public ResponseBo getPositionByNum(@RequestParam("num") List<String> num) {
        List<Long> evp = staffService.getPositionByNum(num);
        return new ResponseBo<>(evp);
    }

    /**
     * @ Description :fegin调用，根据员工id查询对应公司
     * @ Param [id]
     * @ return java.lang.Long
     * @ author LEO
     */
    @ApiIgnore
    @ApiOperation("fegin调用，根据gmt_create_user查询对应公司")
    @GetMapping("getStaffByCreateUser")
    public StaffVo getStaffByCreateUser(@RequestParam("createUser") String createUser) {
        return staffService.getCompanyIdByCreateUser(createUser);
    }


    /**
     * fegin调用，根据员工ids查询对应公司
     *
     * @param createUsers
     * @return
     */
    @ApiIgnore
    @ApiOperation("fegin调用，根据批量gmt_create_user查询对应公司")
    @GetMapping("getStaffByCreateUsers")
    public List<StaffVo> getStaffByCreateUsers(@RequestParam("createUsers") Set<String> createUsers) {
        return staffService.getCompanyIdByCreateUsers(createUsers);
    }

    @VerifyLogin(IsVerify = false)
    @ApiIgnore
    @ApiOperation("fegin调用，根据员工id查询对象信息")
    @GetMapping("getCompanyIdByStaffId")
    public StaffVo getCompanyIdByStaffId(@RequestParam("StaffId") Long staffId) {
        return staffService.getCompanyIdByStaffId(staffId);
    }

    @ApiIgnore
    @ApiOperation(value = "fegin调用，更新年假")
    @PostMapping("updateAnnualLeaveBase")
    public ResponseBo updateAnnualLeaveBase(@RequestParam("id") Long id, @RequestParam("annualLeaveBase") BigDecimal annualLeaveBase) {
        staffService.updateAnnualLeaveBase(id, annualLeaveBase);
        return UpdateResponseBo.ok();
    }

    @ApiIgnore
    @ApiOperation(value = "fegin调用，更新补休")
    @PostMapping("updateCompensatoryLeaveBase")
    public ResponseBo updateCompensatoryLeaveBase(@RequestParam("id") Long id, @RequestParam("compensatoryLeaveBase") BigDecimal compensatoryLeaveBase) {
        staffService.updateCompensatoryLeaveBase(id, compensatoryLeaveBase);
        return UpdateResponseBo.ok();
    }

    /**
     * @ Description :获取全部人名字
     * @ Param [id]
     * @ return java.lang.String
     * @ author LEO
     */
    @ApiIgnore
    @PostMapping("getStaffNameMap")
    @VerifyPermission(IsVerify = false)
    public Map<Long, String> getStaffNameMap(@RequestBody Set<Long> staffId) {
        return staffService.getStaffNameMap(staffId);
    }

    /**
     * @ Description :根据名字模糊查询ids
     * @ Param
     * @ return
     * @ author LEO
     */
    @ApiIgnore
    @GetMapping("getStaffIdByName")
    @VerifyPermission(IsVerify = false)
    public List<Long> getStaffIdByName(@RequestParam("name") String startStaffName) {
        return staffService.getStaffIdByName(startStaffName);
    }

    /**
     * @ Description :根据员工id获取直属上司id
     * @ Param [id]
     * @ return java.lang.String
     * @ author LEO
     */
    @ApiIgnore
    @PostMapping("getStaffSupervisorIdByStaffId")
    @VerifyPermission(IsVerify = false)
    public Long getStaffSupervisorIdByStaffId(@RequestParam("staffId") Long staffId) {
        return staffService.getStaffSupervisorIdByStaffId(staffId);
    }


    /**
     * 获取所属平台下拉框数据
     *
     * @param
     * @return
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "获取区号下拉框数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, description = "权限中心/员工管理/区号下拉框数据")
    @PostMapping("getAreaCodeSelect")
    public ResponseBo<Map<String, Object>> getAreaCodeSelect() {
        return new ListResponseBo<>(ProjectExtraEnum.enumsTranslation2Arrays(ProjectExtraEnum.AREA_CODE_TYPE));
    }

    /**
     * @Description: 获取当天生日的员工
     * @Author: Jerry
     * @Date:14:21 2021/12/1
     */
    @VerifyLogin(IsVerify = false)
    @ApiIgnore
    @GetMapping("getDayOfStaffBirthday")
    public void getDayOfStaffBirthday() {
        staffService.getDayOfStaffBirthday();
    }


    @ApiOperation(value = "获取所有下属员工")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, description = "权限中心/员工管理/获取所有下属员工")
    @GetMapping("getAllSubordinateInfo")
    public ListResponseBo<StaffVo> getAllSubordinateInfo(){
        return new ListResponseBo<>(staffService.getAllSubordinateInfo());
    }

    /**
     * 根据职位编号ids
     *
     * @return
     */
    @ApiIgnore
    @PostMapping("getStaffIdsByPositionNums")
    public List<Long> getStaffIdsByPositionNums(@RequestBody Set<String> positionNums) {
        return staffService.getStaffIdsByPositionNums(positionNums);
    }

    /**
     * 户籍类型下拉
     *
     * @return
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "获取户籍类型下拉", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, description = "权限中心/员工管理/户籍类型下拉")
    @PostMapping("getRegisteredResidenceTypeSelect")
    public ResponseBo<Map<String, Object>> getRegisteredResidenceTypeSelect() {
        return new ListResponseBo<>(ProjectExtraEnum.enumsTranslation2Arrays(ProjectExtraEnum.REGISTERED_RESIDENCE_TYPE));

    }

    /**
     * 获取证件类型
     *
     * @return
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "获取证件类型下拉", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, description = "权限中心/员工管理/获取证件类型下拉")
    @PostMapping("getDocumentTypeSelect")
    public ResponseBo<Map<String, Object>> getDocumentTypeSelect() {
        return new ListResponseBo<>(ProjectExtraEnum.enumsTranslation2Arrays(ProjectExtraEnum.DOCUMENT_TYPE));
    }

    /**
     * 模糊查询员工Ids
     *
     * @param keyWord
     * @return
     */
    @ApiIgnore
    @PostMapping("getStaffIdsBykeyWord")
    public List<Long> getStaffIdsBykeyWord(@RequestParam("keyWord") String keyWord) {
        return staffService.getStaffIdsBykeyWord(keyWord);
    }

    /**
     * 根据公司id获取员工下拉框数据
     *
     * @param fkCompanyId
     * @return
     * @
     */
    @ApiIgnore
    @PostMapping("getStaffByCompanyId")
    public List<BaseSelectEntity> getStaffByCompanyId(@RequestParam("fkCompanyId") Long fkCompanyId) {
        return staffService.getStaffByCompanyId(fkCompanyId);
    }

    /**
     * 根据公司获取对应公司员工
     * @return
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "多公司员工下拉框数据", notes = "回显公司下的员工使用")
    @GetMapping("multiCompanyStaffVoList")
    public ResponseBo<CompanyVo> multiCompanyStaffVoList() {
        return new ResponseBo<>(staffService.batchObtainStaffList());
    }



    /**
     * 根据公司ID获取员工列表
     *
     * @param fkCompanyId
     * @return
     * @
     */
    @ApiIgnore
    @ApiOperation(value = "fegin调用，根据公司ID获取员工列表")
    @PostMapping("getStaffs")
    public List<Staff> getStaffs(@RequestParam("fkCompanyId") Long fkCompanyId) {
        return staffService.getStaffs(fkCompanyId);
    }


    /**
     * @ Description :
     * @ Param [staffIds]
     * @ return java.util.List<com.get.common.entity.fegin.StaffVo>
     * @ author LEO
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "fegin调用，根据员工ids找属性")
    @ApiIgnore
    @PostMapping("getStaffByIds")
    public List<StaffVo> getStaffByIds(@RequestBody Set<Long> staffIds) {
        return staffService.getStaffByIds(staffIds);

    }

    /**
     * 根据输入关键字模糊查询员工列表
     *
     * @Date 2:20 2022/03/11
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "员工名模糊查询员工列表")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.LIST, description = "权限中心/员工管理/根据员工名模糊查询员工列表")
    @PostMapping("getAllStaffByStaffName")
    public ResponseBo getAllStaffByStaffName(@RequestParam(value = "staffName") String staffName) {
        List<Long> listStaff = staffService.getStaffListByStaffName(staffName);
        return new ListResponseBo<>(listStaff);
    }

    @ApiIgnore
    @PostMapping("getAllStaffIds")
    public Set<Long> getAllStaffIds() {
        return staffService.getAllStaffIds();
    }

    @PostMapping("/getStaffIsOnDuty")
    public Result<Map<Long,Boolean>> getStaffIsOnDuty(@RequestBody Set<Long> staffIds){
        return staffService.doGetStaffIsOnDuty(staffIds);
    }

    /**
     * @Description :feign调用 通过员工ids 查找对应的员工姓名map 只需要英文名
     * @Param [ids]
     * <AUTHOR>
     */
    @ApiIgnore
    @PostMapping("getStaffEnNameByIds")
    public Map<Long, String> getStaffEnNameByIds(@RequestBody Set<Long> ids) {
        return staffService.getStaffEnNameByIds(ids);

    }



    /**
     * 根据公司id和部门获取员工下拉框数据
     *
     * @return
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "根据公司id和部门获取员工下拉框数据", notes = "")
    @GetMapping("getStaffListByDepartmentId")
    public ResponseBo<StaffListByDepartmentVo> getStaffListByDepartmentId(@RequestParam(value = "companyId") Long companyId,
                                                                          @RequestParam(value = "departmentId") Long departmentId) {
        List<StaffListByDepartmentVo> datas = staffService.getStaffListByDepartmentId(companyId, departmentId);
        return new ListResponseBo<>(datas);
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "用户偏好设置保存(自定义列)", notes = "")
    @PostMapping("addCustomColumnJson")
    public ResponseBo addCustomColumnJson(@RequestBody AddUserPreferencesAddDto addUserPreferencesAddDto) {
        staffService.addCustomColumnJson(addUserPreferencesAddDto);
        return SaveResponseBo.ok();
    }

    /**
     * 新增员工邮箱
     *
     * @Date 17:21 2022/11/21
     * <AUTHOR>
     */
    @ApiOperation(value = "新增员工邮箱")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.EDIT, description = "权限中心/员工管理/新增员工邮箱")
    @PostMapping("addStaffEmail")
    public ResponseBo addStaffEmail(@RequestBody @Validated(StaffEmailDto.Add.class) StaffEmailDto staffEmailDto) {
        staffService.addStaffEmail(staffEmailDto);
        return UpdateResponseBo.ok();
    }

    /**
     * 员工邮箱列表
     *
     * @Date 17:21 2022/11/21
     * <AUTHOR>
     */
    @ApiOperation(value = "员工邮箱详情")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.EDIT, description = "权限中心/员工管理/员工邮箱列表")
    @GetMapping("staffEmailDatas/{staffId}")
    public ResponseBo<StaffEmailVo> staffEmailDatas(@PathVariable("staffId") Long staffId) {
        return UpdateResponseBo.ok( staffService.staffEmailDatas(staffId));
    }

    /**
     * 删除员工邮箱
     *
     * @Date 17:46 2022/11/21
     * <AUTHOR>
     */
    @ApiOperation(value = "删除员工邮箱")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.EDIT, description = "权限中心/员工管理/删除员工邮箱")
    @PostMapping("staffEmailDelete/{id}")
    public ResponseBo staffEmailDelete(@PathVariable("id") Long id) {
        staffService.staffEmailDelete(id);
        return UpdateResponseBo.ok();
    }

    /**
     * 标记员工主邮箱
     *
     * @Date 17:50 2022/11/21
     * <AUTHOR>
     */
    @ApiOperation(value = "标记员工主邮箱")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.EDIT, description = "权限中心/员工管理/标记员工主邮箱")
    @GetMapping("mainStaffEmailMark/{id}")
    public ResponseBo mainStaffEmailMark(@PathVariable("id") Long id) {
        staffService.mainStaffEmailMark(id);
        return UpdateResponseBo.ok();
    }

    /**
     * 编辑员工邮箱
     *
     * @Date 15:46 2022/11/22
     * <AUTHOR>
     */
    @ApiOperation(value = "编辑员工邮箱")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.EDIT, description = "权限中心/员工管理/编辑员工邮箱")
    @PostMapping("staffEmailUpdate")
    public ResponseBo staffEmailUpdate(@RequestBody StaffEmailDto staffEmailDto) {
        staffService.staffEmailUpdate(staffEmailDto);
        return UpdateResponseBo.ok();
    }

    @ApiOperation(value = "分配学校接口")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.EDIT, description = "权限中心/员工管理/分配学校接口")
    @PostMapping("assignSchool")
    public ResponseBo assignSchool(@RequestBody @Validated ValidList<StaffInstitutionDto> staffInstitutionDtos) {
        staffService.assignSchool(staffInstitutionDtos);
        return UpdateResponseBo.ok();
    }

    @ApiOperation(value = "业务学校列表接口")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.EDIT, description = "权限中心/员工管理/业务学校接口")
    @PostMapping("getBusinessSchoolList")
    public ResponseBo<BusinessSchoolVo> getBusinessSchoolList(@RequestBody @Validated SearchBean<BusinessSchoolDto> page) {
        List<BusinessSchoolVo> datas = staffService.getBusinessSchoolList(page.getData(),page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    @ApiOperation(value = "业务学校删除接口", notes = "staffId为员工id，id为学校id")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.DELETE, description = "权限中心/员工管理/业务学校删除接口")
    @PostMapping("deleteBusinessSchool/{staffId}")
    public ResponseBo deleteBusinessSchool(@PathVariable("staffId") Long staffId,@RequestParam("id")Long id) {
        staffService.deleteBusinessSchool(staffId,id);
        return DeleteResponseBo.ok();
    }


    @ApiOperation(value = "分配业务学校列表接口")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.EDIT, description = "权限中心/员工管理/分配业务学校接口")
    @PostMapping("getAssignBusinessSchoolList")
    public ResponseBo<AssignBusinessSchoolVo> getAssignBusinessSchoolList(@RequestBody @Validated SearchBean<AssignBusinessSchoolDto> page) {
        List<AssignBusinessSchoolVo> datas = staffService.getAssignBusinessSchoolList(page.getData(),page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }
    @ApiOperation(value = "根据身份证解析出生日期")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.EDIT, description = "权限中心/员工管理/编辑员工资料")
    @GetMapping("getBirthdayByIDCard/{idCard}")
    public ResponseBo<LocalDate> getBirthdayByIDCard(@PathVariable String idCard) {
        LocalDate birthdayByIDCard = staffService.getBirthdayByIDCard(idCard);
        return UpdateResponseBo.ok(birthdayByIDCard);
}

    /**
     * 获取当前登录用户直接下级id集合
     * @param staffId
     * @return
     */
    @GetMapping(value = "getObtainDirectSubordinatesIds")
    @ApiIgnore
    public List<Long> getObtainDirectSubordinatesIds(@RequestParam(value = "staffId") Long staffId) {
        return staffService.getObtainDirectSubordinatesIds(staffId);
    }
}


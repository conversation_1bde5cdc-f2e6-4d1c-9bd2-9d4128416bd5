<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>sys-service</artifactId>
        <groupId>com.get</groupId>
        <version>1.0.RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>permission-center</artifactId>
    <name>${project.artifactId}</name>
    <version>${get.project.version}</version>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>core-boot</artifactId>
            <version>${get.project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>core-translation</artifactId>
            <version>${get.project.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.2.3</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>5.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>5.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.get</groupId>
            <artifactId>core-swagger</artifactId>
            <version>${get.project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>permission-center-ap</artifactId>
            <version>${get.project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>file-center-ap</artifactId>
            <version>1.0.RELEASE</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>ais-institution-center-ap</artifactId>
            <version>${get.project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>core-auto</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.shiro</groupId>
            <artifactId>shiro-core</artifactId>
            <version>1.3.2</version>
            <scope>compile</scope>
        </dependency>
        <!-- 模拟页面登录依赖包 -->
        <dependency>
            <groupId>net.sourceforge.htmlunit</groupId>
            <artifactId>htmlunit</artifactId>
            <version>2.50.0</version>
        </dependency>
        <!-- 解析html依赖包 -->
        <dependency>
            <groupId>cn.wanghaomiao</groupId>
            <artifactId>JsoupXpath</artifactId>
            <version>0.1.1</version>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>core-cloud</artifactId>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>i18n-center-ap</artifactId>
            <version>1.0.RELEASE</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>ais-reminder-center-ap</artifactId>
            <version>1.0.RELEASE</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>ais-office-center-ap</artifactId>
            <version>1.0.RELEASE</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>file-option-ap</artifactId>
            <version>1.0.RELEASE</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>ais-sale-center-ap</artifactId>
            <version>1.0.RELEASE</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>help-center-ap</artifactId>
            <version>1.0.RELEASE</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.sun.mail</groupId>
            <artifactId>jakarta.mail</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.name}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring.boot.version}</version>
                    <configuration>
                        <fork>true</fork>
                        <finalName>${project.build.finalName}</finalName>
                    </configuration>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>com.spotify</groupId>
                    <artifactId>docker-maven-plugin</artifactId>
                    <version>1.2.2</version>
                    <executions>
                        <execution>
                            <id>build-image</id>
                            <phase>package</phase>
                            <goals>
                                <goal>build</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <!--打包docker镜像的docker服务器-->
                        <dockerHost>${docker-url}</dockerHost>
                        <!--镜像名，这里用工程名 -->
                        <imageName>${registry-url}/${nexus.project_name}/${project.artifactId}:${nexus.version}</imageName>
                        <!--nexus3 hosted 仓库地址-->
                        <registryUrl>${registry-url}</registryUrl>
                        <!-- ca认证正书-->
                        <!--                        <dockerCertPath>./docker/cert-new</dockerCertPath>-->
                        <!--TAG,这里用工程版本号-->
                        <imageTags>
                            <!-- 指定镜像标签,可以排至多个标签 -->
                            <imageTag>${nexus.version}</imageTag>
                            <imageTag>latest</imageTag>
                        </imageTags>
                        <!--是否强制覆盖已有镜像-->
                        <forceTags>true</forceTags>
                        <!--方式一：1、指定Dockerfile文件所在目录，通过文件执行打包上传nexus私服-->
                        <dockerDirectory>sys-service/${project.artifactId}/src/main/docker</dockerDirectory>
                        <!-- 指定docker镜像打包参数，即dockerfile中使用的参数，通过${参数名}取值 -->
                        <buildArgs>
                            <JAR_FILE>${project.build.finalName}.jar</JAR_FILE>
                            <JAR_FILE_NAME>${project.artifactId}.jar</JAR_FILE_NAME>
                        </buildArgs>
                        <resources>
                            <resource>
                                <targetPath>/</targetPath>
                                <directory>${project.build.directory}</directory>
                                <include>${project.build.finalName}.jar</include>
                            </resource>
                        </resources>
                        <serverId>nexus-docker-prod</serverId>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-antrun-plugin</artifactId>
                    <executions>
                        <execution>
                            <phase>package</phase>
                            <goals>
                                <goal>run</goal>
                            </goals>
                            <configuration>
                                <tasks>
                                    <!--suppress UnresolvedMavenProperty -->
                                    <copy overwrite="true"
                                          tofile="${session.executionRootDirectory}/target/${project.artifactId}.jar"
                                          file="${project.build.directory}/${project.artifactId}.jar" />
                                </tasks>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>dockerfile-maven-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven.plugin.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>UTF-8</encoding>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
#数据源配置
server:
  port: 8094

spring:
  datasource:
    url: ${get.datasource.test.url}
    username: ${get.datasource.test.username}
    password: ${get.datasource.test.password}

  #freemarker模板配置
  freemarker:
    suffix: .ftl
    cache: false
    charset: UTF-8
    contentType: text/html
    requestContextAttribute: ctx
    templateEncoding: UTF-8
    templateLoaderPath: classpath:/templates/
    settings:
      defaultEncoding: UTF-8
      url_escaping_charset: UTF-8
      locale: zh_CN
  activiti:
    database-schema-update: false
    #关闭activiti自动部署（使用流程设计器部署，不使用具体文件访问方式）
    check-process-definitions: false
    #保存历史数据级别设置为full最高级别，便于历史数据的追溯
    history-level: full
  resources:
    static-locations: classpath:/
  mvc:
    static-path-pattern: /**
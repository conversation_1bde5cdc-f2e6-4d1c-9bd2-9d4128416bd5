<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:activiti="http://activiti.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" xmlns:tns="http://www.activiti.org/test" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" expressionLanguage="http://www.w3.org/1999/XPath" id="m1589272205882" name="" targetNamespace="http://www.activiti.org/test" typeLanguage="http://www.w3.org/2001/XMLSchema">
  <process id="holiday2" isClosed="false" isExecutable="true" name="holiday2" processType="None">
    <startEvent id="StartEvent" name="StartEvent"/>
    <userTask activiti:assignee="${user.name}" activiti:exclusive="true" id="UserTask" name="鐢ㄦ埛濉啓">
      <extensionElements>
        <activiti:taskListener event="create"/>
      </extensionElements>
    </userTask>
    <userTask activiti:assignee="zg" activiti:exclusive="true" id="zhuguan" name="涓荤�?">
      <extensionElements>
        <activiti:taskListener event="create"/>
        <activiti:taskListener class="listen.Personnel" event="create"/>
      </extensionElements>
    </userTask>
    <userTask activiti:assignee="zj" activiti:async="false" activiti:exclusive="true" id="zonjian" name="鎬荤�?">
      <extensionElements>
        <activiti:taskListener event="create"/>
      </extensionElements>
    </userTask>
    <userTask activiti:assignee="${geauser.name}" activiti:exclusive="true" id="renshi" name="浜轰�?">
      <extensionElements>
        <activiti:taskListener event="create"/>
      </extensionElements>
    </userTask>
    <endEvent id="EndEvent" name="EndEvent"/>
    <exclusiveGateway gatewayDirection="Unspecified" id="ExclusiveGateway" name="ExclusiveGateway"/>
    <sequenceFlow id="_9" sourceRef="ExclusiveGateway" targetRef="zhuguan">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${user.day<=2}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_10" sourceRef="ExclusiveGateway" targetRef="zonjian">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${user.day>=3}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_11" sourceRef="zonjian" targetRef="renshi"/>
    <sequenceFlow id="_12" sourceRef="zhuguan" targetRef="renshi"/>
    <sequenceFlow id="_13" sourceRef="renshi" targetRef="EndEvent"/>
    <sequenceFlow id="_14" sourceRef="UserTask" targetRef="ExclusiveGateway"/>
    <sequenceFlow id="_15" sourceRef="StartEvent" targetRef="UserTask"/>
  </process>
  <bpmndi:BPMNDiagram documentation="background=#FFFFFF;count=1;horizontalcount=1;orientation=0;width=842.4;height=1195.2;imageableWidth=832.4;imageableHeight=1185.2;imageableX=5.0;imageableY=5.0" id="Diagram-_1" name="New Diagram">
    <bpmndi:BPMNPlane bpmnElement="holiday2">
      <bpmndi:BPMNShape bpmnElement="StartEvent" id="Shape-StartEvent">
        <omgdc:Bounds height="32.0" width="32.0" x="360.0" y="-10.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="32.0" width="32.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="UserTask" id="Shape-UserTask">
        <omgdc:Bounds height="55.0" width="85.0" x="365.0" y="50.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="55.0" width="85.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="zhuguan" id="Shape-zhuguan">
        <omgdc:Bounds height="55.0" width="85.0" x="230.0" y="200.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="55.0" width="85.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="zonjian" id="Shape-zonjian">
        <omgdc:Bounds height="55.0" width="85.0" x="510.0" y="205.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="55.0" width="85.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="renshi" id="Shape-renshi">
        <omgdc:Bounds height="55.0" width="85.0" x="360.0" y="330.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="55.0" width="85.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="EndEvent" id="Shape-EndEvent">
        <omgdc:Bounds height="32.0" width="32.0" x="405.0" y="440.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="32.0" width="32.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="ExclusiveGateway" id="Shape-ExclusiveGateway" isMarkerVisible="false">
        <omgdc:Bounds height="32.0" width="32.0" x="395.0" y="140.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="32.0" width="32.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="_13" id="BPMNEdge__13" sourceElement="renshi" targetElement="EndEvent">
        <omgdi:waypoint x="421.0" y="385.0"/>
        <omgdi:waypoint x="421.0" y="440.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_12" id="BPMNEdge__12" sourceElement="zhuguan" targetElement="renshi">
        <omgdi:waypoint x="270.0" y="255.0"/>
        <omgdi:waypoint x="270.0" y="275.0"/>
        <omgdi:waypoint x="360.0" y="357.5"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_15" id="BPMNEdge__15" sourceElement="StartEvent" targetElement="UserTask">
        <omgdi:waypoint x="378.5" y="21.803480629279107"/>
        <omgdi:waypoint x="378.5" y="50.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_14" id="BPMNEdge__14" sourceElement="UserTask" targetElement="ExclusiveGateway">
        <omgdi:waypoint x="411.0" y="105.0"/>
        <omgdi:waypoint x="411.0" y="140.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_9" id="BPMNEdge__9" sourceElement="ExclusiveGateway" targetElement="zhuguan">
        <omgdi:waypoint x="395.0" y="156.0"/>
        <omgdi:waypoint x="270.0" y="180.0"/>
        <omgdi:waypoint x="270.0" y="200.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_11" id="BPMNEdge__11" sourceElement="zonjian" targetElement="renshi">
        <omgdi:waypoint x="555.0" y="260.0"/>
        <omgdi:waypoint x="555.0" y="305.0"/>
        <omgdi:waypoint x="445.0" y="357.5"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_10" id="BPMNEdge__10" sourceElement="ExclusiveGateway" targetElement="zonjian">
        <omgdi:waypoint x="427.0" y="156.0"/>
        <omgdi:waypoint x="560.0" y="185.0"/>
        <omgdi:waypoint x="560.0" y="205.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>

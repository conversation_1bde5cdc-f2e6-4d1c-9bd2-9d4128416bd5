pipeline {
     agent {
    kubernetes {
        inheritFrom "jenkins-agent:v8"
        yaml """
kind: Pod
metadata:
  name: jenkins-slave
spec:
  affinity:
    podAntiAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        - labelSelector:
            matchExpressions:
              - key: jenkins
                operator: In
                values: [slave]
          topologyKey: kubernetes.io/hostname
  containers:
  - name: jnlp
    image: "************/library/jenkins-agent:v8"
    resources:
      limits:
        memory: "8192Mi"
        cpu: "4"
      requests:
        memory: "2048Mi"
        cpu: "1"
    volumeMounts:
      - name: docker-cmd
        mountPath: /usr/bin/docker
      - name: docker-sock
        mountPath: /var/run/docker.sock
      - name: maven-cache
        mountPath: /root/.m2
  volumes:
    - name: docker-cmd
      hostPath:
        path: /usr/bin/docker
    - name: docker-sock
      hostPath:
        path: /var/run/docker.sock
    - name: maven-cache
      persistentVolumeClaim:
        claimName: jenkins-k8s-data-pvc
  env:
    - name: JAVA_OPTS
      value: "-Xmx3048m -XX:+UseG1GC"
"""
        }

      }

    parameters {
        choice(name: 'service_name', choices: ['gateway', 'authentication', 'permission-center', 'ais-sale-center','ais-institution-center', 'ais-middle-center', 'help-center','ais-insurance-center','ais-rocketmq-center',  'ais-exam-center', 'ais-finance-center', 'ais-mps-center', 'ais-pmp-center',  'ais-office-center', 'ais-registration-center', 'ais-reminder-center', 'ais-report-center', 'ais-resume-center', 'ais-voting-center', 'ais-partner-center', 'ais-mail-center', 'file-center','ais-platform-center', 'platform-config-center', 'sys-log', 'sys-swagger', 'websocket', 'workflow-center', 'xxljob', 'xxljob-admin', 'sys-boot-admin', 'seata-demo'], description: '请选择服务')
    }

    environment {
        GITHUB_CREDENTIAL_ID = 'fzhharborid'
        KUBECONFIG_CREDENTIAL_ID = '0691e53c-7065-4756-a465-e0791002c55c'
        REGISTRY = '************'
        DOCKERHUB_NAMESPACE = 'zxl_docker_9394'
    }

    stages {
        stage ('checkout scm') {
            steps {
                script {
                    switch(service_name) {
                        case "gateway":
                            projectName = "gateway"
                            sh 'echo build gateway'
                            break
                        case "authentication":
                            projectName = "authentication"
                            sh 'echo build authentication'
                            break
                        case "permission-center":
                            projectName = "sys-service/permission-center"
                            sh 'echo build permission-center'
                            break
                        case "ais-sale-center":
                            projectName = "biz-service/ais-sale-center"
                            sh 'echo build permission-center'
                            break
                        case "ais-institution-center":
                            projectName = "biz-service/ais-institution-center"
                            sh 'echo build ais-institution-center'
                            break
                        case "ais-middle-center":
                            projectName = "biz-service/ais-middle-center"
                            sh 'echo build ais-middle-center'
                            break
                        case "help-center":
                            projectName = "sys-service/help-center"
                            sh 'echo build help-center'
                            break
                        case "ais-insurance-center":
                            projectName = "biz-service/ais-insurance-center"
                            sh 'echo build ais-insurance-center'
                            break
                        case "ais-rocketmq-center":
                            projectName = "biz-service/ais-rocketmq-center"
                            sh 'echo build ais-rocketmq-center'
                            break
                        case "ais-exam-center":
                            projectName = "biz-service/ais-exam-center"
                            sh 'echo build exam-center'
                            break
                        case "ais-finance-center":
                            projectName = "biz-service/ais-finance-center"
                            sh 'echo build finance-center'
                            break
                        case "ais-mps-center":
                            projectName = "biz-service/ais-mps-center"
                            sh 'echo build mps-center'
                            break
                        case "ais-pmp-center":
                            projectName = "biz-service/ais-pmp-center"
                            sh 'echo build pmp-center'
                            break
                        case "ais-office-center":
                            projectName = "biz-service/ais-office-center"
                            sh 'echo build office-center'
                            break
                        case "ais-registration-center":
                            projectName = "biz-service/ais-registration-center"
                            sh 'echo build registration-center'
                            break
                        case "ais-reminder-center":
                            projectName = "biz-service/ais-reminder-center"
                            sh 'echo build reminder-center'
                            break
                        case "ais-report-center":
                            projectName = "biz-service/ais-report-center"
                            sh 'echo build report-center'
                            break
                        case "ais-resume-center":
                            projectName = "biz-service/ais-resume-center"
                            sh 'echo build resume-center'
                            break
                        case "ais-voting-center":
                            projectName = "biz-service/ais-voting-center"
                            sh 'echo build voting-center'
                            break
                        case "ais-partner-center":
                            projectName = "biz-service/ais-partner-center"
                            sh 'echo build partner-center'
                            break
                        case "ais-mail-center":
                            projectName = "biz-service/ais-mail-center"
                            sh 'echo build mail-center'
                            break
                        case "ais-platform-center":
                            projectName = "biz-service/ais-platform-center"
                            sh 'echo build ais-platform-center'
                            break
                        case "file-center":
                            projectName = "sys-service/file-center"
                            sh 'echo build file-center'
                            break
                        case "platform-config-center":
                            projectName = "sys-service/platform-config-center"
                            sh 'echo build platform-config-center'
                            break
                        case "sys-log":
                            projectName = "sys-service/sys-log"
                            sh 'echo build sys-log'
                            break
                        case "sys-swagger":
                            projectName = "sys-service/sys-swagger"
                            sh 'echo build sys-swagger'
                            break
                        case "websocket":
                            projectName = "sys-service/websocket"
                            sh 'echo build websocket'
                            break
                        case "workflow-center":
                            projectName = "sys-service/workflow-center"
                            sh 'echo build workflow-center'
                            break
                        case "xxljob":
                            projectName = "sys-service/xxljob"
                            sh 'echo build xxljob'
                            break
                        case "xxljob-admin":
                            projectName = "sys-service/xxljob-admin"
                            sh 'echo build xxljob-admin'
                            break
                        case "sys-boot-admin":
                            projectName = "sys-service/sys-boot-admin"
                            sh 'echo build sys-boot-admin'
                            break
                        case "seata-demo":
                            projectName = "sys-service/seata-demo"
                            sh 'echo build seata-demo'
                            break
                    }
                }
            }
        }

        stage ('build & push') {
            steps {
                script {
                    sh " rm -rf /mnt/fzh/kubernetes/jenkins-home/workspace/AIS@script/1ccf215d05ee86e62a16bc5575e17c2369877b1617c71f33a53132d2806055f4/.git/index.lock"
                    checkout(scm)
                    def commitHash = env.GIT_COMMIT
                    def now = new Date()
                    def gitHash=commitHash[0..7]
                    def VERSION="$gitHash-${now.format('yyyyMMdd-HHmmss')}"
                    echo "Latest commit hash: ${commitHash}-${now.format('yyyyMMdd-HH:mm:ss')}"
                    sh " rm -rf /root/.m2/repository/com/get/${projectName.split('/').last()}"
                    sh " mvn clean package -T 2C -pl $projectName -am -e -U -Dmaven.test.skip=true -Dmaven.compile.fork=true  -Dmaven.compiler.useIncrementalCompilation=false "
                    sh " cd ${pwd()}/$projectName && docker build -f src/main/docker/Dockerfile . -t $REGISTRY/hti-java-ais/$projectName-$Branch:$VERSION "
                    sh " docker login -u admin -p Fzhharbor2024 $REGISTRY"
                    sh " docker push $REGISTRY/hti-java-ais/$projectName-$Branch:$VERSION"
                }
            }
        }
    }
}
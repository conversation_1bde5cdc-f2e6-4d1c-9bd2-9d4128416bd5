package com.get.schoolGateCenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @DATE: 2020/8/20
 * @TIME: 17:49
 * @Description:
 **/
@Data
public class ContractVo extends BaseVoEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 学校提供商Id
     */
    @ApiModelProperty(value = "学校提供商Id", required = true)
    @NotNull(message = "学校提供商Id", groups = {Add.class, Update.class})
    private Long fkInstitutionProviderId;
    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    private Long fkInstitutionId;
    /**
     * 学校课程Id
     */
    @ApiModelProperty(value = "学校课程Id")
    private Long fkInstitutionCourseId;
    /**
     * 合同类型
     */
    @ApiModelProperty(value = "合同类型", required = true)
    @NotNull(message = "合同类型不能为空", groups = {Add.class, Update.class})
    private Long fkContractTypeId;
    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号", required = true)
    @NotBlank(message = "合同编号不能为空", groups = {Update.class})
    private String contractNum;
    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;
    /**
     * 合同开始时间
     */
    @ApiModelProperty(value = "合同开始时间", required = true)
    @NotNull(message = "合同开始时间不能为空", groups = {Add.class, Update.class})
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTime;
    /**
     * 合同结束时间
     */
    @ApiModelProperty(value = "合同结束时间", required = true)
    @NotNull(message = "合同结束时间不能为空", groups = {Add.class, Update.class})
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;
    /**
     * 市场费用
     */
    @ApiModelProperty(value = "市场费用", required = true)
    @NotNull(message = "市场费用不能为空", groups = {Add.class, Update.class})
    private String marketExpense;
    /**
     * 后续费用
     */
    @ApiModelProperty(value = "后续费用", required = true)
    @NotNull(message = "后续费用不能为空", groups = {Add.class, Update.class})
    private String followUpExpense;
    /**
     * 实际佣金
     */
    @ApiModelProperty(value = "实际佣金", required = true)
    @NotNull(message = "实际佣金不能为空", groups = {Add.class, Update.class})
    private String commissionRate;
    /**
     * 后续佣金
     */
    @ApiModelProperty(value = "后续佣金", required = true)
    @NotNull(message = "后续佣金不能为空", groups = {Add.class, Update.class})
    private String followUpCommissionRate;
    /**
     * 合同备注
     */
    @ApiModelProperty(value = "合同备注")
    private String remark;
    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是", required = true)
    @NotNull(message = "是否激活不能为空", groups = {Add.class, Update.class})
    private Boolean isActive;
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "查询条件公司Id")
    private Long fkCompanyId;
    /**
     * 国家Id
     */
    @ApiModelProperty(value = "国家Id")
    private Long fkCountryId;
    /**
     * 关键字
     */
    @ApiModelProperty(value = "关键字")
    private String keyWord;
    /**
     * 0新签/1续签
     */
    @ApiModelProperty(value = "0新签/1续签")
    private Integer contractApprovalMode;
    /**
     * 合同表父id
     */
    @ApiModelProperty(value = "合同表父id")
    private Long fkContractIdRevoke;
    /**
     * 流程状态
     */
    @ApiModelProperty(value = "流程状态")
    private Integer status;
    /**
     * 1查询全部，0查询个人，2我的审批
     */
    @ApiModelProperty(value = "1查询全部，0查询个人，2我的审批")
    private Integer selectStatus;
    /**
     * 流程key
     */
    @ApiModelProperty("流程key")
    private String procdkey;


}

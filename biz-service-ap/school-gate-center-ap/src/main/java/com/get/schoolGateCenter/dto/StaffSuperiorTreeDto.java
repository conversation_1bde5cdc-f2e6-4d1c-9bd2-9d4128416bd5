package com.get.schoolGateCenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/7/28
 * @TIME: 10:04
 * @Description: 员工上司树
 **/

@Data
public class StaffSuperiorTreeDto implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "公司ID")
    private String companyId;
    @ApiModelProperty(value = "公司名称")
    private String companyName;
    @ApiModelProperty(value = "父级公司ID")
    private Long fkParentCompanyId;
    @ApiModelProperty(value = "公司编号")
    private String companyNum;
    @ApiModelProperty(value = "子公司")
    private List<StaffSuperiorTreeDto> childList;
    @ApiModelProperty(value = "公司部门集合")
    private List<DepartmentTreeDto> departmentTree;
    /**
     * 部门数量
     */
    @ApiModelProperty(value = "部门数量")
    private Integer totalNum;
    /**
     * 部门
     */
    @ApiModelProperty("部门")
    private String departmentName;
    /**
     * 职位
     */
    @ApiModelProperty("职位")
    private String positionName;
    /**
     * 部门Id
     */
    @ApiModelProperty("部门Id")
    private Long departmentId;
    /**
     * 职位Id
     */
    @ApiModelProperty("职位Id")
    private Long positionId;
    /**
     * 员工编号
     */
    @ApiModelProperty("上司编号")
    private String staffNum;
    /**
     * 上司姓名
     */
    @ApiModelProperty("上司姓名")
    private String staffName;
    /**
     * 上司ID
     */
    @ApiModelProperty(value = "上司ID")
    private String staffId;
}

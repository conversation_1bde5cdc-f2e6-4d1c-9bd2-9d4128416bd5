package com.get.schoolGateCenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("u_area_city_division")
public class AreaCityDivision extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 城市Id
     */
    @ApiModelProperty(value = "城市Id")
    @Column(name = "fk_area_city_id")
    private Long fkAreaCityId;
    /**
     * 区域名称
     */
    @ApiModelProperty(value = "区域名称")
    @Column(name = "name")
    private String name;
    /**
     * 街道名称，逗号隔开，如：洪桥街道,北京街道,六榕街道
     */
    @ApiModelProperty(value = "街道名称，逗号隔开，如：洪桥街道,北京街道,六榕街道")
    @Column(name = "streets_name")
    private String streetsName;
    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;
}
package com.get.schoolGateCenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @DATE: 2020/8/10
 * @TIME: 17:22
 * @Description:
 **/
@Data
public class ResumeCertificateVo extends BaseVoEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 所属简历Id
     */
    @NotNull(message = "简历id不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "所属简历Id", required = true)
    private Long fkResumeId;
    /**
     * 获得时间
     */
    @ApiModelProperty(value = "获得时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date getDate;
    /**
     * 证书名称
     */
    @ApiModelProperty(value = "证书名称")
    private String certificate;
    /**
     * 成绩
     */
    @ApiModelProperty(value = "成绩")
    private String score;
}

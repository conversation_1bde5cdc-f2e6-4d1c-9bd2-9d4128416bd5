package com.get.schoolGateCenter.vo;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: Sea
 * @create: 2020/8/6 11:42
 * @verison: 1.0
 * @description:
 */
@Data
public class RNewsTypeVo extends BaseVoEntity {
    /**
     * 新闻Id
     */
    @ApiModelProperty(value = "新闻Id")
    private Long fkNewsId;

    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String fkTableName;

    /**
     * 表Id
     */
    @ApiModelProperty(value = "表Id")
    private Long fkTableId;

    /**
     * 新闻类型Id
     */
    @ApiModelProperty(value = "新闻类型Id")
    private Long fkNewsTypeId;

}

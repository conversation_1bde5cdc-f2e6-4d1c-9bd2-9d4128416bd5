package com.get.schoolGateCenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/8/5
 * @TIME: 16:01
 * @Description:学校专业学科实体类vo
 **/
@Data
public class InstitutionCourseVo extends BaseVoEntity {
    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    @NotNull(message = "fkInstitutionId", groups = {Add.class, Update.class})
    private Long fkInstitutionId;
    /**
     * 学校学院ID
     */
    @ApiModelProperty(value = "学校学院ID")
    private Long fkInstitutionFacultyId;
    /**
     * 学校学院数组
     */
    @ApiModelProperty(value = "学校学院数组")
    private List<Long> fkInstitutionFacultyIds;
    /**
     * 专业等级id
     */
    @ApiModelProperty(value = "专业等级id")
    private Long fkMajorLevelId;
    /**
     * 专业等级数组
     */
    @ApiModelProperty(value = "专业等级数组")
    @NotNull(message = "fkMajorLevelIds", groups = {Add.class, Update.class})
    private List<Long> fkMajorLevelIds;
    /**
     * 课程类型id
     */
    @ApiModelProperty(value = "课程类型id")
    private Long fkCourseTypeId;
    /**
     * 课程类型数组
     */
    @ApiModelProperty(value = "课程类型数组")
    @NotNull(message = "fkCourseTypeIds", groups = {Add.class, Update.class})
    private List<Long> fkCourseTypeIds;

    /**
     * 课程编号
     */
    @ApiModelProperty(value = "课程编号")
    private String num;
    /**
     * 显示名称
     */
    @ApiModelProperty(value = "显示名称")
    private String nameDisplay;
    /**
     * 课程名称
     */
    @ApiModelProperty(value = "课程名称")
    @NotBlank(message = "name", groups = {Add.class, Update.class})
    private String name;

    /**
     * 课程中文名称
     */
    @ApiModelProperty(value = "课程中文名称")
    private String nameChn;


    /**
     * 开学月份
     */
    @ApiModelProperty(value = "开学月份")
    private String startMonth;

    /**
     * 开学时间描述
     */
    @ApiModelProperty(value = "开学时间描述")
    private String startDateNote;

    /**
     * 申请开始时间
     */
    @ApiModelProperty(value = "申请开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date applyStartDate;

    /**
     * 申请截止时间
     */
    @ApiModelProperty(value = "申请截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date applyEndDate;

    /**
     * 申请时间描述
     */
    @ApiModelProperty(value = "申请时间描述")
    private String applyDateNote;

    /**
     * 课程学费
     */
    @ApiModelProperty(value = "课程学费（最低）")
    private BigDecimal fee;
    /**
     * 课程学费（最高）
     */
    @ApiModelProperty(value = "课程学费（最高）")
    private BigDecimal feeMax;
    /**
     * 课程学费说明
     */
    @ApiModelProperty(value = "课程学费说明")
    private String feeNote;

    /**
     * 课程总时长（年）
     */
    @ApiModelProperty(value = "课程总时长（年）")
    private BigDecimal durationYear;

    /**
     * 课程总时长说明
     */
    @ApiModelProperty(value = "课程总时长说明")
    private String durationNote;

    /**
     * 简介
     */
    @ApiModelProperty(value = "简介")
    private String introduction;

    /**
     * 核心课程
     */
    @ApiModelProperty(value = "核心课程")
    private String coreCourse;

    /**
     * 录取标准
     */
    @ApiModelProperty(value = "录取标准")
    private String entryStandards;

    /**
     * 职业发展
     */
    @ApiModelProperty(value = "职业发展")
    private String occupationDevelopment;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @NotNull(message = "isActive", groups = {Add.class, Update.class})
    private Boolean isActive;

    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    /**
     * 课程code（校方编号）
     */
    @ApiModelProperty(value = "课程code（校方编号）")
    private String code;

    /**
     * 公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2
     */
    @ApiModelProperty(value = "公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
    @NotBlank(message = "publicLevel", groups = {Add.class, Update.class})
    private String publicLevel;

    /**
     * 数据等级：0旧数据导入/1官方数据导入/2需要补充/3采集完整
     */
    @ApiModelProperty(value = "数据等级：0旧数据导入/1官方数据导入/2需要补充/3采集完整")
    @NotNull(message = "数据等级", groups = {Add.class, Update.class})
    private Integer dataLevel;

    /**
     * 校区id
     */
    @ApiModelProperty(value = "校区id")
    private Long fkInstitutionZoneId;

    /**
     * 校区id数组
     */
    @ApiModelProperty(value = "校区id数组")
    @NotNull(message = "fkInstitutionZoneIds", groups = {Add.class, Update.class})
    private List<Long> fkInstitutionZoneIds;

    /**
     * 申请月份，多月逗号分隔：9
     */
    @ApiModelProperty(value = "申请月份，多月逗号分隔：9")
    private String applyMonth;
    /**
     * 旧数据id(gea)
     */
    @ApiModelProperty(value = "旧数据id(gea)")
    private String idGea;

    /**
     * 旧数据id(iae)
     */
    @ApiModelProperty(value = "旧数据id(iae)")
    private String idIae;

    /**
     * 国家ID
     */
    @ApiModelProperty(value = "国家ID")
    private Long fkAreaCountryId;
    /**
     * 课程学费（统一为人民币，主要是学费筛选使用）
     */
    @ApiModelProperty(value = "课程学费（统一为人民币，主要是学费筛选使用）")
    private BigDecimal feeCny;

    /**
     * 排序字段
     */
    @ApiModelProperty(value = "排序字段")
    private String orderBy;

//    /**
//     * 编辑标记 true:编辑中 展示所有课程  false:不是编辑中
//     */
//    @ApiModelProperty(value = "编辑标记 true:编辑中 展示所有课程  false:不是编辑中")
//    private Boolean updateFlag;

    @ApiModelProperty(value = "正在更新的课程id")
    private Long updateCourseId;

    /**
     * 学校类型Id
     */
    @ApiModelProperty(value = "学校类型Id", required = true)
    private Long fkInstitutionTypeId;
}

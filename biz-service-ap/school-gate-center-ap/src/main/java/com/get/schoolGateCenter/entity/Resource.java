package com.get.schoolGateCenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.ibatis.type.Alias;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("m_resource")
@Alias("SchoolGateResource")
public class Resource extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 父系统资源Id
     */
    @ApiModelProperty(value = "父系统资源Id")
    @Column(name = "fk_parent_resource_id")
    private Long fkParentResourceId;

    /**
     * 系统资源Key（主要前端识别）
     */
    @ApiModelProperty(value = "系统资源Key（主要前端识别）")
    @Column(name = "resource_key")
    private String resourceKey;

    /**
     * 系统资源名称
     */
    @ApiModelProperty(value = "系统资源名称")
    @Column(name = "resource_name")
    private String resourceName;

    /**
     * api交互_key（主要后端识别）
     */
    @ApiModelProperty(value = "api交互_key（主要后端识别）")
    @Column(name = "api_key")
    private String apiKey;

    /**
     * 是否菜单，0否/1是
     */
    @ApiModelProperty(value = "是否菜单，0否/1是")
    @Column(name = "is_menu")
    private Boolean isMenu;


    /**
     * 排序，数字由小到大排列
     */
    @ApiModelProperty(value = "排序，数字由小到大排列")
    @Column(name = "view_order")
    private Integer viewOrder;


}
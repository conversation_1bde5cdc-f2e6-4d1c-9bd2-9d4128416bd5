package com.get.schoolGateCenter.dto;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/9/4
 * @TIME: 16:42
 * @Description: 所有的公司，不分上下级
 **/
@Data
public class CompanyParallelDto extends BaseEntity {
    @ApiModelProperty(value = "公司名称")
    private String name;
    @ApiModelProperty(value = "公司简称")
    private String shortName;
    @ApiModelProperty(value = "父级公司ID")
    private Long fkParentCompanyId;
    @ApiModelProperty(value = "公司编号")
    private String num;
    @ApiModelProperty(value = "公司部门集合")
    private List<DepartmentTreeDto> departmentTree;
    @ApiModelProperty(value = "部门数量")
    private Integer totalNum;
}

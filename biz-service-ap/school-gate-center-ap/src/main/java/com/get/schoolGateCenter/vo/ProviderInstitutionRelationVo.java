package com.get.schoolGateCenter.vo;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/8/25
 * @TIME: 14:48
 * @Description:
 **/
@Data
public class ProviderInstitutionRelationVo extends BaseVoEntity {
    /**
     * 学校编号
     */
    @ApiModelProperty(value = "学校编号")
    private String num;

    /**
     * 学校名称
     */
    @ApiModelProperty(value = "学校名称")
    private String name;

    /**
     * 国家Name
     */
    @ApiModelProperty(value = "国家名称")
    private String fkAreaCountryName;

    /**
     * 国家Id
     */
    @ApiModelProperty(value = "国家Id", required = true)
    @NotNull(message = "国家Id不能为空", groups = {Add.class, Update.class})
    private Long fkAreaCountryId;


    /**
     * 标记
     */
    @ApiModelProperty(value = "是否选中")
    private Boolean flag;

    /**
     * 提供商id
     */
    @ApiModelProperty(value = "提供商id", required = true)
    @NotNull(message = "学校名称不能为空", groups = {Add.class, Update.class})
    private Long fkProviderId;


    /**
     * @Description: 学校类型
     * @Param
     * @return
     * <AUTHOR>
     */
    @ApiModelProperty(value = "学校类型", required = true)
    @NotNull(message = "学校类型Id不能为空", groups = {Add.class, Update.class})
    private Long fkInstitutionTypeId;

    @ApiModelProperty(value = "关键词")
    private String keyWord;

    @ApiModelProperty(value = "国家ids")
    private List<Long> countryIds;
}

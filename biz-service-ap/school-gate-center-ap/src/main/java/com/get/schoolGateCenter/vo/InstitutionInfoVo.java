package com.get.schoolGateCenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @DATE: 2020/8/11
 * @TIME: 10:07
 * @Description:
 **/
@Data
public class InstitutionInfoVo extends BaseVoEntity {
    /**
     * 资讯类型Id
     */
    @ApiModelProperty(value = "资讯类型Id", required = true)
    @NotNull(message = "资讯类型Id不能为空", groups = {Add.class, Update.class})
    private Long fkInfoTypeId;

    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id", required = true)
    @NotNull(message = "学校Id不能为空", groups = {Add.class, Update.class})
    private Long fkInstitutionId;

    /**
     * 标题
     */
    @NotBlank(message = "标题不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "标题", required = true)
    private String title;

    /**
     * 内容描述
     */
    @NotBlank(message = "内容描述不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "内容描述", required = true)
    private String description;

    /**
     * 排序，数字由小到大排列
     */
    @NotNull(message = "排序不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "排序，数字由小到大排列", required = true)
    private Integer viewOrder;

    @ApiModelProperty(value = "关键词")
    private String keyWord;
    /**
     * 简介
     */
    @ApiModelProperty(value = "简介")
    private String profile;
    /**
     * 公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2
     */
    @ApiModelProperty(value = "公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
    @NotBlank(message = "公开对象", groups = {Add.class, Update.class})
    private String publicLevel;

    /**
     * 发布时间
     */
    @ApiModelProperty(value = "发布时间")
    @NotNull(message = "发布时间不能为空", groups = {Add.class, Update.class})
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "publish_time")
    private Date publishTime;

    /**
     * 网页标题
     */
    @ApiModelProperty(value = "网页标题")
    private String webTitle;

    /**
     * 网页Meta描述
     */
    @ApiModelProperty(value = "网页Meta描述")
    private String webMetaDescription;

    /**
     * 网页Meta关键字
     */
    @ApiModelProperty(value = "网页Meta关键字")
    private String webMetaKeywords;
}

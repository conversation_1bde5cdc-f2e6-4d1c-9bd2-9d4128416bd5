package com.get.schoolGateCenter.dto;

import com.get.common.annotion.TableDto;
import com.get.schoolGateCenter.entity.Institution;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/8/4
 * @TIME: 18:20
 * @Description:学校dto
 **/
@ApiModel("学校返回类")
@Data
public class InstitutionDto extends Institution {

    /**
     * 国家Name
     */
    @ApiModelProperty(value = "国家名称")
    private String fkAreaCountryName;
    /**
     * 州省Name
     */
    @ApiModelProperty(value = "州省名称")
    private String fkAreaStateName;

    /**
     * 城市Name
     */
    @ApiModelProperty(value = "城市名称")
    private String fkAreaCityName;
    /**
     * 类型Name
     */
    @ApiModelProperty(value = "学校类型名称")
    @TableDto(tableName = "u_institution_type", columnDto = "type_name", entityColumnDto = "fkInstitutionTypeName", columnDtoMainId = "fk_institution_type_id")
    private String fkInstitutionTypeName;
    /**
     * 类型Key
     */
    @ApiModelProperty(value = "学校类型key")
    @TableDto(tableName = "u_institution_type", columnDto = "type_key", entityColumnDto = "fkInstitutionTypeKey", columnDtoMainId = "fk_institution_type_id")
    private String fkInstitutionTypeKey;
    /**
     * 课程数
     */
    @ApiModelProperty(value = "课程数")
    private Integer courseCount;

    /**
     * 学校图片
     */
    @ApiModelProperty(value = "学校图片")
    private MediaAndAttachedDto mediaAndAttachedDto;

    @ApiModelProperty(value = "学校封面图")
    private MediaAndAttachedDto instutionCover;

    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String fkTableName;
    /**
     * 全称
     */
    @ApiModelProperty(value = "全称")
    private String fullName;
    /**
     * 货币名称
     */
    @ApiModelProperty(value = "货币名称")
    private String currencyName;
    /**
     * 公开对象名称
     */
    @ApiModelProperty(value = "公开对象名称")
    private String publicLevelName;

    /**
     * 数据等级名称
     */
    @ApiModelProperty(value = "数据等级名称")
    private String dataLevelName;
    /**
     * 学校提供商和学校中间表id
     */
    @ApiModelProperty(value = "学校提供商和学校中间表id")
    private String fkId;


    /**
     * 招生国家/地区限制
     */
    @ApiModelProperty(value = "招生国家/地区限制")
    private String areaCountryIdLimitName;

    /**
     * 重点推荐的学校等级：1小推荐/2中推荐/3大推荐
     */
    @ApiModelProperty(value = "重点推荐的学校等级：1小推荐/2中推荐/3大推荐")
    private String kpiLevelName;
    /**
     * 校区统计
     */
    @ApiModelProperty(value = "校区统计")
    private Integer zoneNum;
    /**
     * 院校统计
     */
    @ApiModelProperty(value = "院校统计")
    private Integer facultyNum;
    /**
     * 课程统计
     */
    @ApiModelProperty(value = "课程统计")
    private Integer courseNum;
    /**
     * 常见问题统计
     */
    @ApiModelProperty(value = "常见问题统计")
    private Integer faqNum;
    /**
     * 知名校友统计
     */
    @ApiModelProperty(value = "知名校友统计")
    private Integer alumnusNum;
    /**
     * 资讯统计
     */
    @ApiModelProperty(value = "资讯统计")
    private Integer infoNum;

    /**
     * 奖学金统计
     */
    @ApiModelProperty(value = "奖学金统计")
    private Integer scholarshipNum;
    /**
     * 申请费用统计
     */
    @ApiModelProperty(value = "申请费用统计")
    private Integer appFeeNum;
    /**
     * 申请截止统计
     */
    @ApiModelProperty(value = "申请截止统计")
    private Integer deadlineInfoNum;
    /**
     * 是否合作
     */
    @ApiModelProperty(value = "合作状态：0无/1有")
    private Boolean isBindingActive;

    @ApiModelProperty("学校特性")
    private List<CharacterDto> characterDtos;


}

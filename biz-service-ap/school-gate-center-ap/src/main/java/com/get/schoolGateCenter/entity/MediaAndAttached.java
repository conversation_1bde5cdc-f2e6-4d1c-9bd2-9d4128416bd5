package com.get.schoolGateCenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("s_media_and_attached")
public class MediaAndAttached extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 文件guid(文档中心)
     */
    @ApiModelProperty(value = "文件guid(文档中心)")
    @Column(name = "fk_file_guid")
    private String fkFileGuid;
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    @Column(name = "fk_table_name")
    private String fkTableName;
    /**
     * 表Id
     */
    @ApiModelProperty(value = "表Id")
    @Column(name = "fk_table_id")
    private Long fkTableId;
    /**
     * 类型关键字，如：institution_mov/institution_pic/alumnus_head_icon
     */
    @ApiModelProperty(value = "类型关键字，如：institution_mov/institution_pic/alumnus_head_icon")
    @Column(name = "type_key")
    private String typeKey;
    /**
     * 索引值(默认从0开始，同一类型下值唯一)
     */
    @ApiModelProperty(value = "索引值(默认从0开始，同一类型下值唯一)")
    @Column(name = "index_key")
    private Integer indexKey;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;
    /**
     * 链接
     */
    @ApiModelProperty(value = "链接")
    @Column(name = "link")
    private String link;


    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", fkFileGuid=").append(fkFileGuid);
        sb.append(", fkTableName=").append(fkTableName);
        sb.append(", fkTableId=").append(fkTableId);
        sb.append(", typeKey=").append(typeKey);
        sb.append(", indexKey=").append(indexKey);
        sb.append(", remark=").append(remark);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}
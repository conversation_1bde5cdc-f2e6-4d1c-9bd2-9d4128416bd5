package com.get.schoolGateCenter.dto;

import com.get.schoolGateCenter.entity.Resume;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/7/31
 * @TIME: 16:18
 * @Description:
 **/
@Data
public class ResumeDto extends Resume {
    /**
     * 头像链接
     */
    @ApiModelProperty(value = "头像链接")
    private MediaAndAttachedDto headImageDto;

    /**
     * 公司图标链接
     */
    @ApiModelProperty(value = "公司Logo链接")
    private MediaAndAttachedDto companyIconDto;

    /**
     * 职业意向
     */
    @ApiModelProperty(value = "职业意向")
    private ResumeIntentionDto resumeIntentionDto;


    /**
     * 工作经验
     */
    @ApiModelProperty(value = "工作经验")
    private List<ResumeWorkDto> resumeWorkDtos;

    /**
     * 工作经验
     */
    @ApiModelProperty(value = "教育经历")
    private List<ResumeEducationDto> resumeEducationDtos;

    /**
     * 技能
     */
    @ApiModelProperty(value = "技能")
    private List<ResumeSkillDto> resumeSkillDtos;

    /**
     * 证书
     */
    @ApiModelProperty(value = "证书")
    private List<ResumeCertificateDto> resumeCertificateDtos;


    /**
     * 培训经历
     */
    @ApiModelProperty(value = "培训经历")
    private List<ResumeTrainingDto> resumeTrainingDtos;


    /**
     * 附加信息
     */
    @ApiModelProperty(value = "附加信息")
    private List<ResumeOtherDto> resumeOtherDtos;

    /**
     * 简历附件
     */
    @ApiModelProperty(value = "简历附件")
    private List<ResumeAttachmentDto> resumeAttachmentDtos;

    /**
     * 简历类型
     */
    @ApiModelProperty(value = "简历类型")
    private String fkResumeTypeName;


    /**
     * 工作经历（学历）
     */
    @ApiModelProperty(value = "工作经历（公司）")
    private String resumeWorks;

    /**
     * 教育经历（学历）
     */
    @ApiModelProperty(value = "教育经历（学历）")
    private String resumeEducations;


    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String fkCompanyName;


}

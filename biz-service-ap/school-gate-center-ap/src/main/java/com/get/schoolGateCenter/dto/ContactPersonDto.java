package com.get.schoolGateCenter.dto;

import com.get.schoolGateCenter.entity.ContactPerson;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/8/7
 * @TIME: 12:01
 * @Description:
 **/
@Data
public class ContactPersonDto extends ContactPerson {
    /**
     * 联系人类型
     */
    @ApiModelProperty(value = "联系人类型")
    List<String> contactPersonTypeName;

    /**
     * 目标类型名称
     */
    @ApiModelProperty(value = "目标类型名称")
    String targetTypeName;


    @ApiModelProperty(value = "目标类型")
    private String targetName;

    @ApiModelProperty(value = "公司名称")
    private String companyName;
}

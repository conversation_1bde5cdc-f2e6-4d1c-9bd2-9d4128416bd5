package com.get.schoolGateCenter.dto;
import com.get.schoolGateCenter.entity.StaffContract;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/7/7
 * @TIME: 15:14
 **/
@Data
public class StaffContractDto extends StaffContract {

    /**
     * 文件下载链接
     */
    @ApiModelProperty(value = "附件")
    private List<MediaAndAttachedDto> mediaAndAttachedDtos;

    /**
     * 员工名称
     */
    @ApiModelProperty(value = "员工名称")
    private String staffName;


}

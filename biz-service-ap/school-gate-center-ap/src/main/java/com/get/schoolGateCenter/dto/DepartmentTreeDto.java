package com.get.schoolGateCenter.dto;


import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/7/23
 * @TIME: 15:52
 * @Description: 部门树形图
 **/
@Data
public class DepartmentTreeDto extends BaseEntity {

    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    /**
     * 部门编号
     */
    @ApiModelProperty(value = "部门编号")
    private String num;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;


    /**
     * 名称
     */
    @ApiModelProperty(value = "职位数量")
    private Integer totalNum;


    /**
     * 职位集合
     */
    @ApiModelProperty(value = "职位集合")
    private List<PositionTreeDto> positionTreeList;

}

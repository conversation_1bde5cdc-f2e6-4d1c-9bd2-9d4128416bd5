package com.get.schoolGateCenter.vo;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.List;

/**
 * @ClassName: TemplateVo
 * @Author: Eric
 * @Date: 2023/2/15 15:05
 * @Version: 1.0
 */
@Data
public class TemplateVo  extends BaseVoEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "模板名称")
    private String name;

    @ApiModelProperty(value = "内容(html)")
    private String contentHtml;

    @ApiModelProperty(value = "是否激活：0否/1是")
    private Boolean isActive;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private int viewOrder;

    @ApiModelProperty(value = "图片文件")
    List<MediaAndAttachedVo> mediaAndAttachedVos;

}

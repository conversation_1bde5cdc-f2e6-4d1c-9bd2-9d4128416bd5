package com.get.schoolGateCenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName: UserResume
 * @Author: Eric
 * @Date: 2023/7/12 10:52
 * @Version: 1.0
 */
@Data
@TableName("r_user_resume")
public class UserResume extends BaseEntity implements Serializable {

    @ApiModelProperty("人才简历Id")
    @Column(name = "fk_resume_id")
    private Long fkResumeId;

    @ApiModelProperty("用户Id")
    @Column(name = "fk_user_id")
    private Long fkUserId;


}

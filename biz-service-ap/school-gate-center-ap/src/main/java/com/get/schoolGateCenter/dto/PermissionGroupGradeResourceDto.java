package com.get.schoolGateCenter.dto;

import com.get.schoolGateCenter.entity.PermissionGrade;
import com.get.schoolGateCenter.entity.PermissionGroup;
import com.get.schoolGateCenter.entity.PermissionGroupGradeResource;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel("权限资源系统配置返回类")
@Data
public class PermissionGroupGradeResourceDto extends PermissionGroupGradeResource {

    @ApiModelProperty(value = "权限组别和权限等级对应数组")
    List<GroupGradeResourceDto> GroupGradeResourceDto;
    @ApiModelProperty(value = "纵坐标权限等级")
    List<PermissionGrade> permissionGrades;
    @ApiModelProperty(value = "横坐标权限组别")
    List<PermissionGroup> permissionGroups;
}

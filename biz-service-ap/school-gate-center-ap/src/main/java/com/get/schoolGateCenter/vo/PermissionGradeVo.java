package com.get.schoolGateCenter.vo;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class PermissionGradeVo extends BaseVoEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    /**
     * 权限级别编号
     */
    @ApiModelProperty(value = "权限级别编号")
    private String gradeNum;

    /**
     * 权限级别名称
     */
    @ApiModelProperty(value = "权限级别名称")
    @NotBlank(message = "权限级别名称不能为空", groups = {Add.class, Update.class})
    private String gradeName;

    /**
     * 排序，数字由小到大排列
     */
    @ApiModelProperty(value = "排序，数字由小到大排列")
    private Integer viewOrder;
    /**
     * 权限级别
     */
    @ApiModelProperty(value = "权限级别")
    @NotNull(message = "权限级别名称不能为空", groups = {Add.class, Update.class})
    private Integer gradeLevel;
    private String keyword;
}

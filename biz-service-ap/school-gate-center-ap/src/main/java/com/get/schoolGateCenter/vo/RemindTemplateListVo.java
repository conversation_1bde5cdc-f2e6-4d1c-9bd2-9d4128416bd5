package com.get.schoolGateCenter.vo;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * Description:提醒模板列表Vo
 */
@Data
public class RemindTemplateListVo extends BaseVoEntity {

    /**
     * 提醒类型Key，必填
     */
    @ApiModelProperty(value = "提醒类型Key，必填")
    private String fkRemindEventTypeKey;

    /**
     * 短信模板，可填写第三方模板id
     */
    @ApiModelProperty(value = "短信模板，可填写第三方模板id")
    private String smsTemplate;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 电邮模板
     */
    @ApiModelProperty(value = "电邮模板")
    private String emailTemplate;

    /**
     * 关联业务库名
     */
    @ApiModelProperty(value = "关联业务库名")
    private String fkDbName;
}

package com.get.schoolGateCenter.vo;


import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


import javax.persistence.Column;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @DATE: 2020/8/17
 * @TIME: 10:57
 * @Description:
 **/
@Data
public class ContactPersonVo extends BaseVoEntity {
    private static final long serialVersionUID = 1L;
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    @NotBlank(message = "表不能为空", groups = {Add.class, Update.class})
    private String fkTableName;
    /**
     * 表Id
     */
    @ApiModelProperty(value = "表Id", required = true)
    @NotNull(message = "表Id不能为空", groups = {Add.class, Update.class})
    private Long fkTableId;
    /**
     * 联系人类型Key，多值逗号隔开
     */
    @ApiModelProperty(value = "联系人类型Key，多值逗号隔开", required = true)
    @NotBlank(message = "联系人类型不能为空", groups = {Add.class, Update.class})
    private String fkContactPersonTypeKey;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @NotBlank(message = "名称不能为空", groups = {Add.class, Update.class})
    private String name;
    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    @NotNull(message = "性别不能为空", groups = {Add.class, Update.class})
    private Integer gender;
    /**
     * 生日
     */
    @ApiModelProperty(value = "生日")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date birthday;
    /**
     * 公司名
     */
    @ApiModelProperty(value = "公司名")
    @NotBlank(message = "公司名不能为空", groups = {Add.class, Update.class})
    private String company;
    /**
     * 部门
     */
    @ApiModelProperty(value = "部门")
    @NotBlank(message = "部门不能为空", groups = {Add.class, Update.class})
    private String department;
    /**
     * 职位
     */
    @ApiModelProperty(value = "职位")
    @NotBlank(message = "职位不能为空", groups = {Add.class, Update.class})
    private String title;
    /**
     * 移动电话
     */
    @ApiModelProperty(value = "移动电话")
    @NotBlank(message = "移动电话不能为空", groups = {Add.class, Update.class})
    private String mobile;
    /**
     * 电话
     */
    @ApiModelProperty(value = "电话")
    private String tel;
    /**
     * Email
     */
    @ApiModelProperty(value = "Email")
    @NotBlank(message = "Email不能为空", groups = {Add.class, Update.class})
    private String email;
    /**
     * qq
     */
    @ApiModelProperty(value = "qq")
    private String qq;
    /**
     * 微信号
     */
    @ApiModelProperty(value = "微信号")
    private String wechat;
    /**
     * whatsapp
     */
    @ApiModelProperty(value = "whatsapp")
    private String whatsapp;
    /**
     * 联系地址
     */
    @ApiModelProperty(value = "联系地址")
    private String contactAddress;
    @ApiModelProperty(value = "查询关键字")
    private String keyWord;
    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    private Long fkInstitutionId;
    @ApiModelProperty(value = "目标名称")
    private String targetName;
    private List<Long> companyIds;
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;

}
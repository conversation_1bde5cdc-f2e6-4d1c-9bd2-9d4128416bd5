package com.get.schoolGateCenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @DATE: 2022/5/6
 * @TIME: 10:20
 * @Description:
 **/
@Data
public class InstitutionTabDto implements Serializable {
    @ApiModelProperty("id，主键")
    private Long id;
    @ApiModelProperty("名称")
    private String name;
    @ApiModelProperty("中文名称")
    private String nameChn;
    @ApiModelProperty("全称")
    private String fullName;
    @ApiModelProperty("名称")
    private String oldName;
    @ApiModelProperty("旧全称")
    private String oldFullName;
    private String num;
    @ApiModelProperty("副ID")
    private Long deputyId;
    @ApiModelProperty("状态：1显示；0禁用")
    private Long status;
    @ApiModelProperty("排序")
    private Integer order;
    @ApiModelProperty("父id")
    private Long parentId;
    @ApiModelProperty("学习计划id")
    private Long offerItemId;
    @ApiModelProperty("子学习计划学校")
    private List<InstitutionTabDto> institutionTabs;
    @ApiModelProperty("是否后续课程：0否/1是")
    private Boolean isFollow;
    @ApiModelProperty("是否后续课程隐藏：0否/1是")
    private Boolean isFollowHidden;
    @ApiModelProperty("枚举key")
    private Integer mode;
}

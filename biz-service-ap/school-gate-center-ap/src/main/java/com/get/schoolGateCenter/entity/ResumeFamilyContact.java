package com.get.schoolGateCenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("m_resume_family_contact")
@ApiModel(value="MResumeFamilyContact对象", description="")
public class ResumeFamilyContact extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "所属简历Id")
    private Long fkResumeId;

    @ApiModelProperty(value = "名（拼音）")
    private String fristName;

    @ApiModelProperty(value = "姓（拼音）")
    private String lastName;

    @ApiModelProperty(value = "手机区号")
    private String mobileAreaCode;

    @ApiModelProperty(value = "移动电话")
    private String mobile;

    @ApiModelProperty(value = "Email")
    private String email;

    @ApiModelProperty(value = "联系人类型(枚举，父-1，母-2，监护人-3)")
    private Integer contactType;


}

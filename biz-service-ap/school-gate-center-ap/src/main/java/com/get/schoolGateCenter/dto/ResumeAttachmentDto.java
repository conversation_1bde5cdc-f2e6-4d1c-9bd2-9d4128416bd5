package com.get.schoolGateCenter.dto;
import com.get.schoolGateCenter.entity.ResumeAttachment;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/12/29
 * @TIME: 16:44
 * @Description:
 **/
@Data
public class ResumeAttachmentDto extends ResumeAttachment {

    /**
     * 附件下载链接
     */
    @ApiModelProperty(value = "附件下载链接")
    private List<MediaAndAttachedDto> mediaAndAttacheds;
}

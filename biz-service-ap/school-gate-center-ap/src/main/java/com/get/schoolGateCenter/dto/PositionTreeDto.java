package com.get.schoolGateCenter.dto;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/7/23
 * @TIME: 15:47
 * @Description: 职位树形图
 **/

@Data
public class PositionTreeDto extends BaseEntity {
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    /**
     * 部门Id
     */
    @ApiModelProperty(value = "部门Id")
    private Long fkDepartmentId;

    /**
     * 职位编号
     */
    @ApiModelProperty(value = "职位编号")
    private String num;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 员工数量
     */
    @ApiModelProperty(value = "员工数量")
    private Integer totalNum;


    /**
     * 排序，数字由小到大排列
     */
    @ApiModelProperty(value = "排序，数字由小到大排列")
    private Integer viewOrder;

    /**
     * 职位等级
     */
    @ApiModelProperty(value = "职位等级")
    private String posLevel;

    /**
     * 员工
     */
    @ApiModelProperty(value = "员工集合")
    private List<StaffTreeDto> staffTreeList;

}

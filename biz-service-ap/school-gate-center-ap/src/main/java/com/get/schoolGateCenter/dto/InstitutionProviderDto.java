package com.get.schoolGateCenter.dto;

import com.get.schoolGateCenter.entity.InstitutionProvider;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/8/7
 * @TIME: 11:47
 * @Description:提供商dto
 **/
@Data
@ApiModel("学校提供商返回类")
public class InstitutionProviderDto extends InstitutionProvider {

    /**
     * 类型名称
     */
    @ApiModelProperty(value = "类型名称")
    private String typeName;

    /**
     * 国家Id
     */
    @ApiModelProperty(value = "国家名称")
    private String fkAreaCountryName;

    /**
     * 州省Id
     */
    @ApiModelProperty(value = "州省名称")
    private String fkAreaStateName;

    /**
     * 城市Id
     */
    @ApiModelProperty(value = "城市名称")
    private String fkAreaCityName;

    /**
     * 集团名称
     */
    @ApiModelProperty(value = "集团名称")
    private String fkInstitutionGroupName;


    @ApiModelProperty(value = "是否被选中")
    private Boolean isSelect;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String companyName;

    /**
     * 全称
     */
    @ApiModelProperty(value = "全称")
    private String fullName;

    /**
     * 业务国家名称
     */
    @ApiModelProperty(value = "业务国家名称")
    private String areaCountryNames;

    /**
     * 业务国家名称
     */
    @ApiModelProperty(value = "业务国家id")
    private List<Long> areaCountryIds;


    /**
     * 中间表id
     */
    @ApiModelProperty(value = "中间表id")
    private Long ipiId;
    /**
     * 公开对象名称
     */
    @ApiModelProperty(value = "公开对象名称")
    private String publicLevelName;


    /**
     * 所属渠道名称
     */
    @ApiModelProperty(value = "所属渠道名称")
    private String fkInstitutionChannelName;

    /**
     * 学校数量
     */
    @ApiModelProperty(value = "学校数量")
    private Integer institutionCount;

    /**
     * 渠道名称
     */
    @ApiModelProperty(value = "渠道名称")
    private String institutionChannelNames;

    /**
     * 所属渠道Ids
     */
    @ApiModelProperty(value = "所属渠道Ids")
    private List<Long> institutionChannelIds;
    /**
     * 是否合作
     */
    @ApiModelProperty(value = "合作状态：0无/1有")
    private Boolean isBindingActive;

    /**
     * 提供商id
     */
    @ApiModelProperty(value = "提供商id")
    private Long fkInstitutionProviderId;

    @ApiModelProperty(value = "枚举状态：0不参加/1参加/2待定")
    private Integer eventRegistrationStatus;


    @ApiModelProperty(value = "备注")
    private String remark;
}

package com.get.schoolGateCenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**　
 * Date: 2021/11/15
 * Description:提醒任务列表Vo
 */
@Data
public class RemindTaskListVo {
    /**
     * 查询时间
     */
    @ApiModelProperty(value = "查询时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date selectTime;
}

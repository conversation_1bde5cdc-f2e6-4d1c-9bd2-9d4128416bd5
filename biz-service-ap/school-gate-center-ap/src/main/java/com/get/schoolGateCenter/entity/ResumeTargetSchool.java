package com.get.schoolGateCenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * Author: Smail
 * Date: 21/9/2023
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("m_resume_target_school")
@ApiModel(value="MResumeTargetSchool对象", description="")
public class ResumeTargetSchool extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "所属用户Id")
    private Long fkUserId;

    @ApiModelProperty(value = "所属学校Id")
    private Long fkInstitutionId;

    @ApiModelProperty(value = "学生读这所学校的先后循序")
    private Integer ranking;


}

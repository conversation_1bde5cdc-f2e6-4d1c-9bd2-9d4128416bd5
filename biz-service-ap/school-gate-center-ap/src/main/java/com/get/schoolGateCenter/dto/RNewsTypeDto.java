package com.get.schoolGateCenter.dto;

import com.get.common.annotion.TableDto;
import com.get.schoolGateCenter.entity.RNewsType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: Sea
 * @create: 2020/8/6 11:42
 * @verison: 1.0
 * @description:
 */
@Data
public class RNewsTypeDto extends RNewsType {
    /**
     * 新闻类型名称
     */
    @ApiModelProperty(value = "新闻类型名称")
    @TableDto(tableName = "u_news_type", columnDto = "type_name", entityColumnDto = "newsTypeName", columnDtoMainId = "fk_news_type_id")
    private String newsTypeName;
    /**
     * 目标类型名称
     */
    @ApiModelProperty(value = "目标类型名称")
    private String targetTypeName;
    /**
     * 目标名称
     */
    @ApiModelProperty(value = "目标名称")
    private String targetName;

}

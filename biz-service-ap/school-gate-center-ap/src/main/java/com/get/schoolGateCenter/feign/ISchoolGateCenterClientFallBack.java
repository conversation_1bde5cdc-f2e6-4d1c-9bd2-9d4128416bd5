package com.get.schoolGateCenter.feign;

import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.api.Result;
import com.get.schoolGateCenter.dto.SchoolGateStaffDto;
import com.get.schoolGateCenter.dto.UserDto;
import org.springframework.stereotype.Component;

/**
 * Feign失败配置
 */
@Component
@VerifyPermission(IsVerify = false)
public class ISchoolGateCenterClientFallBack implements ISchoolGateCenterClient {
    @Override
    public Result<SchoolGateStaffDto> staffInfo(String userName, String password, String isAvatarLogin) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<UserDto> loginByAuth(String userName, String password) {
        return Result.fail("获取数据失败");
    }

}

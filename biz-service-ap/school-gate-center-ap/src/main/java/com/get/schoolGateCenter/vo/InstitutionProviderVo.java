package com.get.schoolGateCenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/8/25
 * @TIME: 12:47
 * @Description:
 **/
@Data
public class InstitutionProviderVo extends BaseVoEntity {
    /**
     * 国家Id
     */
    @ApiModelProperty(value = "国家Id", required = true)
    private Long fkAreaCountryId;

    /**
     * 业务国家Id
     */
    @ApiModelProperty(value = "业务国家Id", required = true)
    private Long areaCountryId;

    /**
     * 州省Id
     */
    @ApiModelProperty(value = "州省Id", required = true)
    private Long fkAreaStateId;

    /**
     * 城市Id
     */
    @ApiModelProperty(value = "城市Id", required = true)
    private Long fkAreaCityId;

    /**
     * 学校提供商类型Id：渠道=1，集团=2，学校=3
     */
    @ApiModelProperty(value = "学校提供商类型Id：渠道=1，集团=2，学校=3", required = true)
    @NotNull(message = "学校提供商类型Id不能为空", groups = {Add.class, Update.class})
    private Long fkInstitutionProviderTypeId;

    /**
     * 所属集团Id
     */
    @ApiModelProperty(value = "所属集团Id")
    private Long fkInstitutionGroupId;

    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    private String num;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称", required = true)
    @NotNull(message = "名称不能为空", groups = {Add.class, Update.class})
    private String name;

    /**
     * 中文名称
     */
    @ApiModelProperty(value = "中文名称")
    @NotNull(message = "中文名称不能为空", groups = {Add.class, Update.class})
    private String nameChn;

    /**
     * 邮编
     */
    @ApiModelProperty(value = "邮编")
    private String zipCode;

    /**
     * 电邮
     */
    @ApiModelProperty(value = "电邮")
    private String email;

    /**
     * 地址
     */
    @ApiModelProperty(value = "地址")
    private String address;

    /**
     * 申请佣金截止时间
     */
    @ApiModelProperty(value = "申请佣金截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "app_commission_deadline")
    private Date appCommissionDeadline;

    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @NotNull(message = "是否激活不能为空", groups = {Add.class, Update.class})
    private Boolean isActive;

    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    private Long fkInstitutionId;

    /**
     * 查询关键字
     */
    @ApiModelProperty(value = "查询关键字")
    private String keyWord;

    /**
     * 查询公司Id
     */
    @ApiModelProperty(value = "查询公司Id")
    private Long fkCompanyId;


    @ApiModelProperty(value = "查询公司Id集合")
    private List<Long> fkCompanyIds;
    /**
     * 公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2
     */
    @ApiModelProperty(value = "公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
    @NotBlank(message = "公开对象", groups = {Add.class, Update.class})
    private String publicLevel;

    @ApiModelProperty(value = "状态： 0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废")
    private Integer flowStatus;

    /**
     * 业务国家ids
     */
    @ApiModelProperty(value = "业务国家ids")
    @NotNull(message = "公开对象", groups = {Add.class, Update.class})
    private List<Long> areaCountryIds;

    /**
     * 所属渠道Id
     */
    @ApiModelProperty(value = "所属渠道Id")
    @NotNull(message = "所属渠道不能为空", groups = {Add.class, Update.class})
    private List<Long> institutionChannelIds;

    /**
     * 所属渠道Id
     */
    @ApiModelProperty(value = "所属渠道Id")
    private Long fkInstitutionChannelId;

    /**
     * 旧数据id(gea)
     */
    @ApiModelProperty(value = "旧数据id(gea)")
    private String idGea;

    /**
     * 旧数据id(iae)
     */
    @ApiModelProperty(value = "旧数据id(iae)")
    private String idIae;

    /**
     * 学校Id
     */
    @ApiModelProperty(value = "活动Id")
    private Long fkEventId;

    /**
     * 学校Id
     */
    @ApiModelProperty(value = "提供商ids")
    private List<Long> providerIds;
}

package com.get.schoolGateCenter.vo;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @DATE: 2020/7/30
 * @TIME: 12:03
 * @Description:
 **/
@Data
public class SkillTypeVo extends BaseVoEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 类型名称
     */
    @ApiModelProperty(value = "类型名称", required = true)
    @NotBlank(message = "类型名称不能为空", groups = {Add.class, Update.class})
    private String typeName;
    /**
     * 子类型名称
     */
    @ApiModelProperty(value = "子类型名称", required = true)
    @NotBlank(message = "子类型名称不能为空", groups = {Add.class, Update.class})
    private String subTypeName;
    /**
     * 排序，数字由小到大排列
     */
    @ApiModelProperty(value = "排序", required = true)
    private Integer viewOrder;
    /**
     * 关键字
     */
    @ApiModelProperty(value = "关键字")
    private String keyWord;
}

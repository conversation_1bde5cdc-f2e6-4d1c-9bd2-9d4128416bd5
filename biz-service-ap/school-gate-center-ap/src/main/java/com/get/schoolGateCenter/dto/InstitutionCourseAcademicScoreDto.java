package com.get.schoolGateCenter.dto;
import com.get.schoolGateCenter.entity.InstitutionCourseAcademicScore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @DATE: 2020/12/8
 * @TIME: 11:24
 * @Description:
 **/
@Data
@ApiModel("学术成绩返回类")
public class InstitutionCourseAcademicScoreDto extends InstitutionCourseAcademicScore {
    /**
     * 国家名称
     */
    @ApiModelProperty(value = "国家名称")
    private String fkAreaCountryName;
    /**
     * 类型名称
     */
    @ApiModelProperty(value = "类型名称")
    private String typeName;

}

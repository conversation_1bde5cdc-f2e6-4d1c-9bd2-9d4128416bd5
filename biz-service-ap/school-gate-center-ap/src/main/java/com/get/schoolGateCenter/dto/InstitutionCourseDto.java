package com.get.schoolGateCenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import com.get.schoolGateCenter.entity.InstitutionCourse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/8/5
 * @TIME: 16:13
 * @Description:
 **/
@Data
@ApiModel("学校课程返回类")
public class InstitutionCourseDto extends InstitutionCourse {
    /**
     * 学校名称
     */
    @ApiModelProperty(value = "学校名称")
    private String institutionName;
    /**
     * 学校类型名称
     */
    @ApiModelProperty(value = "学校类型名称")
    private String fkInstitutionTypeName;
    /**
     * 学院名称
     */
    @ApiModelProperty(value = "学院名称")
    private String institutionFacultyName;
    /**
     * 文件下载链接
     */
    @ApiModelProperty(value = "课程附件信息")
    private List<MediaAndAttachedDto> mediaAndAttachedDtos;
    /**
     * 新闻
     */
    @ApiModelProperty(value = "学校新闻")
    private List<NewsDto> newsDtos;
    /**
     * 专业等级名称
     */
    @ApiModelProperty(value = "专业等级名称")
    private String majorLevelName;

    /**
     * 类型名称
     */
    @ApiModelProperty(value = "类型名称")
    private String typeName;
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String fkTableName;
    /**
     * 全称
     */
    @ApiModelProperty(value = "全称")
    private String fullName;

    /**
     * 学校学院数组
     */
    @ApiModelProperty(value = "学校学院数组")
    private List<Long> fkInstitutionFacultyIds;

    /**
     * 专业等级数组
     */
    @ApiModelProperty(value = "专业等级数组")
    private List<Long> fkMajorLevelIds;

    /**
     * 课程类型数组
     */
    @ApiModelProperty(value = "课程类型数组")
    private List<Long> fkCourseTypeIds;
    /**
     * 公开对象名称
     */
    @ApiModelProperty(value = "公开对象名称")
    private String publicLevelName;

    /**
     * 数据等级名称
     */
    @ApiModelProperty(value = "数据等级名称")
    private String dataLevelName;

    /**
     * 校区名称
     */
    @ApiModelProperty(value = "校区名称")
    private String zoneName;

    /**
     * 官网
     */
    @ApiModelProperty(value = "官网")
    private String webSite;

    /**
     * 校区id数组
     */
    @ApiModelProperty(value = "校区id数组")
    @NotNull(message = "校区id数组不能为空", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    private List<Long> fkInstitutionZoneIds;

    /**
     * 币种类型
     */
    @ApiModelProperty(value = "币种类型")
    private String fkCurrencyTypeNum;

    /**
     * 币种名称
     */
    @ApiModelProperty(value = "币种名称")
    private String fkCurrencyTypeNumName;
    /**
     * 类型Key
     */
    @ApiModelProperty(value = "学校类型key")
    private String fkInstitutionTypeKey;

}

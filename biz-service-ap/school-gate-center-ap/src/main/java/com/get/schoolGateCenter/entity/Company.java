package com.get.schoolGateCenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.ibatis.type.Alias;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("m_company")
@Alias("SchoolGateCompany")
public class Company extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 父公司Id
     */
    @ApiModelProperty(value = "父公司Id")
    @Column(name = "fk_parent_company_id")
    private Long fkParentCompanyId;
    /**
     * 公司编号
     */
    @ApiModelProperty(value = "公司编号")
    @Column(name = "num")
    private String num;
    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    @Column(name = "name")
    private String name;
    /**
     * 公司中文名称
     */
    @ApiModelProperty(value = "公司中文名称")
    private String nameChn;
    /**
     * 公司简称
     */
    @ApiModelProperty(value = "公司简称")
    private String shortName;
    /**
     * 公司中文简称
     */
    @ApiModelProperty(value = "公司中文简称")
    private String shortNameChn;
    /**
     * 排序，数字由小到大排列
     */
    @ApiModelProperty(value = "排序，数字由小到大排列")
    @Column(name = "view_order")
    private Integer viewOrder;

}
package com.get.schoolGateCenter.dto;
import com.get.schoolGateCenter.entity.MediaAndAttached;
import com.get.schoolGateCenter.entity.News;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2022/12/9 16:52
 * @verison: 1.0
 * @description:
 */
@Data
public class SendNewsMailContext {

    /**
     * 新闻实体
     */
    private News news;

    /**
     * 附件
     */
    private List<MediaAndAttached> mediaAndAttacheds;

    /**
     * 部门配置
     */
    private ConfigDto dpConfig;

    /**
     * 接口密钥配置
     */
    private ConfigDto IaeApiConfig;

    /**
     * 创建时间
     */
    private Date createDate;
}

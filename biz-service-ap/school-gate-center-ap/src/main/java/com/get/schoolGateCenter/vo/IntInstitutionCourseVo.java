package com.get.schoolGateCenter.vo;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName: IntInstitutionCourseVo
 * @Author: Eric
 * @Date: 2023/4/18 11:11
 * @Version: 1.0
 */
@Data
public class IntInstitutionCourseVo extends BaseVoEntity {

    /**
     * 国家Id
     */
    @ApiModelProperty(value = "国家id,多选")
    private Long[] areaCountryIds;

    /**
     * 城市id
     */
    @ApiModelProperty(value = "城市id")
    private Long areaCityId;

    @ApiModelProperty(value = "省区id")
    private Long areaStateId;



    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    private Long fkInstitutionId;

    @ApiModelProperty(value = "学校名字")
    private String schoolName;

    /**
     * 专业等级Id
     */
    @ApiModelProperty(value = "专业等级Id")
    private Long fkMajorLevelId;

    @ApiModelProperty(value = "课程等级")
    private Long fkInstitutionTypeId;



    /**
     * 课程学费
     */
//    @ApiModelProperty(value = "课程学费下限")
//    private BigDecimal feeMin;
//    /**
//     * 课程学费
//     */
//    @ApiModelProperty(value = "课程学费上限")
//    private BigDecimal feeMax;


    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;


    /**
     * 货币类型编号
     */
//    @ApiModelProperty(value = " 货币类型编号")
//    private String feeTypeId;


    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private Long userId;

    /**
     * 模糊查询课程名
     */
    @ApiModelProperty(value = "模糊查询课程名")
    private String keyword;

    /**
     * 专业类型Id
     */
    @ApiModelProperty(value = "专业类型Id")
    private Long fkCourseTypeId;

    /**
     * 课程类型组Id
     */
//    @ApiModelProperty(value = "课程类型组Id")
//    private Long fkCourseTypeGroupId;

    /**
     * 公开对象
     */
    @ApiModelProperty(value = "公开对象")
    private String publicLevel;

    /**
     * 排序规则
     */
    @ApiModelProperty(value = "排序规则，0:ASC;1:DESC")
    private Integer sort;


}

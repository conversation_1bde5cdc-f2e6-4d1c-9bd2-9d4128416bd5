package com.get.schoolGateCenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;

@Data
@TableName("m_resume_work")
public class ResumeWork extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 所属简历Id
     */
    @ApiModelProperty(value = "所属简历Id")
    @Column(name = "fk_resume_id")
    private Long fkResumeId;
    /**
     * 行业类型Id
     */
    @ApiModelProperty(value = "行业类型Id")
    @Column(name = "fk_industry_type_id")
    private Long fkIndustryTypeId;
    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "开始时间")
    @Column(name = "start_date")
    private Date startDate;
    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "结束时间")
    @Column(name = "end_date")
    private Date endDate;
    /**
     * 所在公司
     */
    @ApiModelProperty(value = "所在公司")
    @Column(name = "company")
    private String company;
    /**
     * 职位
     */
    @ApiModelProperty(value = "职位")
    @Column(name = "position")
    private String position;
    /**
     * 部门
     */
    @ApiModelProperty(value = "部门")
    @Column(name = "department")
    private String department;
    /**
     * 公司性质
     */
    @ApiModelProperty(value = "公司性质")
    @Column(name = "company_nature")
    private String companyNature;
    /**
     * 公司规模
     */
    @ApiModelProperty(value = "公司规模")
    @Column(name = "company_size")
    private String companySize;
    /**
     * 工作类型：全职/兼职
     */
    @ApiModelProperty(value = "工作类型：全职/兼职")
    @Column(name = "working_type")
    private String workingType;
    /**
     * 下属人数
     */
    @ApiModelProperty(value = "下属人数")
    @Column(name = "subordinate_count")
    private Integer subordinateCount;
    /**
     * 是否有海外经历：0否/1是
     */
    @ApiModelProperty(value = "是否有海外经历：0否/1是")
    @Column(name = "is_abroad_experience")
    private Boolean isAbroadExperience;
    /**
     * 工作描述
     */
    @ApiModelProperty(value = "工作描述")
    @Column(name = "job_description")
    private String jobDescription;
    /**
     * 主要业绩
     */
    @ApiModelProperty(value = "主要业绩")
    @Column(name = "main_achievement")
    private String mainAchievement;
}
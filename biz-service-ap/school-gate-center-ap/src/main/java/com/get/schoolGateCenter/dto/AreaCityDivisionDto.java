package com.get.schoolGateCenter.dto;

import com.get.schoolGateCenter.entity.AreaCityDivision;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: Sea
 * @create: 2021/3/3 14:48
 * @verison: 1.0
 * @description:
 */
@Data
public class AreaCityDivisionDto extends AreaCityDivision {
    /**
     * 城市名称
     */
    @ApiModelProperty(value = "城市名称")
    private String areaCityName;

    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String fkTableName;
    /**
     * 全称
     */
    @ApiModelProperty(value = "全称")
    private String fullName;
}

package com.get.schoolGateCenter.dto;

import com.get.schoolGateCenter.entity.MediaAndAttached;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description:附件dto
 */
@Data
public class MediaAppdixDto extends MediaAndAttached {

    @ApiModelProperty(value = "文件路径")
    private String filePath;

    @ApiModelProperty(value = "文件guid(文档中心)")
    private String fkFileGuid;

    @ApiModelProperty(value = "表名")
    private String fkTableName;

    @ApiModelProperty(value = "表Id")
    private Long fkTableId;

    @ApiModelProperty(value = "类型关键字，如：institution_mov/institution_pic/alumnus_head_icon")
    private String typeKey;

    @ApiModelProperty(value = "索引值(默认从0开始，同一类型下值唯一)")
    private int indexkey;
}

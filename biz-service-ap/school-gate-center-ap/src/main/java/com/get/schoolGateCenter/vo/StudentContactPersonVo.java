package com.get.schoolGateCenter.vo;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @DATE: 2020/11/11
 * @TIME: 11:53
 * @Description:
 **/
@Data
public class StudentContactPersonVo extends BaseVoEntity {
    /**
     * 学生Id
     */
    @ApiModelProperty(value = "学生Id")
    @NotNull(message = "学生Id不能为空", groups = {Add.class, Update.class})
    private Long fkStudentId;

    /**
     * 与本人关系：父母/兄弟/亲戚/朋友
     */
    @ApiModelProperty(value = "与本人关系：父母/兄弟/亲戚/朋友")
    private String relationship;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    private String gender;

    /**
     * 职业
     */
    @ApiModelProperty(value = "职业")
    private String job;

    /**
     * 移动电话
     */
    @ApiModelProperty(value = "移动电话")
    @NotNull(message = "学生移动电话不能为空", groups = {Add.class, Update.class})
    private String mobile;

    /**
     * 电话
     */
    @ApiModelProperty(value = "电话")
    private String tel;

    /**
     * Email
     */
    @ApiModelProperty(value = "Email")
    private String email;

    /**
     * qq
     */
    @ApiModelProperty(value = "qq")
    private String qq;

    /**
     * 微信号
     */
    @ApiModelProperty(value = "微信号")
    private String wechat;

    /**
     * whatsapp
     */
    @ApiModelProperty(value = "whatsapp")
    private String whatsapp;

    /**
     * 联系地址
     */
    @ApiModelProperty(value = "联系地址")
    private String contactAddress;
}

package com.get.schoolGateCenter.dto;

import com.get.common.annotion.TableDto;
import com.get.schoolGateCenter.entity.Contract;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/8/20
 * @TIME: 17:48
 * @Description: 合同管理DTO
 **/
@Data
public class ContractDto extends Contract implements Serializable {

    @ApiModelProperty(value = "学校提供商名称")
    private String fkInstitutionProviderName;

    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校名称")
    private String fkInstitutionName;

    /**
     * 学校课程Id
     */
    @ApiModelProperty(value = "学校课程名称")
    private String fkInstitutionCourseName;

    /**
     * 合同类型
     */
    @ApiModelProperty(value = "合同类型")
    @TableDto(tableName = "u_contract_type", columnDto = "type_name", entityColumnDto = "fkContractTypeName", columnDtoMainId = "fk_contract_type_id")
    private String fkContractTypeName;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String fkCompanyName;

    /**
     * 流程状态
     */
    @ApiModelProperty(value = "流程状态")
    private Integer status;
    /**
     * 定义下拉枚举：0新签/1续签
     */
    @ApiModelProperty(value = "定义下拉枚举：0新签/1续签")
    private List<String> contractApprovalModeEnum;
    /**
     * 任务版本号
     */
    @ApiModelProperty("任务版本号")
    private Integer taskVersion;
    /**
     * 任务id
     */
    @ApiModelProperty("任务id")
    private String taskId;
    /**
     * 待签或代表
     */
    @ApiModelProperty("待签或代表")
    private int signOrGetStatus;
    /**
     * 流程实例id
     */
    @ApiModelProperty("流程实例id")
    private String procInstId;
    /**
     * 创建人公司id
     */
    @ApiModelProperty("创建人公司id")
    private Long fkcompanyId;

    @ApiModelProperty("发起人id")
    private Long fkStaffId;
    /**
     * 同表父id
     */
    @ApiModelProperty("同表父id")
    private Long fkTableParentId;
    /**
     * 同意按钮状态
     */
    @ApiModelProperty(value = "同意按钮状态")
    private Boolean agreeButtonType;

    /**
     * 拒绝按钮状态
     */
    @ApiModelProperty(value = "拒绝按钮状态")
    private Boolean refuseButtonType;

}

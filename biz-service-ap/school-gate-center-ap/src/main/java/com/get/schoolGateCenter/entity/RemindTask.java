package com.get.schoolGateCenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("m_remind_task")
public class RemindTask extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 任务guid
     */
    @ApiModelProperty(value = "任务guid")
    private String taskGuid;
    /**
     * 提醒类型Key，必填
     */
    @ApiModelProperty(value = "提醒类型Key，必填")
    private String fkRemindEventTypeKey;
    /**
     * 员工Id
     */
    @ApiModelProperty(value = "员工Id")
    private Long fkStaffId;
    /**
     * 任务开始时间
     */
    @ApiModelProperty(value = "任务开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;
    /**
     * 任务结束时间
     */
    @ApiModelProperty(value = "任务结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;
    /**
     * 提前天数提醒，多选：0,1,3,7（0为当天提醒）
     */
    @ApiModelProperty(value = "提前天数提醒，多选：0,1,3,7（0为当天提醒）")
    private String advanceDays;
    /**
     * 提前小时提醒，多选：0,0.5,2,5（0为当时提醒，支持小数2位）
     */
    @ApiModelProperty(value = "提前小时提醒，多选：0,0.5,2,5（0为当时提醒，支持小数2位）")
    private String advanceHours;
    /**
     * 循环间隔日提醒（每隔多少天提醒1次）：1
     */
    @ApiModelProperty(value = "循环间隔日提醒（每隔多少天提醒1次）：1")
    private Integer loopIntervalDays;
    /**
     * 循环周日期提醒（周日-周六）：多选：0,1,2,3,4,5,6
     */
    @ApiModelProperty(value = "循环周日期提醒（周日-周六）：多选：0,1,2,3,4,5,6")
    private String loopWeekDays;
    /**
     * 循环月日期提醒：多选：1,2...31
     */
    @ApiModelProperty(value = "循环月日期提醒：多选：1,2...31")
    private String loopMonthDays;
    /**
     * 任务标题
     */
    @ApiModelProperty(value = "任务标题")
    private String taskTitle;
    /**
     * 任务备注
     */
    @ApiModelProperty(value = "任务备注")
    private String taskRemark;
    /**
     * 任务背景颜色
     */
    @ApiModelProperty(value = "任务背景颜色")
    private String taskBgColor;
    /**
     * 任务文字颜色
     */
    @ApiModelProperty(value = "任务文字颜色")
    private String taskTxtColor;
    /**
     * 任务链接
     */
    @ApiModelProperty(value = "任务链接")
    private String taskLink;
    /**
     * 提醒方式：0不提醒/1邮件/2短信/3系统内，支持多选，逗号分隔
     */
    @ApiModelProperty(value = "提醒方式：0不提醒/1邮件/2短信/3系统内，支持多选，逗号分隔")
    private String remindMethod;
    /**
     * 关联业务库名
     */
    @ApiModelProperty(value = "关联业务库名")
    private String fkDbName;
    /**
     * 关联业务表名
     */
    @ApiModelProperty(value = "关联业务表名")
    private String fkTableName;
    /**
     * 关联业务表Id
     */
    @ApiModelProperty(value = "关联业务表Id")
    private Long fkTableId;
    /**
     * 任务状态：0取消/1执行中/2停止/3结束
     */
    @ApiModelProperty(value = "任务状态：0取消/1执行中/2停止/3结束")
    private Integer status;
}
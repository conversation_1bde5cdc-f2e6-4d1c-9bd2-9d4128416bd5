package com.get.schoolGateCenter.feign;

import com.get.common.constant.AppCenterConstant;
import com.get.core.tool.api.Result;
import com.get.schoolGateCenter.dto.SchoolGateStaffDto;
import com.get.schoolGateCenter.dto.UserDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * Feign接口类
 */
@FeignClient(
        value = AppCenterConstant.APPLICATION_SCHOOL_GATE_CENTER
)
public interface ISchoolGateCenterClient {

    String API_PREFIX = "/feign";

    /**
     * @Description :feign调用 根据代理ids查找对应代理名称map.
     */
    String STAFF_INFO = API_PREFIX + "/staff-info";


    /**
     * 获取用户信息
     * @param userName
     * @param password
     * @param isAvatarLogin
     * @return
     */
    @GetMapping("STAFF_INFO")
    Result<SchoolGateStaffDto> staffInfo(@RequestParam("userName") String userName,
                                         @RequestParam("password") String password,
                                         @RequestParam(value = "isAvatarLogin",required = false) String isAvatarLogin);
    @GetMapping("LOGIN_BY_AUTH")
    Result<UserDto> loginByAuth(@RequestParam("userName") String userName,@RequestParam("password") String password);
}

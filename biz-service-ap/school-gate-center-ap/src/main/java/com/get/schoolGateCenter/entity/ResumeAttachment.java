package com.get.schoolGateCenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("m_resume_attachment")
public class ResumeAttachment extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 所属简历Id
     */
    @ApiModelProperty(value = "所属简历Id")
    @Column(name = "fk_resume_id")
    private Long fkResumeId;
    /**
     * 上传方式：0上传附件/1附件链接
     */
    @ApiModelProperty(value = "上传方式：0上传附件/1附件链接")
    @Column(name = "mode")
    private Integer mode;
    /**
     * 附件链接
     */
    @ApiModelProperty(value = "附件链接")
    @Column(name = "link")
    private String link;
    /**
     * 附件名称
     */
    @ApiModelProperty(value = "附件名称")
    @Column(name = "name")
    private String name;
    /**
     * 附件描述
     */
    @ApiModelProperty(value = "附件描述")
    @Column(name = "description")
    private String description;
}
package com.get.schoolGateCenter.dto;
import com.get.schoolGateCenter.entity.RemindTaskQueue;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * Date: 2021/12/2
 * Description:提醒队列返回类
 */
@Data
public class RemindTaskQueueDto extends RemindTaskQueue {

    /**
     * 任务标题
     */
    @ApiModelProperty(value = "任务标题")
    private String taskTitle;


    /**
     * 任务备注
     */
    @ApiModelProperty(value = "任务备注")
    private String taskRemark;

    /**
     * 关联业务库名
     */
    @ApiModelProperty(value = "关联业务库名")
    private String fkDbName;

    /**
     * 关联业务表名
     */
    @ApiModelProperty(value = "关联业务表名")
    private String fkTableName;

    /**
     * 关联业务表id
     */
    @ApiModelProperty(value = "关联业务表id")
    private Long fkTableId;

    /**
     * 学生id
     */
    @ApiModelProperty(value = "学生id")
    private Long studentId;

    /**
     * 方案id
     */
    @ApiModelProperty(value = "方案id")
    private Long offerId;

    /**
     * 计划id
     */
    @ApiModelProperty(value = "计划id")
    private Long offerItemId;
}

package com.get.schoolGateCenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @DATE: 2020/8/5
 * @TIME: 11:23
 * @Description:
 **/
@Data
public class ResumeWorkVo extends BaseVoEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 所属简历Id
     */
    @ApiModelProperty(value = "所属简历Id", required = true)
    @NotNull(message = "简历id不能为空", groups = {Add.class, Update.class})
    private Long fkResumeId;
    /**
     * 行业类型Id
     */
    @ApiModelProperty(value = "行业类型Id")
    private Long fkIndustryTypeId;
    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @NotNull(message = "开始时间不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "开始时间", required = true)
    private Date startDate;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDate;
    /**
     * 所在公司
     */
    @ApiModelProperty(value = "所在公司")
    private String company;
    /**
     * 职位
     */
    @ApiModelProperty(value = "职位")
    private String position;
    /**
     * 部门
     */
    @ApiModelProperty(value = "部门")
    private String department;
    /**
     * 公司性质
     */
    @ApiModelProperty(value = "公司性质")
    private String companyNature;
    /**
     * 公司规模
     */
    @ApiModelProperty(value = "公司规模")
    private String companySize;
    /**
     * 工作类型：全职/兼职
     */
    @ApiModelProperty(value = "工作类型：全职/兼职")
    private String workingType;
    /**
     * 下属人数
     */
    @ApiModelProperty(value = "下属人数")
    private Integer subordinateCount;
    /**
     * 是否有海外经历：0否/1是
     */
    @ApiModelProperty(value = "是否有海外经历：0否/1是")
    private Boolean isAbroadExperience;
    /**
     * 工作描述
     */
    @ApiModelProperty(value = "工作描述")
    private String jobDescription;
    /**
     * 主要业绩
     */
    @ApiModelProperty(value = "主要业绩")
    private String mainAchievement;
}

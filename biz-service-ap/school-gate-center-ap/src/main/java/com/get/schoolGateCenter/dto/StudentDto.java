package com.get.schoolGateCenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.schoolGateCenter.entity.Student;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/11/4
 * @TIME: 11:25
 * @Description:
 **/
@Data
public class StudentDto extends Student {
    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String fkCompanyName;
    /**
     * 学校名称List
     */
    @ApiModelProperty(value = "学校名称List")
    private Set<String> institutionNameList;
    /**
     * 学生出生所在国家名
     */
    @ApiModelProperty(value = "学生出生所在国家名")
    private String fkAreaCountryBirthName;
    /**
     * 学生出生所在州省名
     */
    @ApiModelProperty(value = "学生出生所在州省名")
    private String fkAreaStateBirthName;
    /**
     * 学生出生所在城市名
     */
    @ApiModelProperty(value = "学生出生所在城市名")
    private String fkAreaCityBirthName;
    /**
     * 学生国籍所在国家Id联查到的对应国家名称
     */
    @ApiModelProperty(value = "学生国籍所在国家名称（下拉）")
    private String countryNameNationality;
    /**
     * 绿卡国家名称
     */
    @ApiModelProperty(value = "绿卡国家名称（下拉）")
    private String countryNameGreenCard;
    /**
     * 学生出生所在国家Id联查到的对应国家名称
     */
    @ApiModelProperty(value = "学生出生所在国家名称（下拉）")
    private String countryNameBirth;
    /**
     * 学生出生所在州省Id联查到的对应州省名称
     */
    @ApiModelProperty("学生出生所在州省名称（下拉）")
    private String stateNameBirth;
    /**
     * 学生出生所在城市Id联查到的对应州省名称
     */
    @ApiModelProperty("学生出生所在城市名称（下拉）")
    private String cityNameBirth;
    /**
     * 学生现居所在国家Id联查到的对应国家名称
     */
    @ApiModelProperty(value = "学生现居所在国家（下拉）")
    private String countryName;
    /**
     * 学生现居所在州省Id联查到的州省名称
     */
    @ApiModelProperty(value = "学生现居所在州省名称（下拉）")
    private String stateName;
    /**
     * 学生现居所在城市Id联查到的城市名称
     */
    @ApiModelProperty(value = "学生现居所在城市名称（下拉）")
    private String cityName;
    /**
     * 毕业国家名称
     */
    @ApiModelProperty(value = "毕业国家名称（下拉）")
    private String countryNameEducation;
    /**
     * 毕业州省名称
     */
    @ApiModelProperty(value = "毕业州省名称（下拉）")
    private String stateNameEducation;
    /**
     * 毕业城市名称
     */
    @ApiModelProperty(value = "毕业城市名称（下拉）")
    private String cityNameEducation;
    /**
     * 毕业国家名称国际
     */
    @ApiModelProperty(value = "毕业国家名称国际（下拉）")
    private String countryNameEducation2;
    /**
     * 毕业州省名称国际
     */
    @ApiModelProperty(value = "毕业州省名称国际（下拉）")
    private String stateNameEducation2;
    /**
     * 毕业城市名称国际
     */
    @ApiModelProperty(value = "毕业城市名称国际（下拉）")
    private String cityNameEducation2;
    /**
     * 年龄
     */
    @ApiModelProperty(value = "年龄")
    private Integer age;
    /**
     * 代理名称
     */
    @ApiModelProperty(value = "代理名称")
    private List<String> fkAgentName;
    /**
     * 员工名称
     */
    @ApiModelProperty(value = "员工名称")
    private List<String> fkStaffName;
    /**
     * 学生业务状态名
     */
    @ApiModelProperty(value = "学生业务状态名(多选)")
    private String conditionTypeName;
    /**
     * 当前申请状态
     */
    @ApiModelProperty(value = "当前申请状态")
    private String stepName;
    /**
     * 失败原因
     */
    @ApiModelProperty(value = "失败原因")
    private String reasonName;
    /**
     * 代理Id
     */
    @ApiModelProperty(value = "代理Id")
    private Long fkAgentId;
    /**
     * 员工Id
     */
    @ApiModelProperty(value = "员工Id")
    private Long fkStaffId;
    /**
     * 绑定代理 / BD
     */
    @ApiModelProperty(value = "绑定代理 / BD")
    private Set<String> bdNameAndStaffNameList;
    /**
     * 当前绑定代理 / BD
     */
    @ApiModelProperty(value = "当前绑定代理 / BD")
    private Set<String> currentBdNameAndStaffNameList;
    /**
     * 项目角色 / 员工
     */
    @ApiModelProperty(value = "项目角色 / 员工")
    private Set<String> projectRoleStaffList;
    /**
     * 状态变更最新时间
     */
    @ApiModelProperty(value = "状态变更最新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date statusChangeLastTime;
    /**
     * 是否有学习申请方案 true:有 false：没有
     */
    @ApiModelProperty(value = "是否有学习申请方案 true:有 false：没有")
    private boolean studentOfferFlag;

    /**
     * 是否有留学住宿 true:有 false：没有
     */
    @ApiModelProperty(value = "是否有留学住宿 true:有 false：没有")
    private boolean studentAccommodationFlag;

    /**
     * 是否有留学保险 true:有 false：没有
     */
    @ApiModelProperty(value = "是否有留学保险 true:有 false：没有")
    private boolean studentInsurancesFlag;

    /**
     * 毕业院校名称（下拉）
     */
    @ApiModelProperty(value = "毕业院校名称（下拉）")
    private String educationInstitutionName;

    /**
     * 毕业院校名称国际（下拉）
     */
    @ApiModelProperty(value = "毕业院校名称国际（下拉）")
    private String educationInstitutionName2;

    /**
     * 学习计划id
     */
    @ApiModelProperty(value = "学习计划id")
    private Long offerItemId;
    /**
     * 多个申请国家
     */
    @ApiModelProperty(value = "多个申请国家")
    private String areaCountryNames;

//    /**
//     * 应收应付信息
//     */
//    @ApiModelProperty(value = "应收应付信息")
//    private List<StudentReceivableAndPaySumDto> studentReceivableAndPayInfo;

    /**
     * 项目说明名称
     */
    @ApiModelProperty(value = "项目说明，枚举：0=3+1/1=2+2/2=4+0/3=交换生")
    private String educationProjectName;

    /**
     * 学位情况名称
     */
    @ApiModelProperty(value = "学位情况名称")
    private String educationDegreeName;


    @ApiModelProperty(value = "学生中英文名")
    private String fullName;

    @ApiModelProperty(value = "邮箱")
    private String email;

    /**
     * 高中成绩类型名称
     */
    @ApiModelProperty(value = "高中成绩类型名称")
    private String highSchoolTestTypeName;


    /**
     * 本科成绩类型名称
     */
    @ApiModelProperty(value = "本科成绩类型名称")
    private String standardTestTypeName;
    /**
     * 英语测试类型
     */
    @ApiModelProperty(value = "英语测试类型")
    private String englishTestTypeName;

    /**
     * 硕士成绩类型，枚举Key
     */
    @ApiModelProperty(value = "硕士成绩类型，枚举Key")
    private String masterTestTypeName;

    /**
     * 硕士成绩
     */
    @ApiModelProperty(value = "本科成绩")
    private String masterTestScore;

    @ApiModelProperty(value = "学生最高状态")
    private Integer maxStepOrder;

    @ApiModelProperty(value = "学生最高状态名")
    private String maxStepOrderName;

    @ApiModelProperty(value = "学生最低状态")
    private Integer minStepOrder;

    @ApiModelProperty(value = "学生最低状态名")
    private String minStepOrderName;

    /**
     * 总记录数
     */
    @ApiModelProperty(value = "总记录数")
    private Integer totalCount;

    @ApiModelProperty(value = "与本人关系：父母/兄弟/亲戚/朋友")
    private String[] relationship;

    @ApiModelProperty(value = "关系：姓")
    private String[] PersonFamilyName;

    @ApiModelProperty(value = "关系：名")
    private String[] PersonFirstName;

    @ApiModelProperty(value = "关系：Email")
    private String[] PersonEmail;

    @ApiModelProperty(value = "第一目标学校")
    private String SchoolName;

    @ApiModelProperty(value = "第一目标学校中文")
    private String SchoolNameChn;

    @ApiModelProperty(value = "第二目标学校")
    private String SchoolName2;

    @ApiModelProperty(value = "第二目标学校中文")
    private String SchoolNameChn2;
}

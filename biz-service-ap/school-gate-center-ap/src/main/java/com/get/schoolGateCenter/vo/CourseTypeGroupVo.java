package com.get.schoolGateCenter.vo;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.util.HtmlUtils;

import javax.validation.constraints.NotBlank;

/**
 * @Description：课程类型组别VO
 * @Param
 * @return
 * @Date 10:47 2021/4/27
 * <AUTHOR>
 */
@Data
public class CourseTypeGroupVo extends BaseVoEntity {
    /**
     * 类型名称
     */
    @ApiModelProperty(value = "类型名称")
    @NotBlank(message = "类型名称不能为空", groups = {Add.class, Update.class})
    private String typeGroupName;


    public String getTypeGroupName() {
        if (StringUtils.isNotBlank(typeGroupName)) {
            return HtmlUtils.htmlUnescape(typeGroupName);
        }
        return null;
    }

    /**
     * 类型组别名称
     */
    @ApiModelProperty(value = "类型组别名称中文")
    private String typeGroupNameChn;
    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    @ApiModelProperty(value = "关键词")
    private String keyWord;
    @ApiModelProperty(value = "分类模式")
    private Integer mode;

    @ApiModelProperty(value = "公开对象")
    private String publicLevel;
}

package com.get.schoolGateCenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/11/4
 * @TIME: 11:26
 * @Description:
 **/
@Data
public class StudentVo extends BaseVoEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id", required = true)
    @NotNull(message = "公司Id不能为空", groups = {Add.class, Update.class})
    @Min(value = 1, message = "缺少公司id参数", groups = {Add.class, Update.class})
    private Long fkCompanyId;
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id", required = true)
    private List<Long> fkCompanyIds;
    /**
     * 学生编号
     */
    @ApiModelProperty(value = "学生编号")
    @Column(name = "num")
    private String num;
    /**
     * 学生姓名（中）
     */
    @NotBlank(message = "学生姓名不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "学生姓名")
    private String name;
    /**
     * 姓（英/拼音）
     */
    @ApiModelProperty(value = "姓（英/拼音）")
    @Column(name = "last_name")
    private String lastName;
    /**
     * 名（英/拼音）
     */
    @ApiModelProperty(value = "名（英/拼音）")
    @Column(name = "first_name")
    private String firstName;
    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    private String gender;
    /**
     * 生日
     */
    @ApiModelProperty(value = "生日")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date birthday;
    /**
     * 学生国籍所在国家Id
     */
    @ApiModelProperty(value = "学生国籍所在国家Id")
    @Column(name = "fk_area_country_id_nationality")
    private Long fkAreaCountryIdNationality;
    /**
     * 学生国籍所在国家名称
     */
    @ApiModelProperty(value = "学生国籍所在国家名称")
    @Column(name = "fk_area_country_name_nationality")
    private String fkAreaCountryNameNationality;
    /**
     * 绿卡国家Id
     */
    @ApiModelProperty(value = "绿卡国家Id")
    private Long fkAreaCountryIdGreenCard;
    /**
     * 护照编号（保险业务必填）
     */
    @ApiModelProperty(value = "护照编号（保险业务必填）")
    @Column(name = "passport_num")
    private String passportNum;
    /**
     * 学生出生所在国家Id
     */
    @ApiModelProperty(value = "学生出生所在国家Id")
    @Column(name = "fk_area_country_id_birth")
    private Long fkAreaCountryIdBirth;
    /**
     * 学生出生所在州省Id
     */
    @ApiModelProperty(value = "学生出生所在州省Id")
    @Column(name = "fk_area_state_id_birth")
    private Long fkAreaStateIdBirth;
    /**
     * 学生出生所在城市Id
     */
    @ApiModelProperty(value = "学生出生所在城市Id")
    @Column(name = "fk_area_city_id_birth")
    private Long fkAreaCityIdBirth;
    /**
     * 学生出生所在国家名称
     */
    @ApiModelProperty(value = "学生出生所在国家名称")
    @Column(name = "fk_area_country_name_birth")
    private String fkAreaCountryNameBirth;
    /**
     * 学生出生所在州省名称
     */
    @ApiModelProperty(value = "学生出生所在州省名称")
    @Column(name = "fk_area_state_name_birth")
    private String fkAreaStateNameBirth;
    /**
     * 学生出生所在城市名称
     */
    @ApiModelProperty(value = "学生出生所在城市名称")
    @Column(name = "fk_area_city_name_birth")
    private String fkAreaCityNameBirth;
    /**
     * 手机区号
     */
    @ApiModelProperty(value = "手机区号")
    private String mobileAreaCode;
    /**
     * 移动电话
     */
    @ApiModelProperty(value = "移动电话")
    private String mobile;
    /**
     * 电话区号
     */
    @ApiModelProperty(value = "电话区号")
    private String telAreaCode;
    /**
     * 电话
     */
    @ApiModelProperty(value = "电话")
    private String tel;
    /**
     * Email
     */
    @ApiModelProperty(value = "Email")
    private String email;
    /**
     * 学生所在国家Id
     */
    @ApiModelProperty(value = "学生所在国家Id")
    private Long fkAreaCountryId;
    /**
     * 学生所在州省Id
     */
    @ApiModelProperty(value = "学生所在州省Id")
    private Long fkAreaStateId;
    /**
     * 学生所在城市Id
     */
    @ApiModelProperty(value = "学生所在城市Id")
    private Long fkAreaCityId;
    /**
     * 学生所在国家名称
     */
    @ApiModelProperty(value = "学生所在国家名称")
    private String fkAreaCountryName;
    /**
     * 学生所在州省名称
     */
    @ApiModelProperty(value = "学生所在州省名称")
    private String fkAreaStateName;
    /**
     * 学生所在城市名称
     */
    @ApiModelProperty(value = "学生所在城市名称")
    private String fkAreaCityName;
    /**
     * 邮编
     */
    @ApiModelProperty(value = "邮编")
    private String zipcode;

//    /**
//     * 学历区域类型：国内/国际
//     */
//    @ApiModelProperty(value = "学历区域类型：国内/国际")
//    private String educationAreaType;
    /**
     * 联系地址
     */
    @ApiModelProperty(value = "联系地址")
    private String contactAddress;
    /**
     * 学历等级类型：中专/高中/大学/大专/专升本/本科/研究生/硕士/博士在读/博士/博士后/HND
     */
    @ApiModelProperty(value = "学历等级类型：中专/高中/大学/大专/专升本/本科/研究生/硕士/博士在读/博士/博士后/HND")
    private String educationLevelType;
    /**
     * 毕业专业
     */
    @ApiModelProperty(value = "毕业专业")
    private String educationMajor;
    /**
     * 毕业大学国家Id
     */
    @ApiModelProperty(value = "毕业大学国家Id")
    private Long fkAreaCountryIdEducation;
    /**
     * 毕业大学州省Id
     */
    @ApiModelProperty(value = "毕业大学州省Id")
    private Long fkAreaStateIdEducation;
    /**
     * 毕业大学城市Id
     */
    @ApiModelProperty(value = "毕业大学城市Id")
    private Long fkAreaCityIdEducation;
    /**
     * 毕业大学国家名称
     */
    @ApiModelProperty(value = "毕业大学国家名称")
    private String fkAreaCountryNameEducation;
    /**
     * 毕业大学州省名称
     */
    @ApiModelProperty(value = "毕业大学州省名称")
    private String fkAreaStateNameEducation;
    /**
     * 毕业大学城市名称
     */
    @ApiModelProperty(value = "毕业大学城市名称")
    private String fkAreaCityNameEducation;
    /**
     * 毕业院校Id
     */
    @ApiModelProperty(value = "毕业院校Id")
    private Long fkInstitutionIdEducation;
    /**
     * 毕业院校Ids
     */
    @ApiModelProperty(value = "毕业院校Ids")
    private List<Long> fkInstitutionIdsEducation;
    /**
     * 毕业院校名称
     */
    @ApiModelProperty(value = "毕业院校名称")
    private String fkInstitutionNameEducation;
    /**
     * 毕业学校类型：985/211/其他，默认选项：其他
     */
    @ApiModelProperty(value = "毕业学校类型：985/211/其他，默认选项：其他")
    private String institutionTypeEducation;
    /**
     * 学历等级类型（国际）：中专/高中/大学/大专/专升本/本科/研究生/硕士/博士在读/博士/博士后/HND
     */
    @ApiModelProperty(value = "学历等级类型（国际）：中专/高中/大学/大专/专升本/本科/研究生/硕士/博士在读/博士/博士后/HND")
    private String educationLevelType2;
    /**
     * 毕业专业（国际）
     */
    @ApiModelProperty(value = "毕业专业（国际）")
    private String educationMajor2;
    /**
     * 毕业大学国家Id（国际）
     */
    @ApiModelProperty(value = "毕业大学国家Id（国际）")
    private Long fkAreaCountryIdEducation2;
    /**
     * 毕业大学州省Id（国际）
     */
    @ApiModelProperty(value = "毕业大学州省Id（国际）")
    private Long fkAreaStateIdEducation2;
    /**
     * 毕业大学城市Id（国际）
     */
    @ApiModelProperty(value = "毕业大学城市Id（国际）")
    @Column(name = "fk_area_city_id_education2")
    private Long fkAreaCityIdEducation2;
    /**
     * 毕业大学国家名称（国际）
     */
    @ApiModelProperty(value = "毕业大学国家名称（国际）")
    private String fkAreaCountryNameEducation2;
    /**
     * 毕业大学州省名称（国际）
     */
    @ApiModelProperty(value = "毕业大学州省名称（国际）")
    private String fkAreaStateNameEducation2;
    /**
     * 毕业大学城市名称（国际）
     */
    @ApiModelProperty(value = "毕业大学城市名称（国际）")
    private String fkAreaCityNameEducation2;
    /**
     * 毕业院校Id（国际）
     */
    @ApiModelProperty(value = "毕业院校Id（国际）")
    private Long fkInstitutionIdEducation2;
    /**
     * 毕业院校Ids（国际）
     */
    @ApiModelProperty(value = "毕业院校Ids（国际）")
    private List<Long> fkInstitutionIdsEducation2;
    /**
     * 毕业院校名称（国际）
     */
    @ApiModelProperty(value = "毕业院校名称（国际）")
    private String fkInstitutionNameEducation2;
    /**
     * 项目说明，枚举：0=3+1/1=2+2/2=4+0/3=交换生
     */
    @ApiModelProperty(value = "项目说明，枚举：0=3+1/1=2+2/2=4+0/3=交换生")
    private Integer educationProject;
    /**
     * 学位情况，枚举：获得双学位/获得国际学位/获得国内学位
     */
    @ApiModelProperty(value = "学位情况，枚举：获得双学位/获得国际学位/获得国内学位")
    private Integer educationDegree;
    /**
     * 高中成绩类型，枚举Key
     */
    @ApiModelProperty(value = "高中成绩类型，枚举Key")
    private String highSchoolTestType;
    /**
     * 高中成绩
     */
    @ApiModelProperty(value = "高中成绩")
    private String highSchoolTestScore;
    /**
     * 标准测试类型
     */
    @ApiModelProperty(value = "本科成绩类型，枚举Key")
    private String standardTestType;
    /**
     * 标准测试成绩
     */
    @ApiModelProperty(value = "本科成绩")
    private String standardTestScore;
    /**
     * 英语测试类型
     */
    @ApiModelProperty(value = "英语测试类型")
    private String englishTestType;
    /**
     * 英语测试成绩
     */
    @ApiModelProperty(value = "英语测试成绩")
    private BigDecimal englishTestScore;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 学生业务状态(多选)：0转代理学生/1奖学金占学费的60%以上
     */
    @ApiModelProperty(value = "学生业务状态(多选)：0转代理学生/1奖学金占学费的60%以上")
    private String conditionType;
    /**
     * 旧数据num(gea)
     */
    @ApiModelProperty(value = "旧数据num(gea)")
    private String numGea;
    /**
     * 旧数据num(iae)
     */
    @ApiModelProperty(value = "旧数据num(iae)")
    @Column(name = "num_iae")
    private String numIae;
    @ApiModelProperty("创建时间(开始)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date beginTime;
    @ApiModelProperty("创建时间(结束)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;
    /**
     * 代理id
     */
    @ApiModelProperty(value = "代理id")
    private Long fkAgentId;
    /**
     * 员工id
     */
    @ApiModelProperty(value = "员工id")
    private Long fkStaffId;
    /**
     * 申请步骤状态
     */
    @ApiModelProperty(value = "申请步骤状态")
    private List<Long> state;
    /**
     * 当前申请步骤状态
     */
    @ApiModelProperty(value = "当前申请步骤状态")
    private List<Long> currentState;
    /**
     * 目标国家
     */
    @ApiModelProperty(value = "目标国家")
    private Long targetCountryId;
    /**
     * 目标国家List
     */
    @ApiModelProperty(value = "目标国家List")
    private List<Long> targetCountryIdList;
    /**
     * 失败原因id
     */
    @ApiModelProperty(value = "失败原因id")
    private Long failureReasonId;
    /**
     * 是否延迟入学
     */
    @ApiModelProperty(value = "是否延迟入学")
    private Boolean isDeferEntrance;
    /**
     * 绑定代理名称
     */
    @ApiModelProperty(value = "绑定代理名称")
    private String agentName;
    /**
     * 绑定BD名称或编号
     */
    @ApiModelProperty(value = "绑定BD名称或编号")
    private String bdNameOrCode;
    /**
     * 项目角色id
     */
    @ApiModelProperty(value = "项目角色id")
    private Long projectRoleId;
    /**
     * 角色成员名称
     */
    @ApiModelProperty(value = "角色成员名称")
    private String projectStaffNameOrEnName;
    /**
     * 状态开始时间
     */
    @ApiModelProperty(value = "状态开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date statusBeginTime;
    /**
     * 状态结束时间
     */
    @ApiModelProperty(value = "状态结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date statusEndTime;
    /**
     * 旧数据id(gea)
     */
    @ApiModelProperty(value = "旧数据id(gea)")
    @Column(name = "id_gea")
    private String idGea;
    /**
     * 旧数据id(iae)
     */
    @ApiModelProperty(value = "旧数据id(iae)")
    @Column(name = "id_iae")
    private String idIae;
    /**
     * 开学日期开始时间
     */
    @ApiModelProperty(value = "开学日期开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date beginOpeningTime;
    /**
     * 开学日期结束时间
     */
    @ApiModelProperty(value = "开学日期结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endOpeningTime;
    @ApiModelProperty(value = "项目成员id列表")
    private List<Long> fkProjectMemberId;
    @ApiModelProperty(value = "毕业学校")
    private String institutionEducationName;

    /**
     * 集团名称
     */
    @ApiModelProperty(value = "集团名称")
    private String groupName;

    /**
     * 渠道名称
     */
    @ApiModelProperty(value = "渠道名称")
    private String channelName;

    /**
     * 课程等级id
     */
    @ApiModelProperty(value = "课程等级id")
    private Long majorLevelId;

    /**
     * 课程ids
     */
    @ApiModelProperty(value = "课程ids")
    private List<Long> courseIds;
    /**
     * 课程ids
     */
    @ApiModelProperty(value = "课程名称")
    private String courseName;

    /**
     * 学校ids
     */
    @ApiModelProperty(value = "学校ids")
    private List<Long> institutionIds;
    /**
     * 学校ids
     */
    @ApiModelProperty(value = "学校名称")
    private String institutionName;

    /**
     * 申请开始时间
     */
    @ApiModelProperty(value = "申请开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date itemBeginTime;

    /**
     * 申请结束时间
     */
    @ApiModelProperty(value = "申请结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date itemEndTime;
    /**
     * 延迟开始时间
     */
    @ApiModelProperty(value = "延迟开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deferEntranceStartTime;

    /**
     * 延迟结束时间
     */
    @ApiModelProperty(value = "延迟结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deferEntranceEndTime;

    /**
     * 积分活动相关属性
     */
    @ApiModelProperty(value = "学生确认时间(开始)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date dpTimeStart;

    /**
     * 积分活动相关属性
     */
    @ApiModelProperty(value = "学生确认时间(结束)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date dpTimeEnd;

    /**
     * 积分活动相关属性
     */
    @ApiModelProperty(value = "学校是否KPI")
    private Integer institutionIsKpi;

    @ApiModelProperty("变更的步骤id")
    private Long changeStepId;

    @ApiModelProperty("旧课程等级名")
    private String oldCourseMajorLevelName;

    /**
     * 硕士成绩类型，枚举Key
     */
    @ApiModelProperty(value = "硕士成绩类型，枚举Key")
    private String masterTestType;

    /**
     * 硕士成绩
     */
    @ApiModelProperty(value = "本科成绩")
    private String masterTestScore;
}

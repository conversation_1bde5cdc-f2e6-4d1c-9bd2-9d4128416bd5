package com.get.schoolGateCenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.ibatis.type.Alias;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("r_permission_group_grade_staff")
@Alias("SchoolGatePermissionGroupGradeStaff")
public class PermissionGroupGradeStaff extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 权限组别Id
     */
    @ApiModelProperty("权限组别Id")
    @Column(name = "fk_permission_group_id")
    private Long fkPermissionGroupId;
    /**
     * 权限组别Id
     */
    @ApiModelProperty("权限级别Id")
    @Column(name = "fk_permission_grade_id")
    private Long fkPermissionGradeId;
    /**
     * 员工Id
     */
    @ApiModelProperty("员工Id")
    @Column(name = "fk_staff_id")
    private Long fkStaffId;


}
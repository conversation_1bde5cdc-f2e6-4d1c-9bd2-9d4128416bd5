package com.get.schoolGateCenter.dto;

import com.get.schoolGateCenter.entity.User;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON>.
 *
 * <AUTHOR> Kevin
 * Date: 12/16/2020 12:57 PM
 * Description:
 */
@Data
@ApiModel(value = "用户返回类")
public class UserDto extends User {
    /**
     * mso用户头像
     */
    @ApiModelProperty("mso用户头像")
    private String userHeadIcon;


    /**
     * 后端权限
     */
    @ApiModelProperty("后端权限")
    private List<String> apiKeys;
    /**
     * 前端资源
     */
    @ApiModelProperty("前端资源")
    private List<String> resourceKeys;
    /**
     * 员工关联国家的id
     */
    @ApiModelProperty("员工关联国家的id")
    private List<Long> countryIds;
    /**
     * 员工当前公司及下属公司的id
     */
    @ApiModelProperty("员工当前公司及下属公司的id")
    private List<Long> companyIds;


    @ApiModelProperty("超级管理员标识")
    private Boolean isAdmin;

    @ApiModelProperty("当前登录平台")
    private String currentPlatform;

    @ApiModelProperty("issue学生表id")
    private Long studentIdIssue;


    @ApiModelProperty("学生绑定的agentId")
    private Long fkAgentId;

    @ApiModelProperty("用户学校信息")
    private List<Map<String, String>> institution;


    /**
     * tokenSessionId
     */
    private String tokenSessionId;

    @ApiModelProperty("微信昵称")
    private String weChatNickName;

    @ApiModelProperty
    private String agentCompanyName;

    @ApiModelProperty("会员过期时间（奖学金小程序）")
    private Date memberExpirationTime;

    @ApiModelProperty(value ="会员id（奖学金小程序）")
    private Long memberId;

    @ApiModelProperty("会员类型：0游客/1代理/2学生（奖学金小程序专用字段）")
    private Integer memberType;

    @ApiModelProperty("用户类型：1=VIP")
    private Integer msoUserType;

    @ApiModelProperty("是否bms直接登录")
    private boolean isBmsLogin;


    @ApiModelProperty("授权用户访问桶网址")
    private String ossFilesUrl;

    @ApiModelProperty("公开用户访问桶网址")
    private String ossImagesUrl;

    @ApiModelProperty("issue登录来源")
    private String issueSource;

    @ApiModelProperty("用户账户绑定的公司id（实习小程序专用字段）")
    private Long fkCompanyId;

    @ApiModelProperty("账号类型：0主账号/1子账号（实习小程序专用字段）")
    private Integer companyAccountType;

    @ApiModelProperty(value = "用户当前登录会话id")
    @Column(name = "session_id")
    private String sessionId;

}

package com.get.schoolGateCenter.dto;

import com.get.schoolGateCenter.entity.PermissionGrade;
import com.get.schoolGateCenter.entity.PermissionGroup;
import com.get.schoolGateCenter.entity.PermissionGroupGradeStaff;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel("权限人员返回类")
@Data
public class PermissionGroupGradeStaffDto extends PermissionGroupGradeStaff {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "权限组别和权限等级对应数组")
    List<GroupGradeStaffDto> GroupGradeStaffDto;
    @ApiModelProperty(value = "纵坐标权限等级")
    List<PermissionGrade> permissionGrades;
    @ApiModelProperty(value = "横坐标权限组别")
    List<PermissionGroup> permissionGroups;
    /**
     * 权限组别名称
     */
    @ApiModelProperty("权限组别名称")
    private String permissionGroupName;
    /**
     * 权限等级名称
     */
    @ApiModelProperty("权限等级名称")
    private String permissionGradeName;
    @ApiModelProperty(value = "权限名称")
    private String permissionName;

}

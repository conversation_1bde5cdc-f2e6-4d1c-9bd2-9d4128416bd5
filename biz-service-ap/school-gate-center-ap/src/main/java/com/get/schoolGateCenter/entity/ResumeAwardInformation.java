package com.get.schoolGateCenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-24
 */
@Data
@Accessors(chain = true)
@TableName("m_resume_award_information")
@ApiModel(value="MResumeAwardInformation对象", description="")
public class ResumeAwardInformation extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "简历关联id")
    private Long fkResumeId;

    @ApiModelProperty(value = "枚举(活动类别)")
    private Long activityCategory;

    @ApiModelProperty(value = "其他(自定义类别)")
    private String customCategory;

    @ApiModelProperty(value = "奖项名称")
    private String activityName;

    @ApiModelProperty(value = "机构名称")
    private String mechanism;

    @ApiModelProperty(value = "年份(由)")
    private String byYear;

    @ApiModelProperty(value = "年份(至)")
    private String toYear;

    @ApiModelProperty(value = "级别")
    private String level;

    @ApiModelProperty(value = "奖品")
    private String prize;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "标记")
    private String mark;

}

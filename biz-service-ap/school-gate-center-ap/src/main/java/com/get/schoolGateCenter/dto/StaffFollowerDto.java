package com.get.schoolGateCenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @DATE: 2020/11/12
 * @TIME: 18:17
 * @Description:
 **/
@Data
public class StaffFollowerDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("层级")
    private Long level;

    @ApiModelProperty("下属名字")
    private String followerName;

    @ApiModelProperty("上级名称")
    private String leaderName;

    @ApiModelProperty("下属公司名称")
    private String followerCompanyName;

    @ApiModelProperty("上级公司名称")
    private String leaderCompanyName;

}

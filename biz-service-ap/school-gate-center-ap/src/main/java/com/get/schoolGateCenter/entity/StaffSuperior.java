package com.get.schoolGateCenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.ibatis.type.Alias;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("r_staff_superior")
@Alias("SchoolGateStaffSuperior")
public class StaffSuperior extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 员工Id
     */
    @ApiModelProperty(value = "员工Id")
    @Column(name = "fk_staff_id")
    private Long fkStaffId;
    /**
     * 上司Id
     */
    @ApiModelProperty(value = "上司Id")
    @Column(name = "fk_staff_superior_id")
    private Long fkStaffSuperiorId;
}
package com.get.schoolGateCenter.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;

@Data
@TableName("s_news")
public class News extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    @Column(name = "fk_table_name")
    private String fkTableName;
    /**
     * 表Id
     */
    @ApiModelProperty(value = "表Id")
    @Column(name = "fk_table_id")
    private Long fkTableId;
    /**
     * 新闻类型Id
     */
    @ApiModelProperty(value = "新闻类型Id")
    @Column(name = "fk_news_type_id")
    private Long fkNewsTypeId;
    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    @Column(name = "title")
    private String title;
    /**
     * 公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2
     */
    @ApiModelProperty(value = "公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
    @Column(name = "public_level")
    private String publicLevel;
    /**
     * 发布时间
     */
    @ApiModelProperty(value = "发布时间")
    @Column(name = "publish_time")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date publishTime;
    /**
     * 描述内容
     */
    @ApiModelProperty(value = "描述内容")
    @Column(name = "description")
    private String description;
    /**
     * 简介
     */
    @ApiModelProperty(value = "简介")
    @Column(name = "profile")
    private String profile;
    /**
     * 网页标题
     */
    @ApiModelProperty(value = "网页标题seo标题")
    @Column(name = "web_title")
    private String webTitle;
    /**
     * 网页Meta描述
     */
    @ApiModelProperty(value = "网页Meta描述SEO描述")
    @Column(name = "web_meta_description")
    private String webMetaDescription;
    /**
     * 网页Meta关键字
     */
    @ApiModelProperty(value = "网页Meta关键字SEO关键词")
    @Column(name = "web_meta_keywords")
    private String webMetaKeywords;

    @ApiModelProperty(value = "生效时间")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Column(name = "effective_start_time")
    @UpdateWithNull
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Date effectiveStartTime;

    @ApiModelProperty(value = "失效时间")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Column(name = "effective_end_time")
    @UpdateWithNull
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Date effectiveEndTime;

    @ApiModelProperty(value = "推荐新闻类型，支持多选，逗号隔开，如：1,2,3")
    @Column(name = "fk_news_type_id_recommend")
    @TableField(updateStrategy =  FieldStrategy.IGNORED)
    private String fkNewsTypeIdRecommend;

    @ApiModelProperty(value = "跳转链接")
    @Column(name = "goto_url")
    private String gotoUrl;

    @ApiModelProperty(value = "活动开始时间")
    @Column(name = "activity_start_time")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date activityStartTime;

    @ApiModelProperty(value = "活动结束时间")
    @Column(name = "activity_end_time")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date activityEndTime;



}
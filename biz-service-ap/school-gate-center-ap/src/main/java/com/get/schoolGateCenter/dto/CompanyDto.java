package com.get.schoolGateCenter.dto;

import com.get.schoolGateCenter.entity.Company;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/6/24
 * @TIME: 14:30
 **/
@Data
public class CompanyDto extends Company {
    private static final long serialVersionUID = 1L;

    /**
     * 子公司集合
     */
    @ApiModelProperty(value = "子公司集合")
    private List<CompanyDto> childCompanyDto;

    @ApiModelProperty(value = "公司图标链接")
    private MediaAndAttachedDto companyIcon;

}

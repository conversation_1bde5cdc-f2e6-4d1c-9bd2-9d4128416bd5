package com.get.schoolGateCenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.schoolGateCenter.entity.SchoolGateStaff;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Set;

@ApiModel("员工返回类")
@Data
public class SchoolGateStaffDto extends SchoolGateStaff {
    private static final long serialVersionUID = 7481120639193000342L;
    /**
     * 后端权限
     */
    @ApiModelProperty("后端权限")
    Set<String> apiKeys;
    /**
     * 前端资源
     */
    @ApiModelProperty("前端资源")
    List<String> resourceKeys;
    /**
     * 员工关联国家的id
     */
    @ApiModelProperty("员工关联国家的id")
    List<Long> countryIds;
    /**
     * 员工当前公司及下属公司的id
     */
    @ApiModelProperty("员工当前公司及下属公司的id")
    List<Long> companyIds;

    /**
     * 全名
     */
    @ApiModelProperty("全名")
    private String fullName;

    @ApiModelProperty("是否激活名称")
    private String isActiveName;

    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "部门名称")
    private String departmentName;

    @ApiModelProperty(value = "职位名称")
    private String positionName;

    @ApiModelProperty(value = "办公室名称")
    private String officeName;

    @ApiModelProperty(value = "业务国家")
    private List<String> areaCountryDtos;

    @ApiModelProperty(value = "业务办公室")
    private List<String> staffOfficeDtos;

    @ApiModelProperty(value = "头像链接")
    private MediaAndAttachedDto headImageDto;

    @ApiModelProperty(value = "直属上司名称")
    private String supervisorName;

    @ApiModelProperty(value = "直属上司公司id")
    private Long supervisorCompanyId;

    @ApiModelProperty(value = "手机区号名称")
    private String mobileAreaCodeName;


    @ApiModelProperty(value = "劳动合开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date contractStartTime;

    @ApiModelProperty(value = "联系地址国家名称")
    private String areaCountryName;

    @ApiModelProperty("联系地址州省名称")
    private String areaStateName;

    @ApiModelProperty("联系地址城市名称")
    private String areaCityName;

    @ApiModelProperty("联系地址城市区域名称")
    private String areaCityDivisionName;

    @ApiModelProperty("户口性质名称")
    private String hukouName;

    @ApiModelProperty("证件类型名称")
    private String identityTypeName;

    @ApiModelProperty(value = "职位编号")
    private String positionNum;

    /**
     * bms授权共享访问的文件URL，目前仅在学校中心使用
     */
    @ApiModelProperty("bms授权共享访问的文件URL")
    private String bmsPrivateFilesShareUrl;

    /**
     * bms授权访问的文件URL
     */
    @ApiModelProperty("bms授权访问的文件URL")
    private String bmsPrivateFilesUrl;
    /**
     * bms公开访问的文件URL
     */
    @ApiModelProperty("bms公开访问的文件URL")
    private String bmsPublicFilesUrl;
}

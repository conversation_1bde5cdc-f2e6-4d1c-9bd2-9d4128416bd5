package com.get.schoolGateCenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @DATE: 2020/7/7
 * @TIME: 15:18
 * @Description:
 **/
@Data
public class StaffContractVo extends BaseVoEntity {
    private static final long serialVersionUID = 1L;
    /**
     * 员工Id
     */
    @ApiModelProperty(value = "员工Id", required = true)
    @NotNull(message = "员工id不能为空", groups = {Add.class, Update.class})
    private Long fkStaffId;

    /**
     * 合同开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "合同开始时间", required = true)
    @NotNull(message = "合同开始时间不能为空", groups = {Add.class, Update.class})
    private Date startTime;

    /**
     * 合同结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "合同结束时间", required = true)
    @NotNull(message = "合同结束时间不能为空", groups = {Add.class, Update.class})
    private Date endTime;

    /**
     * 签约公司
     */
    @ApiModelProperty(value = "签约公司", required = true)
    @NotBlank(message = "签约公司不能为空", groups = {Add.class, Update.class})
    private String signingCompany;

    /**
     * 签约工资
     */
    @ApiModelProperty(value = "签约工资")
    private BigDecimal signingSalary;

    /**
     * 社保属地
     */
    @ApiModelProperty(value = "社保属地")
    private String socialInsurancePlace;

    /**
     * 工作地
     */
    @ApiModelProperty(value = "工作地")
    private String workplace;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @NotNull(message = "合同状态不能为空", groups = {Add.class, Update.class})
    private Boolean isActive;

    /**
     * 关键字
     */
    @ApiModelProperty(value = "查询关键字")
    private String keyWord;

}

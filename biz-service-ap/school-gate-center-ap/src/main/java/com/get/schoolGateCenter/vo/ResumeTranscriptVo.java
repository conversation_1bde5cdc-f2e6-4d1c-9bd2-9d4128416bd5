package com.get.schoolGateCenter.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import com.get.schoolGateCenter.dto.FileDto;
import com.get.schoolGateCenter.entity.ResumeTranscript;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-24
 */
@Data
public class ResumeTranscriptVo extends ResumeTranscript implements Serializable {

    @ApiModelProperty("文件内容")
    private List<FileDto> fileDtos;


}

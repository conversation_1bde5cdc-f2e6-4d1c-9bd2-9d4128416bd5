package com.get.schoolGateCenter.vo;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: Sea
 * @create: 2021/3/3 14:47
 * @verison: 1.0
 * @description:
 */
@Data
public class AreaCityDivisionVo extends BaseVoEntity {
    /**
     * 城市Id
     */
    @ApiModelProperty(value = "城市Id")
    private Long fkAreaCityId;

    /**
     * 区域名称
     */
    @ApiModelProperty(value = "区域名称")
    private String name;

    /**
     * 街道名称，逗号隔开，如：洪桥街道,北京街道,六榕街道
     */
    @ApiModelProperty(value = "街道名称，逗号隔开，如：洪桥街道,北京街道,六榕街道")
    private String streetsName;

    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;
}

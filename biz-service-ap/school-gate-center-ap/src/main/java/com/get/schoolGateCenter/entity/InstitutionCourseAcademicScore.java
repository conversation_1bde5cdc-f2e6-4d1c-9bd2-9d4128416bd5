package com.get.schoolGateCenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@TableName("m_institution_course_academic_score")
public class InstitutionCourseAcademicScore extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 课程Id
     */
    @ApiModelProperty(value = "课程Id")
    @Column(name = "fk_institution_course_id")
    private Long fkInstitutionCourseId;
    /**
     * 国家Id
     */
    @ApiModelProperty(value = "国家Id")
    @Column(name = "fk_area_country_id")
    private Long fkAreaCountryId;
    /**
     * 条件类型，枚举：GPA/GCSE/HKDSE/IB
     */
    @ApiModelProperty(value = "条件类型，枚举：GPA/GCSE/HKDSE/IB")
    @Column(name = "condition_type")
    private Integer conditionType;
    /**
     * 分数类型
     */
    @ApiModelProperty(value = "分数类型")
    @Column(name = "score_type")
    private String scoreType;
    /**
     * 分数
     */
    @ApiModelProperty(value = "分数")
    @Column(name = "score")
    private BigDecimal score;
    /**
     * 成绩描述
     */
    @ApiModelProperty(value = "成绩描述")
    @Column(name = "description")
    private String description;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", fkInstitutionCourseId=").append(fkInstitutionCourseId);
        sb.append(", fkAreaCountryId=").append(fkAreaCountryId);
        sb.append(", conditionType=").append(conditionType);
        sb.append(", scoreType=").append(scoreType);
        sb.append(", score=").append(score);
        sb.append(", description=").append(description);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}
package com.get.institutioncenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;

@Data
@TableName("r_institution_provider_institution")
public class InstitutionProviderInstitution extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "关系所属公司Id，多选，英文逗号分隔")
    @Column(name = "fk_company_ids")
    private String fkCompanyIds;

    /**
     * 学校提供商Id
     */
    @ApiModelProperty(value = "学校提供商Id")
    @Column(name = "fk_institution_provider_id")
    private Long fkInstitutionProviderId;
    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    @Column(name = "fk_institution_id")
    private Long fkInstitutionId;
    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @Column(name = "is_active")
    private Boolean isActive;

    /**
     * 绑定时间
     */
    @ApiModelProperty(value = "绑定时间")
    @Column(name = "active_date")
    private Date activeDate;

    /**
     * 取消绑定时间
     */
    @ApiModelProperty(value = "取消绑定时间")
    @Column(name = "unactive_date")
    private Date unactiveDate;

}
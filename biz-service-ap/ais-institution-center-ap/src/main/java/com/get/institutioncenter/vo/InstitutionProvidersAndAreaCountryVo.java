package com.get.institutioncenter.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class InstitutionProvidersAndAreaCountryVo extends BaseEntity {
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @Column(name = "name")
    private String name;
    /**
     * 中文名称
     */
    @ApiModelProperty(value = "中文名称")
    @Column(name = "name_chn")
    private String nameChn;
    /**
     * 业务国家id
     */
    private Long institutionProviderAreaCountryId;
    /**
     *学校提供商id
     */
    private Long institutionProviderId;
    /**
     * 业务国家id
     */
    private Long areaCountryId;


}

package com.get.institutioncenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * @author: Sea
 * @create: 2021/1/13 17:06
 * @verison: 1.0
 * @description:
 */
@Data
public class AreaCountryInfoDto extends BaseVoEntity {
    /**
     * 国家资讯类型Id
     */
    @NotNull(message = "国家资讯类型不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "国家资讯类型Id")
    private Long fkAreaCountryInfoTypeId;

    /**
     * 国家Id
     */
    @ApiModelProperty(value = "国家Id")
    private Long fkAreaCountryId;

    /**
     * 标题
     */
    @NotBlank(message = "标题不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    /**
     * 公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2
     */
    @NotBlank(message = "公开对象不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
    private String publicLevel;

    /**
     * 发布时间
     */
    @ApiModelProperty(value = "发布时间")
    @NotNull(message = "发布时间不能为空", groups = {Add.class, Update.class})
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "publish_time")
    private Date publishTime;

    /**
     * 内容描述
     */
    @NotBlank(message = "内容描述不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "内容描述")
    private String description;


    /**
     * 网页标题
     */
    @ApiModelProperty(value = "网页标题")
    private String webTitle;

    /**
     * 网页Meta描述
     */
    @ApiModelProperty(value = "网页Meta描述")
    private String webMetaDescription;

    /**
     * 网页Meta关键字
     */
    @ApiModelProperty(value = "网页Meta关键字")
    private String webMetaKeywords;
}

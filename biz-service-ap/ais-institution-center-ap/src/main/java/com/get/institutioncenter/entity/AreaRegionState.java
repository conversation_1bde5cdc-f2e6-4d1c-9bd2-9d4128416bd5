package com.get.institutioncenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("r_area_region_state")
public class AreaRegionState extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 大区Id
     */
    @ApiModelProperty(value = "大区Id")
    @Column(name = "fk_area_region_id")
    private Long fkAreaRegionId;
    /**
     * 州省Id
     */
    @ApiModelProperty(value = "州省Id")
    @Column(name = "fk_area_state_id")
    private Long fkAreaStateId;
}
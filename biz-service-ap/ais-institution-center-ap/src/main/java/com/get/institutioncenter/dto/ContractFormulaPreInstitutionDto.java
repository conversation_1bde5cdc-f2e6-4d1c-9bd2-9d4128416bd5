package com.get.institutioncenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 合同公式-前置学校
 *
 * @Date 16:21 2021/6/2
 * <AUTHOR>
 */
@Data
public class ContractFormulaPreInstitutionDto extends BaseVoEntity {
    /**
     * 合同公式Id
     */
    @ApiModelProperty(value = "合同公式Id")
    private Long fkContractFormulaId;

    /**
     * 学校Id(前置学校)
     */
    @ApiModelProperty(value = "学校Id(前置学校)")
    private Long fkInstitutionId;

}

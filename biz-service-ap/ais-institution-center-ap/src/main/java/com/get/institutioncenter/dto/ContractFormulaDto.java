package com.get.institutioncenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author: Sea
 * @create: 2021/1/8 10:14
 * @verison: 1.0
 * @description:
 */
@Data
public class ContractFormulaDto extends BaseVoEntity {
    /**
     * 学校提供商Id
     */
    @ApiModelProperty(value = "学校提供商Id")
    private Long fkInstitutionProviderId;

    /**
     * 公式类型：0常规/1奖励/2一次性奖励
     */
    @NotNull(message = "公式类型不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "公式类型：0常规/1奖励/2一次性奖励")
    private Integer formulaType;

    /**
     * 公式名称
     */
    @NotBlank(message = "公式名称不能为空")
    @ApiModelProperty(value = "公式名称")
    private String title;

    /**
     * 统计类型：0学生数，1学费，2课程长度(周)，3课程长度(月)，4课程长度(年)
     */
//    @ApiModelProperty(value = "统计类型：0学生数")
    @ApiModelProperty(value = "统计类型：0学生数，1学费，2课程长度(周)，3课程长度(月)，4课程长度(年)")
    private Integer countType;

    /**
     * 统计目标值(小)
     */
    @ApiModelProperty(value = "统计目标值(小)")
    private BigDecimal countValeMin;

    /**
     * 统计目标值(大)
     */
    @ApiModelProperty(value = "统计目标值(大)")
    private BigDecimal countValeMax;

    /**
     * 代理佣金币种编号（不同佣金币种时才需要填写）
     */
    @ApiModelProperty(value = "代理佣金币种编号")
    private String fkCurrencyTypeNumAg;

    /**
     * 佣金上限(总)
     */
    @ApiModelProperty(value = "佣金上限(总)")
    private BigDecimal limitAmount;

    /**
     * 代理佣金上限(总)
     */
    @ApiModelProperty(value = "代理佣金上限(总)")
    private BigDecimal limitAmountAg;

    /**
     * 条件类型（枚举）：0转代理学生 / 1学生获得奖学金占学费的60%以上 / 4只读第一阶段、5只读第二阶段、6只读第三阶段、7只读第四阶段
     */
    @ApiModelProperty(value = "条件类型（枚举）：0转代理学生 / 1学生获得奖学金占学费的60%以上 / 4只读第一阶段、5只读第二阶段、6只读第三阶段、7只读第四阶段")
    private String conditionType;

    /**
     * 生效开始时间
     */
    @ApiModelProperty(value = "生效开始时间")
    private Date startTime;

    /**
     * 生效结束时间
     */
    @ApiModelProperty(value = "生效结束时间")
    private Date endTime;

    /**
     * 币种编号
     */
    @NotBlank(message = "币种编号不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;

    /**
     * 公式备注
     */
    @ApiModelProperty(value = "公式备注")
    private String remark;

    /**
     * 排序（倒序），数字由大到小排列
     */
    @ApiModelProperty(value = "排序（倒序），数字由大到小排列")
    private Integer viewOrder;

    /**
     * 是否激活：0否/1是
     */
    @NotNull(message = "是否生效不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "是否激活：0否/1是")
    private Boolean isActive;

    //自定义内容
    /**
     * 国家区域(多选)
     */
    @ApiModelProperty(value = "国家区域(多选)")
    private List<Long> countryIdList;

    /**
     * 课程类型(多选)
     */
    @ApiModelProperty(value = "课程类型(多选)")
    private List<Long> courseTypeIdList;

    /**
     * 课程等级(多选)
     */
    @ApiModelProperty(value = "课程等级(多选)")
    private List<Long> majorLevelIdList;

    /**
     * 学生来源国家区域(多选)
     */
    @ApiModelProperty(value = "学生来源国家区域(多选)")
    private List<Long> studentCountryIdList;

    /**
     * 学校(多选)
     */
    @ApiModelProperty(value = "学校(多选)")
    private List<Long> institutionIdList;

    /**
     * 课程(多选)
     */
    @ApiModelProperty(value = "课程(多选)")
    private List<Long> courseIdList;

    /**
     * 合同公式佣金配置
     */
    @ApiModelProperty(value = "合同公式佣金配置")
    private List<ContractFormulaCommissionDto> contractFormulaCommissionVos;

    /**
     * 前置学校集团(多选)
     */
    @ApiModelProperty(value = "前置学校集团(多选)")
    private List<Long> preGroupIdList;

    /**
     * 前置学校(多选)
     */
    @ApiModelProperty(value = "前置学校(多选)")
    private List<Long> preInstitutionIdList;

    /**
     * 前置专业等级(多选)
     */
    @ApiModelProperty(value = "前置专业等级(多选)")
    private List<Long> preMajorLevelList;

    /**
     * 校区(多选)
     */
    @ApiModelProperty(value = "校区(多选)")
    private List<Long> zoneIdList;

    /**
     * 学院(多选)
     */
    @ApiModelProperty(value = "学院(多选)")
    private List<Long> facultyIdList;

    /**
     * 所属渠道Id(多选)
     */
    @ApiModelProperty(value = "所属渠道Id(多选)")
    @NotNull(message = "所属渠道不能为空", groups = {Add.class, Update.class})
    private List<Long> institutionChannelIds;
    /**
     * 模糊搜索（标题和备注）
     */
    @ApiModelProperty(value = "模糊搜索（标题和备注）")
    private String keyWord;

    /**
     * 国籍（学生出生地国家/地区）【不包括】
     */
    @ApiModelProperty(value = "国籍（学生出生地国家/地区）【不包括】")
    private List<Long> studentCountryIdNotInList;
}

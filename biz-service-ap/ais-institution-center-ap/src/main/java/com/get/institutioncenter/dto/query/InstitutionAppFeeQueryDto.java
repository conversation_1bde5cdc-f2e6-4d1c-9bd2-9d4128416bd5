package com.get.institutioncenter.dto.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class InstitutionAppFeeQueryDto {
    @ApiModelProperty(value = "目标对象Id")
    private Long fkTableId;

    @ApiModelProperty(value = "目标类型")
    private String fkTableName;

    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    private Boolean isActive;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;

    //=========新增属性===================
    @ApiModelProperty(value = "是否免申请费，0否/1是")
    private Boolean isFree;

    @ApiModelProperty("等级类型：0本科/1硕士/2社区")
    private Integer levelType;

    @ApiModelProperty(value = "名称搜索")
    private String keyword;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;

}

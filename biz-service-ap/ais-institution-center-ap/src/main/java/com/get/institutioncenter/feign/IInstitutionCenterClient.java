package com.get.institutioncenter.feign;

/**
 * <AUTHOR>
 * @DATE: 2021/12/29
 * @TIME: 10:26
 * @Description:
 **/

import com.get.common.constant.AppCenterConstant;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.tool.api.Result;
import com.get.institutioncenter.bo.InstitutionApplicationStaticsQueryBo;
import com.get.institutioncenter.dto.NewsEmailTmpeleteVo;
import com.get.institutioncenter.dto.query.InstitutionQueryDto;
import com.get.institutioncenter.vo.*;
import com.get.institutioncenter.vo.AreaCountryVo;
import com.get.institutioncenter.entity.AreaCountry;
import com.get.institutioncenter.entity.Contract;
import com.get.institutioncenter.entity.ContractFormula;
import com.get.institutioncenter.entity.Institution;
import com.get.institutioncenter.entity.MajorLevel;
import com.get.institutioncenter.dto.*;
import com.get.permissioncenter.vo.CompanyVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 学校 Feign接口类          fallback = IInstitutionCenterClientFallBack.class
 */
@FeignClient(
        value = AppCenterConstant.APPLICATION_INSTITUTION_CENTER
)
public interface IInstitutionCenterClient {
    String API_PREFIX = "/feign";

    /**
     * Feign 获取官网链接
     */
    String GET_WEBSITE_BY_TABLE = API_PREFIX + "/get-web-site-by-table";
    /**
     * 获取官网链接  通过对象传递
     */
    String GET_WEB_SITE_BY_APP_INFO_FEIGN_DTO = API_PREFIX + "/get-web-site-by-app-info-feign-dto";
    /**
     * 通过城市id 查找对应的城市名称
     */
    String GET_CITY_NAME_BY_ID = API_PREFIX + "/get-city-name-by-id";
    /**
     * feign调用 通过城市ids 查找对应的城市名称map
     */
    String GET_CITY_NAMES_BY_IDS = API_PREFIX + "/get-city-names-by-ids";
    /**
     * feign调用 通过城市ids 查找对应的城市中文名称map
     */
    String GET_CITY_NAME_CHNS_BY_IDS = API_PREFIX + "/get-city-name-chns-by-ids";
    /**
     * 通过城市ids 查找对应的城市名称map（拼接name_cn）
     */
    String GET_CITY_FULL_NAMES_BY_IDS = API_PREFIX + "/get-city-full-names-by-ids";
    /**
     * 获取城市名称map（拼接name_cn）
     */
    String GET_CITY_FULL_NAMES = API_PREFIX + "/get-city-full-names";
    /**
     * 通过城市区域ids 查找对应的城市区域名称map（拼接streetsName）
     */
    String GET_CITY_DIVISION_FULL_NAMES_BY_IDS = API_PREFIX + "/get-city-division-full-names-by-ids";
    /**
     * feign 调用 根据国家编号查找国家名称
     */
    String GET_COUNTRY_NAME = API_PREFIX + "/get-country-name";

    /**
     * feign 调用 根据国家编号查找国家名称
     */
    String GET_COUNTRY_NAME_EN = API_PREFIX + "/get-country-name-en";


    /**
     * feign  获取所有国家id
     */
    String GET_ALL_COUNTRY_ID = API_PREFIX + "/get-all-country-id";
    /**
     * 根据国家id查找国家名称
     */
    String GET_COUNTRY_NAME_BY_ID = API_PREFIX + "/get-country-name-by-id";

    String GET_COUNTRY_BY_ID = API_PREFIX + "/get-country-by-id";
    /**
     * 根据国家id查找国家名称
     */
    String GET_COUNTRY_CHN_NAME_BY_ID = API_PREFIX + "/get-country-chn-name-by-id";
    /**
     * feign 调用 通过国家ids 查找对应的国家名称
     */
    String GET_COUNTRY_CHN_NAME_BY_IDS = API_PREFIX + "/get-country-chn-name-by-ids";
    /**
     * feign调用 根据key查找id
     */
    String GET_COUNTRY_ID_BY_KEY = API_PREFIX + "/get-country-id-by-key";
    /**
     * 根据国家ids查找国家名称map
     */
    String GET_COUNTRY_NAMES_BY_IDS = API_PREFIX + "/get-country-names-by-ids";
    /**
     * 获取所有国家名称map
     */
    String GET_COUNTRY_NAME_MAP = API_PREFIX + "/get-country-name-map";
    /**
     * 根据国家id查找国家key
     */
    String GET_COUNTRY_KEY_BY_ID = API_PREFIX + "/get-country-key-by-id";
    /**
     * 根据州省IDs获取国家IDs
     */
    String GET_COUNTRY_IDS_BY_STATE_IDS = API_PREFIX + "get-country-ids-by-state-ids";
    /**
     * feign调用,根据ids获取对象集合
     */
    String GET_AREA_REGION_DTO_BY_IDS = API_PREFIX + "/get-area-region-dto-by-ids";
    /**
     * 通过州省id 查找对应的州省名称
     */
    String GET_STATE_NAME_BY_ID = API_PREFIX + "/get-State-Name-By-Id";
    /**
     * 通过州省id 查找对应的州省名称
     */
    String GET_STATE_FULL_NAME_BY_ID = API_PREFIX + "/get-state-full-name-by-id";
    /**
     * feign调用 通过州省ids 查找对应的州省名称map
     */
    String GET_STATE_NAMES_BY_IDS = API_PREFIX + "/get-State-Names-By-Ids";
    /**
     * 通过州省ids 查找对应的州省名称map（拼接name_cn）
     */
    String GET_STATE_FULL_NAMES_BY_IDS = API_PREFIX + "/get-state-full-names-by-ids";
    /**
     * 获取州省名称
     */
    String GET_STATE_FULL_NAMES = API_PREFIX + "/get-state-full-names";
    /**
     * feign调用，根据id查询表
     */
    String GET_INSTITUTION_CONTRACT_BY_ID = API_PREFIX + "/get-Institution-Contract-By-Id";
    /**
     * 修改m_contract数据
     */
    String UPDATE_CHANGE_STATUS = API_PREFIX + "/update-Change-Status";
    String CHANGE_STATUS = API_PREFIX + "/change-Status";
    /**
     * feign调用 通过合同公式ids 查找对应的合同公式map
     */
    String GET_CONTRACT_FORMULAS_BY_IDS = API_PREFIX + "/get-Contract-Formulas-By-Ids";
    /**
     * feign调用 通过合同公式查询合同公式佣金配置
     */
    String GET_CONTRACT_FORMULA_COMMISSION_BY_CONTRACT_FORMULA_ID = API_PREFIX + "/get-contract-formula-commission-by-contract-formula-id";
    /**
     * feign调用 通过合同公式查询前置学校
     */
    String GET_CONTRACT_FORMULA_PRE_INSTITUTION_BY_CONTRACT_FORMULA_ID = API_PREFIX + "/get-contract-formula-pre-institution-by-contract-formula-id";
    /**
     * feign调用 通过合同公式查询前置集团
     */
    String GET_PRE_INSTITUTION_GROUP_BY_CONTRACT_FORMULA_ID = API_PREFIX + "/get-pre-institution-group-by-contract-formula-id";
    /**
     * feign调用 通过合同公式查询前置课程等级
     */
    String GET_PRE_MAJOR_LEVEL_BY_CONTRACT_FORMULA_ID = API_PREFIX + "/get-pre-major-level-by-contract-formula-id";
    /**
     * feign调用 通过合同公式id查询合同公式  学校课程信息
     */
    String GET_CONTRACT_FORMULA_CONFIG_BY_CONTRACT_FORMULA_ID = API_PREFIX + "/get-contract-formula-config-by-contract-formula-id";
    /**
     * 通过课程类型ids 查找对应的课程类型名称map
     */
    String GET_COURSE_TYPE_NAMES_BY_IDS = API_PREFIX + "/get-course-type-names-by-ids";

    String GET_COURSE_TYPE_NAMES_BY_COURSE_GROUP_TYPE_IDS = API_PREFIX + "/get-course-type-names-by-course-group-type-ids";
    /**
     * 通过课程ids 查找对应的课程类型名称map
     */
    String GET_COURSE_TYPE_NAMES_BY_COURSE_IDS = API_PREFIX + "/get-course-type-names-by-course-ids";
    /**
     * 获取渠道名称
     */
    String GET_CHANNEL_BY_IDS = API_PREFIX + "/get-channel-by-ids";
    /**
     * feign调用 根据输入的学校名称 模糊查询对应的学校ids
     */
    String GET_INSTITUTION_IDS = API_PREFIX + "/get-institution-ids";
    /**
     * feign调用 根据学校id查找学校名称
     */
    String GET_INSTITUTION_NAME = API_PREFIX + "/get-institution-name";

    String GET_INSTITUTION_BY_ID = API_PREFIX + "/get-institution-by-id";
    /**
     * 根据学校ids 查找对应名称map
     */
    String GET_INSTITUTION_NAMES_BY_IDS = API_PREFIX + "/get-institution-names-by-ids";
    /**
     * feign调用 根据学校id查找学校名称
     */
    String GET_INSTITUTION_NAMES_BY_ID = API_PREFIX + "/get-institution-names-by-id";
    /**
     * feign调用 根据学校ids查找国家名字
     */
    String GET_COUNTRY_NAMES_BY_INSTITUTION_IDS = API_PREFIX + "/get-country-names-by-institution-ids";
    /**
     * feign调用 根据学校id查找学校国家
     */
    String GET_COUNTRY_ID_BY_INSTITUTION_ID = API_PREFIX + "/get-country-id-by-institution-id";
    /**
     * 根据课程名称模糊查询
     */
    String GET_COURSE_IDS = API_PREFIX + "/get-course-ids";
    /**
     * 根据id获取课程名称
     */
    String GET_COURSE_NAME_BY_ID = API_PREFIX + "/get-course-name-by-id";
    /**
     * 根据ids获取课程名称
     */
    String GET_COURSE_NAME_BY_IDS = API_PREFIX + "/get-course-name-by-ids";
    /**
     * feign调用 通过自定义课程ids 查找对应的自定义课程名称
     *
     * @param ids
     * @return
     */
    String GET_COURSE_NAME_BY_CUSTOM_IDS = API_PREFIX + "/get-course-name-by-custom-ids";
    /**
     * 根据id获取课程名称
     */
    String GET_COURSE_NAME_CHN_BY_ID = API_PREFIX + "/get-course-name-chn-by-id";

    String GET_COURSE_BY_ID = API_PREFIX + "/get-course-by-id";
    /**
     * 根据ids获取课程名称
     */
    String GET_COURSE_NAME_CHN_BY_IDS = API_PREFIX + "/get-course-name-chn-by-ids";
    /**
     * 根据课程id查詢學費
     */
    String GET_FEE_BY_ID = API_PREFIX + "/get-fee-by-id";
    /**
     * 根据课程ids查詢學費
     */
    String GET_SUM_FEE_BY_IDS = API_PREFIX + "/get-sum-fee-by-ids";
    /**
     * feign调用 根据课程ids 获取课程名
     */
    String GET_INSTITUTION_COURSE_NAMES_BY_IDS = API_PREFIX + "/get-institution-course-names-by-ids";
    /**
     * 根据id获取课程名称
     */
    String GET_INSTITUTION_COURSE_NAME_BY_ID = API_PREFIX + "/get-institution-course-name-by-id";
    /**
     * 根据课程ids 查找对应课程名称map
     */
    String GET_INSTITUTION_GROUP_NAMES_BY_IDS = API_PREFIX + "/get-institution-group-names-by-ids";
    /**
     * feign调用 根据输入的学校提供商名称 模糊查询对应的学校提供商id
     */
    String GET_INSTITUTION_PROVIDER_IDS_BY_NAME = API_PREFIX + "/get-institution-provider-ids-by-name";
    /**
     * feign调用 根据输入的学校提供商id 查询对应的学校提供商名称
     */
    String GET_INSTITUTION_PROVIDER_NAME = API_PREFIX + "/get-institution-provider-name";
    /**
     * fegin调用 根据学校提供商ids 查询名称map
     */
    String GET_INSTITUTION_PROVIDER_NAMES_BY_IDS = API_PREFIX + "/get-institution-provider-names-by-ids";
    /**
     * feign调用 根据学校提供商id查找学校提供商类型
     */
    String GET_INSTITUTION_PROVIDER_TYPE = API_PREFIX + "/get-institution-provider-type";
    /**
     * 学校提供商下拉框数据（feign）
     */
    String GET_INSTITUTION_PROVIDER_SELECT = API_PREFIX + "/get-institution-provider-select";
    /**
     * 根据提供商ids查询公司
     */
    String GET_COMPANY_IDS_BY_PROVIDER_IDS = API_PREFIX + "/get-company-ids-by-provider-ids";

    String GET_PROVIDER_COMPANY_NAME = API_PREFIX + "/get-provider-company-name";
    /**
     * feign调用 获取所有的渠道Map
     */
    String GET_INSTITUTION_PROVIDER_CHANNEL = API_PREFIX + "/get-institution-provider-channel";
    /**
     * feign调用 获取渠道名By id
     */
    String GET_INSTITUTION_PROVIDER_CHANNEL_BY_ID = API_PREFIX + "/get-institution-provider-channel-by-id";

    String GET_INSTITUTION_PROVIDER_CHANNEL_BY_IDS = API_PREFIX + "/get-institution-provider-channel-by-ids";
    /**
     * feign调用 通过课程等级ids 查找对应的课程等级名称map
     */
    String GET_MAJOR_LEVEL_NAMES_BY_IDS = API_PREFIX + "/get-major-level-names-by-ids";

    String GET_MAJOR_LEVEL_IDS_BY_IDS = API_PREFIX + "/get-major-level-ids-by-ids";
    /**
     * feign调用 通过课程等级ids 查找对应的课程等级名称map
     */
    String GET_MAJOR_LEVEL_NAME_CHNS_BY_IDS = API_PREFIX + "/get-major-level-name-chns-by-ids";

    /**
     * feign调用 通过课程等级ids 查找对应的课程等级名称map
     */
    String GET_MAJOR_LEVEL_BY_IDS = API_PREFIX + "/get-major-level-by-ids";
    /**
     * feign调用 通过课程等级id 查找对应的课程等级名称
     */
    String GET_MAJOR_LEVEL_NAMES_BY_ID = API_PREFIX + "/get-major-level-names-by-id";
    /**
     * feign调用 查询新闻标题Map
     */
    String GET_NEWS_TITLES_BY_IDS = API_PREFIX + "/get-news-titles-by-ids";
    /**
     * 获取符和学习计划的合同公式
     */
    String GET_CONTRACT_FORMULAS_BY_OFFER_ITEM = API_PREFIX + "/get-contract-formulas-by-offer-item";
    /**
     * 通过合同公式查询课程id
     */
    String GET_COURSE_IDS_BY_CONTRACT_FORMULA_ID = API_PREFIX + "/get-course-ids-by-contract-formula-id";
    /**
     * 查询国家下面的州省
     */
    String GET_BY_FK_AREA_COUNTRY_ID = API_PREFIX + "/get-by-fk-area-country-id";
    /**
     * 查询州省下面的城市
     */
    String GET_BY_FK_AREA_STATE_ID = API_PREFIX + "/get-by-fk-area-state-id";
    /**
     * 根据关键词查询国家列表数据
     */
    String GET_AREA_COUNTRYS = API_PREFIX + "/get-area-countrys";
    /**
     * 初步检查学习计划-合同公式匹配
     */
    String CHECK_CONTRACT_FORMULA = API_PREFIX + "/check-contract-formula";
    /**
     * feign调用 根据合同公式Id获取合同公式信息
     */
    String GET_CONTRACT_FORMULA_BY_FORMULA_ID = API_PREFIX + "/get-contract-formula-by-formula-id";
    /**
     * 通过城市id 查找对应的城市名称
     */
    String GET_CITY_FULL_NAME_BY_ID = API_PREFIX + "/get-city-full-name-by-id";
    /**
     * 根据国家id获取国家名和国家编号
     */
    String GET_COUNTRY_NAME_AND_NUM_BY_ID = API_PREFIX + "/get-country-name-and-num-by-id";

    String GET_MEDIA_AND_ATTACHED_DTOS = API_PREFIX + "/get-media-and-attached-dtos";
    /**
     * 根据国家keys获取国家列表
     */
    String GET_COUNTRY_BY_KEY = API_PREFIX + "/get-country-by-key";
    /**
     * 根据国家名称模糊查询国家id列表
     */
    String GET_COUNTRY_BY_NAME = API_PREFIX + "/get-country-by-name";
    /**
     * 根据id查找名称
     */
    String GET_CHANNEL_NAME = API_PREFIX + "/get-channel-name";
    /**
     * feign调用  更新所有课程的fee_cny费率
     */
    String UPDATE_COURSEFEE_CNY = API_PREFIX + "/update-coursefee-cny";
    /**
     * 获取学校类型名称
     */
    String GET_ALL_INSTITUTION_TYPE_NAME = API_PREFIX + "/get-all-institution-type-name";
    /**
     * feign调用 根据输入的学校提供商名称 模糊查询对应的学校提供商id
     */
    String GET_INSTITUTION_PROVIDER_IDS = API_PREFIX + "/get-institution-provider-ids";
    /**
     * feign调用 根据输入的学校提供商名称关键字 模糊提供商
     */
    String GET_INSTITUTION_PROVIDERS_BY_NAME = API_PREFIX + "get-institution-providers-by-name";
    /**
     * 获取提供商下拉名称
     */
    String GET_INSTITUTION_PROVIDER_SELECT_NAMES_BY_IDS = API_PREFIX + "/get-institution-provider-select-names-by-ids";
    /**
     * feign 根据学校校区ids获取校区名字
     */
    String GET_INSTITUTION_ZONE_NAMES_BY_IDS = API_PREFIX + "/get-institution-zone-names-by-ids";
    /**
     * 根据国家ids 获取国家编号
     *
     * @param countryIdIdSet
     * @return
     */
    String GET_COUNTRY_NUM_BY_COUNTRY_IDS = API_PREFIX + "/get-country-num-by-country-ids";
    /**
     * 课程对应的课程等级
     */
    String GET_MAJOR_LEVEL_NAMES_BY_COUR_IDS = API_PREFIX + "/get-major-level-names-by-cour-ids";
    /**
     * 根据公司id获取提供商ids
     */
    String GET_INSTITUTION_PROVIDER_IDS_BY_COMPANY_IDS = API_PREFIX + "/get-institution-provider-ids-by-company-ids";
    /**
     * 根据公司和名称搜索提供商ids
     */
    String GET_INSTITUTION_PROVIDER_IDS_BY_COMPANY_ID_AND_NAME = API_PREFIX + "/get-institution-provider-ids-by-company-id-and-name";
    /**
     * 根据公司id获取提供商ids
     */
    String GET_INSTITUTION_PROVIDER_IDS_BY_COMPANY_ID = API_PREFIX + "/get-institution-provider-ids-by-company-id";
    /**
     * 学校类型下拉框数据
     */
    String GET_MAJOR_LEVEL_ID_STRING_BY_COURSE_ID = API_PREFIX + "/get-major-level-id-string-by-course-id";
    /**
     * 学校类型下拉框数据
     */
    String GET_TYPE_ID_STRING_BY_COURSE_ID = API_PREFIX + "/get-type-id-string-by-course-id";
    /**
     * 渠道下拉框
     */
    String GET_INSTITUTION_PROVIDER_CHANNEL_SELECT = API_PREFIX + "/get-institution-provider-channel-select";
    /**
     * 通过名称返回渠道ids
     */
    String GET_INSTITUTION_PROVIDER_CHANNEL_IDS_BY_NAME = API_PREFIX + "/get-institution-provider-channel-ids-by-name";
    String FUZZ_SEARCH_INSTITUTION_CHANNEL = API_PREFIX + "/fuzz-search-institution-channel";
    /**
     * 根据国家ids查找国家名称map
     */
    String GET_COUNTRY_FULL_NAMES_BY_IDS = API_PREFIX + "/get-country-full-names-by-ids";
    /**
     * 根据学校id 获取对应的RPA地图学校id
     *
     * @Date 20:51 2022/4/23
     * <AUTHOR>
     */
    String GET_INSTITUTION_RPA_MAPPING_RELATION = API_PREFIX + "/get-institution-rpa-mapping-relation";

    /**
     * 通过学校权限获取学校信息
     */
   String GET_INSTITUTION_BY_INSTITUTION_PERMISSION_GROUP = API_PREFIX + "/get-institution-by-institution-permission-group";

    /**
     * 根据课程类型ids查询课程类型组别ids
     */
    String GET_COURSE_TYPE_GROUP_IDS_STRING_BY_COURSE_IDS = API_PREFIX + "/get-course-type-group-ids-string-by-course-ids";
    String GET_CITY_CHN_NAME_BY_ID = API_PREFIX + "/get-city-chn-name-by-id";
    String GET_COUNTRY_NAME_BY_NUMS = API_PREFIX + "/get-country-name-by-nums";
    String GET_ALL_AREA_REGION_CHN_NAMES = API_PREFIX + "/get-all-area-region-chn-names";

    String GET_CURRENCY_NUM_BY_COUNTRY_NAME = API_PREFIX + "/get-currency-num-by-country-name";

    String GET_EVENT_REGISTRATION_PROVIDER_BY_IDS = API_PREFIX + "/get-event-registration-provider-by-ids";

    String GET_EVENT_REGISTRATION_PROVIDERS = API_PREFIX + "/get-event-registration-providers";

    String GET_CASE_STUDY_RESULTS = API_PREFIX + "/get-case-study-results";

    /**
     * 获取匹配课程
     */
    String GET_INSTITUTION_COURSE_BY_NAME_MATCH = API_PREFIX + "/get-institution-course-by-name-match";

    String GET_COURSE_GROUP_TYPE_NAME_BY_IDS = API_PREFIX + "/get_course_group_type_name_by_ids";

    String GET_COURSE_GROUP_TYPE_NAME_CHN_BY_IDS = API_PREFIX + "/get-course-group-type-name-chn-by-ids";

    String GET_COURSE_GROUP_TYPE_FULL_NAME_CHN_BY_IDS = API_PREFIX + "get_course_group_type_full_name_by_ids";

    String GET_INSTITUTION_IDS_BY_KEYWORD = API_PREFIX + "get_institution_ids_by_keyword";
    /**
     * 获取学校列表
     */
    String GET_INSTITUTION_DTO_LIST = API_PREFIX + "get_institution_dto_list";
    /**
     * 获取匹配模糊查询学校名称或则学校名称的学校ids
     */
    String GET_LIKE_INSTITUTION_IDS = API_PREFIX + "get_like_institution_ids";

    String GET_AREA_CITY_NAME_BY_IDS = API_PREFIX + "get-area-city-name-by-ids";

    String GET_AREA_STATE_NAME_BY_IDS = API_PREFIX + "get-area-state-name-by-ids";

    String GET_INSTITUTION_SELECT_BY_COMPANY_ID = API_PREFIX + "/get-institution-select-by-company-id";

    String GET_INSTITUTION_PROVIDER_BY_TARGET_NAME = API_PREFIX + "/get-institution-provider-by-target-name";

    String GET_INSTITUTION_PROVIDER_ACCOUNT_LIST = API_PREFIX + "/get-institution-provider-account-list";

    String GET_INSTITUTION_PROVIDER_ACCOUNT_BY_ID = API_PREFIX + "/get-institution-provider-account-by-id";

    String GET_INSTITUTION_ADD_NEWS = API_PREFIX + "/get-institution-add-news";

    String GET_INSTITUTION_GET_NEWS_TYPES = API_PREFIX + "/get-institution-get-news-types";

    String GET_ALL_COUNTRY_LIST = API_PREFIX + "/get-all-country-list";

    String GET_INSTITUTION_CHANNEL_PROVIDER_NAMES_BY_IDS = API_PREFIX + "/get-institution-channel-provider-names-by-ids";

    String GET_ADD_NEWS_MEDIA = API_PREFIX + "/get-add-news-media";

    String GET_CHECK_NEWS = API_PREFIX + "/get-check-news";

    String GET_COUNTRY_BY_PUBLIC_LEVEL = API_PREFIX + "/get-country-by-public-level";

    String GET_INSTITUTION_PROVIDER_BY_INSTITUTION = API_PREFIX + "/get-institution-provider-by-institution";

    String GET_CONTRACT_NEW_BY_PROVIDER_ID = API_PREFIX + "/get-contract-new-by-provider-id";

    String GET_CONTRACT_EXPIRED_BY_CONTRACT_ID = API_PREFIX + "/get-contract-expired-by-contract-id";

    String GET_COUNTRY_DTO_MAP_BY_IDS = API_PREFIX + "/get-country-dto-map-by-ids";

    String GET_INSTITUTION_PROVIDER_MAP_BY_IDS = API_PREFIX + "/get-institution-provider-map-by-ids";

    String GET_INSTITUTION_DTO_MAP_BY_IDS = API_PREFIX + "/get-institution-dto-map-by-ids";

    String GET_INSTITUTION_DTO_BY_IDS = API_PREFIX + "/get-institution-dto-by-ids";

    String GET_REGION_MAP_BY_STATE_IDS = API_PREFIX + "/get-region-map-by-state-ids";

    String GET_AREA_REGION_NAME_BY_IDS = API_PREFIX + "/get-area-region-name-by-ids";

    String FIND_AREA_REGION_BY_ID = API_PREFIX + "/find-area-region-by-id";

    String GET_INSTITUTION_BY_PROVIDER = API_PREFIX + "/get-institution-by-provider";

    /**
     * 根据学校提供商id获取提供商
     *
     * @Date 14:57 2023/9/26
     * <AUTHOR>
     */
    String GET_INSTITUTION_PROVIDER_BY_ID = API_PREFIX + "/get-institution-provider-by-id";

    String GET_INSTITUTION_BY_COUNTRY_IDS = API_PREFIX + "/get-institution-by-country-ids";

    String BATCH_TRANSLATION_AND_COURSE = API_PREFIX + "/batch_Translation_institution_and_course";

    /**
     * 根据课程ids获取英文课程名map
     */
    String GET_COURSE_EN_NAME_BY_IDS = API_PREFIX + "/get_course_en_name_by_ids";

    /**
     *
     */
    String GET_MPS_INSTITUTION_PROVIDER_LIST = API_PREFIX + "get_mps_institution_provider_list";

    String GET_COUNTRY_ID_BY_KEYS = API_PREFIX + "get_country_id_by_keys";

    String GET_NEWS_EMAIL_TMPLETE = API_PREFIX + "/get_news_email_tmplete";

    String GET_APP_FEES = API_PREFIX + "/get_app_fees";

    String GET_RANKING_YEAR = API_PREFIX+"get_ranking_year";

    String UPDATE_CONTRACT_STATUS = API_PREFIX+"update_contract_status";

    String GET_CONTRACT_APPLY_COUNTRY_BY_CONTRACT_ID = API_PREFIX+"get_contract_apply_country_by_contract_id";

    String GET_COUNTRY_BY_PUBLIC_LEVEL_BY_SUMMIT = API_PREFIX+"get_country_by_public_level_by_summit";

    @PostMapping (UPDATE_CONTRACT_STATUS)
    Integer updateContractStatus(@RequestParam("id") Long id, @RequestParam("status") Integer status);


    @GetMapping(GET_RANKING_YEAR)
    Result<Integer> getRankingMaxYear();


    /**
     * Feign 获取官网链接
     *
     * @param tableName
     * @param typeKey
     * @return
     */
    @PostMapping(GET_WEBSITE_BY_TABLE)
    Result<Map<Long, String>> getWebSiteByTable(@RequestParam("tableName") String tableName, @RequestParam("typeKey") String typeKey);

    /**
     * 获取官网链接  通过对象传递
     *
     * @param appInfoFeignVo
     * @return
     */
    @PostMapping(GET_WEB_SITE_BY_APP_INFO_FEIGN_DTO)
    Result<Map<Long, String>> getWebSiteByAppInfoFeignDto(@RequestBody AppInfoFeignVo appInfoFeignVo);

    /**
     * 通过城市id 查找对应的城市名称
     *
     * @param id
     * @return
     */
    @GetMapping(GET_CITY_NAME_BY_ID)
    Result<String> getCityNameById(@RequestParam(required = false) Long id);

    /**
     * feign调用 通过城市ids 查找对应的城市名称map
     *
     * @param ids
     * @return
     */
    @PostMapping(GET_CITY_NAMES_BY_IDS)
    Result<Map<Long, String>> getCityNamesByIds(@RequestBody Set<Long> ids);

    /**
     * feign调用 通过城市ids 查找对应的城市中文名称map
     *
     * @param ids
     * @return
     */
    @PostMapping(GET_CITY_NAME_CHNS_BY_IDS)
    Result<Map<Long, String>> getCityNameChnsByIds(@RequestBody Set<Long> ids);

    /**
     * 通过城市ids 查找对应的城市名称map（拼接name_cn）
     *
     * @param ids
     * @return
     */
    @PostMapping(GET_CITY_FULL_NAMES_BY_IDS)
    Result<Map<Long, String>> getCityFullNamesByIds(@RequestBody Set<Long> ids);

    /**
     * 查找城市名称map（拼接name_cn）
     * @return
     */
    @GetMapping(GET_CITY_FULL_NAMES)
    Result<Map<Long, String>> getCityFullNames();

    /**
     * 通过城市区域ids 查找对应的城市区域名称map（拼接streetsName）
     *
     * @param ids
     * @return
     */
    @PostMapping(GET_CITY_DIVISION_FULL_NAMES_BY_IDS)
    Result<Map<Long, String>> getCityDivisionFullNamesByIds(@RequestBody Set<Long> ids);

    /**
     * feign 调用 根据国家编号查找国家名称
     *
     * @param countryKey
     * @return
     */
    @GetMapping(GET_COUNTRY_NAME)
    Result<String> getCountryName(@RequestParam(required = false) String countryKey);

    /**
     * 获取英文名称
     * @param countryKey
     * @return
     */
    @GetMapping(GET_COUNTRY_NAME_EN)
    Result<String> getCountryNameEn(@RequestParam(required = false) String countryKey);


    /**
     * feign  获取所有国家id
     *
     * @return
     */
    @GetMapping(GET_ALL_COUNTRY_ID)
    Result<Set<Long>> getAllCountryId();

    /**
     * 根据国家id查找国家名称
     *
     * @param id
     * @return
     */
    @GetMapping(GET_COUNTRY_NAME_BY_ID)
    Result<String> getCountryNameById(@RequestParam(required = false) Long id);

    /**
     * 根据国家id查找国家名称
     *
     * @param id
     * @return
     */
    @GetMapping(GET_COUNTRY_BY_ID)
    Result<AreaCountry> getCountryById(@RequestParam(required = false) Long id);

    /**
     * 根据国家id查找国家名称
     *
     * @param id
     * @return
     */
    @GetMapping(GET_COUNTRY_CHN_NAME_BY_ID)
    Result<String> getCountryChnNameById(@RequestParam(required = false) Long id);

    /**
     * feign 调用 通过国家ids 查找对应的国家名称
     *
     * @param ids
     * @return
     */
    @PostMapping(GET_COUNTRY_CHN_NAME_BY_IDS)
    Result<Map<Long, String>> getCountryChnNameByIds(@RequestBody Set<Long> ids);

    /**
     * feign调用 根据key查找id
     *
     * @param keys
     * @return
     */
    @PostMapping(GET_COUNTRY_ID_BY_KEY)
    @VerifyLogin(IsVerify = false)
    Result<List<Long>> getCountryIdByKey(@RequestBody List<String> keys);

    /**
     * 根据国家ids查找国家名称map
     *
     * @param ids
     * @return
     */
    @PostMapping(GET_COUNTRY_NAMES_BY_IDS)
    @VerifyLogin(IsVerify = false)
    Result<Map<Long, String>> getCountryNamesByIds(@RequestBody Set<Long> ids);

    @PostMapping(GET_COUNTRY_NAME_MAP)
    @VerifyLogin(IsVerify = false)
    Result<Map<Long, String>> getCountryNameMap();

    /**
     * 根据国家id查找国家key
     *
     * @param id
     * @return
     */
    @GetMapping(GET_COUNTRY_KEY_BY_ID)
    Result<String> getCountryKeyById(@RequestParam(value = "id") Long id);

    /**
     * 根据州省IDs获取国家IDs
     *
     * @param fkAreaStateIds
     * @return
     */
    @PostMapping(GET_COUNTRY_IDS_BY_STATE_IDS)
    Result<Map<Long, Long>> getCountryIdsByStateIds(@RequestParam(value = "fkAreaStateIds") Set<Long> fkAreaStateIds);

    /**
     * feign调用,根据ids获取对象集合
     *
     * @param ids
     * @return
     */
    @GetMapping(GET_AREA_REGION_DTO_BY_IDS)
    Result<Map<Long, AreaRegionVo>> getAreaRegionDtoByIds(@RequestParam("ids") Set<Long> ids);

    /**
     * 通过州省id 查找对应的州省名称
     *
     * @param id
     * @return
     */
    @GetMapping(GET_STATE_NAME_BY_ID)
    Result<String> getStateNameById(@RequestParam(required = false) Long id);

    /**
     * 通过州省id 查找对应的州省名称
     *
     * @param id
     * @return
     */
    @GetMapping(GET_STATE_FULL_NAME_BY_ID)
    Result<String> getStateFullNameById(@RequestParam(required = false) Long id);

    /**
     * feign调用 通过州省ids 查找对应的州省名称map
     *
     * @param ids
     * @return
     */
    @PostMapping(GET_STATE_NAMES_BY_IDS)
    Result<Map<Long, String>> getStateNamesByIds(@RequestBody Set<Long> ids);

    /**
     * 通过州省ids 查找对应的州省名称map（拼接name_cn）
     *
     * @param ids
     * @return
     */
    @PostMapping(GET_STATE_FULL_NAMES_BY_IDS)
    Result<Map<Long, String>> getStateFullNamesByIds(@RequestBody Set<Long> ids);

    /**
     * 查找州省名称map（拼接name_cn）
     *
     * @param
     * @return
     */
    @GetMapping(GET_STATE_FULL_NAMES)
    Result<Map<Long, String>> getStateFullNames();

    /**
     * feign调用，根据id查询表
     *
     * @param id
     * @return
     */
    @GetMapping(GET_INSTITUTION_CONTRACT_BY_ID)
    Result<ContractVo> getInstitutionContractById(@RequestParam("id") Long id);

    /**
     * 修改m_contract数据
     *
     * @param contract
     * @return
     */
    @PostMapping(UPDATE_CHANGE_STATUS)
    Result<Boolean> updateChangeStatus(@RequestBody Contract contract);

    @PostMapping(CHANGE_STATUS)
    Result<Boolean> changeStatus(@RequestParam("status") Integer status, @RequestParam("tableName") String tableName, @RequestParam("businessKey") Long businessKey);

    /**
     * feign调用 通过合同公式ids 查找对应的合同公式map
     *
     * @param ids
     * @return
     */
    @PostMapping(GET_CONTRACT_FORMULAS_BY_IDS)
    Result<Map<Long, String>> getContractFormulasByIds(@RequestBody Set<Long> ids);

    /**
     * feign调用 通过合同公式查询合同公式佣金配置
     *
     * @param id
     * @return
     */
    @PostMapping(GET_CONTRACT_FORMULA_COMMISSION_BY_CONTRACT_FORMULA_ID)
    Result<List<ContractFormulaCommissionVo>> getContractFormulaCommissionByContractFormulaId(@RequestParam(value = "id") Long id);

    /**
     * feign调用 通过合同公式查询前置学校
     *
     * @param id
     * @return
     */
    @GetMapping(GET_CONTRACT_FORMULA_PRE_INSTITUTION_BY_CONTRACT_FORMULA_ID)
    Result<List<Long>> getContractFormulaPreInstitutionByContractFormulaId(@RequestParam Long id);

    /**
     * feign调用 通过合同公式查询前置集团
     *
     * @param id
     * @return
     */
    @GetMapping(GET_PRE_INSTITUTION_GROUP_BY_CONTRACT_FORMULA_ID)
    Result<List<Long>> getPreInstitutionGroupByContractFormulaId(@RequestParam Long id);

    /**
     * feign调用 通过合同公式查询前置课程等级
     *
     * @param id
     * @return
     */
    @GetMapping(GET_PRE_MAJOR_LEVEL_BY_CONTRACT_FORMULA_ID)
    Result<List<Long>> getPreMajorLevelByContractFormulaId(@RequestParam Long id);

    /**
     * feign调用 通过合同公式id查询合同公式  学校课程信息
     *
     * @param formulaId
     * @return
     */
    @GetMapping(GET_CONTRACT_FORMULA_CONFIG_BY_CONTRACT_FORMULA_ID)
    Result<ContractFormulaFeignVo> getContractFormulaConfigByContractFormulaId(@RequestParam Long formulaId);

    /**
     * 通过课程类型ids 查找对应的课程类型名称map
     *
     * @param ids
     * @return
     */
    @PostMapping(GET_COURSE_TYPE_NAMES_BY_IDS)
    Result<Map<Long, String>> getCourseTypeNamesByIds(@RequestBody Set<Long> ids);

    /**
     * 通过课程类型组ids 查找对应的子课程类型名称map
     *
     * @param ids
     * @return
     */
    @PostMapping(GET_COURSE_TYPE_NAMES_BY_COURSE_GROUP_TYPE_IDS)
    Result<Map<Long, String>> getCourseTypeNamesByCourseGroupTypeIds(@RequestBody Set<Long> ids);

    /**
     * 通过课程ids 查找对应的课程类型名称map
     *
     * @param ids
     * @return
     */
    @PostMapping(GET_COURSE_TYPE_NAMES_BY_COURSE_IDS)
    Result<Map<Long, String>> getCourseTypeNamesByCourseIds(@RequestBody Set<Long> ids);

    /**
     * 获取渠道名称
     *
     * @param ids
     * @return
     */
    @GetMapping(GET_CHANNEL_BY_IDS)
    Result<Map<Long, String>> getChannelByIds(@RequestParam(required = false) Set<Long> ids);

    /**
     * feign调用 根据输入的学校名称 模糊查询对应的学校ids
     *
     * @param institutionName
     * @return
     */
    @GetMapping(GET_INSTITUTION_IDS)
    Result<List<Long>> getInstitutionIds(@RequestParam String institutionName);

    /**
     * feign调用 根据学校id查找学校名称
     *
     * @param id
     * @return
     */
    @GetMapping(GET_INSTITUTION_NAME)
    Result<String> getInstitutionName(@RequestParam(required = false) Long id);

    /**
     * feign调用 根据学校id查找学校名称
     *
     * @param id
     * @return
     */
    @GetMapping(GET_INSTITUTION_BY_ID)
    Result<Institution> getInstitutionById(@RequestParam(required = false) Long id);

    /**
     * 根据学校ids 查找对应名称map
     *
     * @param institutionIdSet
     * @return
     */
    @PostMapping(GET_INSTITUTION_NAMES_BY_IDS)
    Result<Map<Long, String>> getInstitutionNamesByIds(@RequestBody(required = false) Set<Long> institutionIdSet);

    /**
     * feign调用 根据学校id查找学校名称
     *
     * @param id
     * @return
     */
    @PostMapping(GET_INSTITUTION_NAMES_BY_ID)
    Result<String> getInstitutionNamesById(@RequestParam("id") Long id);

    /**
     * feign调用 根据学校ids查找国家名字
     *
     * @param institutionIds
     * @return
     */
    @PostMapping(GET_COUNTRY_NAMES_BY_INSTITUTION_IDS)
    Result<Map<Long, String>> getCountryNamesByInstitutionIds(@RequestBody Set<Long> institutionIds);

    /**
     * feign调用 根据学校id查找学校国家
     *
     * @param institutionIdSet
     * @return
     */
    @PostMapping(GET_COUNTRY_ID_BY_INSTITUTION_ID)
    Result<Map<Long, Long>> getCountryIdByInstitutionId(@RequestBody Set<Long> institutionIdSet);

    /**
     * 根据课程名称模糊查询
     *
     * @param name
     * @return
     */
    @GetMapping(GET_COURSE_IDS)
    Result<List<Long>> getCourseIds(@RequestParam(value = "name") String name);

    /**
     * 根据id获取课程名称
     *
     * @param id
     * @return
     */
    @GetMapping(GET_COURSE_NAME_BY_ID)
    Result<String> getCourseNameById(@RequestParam(value = "id") Long id);

    /**
     * 根据ids获取课程名称
     *
     * @param ids
     * @return
     */
    @PostMapping(GET_COURSE_NAME_BY_IDS)
    Result<Map<Long, String>> getCourseNameByIds(@RequestBody Set<Long> ids);//


    /**
     * feign调用 通过自定义课程ids 查找对应的自定义课程名称
     *
     * @param ids
     * @return
     */
    @GetMapping(GET_COURSE_NAME_BY_CUSTOM_IDS)
    Result<Map<Long, String>> getCourseNameByCustomIds(@RequestParam(value = "ids") Set<Long> ids);

    /**
     * 根据id获取课程名称
     *
     * @param id
     * @return
     */
    @GetMapping(GET_COURSE_NAME_CHN_BY_ID)
    Result<String> getCourseNameChnById(@RequestParam(value = "id") Long id);

    /**
     * 根据id获取课程名称
     *
     * @param id
     * @return
     */
    @GetMapping(GET_COURSE_BY_ID)
    Result<InstitutionCourseVo> getCourseById(@RequestParam(value = "id") Long id);

    /**
     * 根据ids获取课程名称
     *
     * @param ids
     * @return
     */
    @PostMapping(GET_COURSE_NAME_CHN_BY_IDS)
    Result<Map<Long, String>> getCourseNameChnByIds(@RequestBody Set<Long> ids);

    /**
     * 根据课程id查詢學費
     *
     * @param id
     * @return
     */
    @GetMapping(GET_FEE_BY_ID)
    Result<BigDecimal> getFeeById(@RequestParam(value = "id") Long id);

    /**
     * 根据课程ids查詢學費
     *
     * @param ids
     * @return
     */
    @PostMapping(GET_SUM_FEE_BY_IDS)
    Result<BigDecimal> getSumFeeByIds(@RequestBody List<Long> ids);

    /**
     * feign调用 根据课程ids 获取课程名
     *
     * @param institutionCourseIdSet
     * @return
     */
    @PostMapping(GET_INSTITUTION_COURSE_NAMES_BY_IDS)
    Result<Map<Long, String>> getInstitutionCourseNamesByIds(@RequestBody Set<Long> institutionCourseIdSet);

    /**
     * 根据id获取课程名称
     *
     * @param id
     * @return
     */
    @GetMapping(GET_INSTITUTION_COURSE_NAME_BY_ID)
    Result<String> getInstitutionCourseNameById(@RequestParam(value = "id") Long id);

    /**
     * 根据课程ids 查找对应课程名称map
     *
     * @param institutionGroupIdSet
     * @return
     */
    @PostMapping(GET_INSTITUTION_GROUP_NAMES_BY_IDS)
    Result<Map<Long, String>> getInstitutionGroupNamesByIds(@RequestBody Set<Long> institutionGroupIdSet);

    /**
     * feign调用 根据输入的学校提供商名称 模糊查询对应的学校提供商id
     *
     * @param institutionProviderName
     * @return
     */
    @GetMapping(GET_INSTITUTION_PROVIDER_IDS_BY_NAME)
    Result<List<Long>> getInstitutionProviderIdsByName(@RequestParam String institutionProviderName);

    /**
     * feign调用 根据输入的学校提供商id 查询对应的学校提供商名称
     *
     * @param id
     * @return
     */
    @GetMapping(GET_INSTITUTION_PROVIDER_NAME)
    Result<String> getInstitutionProviderName(@RequestParam(required = false) Long id);

    /**
     * fegin调用 根据学校提供商ids 查询名称map
     *
     * @param ids
     * @return
     */
    @PostMapping(GET_INSTITUTION_PROVIDER_NAMES_BY_IDS)
    Result<Map<Long, String>> getInstitutionProviderNamesByIds(@RequestBody Set<Long> ids);

    /**
     * feign调用 根据学校提供商id查找学校提供商类型
     *
     * @param id
     * @return
     */
    @GetMapping(GET_INSTITUTION_PROVIDER_TYPE)
    Result<String> getInstitutionProviderType(@RequestParam(value = "id") Long id);

    /**
     * 学校提供商下拉框数据（feign）
     *
     * @param companyId
     * @return
     */
    @GetMapping(GET_INSTITUTION_PROVIDER_SELECT)
    Result<List<BaseSelectEntity>> getInstitutionProviderSelect(@RequestParam(value = "companyId") Long companyId);

    /**
     * 根据提供商ids查询公司
     *
     * @param providerIds
     * @return
     */
    @GetMapping(GET_COMPANY_IDS_BY_PROVIDER_IDS)
    Result<Map<Long, LinkedList<Long>>> getCompanyIdsByProviderIds(@RequestParam("providerIds") Set<Long> providerIds);


    /**
     * 获取提供商公司名称
     *
     * @param id
     * @return
     */
    @GetMapping(GET_PROVIDER_COMPANY_NAME)
    Result<List<CompanyVo>> getProviderCompanyName(@RequestParam("id") Long id);

    /**
     * feign调用 获取所有的渠道Map
     *
     * @return
     */
    @GetMapping(GET_INSTITUTION_PROVIDER_CHANNEL)
    Result<Map<Long, String>> getInstitutionProviderChannel();

    /**
     * feign调用 获取渠道名By id
     *
     * @param id
     * @return
     */
    @GetMapping(GET_INSTITUTION_PROVIDER_CHANNEL_BY_ID)
    Result<String> getInstitutionProviderChannelById(@RequestParam(value = "id") Long id);

    @PostMapping(GET_INSTITUTION_PROVIDER_CHANNEL_BY_IDS)
    Result<Map<Long, String>> getInstitutionProviderChannelByIds(@RequestBody Set<Long> ids);

    /**
     * feign调用 通过课程等级ids 查找对应的课程等级名称map
     *
     * @param ids
     * @return
     */
    @PostMapping(GET_MAJOR_LEVEL_NAMES_BY_IDS)
    Result<Map<Long, String>> getMajorLevelNamesByIds(@RequestBody Set<Long> ids);

    /**
     * feign调用 通过课程等级ids 查找对应的课程等级ids
     *
     * @param ids
     * @return
     */
    @PostMapping(GET_MAJOR_LEVEL_IDS_BY_IDS)
    Result<Map<Long, Set<Long>>> getMajorLevelIdsByIds(@RequestBody Set<Long> ids);

    /**
     * feign调用 通过课程等级ids 查找对应的课程等级中文名称map
     *
     * @param ids
     * @return
     */
    @PostMapping(GET_MAJOR_LEVEL_NAME_CHNS_BY_IDS)
    Result<Map<Long, String>> getMajorLevelNameChnsByIds(@RequestBody Set<Long> ids);

    @PostMapping(GET_MAJOR_LEVEL_BY_IDS)
    Result<Map<Long, MajorLevel>> getMajorLevelByIds(@RequestBody Set<Long> ids);

    /**
     * feign调用 通过课程等级id 查找对应的课程等级名称
     *
     * @param id
     * @return
     */
    @GetMapping(GET_MAJOR_LEVEL_NAMES_BY_ID)
    Result<String> getMajorLevelNamesById(@RequestParam("id") Long id);

    /**
     * feign调用 查询新闻标题Map
     *
     * @param ids
     * @return
     */
    @PostMapping(GET_NEWS_TITLES_BY_IDS)
    Result<Map<Long, String>> getNewsTitlesByIds(@RequestBody(required = false) Set<Long> ids);

    /**
     * 获取符和学习计划的合同公式
     *
     * @param companyId
     * @param areaCountryId
     * @param studentOfferItem
     * @return
     */
    @PostMapping(GET_CONTRACT_FORMULAS_BY_OFFER_ITEM)
    Result<List<ContractFormula>> getContractFormulasByOfferItem(@RequestParam(value = "companyId") Long companyId, @RequestParam(value = "areaCountryId") Long areaCountryId, @RequestBody InstitutionStudentOfferItemDto studentOfferItem);

    /**
     * 通过合同公式查询课程id
     *
     * @param id
     * @return
     */
    @PostMapping(GET_COURSE_IDS_BY_CONTRACT_FORMULA_ID)
    Result<List<Long>> getCourseIdsByContractFormulaId(@RequestParam(value = "id") Long id);

    /**
     * 查询国家下面的州省
     *
     * @param fkAreaCountryId
     * @return
     */
    @GetMapping(GET_BY_FK_AREA_COUNTRY_ID)
    @VerifyLogin(IsVerify = false)
    Result<List<AreaStateVo>> getByFkAreaCountryId(@RequestParam(value = "fkAreaCountryId", required = false) Long fkAreaCountryId);

    /**
     * 查询州省下面的城市
     *
     * @param id
     * @return
     */
    @GetMapping(GET_BY_FK_AREA_STATE_ID)
    Result<List<AreaCityVo>> getByFkAreaStateId(@RequestParam(value = "id") Long id);

    /**
     * 根据关键词查询国家列表数据
     *
     * @param keyWord
     * @return
     */
    @PostMapping(GET_AREA_COUNTRYS)
    Result<List<AreaCountryVo>> getAreaCountrys(@RequestParam(required = false) String keyWord);

    /**
     * 初步检查学习计划-合同公式匹配
     *
     * @param studentCompanyId
     * @param areaCountryId
     * @param fkInstitutionCourseId
     * @param contractFormulaId
     * @return
     */
    @PostMapping(CHECK_CONTRACT_FORMULA)
    Result<String> checkContractFormula(@RequestParam("studentCompanyId") Long studentCompanyId, @RequestParam(value = "areaCountryId") Long areaCountryId,
                                        @RequestParam(value = "fkInstitutionCourseId") Long fkInstitutionCourseId, @RequestParam("contractFormulaId") Long contractFormulaId);

    /**
     * feign调用 根据合同公式Id获取合同公式信息
     *
     * @param formulaId
     * @return
     */
    @GetMapping(GET_CONTRACT_FORMULA_BY_FORMULA_ID)
    Result<ContractFormula> getContractFormulaByFormulaId(@RequestParam(value = "formulaId") Long formulaId);

    /**
     * 通过城市id 查找对应的城市名称
     *
     * @param id
     * @return
     */
    @GetMapping(GET_CITY_FULL_NAME_BY_ID)
    Result<String> getCityFullNameById(@RequestParam("id") Long id);

    /**
     * 根据国家id获取国家名和国家编号
     *
     * @param id
     * @return
     */
    @GetMapping(GET_COUNTRY_NAME_AND_NUM_BY_ID)
    Result<String> getCountryNameAndNumById(@RequestParam("id") Long id);

    /**
     * 获取文件
     *
     * @param queryVo
     */
    @PostMapping(GET_MEDIA_AND_ATTACHED_DTOS)
    Map<Long, MediaAndAttachedVo> getMediaAndAttachedDtos(@RequestBody MediaAndAttachedQueryDto queryVo);

    /**
     * 根据国家keys获取国家列表
     *
     * @param keys
     * @return
     */
    @PostMapping(GET_COUNTRY_BY_KEY)
    Result<List<AreaCountryVo>> getCountryByKey(@RequestBody List<String> keys);

    /**
     * 根据国家名称模糊
     *
     * <AUTHOR>
     * @DateTime 2022/11/24 14:30
     */
    @PostMapping(GET_COUNTRY_BY_NAME)
    Result<List<Long>> getCountryByName(@RequestParam("name") String name);

    /**
     * 根据id查找名称
     *
     * @param id
     * @return
     */
    @GetMapping(GET_CHANNEL_NAME)
    Result<String> getChannelName(@RequestParam(required = false) Long id);

    /**
     * feign调用  更新所有课程的fee_cny费率
     *
     * @param updateType
     * @return
     */
    @GetMapping(UPDATE_COURSEFEE_CNY)
    Result<Boolean> updateCoursefeeCny(@RequestParam(required = false) String updateType);

    /**
     * 获取学校类型名称
     *
     * @return
     */
    @GetMapping(GET_ALL_INSTITUTION_TYPE_NAME)
    Map<Long, String> getAllInstitutionTypeName();

    /**
     * feign调用 根据输入的学校提供商名称 模糊查询对应的学校提供商id
     *
     * @param institutionProviderName
     * @return
     */
    @GetMapping(GET_INSTITUTION_PROVIDER_IDS)
    List<Long> getInstitutionProviderIds(@RequestParam String institutionProviderName);

    /**
     * feign调用 根据输入的提供商名称关键字，模糊查询提供商
     */
    @GetMapping(GET_INSTITUTION_PROVIDERS_BY_NAME)
    List<InstitutionProviderVo> getInstitutionProvidersByName(@RequestParam("keyword") String keyword);

    /**
     * 获取提供商下拉名称
     *
     * @param ids
     * @return
     */
    @PostMapping(GET_INSTITUTION_PROVIDER_SELECT_NAMES_BY_IDS)
    Map<Long, String> getInstitutionProviderSelectNamesByIds(@RequestBody Set<Long> ids);

    /**
     * feign 根据学校校区ids获取校区名字
     *
     * @param institutionZoneIdSet
     * @return
     */
    @PostMapping(GET_INSTITUTION_ZONE_NAMES_BY_IDS)
    Result<Map<Long, String>> getInstitutionZoneNamesByIds(@RequestBody Set<Long> institutionZoneIdSet);

    /**
     * 根据国家ids 获取国家编号
     *
     * @param countryIdIdSet
     * @return
     */
    @PostMapping(GET_COUNTRY_NUM_BY_COUNTRY_IDS)
    Result<Map<Long, String>> getCountryNumByCountryIds(@RequestBody Set<Long> countryIdIdSet);

    /**
     * 课程对应的课程等级
     *
     * @param ids
     * @return
     */
    @PostMapping(GET_MAJOR_LEVEL_NAMES_BY_COUR_IDS)
    Map<Long, String> getMajorLevelNamesByCourIds(@RequestBody Set<Long> ids);

    /**
     * 根据公司id获取提供商ids
     *
     * @param fkCompanyIds
     * @return
     */
    @PostMapping(GET_INSTITUTION_PROVIDER_IDS_BY_COMPANY_IDS)
    List<Long> getInstitutionProviderIdsByCompanyIds(@RequestBody List<Long> fkCompanyIds);

    /**
     * 根据公司和名称搜索提供商ids
     *
     * @param companyIds
     * @param institutionProviderName
     * @return
     */
    @PostMapping(GET_INSTITUTION_PROVIDER_IDS_BY_COMPANY_ID_AND_NAME)
    List<Long> getInstitutionProviderIdsByCompanyIdAndName(@RequestBody List<Long> companyIds, @RequestParam("institutionProviderName") String institutionProviderName);

    /**
     * 根据公司id获取提供商ids
     *
     * @param companyId
     * @return
     */
    @GetMapping(GET_INSTITUTION_PROVIDER_IDS_BY_COMPANY_ID)
    List<Long> getInstitutionProviderIdsByCompanyId(@RequestParam("companyId") Long companyId);

    /**
     * 学校类型下拉框数据
     *
     * @param id
     * @return
     */
    @GetMapping(GET_MAJOR_LEVEL_ID_STRING_BY_COURSE_ID)
    String getMajorLevelIdStringByCourseId(@RequestParam("id") Long id);

    /**
     * 学校类型下拉框数据
     *
     * @param id
     * @return
     */
    @GetMapping(GET_TYPE_ID_STRING_BY_COURSE_ID)
    public String getTypeIdStringByCourseId(@RequestParam Long id);

    /**
     * 渠道下拉框
     *
     * @return
     */
    @GetMapping(GET_INSTITUTION_PROVIDER_CHANNEL_SELECT)
    Result<List<BaseSelectEntity>> getInstitutionProviderChannelSelect();

    /**
     * 通过名称返回渠道ids
     *
     * @param channelName
     * @return
     */
    @GetMapping(GET_INSTITUTION_PROVIDER_CHANNEL_IDS_BY_NAME)
    Result<List<Long>> getInstitutionProviderChannelIdsByName(@RequestParam("channelName") String channelName);

    /**
     * 根据学校id 获取对应的RPA地图学校id
     *
     * @Date 20:50 2022/4/23
     * <AUTHOR>
     */
    @PostMapping(GET_INSTITUTION_RPA_MAPPING_RELATION)
    Map<Long, Long> getInstitutionRpaMappingRelation(@RequestBody Set<Long> institutionIds);

    @PostMapping(FUZZ_SEARCH_INSTITUTION_CHANNEL)
    Result<List<BaseSelectEntity>> fuzzSearchInstitutionChannel(@RequestParam("keyword") String keyword, @RequestBody List<Long> companyIds);

    /**
     * 根据国家ids查找国家名称map
     *
     * @return
     */
    @PostMapping(GET_COUNTRY_FULL_NAMES_BY_IDS)
    @VerifyLogin(IsVerify = false)
    Result<Map<Long, String>> getCountryFullNamesByIds(@RequestBody Set<Long> ids);

    /**
     * 根据课程类型ids查询课程类型组别ids
     *
     * @param fkInstitutionCourseTypeIds
     * @return
     */
    @GetMapping(GET_COURSE_TYPE_GROUP_IDS_STRING_BY_COURSE_IDS)
    Result<String> getCourseTypeGroupIdsStringByCourseId(@RequestParam String fkInstitutionCourseTypeIds);

    /**
     * 根据国家id查找国家名称
     *
     * @param id
     * @return
     */
    @GetMapping(GET_CITY_CHN_NAME_BY_ID)
    Result<String> getCityChnNameById(@RequestParam(required = false) Long id);

    @PostMapping(GET_COUNTRY_NAME_BY_NUMS)
    Result<Map<String, String>> getCountryNameByNums(@RequestBody Set<String> cNums);

    @PostMapping(GET_ALL_AREA_REGION_CHN_NAMES)
    Result<Map<Long, String>> getAllAreaRegionChnNames();

    @GetMapping(GET_CURRENCY_NUM_BY_COUNTRY_NAME)
    Result<String> getCurrencyNumByCountryName(@RequestParam("countryName") String countryName);

    @PostMapping(GET_EVENT_REGISTRATION_PROVIDER_BY_IDS)
    Result<List<InstitutionProviderVo>> getEventRegistrationProviderByIds(@RequestBody List<Long> providerIds);

    @PostMapping(GET_EVENT_REGISTRATION_PROVIDERS)
    Result<EventRegistrationListResultVo> getEventRegistrationProviders(@RequestBody SearchBean<InstitutionProviderDto> page);

    @PostMapping(GET_CASE_STUDY_RESULTS)
    Result<CaseStudyResultsDto> getCaseStudyResults(@RequestBody CaseStudyQueryDto queryVo);


    /**
     * 获取匹配课程
     *
     * @param courseName
     * @param institutionId
     * @return
     */
    @PostMapping(GET_INSTITUTION_COURSE_BY_NAME_MATCH)
    Result<List<InstitutionCourseMatchVo>> getInstitutionCourseByNameMatch(@RequestParam(value = "courseName") String courseName,
                                                                           @RequestParam(value = "institutionId") Long institutionId);

    @PostMapping(GET_COURSE_GROUP_TYPE_NAME_BY_IDS)
    Result<Map<Long, String>> getCourseGroupTypeNameByIds(@RequestBody(required = false) Set<Long> ids);

    @PostMapping(GET_COURSE_GROUP_TYPE_NAME_CHN_BY_IDS)
    Result<Map<Long, String>> getCourseGroupTypeNameChnByIds(@RequestBody(required = false) Set<Long> ids);

    @PostMapping(GET_COURSE_GROUP_TYPE_FULL_NAME_CHN_BY_IDS)
    Result<Map<Long, String>> getCourseGroupTypeFullNameByIds(@RequestBody(required = false) Set<Long> ids);

    /**
     * 获取匹配模糊查询学校名称或则学校名称的学校ids
     */
    @PostMapping(GET_LIKE_INSTITUTION_IDS)
    Result<Set<Long>> getLikeInstitutionIds(@RequestBody InstitutionApplicationStaticsDto institutionApplicationStaticsDto);

    /**
     * 模糊查询获取学校IDs列表
     */
    @PostMapping(GET_INSTITUTION_IDS_BY_KEYWORD)
    Result<List<Long>> getInstitutionIdsByKeyword(@RequestParam("keyword") String keyword);

    /**
     * 获取学校列表
     */
    @PostMapping(GET_INSTITUTION_DTO_LIST)
    Result<List<InstitutionApplicationStatisticsVo>> getInstitutionDtoList(@RequestBody InstitutionApplicationStaticsQueryBo staticsQueryBo);

    @PostMapping(GET_AREA_CITY_NAME_BY_IDS)
    Result<String> getAreaCityNameByIds(@RequestBody Set<Long> ids);

    @PostMapping(GET_AREA_STATE_NAME_BY_IDS)
    Result<String> getAreaStateNameByIds(@RequestBody Set<Long> ids);

    @GetMapping(GET_INSTITUTION_SELECT_BY_COMPANY_ID)
    List<BaseSelectEntity> getInstitutionSelectByCompanyId(@RequestParam("companyId") Long companyId);

    @GetMapping(GET_INSTITUTION_PROVIDER_BY_TARGET_NAME)
    List<BaseSelectEntity> getInstitutionProviderByTargetName(@RequestParam("targetName") String targetName);

    /**
     * 银行账户下拉框
     *
     * @param fkTargetId
     * @return
     */
    @GetMapping(GET_INSTITUTION_PROVIDER_ACCOUNT_LIST)
    List<BaseSelectEntity> getInstitutionProviderAccountList(@RequestParam("fkTargetId") Long fkTargetId);

    @GetMapping(GET_INSTITUTION_PROVIDER_ACCOUNT_BY_ID)
    String getInstitutionProviderAccountById(@RequestParam("fkBankAccountId") Long fkBankAccountId);

    @PostMapping(GET_INSTITUTION_ADD_NEWS)
    Long addNews(@RequestBody NewsDto newsDto);

    @GetMapping(GET_INSTITUTION_GET_NEWS_TYPES)
    List<NewsTypeVo> getNewsTypes();

    /**
     * 获取所有的国家列表
     *
     * @return
     */
    @PostMapping(GET_ALL_COUNTRY_LIST)
    List<AreaCountryVo> getAllCountry();

    @PostMapping(GET_ADD_NEWS_MEDIA)
    List<MediaAndAttachedVo> addNewsMedia(@RequestBody List<MediaAndAttachedDto> mediaAttachedVos);

    @PostMapping(GET_CHECK_NEWS)
    Boolean checkNews(@RequestBody NewsDto newsDto);

    @PostMapping(GET_INSTITUTION_CHANNEL_PROVIDER_NAMES_BY_IDS)
    Result<Map<Long, String>> getInstitutionChannelProviderNamesByIds(@RequestBody Set<Long> providerIds);

    @PostMapping(GET_COUNTRY_BY_PUBLIC_LEVEL)
    List<AreaCountryVo> getCountryByPublicLevel(@RequestParam("publicLevel") Integer publicLevel);

    /**
     * 根据学校提供商id获取提供商
     *
     * @Date 14:52 2023/9/26
     * <AUTHOR>
     */
    @PostMapping(GET_INSTITUTION_PROVIDER_BY_ID)
    InstitutionProviderVo getInstitutionProviderById(@RequestParam("fkInstitutionProviderId") Long fkInstitutionProviderId);

    /**
     * 根据学校获取所有绑定提供商
     *
     * @param institutionId
     * @return
     */
    @PostMapping(GET_INSTITUTION_PROVIDER_BY_INSTITUTION)
    Set<Long> getInstitutionProviderByInstitution(@RequestParam("institutionId") Long institutionId);

    @PostMapping(GET_CONTRACT_NEW_BY_PROVIDER_ID)
    String getContractNewByProviderId(@RequestParam("fkInstitutionProviderId") Long fkInstitutionProviderId);

    /**
     * 根据合同id获取过期学校提供商信息和合同信息
     */
    @PostMapping(GET_CONTRACT_EXPIRED_BY_CONTRACT_ID)
    InstitutionProviderContractReminderVo getContractExpiredByProviderId(@RequestParam("contractId") Long contractId);

    /**
     * 根据国家ids 获取国家对象Map
     *
     * @Date 17:44 2023/12/18
     * <AUTHOR>
     */
    @PostMapping(GET_COUNTRY_DTO_MAP_BY_IDS)
    Result<Map<Long, AreaCountryVo>> getCountryDtoMapByIds(@RequestBody Set<Long> countryIds);

    /**
     * 根据提供商ids 获取提供商对象Map
     *
     * @Date 17:44 2023/12/18
     * <AUTHOR>
     */
    @PostMapping(GET_INSTITUTION_PROVIDER_MAP_BY_IDS)
    Result<Map<Long, InstitutionProviderVo>> getInstitutionProviderMapByIds(@RequestBody Set<Long> institutionProviderIds);

    /**
     * 根据学校ids 查找学校对象map
     *
     * @param institutionIdSet
     * @return
     */
    @PostMapping(GET_INSTITUTION_DTO_MAP_BY_IDS)
    Result<Map<Long, InstitutionVo>> getInstitutionDtoMapByIds(@RequestBody Set<Long> institutionIdSet);

    /**
     * 根据学校Ids，获取学校对象信息
     */
    @PostMapping(GET_INSTITUTION_DTO_BY_IDS)
    Result<Map<Long, InstitutionVo>> getInstitutionDtoByIds(@RequestBody Set<Long> institutionIdSet);


    /**
     * 根据州省ids获取对应的大区对象Map
     *
     * @Date 17:21 2023/12/19
     * <AUTHOR>
     */
    @PostMapping(GET_REGION_MAP_BY_STATE_IDS)
    Map<Long, AreaRegionVo> getRegionMapByStateIds(@RequestBody List<Long> stateIds);

    @PostMapping(GET_AREA_REGION_NAME_BY_IDS)
    Map<Long, String> getAreaRegionNameByIds(@RequestBody Set<Long> fkAreaRegionIds);

    @PostMapping(FIND_AREA_REGION_BY_ID)
    Result<AreaRegionVo> findAreaRegionById(@RequestParam("id") Long id);

    /**
     * 根据提供商ids获取对应的学校
     *
     * <AUTHOR>
     * @DateTime 2024/4/23 10:02
     */
    @PostMapping(GET_INSTITUTION_BY_PROVIDER)
    Result<List<InstitutionProviderInstitutionVo>> getInstitutionByProvider(@RequestParam("fkCompanyIds") Set<Long> fkCompanyIds,
                                                                            @RequestBody Set<Long> fkInstitutionProviderIds);

    /**
     * 根据国家ids 获取有效的学校列表
     *
     * @param fkAreaCountryIds 国家ids
     * @return
     */
    @PostMapping(GET_INSTITUTION_BY_COUNTRY_IDS)
    Result<List<InstitutionVo>> getInstitutionByCountryIds(@RequestBody Set<Long> fkAreaCountryIds);

    @PostMapping(BATCH_TRANSLATION_AND_COURSE)
    void batchTranslationInstitutionAndCourseInfo();

    /**
     * 根据课程ids获取英文课程名map
     */
    @PostMapping(GET_COURSE_EN_NAME_BY_IDS)
    Result<Map<Long, String>> getCourseEnNameByIds(@RequestBody Set<Long> courseIds);

    /**
     * 根据提供商id 获取的学校列表
     *
     * @param fkProviderId 提供商id
     * @return
     */
    @PostMapping(GET_MPS_INSTITUTION_PROVIDER_LIST)
    Result<List<InstitutionVo>> getMpsInstitutionProviderList(@RequestBody Long fkProviderId);

    /**
     * 根据国家key集合获取对应的id
     *
     * @param keys 国家keys
     * @return
     */
    @PostMapping(GET_COUNTRY_ID_BY_KEYS)
    Result<Map<String, Long>> getCountryIdByKeys(@RequestBody Set<String> keys);

    @PostMapping(GET_NEWS_EMAIL_TMPLETE)
    Result<NewsEmailTmpeleteVo> getNewsEmailTmpelete(@RequestParam Long newsId, @RequestParam Integer sendType);

    /**
     * 根据目标表名和ID获取申请费信息
     *
     * @param fkTableName 目标表名
     * @param fkTableId   主键ID
     * @return
     */
    @PostMapping(GET_APP_FEES)
    Result<List<InstitutionAppFeeVo>> getAppFees(@RequestParam("fkTableName") String fkTableName, @RequestParam("fkTableId") Long fkTableId);


    /**
     * 根据学校权限组获取学校列表
     * @param institutionQueryDto
     * @return
     */
    @PostMapping(GET_INSTITUTION_BY_INSTITUTION_PERMISSION_GROUP)
    Result<ResponseBo> getInstitutionByInstitutionPermissionGroup(@RequestBody SearchBean<InstitutionQueryDto> institutionQueryDto);

    /**
     * 根据合同id获取所有佣金方案下适用国家
     * @param id
     * @return
     */
    @PostMapping(GET_CONTRACT_APPLY_COUNTRY_BY_CONTRACT_ID)
    Result<List<String>> getContractApplyCountryByContractId(Long id);

    /**
     * 峰会表单常用国家下拉框
     * @param publicLevel
     * @return
     */
    @PostMapping(GET_COUNTRY_BY_PUBLIC_LEVEL_BY_SUMMIT)
    List<AreaCountryVo> getCountryByPublicLevelBySummit(@RequestParam("publicLevel") Integer publicLevel);
}

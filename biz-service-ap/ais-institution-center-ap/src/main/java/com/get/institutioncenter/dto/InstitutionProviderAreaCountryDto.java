package com.get.institutioncenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @author: Sea
 * @create: 2021/3/29 10:14
 * @verison: 1.0
 * @description:
 */
@Data
public class InstitutionProviderAreaCountryDto extends BaseVoEntity {
    /**
     * 学校代理Id
     */
    @ApiModelProperty(value = "学校代理Id")
    private Long fkInstitutionProviderId;

    /**
     * 业务国家Id
     */
    @ApiModelProperty(value = "业务国家Id")
    private Long fkAreaCountryId;
}

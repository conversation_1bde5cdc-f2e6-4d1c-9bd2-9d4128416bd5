package com.get.institutioncenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @author: Sea
 * @create: 2020/7/29 10:26
 * @verison: 1.0
 * @description:
 */
@Data
public class ContractTypeDto extends BaseVoEntity {
    /**
     * 类型名称
     */
    @NotBlank(message = "类型名称不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "类型名称", required = true)
    private String typeName;
    /**
     * 类型key
     */
    @ApiModelProperty(value = "类型key")
    private String typeKey;
    /**
     * 排序，数字由小到大排列
     */
    @ApiModelProperty(value = "排序，数字由小到大排列")
    private Integer viewOrder;

    //自定义内容
    /**
     * 关键字
     */
    @ApiModelProperty(value = "关键字")
    private String keyWord;
}

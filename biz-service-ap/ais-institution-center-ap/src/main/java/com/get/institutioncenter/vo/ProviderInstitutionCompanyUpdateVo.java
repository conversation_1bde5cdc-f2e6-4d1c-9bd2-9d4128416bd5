package com.get.institutioncenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;


/**
 * 提供商-学校 安全配置~
 * 
 * @Date 12:19 2023/12/4
 * <AUTHOR>
 */
@Data
public class ProviderInstitutionCompanyUpdateVo {

    @ApiModelProperty(value = "学校提供商Ids")
    @NotEmpty(message = "学校提供商Ids不能为空")
    private List<Long> fkInstitutionProviderIds;

    @ApiModelProperty(value = "公司ids")
    @NotEmpty(message = "公司ids不能为空")
    private List<Long> companyIds;

}

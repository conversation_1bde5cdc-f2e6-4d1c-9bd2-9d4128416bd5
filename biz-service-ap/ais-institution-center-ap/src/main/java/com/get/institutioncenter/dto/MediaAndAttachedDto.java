package com.get.institutioncenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @DATE: 2020/8/4
 * @TIME: 15:15
 * @Description:
 **/
@Data
public class MediaAndAttachedDto extends BaseVoEntity {
    @ApiModelProperty(value = "文件路径", required = true)
    @NotBlank(message = "文件路径不能为空", groups = {Add.class, Update.class})
    private String filePath;

    /**
     * 源文件名
     */
    @ApiModelProperty(value = "源文件名", required = true)
    @NotBlank(message = "文件名不能为空", groups = {Add.class, Update.class})
    private String fileNameOrc;
    /**
     * 文件guid(文档中心)
     */
    @ApiModelProperty(value = "文件guid", required = true)
    @NotBlank(message = "文件guid不能为空", groups = {Add.class, Update.class})
    private String fkFileGuid;

    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String fkTableName;

    /**
     * 表Id
     */
    @ApiModelProperty(value = "表Id")
    private Long fkTableId;

    /**
     * 类型关键字，如：institution_mov/institution_pic/alumnus_head_icon
     */
    @ApiModelProperty(value = "类型关键字", required = true)
    @NotBlank(message = "类型关键字不能为空", groups = {Add.class, Update.class})
    private String typeKey;

    /**
     * 索引值(默认从0开始，同一类型下值唯一)
     */
    @ApiModelProperty(value = "索引值")
    private Integer indexKey;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 链接
     */
    @ApiModelProperty(value = "链接")
    private String link;

    /**
     * 文件key
     */
    @ApiModelProperty(value = "文件key")
    private String fileKey;
}

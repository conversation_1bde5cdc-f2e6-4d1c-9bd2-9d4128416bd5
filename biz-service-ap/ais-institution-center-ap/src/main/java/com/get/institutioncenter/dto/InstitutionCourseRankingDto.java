package com.get.institutioncenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @DATE: 2020/12/18
 * @TIME: 17:47
 * @Description:
 **/
@Data
public class InstitutionCourseRankingDto extends BaseVoEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 课程Id
     */
    @ApiModelProperty(value = "课程Id")
    @NotNull(message = "课程Id不能为空", groups = {Add.class, Update.class})
    private Long fkInstitutionCourseId;
    /**
     * 排名类型：国家排名0/世界排名1
     */
    @ApiModelProperty(value = "排名类型：国家排名0/世界排名1")
    @NotNull(message = "排名类型不能为空", groups = {Add.class, Update.class})
    private Integer rankingType;
    /**
     * 排名（最前）
     */
    @ApiModelProperty(value = "排名（最前）")
    @NotNull(message = "排名（最前）不能为空", groups = {Add.class, Update.class})
    private Integer rankingFirst;
    /**
     * 排名（最后）
     */
    @ApiModelProperty(value = "排名（最后）")
    @NotNull(message = "排名（最后）不能为空", groups = {Add.class, Update.class})
    private Integer rankingLast;
    /**
     * 排名描述
     */
    @ApiModelProperty(value = "排名描述")
    private String rankingNote;
}

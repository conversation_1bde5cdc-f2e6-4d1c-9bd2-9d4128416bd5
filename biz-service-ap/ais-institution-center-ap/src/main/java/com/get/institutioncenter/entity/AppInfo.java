package com.get.institutioncenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("s_app_info")
public class AppInfo extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    @Column(name = "fk_table_name")
    private String fkTableName;
    /**
     * 表Id
     */
    @ApiModelProperty(value = "表Id")
    @Column(name = "fk_table_id")
    private Long fkTableId;
    /**
     * 类型关键字，如：app_course_website
     */
    @ApiModelProperty(value = "类型关键字，如：app_course_website")
    @Column(name = "type_key")
    private String typeKey;
    /**
     * 类型内容
     */
    @ApiModelProperty(value = "类型内容")
    @Column(name = "type_value")
    private String typeValue;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", fkTableName=").append(fkTableName);
        sb.append(", fkTableId=").append(fkTableId);
        sb.append(", typeKey=").append(typeKey);
        sb.append(", typeValue=").append(typeValue);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}
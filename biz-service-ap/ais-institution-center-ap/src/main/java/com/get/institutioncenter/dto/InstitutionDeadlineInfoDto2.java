package com.get.institutioncenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2022/5/6 15:36
 */
@Data
public class InstitutionDeadlineInfoDto2 extends BaseVoEntity {
    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    @NotNull(message = "学校不能为空", groups = {Add.class, Update.class})
    private Long fkInstitutionId;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;

    /**
     * 公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2
     */
    @ApiModelProperty(value = "公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
    private String publicLevel;

    /**
     * 发布时间
     */
    @ApiModelProperty(value = "发布时间")
    private Date publishTime;

    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    private Boolean isActive;

    /**
     * 学校名称
     */
    @ApiModelProperty(value = "学校名称")
    private String fkInstitutionName;

    /**
     * 国家id
     */
    @ApiModelProperty(value = "国家id")
    private Long fkCountryId;

    @ApiModelProperty(value = "学校名称")
    private String schoolName;

}

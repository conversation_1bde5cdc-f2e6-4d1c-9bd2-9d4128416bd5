package com.get.institutioncenter.dto.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class InstitutionScholarshipQueryDto {

    @ApiModelProperty(value = "目标对象Id")
    private Long fkTableId;

    @ApiModelProperty(value = "目标类型")
    private String fkTableName;

    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    private Boolean isActive;

    //=========path=========

    /**
     * 详细信息
     */
    @ApiModelProperty(value = "详细信息")
    private String appDetail;

    @ApiModelProperty(value = "搜索名称")
    private String keyword;

    /**
     * 国家id
     */
    @ApiModelProperty(value = "国家id")
    private Long fkCountryId;

    @ApiModelProperty(value = "生效时间")
    private String effectiveDate;

}

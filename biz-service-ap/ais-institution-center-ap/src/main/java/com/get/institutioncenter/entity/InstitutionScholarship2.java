package com.get.institutioncenter.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;

@Data
@TableName("m_institution_scholarship2")
public class InstitutionScholarship2 extends BaseEntity implements Serializable {
    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    @Column(name = "fk_institution_id")
    private Long fkInstitutionId;

    /**
     * 学校学院Id（一级）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "学校学院Id（一级）")
    @Column(name = "fk_institution_faculty_id")
    private Long fkInstitutionFacultyId;

    /**
     * 学校学院Id（二级）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "学校学院Id（二级）")
    @Column(name = "fk_institution_faculty_id_sub")
    private Long fkInstitutionFacultyIdSub;

    /**
     * 课程专业等级Id
     */
    @ApiModelProperty(value = "课程专业等级Id")
    @Column(name = "fk_major_level_ids")
    private String fkMajorLevelIds;

    /**
     * 奖学金标题
     */
    @ApiModelProperty(value = "奖学金标题")
    @Column(name = "scholarship_title")
    private String scholarshipTitle;

    /**
     * 奖学金金额
     */
    @ApiModelProperty(value = "奖学金金额")
    @Column(name = "scholarship_amount")
    private String scholarshipAmount;

    /**
     * 奖学金名额
     */
    @ApiModelProperty(value = "奖学金名额")
    @Column(name = "scholarship_quota")
    private String scholarshipQuota;


    /**
     * 奖学金适用学生
     */
    @ApiModelProperty(value = "奖学金适用学生")
    @Column(name = "scholarship_apply_to")
    private String scholarshipApplyTo;

    /**
     * 申请条件
     */
    @ApiModelProperty(value = "申请条件")
    @Column(name = "app_condition")
    private String appCondition;

    /**
     * 申请方式
     */
    @ApiModelProperty(value = "申请方式")
    @Column(name = "app_method")
    private String appMethod;

    /**
     * 申请截止日期：[2022-1-5,2023-10-5][2022-1-5,2023-10-5]
     */
    @ApiModelProperty(value = "申请截止日期：[2022-1-5,2023-10-5][2022-1-5,2023-10-5]")
    @Column(name = "app_deadline")
    private String appDeadline;

    /**
     * 公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2
     */
    @ApiModelProperty(value = "公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
    @Column(name = "public_level")
    private String publicLevel;

    /**
     * 发布时间
     */
    @ApiModelProperty(value = "发布时间")
    @Column(name = "publish_time")
    private Date publishTime;

    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @Column(name = "is_active")
    private Boolean isActive;

    /**
     * 详细信息
     */
    @ApiModelProperty(value = "详细信息")
    @Column(name = "app_detail")
    private String appDetail;
}
package com.get.institutioncenter.dto.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Set;

@Data
public class InstitutionApplicationStaticsQueryDto {

    @ApiModelProperty(value = "查询公司ID列表")
    private List<Long> fkCompanyIdList;

    @ApiModelProperty(value = "国家ID")
    private Long fkAreaCountryId;

    @ApiModelProperty(value = "学校ID列表")
    private Set<Long> fkInstitutionIds;

    @ApiModelProperty(value = "学校模糊查询名称")
    private String fkInstitutionName;

    @ApiModelProperty(value = "学校提供商模糊查询名称")
    private String fkInstitutionProviderName;

    @ApiModelProperty(value = "申请创建开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "申请创建结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "首次成功入学结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date succTimeEnd;

    @ApiModelProperty(value = "首次成功入学开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date succTimeStart;

    @ApiModelProperty(value = "统计方式:1-申请创建时间统计；2-首次成功入学时间统计")
    private Integer type;

    @ApiModelProperty(value = "所属集团Id")
    private List<Long> fkInstitutionGroupIds;

    @ApiModelProperty(value = "开学日期开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date beginOpeningTime;

    @ApiModelProperty(value = "开学日期结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endOpeningTime;

    @ApiModelProperty(value = "当前申请步骤状态")
    private List<Long> currentState;

    @ApiModelProperty(value = "排序方式：1-申请数；2-主课数；3-学生数；其他-成功入学（学生数）")
    private Integer sortedType;



}

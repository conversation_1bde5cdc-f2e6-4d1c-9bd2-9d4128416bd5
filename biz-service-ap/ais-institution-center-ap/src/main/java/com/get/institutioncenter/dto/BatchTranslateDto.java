package com.get.institutioncenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: Hardy
 * @create: 2021/6/15 11:50
 * @verison: 1.0
 * @description:
 */
@Data
public class BatchTranslateDto {
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String tableName;
    /**
     * 字段名
     */
    @ApiModelProperty(value = "字段名")
    private String columnName;
    /**
     * 语言编码：zh-cn/en-us/zh-hk/zh-tw
     */
    @ApiModelProperty(value = "语言编码：zh-cn/en-us/zh-hk/zh-tw")
    private String languageCode;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;
    /**
     * 翻译内容
     */
    @ApiModelProperty(value = "翻译内容")
    private String query;
}

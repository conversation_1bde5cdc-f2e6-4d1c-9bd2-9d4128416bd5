package com.get.institutioncenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @DATE: 2022/12/8
 * @TIME: 14:59
 * @Description: 学校申请统计-构造学校提供商DTO
 **/
@Data
public class ApplicationStatisticsProviderVo {
    @ApiModelProperty(value = "提供商公司id拼接串")
    private String fkCompanyIds;
    /**
     * 学校ID
     */
    @ApiModelProperty(value = "学校ID")
    private Long fkInstitutionId;
    /**
     * 提供商ID
     */
    @ApiModelProperty(value = "提供商ID")
    private Long fkInstitutionProviderId;

    /**
     * 学校提供商名称
     */
    @ApiModelProperty(value = "学校提供商名称【状态】")
    private String fkInstitutionProviderName;

}

package com.get.institutioncenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @author: Sea
 * @create: 2021/4/26 14:58
 * @verison: 1.0
 * @description:
 */
@Data
public class ContractFormulaInstitutionFacultyDto extends BaseVoEntity {
    /**
     * 合同公式Id
     */
    @ApiModelProperty(value = "合同公式Id")
    private Long fkContractFormulaId;

    /**
     * 学校学院Id
     */
    @ApiModelProperty(value = "学校学院Id")
    private Long fkInstitutionFacultyId;
}

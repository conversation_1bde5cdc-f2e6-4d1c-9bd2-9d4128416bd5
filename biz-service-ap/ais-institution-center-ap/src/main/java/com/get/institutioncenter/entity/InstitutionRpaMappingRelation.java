package com.get.institutioncenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("r_institution_rpa_mapping_relation")
public class InstitutionRpaMappingRelation extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    @Column(name = "fk_institution_id")
    private Long fkInstitutionId;
    /**
     * RPA课程地图学校Id
     */
    @ApiModelProperty(value = "RPA课程地图学校Id")
    @Column(name = "fk_institution_rpa_mapping_id")
    private Long fkInstitutionRpaMappingId;
}
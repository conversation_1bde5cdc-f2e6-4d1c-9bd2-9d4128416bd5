package com.get.institutioncenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2022/12/7
 * @TIME: 17:04
 * @Description:学校申请统计Dto
 **/
@Data
public class InstitutionApplicationStatisticsVo {
    /**
     * 学校ID
     */
    @ApiModelProperty(value = "学校ID")
    private Long fkInstitutionId;

    /**
     * 国家ID
     */
    @ApiModelProperty(value = "国家ID")
    private Long fkAreaCountryId;

    /**
     * 国家名称
     */
    @ApiModelProperty(value = "国家名称")
    private String fkAreaCountryName;

    /**
     * 学校名称
     */
    @ApiModelProperty(value = "学校名称")
    private String fkInstitutionName;

    /**
     * 提供商列表
     */
    @ApiModelProperty(value = "提供商列表")
    private List<ApplicationStatisticsProviderVo> providerList;


    /**
     * 学生数
     */
    @ApiModelProperty(value = "学生数")
    private BigDecimal studentCount;

    /**
     * 申请数
     */
    @ApiModelProperty(value = "申请数")
    private BigDecimal applicationCount;

    /**
     * 提交数（过滤掉子课的申请数）
     */
    @ApiModelProperty(value = "提交数")
    private BigDecimal mainCourseCount;

    /**
     * 成功入学数（学生数）
     */
    @ApiModelProperty(value = "成功入学数（学生数）")
    private BigDecimal successsStudentCount;


    /**
     * 转化率
     */
    @ApiModelProperty(value = "定校量转化率）")
    private String conversionRate;
}

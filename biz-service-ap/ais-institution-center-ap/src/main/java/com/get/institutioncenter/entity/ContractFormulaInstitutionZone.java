package com.get.institutioncenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("r_contract_formula_institution_zone")
public class ContractFormulaInstitutionZone extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 合同公式Id
     */
    @ApiModelProperty(value = "合同公式Id")
    @Column(name = "fk_contract_formula_id")
    private Long fkContractFormulaId;
    /**
     * 学校校区Id
     */
    @ApiModelProperty(value = "学校校区Id")
    @Column(name = "fk_institution_zone_id")
    private Long fkInstitutionZoneId;
}
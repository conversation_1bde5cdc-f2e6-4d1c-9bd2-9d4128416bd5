package com.get.institutioncenter.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("r_institution_course_pathway")
public class InstitutionCoursePathway extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 课程Id
     */
    @ApiModelProperty(value = "课程Id")
    @Column(name = "fk_institution_course_id")
    private Long fkInstitutionCourseId;
    /**
     * 桥梁课程Id
     */
    @ApiModelProperty(value = "桥梁课程Id")
    @Column(name = "fk_institution_course_id_pathway")
    private Long fkInstitutionCourseIdPathway;
    /**
     * 升读学术要求
     */
    @ApiModelProperty(value = "升读学术要求")
    @Column(name = "req_academic_1")
    @TableField("req_academic_1")
    private String reqAcademic1;
    /**
     * 升读英语要求
     */
    @ApiModelProperty(value = "升读英语要求")
    @Column(name = "req_eng_1")
    @TableField("req_eng_1")
    private String reqEng1;
    /**
     * 升读特定科目要求
     */
    @ApiModelProperty(value = "升读特定科目要求")
    @Column(name = "req_particular_subject")
    private String reqParticularSubject;
    /**
     * 升读大二学术要求
     */
    @ApiModelProperty(value = "升读大二学术要求")
    @Column(name = "req_academic_2")
    @TableField("req_academic_2")
    private String reqAcademic2;
    /**
     * 升读大二英语要求
     */
    @ApiModelProperty(value = "升读大二英语要求")
    @Column(name = "req_eng_2")
    @TableField("req_eng_2")
    private String reqEng2;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", fkInstitutionCourseId=").append(fkInstitutionCourseId);
        sb.append(", fkInstitutionCourseIdPathway=").append(fkInstitutionCourseIdPathway);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}
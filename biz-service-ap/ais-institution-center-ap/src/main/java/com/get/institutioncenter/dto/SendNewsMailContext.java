package com.get.institutioncenter.dto;

import com.get.institutioncenter.entity.MediaAndAttached;
import com.get.institutioncenter.entity.News;
import com.get.permissioncenter.dto.ConfigDto;
import com.get.permissioncenter.vo.ConfigVo;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2022/12/9 16:52
 * @verison: 1.0
 * @description:
 */
@Data
public class SendNewsMailContext {

    /**
     * 新闻实体
     */
    private News news;

    /**
     * 附件
     */
    private List<MediaAndAttached> mediaAndAttacheds;

    /**
     * 部门配置
     */
    //
    private ConfigVo dpConfig;

    /**
     * 接口密钥配置
     */
    //
    private ConfigVo  IaeApiConfig;

    /**
     * 创建时间
     */
    private Date createDate;
}

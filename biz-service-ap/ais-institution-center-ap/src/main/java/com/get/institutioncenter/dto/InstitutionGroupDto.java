package com.get.institutioncenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @DATE: 2020/10/20
 * @TIME: 10:22
 * @Description:学校集团vo
 **/
@Data
public class InstitutionGroupDto extends BaseVoEntity {
    /**
     * 编号
     */
    @ApiModelProperty(value = "编号", required = true)
    private String num;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称", required = true)
    @NotBlank(message = "名称不能为空", groups = {Add.class, Update.class})
    private String name;

    /**
     * 中文名称
     */
    @ApiModelProperty(value = "中文名称")
    private String nameChn;
}

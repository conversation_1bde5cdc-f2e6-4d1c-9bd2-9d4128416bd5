package com.get.institutioncenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @author: Sea
 * @create: 2021/4/26 15:02
 * @verison: 1.0
 * @description:
 */
@Data
public class ContractFormulaInstitutionZoneDto extends BaseVoEntity {
    /**
     * 合同公式Id
     */
    @ApiModelProperty(value = "合同公式Id")
    private Long fkContractFormulaId;

    /**
     * 学校校区Id
     */
    @ApiModelProperty(value = "学校校区Id")
    private Long fkInstitutionZoneId;
}

package com.get.institutioncenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;

/**
 * @author: Sea
 * @create: 2021/4/23 17:09
 * @verison: 1.0
 * @description:
 */
@Data
public class ContractFormulaCommissionDto {
    /**
     * 合同公式Id
     */
    @ApiModelProperty(value = "合同公式Id")
    private Long fkContractFormulaId;

    /**
     * 期数，从1开始，按顺序生成，并按顺序排序
     */
    @ApiModelProperty(value = "期数，从1开始，按顺序生成，并按顺序排序")
    private Integer step;

    /**
     * 佣金比例
     */
    @ApiModelProperty(value = "佣金比例")
    private BigDecimal commissionRate;

    /**
     * 代理佣金比例
     */
    @ApiModelProperty(value = "代理佣金比例")
    private BigDecimal commissionRateAg;

    /**
     * 固定金额
     */
    @ApiModelProperty(value = "固定金额")
    private BigDecimal fixedAmount;

    /**
     * 代理固定金额
     */
    @ApiModelProperty(value = "代理固定金额")
    private BigDecimal fixedAmountAg;

    /**
     * 佣金上限
     */
    @ApiModelProperty(value = "佣金上限")
    private BigDecimal limitAmount;

    /**
     * 代理佣金上限
     */
    @ApiModelProperty(value = "代理佣金上限")
    private BigDecimal limitAmountAg;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;
}

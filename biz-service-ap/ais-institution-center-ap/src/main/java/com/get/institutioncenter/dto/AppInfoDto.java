package com.get.institutioncenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @DATE: 2020/12/10
 * @TIME: 10:36
 * @Description:
 **/
@Data
public class AppInfoDto extends BaseVoEntity  {
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String fkTableName;

    /**
     * 表Id
     */
    @ApiModelProperty(value = "表Id")
    @NotNull(message = "关联目标不能为空", groups = {Add.class, Update.class})
    private Long fkTableId;

    /**
     * 类型关键字，如：app_course_website
     */
    @ApiModelProperty(value = "类型关键字，如：app_course_website")
    @NotBlank(message = "类型关键字不能为空", groups = {Add.class, Update.class})
    private String typeKey;

    /**
     * 类型内容
     */
    @ApiModelProperty(value = "类型内容")
    private String typeValue;
}

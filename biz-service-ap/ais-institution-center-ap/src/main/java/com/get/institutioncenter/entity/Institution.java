package com.get.institutioncenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@TableName("m_institution")
public class Institution extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 学校类型Id
     */
    @ApiModelProperty(value = "学校类型Id")
    @Column(name = "fk_institution_type_id")
    private Long fkInstitutionTypeId;
    /**
     * 国家Id
     */
    @ApiModelProperty(value = "国家Id")
    @Column(name = "fk_area_country_id")
    private Long fkAreaCountryId;
    /**
     * 显示名称
     */
    @ApiModelProperty(value = "显示名称")
    @Column(name = "name_display")
    private String nameDisplay;
    /**
     * 州省Id
     */
    @UpdateWithNull
    @ApiModelProperty(value = "州省Id")
    @Column(name = "fk_area_state_id")
    private Long fkAreaStateId;
    /**
     * 城市Id
     */
    @UpdateWithNull
    @ApiModelProperty(value = "城市Id")
    @Column(name = "fk_area_city_id")
    private Long fkAreaCityId;
    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    @Column(name = "fk_currency_type_num")
    private String fkCurrencyTypeNum;
    /**
     * 学校编号
     */
    @ApiModelProperty(value = "学校编号")
    @Column(name = "num")
    private String num;
    /**
     * 学校名称
     */
    @ApiModelProperty(value = "学校名称")
    @Column(name = "name")
    private String name;
    /**
     * 学校中文名称
     */
    @ApiModelProperty(value = "学校中文名称")
    @Column(name = "name_chn")
    private String nameChn;
    /**
     * 学校简称
     */
    @ApiModelProperty(value = "学校简称")
    @Column(name = "short_name")
    private String shortName;
    /**
     * 学校中文简称
     */
    @ApiModelProperty(value = "学校中文简称")
    @Column(name = "short_name_chn")
    private String shortNameChn;
    /**
     * 学校性质：公立/私立
     */
    @ApiModelProperty(value = "学校性质：公立/私立")
    @Column(name = "nature")
    private String nature;
    /**
     * 成立时间
     */
    @ApiModelProperty(value = "成立时间")
    @Column(name = "established_date")
    private String establishedDate;
    /**
     * 入学申请时间
     */
    @ApiModelProperty(value = "入学申请时间")
    @Column(name = "apply_date")
    private String applyDate;
    /**
     * 学费下限
     */
    @ApiModelProperty(value = "学费下限")
    @Column(name = "apply_fee_min")
    private BigDecimal applyFeeMin;
    /**
     * 学费上限
     */
    @ApiModelProperty(value = "学费上限")
    @Column(name = "apply_fee_max")
    private BigDecimal applyFeeMax;
    /**
     * 学费参考（显示）
     */
    @ApiModelProperty(value = "学费参考（显示）")
    @Column(name = "apply_fee_ref")
    private BigDecimal applyFeeRef;
    /**
     * 学费（统一为人民币，主要是学费筛选使用）
     */
    @ApiModelProperty(value = "学费（统一为人民币，主要是学费筛选使用）")
    @Column(name = "apply_fee_cny")
    private BigDecimal applyFeeCny;
    /**
     * 是否存在官网：0否/1是
     */
    @ApiModelProperty(value = "是否存在官网：0否/1是")
    @Column(name = "is_exist_website")
    private Boolean isExistWebsite;
    /**
     * 官网
     */
    @ApiModelProperty(value = "官网")
    @Column(name = "website")
    private String website;
    /**
     * 邮编
     */
    @ApiModelProperty(value = "邮编")
    @Column(name = "zip_code")
    private String zipCode;
    /**
     * 地址
     */
    @ApiModelProperty(value = "地址")
    @Column(name = "address")
    private String address;
    /**
     * 重点推荐的学校：0否/1是
     */
    @ApiModelProperty(value = "重点推荐的学校：0否/1是")
    @Column(name = "is_kpi")
    private Boolean isKpi;
    /**
     * 重点推荐的学校等级：1小推荐/2中推荐/3大推荐
     */
    @ApiModelProperty(value = "重点推荐的学校等级：1小推荐/2中推荐/3大推荐")
    @Column(name = "kpi_level")
    private Integer kpiLevel;
    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @Column(name = "is_active")
    private Boolean isActive;
    /**
     * 公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2
     */
    @ApiModelProperty(value = "公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
    @Column(name = "public_level")
    private String publicLevel;
    /**
     * 数据等级：0旧数据导入/1官方数据导入/2需要补充/3采集完整
     */
    @ApiModelProperty(value = "数据等级：0旧数据导入/1官方数据导入/2需要补充/3采集完整")
    @Column(name = "data_level")
    private Integer dataLevel;
    /**
     * 旧数据id(gea)
     */
    @ApiModelProperty(value = "旧数据id(gea)")
    @Column(name = "id_gea")
    private String idGea;
    /**
     * 旧数据id(iae)
     */
    @ApiModelProperty(value = "旧数据id(iae)")
    @Column(name = "id_iae")
    private String idIae;
    /**
     * 详细描述
     */
    @ApiModelProperty(value = "详细描述")
    @Column(name = "detail")
    private String detail;
    /**
     * 地图坐标（google）
     */
    @ApiModelProperty(value = "地图坐标（google）")
    @Column(name = "map_xy_gg")
    private String mapXyGg;
    /**
     * 地图坐标（baidu）
     */
    @ApiModelProperty(value = "地图坐标（baidu）")
    @Column(name = "map_xy_bd")
    private String mapXyBd;

    @ApiModelProperty(value = "学校类型")
    @Column(name = "ranking_type")
    private String rankingType;

    @ApiModelProperty(value = "k12类型枚举，多选逗号分隔：0=幼儿园/1=小学/2=初中/3=高中")
    @Column(name = "k12_type")
    private String k12Type;

    @ApiModelProperty(value = "提供给学生住宿方式枚举，多选逗号分隔：1=走读/2=寄宿/3=混合（即偶尔住校）")
    @Column(name = "accommodation_type")
    private String accommodationType;


    @ApiModelProperty(value = "是否教会学校：0否/1是")
    @Column(name = "is_church")
    private int isChurch;

}
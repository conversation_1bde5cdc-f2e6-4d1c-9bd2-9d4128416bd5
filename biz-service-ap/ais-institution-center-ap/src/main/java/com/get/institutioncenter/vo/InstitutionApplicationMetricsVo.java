package com.get.institutioncenter.vo;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class InstitutionApplicationMetricsVo {


    //学校名称
    @ApiModelProperty(value = "学校名称")
    private String institutionName;

    @ApiModelProperty(value = "学校id")
    private Long fkInstitutionId;


    @ApiModelProperty(value = "学校类型")
    private String institutionTypeName;

    @ApiModelProperty(value = "QS排名")
    private Integer qs;


    @ApiModelProperty(value = "提交完成数")
    private Integer submitted;


    @ApiModelProperty(value = "录取数")
    private Integer admitted;


    @ApiModelProperty(value = "已付押金数")
    private Integer deposited;

    @ApiModelProperty(value = "收到签证函数")
    private  Integer cas;

    @ApiModelProperty(value = "成功入学数")
    private Integer enrolled;

    @ApiModelProperty(value = "获得签证数")
    private Integer receivedVisa;

    @ApiModelProperty(value = "准入学数")
    private Integer pendingEnrollmentCount;

    @ApiModelProperty(value = "后续课程数")
    private Integer followUpCourseCount;

    @ApiModelProperty(value = "国家id")
    private Long countryId;
    @ApiModelProperty(value = "国家名称")
    private String countryName;

    //公司id
    @ApiModelProperty(value = "公司id")
    private Long fkCompanyId;

}

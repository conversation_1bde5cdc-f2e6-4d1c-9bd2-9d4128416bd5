package com.get.institutioncenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/7/24 15:07
 * @verison: 1.0
 * @description:
 */
@Data
public class AreaCountryDto extends BaseVoEntity {
    /**
     * 币种编号
     */
    private String fkCurrencyTypeNum;

    /**
     * 国家编号
     */
    @NotBlank(message = "国家编号不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "国家编号", required = true)
    private String num;

    /**
     * 国家名称
     */
    @NotBlank(message = "国家名称不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "国家名称", required = true)
    private String name;

    /**
     * 国家中文名称
     */
    @NotBlank(message = "国家名称不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "国家中文名称", required = true)
    private String nameChn;

    /**
     * 首都
     */
    @ApiModelProperty(value = "首都")
    private String capital;

    /**
     * 人口
     */
    @ApiModelProperty(value = "人口")
    private String population;

    /**
     * 面积
     */
    @ApiModelProperty(value = "面积")
    private String area;

    /**
     * 语言
     */
    @ApiModelProperty(value = "语言")
    private String language;

    /**
     * 宗教
     */
    @ApiModelProperty(value = "宗教")
    private String religion;

    /**
     * 时差
     */
    @ApiModelProperty(value = "时差")
    private String timeDifference;

    /**
     * 现任国家元首
     */
    @ApiModelProperty(value = "现任国家元首")
    private String president;

    /**
     * 国旗意义
     */
    @ApiModelProperty(value = "国旗意义")
    private String flagMeaning;

    /**
     * 国徽意义
     */
    @ApiModelProperty(value = "国徽意义")
    private String emblemMeaning;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2
     */
    @ApiModelProperty(value = "公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
    private String publicLevel;

    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;
    //自定义内容
    /**
     * 关键字
     */
    @ApiModelProperty(value = "关键字")
    private String keyWord;

    /**
     * 媒体附件id
     */
    @ApiModelProperty(value = "媒体附件id")
    private List<Long> fkId;

    /**
     * 业务区域，枚举（US/UK/ANZ/CAN/EUASIA）
     */
    @ApiModelProperty(value = "业务区域，枚举（US/UK/ANZ/CAN/EUASIA）")
    private String businessAreaKey;

    /**
     * 旧数据id(gea)
     */
    @ApiModelProperty(value = "旧数据id(gea)")
    private String idGea;

    /**
     * 旧数据id(iae)
     */
    @ApiModelProperty(value = "旧数据id(iae)")
    private String idIae;

    /**
     * 区号
     */
    @ApiModelProperty(value = "区号")
    private String areaCode;

    @ApiModelProperty(value = "地理大区Ids，支持多选")
    private String fkAreaRegionIds;

}

package com.get.institutioncenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @DATE: 2020/10/29
 * @TIME: 10:21
 * @Description:
 **/
@Data
public class ContractCompanyDto extends BaseVoEntity {
    /**
     * 合同Id
     */
    @ApiModelProperty(value = "合同Id", required = true)
    @NotNull(message = "合同Id", groups = {Add.class, Update.class})
    private Long fkContractId;

    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;
}

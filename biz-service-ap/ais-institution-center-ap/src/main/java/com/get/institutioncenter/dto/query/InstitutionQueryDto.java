package com.get.institutioncenter.dto.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class InstitutionQueryDto {
    /**
     * 数据等级：0旧数据导入/1官方数据导入/2需要补充/3采集完整
     */
    @ApiModelProperty(value = "数据等级：0旧数据导入/1官方数据导入/2需要补充/3采集完整")
    @NotNull(message = "数据等级")
    private Integer dataLevel;

    /**
     * 国家Id
     */
    @ApiModelProperty(value = "国家Id", required = true)
    private Long fkAreaCountryId;

    /**
     * 学校类型Id
     */
    @ApiModelProperty(value = "学校类型", required = true)
    @NotNull(message = "学校类型Id不能为空")
    private Long fkInstitutionTypeId;

    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活", required = true)
    @NotNull(message = "是否激活不能为空")
    private Boolean isActive;

    @ApiModelProperty(value = "关键词")
    private String keyWord;

    /**
     * 公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2
     */
    @ApiModelProperty(value = "公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
    @NotBlank(message = "公开对象")
    private String publicLevel;

    @ApiModelProperty(value = "国家ids")
    private List<Long> countryIds;

    //学校权限关联的学校需要使用的参数

    @ApiModelProperty(value = "学校权限组id")
    private Long fkInstitutionPermissionGroupId;

    @ApiModelProperty(value = "是否绑定")
    private Integer isBind;

    @ApiModelProperty(value = "学校关键词")
    private String institutionKeyWord;

}

package com.get.institutioncenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @DATE: 2020/9/15
 * @TIME: 16:14
 * @Description: 代理联系人-公司安全配置VO
 **/
@Data
public class ContactPersonCompanyDto extends BaseVoEntity {
    /**
     * 联系人Id
     */
    @ApiModelProperty(value = "联系人Id")
    private Long fkContactPersonId;

    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;
}

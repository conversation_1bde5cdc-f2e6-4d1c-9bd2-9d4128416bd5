package com.get.institutioncenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;

@Data
@TableName("m_contract")
public class Contract extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 学校提供商Id
     */
    @ApiModelProperty(value = "学校提供商Id")
    @Column(name = "fk_institution_provider_id")
    private Long fkInstitutionProviderId;
    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    @Column(name = "fk_institution_id")
    private Long fkInstitutionId;
    /**
     * 学校课程Id
     */
    @ApiModelProperty(value = "学校课程Id")
    @Column(name = "fk_institution_course_id")
    private Long fkInstitutionCourseId;
    /**
     * 合同类型
     */
    @ApiModelProperty(value = "合同类型")
    @Column(name = "fk_contract_type_id")
    private Long fkContractTypeId;
    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @Column(name = "contract_num")
    private String contractNum;
    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    @Column(name = "title")
    private String title;
    /**
     * 合同开始时间
     */
    @ApiModelProperty(value = "合同开始时间")
    @Column(name = "start_time")
    private Date startTime;
    /**
     * 合同结束时间
     */
    @ApiModelProperty(value = "合同结束时间")
    @Column(name = "end_time")
    private Date endTime;
    /**
     * 市场费用
     */
    @ApiModelProperty(value = "市场费用")
    @Column(name = "market_expense")
    private String marketExpense;
    /**
     * 后续费用
     */
    @ApiModelProperty(value = "后续费用")
    @Column(name = "follow_up_expense")
    private String followUpExpense;
    /**
     * 实际佣金
     */
    @ApiModelProperty(value = "实际佣金")
    @Column(name = "commission_rate")
    private String commissionRate;
    /**
     * 后续佣金
     */
    @ApiModelProperty(value = "后续佣金")
    @Column(name = "follow_up_commission_rate")
    private String followUpCommissionRate;
    /**
     * 合同备注
     */
    @ApiModelProperty(value = "合同备注")
    @Column(name = "remark")
    private String remark;
    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @Column(name = "is_active")
    private Boolean isActive;
    @ApiModelProperty(value = "合同表父id")
    @Column(name = "fk_contract_id_revoke")
    private Long fkContractIdRevoke;
    /**
     * 状态： 0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废
     */
    @ApiModelProperty(value = "状态： 0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废")
    @Column(name = "status")
    private Integer status;
    /**
     * 0新签/1续签
     */
    @ApiModelProperty(value = "0新签/1续签")
    @Column(name = "contract_approval_mode")
    private Integer contractApprovalMode;

    @ApiModelProperty("申请周期，如：1-2周")
    @Column(name = "application_cycle")
    private String applicationCycle;


    @ApiModelProperty("结算周期，如：8-12周")
    @Column(name = "settlement_cycle")
    private String settlementCycle;
}
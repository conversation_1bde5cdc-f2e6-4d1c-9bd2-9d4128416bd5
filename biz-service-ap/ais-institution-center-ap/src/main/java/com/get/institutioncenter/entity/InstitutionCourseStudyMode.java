package com.get.institutioncenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@TableName("m_institution_course_study_mode")
public class InstitutionCourseStudyMode extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 课程Id
     */
    @ApiModelProperty(value = "课程Id")
    @Column(name = "fk_institution_course_id")
    private Long fkInstitutionCourseId;
    /**
     * 申请资料Id（官网课程链接Id）
     */
    @ApiModelProperty(value = "申请资料Id（官网课程链接Id）")
    @Column(name = "fk_app_info_id")
    private Long fkAppInfoId;
    /**
     * 申请费
     */
    @ApiModelProperty(value = "申请费")
    @Column(name = "app_fee")
    private BigDecimal appFee;
    /**
     * 课程学费
     */
    @ApiModelProperty(value = "课程学费")
    @Column(name = "fee")
    private BigDecimal fee;
    /**
     * 课程学费说明
     */
    @ApiModelProperty(value = "课程学费说明")
    @Column(name = "fee_note")
    private String feeNote;
    /**
     * 课程长度类型(0周、1学期、2年)
     */
    @ApiModelProperty(value = "课程长度类型(0周、1学期、2年)")
    @Column(name = "duration_type")
    private Integer durationType;
    /**
     * 课程长度
     */
    @ApiModelProperty(value = "课程长度")
    @Column(name = "duration")
    private BigDecimal duration;
    /**
     * 课程总时长说明
     */
    @ApiModelProperty(value = "课程总时长说明")
    @Column(name = "duration_note")
    private String durationNote;
    /**
     * 开始年份
     */
    @ApiModelProperty(value = "开始年份")
    @Column(name = "start_year")
    private String startYear;
    /**
     * 开始月份
     */
    @ApiModelProperty(value = "开始月份")
    @Column(name = "start_month")
    private String startMonth;
    /**
     * 开始日期说明
     */
    @ApiModelProperty(value = "开始日期说明")
    @Column(name = "start_date_note")
    private String startDateNote;
    /**
     * 申请日期
     */
    @ApiModelProperty(value = "申请日期")
    @Column(name = "apply_date")
    private String applyDate;
    /**
     * 申请日期描述
     */
    @ApiModelProperty(value = "申请日期描述")
    @Column(name = "apply_date_note")
    private String applyDateNote;
    /**
     * 申请截止日期
     */
    @ApiModelProperty(value = "申请截止日期")
    @Column(name = "apply_date_deadline")
    private String applyDateDeadline;
    /**
     * 申请截止日期说明
     */
    @ApiModelProperty(value = "申请截止日期说明")
    @Column(name = "apply_date_deadline_note")
    private String applyDateDeadlineNote;
    /**
     * 授课语言
     */
    @ApiModelProperty(value = "授课语言")
    @Column(name = "teaching_language")
    private String teachingLanguage;
    /**
     * 学习模式
     */
    @ApiModelProperty(value = "学习模式")
    @Column(name = "study_mode")
    private String studyMode;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;
    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @Column(name = "is_active")
    private Boolean isActive;

    @ApiModelProperty(value = "币种编号")
    @Column(name = "fk_currency_type_num")
    private String fkCurrencyTypeNum;

    @ApiModelProperty(value = "课程学费（统一为人民币，主要是学费筛选使用）")
    @Column(name = "fee_cny")
    private BigDecimal feeCny;
}
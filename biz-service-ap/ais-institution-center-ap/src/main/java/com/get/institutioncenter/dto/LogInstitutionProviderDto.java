package com.get.institutioncenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Description: 提供商日志记录vo
 * @Param
 * @return
 * <AUTHOR>
 */
@Data
public class LogInstitutionProviderDto extends BaseVoEntity {
    /**
     * 学校供应商Id
     */
    @ApiModelProperty(value = "学校供应商Id")
    private Long fkInstitutionProviderId;

    /**
     * 操作类型：更新集团/UPDATE_INSTITUTION_GROUP, 更新渠道/UPDATE_INSTITUTION_CHANNEL
     */
    @ApiModelProperty(value = "操作类型：更新集团/UPDATE_INSTITUTION_GROUP, 更新渠道/UPDATE_INSTITUTION_CHANNEL")
    private String optType;

    /**
     * 操作信息，格式如：AAAA -> BBBB, CCCC -> DDDD
     */
    @ApiModelProperty(value = "操作信息，格式如：AAAA -> BBBB, CCCC -> DDDD")
    private String optInfo;
}

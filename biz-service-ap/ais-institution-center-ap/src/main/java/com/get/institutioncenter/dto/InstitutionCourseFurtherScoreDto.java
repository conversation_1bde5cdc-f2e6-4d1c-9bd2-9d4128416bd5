package com.get.institutioncenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 课程条件升读要求Vo类
 *
 * @Date 9:56 2021/7/14
 * <AUTHOR>
 */
@Data
public class InstitutionCourseFurtherScoreDto extends BaseVoEntity {
    /**
     * 课程Id
     */
    @ApiModelProperty(value = "课程Id")
    @NotNull(message = "课程Id不能为空")
    private Long fkInstitutionCourseId;

    /**
     * 条件类型，枚举：学术要求/英语要求/大二学术要求/大二英语要求/特定科目要求
     */
    @ApiModelProperty(value = "条件类型，枚举：学术要求/英语要求/大二学术要求/大二英语要求/特定科目要求")
    private Integer conditionType;

    /**
     * 分数
     */
    @ApiModelProperty(value = "分数")
    private BigDecimal score;

    /**
     * 要求描述
     */
    @ApiModelProperty(value = "要求描述")
    private String description;

}

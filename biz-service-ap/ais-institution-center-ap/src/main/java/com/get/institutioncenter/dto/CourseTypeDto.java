package com.get.institutioncenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/12/7
 * @TIME: 11:01
 * @Description:
 **/
@Data
public class CourseTypeDto extends BaseVoEntity {
    /**
     * 类型名称
     */
    @ApiModelProperty(value = "类型名称")
    @NotBlank(message = "类型名称不能为空", groups = {Add.class, Update.class})
    private String typeName;
    /**
     * 类型名称
     */
    @ApiModelProperty(value = "类型名称中文")
    private String typeNameChn;
    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    @ApiModelProperty(value = "关键词")
    private String keyWord;

    @NotNull(message = "匹配优先序不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "匹配优先序，1/2/3，数字越小越优先")
    private Integer matchingPriority;

    @ApiModelProperty(value = "课程类型组别id")
    private Long courseTypeGroupId;

    @ApiModelProperty(value = "课程类型组别ids")
    private List<Long> courseTypeGroupIds;

    /**
     * 公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2
     */
    @ApiModelProperty(value = "公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
    @NotBlank(message = "公开对象不能为空", groups = {Add.class, Update.class})
    private String publicLevel;

}

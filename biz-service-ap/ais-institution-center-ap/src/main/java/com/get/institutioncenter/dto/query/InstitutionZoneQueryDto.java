package com.get.institutioncenter.dto.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class InstitutionZoneQueryDto {
    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    private Long fkInstitutionId;

    /**
     * 校区名称
     */
    @ApiModelProperty(value = "校区名称")
    private String name;

    /**
     * 校区中文名称
     */
    @ApiModelProperty(value = "校区中文名称")
    private String nameChn;
}

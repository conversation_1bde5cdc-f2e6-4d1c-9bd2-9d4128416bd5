package com.get.institutioncenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import com.get.institutioncenter.entity.InstitutionCourseAppInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2022/5/6 11:58
 */
@Data
public class InstitutionScholarshipDto2 extends BaseVoEntity {
    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    @NotNull(message = "学校id不能为空", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    private Long fkInstitutionId;

    /**
     * 学校学院Id
     */
    @ApiModelProperty(value = "学校学院Id")
    private Long fkInstitutionFacultyId;

    /**
     * 课程专业等级Id
     */
    @ApiModelProperty(value = "课程专业等级Id")
    private String fkMajorLevelIds;

    /**
     * 奖学金标题
     */
    @ApiModelProperty(value = "奖学金标题")
    private String scholarshipTitle;



    /**
     * 奖学金金额
     */
    @ApiModelProperty(value = "奖学金金额")
    private String scholarshipAmount;

    /**
     * 奖学金名额
     */
    @ApiModelProperty(value = "奖学金名额")
    private String scholarshipQuota;

    /**
     * 奖学金适用学生
     */
    @ApiModelProperty(value = "奖学金适用学生")
    private String scholarshipApplyTo;

    /**
     * 申请条件
     */
    @ApiModelProperty(value = "申请条件")
    private String appCondition;

    /**
     * 申请方式
     */
    @ApiModelProperty(value = "申请方式")
    private String appMethod;

    /**
     * 申请截止日期：[2022-1-5,2023-10-5][2022-1-5,2023-10-5]
     */
    @ApiModelProperty(value = "申请截止日期：[2022-1-5,2023-10-5][2022-1-5,2023-10-5]")
    private String appDeadline;

    /**
     * 公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2
     */
    @ApiModelProperty(value = "公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
    private String publicLevel;

    /**
     * 发布时间
     */
    @ApiModelProperty(value = "发布时间")
    private Date publishTime;

    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    private Boolean isActive;

    /**
     * 详细信息
     */
    @ApiModelProperty(value = "详细信息")
    private String appDetail;

    @ApiModelProperty(value = "学校名称")
    private String schoolName;

    /**
     * 学校学院Id（二级）
     */
    @ApiModelProperty(value = "学校学院Id（二级）")
    private Long fkInstitutionFacultyIdSub;


    @ApiModelProperty(value = "学院名称")
    private String fkInstitutionFacultyName;

    /**
     * 国家id
     */
    @ApiModelProperty(value = "国家id")
    private Long fkCountryId;
}

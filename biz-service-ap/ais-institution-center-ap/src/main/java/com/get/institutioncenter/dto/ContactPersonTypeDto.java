package com.get.institutioncenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @author: Byron
 * @create: 2020/8/20
 * @verison: 1.0
 * @description: 联系人vo
 */
@Data
public class ContactPersonTypeDto extends BaseVoEntity {
    /**
     * 类型名称
     */
    @NotBlank(message = "类型名称不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "类型名称", required = true)
    private String typeName;

    /**
     * 类型Key，枚举类型Key
     */
    @NotBlank(message = "类型Key不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "类型Key，枚举类型Key", required = true)
    private String typeKey;

    /**
     * 排序，数字由小到大排列
     */
    @NotNull(message = "排序不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "排序，数字由小到大排列", required = true)
    private Integer viewOrder;
}

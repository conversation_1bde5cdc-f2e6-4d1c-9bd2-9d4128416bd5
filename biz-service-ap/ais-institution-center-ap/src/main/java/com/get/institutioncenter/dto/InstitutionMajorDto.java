package com.get.institutioncenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @DATE: 2020/8/6
 * @TIME: 11:44
 * @Description:
 **/
@Data
public class InstitutionMajorDto extends BaseVoEntity {
    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id", required = true)
    @NotNull(message = "学校Id不能为空", groups = {Add.class, Update.class})
    private Long fkInstitutionId;

    /**
     * 学校学院Id
     */
    @ApiModelProperty(value = "学校学院Id", required = true)
    @NotNull(message = "学校学院Id不能为空", groups = {Add.class, Update.class})
    private Long fkInstitutionFacultyId;

    /**
     * 专业等级Id
     */
    @ApiModelProperty(value = "专业等级Id", required = true)
    @NotNull(message = "专业等级Id不能为空", groups = {Add.class, Update.class})
    private Long fkMajorLevelId;

    /**
     * 专业编号
     */
    @ApiModelProperty(value = "专业编号")
    private String num;

    /**
     * 专业名称
     */
    @ApiModelProperty(value = "专业名称", required = true)
    @NotBlank(message = "专业名称不能为空", groups = {Add.class, Update.class})
    private String name;

    /**
     * 专业中文名称
     */
    @ApiModelProperty(value = "专业中文名称", required = true)
    @NotBlank(message = "专业中文名称不能为空", groups = {Add.class, Update.class})
    private String nameChn;

    /**
     * 排名信息
     */
    @ApiModelProperty(value = "排名信息", required = true)
    @NotBlank(message = "排名信息不能为空", groups = {Add.class, Update.class})
    private String ranking;

    /**
     * 学费信息
     */
    @ApiModelProperty(value = "学费信息", required = true)
    @NotBlank(message = "学费信息不能为空", groups = {Add.class, Update.class})
    private String fee;

    /**
     * 总学期时长信息
     */
    @ApiModelProperty(value = "总学期时长信息", required = true)
    @NotBlank(message = "总学期时长信息不能为空", groups = {Add.class, Update.class})
    private String duration;

    /**
     * 核心课程
     */
    @ApiModelProperty(value = "核心课程")
    private String coreCourse;

    /**
     * 录取标准
     */
    @ApiModelProperty(value = "录取标准")
    private String entryStandards;

    /**
     * 职业发展
     */
    @ApiModelProperty(value = "职业发展")
    private String occupationDevelopment;

    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活", required = true)
    @NotNull(message = "是否激活不能为空", groups = {Add.class, Update.class})
    private Boolean isActive;

    /**
     * 排序，数字由小到大排列
     */
    @ApiModelProperty(value = "排序", required = true)
    @NotNull(message = "排序不能为空", groups = {Add.class, Update.class})
    private Integer viewOrder;

}

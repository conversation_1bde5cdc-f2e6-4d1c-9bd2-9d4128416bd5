package com.get.institutioncenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("r_institution_course_rpa_mapping_relation")
public class InstitutionCourseRpaMappingRelation extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 课程Id
     */
    @ApiModelProperty(value = "课程Id")
    @Column(name = "fk_institution_course_id")
    private Long fkInstitutionCourseId;
    /**
     * RPA课程地图Id
     */
    @ApiModelProperty(value = "RPA课程地图Id")
    @Column(name = "fk_institution_course_rpa_mapping_id")
    private Long fkInstitutionCourseRpaMappingId;
}
package com.get.institutioncenter.entity;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Table(name = "r_institution_channel_company")
public class RInstitutionChannelCompany extends BaseEntity implements Serializable {

    @ApiModelProperty(value = "学校渠道id")
    @Column(name = "fk_institution_channel_id")
    private Long fkInstitutionChannelId;

    @ApiModelProperty(value = "公司id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;

}

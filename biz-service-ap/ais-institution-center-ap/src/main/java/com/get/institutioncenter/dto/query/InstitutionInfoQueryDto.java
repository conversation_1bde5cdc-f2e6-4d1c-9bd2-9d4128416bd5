package com.get.institutioncenter.dto.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class InstitutionInfoQueryDto {
    /**
     * 资讯类型Id
     */
    @ApiModelProperty(value = "资讯类型Id", required = true)
    @NotNull(message = "资讯类型Id不能为空")
    private Long fkInfoTypeId;

    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id", required = true)
    @NotNull(message = "学校Id不能为空")
    private Long fkInstitutionId;

    @ApiModelProperty(value = "关键词")
    private String keyWord;

    /**
     * 公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2
     */
    @ApiModelProperty(value = "公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
    @NotBlank(message = "公开对象")
    private String publicLevel;
}

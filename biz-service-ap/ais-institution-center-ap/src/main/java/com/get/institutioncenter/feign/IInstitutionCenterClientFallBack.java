package com.get.institutioncenter.feign;

import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.api.Result;
import com.get.institutioncenter.bo.InstitutionApplicationStaticsQueryBo;
import com.get.institutioncenter.dto.NewsEmailTmpeleteVo;
import com.get.institutioncenter.dto.query.InstitutionQueryDto;
import com.get.institutioncenter.vo.*;
import com.get.institutioncenter.vo.AreaCityVo;
import com.get.institutioncenter.entity.AreaCountry;
import com.get.institutioncenter.entity.Contract;
import com.get.institutioncenter.entity.ContractFormula;
import com.get.institutioncenter.entity.Institution;
import com.get.institutioncenter.entity.MajorLevel;
import com.get.institutioncenter.dto.*;
import com.get.permissioncenter.vo.CompanyVo;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @DATE: 2021/12/30
 * @TIME: 10:17
 * @Description:
 **/
@Component
@VerifyPermission(IsVerify = false)
public class IInstitutionCenterClientFallBack implements IInstitutionCenterClient {


    @Override
    public Integer updateContractStatus(Long id, Integer status) {
        return null;
    }

    @Override
    public Result<Integer> getRankingMaxYear() {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<Long, String>> getWebSiteByTable(String tableName, String typeKey) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<Long, String>> getWebSiteByAppInfoFeignDto(AppInfoFeignVo appInfoFeignVo) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<String> getCityNameById(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<Long, String>> getCityNamesByIds(Set<Long> ids) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<Long, String>> getCityNameChnsByIds(Set<Long> ids) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<Long, String>> getCityFullNamesByIds(Set<Long> ids) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<Long, String>> getCityFullNames() {
        return null;
    }

    @Override
    public Result<Map<Long, String>> getCityDivisionFullNamesByIds(Set<Long> ids) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<String> getCountryName(String countryKey) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<String> getCountryNameEn(String countryKey) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Set<Long>> getAllCountryId() {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<String> getCountryNameById(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<AreaCountry> getCountryById(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<String> getCountryChnNameById(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<Long, String>> getCountryChnNameByIds(Set<Long> ids) {
        return Result.fail("获取数据失败");
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<List<Long>> getCountryIdByKey(List<String> keys) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<Long, String>> getCountryNamesByIds(Set<Long> ids) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<Long, String>> getCountryNameMap() {
        return null;
    }

    @Override
    public Result<Map<Long, AreaRegionVo>> getAreaRegionDtoByIds(Set<Long> ids) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<String> getStateNameById(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<String> getStateFullNameById(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<Long, String>> getStateNamesByIds(Set<Long> ids) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<Long, String>> getStateFullNamesByIds(Set<Long> ids) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<Long, String>> getStateFullNames() {
        return null;
    }

    @Override
    public Result<ContractVo> getInstitutionContractById(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Boolean> updateChangeStatus(Contract contract) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Boolean> changeStatus(Integer status, String tableName, Long businessKey) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<Long, String>> getContractFormulasByIds(Set<Long> ids) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<ContractFormulaCommissionVo>> getContractFormulaCommissionByContractFormulaId(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<Long>> getContractFormulaPreInstitutionByContractFormulaId(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<Long>> getPreInstitutionGroupByContractFormulaId(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<Long>> getPreMajorLevelByContractFormulaId(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<ContractFormulaFeignVo> getContractFormulaConfigByContractFormulaId(Long formulaId) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<Long, String>> getCourseTypeNamesByIds(Set<Long> ids) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<Long, String>> getCourseTypeNamesByCourseGroupTypeIds(Set<Long> ids) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<Long, String>> getCourseTypeNamesByCourseIds(Set<Long> ids) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<Long, String>> getChannelByIds(Set<Long> ids) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<Long>> getInstitutionIds(String institutionName) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<String> getInstitutionName(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Institution> getInstitutionById(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<Long, String>> getInstitutionNamesByIds(Set<Long> institutionIdSet) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<String> getInstitutionNamesById(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<Long, String>> getCountryNamesByInstitutionIds(Set<Long> institutionIds) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<Long, Long>> getCountryIdByInstitutionId(Set<Long> institutionIdSet) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<Long>> getCourseIds(String name) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<String> getCourseNameById(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<Long, String>> getCourseNameByIds(Set<Long> ids) {
        return Result.fail("获取数据失败");
    }
    @Override
    public Result<Map<Long, String>> getCourseNameByCustomIds(Set<Long> ids) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<String> getCourseNameChnById(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<InstitutionCourseVo> getCourseById(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<Long, String>> getCourseNameChnByIds(Set<Long> ids) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<BigDecimal> getFeeById(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<BigDecimal> getSumFeeByIds(List<Long> ids) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<Long, String>> getInstitutionCourseNamesByIds(Set<Long> institutionCourseIdSet) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<String> getInstitutionCourseNameById(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<Long, String>> getInstitutionGroupNamesByIds(Set<Long> institutionGroupIdSet) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<Long>> getInstitutionProviderIdsByName(String institutionProviderName) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<String> getInstitutionProviderName(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<Long, String>> getInstitutionProviderNamesByIds(Set<Long> ids) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<String> getInstitutionProviderType(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<BaseSelectEntity>> getInstitutionProviderSelect(Long companyId) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<Long, LinkedList<Long>>> getCompanyIdsByProviderIds(Set<Long> providerIds) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<CompanyVo>> getProviderCompanyName(Long id) {
        return null;
    }

    @Override
    public Result<Map<Long, String>> getInstitutionProviderChannel() {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<String> getInstitutionProviderChannelById(Long ids) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<Long, String>> getInstitutionProviderChannelByIds(Set<Long> ids) {
        return null;
    }

    @Override
    public Result<Map<Long, String>> getMajorLevelNamesByIds(Set<Long> ids) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<Long, Set<Long>>> getMajorLevelIdsByIds(Set<Long> ids) {
        return null;
    }

    @Override
    public Result<Map<Long, String>> getMajorLevelNameChnsByIds(Set<Long> ids) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<Long, MajorLevel>> getMajorLevelByIds(Set<Long> ids) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<String> getMajorLevelNamesById(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<Long, String>> getNewsTitlesByIds(Set<Long> ids) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<ContractFormula>> getContractFormulasByOfferItem(Long companyId, Long areaCountryId, InstitutionStudentOfferItemDto studentOfferItem) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<Long>> getCourseIdsByContractFormulaId(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<AreaStateVo>> getByFkAreaCountryId(Long fkAreaCountryId) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<AreaCityVo>> getByFkAreaStateId(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<AreaCountryVo>> getAreaCountrys(String keyWord) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<String> checkContractFormula(Long studentCompanyId, Long areaCountryId, Long fkInstitutionCourseId, Long contractFormulaId) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<ContractFormula> getContractFormulaByFormulaId(Long formulaId) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<String> getCityFullNameById(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<String> getCountryNameAndNumById(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Map<Long, MediaAndAttachedVo> getMediaAndAttachedDtos(MediaAndAttachedQueryDto queryVo) {
        return Collections.emptyMap();
    }

    @Override
    public Result<List<AreaCountryVo>> getCountryByKey(List<String> keys) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<Long>> getCountryByName(String name){
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<String> getChannelName(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Boolean> updateCoursefeeCny(String updateType) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Map<Long, String> getAllInstitutionTypeName() {
        return null;
    }

    @Override
    public List<Long> getInstitutionProviderIds(String institutionProviderName) {
        return null;
    }

    @Override
    public List<InstitutionProviderVo> getInstitutionProvidersByName(String keyword){
        return null;
    }

    @Override
    public Map<Long, String> getInstitutionProviderSelectNamesByIds(Set<Long> ids) {
        return null;
    }

    @Override
    public Result<Map<Long, String>> getInstitutionZoneNamesByIds(Set<Long> institutionZoneIdSet) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<Long, String>> getCountryNumByCountryIds(Set<Long> countryIdIdSet) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Map<Long, String> getMajorLevelNamesByCourIds(Set<Long> ids) {
        return null;
    }

    @Override
    public List<Long> getInstitutionProviderIdsByCompanyIds(List<Long> fkCompanyIds) {
        return null;
    }

    @Override
    public List<Long> getInstitutionProviderIdsByCompanyIdAndName(List<Long> companyIds, String institutionProviderName) {
        return null;
    }

    @Override
    public List<Long> getInstitutionProviderIdsByCompanyId(Long companyId) {
        return null;
    }

    @Override
    public String getMajorLevelIdStringByCourseId(Long id) {
        return null;
    }

    @Override
    public String getTypeIdStringByCourseId(Long id) {
        return null;
    }

    @Override
    public Result<List<BaseSelectEntity>> getInstitutionProviderChannelSelect() {
        return null;
    }

    @Override
    public Result<List<Long>> getInstitutionProviderChannelIdsByName(String channelName) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Map<Long, Long> getInstitutionRpaMappingRelation(Set<Long> institutionIds) {
        return null;
    }

    @Override
    public Result<String> getCountryKeyById(Long id) {
        return Result.fail("获取数据失败");
    }
    @Override
    public Result<Map<Long,Long>> getCountryIdsByStateIds(Set<Long> fkAreaStateIds){
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<BaseSelectEntity>> fuzzSearchInstitutionChannel(String keyword, List<Long> companyIds) {
        return null;
    }

    @Override
    public Result<Map<Long, String>> getCountryFullNamesByIds(Set<Long> ids) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<String> getCourseTypeGroupIdsStringByCourseId(String fkInstitutionCourseId) {
        return null;
    }

    @Override
    public Result<String> getCityChnNameById(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<String, String>> getCountryNameByNums(Set<String> cNums) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<Long, String>> getAllAreaRegionChnNames() {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<String> getCurrencyNumByCountryName(String countryName) {
        return null;
    }

    @Override
    public Result<List<InstitutionProviderVo>> getEventRegistrationProviderByIds(List<Long> providerIds) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<EventRegistrationListResultVo> getEventRegistrationProviders(SearchBean<InstitutionProviderDto> page) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<CaseStudyResultsDto> getCaseStudyResults(CaseStudyQueryDto queryVo) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<InstitutionCourseMatchVo>> getInstitutionCourseByNameMatch(String courseName, Long institutionId) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<Long, String>> getCourseGroupTypeNameByIds(Set<Long> ids) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<Long, String>> getCourseGroupTypeNameChnByIds(Set<Long> ids) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<Long, String>> getCourseGroupTypeFullNameByIds( Set<Long> ids){
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Set<Long>> getLikeInstitutionIds(InstitutionApplicationStaticsDto institutionApplicationStaticsDto) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<Long>> getInstitutionIdsByKeyword(@RequestParam("keyword") String keyword){
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<InstitutionApplicationStatisticsVo>> getInstitutionDtoList(InstitutionApplicationStaticsQueryBo staticsQueryBo) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<String> getAreaCityNameByIds(Set<Long> ids) {
        return null;
    }

    @Override
    public Result<String> getAreaStateNameByIds(Set<Long> ids) {
        return null;
    }

    @Override
    public List<BaseSelectEntity> getInstitutionSelectByCompanyId(Long companyId) {
        return null;
    }

    @Override
    public List<BaseSelectEntity> getInstitutionProviderByTargetName(String targetName) {
        return null;
    }

    @Override
    public List<BaseSelectEntity> getInstitutionProviderAccountList(Long fkTargetId) {
        return null;
    }

    @Override
    public String getInstitutionProviderAccountById(Long fkBankAccountId) {
        return null;
    }

    @Override
    public Long addNews(NewsDto newsDto) {
        return null;
    }

    @Override
    public List<NewsTypeVo> getNewsTypes() {
        return null;
    }

    @Override
    public List<AreaCountryVo> getAllCountry() {
        return null;
    }

    @Override
    public List<MediaAndAttachedVo> addNewsMedia(List<MediaAndAttachedDto> mediaAttachedVos) {
        return null;
    }

    @Override
    public Boolean checkNews(NewsDto newsDto) {
        return true;
    }

    @Override
    public Result<Map<Long, String>> getInstitutionChannelProviderNamesByIds(Set<Long> providerIds) {
        return null;
    }

    @Override
    public List<AreaCountryVo> getCountryByPublicLevel(Integer publicLevel) {
        return null;
    }

    @Override
    public InstitutionProviderVo getInstitutionProviderById(Long fkInstitutionProviderId) {
        return null;
    }

    @Override
    public Set<Long>  getInstitutionProviderByInstitution(Long institutionId) {
        return null;
    }

    @Override
    public String getContractNewByProviderId(Long fkInstitutionProviderId) {
        return null;
    }

    @Override
    public InstitutionProviderContractReminderVo getContractExpiredByProviderId(Long contractId) {
        return null;
    }

    @Override
    public Result<Map<Long, AreaCountryVo>> getCountryDtoMapByIds(Set<Long> countryIds) {
        return null;
    }

    @Override
    public Result<Map<Long, InstitutionProviderVo>> getInstitutionProviderMapByIds(Set<Long> institutionProviderIds) {
        return null;
    }

    @Override
    public Result<Map<Long, InstitutionVo>> getInstitutionDtoMapByIds(Set<Long> institutionIdSet) {
        return null;
    }

    @Override
    public Result<Map<Long, InstitutionVo>> getInstitutionDtoByIds(Set<Long> institutionIdSet) {
        return null;
    }

    @Override
    public Map<Long, AreaRegionVo> getRegionMapByStateIds(List<Long> stateIds) {
        return null;
    }

    @Override
    public Map<Long, String> getAreaRegionNameByIds(Set<Long> fkAreaRegionIds) {
        return null;
    }

    @Override
    public Result<AreaRegionVo> findAreaRegionById(Long id) {
        return null;
    }

    @Override
    public Result<List<InstitutionProviderInstitutionVo>> getInstitutionByProvider(Set<Long> fkCompanyId, Set<Long> fkInstitutionProviderIds) {
        return null;
    }

    @Override
    public Result<List<InstitutionVo>> getInstitutionByCountryIds(Set<Long> fkAreaCountryIds) {
        return null;
    }

    @Override
    public void batchTranslationInstitutionAndCourseInfo() {

    }


    @Override
    public Result<Map<Long, String>> getCourseEnNameByIds(Set<Long> courseIds) {
        return null;
    }

    @Override
    public Result<List<InstitutionVo>> getMpsInstitutionProviderList(Long fkProviderId) {
        return null;
    }

    @Override
    public Result<Map<String, Long>> getCountryIdByKeys(Set<String> keys) {
        return null;
    }

    @Override
    public Result<NewsEmailTmpeleteVo> getNewsEmailTmpelete(Long newsId, Integer sendType){
        return null;
    }

    @Override
    public Result<List<InstitutionAppFeeVo>> getAppFees(String fkTableName, Long fkTableId) {
        return null;
    }

    @Override
    public Result<ResponseBo> getInstitutionByInstitutionPermissionGroup(SearchBean<InstitutionQueryDto> institutionQueryDto) {
        return null;
    }

    @Override
    public Result<List<String>> getContractApplyCountryByContractId(Long id) {
        return null;
    }

    @Override
    public List<AreaCountryVo> getCountryByPublicLevelBySummit(Integer publicLevel) {
        return null;
    }


}

package com.get.institutioncenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @DATE: 2021/1/15
 * @TIME: 15:22
 * @Description:
 **/
@Data
public class InstitutionZoneDto extends BaseVoEntity {
    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    private Long fkInstitutionId;

    /**
     * 校区名称
     */
    @ApiModelProperty(value = "校区名称")
    private String name;

    /**
     * 校区中文名称
     */
    @ApiModelProperty(value = "校区中文名称")
    private String nameChn;

    /**
     * 简介
     */
    @ApiModelProperty(value = "简介")
    private String description;

    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;
    /**
     * 国家Id
     */
    @ApiModelProperty(value = "国家Id")
    private Long fkAreaCountryId;

    /**
     * 州省Id
     */
    @ApiModelProperty(value = "州省Id")
    private Long fkAreaStateId;

    /**
     * 城市Id
     */
    @ApiModelProperty(value = "城市Id")
    private Long fkAreaCityId;

}

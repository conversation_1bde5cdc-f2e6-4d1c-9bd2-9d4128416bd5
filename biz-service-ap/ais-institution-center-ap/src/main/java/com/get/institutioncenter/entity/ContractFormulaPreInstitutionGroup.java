package com.get.institutioncenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("r_contract_formula_pre_institution_group")
public class ContractFormulaPreInstitutionGroup extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 合同公式Id
     */
    @ApiModelProperty(value = "合同公式Id")
    @Column(name = "fk_contract_formula_id")
    private Long fkContractFormulaId;
    /**
     * 学校集团Id
     */
    @ApiModelProperty(value = "学校集团Id(前置集团)")
    @Column(name = "fk_institution_group_id")
    private Long fkInstitutionGroupId;
}
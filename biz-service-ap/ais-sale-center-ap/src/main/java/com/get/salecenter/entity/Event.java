package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("m_event")
public class Event extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;
    /**
     * 活动类型Id
     */
    @ApiModelProperty(value = "活动类型Id")
    @Column(name = "fk_event_type_id")
    private Long fkEventTypeId;
    /**
     * 活动编号
     */
    @ApiModelProperty(value = "活动编号")
    @Column(name = "num")
    private String num;
    /**
     * 活动时间
     */
    @ApiModelProperty(value = "活动时间")
    @Column(name = "event_time")
    @UpdateWithNull
    private Date eventTime;
    /**
     * 活动结束时间
     */
    @ApiModelProperty(value = "活动结束时间")
    @Column(name = "event_time_end")
    @UpdateWithNull
    private Date eventTimeEnd;
    /**
     * 活动主题
     */
    @ApiModelProperty(value = "活动主题")
    @Column(name = "event_theme")
    private String eventTheme;
    /**
     * 活动目标对象
     */
    @ApiModelProperty(value = "活动目标对象")
    @Column(name = "event_target")
    private String eventTarget;
    /**
     * 活动目标对象负责人
     */
    @ApiModelProperty(value = "活动目标对象负责人")
    @Column(name = "event_target_leader")
    private String eventTargetLeader;
    /**
     * 活动举办国家Id
     */
    @ApiModelProperty(value = "活动举办国家Id")
    @Column(name = "fk_area_country_id_hold")
    @UpdateWithNull
    private Long fkAreaCountryIdHold;
    /**
     * 活动举办州省Id
     */
    @ApiModelProperty(value = "活动举办州省Id")
    @Column(name = "fk_area_state_id_hold")
    @UpdateWithNull
    private Long fkAreaStateIdHold;
    /**
     * 活动举办城市Id
     */
    @ApiModelProperty(value = "活动举办城市Id")
    @Column(name = "fk_area_city_id_hold")
    @UpdateWithNull
    private Long fkAreaCityIdHold;
    /**
     * 员工Id（负责人1）
     */
    @ApiModelProperty(value = "员工Id（负责人1）")
    @Column(name = "fk_staff_id_leader1")
    private Long fkStaffIdLeader1;
    /**
     * 员工Id（负责人2）
     */
    @ApiModelProperty(value = "员工Id（负责人2）")
    @Column(name = "fk_staff_id_leader2")
    private Long fkStaffIdLeader2;
    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    @Column(name = "fk_currency_type_num")
    private String fkCurrencyTypeNum;
    /**
     * BD预算场地费用
     */
    @ApiModelProperty(value = "BD预算场地费用")
    @Column(name = "bd_venue_amount")
    private BigDecimal bdVenueAmount;

    /**
     * BD预算餐饮费用
     */
    @ApiModelProperty(value = "BD预算餐饮费用")
    @Column(name = "bd_food_amount")
    private BigDecimal bdFoodAmount;

    /**
     * BD预算奖品费用
     */
    @ApiModelProperty(value = "BD预算奖品费用")
    @Column(name = "bd_prize_amount")
    private BigDecimal bdPrizeAmount;

    /**
     * BD预算其他费用
     */
    @ApiModelProperty(value = "BD预算其他费用")
    @Column(name = "bd_other_amount")
    private BigDecimal bdOtherAmount;

    /**
     * 预算金额
     */
    @ApiModelProperty(value = "预算金额")
    @Column(name = "budget_amount")
    private BigDecimal budgetAmount;
    /**
     * 实际金额
     */
    @ApiModelProperty(value = "实际金额")
    @Column(name = "actual_amount")
    private BigDecimal actualAmount;
    /**
     * 预算人数
     */
    @ApiModelProperty(value = "预算人数")
    @Column(name = "budget_count")
    private Integer budgetCount;
    /**
     * 参加人数
     */
    @ApiModelProperty(value = "参加人数")
    @Column(name = "attended_count")
    private Integer attendedCount;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;
    /**
     * 状态：0计划/1结束/2取消
     */
    @ApiModelProperty(value = "状态：0计划/1结束/2取消")
    @Column(name = "status")
    private Integer status;

    @ApiModelProperty(value = "公开对象：0不公开/1公开/2学生/3代理，支持多选")
    @Column(name = "public_level")
    private String publicLevel;
}
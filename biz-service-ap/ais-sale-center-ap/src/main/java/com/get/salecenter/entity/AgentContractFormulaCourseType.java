package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("r_agent_contract_formula_course_type")
public class AgentContractFormulaCourseType extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 学生代理合同公式Id
     */
    @ApiModelProperty(value = "学生代理合同公式Id")
    @Column(name = "fk_agent_contract_formula_id")
    private Long fkAgentContractFormulaId;
    /**
     * 课程类型Id
     */
    @ApiModelProperty(value = "课程类型Id")
    @Column(name = "fk_course_type_id")
    private Long fkCourseTypeId;
}
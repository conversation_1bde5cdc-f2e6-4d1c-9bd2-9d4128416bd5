package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("r_user_superior")
public class NewIssueUserSuperior extends BaseEntity implements Serializable {
    /**
     * 用户Id
     */
    @ApiModelProperty(value = "用户Id")
    @Column(name = "fk_user_id")
    private Long fkUserId;

    /**
     * 上司Id
     */
    @ApiModelProperty(value = "上司Id")
    @Column(name = "fk_user_superior_id")
    private Long fkUserSuperiorId;

    @ApiModelProperty("公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;

    private static final long serialVersionUID = 1L;
}
package com.get.salecenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 应付计划取消预付按钮
 *
 * <AUTHOR>
 * @date 2022/3/22 11:06
 */
@Data
public class PrepaymentButtonDto {

    @ApiModelProperty(value = "应付计划ids")
    @NotNull(message = "应付计划ids不能为空", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    private List<Long> payablePlanIds;

    @ApiModelProperty(value = "预付金额百分比")
    @NotNull(message = "预付金额百分比不能为空", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    private BigDecimal percentage;
}

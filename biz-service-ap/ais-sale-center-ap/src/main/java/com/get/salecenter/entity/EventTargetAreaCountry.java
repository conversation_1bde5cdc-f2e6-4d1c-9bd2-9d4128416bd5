package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("r_event_target_area_country")
public class EventTargetAreaCountry extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 活动Id
     */
    @ApiModelProperty(value = "活动Id")
    @Column(name = "fk_event_id")
    private Long fkEventId;
    /**
     * 活动目标对象国家Id
     */
    @ApiModelProperty(value = "活动目标对象国家Id")
    @Column(name = "fk_area_country_id")
    private Long fkAreaCountryId;
}
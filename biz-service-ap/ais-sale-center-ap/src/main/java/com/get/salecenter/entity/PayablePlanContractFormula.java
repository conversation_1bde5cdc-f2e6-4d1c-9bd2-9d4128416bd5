package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("r_payable_plan_contract_formula")
public class PayablePlanContractFormula extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 应付计划Id
     */
    @ApiModelProperty(value = "应付计划Id")
    @Column(name = "fk_payable_plan_id")
    private Long fkPayablePlanId;
    /**
     * 合同公式Id
     */
    @ApiModelProperty(value = "合同公式Id")
    @Column(name = "fk_contract_formula_id")
    private Long fkContractFormulaId;
}
package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import java.io.Serializable;
import lombok.Data;

/**
 * 
 * @TableName r_student_offer_item_uuid
 */
@TableName(value ="r_student_offer_item_uuid")
@Data
public class RStudentOfferItemUuid extends BaseEntity implements Serializable {
    /**
     * 学生申请计划Id
     */
    private Long fkStudentOfferItemId;

    /**
     * 学生申请计划UUID
     */
    private String fkStudentOfferItemUuid;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
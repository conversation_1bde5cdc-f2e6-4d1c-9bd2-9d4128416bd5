package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.ibatis.type.Alias;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-04
 */
@Data
@Alias("SaleTranslationMapping")
@TableName("s_translation_mapping")
@ApiModel(value="TranslationMapping对象", description="")
public class TranslationMapping extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "表名")
    private String fkTableName;

    @ApiModelProperty(value = "字段名")
    private String fkColumnName;

    @ApiModelProperty(value = "字段标题名")
    private String inputTitle;

    @ApiModelProperty(value = "输入类型：0单行/1多行/2富文本")
    private Integer inputType;

    @ApiModelProperty(value = "最大字符限制数")
    private Integer maxLength;

    @ApiModelProperty(value = "排序，数字由小到大排列")
    private Integer viewOrder;

    @ApiModelProperty(value = "是否激活：0否/1是")
    private Boolean isActive;

}

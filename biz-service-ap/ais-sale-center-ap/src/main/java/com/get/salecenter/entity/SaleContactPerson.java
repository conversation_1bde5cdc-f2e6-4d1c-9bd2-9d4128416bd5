package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;

@Data
@TableName("s_contact_person")
public class SaleContactPerson extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    @Column(name = "fk_table_name")
    private String fkTableName;

    /**
     * 表Id
     */
    @ApiModelProperty(value = "表Id")
    @Column(name = "fk_table_id")
    private Long fkTableId;

    /**
     * 联系人类型Key，多值逗号隔开
     */
    @ApiModelProperty(value = "联系人类型Key，多值逗号隔开")
    @Column(name = "fk_contact_person_type_key")
    private String fkContactPersonTypeKey;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @Column(name = "name")
    private String name;

    /**
     * 性别：0女/1男
     */
    @ApiModelProperty(value = "性别：0女/1男")
    @Column(name = "gender")
    private Integer gender;

    /**
     * 生日
     */
    @ApiModelProperty(value = "生日")
    @Column(name = "birthday")
    private Date birthday;

    /**
     * 公司名
     */
    @ApiModelProperty(value = "公司名")
    @Column(name = "company")
    private String company;

    /**
     * 部门
     */
    @ApiModelProperty(value = "部门")
    @Column(name = "department")
    private String department;

    /**
     * 职位
     */
    @ApiModelProperty(value = "职位")
    @Column(name = "title")
    private String title;

    @ApiModelProperty(value = "手机区号")
    @Column(name = "mobile_area_code")
    private String mobileAreaCode;

    /**
     * 移动电话
     */
    @ApiModelProperty(value = "移动电话")
    @Column(name = "mobile")
    private String mobile;


    @ApiModelProperty(value = "电话区号")
    @Column(name = "tel_area_code")
    private String telAreaCode;

    /**
     * 电话
     */
    @ApiModelProperty(value = "电话")
    @Column(name = "tel")
    private String tel;

    /**
     * Email
     */
    @ApiModelProperty(value = "Email")
    @Column(name = "email")
    private String email;

    /**
     * 是否佣金邮件地址
     */
    @ApiModelProperty(value = "是否佣金邮件地址")
    @Column(name = "is_commission_email")
    private Boolean isCommissionEmail;

    /**
     * 是否新闻邮件地址
     */
    @ApiModelProperty(value = "是否新闻邮件地址")
    @Column(name = "is_news_email")
    private Boolean isNewsEmail;

    /**
     * 新闻邮件关系国家Ids串，英文逗号隔开：1,2,3
     */
    @ApiModelProperty(value = "新闻邮件关系国家Ids串，英文逗号隔开：1,2,3")
    @Column(name = "fk_area_country_ids_news")
    private String fkAreaCountryIdsNews;

    /**
     * qq
     */
    @ApiModelProperty(value = "qq")
    @Column(name = "qq")
    private String qq;

    /**
     * 微信号
     */
    @ApiModelProperty(value = "微信号")
    @Column(name = "wechat")
    private String wechat;

    /**
     * whatsapp
     */
    @ApiModelProperty(value = "whatsapp")
    @Column(name = "whatsapp")
    private String whatsapp;

    /**
     * 联系地址
     */
    @ApiModelProperty(value = "联系地址")
    @Column(name = "contact_address")
    private String contactAddress;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;
}
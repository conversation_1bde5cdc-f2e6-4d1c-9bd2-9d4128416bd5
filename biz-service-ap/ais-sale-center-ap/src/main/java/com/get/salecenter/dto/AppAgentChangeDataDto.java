package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * @author: Hardy
 * @create: 2022/11/17 16:22
 * @verison: 1.0
 * @description:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AppAgentChangeDataDto extends BaseVoEntity {

    /**
     * <p> Java类型:String &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 数据库类型:text &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释:变更声明内容 &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @ApiModelProperty(value = "变更声明内容")
    @JsonProperty("changeStatement")
    @NotBlank(message = "变更声明内容")
    private String changeStatement;

}

package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("m_event_incentive")
public class EventIncentive extends BaseEntity implements Serializable {
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;

    /**
     * 学校提供商Id
     */
    @ApiModelProperty(value = "学校提供商Id")
    @Column(name = "fk_institution_provider_id")
    private Long fkInstitutionProviderId;

    /**
     * 活动编号
     */
    @ApiModelProperty(value = "活动编号")
    @Column(name = "num")
    private String num;

    /**
     * 收到奖励时间
     */
    @UpdateWithNull
    @ApiModelProperty(value = "收到奖励时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "receiving_reward_time")
    private Date receivingRewardTime;

    /**
     * 实际宣传时间
     */
    @UpdateWithNull
    @ApiModelProperty(value = "实际宣传时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "actual_publicity_time")
    private Date actualPublicityTime;

    /**
     * 活动开始时间
     */
    @UpdateWithNull
    @ApiModelProperty(value = "活动开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "event_start_time")
    private Date eventStartTime;

    /**
     * 活动结束时间
     */
    @UpdateWithNull
    @ApiModelProperty(value = "活动结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "event_end_time")
    private Date eventEndTime;

    /**
     * 符合奖励intake
     */
    @ApiModelProperty(value = "符合奖励intake")
    @Column(name = "accord_with_incentive_intake")
    private String accordWithIncentiveIntake;

    /**
     * 激励政策
     */
    @ApiModelProperty(value = "激励政策")
    @Column(name = "incentive_policy")
    private String incentivePolicy;

    /**
     * 建议核对时间
     */
    @UpdateWithNull
    @ApiModelProperty(value = "建议核对时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "suggest_check_time")
    private Date suggestCheckTime;

    /**
     * 预计完成学生数
     */
    @ApiModelProperty(value = "预计完成学生数")
    @Column(name = "expect_target_count")
    private Integer expectTargetCount;

    /**
     * 实际完成学生数
     */
    @UpdateWithNull
    @ApiModelProperty(value = "实际完成学生数")
    @Column(name = "actual_target_count")
    private Integer actualTargetCount;

    /**
     * 币种编号
     */
    @UpdateWithNull
    @ApiModelProperty(value = "币种编号")
    @Column(name = "fk_currency_type_num")
    private String fkCurrencyTypeNum;

    /**
     * 实际支付奖励金额
     */
    @UpdateWithNull
    @ApiModelProperty(value = "实际支付奖励金额")
    @Column(name = "actual_pay_amount")
    private BigDecimal actualPayAmount;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;

    /**
     * 状态：0计划/1结束/2取消/3延期
     */
    @ApiModelProperty(value = "状态：0计划/1结束/2取消/3延期")
    @Column(name = "status")
    private Integer status;

    @ApiModelProperty("活动标题")
    @Column(name = "event_title")
    private String eventTitle;

    @ApiModelProperty("是否下发奖励：0否/1是")
    @Column(name = "is_distributed")
    private Boolean isDistributed;

    @ApiModelProperty("公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
    @Column(name = "public_level")
    private String publicLevel;

    private static final long serialVersionUID = 1L;
}
package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * @author: Sea
 * @create: 2020/7/3 10:52
 * @verison: 1.0
 * @description:
 */
@Data
public class ConventionRegistrationDto extends BaseVoEntity {
    /**
     * 峰会Id
     */
    @NotNull(message = "峰会Id不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "峰会Id", required = true)
    private Long fkConventionId;

    /**
     * 学校提供商Id（费用归口）
     */
    @ApiModelProperty(value = "学校提供商Id（费用归口）")
    private Long fkInstitutionProviderId;

    /**
     * 活动费用绑定Id
     */
    @ApiModelProperty(value = "活动费用绑定Id")
    private Long fkEventCostId;

    /**
     * 币种编号
     */
    @NotBlank(message = "币种编号不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "币种编号", required = true)
    private String fkCurrencyTypeNum;

    /**
     * 报名费
     */
    @NotNull(message = "报名费不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "报名费", required = true)
    private BigDecimal registrationFee;

    /**
     * 费用摘要
     */
    @ApiModelProperty(value = "费用摘要")
    private String summaryFee;

    /**
     * 机构名称
     */
    @ApiModelProperty(value = "机构名称")
    private String providerName;

    /**
     * 展位名称
     */
    @NotBlank(message = "展位名称不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "展位名称", required = true)
    private String boothName;

    /**
     * 展位编号
     */
    @ApiModelProperty(value = "展位编号")
    private String boothNum;

    /**
     * 联系人姓名
     */
    @ApiModelProperty(value = "联系人姓名")
    private String contactPersonName;

    /**
     * 联系人姓名（中文）
     */
    @ApiModelProperty(value = "联系人姓名（中文）")
    private String contactPersonNameChn;

    /**
     * 联系人电邮
     */
    @ApiModelProperty(value = "联系人电邮")
    private String contactEmail;

    /**
     * 联系人电话
     */
    @ApiModelProperty(value = "联系人电话")
    private String contactTel;

    /**
     * 快递信息
     */
    @ApiModelProperty(value = "快递信息,允许多个快递信息，格式：[中通快递][xxxxxxxxxxx][5];[中通快递][xxxxxxxxxxx][2];")
    private String expressInfo;

    /**
     * 回执码，8位数字随机数
     */
    @ApiModelProperty(value = "回执码，8位数字随机数")
    private String receiptCode;

    /**
     * 状态：0新建/1已确认/2已收款
     */
    @ApiModelProperty(value = "状态：0新建/1已确认/2已收款")
    private Integer status;

    /**
     * 报名费（折合人民币）
     */
    @ApiModelProperty(value = "报名费（折合人民币）")
    private BigDecimal registrationFeeCny;

    //自定义内容
    /**
     * 学校提供商名称（费用归口）
     */
    @ApiModelProperty(value = "学校提供商名称（费用归口）")
    private String institutionProviderName;

    /**
     * 报名包含的国家编号
     */
    @ApiModelProperty(value = "报名包含的国家编号")
    private List<String> cNums;

    /**
     * 学校提供商ids
     */
    @ApiModelProperty(value = "学校提供商ids")
    private List<Long> institutionProviderIds;

    /**
     * 国家id
     */
    @ApiModelProperty(value = "国家")
    private Long fkAreaCountryId;

    /**
     * 是否有参会人
     */
    @ApiModelProperty(value = "是否有参会人")
    private Boolean hasConventionPerson;

    /**
     * 回执码list
     */
    @ApiModelProperty(value = "回执码list")
    private Set<String> receiptCodes;

    /**
     * 排序条件
     */
    @ApiModelProperty(value = "排序条件")
    private String orderCondition;

    @ApiModelProperty(value = "语言")
    private String language;

    @ApiModelProperty(value = "邮箱验证")
    private Integer isVerified;


}

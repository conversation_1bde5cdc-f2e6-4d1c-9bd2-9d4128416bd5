package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.Set;

/**
 * BD学生统计对比表 VO
 *
 * <AUTHOR>
 * @date 2022/12/27 11:51
 */
@Data
public class AgentAnnualSummaryDto {

    @ApiModelProperty(value = "公司id")
    @NotNull(message = "公司id不能为空")
    private Long fkCompanyId;

    @ApiModelProperty("统计类型 1：按代理创建时间统计 2：按首次申请时间统计")
    @NotNull(message = "统计类型不能为空")
    private Integer statisticalType;

    @ApiModelProperty(value = "1：代理年度总表对比统计   2：代理时间区间对比统计")
    @NotNull(message = "statisticalFrom 不能为空")
    private Integer statisticalFrom;

    @ApiModelProperty("创建时间(开始)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date beginTime;

    @ApiModelProperty("创建时间(结束)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty("同期比对年数")
    @NotNull(message = "同期比对年数 不能为空")
    private Integer comparisonYears;

    @ApiModelProperty("是否区分大区 true:是  false:不区分")
    private Boolean isDistinguishRegionFlag = false;

    @ApiModelProperty("小计显示模式 true:隐藏小计  false:不隐藏小计")
    private Boolean displayModeFlag = true;

    @ApiModelProperty("总计显示模式 true:隐藏总计  false:不隐藏总计")
    private Boolean totalDisplayModeFlag = true;


    @ApiModelProperty(value = "代理所在国家Id")
    private Long fkAreaCountryId;

    @ApiModelProperty(value = "代理所在州省Id LIst")
    private Set<Long> fkAreaStateIdList;

    @ApiModelProperty(value = "代理所在城市Id LIst")
    private Set<Long> fkAreaCityIdList;

    @ApiModelProperty(value = "bd大区Id List")
    private Set<Long> fkAreaRegionIdList;


    @ApiModelProperty(value = "绑定BD id")
    private Set<Long> fkBdIds;


    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date jumpBeginTime;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date jumpEndTime;

    @ApiModelProperty("跳转类型 1:代理数量/通过审核数  2：审核中数 3：拒绝数 4：合同未返回数数")
    private Integer jumpType;

}

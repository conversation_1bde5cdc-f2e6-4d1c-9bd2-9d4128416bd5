package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
public class ReceivableUpdateDto {


    @ApiModelProperty(value = "费率%")
    private BigDecimal commissionRate;

    @ApiModelProperty(value = "应收ids")
    private List<Long> receivableIds;

    @ApiModelProperty("渠道费率")
    private BigDecimal natRate;

    @ApiModelProperty(value = "学费金额")
    private BigDecimal tuitionAmount;

    @ApiModelProperty(value = "是否同步到应付计划")
    private Boolean syn = false;

    @ApiModelProperty(value = "计划收款时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date receivablePlanDate;

    @ApiModelProperty(value = "定额金额")
    private BigDecimal fixedAmount;

    //费率%
    @ApiModelProperty(value = "奖金比例")
    private BigDecimal bonusCommissionRate;

    //定额金额
    @ApiModelProperty(value = "奖金固定金额")
    private BigDecimal bonusFixedAmount;


}

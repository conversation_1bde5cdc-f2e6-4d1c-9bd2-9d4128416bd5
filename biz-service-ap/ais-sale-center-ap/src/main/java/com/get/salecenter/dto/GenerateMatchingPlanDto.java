package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 生成应收应付
 *
 * <AUTHOR>
 * @date 2022/1/24 17:18
 */
@Data
public class GenerateMatchingPlanDto {

    /**
     * 学习计划id
     */
    @ApiModelProperty(value = "学习计划id")
    @NotNull(message = "学习计划id不能为空")
    private Long offerItemId;

    /**
     * 合同公式Id
     */
    @ApiModelProperty(value = "合同公式Id")
    private Long fkContractFormulaId;

    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    @Column(name = "fk_currency_type_num")
    @NotBlank(message = "币种编号不能为空")
    private String fkCurrencyTypeNum;

    /**
     * 佣金比例
     */
    @ApiModelProperty(value = "佣金比例")
    @Column(name = "commission_rate")
    @NotNull(message = "佣金比例不能为空")
    private BigDecimal commissionRate;

    /**
     * 代理佣金币种编号
     */
    @ApiModelProperty(value = "代理佣金币种编号")
    @Column(name = "fk_currency_type_num_ag")
    private String fkCurrencyTypeNumAg;

    /**
     * 代理佣金比例
     */
    @ApiModelProperty(value = "代理佣金比例")
    @Column(name = "commission_rate_ag")
    @NotNull(message = "代理佣金比例不能为空")
    private BigDecimal commissionRateAg;

    /**
     * 固定金额
     */
    @ApiModelProperty(value = "固定金额")
    @Column(name = "fixed_amount")
    @NotNull(message = "固定金额不能为空")
    private BigDecimal fixedAmount;

    /**
     * 代理固定金额
     */
    @ApiModelProperty(value = "代理固定金额")
    @Column(name = "fixed_amount_ag")
    @NotNull(message = "代理固定金额不能为空")
    private BigDecimal fixedAmountAg;

    /**
     * 佣金上限
     */
    @ApiModelProperty(value = "佣金上限")
    @Column(name = "limit_amount")
    @NotNull(message = "佣金上限不能为空")
    private BigDecimal limitAmount;

    /**
     * 代理佣金上限
     */
    @ApiModelProperty(value = "代理佣金上限")
    @Column(name = "limit_amount_ag")
    @NotNull(message = "代理佣金上限不能为空")
    private BigDecimal limitAmountAg;

    /**
     * 佣金上限(总)
     */
    @ApiModelProperty(value = "佣金上限(总)")
    private BigDecimal totalLimitAmount;


    /**
     * 代理佣金上限(总)
     */
    @ApiModelProperty(value = "代理佣金上限(总)")
    private BigDecimal totalLimitAmountAg;

    /**
     * 公司id
     */
    @ApiModelProperty(value = "公司id")
    private Long fkCompanyId;
}

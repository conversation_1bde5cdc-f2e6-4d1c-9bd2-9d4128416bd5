package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("r_convention_sponsor_sponsor_fee")
public class ConventionSponsorSponsorFee extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 峰会赞助商Id
     */
    @ApiModelProperty(value = "峰会赞助商Id")
    @Column(name = "fk_convention_sponsor_id")
    private Long fkConventionSponsorId;
    /**
     * 峰会赞助费用类型Id
     */
    @ApiModelProperty(value = "峰会赞助费用类型Id")
    @Column(name = "fk_convention_sponsor_fee_id")
    private Long fkConventionSponsorFeeId;

    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;

}
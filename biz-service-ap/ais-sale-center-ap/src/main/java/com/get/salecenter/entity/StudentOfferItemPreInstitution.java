package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("r_student_offer_item_pre_institution")
public class StudentOfferItemPreInstitution extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 学生申请方案项目Id
     */
    @ApiModelProperty(value = "学生申请方案项目Id")
    @Column(name = "fk_student_offer_item_id")
    private Long fkStudentOfferItemId;
    /**
     * 学校Id(前置学校)
     */
    @ApiModelProperty(value = "学校Id(前置学校)")
    @Column(name = "fk_institution_id")
    private Long fkInstitutionId;
}
package com.get.salecenter.entity;

import com.get.core.mybatis.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-13
 */
@Data
@TableName("u_student_offer_notice_template")
@ApiModel(value="StudentOfferNoticeTemplate对象", description="")
public class StudentOfferNoticeTemplate extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "邮件模板类型Key，必填")
    private String typeKey;

    @ApiModelProperty(value = "模板标题")
    private String title;

    @ApiModelProperty(value = "电邮模板")
    private String emailTemplate;

    @ApiModelProperty(value = "备注")
    private String remark;
}

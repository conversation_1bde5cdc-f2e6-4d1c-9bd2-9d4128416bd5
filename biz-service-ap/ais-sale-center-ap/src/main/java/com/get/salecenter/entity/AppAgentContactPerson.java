package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("m_app_agent_contact_person")
public class AppAgentContactPerson extends BaseEntity implements Serializable {
    /**
     * 学生代理申请Id
     */
    @ApiModelProperty(value = "学生代理申请Id")
    @Column(name = "fk_app_agent_id")
    private Long fkAppAgentId;

    /**
     * 联系人类型Key，多值逗号隔开
     */
    @ApiModelProperty(value = "联系人类型Key，多值逗号隔开")
    @Column(name = "fk_contact_person_type_key")
    private String fkContactPersonTypeKey;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @Column(name = "name")
    private String name;

    /**
     * 性别：0女/1男
     */
    @ApiModelProperty(value = "性别：0女/1男")
    @Column(name = "gender")
    private Integer gender;

    /**
     * 部门
     */
    @ApiModelProperty(value = "部门")
    @Column(name = "department")
    private String department;

    /**
     * 职位
     */
    @ApiModelProperty(value = "职位")
    @Column(name = "title")
    private String title;

    @ApiModelProperty(value = "手机区号")
    @Column(name = "mobile_area_code")
    private String mobileAreaCode;

    /**
     * 移动电话
     */
    @ApiModelProperty(value = "移动电话")
    @Column(name = "mobile")
    private String mobile;

    /**
     * Email
     */
    @ApiModelProperty(value = "Email")
    @Column(name = "email")
    private String email;

    /**
     * 是否佣金邮件地址
     */
    @ApiModelProperty(value = "是否佣金邮件地址")
    @Column(name = "is_commission_email")
    private Boolean isCommissionEmail;

    /**
     * 是否新闻邮件地址
     */
    @ApiModelProperty(value = "是否新闻邮件地址")
    @Column(name = "is_news_email")
    private Boolean isNewsEmail;

    /**
     * 新闻邮件关系国家Ids串，英文逗号隔开：1,2,3
     */
    @ApiModelProperty(value = "新闻邮件关系国家Ids串，英文逗号隔开：1,2,3")
    @Column(name = "fk_area_country_ids_news")
    private String fkAreaCountryIdsNews;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;

    /**
     * <p> Java类型:Long &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 数据库类型:bigint(19) &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释:转化到联系人Id &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @ApiModelProperty(value = "转化到联系人Id")
    @JsonProperty("fkContactPersonId")
    @TableField("fk_contact_person_id")
    private Long fkContactPersonId;

    private static final long serialVersionUID = 1L;
}
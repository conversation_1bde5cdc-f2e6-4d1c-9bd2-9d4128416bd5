package com.get.salecenter.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 合同模板模式枚举类
 * 
 * <AUTHOR>
 * @date 2025-01-11
 */
@Getter
@AllArgsConstructor
public enum ContractTemplateModeEnum {

    /**
     * MPS主合同
     */
    MPS_MAIN_CONTRACT(0, "MPS主合同"),

    /**
     * PMP主合同
     */
    PMP_MAIN_CONTRACT(1, "PMP主合同"),

    /**
     * PMP附加合同
     */
    PMP_ADDITIONAL_CONTRACT(2, "PMP附加合同");

    /**
     * 合同模板代码
     */
    private final Integer code;

    /**
     * 合同模板描述
     */
    private final String msg;

    /**
     * 合同模板模式映射Map
     */
    private static final Map<Integer, ContractTemplateModeEnum> CONTRACT_TEMPLATE_MODE_MAP = new HashMap<>();

    static {
        for (ContractTemplateModeEnum enumItem : ContractTemplateModeEnum.values()) {
            CONTRACT_TEMPLATE_MODE_MAP.put(enumItem.getCode(), enumItem);
        }
    }

    /**
     * 根据代码获取合同模板模式枚举
     *
     * @param code 合同模板代码
     * @return 合同模板模式枚举实例，如果不存在则返回null
     */
    public static ContractTemplateModeEnum getContractTemplateModeByCode(Integer code) {
        return CONTRACT_TEMPLATE_MODE_MAP.get(code);
    }

}
package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("m_student_insurance")
public class StudentInsurance extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 学生Id
     */
    @ApiModelProperty(value = "学生Id")
    @Column(name = "fk_student_id")
    private Long fkStudentId;
    /**
     * 代理Id（业绩绑定）
     */
    @ApiModelProperty(value = "代理Id（业绩绑定）")
    @Column(name = "fk_agent_id")
    private Long fkAgentId;
    /**
     * 业务渠道Id/佣金合同方Id
     */
    @ApiModelProperty(value = "业务渠道Id/佣金合同方Id")
    @Column(name = "fk_business_channel_id")
    private Long fkBusinessChannelId;
    /**
     * 业务提供商Id
     */
    @ApiModelProperty(value = "业务提供商Id")
    @Column(name = "fk_business_provider_id")
    private Long fkBusinessProviderId;
    /**
     * 购买产品
     */
    @ApiModelProperty(value = "购买产品")
    @Column(name = "business_provider_product")
    private String businessProviderProduct;
    /**
     * 员工Id（业绩绑定，BD）
     */
    @ApiModelProperty(value = "员工Id（业绩绑定，BD）")
    @Column(name = "fk_staff_id")
    private Long fkStaffId;

    @ApiModelProperty(value = "分类，枚举：0学生保险/1陪读人保险")
    @Column(name = "type")
    private Integer type;
    /**
     * 留学保险编号
     */
    @ApiModelProperty(value = "留学保险编号")
    @Column(name = "num")
    private String num;



    @ApiModelProperty(value = "受保人姓名（中）")
    @Column(name = "insurant_name")
    private String insurantName;

    @ApiModelProperty(value = "受保人姓（英/拼音）")
    @Column(name = "insurant_last_name")
    private String insurantLastName;


    @ApiModelProperty(value = "受保人名（英/拼音）")
    @Column(name = "insurant_first_name")
    private String insurantFirstName;

    @ApiModelProperty(value = "受保人性别")
    @Column(name = "insurant_gender")
    private Integer insurantGender;

    @ApiModelProperty(value = "受保人生日")
    @Column(name = "insurant_birthday")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date insurantBirthday;

    @ApiModelProperty(value = "受保人护照编号")
    @Column(name = "insurant_passport_num")
    private String insurantPassportNum;

    /**
     * 前往国家Id
     */
    @ApiModelProperty(value = "前往国家Id")
    @Column(name = "fk_area_country_id")
    private Long fkAreaCountryId;
    /**
     * 保险单号
     */
    @ApiModelProperty(value = "保险单号")
    @Column(name = "insurance_num")
    private String insuranceNum;
    /**
     * 保单开始时间
     */
    @ApiModelProperty(value = "保单开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "insurance_start_time")
    private Date insuranceStartTime;
    /**
     * 保单结束时间
     */
    @ApiModelProperty(value = "保单结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "insurance_end_time")
    private Date insuranceEndTime;
    /**
     * 保险购买时间
     */
    @ApiModelProperty(value = "保险购买时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "insurance_buy_time")
    private Date insuranceBuyTime;
    /**
     * 购买账户
     */
    @ApiModelProperty(value = "购买账户")
    @Column(name = "buy_account")
    private String buyAccount;
    /**
     * 保险费币种
     */
    @ApiModelProperty(value = "保险费币种")
    @Column(name = "fk_currency_type_num_insurance")
    private String fkCurrencyTypeNumInsurance;
    /**
     * 保险金额
     */
    @ApiModelProperty(value = "保险金额")
    @Column(name = "insurance_amount")
    private BigDecimal insuranceAmount;
    /**
     * 保险金额说明
     */
    @ApiModelProperty(value = "保险金额说明")
    @Column(name = "insurance_amount_note")
    private String insuranceAmountNote;
    /**
     * 支付方式枚举：0飞汇/1易思汇/4信用卡
     */
    @ApiModelProperty(value = "支付方式枚举：0飞汇/1易思汇/4信用卡")
    @Column(name = "payment_method")
    private Integer paymentMethod;
    /**
     * 佣金币种
     */
    @ApiModelProperty(value = "佣金币种")
    @Column(name = "fk_currency_type_num_commission")
    private String fkCurrencyTypeNumCommission;
    /**
     * 收取佣金比例%
     */
    @ApiModelProperty(value = "收取佣金比例%")
    @Column(name = "commission_rate_receivable")
    private BigDecimal commissionRateReceivable;
    /**
     * 支付佣金比例%（代理）
     */
    @ApiModelProperty(value = "支付佣金比例%（代理）")
    @Column(name = "commission_rate_payable")
    private BigDecimal commissionRatePayable;
    /**
     * 固定收取佣金金额
     */
    @ApiModelProperty(value = "固定收取佣金金额")
    @Column(name = "fixed_amount_receivable")
    private BigDecimal fixedAmountReceivable;
    /**
     * 固定支付佣金金额（代理）
     */
    @ApiModelProperty(value = "固定支付佣金金额（代理）")
    @Column(name = "fixed_amount_payable")
    private BigDecimal fixedAmountPayable;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;
    /**
     * 备注
     */
    @ApiModelProperty(value = "是否财务专用：0否/1是")
    @Column(name = "is_hidden")
    private Boolean isHidden;
    /**
     * 状态：0关闭/1打开
     */
    @ApiModelProperty(value = "状态：0关闭/1打开")
    @Column(name = "status")
    private Integer status;
}
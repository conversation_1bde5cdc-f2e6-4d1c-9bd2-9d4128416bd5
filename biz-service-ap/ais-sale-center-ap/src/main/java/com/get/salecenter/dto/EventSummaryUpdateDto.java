package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotNull;

/**
 * @author: Hardy
 * @create: 2022/6/7 15:12
 * @verison: 1.0
 * @description:
 */
@Data
public class EventSummaryUpdateDto extends BaseVoEntity {

    /**
     * 公司Id
     */
    @NotNull(
            message = "公司Id不能为空！",
            groups = {Add.class, Update.class}
    )
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;

    /**
     * 摘要
     */
    @NotNull(
            message = "摘要不能为空！",
            groups = {Add.class, Update.class}
    )

    @ApiModelProperty(value = "摘要")
    @Column(name = "event_summary")
    private String eventSummary;

    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;

  


}

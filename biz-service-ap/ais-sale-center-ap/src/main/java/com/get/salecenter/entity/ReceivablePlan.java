package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("m_receivable_plan")
public class ReceivablePlan extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;

    /**
     * 应收类型关键字，枚举，如：m_student_offer_item
     */
    @ApiModelProperty(value = "应收类型关键字，枚举，如：m_student_offer_item")
    @Column(name = "fk_type_key")
    private String fkTypeKey;
    /**
     * 应收类型对应记录Id，如：m_student_offer_item.id
     */
    @ApiModelProperty(value = "应收类型对应记录Id，如：m_student_offer_item.id")
    @Column(name = "fk_type_target_id")
    private Long fkTypeTargetId;
    /**
     * 摘要
     */
    @ApiModelProperty(value = "摘要")
    @Column(name = "summary")
    private String summary;
    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    @Column(name = "fk_currency_type_num")
    private String fkCurrencyTypeNum;
    /**
     * 学费金额
     */
    @ApiModelProperty(value = "学费金额")
    @Column(name = "tuition_amount")
    private BigDecimal tuitionAmount;
    /**
     * 费率%
     */
    @ApiModelProperty(value = "费率%")
    @Column(name = "commission_rate")
    private BigDecimal commissionRate;
    /**
     * 渠道费率%
     */
    @ApiModelProperty(value = "渠道费率%")
    @Column(name = "net_rate")
    private BigDecimal netRate;
    /**
     * 佣金金额
     */
    @ApiModelProperty(value = "佣金金额")
    @Column(name = "commission_amount")
    private BigDecimal commissionAmount;
    /**
     * 定额金额
     */
    @UpdateWithNull
    @ApiModelProperty(value = "定额金额")
    @Column(name = "fixed_amount")
    private BigDecimal fixedAmount;
    /**
     * 其他金额类型：0=奖励金额/1=Incentive奖励/2=保险金额/3=活动费用/4=其他收入
     */
    @ApiModelProperty(value = "其他金额类型：0=奖励金额/1=Incentive奖励/2=保险金额/3=活动费用/4=其他收入")
    @Column(name = "bonus_type")
    private Integer bonusType;
    /**
     * 其他金额
     */
    @ApiModelProperty(value = "其他金额")
    @Column(name = "bonus_amount")
    private BigDecimal bonusAmount;
    /**
     * 应收金额
     */
    @ApiModelProperty(value = "应收金额")
    @Column(name = "receivable_amount")
    private BigDecimal receivableAmount;
    /**
     * 计划收款时间
     */
    @ApiModelProperty(value = "计划收款时间")
    @Column(name = "receivable_plan_date")
    @JsonFormat(pattern="yyyy-MM-dd")
    @UpdateWithNull
    private Date receivablePlanDate;
    /**
     * 应收计划额外原因Id
     */
    @ApiModelProperty(value = "应收计划额外原因Id")
    @Column(name = "fk_receivable_reason_id")
    private Integer fkReceivableReasonId;
    /**
     * 状态：0关闭/1打开/2完成
     */
    @ApiModelProperty(value = "状态：0关闭/1打开/2完成")
    @Column(name = "status")
    private Integer status;
    /**
     * 旧数据财务id(gea)
     */
    @ApiModelProperty(value = "旧数据财务id(gea)")
    @Column(name = "id_gea_finance")
    private String idGeaFinance;
}
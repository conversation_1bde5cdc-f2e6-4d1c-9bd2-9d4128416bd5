package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class BusinessProviderContactPersonDto extends BaseVoEntity {

    @ApiModelProperty("业务提供商Id")
    @NotNull(message = "业务提供商Id不能为空", groups = {Add.class, Update.class})
    private Long fkBusinessProviderId;

    @ApiModelProperty("联系人姓名")
    @NotBlank(message = "联系人姓名不能为空", groups = {Add.class, Update.class})
    private String name;

    @ApiModelProperty("性别：0女/1男")
    private Integer gender;

    @ApiModelProperty("部门")
    private String department;

    @ApiModelProperty("职称")
    private String title;

    @ApiModelProperty("手机号区号")
    private String mobileAreaCode;

    @ApiModelProperty("手机号")
    private String mobile;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("备注")
    private String remark;

   
}

package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @author: Sea
 * @create: 2020/8/31 11:34
 * @verison: 1.0
 * @description:
 */
@Data
public class ConventionTableRegistrationDto extends BaseVoEntity {
    /**
     * 峰会桌子Id
     */
    @NotNull(message = "峰会桌子Id不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "峰会桌子Id", required = true)
    private Long fkConventionTableId;

    /**
     * 峰会报名Id
     */
    @ApiModelProperty(value = "峰会报名Id")
    private Long fkConventionRegistrationId;

  
}

package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("u_student_offer_item_step")
public class StudentOfferItemStep extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 前置条件id，需要完成步骤条件（可多选），逗号分隔：1,2,3
     */
    @Column(name = "fk_student_offer_item_step_id_precondition")
    @ApiModelProperty(value = "前置条件id，需要完成步骤条件（可多选），逗号分隔：1,2,3")
    private String fkStudentOfferItemStepIdPrecondition;
    /**
     * 申请步骤名
     */
    @ApiModelProperty(value = "申请步骤名")
    @Column(name = "step_name")
    private String stepName;
    /**
     * 申请步骤key
     */
    @ApiModelProperty(value = "申请步骤key")
    @Column(name = "step_key")
    private String stepKey;
    /**
     * 申请步骤排序，由0开始按顺序排列
     */
    @ApiModelProperty(value = "申请步骤排序，由0开始按顺序排列")
    @Column(name = "step_order")
    private Integer stepOrder;
    /**
     * 角色Key，多个用逗号分隔
     */
    @ApiModelProperty(value = "角色Key，多个用逗号分隔")
    @Column(name = "role_key")
    private String roleKey;

    @ApiModelProperty(value = "文件类型配置信息")
    @Column(name = "config_json")
    private String configJson;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;
}
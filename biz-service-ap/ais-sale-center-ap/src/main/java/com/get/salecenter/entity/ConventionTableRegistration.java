package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("r_convention_table_registration")
public class ConventionTableRegistration extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 峰会桌子Id
     */
    @ApiModelProperty(value = "峰会桌子Id")
    @Column(name = "fk_convention_table_id")
    private Long fkConventionTableId;
    /**
     * 峰会报名Id
     */
    @ApiModelProperty(value = "峰会报名Id")
    @Column(name = "fk_convention_registration_id")
    private Long fkConventionRegistrationId;
}
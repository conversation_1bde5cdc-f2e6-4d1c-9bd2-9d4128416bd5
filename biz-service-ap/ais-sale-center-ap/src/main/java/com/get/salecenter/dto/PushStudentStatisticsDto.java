package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2022/9/29
 * @TIME: 9:56
 * @Description:
 **/
@Data
public class PushStudentStatisticsDto {
    /**
     * 学生创建开始时间
     */
    @ApiModelProperty(value = "学生创建开始时间" )
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTime;
    /**
     * 学生创建结束时间
     */
    @ApiModelProperty(value = "学生创建结束时间" )
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;
    /**
     * 开学时间
     */
    @ApiModelProperty(value = "开学时间" )
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startOpeningTime;
    /**
     * 开学时间
     */
    @ApiModelProperty(value = "开学时间" )
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endOpeningTime;
    /**
     * 公司id列表
     */
    @ApiModelProperty(value = "公司id列表" )
    private List<Long> fkCompanyIdList;
    /**
     * 公司id
     */
    @ApiModelProperty(value = "公司id")
    private Long fkCompanyId;

    /**
     * bd名称
     */
    @ApiModelProperty(value = "bd名称")
    private String bdName;

    /**
     * BD编号
     */
    @ApiModelProperty(value = "BD编号")
    private String bdCode;

}

package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @author: Hardy
 * @create: 2024/2/5 11:21
 * @verison: 1.0
 * @description:
 */
@Data
public class StudentServiceFeeCostListDto {

    @NotNull(message = "学生留学服务费id不能为空")
    @ApiModelProperty(value = "学生留学服务费id",required = true)
    private Long fkStudentServiceFeeId;

}

package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Set;

/**
 * @author: Hardy
 * @create: 2022/9/13 16:35
 * @verison: 1.0
 * @description:
 */
@Data
public class EventRegistrationStatusDto extends BaseVoEntity {

    /**
     * 活动Id
     */
    @NotNull(message = "活动Id不能为空",groups = {Add.class})
    @ApiModelProperty(value = "活动Ids")
    private Set<Long> fkEventIds;


    @NotNull(message = "学校提供商Id不能为空", groups = {Add.class})
    @ApiModelProperty(value = "枚举状态：0不参加/1参加/2待定")
    private Integer status;
}

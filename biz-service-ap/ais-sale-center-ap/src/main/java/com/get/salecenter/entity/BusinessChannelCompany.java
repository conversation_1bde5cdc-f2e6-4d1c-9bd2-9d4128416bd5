package com.get.salecenter.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import lombok.Data;

/**
 * r_business_channel_company
 * <AUTHOR>
@Data
@TableName("r_business_channel_company")
public class BusinessChannelCompany extends BaseEntity implements Serializable {
    /**
     * 业务渠道/合同方及公司关系Id
     */
    private Long id;

    /**
     * 业务渠道Id
     */
    private Long fkBusinessChannelId;

    /**
     * 公司Id
     */
    private Long fkCompanyId;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 创建用户(登录账号)
     */
    private String gmtCreateUser;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 修改用户(登录账号)
     */
    private String gmtModifiedUser;

    private static final long serialVersionUID = 1L;
}
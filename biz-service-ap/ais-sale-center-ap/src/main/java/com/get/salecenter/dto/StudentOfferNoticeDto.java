package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @author: Hardy
 * @create: 2023/11/13 18:29
 * @verison: 1.0
 * @description:
 */
@Data
public class StudentOfferNoticeDto {

    @ApiModelProperty(value = "公司id")
    private Long fkCompanyId;

    @ApiModelProperty(value = "公司id")
    private Integer status;

    @ApiModelProperty(value = "学生名称")
    private String studentName;

    @ApiModelProperty(value = "申请学校")
    private String institutionName;

    @ApiModelProperty(value = "申请课程")
    private String courseName;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "开学时间开始")
    private Date openingTimeStart;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "开学时间结束")
    private Date openingTimeEnd;
}

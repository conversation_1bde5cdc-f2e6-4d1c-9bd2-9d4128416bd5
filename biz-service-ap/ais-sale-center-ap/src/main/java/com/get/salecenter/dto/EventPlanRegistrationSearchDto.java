package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2023/12/19
 * @TIME: 14:58
 * @Description:
 **/
@Data
public class EventPlanRegistrationSearchDto {
    @ApiModelProperty(value = "所属公司")
    private Long fkCompanyId;

    @ApiModelProperty(value = "业务国家")
    private List<Long> fkAreaCountryIds;

    @ApiModelProperty(value = "年度")
    private Integer year;

    @ApiModelProperty(value = "活动年度计划Id")
    private Long fkEventPlanId;

    @ApiModelProperty(value = "活动年度计划主题Ids")
    private List<Long> fkEventPlanThemeIds;

    @ApiModelProperty(value = "活动年度计划线上项目Ids")
    private List<Long> fkEventPlanThemeOnlineIds;

    @ApiModelProperty(value = "活动年度计划线下项目Ids")
    private List<Long> fkEventPlanThemeOfflineIds;

    @ApiModelProperty(value = "活动年度计划线下子项目Ids")
    private List<Long> fkEventPlanThemeOfflineItemIds;

    @ApiModelProperty(value = "活动年度线下专访项目Ids")
    private List<Long> fkEventPlanThemeWorkshopIds;

    @ApiModelProperty(value = "活动年度计划线下项目业务国家")
    private List<String> areaCountryNames;

    @ApiModelProperty(value = "线下活动项目地区")
    private List<String> offlineLocations;

    @ApiModelProperty(value = "线下专访活动项目地区")
    private List<String> workShopLocations;

    @ApiModelProperty(value = "学校提供商Ids")
    private List<Long> fkInstitutionProviderIds;

    @ApiModelProperty(value = "报名名册Id")
    private List<Long> fkRegistrationIds;

    @ApiModelProperty(value = "月份搜索模糊")
    private String monthKeyword;
}

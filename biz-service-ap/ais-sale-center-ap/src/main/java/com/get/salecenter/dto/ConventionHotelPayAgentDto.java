package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;



@Data
public class ConventionHotelPayAgentDto extends BaseVoEntity {

    @NotNull(message = "峰会Id")
    @ApiModelProperty(value = "峰会Id",required = true)
    private Long fkConventionId;

    @NotNull(message = "峰会参展人员Id")
    @ApiModelProperty(value = "峰会参展人员Id",required = true)
    private Long fkConventionPersonId;

    @NotBlank(message = "住客名称")
    @ApiModelProperty(value = "住客名称",required = true)
    private String name;

    @NotBlank(message = "机构名称")
    @ApiModelProperty(value = "机构名称")
    private String institutionName;

    @NotBlank(message = "房型")
    @ApiModelProperty(value = "房型",required = true)
    private String roomType;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @NotNull(message = "到店日期")
    @ApiModelProperty(value = "到店日期",required = true)
    private Date checkInTime;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @NotNull(message = "离店日期")
    @ApiModelProperty(value = "离店日期",required = true)
    private Date checkOutTime;

    @ApiModelProperty(value = "备注")
    private String remark;

}

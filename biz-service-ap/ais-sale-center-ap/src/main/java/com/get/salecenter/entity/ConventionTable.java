package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("m_convention_table")
public class ConventionTable extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 峰会Id
     */
    @ApiModelProperty(value = "峰会Id")
    @Column(name = "fk_convention_id")
    private Long fkConventionId;
    /**
     * 桌子类型key
     */
    @ApiModelProperty(value = "桌子类型key")
    @Column(name = "fk_table_type_key")
    private String fkTableTypeKey;
    /**
     * 前序号
     */
    @ApiModelProperty(value = "前序号")
    @Column(name = "pre_num")
    private String preNum;
    /**
     * 桌子编号
     */
    @ApiModelProperty(value = "桌子编号")
    @Column(name = "table_num")
    private String tableNum;
    /**
     * 座位数
     */
    @ApiModelProperty(value = "座位数")
    @Column(name = "seat_count")
    private Integer seatCount;
    /**
     * 是否主席台：0否/1是，默认否
     */
    @ApiModelProperty(value = "是否主席台：0否/1是，默认否")
    @Column(name = "is_vip")
    private Integer isVip;
    /**
     * 排序，数字由小到大排列
     */
    @ApiModelProperty(value = "排序，数字由小到大排列")
    @Column(name = "view_order")
    private Integer viewOrder;
}
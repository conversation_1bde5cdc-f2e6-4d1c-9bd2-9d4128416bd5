package com.get.salecenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @author: Hardy
 * @create: 2023/11/22 17:31
 * @verison: 1.0
 * @description:
 */
@Data
public class StudentOfferNoticeUpdateDto extends BaseVoEntity{

//    @ApiModelProperty(value = "学生Id")
//    private Long fkStudentId;
//
//    @ApiModelProperty(value = "学生代理Id")
//    private Long fkAgentId;
//
//    @ApiModelProperty(value = "入读意向通知模板Id")
//    private Long fkStudentOfferNoticeTemplateId;

//    @ApiModelProperty(value = "邮件标题")
//    private String emailSubject;
//
//    @ApiModelProperty(value = "邮件内容")
//    private String emailContent;

    @ApiModelProperty(value = "发件人员工Id")
    private Long fkStaffIdFrom;

//    @ApiModelProperty(value = "发件人地址")
//    private String fromEmail;

    @ApiModelProperty(value = "抄送地址")
    private String ccEmail;

    @ApiModelProperty(value = "收件人地址")
    private String toEmail;

//    @ApiModelProperty(value = "备注（失败Msg）")
//    private String remark;



}

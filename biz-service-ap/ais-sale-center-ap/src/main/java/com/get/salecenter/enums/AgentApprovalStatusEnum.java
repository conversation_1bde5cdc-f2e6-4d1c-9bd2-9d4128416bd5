package com.get.salecenter.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 代理审批状态枚举
 *
 * <AUTHOR>
 * @Date 2025-01-08
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum AgentApprovalStatusEnum {

    /**
     * 无合同
     */
    NO_CONTRACT(0, "无合同"),

    /**
     * 有合同
     */
    HAS_CONTRACT(1, "有合同"),

    /**
     * 续约中
     */
    RENEWING(5, "续约中");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态描述
     */
    private final String msg;

    /**
     * 代理审批状态映射表
     */
    private static final Map<Integer, AgentApprovalStatusEnum> AGENT_APPROVAL_STATUS_MAP = new HashMap<>();

    static {
        for (AgentApprovalStatusEnum agentApprovalStatusEnum : AgentApprovalStatusEnum.values()) {
            AGENT_APPROVAL_STATUS_MAP.put(agentApprovalStatusEnum.getCode(), agentApprovalStatusEnum);
        }
    }

    /**
     * 根据状态码获取对应的代理审批状态枚举实例
     *
     * @param code 状态码
     * @return 对应的代理审批状态枚举实例，如果找不到则返回null
     */
    public static AgentApprovalStatusEnum getAgentApprovalStatusByCode(Integer code) {
        return AGENT_APPROVAL_STATUS_MAP.get(code);
    }

    /**
     * 判断代理是否有合同
     *
     * @param code 状态码
 * @return true表示有合同，false表示无合同
     */
    public static boolean hasContract(Integer code) {
        AgentApprovalStatusEnum statusEnum = getAgentApprovalStatusByCode(code);
        if (statusEnum == null) {
            return false;
        }
        return statusEnum == HAS_CONTRACT || statusEnum == RENEWING;
    }

    /**
     * 判断代理是否在续约中
     *
     * @param code 状态码
     * @return true表示在续约中，false表示不在续约中
     */
    public static boolean isRenewing(Integer code) {
        AgentApprovalStatusEnum statusEnum = getAgentApprovalStatusByCode(code);
        return statusEnum == RENEWING;
    }

    /**
     * 判断代理是否可以发起续约申请
     *
     * @param code 状态码
     * @return true表示可以发起续约申请，false表示不可以发起续约申请
     */
    public static boolean canStartRenewal(Integer code) {
        AgentApprovalStatusEnum statusEnum = getAgentApprovalStatusByCode(code);
        return statusEnum == HAS_CONTRACT;
    }

}

package com.get.salecenter.dto;

import lombok.Data;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2024/3/19
 * @TIME: 10:52
 * @Description:
 **/
@Data
public class OfflineLocationSelectDto {

    @ApiModelProperty(value = "活动计划主题Id")
    private List<Long> fkEventPlanThemeIds;

    @ApiModelProperty(value = "线下活动项目业务国家列表")
    private List<String> areaCountryNames;
}

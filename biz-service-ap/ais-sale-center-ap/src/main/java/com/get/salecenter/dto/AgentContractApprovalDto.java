package com.get.salecenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 合同审批DTO
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AgentContractApprovalDto extends BaseVoEntity {

    /**
     * 学生代理合同Id
     */
    @ApiModelProperty("学生代理合同Id")
    private Long fkAgentContractId;

    /**
     * 审批意见
     */
    @ApiModelProperty("审批意见")
    private String approvalComment;

    /**
     * 审批状态：4审核通过/-4审核驳回
     */
    @ApiModelProperty("审批状态：4审核通过/-4审核驳回")
    private Integer approvalStatus;

    /**
     * 代理合同资料
     */
    @ApiModelProperty(value = "代理合同资料")
    List<MediaAndAttachedDto> mediaAttachedVos;

}
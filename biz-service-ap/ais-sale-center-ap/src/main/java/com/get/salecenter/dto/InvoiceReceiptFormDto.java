package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class InvoiceReceiptFormDto {

    @NotBlank(message = "fkTypeKey不能为空")
    @ApiModelProperty(value = "目标对象类型")
    private String fkTypeKey;

    @NotNull(message = "fkTargetId不能为空")
    @ApiModelProperty(value = "目标对象Id")
    private Long fkTypeTargetId;

    @ApiModelProperty(value = "发票Ids")
    private List<Long> fkInvoiceIds;

    @NotNull(message = "公司id不能为空")
    @Min(value = 1, message = "缺少公司id参数")
    @ApiModelProperty(value = "公司id")
    private Long fkCompanyId;
//
//    @NotBlank(message = "发票编号不能为空")
//    @ApiModelProperty(value = "发票编号")
//    private String invoiceNum;
}

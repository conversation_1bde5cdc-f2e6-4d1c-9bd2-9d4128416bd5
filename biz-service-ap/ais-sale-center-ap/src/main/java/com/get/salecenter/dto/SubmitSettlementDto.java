package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 提交代理确认结算Vo
 *
 * <AUTHOR>
 * @date 2021/12/29 12:10
 */
@Data
public class SubmitSettlementDto {

    @NotNull(message = "代理id不能为空")
    @ApiModelProperty(value = "代理id")
    private Long agentId;

    @NotNull(message = "应付计划id数组不能为空")
    @ApiModelProperty(value = "应付计划id数组")
    private List<SubmitSettlementItemDto> submitSettlementItemVos;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "到账时间（开始时间）")
    private Date receiptStartTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "到账时间（结束时间）")
    private Date receiptEndTime;

}

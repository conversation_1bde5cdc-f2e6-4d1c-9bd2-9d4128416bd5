package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @DATE: 2023/12/14
 * @TIME: 10:45
 * @Description:年度活动主题
 **/
@Data
public class EventPlanThemeDto extends BaseVoEntity implements Serializable{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "活动年度计划Id")
    @NotNull(message = "活动年度计划Id不能为空", groups = {Add.class, Update.class})
    private Long fkEventPlanId;

    @ApiModelProperty(value = "展示类型：线上活动1/线下活动2/线下专坊3")
    @NotNull(message = "展示类型不能为空",groups = {Add.class,Update.class})
    private Integer displayType;

    @ApiModelProperty(value = "主标题")
    @NotBlank(message = "主标题不能为空",groups = {Add.class,Update.class})
    private String mainTitle;

    @ApiModelProperty(value = "副标题")
    private String subTitle;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    @ApiModelProperty(value = "是否激活：0否/1是，若否需要灰掉整个主题")
    private Boolean isActive;

}

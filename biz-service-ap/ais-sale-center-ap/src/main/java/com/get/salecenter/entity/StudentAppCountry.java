package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("u_student_app_country")
public class StudentAppCountry extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 国家名称
     */
    @ApiModelProperty(value = "国家名称")
    @Column(name = "name")
    private String name;
    /**
     * 业务国家Id
     */
    @ApiModelProperty(value = "业务国家Id")
    @Column(name = "fk_area_country_id")
    private Long fkAreaCountryId;
}
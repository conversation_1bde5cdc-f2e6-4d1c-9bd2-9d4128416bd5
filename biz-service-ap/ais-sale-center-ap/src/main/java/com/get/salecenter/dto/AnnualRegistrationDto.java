package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: Hardy
 * @create: 2021/12/6 10:13
 * @verison: 1.0
 * @description:
 */
@Data
public class AnnualRegistrationDto {

    @ApiModelProperty("报名名册id")
    private Long id;

    @ApiModelProperty("峰会id")
    private Long conventionId;

    @ApiModelProperty("机构名")
    private String educationInstitutionName;

    @ApiModelProperty("是否决定了参会人员")
    private Integer isParticipant;

    @ApiModelProperty("主要联系人")
    private String name;

    @ApiModelProperty("电话")
    private String tel;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("地区")
    private String registrationOptions;

    @ApiModelProperty("决定后参会人员")
    private List<AttendeesDto> attendeesList;

}

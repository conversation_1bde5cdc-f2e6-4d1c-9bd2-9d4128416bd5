package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @author: Hardy
 * @create: 2021/11/22 14:49
 * @verison: 1.0
 * @description:
 */
@Data
public class StudentReceivableSumDetailDto {
    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;

    /**
     * 方案id
     */
    @ApiModelProperty(value = "方案id")
    private Long fkStudentOfferId;

    /**
     * 方案开始时间
     */
    @ApiModelProperty(value = "方案开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTime;

    /**
     * 方案结束时间
     */
    @ApiModelProperty(value = "方案结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;
}

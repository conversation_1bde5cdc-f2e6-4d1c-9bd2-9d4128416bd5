package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 财务结算汇总删除佣金结算(第四步删除佣金结算按钮)Vo
 *
 * <AUTHOR>
 * @date 2021/12/29 12:10
 */
@Data
public class DeleteSettlementDeleteDto {


    @NotNull(message = "结算状态不能为空")
    @ApiModelProperty(value = "结算状态：0未结算/1结算中/2代理确认/3财务确认/4财务汇总")
    private Integer statusSettlement;

//    @NotEmpty(message = "学生代理合同账户Id不能为空")
    @ApiModelProperty(value = "学生代理合同账户Id")
    private Long fkAgentContractAccountId;

    @NotNull(message = "应付计划Id不能为空")
    @ApiModelProperty(value = "应付计划Id")
    private Long payablePlanId;


}

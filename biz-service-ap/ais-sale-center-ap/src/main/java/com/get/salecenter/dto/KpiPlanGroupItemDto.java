package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @DATE: 2024/4/18
 * @TIME: 15:26
 * @Description:
 **/
@Data
public class KpiPlanGroupItemDto extends BaseVoEntity {
    @ApiModelProperty(value = "KPI方案Id")
    @NotNull(message = "KPI方案Id不能为空", groups = {KpiPlanGroupItemDto.Add.class, KpiPlanGroupItemDto.Update.class})
    private Long fkKpiPlanId;

    @ApiModelProperty(value = "KPI方案组别Id")
    @NotNull(message = "KPI方案组别Id不能为空", groups = {KpiPlanGroupItemDto.Add.class, KpiPlanGroupItemDto.Update.class})
    private Long fkKpiPlanGroupId;

    @ApiModelProperty(value = "学校提供商Id")
    private Long fkInstitutionProviderId;

    @ApiModelProperty(value = "业务国家Id")
    private String fkAreaCountryIdsKpi;

    @ApiModelProperty(value = "专业等级Ids(多选)，格式：1,2,3")
    private String fkMajorLevelIds;

    @ApiModelProperty(value = "只统计直录（没有子计划的主课）0否/1是")
    private Boolean isDirect;

    @ApiModelProperty(value = "申请国家Ids(多选)，格式：1,2,3")
    private String fkAreaCountryIds;

    @ApiModelProperty(value = "国家包含类型：null没设置/0不包含/1包含")
    private Integer countryIncludeType;

    @ApiModelProperty(value = "剔除提供商下的学校Ids(多选)，格式：1,2,3")
    private String fkInstitutionIds;

    @ApiModelProperty(value = "学生创建时间（开始）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date studentCreateTimeStart;

    @ApiModelProperty(value = "学生创建时间（结束）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date studentCreateTimeEnd;

    /**
     * 申请创建开始时间
     */
    @ApiModelProperty(value = "申请计划创建时间（开始）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date offerItemCreateTimeStart;

    /**
     * 申请创建结束时间
     */
    @ApiModelProperty(value = "申请计划结束时间 （结束）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date offerItemCreateTimeEnd;


    @ApiModelProperty(value = "入学时间（开始）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date intakeTimeStart;

    @ApiModelProperty(value = "入学时间（结束）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date intakeTimeEnd;

    @ApiModelProperty(value = "步骤登记时间（开始）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date stepTimeStart;

    @ApiModelProperty(value = "步骤登记时间（结束）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date stepTimeEnd;

    @ApiModelProperty(value = "目标设置（成功入学）")
    private Integer targetEnrolled;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;


    /**
     * 申请创建开始时间
     */
    @ApiModelProperty(value = "申请创建开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTime;

    /**
     * 申请创建结束时间
     */
    @ApiModelProperty(value = "申请创建结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "是否统计后续课程：0否/1是，默认0不做统计")
    private Boolean  isFollow;

    @ApiModelProperty(value = "学校包含类型：null没设置/0不包含/1包含")
    private Integer institutionIncludeType;
}

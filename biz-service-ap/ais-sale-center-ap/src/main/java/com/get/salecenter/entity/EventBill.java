package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import javax.persistence.Column;
import javax.persistence.Table;
import lombok.Data;


@Data
@TableName("m_event_bill")
public class EventBill extends BaseEntity implements Serializable {
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;

    /**
     * 学校供应商Id
     */
    @ApiModelProperty(value = "学校供应商Id")
    @Column(name = "fk_institution_provider_id")
    private Long fkInstitutionProviderId;

    /**
     * 活动年份
     */
    @ApiModelProperty(value = "活动年份")
    @Column(name = "event_year")
    private Integer eventYear;

    /**
     * invoice币种
     */
    @ApiModelProperty(value = "invoice币种")
    @Column(name = "fk_currency_type_num_invoice")
    private String fkCurrencyTypeNumInvoice;

    /**
     * invoice金额
     */
    @UpdateWithNull
    @ApiModelProperty(value = "invoice金额")
    @Column(name = "invoice_amount")
    private BigDecimal invoiceAmount;

    /**
     * 活动费币种
     */
    @ApiModelProperty(value = "活动费币种")
    @Column(name = "fk_currency_type_num_event")
    private String fkCurrencyTypeNumEvent;

    /**
     * 活动费金额
     */
    @ApiModelProperty(value = "活动费金额")
    @Column(name = "event_amount")
    private BigDecimal eventAmount;

    /**
     * invoice名目
     */
    @ApiModelProperty(value = "invoice名目")
    @Column(name = "invoice_summary")
    private String invoiceSummary;

    /**
     * invoice收件人
     */
    @ApiModelProperty(value = "invoice收件人")
    @Column(name = "invoice_contact_person")
    private String invoiceContactPerson;

    /**
     * invoice收件人Email
     */
    @ApiModelProperty(value = "invoice收件人Email")
    @Column(name = "invoice_contact_email")
    private String invoiceContactEmail;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;

    /**
     * 应收计划Id
     */
//    @UpdateWithNull
//    @ApiModelProperty(value = "应收计划Id")
//    @Column(name = "fk_receivable_plan_id")
//    private Long fkReceivablePlanId;

    /**
     * 发票编号
     */
    @UpdateWithNull
    @ApiModelProperty(value = "发票编号")
    @Column(name = "fk_invoice_num")
    private String fkInvoiceNum;

    /**
     * 状态：0作废/1打开
     */
    @ApiModelProperty(value = "状态：0作废/1打开")
    @Column(name = "status")
    private Integer status;

    private static final long serialVersionUID = 1L;
}
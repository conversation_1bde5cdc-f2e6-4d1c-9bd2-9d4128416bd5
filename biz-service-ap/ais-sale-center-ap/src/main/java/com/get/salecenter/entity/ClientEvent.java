package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;

/**
 * author:<PERSON>
 * Time: 12:33
 * Date: 2022/8/17
 * Description:
 */
@Data
@TableName("m_client_event")
public class ClientEvent extends BaseEntity implements Serializable {

    @ApiModelProperty(value = "客户Id")
    @Column(name = "fk_client_id")
    private Long fkClientId;

    @ApiModelProperty(value = "客户事件类型Id（学生共用）")
    @Column(name = "fk_student_event_type_id")
    private Long fkStudentEventTypeId;

    @ApiModelProperty(value = "预约回访时间")
    @Column(name = "follow_up_time")
    private Date followUpTime;

    @ApiModelProperty(value = "时间内容")
    @Column(name = "description")
    private String description;
}

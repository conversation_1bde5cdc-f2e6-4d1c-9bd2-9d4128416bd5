package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @DATE: 2024/4/16
 * @TIME: 12:32
 * @Description:KPI方案组别
 **/
@TableName("m_kpi_plan_group")
@ApiModel(value="KPI方案组别", description="")
@Data
public class KpiPlanGroup extends BaseEntity implements Serializable {
    @ApiModelProperty(value = "KPI方案ID")
    private Long fkKpiPlanId;

    @ApiModelProperty(value = "组别名称")
    private String groupName;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;
}

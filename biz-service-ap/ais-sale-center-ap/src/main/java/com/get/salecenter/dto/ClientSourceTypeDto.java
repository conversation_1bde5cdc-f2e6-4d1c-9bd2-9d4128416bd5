package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 学生资源推荐来源类型列表新增参数
 */
@Data
public class ClientSourceTypeDto extends BaseVoEntity{

    @ApiModelProperty(value = "类型名称")
    @NotBlank(message = "类型名称不能为空", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    private String typeName;

    /**
     * 类型Key：bms_student_num/crm_contract_num/m_agent/bms_student_num_not_os/m_business_provider
     */
    @ApiModelProperty(value = "类型Key：bms_student_num/crm_contract_num/m_agent/bms_student_num_not_os/m_business_provider")
    @NotBlank(message = "类型Key不能为空", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    private String typeKey;

    @ApiModelProperty(value = "类型标识：100%, 30%")
    @NotBlank(message = "类型标识不能为空", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    private String typeMark;

    /**
     * 排序，倒序：数字由大到小排列，由0开始按顺序排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列，由0开始按顺序排列")
    private Integer viewOrder;

}

package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * author:Neil
 * Time: 15:01
 * Date: 2022/8/19
 * Description:
 */
@Data
public class ClientOfferUpdateDto extends BaseEntity{

    @ApiModelProperty(value = "状态：0作废/1激活")
    private Integer status;

    @ApiModelProperty(value = "学生资源申请方案状态步骤Id")
    private Long fkClientOfferStepId;
  
}

package com.get.salecenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @author: Sea
 * @create: 2021/4/29 10:50
 * @verison: 1.0
 * @description:
 */
@Data
public class AnnualConferenceRegistrationBoothDto extends BaseVoEntity {
    /**
     * 年度会议注册id
     */
    @ApiModelProperty(value = "年度会议注册id")
    private Long fkAnnualConferenceRegistrationId;

    /**
     * 展位名称
     */
    @ApiModelProperty(value = "展位名称")
    private String boothName;

    /**
     * 国家/地区(多选)
     */
    @ApiModelProperty(value = "国家/地区(多选)")
    private String countryRegion;

    /**
     * 展位号
     */
    @ApiModelProperty(value = "展位号")
    private Integer boothIndex;
}

package com.get.salecenter.dto;

import com.get.financecenter.entity.ReceiptFormItem;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: <PERSON>
 * @create: 2023/1/14 10:11
 * @verison: 1.0
 * @description:
 */
@Data
public class EventBillAccountNoticeDto {

    private Map<String, String> headerMap;

    private Set<Long> planIds;

    private Set<Long> receiptFormItemIds;

    private List<ReceiptFormItem> receiptFormItems;
}

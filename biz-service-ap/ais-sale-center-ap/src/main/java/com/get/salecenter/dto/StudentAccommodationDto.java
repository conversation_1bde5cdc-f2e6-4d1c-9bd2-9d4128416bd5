package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2022/1/10
 * @TIME: 12:04
 * @Description:
 **/
@Data
public class StudentAccommodationDto extends BaseVoEntity {
    /**
     * 学生Id
     */
    @ApiModelProperty(value = "学生Id")
    private Long fkStudentId;

    /**
     * 代理Id（业绩绑定）
     */
    @ApiModelProperty(value = "代理Id（业绩绑定）")
    private Long fkAgentId;

    /**
     * 员工Id（业绩绑定，BD）
     */
    @ApiModelProperty(value = "员工Id（业绩绑定，BD）")
    private Long fkStaffId;

    /**
     * 留学住宿编号
     */
    @ApiModelProperty(value = "留学住宿编号")
    private String num;

    @ApiModelProperty(value = "业务提供商Id")
    private Long fkBusinessProviderId;

    /**
     * 前往国家Id
     */
    @ApiModelProperty(value = "前往国家Id")
    @NotNull(message = "国家Id不能为空", groups = {Add.class, Update.class})
    private Long fkAreaCountryId;

    /**
     * 前往州省Id
     */
    @ApiModelProperty(value = "前往州省Id")
    @NotNull(message = "州省Id不能为空", groups = {Add.class, Update.class})
    private Long fkAreaStateId;

    /**
     * 前往城市Id
     */
    @ApiModelProperty(value = "前往城市Id")
    @NotNull(message = "城市Id不能为空", groups = {Add.class, Update.class})
    private Long fkAreaCityId;

    /**
     * 公寓名称
     */
    @ApiModelProperty(value = "公寓名称")
    @NotNull(message = "公寓名称不能为空", groups = {Add.class, Update.class})
    private String apartmentName;

    /**
     * 入住日期
     */
    @ApiModelProperty(value = "入住日期")
    @NotNull(message = "入住日期不能为空", groups = {Add.class, Update.class})
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date checkInDate;

    /**
     * 退房日期
     */
    @ApiModelProperty(value = "退房日期")
    @NotNull(message = "退房日期不能为空", groups = {Add.class, Update.class})
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date checkOutDate;

    /**
     * 住宿天数
     */
    @ApiModelProperty(value = "住宿天数")
    @NotNull(message = "住宿天数不能为空", groups = {Add.class, Update.class})
    private Integer duration;

    /**
     * 业务渠道Id
     */
    @ApiModelProperty(value = "业务渠道Id")
    @NotNull(message = "业务渠道Id不能为空", groups = {Add.class, Update.class})
    private Long fkBusinessChannelId;

    /**
     * 押金支付日期
     */
    @ApiModelProperty(value = "押金支付日期")
    @NotNull(message = "押金支付日期不能为空", groups = {Add.class, Update.class})
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date depositDate;

    /**
     * 住宿费币种
     */
    @ApiModelProperty(value = "住宿费币种")
    @NotBlank(message = "住宿费币种不能为空", groups = {Add.class, Update.class})
    private String fkCurrencyTypeNumAccommodation;

    /**
     * （每周/每月）住宿金额
     */
    @ApiModelProperty(value = "（每周/每月）住宿金额")
    @NotNull(message = "（每周/每月）住宿金额不能为空", groups = {Add.class, Update.class})
    private BigDecimal accommodationAmountPer;

    /**
     * 枚举：1每周/2每月
     */
    @ApiModelProperty(value = "枚举：1每周/2每月")
    @NotNull(message = "枚举：1每周/2每月不能为空", groups = {Add.class, Update.class})
    private Integer accommodationAmountPerUnit;

    /**
     * 总住宿金额
     */
    @ApiModelProperty(value = "总住宿金额")
    @NotNull(message = "总住宿金额不能为空", groups = {Add.class, Update.class})
    private BigDecimal accommodationAmount;

    /**
     * 住宿金额说明
     */
    @ApiModelProperty(value = "住宿金额说明")
    private String accommodationAmountNote;

    /**
     * 佣金币种
     */
    @ApiModelProperty(value = "佣金币种")
    private String fkCurrencyTypeNumCommission;

    /**
     * 收取佣金比例%
     */
    @ApiModelProperty(value = "收取佣金比例%")
    private BigDecimal commissionRateReceivable;

    /**
     * 支付佣金比例%（代理）
     */
    @ApiModelProperty(value = "支付佣金比例%（代理）")
    private BigDecimal commissionRatePayable;

    /**
     * 固定收取佣金金额
     */
    @ApiModelProperty(value = "固定收取佣金金额")
    private BigDecimal fixedAmountReceivable;

    /**
     * 固定支付佣金金额（代理）
     */
    @ApiModelProperty(value = "固定支付佣金金额（代理）")
    private BigDecimal fixedAmountPayable;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 状态：0关闭/1打开
     */
    @ApiModelProperty(value = "状态：0关闭/1打开/2成功/3延期/4失败")
    private Integer status;

    @ApiModelProperty(value = "角色员工id")
    @NotNull(message = "角色员工id不能为空", groups = {Add.class})
    private List<ProjectRoleStaffDto> roleStaffVo;

   
}

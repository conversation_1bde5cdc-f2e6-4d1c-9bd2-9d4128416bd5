package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2024/2/5 10:39
 * @verison: 1.0
 * @description:
 */
@Data
public class StudentServiceFeeCostDto extends BaseVoEntity {

    @NotNull(message = "学生留学服务费id不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "学生留学服务费id",required = true)
    private Long fkStudentServiceFeeId;

    @NotNull(message = "业务提供商Id不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "业务提供商Id",required = true)
    private Long fkBusinessProviderId;

    @NotBlank(message = "币种不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "币种",required = true)
    private String fkCurrencyTypeNum;

    @NotNull(message = "金额不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "金额",required = true)
    private BigDecimal amount;

    @ApiModelProperty("税金")
    private BigDecimal taxes;

    @ApiModelProperty(value = "付款日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date paymentTime;

    @ApiModelProperty(value = "付款银行Id")
    private Long fkBankAccountIdCompany;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "状态：0作废/1生效")
    private Integer status;

    @ApiModelProperty("附件列表")
    private List<MediaAndAttachedDto> mediaAttachedVo;

  
}

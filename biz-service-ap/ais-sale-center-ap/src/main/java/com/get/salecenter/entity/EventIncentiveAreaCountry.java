package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("r_event_incentive_area_country")
public class EventIncentiveAreaCountry extends BaseEntity implements Serializable {
    /**
     * 奖励推广活动Id
     */
    @ApiModelProperty(value = "奖励推广活动Id")
    @Column(name = "fk_event_incentive_id")
    private Long fkEventIncentiveId;

    /**
     * 活动目标对象国家Id
     */
    @ApiModelProperty(value = "活动目标对象国家Id")
    @Column(name = "fk_area_country_id")
    private Long fkAreaCountryId;

    private static final long serialVersionUID = 1L;
}
package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("tmp_staff_name_bd_code")
public class StaffNameBdCode extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 员工名称
     */
    @ApiModelProperty(value = "员工名称")
    @Column(name = "staff_name")
    private String staffName;
    /**
     * BD编号（4位）
     */
    @ApiModelProperty(value = "BD编号（4位）")
    @Column(name = "bd_code")
    private String bdCode;
}
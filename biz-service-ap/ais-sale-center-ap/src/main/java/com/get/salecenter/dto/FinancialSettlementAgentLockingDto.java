package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 财务结算代理锁定Vo
 *
 * <AUTHOR>
 * @date 2022/5/18 11:48
 */
@Data
public class FinancialSettlementAgentLockingDto {
    @NotNull(message = "代理id不能为空")
    @ApiModelProperty(value = "代理id")
    private Long agentId;

    @NotBlank(message = "支付币种不能为空")
    @ApiModelProperty(value = "支付币种")
    private String planCurrencyNum;

    @NotBlank(message = "结算币种不能为空")
    @ApiModelProperty(value = "结算币种")
    private String accountCurrencyNum;

    @NotBlank(message = "业务类型Key不能为空")
    @ApiModelProperty(value = "业务类型Key")
    private String fkTypeKey;
}

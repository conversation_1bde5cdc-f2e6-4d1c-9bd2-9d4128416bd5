package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Description: 修改申请步骤日志备注Vo
 * @Author: Jerry
 * @Date:12:18 2021/8/17
 */
@Data
public class RStudentOfferItemStepUpRemarkDto extends BaseVoEntity {

    /**
     * 学生申请方案项目Id
     */
    @ApiModelProperty(value = "学生申请方案项目Id", required = true)
    @NotNull(message = "学生申请方案项目Id不能为空", groups = {Add.class, Update.class})
    private Long fkStudentOfferItemId;

    /**
     * 学生申请方案项目状态步骤Id
     */
    @ApiModelProperty(value = "学生申请方案项目状态步骤Id", required = true)
    @NotNull(message = "学生申请方案项目状态步骤Id不能为空", groups = {Add.class, Update.class})
    private Long fkStudentOfferItemStepId;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

   
}

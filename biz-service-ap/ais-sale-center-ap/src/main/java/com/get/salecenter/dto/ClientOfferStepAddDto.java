package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotNull;

/**
 * @author: Hardy
 * @create: 2023/12/13 10:39
 * @verison: 1.0
 * @description:
 */
@Data
public class ClientOfferStepAddDto extends BaseVoEntity {
    /**
     * 状态步骤名
     */
    @ApiModelProperty(value = "状态步骤名")
    @Column(name = "step_name")
    private String stepName;

    /**
     * 状态步骤key
     */
    @ApiModelProperty(value = "状态步骤key")
    @Column(name = "step_key")
    private String stepKey;

    /**
     * 状态步骤排序，由0开始按顺序排列
     */
    @ApiModelProperty(value = "状态步骤排序，由0开始按顺序排列")
    @Column(name = "step_order")
    private Integer stepOrder;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;

}

package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * author:Neil
 * Time: 17:38
 * Date: 2022/6/20
 * Description:
 */
@Data
public class BusinessProviderDto extends BaseVoEntity {
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    //数组格式的参数适配
    @ApiModelProperty(value = "公司Id")
    private List<Long> fkCompanyIds;

    @ApiModelProperty(value = "业务提供商Id")
    private Long fkBusinessProviderId;

    /**
     * 业务类型关键字，枚举：m_student_insurance留学保险/m_student_accommodation留学住宿
     */
    @ApiModelProperty(value = "业务类型关键字，枚举：m_student_insurance留学保险/m_student_accommodation留学住宿")
    private String fkTypeKey;

    @ApiModelProperty(value = "提供商业务类型Id")
    private Long fkBusinessProviderTypeId;

    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    private String num;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 中文名称
     */
    @ApiModelProperty(value = "中文名称")
    private String nameChn;

    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    private Boolean isActive;

    private String keyWord;


    @ApiModelProperty(value = "部门Ids，支持多选，1,2,3逗号分隔")
    private String fkDepartmentIds;

    @ApiModelProperty(value = "法定代表人")
    private String legalPerson;

    @ApiModelProperty(value = "产品介绍")
    private String productInfo;


    @ApiModelProperty(value = "合作协议起止时间（开始）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date contractStartTime;

    @ApiModelProperty(value = "合作协议起止时间（结束）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date contractEndTime;

    @ApiModelProperty(value = "备注")
    private String remark;
}

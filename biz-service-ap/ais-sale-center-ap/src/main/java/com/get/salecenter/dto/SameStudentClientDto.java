package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2023/12/1 15:31
 * @verison: 1.0
 * @description:
 */
@Data
public class SameStudentClientDto {


    @ApiModelProperty(value = "客户姓名（中）",required = true)
    @Column(name = "name")
    private String name;
    /**
     * 姓（英/拼音）
     */
    @ApiModelProperty(value = "姓（英/拼音）")
    @Column(name = "last_name")
    private String lastName;
    /**
     * 名（英/拼音）
     */
    @ApiModelProperty(value = "名（英/拼音）")
    @Column(name = "first_name")
    private String firstName;

    /**
     * 生日
     */
    @ApiModelProperty(value = "生日",required = true)
    @Column(name = "birthday")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date birthday;

    /**
     * 国家Id
     */
    @ApiModelProperty(value = "国家Id")
    private Long fkAreaCountryId;

    /**
     * 步骤ids
     */
    @ApiModelProperty(value = "步骤ids")
    private List<Long> stepIds;

}

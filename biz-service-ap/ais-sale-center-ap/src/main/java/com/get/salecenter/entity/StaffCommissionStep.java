package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("u_staff_commission_step")
public class StaffCommissionStep extends BaseEntity implements Serializable {
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;

    /**
     * 学生申请方案项目状态步骤Ids，多个用英文逗号隔开（1,2,3），操作过可计入。
     */
    @ApiModelProperty(value = "学生申请方案项目状态步骤Ids，多个用英文逗号隔开（1,2,3），操作过可计入。")
    @Column(name = "fk_student_offer_item_step_ids")
    private String fkStudentOfferItemStepIds;

    /**
     * 提成申请步骤名称
     */
    @ApiModelProperty(value = "提成申请步骤名称")
    @Column(name = "step_name")
    private String stepName;

    /**
     * 提成申请步骤Key
     */
    @ApiModelProperty(value = "提成申请步骤Key")
    @Column(name = "step_key")
    private String stepKey;

    /**
     * 提成申请步骤排序，由0开始按顺序排列
     */
    @ApiModelProperty(value = "提成申请步骤排序，由0开始按顺序排列")
    @Column(name = "step_order")
    private Integer stepOrder;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;

    /**
     * 特殊步骤模式，0普通/1特殊（设置定额后，只要勾选就增加提成金额）
     */
    @ApiModelProperty(value = "特殊步骤模式，0普通/1特殊（设置定额后，只要勾选就增加提成金额）")
    @Column(name = "special_mode")
    private Integer specialMode;

    private static final long serialVersionUID = 1L;
}
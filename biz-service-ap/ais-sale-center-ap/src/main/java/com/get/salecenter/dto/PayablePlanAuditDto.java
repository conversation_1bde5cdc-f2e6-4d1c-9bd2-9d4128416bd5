package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/7 11:54
 * @desciption: 待审核VO
 */
@Data
public class PayablePlanAuditDto extends BaseVoEntity {

    @ApiModelProperty(value = "应收计划类型")
    private String fkTypeKey;

    @ApiModelProperty(value="国家Id")
    private Long fkAreaCountryIds;

    @ApiModelProperty(value = "学生信息（中英名称/生日）")
    private String studentName;

    @ApiModelProperty(value = "学生id")
    private Long fkStudentId;

    @ApiModelProperty(value = "代理信息")
    private String agentName;

    @ApiModelProperty(value = "代理id")
    private Long fkAgentId;

    @ApiModelProperty(value = "业务信息（学校/课程/保险/住宿）")
    private String bziName;

    @ApiModelProperty(value = "入学时间（开始）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date beginTime;

    @ApiModelProperty(value = "入学时间（结束）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty("申请计划创建时间(开始)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date itemCreatTime;

    @ApiModelProperty("申请计划创建时间(结束)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date itemEndTime;

    /**
     * 付款状态：0/1/2：未付/已付部分/已付齐
     */
    @ApiModelProperty(value = "收齐状态：0未付/1已付部分/3未付齐")
    private Integer payableStatus;

    //审核状态
    @ApiModelProperty(value = "审核状态,-1差异审核/0未审核0")
    @NotNull
    private Integer fkAuditStatus;

    //申请计划状态
    @ApiModelProperty(value = "申请计划步骤")
    private Long stepId;

    @ApiModelProperty(value = "导出ids")
    private List<Long> exportIds;

    @ApiModelProperty(value = "大区")
    private Long fkAreaRegionId;

    @ApiModelProperty(value = "公司id")
    private Long fkCompanyId;

   
}

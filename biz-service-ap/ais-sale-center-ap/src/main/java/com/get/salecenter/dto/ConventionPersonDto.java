package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/7/8 14:13
 * @verison: 1.0
 * @description:
 */
@Data
public class ConventionPersonDto extends BaseVoEntity{

    /**
     * 峰会Id
     */
    @NotNull(message = "峰会Id不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "峰会Id", required = true)
    private Long fkConventionId;

    /**
     * 参展人员类型（枚举定义）：0校方/1代理/2嘉宾/3员工/4工作人员
     */
    @NotNull(message = "参展人员类型不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "参展人员类型（枚举定义）：0校方/1代理/2嘉宾/3员工/4工作人员", required = true)
    private Integer type;

    /**
     * 参会编号
     */
//    @NotBlank(message = "参会编号不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "参会编号")
    private String num;

    /**
     * 参加人姓名
     */
    @NotBlank(message = "参加人姓名不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "参加人姓名", required = true)
    private String name;

    /**
     * 参加人姓名（中文）
     */
    @NotBlank(message = "参加人姓名（中文）不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "参加人姓名（中文）", required = true)
    private String nameChn;

    /**
     * 性别：0女/1男
     */
    @NotNull(message = "性别不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "性别：0女/1男", required = true)
    private Integer gender;

    /**
     * 参加人职位
     */
    @ApiModelProperty(value = "参加人职位")
    private String title;

    /**
     * 公司
     */
    @ApiModelProperty(value = "公司")
    private String company;

    /**
     * 参加人电邮
     */
    @ApiModelProperty(value = "参加人电邮")
    private String email;

    /**
     * 参加人电话
     */
    @NotBlank(message = "参加人电话不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "参加人电话", required = true)
    private String tel;

    /**
     * 护照号
     */
    @ApiModelProperty(value = "护照号")
    private String passportNum;

    /**
     * 身份证号
     */
    @ApiModelProperty(value = "身份证号")
    private String idCardNum;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 到店日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "到店日期")
    private Date checkInTime;

    /**
     * 离店日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "离店日期")
    private Date checkOutTime;

    /**
     * 预计到达时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "预计到达时间")
    private Date arrivalTime;

    /**
     * 到达交通类型（枚举定义选择）：0飞机/1高铁/2汽车
     */
    @ApiModelProperty(value = "到达交通类型（枚举定义选择）：0飞机/1高铁/2汽车")
    private String arrivalTransportation;

    /**
     * 到达交通编号：航班号/高铁班次/汽车班次
     */
    @ApiModelProperty(value = "到达交通编号：航班号/高铁班次/汽车班次")
    private String arrivalTransportationCode;

    /**
     * 预计离开时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "预计离开时间")
    private Date leaveTime;


    /**
     * 离开交通类型（枚举定义选择）：0飞机/1高铁/2汽车
     */
    @ApiModelProperty(value = "离开交通类型（枚举定义选择）：0飞机/1高铁/2汽车")
    private String leaveTransportation;


    /**
     * 到达交通编号：航班号/高铁班次/汽车班次
     */
    @ApiModelProperty(value = "到达交通编号：航班号/高铁班次/汽车班次")
    private String leaveTransportationCode;


    /**
     * 酒店住宿费用类型：0公费/1自费
     */
    @ApiModelProperty(value = "酒店住宿费用类型：0公费/1自费")
    private Integer hotelFeeType;

    /**
     * 酒店房型Id
     */
    @ApiModelProperty(value = "酒店房型Id")
    @Column(name = "fk_convention_hotel_id")
    private Long fkConventionHotelId;

    /**
     * 价格币种编号
     */
    @ApiModelProperty(value = "价格币种编号")
    private String fkCurrencyTypeNumHotelExpense;

    /**
     * 住宿费用单价（每晚）
     */
    @ApiModelProperty(value = "住宿费用单价（每晚）")
    private BigDecimal hotelExpense;

    /**
     * BD编号（4位）
     */
    @ApiModelProperty(value = "BD编号（4位）")
    private String bdCode;

    @ApiModelProperty(value = "大区")
    private Long fkAreaRegionId;

    /**
     * 是否VIP：0否/1是
     */
    @ApiModelProperty(value = "是否VIP：0否/1是")
    @NotNull(message = "是否VIP不能为空", groups = {Add.class, Update.class})
    private Boolean isVip;

    /**
     * 是否出席：0否/1是
     */
    @ApiModelProperty(value = "是否出席：0否/1是")
    private Boolean isAttend;

    //-------自定义内容
    /**
     * 选择项目对应的id(嘉宾有两个，按所属学校提供商，所属学校的顺序保存)
     */
    @ApiModelProperty(value = "选择项目对应的id")
    private List<Long> selectProjectIds;

    /**
     * 学校供应商名称
     */
    @ApiModelProperty(value = "学校供应商名称")
    private String institutionProviderName;

    /**
     * 学校名称
     */
    @ApiModelProperty(value = "学校名称")
    private String institutionName;

    /**
     * 代理编号
     */
    @ApiModelProperty(value = "代理编号")
    private String agentNum;

    /**
     * 代理名称
     */
    @ApiModelProperty(value = "代理名称")
    private String agentName;

    /**
     * 交通编号
     */
    @ApiModelProperty(value = "交通编号")
    private String transportationCode;

    /**
     * bd名称关键字
     */
    @ApiModelProperty(value = "bd名称关键字")
    private String bdNameKey;

    /**
     * 参会人员名称关键字
     */
    @ApiModelProperty(value = "参会人员名称关键字")
    private String nameKey;

    /**
     * 峰会流程id
     */
    @ApiModelProperty(value = "峰会流程ids")
    private List<Long> fkConventionProcedureIds;

    /**
     * 意向房型
     */
    @ApiModelProperty(value = "意向房型")
    private String intentionRoomType;

    /**
     * 根据登录人bdcode查询
     */
    @ApiModelProperty(value = "根据登录人bdcode查询")
    private Boolean bdCodeKey;

//    /**
//     * 是否参与晚宴流程
//     */
//    @ApiModelProperty(value = "是否参与晚宴流程")
//    private Boolean isAttendDinnerProcedure;
//
//    /**
//     * 是否参与培训流程
//     */
//    @ApiModelProperty(value = "是否参与培训流程")
//    private Boolean isAttendTrainingProcedure;

    /**
     * 过滤类型
     */
    @ApiModelProperty(value = "过滤类型：0/1:没参加晚宴/没参加培训")
    private Integer filterType;

    /**
     * 是否出席：0否/1是
     */
    @ApiModelProperty(value = "是否参加晚宴：0否/1是")
    @Column(name = "is_attend_dinner")
    private Boolean isAttendDinner;
    /**
     * 是否出席：0否/1是
     */
    @ApiModelProperty(value = "是否代订酒店：0否/1是")
    @Column(name = "is_book_hotel")
    private Boolean isBookHotel;

    @ApiModelProperty(value = "BD编号list")
    private List<String> bdCodes;
}

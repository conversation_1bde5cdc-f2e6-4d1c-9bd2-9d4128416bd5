package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/1/17 18:05
 */
@Data
public class PendingSettlementMarkDto {
    @ApiModelProperty(value = "代理id")
    @NotNull(message = "代理id不能为空")
    private Long agentId;

    @ApiModelProperty(value = "待结算标记  true：打标记  false:取消标记")
    @NotNull(message = "待结算标记不能为空")
    private Boolean flag;

}

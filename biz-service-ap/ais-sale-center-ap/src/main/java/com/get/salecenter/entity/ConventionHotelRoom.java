package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;

@Data
@TableName("m_convention_hotel_room")
public class ConventionHotelRoom extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 酒店房型Id
     */
    @ApiModelProperty(value = "酒店房型Id")
    @Column(name = "fk_convention_hotel_id")
    private Long fkConventionHotelId;
    /**
     * 系统房间编号
     */
    @ApiModelProperty(value = "系统房间编号")
    @Column(name = "system_room_num")
    private String systemRoomNum;
    /**
     * 酒店房间编号
     */
    @ApiModelProperty(value = "酒店房间编号")
    @Column(name = "hotel_room_num")
    private String hotelRoomNum;
    /**
     * 住店日期
     */
    @ApiModelProperty(value = "住店日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "stay_date")
    private Date stayDate;
}
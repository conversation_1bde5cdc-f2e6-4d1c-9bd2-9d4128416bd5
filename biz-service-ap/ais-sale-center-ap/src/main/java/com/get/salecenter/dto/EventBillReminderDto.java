package com.get.salecenter.dto;

import com.get.common.annotion.IgnoreRemind;
import com.get.core.mybatis.base.BaseEntity;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * @author: Hardy
 * @create: 2022/7/1 11:04
 * @verison: 1.0
 * @description:
 */
@Data
public class EventBillReminderDto extends BaseEntity {

    /**
     * 公司Id
     */
//    @NotNull(
//            message = "公司Id不能为空！",
//            groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class}
//    )
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;

    @ApiModelProperty(value = "到账金额")
    private BigDecimal actualReceivableAmount;

    @ApiModelProperty(value = "总到账金额")
    private BigDecimal totalActualReceivableAmount;

    @ApiModelProperty(value = "到账金额币种")
    private String actualReceivableAmountCurrencyNum;

    @IgnoreRemind
    @ApiModelProperty(value = "学校供应商Id")
    private Long fkInstitutionProviderId;

    @IgnoreRemind
    @ApiModelProperty(value = "业务国家多选")
    private List<Long> fkAreaCountryIdList;

    @ApiModelProperty(value = "活动年份")
    private Integer eventYear;

    @IgnoreRemind
    @ApiModelProperty(value = "invoice币种")
    private String fkCurrencyTypeNumInvoice;

    @ApiModelProperty(value = "invoice金额")
    private BigDecimal invoiceAmount;

    @IgnoreRemind
    @ApiModelProperty(value = "活动费币种")
    private String fkCurrencyTypeNumEvent;

    @ApiModelProperty(value = "活动费金额")
    private BigDecimal eventAmount;

    @ApiModelProperty(value = "invoice名目")
    private String invoiceSummary;

    @ApiModelProperty(value = "invoice收件人")
    private String invoiceContactPerson;

    @ApiModelProperty(value = "invoice收件人Email")
    private String invoiceContactEmail;

    @IgnoreRemind
    @ApiModelProperty(value = "摘要Ids")
    private List<Long> fkEventSummaryIdList;

    @IgnoreRemind
    @ApiModelProperty(value = "通知人id")
    private Set<Long> fkStaffIds;

    @ApiModelProperty(value = "备注")
    private String remark;

    @IgnoreRemind
    @ApiModelProperty(value = "活动费用汇总id")
    private Long fkEventBillId;

//    @ApiModelProperty(value = "开始时间")
//    private String startTime;

    @ApiModelProperty(value = "任务链接")
    private String taskLink;

    @ApiModelProperty(value = "业务国家高亮显示")
    private String fkAreaCountryNameHighline;

    @ApiModelProperty(value = "学校供应商高亮显示")
    private String fkInstitutionProviderNameHighline;

    @ApiModelProperty(value = "活动年份高亮显示")
    private String eventYearHighline;

    @ApiModelProperty(value = "invoice币种高亮显示")
    private String fkCurrencyTypeNumInvoiceHighline;

    @ApiModelProperty(value = "invoice金额高亮显示")
    private String invoiceAmountHighline;

    @ApiModelProperty(value = "活动费币种高亮显示")
    private String fkCurrencyTypeNumEventHighline;

    @ApiModelProperty(value = "活动费金额高亮显示")
    private String eventAmountHighline;

    @ApiModelProperty(value = "invoice名目高亮显示")
    private String invoiceSummaryHighline;

    @ApiModelProperty(value = "invoice收件人高亮显示")
    private String invoiceContactPersonHighline;

    @ApiModelProperty(value = "invoice收件人Email高亮显示")
    private String invoiceContactEmailHighline;

    @ApiModelProperty(value = "摘要高亮显示")
    private String fkEventSummaryHighline;

    @ApiModelProperty(value = "备注高亮显示")
    private String remarkHighline;

}

package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("m_convention_award_code")
public class ConventionAwardCode extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 峰会Id
     */
    @ApiModelProperty(value = "峰会Id")
    @Column(name = "fk_convention_id")
    private Long fkConventionId;
    /**
     * 抽奖号码（预生成，可自定义前序字符）
     */
    @ApiModelProperty(value = "抽奖号码（预生成，可自定义前序字符）")
    @Column(name = "award_code")
    private String awardCode;
    /**
     * 峰会参展人员Id（抽奖码占用人）
     */
    @ApiModelProperty(value = "峰会参展人员Id（抽奖码占用人）")
    @Column(name = "fk_convention_person_id")
    private Long fkConventionPersonId;
    /**
     * 系统支付单号，guid
     */
    @ApiModelProperty(value = "系统支付单号，guid")
    @Column(name = "pay_system_order_num")
    private String paySystemOrderNum;
    /**
     * 微信支付单号
     */
    @ApiModelProperty(value = "微信支付单号")
    @Column(name = "pay_wechat_order_num")
    private String payWechatOrderNum;
    /**
     * 使用状态：0锁定/1已购买未使用/2已购买已使用（中奖后不能再参与抽奖）
     */
    @ApiModelProperty(value = "使用状态：0锁定/1已购买未使用/2已购买已使用（中奖后不能再参与抽奖）")
    @Column(name = "use_type")
    private Integer useType;
}
package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2022/5/25
 * @TIME: 15:25
 * @Description:
 **/
@Data
public class AgentApplicationRankingDto {
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    /**
     * 国家ID列表,员工（登录人）业务国家
     */
    @ApiModelProperty(value = "国家ID列表,员工（登录人）业务国家")
    private List<Long> fkAreaCountryIdList;

    /**
     * 大区Id
     */
    @ApiModelProperty(value = "大区Id")
    private Long fkAreaRegionId;

    /**
     * bd名称
     */
    @ApiModelProperty(value = "bd名称")
    private String bdName;

    /**
     * BD编号
     */
    @ApiModelProperty(value = "BD编号")
    private String bdCode;

    /**
     * 排序类型
     */
    @ApiModelProperty(value = "排序类型：1-按新建学生数排序；2-按定校量排序；3-按成功入学量排序")
    private Integer sortType;

    /**
     * 绑定BD员工ids
     */
    @ApiModelProperty(value = "绑定BD员工ids")
    private List<Long> fkStaffIds;

    /**
     * 性质列表：公司/个人/工作室/国际学校/其他
     */
    @ApiModelProperty(value = "性质列表：公司/个人/工作室/国际学校/其他")
    private List<String> natureList;

    /**
     * 国家列表
     */
    @ApiModelProperty(value = "国家列表")
    private List<Long> fkCountryIds;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;
    /**
     * 开学时间
     */
    @ApiModelProperty(value = "开学时间" )
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startOpeningTime;

    /**
     * 开学时间
     */
    @ApiModelProperty(value = "开学时间" )
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endOpeningTime;

    /**
     * 代理ids
     */
    @ApiModelProperty(value = "代理ids")
    private Set<Long> fkAgentIds;


    /**
     * 员工以及旗下员工所创建的代理ids
     */
    @ApiModelProperty(value = "员工以及旗下员工所创建的代理ids")
    private List<Long> staffFollowerIds;

    /**
     * 所属集团Id
     */
    @ApiModelProperty(value = "所属集团Id")
    private List<Long> fkInstitutionGroupIds;



    /**
     * 业绩统计GEA定校量相关步骤列表
     */
    @ApiModelProperty(value = "业绩统计GEA定校量相关步骤列表")
    private List<String> geaConfirmationStatisticsStepList;

    /**
     * 业绩统计IAE定校量相关步骤列表
     */
    @ApiModelProperty(value = "业绩统计IAE定校量相关步骤列表")
    private List<String> iaeConfirmationStatisticsStepList;

    /**
     * 业绩统计GEA成功量相关步骤列表
     */
    @ApiModelProperty(value = "业绩统计GEA成功量相关步骤列表")
    private List<String> geaSuccessStatisticsStepList;

    /**
     * 业绩统计IAE成功量相关步骤列表
     */
    @ApiModelProperty(value = "业绩统计IAE成功量相关步骤列表")
    private List<String> iaeSuccessStatisticsStepList;

    @ApiModelProperty(value = "代理名称/编号")
    private String agentNameOrNum;
}

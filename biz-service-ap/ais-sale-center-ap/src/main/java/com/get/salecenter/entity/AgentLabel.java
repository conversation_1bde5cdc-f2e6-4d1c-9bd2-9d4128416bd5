package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("r_agent_label")
public class AgentLabel extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "代理标记关系Id")
    @Column(name = "id")
    private Long id;

    @ApiModelProperty(value = "学生代理Id")
    @Column(name = "fk_agent_id")
    private Long fkAgentId;

    @ApiModelProperty(value = "代理电邮地址")
    @Column(name = "email")
    private String email;

    @ApiModelProperty(value = "标签Id")
    @Column(name = "fk_label_id")
    private Long fkLabelId;
}
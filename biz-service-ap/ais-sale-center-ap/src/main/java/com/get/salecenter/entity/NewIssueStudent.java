package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;

@Data
@TableName("m_student")
public class NewIssueStudent extends BaseEntity implements Serializable {
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @TableField("fk_company_id")
    private Long fkCompanyId;

    /**
     * 回执号
     */
    @ApiModelProperty(value = "回执号")
    @TableField("receipt_code")
    private String receiptCode;

    /**
     * 代理id
     */
    @ApiModelProperty(value = "代理id")
    @TableField("fk_agent_id")
    private Long fkAgentId;

    /**
     * 代理联系电邮
     */
    @ApiModelProperty(value = "代理联系电邮")
    @TableField("agent_contact_email")
    private String agentContactEmail;

    /**
     * 学生中文名和申请页通用
     */
    @ApiModelProperty(value = "学生中文名和申请页通用")
    @TableField("name_zh")
    private String nameZh;

    /**
     * 学生英文名和申请页通用
     */
    @ApiModelProperty(value = "学生英文名和申请页通用")
    @TableField("name_en")
    private String nameEn;

    /**
     * 姓 （拼音）和申请页通用
     */
    @ApiModelProperty(value = "姓 （拼音）和申请页通用")
    @TableField("last_name")
    private String lastName;

    /**
     * 名 （拼音）和申请页通用
     */
    @ApiModelProperty(value = "名 （拼音）和申请页通用")
    @TableField("first_name")
    private String firstName;

    /**
     * 联系电话区号
     */
    @ApiModelProperty(value = "联系电话区号")
    @TableField("phone_region")
    private String phoneRegion;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    @TableField("phone_number")
    private String phoneNumber;

    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    @TableField("email")
    private String email;

    /**
     * 邮箱密码
     */
    @ApiModelProperty(value = "邮箱密码")
    @TableField("email_password")
    private String emailPassword;

    /**
     * 学生的国家
     */
    @ApiModelProperty(value = "学生的国家")
    @TableField("fk_student_app_country_id_from")
    private String fkStudentAppCountryIdFrom;

    /**
     * 学生的区域/子国家（英国）
     */
    @ApiModelProperty(value = "学生的区域/子国家（英国）")
    @TableField("fk_area_region_id_from")
    private String fkAreaRegionIdFrom;

    /**
     * 学生的省份
     */
    @ApiModelProperty(value = "学生的省份")
    @TableField("fk_area_state_id_from")
    private String fkAreaStateIdFrom;

    /**
     * 学生的城市
     */
    @ApiModelProperty(value = "学生的城市")
    @TableField("fk_area_city_id_from")
    private String fkAreaCityIdFrom;

    /**
     * 学生的城市区域
     */
    @ApiModelProperty(value = "学生的城市区域")
    @TableField("fk_area_city_division_id_from")
    private String fkAreaCityDivisionIdFrom;

    /**
     * 学生联系地址1
     */
    @ApiModelProperty(value = "学生联系地址1")
    @TableField("contact_address_1")
    private String contactAddress1;

    /**
     * 学生联系地址2
     */
    @ApiModelProperty(value = "学生联系地址2")
    @TableField("contact_address_2")
    private String contactAddress2;

    /**
     * 学生联系地址邮编
     */
    @ApiModelProperty(value = "学生联系地址邮编")
    @TableField("contact_postcode")
    private String contactPostcode;

    /**
     * 生日
     */
    @ApiModelProperty(value = "生日")
    @TableField("birthday")
    private Date birthday;

    /**
     * 性别：0女/1男
     */
    @ApiModelProperty(value = "性别：0女/1男")
    @TableField("gender")
    private Integer gender;

    /**
     * 称谓
     */
    @ApiModelProperty(value = "称谓")
    @TableField("title")
    private Integer title;

    /**
     * 学生国籍
     */
    @ApiModelProperty(value = "学生国籍")
    @TableField("fk_student_app_country_id")
    private Long fkStudentAppCountryId;

    /**
     * 学生血统
     */
    @ApiModelProperty(value = "学生血统")
    @TableField("fk_student_app_ancestry_id")
    private Long fkStudentAppAncestryId;

    /**
     * 学生母语
     */
    @ApiModelProperty(value = "学生母语")
    @TableField("fk_student_app_mother_language_id")
    private Long fkStudentAppMotherLanguageId;

    /**
     * 出生国家
     */
    @ApiModelProperty(value = "出生国家")
    @TableField("fk_student_app_country_id_birth")
    private Long fkStudentAppCountryIdBirth;

    /**
     * 出生城市
     */
    @ApiModelProperty(value = "出生城市")
    @TableField("city_birth")
    private String cityBirth;

    /**
     * 是否自出生便在此居住?1是/0否
     */
    @ApiModelProperty(value = "是否自出生便在此居住?1是/0否")
    @TableField("is_lived_since_birth")
    private Boolean isLivedSinceBirth;

    /**
     * 居住国家
     */
    @ApiModelProperty(value = "居住国家")
    @TableField("fk_student_app_country_id_live")
    private Long fkStudentAppCountryIdLive;

    /**
     * 移居时间（居住国家选择否显示）
     */
    @ApiModelProperty(value = "移居时间（居住国家选择否显示）")
    @TableField("migration_time")
    private Date migrationTime;

    /**
     * 取居留权时间（居住国家选择否显示）
     */
    @ApiModelProperty(value = "取居留权时间（居住国家选择否显示）")
    @TableField("staying_right_time")
    private Date stayingRightTime;

    /**
     * 护照签发国家
     */
    @ApiModelProperty(value = "护照签发国家")
    @TableField("fk_student_app_country_id_passport")
    private Long fkStudentAppCountryIdPassport;

    /**
     * 是否有护照?1是 /0否
     */
    @ApiModelProperty(value = "是否有护照?1是 /0否")
    @TableField("is_have_passport")
    private Boolean isHavePassport;

    /**
     * 签发日期
     */
    @ApiModelProperty(value = "签发日期")
    @TableField("passport_issue_date")
    private Date passportIssueDate;

    /**
     * 有效期至
     */
    @ApiModelProperty(value = "有效期至")
    @TableField("passport_issue_expried")
    private Date passportIssueExpried;

    /**
     * 护照号码
     */
    @ApiModelProperty(value = "护照号码")
    @TableField("passport_number")
    private String passportNumber;

    /**
     * 跟进人电话号码区号1
     */
    @ApiModelProperty(value = "跟进人电话号码区号1")
    @TableField("follow_up_phone_code_1")
    private String followUpPhoneCode1;

    /**
     * 跟进人电话号码区号2
     */
    @ApiModelProperty(value = "跟进人电话号码区号2")
    @TableField("follow_up_phone_code_2")
    private String followUpPhoneCode2;

    /**
     * 跟进人电话号码区号3
     */
    @ApiModelProperty(value = "跟进人电话号码区号3")
    @TableField("follow_up_phone_code_3")
    private String followUpPhoneCode3;

    /**
     * 跟进人电话号码1
     */
    @ApiModelProperty(value = "跟进人电话号码1")
    @TableField("follow_up_phone_number_1")
    private String followUpPhoneNumber1;

    /**
     * 跟进人电话号码2
     */
    @ApiModelProperty(value = "跟进人电话号码2")
    @TableField("follow_up_phone_number_2")
    private String followUpPhoneNumber2;

    /**
     * 跟进人电话号码3
     */
    @ApiModelProperty(value = "跟进人电话号码3")
    @TableField("follow_up_phone_number_3")
    private String followUpPhoneNumber3;

    /**
     * 跟进人电邮地址1
     */
    @ApiModelProperty(value = "跟进人电邮地址1")
    @TableField("follow_up_email_1")
    private String followUpEmail1;

    /**
     * 跟进人电邮地址2
     */
    @ApiModelProperty(value = "跟进人电邮地址2")
    @TableField("follow_up_email_2")
    private String followUpEmail2;

    /**
     * 跟进人电邮地址3
     */
    @ApiModelProperty(value = "跟进人电邮地址3")
    @TableField("follow_up_email_3")
    private String followUpEmail3;

    /**
     * 父亲的姓
     */
    @ApiModelProperty(value = "父亲的姓")
    @TableField("father_last_name")
    private String fatherLastName;

    /**
     * 父亲的名
     */
    @ApiModelProperty(value = "父亲的名")
    @TableField("father_first_name")
    private String fatherFirstName;

    /**
     * 父亲邮箱
     */
    @ApiModelProperty(value = "父亲邮箱")
    @TableField("father_email")
    private String fatherEmail;

    /**
     * 父亲联系电话
     */
    @ApiModelProperty(value = "父亲联系电话")
    @TableField("father_phone_number")
    private String fatherPhoneNumber;

    /**
     * 母亲的姓
     */
    @ApiModelProperty(value = "母亲的姓")
    @TableField("monter_last_name")
    private String monterLastName;

    /**
     * 母亲的名
     */
    @ApiModelProperty(value = "母亲的名")
    @TableField("monter_first_name")
    private String monterFirstName;

    /**
     * 母亲邮箱
     */
    @ApiModelProperty(value = "母亲邮箱")
    @TableField("monter_email")
    private String monterEmail;

    /**
     * 母亲联系电话
     */
    @ApiModelProperty(value = "母亲联系电话")
    @TableField("monter_phone_number")
    private String monterPhoneNumber;

    /**
     * 父母联系地址邮编
     */
    @ApiModelProperty(value = "父母联系地址邮编")
    @TableField("parent_contact_postcode")
    private String parentContactPostcode;

    /**
     * 父母联系地址1
     */
    @ApiModelProperty(value = "父母联系地址1")
    @TableField("parent_contact_address1")
    private String parentContactAddress1;

    /**
     * 父母联系地址2
     */
    @ApiModelProperty(value = "父母联系地址2")
    @TableField("parent_contact_address2")
    private String parentContactAddress2;

    /**
     * 父母的国家
     */
    @ApiModelProperty(value = "父母的国家")
    @TableField("fk_student_app_country_id_parent")
    private String fkStudentAppCountryIdParent;

    /**
     * 父母的省份
     */
    @ApiModelProperty(value = "父母的省份")
    @TableField("fk_area_state_id_parent")
    private String fkAreaStateIdParent;

    /**
     * 父母的城市
     */
    @ApiModelProperty(value = "父母的城市")
    @TableField("fk_area_city_id_parent")
    private String fkAreaCityIdParent;

    /**
     * 是否需要安排住宿：0否/1是
     */
    @ApiModelProperty(value = "是否需要安排住宿：0否/1是")
    @TableField("is_need_accommodation")
    private Boolean isNeedAccommodation;

    /**
     * 住宿类型：1=Homestay寄宿家庭,2=University Accommodation 學校宿舍，3=Off Campus Accommodation 校外宿舍
     */
    @ApiModelProperty(value = "住宿类型：1=Homestay寄宿家庭,2=University Accommodation 學校宿舍，3=Off Campus Accommodation 校外宿舍")
    @TableField("accommodation_type")
    private Integer accommodationType;

    /**
     * 学生是否吸烟（寄宿家庭）
     */
    @ApiModelProperty(value = "学生是否吸烟（寄宿家庭）")
    @TableField("is_accommodation_smoke")
    private Boolean isAccommodationSmoke;

    /**
     * 是否介意有宠物的家庭（寄宿家庭）
     */
    @ApiModelProperty(value = "是否介意有宠物的家庭（寄宿家庭）")
    @TableField("is_accommodation_pet")
    private Boolean isAccommodationPet;

    /**
     * 是否介意有小孩家庭（寄宿家庭）
     */
    @ApiModelProperty(value = "是否介意有小孩家庭（寄宿家庭）")
    @TableField("is_accommodation_children")
    private Boolean isAccommodationChildren;

    /**
     * 你對食物是否有特殊要求 例如：素食  0否/1是（寄宿家庭）
     */
    @ApiModelProperty(value = "你對食物是否有特殊要求 例如：素食  0否/1是（寄宿家庭）")
    @TableField("is_accommodation_food")
    private Boolean isAccommodationFood;

    /**
     * 食物特殊要求详细说明
     */
    @ApiModelProperty(value = "食物特殊要求详细说明")
    @TableField("accommodation_food_note")
    private String accommodationFoodNote;

    /**
     * 是否还有其他要求（寄宿家庭）
     */
    @ApiModelProperty(value = "是否还有其他要求（寄宿家庭）")
    @TableField("is_accommodation_other")
    private Boolean isAccommodationOther;

    /**
     * 其他要求说明
     */
    @ApiModelProperty(value = "其他要求说明")
    @TableField("accommodation_other_note")
    private String accommodationOtherNote;

    /**
     * 你是否需要安排接机
     */
    @ApiModelProperty(value = "你是否需要安排接机")
    @TableField("is_need_pick_up")
    private Boolean isNeedPickUp;

    /**
     * 状态：0新建/1已提交/2需补件/3加申/4在线表单提交
     */
    @ApiModelProperty(value = "状态：0新建/1已提交/2需补件/3加申/4在线表单提交")
    @TableField("status")
    private Integer status;

    /**
     * 创建用户id
     */
    @ApiModelProperty(value = "创建用户id")
    @TableField("gmt_create_user_id")
    private Long gmtCreateUserId;

    private static final long serialVersionUID = 1L;
}
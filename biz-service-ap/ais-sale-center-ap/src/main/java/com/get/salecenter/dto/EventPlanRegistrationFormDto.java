package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2023/12/19
 * @TIME: 12:42
 * @Description:
 **/
@Data
public class EventPlanRegistrationFormDto {
    @ApiModelProperty(value = "报名名册Id")
    private Long Id;

    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    @ApiModelProperty(value = "活动年度计划Id")
    private Long fkEventPlanId;

    @ApiModelProperty(value = "学校提供商Id")
    private Long fkInstitutionProviderId;

    @ApiModelProperty(value = "发票建议币种")
    private String fkCurrencyTypeNumInvoice;

    @ApiModelProperty(value = "联系人列表")
    private List<EventPlanRegistrationContactPersonFormDto> personFormVoList;

    @ApiModelProperty(value = "线上项目Ids")
    private List<Long> fkEventPlanThemeOnlineIds;

    @ApiModelProperty(value = "线下项目子项Ids")
    private List<Long> fkEventPlanThemeOfflineItemIds;

    @ApiModelProperty(value = "线下专访Ids")
    private List<Long> fkEventPlanThemeWorkShopIds;
}

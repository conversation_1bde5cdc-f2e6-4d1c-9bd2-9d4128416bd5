package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @DATE: 2021/6/16
 * @TIME: 10:49
 * @Description:
 **/
@Data
@TableName("u_enrol_failure_reason")
public class EnrolFailureReason extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 入学失败原因名称
     */
    @ApiModelProperty(value = "入学失败原因名称")
    @Column(name = "reason_name")
    private String reasonName;
    /**
     * 原因Key
     */
    @ApiModelProperty(value = "原因Key")
    @Column(name = "reason_key")
    private String reasonKey;
    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;
}

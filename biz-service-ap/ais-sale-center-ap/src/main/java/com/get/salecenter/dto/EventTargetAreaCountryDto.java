package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @author: Sea
 * @create: 2020/12/7 18:10
 * @verison: 1.0
 * @description:
 */
@Data
public class EventTargetAreaCountryDto extends BaseVoEntity{
    /**
     * 活动Id
     */
    @ApiModelProperty(value = "活动Id")
    private Long fkEventId;

    /**
     * 活动目标对象国家Id
     */
    @ApiModelProperty(value = "活动目标对象国家Id")
    private Long fkAreaCountryId;

   
}

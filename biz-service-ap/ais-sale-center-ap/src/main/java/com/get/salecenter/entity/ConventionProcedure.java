package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;

@Data
@TableName("m_convention_procedure")
public class ConventionProcedure extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 峰会Id
     */
    @ApiModelProperty(value = "峰会Id")
    @Column(name = "fk_convention_id")
    private Long fkConventionId;
    /**
     * 流程涉及分配桌子类型key(可无)
     */
    @ApiModelProperty(value = "流程涉及分配桌子类型key(可无)")
    @Column(name = "fk_table_type_key")
    private String fkTableTypeKey;
    /**
     * 流程主题
     */
    @ApiModelProperty(value = "流程主题")
    @Column(name = "subject")
    private String subject;
    /**
     * 举行地点
     */
    @ApiModelProperty(value = "举行地点")
    @Column(name = "venue")
    private String venue;
    /**
     * 流程描述
     */
    @ApiModelProperty(value = "流程描述")
    @Column(name = "description")
    private String description;
    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    @Column(name = "start_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date startTime;
    /**
     * 计划结束时间
     */
    @ApiModelProperty(value = "计划结束时间")
    @Column(name = "end_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date endTime;
    /**
     * 步骤索引
     */
    @ApiModelProperty(value = "步骤索引")
    @Column(name = "step_index")
    private Integer stepIndex;
}
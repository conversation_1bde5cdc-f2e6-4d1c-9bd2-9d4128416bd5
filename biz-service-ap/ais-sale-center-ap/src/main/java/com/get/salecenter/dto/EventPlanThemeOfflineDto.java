package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2023/12/14
 * @TIME: 14:20
 * @Description:
 **/
@Data
public class EventPlanThemeOfflineDto extends BaseVoEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "活动计划主题Id")
    private Long fkEventPlanThemeId;

    @ApiModelProperty(value = "活动适用业务国家地区名称（表格展示名称）")
    private String areaCountryName;

    @ApiModelProperty(value = "活动目标对象国家Ids，多选用逗号隔开：1,2,3")
    private List<Long> fkAreaCountryIds;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    @ApiModelProperty(value = "是否激活：0否/1是，若否需要灰掉活动项目")
    private Boolean isActive;

    @ApiModelProperty("子项目列表")
    private List<EventPlanThemeOfflineItemDto> offlineItemList;

 
}

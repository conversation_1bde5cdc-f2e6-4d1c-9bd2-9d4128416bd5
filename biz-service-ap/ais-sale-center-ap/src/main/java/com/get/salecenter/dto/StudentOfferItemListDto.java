package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseEntity;
import com.get.core.mybatis.base.BaseVoEntity;
import com.get.permissioncenter.dto.ConfigRemindDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class StudentOfferItemListDto extends BaseEntity {


    @NotNull(message = "页码不能为空")
    @ApiModelProperty(value = "页码")
    private Integer pageNumber;

    @ApiModelProperty(value = "偏移量")
    private Integer offset;

    @NotNull(message = "显示条数不能为空")
    @ApiModelProperty(value = "页面大小")
    private Integer pageSize;

    @ApiModelProperty(value = "是否成功")
    private Boolean isSuccess;

    @ApiModelProperty(value = "导出列表的staffId")
    private Long exportStaffId;

    @ApiModelProperty(value = "导出列表的国家ids")
    private List<Long> exportCountryIds;

    @ApiModelProperty(value = "导出列表的语言")
    private String exportLocale;

    /**
     * 学生申请方案项目父Id
     */
    @ApiModelProperty(value = "学生申请方案项目父Id")
    @Column(name = "fk_parent_student_offer_item_id")
    private Long fkParentStudentOfferItemId;

    /**
     * 子学生申请方案项目
     */
    @ApiModelProperty(value = "子学生申请方案项目")
    private List<StudentOfferItemDto> studentOfferItemVos;
    /**
     * 学生Id
     */
    @ApiModelProperty(value = "学生Id")
    @NotNull(message = "学生Id不能为空", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    private Long fkStudentId;

    /**
     * 代理Id（业绩绑定）
     */
    @ApiModelProperty(value = "代理Id（业绩绑定）")
    @NotNull(message = "代理Id不能为空", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    private Long fkAgentId;

    /**
     * 员工Id（业绩绑定，BD）
     */
    @ApiModelProperty(value = "员工Id（业绩绑定，BD）")
    @NotNull(message = "员工Id（业绩绑定，BD）不能为空", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    private Long fkStaffId;

    /**
     * 学生申请方案Id
     */
    @ApiModelProperty(value = "学生申请方案Id")
    @NotNull(message = "学生申请方案Id不能为空", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    private Long fkStudentOfferId;

    /**
     * 学生国籍所在国家Id
     */
    @ApiModelProperty(value = "学生国籍所在国家Id")
    @Column(name = "fk_area_country_id_nationality")
    private Long fkAreaCountryIdNationality;

    /**
     * 学生国籍所在国家名称
     */
    @ApiModelProperty(value = "学生国籍所在国家名称")
    @Column(name = "fk_area_country_id_nationality")
    private String fkAreaCountryNameNationality;

    /**
     * 学生现居所在国家Id
     */
    @ApiModelProperty(value = "学生现居所在国家Id")
    @NotNull(message = "国家Id不能为空", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    private Long fkAreaCountryId;

    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
//    @NotNull(message = "学校Id不能为空", groups = {Add.class, Update.class})
    private Long fkInstitutionId;

    /**
     * 学校校区Id
     */
    @ApiModelProperty(value = "学校校区Id")
    @Column(name = "fk_institution_zone_id")
    private Long fkInstitutionZoneId;

    /**
     * 课程Id
     */
    @ApiModelProperty(value = "课程Id")
//    @NotNull(message = "课程Id不能为空", groups = {Add.class, Update.class})
    private Long fkInstitutionCourseId;

    /**
     * 自定义课程Id
     */
    @ApiModelProperty(value = "自定义课程Id")
    private Long fkInstitutionCourseCustomId;

    /**
     * 学校提供商Id
     */
    @ApiModelProperty(value = "学校提供商Id")
//    @NotNull(message = "学校提供商Id不能为空", groups = {Add.class, Update.class})
    private Long fkInstitutionProviderId;

    /**
     * 学校提供商Ids
     */
    @ApiModelProperty(value = "学校提供商Ids")
    private List<Long> fkInstitutionProviderIds;

    @ApiModelProperty(value = "学校提供商名字")
    private String fkInstitutionProviderName;

    /**
     * 渠道来源Id
     */
    @ApiModelProperty(value = "渠道来源Id")
    private Long fkInstitutionChannelId;

    /**
     * 申请方案项目编号
     */
    @ApiModelProperty(value = "申请方案项目编号")
    private String num;

    @ApiModelProperty(value = "学生编号")
    private String studentNum;

    /**
     * 学生ID
     */
    @ApiModelProperty(value = "学生ID")
    private String studentId;

    /**
     * 课程长度类型(0周、1学期、2年)
     */
    @ApiModelProperty(value = "课程长度类型(0周、1学期、2年、3学期)")
    private Integer durationType;

    /**
     * 课程长度
     */
    @ApiModelProperty(value = "课程长度")
    private BigDecimal duration;

    /**
     * 开学时间
     */
    @ApiModelProperty(value = "开学时间")
    @NotNull(message = "开学时间不能为空", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date openingTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
//    @NotNull(message = "结束时间不能为空", groups = {Add.class, Update.class})
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date closingTime;

    /**
     * 支付押金截止时间
     */
    @ApiModelProperty(value = "支付押金截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date depositDeadline;

    /**
     * 接受Offer截止时间
     */
    @ApiModelProperty(value = "接受Offer截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date acceptOfferDeadline;

    /**
     * 是否主课程：0否/1是（一套方案，只有一个主课程）
     */
    @ApiModelProperty(value = "是否主课程：0否/1是（一套方案，只有一个主课程）")
    private Boolean isMain;

    /**
     * 是否后续课程：0否/1是
     */
    @ApiModelProperty(value = "是否后续课程：0否/1是")
    private Boolean isFollow;


    /**
     * 是否减免学分：0否/1是
     */
    @ApiModelProperty(value = "是否减免学分：0否/1是")
    private Boolean isCreditExemption;

    /**
     * 是否加申，0否/1是
     */
    @ApiModelProperty(value = "是否加申，0否/1是")
    @Column(name = "is_add_app")
    private Boolean isAddApp;

    /**
     * 是否步骤更随主课，0否/1是
     */
    @ApiModelProperty(value = "是否步骤更随主课，0否/1是")
    @Column(name = "is_step_follow")
    private Boolean isStepFollow;

    /**
     * 是否记录跟进步骤，0否/1是
     */
    @ApiModelProperty(value = "是否记录跟进步骤，0否/1是")
    @Column(name = "is_add_step")
    private Boolean isAddStep;

    /**
     * 申请方式：0网申/1扫描/2原件邮递/3其他
     */
    @ApiModelProperty(value = "申请方式：0网申/1扫描/2原件邮递/3其他")
    private Integer appMethod;

    /**
     * 申请备注（网申信息）
     */
    @ApiModelProperty(value = "申请备注（网申信息）")
    private String appRemark;

    /**
     * 学习计划业务状态(多选)：0获得第一阶段佣金、1获得第二阶段佣金、2获得第三阶段佣金、3获得第四阶段佣金 / 4只读第一阶段、5只读第二阶段、6只读第三阶段、7只读第四阶段
     */
    @ApiModelProperty(value = "学习计划业务状态(多选)：0获得第一阶段佣金、1获得第二阶段佣金、2获得第三阶段佣金、3获得第四阶段佣金 / 4只读第一阶段、5只读第二阶段、6只读第三阶段、7只读第四阶段")
    private String conditionType;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 状态：0关闭/1打开
     */
    @ApiModelProperty(value = "状态：0关闭/1打开")
    private Integer status;

    /**
     * 旧系统学校名称
     */
    @ApiModelProperty(value = "旧系统学校名称")
    private String oldInstitutionName;

    /**
     * 旧系统学校全称
     */
    @ApiModelProperty(value = "旧系统学校全称")
    private String oldInstitutionFullName;

    /**
     * 旧系统课程名称
     */
    @ApiModelProperty(value = "旧系统课程名称")
    private String oldCourseCustomName;

    /**
     * 旧系统课程专业等级名称
     */
    @ApiModelProperty(value = "旧系统课程专业等级名称")
    private String oldCourseMajorLevelName;

    /**
     * 旧系统课程类型名称
     */
    @ApiModelProperty(value = "旧系统课程类型名称")
    @Column(name = "old_course_type_name")
    private String oldCourseTypeName;

    /**
     * 学费金额
     */
    @ApiModelProperty(value = "学费金额")
    // @NotNull(message = "学费不能为空", groups = {Add.class, Update.class})
    private BigDecimal tuitionAmount;

    /**
     * 课程官网URL
     */
    @ApiModelProperty(value = "课程官网URL")
    private String courseWebsite;

    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;

    /**
     * 学校Id(前置学校)
     */
    @ApiModelProperty(value = "学校Id(前置学校)")
    private Long fkPreInstitutionId;

    /**
     * 学校集团Id(前置集团)
     */
    @ApiModelProperty(value = "学校集团Id(前置集团)")
    private Long fkPreInstitutionGroupId;

    /**
     * 专业等级Id(前置等级)
     */
    @ApiModelProperty(value = "专业等级Id(前置等级)")
    private Long fkPreMajorLevelId;

    /**
     * 入学失败原因Id
     */
    @ApiModelProperty(value = "入学失败原因Id")
    private Long fkEnrolFailureReasonId;

    /**
     * 其他入学失败原因
     */
    @ApiModelProperty(value = "其他入学失败原因")
    private String otherFailureReason;

    /**
     * 是否延迟入学标记：0否/1是
     */
    @ApiModelProperty(value = "是否延迟入学标记：0否/1是")
    private Boolean isDeferEntrance;

    /**
     * 延迟入学时间
     */
    @ApiModelProperty(value = "延迟入学时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private List<Date> deferEntranceTimes;

    /**
     * 学习模式：枚举定义：0未定/1面授/2网课
     */
    @ApiModelProperty(value = "学习模式：枚举定义：0未定/1面授/2网课")
    private Integer learningMode;

    /**
     * 提供商下拉框副id（渠道id）
     */
    @ApiModelProperty(value = "提供商下拉框副id（渠道id）")
    private Long deputyId;

    /**
     * 旧数据id(gea)
     */
    @ApiModelProperty(value = "旧数据id(gea)")
    @Column(name = "id_gea")
    private String idGea;

    /**
     * 旧数据id(iae)
     */
    @ApiModelProperty(value = "旧数据id(iae)")
    @Column(name = "id_iae")
    private String idIae;

    /**
     * 公司id
     */
    @ApiModelProperty(value = "公司id")
    private Long fkCompanyId;

    /**
     * 学生姓名
     */
    @ApiModelProperty("学生姓名")
    private String studentName;

    /**
     * 开学时间（开始范围）
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "开学时间（开始范围）")
    private Date startTime;

    /**
     * 开学时间（结束范围）
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "开学时间（结束范围）")
    private Date endTime;
    /**
     * 开学日期开始时间
     */
    @ApiModelProperty(value = "开学日期开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date beginOpeningTime;
    /**
     * 开学日期结束时间
     */
    @ApiModelProperty(value = "开学日期结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endOpeningTime;
    /**
     * 申请创建时间（开始范围）
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "申请创建时间（开始范围）")
    private Date createStartTime;

    /**
     * 申请创建时间（结束范围）
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "申请创建时间（结束范围）")
    private Date createEndTime;

    /**
     * 步骤状态变更时间（开始范围）
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "步骤状态变更时间（开始范围）")
    private Date offerStepStartTime;

    /**
     * 步骤状态变更时间（结束范围）
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "步骤状态变更时间（结束范围）")
    private Date offerStepEndTime;

    /**
     * 申请步骤id
     */
    @ApiModelProperty(value = "申请步骤id")
    private Long fkStudentOfferItemStepId;

    /**
     * 学生ids
     */
    @ApiModelProperty(value = "学生ids")
    private String studentIds;

    /**
     * 学生ids
     */
    @ApiModelProperty(value = "学生ids")
    private List<Long> ids;

    /**
     * 状态：0关闭/1打开
     */
    @ApiModelProperty(value = "状态：0关闭/1打开")
    private List<Integer> statusList;

    /**
     * 多选步骤
     */
    @ApiModelProperty(value = "多选步骤")
    private List<Integer> stepOrderList;

    /**
     * 学生查询条件
     */
    @ApiModelProperty(value = "学生查询条件")
    private StudentDto studentVo;
    @ApiModelProperty(value = "学校名")
    private String schoolName;

    /**
     * 学校IDS集合
     */
    @ApiModelProperty(value = "学校IDS集合")
    private List<Long> institutionIds;

    @ApiModelProperty(value = "申请课程名")
    private String courseName;

    @ApiModelProperty(value = "课程名称搜索的ids集合")
    private List<Long> courseIds;

    @ApiModelProperty(value = "代理名")
    private String agentName;

    @ApiModelProperty(value = "bd名")
    private String bdName;

    @ApiModelProperty(value = "是否已经创建应收应付")
    private String statusKey;

    @ApiModelProperty(value = "学校id列表")
    private List<Long> fkInstitutionIds;

    @ApiModelProperty(value = "课程id列表")
    private List<Long> fkInstitutionCourseIds;

    @ApiModelProperty(value = "项目成员id列表")
    private List<Long> fkProjectRoleIds;

    @ApiModelProperty(value = "学生来源")
    private String studentSource;

    @ApiModelProperty(value = "电话")
    private String tel;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "项目成员")
    private List<Long> projectRoleStaffIds;

    @ApiModelProperty(value = "是否延迟入学 true:是  false:否")
    private Boolean deferFlag;

    @ApiModelProperty(value = "一键申请状态")
    private String fkIssueRpaOrderId;

    /**
     * 公司ids
     */
    @ApiModelProperty(value = "公司ids")
    private List<Long> fkCompanyIds;

    /**
     * 应收状态
     */
    @ApiModelProperty(value = "应收状态")
    private Integer arStatus;

    /**
     * 应付状态
     */
    @ApiModelProperty(value = "应付状态")
    private Integer apStatus;

    /**
     * 结算状态
     */
    @ApiModelProperty(value = "结算状态")
    private Integer settlementStatus;

    /**
     * 集团名称
     */
    @ApiModelProperty(value = "集团名称")
    private String groupName;

    /**
     * 渠道名称
     */
    @ApiModelProperty(value = "渠道名称")
    private String channelName;

    /**
     * 渠道Ids
     */
    @ApiModelProperty(value = "渠道Ids")
    private List<Long> channelIds;
    /**
     * 课程等级名称
     */
    @ApiModelProperty(value = "课程等级名称")
    private String courseTypeName;


    /**
     * 申请步骤状态
     */
    @ApiModelProperty(value = "申请步骤状态")
    private List<Long> currentState;

    @ApiModelProperty(value = "特殊申请步骤状态（默认传4）")
    private List<Long> specialCurrentState;
    /**
     * 课程等级id
     */
    @ApiModelProperty(value = "课程等级id")
    private Long majorLevelId;

    @ApiModelProperty("变更的步骤id")
    private List<Long> changeStepId;

    @ApiModelProperty("项目成员过滤")
    private List<Long> fkProjectMemberId;

    /**
     * 收齐状态：0未收/1部分已收/2已收齐
     */
    @ApiModelProperty(value = "收齐状态：0未收/1部分已收/2已收齐/3未收齐（未收+部分已收）")
    private Integer receiveStatus;

    /**
     * 付款状态：0/1/2：未付/已付部分/已付齐
     */
    @ApiModelProperty(value = "付款状态：0/1/2/3：未付/已付部分/已付齐/未付齐（未付+已付部分）")
    private Integer payableStatus;

    /**
     * 是否无佣金：0否/1是
     */
    @ApiModelProperty(value = "是否无佣金：0否/1是")
    @Column(name = "is_no_commission")
    private Boolean isNoCommission;

    @ApiModelProperty(value = "提醒时间vo")
    private ConfigRemindDto configRemindVo;


    @ApiModelProperty(value = "排序，0申请创建时间倒序/1学生名称正序")
    private Integer selectionSort;

    @ApiModelProperty(value = "学生佣金结算标记关键字")
    private String commissionMark;

  
}

package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.common.annotion.IgnoreRemind;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2022/7/26 15:16
 * @verison: 1.0
 * @description:
 */
@Data
public class EventIncentiveReminderDto {

    @IgnoreRemind
    @ApiModelProperty(value = "学校提供商Id")
    private Long fkInstitutionProviderId;

    @IgnoreRemind
    @ApiModelProperty(value = "业务国家多选")
    private List<Long> fkAreaCountryIdList;

    /**
     * 收到奖励时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "收到奖励时间")
    private Date receivingRewardTime;

    /**
     * 实际宣传时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "实际宣传时间")
    private Date actualPublicityTime;

    /**
     * 活动开始时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "活动开始时间")
    private Date eventStartTime;

    /**
     * 活动结束时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "活动结束时间")
    private Date eventEndTime;


    @ApiModelProperty(value = "符合奖励intake")
    private String accordWithIncentiveIntake;


    @ApiModelProperty(value = "激励政策")
    private String incentivePolicy;

    /**
     * 建议核对时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "建议核对时间")
    private Date suggestCheckTime;

    /**
     * 预计完成学生数
     */
    @ApiModelProperty(value = "预计完成学生数")
    private Integer expectTargetCount;

    /**
     * 实际完成学生数
     */
    @ApiModelProperty(value = "实际完成学生数")
    private Integer actualTargetCount;

    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;

    /**
     * 实际支付奖励金额
     */
    @ApiModelProperty(value = "实际支付奖励金额")
    private BigDecimal actualPayAmount;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    @IgnoreRemind
    @ApiModelProperty(value = "活动费用汇总id")
    private Long fkEventIncentiveId;

    @ApiModelProperty("活动标题")
    private String eventTitle;
}

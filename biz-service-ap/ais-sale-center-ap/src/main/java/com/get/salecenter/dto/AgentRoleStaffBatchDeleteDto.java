package com.get.salecenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 代理项目成员批量删除Vo类
 *
 * <AUTHOR>
 * @date 2021/8/2 11:36
 */
@Data
public class AgentRoleStaffBatchDeleteDto extends BaseVoEntity {

    /**
     * 代理Id
     */
    @ApiModelProperty(value = "代理Id")
    @NotNull(message = "代理Id不能为空", groups = {Add.class, Update.class})
    private Long fkAgentId;

    /**
     * 学生项目角色Id
     */
    @ApiModelProperty(value = "学生项目角色Id")
    @NotNull(message = "学生项目角色Id", groups = {Add.class, Update.class})
    private Long fkStudentProjectRoleId;

    /**
     * 员工Id
     */
    @ApiModelProperty(value = "员工Id")
    @NotNull(message = "学生项目角色Id", groups = {Add.class, Update.class})
    private Long fkStaffId;

    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    /**
     * 国家Id，可不选，不选为适合所有国家线(多选)
     */
    @ApiModelProperty(value = "国家Id，可不选，不选为适合所有国家线(多选)")
    private List<Long> fkAreaCountryIds;

}

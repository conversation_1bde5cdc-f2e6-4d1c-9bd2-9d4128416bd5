package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: Hardy
 * @create: 2024/10/10 11:27
 * @verison: 1.0
 * @description:
 */
@Data
public class LikeConventionEditDto {

    @ApiModelProperty(value = "新媒体推广助力赛Id")
    private Long id;

    @ApiModelProperty(value = "昵称")
    private String nickname;

    @ApiModelProperty(value = "社交平台id")
    private String platformAccount;

    @ApiModelProperty(value = "集赞数")
    private Integer likeCount;
}

package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@Data
@TableName("apl_order")
public class AplOldIssueOrder extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    @Column(name = "order_id")
    private Integer orderId;
    private String createName;
    private Integer stepId;
    /**
     * 课程等级
     */
    @ApiModelProperty(value = "课程等级")
    @Column(name = "step_level")
    private String stepLevel;
    private Integer stuId;
    /**
     * 课程ID
     */
    @ApiModelProperty(value = "课程ID")
    @Column(name = "cdet_id")
    private Integer cdetId;
    /**
     * 学生来源
     */
    @ApiModelProperty(value = "学生来源")
    @Column(name = "stu_source")
    private String stuSource;
    /**
     * Surname/Family Name 姓
     */
    @ApiModelProperty(value = "Surname/Family Name 姓")
    @Column(name = "stu_surname")
    private String stuSurname;
    /**
     * Given/First Name 名
     */
    @ApiModelProperty(value = "Given/First Name 名")
    @Column(name = "stu_first_name")
    private String stuFirstName;
    @ApiModelProperty(value = "Email Address 邮箱")
    @Column(name = "email")
    private String email;
    /**
     * Country or Region Name 国家或地区名稱(课程1)
     */
    @ApiModelProperty(value = "Country or Region Name 国家或地区名稱(课程1)")
    @Column(name = "cdet_schl_ctry")
    private String cdetSchlCtry;
    /**
     * 学校ID
     */
    @ApiModelProperty(value = "学校ID")
    @Column(name = "cdet_schl_id")
    private Integer cdetSchlId;
    /**
     * 选课名称
     */
    @ApiModelProperty(value = "选课名称")
    @Column(name = "cdet_crse_name")
    private String cdetCrseName;
    /**
     * Course Code 課程代碼
     */
    @ApiModelProperty(value = "Course Code 課程代碼")
    @Column(name = "cdet_crse_code")
    private String cdetCrseCode;
    /**
     * 開學時間
     */
    @ApiModelProperty(value = "開學時間")
    @Column(name = "cdet_star_date")
    private Date cdetStarDate;
    /**
     * 学生在学校注册账号名
     */
    @ApiModelProperty(value = "学生在学校注册账号名")
    @Column(name = "cdet_sch_login_name")
    private String cdetSchLoginName;
    /**
     * 学生在学校注册账号密码
     */
    @ApiModelProperty(value = "学生在学校注册账号密码")
    @Column(name = "cdet_sch_login_pw")
    private String cdetSchLoginPw;
    /**
     * 此记录创建时间
     */
    @ApiModelProperty(value = "此记录创建时间")
    @Column(name = "order_createtime")
    private Date orderCreatetime;
    /**
     * 发送到ROBOT时间
     */
    @ApiModelProperty(value = "发送到ROBOT时间")
    @Column(name = "order_sendtime")
    private Date orderSendtime;
    /**
     * N=New, C=Completed, E=Error
     */
    @ApiModelProperty(value = "N=New, C=Completed, E=Error")
    @Column(name = "order_status")
    private String orderStatus;
    /**
     * N=New, C=Completed, E=Error
     */
    @ApiModelProperty(value = "N=New, C=Completed, E=Error")
    @Column(name = "robot_status")
    private String robotStatus;
    /**
     * Robot 执行 Completed or Error  时间
     */
    @ApiModelProperty(value = "Robot 执行 Completed or Error  时间")
    @Column(name = "robot_status_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date robotStatusTime;
    /**
     * rpa記錄哪台RPA 出錯
     */
    @ApiModelProperty(value = "rpa記錄哪台RPA 出錯")
    @Column(name = "error_bp_no")
    private Integer errorBpNo;
    private Integer createrId;
    @ApiModelProperty(value = "备注")
    @Column(name = "people_msg")
    private String peopleMsg;
    /**
     * order创建状态信息
     */
    @ApiModelProperty(value = "order创建状态信息")
    @Column(name = "order_create_msg")
    private String orderCreateMsg;
    /**
     * Robot 执行 Completed or Error  反馈信息
     */
    @ApiModelProperty(value = "Robot 执行 Completed or Error  反馈信息")
    @Column(name = "robot_return_msg")
    private String robotReturnMsg;
    /**
     * rpa用來紀錄運行時間
     */
    @ApiModelProperty(value = "rpa用來紀錄運行時間")
    @Column(name = "Run_Duration")
    private String runDuration;
}
package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class RecommendedViewingQueryDto {

    @ApiModelProperty(value = "国家id")
    private Long countryId;

    @ApiModelProperty(value = "城市id")
    private Long cityId;

    @ApiModelProperty(value = "课程等级id")
    private Long courseLevelId;

    @ApiModelProperty(value = "专业小分类id")
    private Long typeId;

    @ApiModelProperty(value = "专业大类id")
    private Long pcTypeId;

    @ApiModelProperty(value = "学历")
    private String education;

    @ApiModelProperty(value = "学校id")
    private Long institutionId;

    @ApiModelProperty(value = "毕业学校id")
    private Long graduationSchoolId;

    @ApiModelProperty(value = "成绩key")
    private String scoreKey;

    @ApiModelProperty(value = "专业成绩类型")
    private Integer professionalScoreType;

    @ApiModelProperty(value = "专业成绩")
    private String professionalScore;
}

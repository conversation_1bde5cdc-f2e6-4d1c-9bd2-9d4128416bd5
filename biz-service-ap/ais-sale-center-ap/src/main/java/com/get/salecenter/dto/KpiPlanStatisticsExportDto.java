package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * KPI方案统计导出
 */
@Data
public class KpiPlanStatisticsExportDto {

    @ApiModelProperty(value = "KPI方案Id")
    @NotNull(message = "KPI方案Id不能为空")
    private Long fkKpiPlanId;

    @ApiModelProperty(value = "根节点员工Id（以其为根节点维度）")
    @NotNull(message = "根节点员工Id不能为空")
    private Long rootFkStaffId;

    @ApiModelProperty(value = "KPI统计表头信息")
    @NotEmpty(message = "KPI统计表头信息不能为空")
    private List<KpiPlanStatisticsHeaderDto> kpiPlanStatisticsHeaderList;

//    @ApiModelProperty(value = "KPI统计组别小计表头信息")
//    @NotEmpty(message = "KPI统计组别小计表头信息不能为空")
//    private List<KpiPlanStatisticsHeaderDto> kpiPlanGroupAllStatisticsHeaderList;

    @ApiModelProperty(value = "单元格合并的数量")
    private int mergeNum;
}

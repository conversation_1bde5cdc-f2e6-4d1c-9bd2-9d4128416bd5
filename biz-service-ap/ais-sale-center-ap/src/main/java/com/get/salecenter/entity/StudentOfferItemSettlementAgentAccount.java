package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("r_student_offer_item_settlement_agent_account")
public class StudentOfferItemSettlementAgentAccount extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 学生申请方案项目Id
     */
    @ApiModelProperty(value = "学生申请方案项目Id")
    @Column(name = "fk_student_offer_item_id")
    private Long fkStudentOfferItemId;
    /**
     * 币种编号（应付计划）
     */
    @ApiModelProperty(value = "币种编号（应付计划）")
    @Column(name = "fk_currency_type_num_payable_plan")
    private String fkCurrencyTypeNumPayablePlan;
    /**
     * 学生代理合同账户Id
     */
    @ApiModelProperty(value = "学生代理合同账户Id")
    @Column(name = "fk_agent_contract_account_id")
    private Long fkAgentContractAccountId;
    /**
     * 币种编号（代理账户）
     */
    @ApiModelProperty(value = "币种编号（代理账户）")
    @Column(name = "fk_currency_type_num")
    private String fkCurrencyTypeNum;
}
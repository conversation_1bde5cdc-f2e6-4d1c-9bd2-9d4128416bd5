package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("r_student_to_client_approval")
public class RStudentToClientApproval extends BaseEntity implements Serializable {

    @ApiModelProperty("公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;

    @ApiModelProperty("学生Id")
    @Column(name = "fk_student_id")
    private Long fkStudentId;

    @ApiModelProperty("客户Id")
    @Column(name = "fk_client_id")
    private Long fkClientId;

    @ApiModelProperty("审批人Id")
    @Column(name = "fk_staff_id_approval")
    private Long fkStaffIdApproval;

    @ApiModelProperty("申请Id")
    @Column(name = "fk_staff_id_apply")
    private Long fkStaffIdApply;

    @ApiModelProperty("审批意见")
    @Column(name = "approval_opinion")
    private String approvalOpinion;

    @ApiModelProperty("审批状态：0新申请/1通过/2拒绝")
    @Column(name = "approval_status")
    private Integer approvalStatus;
}

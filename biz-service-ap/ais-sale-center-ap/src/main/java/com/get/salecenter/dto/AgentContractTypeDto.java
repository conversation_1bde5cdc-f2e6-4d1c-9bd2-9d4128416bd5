package com.get.salecenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @DATE: 2020/10/30
 * @TIME: 15:01
 * @Description:
 **/
@Data
public class AgentContractTypeDto extends BaseVoEntity {
    /**
     * 类型key
     */
    @ApiModelProperty(value = "类型key")
    private String typeKey;
    /**
     * 类型名称
     */
    @ApiModelProperty(value = "类型名称", required = true)
    @NotNull(message = "学生代理id不能为空", groups = {Add.class, Update.class})
    private String typeName;

    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

}

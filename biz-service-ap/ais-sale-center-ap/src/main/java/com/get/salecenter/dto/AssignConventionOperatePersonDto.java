package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2023/5/22 15:11
 * @verison: 1.0
 * @description:
 */
@Data
public class AssignConventionOperatePersonDto {

    @NotEmpty(message = "员工ids不为空")
    @ApiModelProperty("员工ids（操作人员ids）")
    private List<Long> fkStaffIds;

    @NotNull(message = "峰会id不为空")
    @ApiModelProperty("峰会id")
    private Long fkConventionId;

    @ApiModelProperty("权限模式：0禁止进入/1允许进入")
    private Integer mode;
}

package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 计划凭证
 */
@Data
public class OfferItemVoucherDto {

    @ApiModelProperty(value = "父计划id")
    @NotNull(message = "父计划id不能为空")
    private Long parentItemId;

    @ApiModelProperty(value = "当前学习计划id")
    @NotNull(message = "当前学习计划id不能为空")
    private Long itemId;

    @ApiModelProperty(value = "学习计划步骤id")
    @NotNull(message = "学习计划步骤id不能为空")
    private Long fkStudentOfferItemStepId;

}

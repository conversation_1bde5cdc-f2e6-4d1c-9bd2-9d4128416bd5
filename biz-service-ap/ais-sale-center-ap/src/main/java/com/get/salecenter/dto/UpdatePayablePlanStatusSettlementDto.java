package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 更新应付计划佣金状态类
 */
@Data
public class UpdatePayablePlanStatusSettlementDto {

    @ApiModelProperty(value = "应付计划ids")
    private List<Long> payablePlanIdList;

    @ApiModelProperty(value = "旧结算状态")
    private List<Integer> oldStatusSettlements;

    @ApiModelProperty(value = "新结算状态")
    private Integer newStatusSettlement;

}

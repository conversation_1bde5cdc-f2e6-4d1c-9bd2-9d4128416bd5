package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/6/27 12:56
 * @desciption:
 */
@Data
public class StudentClientSourceDto {

    @ApiModelProperty(value = "推荐来源编号")
    private String studentSource;

    @ApiModelProperty(value = "推荐来源代理")
    private String sourceAgentName;

    @ApiModelProperty(value = "推荐来源BD")
    private String sourceBdName;

    //来源
    @ApiModelProperty(value = "来源类型")
    private String clientSourceType;

    @ApiModelProperty(value = "推荐来源类型名称")
    private String fkTableNameValue;

    @ApiModelProperty(value = "推荐来源本地市场来源名称")
    private String sourceBusinessProviderName;
}

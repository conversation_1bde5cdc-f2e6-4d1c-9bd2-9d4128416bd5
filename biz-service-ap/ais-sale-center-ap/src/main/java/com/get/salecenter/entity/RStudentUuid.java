package com.get.salecenter.entity;



import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-02-26 14:25:10
 */

@Data
@TableName("r_student_uuid")
public class RStudentUuid extends BaseEntity implements Serializable {


  @ApiModelProperty( "学生Id")
  private Long fkStudentId;
 

  @ApiModelProperty( "学生UUID")
  private String fkStudentUuid;
 

 

}

package com.get.salecenter.enums;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 代理合同审批状态枚举
 *
 * <AUTHOR>
 * @Date 2025-01-08
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum AgentContractApprovalStatusEnum {

    /**
     * 无合同
     */
    NO_CONTRACT(0, "无合同"),

    /**
     * 有合同
     */
    HAS_CONTRACT(1, "有合同"),

    /**
     * 未签署
     */
    UNSIGNED(2, "未签署"),

    /**
     * 待审核
     */
    PENDING_APPROVAL(3, "待审核"),

    /**
     * 审核通过
     */
    APPROVED(4, "审核通过"),

    /**
     * 审核驳回
     */
    REJECTED(-4, "审核驳回"),

    /**
     * 续约中
     */
    RENEWING(5, "续约中"),

    /**
     * 生效中
     */
    EFFECTIVE(6, "生效中"),

    /**
     * 已过期
     */
    EXPIRED(7, "已过期");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态描述
     */
    private final String msg;

    /**
     * 代理合同审批状态映射表
     */
    private static final Map<Integer, AgentContractApprovalStatusEnum> AGENT_CONTRACT_APPROVAL_STATUS_MAP = new HashMap<>();

    static {
        for (AgentContractApprovalStatusEnum agentContractApprovalStatusEnum : AgentContractApprovalStatusEnum
                .values()) {
            AGENT_CONTRACT_APPROVAL_STATUS_MAP.put(agentContractApprovalStatusEnum.getCode(),
                    agentContractApprovalStatusEnum);
        }
    }

    /**
     * 根据状态码获取对应的代理合同审批状态枚举实例
     *
     * @param code 状态码
     * @return 对应的代理合同审批状态枚举实例，如果找不到则返回null
     */
    public static AgentContractApprovalStatusEnum getAgentContractApprovalStatusByCode(Integer code) {
        if (ObjectUtils.isNull(code)) {
            return null;
        }
        return AGENT_CONTRACT_APPROVAL_STATUS_MAP.get(code);
    }

    /**
     * 判断是否为需要审核的状态
     *
     * @param code 状态码
     * @return true表示需要审核，false表示不需要审核
     */
    public static boolean isApprovalRequired(Integer code) {
        AgentContractApprovalStatusEnum statusEnum = getAgentContractApprovalStatusByCode(code);
        if (statusEnum == null) {
            return false;
        }
        return statusEnum == UNSIGNED || statusEnum == PENDING_APPROVAL;
    }

    /**
     * 判断是否为已完成状态（审核通过或有合同）
     *
     * @param code 状态码
     * @return true表示已完成，false表示未完成
     */
    public static boolean isCompleted(Integer code) {
        AgentContractApprovalStatusEnum statusEnum = getAgentContractApprovalStatusByCode(code);
        if (statusEnum == null) {
            return false;
        }
        return statusEnum == HAS_CONTRACT || statusEnum == APPROVED;
    }

    /**
     * 判断是否为可以续约的状态
     *
     * @param code 状态码
     * @return true表示可以续约，false表示不可以续约
     */
    public static boolean canRenew(Integer code) {
        AgentContractApprovalStatusEnum statusEnum = getAgentContractApprovalStatusByCode(code);
        if (statusEnum == null) {
            return false;
        }
        return statusEnum == HAS_CONTRACT || statusEnum == APPROVED;
    }

    /**
     * 判断是否允许续约申请回显
     * 只有状态不是"未签署、待审核、已驳回"才允许续约
     *
     * @param code 状态码
     * @return true表示允许续约申请回显，false表示不允许
     */
    public static boolean isRenewalApplicationAllowed(Integer code) {
        if (ObjectUtils.isNull(code)) {
            return false;
        }
        
        // 不允许续约的状态：未签署、待审核、已驳回
        return !Objects.equals(code, UNSIGNED.getCode()) &&
               !Objects.equals(code, PENDING_APPROVAL.getCode()) &&
               !Objects.equals(code, REJECTED.getCode());
    }

    /**
     * 根据代理状态、合同状态和时间判断合同的实际状态
     *
     */
    public static AgentContractApprovalStatusEnum getAgentStatus(
            Integer agentStatus,
            Integer contractStatus,
            Date contractStartTime,
            Date contractEndTime
    ) {
        if (AgentApprovalStatusEnum.RENEWING.getCode().equals(agentStatus)) {
            return AgentContractApprovalStatusEnum.RENEWING;
        }

        // 当前时间
        Date currentTime = new Date();

        // 参数校验：如果关键参数为空，直接返回原状态或null
        if (contractStatus == null || contractEndTime == null) {
            return getAgentContractApprovalStatusByCode(contractStatus);
        }

        // 计算合同是否过期 / 生效
        if (AgentContractApprovalStatusEnum.APPROVED.getCode().equals(contractStatus)) {
            // 参数校验：如果开始时间为空，使用原逻辑
            if (contractStartTime == null) {
                return contractEndTime.before(currentTime) ? EXPIRED : EFFECTIVE;
            }

            // 完整的时间段判断
            if (currentTime.before(contractStartTime)) {
                // 当前时间 < 开始时间：合同尚未生效，返回审核通过状态
                return APPROVED;
            } else if (currentTime.after(contractEndTime)) {
                // 当前时间 > 结束时间：合同已过期
                return EXPIRED;
            } else {
                // 开始时间 <= 当前时间 <= 结束时间：合同生效中
                return EFFECTIVE;
            }
        }

        // 其他情况返回原状态
        return getAgentContractApprovalStatusByCode(contractStatus);
    }

    public static AgentContractApprovalStatusEnum getActualContractStatus(Integer contractStatus, Date contractStartTime,
            Date contractEndTime) {
        return getActualContractStatus(null, contractStatus, contractStartTime, contractEndTime);
    }

    public static AgentContractApprovalStatusEnum getActualContractStatus(Integer agentStatus, Integer contractStatus, Date contractStartTime,
            Date contractEndTime) {
        AgentContractApprovalStatusEnum actualContractStatus = getAgentStatus(agentStatus, contractStatus, contractStartTime,
                contractEndTime);
        if (ObjectUtils.isNull(actualContractStatus)) {
            return actualContractStatus;
        }
        if (ObjectUtils.isNotNull(agentStatus)) {
            // 代理管理字段展示
            return isAgentAllowedDisplayStatus(actualContractStatus) ? actualContractStatus : null;
        } else {
            // 合同管理字段展示
            return isContractAllowedDisplayStatus(actualContractStatus) ? actualContractStatus : null;
        }
    }

    /**
     * 判断合同状态在代理管理中是否允许显示
     * 允许显示的状态：无合同(0)、已过期(7)、生效中(6)、待审核(3)、已驳回(-4)、续约中(5)
     *
     * @return true表示允许显示，false表示不允许显示
     */
    private static boolean isAgentAllowedDisplayStatus(AgentContractApprovalStatusEnum actualContractStatus) {
        if (actualContractStatus == null) {
            return false;
        }
        switch (actualContractStatus) {
            case NO_CONTRACT: // 无合同
            case PENDING_APPROVAL: // 待审核
            case REJECTED: // 已驳回
            case RENEWING: // 续约中
            case EFFECTIVE: // 生效中
            case EXPIRED: // 已过期
                return true;
            default:
                return false;
        }
    }

    /**
     * 判断合同状态在合同管理中是否允许显示
     * 允许显示的状态：
     * ①生效中：合同已完成签署并处于有效期内
     * ②已过期：合同已过有效期
     * ③未签署：用户未在小程序完成签署动作(包括签名和盖章回传)
     * ④待审核：用户提交审核后，AIS后台未进行审核，审核通过后，变成生效中
     * ⑤已驳回：合同被驳回审核
     *
     * @return true表示允许显示，false表示不允许显示
     */
    private static boolean isContractAllowedDisplayStatus(AgentContractApprovalStatusEnum actualContractStatus) {
        if (actualContractStatus == null) {
            return false;
        }
        switch (actualContractStatus) {
            case EFFECTIVE: // 生效中
            case EXPIRED: // 已过期
            case UNSIGNED: // 未签署
            case PENDING_APPROVAL: // 待审核
            case REJECTED: // 已驳回
                return true;
            default:
                return false;
        }
    }

}

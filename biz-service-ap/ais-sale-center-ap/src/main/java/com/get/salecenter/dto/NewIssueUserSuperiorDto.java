package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2022/10/25 14:32
 */
@Data
public class NewIssueUserSuperiorDto  extends BaseVoEntity {
    /**
     * 用户Id
     */
    @ApiModelProperty(value = "用户Id")
    private Long fkUserId;

    /**
     * 上司Id
     */
    @ApiModelProperty(value = "上司Id")
    private Long fkUserSuperiorId;

  
}

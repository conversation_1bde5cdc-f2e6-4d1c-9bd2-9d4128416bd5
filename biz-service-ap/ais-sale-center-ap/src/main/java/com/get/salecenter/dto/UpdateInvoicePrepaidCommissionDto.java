package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/7/1 12:12
 */
@Data
public class UpdateInvoicePrepaidCommissionDto {

    @ApiModelProperty(value = "发票-应收计划关系表id")
    private Long id;

    @ApiModelProperty(value = "发票绑定金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "预付百分比：50, 100")
    private BigDecimal payInAdvancePercent;



}

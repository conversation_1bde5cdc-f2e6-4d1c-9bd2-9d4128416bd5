package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/7/26 11:19
 */
@Data
@TableName("m_receivable_plan_date")
public class ReceivablePlanDate extends BaseEntity implements Serializable {

    /**
     * 应收计划Id
     */
    @ApiModelProperty(value = "应收计划Id")
    @Column(name = "fk_receivable_plan_id")
    private Long fkReceivablePlanId;

    /**
     * 计划收款时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "计划收款时间")
    @Column(name = "receivable_plan_date")
    private Date receivablePlanDate;


}

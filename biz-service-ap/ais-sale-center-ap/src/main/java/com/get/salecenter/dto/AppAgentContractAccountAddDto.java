package com.get.salecenter.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2022/11/17 17:07
 * @verison: 1.0
 * @description:
 */
@Data
public class AppAgentContractAccountAddDto extends BaseVoEntity {

    //=========继承AppAgentContractAccountDto===================
    /**
     * 学生代理申请Id
     */
    @NotNull(message = "学生代理申请Id", groups = {AppAgentContractAccountDto.Save.class})
    @ApiModelProperty(value = "学生代理申请Id")
    private Long fkAppAgentId;

    /**
     * 币种编号
     */
    @NotBlank(message = "币种编号", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;

    /**
     * 银行账户名称
     */
    @NotBlank(message = "银行账户名称", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "银行账户名称")
    private String bankAccount;

    /**
     * 银行账号
     */
    @NotBlank(message = "银行账号", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "银行账号")
    private String bankAccountNum;

    /**
     * 银行名称
     */
    @NotBlank(message = "银行名称", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "银行名称")
    private String bankName;

    /**
     * 银行支行名称
     */
    @ApiModelProperty(value = "银行支行名称")
    private String bankBranchName;

    /**
     * 银行地址国家Id
     */
    @ApiModelProperty(value = "银行地址国家Id")
    private Long fkAreaCountryId;

    /**
     * 银行地址州省Id
     */
    @ApiModelProperty(value = "银行地址州省Id")
    private Long fkAreaStateId;

    /**
     * 银行地址城市Id
     */
    @ApiModelProperty(value = "银行地址城市Id")
    private Long fkAreaCityId;

    /**
     * 银行地址城市区域Id
     */
    @ApiModelProperty(value = "银行地址城市区域Id")
    private Long fkAreaCityDivisionId;

    /**
     * 银行地址
     */
    @ApiModelProperty(value = "银行地址")
    private String bankAddress;

    /**
     * 银行编号类型：SwiftCode/BSB
     */
    @ApiModelProperty(value = "银行编号类型：SwiftCode/BSB")
    private String bankCodeType;

    /**
     * 银行编号
     */
    @ApiModelProperty(value = "银行编号")
    private String bankCode;

    /**
     * 国家编码
     */
    @ApiModelProperty(value = "国家编码")
    private String areaCountryCode;

    /**
     * 是否默认首选：0否/1是
     */
    @NotNull(message = "是否默认首选", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "是否默认首选：0否/1是")
    private Boolean isDefault;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 声明文件
     */
    @ApiModelProperty(value = "声明文件")
    private List<MediaAndAttachedDto> mediaAndAttachedVos;

    public interface Save {
    }

    /**
     * 转化到代理Id
     */
    @ApiModelProperty(value = "转化到代理Id")
    private Long fkAgentId;

    @ApiModelProperty(value = "转化到代理合同账户Id")
    @JsonProperty("fkAgentContractAccountId")
    @TableField("fk_agent_contract_account_id")
    private Long fkAgentContractAccountId;

}

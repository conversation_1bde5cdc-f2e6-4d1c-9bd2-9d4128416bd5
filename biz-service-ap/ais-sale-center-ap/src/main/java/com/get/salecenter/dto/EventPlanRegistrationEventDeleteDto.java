package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2024/3/29
 * @TIME: 17:59
 * @Description:删除报名名册项目VO
 **/
@Data
public class EventPlanRegistrationEventDeleteDto {

    @NotNull(message = "报名名册Id不能为空")
    @ApiModelProperty(value = "报名名册Id")
    private Long fkEventPlanRegistrationId;

    @NotEmpty(message = "报名名册项目Id列表不能为空")
    @ApiModelProperty(value = "报名名册项目Id列表")
    private List<Long> fkEventPlanRegistrationEventIdList;
}

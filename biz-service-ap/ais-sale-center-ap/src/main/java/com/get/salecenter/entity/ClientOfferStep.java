package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("u_client_offer_step")
public class ClientOfferStep extends BaseEntity implements Serializable {
    /**
     * 状态步骤名
     */
    @ApiModelProperty(value = "状态步骤名")
    @Column(name = "step_name")
    private String stepName;

    /**
     * 状态步骤key
     */
    @ApiModelProperty(value = "状态步骤key")
    @Column(name = "step_key")
    private String stepKey;

    /**
     * 状态步骤排序，由0开始按顺序排列
     */
    @ApiModelProperty(value = "状态步骤排序，由0开始按顺序排列")
    @Column(name = "step_order")
    private Integer stepOrder;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;

    private static final long serialVersionUID = 1L;
}
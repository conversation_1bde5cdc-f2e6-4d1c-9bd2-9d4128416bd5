package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 奖励统计学生列表
 * <AUTHOR>
 * @since 2023-03-13
 */
@Data
@TableName("r_incentive_policy_student_offer_item")
@ApiModel(value="RIncentivePolicyStudentOfferItem对象", description="")
public class IncentivePolicyStudentOfferItem extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "Incentive奖励政策Id")
    private Long fkIncentivePolicyId;

    @ApiModelProperty(value = "学生申请方案项目Id")
    private Long fkStudentOfferItemId;

    @ApiModelProperty(value = "统计类型：0人工剔除/1人工计入/2系统计入")
    private Integer countType;

    @ApiModelProperty(value = "财务状态：0未结算/1已结算")
    private Integer financeStatus;

    @ApiModelProperty(value = "结算时间")
    private LocalDateTime settlementTime;

    @ApiModelProperty(value = "应收计划Id")
    private Long fkReceivablePlanId;

    @ApiModelProperty(value = "应付计划Id")
    private Long fkPayablePlanId;
}

package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 学习计划生成失败VO类
 *
 * <AUTHOR>
 * @date 2021/7/8 17:15
 */
@Data
public class StudentOfferItemFailureDto extends BaseVoEntity {

    @ApiModelProperty(value = "学习计划编号")
    private String offerItemNum;

    @ApiModelProperty(value = "学生代理id")
    private String agentId;

    @ApiModelProperty(value = "学生代理名称")
    private String agentName;

    @ApiModelProperty(value = "学生中文名称")
    private String studentName;

    @ApiModelProperty(value = "学生英文名称")
    private String studentNameEng;

    @ApiModelProperty(value = "目标国家id")
    private Long areaCountryId;

    @ApiModelProperty(value = "学校提供商id")
    private Long institutionProviderId;

    @ApiModelProperty(value = "学校id")
    private Long institutionId;

    @ApiModelProperty(value = "课程id")
    private Long institutionCourseId;

    @ApiModelProperty(value = "公司id")
    private Long companyId;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "开始时间")
    private Date createBeginDate;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "结束时间")
    private Date createEndDate;

    @ApiModelProperty(value = "状态：0执行失败/1执行成功")
    private Integer status;

  
}

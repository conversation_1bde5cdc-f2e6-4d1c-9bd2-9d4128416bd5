package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import java.io.Serializable;
import lombok.Data;


@TableName(value ="r_agent_uuid")
@Data
public class RAgentUuid extends BaseEntity implements Serializable {
    /**
     * 学生代理Id
     */
    private Long fkAgentId;

    /**
     * 学生代理UUID
     */
    private String fkAgentUuid;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
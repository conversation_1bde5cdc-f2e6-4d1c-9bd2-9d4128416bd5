package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @DATE: 2020/9/15
 * @TIME: 14:29
 * @Description:
 **/
@Data
public class AgentContractCompanyDto extends BaseVoEntity {

    /**
     * 学生代理合同Id
     */
    @ApiModelProperty(value = "学生代理合同Id", required = true)
    @NotNull(message = "学生代理合同Id不能为空", groups = {Add.class, Update.class})
    private Long fkAgentContractId;

    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;
}

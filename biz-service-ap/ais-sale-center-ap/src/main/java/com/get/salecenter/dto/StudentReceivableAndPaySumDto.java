package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2021/11/22 10:30
 * @verison: 1.0
 * @description:
 */
@Data
public class StudentReceivableAndPaySumDto {
    /**
     * 公司id
     */
    @ApiModelProperty(value = "公司id")
    private Long fkCompanyId;

    /**
     * 学生id
     */
    @ApiModelProperty(value = "学生id")
    private Long fkStudentId;

    /**
     * 公司id
     */
    @ApiModelProperty(value = "公司id")
    private Long fkAreaCountryId;

    /**
     * 学校名称
     */
    @ApiModelProperty(value = "学校名称")
    private String institutionName;

    /**
     * 课程名称
     */
    @ApiModelProperty(value = "课程名称")
    private String institutionCourseName;

    /**
     * 学生名字
     */
    @ApiModelProperty(value = "学生名字")
    private String studentName;

    /**
     * 方案开始时间
     */
    @ApiModelProperty(value = "方案开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTime;

    /**
     * 方案结束时间
     */
    @ApiModelProperty(value = "方案结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

    /**
     * 学校id
     */
    @ApiModelProperty(value = "学校id")
    private String fkInstitutionId;

    /**
     * 提供商id
     */
    @ApiModelProperty(value = "提供商id")
    private Long fkProviderId;

    /**
     * 开学年份开始
     */
    @ApiModelProperty(value = "开学年份开始")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date openingTimeStart;

    /**
     * 开学年份结束
     */
    @ApiModelProperty(value = "开学年份结束")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date openingTimeEnd;

    /**
     * 收齐状态：0未收/1部分已收/2已收齐
     */
    @ApiModelProperty(value = "收齐状态：0未收/1部分已收/2已收齐/3未收齐（未收+部分已收）")
    private Integer receiveStatus;

    /**
     * 付款状态：0/1/2：未付/已付部分/已付齐
     */
    @ApiModelProperty(value = "付款状态：0/1/2/3：未付/已付部分/已付齐/未付齐（未付+已付部分）")
    private Integer payableStatus;

    /**
     * 公司ids
     */
    @ApiModelProperty(value = "公司ids")
    private List<Long> fkCompanyIds;

    @ApiModelProperty(value = "大区Id")
    private Long fkAreaRegionId;

    @ApiModelProperty(value = "状态步骤变更")
    private List<Long> changeStepId;

    @ApiModelProperty(value = "状态开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date statusBeginTime;

    @ApiModelProperty(value = "状态结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date statusEndTime;

    @ApiModelProperty(value = "发票编号")
    private String fkInvoiceNums;

    @ApiModelProperty("代理名称/代理编号")
    private String agentNameNum;

}

package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class PayablePlanCheckOutDto {

    /**
     * 应付金额
     */
    @ApiModelProperty(value = "应付金额")
    @NotNull(message = "应付金额不能为空")
    private BigDecimal payableAmount;

    @ApiModelProperty(value = "应付币种编号")
    @NotBlank(message = "应付币种编号不能为空")
    private String fkCurrencyTypeNum;

    /**
     * 应收计划Id
     */
    @ApiModelProperty(value = "应收计划Id")
//    @NotNull(message = "应收计划Id不能为空")
    private Long fkReceivablePlanId;
}

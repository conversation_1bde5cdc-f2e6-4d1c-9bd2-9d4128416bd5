package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("r_staff_commission_institution")
public class StaffCommissionInstitution extends BaseEntity implements Serializable {
    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    @Column(name = "fk_institution_id")
    private Long fkInstitutionId;

    /**
     * 状态，枚举：1激活提成
     */
    @ApiModelProperty(value = "状态，枚举：1激活提成")
    @Column(name = "status")
    private Integer status;

    private static final long serialVersionUID = 1L;
}
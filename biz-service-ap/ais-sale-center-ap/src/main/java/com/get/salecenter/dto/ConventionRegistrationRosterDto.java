package com.get.salecenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

@Data
public class ConventionRegistrationRosterDto extends BaseVoEntity {
    /**
     * 峰会Id
     */
    @NotNull(message = "峰会Id不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "峰会Id", required = true)
    private Long fkConventionId;

    /**
     * 学校提供商Id（费用归口）
     */
    @ApiModelProperty(value = "学校提供商Id（费用归口）")
    private Long fkInstitutionProviderId;


    /**
     * 机构名称
     */
    @ApiModelProperty(value = "机构名称")
    private String providerName;

    /**
     * 展位名称
     */
    @ApiModelProperty(value = "展位名称", required = true)
    private List<ConventionRegistrationBoothDto> boothNameList;



    /**
     * 联系人姓名
     */
    @ApiModelProperty(value = "联系人姓名")
    private String contactPersonName;



    /**
     * 联系人电邮
     */
    @ApiModelProperty(value = "联系人电邮")
    private String contactEmail;

    /**
     * 联系人电话
     */
    @ApiModelProperty(value = "联系人电话")
    private String contactTel;


    /**
     * 回执码，8位数字随机数
     */
    @ApiModelProperty(value = "回执码，8位数字随机数")
    private String receiptCode;




    @ApiModelProperty(value = "语言")
    private String language;
}

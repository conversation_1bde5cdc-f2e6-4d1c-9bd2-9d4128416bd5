package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: Hardy
 * @create: 2021/7/8 14:34
 * @verison: 1.0
 * @description:
 */
@Data
public class IaeAnnualReservationFormDto {


    /**
     * 回执码
     */
    @ApiModelProperty(value = "回执码")
    private String receiptCode;

    /**
     * 展位vos
     */
    @ApiModelProperty(value = "展位vos")
    private List<AnnualReservationBoothDto> annualReservationBoothVoList;

    /**
     * 参会人vos
     */
    @ApiModelProperty(value = "参会人vos")
    private List<AnnualReservationFormDto> annualReservationFormVoList;
}

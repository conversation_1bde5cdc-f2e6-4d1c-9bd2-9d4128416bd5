package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName(value = "m_business_provider_contact_person")
public class BusinessProviderContactPerson extends BaseEntity implements Serializable {

    @ApiModelProperty("业务提供商Id")
    private Long fkBusinessProviderId;

    @ApiModelProperty("联系人姓名")
    private String name;

    @ApiModelProperty("性别：0女/1男")
    private Integer gender;

    @ApiModelProperty("部门")
    private String department;

    @ApiModelProperty("职称")
    private String title;

    @ApiModelProperty("手机号区号")
    private String mobileAreaCode;

    @ApiModelProperty("手机号")
    private String mobile;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("备注")
    private String remark;
}

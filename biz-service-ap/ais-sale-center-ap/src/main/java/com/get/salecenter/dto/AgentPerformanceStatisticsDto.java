package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2022/11/21
 * @TIME: 9:57
 * @Description:
 **/
@Data
public class AgentPerformanceStatisticsDto {
    @ApiModelProperty(value = "公司ID")
    private Long fkCompanyId;

    @NotNull(message = "代理ID不能为空")
    @ApiModelProperty(value = "代理ID")
    private Long fkAgentId;

    @NotNull(message = "查询类型不能为空")
    @ApiModelProperty(value = "类型[1：当年；2：当月（系统当前时间自然月）；3：一月内（当前时间日期-30天）；4：半年内（当前时间日期-180天）；5：一年内（当前时间日期-360天）；6：二年内（当前时间日期-720天）]")
    private Integer type;

    @ApiModelProperty(value = "当前时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date now;

    /**
     * 员工以及旗下员工所创建的代理ids
     */
    @ApiModelProperty(value = "员工以及旗下员工所创建的代理ids")
    private List<Long> staffFollowerIds;

    /**
     * 国家ID列表,员工（登录人）业务国家
     */
    @ApiModelProperty(value = "国家ID列表,员工（登录人）业务国家")
    private List<Long> fkAreaCountryIdList;

    /**
     * 业绩统计GEA定校量相关步骤列表枚举
     */
    @ApiModelProperty(value = "业绩统计GEA定校量相关步骤列表枚举")
    private List<String> geaConfirmationStatisticsStepList;

    /**
     * 业绩统计IAE定校量相关步骤列表枚举
     */
    @ApiModelProperty(value = "业绩统计IAE定校量相关步骤列表枚举")
    private List<String> iaeConfirmationStatisticsStepList;

    /**
     * 业绩统计GEA成功量相关步骤列表枚举
     */
    @ApiModelProperty(value = "业绩统计GEA成功量相关步骤列表枚举")
    private List<String> geaSuccessStatisticsStepList;

    /**
     * 业绩统计IAE成功量相关步骤列表枚举
     */
    @ApiModelProperty(value = "业绩统计IAE成功量相关步骤列表枚举")
    private List<String> iaeSuccessStatisticsStepList;
}

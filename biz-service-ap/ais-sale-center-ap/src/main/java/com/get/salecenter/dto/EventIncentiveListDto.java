package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * @author: Hardy
 * @create: 2022/7/19 16:20
 * @verison: 1.0
 * @description:
 */
@Data
public class EventIncentiveListDto {

    //---------列表查询条件----------

    @ApiModelProperty("活动标题")
    private String eventTitle;

    @ApiModelProperty(value = "活动编号")
    private String num;

    @ApiModelProperty("公司id")
    private Long fkCompanyId;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("提供商名称")
    private String fkInstitutionProviderName;

    @ApiModelProperty("业务国家(多选）")
    private List<Long> fkAreaCountryIdList;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "建议核对时间开始")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date suggestCheckTimeBegin;

    @ApiModelProperty(value = "建议核对时间结束")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date suggestCheckTimeEnd;

    //公开对象
    @ApiModelProperty("公开对象：0不公开/1公开/2学生/3代理")
    private Integer publicLevel;


}

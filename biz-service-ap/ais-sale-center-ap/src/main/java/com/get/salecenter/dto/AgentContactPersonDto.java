package com.get.salecenter.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class AgentContactPersonDto extends BaseVoEntity{

    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String fkTableName;
    /**
     * 表Id
     */
    /**
     * 联系人类型Key，多值逗号隔开
     */
    @ApiModelProperty(value = "联系人类型Key，多值逗号隔开", required = true)
//    @NotBlank(message = "联系人类型不能为空", groups = {Add.class, Update.class})
    private String fkContactPersonTypeKey;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;
    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    private Integer gender;
    /**
     * 生日
     */
    @ApiModelProperty(value = "生日")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date birthday;
    /**
     * 公司名
     */
    @ApiModelProperty(value = "公司名")
    @NotBlank(message = "公司名不能为空", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    private String company;
    /**
     * 部门
     */
    @ApiModelProperty(value = "部门")
    @NotBlank(message = "部门不能为空", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    private String department;
    /**
     * 职位
     */
    @ApiModelProperty(value = "职位")
    @NotBlank(message = "职位不能为空", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    private String title;
    /**
     * 移动电话
     */
    @ApiModelProperty(value = "移动电话")
    @NotBlank(message = "移动电话不能为空", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    private String mobile;

    @ApiModelProperty(value = "手机区号")
    @NotBlank(message = "移动电话区号不能为空", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    private String mobileAreaCode;


    @ApiModelProperty(value = "电话区号")
    private String telAreaCode;
    /**
     * 电话
     */
    @ApiModelProperty(value = "电话")
    private String tel;
    /**
     * Email
     */
    @ApiModelProperty(value = "Email")
    @NotBlank(message = "Email不能为空", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    private String email;
    /**
     * qq
     */
    @ApiModelProperty(value = "qq")
    private String qq;
    /**
     * 微信号
     */
    @ApiModelProperty(value = "微信号")
    private String wechat;
    /**
     * whatsapp
     */
    @ApiModelProperty(value = "whatsapp")
    private String whatsapp;
    /**
     * 联系地址
     */
    @ApiModelProperty(value = "联系地址")
    private String contactAddress;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    @ApiModelProperty(value = "查询关键字")
    private String keyWord;
    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    private Long fkInstitutionId;
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;
    /**
     * 国家Id
     */
    @ApiModelProperty(value = "代理国家Id")
    private Long fkAreaCountryId;
    /**
     * 州省Id
     */
    @ApiModelProperty(value = "代理州省Id")
    private Long fkAreaStateId;
    /**
     * 城市Id
     */
    @ApiModelProperty(value = "代理城市Id")
    private Long fkAreaCityId;
    /**
     * 大区Id
     */
    @ApiModelProperty(value = "大区Id")
    private Long fkAreaRegionId;
    /**
     * 代理编号
     */
    @ApiModelProperty(value = "代理编号")
    private String num;
    /**
     * 代理名称
     */
    @ApiModelProperty(value = "代理名称")
    @TableField("name")
    private String agentName;
    /**
     * 名称备注
     */
    @ApiModelProperty(name = "名称备注")
    private String nameNote;
    /**
     * bd名称
     */
    @ApiModelProperty(name = "bd名称")
    private String bdName;
    /**
     * 是否关键代理：0否/1是
     */
    @ApiModelProperty(value = "是否关键代理：0否/1是")
    private Boolean isKeyAgent;
    /**
     * 代理编号/名称/名称备注/
     */
    @ApiModelProperty(value = "是否关键代理：0否/1是")
    private String queryWord;

    @ApiModelProperty(value = "是否合同联系人")
    private Boolean isContractContact;

    @ApiModelProperty(value = "是否代理联系人")
    private Boolean isAgentContact;

   
}

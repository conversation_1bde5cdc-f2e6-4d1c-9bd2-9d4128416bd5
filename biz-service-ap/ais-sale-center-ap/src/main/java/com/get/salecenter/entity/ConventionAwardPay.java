package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@TableName("m_convention_award_pay")
public class ConventionAwardPay extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 峰会Id
     */
    @ApiModelProperty(value = "峰会Id")
    @Column(name = "fk_convention_id")
    private Long fkConventionId;
    /**
     * 峰会参展人员Id
     */
    @ApiModelProperty(value = "峰会参展人员Id")
    @Column(name = "fk_convention_person_id")
    private Long fkConventionPersonId;
    /**
     * 支付数量
     */
    @ApiModelProperty(value = "支付数量")
    @Column(name = "pay_count")
    private Integer payCount;
    /**
     * 支付金额
     */
    @ApiModelProperty(value = "支付金额")
    @Column(name = "pay_amount")
    private BigDecimal payAmount;
    /**
     * 系统支付单号，guid
     */
    @ApiModelProperty(value = "系统支付单号，guid")
    @Column(name = "pay_system_order_num")
    private String paySystemOrderNum;
    /**
     * 支付状态：0未支付/1完成支付
     */
    @ApiModelProperty(value = "支付状态：0未支付/1完成支付")
    @Column(name = "pay_type")
    private Integer payType;
}
package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

/**
 * @Author:cream
 * @Date: 2023/5/11  12:33
 */
@Data
@TableName("m_student_subject_score")
public class StudentSubjectScore extends BaseEntity implements Serializable {

    @ApiModelProperty("学生id")
    @Column(name = "fk_student_id")
    private Long fkStudentId;

    @ApiModelProperty("模式：0学术成绩/1英语成绩")
    @Column(name = "mode")
    private Integer mode;

    @ApiModelProperty("成绩类型枚举值")
    @Column(name = "score_type")
    private Integer scoreType;

    @ApiModelProperty("学科方向：0文科/1理科")
    @Column(name = "subject_direction")
    private Integer subjectDirection;

    @ApiModelProperty("学科枚举值：中文0/英语1/数学2")
    @Column(name = "subject_type")
    private Integer subjectType;

    @ApiModelProperty("成绩分数")
    @Column(name = "score")
    private String score;

    @ApiModelProperty("分数详细")
    @Column(name = "detail")
    private String detail;
}

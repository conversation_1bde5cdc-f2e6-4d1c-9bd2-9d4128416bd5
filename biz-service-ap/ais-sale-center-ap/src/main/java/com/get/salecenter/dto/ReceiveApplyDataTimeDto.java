package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
public class ReceiveApplyDataTimeDto extends BaseVoEntity  {

    @ApiModelProperty(value = "收到申请资料时间")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date receivedApplicationDataDate;

  
}

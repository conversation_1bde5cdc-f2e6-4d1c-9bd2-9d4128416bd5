package com.get.salecenter.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import lombok.Data;

/**
 * r_student_offer_identify
 * <AUTHOR>
@Data
@TableName("r_student_offer_identify")
public class StudentOfferIdentify extends BaseEntity implements Serializable {
    /**
     * 模型识别OfferId
     */
    private Long id;

    /**
     * 学生申请方案项目Id
     */
    private Long fkStudentOfferItemId;

    /**
     * 币种编号
     */
    private String fkCurrencyTypeNum;

    /**
     * 学费金额
     */
    private BigDecimal tuitionAmount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 创建用户(登录账号)
     */
    private String gmtCreateUser;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 修改用户(登录账号)
     */
    private String gmtModifiedUser;

    private static final long serialVersionUID = 1L;
}
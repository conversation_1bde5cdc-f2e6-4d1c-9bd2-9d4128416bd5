package com.get.salecenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2022/11/7 15:21
 * @verison: 1.0
 * @description:
 */
@Data
public class AgentRoleStaffAddDto {

//    /**
//     * 代理Id
//     */
//    @ApiModelProperty(value = "代理Id")
////    @NotNull(message = "代理Id不能为空", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
//    private Long fkAgentId;

    /**
     * 学生项目角色Id
     */
    @ApiModelProperty(value = "学生项目角色Id")
    @NotNull(message = "学生项目角色Id", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    private Long fkStudentProjectRoleId;

    /**
     * 员工Id
     */
    @ApiModelProperty(value = "员工Id")
    @NotNull(message = "学生项目角色Id", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    private Long fkStaffId;

    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @NotNull(message = "公司Id", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    private Long fkCompanyId;

    /**
     * 国家Id，可不选，不选为适合所有国家线
     */
    @ApiModelProperty(value = "国家Id，可不选，不选为适合所有国家线")
    private Long fkAreaCountryId;

    /**
     * 国家Id，可不选，不选为适合所有国家线(多选)
     */
    @ApiModelProperty(value = "国家Id，可不选，不选为适合所有国家线(多选)")
    private List<Long> fkAreaCountryIds;

//    /**
//     * 是否激活：0否/1是
//     */
//    @ApiModelProperty(value = "是否激活：0否/1是")
//    private Boolean isActive;

    /**
     * 目标类型关键字，枚举：m_student_offer留学申请/m_student_insurance留学保险/m_student_accommodation留学住宿
     */
    @ApiModelProperty(value = "目标类型关键字，枚举：m_student_offer留学申请/m_student_insurance留学保险/m_student_accommodation留学住宿")
    private String fkTypeKey;

}

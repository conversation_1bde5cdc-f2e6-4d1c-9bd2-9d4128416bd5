package com.get.salecenter.dto;

import com.get.core.mybatis.annotation.UpdateWithNull;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 奖励策略内的奖品json对象
 * <AUTHOR>
 * @since 2023-03-13
 */
@Data
public class IncentivePolicyRewardDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "其它奖品奖励集合")
    private List<Long> rewardIds;

//    @ApiModelProperty(value = "其它奖品奖励名称集合(返回)")
//    @TableField(exist = false)
//    private List<String> rewardNames;

    @ApiModelProperty(value = "策略奖品集合")
    private List<RewardDetail> rewardDetails;

    @Data
    public static class RewardDetail implements Serializable{
        @ApiModelProperty(value = "奖励统计起点")
        private Integer rewardStartNum;

        @ApiModelProperty(value = "奖励方式（1 每人奖励 2 一次性奖励）")
        private Integer rewardType;

        /**
         * 应收币种
         */
        @ApiModelProperty(value = "应收币种")
        private String receivableCurrencyTypeNum;
//        /**
//         * 应收币种名称
//         */
//        @ApiModelProperty(value = "应收币种名称(返回)")
//        @TableField(exist = false)
//        private String receivableCurrencyTypeName;

        /**
         * 应收金额
         */
        @ApiModelProperty(value = "应收金额")
        @UpdateWithNull
        private BigDecimal receivableAmount;

        /**
         * 应付币种
         */
        @ApiModelProperty(value = "应付币种")
        private String payableCurrencyTypeNum;

//        /**
//         * 应付币种名称
//         */
//        @ApiModelProperty(value = "应付币种名称(返回)")
//        @TableField(exist = false)
//        private String payableCurrencyTypeName;

        /**
         * 应付金额
         */
        @ApiModelProperty(value = "应付金额")
        @UpdateWithNull
        private BigDecimal payableAmount;

        /**
         * 摘要说明
         */
        @ApiModelProperty(value = "摘要说明")
        private String summaryDes;

    }


}

package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12
 */
@Data
@TableName("m_event_plan_registration")
@ApiModel(value="EventPlanRegistration对象", description="")
public class EventPlanRegistration extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    @ApiModelProperty(value = "活动年度计划Id")
    private Long fkEventPlanId;

    @ApiModelProperty(value = "学校提供商Id")
    private Long fkInstitutionProviderId;

    @ApiModelProperty(value = "发票建议币种")
    private String fkCurrencyTypeNumInvoice;
}

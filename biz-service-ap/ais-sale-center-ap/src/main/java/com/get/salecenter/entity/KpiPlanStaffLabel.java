package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import java.io.Serializable;

/**
 * 考核人员标签表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("m_kpi_plan_staff_label")
@ApiModel(value = "KPI考核人员标签表", description = "")
public class KpiPlanStaffLabel extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "KPI方案考核人员Id")
    @Column(name = "fk_kpi_plan_staff_id")
    private Long fkKpiPlanStaffId;

    @ApiModelProperty(value = "文字标签")
    @Column(name = "word_label")
    private String wordLabel;
}

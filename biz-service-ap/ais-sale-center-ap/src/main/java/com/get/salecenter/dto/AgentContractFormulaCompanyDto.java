package com.get.salecenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @author: Sea
 * @create: 2021/1/6 15:35
 * @verison: 1.0
 * @description:
 */
@Data
public class AgentContractFormulaCompanyDto extends BaseVoEntity {
    /**
     * 学生代理合同公式Id
     */
    @ApiModelProperty(value = "学生代理合同公式Id")
    private Long fkAgentContractFormulaId;

    /**
     * 公司Id
     */
    @NotNull(message = "公司不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    //自定义内容
    /**
     * 代理id
     */
    @ApiModelProperty(value = "代理id")
    private Long agentId;
}

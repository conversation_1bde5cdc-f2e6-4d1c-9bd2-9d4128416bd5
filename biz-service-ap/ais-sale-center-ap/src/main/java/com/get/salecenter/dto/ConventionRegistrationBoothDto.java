package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ConventionRegistrationBoothDto {

    /**
     * 展位名称
     */
    @ApiModelProperty(value = "展位名称", required = true)
    private String boothName;


    /**
     * 报名包含的国家编号
     */
    @ApiModelProperty(value = "报名包含的国家编号")
    private List<String> cNums;
}

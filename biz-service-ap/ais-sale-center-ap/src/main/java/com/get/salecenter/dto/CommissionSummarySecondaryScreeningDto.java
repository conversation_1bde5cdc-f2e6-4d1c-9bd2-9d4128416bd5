package com.get.salecenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 财务佣金汇总二次筛选Vo
 *
 * <AUTHOR>
 * @date 2021/12/24 15:40
 */
@Data
@ApiModel(value = "财务佣金汇总Vo")
public class CommissionSummarySecondaryScreeningDto extends BaseVoEntity implements Serializable {

    @ApiModelProperty(value = "代理id")
    private Long agentId;

    @ApiModelProperty(value = "应付币种编号")
    private String payPlanCurrencyTypeNum;

    @ApiModelProperty(value = "结算账号币种编号")
    private String accountCurrencyTypeNum;

    @ApiModelProperty(value = "业务类型")
    private String fkTypeKey;

}

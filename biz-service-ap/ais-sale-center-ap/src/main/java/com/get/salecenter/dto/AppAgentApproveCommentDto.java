package com.get.salecenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @author: Hardy
 * @create: 2023/3/31 10:47
 * @verison: 1.0
 * @description:
 */
@Data
public class AppAgentApproveCommentDto extends BaseVoEntity {
    /**
     * 学生代理申请Id
     */
    @NotNull(message = "学生代理申请Id", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "学生代理申请Id")
    @Column(name = "fk_app_agent_id")
    private Long fkAppAgentId;

    /**
     * 审批意见
     */
    @NotBlank(message = "审批意见", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "审批意见")
    @Column(name = "approve_comment")
    private String approveComment;

    /**
     * 审批意见
     */
    @ApiModelProperty(value = "是否拒绝")
    private Boolean isReject;

//    /**
//     * 邮件通知时间，有就表示已发邮件
//     */
//    @ApiModelProperty(value = "邮件通知时间，有就表示已发邮件")
//    @Column(name = "email_time")
//    private Date emailTime;
}

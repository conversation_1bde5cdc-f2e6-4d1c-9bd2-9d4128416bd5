package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2024/4/22
 * @TIME: 12:22
 * @Description:
 **/
@Data
public class KpiPlanStudentOfferItemListDto {

//    @ApiModelProperty(value = "公司ID")
//    private String fkCompanyIds;

    @ApiModelProperty(value = "公司ID")
    private Set<Long> fkCompanyIds;

    @ApiModelProperty(value = "学校提供商下的学校IDS")
    private Set<Long> fkInstitutionIds;

    @ApiModelProperty(value = "学生创建时间（开始）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date studentCreateTimeStart;

    @ApiModelProperty(value = "学生创建时间（结束）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date studentCreateTimeEnd;

    /**
     * 申请创建开始时间
     */
    @ApiModelProperty(value = "申请计划创建时间（开始）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date offerItemCreateTimeStart;

    /**
     * 申请创建结束时间
     */
    @ApiModelProperty(value = "申请计划结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date offerItemCreateTimeEnd;



    @ApiModelProperty(value = "入学时间（开始）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date intakeTimeStart;

    @ApiModelProperty(value = "入学时间（结束）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date intakeTimeEnd;

    @ApiModelProperty(value = "业务步骤登记时间（开始）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date stepTimeStart;

    @ApiModelProperty(value = "业务步骤登记时间（结束）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date stepTimeEnd;

    /**
     * 业绩统计GEA定校量相关步骤列表枚举
     */
    @ApiModelProperty(value = "业绩统计GEA定校量相关步骤列表枚举")
    private List<String> geaConfirmationStatisticsStepList;

    /**
     * 业绩统计IAE定校量相关步骤列表枚举
     */
    @ApiModelProperty(value = "业绩统计IAE定校量相关步骤列表枚举")
    private List<String> iaeConfirmationStatisticsStepList;

    /**
     * 业绩统计GEA成功量相关步骤列表枚举
     */
    @ApiModelProperty(value = "业绩统计GEA成功量相关步骤列表枚举")
    private List<String> geaSuccessStatisticsStepList;

    /**
     * 业绩统计IAE成功量相关步骤列表枚举
     */
    @ApiModelProperty(value = "业绩统计IAE成功量相关步骤列表枚举")
    private List<String> iaeSuccessStatisticsStepList;

    @ApiModelProperty(value = "类型：申请量-application;交押量-confirmation;成功入学量-success")
    private String typeKey;
}

package com.get.salecenter.dto;

import com.get.salecenter.vo.EventCostVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 报名名册一键生成活动费用汇总参数
 */
@Data
public class ConventionRegistrationEventBillDto {

    @ApiModelProperty(value = "公司Id")
    @NotNull(message = "公司Id不能为空")
    private Long fkCompanyId;

    @ApiModelProperty(value = "学校供应商Id")
    @NotNull(message = "学校供应商Id不能为空")
    private Long fkInstitutionProviderId;

    @ApiModelProperty(value = "业务国家多选")
    @NotEmpty(message = "业务国家不能为空")
    private List<Long> fkAreaCountryIdList;

    @ApiModelProperty(value = "活动费用币种")
    @NotBlank(message = "活动费用币种不能为空")
    private String fkCurrencyTypeNumEvent;

    @ApiModelProperty(value = "活动费用金额")
    @NotNull(message = "活动费用金额不能为空")
    private BigDecimal eventAmount;

    @ApiModelProperty("批量分配活动金额")
    private List<EventCostDto> eventCostVos;

    @ApiModelProperty(value = "发起invoice币种")
    @NotBlank(message = "发起invoice币种不能为空")
    private String fkCurrencyTypeNumInvoice;

    @ApiModelProperty(value = "发起Invoice金额")
    private BigDecimal invoiceAmount;

    @ApiModelProperty(value = "invoice名目")
    @NotBlank(message = "invoice名目不能为空")
    private String invoiceSummary;

    @ApiModelProperty(value = "invoice收件人")
    @NotBlank(message = "invoice收件人不能为空")
    private String invoiceContactPerson;

    @ApiModelProperty(value = "invoice收件人Email")
    @NotBlank(message = "invoice收件人Email不能为空")
    private String invoiceContactEmail;

    @ApiModelProperty(value = "活动年份")
    @NotNull(message = "活动年份不能为空")
    private Integer eventYear;

    @ApiModelProperty(value = "活动摘要多选")
    @NotEmpty(message = "活动摘要不能为空")
    private List<Long> fkEventSummaryIdList;

    @ApiModelProperty(value = "状态：0作废/1打开")
    private Integer status;

}

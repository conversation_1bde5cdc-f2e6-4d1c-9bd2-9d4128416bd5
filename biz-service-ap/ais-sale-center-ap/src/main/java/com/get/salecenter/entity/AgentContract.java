package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;

@Data
@TableName("m_agent_contract")
public class AgentContract extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "学生代理申请签名关系Id")
    @JsonProperty("id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 学生代理Id
     */
    @ApiModelProperty(value = "学生代理Id")
    @Column(name = "fk_agent_id")
    private Long fkAgentId;
    /**
     * 学生代理合同类型Id
     */
    @ApiModelProperty(value = "学生代理合同类型Id")
    @Column(name = "fk_agent_contract_type_id")
    private Long fkAgentContractTypeId;
    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @Column(name = "contract_num")
    private String contractNum;
    /**
     * 合同开始时间
     */
    @ApiModelProperty(value = "合同开始时间")
    @Column(name = "start_time")
    private Date startTime;
    /**
     * 合同结束时间
     */
    @ApiModelProperty(value = "合同结束时间")
    @Column(name = "end_time")
    private Date endTime;

    @ApiModelProperty(value = "合同模板：0=MPS主合同/1=PMP主合同/2=PMP附加合同")
    private Integer contractTemplateMode;

    @ApiModelProperty(value = "附加协议内容")
    private String additional;
    /**
     * 返佣比例备注
     */
    @ApiModelProperty(value = "返佣比例备注")
    @Column(name = "return_commission_rate_note")
    private String returnCommissionRateNote;
    /**
     * 合同备注
     */
    @ApiModelProperty(value = "合同备注")
    @Column(name = "remark")
    private String remark;
    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @Column(name = "is_active")
    private Boolean isActive;
    @ApiModelProperty(value = "状态： 0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废")
    @Column(name = "status")
    private Integer status;
    @ApiModelProperty(value = "合同审批模式：0普通/1特殊")
    @Column(name = "contract_approval_mode")
    private Integer contractApprovalMode;
    @ApiModelProperty(value = "关联撤销合同Id")
    @Column(name = "fk_agent_contract_id_revoke")
    private Long fkAgentContractIdRevoke;

    /**
     * <p> Java类型:Integer &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 数据库类型:int(10) &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释:合同审批状态：0无合同/1有合同/2未签署/3待审核/4审核通过/-4审核驳回/5续约中/6生效中/7已过期（6和7为逻辑生成，非数据库字段） &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @ApiModelProperty(name = "合同审批状态：0无合同/1有合同/2未签署/3待审核/4审核通过/-4审核驳回/5续约中")
    @JsonProperty("contractApprovalStatus")
    @Column(name = "contract_approval_status")
    private Integer contractApprovalStatus;

}
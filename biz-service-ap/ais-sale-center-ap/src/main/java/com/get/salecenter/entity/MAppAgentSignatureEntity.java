package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.sql.Blob;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2025-06-26
 */
@Data
@TableName("m_app_agent_signature")
@ApiModel(description = "app代理签名")
@EqualsAndHashCode(callSuper = true)
public class MAppAgentSignatureEntity extends Model<MAppAgentSignatureEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * <p> Java类型:Long &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 数据库类型:bigint(19) &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释:学生代理申请签名关系Id &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @ApiModelProperty(value = "学生代理申请签名关系Id")
    @JsonProperty("id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * <p> Java类型:Long &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 数据库类型:bigint(19) &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释:学生代理申请Id &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @ApiModelProperty(value = "学生代理申请Id")
    @JsonProperty("fkAppAgentId")
    @TableField("fk_app_agent_id")
    private Long fkAppAgentId;

    /**
     * <p> Java类型:Integer &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 数据库类型:int(10) &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释:签名类型：1合同/2变更声明 &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @ApiModelProperty(value = "签名类型：1合同/2变更声明")
    @JsonProperty("type")
    @TableField("type")
    private Integer type;

    /**
     * <p> Java类型:Blob &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 数据库类型:blob &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释:签名文件 &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @ApiModelProperty(value = "签名文件")
    @JsonProperty("signature")
    @TableField("signature")
    private Blob signature;

    /**
     * <p> Java类型:Date &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 数据库类型:datetime &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释:创建时间 &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @ApiModelProperty(value = "创建时间")
    @JsonProperty("gmtCreate")
    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * <p> Java类型:String &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 数据库类型:varchar(50) &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释:创建用户(登录账号) &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @ApiModelProperty(value = "创建用户(登录账号)")
    @JsonProperty("gmtCreateUser")
    @TableField("gmt_create_user")
    private String gmtCreateUser;

    /**
     * <p> Java类型:Date &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 数据库类型:datetime &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释:修改时间 &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @ApiModelProperty(value = "修改时间")
    @JsonProperty("gmtModified")
    @TableField("gmt_modified")
    private Date gmtModified;

    /**
     * <p> Java类型:String &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 数据库类型:varchar(50) &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释:修改用户(登录账号) &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @ApiModelProperty(value = "修改用户(登录账号)")
    @JsonProperty("gmtModifiedUser")
    @TableField("gmt_modified_user")
    private String gmtModifiedUser;


} 
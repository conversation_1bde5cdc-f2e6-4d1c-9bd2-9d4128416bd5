package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("log_insurance_order_settlement")
public class LogInsuranceOrderSettlement extends BaseEntity implements Serializable {

    @ApiModelProperty(value = "保险订单Id")
    private Long fkInsuranceOrderId;

    @ApiModelProperty(value = "操作类型Key")
    private String operationKey;

    @ApiModelProperty(value = "传递参数Json")
    private String paramJson;

    @ApiModelProperty(value = "状态：0失败/1成功（成功可以不用记录）")
    private Integer status;

    @ApiModelProperty(value = "报错信息")
    private String errorMessage;

}
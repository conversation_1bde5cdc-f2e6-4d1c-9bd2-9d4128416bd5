package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2024/3/18
 * @TIME: 10:41
 * @Description:
 **/
@Data
public class InstitutionSelectDto {
    @ApiModelProperty(value = "公司Ids")
    private List<Long> fkCompanyIds;

    @ApiModelProperty(value = "国家Id")
    private Long fkAreaCountryId;

    @ApiModelProperty(value = "州省Id")
    private List<Long> fkAreaStateIds;

    @ApiModelProperty(value = "入学年份")
    private List<Integer> years;
}

package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("m_cycling_registration")
public class CyclingRegistration extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 峰会Id
     */
    @ApiModelProperty(value = "峰会Id")
    @Column(name = "fk_convention_id")
    private Long fkConventionId;

    /**
     * 峰会参展人员Id
     */
    @ApiModelProperty(value = "峰会参展人员Id")
    @Column(name = "fk_convention_person_id")
    private Long fkConventionPersonId;

    /**
     * 骑行路线
     */
    @ApiModelProperty(value = "骑行路线")
    @Column(name = "cycling_route")
    private String cyclingRoute;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    @Column(name = "name")
    private String name;
    /**
     * 英文名
     */
    @ApiModelProperty(value = "英文名")
    @Column(name = "name_en")
    private String nameEn;
    /**
     * 性别：0女/1男
     */
    @ApiModelProperty(value = "性别：0女/1男")
    @Column(name = "gender")
    private Integer gender;

    /**
     * 生日
     */
    @ApiModelProperty(value = "生日")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "birthday")
    private Date birthday;

    /**
     * 移动电话
     */
    @ApiModelProperty(value = "骑行移动电话")
    @Column(name = "mobile")
    private String mobile;
    /**
     * 身份类型：0身份证/1护照
     */
    @ApiModelProperty(value = "身份类型：0身份证/1护照/2港澳回乡证/3台胞证")
    @Column(name = "id_type")
    private Integer idType;
    /**
     * 证件号码
     */
    @ApiModelProperty(value = "证件号码")
    @Column(name = "id_num")
    private String idNum;
    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    @Column(name = "company")
    private String company;

    /**
     * 第一套骑行服尺码
     */
    @ApiModelProperty(value = "第一套骑行服尺码")
    private String suitSize1;

    /**
     * 第二套骑行服尺码
     */
    @ApiModelProperty(value = "第二套骑行服尺码")
    private String suitSize2;

    /**
     * 支付金额
     */
    @ApiModelProperty(value = "支付金额")
    @Column(name = "pay_amount")
    private BigDecimal payAmount;
    /**
     * 支付流水号
     */
    @ApiModelProperty(value = "支付流水号")
    @Column(name = "pay_order_num")
    private String payOrderNum;
    /**
     * 支付状态：0失败/1成功
     */
    @ApiModelProperty(value = "支付状态：0失败/1成功")
    @Column(name = "pay_status")
    private Integer payStatus;

    /**
     * 备注
     */
    @UpdateWithNull
    @ApiModelProperty(value = "备注")
    private String remark;
}
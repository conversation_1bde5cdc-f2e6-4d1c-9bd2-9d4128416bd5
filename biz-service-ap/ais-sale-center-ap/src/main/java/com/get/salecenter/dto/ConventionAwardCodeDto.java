package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @DATE: 2021/9/9
 * @TIME: 12:16
 * @Description:
 **/
@Data
public class ConventionAwardCodeDto extends BaseVoEntity implements Serializable {
    /**
     * 峰会Id
     */
    @ApiModelProperty(value = "峰会Id")
    private Long fkConventionId;

    /**
     * 抽奖号码（预生成，可自定义前序字符）
     */
    @ApiModelProperty(value = "抽奖号码（预生成，可自定义前序字符）")
    private String awardCode;

    /**
     * 峰会参展人员Id（抽奖码占用人）
     */
    @ApiModelProperty(value = "峰会参展人员Id（抽奖码占用人）")
    private Long fkConventionPersonId;

    /**
     * 系统支付单号，guid
     */
    @ApiModelProperty(value = "系统支付单号，guid")
    private String paySystemOrderNum;

    /**
     * 微信支付单号
     */
    @ApiModelProperty(value = "微信支付单号")
    private String payWechatOrderNum;

    /**
     * 使用状态：0锁定/1已购买未使用/2已购买已使用（中奖后不能再参与抽奖）
     */
    @ApiModelProperty(value = "使用状态：0锁定/1已购买未使用/2已购买已使用（中奖后不能再参与抽奖）")
    private Integer useType;
    /**
     * 开始数字
     */
    @ApiModelProperty(value = "开始数字")
    @NotNull(message = "开始数字", groups = {Add.class, Update.class})
    private int startNum;
    /**
     * 结束数字
     */
    @ApiModelProperty(value = "结束数字")
    @NotNull(message = "结束数字", groups = {Add.class, Update.class})
    private int endNum;

    /**
     * 位数
     */
    @ApiModelProperty(value = "位数")
    @NotNull(message = "位数", groups = {Add.class, Update.class})
    private int digit;
    /**
     * 前序
     */
    @ApiModelProperty(value = "前序")
    private String preamble;
    /**
     * 参会人姓名
     */
    @ApiModelProperty(value = "参会人姓名")
    private String name;


}

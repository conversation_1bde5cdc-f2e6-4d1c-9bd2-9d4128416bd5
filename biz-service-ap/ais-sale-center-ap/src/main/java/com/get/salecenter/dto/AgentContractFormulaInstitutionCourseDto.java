package com.get.salecenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @author: Sea
 * @create: 2021/4/22 18:01
 * @verison: 1.0
 * @description:
 */
@Data
public class AgentContractFormulaInstitutionCourseDto extends BaseVoEntity{
    /**
     * 学生代理合同公式Id
     */
    @ApiModelProperty(value = "学生代理合同公式Id")
    private Long fkAgentContractFormulaId;

    /**
     * 课程Id
     */
    @ApiModelProperty(value = "课程Id")
    private Long fkInstitutionCourseId;
}

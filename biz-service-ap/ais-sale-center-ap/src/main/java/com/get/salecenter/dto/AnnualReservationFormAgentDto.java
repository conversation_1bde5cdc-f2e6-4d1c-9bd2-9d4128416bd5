package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * iae年会代理报名表单
 *
 * @Date 10:34 2022/8/9
 * <AUTHOR>
 */
@Data
public class AnnualReservationFormAgentDto extends BaseVoEntity {

//    /**
//     * 中文姓
//     */
//    @ApiModelProperty(value = "中文姓")
//    @NotNull(message = "中文姓不能为空")
//    private String nameChn;

    /**
     * 中文名
     */
    @ApiModelProperty(value = "中文名")
    @NotNull(message = "中文名不能为空")
    private String nameChn;

    /**
     * 中文名
     */
    @ApiModelProperty(value = "中文名")
    @NotNull(message = "英文名")
    private String name;

    /**
     * 机构名
     */
    @ApiModelProperty(value = "机构名")
    @NotNull(message = "机构名不能为空")
    private String company;

    /**
     * 参加人电邮
     */
    @ApiModelProperty(value = "参加人电邮")
    @NotNull(message = "参加人电邮不能为空")
    private String email;

    /**
     * 参加人电话
     */
    @ApiModelProperty(value = "参加人电话")
    @NotNull(message = "参加人电话不能为空")
    private String tel;

    /**
     * 性别：0女/1男
     */
    @ApiModelProperty(value = "性别：0女/1男")
    @NotNull(message = "性别不能为空")
    private Integer gender;

    /**
     * 护照号
     */
    @ApiModelProperty(value = "护照号")
    private String passportNum;

    /**
     * 身份证号
     */
    @ApiModelProperty(value = "身份证号")
    private String idCardNum;

    @ApiModelProperty(value = "参加人职位")
    private String title;

    /**
     * 到店日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "到店日期")
    private Date checkInTime;

    /**
     * 离店日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "离店日期")
    private Date checkOutTime;

    @ApiModelProperty(value = "BD编号（4位）")
    @NotEmpty(message = "BD编号不能为空")
    private String bdCode;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

}

package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 代理合同续签DTO
 *
 * <AUTHOR>
 * @Date 2025-01-08
 * @Version 1.0
 */
@Data
@ApiModel(value = "代理合同续签DTO")
public class AgentContractRenewalDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 代理ID
     */
    @ApiModelProperty(value = "代理ID", required = true)
    private Long agentId;


    @ApiModelProperty(value = "代理ID签名", required = true)
    @NotNull(message = "代理ID签名")
    private String sign;

    /**
     * 联系人ID
     */
    @ApiModelProperty(value = "联系人ID", required = true)
    private Long contactPersonId;

    /**
     * <p> Java类型:String &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释:续约token &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @ApiModelProperty(value = "续约token")
    @JsonProperty("renewalToken")
    private String renewalToken;

}
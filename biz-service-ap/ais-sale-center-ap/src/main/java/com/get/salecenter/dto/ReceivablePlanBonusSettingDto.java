package com.get.salecenter.dto;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@TableName("r_receivable_plan_bonus_setting")
public class ReceivablePlanBonusSettingDto extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "应收计划ID")
    @Column(name = "fk_receivable_plan_id")
    private Long fkReceivablePlanId; // 应收计划ID

    //类型：BD奖金=1
    @ApiModelProperty(value = "类型")
    @Column(name = "type")
    private Integer type;

    //费率%
    @ApiModelProperty(value = "奖金比例")
    @Column(name = "bonus_commission_rate")
    private BigDecimal bonusCommissionRate;

    //定额金额
    @ApiModelProperty(value = "奖金固定金额")
    @Column(name = "bonus_fixed_amount")
    private BigDecimal bonusFixedAmount;

}

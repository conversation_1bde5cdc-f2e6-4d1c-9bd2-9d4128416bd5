package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("r_convention_hotel_room_person")
public class ConventionHotelRoomPerson extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 酒店房间Id
     */
    @ApiModelProperty(value = "酒店房间Id")
    @Column(name = "fk_convention_hotel_room_id")
    private Long fkConventionHotelRoomId;
    /**
     * 峰会参展人员Id
     */
    @ApiModelProperty(value = "峰会参展人员Id")
    @Column(name = "fk_convention_person_id")
    private Long fkConventionPersonId;
}
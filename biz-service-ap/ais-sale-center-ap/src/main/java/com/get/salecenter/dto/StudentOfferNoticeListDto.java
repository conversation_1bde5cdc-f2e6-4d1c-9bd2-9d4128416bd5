package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: Hardy
 * @create: 2023/11/21 15:03
 * @verison: 1.0
 * @description:
 */
@Data
public class StudentOfferNoticeListDto {

    @ApiModelProperty("公司id")
    private Long fkCompanyId;

//    @ApiModelProperty("发送状态未发=0/已发=1/失败=-1")
//    private Integer status;

    @ApiModelProperty("发送状态未发=0/已发=1/失败=-1")
    private Integer reportStatus;

    @ApiModelProperty("学生名称")
    private String studentName;

    @ApiModelProperty("学校名称")
    private String institutionName;

    @ApiModelProperty("课程名称")
    private String courseName;

//    @ApiModelProperty(value = "开学时间")
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
//    private Date openingTimeStart;
//
//    @ApiModelProperty(value = "开学时间")
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
//    private Date openingTimeEnd;


}
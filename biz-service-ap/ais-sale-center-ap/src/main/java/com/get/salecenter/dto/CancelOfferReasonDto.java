package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @author: Hardy
 * @create: 2023/1/11 10:54
 * @verison: 1.0
 * @description:
 */
@Data
public class CancelOfferReasonDto extends BaseVoEntity {
    /**
     * 原因名称
     */
    @NotBlank(message = "原因名称", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "原因名称")
    private String reasonName;

    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    /**
     * 关键字
     */
    @ApiModelProperty(value = "关键字")
    private String keyWord;

    private static final long serialVersionUID = 1L;
}

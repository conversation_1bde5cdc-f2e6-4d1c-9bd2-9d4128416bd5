package com.get.salecenter.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 代理申请状态枚举
 *
 * <AUTHOR>
 * @Date 2025/7/30
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum AppAgentStatusEnum {

    /**
     * 待审核
     */
    NEW(0, "待审核"),

    /**
     * 审核中
     */
    REVIEW(1, "审核中"),

    /**
     * 已通过
     */
    AGREE(2, "已通过"),

    /**
     * 已拒绝
     */
    REJECT(3, "已拒绝");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态描述
     */
    private final String msg;

    /**
     * 代理申请状态枚举映射表
     */
    private static final Map<Integer, AppAgentStatusEnum> APP_AGENT_STATUS_MAP = new HashMap<>();

    static {
        for (AppAgentStatusEnum statusEnum : AppAgentStatusEnum.values()) {
            APP_AGENT_STATUS_MAP.put(statusEnum.getCode(), statusEnum);
        }
    }

    /**
     * 根据状态码获取对应的代理申请状态枚举实例
     *
     * @param code 状态码
     * @return 对应的代理申请状态枚举实例，如果找不到则返回null
     */
    public static AppAgentStatusEnum getAppAgentStatusByCode(Integer code) {
        return APP_AGENT_STATUS_MAP.get(code);
    }

    /**
     * 根据状态码获取状态描述
     *
     * @param code 状态码
     * @return 状态描述，如果找不到则返回null
     */
    public static String getMsgByCode(Integer code) {
        AppAgentStatusEnum statusEnum = getAppAgentStatusByCode(code);
        return statusEnum != null ? statusEnum.getMsg() : null;
    }

    /**
     * 判断状态是否为待审核
     *
     * @param code 状态码
     * @return true表示是待审核状态，false表示不是
     */
    public static boolean isNew(Integer code) {
        return NEW.getCode().equals(code);
    }

    /**
     * 判断状态是否为审核中
     *
     * @param code 状态码
     * @return true表示是审核中状态，false表示不是
     */
    public static boolean isReview(Integer code) {
        return REVIEW.getCode().equals(code);
    }

    /**
     * 判断状态是否为已通过
     *
     * @param code 状态码
     * @return true表示是已通过状态，false表示不是
     */
    public static boolean isAgree(Integer code) {
        return AGREE.getCode().equals(code);
    }

    /**
     * 判断状态是否为已拒绝
     *
     * @param code 状态码
     * @return true表示是已拒绝状态，false表示不是
     */
    public static boolean isReject(Integer code) {
        return REJECT.getCode().equals(code);
    }

}
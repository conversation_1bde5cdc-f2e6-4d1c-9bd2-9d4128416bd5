package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @DATE: 2022/3/16
 * @TIME: 18:37
 * @Description:批量生成应收应付
 **/
@Data
public class BatchGenerateMatchingPlanDto extends BaseVoEntity {

    /**
     * 学习计划列表
     */
    @ApiModelProperty(value = "学习计划id列表")
    @NotEmpty(message = "学习计划id不能为空")
    private List<Long> offerItemIdList;


    @NotNull(message = "公司id不能为空")
    @ApiModelProperty(value = "公司id")
    private Long fkCompanyId;

    @NotNull(message = "应收不能为空")
    @ApiModelProperty(value = "应收计划")
    private ReceivablePlanDto receivablePlanVo;

    @ApiModelProperty(value = "应付计划")
    private PayablePlanDto payablePlanVo;

    @ApiModelProperty(value = "是否创建应付 true创建应付,false则可以不创建")
    @NotNull(message = "请传递createFlag")
    private Boolean createFlag;

    @ApiModelProperty(value = "是否同步学费到申请计划")
    private Boolean flag=false;

    public Boolean getCreateFlag() {
        if (Objects.isNull(createFlag)) {
            return false;
        }
        return createFlag;
    }
}

package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("r_student_service_fee_type_company")
public class StudentServiceFeeTypeCompany extends BaseEntity {

    @ApiModelProperty("学生服务费类型Id")
    @Column(name = "fk_student_service_fee_type_id")
    private Long fkStudentServiceFeeTypeId;

    @ApiModelProperty("公司id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;
}

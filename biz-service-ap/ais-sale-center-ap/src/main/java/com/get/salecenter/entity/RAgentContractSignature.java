package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.ibatis.type.BlobTypeHandler;

import java.io.Serializable;

@Data
@TableName("r_agent_contract_signature")
public class RAgentContractSignature extends BaseEntity implements Serializable {

    @TableField("fk_agent_contract_id")
    @ApiModelProperty(value = "学生代理合同Id")
    private Long fkAgentContractId;

    @TableField(value = "signature", typeHandler = BlobTypeHandler.class)
    @ApiModelProperty(value = "签名文件（二进制数据）")
    private String signature;
//    private byte[] signature;
}

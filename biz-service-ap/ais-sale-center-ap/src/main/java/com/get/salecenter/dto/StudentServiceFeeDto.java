package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class StudentServiceFeeDto extends BaseVoEntity{

    @NotNull(message = "学生id不能为空")
    @ApiModelProperty("学生id")
    private Long fkStudentId;


    @ApiModelProperty("代理id")
    private Long fkAgentId;


    @ApiModelProperty("BD")
    private Long fkStaffId;

    @ApiModelProperty("编号")
    private String num;

    @NotEmpty(message = "业务国家不能为空",groups = {Add.class,Update.class})
    @ApiModelProperty("业务国家")
    private List<Long> fkAreaCountryIds;

    @NotNull(message = "服务类型id不能为空",groups = {Add.class,Update.class})
    @ApiModelProperty("服务类型id")
    private Long fkStudentServiceFeeTypeId;

    @ApiModelProperty("收款方类型，枚举，如：m_student/m_institution_provider")
    @NotEmpty(message = "收款方类型不能为空",groups = {Add.class,Update.class})
    private String fkTypeKeyReceivable;

    @ApiModelProperty("收款方类型Id，如：m_student.id")
    @NotNull(message = "收款方类型Id不能为空",groups = {Add.class,Update.class})
    private Long fkTypeTargetIdReceivable;

    @ApiModelProperty("币种")
    private String fkCurrencyTypeNum;

    @ApiModelProperty("金额")
    private BigDecimal amount;

    @ApiModelProperty("税金")
    private BigDecimal taxes;

    @ApiModelProperty("业务发生开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date businessStartTime;

    @ApiModelProperty("业务发生结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date businessEndTime;

    @ApiModelProperty("销售时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date salesTime;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty(value = "角色员工id")
    @NotNull(message = "角色员工不能为空", groups = {Add.class})
    private List<ProjectRoleStaffDto> roleStaffVo;

    @ApiModelProperty("状态 0作废/1生效")
    private Integer status;

    @ApiModelProperty("附件列表")
    private List<MediaAndAttachedDto> mediaAttachedVo;


   
}

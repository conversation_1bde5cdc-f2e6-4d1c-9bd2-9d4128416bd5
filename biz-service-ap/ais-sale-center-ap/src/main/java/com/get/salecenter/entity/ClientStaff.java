package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.io.Serializable;
import java.util.Date;

@Data
@TableName("r_client_staff")
public class ClientStaff extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "学生资源Id")
    @Column(name = "fk_client_id", nullable = false)
    private Long fkClientId;

    @ApiModelProperty(value = "负责人Id")
    @Column(name = "fk_staff_id", nullable = false)
    private Long fkStaffId;

    @ApiModelProperty(value = "是否激活：0否/1是")
    @Column(name = "is_active")
    private Integer isActive;

    @ApiModelProperty(value = "绑定时间")
    @Column(name = "active_date")
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private Date activeDate;

    @ApiModelProperty(value = "取消绑定时间（下次绑定时，需要重新建立记录）")
    @Column(name = "unactive_date")
    @Temporal(TemporalType.TIMESTAMP)
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private Date unactiveDate;


}

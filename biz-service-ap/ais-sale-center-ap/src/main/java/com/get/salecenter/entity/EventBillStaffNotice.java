package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;

@Data
@TableName("r_event_bill_staff_notice")
public class EventBillStaffNotice extends BaseEntity implements Serializable {
    /**
     * 活动Id
     */
    @ApiModelProperty(value = "活动Id")
    @Column(name = "fk_event_bill_id")
    private Long fkEventBillId;

    /**
     * 通知员工Id
     */
    @ApiModelProperty(value = "通知员工Id")
    @Column(name = "fk_staff_id_notice")
    private Long fkStaffIdNotice;

    private static final long serialVersionUID = 1L;
}
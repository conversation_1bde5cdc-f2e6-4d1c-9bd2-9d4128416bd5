package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 学生当前申请状态汇总列表Vo
 *
 * <AUTHOR>
 * @date 2022/6/30 11:49
 */
@Data
public class CurrentStudentApplicationStatusListDto {

    @NotNull(message = "公司id不能为空")
    @Min(value = 1, message = "缺少公司id参数")
    @ApiModelProperty(value = "公司id")
    private Long companyId;

    @ApiModelProperty("学生创建时间(开始)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date studentBeginTime;

    @ApiModelProperty("学生创建时间(结束)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date studentEndTime;

    @ApiModelProperty(value = "报考国家id")
    private List<Long> fkAreaCountryIds;

    @ApiModelProperty(value = "绑定BD名称或编号")
    private String bdNameOrCode;

    @ApiModelProperty(value = "员工Id（业绩绑定，BD）")
    private Long fkBdId;

    @ApiModelProperty(value = "大区Id")
    private Long fkAreaRegionId;

//    @ApiModelProperty(value = "项目成员名称")
//    private String fkProjectMemberName;

    @ApiModelProperty(value = "项目成员id列表")
    private List<Long> fkProjectMemberIds;


    /**
     *  时间搜索类型：1-操作步骤时间；2-申请创建时间；3-学生开学时间 4-学生创建时间
     */
    @ApiModelProperty("时间搜索类型：1-操作步骤时间；2-申请创建时间；3-学生开学时间 4-学生创建时间 5-【业绩(按学生)统计时间】")
    private Integer selectType;

    @ApiModelProperty("修改时间(开始)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date beginTime;

    @ApiModelProperty("修改时间(结束)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "学生申请方案项目状态步骤Ids")
    private List<Long> fkStudentOfferItemStepIdList;

    @ApiModelProperty(value = "代理国家Id")
    private Long fkAreaCountryIdAgent;

    @ApiModelProperty(value = "代理省份ID")
    private List<Long> fkAreaStateIdAgent;

}

package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("r_staff_commission_student")
public class StaffCommissionStudent extends BaseEntity implements Serializable {
    /**
     * 学生Id
     */
    @ApiModelProperty(value = "学生Id")
    @Column(name = "fk_student_id")
    private Long fkStudentId;

    /**
     * 状态，枚举：1激活提成/2完成结算
     */
    @ApiModelProperty(value = "状态，枚举：1激活提成/2完成结算")
    @Column(name = "status")
    private Integer status;

    private static final long serialVersionUID = 1L;
}
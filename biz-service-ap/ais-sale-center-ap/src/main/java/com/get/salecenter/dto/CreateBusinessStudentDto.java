package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseVoEntity;
import com.get.salecenter.vo.ClientAgentVo;
import com.get.salecenter.vo.ClientProjectRoleStaffVo;
import com.get.salecenter.vo.ClientVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2024/1/11 17:32
 * @verison: 1.0
 * @description:
 */
@Data
public class CreateBusinessStudentDto extends BaseVoEntity {

 

    //==========继承ClientVo============
    @ApiModelProperty(value = "绑定的项目成员")
    private List<ClientProjectRoleStaffVo> clientProjectRoleStaffDtos;

    /**
     * 项目说明名称
     */
    @ApiModelProperty(value = "项目说明，枚举：0=3+1/1=2+2/2=4+0/3=交换生")
    private String educationProjectName;

    /**
     * 学位情况名称
     */
    @ApiModelProperty(value = "学位情况名称")
    private String educationDegreeName;

    @ApiModelProperty(value = "绑定的代理")
    List<ClientAgentVo> clientAgentDtoList;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String fkCompanyName;

    /**
     * 学生现居所在城市Id联查到的城市名称
     */
    @ApiModelProperty(value = "学生现居所在城市名称（下拉）")
    private String cityName;
    /**
     * 毕业国家名称
     */
    @ApiModelProperty(value = "毕业国家名称（下拉）")
    private String countryNameEducation;
    /**
     * 毕业州省名称
     */
    @ApiModelProperty(value = "毕业州省名称（下拉）")
    private String stateNameEducation;
    /**
     * 毕业城市名称
     */
    @ApiModelProperty(value = "毕业城市名称（下拉）")
    private String cityNameEducation;

    @ApiModelProperty(value = "国内学历名称")
    private String domesticEducationName;

    @ApiModelProperty(value = "国际学历名称")
    private String internationalEducationName;
    /**
     * 毕业国家名称国际
     */
    @ApiModelProperty(value = "毕业国家名称国际（下拉）")
    private String countryNameEducation2;
    /**
     * 毕业州省名称国际
     */
    @ApiModelProperty(value = "毕业州省名称国际（下拉）")
    private String stateNameEducation2;
    /**
     * 毕业城市名称国际
     */
    @ApiModelProperty(value = "毕业城市名称国际（下拉）")
    private String cityNameEducation2;
    /**
     * 年龄
     */
    @ApiModelProperty(value = "年龄")
    private Integer age;

    /**
     * 学生现居所在国家Id联查到的对应国家名称
     */
    @ApiModelProperty(value = "学生现居所在国家（下拉）")
    private String countryName;
    /**
     * 学生现居所在州省Id联查到的州省名称
     */
    @ApiModelProperty(value = "学生现居所在州省名称（下拉）")
    private String stateName;

    /**
     * 学生出生所在国家名
     */
    @ApiModelProperty(value = "学生出生所在国家名")
    private String fkAreaCountryBirthName;
    /**
     * 学生出生所在州省名
     */
    @ApiModelProperty(value = "学生出生所在州省名")
    private String fkAreaStateBirthName;
    /**
     * 学生出生所在城市名
     */
    @ApiModelProperty(value = "学生出生所在城市名")
    private String fkAreaCityBirthName;

    /**
     * 学生国籍所在国家Id联查到的对应国家名称
     */
    @ApiModelProperty(value = "学生国籍所在国家名称（下拉）")
    private String countryNameNationality;
    /**
     * 绿卡国家名称
     */
    @ApiModelProperty(value = "绿卡国家名称（下拉）")
    private String countryNameGreenCard;

    /**
     * 代理名称
     */
    @ApiModelProperty(value = "代理名称")
    private List<String> fkAgentName;
    /**
     * 员工名称
     */
    @ApiModelProperty(value = "员工名称")
    private List<String> fkStaffName;

    /**
     * 高中成绩类型名称
     */
    @ApiModelProperty(value = "高中成绩类型名称")
    private String highSchoolTestTypeName;


    /**
     * 本科成绩类型名称
     */
    @ApiModelProperty(value = "本科成绩类型名称")
    private String standardTestTypeName;
    /**
     * 英语测试类型
     */
    @ApiModelProperty(value = "英语测试类型")
    private String englishTestTypeName;

    /**
     * 硕士成绩类型，枚举Key
     */
    @ApiModelProperty(value = "硕士成绩类型，枚举Key")
    private String masterTestTypeName;

    /**
     * 硕士成绩
     */
    @ApiModelProperty(value = "本科成绩")
    private String masterTestScore;

    @ApiModelProperty(value = "是否有项目方案")
    private Boolean cilentOfferFlag;
    /**
     * 硕士成绩
     */
    @ApiModelProperty(value = "国内学校名称（下拉）")
    private String educationInstitutionName;

    /**
     * 硕士成绩
     */
    @ApiModelProperty(value = "国际学校名称（下拉）")
    private String educationInstitutionName2;

    @ApiModelProperty(value = "是否业务创建学生true是/false否")
    private Boolean isBusinessCreateStudent;

    @ApiModelProperty(value = "业务创建学生编号")
    private String businessCreateStudentNum;

    @ApiModelProperty(value = "学生id")
    private Long fkStudentId;

    @ApiModelProperty(value = "部门名称")
    private String departmentName;

    //==============实体类Client===================
    private static final long serialVersionUID = 1L;
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;
    /**
     * 客户编号
     */
    @ApiModelProperty(value = "客户编号")
    @Column(name = "num")
    private String num;
    /**
     * 客户姓名（中）
     */
    @ApiModelProperty(value = "客户姓名（中）")
    @Column(name = "name")
    private String name;
    /**
     * 姓（英/拼音）
     */
    @ApiModelProperty(value = "姓（英/拼音）")
    @Column(name = "last_name")
    private String lastName;
    /**
     * 名（英/拼音）
     */
    @ApiModelProperty(value = "名（英/拼音）")
    @Column(name = "first_name")
    private String firstName;
    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    @Column(name = "gender")
    private String gender;
    /**
     * 生日
     */
    @ApiModelProperty(value = "生日")
    @Column(name = "birthday")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date birthday;


    @ApiModelProperty("客户国籍所在国家Id")
    @Column(name = "fk_area_country_id_nationality")
    private Long fkAreaCountryIdNationality;

    @ApiModelProperty(value = "客户国籍所在国家名称")
    @Column(name = "fk_area_country_name_nationality")
    private String fkAreaCountryNameNationality;

    @ApiModelProperty(value = "绿卡国家Id")
    @Column(name = "fk_area_country_id_green_card")
    private Long fkAreaCountryIdGreenCard;

    @ApiModelProperty(value = "护照编号")
    @Column(name = "passport_num")
    private String passportNum;
    /**
     * 学生出生所在国家Id
     */
    @UpdateWithNull
    @ApiModelProperty(value = "学生出生所在国家Id")
    @Column(name = "fk_area_country_id_birth")
    private Long fkAreaCountryIdBirth;
    /**
     * 学生出生所在州省Id
     */
    @UpdateWithNull
    @ApiModelProperty(value = "学生出生所在州省Id")
    @Column(name = "fk_area_state_id_birth")
    private Long fkAreaStateIdBirth;
    /**
     * 学生出生所在城市Id
     */
    @UpdateWithNull
    @ApiModelProperty(value = "学生出生所在城市Id")
    @Column(name = "fk_area_city_id_birth")
    private Long fkAreaCityIdBirth;
    /**
     * 学生出生所在国家名称
     */
    @UpdateWithNull
    @ApiModelProperty(value = "学生出生所在国家名称")
    @Column(name = "fk_area_country_name_birth")
    private String fkAreaCountryNameBirth;
    /**
     * 学生出生所在州省名称
     */
    @UpdateWithNull
    @ApiModelProperty(value = "学生出生所在州省名称")
    @Column(name = "fk_area_state_name_birth")
    private String fkAreaStateNameBirth;
    /**
     * 学生出生所在城市名称
     */
    @UpdateWithNull
    @ApiModelProperty(value = "学生出生所在城市名称")
    @Column(name = "fk_area_city_name_birth")
    private String fkAreaCityNameBirth;
    /**
     * 手机区号
     */
    @ApiModelProperty(value = "手机区号")
    @Column(name = "mobile_area_code")
    private String mobileAreaCode;
    /**
     * 移动电话
     */
    @ApiModelProperty(value = "移动电话")
    @Column(name = "mobile")
    private String mobile;
    /**
     * 电话区号
     */
    @ApiModelProperty(value = "电话区号")
    @Column(name = "tel_area_code")
    private String telAreaCode;
    /**
     * 电话
     */
    @ApiModelProperty(value = "电话")
    @Column(name = "tel")
    private String tel;
    /**
     * Email
     */
    @ApiModelProperty(value = "Email")
    @Column(name = "email")
    private String email;

    @ApiModelProperty(value = "微信号")
    @Column(name = "wechat")
    private String wechat;
    /**
     * 学生现居所在国家Id
     */
    @UpdateWithNull
    @ApiModelProperty(value = "学生现居所在国家Id")
    @Column(name = "fk_area_country_id")
    private Long fkAreaCountryId;
    /**
     * 学生现居所在州省Id
     */
    @UpdateWithNull
    @ApiModelProperty(value = "学生现居所在州省Id")
    @Column(name = "fk_area_state_id")
    private Long fkAreaStateId;
    /**
     * 学生现居所在城市Id
     */
    @UpdateWithNull
    @ApiModelProperty(value = "学生现居所在城市Id")
    @Column(name = "fk_area_city_id")
    private Long fkAreaCityId;
    /**
     * 学生现居所在国家名称
     */
    @UpdateWithNull
    @ApiModelProperty(value = "学生现居所在国家名称")
    @Column(name = "fk_area_country_name")
    private String fkAreaCountryName;
    /**
     * 学生现居所在州省名称
     */
    @UpdateWithNull
    @ApiModelProperty(value = "学生现居所在州省名称")
    @Column(name = "fk_area_state_name")
    private String fkAreaStateName;
    /**
     * 学生现居所在城市名称
     */
    @UpdateWithNull
    @ApiModelProperty(value = "学生现居所在城市名称")
    @Column(name = "fk_area_city_name")
    private String fkAreaCityName;
    /**
     * 邮编
     */
    @ApiModelProperty(value = "邮编")
    @Column(name = "zipcode")
    private String zipcode;

    /**
     * 联系地址
     */
    @ApiModelProperty(value = "联系地址")
    @Column(name = "contact_address")
    private String contactAddress;

    @ApiModelProperty(value = "手机区号（父亲）")
    @Column(name = "mobile_area_code_father")
    private String mobileAreaCodeFather;

    @ApiModelProperty(value = "移动电话（父亲）")
    @Column(name = "mobile_father")
    private String mobileFather;

    @ApiModelProperty(value = "电话区号（父亲）")
    @Column(name = "tel_area_code_father")
    private String telAreaCodeFather;

    @ApiModelProperty(value = "电话（父亲）")
    @Column(name = "tel_father")
    private String telFather;

    @ApiModelProperty(value = "微信号（父亲）")
    @Column(name = "wechat_father")
    private String wechatFather;

    @ApiModelProperty(value = "手机区号（母亲）")
    @Column(name = "mobile_area_code_mother")
    private String mobileAreaCodeMother;

    @ApiModelProperty(value = "移动电话（母亲）")
    @Column(name = "mobile_mother")
    private String mobileMother;

    @ApiModelProperty(value = "电话区号（母亲）")
    @Column(name = "tel_area_code_mother")
    private String telAreaCodeMother;

    @ApiModelProperty(value = "电话（母亲）")
    @Column(name = "tel_mother")
    private String telMother;

    @ApiModelProperty(value = "微信号（母亲）")
    @Column(name = "wechat_mother")
    private String wechatMother;

    /**
     * 学历等级类型：中专/高中/大学/大专/专升本/本科/研究生/硕士/博士在读/博士/博士后/HND
     */
    @ApiModelProperty(value = "学历等级类型：中专/高中/大学/大专/专升本/本科/研究生/硕士/博士在读/博士/博士后/HND")
    @Column(name = "education_level_type")
    private String educationLevelType;
    /**
     * 毕业专业
     */
    @ApiModelProperty(value = "毕业专业")
    @Column(name = "education_major")
    private String educationMajor;
    /**
     * 毕业大学国家Id
     */
    @UpdateWithNull
    @ApiModelProperty(value = "毕业大学国家Id")
    @Column(name = "fk_area_country_id_education")
    private Long fkAreaCountryIdEducation;
    /**
     * 毕业大学州省Id
     */
    @UpdateWithNull
    @ApiModelProperty(value = "毕业大学州省Id")
    @Column(name = "fk_area_state_id_education")
    private Long fkAreaStateIdEducation;
    /**
     * 毕业大学城市Id
     */
    @UpdateWithNull
    @ApiModelProperty(value = "毕业大学城市Id")
    @Column(name = "fk_area_city_id_education")
    private Long fkAreaCityIdEducation;
    /**
     * 毕业大学国家名称
     */
    @UpdateWithNull
    @ApiModelProperty(value = "毕业大学国家名称")
    @Column(name = "fk_area_country_name_education")
    private String fkAreaCountryNameEducation;
    /**
     * 毕业大学州省名称
     */
    @UpdateWithNull
    @ApiModelProperty(value = "毕业大学州省名称")
    @Column(name = "fk_area_state_name_education")
    private String fkAreaStateNameEducation;
    /**
     * 毕业大学城市名称
     */
    @UpdateWithNull
    @ApiModelProperty(value = "毕业大学城市名称")
    @Column(name = "fk_area_city_name_education")
    private String fkAreaCityNameEducation;
    /**
     * 毕业院校Id
     */
    @UpdateWithNull
    @ApiModelProperty(value = "毕业院校Id")
    @Column(name = "fk_institution_id_education")
    private Long fkInstitutionIdEducation;
    /**
     * 毕业院校名称
     */
    @UpdateWithNull
    @ApiModelProperty(value = "毕业院校名称")
    @Column(name = "fk_institution_name_education")
    private String fkInstitutionNameEducation;
    /**
     * 毕业学校类型：985/211/其他，默认选项：其他
     */
    @ApiModelProperty(value = "毕业学校类型：985/211/其他，默认选项：其他")
    @Column(name = "institution_type_education")
    private String institutionTypeEducation;
    /**
     * 学历等级类型（国际）：中专/高中/大学/大专/专升本/本科/研究生/硕士/博士在读/博士/博士后/HND
     */
    @ApiModelProperty(value = "学历等级类型（国际）：中专/高中/大学/大专/专升本/本科/研究生/硕士/博士在读/博士/博士后/HND")
    @Column(name = "education_level_type2")
    private String educationLevelType2;
    /**
     * 毕业专业（国际）
     */
    @UpdateWithNull
    @ApiModelProperty(value = "毕业专业（国际）")
    @Column(name = "education_major2")
    private String educationMajor2;
    /**
     * 毕业大学国家Id（国际）
     */
    @UpdateWithNull
    @ApiModelProperty(value = "毕业大学国家Id（国际）")
    @Column(name = "fk_area_country_id_education2")
    private Long fkAreaCountryIdEducation2;
    /**
     * 毕业大学州省Id（国际）
     */
    @UpdateWithNull
    @ApiModelProperty(value = "毕业大学州省Id（国际）")
    @Column(name = "fk_area_state_id_education2")
    private Long fkAreaStateIdEducation2;
    /**
     * 毕业大学城市Id（国际）
     */
    @UpdateWithNull
    @ApiModelProperty(value = "毕业大学城市Id（国际）")
    @Column(name = "fk_area_city_id_education2")
    private Long fkAreaCityIdEducation2;
    /**
     * 毕业大学国家名称（国际）
     */
    @UpdateWithNull
    @ApiModelProperty(value = "毕业大学国家名称（国际）")
    @Column(name = "fk_area_country_name_education2")
    private String fkAreaCountryNameEducation2;
    /**
     * 毕业大学州省名称（国际）
     */
    @UpdateWithNull
    @ApiModelProperty(value = "毕业大学州省名称（国际）")
    @Column(name = "fk_area_state_name_education2")
    private String fkAreaStateNameEducation2;
    /**
     * 毕业大学城市名称（国际）
     */
    @UpdateWithNull
    @ApiModelProperty(value = "毕业大学城市名称（国际）")
    @Column(name = "fk_area_city_name_education2")
    private String fkAreaCityNameEducation2;
    /**
     * 毕业院校Id（国际）
     */
    @UpdateWithNull
    @ApiModelProperty(value = "毕业院校Id（国际）")
    @Column(name = "fk_institution_id_education2")
    private Long fkInstitutionIdEducation2;
    /**
     * 毕业院校名称（国际）
     */
    @UpdateWithNull
    @ApiModelProperty(value = "毕业院校名称（国际）")
    @Column(name = "fk_institution_name_education2")
    private String fkInstitutionNameEducation2;
    /**
     * 项目说明，枚举：0=3+1/1=2+2/2=4+0/3=交换生
     */
    @ApiModelProperty(value = "项目说明，枚举：0=3+1/1=2+2/2=4+0/3=交换生")
    @Column(name = "education_project")
    private Integer educationProject;
    /**
     * 学位情况，枚举：获得双学位/获得国际学位/获得国内学位
     */
    @ApiModelProperty(value = "学位情况，枚举：获得双学位/获得国际学位/获得国内学位")
    @Column(name = "education_degree")
    private Integer educationDegree;
    /**
     * 高中成绩类型，枚举Key
     */
    @ApiModelProperty(value = "高中成绩类型，枚举Key")
    @Column(name = "high_school_test_type")
    private String highSchoolTestType;
    /**
     * 高中成绩
     */
    @ApiModelProperty(value = "高中成绩")
    @Column(name = "high_school_test_score")
    private String highSchoolTestScore;
    /**
     * 本科成绩类型，枚举Key
     */
    @ApiModelProperty(value = "本科成绩类型，枚举Key")
    @Column(name = "standard_test_type")
    private String standardTestType;
    /**
     * 本科成绩
     */
    @ApiModelProperty(value = "本科成绩")
    @Column(name = "standard_test_score")
    private String standardTestScore;
    /**
     * 硕士成绩类型，枚举Key
     */
    @ApiModelProperty(value = "硕士成绩类型，枚举Key")
    @Column(name = "master_test_type")
    private String masterTestType;

    /**
     * 英语测试类型
     */
    @ApiModelProperty(value = "英语测试类型")
    @Column(name = "english_test_type")
    private String englishTestType;
    /**
     * 英语测试成绩
     */
    @ApiModelProperty(value = "英语测试成绩")
    @Column(name = "english_test_score")
    private BigDecimal englishTestScore;

    @ApiModelProperty(value = "客户星级")
    @Column(name = "star_level")
    private Integer starLevel;

    @ApiModelProperty(value = "预计签约时间")
    @Column(name = "expect_signing_time")
    private String expectSigningTime;

    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "入读时间")
    @Column(name = "start_time_education")
    private Date startTimeEducation;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "毕业时间")
    @Column(name = "end_time_education")
    private Date endTimeEducation;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "入读时间（国际）")
    @Column(name = "start_time_education2")
    private Date startTimeEducation2;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "毕业时间（国际）")
    @Column(name = "end_time_education2")
    private Date endTimeEducation2;

    @ApiModelProperty(value = "是否入境，false否/true是")
    @Column(name = "is_enter_country")
    private Boolean isEnterCountry;
}

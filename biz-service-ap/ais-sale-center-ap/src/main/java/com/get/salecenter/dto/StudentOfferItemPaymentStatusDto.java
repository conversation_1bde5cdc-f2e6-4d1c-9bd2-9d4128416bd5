package com.get.salecenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 代付费用日志VO
 */
@Data
public class StudentOfferItemPaymentStatusDto extends BaseVoEntity {

    @ApiModelProperty(value = "申请计划id")
    @NotNull(message = "申请计划id不能为空", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    private Long fkStudentOfferItemId;

    @ApiModelProperty(value = "支付类型：1.申请费 2.全额学费 3.学费首付 Deposit 4.学费尾款 Remaining")
    @NotNull(message = "支付类型不能为空", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    private Integer paymentType;

    @ApiModelProperty(value = "支付币种")
    @NotNull(message = "支付币种不能为空", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    private String fkCurrencyTypeNum;

    @ApiModelProperty(value = "支付金额")
    @NotNull(message = "支付金额不能为空", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    private BigDecimal paidAmount;

    @ApiModelProperty(value = "支付状态：0.付费失败 1.已发送代付邮件 2.已付费(支付渠道) 3.已付费(学校)")
    @NotNull(message = "支付状态不能为空", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    private Integer paymentStatus;

    @ApiModelProperty(value = "备注")
    private String remark;

}

package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@Data
public class StaffCommissionRefundDto {

    /**
     * 公司IDS
     */
    @ApiModelProperty(value = "公司IDs")
    @NotNull(message = "公司Ids不能为空")
    private List<Long> fkCompanyIds;

    /**
     * 结算日期
     */
    @ApiModelProperty(value = "结算日期")
    private String settlementDate;

    /**
     * 退款审批状态
     */
    @ApiModelProperty(value = "退款审批状态")
    private Integer refundReviewStatus;

    /**
     * 退款结算状态
     */
    @ApiModelProperty(value = "退款结算状态")
    private Integer refundSettlementStatus;

    /**
     * 结算角色/成员名称
     */
    @ApiModelProperty(value = "结算角色/成员名称")
    private String staffName;

    @ApiModelProperty(value = "代理名称")
    private String agentName;

    /**
     * 学生名称
     */
    @ApiModelProperty(value = "学生名称")
    private String studentName;

    /**
     * 学校名称
     */
    @ApiModelProperty(value = "学校名称")
    private String institutionName;

    @ApiModelProperty(value = "退款结算操作时间(开始范围)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createBeginTime;

    @ApiModelProperty(value = "退款结算结束时间(结束范围)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createEndTime;

    @ApiModelProperty(value = "退款审核操作时间(开始范围)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date refundReviewBeginTime;

    @ApiModelProperty(value = "退款审核操作时间(结束范围)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date refundReviewEndTime;

    @ApiModelProperty(value = "入学失败操作时间(开始范围)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date stepFailureBeginTime;

    @ApiModelProperty(value = "入学失败操作时间(结束范围)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date stepFailureEndTime;

    @ApiModelProperty(value = "结算退款原因Ids")
    private List<Long> fkSettlementRefundReasonIds;

}

package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @DATE: 2024/4/16
 * @TIME: 12:32
 * @Description:KPI目标设置
 **/
@Data
@TableName("m_kpi_plan_target")
@ApiModel(value="KPI目标设置", description="")
public class KpiPlanTarget extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "KPI方案组别子项Id")
    private Long fkKpiPlanGroupItemId;
//
//    @ApiModelProperty(value = "员工Id")
//    private Long fkStaffId;

    @ApiModelProperty(value = "目标设置（成功入学）")
    private Integer targetEnrolled;

    @ApiModelProperty(value = "KPI方案考核人员Id")
    //@Column(name = "fk_kpi_plan_staff_id")
    private Long fkKpiPlanStaffId;
}

package com.get.salecenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2024/2/23 10:04
 * @verison: 1.0
 * @description:
 */
@Data
public class StudentOfferItemStepCountryDto extends BaseVoEntity implements Serializable{

    private static final long serialVersionUID = 1L;

    @NotNull(message = "学生申请方案项目状态步骤Id", groups = {Add.class,Datas.class})
    @ApiModelProperty(value = "学生申请方案项目状态步骤Id")
    private Long fkStudentOfferItemStepId;

    @NotEmpty(message = "前置步骤Id（多选）", groups = {Add.class})
    @ApiModelProperty(value = "前置条件id，需要完成步骤条件")
    private List<Long> fkStudentOfferItemStepIdsPrecondition;

    @NotEmpty(message = "适用国家Id（多选）", groups = {Add.class})
    @ApiModelProperty(value = "适用国家Ids（多选）")
    private List<Long> fkAreaCountryIdList;

    public interface Datas {
    }

  
}

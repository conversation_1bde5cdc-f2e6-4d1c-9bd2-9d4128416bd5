package com.get.salecenter.dto;

import com.get.core.mybatis.utils.ValidList;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 生成应收应付
 *
 * <AUTHOR>
 * @date 2022/1/24 17:18
 */
@Data
public class GenerateMatchingPlanListDto {

    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    @Column(name = "fk_currency_type_num")
    @NotBlank(message = "币种编号不能为空")
    private String fkCurrencyTypeNum;

    /**
     * 学费金额
     */
    @ApiModelProperty(value = "学费金额")
    @Column(name = "tuition_amount")
    @NotNull(message = "学费金额不能为空")
    private BigDecimal tuitionAmount;

    /**
     * 生成应收应付列表
     */
    @ApiModelProperty(value = "生成应收应付列表")
    @NotEmpty(message = "生成应收应付列表不能为空")
    private ValidList<GenerateMatchingPlanDto> generateMatchingPlanVoList;

}

package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("m_convention_person")
public class ConventionPerson extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 峰会Id
     */
    @ApiModelProperty(value = "峰会Id")
    @Column(name = "fk_convention_id")
    private Long fkConventionId;
    /**
     * 参展人员类型（枚举定义）：0校方/1代理/2嘉宾/3员工/4工作人员
     */
    @ApiModelProperty(value = "参展人员类型（枚举定义）：0校方/1代理/2嘉宾/3员工/4工作人员")
    @Column(name = "type")
    private Integer type;
    /**
     * 参会编号
     */
    @ApiModelProperty(value = "参会编号")
    @Column(name = "num")
    private String num;
    /**
     * 参加人姓名
     */
    @ApiModelProperty(value = "参加人姓名")
    @Column(name = "name")
    private String name;
    /**
     * 参加人姓名（中文）
     */
    @ApiModelProperty(value = "参加人姓名（中文）")
    @Column(name = "name_chn")
    private String nameChn;
    /**
     * 性别：0女/1男
     */
    @ApiModelProperty(value = "性别：0女/1男")
    @Column(name = "gender")
    private Integer gender;
    /**
     * 参加人职位
     */
    @ApiModelProperty(value = "参加人职位")
    @Column(name = "title")
    private String title;
    /**
     * 公司
     */
    @ApiModelProperty(value = "公司")
    @Column(name = "company")
    private String company;
    /**
     * 参加人电邮
     */
    @ApiModelProperty(value = "参加人电邮")
    @Column(name = "email")
    private String email;
    /**
     * 参加人电话
     */
    @ApiModelProperty(value = "参加人电话")
    @Column(name = "tel")
    private String tel;
    /**
     * 护照号
     */
    @ApiModelProperty(value = "护照号")
    @Column(name = "passport_num")
    private String passportNum;
    /**
     * 身份证号
     */
    @ApiModelProperty(value = "身份证号")
    @Column(name = "id_card_num")
    private String idCardNum;
    /**
     * 备注
     */
    @UpdateWithNull
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;

    @UpdateWithNull
    @ApiModelProperty(value = "其他自定义信息记录")
    @Column(name = "remark_json")
    private String remarkJson;

    /**
     * 到店日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "到店日期")
    @Column(name = "check_in_time")
    private Date checkInTime;
    /**
     * 离店日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "离店日期")
    @Column(name = "check_out_time")
    private Date checkOutTime;
    /**
     * 预计到达时间
     */
    @ApiModelProperty(value = "预计到达时间")
    @Column(name = "arrival_time")
    private Date arrivalTime;
    /**
     * 到达交通类型（枚举定义选择）：0飞机/1高铁/2汽车
     */
    @ApiModelProperty(value = "到达交通类型（枚举定义选择）：0飞机/1高铁/2汽车")
    @Column(name = "arrival_transportation")
    private String arrivalTransportation;
    /**
     * 到达交通编号：航班号/高铁班次/汽车班次
     */
    @ApiModelProperty(value = "到达交通编号：航班号/高铁班次/汽车班次")
    @Column(name = "arrival_transportation_code")
    private String arrivalTransportationCode;
    /**
     * 预计离开时间
     */
    @ApiModelProperty(value = "预计离开时间")
    @Column(name = "leave_time")
    private Date leaveTime;
    /**
     * 离开交通类型（枚举定义选择）：0飞机/1高铁/2汽车
     */
    @ApiModelProperty(value = "离开交通类型（枚举定义选择）：0飞机/1高铁/2汽车")
    @Column(name = "leave_transportation")
    private String leaveTransportation;
    /**
     * 离开交通编号：航班号/高铁班次/汽车班次
     */
    @ApiModelProperty(value = "离开交通编号：航班号/高铁班次/汽车班次")
    @Column(name = "leave_transportation_code")
    private String leaveTransportationCode;
    /**
     * 酒店住宿费用类型：0公费/1自费
     */
    @ApiModelProperty(value = "酒店住宿费用类型：0公费/1自费")
    @Column(name = "hotel_fee_type")
    private Integer hotelFeeType;
    /**
     * 价格币种编号
     */
    @ApiModelProperty(value = "价格币种编号")
    @Column(name = "fk_currency_type_num_hotel_expense")
    private String fkCurrencyTypeNumHotelExpense;
    /**
     * 住宿费用单价（每晚）
     */
    @ApiModelProperty(value = "住宿费用单价（每晚）")
    @Column(name = "hotel_expense")
    private BigDecimal hotelExpense;
    /**
     * 酒店房型Id
     */
    @ApiModelProperty(value = "酒店房型Id")
    @Column(name = "fk_convention_hotel_id")
    private Long fkConventionHotelId;
    /**
     * BD编号（4位）
     */
    @ApiModelProperty(value = "BD编号（4位）")
    @Column(name = "bd_code")
    private String bdCode;

    /**
     * 是否VIP：0否/1是
     */
    @ApiModelProperty(value = "是否VIP：0否/1是")
    @Column(name = "is_vip")
    private Boolean isVip;
    /**
     * 是否出席：0否/1是
     */
    @ApiModelProperty(value = "是否出席：0否/1是")
    @Column(name = "is_attend")
    private Boolean isAttend;
    /**
     * 是否出席：0否/1是
     */
    @ApiModelProperty(value = "是否参加晚宴：0否/1是")
    @Column(name = "is_attend_dinner")
    private Boolean isAttendDinner;
    /**
     * 是否出席：0否/1是
     */
    @ApiModelProperty(value = "是否代订酒店：0否/1是")
    @Column(name = "is_book_hotel")
    private Boolean isBookHotel;
}
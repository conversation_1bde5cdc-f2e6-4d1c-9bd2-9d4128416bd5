package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 提交代理确认结算ItemVo
 *
 * <AUTHOR>
 * @date 2021/12/29 12:10
 */
@Data
public class SubmitSettlementItemDto {

    @NotNull(message = "应付计划id不能为空")
    @ApiModelProperty(value = "应付计划id")
    private Long fkPayablePlanId;

    @NotNull(message = "是否预付标记不能为空")
    @ApiModelProperty(value = "是否预付标记  true:是预付 false:不是预付")
    private Boolean isPayInAdvance;

    @ApiModelProperty(value = "学生代理合同账户Id")
    private Long fkAgentContractAccountId;

    @ApiModelProperty(value = "学生代理合同账户币种编号")
    private String agentContractAccountNum;

    @ApiModelProperty(value = "结算ids")
    private String settlementIds;

}

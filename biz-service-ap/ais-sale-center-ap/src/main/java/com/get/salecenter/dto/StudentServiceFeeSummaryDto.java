package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class StudentServiceFeeSummaryDto {

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("公司id")
    private Long fkCompanyId;

    @ApiModelProperty("公司id")
    private List<Long> fkCompanyIds;

    @ApiModelProperty("学生名称 OR 编号")
    private String studentName;

    @ApiModelProperty("代理名称")
    private String agentName;

    @ApiModelProperty("BD名称")
    private String bdName;

    @ApiModelProperty("项目成员名称")
    private String projectRoleName;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTime;

    @ApiModelProperty("结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    @ApiModelProperty("部门id")
    private Long fkDepartmentId;

    @ApiModelProperty("学生服务费类型Id")
    private Long fkStudentServiceFeeTypeId;

    @ApiModelProperty("学生服务费类型Ids")
    private List<Long> fkStudentServiceFeeTypeIds;

    @ApiModelProperty("学生服务费Id")
    private Long fkStudentServiceFeeId;

    /**
     * 应收状态
     */
    @ApiModelProperty(value = "应收状态：0/未收 1/部分已收 2/收款完成")
    private Integer arStatus;

    /**
     * 应付状态
     */
    @ApiModelProperty(value = "应付状态：0/未付 1/部分已付 2/已付清 3/未付【已创建应付计划】")
    private Integer apStatus;

    @ApiModelProperty("业务状态：0未完成/1已完成")
    private Integer businessStatus;

    @ApiModelProperty("结算审批状态：0未审批/1已审批")
    private Integer approveStatus;

    @ApiModelProperty("审批开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date approveStartTime;

    @ApiModelProperty("审批结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date approveEndTime;

    /**
     * ProjectExtraEnum.SETTLEMENT_TYPE
     */
    //@ApiModelProperty("结算状态：1处理中/2已完成")
    @ApiModelProperty("结算状态：0未开始/1已提交财务/2已完成")
    private Integer settlementStatus;

    @ApiModelProperty("销售开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date salesStartTime;

    @ApiModelProperty("销售结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date salesEndTime;
}

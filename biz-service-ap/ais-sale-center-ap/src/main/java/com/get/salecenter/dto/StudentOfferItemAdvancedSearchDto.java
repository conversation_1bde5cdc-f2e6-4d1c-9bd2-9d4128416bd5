package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 *申请计划汇总高级搜索
 */
@Data
public class StudentOfferItemAdvancedSearchDto {

    @ApiModelProperty(value = "查询方式：精准查询/模糊查询")
    private String searchMethod;

    @ApiModelProperty(value = "查询属性：学生姓名/student_id")
    private String attribute;

    @ApiModelProperty(value = "属性值")
    private String attributeValue;

    @ApiModelProperty(value = "多个属性值")
    private List<String> attributeValues;
}

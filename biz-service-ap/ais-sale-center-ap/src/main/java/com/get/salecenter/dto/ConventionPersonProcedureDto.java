package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @author: Sea
 * @create: 2020/7/15 15:03
 * @verison: 1.0
 * @description:
 */
@Data
public class ConventionPersonProcedureDto extends BaseVoEntity{
    /**
     * 参会人员姓名关键字
     */
    String nameKey;
    /**
     * 参会人员公司关键字
     */
    String companyKey;

    //自定义内容
    /**
     * 参会人员类型
     */
    Integer type;
    /**
     * bd名称关键字
     */
    String bdNameKey;
    /**
     * 峰会参展人员Id
     */
    @NotNull(message = "峰会参展人员Id不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "峰会参展人员Id", required = true)
    private Long fkConventionPersonId;
    /**
     * 峰会流程Id
     */
    @NotNull(message = "峰会流程Id不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "峰会流程Id", required = true)
    private Long fkConventionProcedureId;
    /**
     * 是否参加
     */
    @ApiModelProperty(value = "是否参加")
    private boolean join;
}

package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @DATE: 2020/11/2
 * @TIME: 14:24
 * @Description:
 **/
@Data
public class ReceivableReasonDto  extends BaseVoEntity {
    /**
     * 原因名称
     */
    @ApiModelProperty(value = "原因名称")
    @NotBlank(message = "原因名称不能为空", groups = {Add.class, Update.class})
    private String reasonName;

    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

  
}

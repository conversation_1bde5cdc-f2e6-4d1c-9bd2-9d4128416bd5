package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("m_convention_exam_prizes")
public class ConventionExamPrizes extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("峰会Id")
    private Long fkConventionId;

    @ApiModelProperty("考试Id")
    private Long fkExaminationId;

    @ApiModelProperty("奖品类型：100分奖品=1/70分奖品=2")
    private Integer prizeType;

    @ApiModelProperty("奖品序号")
    private Integer prizeIndex;

    @ApiModelProperty("奖品名称")
    private String prizeName;

    @ApiModelProperty("注册中心的user id")
    private Long fkUserId;

    @ApiModelProperty("峰会参展人员Id（获奖人员）")
    private Long fkConventionPersonId;

    @ApiModelProperty("是否已经兑换：0否/1是")
    private Boolean isExchange;

}

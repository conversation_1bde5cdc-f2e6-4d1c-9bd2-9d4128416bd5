package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 合同审批实体类
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Data
@TableName("m_agent_contract_approval")
@EqualsAndHashCode(callSuper = true)
public class AgentContractApprovalEntity extends BaseEntity implements Serializable {


    /**
     * <p>
     * Java类型:Long &nbsp;&nbsp;&nbsp;&nbsp;
     * <p>
     * 数据库类型:bigint(19) &nbsp;&nbsp;&nbsp;&nbsp;
     * <p>
     * 注释:合同审批Id &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @ApiModelProperty("合同审批Id")
    @JsonProperty("id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * <p>
     * Java类型:Long &nbsp;&nbsp;&nbsp;&nbsp;
     * <p>
     * 数据库类型:bigint(19) &nbsp;&nbsp;&nbsp;&nbsp;
     * <p>
     * 注释:学生代理合同Id &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @ApiModelProperty("学生代理合同Id")
    @JsonProperty("fkAgentContractId")
    @TableField("fk_agent_contract_id")
    private Long fkAgentContractId;

    /**
     * <p>
     * Java类型:Long &nbsp;&nbsp;&nbsp;&nbsp;
     * <p>
     * 数据库类型:bigint(19) &nbsp;&nbsp;&nbsp;&nbsp;
     * <p>
     * 注释:审批人Id &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @ApiModelProperty("审批人Id")
    @JsonProperty("fkStaffId")
    @TableField("fk_staff_id")
    private Long fkStaffId;

    /**
     * <p>
     * Java类型:String &nbsp;&nbsp;&nbsp;&nbsp;
     * <p>
     * 数据库类型:varchar(2000) &nbsp;&nbsp;&nbsp;&nbsp;
     * <p>
     * 注释:审批意见 &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @ApiModelProperty("审批意见")
    @JsonProperty("approvalComment")
    @TableField("approval_comment")
    private String approvalComment;

    /**
     * <p>
     * Java类型:Integer &nbsp;&nbsp;&nbsp;&nbsp;
     * <p>
     * 数据库类型:int(10) &nbsp;&nbsp;&nbsp;&nbsp;
     * <p>
     * 注释:审批状态：4审核通过/-4审核驳回 &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @ApiModelProperty("审批状态：4审核通过/-4审核驳回")
    @JsonProperty("approvalStatus")
    @TableField("approval_status")
    private Integer approvalStatus;

    /**
     * <p>
     * Java类型:Date &nbsp;&nbsp;&nbsp;&nbsp;
     * <p>
     * 数据库类型:datetime &nbsp;&nbsp;&nbsp;&nbsp;
     * <p>
     * 注释:审批时间 &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @ApiModelProperty("审批时间")
    @JsonProperty("approvalTime")
    @TableField("approval_time")
    private Date approvalTime;

    /**
     * <p>
     * Java类型:Date &nbsp;&nbsp;&nbsp;&nbsp;
     * <p>
     * 数据库类型:datetime &nbsp;&nbsp;&nbsp;&nbsp;
     * <p>
     * 注释:创建时间 &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @ApiModelProperty("创建时间")
    @JsonProperty("gmtCreate")
    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * <p>
     * Java类型:String &nbsp;&nbsp;&nbsp;&nbsp;
     * <p>
     * 数据库类型:varchar(50) &nbsp;&nbsp;&nbsp;&nbsp;
     * <p>
     * 注释:创建用户(登录账号) &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @ApiModelProperty("创建用户(登录账号)")
    @JsonProperty("gmtCreateUser")
    @TableField("gmt_create_user")
    private String gmtCreateUser;

    /**
     * <p>
     * Java类型:Date &nbsp;&nbsp;&nbsp;&nbsp;
     * <p>
     * 数据库类型:datetime &nbsp;&nbsp;&nbsp;&nbsp;
     * <p>
     * 注释:修改时间 &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @ApiModelProperty("修改时间")
    @JsonProperty("gmtModified")
    @TableField("gmt_modified")
    private Date gmtModified;

    /**
     * <p>
     * Java类型:String &nbsp;&nbsp;&nbsp;&nbsp;
     * <p>
     * 数据库类型:varchar(50) &nbsp;&nbsp;&nbsp;&nbsp;
     * <p>
     * 注释:修改用户(登录账号) &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @ApiModelProperty("修改用户(登录账号)")
    @JsonProperty("gmtModifiedUser")
    @TableField("gmt_modified_user")
    private String gmtModifiedUser;

}
package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2022/1/13
 * @TIME: 12:37
 * @Description:
 **/
@Data
public class InsuranceSummaryDto extends BaseVoEntity {
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;


    @ApiModelProperty(value = "公司Ids")
    private List<Long> fkCompanyIds;

    /**
     * 业务渠道Id
     */
    @ApiModelProperty(value = "业务渠道Id")
    private Long fkBusinessChannelId;

    /**
     * 学生名称编号
     */
    @ApiModelProperty(value = "学生名称编号")
    private String studentName;

    /**
     * 代理名称
     */
    @ApiModelProperty(value = "代理名称")
    private String agentName;

    /**
     * bd名称
     */
    @ApiModelProperty(value = "bd名称")
    private String staffName;

    /**
     * 项目成员名称
     */
    @ApiModelProperty(value = "项目成员名称")
    private String memberName;

    /**
     * 保险单号
     */
    @ApiModelProperty(value = "保险单号")
    private String insuranceNum;


    /**
     * 状态：0关闭/1打开
     */
    @ApiModelProperty(value = "状态：0关闭/1打开")
    private Integer status;


    /**
     * 保单开始时间
     */
    @ApiModelProperty(value = "保单开始时间-开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date insuranceStartTimeStart;
    /**
     * 保单开始时间
     */
    @ApiModelProperty(value = "保单开始时间-结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date insuranceStartTimeEnd;
    /**
     * 保单结束时间
     */
    @ApiModelProperty(value = "保单结束时间-开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date insuranceEndTimeStart;
    /**
     * 保单结束时间
     */
    @ApiModelProperty(value = "保单结束时间-结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date insuranceEndTimeEnd;

    /**
     * 保险购买时间
     */
    @ApiModelProperty(value = "保险购买时间-开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date insuranceBuyTimeStart;

    /**
     * 保险购买时间
     */
    @ApiModelProperty(value = "保险购买时间-结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date insuranceBuyTimeEnd;


    /**
     * 保险批量生成应收应付数组
     */
    @ApiModelProperty(value = "保险批量生成应收应付数组")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private List<Long> insuranceIds;

    /**
     * 佣金币种
     */
    @ApiModelProperty(value = "佣金币种")
    private String fkCurrencyTypeNumCommission;

    /**
     * 收取佣金比例%
     */
    @ApiModelProperty(value = "收取佣金比例%")
    private BigDecimal commissionRateReceivable;

    /**
     * 支付佣金比例%（代理）
     */
    @ApiModelProperty(value = "支付佣金比例%（代理）")
    private BigDecimal commissionRatePayable;

    /**
     * 固定收取佣金金额
     */
    @ApiModelProperty(value = "固定收取佣金金额")
    private BigDecimal fixedAmountReceivable;

    /**
     * 固定支付佣金金额（代理）
     */
    @ApiModelProperty(value = "固定支付佣金金额（代理）")
    private BigDecimal fixedAmountPayable;

    @ApiModelProperty(value = "保险创建时间范围起")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date gmtCreateBeginTime;

    @ApiModelProperty(value = "保险创建时间范围末")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date gmtCreateEndTime;

    private List<Long> staffFollowerIds;

    /**
     * 备注
     */
    @ApiModelProperty(value = "是否财务专用：0否/1是")
    @Column(name = "is_hidden")
    private Boolean isHidden;

    /**
     * 应收状态
     */
    @ApiModelProperty(value = "应收状态：0/未收 1/部分已收 2/已收齐")
    private Integer arStatus;

    /**
     * 应付状态
     */
    @ApiModelProperty(value = "应付状态：0/未付 1/部分已付 2/已付清")
    private Integer apStatus;

    /**
     * 服务提供商/产品
     */
    @ApiModelProperty(value = "服务提供商/产品")
    private String businessProviderAndProductName;


  
}

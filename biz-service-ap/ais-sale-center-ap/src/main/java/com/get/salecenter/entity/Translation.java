package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.ibatis.type.Alias;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-04
 */
@Data
@Alias("SaleTranslation")
@TableName("s_translation")
@ApiModel(value="Translation对象", description="")
public class Translation extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "表名")
    private String fkTableName;

    @ApiModelProperty(value = "表Id")
    private Long fkTableId;

    @ApiModelProperty(value = "翻译配置Id")
    private Long fkTranslationMappingId;

    @ApiModelProperty(value = "语言枚举code：zh-hk/en-us")
    private String languageCode;

    @ApiModelProperty(value = "翻译内容")
    private String translation;

}

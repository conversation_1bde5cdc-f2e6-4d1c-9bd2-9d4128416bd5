package com.get.salecenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @author: Sea
 * @create: 2021/5/8 15:07
 * @verison: 1.0
 * @description:
 */
@Data
public class ConventionSponsorSponsorFeeDto  extends BaseVoEntity{
    /**
     * 峰会赞助商Id
     */
    @ApiModelProperty(value = "峰会赞助商Id")
    private Long fkConventionSponsorId;

    /**
     * 峰会赞助费用类型Id
     */
    @ApiModelProperty(value = "峰会赞助费用类型Id")
    private Long fkConventionSponsorFeeId;

    @ApiModelProperty(value = "备注")
    private String remark;
   
}

package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.common.annotion.UnableUpdateField;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @DATE: 2022/3/17
 * @TIME: 12:43
 * @Description:
 **/
@Data
@TableName("agency_company_has_agency_users")
public class AgencyCompanyHasAgencyUsers extends BaseEntity implements Serializable {

    @ApiModelProperty("")
    @Column(name = "agency_company_id")
    private Long agencyCompanyId;

    @ApiModelProperty("")
    @Column(name = "agency_users_id")
    private Long agencyUsersId;

    @ApiModelProperty("代理人职位")
    @Column(name = "agency_users_position")
    private Long agencyUsersPosition;

    @ApiModelProperty("CPP 中介代理的id")
    @Column(name = "cpp_agency_id")
    private Long cppAgencyId;

    @ApiModelProperty("")
    @Column(name = "bms_agency_id")
    private Long bmsAgencyId;

    @ApiModelProperty("角色  0=负责人  1=员工")
    @Column(name = "user_role")
    private int userRole;

    @ApiModelProperty("创建时间")
    @UnableUpdateField
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "createtime")
    private Date createtime;

    @ApiModelProperty("修改时间")
    @UnableUpdateField
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "updatetime")
    private Date updatetime;


}

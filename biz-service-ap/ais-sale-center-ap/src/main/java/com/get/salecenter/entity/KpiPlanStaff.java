package com.get.salecenter.entity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import java.io.Serializable;
/**
 * <AUTHOR>
 * @DATE: 2024/4/16
 * @TIME: 12:20
 * @Description:KPI方案考核人员
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("m_kpi_plan_staff")
@ApiModel(value="KPI方案考核人员", description="")
public class KpiPlanStaff  extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "KPI方案Id")
    @Column(name = "fk_kpi_plan_id")
    private Long fkKpiPlanId;

    @ApiModelProperty(value = "目标设置人Id")
    @Column(name = "fk_staff_id_add")
    private Long fkStaffIdAdd;

    @ApiModelProperty(value = "统计角色，枚举：BD=1/项目成员=2/PM=3")
    @Column(name = "count_role")
    private Integer countRole;

    @ApiModelProperty(value = "统计方式，枚举：个人=1/团队(含业务下属)=2")
    @Column(name = "count_mode")
    private Integer countMode;

    @ApiModelProperty(value = "员工Id")
    @Column(name = "fk_staff_id")
    private Long fkStaffId;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;
}

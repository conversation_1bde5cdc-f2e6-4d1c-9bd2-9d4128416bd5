package com.get.salecenter.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * @author: Hardy
 * @create: 2022/11/17 17:06
 * @verison: 1.0
 * @description:
 */
@Data
public class AppAgentContactPersonAddDto extends BaseVoEntity{

    /**
     * 学生代理申请Id
     */
    @NotNull(message = "学生代理申请Id", groups = {Save.class})
    @ApiModelProperty(value = "学生代理申请Id")
    private Long fkAppAgentId;

    /**
     * 联系人类型Key，多值逗号隔开
     */
    @NotBlank(message = "联系人类型Key", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "联系人类型Key，多值逗号隔开")
    private String fkContactPersonTypeKey;

    /**
     * 名称
     */
    @NotBlank(message = "名称", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 性别：0女/1男
     */
    @NotNull(message = "性别", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "性别：0女/1男")
    private Integer gender;

    /**
     * 部门
     */
    @NotBlank(message = "部门", groups = {Add.class, Update.class})
    private String department;

    /**
     * 职位
     */
    @NotBlank(message = "职位", groups = {Add.class, Update.class})
    private String title;

    @NotBlank(message = "手机区号", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "手机区号")
    private String mobileAreaCode;

    /**
     * 移动电话
     */
    @NotBlank(message = "移动电话", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "移动电话")
    private String mobile;

    /**
     * Email
     */
    @NotBlank(message = "Email", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "Email")
    private String email;

    /**
     * 是否佣金邮件地址
     */
    @ApiModelProperty(value = "是否佣金邮件地址")
    @Column(name = "is_commission_email")
    private Boolean isCommissionEmail;

    /**
     * 是否新闻邮件地址
     */
    @ApiModelProperty(value = "是否新闻邮件地址")
    @Column(name = "is_news_email")
    private Boolean isNewsEmail;

    /**
     * 新闻邮件关系国家Ids串，英文逗号隔开：1,2,3
     */
    @ApiModelProperty(value = "新闻邮件关系国家Ids串，英文逗号隔开：1,2,3")
    @Column(name = "fk_area_country_ids_news")
    private String fkAreaCountryIdsNews;

    /**
     * <p> Java类型:Long &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 数据库类型:bigint(19) &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释:转化到联系人Id &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @ApiModelProperty(value = "转化到联系人Id")
    @JsonProperty("fkContactPersonId")
    @TableField("fk_contact_person_id")
    private Long fkContactPersonId;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    public interface Save {

    }
}

package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @author: Hardy
 * @create: 2023/3/7 12:28
 * @verison: 1.0
 * @description:
 */
@Data
public class StaffSettlementStatisticsDto {

    @ApiModelProperty("公司id")
    private Long fkCompanyId;

    @ApiModelProperty("员工id")
    private Long fkStaffId;

    @ApiModelProperty("结算日期")
    @JsonFormat(pattern = "yyyy-MM",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM")
    private Date settlementDate;
}

package com.get.salecenter.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 代理申请类型 枚举
 *
 * <AUTHOR>
 * @Date 2025/6/27 下午12:09
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum AgentAppTypeEnum {

    NEW_APPLICATION(1, "新申请"),

    RENEWAL_APPLICATION(2, "续约申请")
    ;

    private final Integer code;

    private final String msg;

    private static final Map<Integer, AgentAppTypeEnum> AGENT_APP_TYPE_ENUM_MAP = new HashMap<>();

    static {
        for (AgentAppTypeEnum agentAppTypeEnum : AgentAppTypeEnum.values()) {
            AGENT_APP_TYPE_ENUM_MAP.put(agentAppTypeEnum.getCode(), agentAppTypeEnum);
        }
    }

    public static AgentAppTypeEnum getAgentAppTypeEnum(Integer code) {
        return AGENT_APP_TYPE_ENUM_MAP.get(code);
    }

}

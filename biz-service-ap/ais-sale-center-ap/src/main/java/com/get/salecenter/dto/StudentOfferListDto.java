package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseEntity;
import com.get.core.mybatis.base.BaseVoEntity;
import com.get.salecenter.vo.StudentRoleAndStaffVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;

@Data
public class StudentOfferListDto extends BaseEntity implements Serializable {


    @NotNull(message = "页码不能为空")
    @ApiModelProperty(value = "页码")
    private Integer pageNumber;

    @ApiModelProperty(value = "偏移量")
    private Integer offset;

    @NotNull(message = "显示条数不能为空")
    @ApiModelProperty(value = "页面大小")
    private Integer pageSize;

    /**
     * 学生Id
     */
    @NotNull(message = "学生Id不能为空", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    @ApiModelProperty(value = "学生Id", required = true)
    private Long fkStudentId;

    /**
     * 代理Id（业绩绑定）
     */
    @NotNull(message = "代理Id不能为空", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    @ApiModelProperty(value = "代理Id（业绩绑定）", required = true)
    private Long fkAgentId;

    /**
     * 员工Id（业绩绑定，BD）
     */
    @NotNull(message = "员工Id不能为空", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    @ApiModelProperty(value = "员工Id（业绩绑定，BD）", required = true)
    private Long fkStaffId;

    /**
     * 国家Id
     */
    @ApiModelProperty(value = "国家Id")
    @NotNull(message = "国家Id不能为空", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    private Long fkAreaCountryId;

    /**
     * 申请国家ID列表
     */
    @ApiModelProperty(value = "申请国家ID列表")
    private List<Long> targetCountryIdList;

    /**
     * 申请方案编号
     */
    @ApiModelProperty(value = "申请方案编号")
    private String num;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @NotNull(message = "备注不能为空", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    private String remark;

    /**
     * 状态：0关闭/1打开
     */
    @ApiModelProperty(value = "状态：0关闭/1打开")
    private Integer status;


    @ApiModelProperty(value = "角色员工id")
    @NotNull(message = "角色员工不能为空", groups = {BaseVoEntity.Add.class})
    private List<ProjectRoleStaffDto> roleStaffVo;

    /**
     * 1查询全部，0查询个人,2 查询我的审批
     */
    @ApiModelProperty(value = "1查询全部，0查询个人,2 查询我的审批")
    private String selectStatus;

    @ApiModelProperty(value = "角色Id")
    private Set<Long> fkRoleIds;

    /**
     * 发起人id
     */
    @ApiModelProperty(value = "发起人id")
    private Long fkStaffIdWorkflow;

    /**
     * 公司id
     */
    @ApiModelProperty(value = "公司id")
    private Long fkCompanyId;

    /**
     * 公司ids
     */
    @ApiModelProperty(value = "公司ids")
    private List<Long> fkCompanyIds;

    /**
     * 状态：0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废/6撤销
     */
    @ApiModelProperty(value = "状态：0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废/6撤销")
    @Column(name = "status_workflow")
    private Integer statusWorkflow;

    /**
     * 旧数据id(gea)
     */
    @ApiModelProperty(value = "旧数据id(gea)")
    @Column(name = "id_gea")
    private String idGea;

    /**
     * 旧数据id(iae)
     */
    @ApiModelProperty(value = "旧数据id(iae)")
    @Column(name = "id_iae")
    private String idIae;

    /**
     * 发起人ids
     */
    @ApiModelProperty(value = "发起人ids")
    private String fkStaffIdWorkflowIds;

    /**
     * 学生ids
     */
    @ApiModelProperty(value = "学生ids")
    private String studentIds;

    /**
     * 可查看所有学生的ids
     */
    @ApiModelProperty(value = "可查看所有学生的ids")
    private String allStudentIds;

    /**
     * ids
     */
    @ApiModelProperty(value = "ids")
    private String ids;

    /**
     * 课程名称
     */
    @ApiModelProperty(value = "课程名称")
    private String courseFullName;

    /**
     * 学校名称
     */
    @ApiModelProperty(value = "学校名称")
    private String institutionFullName;

    /**
     * 课程id
     */
    @ApiModelProperty(value = "课程id")
    private Long fkInstitutionCourseId;

    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    private Long fkInstitutionId;

    /**
     * 学生名称
     */
    @ApiModelProperty(value = "学生名称")
    private String studentName;

    /**
     * 状态(多选)
     */
    @ApiModelProperty(value = "状态(多选)")
    private List<Integer> statusList;
    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    private String email;

    /**
     * 开学时间start
     */
    @ApiModelProperty(value = "开学时间start")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date openingTimeStart;

    /**
     * 开学时间End
     */
    @ApiModelProperty(value = "开学时间End")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date openingTimeEnd;

    /**
     * 开始创建时间
     */
    @ApiModelProperty(value = "开始创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createBeginDate;

    /**
     * 结束创建时间
     */
    @ApiModelProperty(value = "结束创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createEndDate;


    /**
     * itemCreateBeginDate
     */
    @ApiModelProperty(value = "申请计划开始创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date itemCreateBeginDate;

    /**
     * 申请计划结束创建时间
     */
    @ApiModelProperty(value = "申请计划结束创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date itemCreateEndDate;

    /**
     * 步骤状态变更时间begin
     */
    @ApiModelProperty(value = "步骤状态变更时间begin")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date stepChangeBeginDate;

    /**
     * 步骤状态变更时间end
     */
    @ApiModelProperty(value = "步骤状态变更时间end")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date stepChangeEndDate;

    /**
     * 学生查询条件
     */
    @ApiModelProperty(value = "学生查询条件")
    private StudentDto studentVo;

    /**
     * 学生国籍所在国家Id
     */
    @ApiModelProperty(value = "学生国籍所在国家Id")
    private Long fkAreaCountryIdNationality;


    @ApiModelProperty(value = "角色员工")
    private List<StudentRoleAndStaffVo> studentRoleAndStaffList;

    @ApiModelProperty("步骤状态")
    private List<Long> stepOrderList;

    @ApiModelProperty("是否延迟入学")
    private Boolean isDeferEntrance;

    @ApiModelProperty("失败原因id")
    private Long failureReasonId;

    @ApiModelProperty("学生编号")
    private String studentNum;

    @ApiModelProperty("国籍")
    private Long studentNationalityCountryId;

    @ApiModelProperty("学生电话")
    private String tel;

    @ApiModelProperty("代理名称")
    private String agentName;

    @ApiModelProperty("BD名称")
    private String bdName;

    @ApiModelProperty("bd编号")
    private String bdCode;

    @ApiModelProperty("课程名称")
    private String courseName;

    @ApiModelProperty("课程ids")
    private List<Long> courseIds;

    @ApiModelProperty(value = "是否全局匹配课程名称 枚举：0百度式搜索/1全局匹配")
    private Boolean isCourseGlobalMatching = false;

    @ApiModelProperty(value = "课程组别ids")
    private List<Long> courseTypeGroupIds;

    @ApiModelProperty("旧课程类型组别名称（多选）")
    private List<String> oldCourseTypeGroupNames;

    @ApiModelProperty("学校名称")
    private String schoolName;

    @ApiModelProperty("项目成员名称")
    private String projectRoleStaffName;

    @ApiModelProperty("学校ids")
    private List<Long> institutionIds;

    @ApiModelProperty("项目成员ids-查询")
    private List<Long> fkProjectRoleIds;

    /**
     * 新加---------------------
     */
    @ApiModelProperty("课程等级")
    private Long majorLevelId;

    @ApiModelProperty("渠道")
    private String channelName;

    @ApiModelProperty("集团名称")
    private String groupName;

    @ApiModelProperty("变更的步骤id")
    private List<Long> changeStepId;

    @ApiModelProperty("变更的步骤id")
    private String oldCourseMajorLevelName;

    @ApiModelProperty("批量分配申请方案项目成员条件")
    private StudentProjectRoleStaffDto studentProjectRoleStaff;

    /**
     * 代理国家
     */
    @ApiModelProperty(value = "代理国家")
    private Long fkAreaCountryIdAgent;

    /**
     * 代理省份ID列表
     */
    @ApiModelProperty(value = "代理省份ID列表")
    private List<Long> fkAreaStateIdAgentList;

    @ApiModelProperty(value = "课程等级ids（多选）")
    private List<Long> majorLevelIds;

    @ApiModelProperty(value = "旧课程等级名称（多选）")
    private List<String> oldCourseMajorLevelNames;

    /**
     * 集团名称（多选）
     */
    @ApiModelProperty(value = "集团名称（多选）")
    private List<String> institutionGroupNames;

    /**
     * 所属集团Id
     */
    @ApiModelProperty(value = "所属集团Id")
    private List<Long> fkInstitutionGroupIds;


    /**
     * 入学失败原因Id
     */
    @ApiModelProperty(value = "入学失败原因Ids")
    private List<Long> fkEnrolFailureReasonIds;

    @ApiModelProperty(value = "渠道名称（多选）")
    private List<String> channelNames;

    /**
     * 代理ids
     */
    @ApiModelProperty(value = "代理ids")
    private List<Long> fkAgentIds;

    @ApiModelProperty(value = "BD的ids")
    private List<Long> fkBdIds;

    @ApiModelProperty(value = "学校提供商")
    private Long fkInstitutionProviderId;
    /**
     * 大区
     */
    @ApiModelProperty(value = "大区")
    private Long fkAreaRegionId;

    @ApiModelProperty(value = "新申请状态：枚举：0缺资料/1未开放")
    private Integer newAppStatus;

    @ApiModelProperty(value = "是否批量修改项目成员")
    private Boolean isProjectUpate;

   
}

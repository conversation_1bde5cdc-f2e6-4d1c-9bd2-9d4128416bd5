package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("u_student_project_role")
public class StudentProjectRole extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;
    /**
     * 角色名称
     */
    @ApiModelProperty(value = "角色名称")
    @Column(name = "role_name")
    private String roleName;
    /**
     * 角色Key(系统使用)
     */
    @ApiModelProperty(value = "角色Key(系统使用)")
    @Column(name = "role_key")
    private String roleKey;
    /**
     * 部门编号引用，可以多选，用逗号隔开，如：D001,D002
     */
    @ApiModelProperty(value = "部门编号引用，可以多选，用逗号隔开，如：D001,D002")
    @Column(name = "department_num")
    private String departmentNum;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;
    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;
}
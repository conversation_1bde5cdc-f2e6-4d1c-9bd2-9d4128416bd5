package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @author: Hardy
 * @create: 2022/9/13 16:35
 * @verison: 1.0
 * @description:
 */
@Data
public class EventRegistrationDto extends BaseVoEntity {

    /**
     * 活动Id
     */
    @NotNull(message = "活动Id不能为空",groups = {Add.class, Update.class})
    @ApiModelProperty(value = "活动Id")
    private Long fkEventId;

    /**
     * 学校提供商Id
     */
    @NotNull(message = "学校提供商Id不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "学校提供商Id")
    private Long fkInstitutionProviderId;

    @ApiModelProperty(value = "枚举状态：0不参加/1参加/2待定")
    private Integer status;

    @ApiModelProperty(value = "备注")
    private String remark;

   

}

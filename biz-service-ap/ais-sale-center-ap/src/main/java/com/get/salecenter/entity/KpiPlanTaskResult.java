package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;

/**
 * KPI方案定时任务结果表
 */
@TableName("m_kpi_plan_task_result")
@ApiModel(value = "KPI方案定时任务结果表", description = "")
@Data
public class KpiPlanTaskResult extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "KPI方案Id")
    @Column(name = "fk_kpi_plan_id")
    private Long fkKpiPlanId;

    @ApiModelProperty(value = "根节点员工Id")
    @Column(name = "fk_staff_id_root")
    private Long fkStaffIdRoot;

    @ApiModelProperty(value = "定时任务开始时间")
    @Column(name = "task_start_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date taskStartTime;

    @ApiModelProperty(value = "定时任务结束时间")
    @Column(name = "task_end_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date taskEndTime;

    @ApiModelProperty(value = "KPI统计结果")
    @Column(name = "kpi_statistical_result")
    private String kpiStatisticalResult;

}

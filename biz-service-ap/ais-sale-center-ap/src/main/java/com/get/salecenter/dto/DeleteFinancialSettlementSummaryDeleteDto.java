package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 财务结算汇总删除佣金结算(第四步删除佣金结算按钮)Vo
 *
 * <AUTHOR>
 * @date 2021/12/29 12:10
 */
@Data
public class DeleteFinancialSettlementSummaryDeleteDto {

    @NotNull(message = "代理id不能为空")
    @ApiModelProperty(value = "代理id")
    private Long agentId;

    @NotBlank(message = "支付币种不能为空")
    @ApiModelProperty(value = "支付币种")
    private String planCurrencyNum;

    @NotBlank(message = "结算币种不能为空")
    @ApiModelProperty(value = "结算币种")
    private String accountCurrencyNum;

    @NotBlank(message = "业务类型Key不能为空")
    @ApiModelProperty(value = "业务类型Key")
    private String fkTypeKey;

    @NotBlank(message = "结算ids不能为空")
    @ApiModelProperty(value = "结算ids")
    private String settlementIds;

}

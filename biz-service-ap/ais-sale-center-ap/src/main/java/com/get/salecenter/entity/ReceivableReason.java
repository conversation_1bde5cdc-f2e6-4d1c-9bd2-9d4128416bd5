package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("u_receivable_reason")
public class ReceivableReason extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 原因名称
     */
    @ApiModelProperty(value = "原因名称")
    @Column(name = "reason_name")
    private String reasonName;
    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;
}
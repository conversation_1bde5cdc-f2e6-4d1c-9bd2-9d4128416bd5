package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName(value = "m_business_provider_account")
public class BusinessProviderAccount extends BaseEntity {
    @ApiModelProperty("业务提供商Id")
    private Long fkBusinessProviderId;

    @ApiModelProperty("币种编号")
    private String fkCurrencyTypeNum;

    @ApiModelProperty("账户卡类型")
    private Integer accountCardType;

    @ApiModelProperty("账户名称")
    private String bankAccount;

    @ApiModelProperty("账户账号")
    private String bankAccountNum;

    @ApiModelProperty("银行名称")
    private String bankName;

    @ApiModelProperty("支行名称")
    private String bankBranchName;

    @ApiModelProperty("国家id")
    private Long fkAreaCountryId;

    @ApiModelProperty("州省id")
    private Long fkAreaStateId;

    @ApiModelProperty("城市id")
    private Long fkAreaCityId;

    @ApiModelProperty("城市区域id")
    private Long fkAreaCityDivisionId;

    @ApiModelProperty("银行地址")
    private String bankAddress;

    @ApiModelProperty("银行编号类型：SwiftCode/BSB")
    private String bankCodeType;

    @ApiModelProperty("银行编号")
    private String bankCode;

    @ApiModelProperty("国家编码")
    private String areaCountryCode;

    @ApiModelProperty("是否默认首选：0否/1是")
    private Boolean isDefault;

    @ApiModelProperty("是否激活：0否/1是")
    private Boolean isActive;

    @ApiModelProperty("备注")
    private String remark;
}

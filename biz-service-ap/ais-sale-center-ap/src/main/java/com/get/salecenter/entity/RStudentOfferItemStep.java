package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;

@Data
@TableName("r_student_offer_item_step")
public class RStudentOfferItemStep extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 学生申请方案项目Id
     */
    @ApiModelProperty(value = "学生申请方案项目Id")
    @Column(name = "fk_student_offer_item_id")
    private Long fkStudentOfferItemId;
    /**
     * 学生申请方案项目状态步骤Id
     */
    @ApiModelProperty(value = "学生申请方案项目状态步骤Id")
    @Column(name = "fk_student_offer_item_step_id")
    private Long fkStudentOfferItemStepId;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;

    //reassign_time
    @ApiModelProperty(value = " reassign_time")
    @Column(name = "reassign_time")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date reassignTime;

}
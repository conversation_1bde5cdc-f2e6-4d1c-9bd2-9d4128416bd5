package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 代付费用日志VO（提供给app端）
 */
@Data
public class
StudentOfferItemPaymentStatusToAppDto {

    @ApiModelProperty(value = "表名：m_student_offer_item")
    @NotBlank(message = "表名不能为空")
    private String fkTableName;

    @ApiModelProperty(value = "表id：m_student_offer_item.id")
    @NotNull(message = "表id不能为空")
    private Long fkTableId;

    @ApiModelProperty(value = "支付类型：1.申请费 2.全额学费 3.学费首付 Deposit 4.学费尾款 Remaining")
    @NotNull(message = "支付类型不能为空")
    private Integer paymentType;

    @ApiModelProperty(value = "支付币种")
    @NotBlank(message = "支付币种不能为空")
    private String fkCurrencyTypeNum;

    @ApiModelProperty(value = "支付金额")
    @NotNull(message = "支付金额不能为空")
    private BigDecimal paidAmount;

    @ApiModelProperty(value = "支付状态：0.付费失败 1.已发送代付邮件 2.已付费(支付渠道) 3.已付费(学校)")
    @NotNull(message = "支付状态不能为空")
    private Integer paymentStatus;

    /**
     * H5的url/小程序地址支付二维码的url/小程序的scheme码
     */
    @ApiModelProperty(value = "H5的url/小程序地址支付二维码的url/小程序的scheme码")
    @NotBlank(message = "url不能为空")
    private String payUrl;

    /**
     * 易思汇支付日志id，如果是手动新增状态，则为空
     */
    @ApiModelProperty(value = "易思汇支付日志id，如果是手动新增状态，则为空")
    @NotNull(message = "易思汇支付日志id不能为空")
    private Long fkEasyTransferPaymentLogId;

    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 用于服务通信的签名
     */
    @ApiModelProperty(value = "时间戳")
    @NotNull(message = "时间戳不能为空")
    private Long appTimeMillis;

    @ApiModelProperty(value = "签名")
    @NotBlank(message = "签名不能为空")
    private String sign;

}

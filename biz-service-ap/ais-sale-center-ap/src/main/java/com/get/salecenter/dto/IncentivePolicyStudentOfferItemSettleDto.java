package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-03-13
 */
@Data
public class IncentivePolicyStudentOfferItemSettleDto {

    @ApiModelProperty(value = "Incentive奖励政策Id")
    private Long fkIncentivePolicyId;

    //結算更改為批量一次性結算，不再選擇性結算
//    @ApiModelProperty(value = "学生申请方案项目Id")
//    private List<Long> fkStudentOfferItemIds;
}

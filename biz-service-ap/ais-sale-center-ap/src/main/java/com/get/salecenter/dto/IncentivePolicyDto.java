package com.get.salecenter.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-03-13
 */
@Data
public class IncentivePolicyDto extends BaseVoEntity {

    @NotNull(message = "公司Id", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "公司Id", required = true)
    private Long fkCompanyId;

    @ApiModelProperty(value = "国家Id")
    @UpdateWithNull
    private Long fkAreaCountryId;

    @ApiModelProperty(value = "学校提供商Id")
    @UpdateWithNull
    private Long fkInstitutionProviderId;

    @ApiModelProperty(value = "学校Ids，逗号隔开：1,2,3")
    private String fkInstitutionIds;

    @ApiModelProperty(value = "奖励政策编号，编号规则：系统生成，ICP+6位ID数字，例：ICP000001")
    private String num;

    @NotNull(message = "公司Id", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "年度")
    private Integer year;

    @ApiModelProperty(value = "活动时间说明")
    private String activityTimeNote;

    @ApiModelProperty(value = "提交申请时间说明")
    private String submittedTimeNote;

    @ApiModelProperty(value = "押金缴费时间说明")
    private String depositPaidTimeNote;

    @ApiModelProperty(value = "学费缴费时间说明")
    private String tuitionTimeNote;

    @ApiModelProperty(value = "入学时间说明")
    private String enrolledTimeNote;

    @ApiModelProperty(value = "适用课程说明")
    private String conditionNote;

    @ApiModelProperty(value = "奖励内容说明")
    private String rewardNote;

    @ApiModelProperty(value = "适用课程json")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private IncentivePolicyConditionDto conditionJson;

    @ApiModelProperty(value = "奖励内容json")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private IncentivePolicyRewardDto rewardJson;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    @NotNull(message = "公司Id", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
    private String publicLevel;

    @ApiModelProperty(value = "状态：0作废/1打开")
    private Integer status;

    @ApiModelProperty(value = "附件集合：typeKey:incentive_policy_file")
    List<MediaAndAttachedDto> mediaAndAttachedVos;

   
}

package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12
 */
@Data
@TableName("r_event_plan_registration_event")
@ApiModel(value="EventPlanRegistrationEvent对象", description="")
public class EventPlanRegistrationEvent extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "活动年度计划报名名册Id")
    private Long fkEventPlanRegistrationId;

    @ApiModelProperty(value = "活动类型表名：m_event_plan_theme_online/m_event_plan_theme_offline_item/m_event_plan_theme_workshop")
    private String fkTableName;

    @ApiModelProperty(value = "活动类型表Id")
    private Long fkTableId;

    @ApiModelProperty(value = "是否取消：0否/1是")
    private Boolean isCancel;

}

package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 财务佣金汇总批次子项列表
 *
 * <AUTHOR>
 * @date 2021/12/27 11:42
 */
@Data
public class CommissionSummaryBatchDto {

    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    @ApiModelProperty(value = "支付单银行流水号 付款单编号（凭证号）")
    private String numBank;

    @ApiModelProperty(value = "业务类型")
    private String businessType;

    @ApiModelProperty(value = "财务结算汇总批次号")
    private String numSettlementBatch;

    @ApiModelProperty(value = "代理名称或编号")
    private String agentNameOrNum;

    @ApiModelProperty(value = "学生名称")
    private String studentName;

    @ApiModelProperty(value = "结算账号币种")
    private String fkCurrencyTypeNum;

    @ApiModelProperty(value = "代理州省Id")
    private Long agentAreaStateId;

    /**
     * 公司Ids
     */
    @ApiModelProperty(value = "公司Ids")
    private List<Long> fkCompanyIds;

    @ApiModelProperty(value = "true:存在未打标记 false：全部已打标记")
    private Boolean isEexchangeInputFlag;

}

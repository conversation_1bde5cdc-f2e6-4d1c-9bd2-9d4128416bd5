package com.get.salecenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @author: Sea
 * @create: 2020/11/3 11:36
 * @verison: 1.0
 * @description:
 */
@Data
public class StudentOfferItemStepDto extends BaseVoEntity{
    /**
     * 前置条件id，需要完成步骤条件（可多选），逗号分隔：1,2,3
     */
    @ApiModelProperty(value = "前置条件id，需要完成步骤条件（可多选），逗号分隔：1,2,3")
    private String studentOfferItemStepIdPrecondition;
    /**
     * 申请步骤名
     */
    @ApiModelProperty(value = "申请步骤名")
    private String stepName;

    /**
     * 申请步骤key
     */
    @ApiModelProperty(value = "申请步骤key")
    private String stepKey;

    /**
     * 申请步骤排序，由0开始按顺序排列
     */
    @ApiModelProperty(value = "申请步骤排序，由0开始按顺序排列")
    private Integer stepOrder;

    /**
     * 角色Key，多个用逗号分隔
     */
    @ApiModelProperty(value = "角色Key，多个用逗号分隔")
    private String roleKey;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

   
}

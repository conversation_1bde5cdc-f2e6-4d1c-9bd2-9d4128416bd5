package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class StudentCourseStatisticsSearchDto {

    @NotNull(message = "公司id")
    @ApiModelProperty(value = "公司id")
    private Long companyId;

    @ApiModelProperty(value = "毕业国家id")
    private Long countryId;

    @ApiModelProperty(value = "毕业国家省份id")
    private Long provinceId;

    @ApiModelProperty(value = "毕业国家学校id")
    private Long graduateSchoolId;

    @ApiModelProperty(value = "毕业国家学校名称")
    private String graduateSchoolName;

    @ApiModelProperty(value = "主修专业")
    private String majorName;

    @ApiModelProperty(value = "申请学校id")
    private Long applyCountryId;

    @ApiModelProperty(value = "申请学校id")
    private Long applySchoolId;

    @ApiModelProperty("课程id")
    private List<Long> courseId;

    @ApiModelProperty("课程名称")
    private String courseName;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date beginTime;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

}

package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;

/**
 * author:Neil
 * Time: 12:45
 * Date: 2022/8/17
 * Description:
 */
@TableName("r_client_agent")
@Data
public class ClientAgent extends BaseEntity implements Serializable {

    @ApiModelProperty(value = "客户Id")
    @Column(name = "fk_client_id")
    private Long fkClientId;

    @ApiModelProperty(value = "代理Id")
    @Column(name = "fk_agent_id")
    private Long fkAgentId;

    @ApiModelProperty(value = "是否激活：0否/1是")
    @Column(name = "is_active")
    private Boolean isActive;

    @ApiModelProperty(value = "绑定时间")
    @Column(name = "active_date")
    @UpdateWithNull
    private Date activeDate;

    @Column(name = "unactive_date")
    @ApiModelProperty(value = "取消绑定时间（下次绑定时，需要重新建立记录）")
    @UpdateWithNull
    private Date unactiveDate;
}

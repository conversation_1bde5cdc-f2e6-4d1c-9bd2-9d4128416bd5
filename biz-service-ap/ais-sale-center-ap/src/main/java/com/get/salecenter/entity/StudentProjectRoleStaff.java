package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;

@Data
@TableName("s_student_project_role_staff")
public class StudentProjectRoleStaff extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    @Column(name = "fk_table_name")
    private String fkTableName;
    /**
     * 表Id
     */
    @ApiModelProperty(value = "表Id")
    @Column(name = "fk_table_id")
    private Long fkTableId;
    /**
     * 学生项目角色Id
     */
    @ApiModelProperty(value = "学生项目角色Id")
    @Column(name = "fk_student_project_role_id")
    private Long fkStudentProjectRoleId;
    /**
     * 员工Id
     */
    @ApiModelProperty(value = "员工Id")
    @Column(name = "fk_staff_id")
    private Long fkStaffId;
    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @Column(name = "is_active")
    private Boolean isActive;
    /**
     * 绑定时间
     */
    @ApiModelProperty(value = "绑定时间")
    @Column(name = "active_date")
    private Date activeDate;
    /**
     * 取消绑定时间（下次绑定时，需要重新建立记录）
     */
    @ApiModelProperty(value = "取消绑定时间（下次绑定时，需要重新建立记录）")
    @Column(name = "unactive_date")
    private Date unactiveDate;
}
package com.get.salecenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @author: Sea
 * @create: 2021/4/29 11:15
 * @verison: 1.0
 * @description:
 */
@Data
public class AnnualConferenceRegistrationSponsorshipDto extends BaseVoEntity {
    /**
     * 年度会议注册id
     */
    @ApiModelProperty(value = "年度会议注册id")
    private Long fkAnnualConferenceRegistrationId;

    /**
     * 赞助id
     */
    @ApiModelProperty(value = "赞助id")
    private Long fkSponsorshipConfigId;
}

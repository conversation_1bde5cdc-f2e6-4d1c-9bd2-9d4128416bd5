package com.get.salecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("m_convention_award")
public class ConventionAward extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 峰会Id
     */
    @ApiModelProperty(value = "峰会Id")
    @Column(name = "fk_convention_id")
    private Long fkConventionId;
    /**
     * 奖品名称
     */
    @ApiModelProperty(value = "奖品名称")
    @Column(name = "award_name")
    private String awardName;
    /**
     * 奖品描述
     */
    @ApiModelProperty(value = "奖品描述")
    @Column(name = "award_description")
    private String awardDescription;
    /**
     * 奖品数量
     */
    @ApiModelProperty(value = "奖品数量")
    @Column(name = "award_count")
    private Integer awardCount;
    /**
     * 奖品提供机构名称
     */
    @ApiModelProperty(value = "奖品提供机构名称")
    @Column(name = "provider_name")
    private String providerName;
    /**
     * 奖品提供机构副名称
     */
    @ApiModelProperty(value = "奖品提供机构副名称")
    @Column(name = "provider_name_sub")
    private String providerNameSub;
    /**
     * 可获得角色，可多选，逗号分割：0校方/1代理/2嘉宾/3员工/4工作人员
     */
    @ApiModelProperty(value = "可获得角色，可多选，逗号分割：0校方/1代理/2嘉宾/3员工/4工作人员")
    @Column(name = "get_role")
    private String getRole;
    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;
    /**
     * 可获得用户id，多个用逗号隔开，如：1,2
     */
    @ApiModelProperty(value = "可获得用户id，多个用逗号隔开，如：1,2")
    @Column(name = "get_fk_convention_person_id")
    private String getFkConventionPersonId;
}
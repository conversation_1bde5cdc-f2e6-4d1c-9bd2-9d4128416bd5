package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.salecenter.vo.StudentEventTypeVo;
import com.get.salecenter.dto.StudentEventTypeDto;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/11/2
 * @TIME: 12:56
 * @Description:
 **/
public interface IStudentEventTypeService {
    /**
     * 详情
     *
     * @param id
     * @return
     */
    StudentEventTypeVo findStudentEventTypeById(Long id);

    /**
     * 列表数据
     *
     * @param studentEventTypeDto
     * @param page
     * @return
     */
    List<StudentEventTypeVo> getStudentEventTypes(StudentEventTypeDto studentEventTypeDto, Page page);

    /**
     * 修改
     *
     * @param studentEventTypeDto
     * @return
     */
    StudentEventTypeVo updateStudentEventType(StudentEventTypeDto studentEventTypeDto);

    /**
     * 保存
     *
     * @param studentEventTypeDto
     * @return
     */
    Long addStudentEventType(StudentEventTypeDto studentEventTypeDto);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 批量保存
     *
     * @param studentEventTypeDtos
     * @return
     */
    void batchAdd(List<StudentEventTypeDto> studentEventTypeDtos);

    /**
     * 上移下移
     *
     * @param studentEventTypeDtos
     * @return
     */
    void movingOrder(List<StudentEventTypeDto> studentEventTypeDtos);

    /**
     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 下拉
     * @Param []
     * <AUTHOR>
     */
    List<BaseSelectEntity> getStudentEventTypeSelect();
}

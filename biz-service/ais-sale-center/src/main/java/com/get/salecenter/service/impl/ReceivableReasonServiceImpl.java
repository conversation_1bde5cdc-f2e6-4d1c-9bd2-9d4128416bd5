package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.ReceivableReasonMapper;
import com.get.salecenter.vo.ReceivableReasonVo;
import com.get.salecenter.entity.ReceivableReason;
import com.get.salecenter.service.IReceivableReasonService;
import com.get.salecenter.dto.ReceivableReasonDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/11/2
 * @TIME: 14:54
 * @Description:
 **/
@Service
public class ReceivableReasonServiceImpl implements IReceivableReasonService {
    @Resource
    private ReceivableReasonMapper receivableReasonMapper;
    @Resource
    private UtilService utilService;

    @Override
    public ReceivableReasonVo findReceivableReasonById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ReceivableReason receivableReason = receivableReasonMapper.selectById(id);
        ReceivableReasonVo receivableReasonVo = BeanCopyUtils.objClone(receivableReason, ReceivableReasonVo::new);
        return receivableReasonVo;
    }

    @Override
    public List<ReceivableReasonVo> getReceivableReasons(ReceivableReasonDto receivableReasonDto, Page page) {
        LambdaQueryWrapper<ReceivableReason> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(receivableReasonDto)) {
            if (GeneralTool.isNotEmpty(receivableReasonDto.getReasonName())) {
                lambdaQueryWrapper.like(ReceivableReason::getReasonName, receivableReasonDto.getReasonName());
            }
        }
        lambdaQueryWrapper.orderByDesc(ReceivableReason::getViewOrder);
        IPage<ReceivableReason> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        IPage<ReceivableReason> pages = receivableReasonMapper.selectPage(iPage, lambdaQueryWrapper);
        //获取分页数据
        List<ReceivableReason> receivableReasons = pages.getRecords();
        page.setAll((int) pages.getTotal());

        List<ReceivableReasonVo> convertDatas = new ArrayList<>();
        for (ReceivableReason receivableReason : receivableReasons) {
            ReceivableReasonVo receivableReasonVo = BeanCopyUtils.objClone(receivableReason, ReceivableReasonVo::new);
            convertDatas.add(receivableReasonVo);
        }
        return convertDatas;
    }

    @Override
    public ReceivableReasonVo updateReceivableReason(ReceivableReasonDto receivableReasonDto) {
        if (receivableReasonDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        ReceivableReason rs = receivableReasonMapper.selectById(receivableReasonDto.getId());
        ReceivableReason receivableReason = BeanCopyUtils.objClone(receivableReasonDto, ReceivableReason::new);
        utilService.updateUserInfoToEntity(receivableReason);
        receivableReasonMapper.updateById(receivableReason);
        return findReceivableReasonById(receivableReason.getId());
    }

    @Override
    public Long addReceivableReason(ReceivableReasonDto receivableReasonDto) {
        ReceivableReason receivableReason = BeanCopyUtils.objClone(receivableReasonDto, ReceivableReason::new);
        receivableReason.setViewOrder(receivableReasonMapper.getMaxViewOrder());
        utilService.updateUserInfoToEntity(receivableReason);
        receivableReasonMapper.insert(receivableReason);
        return receivableReason.getId();
    }

    @Override
    public void delete(Long id) {
        //TODO 改过
        //ReceivableReason receivableReason = findReceivableReasonById(id);
        ReceivableReasonVo receivableReason = findReceivableReasonById(id);
        if (receivableReason == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        receivableReasonMapper.deleteById(receivableReason);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(List<ReceivableReasonDto> receivableReasonDtos) {
        for (ReceivableReasonDto receivableReasonDto : receivableReasonDtos) {
            if (GeneralTool.isEmpty(receivableReasonDto.getId())) {
                ReceivableReason receivableReason = BeanCopyUtils.objClone(receivableReasonDto, ReceivableReason::new);
                receivableReason.setViewOrder(receivableReasonMapper.getMaxViewOrder());
                utilService.updateUserInfoToEntity(receivableReason);
                receivableReasonMapper.insert(receivableReason);
            } else {
                ReceivableReason receivableReason = BeanCopyUtils.objClone(receivableReasonDto, ReceivableReason::new);
                utilService.updateUserInfoToEntity(receivableReason);
                receivableReasonMapper.updateById(receivableReason);
            }

        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void movingOrder(List<ReceivableReasonDto> receivableReasonDtos) {
        if (GeneralTool.isEmpty(receivableReasonDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        ReceivableReason ro = BeanCopyUtils.objClone(receivableReasonDtos.get(0), ReceivableReason::new);
        Integer oneorder = ro.getViewOrder();
        ReceivableReason rt = BeanCopyUtils.objClone(receivableReasonDtos.get(1), ReceivableReason::new);
        Integer twoorder = rt.getViewOrder();
        ro.setViewOrder(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setViewOrder(oneorder);
        utilService.updateUserInfoToEntity(rt);
        receivableReasonMapper.updateById(ro);
        receivableReasonMapper.updateById(rt);
    }

    @Override
    public List<BaseSelectEntity> getReasonSelect() {
        return receivableReasonMapper.getReasonSelect();
    }

    @Override
    public String getReasonNameById(Integer id) {
        if (GeneralTool.isEmpty(id)) {
            return null;
        }
        return receivableReasonMapper.getReasonNameById(id);
    }

    @Override
    public Map<Long, String> getReasonNameByIds(Set<Integer> ids) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(ids)) {
            return map;
        }
//        Example example = new Example(ReceivableReason.class);
//        example.createCriteria().andIn("id",ids);
        List<ReceivableReason> receivableReasons = receivableReasonMapper.selectBatchIds(ids);
        if (GeneralTool.isEmpty(receivableReasons)) {
            return map;
        }
        for (ReceivableReason receivableReason : receivableReasons) {
            map.put(receivableReason.getId(), receivableReason.getReasonName());
        }
        return map;
    }

}

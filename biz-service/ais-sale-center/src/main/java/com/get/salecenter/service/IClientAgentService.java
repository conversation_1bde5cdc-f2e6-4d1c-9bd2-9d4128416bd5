package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.salecenter.vo.ClientAgentVo;
import com.get.salecenter.entity.ClientAgent;
import com.get.salecenter.dto.ClientAgentDto;

import java.util.Map;
import java.util.Set;

/**
 * author:Neil
 * Time: 14:04
 * Date: 2022/8/17
 * Description:
 */
public interface IClientAgentService {

    /**
     * 根据客户ids查询代理名称
     *
     * @param clientIds
     * @return
     */
    Map<Long, String> getAgentNameByClientIds(Set<Long> clientIds);


    /**
     * 根据客户ids获取bd
     *
     * @param clientIds
     * @return
     */
    Map<Long, String> getBdCodeByClientIds(Set<Long> clientIds);


    /**
     * 绑定代理
     * @param clientAgentDto
     * @return
     */
    ResponseBo<Long> addClientAgent(ClientAgentDto clientAgentDto);


    /**
     * 解绑代理
     * @param clientAgentDto
     * @return
     */
    ResponseBo<ClientAgent> updateClientAgent(ClientAgentDto clientAgentDto);

    /**
     * 获取客户代理列表
     * @param clientAgentDto
     * @param page
     * @return
     */
    ResponseBo<ClientAgentVo> getClientAgentList(ClientAgentDto clientAgentDto, Page page);
}

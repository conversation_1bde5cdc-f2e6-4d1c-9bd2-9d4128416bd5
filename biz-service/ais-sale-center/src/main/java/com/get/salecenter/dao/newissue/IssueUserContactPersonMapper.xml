<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.newissue.IssueUserContactPersonMapper">
  <resultMap id="BaseResultMap" type="com.get.salecenter.entity.UserContactPerson">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_user_id" jdbcType="BIGINT" property="fkUserId" />
    <result column="fk_contact_person_id" jdbcType="BIGINT" property="fkContactPersonId" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
  <sql id="Base_Column_List">
    id, fk_user_id, fk_contact_person_id, gmt_create, gmt_create_user, gmt_modified, 
    gmt_modified_user
  </sql>


<!--  <insert id="insertSelective" parameterType="com.get.issuecenter.entity.UserContactPerson">-->
<!--    insert into r_user_contact_person-->
<!--    <trim prefix="(" suffix=")" suffixOverrides=",">-->
<!--      <if test="id != null">-->
<!--        id,-->
<!--      </if>-->
<!--      <if test="fkUserId != null">-->
<!--        fk_user_id,-->
<!--      </if>-->
<!--      <if test="fkContactPersonId != null">-->
<!--        fk_contact_person_id,-->
<!--      </if>-->
<!--      <if test="gmtCreate != null">-->
<!--        gmt_create,-->
<!--      </if>-->
<!--      <if test="gmtCreateUser != null">-->
<!--        gmt_create_user,-->
<!--      </if>-->
<!--      <if test="gmtModified != null">-->
<!--        gmt_modified,-->
<!--      </if>-->
<!--      <if test="gmtModifiedUser != null">-->
<!--        gmt_modified_user,-->
<!--      </if>-->
<!--    </trim>-->
<!--    <trim prefix="values (" suffix=")" suffixOverrides=",">-->
<!--      <if test="id != null">-->
<!--        #{id,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="fkUserId != null">-->
<!--        #{fkUserId,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="fkContactPersonId != null">-->
<!--        #{fkContactPersonId,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="gmtCreate != null">-->
<!--        #{gmtCreate,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="gmtCreateUser != null">-->
<!--        #{gmtCreateUser,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="gmtModified != null">-->
<!--        #{gmtModified,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="gmtModifiedUser != null">-->
<!--        #{gmtModifiedUser,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--    </trim>-->
<!--  </insert>-->
</mapper>
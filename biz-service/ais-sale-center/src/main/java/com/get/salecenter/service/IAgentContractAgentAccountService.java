package com.get.salecenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.common.result.Page;
import com.get.salecenter.dto.AgentContractAgentAccountDto;
import com.get.salecenter.entity.AgentContractAgentAccount;
import com.get.salecenter.vo.AgentContractAccountVo;

import java.util.List;


/**
 * 合同账户管理逻辑处理类
 *
 * @Date 16:05 2021/6/28
 * <AUTHOR>
 */
public interface IAgentContractAgentAccountService extends IService<AgentContractAgentAccount> {

    /**
     * @return java.util.List<com.get.salecenter.vo.AgentContractAgentAccountVo>
     * @Description:查询
     * @Param [agentContractAgentAccountDto, page]
     * <AUTHOR>
     **/
    List<AgentContractAccountVo> getAgentContractAgentAccount(AgentContractAgentAccountDto agentContractAgentAccountDto, Page page);

    /**
     * 代理合同-账户绑定
     *
     * @Date 16:50 2021/6/28
     * <AUTHOR>
     */
    void bindingContractAgentAccount(List<AgentContractAgentAccountDto> contractAgentAccountVos, Long contractId);

    /**
     * 合同绑定账户详情列表数据
     *
     * @Date 12:26 2021/6/29
     * <AUTHOR>
     */
    List<AgentContractAccountVo> getAgentContractAgentAccountDetail(Long contractId);


}

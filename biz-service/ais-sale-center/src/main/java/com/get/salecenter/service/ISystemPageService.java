package com.get.salecenter.service;


import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.salecenter.vo.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE: 2021/2/2
 * @TIME: 16:43
 * @Description:
 **/
public interface ISystemPageService {
    /**
     * @return com.get.salecenter.vo.SystemPageVo1
     * @Description: 系统首页初始数据
     * @Param [statisticsFlag true:统计申请计划数  false:统计学生数]
     * <AUTHOR>
     **/
    SystemPageVo1 getInitialDatas(List<Long> companyIds, String num, String year, Boolean statisticsFlag);


    void pushDatas();

    /**
     * 首页年份下拉框
     *
     * @Date 17:00 2022/1/4
     * <AUTHOR>
     */
    List<BaseSelectEntity> yearSelect(List<Long> companyId);

    /**
     * @return com.get.salecenter.vo.SystemPageVo1
     * @Description: 系统首页初始数据-获取总学生数
     * @Param [statisticsFlag true:统计申请计划数  false:统计学生数]
     * <AUTHOR>
     **/
    Long getStudentTotalSum(List<Long> companyIds, List<Long> areaCountryIds, String num, String year, Boolean statisticsFlag);

    /**
     * @return com.get.salecenter.vo.SystemPageVo1
     * @Description: 系统首页初始数据-学生申请记录
     * @Param []
     **/
    List<StudentCountVo> getStudentCountRecords(List<Long> companyIds, String num, String year);

    /**
     * @Description: 系统首页初始数据-国家学生数
     * @Param []
     **/
    List<WorldHistogramVo> getCountryStudentCount(List<Long> companyIds, List<Long> areaCountryIds, String num, String year, Boolean statisticsFlag);

    /**
     * @Description: 系统首页初始数据-州省学生数
     * @Param []
     **/
    List<WorldHistogramVo> getStateStudentCount(List<Long> companyIds, List<Long> areaCountryIds, String num, String year, Boolean statisticsFlag);

    /**
     * @Description: 系统首页初始数据-世界地图
     * @Param [statisticsFlag  true:统计申请计划数  false:统计学生数]
     **/
    List<WorldMapVo> getWorldMap(List<Long> companyIds, List<Long> areaCountryIds, String num, String year, Boolean statisticsFlag);

    /**
     * @Description: 系统首页初始数据-经纬度信息
     * @Param []
     **/
    SystemPageLonlatVo getLonlat(List<Long> companyIds, String num, String year);

    /**
     * @param companyIds
     * @param num
     * @param year
     * @return
     * @Description: 学生申请状态统计
     */
    List<StudentApplicationStatusCountVo> getStudentApplicationStatusCount(List<Long> companyIds, String num, String year);

    /**
     * 院校学生统计
     *
     * @param companyIds
     * @param num
     * @param year
     * @return
     */
    List<AssistantVo> getStudentStatisticsCount(List<Long> companyIds, String num, String year);


    /**
     * COE月度统计
     *
     * @param companyIds
     * @param num
     * @param year
     * @return
     */
    COEMonthlyStatisticsVo getCOEMonthlyStatistics(List<Long> companyIds, String num, String year);

    Map<String,List<FinanceStatisticVO>> getFinanceStatistic(List<Long> companyIds, String num, String year, String currencyTypeNumTO);

    /**
     * 送生统计占比
     * @param companyIds
     * @param num
     * @param year
     * @return
     */
    List<AssistantVo> getStudentStatistics(List<Long> companyIds, String num, String year);

    List<AreaRegionStatisticsVo> getRegionStudentCount(List<Long> companyIds, String num, String year);


    /**
     * 系统首页数据-获取总学生申请数
     *
     * @Date 11:32 2022/8/2
     * <AUTHOR>
     */
//    Long getStudentItemTotalSum(List<Long> companyIds, List<Long> countryIds, String year);


}

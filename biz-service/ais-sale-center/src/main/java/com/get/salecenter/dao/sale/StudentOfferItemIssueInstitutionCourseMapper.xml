<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.StudentOfferItemIssueInstitutionCourseMapper">
  <insert id="insert" parameterType="com.get.salecenter.entity.StudentOfferItemIssueInstitutionCourse">
    insert into r_student_offer_item_issue_institution_course (id, fk_student_id, fk_student_offer_item_id, 
      fk_student_institution_course_id_issue, gmt_create, 
      gmt_create_user, gmt_modified, gmt_modified_user
      )
    values (#{id,jdbcType=BIGINT}, #{fkStudentId,jdbcType=BIGINT}, #{fkStudentOfferItemId,jdbcType=BIGINT}, 
      #{fkStudentInstitutionCourseIdIssue,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.get.salecenter.entity.StudentOfferItemIssueInstitutionCourse">
    insert into r_student_offer_item_issue_institution_course
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkStudentId != null">
        fk_student_id,
      </if>
      <if test="fkStudentOfferItemId != null">
        fk_student_offer_item_id,
      </if>
      <if test="fkStudentInstitutionCourseIdIssue != null">
        fk_student_institution_course_id_issue,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkStudentId != null">
        #{fkStudentId,jdbcType=BIGINT},
      </if>
      <if test="fkStudentOfferItemId != null">
        #{fkStudentOfferItemId,jdbcType=BIGINT},
      </if>
      <if test="fkStudentInstitutionCourseIdIssue != null">
        #{fkStudentInstitutionCourseIdIssue,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="getCourseIdIssueByOfferItemId" resultType="java.lang.Long">
      select fk_student_institution_course_id_issue from r_student_offer_item_issue_institution_course
where fk_student_offer_item_id =#{id} limit 1
  </select>

  <select id="getCourseIdIssue2ByOfferItemIds" resultType="com.get.salecenter.entity.StudentOfferItemIssueInstitutionCourse">
    SELECT * FROM r_student_offer_item_issue_institution_course
    <where>
      <if test="ids!=null and ids.size()>0">
        fk_student_offer_item_id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
          #{id}
        </foreach>
      </if>
    </where>
  </select>
</mapper>
package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.service.GetService;
import com.get.salecenter.vo.CancelOfferReasonVo;
import com.get.salecenter.entity.CancelOfferReason;
import com.get.salecenter.dto.CancelOfferReasonDto;

import java.util.List;

/**
 * @author: Hardy
 * @create: 2023/1/11 10:49
 * @verison: 1.0
 * @description:
 */
public interface ICancelOfferReasonService extends GetService<CancelOfferReason> {

    /**
     * 新增
     * @param cancelOfferReasonDtos
     */
    void addCancelOfferReason(List<CancelOfferReasonDto> cancelOfferReasonDtos);

    /**
     * 删除
     * @param id
     */
    void deletecancelOfferReason(Long id);

    /**
     * 更新
     * @param cancelOfferReasonDto
     * @return
     */
    CancelOfferReasonVo updateCancelOfferReason(CancelOfferReasonDto cancelOfferReasonDto);

    /**
     * 详情
     * @param id
     * @return
     */
    CancelOfferReasonVo findCancelOfferReasonById(Long id);

    /**
     * 列表
     * @param cancelOfferReasonDto
     * @param page
     * @return
     */
    List<CancelOfferReasonVo> getCancelOfferReasonDtos(CancelOfferReasonDto cancelOfferReasonDto, Page page);

    /**
     * 排序
     * @param cancelOfferReasonDtos
     */
    void movingOrder(List<CancelOfferReasonDto> cancelOfferReasonDtos);

    /**
     * 申请方案终止作废原因下拉数据
     * @return
     */
    List<BaseSelectEntity> getCancelOfferReasonSelect();
}

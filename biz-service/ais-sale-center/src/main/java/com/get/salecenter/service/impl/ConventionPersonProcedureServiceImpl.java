package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.file.utils.FileUtils;
import com.get.salecenter.dao.sale.ConventionPersonMapper;
import com.get.salecenter.dao.sale.ConventionPersonProcedureMapper;
import com.get.salecenter.dao.sale.ConventionProcedureMapper;
import com.get.salecenter.vo.ConventionPersonProcedureVo;
import com.get.salecenter.vo.ConventionPersonProcedureExportVo;
import com.get.salecenter.vo.ConventionProcedureVo;
import com.get.salecenter.entity.ConventionPerson;
import com.get.salecenter.entity.ConventionPersonProcedure;
import com.get.salecenter.entity.ConventionProcedure;
import com.get.salecenter.entity.ConventionTable;
import com.get.salecenter.service.IConventionPersonProcedureService;
import com.get.salecenter.service.IConventionPersonService;
import com.get.salecenter.service.IConventionProcedureService;
import com.get.salecenter.service.IConventionTablePersonService;
import com.get.salecenter.dto.ConventionPersonProcedureDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.StringJoiner;
import java.util.stream.Collectors;

/**
 * @author: Sea
 * @create: 2020/7/15 15:39
 * @verison: 1.0
 * @description: 峰会参展人员-参会流程配置管理实现类
 */
@Service
public class ConventionPersonProcedureServiceImpl implements IConventionPersonProcedureService {
    @Resource
    private ConventionPersonProcedureMapper conventionPersonProcedureMapper;
    @Resource
    private IConventionProcedureService conventionProcedureService;
    @Resource
    private UtilService utilService;
    @Resource
    private IConventionTablePersonService tablePersonService;
    @Resource
    private IConventionPersonService personService;
    @Resource
    private ConventionProcedureMapper conventionProcedureMapper;
    @Resource
    private ConventionPersonMapper conventionPersonMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void conventionProcedureConfiguration(List<ConventionPersonProcedureDto> conventionPersonProcedureDtos) {
        for (ConventionPersonProcedureDto conventionPersonProcedureDto : conventionPersonProcedureDtos) {
            ConventionPersonProcedure conventionPersonProcedure = BeanCopyUtils.objClone(conventionPersonProcedureDto, ConventionPersonProcedure::new);
            Long conventionPersonProcedureId = conventionPersonProcedureMapper.getConventionPersonProcedureId(conventionPersonProcedureDto.getFkConventionPersonId(), conventionPersonProcedureDto.getFkConventionProcedureId());
            //有数据先删除(防止重复添加)
            if (GeneralTool.isNotEmpty(conventionPersonProcedureId)) {
                conventionPersonProcedureMapper.deleteById(conventionPersonProcedureId);
            }
            //判断是否参加
            if (conventionPersonProcedureDto.isJoin()) {
                utilService.updateUserInfoToEntity(conventionPersonProcedure);
                conventionPersonProcedureMapper.insertSelective(conventionPersonProcedure);
            }
        }

        //晚宴流程
        ConventionPerson conventionPerson = new ConventionPerson();
        conventionPerson.setIsAttendDinner(false);
        utilService.setUpdateInfo(conventionPerson);
        if (GeneralTool.isNotEmpty(conventionPersonProcedureDtos)) {
            List<ConventionPersonProcedureDto> conventionPersonProcedureDtoList = conventionPersonProcedureDtos.stream().filter(ConventionPersonProcedureDto::isJoin).collect(Collectors.toList());
            if (GeneralTool.isNotEmpty(conventionPersonProcedureDtoList)) {
                List<Long> fkConventionProcedureIds = conventionPersonProcedureDtoList.stream().map(ConventionPersonProcedureDto::getFkConventionProcedureId).collect(Collectors.toList());
                List<ConventionProcedure> conventionProcedures = conventionProcedureMapper.selectList(Wrappers.<ConventionProcedure>lambdaQuery().in(ConventionProcedure::getId, fkConventionProcedureIds).eq(ConventionProcedure::getFkTableTypeKey, ProjectKeyEnum.CONVENTION_DINNER_TABLE.key));
                if (GeneralTool.isNotEmpty(conventionProcedures)) {
                    conventionPerson.setIsAttendDinner(true);
                }
            }
        }
        Long fkConventionPersonId = conventionPersonProcedureDtos.get(0).getFkConventionPersonId();
        conventionPersonMapper.update(conventionPerson, Wrappers.<ConventionPerson>lambdaUpdate().eq(ConventionPerson::getId, fkConventionPersonId));

    }

    @Override
    public List<ConventionProcedureVo> getConventionPersonProcedure(Long conventionId, Long personId) {
        List<ConventionProcedureVo> convertDatas = new ArrayList<>();

        List<ConventionProcedureVo> conventionProcedureVos = conventionProcedureService.findConventionProcedureByFkId(conventionId);
        for (ConventionProcedureVo conventionProcedureVo : conventionProcedureVos) {
            //通过参会人员id 和 流程id查找对应参会流程配置
            Long conventionPersonProcedureId = conventionPersonProcedureMapper.getConventionPersonProcedureId(personId, conventionProcedureVo.getId());
            if (GeneralTool.isNotEmpty(conventionPersonProcedureId)) {
                //有数据表示愿意参加
                conventionProcedureVo.setJoin(true);
            }
            conventionProcedureVo.setPersonId(personId);
            convertDatas.add(conventionProcedureVo);
        }
        return convertDatas;
    }

    @Override
    public List<ConventionPersonProcedureVo> getConventionPerson(ConventionPersonProcedureDto conventionPersonProcedureDto1, Page page) {
        Long procedureId = conventionPersonProcedureDto1.getFkConventionProcedureId();
        ConventionProcedureVo conventionProcedureVo = conventionProcedureService.findConventionProcedureById(procedureId);
        Long conventionId = conventionProcedureVo.getFkConventionId();
        //当有bdNameKey条件时，查出对应bdCode，再去sql中筛选
        List<String> bdCodeList = null;
        if (GeneralTool.isNotEmpty(conventionPersonProcedureDto1) && GeneralTool.isNotEmpty(conventionPersonProcedureDto1.getBdNameKey())) {
            bdCodeList = personService.getBdCode(conventionPersonProcedureDto1.getBdNameKey());
        }
        List<ConventionPersonProcedureVo> conventionPersonProcedureVoList;
        IPage<ConventionPersonProcedureVo> iPage;
        if (GeneralTool.isEmpty(page)){
            iPage = null;
        }else {
            iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        }
        //查询条件-是否参加(不参加不用设置桌台)
        if (conventionPersonProcedureDto1.isJoin()) {
//            PageHelper.startPage(page.getCurrentPage(), page.getShowCount());

            conventionPersonProcedureVoList = conventionPersonProcedureMapper.getConventionPerson(iPage, procedureId, conventionPersonProcedureDto1, bdCodeList);
            //设置桌台编号
            setTableNum(conventionPersonProcedureVoList);
            //设置BD名称
            setBdName(conventionPersonProcedureVoList);
        } else {
            conventionPersonProcedureVoList = conventionPersonProcedureMapper.getConventionPerson(null, procedureId, conventionPersonProcedureDto1, bdCodeList);
            //已参加得人员id
            List<Long> personIds = conventionPersonProcedureVoList.stream().map(ConventionPersonProcedureVo::getFkConventionPersonId).collect(Collectors.toList());
            //防止in()报错
            if (GeneralTool.isEmpty(personIds)) {
                personIds.add(0L);
            }
//            PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
            conventionPersonProcedureVoList = conventionPersonProcedureMapper.getNotJoinConventionPerson(iPage, personIds, conventionPersonProcedureDto1, bdCodeList, conventionId);
            //设置BD名称
            setBdName(conventionPersonProcedureVoList);
            //前端判断用
            conventionPersonProcedureVoList.forEach(conventionPersonProcedureDto -> conventionPersonProcedureDto.setJoin(false));
        }
        if (GeneralTool.isNotEmpty(iPage)){
            page.setAll((int) iPage.getTotal());
        }
//        page.restPage(conventionPersonProcedureVoList);

        return conventionPersonProcedureVoList;
    }

    /**
     * 导出流程的参会人列表
     *
     * @param response
     * @param conventionPersonProcedureDto
     */
    @Override
    public void exportConventionPersonProcedureDetail(HttpServletResponse response, ConventionPersonProcedureDto conventionPersonProcedureDto) {

        List<ConventionPersonProcedureVo> conventionPerson = getConventionPerson(conventionPersonProcedureDto, null);

        List<ConventionPersonProcedureExportVo> conventionPersonProcedureExportVos = conventionPerson.stream().map(c -> {
            ConventionPersonProcedureExportVo conventionPersonProcedureExportVo = BeanCopyUtils.objClone(c, ConventionPersonProcedureExportVo::new);
            if (c.getGender().equals(0)) {
                conventionPersonProcedureExportVo.setGenderName("女");
            } else {
                conventionPersonProcedureExportVo.setGenderName("男");
            }
            conventionPersonProcedureExportVo.setTypeName(ProjectExtraEnum.getValueByKey(c.getType(), ProjectExtraEnum.CONVENTION_PERSON_TYPE));
            if (GeneralTool.isNotEmpty(c.getDinnerNumList())) {
                StringJoiner sj = new StringJoiner("/");
                for (String s : c.getDinnerNumList()) {
                    sj.add(s);
                }
                conventionPersonProcedureExportVo.setDinnerNum(sj.toString());
            }
            if (GeneralTool.isNotEmpty(c.getTrainingNumList())) {
                StringJoiner sj = new StringJoiner("/");
                for (String s : c.getTrainingNumList()) {
                    sj.add(s);
                }
                conventionPersonProcedureExportVo.setTrainingNum(sj.toString());
            }
            return conventionPersonProcedureExportVo;
        }).collect(Collectors.toList());

        FileUtils.exportExcelNotWrapText(response, conventionPersonProcedureExportVos, "ConventionPersonProcedure", ConventionPersonProcedureExportVo.class);
    }

    /**
     * @return void
     * @Description :设置BD名称
     * @Param [conventionPersonProcedureVoList]
     * <AUTHOR>
     */
    private void setBdName(List<ConventionPersonProcedureVo> conventionPersonProcedureVoList) {
        for (ConventionPersonProcedureVo conventionPersonProcedureVo : conventionPersonProcedureVoList) {
            String bdCode = conventionPersonProcedureVo.getBdCode();
            conventionPersonProcedureVo.setBdName(personService.getBdName(bdCode));
        }
    }

    /**
     * @return void
     * @Description :设置桌台编号
     * @Param [conventionPersonProcedureVoList]
     * <AUTHOR>
     */
    private void setTableNum(List<ConventionPersonProcedureVo> conventionPersonProcedureVoList) {
        for (ConventionPersonProcedureVo conventionPersonProcedureVo : conventionPersonProcedureVoList) {
            List<String> trainingNumList = new ArrayList<>();
            List<String> dinnerNumList = new ArrayList<>();
            //通过参会人员id 获取对应桌台信息
            List<ConventionTable> conventionTables = tablePersonService.getTableByPersonId(conventionPersonProcedureVo.getFkConventionPersonId());
            if (GeneralTool.isNotEmpty(conventionTables)) {
                for (ConventionTable conventionTable : conventionTables) {
                    //判断晚宴桌还是培训桌
                    if (ProjectKeyEnum.CONVENTION_TRAINING_TABLE.key.equals(conventionTable.getFkTableTypeKey())) {
                        trainingNumList.add(conventionTable.getTableNum());
                    } else if (ProjectKeyEnum.CONVENTION_DINNER_TABLE.key.equals(conventionTable.getFkTableTypeKey())) {
                        dinnerNumList.add(conventionTable.getTableNum());
                    }
                }
            }
            conventionPersonProcedureVo.setTrainingNumList(trainingNumList);
            conventionPersonProcedureVo.setDinnerNumList(dinnerNumList);
            //前端判断用
            conventionPersonProcedureVo.setJoin(true);
        }
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        ConventionPersonProcedure conventionPersonProcedure = conventionPersonProcedureMapper.selectById(id);
        if (conventionPersonProcedure == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        conventionPersonProcedureMapper.deleteById(id);
    }
}

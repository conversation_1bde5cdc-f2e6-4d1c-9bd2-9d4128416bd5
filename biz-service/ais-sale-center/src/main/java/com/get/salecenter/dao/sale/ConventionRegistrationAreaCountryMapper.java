package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.entity.ConventionRegistrationAreaCountry;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @author: Sea
 * @create: 2020/7/27 12:21
 * @verison: 1.0
 * @description: 峰会报名和国家关联中间表mapper
 */
@Mapper
public interface ConventionRegistrationAreaCountryMapper extends BaseMapper<ConventionRegistrationAreaCountry> {


    /**
     * 添加
     *
     * @param record
     * @return
     */
    int insertSelective(ConventionRegistrationAreaCountry record);

    /**
     * 通过关联表根据报名id查出国家keys
     *
     * @param id
     * @return
     */
    List<String> getCountryKey(Long id);
}
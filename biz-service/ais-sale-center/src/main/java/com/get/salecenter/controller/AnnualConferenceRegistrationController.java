package com.get.salecenter.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.redis.lock.RedisLock;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.vo.*;
import com.get.salecenter.vo.AnnualRegistrationVo;
import com.get.salecenter.service.IAnnualConferenceRegistrationService;
import com.get.salecenter.dto.*;
import com.get.salecenter.dto.AgentConventionPersonSaveDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * @author: Sea
 * @create: 2021/4/29 10:49
 * @verison: 1.0
 * @description:
 */
@Api(tags = "年度会议注册管理")
@RestController
@RequestMapping("sale/annualConferenceRegistration")
public class AnnualConferenceRegistrationController {
    @Resource
    private IAnnualConferenceRegistrationService annualConferenceRegistrationService;

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.AnnualConferenceRegistrationVo>
     * @Description :详情
     * @Param [annualConferenceRegistrationVo]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "配置详情接口", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/年度会议注册管理/年度会议注册详情")
    @GetMapping("getAnnualConferenceRegistrationDto")
    public ResponseBo<AnnualConferenceRegistrationVo> getAnnualConferenceRegistrationDto(@RequestParam Long conventionId, @RequestParam String receiptCode) {
        AnnualConferenceRegistrationVo data = annualConferenceRegistrationService.getAnnualConferenceRegistrationDto(conventionId, receiptCode);
        return new ResponseBo<>(data);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :新增信息
     * @Param [annualConferenceRegistrationDto]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "新增接口", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/年度会议注册管理/新增年度会议注册")
    @PostMapping("add")
    public ResponseBo add(@RequestBody AnnualConferenceRegistrationDto annualConferenceRegistrationDto) {
//        String data = request.getParameter("data");
//        JSONObject jsonData = JSONObject.parseObject(data);
//        JSONArray registrationVos = jsonData.getJSONArray("registrationVos");
//        JSONObject sponsorVo = jsonData.getJSONObject("sponsorVo");
//        AnnualConferenceRegistrationDto annualConferenceRegistrationDto = new AnnualConferenceRegistrationDto();
//        annualConferenceRegistrationDto.setRegistrationVos(registrationVos.toJavaList(ConventionRegistrationDto.class));
//        annualConferenceRegistrationDto.setSponsorVo(sponsorVo.toJavaObject(ConventionSponsorDto.class));
        annualConferenceRegistrationService.addAnnualConferenceRegistration(annualConferenceRegistrationDto);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.AnnualConferenceRegistrationVo>
     * @Description :修改信息
     * @Param [annualConferenceRegistrationVo]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "修改接口", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/年度会议注册管理/更新年度会议注册")
    @PostMapping("update")
    public ResponseBo update(HttpServletRequest request) {
        String data = request.getParameter("data");
        JSONObject jsonData = JSONObject.parseObject(data);
        JSONArray registrationVos = jsonData.getJSONArray("registrationVos");
        JSONObject sponsorVo = jsonData.getJSONObject("sponsorVo");
        AnnualConferenceRegistrationDto annualConferenceRegistrationDto = new AnnualConferenceRegistrationDto();
        annualConferenceRegistrationDto.setRegistrationVos(registrationVos.toJavaList(ConventionRegistrationDto.class));
        annualConferenceRegistrationDto.setSponsorVo(sponsorVo.toJavaObject(ConventionSponsorDto.class));
        annualConferenceRegistrationService.updateAnnualConferenceRegistration(annualConferenceRegistrationDto);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :赞助列表及是否剩余
     * @Param []
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "赞助列表及是否剩余", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/年度会议注册管理/赞助列表及是否剩余")
    @GetMapping("getSponsorshipConfig")
    public ResponseBo getSponsorshipConfig(@RequestParam Long conventionId) {
        List<Map<String, List<ConventionSponsorFeeVo>>> datas = annualConferenceRegistrationService.getSponsorshipConfig(conventionId);
        return new ListResponseBo<>(datas);
    }

    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "获取早鸟价配置", notes = "")
    @GetMapping("getEarlyBirdConfig")
    public ResponseBo getEarlyBirdConfig() {
        EarlyBirdConfigVo earlyBirdConfigVo = annualConferenceRegistrationService.getEarlyBirdConfig();
        return new ResponseBo<>(earlyBirdConfigVo);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :单个赞助对象验证是否售空
     * @Param [sponsorshipConfigId, initNum]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "单个赞助对象验证是否售空", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/年度会议注册管理/赞助列表及是否剩余")
    @GetMapping("soldOut")
    public ResponseBo soldOut(@RequestParam("sponsorshipConfigId") Long sponsorshipConfigId, @RequestParam("initNum") Integer initNum) {
        return new ResponseBo<>(annualConferenceRegistrationService.soldOut(sponsorshipConfigId, initNum));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :获取已选了的展位号
     * @Param []
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "获取已选了的展位号", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/年度会议注册管理/赞助列表及是否剩余")
    @GetMapping("getBoothIndex")
    public ResponseBo getBoothIndex(@RequestParam Long conventionId) {
        return new ResponseBo<>(annualConferenceRegistrationService.getBoothIndex(conventionId));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :该展位是否已坐
     * @Param [conventionId, boothNum]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "该展位是否已坐", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/年度会议注册管理/赞助列表及是否剩余")
    @GetMapping("haveSit")
    public ResponseBo haveSit(@RequestParam Long conventionId, @RequestParam("boothIndex") String boothNum) {
        return new ResponseBo<>(annualConferenceRegistrationService.haveSit(conventionId, boothNum));
    }


    /**
     * @return
     * @Description :
     * @Param
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "验证名字是否重复", notes = "")
    @GetMapping("providerNameVerify")
    public ResponseBo providerNameVerify(@RequestParam Long conventionId, @RequestParam("providerName") String providerName) {
        return new ResponseBo<>(annualConferenceRegistrationService.providerNameVerify(conventionId, providerName));
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description :新增信息
     * @Param []
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "新增接口", notes = "")
    @PostMapping("addRegistration")
    public ResponseBo addRegistration(@RequestBody AnnualRegistrationDto annualRegistrationDto) throws Exception {
//        String requestPayload = MyStringUtils.getRequestPayload(request);
//        JSONObject jsonObject = JSONObject.parseObject(requestPayload);
//        Long conventionId = jsonObject.getLong("conventionId");
//        String educationInstitutionName = jsonObject.getString("educationInstitutionName");
//        Integer isParticipant = jsonObject.getInteger("isParticipant");
//        String name = jsonObject.getString("name");
//        String tel = jsonObject.getString("tel");
//        String email = jsonObject.getString("email");
//        String registrationOptions = jsonObject.getString("registrationOptions");
//        JSONArray attendeesList = jsonObject.getJSONArray("attendeesList");
//        List<AttendeesDto> attendeesVoList = attendeesList.toJavaList(AttendeesDto.class);
//        AnnualRegistrationDto annualRegistrationDto = new AnnualRegistrationDto();
//        annualRegistrationDto.setConventionId(conventionId);
//        annualRegistrationDto.setEducationInstitutionName(educationInstitutionName);
//        annualRegistrationDto.setIsParticipant(isParticipant);
//        annualRegistrationDto.setName(name);
//        annualRegistrationDto.setTel(tel);
//        annualRegistrationDto.setEmail(email);
//        annualRegistrationDto.setRegistrationOptions(registrationOptions);
//        annualRegistrationDto.setAttendeesList(attendeesVoList);
        annualConferenceRegistrationService.addRegistration(annualRegistrationDto);
        return ResponseBo.ok();
    }

//    /**
//     * @return com.get.common.result.ResponseBo
//     * @Description :新增信息
//     * @Param []
//     * <AUTHOR>
//     */
//    @VerifyPermission(IsVerify = false)
//    @VerifyLogin(IsVerify = false)
//    @ApiOperation(value = "更新接口", notes = "")
//    @PostMapping("updateRegistration")
//    public ResponseBo updateRegistration(HttpServletRequest request)  {
//        String requestPayload = MyStringUtils.getRequestPayload(request);
//        JSONObject jsonObject = JSONObject.parseObject(requestPayload);
//        Long id = jsonObject.getLong("id");
//        Long conventionId = jsonObject.getLong("conventionId");
//        String educationInstitutionName = jsonObject.getString("educationInstitutionName");
//        Integer isParticipant = jsonObject.getInteger("isParticipant");
//        String name = jsonObject.getString("name");
//        String tel = jsonObject.getString("tel");
//        String registrationOptions = jsonObject.getString("registrationOptions");
//        JSONArray attendeesList = jsonObject.getJSONArray("attendeesList");
//        List<AttendeesDto> attendeesVoList = attendeesList.toJavaList(AttendeesDto.class);
//        AnnualRegistrationDto annualRegistrationVo = new AnnualRegistrationDto();
//        annualRegistrationVo.setId(id);
//        annualRegistrationVo.setConventionId(conventionId);
//        annualRegistrationVo.setEducationInstitutionName(educationInstitutionName);
//        annualRegistrationVo.setIsParticipant(isParticipant);
//        annualRegistrationVo.setName(name);
//        annualRegistrationVo.setTel(tel);
//        annualRegistrationVo.setRegistrationOptions(registrationOptions);
//        annualRegistrationVo.setAttendeesList(attendeesVoList);
//        AnnualRegistrationVo data = annualConferenceRegistrationService.updateRegistration(annualRegistrationVo);
//        return new ResponseBo<>(data);
//    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.AnnualHotelReservationVo>
     * @Description :删除参会人接口
     * @Param [id]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "删除参会人接口", notes = "")
    @GetMapping("deletePerson/{id}")
    public ResponseBo deletePerson(@PathVariable("id") Long id) {
        annualConferenceRegistrationService.deleteConventionPerson(id);
        return DeleteResponseBo.ok();
    }

//    /**
//     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.AnnualConferenceRegistrationVo>
//     * @Description :详情
//     * @Param [annualConferenceRegistrationVo]
//     * <AUTHOR>
//     */
//    @VerifyPermission(IsVerify = false)
//    @VerifyLogin(IsVerify = false)
//    @ApiOperation(value = "报名名册表单详情", notes = "")
//    @GetMapping("getAnnualRegistrationDto")
//    public ResponseBo<AnnualRegistrationVo> getAnnualRegistrationDto(@RequestParam Long conventionId, @RequestParam String receiptCode)  {
//        AnnualConferenceRegistrationVo data = annualConferenceRegistrationService.getAnnualConferenceRegistrationDto(conventionId, receiptCode);
//        return new ResponseBo<>(data);
//    }


    /**
     * 列表数据
     *
     * @param page
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "列表数据", notes = "institutionProviderName为学校提供商名称 , contactPersonName为联系人名称,fkConventionId该峰会id(一定有)")
    @PostMapping("datas")
    public ResponseBo<AnnualRegistrationVo> datas(@RequestBody SearchBean<AnnualRegistrationDto> page) {
        List<AnnualRegistrationVo> datas = annualConferenceRegistrationService.getAnnualRegistrationDtos(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }


//    /**
//     * @return void
//     * @Description :导出参会人员名册Excel
//     * @Param [response,conventionSponsorVo]
//     * <AUTHOR>
//     */
//    @ApiOperation(value = "导出参会人员名册Excel")
//    @PostMapping("/exportRegistrationExcel")
//    @ResponseBody
//    public void exportRegistrationExcel(HttpServletResponse response, @RequestBody AnnualRegistrationDto annualRegistrationVo)  {
//        annualConferenceRegistrationService.exportRegistrationExcel(response, annualRegistrationVo);
//    }

//    /**
//     * @return void
//     * @Description :测试用导出报名名册表单Excel
//     * @Param [response,conventionSponsorVo]
//     * <AUTHOR>
//     */
//    @VerifyPermission(IsVerify = false)
//    @VerifyLogin(IsVerify = false)
//    @ApiOperation(value = "测试用导出报名名册表单Excel")
//    @GetMapping("/exportRegistrationExcel")
//    @ResponseBody
//    public void exportRegistrationExcel(HttpServletResponse response)  {
//        AnnualRegistrationDto annualRegistrationVo = new AnnualRegistrationDto();
//        annualRegistrationVo.setConventionId(14L);
//        annualConferenceRegistrationService.exportRegistrationExcel(response, annualRegistrationVo);
//    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :代理参会人员报名
     * @Param []
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "代理参会人员报名", notes = "")
    @PostMapping("addAgentConventionPerson")
    @RedisLock(value = "get:addAgentConventionPerson:RedisLock", param = "#agentConventionPersonSaveDto.fkTableTypeKey+#agentConventionPersonSaveDto.tableNum", waitTime = 10L)
    public ResponseBo addAgentConventionPerson(@RequestBody AgentConventionPersonSaveDto agentConventionPersonSaveDto) {
        annualConferenceRegistrationService.addAgentConventionPerson(agentConventionPersonSaveDto);
        return ResponseBo.ok();
    }

    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "2022GEA峰会 代理参会人员报名培训桌下拉框", notes = "")
    @GetMapping("agentConventionTableList")
    public ResponseBo agentConventionTableList() {
        List<ConventionTableVo> conventionTableVos = annualConferenceRegistrationService.agentConventionTableList();
        return new ResponseBo<>(conventionTableVos);
    }





}

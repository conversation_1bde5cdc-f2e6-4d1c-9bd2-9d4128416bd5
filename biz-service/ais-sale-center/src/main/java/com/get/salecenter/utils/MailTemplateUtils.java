package com.get.salecenter.utils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;

/**
 * @author: Sea
 * @create: 2021/5/9 18:31
 * @verison: 1.0
 * @description:
 */
public class MailTemplateUtils {
    public static String getMailTemplate(String name, String receiptCode, String filename) {
        //String filename = "template.html";
//        InputStream inputStream = ClassLoader.getSystemResourceAsStream(filename);
        InputStream inputStream = MailTemplateUtils.class.getClassLoader().getResourceAsStream(filename);
        BufferedReader fileReader = new BufferedReader(new InputStreamReader(inputStream));
        StringBuffer buffer = new StringBuffer();

        String line = "";
        try {
            while ((line = fileReader.readLine()) != null) {
                buffer.append(line);
            }
            System.out.println(buffer.toString());
        } catch (IOException e) {
            e.printStackTrace();
            System.out.println("read template");
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            if (fileReader != null) {
                try {
                    fileReader.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        //替换回执码
//        String htmlText = buffer.toString().replace("#{receiptCode}", receiptCode);
        //替换姓名
        String replace = buffer.toString().replace("#{name}", name);
        return replace;
    }
}

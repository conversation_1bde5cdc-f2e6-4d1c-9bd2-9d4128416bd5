package com.get.salecenter.service.impl;

import com.alibaba.druid.util.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.cache.utils.CacheUtil;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.CollectionUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.file.utils.FileUtils;
import com.get.filecenter.dto.FileDto;
import com.get.filecenter.feign.IFileCenterClient;
import com.get.financecenter.feign.IFinanceCenterClient;
import com.get.institutioncenter.entity.MediaAndAttached;
import com.get.institutioncenter.vo.AreaRegionVo;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.dao.convention.ConventionHotelPayMapper;
import com.get.salecenter.dao.sale.AgentMapper;
import com.get.salecenter.dao.sale.ConventionHotelMapper;
import com.get.salecenter.dao.sale.ConventionHotelRoomPersonMapper;
import com.get.salecenter.dao.sale.ConventionPersonAgentMapper;
import com.get.salecenter.dao.sale.ConventionPersonInstitutionMapper;
import com.get.salecenter.dao.sale.ConventionPersonInstitutionProviderMapper;
import com.get.salecenter.dao.sale.ConventionPersonMapper;
import com.get.salecenter.dao.sale.ConventionPersonProcedureMapper;
import com.get.salecenter.dao.sale.ConventionPersonRegistrationMapper;
import com.get.salecenter.dao.sale.ConventionPersonStaffMapper;
import com.get.salecenter.dao.sale.ConventionProcedureMapper;
import com.get.salecenter.dao.sale.ConventionRegistrationMapper;
import com.get.salecenter.dao.sale.ConventionTablePersonMapper;
import com.get.salecenter.dao.sale.StaffBdCodeMapper;
import com.get.salecenter.dto.*;
import com.get.salecenter.entity.*;
import com.get.salecenter.service.*;
import com.get.salecenter.vo.*;
import com.get.salecenter.utils.MyStringUtils;
import com.get.salecenter.utils.sale.VerifyStudentOfferItemUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.StringJoiner;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.get.common.cache.CacheNames.STAFF_FOLLOWER_IDS_CACHE;

/**
 * @author: Sea
 * @create: 2020/7/8 14:15
 * @verison: 1.0
 * @description: 峰会参展人员管理实现类
 */
@Service
public class ConventionPersonServiceImpl extends BaseServiceImpl<ConventionPersonMapper, ConventionPerson> implements IConventionPersonService {
    @Resource
    private ConventionPersonMapper conventionPersonMapper;
    @Resource
    private ConventionPersonRegistrationMapper conventionPersonRegistrationMapper;
    @Resource
    private ConventionPersonAgentMapper conventionPersonAgentMapper;
    @Resource
    private ConventionPersonInstitutionMapper conventionPersonInstitutionMapper;
    @Resource
    private ConventionPersonInstitutionProviderMapper conventionPersonInstitutionProviderMapper;
    @Resource
    private ConventionPersonStaffMapper conventionPersonStaffMapper;
    @Resource
    private ConventionRegistrationMapper conventionRegistrationMapper;
    @Resource
    private AgentMapper agentMapper;
    @Resource
    private IAgentService agentService;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private UtilService utilService;
    @Resource
    private IDeleteService deleteService;
    @Resource
    private ConventionHotelMapper conventionHotelMapper;
    @Resource
    private IConventionService conventionService;
    @Resource
    private IFinanceCenterClient financeCenterClient;
    @Resource
    private ConventionProcedureMapper conventionProcedureMapper;
    @Resource
    private ConventionPersonProcedureMapper conventionPersonProcedureMapper;
    @Resource
    private StaffBdCodeMapper staffBdCodeMapper;
    @Resource
    private ConventionHotelRoomPersonMapper conventionHotelRoomPersonMapper;
    @Resource
    private ConventionTablePersonMapper conventionTablePersonMapper;
    @Resource
    private IConventionPersonRegistrationService conventionPersonRegistrationService;
    @Resource
    private IConventionPersonAgentService conventionPersonAgentService;
    @Resource
    private IStaffBdCodeService staffBdCodeService;
    @Resource
    private VerifyStudentOfferItemUtils verifyStudentOfferItemUtils;
    @Resource
    private IMediaAndAttachedService attachedService;
    @Resource
    private IFileCenterClient fileCenterClient;
    @Autowired
    private ConventionHotelPayMapper conventionHotelPayMapper;

    @Override
    public ConventionPersonVo findConventionPersonById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ConventionPerson conventionPerson = conventionPersonMapper.selectById(id);
        ConventionPersonVo conventionPersonVo = BeanCopyUtils.objClone(conventionPerson, ConventionPersonVo::new);
        ConventionHotel conventionHotel = conventionHotelMapper.getConventionHotelByConventionPerson(id);
        String roomType;
        if (GeneralTool.isEmpty(conventionHotel)) {
            roomType = "";
        } else {
            roomType = conventionHotel.getRoomType();
        }
        if (GeneralTool.isNotEmpty(conventionPerson.getBdCode())){
            Long regionId = staffBdCodeService.getAreaRegionIdIdByBdCode(conventionPerson.getBdCode());
            if (GeneralTool.isNotEmpty(regionId)){
                conventionPersonVo.setFkAreaRegionId(regionId);
                AreaRegionVo areaRegionVo = institutionCenterClient.findAreaRegionById(regionId).getData();
                conventionPersonVo.setFkAreaRegionName(areaRegionVo.getName()+"("+ areaRegionVo.getNameChn()+")");
            }
        }
        conventionPersonVo.setRoomTypeName(roomType);
        getInfo(conventionPersonVo);
        return conventionPersonVo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long addConventionPerson(ConventionPersonDto conventionPersonDto) {
        if (GeneralTool.isEmpty(conventionPersonDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        ConventionPerson conventionPerson = BeanCopyUtils.objClone(conventionPersonDto, ConventionPerson::new);
        String validateResult = validateAdd(conventionPersonDto);
        if (GeneralTool.isEmpty(validateResult)) {
            utilService.updateUserInfoToEntity(conventionPerson);
            conventionPersonMapper.insertSelective(conventionPerson);
            if (GeneralTool.isEmpty(conventionPersonDto.getNum())){
                String num = "";
                if (ProjectExtraEnum.INSTITUTION_AGENT.key.equals(conventionPersonDto.getType())){
                    num = MyStringUtils.getNumCommon("A",conventionPerson.getId(),8);
                }else if (ProjectExtraEnum.AGENT.key.equals(conventionPersonDto.getType())){
                    num = MyStringUtils.getNumCommon("B",conventionPerson.getId(),8);
                }else if (ProjectExtraEnum.GUEST.key.equals(conventionPersonDto.getType())){
                    num = MyStringUtils.getNumCommon("C",conventionPerson.getId(),8);
                }else {
                    num = MyStringUtils.getNumCommon("D",conventionPerson.getId(),8);
                }
                conventionPerson.setNum(num);
                conventionPersonMapper.updateById(conventionPerson);
            }
            //插入后返回的参展人员id
            Long id = conventionPerson.getId();
            //关联表的保存
            insertTable(id, conventionPersonDto);
        } else {
            throw new GetServiceException(validateResult);
        }
        return conventionPerson.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ConventionPerson conventionPerson = conventionPersonMapper.selectById(id);
        if (conventionPerson == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
//        //判断该参会人员是否能删除
//        deleteService.deleteValidatePerson(id);
        //修改为直接删除参会人所有关联表
        int i = conventionPersonMapper.deleteById(id);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
        deleteAssociationTable(id, conventionPerson);
        //关联表的删除
        //deleteTable(id, conventionPerson);

        List<ConventionPerson> conventionPersonList = conventionPersonMapper.selectList(Wrappers.lambdaQuery(ConventionPerson.class)
                .like(ConventionPerson::getRemark, "同住人:" + id));

        if (GeneralTool.isNotEmpty(conventionPersonList)){
            for (ConventionPerson person : conventionPersonList) {
                String remark = person.getRemark();
                List<String> list = MyStringUtils.getContentBetweenMiddleBrackets(remark);
                for (String s : list) {
                    if (s.contains("同住人")){
                        remark = remark.replace(s,"");
                        remark = remark.replace("[]","");
                    }
                }
                person.setRemark(remark);
                utilService.setUpdateInfo(person);
                conventionPersonMapper.updateById(person);
            }
        }

    }

    /**
     * 删除参会人关联表
     *
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteAssociationTable(Long id, ConventionPerson conventionPerson) {
        //删除人员类型关系
        deleteTable(id, conventionPerson);
//        Example example = new Example(ConventionHotelRoomPerson.class);
//        example.createCriteria().andEqualTo("fkConventionPersonId", id);
//        conventionHotelRoomPersonMapper.deleteByExample(example);
        conventionHotelRoomPersonMapper.delete(Wrappers.<ConventionHotelRoomPerson>lambdaQuery().eq(ConventionHotelRoomPerson::getFkConventionPersonId, id));

//        Example example1 = new Example(ConventionPersonProcedure.class);
//        example1.createCriteria().andEqualTo("fkConventionPersonId", id);
//        conventionPersonProcedureMapper.deleteByExample(example1);
        conventionPersonProcedureMapper.delete(Wrappers.<ConventionPersonProcedure>lambdaQuery().eq(ConventionPersonProcedure::getFkConventionPersonId, id));


//        Example example2 = new Example(ConventionPersonInstitution.class);
//        example2.createCriteria().andEqualTo("fkConventionPersonId", id);
//        conventionPersonInstitutionMapper.deleteByExample(example2);
        conventionPersonInstitutionMapper.delete(Wrappers.<ConventionPersonInstitution>lambdaQuery().eq(ConventionPersonInstitution::getFkConventionPersonId, id));


//        Example example3 = new Example(ConventionTablePerson.class);
//        example3.createCriteria().andEqualTo("fkConventionPersonId", id);
//        conventionTablePersonMapper.deleteByExample(example3);
        conventionTablePersonMapper.delete(Wrappers.<ConventionTablePerson>lambdaQuery().eq(ConventionTablePerson::getFkConventionPersonId, id));

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ConventionPersonVo updateConventionPerson(ConventionPersonDto conventionPersonDto) {
        if (conventionPersonDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        ConventionPerson result = conventionPersonMapper.selectById(conventionPersonDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        ConventionPerson conventionPerson = BeanCopyUtils.objClone(conventionPersonDto, ConventionPerson::new);
        String validateResult = validateUpdate(conventionPersonDto);
        if (GeneralTool.isEmpty(validateResult)) {
            utilService.updateUserInfoToEntity(conventionPerson);
            conventionPersonMapper.updateById(conventionPerson);

            //晚宴行程
            List<ConventionProcedure> conventionProcedures = conventionProcedureMapper.selectList(Wrappers.<ConventionProcedure>lambdaQuery()
                    .eq(ConventionProcedure::getFkConventionId, conventionPersonDto.getFkConventionId())
                    .eq(ConventionProcedure::getFkTableTypeKey, ProjectKeyEnum.CONVENTION_DINNER_TABLE.key));
            if (GeneralTool.isNotEmpty(conventionProcedures)) {
                List<Long> proceduresIds = conventionProcedures.stream().map(ConventionProcedure::getId).collect(Collectors.toList());
                conventionPersonProcedureMapper.delete(Wrappers.<ConventionPersonProcedure>lambdaQuery()
                        .eq(ConventionPersonProcedure::getFkConventionPersonId, conventionPerson.getId())
                        .in(ConventionPersonProcedure::getFkConventionProcedureId, proceduresIds));
                if (GeneralTool.isNotEmpty(conventionPerson.getIsAttendDinner()) && conventionPerson.getIsAttendDinner()) {
                    for (Long proceduresId : proceduresIds) {
                        ConventionPersonProcedure conventionPersonProcedure = new ConventionPersonProcedure();
                        conventionPersonProcedure.setFkConventionPersonId(conventionPerson.getId());
                        conventionPersonProcedure.setFkConventionProcedureId(proceduresId);
                        utilService.setCreateInfo(conventionPersonProcedure);
                        conventionPersonProcedureMapper.insert(conventionPersonProcedure);
                    }
                }
            }

            //关联表的修改
            deleteTable(conventionPersonDto.getId(), result);
            insertTable(conventionPersonDto.getId(), conventionPersonDto);
        } else {
            throw new GetServiceException(validateResult);
        }
        return findConventionPersonById(conventionPersonDto.getId());
    }

    @Override
    public List<ConventionPersonVo> getConventionPersons(ConventionPersonDto conventionPersonDto, Page page) {
        List<Long> intersectResult = new ArrayList<>();
        if (GeneralTool.isNotEmpty(conventionPersonDto.getFilterType())) {
            switch (conventionPersonDto.getFilterType()) {
                case 0:
                    intersectResult = conventionPersonMapper.getPersonIdsByNotDinnerProcedure(conventionPersonDto);
                    break;
                case 1:
                    intersectResult = conventionPersonMapper.getPersonIdsByNotTrainingProcedure(conventionPersonDto);
                    break;
                default:
                    break;
            }
        }

        LambdaQueryWrapper<ConventionPerson> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ConventionPerson::getFkConventionId, conventionPersonDto.getFkConventionId());

        if (GeneralTool.isNotEmpty(conventionPersonDto.getFilterType())) {
            if (GeneralTool.isNotEmpty(intersectResult)) {
                lambdaQueryWrapper.in(ConventionPerson::getId, intersectResult);
            } else {
                return Collections.emptyList();
            }
        }

        if (GeneralTool.isEmpty(conventionPersonDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        //查询条件-是否vip
        if (GeneralTool.isNotEmpty(conventionPersonDto.getIsVip())) {
            lambdaQueryWrapper.eq(ConventionPerson::getIsVip, conventionPersonDto.getIsVip());
        }
        //查询条件-bd页面
        if (GeneralTool.isNotEmpty(conventionPersonDto.getBdCodeKey())) {
            if (conventionPersonDto.getBdCodeKey()) {
//                String bdCode = getBdCodeByStaffId(GetAuthInfo.getStaffId());
                Long staffId = SecureUtil.getStaffId();
                List<Long> staffFollowerIds = Lists.newArrayList();
                List<Long> followerIds = CacheUtil.get(
                        STAFF_FOLLOWER_IDS_CACHE,
                        "staffId:",
                        staffId,
                        ()->permissionCenterClient.getStaffFollowerIds(staffId).getData()
                );
                if (GeneralTool.isNotEmpty(followerIds)) {
                    staffFollowerIds.addAll(followerIds.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList()));
                }
                staffFollowerIds.add(staffId);
                List<String> bdCodes = getBdCodeByStaffIds(staffFollowerIds);
                if (GeneralTool.isNotEmpty(bdCodes)){
                    lambdaQueryWrapper.in(ConventionPerson::getBdCode, bdCodes);
                }else {
                    return Collections.emptyList();
                }
            }
        }

        //bdCode和大区
        if (GeneralTool.isNotEmpty(conventionPersonDto.getBdCode())){
            lambdaQueryWrapper.eq(ConventionPerson::getBdCode, conventionPersonDto.getBdCode());
        } else if (GeneralTool.isNotEmpty(conventionPersonDto.getFkAreaRegionId())) {
            List<StaffBdCodeVo> bdList = getBdList(conventionPersonDto.getFkConventionId(), conventionPersonDto.getFkAreaRegionId());
            Set<String> bdCodeRegions = bdList.stream().map(StaffBdCodeVo::getBdCode).collect(Collectors.toSet());
            lambdaQueryWrapper.in(ConventionPerson::getBdCode, bdCodeRegions);
        }


        //查询条件-类别
        if (GeneralTool.isNotEmpty(conventionPersonDto.getType())) {
            lambdaQueryWrapper.eq(ConventionPerson::getType, conventionPersonDto.getType());
        }
        //查询条件-参会编号
        if (GeneralTool.isNotEmpty(conventionPersonDto.getNum())) {
            lambdaQueryWrapper.like(ConventionPerson::getNum, conventionPersonDto.getNum());
        }
        //查询条件-人员名称
        if (GeneralTool.isNotEmpty(conventionPersonDto.getNameKey())) {
            lambdaQueryWrapper.and(wrapper -> wrapper.apply("REPLACE(name,\" \",\"\") like " + "\"%" + conventionPersonDto.getNameKey().trim() + "%\"")
                    .or().like(ConventionPerson::getName, conventionPersonDto.getNameKey())
                    .or().like(ConventionPerson::getNameChn, conventionPersonDto.getNameKey())
                    .or().apply("REPLACE(name_chn,\" \",\"\") like " + "\"%" + conventionPersonDto.getNameKey().trim() + "%\""));
//                    .or().apply("to_pinyin(REPLACE ( name_chn, \" \", \"\" )) like " + "\"%" + conventionPersonDto.getNameKey().trim() + "%\"");
        }
        //查询条件-联系电话
        if (GeneralTool.isNotEmpty(conventionPersonDto.getTel())) {
            lambdaQueryWrapper.like(ConventionPerson::getTel, conventionPersonDto.getTel());
        }
        //查询条件-交通编号
        if (GeneralTool.isNotEmpty(conventionPersonDto.getTransportationCode())) {
            lambdaQueryWrapper.and(wrapper -> wrapper.like(ConventionPerson::getArrivalTransportationCode, conventionPersonDto.getTransportationCode())
                    .or().like(ConventionPerson::getLeaveTransportationCode, conventionPersonDto.getTransportationCode()));

        }
        //查询条件-学校名称-公司查询
        if (GeneralTool.isNotEmpty(conventionPersonDto.getInstitutionName())) {
            lambdaQueryWrapper.like(ConventionPerson::getCompany, conventionPersonDto.getInstitutionName());
        }
        List<Long> idList = new ArrayList<>();
        //查询条件-学校提供商名称
        if (GeneralTool.isNotEmpty(conventionPersonDto.getInstitutionProviderName())) {
            //根据providerName查询报名名册id
            List<Long> conventionRegistrationIds = getConventionRegistration(conventionPersonDto.getInstitutionProviderName());
            //根据报名名册ids
            List<Long> conventionPersonIds = getConventionPersonIds(conventionRegistrationIds);
            if (GeneralTool.isNotEmpty(conventionPersonIds)) {
                idList.addAll(conventionPersonIds);
            }
        }
        //查询条件-代理编号 代理名称
        if (GeneralTool.isNotEmpty(conventionPersonDto.getAgentNum()) || GeneralTool.isNotEmpty(conventionPersonDto.getAgentName())) {
            //根据代理编号、代理名称模糊查询代理ids
            List<Long> agentIds = agentService.getAgentIds(conventionPersonDto.getAgentNum(), conventionPersonDto.getAgentName());
            //根据参展人员和代理关联中间表 查出的参展人员id属于agentIds内
            List<Long> conventionPersonIds = conventionPersonAgentMapper.getConventionPersonIds(agentIds);
            //若为空 防止 in（）报错，默认给值为0
            if (GeneralTool.isEmpty(conventionPersonIds)) {
                conventionPersonIds.add(0L);
            }
            idList.addAll(conventionPersonIds);
        }
        if (GeneralTool.isNotEmpty(idList)) {
            lambdaQueryWrapper.in(ConventionPerson::getId, idList.stream().distinct().collect(Collectors.toList()));
        }
        //查询条件-是否签到
        if (GeneralTool.isNotEmpty(conventionPersonDto.getIsAttend())) {
            lambdaQueryWrapper.and(wrapper -> {
                if (!conventionPersonDto.getIsAttend()) {
                    wrapper.eq(ConventionPerson::getIsAttend, conventionPersonDto.getIsAttend());
                    wrapper.or().isNull(ConventionPerson::getIsAttend);
                } else {
                    wrapper.eq(ConventionPerson::getIsAttend, conventionPersonDto.getIsAttend());
                }
            });

        }
        //查询条件-跟进BD名称
        if (GeneralTool.isNotEmpty(conventionPersonDto.getBdNameKey())) {
            //根据bdName模糊查询bdNumList
            List<String> bdCodeList = getBdCode(conventionPersonDto.getBdNameKey());
            if (GeneralTool.isNotEmpty(bdCodeList)) {
                lambdaQueryWrapper.in(ConventionPerson::getBdCode, bdCodeList);
            }else {
                return Collections.emptyList();
            }
        }
        //查询条件-性别
        if (GeneralTool.isNotEmpty(conventionPersonDto.getGender())) {
            lambdaQueryWrapper.eq(ConventionPerson::getGender, conventionPersonDto.getGender());
        }
        if (GeneralTool.isNotEmpty(conventionPersonDto.getIsAttendDinner())){
            lambdaQueryWrapper.eq(ConventionPerson::getIsAttendDinner, conventionPersonDto.getIsAttendDinner());
        }
        if (GeneralTool.isNotEmpty(conventionPersonDto.getIsBookHotel())){
            lambdaQueryWrapper.eq(ConventionPerson::getIsBookHotel, conventionPersonDto.getIsBookHotel());
        }
        //房型
        if (GeneralTool.isNotEmpty(conventionPersonDto.getFkConventionHotelId())){
            lambdaQueryWrapper.eq(ConventionPerson::getFkConventionHotelId, conventionPersonDto.getFkConventionHotelId());
        }
        lambdaQueryWrapper.orderByDesc(ConventionPerson::getGmtCreate);
        List<ConventionPerson> conventionPersons = new ArrayList<>();
        if (page == null) {
            conventionPersons = conventionPersonMapper.selectList(lambdaQueryWrapper);
        } else {
            IPage<ConventionPerson> iPage = conventionPersonMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
            page.setAll((int) iPage.getTotal());
            conventionPersons = iPage.getRecords();
        }
        if (GeneralTool.isEmpty(conventionPersons)){
            return Collections.emptyList();
        }
        List<Long> ids = conventionPersons.stream().map(ConventionPerson::getId).collect(Collectors.toList());
        List<ConventionTablePersonNumVo> conventionTablePersonDtos = conventionTablePersonMapper.getTableByPersonIds(ids);
        HashMap<Long, String> dinnerMap = new HashMap<>();
        HashMap<Long, String> trainingMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(conventionTablePersonDtos)){
            trainingMap =
                    conventionTablePersonDtos.stream().filter(x -> x.getFkTableTypeKey().equals(ProjectKeyEnum.CONVENTION_TRAINING_TABLE.key)).collect(HashMap::new, (m
                    , v) -> m.put(v.getFkConventionPersonId(), v.getTableNum()), HashMap::putAll);
            dinnerMap =
                    conventionTablePersonDtos.stream().filter(x -> x.getFkTableTypeKey().equals(ProjectKeyEnum.CONVENTION_DINNER_TABLE.key)).collect(HashMap::new, (m
                            , v) -> m.put(v.getFkConventionPersonId(), v.getTableNum()), HashMap::putAll);
        }
        List<String> bdlist = conventionPersons.stream().map(ConventionPerson::getBdCode).filter(Objects::nonNull).collect(Collectors.toList());
        Map<String, String> bdNameMap = getBdNameMap(bdlist);

        Set<Long> hoteIds = conventionPersons.stream().map(ConventionPerson::getFkConventionHotelId).collect(Collectors.toSet());
        Map<Long, String> hoteNameMap = gethoteMap(hoteIds);

        Set<String> bdCodes = conventionPersons.stream().map(ConventionPerson::getBdCode).collect(Collectors.toSet());
        Map<String, String> areaRegionBybdCodeMap = staffBdCodeService.getAreaRegionBybdCode(bdCodes);

        //支付情况
        List<ConventionPersonPayTypeVo> conventionPersonPayTypeVoList = conventionHotelPayMapper.getPayTypeByConventionPersonIds(ids);
        Map<Long, Integer> payTypeMap = conventionPersonPayTypeVoList.stream().collect(Collectors.toMap(ConventionPersonPayTypeVo::getFkConventionPersonId, ConventionPersonPayTypeVo::getPayType));

        List<ConventionPersonVo> convertDatas = new ArrayList<>();
        for (ConventionPerson conventionPerson : conventionPersons) {
            ConventionPersonVo conventionPersonVo = BeanCopyUtils.objClone(conventionPerson, ConventionPersonVo::new);
            getInfo(conventionPersonVo);
            //设置bdName
//            conventionPersonVo.setBdName(getBdName(conventionPersonVo.getBdCode()));
            if (GeneralTool.isNotEmpty(bdNameMap)){
                conventionPersonVo.setBdName(bdNameMap.get(conventionPersonVo.getBdCode()));
            }
            if (GeneralTool.isNotEmpty(areaRegionBybdCodeMap) && GeneralTool.isNotEmpty(conventionPerson.getBdCode())){
                conventionPersonVo.setFkAreaRegionName(areaRegionBybdCodeMap.get(conventionPerson.getBdCode()));
            }
            //设置意向房型
            if (GeneralTool.isNotEmpty(hoteNameMap)){
                conventionPersonVo.setIntentionRoomType(hoteNameMap.get(conventionPerson.getFkConventionHotelId()));
            }

            //设置餐桌号
            if (GeneralTool.isNotEmpty(dinnerMap)) {
                conventionPersonVo.setConventionDinnerTable(dinnerMap.get(conventionPersonVo.getId()));
            }

            //设置培训桌号
            if (GeneralTool.isNotEmpty(trainingMap)) {
                conventionPersonVo.setConventionTrainingTable(trainingMap.get(conventionPersonVo.getId()));
            }

            if (GeneralTool.isNotEmpty(payTypeMap)) {
                Integer payType = payTypeMap.get(conventionPersonVo.getId());
                conventionPersonVo.setPayType(GeneralTool.isNotEmpty(payType) ? payType : 0);
            }

         //设置房型 通过person查询房型
            ConventionHotel conventionHotel = conventionHotelMapper.getConventionHotelByConventionPerson(conventionPerson.getId());
            String roomType;
            if (GeneralTool.isEmpty(conventionHotel)) {
                roomType = "";
            } else {
                roomType = conventionHotel.getRoomType();
            }
            conventionPersonVo.setRoomTypeName(roomType);
            convertDatas.add(conventionPersonVo);
        }
//        if (GeneralTool.isNotEmpty(conventionPersonDto.getIsAttendDinnerProcedure())) {
//            //过滤是否参加晚宴流程
//            filterConvertDatasByDinnerProcedure(conventionPersonDto, convertDatas);
//        }
//        if (GeneralTool.isNotEmpty(conventionPersonDto.getIsAttendTrainingProcedure())) {
//            //过滤是否参加培训流程
//            filterConvertDatasByTrainingProcedure(conventionPersonDto, convertDatas);
//        }
        return convertDatas;
    }

    /**
     * 获取bdcode
     *
     * @param id
     * @return
     */
    private String getBdCodeByStaffId(Long id) {
//        Example example = new Example(StaffBdCode.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkStaffId", id);
//        List<StaffBdCode> staffBdCodes = staffBdCodeMapper.selectByExample(example);
        List<StaffBdCode> staffBdCodes = staffBdCodeMapper.selectList(Wrappers.<StaffBdCode>lambdaQuery().eq(StaffBdCode::getFkStaffId, id));
        if (GeneralTool.isNotEmpty(staffBdCodes)) {
            return staffBdCodes.get(0).getBdCode();
        }
        return null;
    }

    /**
     * 获取bdcode
     *
     * @param staffIds
     * @return
     */
    private List<String> getBdCodeByStaffIds(List<Long> staffIds) {
        List<StaffBdCode> staffBdCodes = staffBdCodeMapper.selectList(Wrappers.<StaffBdCode>lambdaQuery().in(StaffBdCode::getFkStaffId, staffIds));
        if (GeneralTool.isEmpty(staffBdCodes)) {
            return Collections.emptyList();
        }
        return staffBdCodes.stream().map(StaffBdCode::getBdCode).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 根据报名名册ids获取参会人
     *
     * @param conventionRegistrationIds
     * @return
     */
    private List<Long> getConventionPersonIds(List<Long> conventionRegistrationIds) {
//        Example example = new Example(ConventionPersonRegistration.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andIn("fkConventionRegistrationId", conventionRegistrationIds);
//        List<ConventionPersonRegistration> conventionPersonRegistrations = conventionPersonRegistrationMapper.selectByExample(example);
        if (GeneralTool.isEmpty(conventionRegistrationIds)){
            return Lists.newArrayList(0L);
        }
        List<ConventionPersonRegistration> conventionPersonRegistrations = conventionPersonRegistrationMapper.selectList(Wrappers.<ConventionPersonRegistration>lambdaQuery().in(ConventionPersonRegistration::getFkConventionRegistrationId, conventionRegistrationIds));
        List<Long> conventionPersonIds = new ArrayList<>();
        if (GeneralTool.isNotEmpty(conventionPersonRegistrations)) {
            conventionPersonIds = conventionPersonRegistrations.stream().map(ConventionPersonRegistration::getFkConventionPersonId).collect(Collectors.toList());
        }else {
            conventionPersonIds.add(0L);
        }
        return conventionPersonIds;
    }

    /**
     * 根据institutionName获取报名名册id
     *
     * @param institutionName
     */
    private List<Long> getConventionRegistration(String institutionName) {
//        Example example = new Example(ConventionRegistration.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andLike("boothName", "%" + institutionName + "%");
//        List<ConventionRegistration> conventionRegistrations = conventionRegistrationMapper.selectByExample(example);
        List<ConventionRegistration> conventionRegistrations = conventionRegistrationMapper.selectList(Wrappers.<ConventionRegistration>lambdaQuery().in(ConventionRegistration::getBoothName, institutionName));

        //获取报名名册id
        if (GeneralTool.isNotEmpty(conventionRegistrations)) {
            return conventionRegistrations.stream().map(ConventionRegistration::getId).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    /**
     * @return void
     * @Description :设置bdName
     * @Param [conventionPersonDto]
     * <AUTHOR>
     */
    @Override
    public String getBdName(String bdCode) {
        //根据bdCode查找对应bd名称
        Long staffId = conventionPersonMapper.getBdName(bdCode);
        Result<String> result = permissionCenterClient.getStaffName(staffId);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            return result.getData();
        }
        return "";
    }

    @Override
    public Map<String,String> getBdNameMap(List<String> bdCodeList) {
        if (GeneralTool.isEmpty(bdCodeList)){
            return null;
        }
        HashMap<String, String> map = new HashMap<>();
        //根据bdCode查找对应bd名称
        List<StaffBdCode> staffBdCode = conventionPersonMapper.getBdNameList(bdCodeList);
        if (GeneralTool.isNotEmpty(staffBdCode)) {
            Set<Long> staffIds = staffBdCode.stream().map(StaffBdCode::getFkStaffId).collect(Collectors.toSet());

            Map<Long, String> staffNamesByIds = permissionCenterClient.getStaffNamesByIds(staffIds);
            if (GeneralTool.isNotEmpty(staffNamesByIds)){
                for (StaffBdCode bdCode : staffBdCode) {
                    String bdName = staffNamesByIds.get(bdCode.getFkStaffId());
                    map.put(bdCode.getBdCode(), bdName);
                }
            }
        }
        return map;
    }

    public Map<Long,String> gethoteMap(Set<Long> ids) {
        if (GeneralTool.isEmpty(ids)){
            return null;
        }
        HashMap<Long, String> map = new HashMap<>();
        //查询意向房型
        List<ConventionHotel> conventionHotels = conventionHotelMapper.selectBatchIds(ids);
        if (GeneralTool.isNotEmpty(conventionHotels)) {
            for (ConventionHotel conventionHotel : conventionHotels) {
                map.put(conventionHotel.getId(), conventionHotel.getHotel() + "(" +conventionHotel.getRoomType() + ")");
            }
        }
        return map;
    }

    @Override
    public List<ConventionPerson> getConventionPersonList(Long conventionId) {
//        Example example = new Example(ConventionPerson.class);
//        Example.Criteria criteria = example.createCriteria();
//        if (GeneralTool.isNotEmpty(conventionId)) {
//            criteria.andEqualTo("fkConventionId", conventionId);
//        }
        LambdaQueryWrapper<ConventionPerson> conventionPersonLambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(conventionId)) {
            conventionPersonLambdaQueryWrapper.eq(ConventionPerson::getFkConventionId, conventionId);
        }
        return conventionPersonMapper.selectList(conventionPersonLambdaQueryWrapper);
    }

    @Override
    public List<ConventionPerson> getPersonAndLockBedList(Long conventionId, Boolean bdCodeKey) {
//        Example example = new Example(ConventionPerson.class);
//        Example.Criteria criteria = example.createCriteria();
//        if (bdCodeKey) {
//            criteria.andEqualTo("bdCode", bdCode);
//        }
//        if (GeneralTool.isNotEmpty(conventionId)) {
//            criteria.andEqualTo("fkConventionId", conventionId);
//        }
//        List<ConventionPerson> conventionPersonList = conventionPersonMapper.selectByExample(example);

        LambdaQueryWrapper<ConventionPerson> conventionPersonLambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (bdCodeKey) {
            Long staffId = SecureUtil.getStaffId();
            List<Long> staffFollowerIds = Lists.newArrayList();
            List<Long> followerIds = CacheUtil.get(
                    STAFF_FOLLOWER_IDS_CACHE,
                    "staffId:",
                    staffId,
                    ()->permissionCenterClient.getStaffFollowerIds(staffId).getData()
            );
            if (GeneralTool.isNotEmpty(followerIds)) {
                staffFollowerIds.addAll(followerIds.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList()));
            }
            staffFollowerIds.add(staffId);
            List<String> bdCodes = getBdCodeByStaffIds(staffFollowerIds);
            if (GeneralTool.isNotEmpty(bdCodes)){
                conventionPersonLambdaQueryWrapper.in(ConventionPerson::getBdCode, bdCodes);
            }
        }
        if (GeneralTool.isNotEmpty(conventionId)) {
            conventionPersonLambdaQueryWrapper.eq(ConventionPerson::getFkConventionId, conventionId);
        }
        List<ConventionPerson> conventionPersonList = conventionPersonMapper.selectList(conventionPersonLambdaQueryWrapper);

        if (GeneralTool.isEmpty(conventionPersonList)){
            ConventionPerson conventionPerson = new ConventionPerson();
            conventionPerson.setNameChn("[系统] 锁定床位");
            conventionPerson.setName("[系统] 锁定床位");
            conventionPersonList.add(0, conventionPerson);
            return conventionPersonList;
        }

        Map<Long, String> institutionAgentMap = null;
        Map<Long, String> personAgentMap = null;
        if (GeneralTool.isNotEmpty(conventionPersonList)){
            List<ConventionPerson> institutionAgents = conventionPersonList.stream().filter(c -> ProjectExtraEnum.INSTITUTION_AGENT.key.equals(c.getType()) && GeneralTool.isEmpty(c.getCompany())).collect(Collectors.toList());
            List<ConventionPerson> agents = conventionPersonList.stream().filter(c -> ProjectExtraEnum.AGENT.key.equals(c.getType()) && GeneralTool.isEmpty(c.getCompany())).collect(Collectors.toList());

            //查询校代的
            if (GeneralTool.isNotEmpty(institutionAgents)){
                List<Long> institutionAgentIds = institutionAgents.stream().map(ConventionPerson::getId).collect(Collectors.toList());
                institutionAgentMap = conventionPersonRegistrationService.getConventionPersonProvideNameMapByIds(institutionAgentIds);
            }

            //代理
            if (GeneralTool.isNotEmpty(agents)){
                List<Long> personAgentIds = agents.stream().map(ConventionPerson::getId).collect(Collectors.toList());
                personAgentMap = conventionPersonAgentService.getConventionPersonAgentNameMapByIds(personAgentIds);
            }


        }

        for (ConventionPerson conventionPerson : conventionPersonList) {
            if (ProjectExtraEnum.INSTITUTION_AGENT.key.equals(conventionPerson.getType())&&GeneralTool.isEmpty(conventionPerson.getCompany())){
                if (GeneralTool.isNotEmpty(institutionAgentMap)&&GeneralTool.isNotEmpty(institutionAgentMap.get(conventionPerson.getId()))){
                    conventionPerson.setCompany(institutionAgentMap.get(conventionPerson.getId()));
                }
            }
            if (ProjectExtraEnum.AGENT.key.equals(conventionPerson.getType())&&GeneralTool.isEmpty(conventionPerson.getCompany())){
                if (GeneralTool.isNotEmpty(personAgentMap)&&GeneralTool.isNotEmpty(personAgentMap.get(conventionPerson.getId()))){
                    conventionPerson.setCompany(personAgentMap.get(conventionPerson.getId()));
                }
            }

        }

        ConventionPerson conventionPerson = new ConventionPerson();
        conventionPerson.setNameChn("[系统] 锁定床位");
        conventionPerson.setName("[系统] 锁定床位");
        conventionPersonList.add(0, conventionPerson);

//        ProjectExtraEnum.INSTITUTION_AGENT.key
        return conventionPersonList;
    }

    @Override
    public ConventionPersonVo findConventionPersonNameAndSex(Long id) {
        return conventionPersonMapper.findConventionPersonNameAndSex(id);
    }

    @Override
    public Boolean setSign(Long id, Boolean isAttend) {
        //TODO 改过
        //ConventionPerson conventionPerson = new ConventionPersonVo();
        ConventionPerson conventionPerson = new ConventionPerson();
        conventionPerson.setId(id);
        conventionPerson.setIsAttend(isAttend);
        int i = conventionPersonMapper.updateById(conventionPerson);
        if (i < 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
        return isAttend;
    }

    @Override
    public List<StaffBdCodeVo> getBdList(Long fkConventionId, Long fkAreaRegionId) {
        //根据当前的峰会活动ID获取峰会所属公司
        ConventionVo conventionById = conventionService.findConventionById(fkConventionId);
        //获取所属公司下面的所有BD
        List<StaffBdCodeVo> bdList = conventionPersonMapper.getBdList(conventionById.getFkCompanyId(),fkAreaRegionId);
        //获取所有人员ID
//        Set<Long> staffIds = bdList.stream().map(StaffBdCodeVo::getFkStaffId).collect(Collectors.toSet());
//        //根据人员ID集合获取所有人员姓名
//        Result<Map<Long, String>> staffNameResult = permissionCenterClient.getStaffNameMap(staffIds);
//        if (staffNameResult.isSuccess() && GeneralTool.isNotEmpty(staffNameResult)) {
//            Map<Long, String> staffNamesByIds = staffNameResult.getData();
//            for (StaffBdCodeVo staffBdCodeDto : bdList) {
//                staffBdCodeDto.setBdName(staffNamesByIds.get(staffBdCodeDto.getFkStaffId()));
//            }
//        }

        return bdList;
    }

    @Override
    public List<StaffBdCodeVo> getAllBdList(Long fkConventionId) {

        //根据当前的峰会活动ID获取峰会所属公司
        ConventionVo conventionById = conventionService.findConventionById(fkConventionId);
        //获取所属公司下面的所有BD
        List<StaffBdCodeVo> bdList = conventionPersonMapper.getAllBdList(conventionById.getFkCompanyId());
        Set<String> fkAreaRegionIds = bdList.stream().filter(staffBdCodeDto -> GeneralTool.isNotEmpty(staffBdCodeDto.getFkAreaRegionId())).map(StaffBdCodeVo::getFkAreaRegionId).collect(Collectors.toSet());
        Set<Long> areaRegionIds = new HashSet();
        for (String fkAreaRegionId : fkAreaRegionIds) {
            for (String sp :  fkAreaRegionId.split(","))
                areaRegionIds.add(Long.valueOf(sp));
        }
        // 根据区域id，获取区域名称
        Result regionDtoByIds = institutionCenterClient.getAreaRegionDtoByIds(areaRegionIds);
        Map<Long, AreaRegionVo> regionDtoByIdsMap = (Map<Long, AreaRegionVo>)regionDtoByIds.getData();
        for (StaffBdCodeVo staffBdCodeDto:bdList){
            if (GeneralTool.isEmpty(staffBdCodeDto.getFkAreaRegionId())) {
                staffBdCodeDto.setBdName(staffBdCodeDto.getBdName() + "【无绑定区域】");
                continue;
            }
            for (String id :  staffBdCodeDto.getFkAreaRegionId().split(",")){
                if (GeneralTool.isNotEmpty(regionDtoByIdsMap.get(Long.valueOf(id)))) {
                    AreaRegionVo areaRegionDto = regionDtoByIdsMap.get(Long.valueOf(id));
                    staffBdCodeDto.setBdName(staffBdCodeDto.getBdName() +"【"+ areaRegionDto.getNameChn() +" "+ areaRegionDto.getName()+"】");
                }
            }
        }
        return bdList;
    }


    /**
     * @Description: 根据手机号获取对应的峰会人员姓名
     * @Author: Jerry
     * @Date:10:47 2021/10/15
     */
    @Override
    public Map<String, String> getNamesByMobiles(Set<String> mobiles) {
        Map<String, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(mobiles)) {
            return map;
        }
//        Example example = new Example(ConventionPerson.class);
//        example.createCriteria().andIn("tel", mobiles).andEqualTo("fkConventionId", 9);
//        List<ConventionPerson> conventionPeople = conventionPersonMapper.selectByExample(example);
        List<ConventionPerson> conventionPeople = conventionPersonMapper.selectList(Wrappers.<ConventionPerson>lambdaQuery()
                .in(ConventionPerson::getTel, mobiles)
                .eq(ConventionPerson::getFkConventionId, 9));
        if (GeneralTool.isEmpty(conventionPeople)) {
            return map;
        }
        for (ConventionPerson conventionPerson : conventionPeople) {
            if (GeneralTool.isNotEmpty(conventionPerson.getName()) && !StringUtils.equals(conventionPerson.getName(), conventionPerson.getNameChn())) {
                StringBuilder sb = new StringBuilder();
                sb.append(conventionPerson.getNameChn()).append("（").append(conventionPerson.getName()).append("）");
                map.put(conventionPerson.getTel(), sb.toString());
            } else {
                map.put(conventionPerson.getTel(), conventionPerson.getNameChn());
            }
        }
        return map;
    }

    @Override
    public List<Long> getPersonIdsByName(String personName) {
        List<Long> personIds = conventionPersonMapper.getPersonIdsByName(personName);
        if (GeneralTool.isEmpty(personIds)) {
            personIds.add(0L);
        }
        return personIds;
    }

    @Override
    public List<Long> getPersonIdsByType(Integer personType) {
        List<Long> personIds = conventionPersonMapper.getPersonIdsByType(personType);
        //不设置会报错
        if (GeneralTool.isEmpty(personIds)) {
            personIds.add(0L);
        }
        return personIds;
    }


    @Override
    public void exportConventionPersonExcel(HttpServletResponse response, ConventionPersonDto conventionPersonDto) {
        String female = "女";
        String male = "男";
        List<ConventionPersonVo> conventionPersons = getConventionPersons(conventionPersonDto, null);
        //所有参会人ids
        List<Long> personIds = conventionPersons.stream().map(ConventionPersonVo::getId).collect(Collectors.toList());
        List<ConventionPersonExportVo> conventionPersonExportVos = new ArrayList<>();
//        Example example = new Example(ConventionProcedure.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkConventionId", conventionPersonDto.getFkConventionId());
//        example.orderBy("stepIndex").asc();
//        List<ConventionProcedure> conventionProcedures = conventionProcedureMapper.selectByExample(example);

        List<ConventionProcedure> conventionProcedures = conventionProcedureMapper.selectList(Wrappers.<ConventionProcedure>lambdaQuery()
                .in(ConventionProcedure::getFkConventionId, conventionPersonDto.getFkConventionId())
                .orderByAsc(ConventionProcedure::getStepIndex));

        Map<Long, String> conventionProcedureMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(conventionProcedures)) {
            //峰会所有流程名
            conventionProcedureMap = conventionProcedures.stream().collect(Collectors.toMap(ConventionProcedure::getId, ConventionProcedure::getSubject));
        }

        //参会人-流程map
        List<ConventionPersonVo> conventionPersonProceduresMapList = conventionPersonProcedureMapper.getConventionPersonProceduresMap(personIds);
        Map<Long, String> conventionPersonProceduresMap = new HashMap<>();
        for (ConventionPersonVo conventionPersonVo : conventionPersonProceduresMapList) {
            conventionPersonProceduresMap.put(conventionPersonVo.getId(), conventionPersonVo.getProcedureIds());
        }

        //参会人-展位名称map
        List<ConventionPersonVo> conventionPersonBooths = conventionPersonMapper.getConventionPersonBoothMap(personIds);
        Map<Long, String> conventionPersonBoothMap = new HashMap<>();
        for (ConventionPersonVo conventionPersonBooth : conventionPersonBooths) {
            conventionPersonBoothMap.put(conventionPersonBooth.getId(), conventionPersonBooth.getExcelBoothName());
        }

        //参会人类型
        ProjectExtraEnum[] conventionPersonType = ProjectExtraEnum.CONVENTION_PERSON_TYPE;
        Set<String> currencyTypeNums = new HashSet<>();
        Map<String, String> currencyNameMap = new HashMap<>();
        currencyTypeNums =
                conventionPersons.stream().map(ConventionPersonVo::getFkCurrencyTypeNumHotelExpense).filter(Objects::nonNull).filter(x -> x!="").collect(Collectors.toSet());
//        for (ConventionPersonVo conventionPersonDto : conventionPersons) {
//            if (GeneralTool.isNotEmpty(conventionPersonDto.getFkCurrencyTypeNumHotelExpense())) {
//                currencyTypeNums.add(conventionPersonDto.getFkCurrencyTypeNumHotelExpense());
//            }
//        }
        if (GeneralTool.isNotEmpty(currencyTypeNums)) {
            Result<Map<String, String>> result = financeCenterClient.getCurrencyNamesByNums(currencyTypeNums);
            if (result.isSuccess() && result.getData() != null) {
                currencyNameMap = result.getData();
            }
        }
        //设置性别和人员类型名字
        for (ConventionPersonVo conventionPersonVo : conventionPersons) {
            ConventionPersonExportVo conventionPersonExportVo = BeanCopyUtils.objClone(conventionPersonVo, ConventionPersonExportVo::new);
            if (GeneralTool.isEmpty(conventionPersonVo.getIsVip())){
                conventionPersonExportVo.setVip("否");
            }else {
                if (conventionPersonVo.getIsVip()){
                    conventionPersonExportVo.setVip("是");
                }else {
                    conventionPersonExportVo.setVip("否");
                }
            }
            if (GeneralTool.isNotEmpty(conventionPersonVo.getGender())) {
                if (conventionPersonVo.getGender() == 0) {
                    conventionPersonExportVo.setGenderName(female);
                } else {
                    conventionPersonExportVo.setGenderName(male);
                }
            }
            for (ProjectExtraEnum projectExtraEnum : conventionPersonType) {
                if (conventionPersonVo.getType().equals(projectExtraEnum.key)) {
                    conventionPersonExportVo.setTypeName(projectExtraEnum.value);
                    break;
                }
            }
            //设置住房费用类型
            if (GeneralTool.isNotEmpty(conventionPersonVo.getHotelFeeType())) {
                if (conventionPersonVo.getHotelFeeType() == 0) {
                    conventionPersonExportVo.setHotelFeeTypeName("公费");
                } else if (conventionPersonVo.getHotelFeeType() == 1) {
                    conventionPersonExportVo.setHotelFeeTypeName("自费");
                }
            }
            //设置货币类型
            if (GeneralTool.isNotEmpty(conventionPersonVo.getFkCurrencyTypeNumHotelExpense())) {
                conventionPersonExportVo.setCurrencyTypeName(currencyNameMap.get(conventionPersonVo.getFkCurrencyTypeNumHotelExpense()));
            }
            //设置流程名称
            String procedureStr = conventionPersonProceduresMap.get(conventionPersonVo.getId());
            if (GeneralTool.isNotEmpty(procedureStr)) {
                List<String> strList = Arrays.asList(procedureStr.split(","));
                List<Long> procedureIds = strList.stream().map(Long::valueOf).collect(Collectors.toList());
//                Example example1 = new Example(ConventionProcedure.class);
//                Example.Criteria criteria1 = example1.createCriteria();
//                criteria1.andIn("id", procedureIds);
//                example1.orderBy("stepIndex").asc();
//                List<ConventionProcedure> conventionProcedureList = conventionProcedureMapper.selectByExample(example1);

                List<ConventionProcedure> conventionProcedureList = conventionProcedureMapper.selectList(Wrappers.<ConventionProcedure>lambdaQuery()
                        .in(ConventionProcedure::getId, procedureIds)
                        .orderByAsc(ConventionProcedure::getStepIndex));

                StringJoiner stringJoiner = new StringJoiner(",");
                for (ConventionProcedure conventionProcedure : conventionProcedureList) {
                    stringJoiner.add(conventionProcedureMap.get(conventionProcedure.getId()));
                }
                conventionPersonExportVo.setJoinProcedure(stringJoiner.toString());
            }

            //设置展位名称
            String boothStr = conventionPersonBoothMap.get(conventionPersonVo.getId());
            if (GeneralTool.isNotEmpty(boothStr)) {
                conventionPersonExportVo.setBoothName(boothStr);
            }
            conventionPersonExportVos.add(conventionPersonExportVo);
        }

        FileUtils.exportExcelNotWrapText(response, conventionPersonExportVos, "ConventionPerson", ConventionPersonExportVo.class);

    }


    /**
     * @Description: 根据名称模糊搜索参展人员ids
     * @Author: Jerry
     * @Date:16:24 2021/9/15
     */
    @Override
    public Set<Long> getConventionPersonIdsByName(String name) {
//        Example example = new Example(ConventionPerson.class);
//        example.createCriteria().orLike("name", name).orLike("nameChn", name);
//        List<ConventionPerson> conventionPeople = conventionPersonMapper.selectByExample(example);

        List<ConventionPerson> conventionProcedures = conventionPersonMapper.selectList(Wrappers.<ConventionPerson>lambdaQuery()
                .like(ConventionPerson::getName, name)
                .or().like(ConventionPerson::getNameChn, name));
        if (GeneralTool.isEmpty(conventionProcedures)) {
            return null;
        }
        return conventionProcedures.stream().map(ConventionPerson::getId).collect(Collectors.toSet());
    }


    /**
     * @Description: 根据人员ids获取对象
     * @Author: Jerry
     * @Date:17:04 2021/9/15
     */
    @Override
    public Map<Long, ConventionPerson> getConventionPersonByConventionPersonIds(Set<Long> conventionPersonIds) {
        Map<Long, ConventionPerson> map = new HashMap<>();
        if (GeneralTool.isEmpty(conventionPersonIds)) {
            return map;
        }
//        Example example = new Example(ConventionPerson.class);
//        example.createCriteria().andIn("id", conventionPersonIds);
//        List<ConventionPerson> conventionPeople = conventionPersonMapper.selectByExample(example);

        List<ConventionPerson> conventionPeople = conventionPersonMapper.selectList(Wrappers.<ConventionPerson>lambdaQuery()
                .in(ConventionPerson::getId, conventionPersonIds));
        if (GeneralTool.isEmpty(conventionPeople)) {
            return map;
        }
        for (ConventionPerson conventionPerson : conventionPeople) {
            map.put(conventionPerson.getId(), conventionPerson);
        }
        return map;
    }

    @Override
    public List<Long> getPersonIdsByBdCode(String bdCode) {
        List<Long> personIds = conventionPersonMapper.getPersonIdsByBdCode(bdCode);
        if (GeneralTool.isEmpty(personIds)) {
            personIds.add(0L);
        }
        return personIds;
    }

    @Override
    public List<ConventionPersonVo> findConventionPersonNameAndSexByConventionId(Long conventionId, List<String> bdCodes) {
        return conventionPersonMapper.findConventionPersonNameAndSexByConventionId(conventionId,bdCodes);
    }

    @Override
    public ConventionPerson getConventionPersonById(Long conventionPersonId) {
        return conventionPersonMapper.selectById(conventionPersonId);
    }

    @Override
    public List<Long> getRepeatPerson(Integer searchRepeatPerson,Long conventionId,String tableTypeKey) {
        return conventionPersonMapper.getRepeatPerson(searchRepeatPerson,conventionId,tableTypeKey);
    }

    @Override
    public ConventionPersonVo findPersonById(Long id) {
        return conventionPersonMapper.findPersonById(id);
    }

    @Override
    public List<ConventionPerson> getPersonForTableList(Long id, String type) {
        List<ConventionPerson> conventionPersonList = conventionPersonMapper.getPersonForTableList(id, type);

        if (GeneralTool.isEmpty(conventionPersonList)){
            return conventionPersonList;
        }

        Map<Long, String> institutionAgentMap = null;
        Map<Long, String> personAgentMap = null;
        if (GeneralTool.isNotEmpty(conventionPersonList)){
            List<ConventionPerson> institutionAgents = conventionPersonList.stream().filter(c -> ProjectExtraEnum.INSTITUTION_AGENT.key.equals(c.getType()) && GeneralTool.isEmpty(c.getCompany())).collect(Collectors.toList());
            List<ConventionPerson> agents = conventionPersonList.stream().filter(c -> ProjectExtraEnum.AGENT.key.equals(c.getType()) && GeneralTool.isEmpty(c.getCompany())).collect(Collectors.toList());
            //查询校代的
            if (GeneralTool.isNotEmpty(institutionAgents)){
                List<Long> institutionAgentIds = institutionAgents.stream().map(ConventionPerson::getId).collect(Collectors.toList());
                institutionAgentMap = conventionPersonRegistrationService.getConventionPersonProvideNameMapByIds(institutionAgentIds);
            }

            //代理
            if (GeneralTool.isNotEmpty(agents)){
                List<Long> personAgentIds = agents.stream().map(ConventionPerson::getId).collect(Collectors.toList());
                personAgentMap = conventionPersonAgentService.getConventionPersonAgentNameMapByIds(personAgentIds);
            }
        }

        for (ConventionPerson conventionPerson : conventionPersonList) {
            if (ProjectExtraEnum.INSTITUTION_AGENT.key.equals(conventionPerson.getType())&&GeneralTool.isEmpty(conventionPerson.getCompany())){
                if (GeneralTool.isNotEmpty(institutionAgentMap)&&GeneralTool.isNotEmpty(institutionAgentMap.get(conventionPerson.getId()))){
                    conventionPerson.setCompany(institutionAgentMap.get(conventionPerson.getId()));
                }
            }
            if (ProjectExtraEnum.AGENT.key.equals(conventionPerson.getType())&&GeneralTool.isEmpty(conventionPerson.getCompany())){
                if (GeneralTool.isNotEmpty(personAgentMap)&&GeneralTool.isNotEmpty(personAgentMap.get(conventionPerson.getId()))){
                    conventionPerson.setCompany(personAgentMap.get(conventionPerson.getId()));
                }
            }

        }

        return conventionPersonList;
    }

    /**
     * 根据峰会id和桌子类型 获取已安排桌子人员
     *
     * @Date 15:21 2021/6/29
     * <AUTHOR>
     */
    @Override
    public List<ConventionPerson> getPersonForTable(Long id, String type) {
        return conventionPersonMapper.getPersonForTable(id, type);
    }

    @Override
    public List<ConventionPersonVo> getNotArrangedPersonList(IPage<ConventionPersonVo> iPage, List<Long> personIds, ConventionPersonDto conventionPersonDto) {
        return conventionPersonMapper.getNotArrangedPersonList(iPage, personIds, conventionPersonDto);
    }

    @Override
    public List<ConventionPersonVo> getRoomNotArrangedPersonList(IPage<ConventionPersonVo> conventionPersonDtoIPage, List<Long> roomIds, ConventionPersonDto conventionPersonDto) {
        if (GeneralTool.isNotEmpty(conventionPersonDto.getBdCodeKey())&& conventionPersonDto.getBdCodeKey()) {
            List<Long> staffFollowerIds = verifyStudentOfferItemUtils.getStaffFollowerIds(SecureUtil.getStaffId());
            List<String> bdCodes = getBdCodeByStaffIds(staffFollowerIds);
            if (GeneralTool.isNotEmpty(bdCodes)){
                conventionPersonDto.setBdCodes(bdCodes);
            }
        }
//        if (GeneralTool.isNotEmpty(conventionPersonDto.getBdCodeKey())) {
//            if (conventionPersonDto.getBdCodeKey()) {
//                String bdCode = getBdCodeByStaffId(GetAuthInfo.getStaffId());
//                conventionPersonDto.setBdCode(bdCode);
//            }
//        }
        if (GeneralTool.isNotEmpty(conventionPersonDto.getNameKey())) {
            conventionPersonDto.setNameKey("%" + conventionPersonDto.getNameKey() + "%");
        }
        return conventionPersonMapper.getRoomNotArrangedPersonList(conventionPersonDtoIPage, roomIds, conventionPersonDto);
    }

    @Override
    public List<String> getBdCode(String bdNameKey) {
//        List<Long> staffIds = permissionCenterClient.getStaffIdByName(bdNameKey);
        Result<List<Long>> result = permissionCenterClient.getStaffIdsByNameKeyOrEnNameKey(bdNameKey);
        if (result.isSuccess() && CollectionUtil.isNotEmpty(result.getData())) {
            return conventionPersonMapper.getBdCode(result.getData());
        }

//        List<String> bdCodeList = conventionPersonMapper.getBdCode("%" + bdNameKey + "%");
//        if (GeneralTool.isEmpty(bdCodeList)) {
//            bdCodeList.add("0");
//        }
        return Collections.emptyList();
    }

    /**
     * 关联表的保存
     */
    private void insertTable(Long id, ConventionPersonDto conventionPersonDto) {
        switch (conventionPersonDto.getType()) {
            case 0:
                //0校方-r_convention_person_registration 中间表对象
                ConventionPersonRegistration conventionPersonRegistration = new ConventionPersonRegistration();
                //参展人员id为刚插入成功返回的id
                conventionPersonRegistration.setFkConventionPersonId(id);
                //所属报名名册的id
                conventionPersonRegistration.setFkConventionRegistrationId(conventionPersonDto.getSelectProjectIds().get(0));
                if (GeneralTool.isNotEmpty(conventionPersonRegistration.getFkConventionRegistrationId())) {
                    utilService.updateUserInfoToEntity(conventionPersonRegistration);
                    conventionPersonRegistrationMapper.insert(conventionPersonRegistration);
                }
                break;
            case 1:
                //1代理-r_convention_person_agent 中间表对象
                ConventionPersonAgent conventionPersonAgent = new ConventionPersonAgent();
                //参展人员id为刚插入成功返回的id
                conventionPersonAgent.setFkConventionPersonId(id);
                //所属代理的id
                conventionPersonAgent.setFkAgentId(conventionPersonDto.getSelectProjectIds().get(0));
                if (GeneralTool.isNotEmpty(conventionPersonAgent.getFkAgentId())) {
                    utilService.updateUserInfoToEntity(conventionPersonAgent);
                    conventionPersonAgentMapper.insert(conventionPersonAgent);
                }
                break;
            case 2:
                int a = 2;
                if (conventionPersonDto.getSelectProjectIds().size() < a) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
                }
                //2嘉宾-r_convention_person_institution & r_convention_person_institution_provider 中间表对象
                ConventionPersonInstitutionProvider conventionPersonInstitutionProvider = new ConventionPersonInstitutionProvider();
                ConventionPersonInstitution conventionPersonInstitution = new ConventionPersonInstitution();
                //参展人员id为刚插入成功返回的id
                conventionPersonInstitutionProvider.setFkConventionPersonId(id);
                conventionPersonInstitution.setFkConventionPersonId(id);
                //所属学校提供商的id
                conventionPersonInstitutionProvider.setFkInstitutionProviderId(conventionPersonDto.getSelectProjectIds().get(0));
                //所属学校的id
                conventionPersonInstitution.setFkInstitutionId(conventionPersonDto.getSelectProjectIds().get(1));

                if (GeneralTool.isNotEmpty(conventionPersonInstitutionProvider.getFkInstitutionProviderId())) {
                    utilService.updateUserInfoToEntity(conventionPersonInstitutionProvider);
                    conventionPersonInstitutionProviderMapper.insert(conventionPersonInstitutionProvider);
                }

                if (GeneralTool.isNotEmpty(conventionPersonInstitution.getFkInstitutionId())) {
                    utilService.updateUserInfoToEntity(conventionPersonInstitution);
                    conventionPersonInstitutionMapper.insert(conventionPersonInstitution);
                }
                break;
            case 3:
                //3员工-r_convention_person_staff 中间表对象
                ConventionPersonStaff conventionPersonStaff = new ConventionPersonStaff();
                //参展人员id为刚插入成功返回的id
                conventionPersonStaff.setFkConventionPersonId(id);
                //所属员工id
                conventionPersonStaff.setFkStaffId(conventionPersonDto.getSelectProjectIds().get(0));
                if (GeneralTool.isNotEmpty(conventionPersonStaff.getFkStaffId())) {
                    utilService.updateUserInfoToEntity(conventionPersonStaff);
                    conventionPersonStaffMapper.insert(conventionPersonStaff);
                }
                break;
            case 4:

                break;
            default:
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
    }

    /**
     * 关联表的删除
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteTable(Long id, ConventionPerson conventionPerson) {
        Integer type = conventionPerson.getType();
        switch (type) {
            case 0:
//                Example example = new Example(ConventionPersonRegistration.class);
//                Example.Criteria criteria = example.createCriteria();
//                criteria.andEqualTo("fkConventionPersonId", id);
//                conventionPersonRegistrationMapper.deleteByExample(example);

                conventionPersonRegistrationMapper.delete(Wrappers.<ConventionPersonRegistration>lambdaQuery().eq(ConventionPersonRegistration::getFkConventionPersonId, id));
                break;
            case 1:
//                Example example1 = new Example(ConventionPersonAgent.class);
//                Example.Criteria criteria1 = example1.createCriteria();
//                criteria1.andEqualTo("fkConventionPersonId", id);
//                conventionPersonAgentMapper.deleteByExample(example1);

                conventionPersonAgentMapper.delete(Wrappers.<ConventionPersonAgent>lambdaQuery().eq(ConventionPersonAgent::getFkConventionPersonId, id));
                break;
            case 2:
//                Example example21 = new Example(ConventionPersonInstitution.class);
//                Example.Criteria criteria21 = example21.createCriteria();
//                criteria21.andEqualTo("fkConventionPersonId", id);
//                conventionPersonInstitutionMapper.deleteByExample(example21);

                conventionPersonInstitutionMapper.delete(Wrappers.<ConventionPersonInstitution>lambdaQuery().eq(ConventionPersonInstitution::getFkConventionPersonId, id));


//                Example example22 = new Example(ConventionPersonInstitutionProvider.class);
//                Example.Criteria criteria22 = example22.createCriteria();
//                criteria22.andEqualTo("fkConventionPersonId", id);
//                conventionPersonInstitutionProviderMapper.deleteByExample(example22);

                conventionPersonInstitutionProviderMapper.delete(Wrappers.<ConventionPersonInstitutionProvider>lambdaQuery().eq(ConventionPersonInstitutionProvider::getFkConventionPersonId, id));

                break;
            case 3:
//                Example example3 = new Example(ConventionPersonStaff.class);
//                Example.Criteria criteria3 = example3.createCriteria();
//                criteria3.andEqualTo("fkConventionPersonId", id);
//                conventionPersonStaffMapper.deleteByExample(example3);

                conventionPersonStaffMapper.delete(Wrappers.<ConventionPersonStaff>lambdaQuery().eq(ConventionPersonStaff::getFkConventionPersonId, id));

                break;
            case 4:

                break;
            default:
                throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
    }

    /**
     * 查出的参展人员数据各自的类型 查找关联表 得到绑定联系的具体信息
     */
    private void getInfo(ConventionPersonVo conventionPersonVo) {
        Long id = conventionPersonVo.getId();
        List<Long> ids = new ArrayList<>();
        switch (conventionPersonVo.getType()) {
            case 0:
                //关联表得到报名id
                Long conventionRegistrationId = conventionPersonRegistrationMapper.getConventionRegistrationId(id);
                ids.add(conventionRegistrationId);
                conventionPersonVo.setSelectProjectIds(ids);
                //报名id得到展位名称
                String boothName = conventionRegistrationMapper.getBoothNameById(conventionRegistrationId);
                conventionPersonVo.setResult(boothName);
                break;

            case 1:
                //关联表得到代理id
                Long agentId = conventionPersonAgentMapper.getAgentId(id);
                ids.add(agentId);
                conventionPersonVo.setSelectProjectIds(ids);
                //代理id得到代理名称
                String agentName = agentMapper.getAgentNameById(agentId);
                conventionPersonVo.setResult(agentName);
                break;

            case 2:
                //关联表得到学校提供商id
                Long institutionProviderId = conventionPersonInstitutionProviderMapper.getInstitutionProviderId(id);
                ids.add(institutionProviderId);
                conventionPersonVo.setSelectProjectIds(ids);
                //关联表得到学校id
                Long institutionId = conventionPersonInstitutionMapper.getInstitutionId(id);
                ids.add(institutionId);
                conventionPersonVo.setSelectProjectIds(ids);
                //学校id得到学校名称
//                String institutionName = institutionCenterClient.getInstitutionName(institutionId);
                String institutionName = "";
                Result<String> institutionNameResult = institutionCenterClient.getInstitutionName(institutionId);
                if (institutionNameResult.isSuccess() && GeneralTool.isNotEmpty(institutionNameResult.getData())) {
                    institutionName = institutionNameResult.getData();
                }
                //学校提供商id得到学校提供商名称

                String institutionProviderName = "";
                Result<String> providerNameResult = institutionCenterClient.getInstitutionProviderName(institutionProviderId);
                if (providerNameResult.isSuccess() && GeneralTool.isNotEmpty(providerNameResult.getData())) {
                    institutionProviderName = providerNameResult.getData();
                }
                if (GeneralTool.isNotEmpty(institutionName) && GeneralTool.isNotEmpty(institutionProviderName)) {
                    String result = institutionProviderName + "/" + institutionName;
                    conventionPersonVo.setResult(result);
                }
                break;

            case 3:
                //关联表得到员工id
                Long staffId = conventionPersonStaffMapper.getStaffId(id);
                ids.add(staffId);
                conventionPersonVo.setSelectProjectIds(ids);
                //员工id得到员工姓名
                Result<String> result = permissionCenterClient.getStaffName(staffId);
                if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                    String staffName = result.getData();
                    System.out.println(staffName);
                    conventionPersonVo.setResult(staffName);
                }

                break;
            case 4:

                break;
            default:
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
    }

    private String validateAdd(ConventionPersonDto conventionPersonDto) {
        List<ConventionPerson> list = getConventionPsersonList(conventionPersonDto);
        if (GeneralTool.isNotEmpty(list)) {
            StringBuilder sb = new StringBuilder();
            String resultMsg = null;
            for (ConventionPerson conventionPerson : list) {
                resultMsg = setValidMsg(conventionPersonDto, sb, conventionPerson);
            }
            return resultMsg;
        }
        return null;
    }

    private String validateUpdate(ConventionPersonDto conventionPersonDto) {
        List<ConventionPerson> list = getConventionPsersonList(conventionPersonDto);
        if (GeneralTool.isNotEmpty(list)) {
            StringBuilder sb = new StringBuilder();
            String resultMsg = null;
            for (ConventionPerson conventionPerson : list) {
                if (!conventionPersonDto.getId().equals(conventionPerson.getId())) {
                    resultMsg = setValidMsg(conventionPersonDto, sb, conventionPerson);
                }
            }
            return resultMsg;
        }
        return null;
    }

    /**
     * @return java.lang.String
     * @Description :设置返回验证信息
     * @Param [conventionPersonDto, sb, conventionPerson]
     * <AUTHOR>
     */
    private String setValidMsg(ConventionPersonDto conventionPersonDto, StringBuilder sb, ConventionPerson conventionPerson) {
        if (conventionPerson.getTel().equals(conventionPersonDto.getTel())) {
            sb.append("电话已存在，");
        }
        if (GeneralTool.isNotEmpty(conventionPersonDto.getNum())){
            if (conventionPerson.getNum().equals(conventionPersonDto.getNum())) {
                sb.append("参会编号已存在，");
            }
        }
        return sub(sb);
    }

    /**
     * @return java.lang.String
     * @Description :截取字符串逗号
     * @Param [sb]
     * <AUTHOR>
     */
    private String sub(StringBuilder sb) {
        if (GeneralTool.isEmpty(sb)) {
            return null;
        }
        String substring = null;
        int i = sb.lastIndexOf("，");
        if (i != -1) {
            substring = sb.substring(0, i);
        }
        return substring;
    }

    /**
     * @return java.util.List<com.get.salecenter.entity.ConventionPerson>
     * @Description :根据验证条件获取list
     * @Param [conventionPersonDto]
     * <AUTHOR>
     */
    private List<ConventionPerson> getConventionPsersonList(ConventionPersonDto conventionPersonDto) {
//        Example example = new Example(ConventionPerson.class);
//        Example.Criteria criteria = example.createCriteria();
//        Example.Criteria criteria1 = example.createCriteria();
//        criteria.andEqualTo("fkConventionId", conventionPersonDto.getFkConventionId());
//        criteria1.andEqualTo("tel", conventionPersonDto.getTel());
//        criteria1.orEqualTo("num", conventionPersonDto.getNum());
//        example.and(criteria1);

        LambdaQueryWrapper<ConventionPerson> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ConventionPerson::getFkConventionId, conventionPersonDto.getFkConventionId());
        lambdaQueryWrapper.and(wrapper -> wrapper.eq(ConventionPerson::getTel, conventionPersonDto.getTel()).or().eq(ConventionPerson::getNum, conventionPersonDto.getNum()));
        return this.conventionPersonMapper.selectList(lambdaQueryWrapper);
    }

    @Override
    public List<ConventionPersonVotingRankingVo> helpVotingRanking(ConventionPersonVotingRankingDto conventionPersonVotingRankingDto, Page page) {
        Set<Long> bdIds = null;
        List<String> bdCodeList = null;
        //查询条件-跟进BD名称
        if (GeneralTool.isNotEmpty(conventionPersonVotingRankingDto.getBdName())) {
            //根据bdName模糊查询bdNumList
            bdCodeList = getBdCode(conventionPersonVotingRankingDto.getBdName());
        }

        //总
        List<ConventionPersonVotingRankingVo> listRanking = conventionPersonMapper.getRankingList();
        List<ConventionPersonVotingRankingVo> listPage = new ArrayList<>();
        //分页
        IPage<ConventionPersonVotingRankingVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        listPage = conventionPersonMapper.getHelpRankingList(iPage, bdCodeList, conventionPersonVotingRankingDto.getName(), conventionPersonVotingRankingDto.getTel());
        page.setAll((int) iPage.getTotal());
        for (int i = 0; i < listRanking.size(); i++) {
            for(int j = 0; j < listPage.size(); j++){
                if(listRanking.get(i).getFkConventionPersonId().equals(listPage.get(j).getFkConventionPersonId())){
                    listPage.get(j).setBdName(getBdName(listPage.get(j).getBdCode()));
                    listPage.get(j).setRanking(i+1);
                }
            }
        }
        return  listPage;
    }

    @Override
    public List<LikeCollectionActivityVo> likeCollectionActivity(LikeCollectionActivitySearchDto likeCollectionActivitySearchVo, Page page) {
        IPage<LikeCollectionActivityVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<LikeCollectionActivityVo> likeCollectionActivityList = baseMapper.getLikeCollectionActivityList(iPage, likeCollectionActivitySearchVo);
        //获取附件
        if (GeneralTool.isNotEmpty(likeCollectionActivityList)){
            List<SaleMediaAndAttached> saleMediaAndAttacheds = new ArrayList<>();
            likeCollectionActivityList.forEach(lcDto -> {
                MediaAndAttachedVo mediaAndAttached = baseMapper.getMediaAndAttached(lcDto.getId(), FileTypeEnum.VOTING_LIKE_PIC.key, TableEnum.VOTING_LIKE_COUNT.key);
                //TODO 复制过去
                //SaleMediaAndAttached saleMediaAndAttached=BeanCopyUtils.objClone(mediaAndAttached, SaleMediaAndAttached::new);
                SaleMediaAndAttached saleMediaAndAttached = new SaleMediaAndAttached();
                BeanCopyUtils.copyProperties(mediaAndAttached,saleMediaAndAttached);
                saleMediaAndAttacheds.add(saleMediaAndAttached);
            });
            List<MediaAndAttachedVo> fileMedias = getFileMedia(saleMediaAndAttacheds);
            Map<Long, List<MediaAndAttachedVo>> map = new HashMap<>();
            if (GeneralTool.isNotEmpty(fileMedias)) {
                map = fileMedias.stream().collect(Collectors.groupingBy(MediaAndAttachedVo::getFkTableId));
            }
            Map<Long, List<MediaAndAttachedVo>> finalMap = map;
            likeCollectionActivityList.forEach(lcDto -> {
                lcDto.setMediaAndAttachedDtos(finalMap.get(lcDto.getId()));
            });
        }
        page.setAll((int) iPage.getTotal());
        return likeCollectionActivityList;
    }

    @Override
    public Set<ConventionSelectVo> conventionSelect() {
        Set<ConventionSelectVo> conventionSelectVos = baseMapper.getConventionSelect();

        return conventionSelectVos;
    }

    @Override
    public void likeConventionApproval(Long id,Integer status) {
       baseMapper.likeConventionApproval(id,status);
    }

    @Override
    public void likeConventionEdit(LikeConventionEditDto likeConventionEditDto) {
        baseMapper.likeConventionEdit(likeConventionEditDto);
    }

    /**
     * 曾参加过峰会的同名员工列表
     * @param type
     * @param fkCompanyId
     * @param name
     * @return
     */
    @Override
    public List<DuplicatedConventionPersonVo> getDuplicatedConventionPersonsInfo(Integer type, Long fkCompanyId, String name) {

        List<Convention> conventions = conventionService.list(Wrappers.lambdaQuery(conventionService.getEntityClass())
                .eq(Convention::getFkCompanyId, fkCompanyId));

        if (GeneralTool.isEmpty(conventions)){
            return Collections.emptyList();
        }

        List<Long> conventionIds = conventions.stream().map(Convention::getId).collect(Collectors.toList());
        Map<Long, String> conventionNameMap = conventions.stream().collect(Collectors.toMap(Convention::getId, Convention::getThemeName));

        List<ConventionPerson> conventionPersonList = conventionPersonMapper.selectList(Wrappers.lambdaQuery(ConventionPerson.class)
                .eq(ConventionPerson::getType, type)
                .and(wrapper->
                        wrapper.eq(ConventionPerson::getName, name)
                        .or()
                        .eq(ConventionPerson::getNameChn, name))
                .in(ConventionPerson::getFkConventionId, conventionIds)
                .orderByDesc(ConventionPerson::getFkConventionId));

        if (GeneralTool.isEmpty(conventionPersonList)){
            return Collections.emptyList();
        }

        List<Long> personIds = conventionPersonList.stream().map(ConventionPerson::getId).collect(Collectors.toList());

//                INSTITUTION_AGENT(0, "校代"),
//                AGENT(1, "代理"),
//                GUEST(2, "嘉宾"),
//                STAFF(3, "员工"),
//                WORKING_PERSON(4, "工作人员")

        Map<Long, String> personBoothNameMap = Maps.newHashMap();
        if (ProjectExtraEnum.INSTITUTION_AGENT.key.equals(type)){
            List<ConventionPersonRegistration> conventionPersonRegistrations = conventionPersonRegistrationMapper.selectList(Wrappers.lambdaQuery(ConventionPersonRegistration.class)
                    .in(ConventionPersonRegistration::getFkConventionPersonId, personIds));

            if (GeneralTool.isNotEmpty(conventionPersonRegistrations)){
                Set<Long> conventionPersonRegistrationIds = conventionPersonRegistrations.stream().map(ConventionPersonRegistration::getFkConventionRegistrationId).collect(Collectors.toSet());
                List<ConventionRegistration> conventionRegistrations = conventionRegistrationMapper.selectBatchIds(conventionPersonRegistrationIds);
                Map<Long, String> registrationBoothNameMap = conventionRegistrations.stream().collect(Collectors.toMap(ConventionRegistration::getId, ConventionRegistration::getBoothName));

                for (ConventionPersonRegistration conventionPersonRegistration : conventionPersonRegistrations) {
                    String boothName = registrationBoothNameMap.get(conventionPersonRegistration.getFkConventionRegistrationId());
                    if (GeneralTool.isNotEmpty(boothName)){
                        personBoothNameMap.put(conventionPersonRegistration.getFkConventionPersonId(),boothName);
                    }
                }
            }
        }

        Map<Long, String> personAgentNameMap = Maps.newHashMap();
        if (ProjectExtraEnum.AGENT.key.equals(type)){
            List<ConventionPersonAgent> conventionPersonAgents = conventionPersonAgentMapper.selectList(Wrappers.lambdaQuery(ConventionPersonAgent.class)
                    .in(ConventionPersonAgent::getFkConventionPersonId, personIds));

            if (GeneralTool.isNotEmpty(conventionPersonAgents)){
                Set<Long> agentIds = conventionPersonAgents.stream().map(ConventionPersonAgent::getFkAgentId).collect(Collectors.toSet());
                List<Agent> agents = agentMapper.selectBatchIds(agentIds);
                Map<Long, String> agentNameMap = agents.stream().collect(Collectors.toMap(Agent::getId, Agent::getName));

                for (ConventionPersonAgent conventionPersonAgent : conventionPersonAgents) {
                    String agentName = agentNameMap.get(conventionPersonAgent.getFkAgentId());
                    if (GeneralTool.isNotEmpty(agentName)){
                        personAgentNameMap.put(conventionPersonAgent.getFkConventionPersonId(),agentName);
                    }
                }
            }
        }

        Map<Long, String> personInstitutionProviderNameMap = Maps.newHashMap();
        if (ProjectExtraEnum.GUEST.key.equals(type)){
            List<ConventionPersonInstitutionProvider> conventionPersonInstitutionProviders = conventionPersonInstitutionProviderMapper.selectList(Wrappers.lambdaQuery(ConventionPersonInstitutionProvider.class)
                    .in(ConventionPersonInstitutionProvider::getFkConventionPersonId, personIds));

            if (GeneralTool.isNotEmpty(conventionPersonInstitutionProviders)){
                Set<Long> fkInstitutionProviderIds = conventionPersonInstitutionProviders.stream().map(ConventionPersonInstitutionProvider::getFkInstitutionProviderId).collect(Collectors.toSet());
                Map<Long, String> institutionProviderNameMap = institutionCenterClient.getInstitutionProviderNamesByIds(fkInstitutionProviderIds).getData();

                for (ConventionPersonInstitutionProvider conventionPersonInstitutionProvider : conventionPersonInstitutionProviders) {
                    String institutionProviderName = institutionProviderNameMap.get(conventionPersonInstitutionProvider.getFkInstitutionProviderId());
                    if (GeneralTool.isNotEmpty(institutionProviderName)){
                        personInstitutionProviderNameMap.put(conventionPersonInstitutionProvider.getFkConventionPersonId(),institutionProviderName);
                    }
                }
            }
        }


        List<DuplicatedConventionPersonVo> duplicatedConventionPersonVos = BeanCopyUtils.copyListProperties(conventionPersonList, DuplicatedConventionPersonVo::new);

        for (DuplicatedConventionPersonVo duplicatedConventionPersonVo : duplicatedConventionPersonVos) {
            String personName = Stream.of(duplicatedConventionPersonVo.getName(), duplicatedConventionPersonVo.getNameChn()).filter(Objects::nonNull).collect(Collectors.joining(","));

            StringBuilder fullName = new StringBuilder();
            String tail = "";
            if (ProjectExtraEnum.INSTITUTION_AGENT.key.equals(type)){
                fullName.append("【").append(ProjectExtraEnum.INSTITUTION_AGENT.value).append("】").append(personName);
                tail = personBoothNameMap.get(duplicatedConventionPersonVo.getId());
            }
            if (ProjectExtraEnum.AGENT.key.equals(type)){
                fullName.append("【").append(ProjectExtraEnum.AGENT.value).append("】").append(personName);
                tail = personAgentNameMap.get(duplicatedConventionPersonVo.getId());
            }
            if (ProjectExtraEnum.GUEST.key.equals(type)){
                fullName.append("【").append(ProjectExtraEnum.GUEST.value).append("】").append(personName);
                tail = personInstitutionProviderNameMap.get(duplicatedConventionPersonVo.getId());
            }
            if (ProjectExtraEnum.STAFF.key.equals(type)){
                fullName.append("【").append(ProjectExtraEnum.STAFF.value).append("】").append(personName);
            }
            if (ProjectExtraEnum.WORKING_PERSON.key.equals(type)){
                fullName.append("【").append(ProjectExtraEnum.WORKING_PERSON.value).append("】").append(personName);
            }
            String conventionName = conventionNameMap.get(duplicatedConventionPersonVo.getFkConventionId());
            if (GeneralTool.isNotEmpty(conventionName)){
                fullName.append(",").append(conventionName);
            }
            if (GeneralTool.isNotEmpty(tail)){
                fullName.append(",").append(tail);
            }
            duplicatedConventionPersonVo.setFullName(fullName.toString());
        }

        return duplicatedConventionPersonVos;
    }

    /**
     *代理参会统计
     * @param agentAttendanceStatisticsDto
     * @return
     */
    @Override
    public List<AgentAttendanceStatisticsVo> getAgentAttendanceStatistics(AgentAttendanceStatisticsDto agentAttendanceStatisticsDto) {
        List<AgentAttendanceStatisticsVo> agentAttendanceStatisticsVos = conventionPersonMapper.getAgentAttendanceStatistics(agentAttendanceStatisticsDto);
        return agentAttendanceStatisticsVos;
    }


    /**
     * 获取代理参会人绑定bd数量信息
     * @param agentAttendanceStatisticsDto
     * @return
     */
    @Override
    public AgentAttendanceBindingInfoVo getAgentAttendanceBindingInfo(AgentAttendanceStatisticsDto agentAttendanceStatisticsDto) {
        return conventionPersonMapper.getAgentAttendanceBindingInfo(agentAttendanceStatisticsDto);
    }
    public List<MediaAndAttachedVo> getFileMedia(List<SaleMediaAndAttached> mediaAndAttachedList) {
        if (GeneralTool.isEmpty(mediaAndAttachedList)) {
            return Collections.emptyList();
        }
        mediaAndAttachedList.removeIf(Objects::isNull);
        if (GeneralTool.isEmpty(mediaAndAttachedList)) {
            return Collections.emptyList();
        }
        //获取guid集合
        List<String> guidList = mediaAndAttachedList.stream().map(SaleMediaAndAttached::getFkFileGuid).collect(Collectors.toList());
        //转换成DTO
        List<MediaAndAttachedVo> mediaAndAttachedDtos = mediaAndAttachedList.stream()
                .map(mediaAndAttached -> BeanCopyUtils.objClone(mediaAndAttached, MediaAndAttachedVo::new)).collect(Collectors.toList());
        //根据GUID服务调用查询
        Map<String, List<String>> guidListWithTypeMap = new HashMap<>();
        guidListWithTypeMap.put(LoggerModulesConsts.VOTINGCENTER, guidList);
        Result<List<FileDto>> fileDtoResult = fileCenterClient.findFileByGuid(guidListWithTypeMap);
        if (!fileDtoResult.isSuccess()) {
            return Collections.emptyList();
        }
        List<FileDto> fileDtos = fileDtoResult.getData();
        if (fileDtos.isEmpty()) {
            return Collections.emptyList();
        }
        //返回结果不为空时
        List<MediaAndAttachedVo> collect;
        //遍历查询GUID是否一致
        collect = mediaAndAttachedDtos.stream().map(mediaAndAttachedDto -> fileDtos
                .stream()
                .filter(fileDto -> fileDto.getFileGuid().equals(mediaAndAttachedDto.getFkFileGuid()))
                .findFirst()
                .map(fileDto -> {
                    mediaAndAttachedDto.setFilePath(fileDto.getFilePath());
                    mediaAndAttachedDto.setFileNameOrc(fileDto.getFileNameOrc());
                    mediaAndAttachedDto.setTypeValue(FileTypeEnum.getValue(mediaAndAttachedDto.getTypeKey()));
                    mediaAndAttachedDto.setFileKey(fileDto.getFileKey());
                    mediaAndAttachedDto.setFileTypeOrc(fileDto.getFileTypeOrc());
                    /*mediaAndAttachedDto.setFkTableName(null);*/
                    return mediaAndAttachedDto;
                }).orElse(null)
        ).collect(Collectors.toList());
        collect.removeIf(Objects::isNull);
        return collect;
    }
}


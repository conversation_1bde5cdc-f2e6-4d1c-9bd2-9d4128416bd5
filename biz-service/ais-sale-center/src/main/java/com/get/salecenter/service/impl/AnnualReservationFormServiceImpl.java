package com.get.salecenter.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.CommonUtil;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseEntity;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.vo.CurrencyTypeVo;
import com.get.financecenter.feign.IFinanceCenterClient;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.ConfigVo;
import com.get.salecenter.dao.convention.ConventionHotelPayMapper;
import com.get.salecenter.dao.sale.ConventionHotelMapper;
import com.get.salecenter.dao.sale.ConventionPersonMapper;
import com.get.salecenter.dao.sale.ConventionPersonProcedureMapper;
import com.get.salecenter.dao.sale.ConventionPersonRegistrationMapper;
import com.get.salecenter.dao.sale.ConventionProcedureMapper;
import com.get.salecenter.dao.sale.ConventionRegistrationAreaCountryMapper;
import com.get.salecenter.dao.sale.ConventionRegistrationMapper;
import com.get.salecenter.dao.sale.EventItemConfigMapper;
import com.get.salecenter.dao.sale.StaffBdCodeMapper;
import com.get.salecenter.service.*;
import com.get.salecenter.vo.*;
import com.get.salecenter.vo.AnnualBoothListVo;
import com.get.salecenter.entity.Convention;
import com.get.salecenter.entity.ConventionHotel;
import com.get.salecenter.entity.ConventionHotelPay;
import com.get.salecenter.entity.ConventionPerson;
import com.get.salecenter.entity.ConventionPersonProcedure;
import com.get.salecenter.entity.ConventionPersonRegistration;
import com.get.salecenter.entity.ConventionProcedure;
import com.get.salecenter.entity.ConventionRegistration;
import com.get.salecenter.entity.ConventionRegistrationAreaCountry;
import com.get.salecenter.entity.EventItemConfig;
import com.get.salecenter.utils.MyDateUtils;
import com.get.salecenter.utils.MyStringUtils;
import com.get.salecenter.dto.*;
import com.get.salecenter.dto.AnnualReservationFormDto;
import com.github.binarywang.utils.qrcode.MatrixToImageWriter;
import com.github.binarywang.wxpay.bean.notify.SignatureHeader;
import com.github.binarywang.wxpay.bean.notify.WxPayOrderNotifyV3Result;
import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderV3Request;
import com.github.binarywang.wxpay.bean.result.WxPayOrderQueryV3Result;
import com.github.binarywang.wxpay.bean.result.enums.TradeTypeEnum;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import com.google.common.collect.Maps;
import com.google.zxing.common.BitMatrix;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @author: Hardy
 * @create: 2021/7/7 15:32
 * @verison: 1.0
 * @description:
 */
@Service
@Slf4j
public class AnnualReservationFormServiceImpl implements IAnnualReservationFormService {

    @Resource
    private ConventionProcedureMapper conventionProcedureMapper;
    @Resource
    private ConventionRegistrationMapper conventionRegistrationMapper;
    @Resource
    private IConventionHotelService conventionHotelService;
    @Resource
    private ConventionPersonMapper conventionPersonMapper;
    @Resource
    private ConventionPersonRegistrationMapper conventionPersonRegistrationMapper;
    @Resource
    private ConventionPersonProcedureMapper conventionPersonProcedureMapper;
    @Resource
    private ConventionHotelMapper conventionHotelMapper;
    @Resource
    private IFormCommonService formCommonService;
    @Resource
    private StaffBdCodeMapper staffBdCodeMapper;
    @Resource
    private IConventionService conventionService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private EventItemConfigMapper eventItemConfigMapper;
    @Resource
    private ConventionRegistrationAreaCountryMapper conventionRegistrationAreaCountryMapper;
    @Resource
    private WxPayService wxPayService;
    @Resource
    private ConventionHotelPayMapper conventionHotelPayMapper;
    @Resource
    private IFinanceCenterClient financeCenterClient;
    @Resource
    private IConventionPersonService conventionPersonService;

    /**
     * 获得流程多选框
     *
     * @param receiptCode
     * @return
     */
    @Override
    public List<ConventionProcedureVo> getConventionProcedures(String receiptCode) {
        Long conventionId = conventionRegistrationMapper.getConventionIdByReceiptCode(receiptCode);
        if (GeneralTool.isEmpty(conventionId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("receipt_code_error"));
        }
//        Example example = new Example(ConventionProcedure.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkConventionId", conventionId);
//        example.orderBy("stepIndex").asc();
//        List<ConventionProcedure> conventionProcedures = conventionProcedureMapper.selectByExample(example);

        List<ConventionProcedure> conventionProcedures = conventionProcedureMapper.selectList(Wrappers.<ConventionProcedure>lambdaQuery().eq(ConventionProcedure::getFkConventionId, conventionId).orderByAsc(ConventionProcedure::getStepIndex));


        return conventionProcedures.stream().map(conventionProcedure -> BeanCopyUtils.objClone(conventionProcedure, ConventionProcedureVo::new)).collect(Collectors.toList());
    }

    /**
     * 新增/修改表单内容表单
     *
     * @param iaeAnnualReservationFormDto
     * @
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addAnnualReservationForm(IaeAnnualReservationFormDto iaeAnnualReservationFormDto) {
        Long conventionId = conventionRegistrationMapper.getConventionIdByReceiptCode(iaeAnnualReservationFormDto.getReceiptCode());
        if (GeneralTool.isEmpty(conventionId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("receipt_code_error"));
        }
        //添加快递信息到报名名册表
        List<AnnualReservationBoothDto> annualReservationBoothDtoList = iaeAnnualReservationFormDto.getAnnualReservationBoothVoList();
        // updateBooths(annualReservationBoothDtoList);
        //添加人信息
        List<AnnualReservationFormDto> annualReservationFormDtoList = iaeAnnualReservationFormDto.getAnnualReservationFormVoList();
        for (AnnualReservationFormDto annualReservationFormDto : annualReservationFormDtoList) {
            //添加参会人信息
            annualReservationFormDto.setFkConventionId(conventionId);
            addConventionPerson(annualReservationFormDto);
        }
    }

    /**
     * 回显表单内容
     *
     * @param receiptCode
     * @return
     */
    @Override
    public IaeAnnualReservationFormVo getIaeAnnualReservationFormDto(String receiptCode) {
        Long conventionId = conventionRegistrationMapper.getConventionIdByReceiptCode(receiptCode);
        if (GeneralTool.isEmpty(conventionId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("receipt_code_error"));
        }
        IaeAnnualReservationFormVo iaeAnnualReservationFormVo = new IaeAnnualReservationFormVo();
        iaeAnnualReservationFormVo.setReceiptCode(receiptCode);
//        Example example = new Example(ConventionRegistration.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("receiptCode", receiptCode);
//        //查询报名名册-即查询展位
//        List<ConventionRegistration> conventionRegistrations = conventionRegistrationMapper.selectByExample(example);

        //查询报名名册-即查询展位
        List<ConventionRegistration> conventionRegistrations = conventionRegistrationMapper.selectList(Wrappers.<ConventionRegistration>lambdaQuery()
                .eq(ConventionRegistration::getReceiptCode, receiptCode));


        List<AnnualReservationBoothVo> annualReservationBoothVoList = new ArrayList<>();
        //设置展位
        for (ConventionRegistration conventionRegistration : conventionRegistrations) {
            AnnualReservationBoothVo annualReservationBoothVo = new AnnualReservationBoothVo();
            //快递信息list
            List<ExpressInfoVo> expressInfoVoList = new ArrayList<>();
            annualReservationBoothVo.setConventionRegistrationId(conventionRegistration.getId());
            if (GeneralTool.isNotEmpty(conventionRegistration.getExpressInfo())) {
                String expressInfo = conventionRegistration.getExpressInfo();
                String[] split = expressInfo.split(";");
                for (String express : split) {
                    List<String> list = MyStringUtils.extractMessage(express);
                    if (list.size() != 3) {
                        throw new GetServiceException(LocaleMessageUtils.getMessage("express_info_error"));
                    }
                    ExpressInfoVo expressInfoVo = new ExpressInfoVo();
                    expressInfoVo.setCourierCompany(list.get(0));
                    expressInfoVo.setCourierNumber(list.get(1));
                    if (GeneralTool.isEmpty(list.get(2)) || "null".equals(list.get(2))) {
                        expressInfoVo.setPieces(null);
                    } else {
                        expressInfoVo.setPieces(Integer.valueOf(list.get(2)));
                    }
                    expressInfoVoList.add(expressInfoVo);
                }
            }
            annualReservationBoothVo.setExpressInfoDtoList(expressInfoVoList);
            if (GeneralTool.isNotEmpty(conventionRegistration.getProviderName())) {
                annualReservationBoothVo.setInstitutionName(conventionRegistration.getProviderName());
            }
            if (GeneralTool.isNotEmpty(conventionRegistration.getBoothName())) {
                annualReservationBoothVo.setBoothName(conventionRegistration.getBoothName());
            }
            annualReservationBoothVoList.add(annualReservationBoothVo);
        }
        //设置展位list
        iaeAnnualReservationFormVo.setAnnualReservationBoothDtoList(annualReservationBoothVoList);
        //设置参会人信息 - 新建参会人dtoList
        List<AnnualReservationFormVo> annualReservationFormVoList = new ArrayList<>();
        //根据回执码获取峰会id
        List<Long> conventionRegistrationIds = new ArrayList<>();
        for (ConventionRegistration conventionRegistration : conventionRegistrations) {
            conventionRegistrationIds.add(conventionRegistration.getId());
        }
        //参会人员ids
        List<Long> conventionPersonIds = new ArrayList<>();
        //根据报名名册id - 查询参会人员
        for (Long conventionRegistrationId : conventionRegistrationIds) {
            List<ConventionPersonRegistration> conventionPersonRegistrations = getconventionPersonByConventionRegistrationId(conventionRegistrationId);
            for (ConventionPersonRegistration conventionPersonRegistration : conventionPersonRegistrations) {
                conventionPersonIds.add(conventionPersonRegistration.getFkConventionPersonId());
            }
        }
        //去重
        List<Long> ids = conventionPersonIds.stream().distinct().sorted().collect(Collectors.toList());

        if (ids.size() <= 0) {
            //annualReservationFormVoList.add(new AnnualReservationFormVo());
            iaeAnnualReservationFormVo.setAnnualReservationFormDtoList(annualReservationFormVoList);
            return iaeAnnualReservationFormVo;
        }
        Map<Long, List<ConventionHotelPay>> payTypeMap = Maps.newHashMap();
        List<ConventionHotelPay> conventionHotelPays = conventionHotelPayMapper.selectList(Wrappers.lambdaQuery(ConventionHotelPay.class)
                .in(ConventionHotelPay::getFkConventionPersonId, ids)
                .eq(ConventionHotelPay::getPayType, 1));
        if (GeneralTool.isNotEmpty(conventionHotelPays)){
            payTypeMap = conventionHotelPays.stream().collect(Collectors.groupingBy(ConventionHotelPay::getFkConventionPersonId));
        }


        for (Long conventionPersonId : ids) {
            //设置信息
            ConventionPerson conventionPerson = conventionPersonMapper.selectById(conventionPersonId);
            if (GeneralTool.isEmpty(conventionPerson.getFkConventionHotelId())) {
                break;
            }
            //set参会人字段信息
            AnnualReservationFormVo annualReservationFormVo = setAnnualHotelReservationDto(conventionPerson);
            if (GeneralTool.isNotEmpty(annualReservationFormVo)) {
                annualReservationFormVo.setFkConventionHotelId(conventionPerson.getFkConventionHotelId());
                ConventionHotel roomType = conventionHotelMapper.selectById(conventionPerson.getFkConventionHotelId());
                annualReservationFormVo.setRoomTypeName(roomType.getRoomType());
            }
            //支付状态
            List<ConventionHotelPay> hotelPays = payTypeMap.get(conventionPersonId);
            if (GeneralTool.isNotEmpty(hotelPays)){
                annualReservationFormVo.setPayType(1);
            }else {
                annualReservationFormVo.setPayType(0);
            }
            //查询参会人-报名名册中间表
            ConventionPersonRegistration conventionPersonRegistration = getConventionPersonRegistration(conventionPersonId);
            if (GeneralTool.isEmpty(conventionPersonRegistration)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("convention_person_not_found"));
            }
            if (GeneralTool.isEmpty(conventionPersonRegistration.getFkConventionRegistrationId())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("convention_registration_id_is_null"));
            }
            annualReservationFormVo.setConventionRegistrationId(conventionPersonRegistration.getFkConventionRegistrationId());
            //设置展位、机构名
            ConventionRegistration conventionRegistration = getConventionRegistration(annualReservationFormVo.getConventionRegistrationId());
            annualReservationFormVo.setInstitutionName(conventionRegistration.getProviderName());
            annualReservationFormVo.setBoothName(conventionRegistration.getBoothName());
            annualReservationFormVo.setId(conventionPersonId);
            annualReservationFormVo.setReceiptCode(receiptCode);
            //查询流程ids - 新建流程ids
            List<Long> procedureIds = getConventionProcedureIds(conventionPersonId);
            annualReservationFormVo.setConventionProcedureIds(procedureIds);
            annualReservationFormVoList.add(annualReservationFormVo);
        }
        iaeAnnualReservationFormVo.setAnnualReservationFormDtoList(annualReservationFormVoList);
        return iaeAnnualReservationFormVo;
    }

    /**
     * 获取参会人信息
     *
     * @param conventionRegistrationId
     * @return
     */
    private List<ConventionPersonRegistration> getconventionPersonByConventionRegistrationId(Long conventionRegistrationId) {
//        Example example = new Example(ConventionPersonRegistration.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkConventionRegistrationId", conventionRegistrationId);
        List<ConventionPersonRegistration> conventionRegistrations = conventionPersonRegistrationMapper.selectList(Wrappers.<ConventionPersonRegistration>lambdaQuery()
                .eq(ConventionPersonRegistration::getFkConventionRegistrationId, conventionRegistrationId));
        return conventionRegistrations;
    }

    /**
     * 房间类型下拉框
     *
     * @param receiptCode
     * @return
     * @
     */
    @Override
    public List<ConventionHotelVo> getRoomTypeSelect(String receiptCode) {
        Long conventionId = conventionRegistrationMapper.getConventionIdByReceiptCode(receiptCode);
        if (GeneralTool.isEmpty(conventionId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("receipt_code_error"));
        }
//        Example example = new Example(ConventionHotel.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkConventionId", conventionId);
//        List<ConventionHotel> conventionHotels = conventionHotelMapper.selectByExample(example);

        List<ConventionHotel> conventionHotels = conventionHotelMapper.selectList(Wrappers.<ConventionHotel>lambdaQuery()
                .eq(ConventionHotel::getFkConventionId, conventionId).orderByDesc(ConventionHotel::getViewOrder));
        return conventionHotels.stream().map(conventionHotel -> BeanCopyUtils.objClone(conventionHotel, ConventionHotelVo::new)).collect(Collectors.toList());
    }

    /**
     * 展位名下拉框
     *
     * @param receiptCode
     * @param fkConventionId
     * @return
     * @
     */
    @Override
    public List<ConventionRegistrationVo> getInstitutionNameSelect(String receiptCode, Long fkConventionId) {
        //判断回执码下，存在多少个展位
        List<ConventionRegistrationVo> conventionRegistrationVoList = conventionRegistrationMapper.getInstitutionNameSelect(receiptCode, fkConventionId);
        if (conventionRegistrationVoList.isEmpty()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("entry_failed"));
        }
        return conventionRegistrationVoList;
    }

    /**
     * 根据参会人员的id删除信息
     *
     * @param id
     * @param receiptCode
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteConventionPerson(Long id, String receiptCode) {
        Boolean flag = conventionPersonRegistrationMapper.checkConventionPersonRegistration(id, receiptCode);
        if (!flag) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("convention_person_not_found"));
        }

        //删除 参会人-报名关系表
        deleteTable(id);
        //校验参会人
        ConventionPerson conventionPerson = conventionPersonMapper.selectById(id);
        if (GeneralTool.isEmpty(conventionPerson)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("convention_person_not_found"));
        }
        //删除 参会人-流程关系表
//        Example example = new Example(ConventionPersonProcedure.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkConventionPersonId", id);

        LambdaQueryWrapper<ConventionPersonProcedure> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ConventionPersonProcedure::getFkConventionPersonId, id);
        List<ConventionPersonProcedure> conventionPersonProcedures = conventionPersonProcedureMapper.selectList(lambdaQueryWrapper);
        if (GeneralTool.isNotEmpty(conventionPersonProcedures)) {
            conventionPersonProcedureMapper.delete(lambdaQueryWrapper);
        }
        //删除 参会人表
        conventionPersonMapper.deleteById(id);
        // 在删除参会人员时，同步删除关系表数据
        conventionPersonService.deleteAssociationTable(id, conventionPerson);

        List<ConventionPerson> conventionPersonList = conventionPersonMapper.selectList(Wrappers.lambdaQuery(ConventionPerson.class)
                .like(ConventionPerson::getRemark, "同住人:" + id));

        if (GeneralTool.isNotEmpty(conventionPersonList)){
            for (ConventionPerson person : conventionPersonList) {
                String remark = person.getRemark();
                List<String> list = MyStringUtils.getContentBetweenMiddleBrackets(remark);
                for (String s : list) {
                    if (s.contains("同住人")){
                        remark = remark.replace(s,"");
                        remark = remark.replace("[]","");
                    }
                }
                person.setRemark(remark);
                updateUserInfoToEntity(person);
                conventionPersonMapper.updateById(person);
            }
        }
    }

    /**
     * 删除快递信息
     *
     * @param id
     * @param expressInfoDtoList
     * @
     */
    @Override
    public void deleteExpressInfo(Long id, List<ExpressInfoDto> expressInfoDtoList, Integer provideGifts) {
        ConventionRegistration conventionRegistration = conventionRegistrationMapper.selectById(id);
        if (GeneralTool.isEmpty(conventionRegistration)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_search"));
        }
        StringBuilder expressInfo = transExpressVoList2ExpressInfo(expressInfoDtoList);

        String provideGiftsStr = "是否提供晚宴抽奖礼品=";
        //转换为快递信息
        if (GeneralTool.isNotEmpty(provideGifts)) {
            if (provideGifts == 0) {
                expressInfo.append(";").append("[").append(provideGiftsStr).append("否").append("]");
            } else if (provideGifts == 1) {
                expressInfo.append(";").append("[").append(provideGiftsStr).append("是").append("]");
            }
        }
        conventionRegistration.setExpressInfo(expressInfo.toString());
        updateUserInfoToEntity(conventionRegistration);
        conventionRegistrationMapper.updateById(conventionRegistration);
    }

    /**
     * 接口验证手机号是否重复
     *
     * @param conventionId
     * @param phone
     * @param personId
     * @return
     */
    @Override
    public Boolean validatedTel(Long conventionId, String phone, Long personId) {
//        Example example = new Example(ConventionPerson.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkConventionId", conventionId);
//        criteria.andEqualTo("tel", phone);
//        if (GeneralTool.isNotEmpty(personId)) {
//            //修改情况时
//            criteria.andNotEqualTo("id", personId);
//        }

        LambdaQueryWrapper<ConventionPerson> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ConventionPerson::getFkConventionId, conventionId);
        lambdaQueryWrapper.eq(ConventionPerson::getTel, phone);
        if (GeneralTool.isNotEmpty(personId)) {
            //修改情况时
            lambdaQueryWrapper.ne(ConventionPerson::getId, personId);
        }
        List<ConventionPerson> conventionPeople = conventionPersonMapper.selectList(lambdaQueryWrapper);
        return GeneralTool.isEmpty(conventionPeople);
    }

    /**
     * 保存展位信息
     *
     * @param annualReservationBoothDtos
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveBoothVos(String receiptCode, List<AnnualReservationBoothDto> annualReservationBoothDtos) {
        Long conventionId = conventionRegistrationMapper.getConventionIdByReceiptCode(receiptCode);
        if (GeneralTool.isEmpty(conventionId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("receipt_code_error"));
        }
        updateBooths(annualReservationBoothDtos);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addAnnualPersons(String receiptCode, List<AnnualReservationFormDto> annualReservationFormDtos) {
        Long conventionId = conventionRegistrationMapper.getConventionIdByReceiptCode(receiptCode);
        if (GeneralTool.isEmpty(conventionId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("receipt_code_error"));
        }
        //费尔蒙酒店房型校验
        List<Long> hotelIds = annualReservationFormDtos.stream().map(AnnualReservationFormDto::getFkConventionHotelId).collect(Collectors.toList());
        List<ConventionHotel> conventionHotels = conventionHotelMapper.selectBatchIds(hotelIds);
        //如果hotel包含费尔蒙三个字，则查询出来
        conventionHotels = conventionHotels.stream().filter(conventionHotel -> conventionHotel.getHotel().contains("费尔蒙")).collect(Collectors.toList());
        if (GeneralTool.isNotEmpty(conventionHotels) && conventionHotels.size() >= 3) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("FAIRMONT_SANYA_HAITANG_BAY_RESTRICTIONS"));
        }
        for (AnnualReservationFormDto annualReservationFormDto : annualReservationFormDtos) {
            //添加参会人信息
            annualReservationFormDto.setFkConventionId(conventionId);
            addConventionPerson(annualReservationFormDto);
        }
    }

    /**
     * 展位回显
     *
     * @param receiptCode
     * @return AnnualBoothListVo
     * @
     */
    @Override
    public AnnualBoothListVo getAnnualBoothListDto(String receiptCode) {
        Long conventionId = conventionRegistrationMapper.getConventionIdByReceiptCode(receiptCode);
        if (GeneralTool.isEmpty(conventionId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("receipt_code_error"));
        }
        AnnualBoothListVo annualBoothListVo = new AnnualBoothListVo();
        annualBoothListVo.setReceiptCode(receiptCode);
//        Example example = new Example(ConventionRegistration.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("receiptCode", receiptCode);
//        //查询报名名册-即查询展位
//        List<ConventionRegistration> conventionRegistrations = conventionRegistrationMapper.selectByExample(example);

        LambdaQueryWrapper<ConventionRegistration> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ConventionRegistration::getReceiptCode, receiptCode);
        List<ConventionRegistration> conventionRegistrations = conventionRegistrationMapper.selectList(lambdaQueryWrapper);

        List<AnnualReservationBoothVo> annualReservationBoothVoList = new ArrayList<>();
        //设置展位
        for (ConventionRegistration conventionRegistration : conventionRegistrations) {
            AnnualReservationBoothVo annualReservationBoothVo = new AnnualReservationBoothVo();
            //快递信息list
            List<ExpressInfoVo> expressInfoVoList = new ArrayList<>();
            annualReservationBoothVo.setConventionRegistrationId(conventionRegistration.getId());
            if (GeneralTool.isNotEmpty(conventionRegistration.getExpressInfo())) {
                String expressInfo = conventionRegistration.getExpressInfo();
                String[] split = expressInfo.split(";");
                for (String express : split) {
                    if (split.length > 1 && "".equals(express)) {
                        continue;
                    }
                    List<String> list = MyStringUtils.extractMessage(express);
//                    if (list.size() != 4&&list.size() !=1) {
//                        throw new GetServiceException(LocaleMessageUtils.getMessage("express_info_error"));
//                    }
                    if (list.size() == 4) {
                        ExpressInfoVo expressInfoVo = new ExpressInfoVo();
                        expressInfoVo.setCourierCompany(list.get(0));
                        expressInfoVo.setCourierNumber(list.get(1));
                        if (GeneralTool.isEmpty(list.get(2)) || "null".equals(list.get(2))) {
                            expressInfoVo.setPieces(null);
                        } else {
                            expressInfoVo.setPieces(Integer.valueOf(list.get(2)));
                        }
                        expressInfoVo.setRemark(list.get(3));
                        expressInfoVoList.add(expressInfoVo);
                    } else if (list.size() == 3) {
                        ExpressInfoVo expressInfoVo = new ExpressInfoVo();
                        expressInfoVo.setCourierCompany(list.get(0));
                        expressInfoVo.setCourierNumber(list.get(1));
                        if (GeneralTool.isEmpty(list.get(2)) || "null".equals(list.get(2))) {
                            expressInfoVo.setPieces(null);
                        } else {
                            expressInfoVo.setPieces(Integer.valueOf(list.get(2)));
                        }
                        expressInfoVoList.add(expressInfoVo);
                    } else if (list.size() == 1) {
                        String s = list.get(0);
                        String[] split1 = s.split("=");
                        if (split1.length != 2) {
                            annualReservationBoothVo.setProvideGifts(0);
                        } else if ("否".equals(split1[1].trim())) {
                            annualReservationBoothVo.setProvideGifts(0);
                        } else if ("是".equals(split1[1].trim())) {
                            annualReservationBoothVo.setProvideGifts(1);
                        }
                    } else {
                        throw new GetServiceException(LocaleMessageUtils.getMessage("express_info_error"));
                    }
                }
            }
            annualReservationBoothVo.setExpressInfoDtoList(expressInfoVoList);
            if (GeneralTool.isNotEmpty(conventionRegistration.getProviderName())) {
                annualReservationBoothVo.setInstitutionName(conventionRegistration.getProviderName());
            }
            if (GeneralTool.isNotEmpty(conventionRegistration.getBoothName())) {
                annualReservationBoothVo.setBoothName(conventionRegistration.getBoothName());
            }
            annualReservationBoothVoList.add(annualReservationBoothVo);
        }
        //设置展位list
        annualBoothListVo.setAnnualReservationBoothDtoList(annualReservationBoothVoList);
        return annualBoothListVo;
    }

    /**
     * 参会人回显
     *
     * @param receiptCode
     * @return
     * @
     */
    @Override
    public List<AnnualReservationFormVo> getAnnualPerson(String receiptCode) {
        Long conventionId = conventionRegistrationMapper.getConventionIdByReceiptCode(receiptCode);
        if (GeneralTool.isEmpty(conventionId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("receipt_code_error"));
        }
//        Example example = new Example(ConventionRegistration.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("receiptCode", receiptCode);
//        //查询报名名册-即查询展位
//        List<ConventionRegistration> conventionRegistrations = conventionRegistrationMapper.selectByExample(example);

        LambdaQueryWrapper<ConventionRegistration> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ConventionRegistration::getReceiptCode, receiptCode);
        List<ConventionRegistration> conventionRegistrations = conventionRegistrationMapper.selectList(lambdaQueryWrapper);

        List<AnnualReservationFormVo> annualReservationFormVoList = new ArrayList<>();
        //根据回执码获取峰会id
        List<Long> conventionRegistrationIds = new ArrayList<>();
        for (ConventionRegistration conventionRegistration : conventionRegistrations) {
            conventionRegistrationIds.add(conventionRegistration.getId());
        }
        //参会人员ids
        List<Long> conventionPersonIds = new ArrayList<>();
        //根据报名名册id - 查询参会人员
        for (Long conventionRegistrationId : conventionRegistrationIds) {
            List<ConventionPersonRegistration> conventionPersonRegistrations = getconventionPersonByConventionRegistrationId(conventionRegistrationId);
            for (ConventionPersonRegistration conventionPersonRegistration : conventionPersonRegistrations) {
                conventionPersonIds.add(conventionPersonRegistration.getFkConventionPersonId());
            }
        }
        //去重
        List<Long> ids = conventionPersonIds.stream().distinct().sorted().collect(Collectors.toList());
        if (ids.size() <= 0) {
            return annualReservationFormVoList;
        }
        for (Long conventionPersonId : ids) {
            //设置信息
            ConventionPerson conventionPerson = conventionPersonMapper.selectById(conventionPersonId);
            if (GeneralTool.isEmpty(conventionPerson.getFkConventionHotelId())) {
                break;
            }
            //set参会人字段信息
            AnnualReservationFormVo annualReservationFormVo = setAnnualHotelReservationDto(conventionPerson);
            if (GeneralTool.isNotEmpty(annualReservationFormVo)) {
                annualReservationFormVo.setFkConventionHotelId(conventionPerson.getFkConventionHotelId());
                ConventionHotel roomType = conventionHotelMapper.selectById(conventionPerson.getFkConventionHotelId());
                annualReservationFormVo.setRoomTypeName(roomType.getRoomType());
            }
            //查询参会人-报名名册中间表
            ConventionPersonRegistration conventionPersonRegistration = getConventionPersonRegistration(conventionPersonId);
            if (GeneralTool.isEmpty(conventionPersonRegistration)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("convention_person_not_found"));
            }
            if (GeneralTool.isEmpty(conventionPersonRegistration.getFkConventionRegistrationId())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("convention_registration_id_is_null"));
            }
            annualReservationFormVo.setConventionRegistrationId(conventionPersonRegistration.getFkConventionRegistrationId());
            //设置展位、机构名
            ConventionRegistration conventionRegistration = getConventionRegistration(annualReservationFormVo.getConventionRegistrationId());
            annualReservationFormVo.setInstitutionName(conventionRegistration.getProviderName());
            annualReservationFormVo.setBoothName(conventionRegistration.getBoothName());
            annualReservationFormVo.setId(conventionPersonId);
            annualReservationFormVo.setReceiptCode(receiptCode);
            //查询流程ids - 新建流程ids
            List<Long> procedureIds = getConventionProcedureIds(conventionPersonId);
            annualReservationFormVo.setConventionProcedureIds(procedureIds);
            annualReservationFormVoList.add(annualReservationFormVo);
        }
        return annualReservationFormVoList;
    }

    /**
     * 获取参会人数量
     *
     * @param receiptCode
     * @return
     */
    @Override
    public Integer getPersonCount(String receiptCode) {
        return conventionPersonMapper.getPersonCount(receiptCode);
    }


    /**
     * 2022年度iae年会代理报名表单
     *
     * @return
     * @Date 11:02 2022/8/9
     * <AUTHOR>
     */
    @Override
    public List<ConventionPerson> addAgentAnnualPersons(List<AnnualReservationFormAgentDto> annualReservationFormAgentDtos, Long conventionId) {
        List<ConventionPerson> conventionPersons = new ArrayList<>();
        for (AnnualReservationFormAgentDto annualReservationFormAgentDto : annualReservationFormAgentDtos) {
            ConventionPerson conventionPerson = new ConventionPerson();
            conventionPerson.setFkConventionId(conventionId);
            if (!validatedTel(conventionId, annualReservationFormAgentDto.getTel(), null)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("phone_number_is_been_used"));
            }
            conventionPerson.setType(1);
//            if (annualReservationFormAgentDto.getFirstNameChn().matches(".*[a-zA-z].*")){
//                conventionPerson.setNameChn(annualReservationFormAgentDto.getLastNameChn() + " " + annualReservationFormAgentDto.getFirstNameChn());
//                conventionPerson.setName(annualReservationFormAgentDto.getLastNameChn() + " " + annualReservationFormAgentDto.getFirstNameChn());
//            } else {
//                conventionPerson.setNameChn(annualReservationFormAgentDto.getLastNameChn() + annualReservationFormAgentDto.getFirstNameChn());
//                conventionPerson.setName(annualReservationFormAgentDto.getLastNameChn() + annualReservationFormAgentDto.getFirstNameChn());
//            }
            conventionPerson.setNameChn(annualReservationFormAgentDto.getNameChn());
            conventionPerson.setName(annualReservationFormAgentDto.getNameChn());
            conventionPerson.setGender(annualReservationFormAgentDto.getGender());
            conventionPerson.setCompany(annualReservationFormAgentDto.getCompany());
            conventionPerson.setEmail(annualReservationFormAgentDto.getEmail());
            conventionPerson.setTitle(annualReservationFormAgentDto.getTitle());
            conventionPerson.setTel(annualReservationFormAgentDto.getTel());
            conventionPerson.setPassportNum(annualReservationFormAgentDto.getPassportNum());
            conventionPerson.setIdCardNum(annualReservationFormAgentDto.getIdCardNum());
            conventionPerson.setRemark(annualReservationFormAgentDto.getRemark());
            conventionPerson.setCheckInTime(annualReservationFormAgentDto.getCheckInTime());
            conventionPerson.setCheckOutTime(annualReservationFormAgentDto.getCheckOutTime());
            conventionPerson.setBdCode(annualReservationFormAgentDto.getBdCode());
            conventionPerson.setIsAttendDinner(true);
            //添加人信息
            addUserInfoToEntity(conventionPerson);
            conventionPersonMapper.insertSelective(conventionPerson);
            long personId = conventionPerson.getId();
            //设置参会人员编号  用新增的id生成编号
            conventionPerson.setNum(MyStringUtils.getConventionPersonNum(conventionPerson.getId()));
            updateUserInfoToEntity(conventionPerson);
            conventionPersonMapper.updateById(conventionPerson);
            conventionPersons.add(conventionPerson);

            List<ConventionProcedure> conventionProcedures = conventionProcedureMapper.selectList(Wrappers.<ConventionProcedure>lambdaQuery().eq(ConventionProcedure::getFkConventionId, conventionId));

            //选择参加流程-更新关联表
            for (ConventionProcedure conventionProcedure : conventionProcedures) {
                ConventionPersonProcedure conventionPersonProcedure = new ConventionPersonProcedure();
                conventionPersonProcedure.setFkConventionProcedureId(conventionProcedure.getId());
                conventionPersonProcedure.setFkConventionPersonId(personId);
                //设置时间
                addUserInfoToEntity(conventionPersonProcedure);
                //添加
                conventionPersonProcedureMapper.insertSelective(conventionPersonProcedure);
            }
        }
        return conventionPersons;
    }

    /**
     * 峰会BD下拉框
     *
     * @Date 14:50 2022/8/9
     * <AUTHOR>
     */
    @Override
    public List<BaseSelectEntity> getAreaRegionSelectByConventionId(Long conventionId) {
        // HTI使用，目前写死
        Long companyId = 3L;
        // BD_DDL_FILTER BD下拉过滤配置，value1=通用字符过滤，value2=BDCode过滤
        Map<Long, String> charFilterMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.BD_DDL_FILTER.key, 1).getData();
        Map<Long, String> bdCodeFilterMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.BD_DDL_FILTER.key, 2).getData();
        String configValue1 = charFilterMap.get(companyId);
        String configValue2 = bdCodeFilterMap.get(companyId);
        JSONArray charFilter = JSONObject.parseArray(configValue1);
        JSONArray bdCodeFilter = JSONObject.parseArray(configValue2);
        List<String> charFilterList = charFilter.toJavaList(String.class);
        List<Long> bdCodeFilterList = bdCodeFilter.toJavaList(Long.class);

        return staffBdCodeMapper.getAreaRegionSelectByConventionId(conventionId, charFilterList, bdCodeFilterList);
    }

    @Override
    public String getConventionName(Long conventionId) {
        Convention convention = conventionService.getById(conventionId);
        if (GeneralTool.isEmpty(convention)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        return convention.getThemeName();
    }

    /**
     * 加拿大冬季Retreat报名
     *
     * @Date 16:01 2023/7/7
     * <AUTHOR>
     */
    @Override
    public void canadaWinterRetreat(CanadaWinterRetreatDto canadaWinterRetreatDto, Long conventionId) {
        List<ConventionRegistration> conventionRegistrations = conventionRegistrationMapper.selectList(Wrappers.<ConventionRegistration>lambdaQuery()
                .eq(ConventionRegistration::getFkConventionId, conventionId)
                .eq(ConventionRegistration::getExpressInfo, canadaWinterRetreatDto.getItemName()));

        EventItemConfig eventItemConfig = eventItemConfigMapper.selectOne(Wrappers.<EventItemConfig>lambdaQuery()
                .eq(EventItemConfig::getEventType, "2023CanadaConference")
                .eq(EventItemConfig::getItemName, canadaWinterRetreatDto.getItemName()));
        //报名限制人数
        if (conventionRegistrations.size() >= eventItemConfig.getQuota()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("Reach_the_limit"));
        }
        ConventionRegistration conventionRegistration = new ConventionRegistration();
        conventionRegistration.setFkConventionId(conventionId);
        conventionRegistration.setFkCurrencyTypeNum("CAD");
        conventionRegistration.setRegistrationFee(BigDecimal.valueOf(Long.parseLong(eventItemConfig.getValue2())));
        conventionRegistration.setSummaryFee(canadaWinterRetreatDto.getSummaryFee());
        conventionRegistration.setProviderName(canadaWinterRetreatDto.getProviderName());
        conventionRegistration.setContactPersonName(canadaWinterRetreatDto.getContactPersonName());
        conventionRegistration.setContactEmail(canadaWinterRetreatDto.getEmail());
        conventionRegistration.setContactTel(canadaWinterRetreatDto.getTel());
        conventionRegistration.setExpressInfo(canadaWinterRetreatDto.getItemName());
        conventionRegistration.setStatus(0);
        conventionRegistration.setGmtCreate(new Date());
        conventionRegistration.setGmtCreateUser("online_form");
        conventionRegistrationMapper.insertSelective(conventionRegistration);

        ConventionRegistrationAreaCountry conventionRegistrationAreaCountry = new ConventionRegistrationAreaCountry();
        conventionRegistrationAreaCountry.setFkConventionRegistrationId(conventionRegistration.getId());
        conventionRegistrationAreaCountry.setFkAreaCountryKey("CAN");
        conventionRegistrationAreaCountry.setGmtCreate(new Date());
        conventionRegistrationAreaCountry.setGmtCreateUser("online_form");
        conventionRegistrationAreaCountryMapper.insert(conventionRegistrationAreaCountry);
    }

    /**
     * 加拿大冬季Retreat报名下拉框
     *
     * @Date 17:08 2023/7/7
     * <AUTHOR>
     */
    @Override
    public List<EventItemConfigVo> getCanadaWinterRetreatSelect(Long conventionId) {
        List<EventItemConfigVo> eventItemConfigVoList = new ArrayList<>();

        List<EventItemConfig> eventItemConfigs = eventItemConfigMapper.selectList(Wrappers.<EventItemConfig>lambdaQuery().eq(EventItemConfig::getEventType, "2023CanadaConference"));
        for (EventItemConfig eventItemConfig : eventItemConfigs) {
            EventItemConfigVo eventItemConfigVo = new EventItemConfigVo();
            BeanCopyUtils.copyProperties(eventItemConfig, eventItemConfigVo);
            if (eventItemConfig.getItemName().equals("A+ Institutions")) {
                eventItemConfig.setQuota(0);
            } else {
                List<ConventionRegistration> conventionRegistrations = conventionRegistrationMapper.selectList(Wrappers.<ConventionRegistration>lambdaQuery()
                        .eq(ConventionRegistration::getFkConventionId, conventionId)
                        .eq(ConventionRegistration::getExpressInfo, eventItemConfig.getItemName()));
                eventItemConfigVo.setRemainingQuantity(eventItemConfig.getQuota() - conventionRegistrations.size());
            }
            eventItemConfigVoList.add(eventItemConfigVo);
        }
        return eventItemConfigVoList;
    }


    @Override
    public void generateOrders(ConventionHotelPayDto conventionHotelPayDto, HttpServletResponse response) throws IOException {
        // 设置响应流信息
//        response.setContentType("image/jpg");
        response.setHeader("Pragma", "no-cache");
        response.setHeader("Cache-Control", "no-cache");
        response.setDateHeader("Expires", 0);
        OutputStream stream = response.getOutputStream();

        String orderCode = getOrderCode();
        String roomType = conventionHotelPayDto.getRoomType();
        ConventionHotel conventionHotel = conventionHotelMapper.selectOne(Wrappers.lambdaQuery(ConventionHotel.class)
                .eq(ConventionHotel::getRoomType, roomType)
                .eq(ConventionHotel::getFkConventionId, conventionHotelPayDto.getFkConventionId()));
        if (GeneralTool.isEmpty(conventionHotel)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("cannot_find_room_type"));
        }

        BigDecimal price = conventionHotel.getPrice();
        if (GeneralTool.isEmpty(price)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_room_type_price_set"));
        }
        ConventionHotelPay conventionHotelPay = BeanCopyUtils.objClone(conventionHotelPayDto, ConventionHotelPay::new);
        assert conventionHotelPay != null;
        int days = MyDateUtils.differentDays(conventionHotelPay.getCheckInTime(), conventionHotelPay.getCheckOutTime());
        conventionHotelPay.setSystemOrderNum(orderCode);
        addUserInfoToEntity(conventionHotelPay);
        conventionHotelPay.setPayType(0);
        String remark = conventionHotelPay.getInstitutionName()+","+conventionHotelPay.getRoomType()
                +(GeneralTool.isNotEmpty(conventionHotelPayDto.getNameChn())?","+ conventionHotelPayDto.getNameChn():","+ conventionHotelPayDto.getName());
        conventionHotelPay.setRemark(remark);
        conventionHotelPay.setPayCount(1);
        conventionHotelPay.setPayAmount(price.multiply(new BigDecimal((days))));
        conventionHotelPay.setResidents(conventionHotelPayDto.getName()+(GeneralTool.isNotEmpty(conventionHotelPayDto.getNameChn())?"("+ conventionHotelPayDto.getNameChn()+")":""));
        conventionHotelPayMapper.insert(conventionHotelPay);
        log.info("新增成功#generateOrders------------------------------");
        if (GeneralTool.isEmpty(conventionHotelPay.getId())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }

        BigDecimal amount = price.multiply(new BigDecimal((days)*100));

        WxPayUnifiedOrderV3Request request = new WxPayUnifiedOrderV3Request()
                .setAppid(wxPayService.getConfig().getAppId())
                .setMchid(wxPayService.getConfig().getMchId())
                .setDescription("酒店房费-"+ price +"/晚")
                .setOutTradeNo(orderCode)
                .setAttach(String.valueOf(conventionHotelPay.getId()))
                .setNotifyUrl("https://convention.ht-international.net/sale/annualReservationForm/notifyWeiXinPay")
                .setAmount(new WxPayUnifiedOrderV3Request.Amount().setTotal(amount.intValue()));

        String result;
        try {
            result = wxPayService.createOrderV3(TradeTypeEnum.NATIVE, request);
            log.info("result#generateOrders:{}------------------------",result);
        } catch (WxPayException e) {
            log.info("微信统一下单异常#generateOrders------------------------------");
            throw new GetServiceException(LocaleMessageUtils.getMessage("wechat_unified_ordering_exception"));
        }
        //获取一个二维码图片
        BitMatrix bitMatrix = MyStringUtils.createCode(result);
//        //以流的形式输出到前端
        MatrixToImageWriter.writeToStream(bitMatrix , "jpg" , stream);
    }


    @DSTransactional
    @Override
    public void notifyWeiXinPay(SignatureHeader signatureHeaderByHttpHeaders, String notifyData) throws Exception {
        WxPayOrderNotifyV3Result wxPayOrderNotifyV3Result = wxPayService.parseOrderNotifyV3Result(notifyData, signatureHeaderByHttpHeaders);
        WxPayOrderNotifyV3Result.DecryptNotifyResult result = wxPayOrderNotifyV3Result.getResult();
        //微信订单号
        String transactionId = result.getTransactionId();
        if (GeneralTool.isEmpty(transactionId)){
            return;
        }
        log.info("微信订单号：{}#notifyWeiXinPay------------------------------",transactionId);
        //支付信息
        String attach = result.getAttach();
        log.info("支付信息id：{}#notifyWeiXinPay------------------------------",attach);
        Long id = Long.parseLong(attach);
        ConventionHotelPay conventionHotelPay = conventionHotelPayMapper.selectById(id);
        if (GeneralTool.isNotEmpty(conventionHotelPay)){
            if (conventionHotelPay.getPayType() == 0){
                conventionHotelPay.setPayType(1);
                conventionHotelPay.setPayOrderNum(transactionId);
                updateUserInfoToEntity(conventionHotelPay);
                conventionHotelPayMapper.updateById(conventionHotelPay);
                log.info("更新成功：#notifyWeiXinPay------------------------------");
                if (GeneralTool.isNotEmpty(conventionHotelPay.getFkConventionPersonId())){
                    ConventionPerson conventionPerson = conventionPersonMapper.selectById(conventionHotelPay.getFkConventionPersonId());
                    conventionPerson.setCheckInTime(conventionHotelPay.getCheckInTime());
                    conventionPerson.setCheckOutTime(conventionHotelPay.getCheckOutTime());
                    updateUserInfoToEntity(conventionPerson);
                    conventionPersonMapper.updateById(conventionPerson);
                    log.info("更新参会人入住退房时间成功：#notifyWeiXinPay------------------------------");
                }
            }
        }

    }

    /**
     * 房型价格提示
     * @param amountPayNoticeDto
     * @return
     */
    @Override
    public AmountPayNoticeVo getAmountPayNotice(AmountPayNoticeDto amountPayNoticeDto) {
        ConventionHotel conventionHotel = conventionHotelMapper.selectById(amountPayNoticeDto.getId());
        if (GeneralTool.isEmpty(conventionHotel)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("cannot_find_room_type"));
        }
        BigDecimal price = conventionHotel.getPrice();
//        if (GeneralTool.isEmpty(price)){
//            throw new GetServiceException(LocaleMessageUtils.getMessage("no_room_type_price_set"));
//        }
        if (GeneralTool.isEmpty(conventionHotel)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("cannot_find_room_type"));
        }
        AmountPayNoticeVo amountPayNoticeVo = new AmountPayNoticeVo();
        if (GeneralTool.isEmpty(amountPayNoticeDto.getFkConventionPersonId())){
            amountPayNoticeVo.setNoticeType(0);
        }else {
            List<ConventionHotelPay> conventionHotelPays = conventionHotelPayMapper.selectList(Wrappers.lambdaQuery(ConventionHotelPay.class)
                    .eq(ConventionHotelPay::getFkConventionPersonId, amountPayNoticeDto.getFkConventionPersonId())
                    .eq(ConventionHotelPay::getPayType, 1));
            amountPayNoticeVo.setNoticeType(GeneralTool.isEmpty(conventionHotelPays)?1:2);
        }
        int days = MyDateUtils.differentDays(amountPayNoticeDto.getCheckInTime(), amountPayNoticeDto.getCheckOutTime());
        if (GeneralTool.isNotEmpty(price)) {
            BigDecimal total = price.multiply(new BigDecimal(days));
            amountPayNoticeVo.setAmount(price.toString());
            amountPayNoticeVo.setTotalAmount(total.toString());
        }
        amountPayNoticeVo.setCount(String.valueOf(days));
//        String notice =" 入住房型为："+price.intValue()+"元/晚，共"+days+"晚，价格为："+total.intValue()+"元（点击我要支付，扫码支付）";
        return amountPayNoticeVo;
    }

    /**
     * 获取支付状态
     * @param fkConventionPersonId
     * @return
     */
    @DSTransactional
    @Override
    public Integer getPayType(Long fkConventionPersonId) {
        List<ConventionHotelPay> conventionHotelPays = conventionHotelPayMapper.selectList(Wrappers.lambdaQuery(ConventionHotelPay.class)
                .eq(ConventionHotelPay::getFkConventionPersonId, fkConventionPersonId)
                .orderByDesc(ConventionHotelPay::getGmtCreate));
        List<ConventionHotelPay> paidList = conventionHotelPays.stream().filter(c -> c.getPayType() == 1).collect(Collectors.toList());
        if (GeneralTool.isNotEmpty(paidList)){
            return 1;
        }
        List<ConventionHotelPay> unpaidList = conventionHotelPays.stream().filter(c -> c.getPayType() == 0).collect(Collectors.toList());
        if (GeneralTool.isEmpty(unpaidList)){
            return 0;
        }
        ConventionHotelPay conventionHotelPay = unpaidList.get(0);
        String systemOrderNum = conventionHotelPay.getSystemOrderNum();
        if (GeneralTool.isEmpty(systemOrderNum)){
            return 0;
        }
        try {
            //主动调用微信查询订单状态接口
            WxPayOrderQueryV3Result wxPayOrderQueryV3Result = wxPayService.queryOrderV3(null, systemOrderNum);
            log.info("wxPayOrderQueryV3Result#getPayType:{}------------------------",JSONObject.toJSONString(wxPayOrderQueryV3Result));
            String tradeState = wxPayOrderQueryV3Result.getTradeState();
            String transactionId = wxPayOrderQueryV3Result.getTransactionId();
            if ("SUCCESS".equals(tradeState)){
                conventionHotelPay.setPayType(1);
                conventionHotelPay.setPayOrderNum(transactionId);
                updateUserInfoToEntity(conventionHotelPay);
                conventionHotelPayMapper.updateById(conventionHotelPay);
                log.info("更新成功：#getPayType------------------------------");
                if (GeneralTool.isNotEmpty(conventionHotelPay.getFkConventionPersonId())){
                    ConventionPerson conventionPerson = conventionPersonMapper.selectById(conventionHotelPay.getFkConventionPersonId());
                    conventionPerson.setCheckInTime(conventionHotelPay.getCheckInTime());
                    conventionPerson.setCheckOutTime(conventionHotelPay.getCheckOutTime());
                    updateUserInfoToEntity(conventionPerson);
                    conventionPersonMapper.updateById(conventionPerson);
                    log.info("更新参会人入住退房时间成功：#getPayType------------------------------");
                }
                return 1;
            }
        } catch (WxPayException e) {
            log.info("获取微信支付订单状态失败#getPayType------------------------------");
            throw new GetServiceException(LocaleMessageUtils.getMessage("failed_to_obtain_wechat_pay_order_status"));
        }
        return 0;
    }

    /**
     * 同住人下拉框
     * @param fkConventionId
     * @param fkConventionPersonId
     * @return
     */
    @Override
    public List<BaseSelectEntity> getResidentSelect(Long fkConventionId, Long fkConventionPersonId,Long fkConventionRegistrationId) {

        List<ConventionPersonRegistration> conventionPersonRegistrations = conventionPersonRegistrationMapper.selectList(Wrappers.lambdaQuery(ConventionPersonRegistration.class)
                .eq(ConventionPersonRegistration::getFkConventionRegistrationId, fkConventionRegistrationId));
        if (GeneralTool.isEmpty(conventionPersonRegistrations)){
            return Collections.emptyList();
        }
        List<Long> conventionPersonIds = conventionPersonRegistrations.stream().map(ConventionPersonRegistration::getFkConventionPersonId).collect(Collectors.toList());
        LambdaQueryWrapper<ConventionPerson> wrapper = Wrappers.lambdaQuery(ConventionPerson.class);
        if (GeneralTool.isNotEmpty(conventionPersonIds)){
            wrapper.in(ConventionPerson::getId,conventionPersonIds);
        }
        if (GeneralTool.isNotEmpty(fkConventionPersonId)){
            //非填表人
            wrapper.ne(ConventionPerson::getId,fkConventionPersonId);
        }
        List<ConventionPerson> conventionPersonList = conventionPersonMapper.selectList(wrapper.eq(ConventionPerson::getFkConventionId, fkConventionId)
                .eq(ConventionPerson::getType, 0)
                //非自行预订
                .ne(ConventionPerson::getFkConventionHotelId, 224L)
                .orderByDesc(ConventionPerson::getGmtCreate));

        if (GeneralTool.isEmpty(conventionPersonList)){
            return Collections.emptyList();
        }

        Set<Long> personIds = conventionPersonList.stream().filter(c -> {
            if (GeneralTool.isNotEmpty(c.getRemark()) && c.getRemark().startsWith("同住人:")) {
                return true;
            }
            return false;
        }).map(c -> {
            String input = c.getRemark();
            String prefix = "同住人:";
            String delimiter = ",";
            int startIndex = input.indexOf(prefix) + prefix.length();
            int endIndex = input.indexOf(delimiter, startIndex);
            if (!(startIndex != -1 && endIndex != -1)) {
                return null;
            }
            String result = input.substring(startIndex, endIndex);
            try {
                return Long.parseLong(result);
            } catch (Exception e) {
                return null;
            }
        }).filter(Objects::nonNull).collect(Collectors.toSet());

        if (GeneralTool.isNotEmpty(personIds)){
            conventionPersonList = conventionPersonList.stream().filter(c->!personIds.contains(c.getId())).collect(Collectors.toList());
        }

        return conventionPersonList.stream().map(c->{
            BaseSelectEntity baseSelectEntity = new BaseSelectEntity();
            baseSelectEntity.setId(c.getId());
            baseSelectEntity.setName(c.getName());
            baseSelectEntity.setFullName(c.getName()+(GeneralTool.isNotEmpty(c.getNameChn())?"（"+c.getNameChn()+"）":""));
            return baseSelectEntity;
        }).collect(Collectors.toList());

    }

    /**
     * 常用币种下拉
     * @return
     */
    @Override
    public List<BaseSelectEntity> getCommonCurrencySelect() {
        Result<List<CurrencyTypeVo>> result = financeCenterClient.getCurrencyByPublicLevel(ProjectExtraEnum.PUBLIC_CURRENCY_COMMON.key);
        if (!result.isSuccess()||GeneralTool.isEmpty(result.getData())) {
            return Collections.emptyList();
        }
        List<CurrencyTypeVo> currencyTypeVos = result.getData();
        return currencyTypeVos.stream().map(c->{
            BaseSelectEntity baseSelectEntity = new BaseSelectEntity();
            baseSelectEntity.setId(c.getId());
            baseSelectEntity.setNum(c.getNum());
            baseSelectEntity.setName(c.getTypeName());
            baseSelectEntity.setFullName(c.getTypeName()+"（"+c.getNum()+"）");
            return baseSelectEntity;
        }).collect(Collectors.toList());
    }

    /**
     * 保存发票信息
     * @param invoiceInfoSaveDto
     */
    @Override
    public void saveInvoiceInfo(InvoiceInfoSaveDto invoiceInfoSaveDto) {
        Long fkConventionPersonId = invoiceInfoSaveDto.getFkConventionPersonId();
        List<ConventionHotelPay> conventionHotelPays = conventionHotelPayMapper.selectList(Wrappers.lambdaQuery(ConventionHotelPay.class)
                .eq(ConventionHotelPay::getFkConventionPersonId, fkConventionPersonId)
                .orderByDesc(ConventionHotelPay::getGmtCreate));
        ConventionHotelPay conventionHotelPay = conventionHotelPays.get(0);
        List<InvoiceInfoDto> invoiceInfoDtos = invoiceInfoSaveDto.getInvoiceInfoVos();
        String invoiceInfoStr = JSONObject.toJSONString(invoiceInfoDtos);
        if (GeneralTool.isNotEmpty(conventionHotelPay)){
            conventionHotelPay.setInvoiceInfo(invoiceInfoStr);
            updateUserInfoToEntity(conventionHotelPay);
            conventionHotelPayMapper.updateById(conventionHotelPay);
        }
    }

    /**
     * 回显发票信息
     * @param fkConventionPersonId
     * @return
     */
    @Override
    public InvoiceInfoVo getInvoiceInfo(Long fkConventionPersonId) {
        if (GeneralTool.isEmpty(fkConventionPersonId)){
            return null;
        }
        InvoiceInfoVo invoiceInfoVo = new InvoiceInfoVo();
        invoiceInfoVo.setFkConventionPersonId(fkConventionPersonId);
        List<ConventionHotelPay> conventionHotelPays = conventionHotelPayMapper.selectList(Wrappers.lambdaQuery(ConventionHotelPay.class)
                .eq(ConventionHotelPay::getFkConventionPersonId, fkConventionPersonId)
                .orderByDesc(ConventionHotelPay::getGmtCreate));
        if (GeneralTool.isEmpty(conventionHotelPays)){
            return null;
        }
        ConventionHotelPay conventionHotelPay = conventionHotelPays.get(0);
        String invoiceInfo = conventionHotelPay.getInvoiceInfo();
        if (GeneralTool.isNotEmpty(invoiceInfo)){
            List<InvoiceInfoDto> invoiceInfoDtos = JSONObject.parseArray(invoiceInfo, InvoiceInfoDto.class);
            invoiceInfoVo.setInvoiceInfoVos(invoiceInfoDtos);
        }
        return invoiceInfoVo;
    }

    /**
     * 代理年会微信支付
     * @param conventionHotelPayAgentDto
     * @param response
     */
    @Override
    public void agentGenerateOrders(ConventionHotelPayAgentDto conventionHotelPayAgentDto, HttpServletResponse response) throws IOException {
        // 设置响应流信息
        response.setHeader("Pragma", "no-cache");
        response.setHeader("Cache-Control", "no-cache");
        response.setDateHeader("Expires", 0);
        OutputStream stream = response.getOutputStream();

        String orderCode = getOrderCode();
        String roomType = conventionHotelPayAgentDto.getRoomType();
//        ConventionHotel conventionHotel = conventionHotelMapper.selectOne(Wrappers.lambdaQuery(ConventionHotel.class)
//                .eq(ConventionHotel::getRoomType, roomType)
//                .eq(ConventionHotel::getFkConventionId, conventionHotelPayAgentDto.getFkConventionId()));
//        if (GeneralTool.isEmpty(conventionHotel)){
//            throw new GetServiceException(LocaleMessageUtils.getMessage("cannot_find_room_type"));
//        }

        Result<ConfigVo> config = permissionCenterClient.getConfigByKey("SUMMIT_PARTNER_FORM");
        if (!config.isSuccess()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_room_type_price_set"));
        }
        String value1 = config.getData().getValue1();

        BigDecimal price = new BigDecimal(value1);
        if (GeneralTool.isEmpty(price)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_room_type_price_set"));
        }
        ConventionHotelPay conventionHotelPay = BeanCopyUtils.objClone(conventionHotelPayAgentDto, ConventionHotelPay::new);
        assert conventionHotelPay != null;
        int days = MyDateUtils.differentDays(conventionHotelPay.getCheckInTime(), conventionHotelPay.getCheckOutTime());
        conventionHotelPay.setSystemOrderNum(orderCode);
        addUserInfoToEntity(conventionHotelPay);
        conventionHotelPay.setPayType(0);
        String remark = conventionHotelPay.getInstitutionName()+","+conventionHotelPay.getRoomType() + "," + conventionHotelPayAgentDto.getName();
        conventionHotelPay.setRemark(remark);
        conventionHotelPay.setPayCount(1);
        conventionHotelPay.setPayAmount(price.multiply(new BigDecimal((days))));
        conventionHotelPay.setResidents(conventionHotelPayAgentDto.getName());
        conventionHotelPayMapper.insert(conventionHotelPay);
        log.info("新增成功#generateOrders------------------------------");
        if (GeneralTool.isEmpty(conventionHotelPay.getId())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }

        BigDecimal amount = price.multiply(new BigDecimal((days)*100));

        WxPayUnifiedOrderV3Request request = new WxPayUnifiedOrderV3Request()
                .setAppid(wxPayService.getConfig().getAppId())
                .setMchid(wxPayService.getConfig().getMchId())
                .setDescription("酒店房费-"+ price +"/晚")
                .setOutTradeNo(orderCode)
                .setAttach(String.valueOf(conventionHotelPay.getId()))
                .setNotifyUrl("https://convention.ht-international.net/sale/annualReservationForm/notifyWeiXinAgentPay")
                .setAmount(new WxPayUnifiedOrderV3Request.Amount().setTotal(amount.intValue()));

        String result;
        try {
            result = wxPayService.createOrderV3(TradeTypeEnum.NATIVE, request);
            log.info("result#generateOrders:{}------------------------",result);
        } catch (WxPayException e) {
            log.info("微信统一下单异常#generateOrders------------------------------");
            throw new GetServiceException(LocaleMessageUtils.getMessage("wechat_unified_ordering_exception"));
        }
        //获取一个二维码图片
        BitMatrix bitMatrix = MyStringUtils.createCode(result);
//        //以流的形式输出到前端
        MatrixToImageWriter.writeToStream(bitMatrix , "jpg" , stream);
    }

    /**
     * 代理微信支付回调
     * @param signatureHeaderByHttpHeaders
     * @param notifyData
     */
    @Override
    @DSTransactional
    public void notifyWeiXinAgentPay(SignatureHeader signatureHeaderByHttpHeaders, String notifyData) throws WxPayException {
        WxPayOrderNotifyV3Result wxPayOrderNotifyV3Result = wxPayService.parseOrderNotifyV3Result(notifyData, signatureHeaderByHttpHeaders);
        WxPayOrderNotifyV3Result.DecryptNotifyResult result = wxPayOrderNotifyV3Result.getResult();
        //微信订单号
        String transactionId = result.getTransactionId();
        if (GeneralTool.isEmpty(transactionId)){
            return;
        }
        log.info("微信订单号：{}#notifyWeiXinPay------------------------------",transactionId);
        //支付信息
        String attach = result.getAttach();
        log.info("支付信息id：{}#notifyWeiXinPay------------------------------",attach);
        Long id = Long.parseLong(attach);
        ConventionHotelPay conventionHotelPay = conventionHotelPayMapper.selectById(id);
        if (GeneralTool.isNotEmpty(conventionHotelPay)) {
            if (conventionHotelPay.getPayType() == 0){
                conventionHotelPay.setPayType(1);
                conventionHotelPay.setPayOrderNum(transactionId);
                updateUserInfoToEntity(conventionHotelPay);
                conventionHotelPayMapper.updateById(conventionHotelPay);
                log.info("更新成功：#notifyWeiXinPay------------------------------");
                if (GeneralTool.isNotEmpty(conventionHotelPay.getFkConventionPersonId())){
                    ConventionPerson conventionPerson = conventionPersonMapper.selectById(conventionHotelPay.getFkConventionPersonId());
                    conventionPerson.setCheckInTime(conventionHotelPay.getCheckInTime());
                    conventionPerson.setCheckOutTime(conventionHotelPay.getCheckOutTime());
                    updateUserInfoToEntity(conventionPerson);
                    conventionPersonMapper.updateById(conventionPerson);
                    log.info("更新参会人入住退房时间成功：#notifyWeiXinPay------------------------------");
                }
            }
        }

    }

    /**
     * 获取升级房费
     * @return
     */
    @Override
    public BigDecimal getUpgradeRoomFee() {
        Result<ConfigVo> config = permissionCenterClient.getConfigByKey("SUMMIT_PARTNER_FORM");
        if (!config.isSuccess()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_room_type_price_set"));
        }
        String value1 = config.getData().getValue1();

        BigDecimal price = new BigDecimal(value1);
        if (GeneralTool.isEmpty(price)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_room_type_price_set"));
        }
        return price;
    }


    /**
     * 获取订单号
     * @return
     */
    private String getOrderCode(){
        SimpleDateFormat ft = new SimpleDateFormat("yyyyMMddHHmmss");
        String time = ft.format(new Date());
        int mathCode = (int) ((Math.random() * 9 + 1) * 10000);// 5位随机数
        String resultCode = time+mathCode;
        return resultCode;
    }

    /**
     * 保存展位
     *
     * @param annualReservationBoothDtos
     * @
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateBooths(List<AnnualReservationBoothDto> annualReservationBoothDtos) {
        String provideGiftsStr = "是否提供晚宴抽奖礼品=";
        for (AnnualReservationBoothDto annualReservationBoothDto : annualReservationBoothDtos) {
            List<ExpressInfoDto> expressInfoDtoList = annualReservationBoothDto.getExpressInfoVoList();
            //转换为快递信息
            StringBuilder expressInfo = transExpressVoList2ExpressInfo(expressInfoDtoList);
            if (GeneralTool.isNotEmpty(annualReservationBoothDto.getProvideGifts())) {
                if (annualReservationBoothDto.getProvideGifts() == 0) {
                    expressInfo.append(";").append("[").append(provideGiftsStr).append("否").append("]");
                } else if (annualReservationBoothDto.getProvideGifts() == 1) {
                    expressInfo.append(";").append("[").append(provideGiftsStr).append("是").append("]");
                }
            }
            ConventionRegistration conventionRegistration = conventionRegistrationMapper.selectById(annualReservationBoothDto.getConventionRegistrationId());
            if (GeneralTool.isNotEmpty(annualReservationBoothDto.getModifyName())) {
                conventionRegistration.setBoothName(annualReservationBoothDto.getModifyName());
            }
            conventionRegistration.setExpressInfo(expressInfo.toString());
            updateUserInfoToEntity(conventionRegistration);
            conventionRegistrationMapper.updateById(conventionRegistration);
        }
    }

    /**
     * 将快递信息转换为StringBuilder
     *
     * @param expressInfoDtoList
     * @return
     */
    private StringBuilder transExpressVoList2ExpressInfo(List<ExpressInfoDto> expressInfoDtoList) {
        StringBuilder expressInfo = new StringBuilder();
        for (ExpressInfoDto expressInfoDto : expressInfoDtoList) {
            if (!"".equals(expressInfo.toString())) {
                expressInfo.append(";");
            }
            if (GeneralTool.isNotEmpty(expressInfoDto.getCourierCompany())
                    && GeneralTool.isNotEmpty(expressInfoDto.getCourierNumber())
                    && GeneralTool.isNotEmpty(expressInfoDto.getPieces()) && !"null".equals(expressInfoDto.getPieces().toString())) {
                expressInfo.append("[").append(expressInfoDto.getCourierCompany()).append("]")
                        .append("[").append(expressInfoDto.getCourierNumber()).append("]")
                        .append("[").append(expressInfoDto.getPieces()).append("]");
                //添加内容备注
                if (GeneralTool.isNotEmpty(expressInfoDto.getRemark())) {
                    expressInfo.append("[").append(expressInfoDto.getRemark()).append("]");
                } else {
                    expressInfo.append("[").append("]");
                }
            }
        }
        return expressInfo;
    }


    /**
     * 查看参会人选择的流程ids
     *
     * @param conventionPersonId
     * @return
     */
    private List<Long> getConventionProcedureIds(Long conventionPersonId) {
//        Example example = new Example(ConventionPersonProcedure.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkConventionPersonId", conventionPersonId);
//        List<ConventionPersonProcedure> conventionPersonProcedures = conventionPersonProcedureMapper.selectByExample(example);

        LambdaQueryWrapper<ConventionPersonProcedure> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ConventionPersonProcedure::getFkConventionPersonId, conventionPersonId);
        List<ConventionPersonProcedure> conventionPersonProcedures = conventionPersonProcedureMapper.selectList(lambdaQueryWrapper);

        List<Long> ids = new ArrayList<>();
        if (GeneralTool.isEmpty(conventionPersonProcedures)) {
            //ids.add(0L);
            return ids;
        }
        for (ConventionPersonProcedure conventionPersonProcedure : conventionPersonProcedures) {
            ids.add(conventionPersonProcedure.getFkConventionProcedureId());
        }
        return ids;
    }

    /**
     * 新增/修改参会人
     *
     * @param annualReservationFormDto
     * @
     */
    @Transactional(rollbackFor = Exception.class)
    public void addConventionPerson(AnnualReservationFormDto annualReservationFormDto) {
//        if (GeneralTool.isEmpty(annualReservationFormDto.getReceiptCode())) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("receipt_code_error"));
//        }

        List<ConventionProcedure> conventionProcedures = conventionProcedureMapper.selectList(Wrappers.<ConventionProcedure>lambdaQuery().in(ConventionProcedure::getId, annualReservationFormDto.getConventionProcedureIds())
                .eq(ConventionProcedure::getFkTableTypeKey, ProjectKeyEnum.CONVENTION_DINNER_TABLE.key));
        boolean isAttendDinner = GeneralTool.isNotEmpty(conventionProcedures);

        //区分新增还是修改
        if (GeneralTool.isNotEmpty(annualReservationFormDto.getId())) {
            //修改-更新参会人信息
            ConventionPerson conventionPerson = conventionPersonMapper.selectById(annualReservationFormDto.getId());
            if (GeneralTool.isEmpty(conventionPerson)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("convention_person_not_found"));
            }
            if (!validatedTel(annualReservationFormDto)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("phone_number_is_been_used"));
            }
            setInfo(annualReservationFormDto, conventionPerson);
            conventionPerson.setIsBookHotel(true);
            conventionPerson.setIsAttendDinner(isAttendDinner);
            updateUserInfoToEntity(conventionPerson);
            conventionPersonMapper.updateByIdWithNull(conventionPerson);
            //流程信息
            addConventionPersonProcedure(annualReservationFormDto.getConventionProcedureIds(), conventionPerson.getId());
        } else {
            //新增
            ConventionPerson conventionPerson = new ConventionPerson();
            //根据回执码获取峰会id
            Long conventionId = annualReservationFormDto.getFkConventionId();
            //根据峰会id查出所有房间类型，获取其中指定房型的id
            conventionPerson.setFkConventionHotelId(annualReservationFormDto.getFkConventionHotelId());
            conventionPerson.setFkConventionId(conventionId);
            if (!validatedTel(annualReservationFormDto)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("phone_number_is_been_used"));
            }
            setInfo(annualReservationFormDto, conventionPerson);
            //添加人信息
            addUserInfoToEntity(conventionPerson);
            conventionPerson.setIsBookHotel(true);
            conventionPerson.setIsAttendDinner(isAttendDinner);
            conventionPersonMapper.insertSelective(conventionPerson);
            long personId = conventionPerson.getId();
            //设置参会人员编号  用新增的id生成编号
            conventionPerson.setNum(MyStringUtils.getConventionPersonNum(conventionPerson.getId()));
            updateUserInfoToEntity(conventionPerson);
            conventionPersonMapper.updateById(conventionPerson);
            insertTable(conventionPerson.getId(), annualReservationFormDto);
            //选择参加流程-更新关联表
            addConventionPersonProcedure(annualReservationFormDto.getConventionProcedureIds(), personId);
        }
    }


    /**
     * 选择/修改流程信息
     *
     * @param conventionProcedureIds
     * @param personId
     * @
     */
    @Transactional(rollbackFor = Exception.class)
    public void addConventionPersonProcedure(List<Long> conventionProcedureIds, long personId) {
        //先删除 - 后新增
//        Example example = new Example(ConventionPersonProcedure.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkConventionPersonId", personId);
//        List<ConventionPersonProcedure> conventionPersonProcedures = conventionPersonProcedureMapper.selectByExample(example);
        LambdaQueryWrapper<ConventionPersonProcedure> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ConventionPersonProcedure::getFkConventionPersonId, personId);
        List<ConventionPersonProcedure> conventionPersonProcedures = conventionPersonProcedureMapper.selectList(lambdaQueryWrapper);
        if (GeneralTool.isNotEmpty(conventionPersonProcedures)) {
            //删除
            conventionPersonProcedureMapper.delete(lambdaQueryWrapper);
        }
        for (Long conventionProcedureId : conventionProcedureIds) {
//            if (conventionProcedureId==0){
//                break;
//            }
            ConventionPersonProcedure conventionPersonProcedure = new ConventionPersonProcedure();
            conventionPersonProcedure.setFkConventionProcedureId(conventionProcedureId);
            conventionPersonProcedure.setFkConventionPersonId(personId);
            //设置时间
            addUserInfoToEntity(conventionPersonProcedure);
            //添加
            conventionPersonProcedureMapper.insertSelective(conventionPersonProcedure);
        }
    }


    private String setName(String lastName,String firstName){
        String nameChn = "";
        if (StringUtils.isNotBlank(lastName)) {
            nameChn = lastName;
        }
        if (StringUtils.isNotBlank(firstName)) {
            nameChn += " " + firstName;
        }
        if (CommonUtil.isContainsChinese(nameChn)) {
            nameChn = nameChn.replace(" ","");
        }
        return nameChn.trim();
    }
    /**
     * 设置属性值
     *
     * @param
     * @param conventionPerson
     */
    private void setInfo(AnnualReservationFormDto annualReservationFormDto, ConventionPerson conventionPerson) {
        ConventionRegistration conventionRegistration = getConventionRegistration(annualReservationFormDto.getConventionRegistrationId());
        //校代为类型0
        conventionPerson.setType(0);

//        conventionPerson.setNameChn(setName(annualReservationFormDto.getLastNameChn(),annualReservationFormDto.getFirstNameChn()));
//        conventionPerson.setName(setName(annualReservationFormDto.getLastName(),annualReservationFormDto.getFirstName()));
        conventionPerson.setNameChn(annualReservationFormDto.getNameChn());
        if (GeneralTool.isEmpty(annualReservationFormDto.getName())){
            conventionPerson.setName(annualReservationFormDto.getNameChn());
        }else {
            conventionPerson.setName(annualReservationFormDto.getName());
        }
        if (GeneralTool.isNotEmpty(annualReservationFormDto.getResidentName())){
//            ConventionPerson person = conventionPersonMapper.selectById(annualReservationFormDto.getResidentId());
//            if (GeneralTool.isEmpty(person)){
//                throw new GetServiceException(LocaleMessageUtils.getMessage("the_cohabitant_does_not_exist"));
//            }
            String remark = "同住人:"+annualReservationFormDto.getResidentName()+(GeneralTool.isNotEmpty(annualReservationFormDto.getResidentNChn())?"（"+annualReservationFormDto.getResidentNChn()+"）":"");
            if (GeneralTool.isNotEmpty(conventionPerson.getRemark())){
                String personRemark = conventionPerson.getRemark();
//                List<String> strList = MyStringUtils.getContentBetweenMiddleBrackets(personRemark);
//                for (String s : strList) {
//                    if (s.contains("同住人")){
//                        personRemark = personRemark.replace(s,remark);
//                    }
//                }
//                conventionPerson.setRemark(personRemark+"["+remark+"]");
                // 移除所有同住人相关信息
                if (GeneralTool.isNotEmpty(personRemark)) {
                    personRemark = personRemark.replaceAll("\\[同住人:[^\\[\\]]*（[^\\[\\]]*）\\]", "");
                    // 清理多余的空括号对
                    personRemark = personRemark.replaceAll("\\[\\]", "");
                }

                // 添加新的同住人信息
                if (GeneralTool.isNotEmpty(remark)) {
                    if (GeneralTool.isNotEmpty(personRemark) && !personRemark.equals("[]")) {
                        personRemark += "[" + remark + "]";
                    } else {
                        personRemark = "[" + remark + "]";
                    }
                }

                // 如果最终结果为空，则设置为null或空字符串
                if (GeneralTool.isEmpty(personRemark) || personRemark.equals("[]")) {
                    personRemark = "";
                }

                conventionPerson.setRemark(personRemark);
            }else {
                conventionPerson.setRemark("["+remark+"]");
            }
        }else {
            if (GeneralTool.isNotEmpty(conventionPerson.getRemark())){
                String personRemark = conventionPerson.getRemark();
                List<String> strList = MyStringUtils.getContentBetweenMiddleBrackets(personRemark);
                for (String s : strList) {
                    if (s.contains("同住人")){
                        personRemark = personRemark.replaceAll("\\[同住人:[^\\[\\]]*（[^\\[\\]]*）\\]", "");
                        // 清理多余的空括号对
                        personRemark = personRemark.replaceAll("\\[\\]", "");
                    }
                }
                conventionPerson.setRemark(personRemark);
            }
        }
        conventionPerson.setGender(annualReservationFormDto.getGender());
        conventionPerson.setTitle(annualReservationFormDto.getTitle());
        conventionPerson.setEmail(annualReservationFormDto.getEmail());
        conventionPerson.setTel(annualReservationFormDto.getTel());
        conventionPerson.setCompany(conventionRegistration.getProviderName());
        conventionPerson.setCheckInTime(annualReservationFormDto.getCheckInTime());
        conventionPerson.setCheckOutTime(annualReservationFormDto.getCheckOutTime());
        conventionPerson.setArrivalTime(annualReservationFormDto.getArrivalTime());
        conventionPerson.setArrivalTransportationCode(annualReservationFormDto.getArrivalTransportationCode());
        conventionPerson.setLeaveTime(annualReservationFormDto.getLeaveTime());
        conventionPerson.setLeaveTransportationCode(annualReservationFormDto.getLeaveTransportationCode());
        conventionPerson.setFkConventionHotelId(annualReservationFormDto.getFkConventionHotelId());
        conventionPerson.setIsAttend(false);
        //枚举获取值
        String arrivalTransportation = ProjectExtraEnum.getInitialValueByKey(annualReservationFormDto.getArrivalTransportation(), ProjectExtraEnum.TRANSPORTATION);
        conventionPerson.setArrivalTransportation(arrivalTransportation);
        String leaveTransportation = ProjectExtraEnum.getInitialValueByKey(annualReservationFormDto.getLeaveTransportation(), ProjectExtraEnum.TRANSPORTATION);
        conventionPerson.setLeaveTransportation(leaveTransportation);
//        if (GeneralTool.isNotEmpty(annualReservationFormDto.getDocumentType())) {
//            switch (annualReservationFormDto.getDocumentType()) {
//                //0身份证1护照2其他
//                case 0:
//                    conventionPerson.setIdCardNum(annualReservationFormDto.getDocumentContent());
//                    conventionPerson.setPassportNum("");
//                    conventionPerson.setRemark("");
//                    break;
//                case 1:
//                    conventionPerson.setIdCardNum("");
//                    conventionPerson.setPassportNum(annualReservationFormDto.getDocumentContent());
//                    conventionPerson.setRemark("");
//                    break;
//                case 2:
//                    conventionPerson.setIdCardNum("");
//                    conventionPerson.setPassportNum("");
//                    conventionPerson.setRemark(annualReservationFormDto.getDocumentContent());
//                    break;
//                default:
//                    conventionPerson.setIdCardNum("");
//                    conventionPerson.setPassportNum("");
//                    conventionPerson.setRemark("");
//                    break;
//            }
//        } else {
//            conventionPerson.setIdCardNum("");
//            conventionPerson.setPassportNum("");
//            conventionPerson.setRemark("");
//        }
    }

    /**
     * 根据房间名称和峰会id返回房型id
     *
     * @param roomTypeName
     * @param conventionId
     * @return
     */
    private Long getConventionHotelByRoomTypeAndConventionId(String roomTypeName, Long conventionId) {
//        Example example = new Example(ConventionHotel.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("roomType", roomTypeName);
//        criteria.andEqualTo("fkConventionId", conventionId);
//        List<ConventionHotel> conventionHotels = conventionHotelMapper.selectByExample(example);

        List<ConventionHotel> conventionHotels = conventionHotelMapper.selectList(Wrappers.<ConventionHotel>lambdaQuery()
                .eq(ConventionHotel::getRoomType, roomTypeName)
                .eq(ConventionHotel::getFkConventionId, conventionId));
        if (conventionHotels.size() != 1) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("room_type_name_not_found"));
        }
        return conventionHotels.get(0).getId();
    }

    /**
     * 设置创建信息
     *
     * @param entity
     * @
     */
    public void addUserInfoToEntity(BaseEntity entity) {
        formCommonService.addUserInfoToEntity(entity);
    }

    /**
     * 设置更新信息
     *
     * @param entity
     * @
     */
    public void updateUserInfoToEntity(BaseEntity entity) {
        formCommonService.updateUserInfoToEntity(entity);
    }

    /**
     * 关联表保存；参会人和报名名册
     *
     * @param id
     * @param annualReservationFormDto
     */
    @Transactional(rollbackFor = Exception.class)
    public void insertTable(Long id, AnnualReservationFormDto annualReservationFormDto) {
        //校方-r_convention_person_registration 中间表对象
        ConventionPersonRegistration conventionPersonRegistration = new ConventionPersonRegistration();
        //参展人员id为刚插入成功返回的id
        conventionPersonRegistration.setFkConventionPersonId(id);
        //所属报名名册的id
        if (GeneralTool.isEmpty(annualReservationFormDto.getConventionRegistrationId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("convention_registration_id_is_null"));
        }
        conventionPersonRegistration.setFkConventionRegistrationId(annualReservationFormDto.getConventionRegistrationId());
        updateUserInfoToEntity(conventionPersonRegistration);
        conventionPersonRegistrationMapper.insert(conventionPersonRegistration);
    }

    /**
     * 关联表删除：参会人和报名名册
     *
     * @param id
     */
    public void deleteTable(Long id) {
        formCommonService.deleteTable(id);
    }

    /**
     * 回显设置参会人信息
     *
     * @param conventionPerson
     * @return
     */
    private AnnualReservationFormVo setAnnualHotelReservationDto(ConventionPerson conventionPerson) {
        AnnualReservationFormVo annualReservationFormVo = new AnnualReservationFormVo();


        if (GeneralTool.isNotEmpty(conventionPerson.getName())) {
            annualReservationFormVo.setName(conventionPerson.getName());
        }
        if (GeneralTool.isNotEmpty(conventionPerson.getNameChn())) {
            annualReservationFormVo.setNameChn(conventionPerson.getNameChn());
        }
//        if (GeneralTool.isNotEmpty(conventionPerson.getName())) {
//            String personName = conventionPerson.getName();
//            String[] s = personName.trim().split(" ");
//            annualReservationFormVo.setFirstName(s[0]);
//            annualReservationFormVo.setLastName(s[1]);
//        }
//        if (GeneralTool.isNotEmpty(conventionPerson.getNameChn())) {
//            String personNameChn = conventionPerson.getNameChn();
//            String[] s = personNameChn.trim().split(" ");
//            if (s.length >= 2) {
//                annualReservationFormVo.setFirstNameChn(s[1]);
//                annualReservationFormVo.setLastNameChn(s[0]);
//            } else {
//                annualReservationFormVo.setFirstNameChn(personNameChn);
//            }
//        }
        annualReservationFormVo.setEmail(conventionPerson.getEmail());
        annualReservationFormVo.setGender(conventionPerson.getGender());
        annualReservationFormVo.setTitle(conventionPerson.getTitle());
        annualReservationFormVo.setTel(conventionPerson.getTel());
        //日期设置
        annualReservationFormVo.setCheckInTime(conventionPerson.getCheckInTime());
        annualReservationFormVo.setCheckOutTime(conventionPerson.getCheckOutTime());
        //设置展位名
        Long conventionRegistrationId = conventionPersonRegistrationMapper.getConventionRegistrationId(conventionPerson.getId());
        String boothName = conventionRegistrationMapper.getBoothNameById(conventionRegistrationId);
        annualReservationFormVo.setBoothName(boothName);
        annualReservationFormVo.setArrivalTime(conventionPerson.getArrivalTime());
        Map<String, Integer> transportationMap = new HashMap<>();
        for (ProjectExtraEnum transportation : ProjectExtraEnum.TRANSPORTATION) {
            transportationMap.put(transportation.value, transportation.key);
        }
        annualReservationFormVo.setArrivalTransportation(transportationMap.get(conventionPerson.getArrivalTransportation()));
        annualReservationFormVo.setArrivalTransportationCode(conventionPerson.getArrivalTransportationCode());
        annualReservationFormVo.setLeaveTime(conventionPerson.getLeaveTime());
        annualReservationFormVo.setLeaveTransportation(transportationMap.get(conventionPerson.getLeaveTransportation()));
        annualReservationFormVo.setLeaveTransportationCode(conventionPerson.getLeaveTransportationCode());
//        if (GeneralTool.isNotEmpty(conventionPerson.getRemark())) {
//            annualReservationFormVo.setDocumentType(2);
//            annualReservationFormVo.setDocumentContent(conventionPerson.getRemark());
//        } else if (GeneralTool.isNotEmpty(conventionPerson.getPassportNum())) {
//            annualReservationFormVo.setDocumentType(1);
//            annualReservationFormVo.setDocumentContent(conventionPerson.getPassportNum());
//        } else {
//            annualReservationFormVo.setDocumentType(0);
//            annualReservationFormVo.setDocumentContent(conventionPerson.getIdCardNum());
//        }

        //设置同住人id
        String input = conventionPerson.getRemark();
        if (GeneralTool.isNotEmpty(input)){
            // 定义正则表达式，提取两个字段值
            // 第一个分组匹配testA001部分，第二个分组匹配可选的testA002部分
            Pattern pattern = Pattern.compile("\\[同住人:(.+?)(?:（(.+?)）)?\\]");
            Matcher matcher = pattern.matcher(input);

            if (matcher.find()) {
                String value1 = matcher.group(1);
                // 当第二个分组不存在时，返回空字符串
                String value2 = matcher.group(2) != null ? matcher.group(2) : "";
                annualReservationFormVo.setResidentName(value1);
                annualReservationFormVo.setResidentNChn(value2);
            }

//            String prefix = "同住人:";
//            String delimiter = ",";
//            int startIndex = input.indexOf(prefix) + prefix.length();
//            int endIndex = input.indexOf(delimiter, startIndex);
//            if (startIndex != -1 && endIndex != -1) {
//                String result = input.substring(startIndex, endIndex);
//                try {
//                    annualReservationFormVo.setResidentId(Long.parseLong(result));
//                } catch (Exception e) {
//                    annualReservationFormVo.setResidentId(null);
//                }
//            }
        }
        return annualReservationFormVo;
    }

    /**
     * 根据参会人id获取中间表
     *
     * @param conventionPersonId
     * @return
     */
    private ConventionPersonRegistration getConventionPersonRegistration(Long conventionPersonId) {
        return formCommonService.getConventionPersonRegistration(conventionPersonId);
    }

    /**
     * 根据报名名册id获取机构名
     *
     * @param conventionRegistrationId
     * @return
     */
    private ConventionRegistration getConventionRegistration(Long conventionRegistrationId) {
        ConventionRegistration conventionRegistration = conventionRegistrationMapper.selectById(conventionRegistrationId);
        if (GeneralTool.isEmpty(conventionRegistration.getProviderName())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("provider_name_is_null"));
        }
        return conventionRegistration;
    }


    /**
     * 验证手机唯一
     *
     * @param annualReservationFormDto
     * @return
     */
    private Boolean validatedTel(AnnualReservationFormDto annualReservationFormDto) {
        LambdaQueryWrapper<ConventionPerson> lambdaQueryWrapper = Wrappers.<ConventionPerson>lambdaQuery();
        lambdaQueryWrapper.eq(ConventionPerson::getFkConventionId, annualReservationFormDto.getFkConventionId());
        lambdaQueryWrapper.eq(ConventionPerson::getTel, annualReservationFormDto.getTel());
        if (GeneralTool.isNotEmpty(annualReservationFormDto.getId())) {
            //修改情况时
            lambdaQueryWrapper.ne(ConventionPerson::getId, annualReservationFormDto.getId());
        }
        List<ConventionPerson> conventionPeople = conventionPersonMapper.selectList(lambdaQueryWrapper);

        return GeneralTool.isEmpty(conventionPeople);
    }
}

package com.get.salecenter.controller;

import com.get.common.cache.CacheNames;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.redis.lock.RedisLock;
import com.get.salecenter.vo.ConventionPersonProcedureVo;
import com.get.salecenter.vo.ConventionProcedureVo;
import com.get.salecenter.service.IConventionPersonProcedureService;
import com.get.salecenter.dto.ConventionPersonProcedureDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/7/15 15:34
 * @verison: 1.0
 * @description: 峰会参展人员-参会流程配置管理控制器
 */
@Api(tags = "峰会参展人员-参会流程配置管理")
@RestController
@RequestMapping("sale/conventionProcedureConfiguration")
public class ConventionPersonProcedureController {

    @Resource
    private IConventionPersonProcedureService conventionPersonProcedureService;

    /**
     * 删除参会流程配置
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除参会流程配置", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/峰会参展人员-参会流程配置管理/删除参会流程配置")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        conventionPersonProcedureService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * 参会流程配置
     *
     * @param conventionPersonProcedureDtos
     * @return
     * @
     */
    @RedisLock(value = CacheNames.CONVENTION_PROCEDURE_CONFIGURATION, param = "#conventionPersonId", waitTime = 5L)
    @ApiOperation(value = "参会流程配置", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/峰会参展人员-参会流程配置管理/新增参会流程配置")
    @PostMapping("conventionProcedureConfiguration")
    public ResponseBo conventionProcedureConfiguration(@RequestBody List<ConventionPersonProcedureDto> conventionPersonProcedureDtos, @RequestParam("conventionPersonId")Long conventionPersonId) {
        conventionPersonProcedureService.conventionProcedureConfiguration(conventionPersonProcedureDtos);
        return ResponseBo.ok();
    }

    /**
     * 参会流程配置列表
     *
     * @param conventionId
     * @param personId
     * @return
     * @
     */
    @ApiOperation(value = "参会流程配置列表", notes = "conventionId峰会id personId参会人员id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/峰会参展人员-参会流程配置管理/查询参会流程配置")
    @PostMapping("datas")
    public ResponseBo<ConventionProcedureVo> datas(@RequestParam Long conventionId, @RequestParam Long personId) {
        List<ConventionProcedureVo> datas = conventionPersonProcedureService.getConventionPersonProcedure(conventionId, personId);
        return new ListResponseBo<>(datas);
    }

    /**
     * 流程详情-包含人员查询条件
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "配置流程详情接口-包含人员查询条件", notes = "id为此条数据id,nameKey为姓名搜索条件,companyKey为公司搜索条件")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/峰会参展人员-参会流程配置管理/峰会流程详情-包含人员查询条件")
    @PostMapping("/detail")
    public ListResponseBo<ConventionPersonProcedureVo> detail(@RequestBody SearchBean<ConventionPersonProcedureDto> page) {
        List<ConventionPersonProcedureVo> datas = conventionPersonProcedureService.getConventionPerson(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 导出峰会流程参会人员列表
     *
     * @param conventionPersonProcedureDto
     * @return
     * @
     */
    @ApiOperation(value = "导出峰会流程参会人员列表", notes = "id为此条数据id,nameKey为姓名搜索条件,companyKey为公司搜索条件")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/峰会参展人员-参会流程配置管理/导出峰会流程参会人员列表")
    @PostMapping("/exportConventionPersonProcedureDetail")
    public void exportConventionPersonProcedureDetail(HttpServletResponse response,@RequestBody ConventionPersonProcedureDto conventionPersonProcedureDto) {
        conventionPersonProcedureService.exportConventionPersonProcedureDetail(response, conventionPersonProcedureDto);
    }

}

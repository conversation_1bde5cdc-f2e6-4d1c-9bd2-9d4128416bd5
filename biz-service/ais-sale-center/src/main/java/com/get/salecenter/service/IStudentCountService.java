package com.get.salecenter.service;


import com.get.salecenter.vo.StudentCountVo;
import com.get.salecenter.vo.WorldHistogramVo;
import com.get.salecenter.vo.WorldMapVo;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2021/2/2
 * @TIME: 10:02
 * @Description:
 **/
public interface IStudentCountService {

    /**
     * @return java.util.List<com.get.salecenter.vo.StudentCountVo>
     * @Description: 获取学习计划新增记录
     * @Param [companyId]
     * <AUTHOR>
     */
    List<StudentCountVo> getStudentCountRecord(List<Long> companyId, List<Long> areaCountryIds, String year);

    /**
     * @return java.lang.Long
     * @Description: 申请总数
     * @Param [companyId]
     * <AUTHOR>
     */
    Long getStudentTotalSum(List<Long> companyId, List<Long> areaCountryIds, String year, Boolean isStudentOfferItemFinancialHiding,List<Long> staffFollowerIds,Boolean isStudentAdmin);

    /**
     * @return com.get.salecenter.vo.WorldHistogramVo
     * @Description: 学生分布世界柱状图
     * @Param []
     * <AUTHOR>
     **/
    List<WorldHistogramVo> getWorldHistogram(List<Long> companyId, List<Long> areaCountryIds, String year, Boolean statisticsFlag);

    /**
     * 所有学生分布世界柱状图
     *
     * @Date 19:36 2021/10/25
     * <AUTHOR>
     */
    List<WorldHistogramVo> getAllWorldHistogram(List<Long> companyId, String year);

    /**
     * @return com.get.salecenter.vo.WorldHistogramVo
     * @Description: 学生分布州省数量
     * @Param []
     * <AUTHOR>
     **/
    List<WorldHistogramVo> getStateStudentNum(List<Long> companyId, List<Long> areaCountryIds, List<Long> stateIds, String year, Boolean statisticsFlag, Boolean isStudentOfferItemFinancialHiding, List<Long> staffFollowerIds);

    /**
     * @return java.util.List<com.get.salecenter.vo.WorldMapVo>
     * @Description: 获取世界地图
     * @Param [statisticsFlag  true:统计申请计划数  false:统计学生数]
     * <AUTHOR>
     **/
    List<WorldMapVo> getWorldMapDtos(List<Long> companyId, List<Long> areaCountryIds, String year, Boolean statisticsFlag);

    /**
     * 学生分布城市数量
     *
     * @param companyIds
     * @param cityIds
     * @param statisticsFlag
     * @param isStudentOfferItemFinancialHiding
     * @return
     */
    List<WorldHistogramVo> getCityStudentNum(List<Long> companyIds, List<Long> areaCountryIds, List<Long> cityIds, String year, Boolean statisticsFlag, Boolean isStudentOfferItemFinancialHiding, List<Long> staffFollowerIds);

    /**
     * 系统首页数据-获取总学生申请数
     *
     * @Date 11:33 2022/8/2
     * <AUTHOR>
     */
    Long getStudentItemTotalSum(List<Long> companyIds, List<Long> countryIds, String year, Boolean isStudentOfferItemFinancialHiding,List<Long> staffFollowerIds,Boolean isStudentAdmin);
}

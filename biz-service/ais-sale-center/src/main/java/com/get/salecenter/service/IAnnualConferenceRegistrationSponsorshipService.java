package com.get.salecenter.service;


import com.get.salecenter.vo.AnnualConferenceRegistrationSponsorshipVo;
import com.get.salecenter.dto.AnnualConferenceRegistrationSponsorshipDto;

import java.util.List;

/**
 * @author: Sea
 * @create: 2021/4/29 11:20
 * @verison: 1.0
 * @description:
 */
public interface IAnnualConferenceRegistrationSponsorshipService {
    /**
     * @return void
     * @Description :新增
     * @Param [annualConferenceRegistrationSponsorshipDto]
     * <AUTHOR>
     */
    void addAnnualConferenceRegistrationSponsorship(AnnualConferenceRegistrationSponsorshipDto annualConferenceRegistrationSponsorshipDto);

    /**
     * @return void
     * @Description :通过fkid删除
     * @Param [annualConferenceRegistrationId]
     * <AUTHOR>
     */
    void deleteByFkid(Long annualConferenceRegistrationId);

    /**
     * @return java.util.List<com.get.salecenter.vo.AnnualConferenceRegistrationSponsorshipVo>
     * @Description :根据id查找对应赞助对象集合
     * @Param [annualConferenceRegistrationId]
     * <AUTHOR>
     */
    List<AnnualConferenceRegistrationSponsorshipVo> getSponsorshipDto(Long annualConferenceRegistrationId);

    /**
     * @return java.util.List<com.get.salecenter.vo.AnnualConferenceRegistrationSponsorshipVo>
     * @Description :赞助次数是否剩余
     * @Param [annualConferenceRegistrationSponsorshipVos]
     * <AUTHOR>
     */
    Boolean soldOut(Long sponsorshipConfigId, Integer initNum);
}

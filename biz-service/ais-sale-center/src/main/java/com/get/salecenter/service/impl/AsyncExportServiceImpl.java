package com.get.salecenter.service.impl;

import cn.hutool.core.io.IoUtil;
import cn.hutool.poi.excel.BigExcelWriter;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.FocExportVo;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.CommonUtil;
import com.get.common.utils.LocaleMessageUtils;
import com.get.common.utils.UnderlineToCamelUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.secure.StaffInfo;
import com.get.core.secure.UserInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.tool.utils.RequestHeaderHandler;
import com.get.file.utils.FileUtils;
import com.get.filecenter.dto.FileDto;
import com.get.filecenter.dto.SaleFileDto;
import com.get.filecenter.feign.IFileCenterClient;
import com.get.filecenter.vo.FileVo;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.entity.StaffDownload;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.StaffVo;
import com.get.salecenter.component.FileDownloadHelper;
import com.get.salecenter.dao.convention.GoproNucleicAcidMapper;
import com.get.salecenter.dao.sale.*;
import com.get.salecenter.dto.*;
import com.get.salecenter.dto.query.StudentListQueryDto;
import com.get.salecenter.dto.query.StudentOfferItemListQueryDto;
import com.get.salecenter.entity.*;
import com.get.salecenter.service.*;
import com.get.salecenter.utils.ConvertUtils;
import com.get.salecenter.vo.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class AsyncExportServiceImpl implements AsyncExportService {

    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Resource
    private IStudentService studentService;

    @Resource
    private StudentEducationLevelTypeMapper studentEducationLevelTypeMapper;

    @Resource
    private IFileCenterClient fileCenterClient;

    @Resource
    private IStudentOfferItemService studentOfferItemService;

    @Resource
    @Qualifier(value = "saleFileDownloadTaskExecutor")
    private ThreadPoolTaskExecutor taskExecutor;

    @Resource
    private IInstitutionCenterClient institutionCenterClient;

    @Resource
    private IAgentService agentService;

    @Resource
    private EnrolFailureReasonMapper enrolFailureReasonMapper;

    @Resource
    private StudentOfferItemStepMapper studentOfferItemStepMapper;

    @Resource
    private StudentProjectRoleStaffMapper studentProjectRoleStaffMapper;

    @Resource
    private GoproNucleicAcidMapper goproNucleicAcidMapper;

    @Resource
    private MediaAndAttachedMapper mediaAndAttachedMapper;

    @Resource
    private FileDownloadHelper fileDownloadHelper;

    @Resource
    private ClientMapper clientMapper;

    @Resource
    private ClientEventMapper clientEventMapper;

    @Resource
    private IStudentProjectRoleStaffService projectRoleStaffService;

    @Resource
    private AgentStaffMapper agentStaffMapper;

    @Resource
    private ClientOfferStepMapper clientOfferStepMapper;
    @Resource
    @Lazy
    private KpiPlanService kpiPlanService;

    @Resource
    private StudentOfferItemMapper studentOfferItemMapper;


    /**
     * 导出学生信息Excel
     *
     * @param studentListQueryDto
     * @param focExportVos
     * @param headerMap
     * @param user
     * @param locale
     * @param countryIds
     */
    @Async
    @Override
    public void exportStudentList(StudentListQueryDto studentListQueryDto, List<FocExportVo> focExportVos, Map<String, String> headerMap, UserInfo user, String locale, List<Long> countryIds) {
        RequestHeaderHandler.setHeaderMap(headerMap);
        log.info("ExportStudentInfoExcel userInfo{}", user);
         Long staffId = user.getStaffId();
        StaffDownload download = new StaffDownload();
        try {
            long time = System.currentTimeMillis();
            Map<Long, String> cName = permissionCenterClient.getCompanyNamesByIds(new HashSet<>(studentListQueryDto.getFkCompanyIds())).getData();
            Collection<String> values = cName.values();
            CommonUtil<StudentListQueryDto> commonUtil = new CommonUtil<>();
            String description = commonUtil.getFiledValue(studentListQueryDto, StudentListQueryDto.class);
            download.setFkStaffId(staffId)
                    .setOptKey(FileTypeEnum.D_LIST_EXPORT.key)
                    .setOptDescription(String.format("【学生管理】《学生列表》 公司=%s%s", values, description))
                    .setStatus(1)
                    .setGmtCreate(new Date());
            download.setGmtCreateUser(user.getLoginId());
            //初始化下载记录
            download.setId(permissionCenterClient.addDownloadRecord(download));
            String[] times = {"0", "0"};//[0]-o-主SQL执行时间,[1]-f-远程调用时间
            studentListQueryDto.setPageNumber(0);
            studentListQueryDto.setPageSize(null);
            //TODO 慢
//            long startTime = System.currentTimeMillis();
            studentListQueryDto.setIsExport(true);
            List<StudentVo> studentVos = studentService.getStudents(studentListQueryDto, times, staffId, locale, countryIds);
//            long endTime = System.currentTimeMillis();
//            System.out.println("=======studentService.getStudents执行时间:============"+(String.valueOf(endTime - startTime)));
            List<StudentExportVo> studentExportVoList = new ArrayList<>();
            List<StudentEducationLevelType> studentEducationLevelTypes = studentEducationLevelTypeMapper.selectList(Wrappers.lambdaQuery());
            Map<Long, String> educationMap = studentEducationLevelTypes.stream().collect(Collectors.toMap(StudentEducationLevelType::getId, StudentEducationLevelType::getTypeNameChn));
            for (StudentVo studentVo : studentVos) {
                StudentExportVo studentExportVo = BeanCopyUtils.objClone(studentVo, StudentExportVo::new);
                //处理性别
                studentExportVo.setGender("男");
                if ("0".equals(studentVo.getGender())) {
                    studentExportVo.setGender("女");
                }
                String educationLevelType = studentVo.getEducationLevelType();
                String educationLevelType2 = studentVo.getEducationLevelType2();
                if (StringUtils.isNotBlank(educationLevelType) && StringUtils.isNumeric(educationLevelType)) {
                    studentExportVo.setEducationLevelType(educationMap.get(Long.valueOf(educationLevelType)));
                }
                if (StringUtils.isNotBlank(educationLevelType2) && StringUtils.isNumeric(educationLevelType2)) {
                    studentExportVo.setEducationLevelType2(educationMap.get(Long.valueOf(educationLevelType2)));
                }
                //处理所在区域
                StringBuilder inArea = new StringBuilder();
                if (GeneralTool.isNotEmpty(studentVo.getCountryName())) {
                    inArea.append(studentVo.getCountryName());
                }
                if (GeneralTool.isNotEmpty(studentVo.getStateName())) {
                    inArea.append("/");
                    inArea.append(studentVo.getStateName());
                }
                if (GeneralTool.isNotEmpty(studentVo.getCityName())) {
                    inArea.append("/");
                    inArea.append(studentVo.getCityName());
                }
                studentExportVo.setInArea(inArea.toString());

                //处理手填问题
                if (GeneralTool.isEmpty(studentExportVo.getEducationInstitutionName())) {
                    studentExportVo.setEducationInstitutionName(studentVo.getEducationInstitutionName2());
                }
                if (GeneralTool.isEmpty(studentExportVo.getCountryNameEducation())) {
                    studentExportVo.setCountryNameEducation(studentVo.getFkAreaCountryNameEducation());
                }
                if (GeneralTool.isEmpty(studentExportVo.getStateNameEducation())) {
                    studentExportVo.setStateNameEducation(studentVo.getFkAreaStateNameEducation());
                }
                if (GeneralTool.isEmpty(studentExportVo.getCityNameEducation())) {
                    studentExportVo.setCityNameEducation(studentVo.getFkAreaCityName());
                }

                if (GeneralTool.isEmpty(studentExportVo.getEducationInstitutionName2())) {
                    studentExportVo.setEducationInstitutionName2(studentVo.getEducationInstitutionName2());
                }
                if (GeneralTool.isEmpty(studentExportVo.getCountryNameEducation2())) {
                    studentExportVo.setCountryNameEducation2(studentVo.getFkAreaCountryNameEducation2());
                }
                if (GeneralTool.isEmpty(studentExportVo.getStateNameEducation2())) {
                    studentExportVo.setStateNameEducation2(studentVo.getFkAreaStateNameEducation2());
                }
                if (GeneralTool.isEmpty(studentExportVo.getCityNameEducation2())) {
                    studentExportVo.setCityNameEducation2(studentVo.getFkAreaCityNameEducation2());
                }


                //处理绑定代理
                StringJoiner staffNameJoiner = new StringJoiner("\n");
                //绑定代理标签
                StringJoiner staffLabelNameJoiner = new StringJoiner("\n");
                if(GeneralTool.isNotEmpty(studentVo.getCurrentStaffNameAndAgentLabelList())){
                    studentVo.getCurrentStaffNameAndAgentLabelList().forEach((item)->{
                        staffNameJoiner.add(item.getAgentName());
                        if (GeneralTool.isNotEmpty(item.getAgentLabelVos())){
                            staffLabelNameJoiner.add(
                                    item.getAgentLabelVos().stream()
                                            // 给每个标签名称包裹【】符号
                                            .map(vo -> "【" + vo.getLabelName() + "】")
                                            // 用逗号拼接所有带符号的标签
                                            .collect(Collectors.joining(" "))
                            );
                        }
                    });
                }

//                if (GeneralTool.isNotEmpty(studentVo.getCurrentStaffNameList())) {
//                    for (String staffName : studentVo.getCurrentStaffNameList()) {
//                        staffNameJoiner.add(staffName);
//                    }
//                }
                StringJoiner currentAgentNumJoiner = new StringJoiner("\n");
                if (GeneralTool.isNotEmpty(studentVo.getCurrentAgentNumList())) {
                    for (String agentNum : studentVo.getCurrentAgentNumList()) {
                        currentAgentNumJoiner.add(agentNum);
                    }
                }
                studentExportVo.setStaffNameList(staffNameJoiner.toString());
                studentExportVo.setStaffLabelNames(staffLabelNameJoiner.toString());
                studentExportVo.setCurrentAgentNumList(currentAgentNumJoiner.toString());

                //处理绑定BD
                StringJoiner bdNameJoiner = new StringJoiner("\n");
                if (GeneralTool.isNotEmpty(studentVo.getCurrentBdNameList())) {
                    for (String bdName : studentVo.getCurrentBdNameList()) {
                        bdNameJoiner.add(bdName);
                    }
                }
                studentExportVo.setBdNameList(bdNameJoiner.toString());


                //处理学生申请方案绑定代理
                StringJoiner studentOfferStaffJoiner = new StringJoiner("\n");
                StringJoiner studentOfferStaffLabelNameJoiner = new StringJoiner("\n");
                StringJoiner studentOfferAgentJoiner = new StringJoiner("\n");
//                if (GeneralTool.isNotEmpty(studentVo.getStudentOfferStaffNameList())) {
//                    for (String staffName : studentVo.getStudentOfferStaffNameList()) {
//                        studentOfferStaffJoiner.add(staffName);
//                    }
//                }
                if (GeneralTool.isNotEmpty(studentVo.getStudentOfferStaffNameAndAgentLabelList())){
                    studentVo.getStudentOfferStaffNameAndAgentLabelList().forEach((item)->{
                        studentOfferStaffJoiner.add(item.getAgentName());
                        if (GeneralTool.isNotEmpty(item.getAgentLabelVos())){
                            studentOfferStaffLabelNameJoiner.add(
                                    item.getAgentLabelVos().stream()
                                            // 给每个标签名称包裹【】符号
                                            .map(vo -> "【" + vo.getLabelName() + "】")
                                            // 用逗号拼接所有带符号的标签
                                            .collect(Collectors.joining(" "))
                            );
                        }
                    });
                }
                if (GeneralTool.isNotEmpty(studentVo.getStudentOfferAgentNumList())) {
                    for (String agentNum : studentVo.getStudentOfferAgentNumList()) {
                        studentOfferAgentJoiner.add(agentNum);
                    }
                }
                studentExportVo.setStudentOfferStaffNameList(studentOfferStaffJoiner.toString());
                studentExportVo.setStudentOfferStaffLabelNames(studentOfferStaffLabelNameJoiner.toString());
                studentExportVo.setStudentOfferAgentNumList(studentOfferAgentJoiner.toString());

                //处理学生申请方案绑定BD
                StringJoiner studentOfferBdJoiner = new StringJoiner("\n");
                if (GeneralTool.isNotEmpty(studentVo.getStudentOfferBdNamList())) {
                    for (String bdName : studentVo.getStudentOfferBdNamList()) {
                        studentOfferBdJoiner.add(bdName);
                    }
                }
                studentExportVo.setStudentOfferBdNamList(studentOfferBdJoiner.toString());

                //处理项目角色 / 员工
                if (GeneralTool.isNotEmpty(studentVo.getProjectRoleStaffList())) {
                    studentExportVo.setProjectRoleStaffList(studentVo.getProjectRoleStaffList());
                }


                String buffer = studentVo.getLastName() +" "+
                        studentVo.getFirstName();
                studentExportVo.setNameEng(buffer);
                studentExportVoList.add(studentExportVo);
            }
//            FileUtils.exportExcelNotWrapText(response, studentExportVoList, "StudentInfo", StudentExportVo.class);
            //生成excel，并上传到腾讯云
            BigExcelWriter writer;
            if (focExportVos.isEmpty()) {
                writer = FileUtils.getExcelWhithHeaderStyle(studentExportVoList, "StudentInfo", StudentExportVo.class, locale);
            } else {
                List<String> collect = focExportVos.stream().filter(FocExportVo::getSelected).map(FocExportVo::getKey).collect(Collectors.toList());
                writer = FileUtils.getExcelWhithHeaderStyle(studentExportVoList, focExportVos, "StudentInfo", locale);
                permissionCenterClient.saveStaffConfigByType(TableEnum.SALE_STUDENT.key, collect);
            }
            MultipartFile studyInfo = FileUtils.getFile(writer, "StudentInfo.xlsx");
            Result<List<FileDto>> upload = fileCenterClient.uploadAppendix(new MultipartFile[]{studyInfo}, LoggerModulesConsts.EXPORT);
            long time1 = System.currentTimeMillis();
            System.out.println("============导出总共时间:======================"+(time1-time));
            boolean flag = false;
             if (upload.isSuccess()) {
                List<FileDto> data = upload.getData();
                if (data != null && !data.isEmpty()) {
                    FileDto fileDto = data.get(0);
                    if (fileDto.getFileGuid() != null) {
                        download.setStatus(2)
                                .setFkFileGuid(fileDto.getFileGuid());
                        flag = true;
                    }
                }
            }
            if (!flag) {
                download.setStatus(0);
            }
        } catch (Exception e) {
            e.printStackTrace();
            download.setStatus(0);
            download.setRemark(LocaleMessageUtils.getMessage("no_data_search"));
        } finally {
            download.setGmtModifiedUser(user.getLoginId());
            download.setGmtModified(new Date());
            permissionCenterClient.updateDownload(download);
        }
    }

    @Async
    @Override
    public void exportStudyInfoList(StudentOfferItemCollectDto studentOfferItemVo, List<FocExportVo> focExportVos,
                                    Map<String, String> headerMap, UserInfo user, String locale, List<Long> secureCountryIds,
                                    List<Long> secureInstitutionIds, List<Long> securePermissionGroupInstitutionIds, List<Long> secureStaffBoundBdIds) {
        RequestHeaderHandler.setHeaderMap(headerMap);
        log.info("ExportStudentOfferItemExcel userInfo{}", user);
        Long staffId = user.getStaffId();

        StaffDownload download = new StaffDownload();
        try {
            Map<Long, String> cNameMap = permissionCenterClient.getCompanyNamesByIds(new HashSet<>(studentOfferItemVo.getFkCompanyIds())).getData();
            Collection<String> values = cNameMap.values();
            CommonUtil<StudentOfferItemCollectDto> commonUtil = new CommonUtil<>();
            String description = commonUtil.getFiledValue(studentOfferItemVo, StudentOfferItemCollectDto.class);
            download.setFkStaffId(staffId)
                    .setOptKey(FileTypeEnum.D_LIST_EXPORT.key)
                    .setOptDescription(String.format("【学生管理】《申请计划列表》 公司=%s%s", values, description))
                    .setStatus(1)
                    .setGmtCreate(new Date());
            download.setGmtCreateUser(user.getLoginId());
            download.setId(permissionCenterClient.addDownloadRecord(download));
            Integer MAX_COUNT = 50000;
            //员工id + 业务下属员工ids
            List<Long> staffFollowerIds = permissionCenterClient.getStaffFollowerIds(staffId).getData().stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
            staffFollowerIds.add(staffId);

            //获取员工和下属对应业务国家(KPI统计跳转)
      /*      if(GeneralTool.isNotEmpty(studentOfferItemVo.getCountRole()) && studentOfferItemVo.getCountRole().equals(ProjectExtraEnum.CPM.key)){
                List<StaffAreaCountry> staffAreaCountrys = permissionCenterClient.getStaffAreaCountryByStaffIds(staffFollowerIds);
                if(GeneralTool.isNotEmpty(staffAreaCountrys)){
                    Set<String> fkAreaCountryKeys = staffAreaCountrys.stream().map(StaffAreaCountry::getFkAreaCountryKey).collect(Collectors.toSet());
                    List<AreaCountryVo> areaCountryList = institutionCenterClient.getCountryByKey(new ArrayList<>(fkAreaCountryKeys)).getData();
                    if(GeneralTool.isNotEmpty(areaCountryList)){
                        Set<Long> countryIds = areaCountryList.stream().map(AreaCountryVo::getId).collect(Collectors.toSet());
                        if(GeneralTool.isNotEmpty(countryIds)){
                            studentOfferItemVo.setFkAreaCountryIdList(new ArrayList<>(countryIds));
                        }
                    }

                }

            }*/

            Map<Long, String> allInstitutionTypeName = institutionCenterClient.getAllInstitutionTypeName();
            Set<Long> allCountryId = institutionCenterClient.getAllCountryId().getData();
            Map<Long, String> countryChnNameByIds = institutionCenterClient.getCountryChnNameByIds(allCountryId).getData();
            Set<Long> staffIds = permissionCenterClient.getAllStaffIds();
            Map<Long, String> staffNamesByIds = permissionCenterClient.getStaffNameMap(staffIds).getData();
//            Map<String, List<Long>> positionNumAndStaffIdMap = permissionCenterClient.getPositionNumAndStaffIdMap(staffIds);
            Map<Long, List<Long>> staffSuperiorMap = permissionCenterClient.getStaffSuperiorByIds(staffIds);
            //模糊搜索申请的课程名称
//            if (GeneralTool.isNotEmpty(studentOfferItemVo.getCourseName())) {
//                //如有，则拼接搜索，没有则直接返回空
//                List<Long> courseIds = institutionCenterClient.getCourseIds(studentOfferItemVo.getCourseName()).getData();
//                if (!GeneralTool.isEmpty(courseIds)) {
//                    studentOfferItemVo.setCourseIds(courseIds);//写入集合，拼接到SQL进行in搜索
//                }
//            }
//            //模糊搜索申请的学校名称
//            if (GeneralTool.isNotEmpty(studentOfferItemVo.getSchoolName())) {
//                //如有，则拼接搜索，没有则直接返回空
//                List<Long> institutionIds = institutionCenterClient.getInstitutionIds(studentOfferItemVo.getSchoolName()).getData();
//                if (!GeneralTool.isEmpty(institutionIds)) {
//                    studentOfferItemVo.setInstitutionIds(institutionIds);//写入集合，拼接到SQL进行in搜索
//                }
//            }
            List<StudentOfferItemStep> studentOfferItemSteps = studentOfferItemStepMapper.selectList(null);
            Map<Long, Integer> orderMap = studentOfferItemSteps.stream().collect(Collectors.toMap(StudentOfferItemStep::getId, StudentOfferItemStep::getStepOrder));
            Map<Integer, String> educationProjectMap = new HashMap<>();
            for (ProjectExtraEnum projectExtraEnum : ProjectExtraEnum.EDUCATION_PROJECT) {
                educationProjectMap.put(projectExtraEnum.key, projectExtraEnum.value);
            }
            List<Future<List<StudentProjectRoleStaffVo>>> futureList = new ArrayList<>(4);
            List<EnrolFailureReason> enrolFailureReasons = enrolFailureReasonMapper.selectList(Wrappers.lambdaQuery());
            Map<Long, String> resonMap = enrolFailureReasons.stream().collect(Collectors.toMap(EnrolFailureReason::getId, EnrolFailureReason::getReasonName));

//            List<Long> prdIds = positionNumAndStaffIdMap.get("RD03");
//            if (GeneralTool.isEmpty(prdIds)) {
//                prdIds.add(0L);
//            }
//            List<Long> sddIds = positionNumAndStaffIdMap.get("RM04");
//            if (GeneralTool.isEmpty(sddIds)) {
//                sddIds.add(0L);
//            }
//            List<Long> sdmIds = positionNumAndStaffIdMap.get("RS05");
//            if (GeneralTool.isEmpty(sdmIds)) {
//                sdmIds.add(0L);
//            }

            LocalDateTime start = LocalDateTime.now();
            long startTime = start.toInstant(ZoneOffset.of("+8")).toEpochMilli();
            log.info("===>for开始");
            Map<Long, List<StudentProjectRoleStaffVo>> rcMap = new HashMap<>(1);
            Map<Long, List<StudentProjectRoleStaffVo>> arcMap = new HashMap<>(1);
            Map<Long, List<StudentProjectRoleStaffVo>> liMap = new HashMap<>(1);
            Map<Long, List<StudentProjectRoleStaffVo>> adMap = new HashMap<>(1);
            Map<Long, List<StudentProjectRoleStaffVo>> cmMap = new HashMap<>(1);
            Map<Long, List<StudentProjectRoleStaffVo>> iaeLiMap = new HashMap<>(1);
            Map<Long, List<StudentProjectRoleStaffVo>> iaeSupMap = new HashMap<>(1);
            Map<Long, List<StudentProjectRoleStaffVo>> iaeAdMap = new HashMap<>(1);
            Map<Long, List<StudentProjectRoleStaffVo>> indexCmMap = new HashMap<>(1);
            Map<Long, List<StudentProjectRoleStaffVo>> indexArcMap = new HashMap<>(1);
            Map<Long, List<StudentProjectRoleStaffVo>> seaArcMap = new HashMap<>(1);
            Map<Long, List<StudentProjectRoleStaffVo>> seaCmMap = new HashMap<>(1);
//        Integer count = studentOfferItemMapper.getCountStudentOfferItemDtoListNew(studentOfferItemVo,staffFollowerIds);
//        if (count.compareTo(MAX_COUNT) <= 0) {
            BigExcelWriter writer = null;

            String sort = "gmt_create";
            List<FocExportVo> sortStrs = focExportVos.stream().filter(FocExportVo::getSorted).filter(FocExportVo::getSelected).collect(Collectors.toList());
            if (GeneralTool.isNotEmpty(sortStrs) && sortStrs.size() == 1) {
                String filedName = sortStrs.get(0).getFiledName();
                //将从前端接收的驼峰字符串转化为下划线形式
                sort = UnderlineToCamelUtils.camelToUnderline(filedName);
            }
            List<StudentOfferItemListVo> studentOfferItemList = studentOfferItemService.AsyncGetStudentOfferItems(studentOfferItemVo,
                    0, MAX_COUNT, headerMap, staffFollowerIds, locale, staffId, sort, secureCountryIds, secureInstitutionIds, securePermissionGroupInstitutionIds, secureStaffBoundBdIds);
            Map<Long, List<StudentOfferItemListVo>> studentMap = studentOfferItemList.stream().filter(s -> GeneralTool.isNotEmpty(s.getFkStudentId())).collect(Collectors.groupingBy(StudentOfferItemListVo::getFkStudentId));

            Map<Integer, String> runMap = new HashMap<>();
            runMap.put(0, "RC");
            runMap.put(1, "ARC");
            runMap.put(2, "LI");
            runMap.put(3, "AD");
            runMap.put(4, "CM");
            runMap.put(5, "IAELI");
            runMap.put(6, "IAESUP");
            runMap.put(7, "IAEAD");
            runMap.put(8, "INDEXCM");
            runMap.put(9, "INDEXARC");
            runMap.put(10, "SEAARC");
            runMap.put(11, "SEACM");

            Set<Long> collect = studentOfferItemList.stream().map(StudentOfferItemListVo::getFkStudentOfferId).collect(Collectors.toSet());
            collect.removeIf(Objects::isNull);
            if (GeneralTool.isEmpty(collect)) {
                collect.add(0L);
            }
            List<StudentOfferItemSummaryExportVo> studentOfferItemSummaryExportVos = new ArrayList<>();
            for (Integer i : runMap.keySet()) {
                ServletRequestAttributes sra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
                RequestContextHolder.setRequestAttributes(sra, true);
                Future<List<StudentProjectRoleStaffVo>> future = taskExecutor.submit(() -> studentProjectRoleStaffMapper.getStaffIdByRoleKey(runMap.get(i), collect));
                futureList.add(i, future);
            }

            try {
                for (int i = 0; i < 12; i++) {
                    List<StudentProjectRoleStaffVo> studentProjectRoleStaffVos;
                    switch (i) {
                        case 0:
                            studentProjectRoleStaffVos = futureList.get(i).get();
                            rcMap = studentProjectRoleStaffVos.stream().collect(Collectors.groupingBy(StudentProjectRoleStaffVo::getFkTableId));
                            break;
                        case 1:
                            studentProjectRoleStaffVos = futureList.get(i).get();
                            arcMap = studentProjectRoleStaffVos.stream().collect(Collectors.groupingBy(StudentProjectRoleStaffVo::getFkTableId));
                            break;
                        case 2:
                            studentProjectRoleStaffVos = futureList.get(i).get();
                            liMap = studentProjectRoleStaffVos.stream().collect(Collectors.groupingBy(StudentProjectRoleStaffVo::getFkTableId));
                            break;
                        case 3:
                            studentProjectRoleStaffVos = futureList.get(i).get();
                            adMap = studentProjectRoleStaffVos.stream().collect(Collectors.groupingBy(StudentProjectRoleStaffVo::getFkTableId));
                            break;
                        case 4:
                            studentProjectRoleStaffVos = futureList.get(i).get();
                            cmMap = studentProjectRoleStaffVos.stream().collect(Collectors.groupingBy(StudentProjectRoleStaffVo::getFkTableId));
                            break;
                        case 5:
                            studentProjectRoleStaffVos = futureList.get(i).get();
                            iaeLiMap = studentProjectRoleStaffVos.stream().collect(Collectors.groupingBy(StudentProjectRoleStaffVo::getFkTableId));
                            break;
                        case 6:
                            studentProjectRoleStaffVos = futureList.get(i).get();
                            iaeSupMap = studentProjectRoleStaffVos.stream().collect(Collectors.groupingBy(StudentProjectRoleStaffVo::getFkTableId));
                            break;
                        case 7:
                            studentProjectRoleStaffVos = futureList.get(i).get();
                            iaeAdMap = studentProjectRoleStaffVos.stream().collect(Collectors.groupingBy(StudentProjectRoleStaffVo::getFkTableId));
                            break;
                        case 8:
                            studentProjectRoleStaffVos = futureList.get(i).get();
                            indexCmMap = studentProjectRoleStaffVos.stream().collect(Collectors.groupingBy(StudentProjectRoleStaffVo::getFkTableId));
                            break;
                        case 9:
                            studentProjectRoleStaffVos = futureList.get(i).get();
                            indexArcMap = studentProjectRoleStaffVos.stream().collect(Collectors.groupingBy(StudentProjectRoleStaffVo::getFkTableId));
                            break;
                        case 10:
                            studentProjectRoleStaffVos = futureList.get(i).get();
                            seaArcMap = studentProjectRoleStaffVos.stream().collect(Collectors.groupingBy(StudentProjectRoleStaffVo::getFkTableId));
                            break;
                        case 11:
                            studentProjectRoleStaffVos = futureList.get(i).get();
                            seaCmMap = studentProjectRoleStaffVos.stream().collect(Collectors.groupingBy(StudentProjectRoleStaffVo::getFkTableId));
                            break;
                        default:
                            break;
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            Set<Long> agentIds = studentOfferItemList.stream().map(StudentOfferItemListVo::getAgentId).collect(Collectors.toSet());
            Map<Long, Agent> agentsByIds = agentService.getAgentsByIds(agentIds);
            //获取学生集合的所有州省ids
            Set<Long> stateIds = studentOfferItemList.stream().map(StudentOfferItemListVo::getFkAreaStateIdEducation).collect(Collectors.toSet());
            stateIds.addAll(studentOfferItemList.stream().map(StudentOfferItemListVo::getFkAreaStateIdEducation2).collect(Collectors.toSet()));
            //获取学生集合的所有城市ids
            Set<Long> cityIds = studentOfferItemList.stream().map(StudentOfferItemListVo::getFkAreaCityIdEducation).collect(Collectors.toSet());
            cityIds.addAll(studentOfferItemList.stream().map(StudentOfferItemListVo::getFkAreaCityIdEducation2).collect(Collectors.toSet()));
            //获取学生集合的所有毕业院校ids
            Set<Long> institutionIds = studentOfferItemList.stream().map(StudentOfferItemListVo::getFkInstitutionIdEducation).collect(Collectors.toSet());
            institutionIds.addAll(studentOfferItemList.stream().map(StudentOfferItemListVo::getFkInstitutionIdEducation2).collect(Collectors.toSet()));
            Set<Long> offerItemIds = studentOfferItemList.stream().map(StudentOfferItemListVo::getId).collect(Collectors.toSet());

            //后续课程
            Map<Long, String> followItemCourseNameMap = new HashMap<>();
            List<StudentOfferItemVo> followItem = studentOfferItemMapper.getFollowCourseName(offerItemIds);
            if (GeneralTool.isNotEmpty(followItem)) {
                for (StudentOfferItemVo studentOfferItem : followItem) {
                    String followName = followItemCourseNameMap.get(studentOfferItem.getFkParentStudentOfferItemId());
                    if (GeneralTool.isEmpty(followName)) {
                        followItemCourseNameMap.put(studentOfferItem.getFkParentStudentOfferItemId(), studentOfferItem.getCourseFullName());
                    } else {
                        followItemCourseNameMap.put(studentOfferItem.getFkParentStudentOfferItemId(), followName + " + " + studentOfferItem.getCourseFullName());
                    }
                }
            }

            //根据州省ids获取州省名称
            Map<Long, String> stateNamesByIds = new HashMap<>();
            if (GeneralTool.isNotEmpty(stateIds)) {
                stateNamesByIds = institutionCenterClient.getStateFullNamesByIds(stateIds).getData();
            }
            //根据城市ids获取城市名称
            Map<Long, String> cityNamesByIds = new HashMap<>();
            if (GeneralTool.isNotEmpty(cityIds)) {
                cityNamesByIds = institutionCenterClient.getCityFullNamesByIds(cityIds).getData();
            }
            //根据学校ids获取学校名称
            Map<Long, String> institutionNamesByIds = new HashMap<>();
            if (GeneralTool.isNotEmpty(institutionIds)) {
                institutionNamesByIds = institutionCenterClient.getInstitutionNamesByIds(institutionIds).getData();
            }
            // 根据登录ids获取姓名
            Map<String, StaffVo> staffDtoMapByLoginIds = new HashMap<>();
            Set<String> loginIdSet = studentOfferItemList.stream().map(StudentOfferItemListVo::getFinalCreateUser).collect(Collectors.toSet());
            Result<Map<String, StaffVo>> result = permissionCenterClient.getStaffDtoMapByLoginIds(loginIdSet);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                staffDtoMapByLoginIds = result.getData();
            }


            studentOfferItemService.setAttribute(countryChnNameByIds, educationProjectMap, studentOfferItemSummaryExportVos, studentOfferItemList,
                    staffNamesByIds, staffSuperiorMap, rcMap, arcMap, liMap, adMap, cmMap, iaeLiMap, iaeSupMap, iaeAdMap, indexCmMap, indexArcMap, seaArcMap, seaCmMap,
                    resonMap, allInstitutionTypeName, studentMap, orderMap, agentsByIds, locale, stateNamesByIds, cityNamesByIds, institutionNamesByIds,
                    staffDtoMapByLoginIds, followItemCourseNameMap);

            if (focExportVos.isEmpty()) {
                writer = FileUtils.getExcelWhithHeaderStyle(studentOfferItemSummaryExportVos, "studyInfo", StudentOfferItemSummaryExportVo.class, locale);
            } else {
                List<String> keys = focExportVos.stream().filter(FocExportVo::getSelected).map(FocExportVo::getKey).collect(Collectors.toList());
                writer = FileUtils.getExcelWhithHeaderStyle(studentOfferItemSummaryExportVos, focExportVos, "studyInfo", locale);
                permissionCenterClient.saveStaffConfigByType(TableEnum.SALE_STUDENT_OFFER_ITEM.key, keys);
            }
//            FileUtils.doExportExcel(response, writer, "studyInfo");
            MultipartFile studyInfo = FileUtils.getFile(writer, "studyInfo.xlsx");
            Result<List<FileDto>> upload = fileCenterClient.uploadAppendix(new MultipartFile[]{studyInfo}, LoggerModulesConsts.EXPORT);
            boolean flag = false;
            if (upload.isSuccess()) {
                List<FileDto> data = upload.getData();
                if (data != null && !data.isEmpty()) {
                    FileDto fileDto = data.get(0);
                    if (fileDto.getFileGuid() != null) {
                        download.setStatus(2)
                                .setFkFileGuid(fileDto.getFileGuid());
                        flag = true;
                    }
                }
            }
            if (!flag) {
                download.setStatus(0);
            }
            log.info("申请计划导出数据大小size{}", studentOfferItemSummaryExportVos.size());
            LocalDateTime end1 = LocalDateTime.now();
            long endTime1 = end1.toInstant(ZoneOffset.of("+8")).toEpochMilli();
            log.info("===>for结束,用时：" + (endTime1 - startTime));
        } catch (Exception e) {
            e.printStackTrace();
            download.setStatus(0);
            download.setRemark(LocaleMessageUtils.getMessage("no_data_search"));
        } finally {
            download.setGmtModifiedUser(user.getLoginId());
            download.setGmtModified(new Date());
            permissionCenterClient.updateDownload(download);
        }
    }

    @Async("saleTaskExecutor")
    @Override
    public void exportAllImageInfo(GoproNucleicAcidUpdateDto goproNucleicAcidUpdateDto, Map<String, String> headerMap, UserInfo user, String locale) {
        RequestHeaderHandler.setHeaderMap(headerMap);
        log.info("ExportStudentOfferItemExcel userInfo{}", user);
        Long staffId = user.getStaffId();
        StaffDownload download = new StaffDownload();
        try {
            String cName = permissionCenterClient.getCompanyNameById(2L).getData();
            CommonUtil<GoproNucleicAcidUpdateDto> commonUtil = new CommonUtil<>();
            String description = commonUtil.getFiledValue(goproNucleicAcidUpdateDto, GoproNucleicAcidUpdateDto.class);
            download.setFkStaffId(staffId)
                    .setOptKey(FileTypeEnum.D_LIST_EXPORT.key)
                    .setOptDescription(String.format("【活动管理】《gopro核酸图片信息》 公司=%s" + description, cName))
                    .setStatus(1)
                    .setGmtCreate(new Date());
            download.setGmtCreateUser(user.getLoginId());
            download.setId(permissionCenterClient.addDownloadRecord(download));


            List<GoproNucleicAcid> goproNucleicAcids = goproNucleicAcidMapper.selectList(Wrappers.<GoproNucleicAcid>lambdaQuery()
                    .eq(GoproNucleicAcid::getRetreatTypeName, goproNucleicAcidUpdateDto.getRetreatTypeName())
                    .isNotNull(GoproNucleicAcid::getOrderId));

            if (GeneralTool.isEmpty(goproNucleicAcids)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_search"));
            }

            List<SaleMediaAndAttached> saleMediaAndAttacheds = mediaAndAttachedMapper.selectList(Wrappers.<SaleMediaAndAttached>lambdaQuery()
                    .eq(SaleMediaAndAttached::getFkTableName, TableEnum.CONVENTION_GOPRO_NUCLEIC_ACID.key)
                    .eq(SaleMediaAndAttached::getTypeKey, FileTypeEnum.CONVENTION_GOPRO_NUCLEIC_ACID_FILE.key)
            );
            List<String> guids = saleMediaAndAttacheds.stream().map(SaleMediaAndAttached::getFkFileGuid).collect(Collectors.toList());
            Map<String, String> fileKey = fileCenterClient.getSaleFileKeyByGuids(guids).getData();
            Map<String, String> fileNameOrc = fileCenterClient.getSaleFileNameOrcByGuids(guids).getData();

            Map<Long, List<SaleMediaAndAttached>> listMap = saleMediaAndAttacheds.stream().collect(Collectors.groupingBy(SaleMediaAndAttached::getFkTableId));
            if (GeneralTool.isEmpty(listMap)) {
                download.setStatus(0);
                download.setRemark("文件结果为空");
            }

            if (GeneralTool.isNotEmpty(goproNucleicAcids)) {
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                ZipOutputStream zipOutputStream = new ZipOutputStream(bos);

                //创建一个文件夹
                zipOutputStream.putNextEntry(new ZipEntry("活动资料" + " /"));
                zipOutputStream.closeEntry();
                String path = null;

                for (GoproNucleicAcid goproNucleicAcid : goproNucleicAcids) {

                    path = "活动资料";
                    List<SaleFileDto> fileList = new ArrayList<>();
                    List<SaleMediaAndAttached> mediaAndAttacheds = listMap.get(goproNucleicAcid.getId());

                    List<CompletableFuture<SaleFileDto>> futures = new ArrayList<>();
                    if (GeneralTool.isNotEmpty(mediaAndAttacheds)) {
                        for (SaleMediaAndAttached itemMedia : mediaAndAttacheds) {
                            if (GeneralTool.isNotEmpty(fileKey.get(itemMedia.getFkFileGuid()))) {
                                FileVo fileVo = new FileVo();
                                fileVo.setFileNameOrc(fileNameOrc.get(itemMedia.getFkFileGuid()));
                                fileVo.setFileKey(fileKey.get(itemMedia.getFkFileGuid()));

                                CompletableFuture<SaleFileDto> saleFileDtoCompletableFuture = fileDownloadHelper.doDownloadSaleFile(fileVo);
                                futures.add(saleFileDtoCompletableFuture);
//                                Result<SaleFileDto> result = fileCenterClient.getDownloadFile(fileVo);
//                                SaleFileDto data = result.getData();

//                                if (GeneralTool.isNotEmpty(data)){
//                                    fileList.add(data);
//                                }

                            }

                        }

                        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
                        for (CompletableFuture<SaleFileDto> future : futures) {
                            SaleFileDto saleFileDto = future.get();
                            if (GeneralTool.isNotEmpty(saleFileDto)) {
                                fileList.add(saleFileDto);
                            }
                        }

                        // 创建 ZipEntry 对象
                        if (GeneralTool.isNotEmpty(fileList)) {
                            int i = 0;
                            for (SaleFileDto saleFileDto : fileList) {
                                String fileName = saleFileDto.getFileName();
                                String[] split = fileName.split("\\.");
                                if (!"png".equals(split[1]) && !"jpg".equals(split[1]) && !"pdf".equals(split[1]) && !"PDF".equals(split[1])) {
                                    fileName = split[0] + "_" + goproNucleicAcid.getId() + "_" + i + "." + "png";
                                } else {
                                    fileName = split[0] + "_" + goproNucleicAcid.getId() + "_" + i + "." + split[1];
                                }

                                if (i == 0) {
                                    zipOutputStream.putNextEntry(new ZipEntry(path + " /" + goproNucleicAcid.getOrderId() + " " + goproNucleicAcid.getName() + " /"));
                                    zipOutputStream.closeEntry();
                                }
                                ZipEntry zipEntry = new ZipEntry(path + " /" + goproNucleicAcid.getOrderId() + " " + goproNucleicAcid.getName() + " /" + fileName);
                                zipOutputStream.putNextEntry(zipEntry);
                                byte[] outBytes = saleFileDto.getBytes();
                                zipOutputStream.write(outBytes);
                                saleFileDto = null;
                                i++;
                            }

                        }

                    }

                }
                if (zipOutputStream != null) {
                    zipOutputStream.flush();
                    zipOutputStream.closeEntry();
                    zipOutputStream.close();
                }


                IoUtil.close(bos);

                MultipartFile studyInfo = FileUtils.getFile(bos, "goproNucleicAcidImages.zip", "zip");

                Result<List<FileDto>> upload = fileCenterClient.uploadAppendix(new MultipartFile[]{studyInfo}, LoggerModulesConsts.EXPORT);
                boolean flag = false;
                if (upload.isSuccess()) {
                    List<FileDto> data = upload.getData();
                    if (data != null && !data.isEmpty()) {
                        FileDto fileDto = data.get(0);
                        if (fileDto.getFileGuid() != null) {
                            download.setStatus(2)
                                    .setFkFileGuid(fileDto.getFileGuid());
                            flag = true;
                        }
                    }
                }
                if (!flag) {
                    download.setStatus(0);
                }

            }
        } catch (Exception e) {
            e.printStackTrace();
            download.setStatus(0);
            download.setRemark("文件结果为空");
            log.error("查询结果为空", e);
        } finally {
            download.setGmtModifiedUser(user.getLoginId());
            download.setGmtModified(new Date());
            permissionCenterClient.updateDownload(download);
        }
    }

    /**
     * 导出售前资源列表
     *
     * @param studentOfferItemListVo
     * @param headerMap
     * @param staffInfo
     * @param locale
     * @param countryIds
     */
    @Async("saleTaskExecutor")
    @Override
    public void doExportedCustomerListForMarket(StudentOfferItemListQueryDto studentOfferItemListVo, Map<String, String> headerMap, StaffInfo staffInfo, String locale, List<Long> countryIds) {
        RequestHeaderHandler.setHeaderMap(headerMap);
        Long staffId = staffInfo.getStaffId();

        StaffDownload download = new StaffDownload();
        Set<Long> companyIds = new HashSet<>(1);
        companyIds.add(studentOfferItemListVo.getFkCompanyId());
        Map<Long, String> cName = permissionCenterClient.getCompanyNamesByIds(companyIds).getData();
        Collection<String> values = cName.values();
        CommonUtil<StudentOfferItemListDto> commonUtil = new CommonUtil<>();
        String description = commonUtil.getFiledValue(studentOfferItemListVo, StudentOfferItemListDto.class);

        download.setFkStaffId(staffId)
                .setOptKey(FileTypeEnum.D_LIST_EXPORT.key)
                .setOptDescription(String.format("【学生管理】《申请结算汇总》 公司=%s%s", values, description))
                .setStatus(1)
                .setGmtCreate(new Date());
        download.setGmtCreateUser(staffInfo.getLoginId());
        download.setId(permissionCenterClient.addDownloadRecord(download));

        try {

            DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            DateFormat dateFormat1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");


            String[] times = {"0", "0"};//[0]-o-主SQL执行时间,[1]-f-远程调用时间
            studentOfferItemListVo.setOffset(0);
            studentOfferItemListVo.setPageNumber(0);
            studentOfferItemListVo.setPageSize(null);
            studentOfferItemListVo.setExportStaffId(staffId);
            studentOfferItemListVo.setExportCountryIds(countryIds);
            studentOfferItemListVo.setExportLocale(locale);
            List<StudentOfferItemListVo> studentOfferItemListVos = studentOfferItemService.getSuccessfulCustomersListForMarket(studentOfferItemListVo, times, staffInfo);

            List<CustomerListForMarketExportVo> customerListForMarketExportVos = studentOfferItemListVos.stream().map(s -> {
                CustomerListForMarketExportVo c = new CustomerListForMarketExportVo();
                BeanCopyUtils.copyProperties(s, c);
                if (GeneralTool.isNotEmpty(s.getBirthday())) {
                    c.setBirthday(dateFormat.format(s.getBirthday()));
                }
                if (GeneralTool.isNotEmpty(s.getDeferOpeningTime())) {
                    c.setOpeningTime(dateFormat.format(s.getDeferOpeningTime()));
                }
                if (GeneralTool.isNotEmpty(s.getGmtCreate())) {
                    c.setGmtCreate(dateFormat1.format(s.getGmtCreate()));
                }
                if (GeneralTool.isNotEmpty(s.getStatus())) {
                    if (s.getStatus().equals(1)) {
                        c.setStatusName("有效");
                    } else {
                        c.setStatusName("作废");
                    }
                }
                if (GeneralTool.isNotEmpty(s.getFkInstitutionCourseId())) {
                    if (s.getFkInstitutionCourseId().equals(-1L)) {
                        c.setCourseFullName(s.getOldCourseCustomName());
                    }
                }
                if (GeneralTool.isNotEmpty(s.getIsPayInAdvance())) {
                    c.setPayInAdvanceStatusName(s.getIsPayInAdvance() ? "是" : "否");
                }
                if (GeneralTool.isNotEmpty(s.getAgentLabelVos())){
                    c.setAgentLabelNames(
                            s.getAgentLabelVos().stream()
                                    // 给每个标签名称包裹【】符号
                                    .map(vo -> "【" + vo.getLabelName() + "】")
                                    // 用逗号拼接所有带符号的标签
                                    .collect(Collectors.joining(","))
                    );
                }
                return c;
            }).collect(Collectors.toList());
            //fkInstitutionCourseId 判断是否是-1

            BigExcelWriter writer = FileUtils.getExcelWhithHeaderStyle(customerListForMarketExportVos, "customerListForMarket", CustomerListForMarketExportVo.class, locale);
            MultipartFile studyInfo = FileUtils.getFile(writer, "customerListForMarket.xlsx");
            Result<List<FileDto>> upload = fileCenterClient.uploadAppendix(new MultipartFile[]{studyInfo}, LoggerModulesConsts.EXPORT);
            boolean flag = false;
            if (upload.isSuccess()) {
                List<FileDto> data = upload.getData();
                if (data != null && !data.isEmpty()) {
                    FileDto fileDto = data.get(0);
                    if (fileDto.getFileGuid() != null) {
                        download.setStatus(2)
                                .setFkFileGuid(fileDto.getFileGuid());
                        flag = true;
                    }
                }
            }
            if (!flag) {
                download.setStatus(0);
            }
            log.info("申请计划导出数据大小size{}", customerListForMarketExportVos.size());


        } catch (Exception e) {
            e.printStackTrace();
            download.setStatus(0);
            download.setRemark(LocaleMessageUtils.getMessage("no_data_search"));
            log.error("查询结果为空", e);

        }

        download.setGmtModifiedUser(staffInfo.getLoginId());
        download.setGmtModified(new Date());
        permissionCenterClient.updateDownload(download);

        log.info("----------导出结束-------------");
    }


    @Async("saleTaskExecutor")
    @Override
    public void exportClientResources(ClientDto clientDto, List<FocExportVo> focExportVos, Map<String, String> headerMap, StaffInfo staffInfo, String locale) {
        RequestHeaderHandler.setHeaderMap(headerMap);
        Long staffId = staffInfo.getStaffId();
        Set<Long> companyIds = new HashSet<>(1);
        companyIds.add(clientDto.getFkCompanyId());
        Map<Long, String> cName = permissionCenterClient.getCompanyNamesByIds(companyIds).getData();
        Collection<String> values = cName.values();

        StaffDownload download = new StaffDownload();
        CommonUtil<ClientDto> commonUtil = new CommonUtil<>();
        String description = commonUtil.getFiledValue(clientDto, ClientDto.class);

        download.setFkStaffId(staffId)
                .setOptKey(FileTypeEnum.D_LIST_EXPORT.key)
                .setOptDescription(String.format("【学生资源管理】《学生资源列表》 公司=%s" + description, values))
                .setStatus(1)
                .setGmtCreate(new Date());
        download.setGmtCreateUser(staffInfo.getLoginId());
        download.setId(permissionCenterClient.addDownloadRecord(download));

        try {

            if (null == clientDto) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null", locale));
            }

            Result<List<Long>> result = permissionCenterClient.getStaffFollowerIds(staffId);
            if (!result.isSuccess()) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("export_empty", locale));
            }
            List<Long> followerIds = result.getData().stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
            followerIds.add(staffId);
            if (StringUtils.isNotBlank(clientDto.getName())) {
                clientDto.setName(clientDto.getName().trim().toLowerCase());
            }
            List<String> resourceKeysByStaffId = SecureUtil.getApiKeysByStaffId(staffId);
            boolean hide = resourceKeysByStaffId.contains(ProjectKeyEnum.AGENT_HIDE_KEY.key);
            List<ClientListInfoVo> clientDtos = clientMapper.getResourceList(null, clientDto, followerIds, true, hide);
            if (clientDtos.isEmpty()) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("export_empty", locale));
            }
            List<ClientListInfoDto> list = new ArrayList<>(clientDtos.size());
            pack(clientDtos, clientDto, list);

            List<ClientListExportDto> listExportVos = Lists.newArrayList();


            for (ClientListInfoDto clientListInfoDto : list) {
                ClientListExportDto clientListExportDto = BeanCopyUtils.objClone(clientListInfoDto, ClientListExportDto::new);

                if (GeneralTool.isNotEmpty(clientListInfoDto.getInstitutionName())) {
                    clientListExportDto.setInstitutionName(clientListInfoDto.getInstitutionName().replace("<br>", "\n"));
                }

                if (GeneralTool.isNotEmpty(clientListInfoDto.getMajorName())) {
                    clientListExportDto.setMajorName(clientListInfoDto.getMajorName().replace("<br>", "\n"));
                }

                if (GeneralTool.isNotEmpty(clientListInfoDto.getStartTimeEducation())) {
                    clientListExportDto.setStartTimeEducation(clientListInfoDto.getStartTimeEducation().replace("<br>", "\n"));
                }

                if (GeneralTool.isNotEmpty(clientListInfoDto.getEndTimeEducation())) {
                    clientListExportDto.setEndTimeEducation(clientListInfoDto.getEndTimeEducation().replace("<br>", "\n"));
                }

                if (GeneralTool.isNotEmpty(clientListInfoDto.getProjectRoleName())) {
                    clientListExportDto.setProjectRoleName(clientListInfoDto.getProjectRoleName().replace("<br>", "\n"));
                }
                if (GeneralTool.isNotEmpty(clientListInfoDto.getBirthday())) {
                    clientListExportDto.setBirthday(new SimpleDateFormat("yyyy-MM-dd").format(clientListInfoDto.getBirthday()));
                }

                if (GeneralTool.isNotEmpty(clientListInfoDto.getIsBmsStudent()) && clientListInfoDto.getIsBmsStudent()) {
//                    StringJoiner stringJoiner = new StringJoiner("/");
                    if (GeneralTool.isNotEmpty(clientListInfoDto.getStudentSource())) {
                        clientListExportDto.setStudentSource(clientListInfoDto.getStudentSource());
//                        stringJoiner.add(clientListInfoDto.getStudentSource());
                    }
//                    if (GeneralTool.isNotEmpty(clientListInfoDto.getSourceAgentName())){
//                        stringJoiner.add(clientListInfoDto.getSourceAgentName());
//                    }
//                    if (GeneralTool.isNotEmpty(clientListInfoDto.getSourceBdName())){
//                        stringJoiner.add(clientListInfoDto.getSourceBdName());
//                    }
//                    clientListExportDto.setStudentSource(stringJoiner.toString());
                }
                if (GeneralTool.isNotEmpty(clientListInfoDto.getFkTableName())) {
                    clientListExportDto.setFkTableName(ProjectKeyEnum.getInitialValue(clientListInfoDto.getFkTableName()));
                }
                if (GeneralTool.isNotEmpty(clientListInfoDto.getFkStudentStaffNames())){
                    clientListExportDto.setFkStudentStaffNames(clientListInfoDto.getFkStudentStaffNames());
                }
                listExportVos.add(clientListExportDto);
            }

            BigExcelWriter writer;
            if (focExportVos.isEmpty()) {
                writer = FileUtils.getExcelWhithHeaderStyle(listExportVos, "clientExport", ClientListExportDto.class, SecureUtil.getLocale());
            } else {
                Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.STUDENT_TO_CLIENT_APPROVER_DEFAULT.key, 1).getData();
                String configValue1 = companyConfigMap.get(staffInfo.getFkCompanyId());
                Long staffIdApproval = Long.valueOf(configValue1);

//                ConfigVo configDto = permissionCenterClient.getConfigByKey(ProjectKeyEnum.STUDENT_TO_CLIENT_APPROVER_DEFAULT.key).getData();
//                Long staffIdApproval = 0L;
//                if (GeneralTool.isNotEmpty(configDto)){
//                    String configJson = configDto.getValue1();
//                    JSONObject configJsonObject = JSON.parseObject(configJson);
//                    if (staffInfo.getFkCompanyId().equals(3L)){
//                        String iae = configJsonObject.getString("IAE");
//                        if (GeneralTool.isNotEmpty(iae)){
//                            staffIdApproval = Long.valueOf(iae);
//                        }
//                    }else {
//                        String gea = configJsonObject.getString("OTHER");
//                        if (GeneralTool.isNotEmpty(gea)){
//                            staffIdApproval = Long.valueOf(gea);
//                        }
//                    }
//                }

                if (staffIdApproval <= 0) {
                    //不在配置的员工id不导出
                    focExportVos = focExportVos.stream().filter(f -> !"studentSource".equals(f.getFiledName())
                            && !"sourceAgentName".equals(f.getFiledName())
                            && !"sourceBdName".equals(f.getFiledName())
                            && !"isEnterCountryName".equals(f.getFiledName())).collect(Collectors.toList());
                }

                List<String> collect = focExportVos.stream().filter(FocExportVo::getSelected).map(FocExportVo::getKey).collect(Collectors.toList());
                writer = FileUtils.getExcelWhithHeaderStyle(listExportVos, focExportVos, "clientExport", locale);
                CellStyle headCellStyle = writer.getHeadCellStyle();
                headCellStyle.setFillForegroundColor(IndexedColors.LIGHT_ORANGE.getIndex());
                permissionCenterClient.saveStaffConfigByType(TableEnum.SALE_CLIENT.key, collect);
            }

//            BigExcelWriter writer = FileUtils.getExcelWhithHeaderStyle(listExportVos, "customerListForMarket", CustomerListForMarketExportVo.class,locale);
            MultipartFile studyInfo = FileUtils.getFile(writer, "clientExport.xlsx");
            Result<List<FileDto>> upload = fileCenterClient.uploadAppendix(new MultipartFile[]{studyInfo}, LoggerModulesConsts.EXPORT);
            boolean flag = false;
            if (upload.isSuccess()) {
                List<FileDto> data = upload.getData();
                if (data != null && !data.isEmpty()) {
                    FileDto fileDto = data.get(0);
                    if (fileDto.getFileGuid() != null) {
                        download.setStatus(2)
                                .setFkFileGuid(fileDto.getFileGuid());
                        flag = true;
                    }
                }
            }
            if (!flag) {
                download.setStatus(0);
            }
            log.info("学生资源列表导出数据大小size{}", focExportVos.size());


        } catch (Exception e) {
            e.printStackTrace();
            download.setStatus(0);
            download.setRemark(LocaleMessageUtils.getMessage("no_data_search"));
            log.error("查询结果为空", e);

        }

        download.setGmtModifiedUser(staffInfo.getLoginId());
        download.setGmtModified(new Date());
        permissionCenterClient.updateDownload(download);

        log.info("----------导出结束-------------");
    }

    /**
     * 导出KPI代理排名
     *
     * @param headerMap
     * @param locale
     * @param user
     * @param kpiPlanStatisticsDto
     */
    @Override
    @Async
    public void asyncExportKpiAgentRankExcel(Map<String, String> headerMap, String locale, UserInfo user, KpiPlanStatisticsDto kpiPlanStatisticsDto) {
        RequestHeaderHandler.setHeaderMap(headerMap);
        log.info("asyncExportKpiAgentRankExcel");
        StaffDownload download = new StaffDownload();
        try {
            Long staffId = user.getStaffId();
            download.setFkStaffId(staffId)
                    .setOptKey(FileTypeEnum.D_LIST_EXPORT.key)
                    .setOptDescription("【KPI方案统计】《KPI代理排名》")
                    .setStatus(1)
                    .setGmtCreate(new Date());
            download.setGmtCreateUser(user.getLoginId());
            download.setId(permissionCenterClient.addDownloadRecord(download));

            KpiPlanStatisticsVo kpiPlanStatistics = kpiPlanService.getKpiPlanStatistics(kpiPlanStatisticsDto);
            // KPI代理排名
            List<KpiAgentRankVo> kpiAgentRankVos = kpiPlanStatistics.getKpiAgentRankDtos();
            // 导出列表
            List<KpiAgentRankExport> kpiAgentRankExportList = new ArrayList<>();
            for (int i = 0; i < kpiAgentRankVos.size(); i++) {
                KpiAgentRankVo kpiAgentRankVo = kpiAgentRankVos.get(i);
                KpiAgentRankExport kpiAgentRankExport = BeanCopyUtils.objClone(kpiAgentRankVo, KpiAgentRankExport::new);
                if (i == kpiAgentRankVos.size() - 1) {
                    // 小计在最后一条
                    kpiAgentRankExport.setRankNum("小计");
                } else {
                    kpiAgentRankExport.setRankNum(String.valueOf(i + 1));
                }
                // 大区
                List<AreaRegionVo> areaRegionVos = kpiAgentRankVo.getAreaRegionDtos();
                if (GeneralTool.isNotEmpty(areaRegionVos)) {
                    for (AreaRegionVo areaRegionVo : areaRegionVos) {
                        if (GeneralTool.isNotEmpty(areaRegionVo.getFkAreaCountryName()) && GeneralTool.isNotEmpty(areaRegionVo.getNameChn())) {
                            String areaRegion = areaRegionVo.getFkAreaCountryName() + " - " + areaRegionVo.getNameChn() + ";";
                            kpiAgentRankExport.setAreaRegion(areaRegion);
                        }
                    }
                }
                // 区域
                StringBuilder region = new StringBuilder();
                if (GeneralTool.isNotEmpty(kpiAgentRankVo.getCountryName())) {
                    region.append(kpiAgentRankVo.getCountryName()).append("/");
                }
                if (GeneralTool.isNotEmpty(kpiAgentRankVo.getStateName())) {
                    region.append(kpiAgentRankVo.getStateName()).append("/");
                }
                if (GeneralTool.isNotEmpty(kpiAgentRankVo.getCityName())) {
                    region.append(kpiAgentRankVo.getCityName()).append("/");
                }
                if (region.length() > 0) {
                    region.deleteCharAt(region.length() - 1);
                }
                kpiAgentRankExport.setRegion(region.toString());
                kpiAgentRankExportList.add(kpiAgentRankExport);
            }
            // 文件导出
            BigExcelWriter writer = FileUtils.getExcelWhithHeaderStyle(kpiAgentRankExportList, "kpiAgentRank", KpiAgentRankExport.class, locale);
            MultipartFile fileInfo = FileUtils.getFile(writer, "kpiAgentRank.xlsx");
            Result<List<FileDto>> upload = fileCenterClient.uploadAppendix(new MultipartFile[]{fileInfo}, LoggerModulesConsts.EXPORT);
            boolean flag = false;
            if (upload.isSuccess()) {
                List<FileDto> data = upload.getData();
                if (data != null && !data.isEmpty()) {
                    FileDto fileDto = data.get(0);
                    if (fileDto.getFileGuid() != null) {
                        download.setStatus(2)
                                .setFkFileGuid(fileDto.getFileGuid());
                        flag = true;
                    }
                }
            }
            if (!flag) {
                download.setStatus(0);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.info(e.getMessage());
            download.setStatus(0);
            download.setRemark(LocaleMessageUtils.getMessage("no_data_search"));
        } finally {
            download.setGmtModifiedUser(user.getLoginId());
            download.setGmtModified(new Date());
            permissionCenterClient.updateDownload(download);
        }
    }


    public void pack(List<ClientListInfoVo> clientDtos, ClientDto clientDto1, List<ClientListInfoDto> list) {
        Set<Long> companyIds = clientDtos.stream().map(ClientListInfoVo::getFkCompanyId).collect(Collectors.toSet());
        Result<Map<Long, String>> companyNamesByIds = permissionCenterClient.getCompanyNamesByIds(companyIds);
        Map<Long, String> cMap = new HashMap<>(companyIds.size());
        if (companyNamesByIds.isSuccess() && companyNamesByIds.getData() != null) {
            cMap = companyNamesByIds.getData();
        }
        Set<Long> ids = clientDtos.stream().map(ClientListInfoVo::getId).collect(Collectors.toSet());
        List<SelItem> visitTime = clientEventMapper.getLastVisitTime(ids);
        Map<Long, Object> convert = ConvertUtils.convert(visitTime);
        List<SelItem> time = clientEventMapper.getVisitTime(ids, clientDto1.getBeginFollowUpTime(), clientDto1.getEndFollowUpTime());
        Map<Long, Object> c2 = ConvertUtils.convert(time);
        Map<Long, Object> projectRole = projectRoleStaffService.getAFollowUpConsultant(TableEnum.CLIENT_OFFER.key, ids, "GEA_RESOURCE_ARC");


        Set<Integer> stepOrders = clientDtos.stream().map(ClientListInfoVo::getMaxStepOrder).collect(Collectors.toSet());
        Map<Integer, ClientOfferStep> clientOfferStepMap = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(stepOrders)) {
            List<ClientOfferStep> clientOfferSteps = clientOfferStepMapper.selectList(Wrappers.lambdaQuery(ClientOfferStep.class)
                    .in(ClientOfferStep::getStepOrder, stepOrders));
            clientOfferStepMap = clientOfferSteps.stream().collect(Collectors.toMap(ClientOfferStep::getStepOrder, Function.identity()));
        }

        //院校名称
        Set<Long> institutionIds = Sets.newHashSet();
        Set<Long> fkInstitutionIdEducations = clientDtos.stream().map(ClientListInfoVo::getFkInstitutionIdEducation).collect(Collectors.toSet());
        Set<Long> fkInstitutionIdEducation2s = clientDtos.stream().map(ClientListInfoVo::getFkInstitutionIdEducation2).collect(Collectors.toSet());
        institutionIds.addAll(fkInstitutionIdEducations);
        institutionIds.addAll(fkInstitutionIdEducation2s);
        Map<Long, String> institutionNameMap = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(institutionIds)) {
            institutionNameMap = institutionCenterClient.getInstitutionNamesByIds(institutionIds).getData();
        }


        Set<Long> countryIds = clientDtos.stream().map(ClientListInfoVo::getOfferCountryId).collect(Collectors.toSet());
        Map<Long, String> countryNameIds = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(countryIds)) {
            countryNameIds = institutionCenterClient.getCountryNamesByIds(countryIds).getData();
        }
        Set<Long> agentIds = Sets.newHashSet();
        Set<Long> clientAgentIds = clientDtos.stream().map(ClientListInfoVo::getFkAgentId).filter(Objects::nonNull).collect(Collectors.toSet());
        Set<Long> sourceAgentIds = clientDtos.stream().map(ClientListInfoVo::getSourceAgentId).filter(Objects::nonNull).collect(Collectors.toSet());
        agentIds.addAll(clientAgentIds);
        agentIds.addAll(sourceAgentIds);
        Map<Long, String> agentNamesMap = Maps.newHashMap();
        Map<Long, String> bdNameMap = Maps.newHashMap();
        Map<Long, String> staffNamesByIds = Maps.newHashMap();
        Set<Long> staffIds = Sets.newHashSet();
        Set<Long> sourceStaffIds = clientDtos.stream().map(ClientListInfoVo::getSourceStaffId).filter(Objects::nonNull).collect(Collectors.toSet());
        staffIds.addAll(sourceStaffIds);
        if (GeneralTool.isNotEmpty(clientAgentIds)) {
            List<AgentStaff> agentStaffs = agentStaffMapper.selectList(Wrappers.lambdaQuery(AgentStaff.class)
                    .in(AgentStaff::getFkAgentId, clientAgentIds)
                    .eq(AgentStaff::getIsActive, true));
            if (GeneralTool.isNotEmpty(agentStaffs)) {
                Set<Long> clientStaffIds = agentStaffs.stream().map(AgentStaff::getFkStaffId).collect(Collectors.toSet());
                staffIds.addAll(clientStaffIds);
                if (GeneralTool.isNotEmpty(staffIds)) {
                    staffNamesByIds = permissionCenterClient.getStaffNamesByIds(staffIds);
                }
                if (GeneralTool.isNotEmpty(clientStaffIds)) {
                    Map<Long, List<AgentStaff>> bdListMap = agentStaffs.stream().collect(Collectors.groupingBy(AgentStaff::getFkAgentId));
                    for (Map.Entry<Long, List<AgentStaff>> entry : bdListMap.entrySet()) {
                        AgentStaff agentStaff = entry.getValue().get(0);
                        Long fkStaffId = agentStaff.getFkStaffId();
                        String bdName = staffNamesByIds.get(fkStaffId);
                        if (GeneralTool.isNotEmpty(bdName)) {
                            bdNameMap.put(entry.getKey(), bdName);
                        }
                    }
                }
            }
        }
        if (GeneralTool.isNotEmpty(agentIds)) {
            agentNamesMap = agentService.getAgentNamesByIds(agentIds);
        }
        if (GeneralTool.isNotEmpty(staffIds) && GeneralTool.isEmpty(staffNamesByIds)) {
            staffNamesByIds = permissionCenterClient.getStaffNamesByIds(staffIds);
        }

        Set<Long> offerCountryIds = clientDtos.stream().map(ClientListInfoVo::getOfferCountryId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Long, String> countryNameMap = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(offerCountryIds)) {
            countryNameMap = institutionCenterClient.getCountryNamesByIds(offerCountryIds).getData();
        }

        //项目成员
        Map<Long, List<ClientProjectRoleStaffVo>> projectRoleStaffListMap = Maps.newHashMap();
        Map<Long, String> projectStaffNamesByIds = Maps.newHashMap();
        List<Long> clientIds = clientDtos.stream().map(ClientListInfoVo::getId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<ClientProjectRoleStaffVo> clientProjectRoleStaffVos = studentProjectRoleStaffMapper.selectProjectStaffByClientIds(clientIds);
        if (GeneralTool.isNotEmpty(clientProjectRoleStaffVos)) {
            projectRoleStaffListMap = clientProjectRoleStaffVos.stream().collect(Collectors.groupingBy(ClientProjectRoleStaffVo::getFkTableId));
            Set<Long> clientStaffIds = clientProjectRoleStaffVos.stream().map(ClientProjectRoleStaffVo::getFkStaffId).collect(Collectors.toSet());
            if (GeneralTool.isNotEmpty(clientStaffIds)) {
                projectStaffNamesByIds = permissionCenterClient.getStaffNamesByIds(clientStaffIds);
            }
        }

        for (ClientListInfoVo clientDto : clientDtos) {
            ClientListInfoDto listInfoVo = new ClientListInfoDto();
            BeanUtils.copyProperties(clientDto, listInfoVo);

            listInfoVo.setFkAgentName(clientDto.getAgentName());
            Object o = convert.get(clientDto.getId());
            if (o != null) {
                listInfoVo.setLastVisitTime((Date) o);
            }
            Object o1 = c2.get(clientDto.getId());
            if (o1 != null) {
                listInfoVo.setFollowUpTime((Date) o1);
            }
            listInfoVo.setRemark(clientDto.getOfferRemark());
            listInfoVo.setFkCompanyName(cMap.get(clientDto.getFkCompanyId()));
            if (projectRole.containsKey(clientDto.getOfferId())) {
                listInfoVo.setProjectRoleName((String) projectRole.get(clientDto.getOfferId()));
            }
//            listInfoVo.setFollowUpStatus(ClientEnum.getValueByKey(clientDto.getStatus(), ClientEnum.FUS));

            //入读/毕业院校
            String institutionNameEducation = "";
            String institutionNameEducation2 = "";
            if (GeneralTool.isNotEmpty(clientDto.getFkInstitutionIdEducation()) && GeneralTool.isNotEmpty(institutionNameMap.get(clientDto.getFkInstitutionIdEducation()))) {
                institutionNameEducation = institutionNameMap.get(clientDto.getFkInstitutionIdEducation());
            }
            if (GeneralTool.isNotEmpty(clientDto.getFkInstitutionNameEducation())) {
                institutionNameEducation = clientDto.getFkInstitutionNameEducation();
            }
            if (GeneralTool.isNotEmpty(clientDto.getFkInstitutionIdEducation2()) && GeneralTool.isNotEmpty(institutionNameMap.get(clientDto.getFkInstitutionIdEducation2()))) {
                institutionNameEducation2 = institutionNameMap.get(clientDto.getFkInstitutionIdEducation2());
            }
            if (GeneralTool.isNotEmpty(clientDto.getFkInstitutionNameEducation2())) {
                institutionNameEducation2 = clientDto.getFkInstitutionNameEducation2();
            }
            String institutionName = "";
            if (GeneralTool.isNotEmpty(institutionNameEducation)) {
                institutionName = "国内：" + institutionNameEducation;
            }
            if (GeneralTool.isNotEmpty(institutionNameEducation2)) {
                if (GeneralTool.isNotEmpty(institutionName)) {
                    institutionName = institutionName + "<br>";
                }
                institutionName = institutionName + "国际：" + institutionNameEducation2;
            }
            if (GeneralTool.isNotEmpty(institutionName)) {
                listInfoVo.setInstitutionName(institutionName);
            }

            //入读/毕业专业
            String educationMajor = (GeneralTool.isNotEmpty(clientDto.getEducationMajor()) ? clientDto.getEducationMajor() : "");
            String educationMajor2 = (GeneralTool.isNotEmpty(clientDto.getEducationMajor2()) ? clientDto.getEducationMajor2() : "");
            String educationMajorFullName = "";
            if (GeneralTool.isNotEmpty(educationMajor)) {
                educationMajorFullName = "国内：" + educationMajor;
            }
            if (GeneralTool.isNotEmpty(educationMajor2)) {
                if (GeneralTool.isNotEmpty(educationMajorFullName)) {
                    educationMajorFullName = educationMajorFullName + "<br>";
                }
                educationMajorFullName = educationMajorFullName + "国际：" + educationMajor2;
            }
            if (GeneralTool.isNotEmpty(educationMajorFullName)) {
                listInfoVo.setMajorName(educationMajorFullName);
            }


            //入读时间
            SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
            String startTimeEducation = GeneralTool.isNotEmpty(clientDto.getStartTimeEducation()) ? sf.format(clientDto.getStartTimeEducation()) : "";
            String startTimeEducation2 = GeneralTool.isNotEmpty(clientDto.getStartTimeEducation2()) ? sf.format(clientDto.getStartTimeEducation2()) : "";
            String startTimeEducationFullName = "";
            if (GeneralTool.isNotEmpty(startTimeEducation)) {
                startTimeEducationFullName = "国内：" + startTimeEducation;
            }
            if (GeneralTool.isNotEmpty(startTimeEducation2)) {
                if (GeneralTool.isNotEmpty(startTimeEducationFullName)) {
                    startTimeEducationFullName = startTimeEducationFullName + "<br>";
                }
                startTimeEducationFullName = startTimeEducationFullName + "国际：" + startTimeEducation2;
            }
            if (GeneralTool.isNotEmpty(startTimeEducationFullName)) {
                listInfoVo.setStartTimeEducation(startTimeEducationFullName);
            }


            //毕业时间
            String endTimeEducation = GeneralTool.isNotEmpty(clientDto.getEndTimeEducation()) ? sf.format(clientDto.getEndTimeEducation()) : "";
            String endTimeEducation2 = GeneralTool.isNotEmpty(clientDto.getEndTimeEducation2()) ? sf.format(clientDto.getEndTimeEducation2()) : "";
            String endTimeEducationFullName = "";
            if (GeneralTool.isNotEmpty(endTimeEducation)) {
                endTimeEducationFullName = "国内：" + endTimeEducation;
            }
            if (GeneralTool.isNotEmpty(endTimeEducation2)) {
                if (GeneralTool.isNotEmpty(endTimeEducationFullName)) {
                    endTimeEducationFullName = endTimeEducationFullName + "<br>";
                }
                endTimeEducationFullName = endTimeEducationFullName + "国际：" + endTimeEducation2;
            }
            if (GeneralTool.isNotEmpty(endTimeEducationFullName)) {
                listInfoVo.setEndTimeEducation(endTimeEducationFullName);
            }


            //是否入境
            if (GeneralTool.isNotEmpty(clientDto.getIsEnterCountry())) {
                listInfoVo.setIsEnterCountryName(clientDto.getIsEnterCountry() ? "是" : "否");
            } else {
                listInfoVo.setIsEnterCountryName("否");
            }

            //bd
            listInfoVo.setBdName(bdNameMap.get(clientDto.getFkAgentId()));

            //项目成员
            List<ClientProjectRoleStaffVo> projectRoleStaffDtos = projectRoleStaffListMap.get(clientDto.getOfferId());
            StringBuilder sb = new StringBuilder();
            if (GeneralTool.isNotEmpty(projectRoleStaffDtos)) {
                for (ClientProjectRoleStaffVo projectRoleStaffDto : projectRoleStaffDtos) {
                    if (sb.toString().contains("：")) {
                        sb.append("<br>");
                    }
                    sb.append(projectRoleStaffDto.getRoleName()).append("：");
                    if (GeneralTool.isNotEmpty(projectRoleStaffDto.getFkStaffId()) && GeneralTool.isNotEmpty(projectStaffNamesByIds.get(projectRoleStaffDto.getFkStaffId()))) {
                        sb.append(projectStaffNamesByIds.get(projectRoleStaffDto.getFkStaffId()));
                    }
                }
            }
            listInfoVo.setProjectRoleName(sb.toString());
            if (GeneralTool.isNotEmpty(clientDto.getOfferCountryId())) {
                listInfoVo.setOfferCountryName(countryNameIds.get(clientDto.getOfferCountryId()));
            }

            if (GeneralTool.isNotEmpty(clientDto.getStudentSource())) {
                listInfoVo.setIsBmsStudent(clientDto.getStudentSource().startsWith("ST"));
                if (clientDto.getStudentSource().startsWith("ST")) {
                    listInfoVo.setFkStudentId(clientDto.getFkTableId());
                }
            } else {
                listInfoVo.setIsBmsStudent(false);
            }

            if (GeneralTool.isNotEmpty(clientDto.getMaxStepOrder())) {
                ClientOfferStep clientOfferStep = clientOfferStepMap.get(clientDto.getMaxStepOrder());
                if (GeneralTool.isNotEmpty(clientOfferStep)) {
                    listInfoVo.setFkClientOfferStepName(clientOfferStep.getStepName());
                }

            }
            if (GeneralTool.isNotEmpty(clientDto.getFkAgentId())) {
                listInfoVo.setFkAgentName(agentNamesMap.get(clientDto.getFkAgentId()));
            } else if (GeneralTool.isNotEmpty(clientDto.getAgentName())) {
                listInfoVo.setFkAgentName(clientDto.getAgentName());
            }

            if (GeneralTool.isNotEmpty(clientDto.getSourceStaffId())) {
                listInfoVo.setSourceBdName(staffNamesByIds.get(clientDto.getSourceStaffId()));
            }

            if (GeneralTool.isNotEmpty(clientDto.getSourceAgentId())) {
                listInfoVo.setSourceAgentName(agentNamesMap.get(clientDto.getSourceAgentId()));
            }


            list.add(listInfoVo);
        }
    }

}

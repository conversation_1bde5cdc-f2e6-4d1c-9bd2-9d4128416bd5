package com.get.salecenter.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.salecenter.entity.ConventionPersonRegistration;

import java.util.List;
import java.util.Map;

/**
 * @author: Hardy
 * @create: 2022/7/14 10:43
 * @verison: 1.0
 * @description:
 */
public interface IConventionPersonRegistrationService {

    List<ConventionPersonRegistration> getConventionPersonRegistrationByCondition(LambdaQueryWrapper<ConventionPersonRegistration> lambdaQueryWrapper);

    Map<Long, String> getConventionPersonProvideNameMapByIds(List<Long> institutionAgentIds);
}

package com.get.salecenter.service.impl;

import cn.hutool.core.io.IoUtil;
import cn.hutool.poi.excel.BigExcelWriter;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.ConvertUtils;
import com.get.common.utils.DataConverter;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.DateUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.file.utils.FileUtils;
import com.get.financecenter.dto.PaymentFormParamDto;
import com.get.financecenter.dto.ReceiptFormParamDto;
import com.get.financecenter.dto.ServiceFeePaymentFormDto;
import com.get.financecenter.dto.ServiceFeeReceiptFormDto;
import com.get.financecenter.feign.IFinanceCenterClient;
import com.get.financecenter.vo.PaymentFormVo;
import com.get.financecenter.vo.ReceiptFormItemVo;
import com.get.financecenter.vo.ReceiptFormVo;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.StaffVo;
import com.get.salecenter.dao.sale.AgentContractAccountMapper;
import com.get.salecenter.dao.sale.PayablePlanMapper;
import com.get.salecenter.dao.sale.ReceivablePlanMapper;
import com.get.salecenter.dao.sale.StudentMapper;
import com.get.salecenter.dao.sale.StudentProjectRoleStaffMapper;
import com.get.salecenter.dao.sale.StudentServiceFeeCostMapper;
import com.get.salecenter.dao.sale.StudentServiceFeeMapper;
import com.get.salecenter.dto.CommentDto;
import com.get.salecenter.dto.MediaAndAttachedDto;
import com.get.salecenter.dto.ServiceFeeAPDto;
import com.get.salecenter.dto.ServiceFeeARAPDto;
import com.get.salecenter.dto.StudentFeeCommissionStatementExportDto;
import com.get.salecenter.dto.StudentServiceFeeDto;
import com.get.salecenter.dto.StudentServiceFeeSummaryDto;
import com.get.salecenter.entity.Agent;
import com.get.salecenter.entity.AgentContractAccount;
import com.get.salecenter.entity.PayablePlan;
import com.get.salecenter.entity.ReceivablePlan;
import com.get.salecenter.entity.SaleComment;
import com.get.salecenter.entity.Student;
import com.get.salecenter.entity.StudentServiceFee;
import com.get.salecenter.entity.StudentServiceFeeCost;
import com.get.salecenter.service.IAgentService;
import com.get.salecenter.service.IBusinessProviderService;
import com.get.salecenter.service.ICommentService;
import com.get.salecenter.service.IMediaAndAttachedService;
import com.get.salecenter.service.IPayablePlanService;
import com.get.salecenter.service.IReceivablePlanService;
import com.get.salecenter.service.IStudentProjectRoleStaffService;
import com.get.salecenter.service.StudentServiceFeeService;
import com.get.salecenter.utils.DocUtils;
import com.get.salecenter.utils.MyStringUtils;
import com.get.salecenter.utils.sale.GetAgentLabelDataUtils;
import com.get.salecenter.vo.AgentLabelVo;
import com.get.salecenter.vo.CommentVo;
import com.get.salecenter.vo.MediaAndAttachedVo;
import com.get.salecenter.vo.ServiceFeePayFormDetailVo;
import com.get.salecenter.vo.StudentProjectRoleStaffVo;
import com.get.salecenter.vo.StudentServiceFeeCostVo;
import com.get.salecenter.vo.StudentServiceFeeExportVo;
import com.get.salecenter.vo.StudentServiceFeeSummaryVo;
import com.get.salecenter.vo.StudentServiceFeeVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.StringJoiner;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static java.util.Arrays.stream;

/**
 * <AUTHOR>
 */
@Service
public class StudentServiceFeeServiceImpl extends ServiceImpl<StudentServiceFeeMapper, StudentServiceFee> implements StudentServiceFeeService {

    @Resource
    private StudentServiceFeeMapper studentServiceFeeMapper;

    @Resource
    private IMediaAndAttachedService attachedService;

    @Resource
    private ICommentService commentService;

    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Resource
    private StudentProjectRoleStaffMapper studentProjectRoleStaffMapper;

    @Resource
    private IInstitutionCenterClient iInstitutionCenterClient;

    @Resource
    private IFinanceCenterClient financeCenterClient;

    @Resource
    @Lazy
    private IAgentService agentService;

    @Resource
    private ReceivablePlanMapper receivablePlanMapper;

    @Resource
    private IReceivablePlanService receivablePlanService;

    @Resource
    private IPayablePlanService payablePlanService;

    @Resource
    private PayablePlanMapper payablePlanMapper;

    @Resource
    private StudentMapper studentMapper;

    @Resource
    private UtilService<Object> utilService;
    @Resource
    private IStudentProjectRoleStaffService projectRoleStaffService;

    @Resource
    private StudentServiceFeeCostMapper studentServiceFeeCostMapper;

    @Resource
    private IBusinessProviderService businessProviderService;
    @Resource
    private AgentContractAccountMapper agentContractAccountMapper;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    private GetAgentLabelDataUtils getAgentLabelDataUtils;

    /**
     * Author Cream
     * Description : //获取列表数据
     * Date 2023/2/7 11:21
     * Params:
     * Return
     */
    @Override
    public ListResponseBo<StudentServiceFeeVo> datas(StudentServiceFeeDto studentServiceFeeDto, Page page) {
        if (Objects.isNull(studentServiceFeeDto) || Objects.isNull(studentServiceFeeDto.getFkStudentId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        IPage<StudentServiceFeeVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<StudentServiceFeeVo> list = studentServiceFeeMapper.datas(iPage, studentServiceFeeDto);
        Set<Long> agentIds = list.stream().map(StudentServiceFeeVo::getFkAgentId).collect(Collectors.toSet());
        Map<Long, List<AgentLabelVo>> agentLabelMap = getAgentLabelDataUtils.getAgentLabelMap(agentIds).getAgentLabelMap();
        if (GeneralTool.isNotEmpty(agentIds)) {
            list.forEach(item -> item.setAgentLabelVos(agentLabelMap.get(item.getFkAgentId())));
        }
        page.setAll((int) iPage.getTotal());
        if (list.isEmpty()) {
            return new ListResponseBo<>();
        }
        setList(list);
        return new ListResponseBo<>(list, BeanCopyUtils.objClone(page, Page::new));
    }

    private void setList(List<StudentServiceFeeVo> list) {
        List<Long> ids = list.stream().map(StudentServiceFeeVo::getId).collect(Collectors.toList());
        List<StudentProjectRoleStaffVo> studentProjectRoleStaffVos = studentProjectRoleStaffMapper.selectProjectStaffByIds(TableEnum.SALE_STUDENT_SERVICE_FEE.key, new ArrayList<>(ids));
        Map<Long, List<StudentProjectRoleStaffVo>> studentProjectRoleStaffMap =
                studentProjectRoleStaffVos.stream().collect(Collectors.groupingBy(StudentProjectRoleStaffVo::getFkTableId));
        //获取国家id
        Set<Long> countryIds = stream(list.stream().filter(f -> StringUtils.isNotBlank(f.getFkAreaCountryIds()))
                .map(StudentServiceFeeVo::getFkAreaCountryIds).collect(Collectors.joining(",")).split(",")).map(Long::valueOf).collect(Collectors.toSet());

        Map<Long, String> studentNameMap = new HashedMap();
        Map<Long, String> providerNameMap = new HashedMap();
        List<Long> studentIds = list.stream().filter(f -> f.getFkTypeKeyReceivable().equals(TableEnum.SALE_STUDENT.key))
                .map(StudentServiceFeeVo::getFkTypeTargetIdReceivable).collect(Collectors.toList());
        studentIds.addAll(list.stream().map(StudentServiceFeeVo::getFkStudentId).filter(Objects::nonNull).collect(Collectors.toList()));
        if (GeneralTool.isNotEmpty(studentIds)) {
            List<Student> students = studentMapper.selectBatchIds(studentIds);
            students.forEach(student -> {
                studentNameMap.put(student.getId(), student.getName() + "（" + student.getFirstName() + student.getLastName() + "）");
            });
        }
        Set<Long> providerIds = list.stream().filter(f -> f.getFkTypeKeyReceivable().equals(TableEnum.INSTITUTION_PROVIDER.key))
                .map(StudentServiceFeeVo::getFkTypeTargetIdReceivable).collect(Collectors.toSet());
        if (GeneralTool.isNotEmpty(providerIds)) {
            providerNameMap = institutionCenterClient.getInstitutionProviderNamesByIds(providerIds).getData();
        }
        //查询国家
        Map<Long, String> data = iInstitutionCenterClient.getCountryFullNamesByIds(countryIds).getData();
        for (StudentServiceFeeVo serviceFeeDto : list) {
            serviceFeeDto.setFkAreaCountryName(getCountryName(serviceFeeDto.getFkAreaCountryIds(), data));
            serviceFeeDto.setStudentProjectRoleStaffDtos(studentProjectRoleStaffMap.get(serviceFeeDto.getId()));
            serviceFeeDto.setFkTypeKeyReceivableName(TableEnum.getValueByKey(serviceFeeDto.getFkTypeKeyReceivable(), TableEnum.TYPE_KEY_RECEIVABLE));
            if (GeneralTool.isNotEmpty(serviceFeeDto.getFkStudentId())) {
                serviceFeeDto.setFkStudentName(studentNameMap.get(serviceFeeDto.getFkStudentId()));
            }
            if (TableEnum.SALE_STUDENT.key.equals(serviceFeeDto.getFkTypeKeyReceivable())) {
                serviceFeeDto.setReceivableName(studentNameMap.get(serviceFeeDto.getFkTypeTargetIdReceivable()));
            } else if (TableEnum.INSTITUTION_PROVIDER.key.equals(serviceFeeDto.getFkTypeKeyReceivable())) {
                serviceFeeDto.setReceivableName(providerNameMap.get(serviceFeeDto.getFkTypeTargetIdReceivable()));
            }
        }
    }

    /**
     * Author Cream
     * Description : // 详情
     * Date 2023/1/30 14:24
     * Params:
     * Return
     */
    @Override
    public StudentServiceFeeVo findServiceFeeById(Long id) {
        if (Objects.isNull(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        StudentServiceFeeVo serviceFeeDto = studentServiceFeeMapper.findById(id);
        String fkAreaCountryIds = serviceFeeDto.getFkAreaCountryIds();
        if (StringUtils.isNotBlank(fkAreaCountryIds)) {
            Set<Long> countryIds = Arrays.stream(fkAreaCountryIds.split(",")).map(Long::valueOf).collect(Collectors.toSet());
            Map<Long, String> data = iInstitutionCenterClient.getCountryNamesByIds(countryIds).getData();
            String val = String.join(",", data.values());
            serviceFeeDto.setFkAreaCountryName(val);
            serviceFeeDto.setAreaCountryIds(countryIds);
        }
        Long fkAgentId = serviceFeeDto.getFkAgentId();
        if (GeneralTool.isNotEmpty(fkAgentId)) {
            Agent agent = agentService.getAgentById(fkAgentId);
            serviceFeeDto.setAgentName(agent.getName());
            if (StringUtils.isNotBlank(agent.getNameNote())) {
                serviceFeeDto.setAgentName(serviceFeeDto.getAgentName() + "（" + agent.getNameNote() + "）");
            }
        }
        Long fkStaffId = serviceFeeDto.getFkStaffId();
        if (GeneralTool.isNotEmpty(fkStaffId)) {
            StaffVo data = permissionCenterClient.getStaffById(fkStaffId).getData();
            if (GeneralTool.isNotEmpty(data)) {
                serviceFeeDto.setBdName(data.getFullName());
            }
        }
        String fkCurrencyTypeNum = serviceFeeDto.getFkCurrencyTypeNum();
        if (StringUtils.isNotBlank(fkCurrencyTypeNum)) {
            serviceFeeDto.setFkCurrencyNumName(financeCenterClient.getCurrencyNameByNum(fkCurrencyTypeNum).getData());
        }
        Student student = studentMapper.selectById(serviceFeeDto.getFkStudentId());
        if (GeneralTool.isNotEmpty(student)) {
            serviceFeeDto.setFkStudentName(student.getName() + "（" + student.getLastName() + student.getFirstName() + "）");
        }
        List<StudentProjectRoleStaffVo> projectRoleStaffDtos = studentProjectRoleStaffMapper.selectProjectStaffById(TableEnum.SALE_STUDENT_SERVICE_FEE.key, id);
        serviceFeeDto.setStudentProjectRoleStaffDtos(projectRoleStaffDtos);

        serviceFeeDto.setFkTypeKeyReceivableName(TableEnum.getValueByKey(serviceFeeDto.getFkTypeKeyReceivable(), TableEnum.TYPE_KEY_RECEIVABLE));
        List<StudentServiceFeeVo> studentServiceFeeVos = studentServiceFeeMapper.getFeeTargetName(Collections.singletonList(serviceFeeDto.getId()));
        if (GeneralTool.isNotEmpty(studentServiceFeeVos)) {
            serviceFeeDto.setReceivableName(studentServiceFeeVos.get(0).getReceivableName());
        }
        // 获取该服务费的应收应付
        Integer reCount = receivablePlanMapper.selectCount(Wrappers.<ReceivablePlan>lambdaQuery()
                .eq(ReceivablePlan::getFkTypeKey, TableEnum.SALE_STUDENT_SERVICE_FEE.key)
                .eq(ReceivablePlan::getFkTypeTargetId, id)
                .ne(ReceivablePlan::getStatus, 0));
        Integer pyCount = payablePlanMapper.selectCount(Wrappers.<PayablePlan>lambdaQuery()
                .eq(PayablePlan::getFkTypeKey, TableEnum.SALE_STUDENT_SERVICE_FEE.key)
                .eq(PayablePlan::getFkTypeTargetId, id)
                .ne(PayablePlan::getStatus, 0));
        serviceFeeDto.setShowCancelRePy(reCount > 0 || pyCount > 0);
        return serviceFeeDto;
    }

    /**
     * Author Cream
     * Description : //获取留学服务费
     * Date 2023/2/15 12:05
     * Params:
     * Return
     */
    @Override
    public List<StudentServiceFeeVo> getServiceFeeByIds(List<Long> ids) {
        if (ids.isEmpty()) {
            return Collections.emptyList();
        }
        List<StudentServiceFeeVo> studentServiceFeeVoList = studentServiceFeeMapper.getServiceFeeByIds(ids);
        setList(studentServiceFeeVoList);
        return studentServiceFeeVoList;
    }


    /**
     * Author Cream
     * Description : // 保存
     * Date 2023/1/30 14:24
     * Params:
     * Return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public SaveResponseBo save(StudentServiceFeeDto studentServiceFeeDto) {
        if (Objects.isNull(studentServiceFeeDto) || Objects.isNull(studentServiceFeeDto.getFkStudentId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        StudentServiceFee studentServiceFee = BeanCopyUtils.objClone(studentServiceFeeDto, StudentServiceFee::new);
        String country = studentServiceFeeDto.getFkAreaCountryIds().stream().map(String::valueOf).collect(Collectors.joining(","));
        studentServiceFee.setFkAreaCountryIds(country);
        studentServiceFee.setStatus(1);
        studentServiceFee.setBusinessStatus(0);
        studentServiceFee.setApproveStatus(0);
        //新增服务费，设置结算状态，默认设置0 未处理
        studentServiceFee.setSettlementStatus(0);
        utilService.setCreateInfo(studentServiceFee);
        studentServiceFeeMapper.insert(studentServiceFee);
        studentServiceFee.setNum(MyStringUtils.getStudentServiceFeeNum(studentServiceFee.getId()));
        studentServiceFeeMapper.updateById(studentServiceFee);
        //批量添加项目成员
        projectRoleStaffService.batchProcessorPrs(studentServiceFeeDto.getRoleStaffVo(), studentServiceFee.getId(), TableEnum.SALE_STUDENT_SERVICE_FEE.key);

        // 上传附件
        if (GeneralTool.isNotEmpty(studentServiceFeeDto.getMediaAttachedVo())) {
            List<MediaAndAttachedDto> mediaAndAttachedDtoList = studentServiceFeeDto.getMediaAttachedVo();
            for (MediaAndAttachedDto mediaAndAttachedDto : mediaAndAttachedDtoList) {
                mediaAndAttachedDto.setFkTableId(studentServiceFee.getId());
                mediaAndAttachedDto.setFkTableName(TableEnum.SALE_STUDENT_SERVICE_FEE.key);
                attachedService.addMediaAndAttached(mediaAndAttachedDto);
            }
        }
        return new SaveResponseBo(studentServiceFee.getId());
    }

    /**
     * Author Cream
     * Description : //编辑
     * Date 2023/2/2 15:44
     * Params:
     * Return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseBo<StudentServiceFeeVo> update(StudentServiceFeeDto studentServiceFeeDto) {
        if (Objects.isNull(studentServiceFeeDto) || Objects.isNull(studentServiceFeeDto.getFkStudentId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        StudentServiceFee studentServiceFee = studentServiceFeeMapper.selectById(studentServiceFeeDto.getId());
        if (Objects.isNull(studentServiceFee)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("service_fee_does_not_exist"));
        }
        BeanUtils.copyProperties(studentServiceFeeDto, studentServiceFee);
        if (GeneralTool.isNotEmpty(studentServiceFeeDto.getFkAreaCountryIds())) {
            //逗号分隔
            String fkAreaCountryIds = studentServiceFeeDto.getFkAreaCountryIds().stream().map(String::valueOf).collect(Collectors.joining(","));
            studentServiceFee.setFkAreaCountryIds(fkAreaCountryIds);
        }
        utilService.setUpdateInfo(studentServiceFee);
        studentServiceFeeMapper.updateById(studentServiceFee);

        //更新对应的应收计划的币种 定额 和 应收金额数据
        LambdaQueryWrapper<ReceivablePlan> receivablePlanLambdaQueryWrapper = new LambdaQueryWrapper<>();
        if(GeneralTool.isNotEmpty(studentServiceFeeDto.getId()) ){
            receivablePlanLambdaQueryWrapper.eq(ReceivablePlan::getFkTypeKey, TableEnum.SALE_STUDENT_SERVICE_FEE.key);
            receivablePlanLambdaQueryWrapper.eq(ReceivablePlan::getFkTypeTargetId, studentServiceFeeDto.getId());
        }

        List<ReceivablePlan> receivablePlanList = receivablePlanMapper.selectList(receivablePlanLambdaQueryWrapper);
        if (GeneralTool.isNotEmpty(receivablePlanList)){
            for (ReceivablePlan plan : receivablePlanList) {
                ReceivablePlan updatePlan = BeanCopyUtils.objClone(plan, ReceivablePlan::new);
                updatePlan.setFkCurrencyTypeNum(studentServiceFeeDto.getFkCurrencyTypeNum());
                updatePlan.setFixedAmount(studentServiceFeeDto.getAmount());
                //应收金额等于 金额 +稅金
                if (GeneralTool.isNotEmpty(studentServiceFeeDto.getTaxes())){
                    updatePlan.setReceivableAmount(studentServiceFeeDto.getAmount().add(studentServiceFeeDto.getTaxes()));
                }else {
                    updatePlan.setReceivableAmount(studentServiceFeeDto.getAmount());
                }
                utilService.updateUserInfoToEntity(updatePlan);
                receivablePlanMapper.update(updatePlan, new LambdaUpdateWrapper<ReceivablePlan>().eq(ReceivablePlan::getId, plan.getId()));
            }
        }

        //批量添加项目成员
        projectRoleStaffService.batchProcessorPrs(studentServiceFeeDto.getRoleStaffVo(), studentServiceFee.getId(), TableEnum.SALE_STUDENT_SERVICE_FEE.key);
        return new ResponseBo<>(findServiceFeeById(studentServiceFeeDto.getId()));
    }

    /**
     * Author Cream
     * Description : //一键作废
     * Date 2023/2/2 15:49
     * Params:
     * Return
     */
    @Override
    public SaveResponseBo cancel(Long id, Integer status) {
        if (Objects.isNull(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        StudentServiceFee studentServiceFee = studentServiceFeeMapper.selectById(id);
        if (Objects.isNull(studentServiceFee)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("service_fee_does_not_exist"));
        }
        Integer reCount = receivablePlanMapper.selectCount(Wrappers.<ReceivablePlan>lambdaQuery()
                .eq(ReceivablePlan::getFkTypeTargetId, id)
                .ne(ReceivablePlan::getStatus, 0)
                .eq(ReceivablePlan::getFkTypeKey, TableEnum.SALE_STUDENT_SERVICE_FEE.key));
        Integer pyCount = payablePlanMapper.selectCount(Wrappers.<PayablePlan>lambdaQuery()
                .eq(PayablePlan::getFkTypeTargetId, id)
                .ne(PayablePlan::getStatus, 0)
                .eq(PayablePlan::getFkTypeKey, TableEnum.SALE_STUDENT_SERVICE_FEE.key));
        if (reCount > 0 || pyCount > 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("cannot_be_invalidated"));
        }
        studentServiceFee.setStatus(status);
        utilService.setUpdateInfo(studentServiceFee);
        studentServiceFeeMapper.updateById(studentServiceFee);
        return SaveResponseBo.ok(id);
    }

    /**
     * 作废应收应付
     *
     * @param serviceFeeId 服务费Id
     * @return
     */
    @Override
    public UpdateResponseBo cancelReceivablePayable(Long serviceFeeId) {
        if (GeneralTool.isEmpty(serviceFeeId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        // 获取该服务费的应收计划
        List<ReceivablePlan> receivablePlans = receivablePlanMapper.selectList(Wrappers.<ReceivablePlan>lambdaQuery()
                .eq(ReceivablePlan::getFkTypeKey, TableEnum.SALE_STUDENT_SERVICE_FEE.key)
                .eq(ReceivablePlan::getFkTypeTargetId, serviceFeeId)
                .ne(ReceivablePlan::getStatus, 0));
        // 获取该服务费的应付计划
        List<PayablePlan> payablePlans = payablePlanMapper.selectList(Wrappers.<PayablePlan>lambdaQuery()
                .eq(PayablePlan::getFkTypeKey, TableEnum.SALE_STUDENT_SERVICE_FEE.key)
                .eq(PayablePlan::getFkTypeTargetId, serviceFeeId)
                .ne(PayablePlan::getStatus, 0));

        if (GeneralTool.isNotEmpty(receivablePlans)) {
            Set<Long> receivablePlanIds = receivablePlans.stream().map(ReceivablePlan::getId).collect(Collectors.toSet());
            // 判断是否有收款单
            List<ReceiptFormItemVo> receiptFormItemList = financeCenterClient.getReceiptFormItemListFeignByPlanIds(receivablePlanIds);
            if (GeneralTool.isNotEmpty(receiptFormItemList)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("receipt_form_item_exist_cancel_fail"));
            }
            // 作废应收计划
            receivablePlans.forEach(receivablePlan -> {
                receivablePlan.setStatus(0);
                utilService.setUpdateInfo(receivablePlan);
            });
            boolean cancelRe = receivablePlanService.updateBatchById(receivablePlans, DEFAULT_BATCH_SIZE);
            if (!cancelRe) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
            }
        }

        if (GeneralTool.isNotEmpty(payablePlans)) {
            Set<Long> payablePlanIds = payablePlans.stream().map(PayablePlan::getId).collect(Collectors.toSet());
            // 判断是否有付款单
            List<PaymentFormVo> paymentFormItemList = financeCenterClient.getPayFormListFeignByPlanIds(payablePlanIds);
            if (GeneralTool.isNotEmpty(paymentFormItemList)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("pay_form_item_exist_cancel_fail"));
            }
            // 作废应付计划
            payablePlans.forEach(payablePlan -> {
                payablePlan.setStatus(0);
                utilService.setUpdateInfo(payablePlan);
            });
            boolean cancelPy = payablePlanService.updateBatchById(payablePlans, DEFAULT_BATCH_SIZE);
            if (!cancelPy) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
            }
        }
        return UpdateResponseBo.ok(serviceFeeId);
    }

    /**
     * Author Cream
     * Description : //获取附件列表
     * Date 2023/2/8 11:19
     * Params:
     * Return
     */
    @Override
    public ResponseBo<MediaAndAttachedVo> getAttacheFileMedia(MediaAndAttachedDto attachedVo, Page page) {
        if (Objects.isNull(attachedVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        attachedVo.setFkTableName(TableEnum.SALE_STUDENT_SERVICE_FEE.key);
        List<MediaAndAttachedVo> mediaAndAttachedVo = attachedService.getMediaAndAttachedDto(attachedVo, page,
                FileTypeEnum.M_STUDENT_SERVICE_FEE_INVOICE.key, FileTypeEnum.M_STUDENT_SERVICE_FEE_MEDIA.key);
        if (GeneralTool.isNotEmpty(mediaAndAttachedVo)) {
            for (MediaAndAttachedVo m : mediaAndAttachedVo) {
                m.setTypeValue(FileTypeEnum.getValue(m.getTypeKey()));
            }
        }
        return new ListResponseBo<>(mediaAndAttachedVo, BeanCopyUtils.objClone(page, Page::new));
    }

    /**
     * Author Cream
     * Description : //上传附件
     * Date 2023/2/8 11:34
     * Params:
     * Return
     */
    @Override
    public List<MediaAndAttachedVo> addItemMedia(ValidList<MediaAndAttachedDto> mediaAttachedVo) {
        if (GeneralTool.isEmpty(mediaAttachedVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        List<MediaAndAttachedVo> mediaAndAttachedVos = new ArrayList<>();
        for (MediaAndAttachedDto mediaAndAttachedDto : mediaAttachedVo) {
            //设置插入的表
            mediaAndAttachedDto.setFkTableName(TableEnum.SALE_STUDENT_SERVICE_FEE.key);
            mediaAndAttachedVos.add(attachedService.addMediaAndAttached(mediaAndAttachedDto));
        }
        return mediaAndAttachedVos;
    }

    /**
     * Author Cream
     * Description : //编辑备注
     * Date 2023/2/8 11:28
     * Params:
     * Return
     */
    @Override
    public SaveResponseBo editComment(CommentDto commentDto) {
        SaleComment comment = BeanCopyUtils.objClone(commentDto, SaleComment::new);
        if (GeneralTool.isNotEmpty(commentDto)) {
            if (GeneralTool.isNotEmpty(commentDto.getId())) {
                comment.setFkTableName(TableEnum.SALE_STUDENT_SERVICE_FEE.key);
                commentService.updateComment(comment);
            } else {
                comment.setFkTableName(TableEnum.SALE_STUDENT_SERVICE_FEE.key);
                commentService.addComment(comment);
            }
        }
        return SaveResponseBo.ok(comment.getId());
    }

    @Override
    public ResponseBo<CommentVo> getComments(CommentDto commentDto, Page page) {
        commentDto.setFkTableName(TableEnum.SALE_STUDENT_SERVICE_FEE.key);
        List<CommentVo> datas = commentService.datas(commentDto, page);
        return new ListResponseBo<>(datas, BeanCopyUtils.objClone(page, Page::new));
    }

    /**
     * Author Cream
     * Description : //获取学生对象id
     * Date 2023/2/8 10:14
     * Params:
     * Return
     */
    @Override
    public List<Long> getServiceFeeStudentIds(String targetName) {
        if (targetName != null) {
            targetName = targetName.trim();
        }
        return studentServiceFeeMapper.getStudentIdsByTargetName(targetName);
    }

    /**
     * Author Cream
     * Description : //获取公司服务费下的学生列表
     * Date 2023/1/30 16:18
     * Params:
     * Return
     */
    @Override
    public List<BaseSelectEntity> getInvoiceStudentSelection(Long companyId) {
        return studentServiceFeeMapper.getStudentListByCompanyId(companyId);
    }


    /**
     * Author Cream
     * Description : //留学服务费应收应付汇总
     * Date 2023/2/2 17:13
     * Params:
     * Return
     */
    @Override
    public ResponseBo<StudentServiceFeeSummaryVo> serviceFeeSummary(StudentServiceFeeSummaryDto studentServiceFeeSummaryDto, Page page) {
        if (Objects.isNull(studentServiceFeeSummaryDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        //处理要查询的字符串
        studentServiceFeeSummaryDto.setStudentName(DataConverter.stringManipulation(studentServiceFeeSummaryDto.getStudentName()));
        studentServiceFeeSummaryDto.setBdName(DataConverter.stringManipulation(studentServiceFeeSummaryDto.getBdName()));
        studentServiceFeeSummaryDto.setProjectRoleName(DataConverter.stringManipulation(studentServiceFeeSummaryDto.getProjectRoleName()));
        Long staffId = SecureUtil.getStaffId();
        //获取当前登录人的所有业务下属
        List<Long> staffIds = new ArrayList<>();
        Result<List<Long>> result_ = permissionCenterClient.getStaffFollowerIds(staffId);
        if (result_.isSuccess() && GeneralTool.isNotEmpty(result_.getData())) {
            staffIds.addAll(result_.getData());
        }
        staffIds.add(staffId);
        //查询
        List<StudentServiceFeeSummaryVo> serviceFeeSummary = Lists.newArrayList();
        long ot1 = System.currentTimeMillis();
        if (GeneralTool.isNotEmpty(page)) {
            IPage<StudentServiceFeeSummaryVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
            serviceFeeSummary = studentServiceFeeMapper.serviceFeeSummary(iPage, studentServiceFeeSummaryDto, staffIds, SecureUtil.getStaffInfo().getIsStudentAdmin(), SecureUtil.getPermissionGroupInstitutionIds(), SecureUtil.getStaffBoundBdIds());
            page.setAll((int) iPage.getTotal());
        } else {
            serviceFeeSummary = studentServiceFeeMapper.serviceFeeSummary(null, studentServiceFeeSummaryDto, staffIds, SecureUtil.getStaffInfo().getIsStudentAdmin(), SecureUtil.getPermissionGroupInstitutionIds(), SecureUtil.getStaffBoundBdIds());
        }
        long ot2 = System.currentTimeMillis() - ot1;
        if (serviceFeeSummary.isEmpty()) {
            return new ListResponseBo<>(Collections.emptyList());
        }
        //获取国家id
        Set<Long> countryIds = Arrays.stream(serviceFeeSummary.stream().filter(f -> StringUtils.isNotBlank(f.getCountryIds()))
                .map(StudentServiceFeeSummaryVo::getCountryIds).collect(Collectors.joining(",")).split(",")).map(Long::valueOf).collect(Collectors.toSet());
        //查询国家
        long ft1 = System.currentTimeMillis();
        Map<Long, String> data = iInstitutionCenterClient.getCountryFullNamesByIds(countryIds).getData();
        long ft2 = System.currentTimeMillis() - ft1;
        //获取币种名称
        Set<String> nums = serviceFeeSummary.stream().map(StudentServiceFeeSummaryVo::getServiceFeeCurrencyNum).collect(Collectors.toSet());
        List<Long> ids = serviceFeeSummary.stream().map(StudentServiceFeeSummaryVo::getServiceFeeId).collect(Collectors.toList());

        //获取代理id
        Set<Long> agentIds = serviceFeeSummary.stream().map(StudentServiceFeeSummaryVo::getFkAgentId).collect(Collectors.toSet());
        //代理标签
        Map<Long, List<AgentLabelVo>> agentLabelMap = getAgentLabelDataUtils.getAgentLabelMap(agentIds).getAgentLabelMap();

        //获取成本支出
        List<StudentServiceFeeCost> studentServiceFeeCosts = studentServiceFeeCostMapper.selectList(Wrappers.lambdaQuery(StudentServiceFeeCost.class)
                .eq(StudentServiceFeeCost::getStatus, 1)
                .in(StudentServiceFeeCost::getFkStudentServiceFeeId, ids)
        );
        //
        Set<Long> businessProviderIds = studentServiceFeeCosts.stream().map(StudentServiceFeeCost::getFkBusinessProviderId).collect(Collectors.toSet());
        Map<Long, String> businessProviderMap = businessProviderService.getNamesByIds(businessProviderIds);

        Map<Long, List<StudentServiceFeeCost>> studentServiceFeeCostsMap = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(studentServiceFeeCosts)) {
            Set<String> fkCurrencyTypeNums = studentServiceFeeCosts.stream().map(StudentServiceFeeCost::getFkCurrencyTypeNum).collect(Collectors.toSet());
            if (GeneralTool.isNotEmpty(fkCurrencyTypeNums)) {
                nums.addAll(fkCurrencyTypeNums);
            }
            studentServiceFeeCostsMap = studentServiceFeeCosts.stream().collect(Collectors.groupingBy(StudentServiceFeeCost::getFkStudentServiceFeeId));
        }
        Map<String, String> numsMap = financeCenterClient.getCurrencyNamesByNums(nums).getData();

        //获取项目成员
        List<StudentProjectRoleStaffVo> studentProjectRoleStaffVos = studentProjectRoleStaffMapper.selectProjectStaffByIds(TableEnum.SALE_STUDENT_SERVICE_FEE.key, ids);
        Map<Long, List<StudentProjectRoleStaffVo>> studentProjectRoleStaffMap =
                studentProjectRoleStaffVos.stream().collect(Collectors.groupingBy(StudentProjectRoleStaffVo::getFkTableId));
        List<ServiceFeePayFormDetailVo> paidAmountByIds = studentServiceFeeMapper.getPaidAmountByIds(ids);
        Map<Long, List<ServiceFeePayFormDetailVo>> collect = paidAmountByIds.stream().collect(Collectors.groupingBy(ServiceFeePayFormDetailVo::getServiceFeeId));
        //参考 /sale/studentInsurance/datas
        Map<Long, List<Long>> reMap = receivablePlanService.getReceivablePlanIds(TableEnum.SALE_STUDENT_SERVICE_FEE.key, Sets.newHashSet(ids));
        Map<Long, List<Long>> paMap = payablePlanService.getPayablePlanIds(TableEnum.SALE_STUDENT_SERVICE_FEE.key, Sets.newHashSet(ids));

        // 获取收款单信息
//        Map<Long, List<ReceiptFormVo>> receiptFormMapToStudent = Maps.newHashMap();
//        Map<Long, List<ReceiptFormVo>> receiptFormMapToProvider = Maps.newHashMap();
        Map<Long, List<ReceiptFormVo>> receiptFormMap = Maps.newHashMap();


        Map<Long, String> studentNameMap = new HashedMap();
        Map<Long, String> providerNameMap = new HashedMap();
        if (GeneralTool.isNotEmpty(serviceFeeSummary)) {
            List<Long> studentIds = serviceFeeSummary.stream().filter(f -> f.getFkTypeKeyReceivable().equals(TableEnum.SALE_STUDENT.key))
                    .map(StudentServiceFeeSummaryVo::getFkTypeTargetIdReceivable).collect(Collectors.toList());
            if (GeneralTool.isNotEmpty(studentIds)) {
                List<Student> students = studentMapper.selectBatchIds(studentIds);
                students.forEach(student -> {
                    studentNameMap.put(student.getId(), student.getName() + "（" + student.getFirstName() + student.getLastName() + "）");
                });
            }
            Set<Long> providerIds = serviceFeeSummary.stream().filter(f -> f.getFkTypeKeyReceivable().equals(TableEnum.INSTITUTION_PROVIDER.key))
                    .map(StudentServiceFeeSummaryVo::getFkTypeTargetIdReceivable).collect(Collectors.toSet());
            if (GeneralTool.isNotEmpty(providerIds)) {
                providerNameMap = institutionCenterClient.getInstitutionProviderNamesByIds(providerIds).getData();
            }

            /**
             * 获取收款单信息
             */

            List<Long> feeIds = serviceFeeSummary.stream().map(studentServiceFeeSummaryVo -> studentServiceFeeSummaryVo.getServiceFeeId()).collect(Collectors.toList());
            Result<Map<Long, List<ReceiptFormVo>>> receiptFormToStudentResult = financeCenterClient.getReceiptFormsByFeeIds(feeIds);
            if (receiptFormToStudentResult.isSuccess() && GeneralTool.isNotEmpty(receiptFormToStudentResult.getData())) {
                receiptFormMap = receiptFormToStudentResult.getData();
            }

            // 学生收款单
            //Result<Map<Long, List<ReceiptFormVo>>> receiptFormToStudentResult = financeCenterClient.getReceiptFormsByTargetIds(TableEnum.SALE_STUDENT.key, Sets.newHashSet(studentIds));
//            Result<Map<Long, List<ReceiptFormVo>>> receiptFormToStudentResult = financeCenterClient.getReceiptFormsByTargetIds(TableEnum.SALE_STUDENT.key, Sets.newHashSet(studentIds));
//            if (receiptFormToStudentResult.isSuccess() && GeneralTool.isNotEmpty(receiptFormToStudentResult.getData())) {
//                receiptFormMapToStudent = receiptFormToStudentResult.getData();
//            }
//            // 提供商收款单
//            Result<Map<Long, List<ReceiptFormVo>>> receiptFormToProviderResult = financeCenterClient.getReceiptFormsByTargetIds(TableEnum.INSTITUTION_PROVIDER.key, Sets.newHashSet(providerIds));
//            if (receiptFormToProviderResult.isSuccess() && GeneralTool.isNotEmpty(receiptFormToProviderResult.getData())) {
//                receiptFormMapToProvider = receiptFormToProviderResult.getData();
//            }
        }

        // 根据登录人的公司，若系统参数有设置值，就显示税金的方式，否则隐藏
        List<Long> showTaxesCompanyIds = Lists.newArrayList();
        Map<Long, String> configMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.STUDENT_SERVICE_FEE_TAXES.key, 1).getData();
        for (Map.Entry<Long, String> entry : configMap.entrySet()) {
            String value = entry.getValue();
            JSONObject taxesConfig = JSONObject.parseObject(value);
            if (!taxesConfig.isEmpty()) {
                showTaxesCompanyIds.add(entry.getKey());
            }
        }
        // 是否显示税金相关内容
        boolean flag = showTaxesCompanyIds.contains(SecureUtil.getFkCompanyId());

        for (StudentServiceFeeSummaryVo summaryDto : serviceFeeSummary) {
            List<Long> receivablePlanIds = reMap.getOrDefault(summaryDto.getServiceFeeId(), Lists.newArrayList());
            List<Long> payablePlanIds = paMap.getOrDefault(summaryDto.getServiceFeeId(), Lists.newArrayList());
            if (GeneralTool.isNotEmpty(payablePlanIds)) { // 一般应付计划只有一个，如果有多个取MIN(id)
                Long minPayablePlanId = payablePlanIds.stream().min(Long::compare).get();
                // 应付计划Id
                summaryDto.setPayablePlanId(minPayablePlanId);
            }
            receivablePlanIds.addAll(payablePlanIds);
            summaryDto.setARAPIds(receivablePlanIds);
            //封装国家信息
            summaryDto.setCountryNames(getCountryName(summaryDto.getCountryIds(), data));
            summaryDto.setCollectionStatus(ProjectExtraEnum.getValueByKey(summaryDto.getReceiveStatus(), ProjectExtraEnum.AR_STATUS));
            summaryDto.setPaymentStatus(ProjectExtraEnum.getValueByKey(summaryDto.getPayableStatus(), ProjectExtraEnum.AP_STATUS));
            //设置留学服务费的结算状态名称
            if (GeneralTool.isNotEmpty(summaryDto.getSettlementStatus())) {
                // summaryDto.setSettlementStatusName(ProjectExtraEnum.getValueByKey(summaryDto.getSettlementStatus(), ProjectExtraEnum.SETTLEMENT_TYPE));
                summaryDto.setSettlementStatusName(ProjectExtraEnum.getInitialValueByKey(summaryDto.getSettlementStatus(), ProjectExtraEnum.STUDY_ABROAD_FEE_STATUS));
            }
            summaryDto.setServiceFeeCurrencyName(numsMap.get(summaryDto.getServiceFeeCurrencyNum()));
            summaryDto.setPayDetailList(collect.get(summaryDto.getServiceFeeId()));
            summaryDto.setProjectRoleStaffDtos(studentProjectRoleStaffMap.get(summaryDto.getServiceFeeId()));
            if (summaryDto.getBusinessStatus().equals(0)) {
                summaryDto.setBusinessStatusName("未完成");
            } else if (summaryDto.getBusinessStatus().equals(1)) {
                summaryDto.setBusinessStatusName("已完成");
            }
            // 结算状态审批
            if (summaryDto.getApproveStatus().equals(0)) {
                summaryDto.setApproveStatusName("未审批");
            } else if (summaryDto.getApproveStatus().equals(1)) {
                summaryDto.setApproveStatusName("已审批");
            }

            summaryDto.setFkTypeKeyReceivableName(TableEnum.getValueByKey(summaryDto.getFkTypeKeyReceivable(), TableEnum.TYPE_KEY_RECEIVABLE));
//            if (TableEnum.SALE_STUDENT.key.equals(summaryDto.getFkTypeKeyReceivable())) {
//                summaryDto.setReceivableName(studentNameMap.get(summaryDto.getFkTypeTargetIdReceivable()));
//                // 收款单信息
//                summaryDto.setReceiptFormDtoList(receiptFormMapToStudent.get(summaryDto.getFkTypeTargetIdReceivable()));
//            } else if (TableEnum.INSTITUTION_PROVIDER.key.equals(summaryDto.getFkTypeKeyReceivable())) {
//                summaryDto.setReceivableName(providerNameMap.get(summaryDto.getFkTypeTargetIdReceivable()));
//                // 收款单信息
//                summaryDto.setReceiptFormDtoList(receiptFormMapToProvider.get(summaryDto.getFkTypeTargetIdReceivable()));
//            }
            summaryDto.setReceiptFormDtoList(receiptFormMap.get(summaryDto.getServiceFeeId()));
            //代理标签
            if (GeneralTool.isNotEmpty(summaryDto.getFkAgentId())){
                getAgentLabelDataUtils.setAgentLabelVosByLabelMap(summaryDto, agentLabelMap, summaryDto.getFkAgentId(), StudentServiceFeeSummaryVo::setAgentLabelVos);
            }

            // 业务发生时间
            if (GeneralTool.isNotEmpty(summaryDto.getBusinessStartTime()) && GeneralTool.isNotEmpty(summaryDto.getBusinessEndTime())) {
                summaryDto.setBusinessTimeStr(DateUtil.formatDate(summaryDto.getBusinessStartTime()) + " 至 " + DateUtil.formatDate(summaryDto.getBusinessEndTime()));
            }
            if (GeneralTool.isNotEmpty(summaryDto.getServiceFeeAmount())) {
                summaryDto.setServiceFeeAmount(summaryDto.getServiceFeeAmount());
            }
            if (GeneralTool.isNotEmpty(summaryDto.getReceivableAmount())) {
                summaryDto.setReceivableAmount(summaryDto.getReceivableAmount());
            }
            if (GeneralTool.isNotEmpty(summaryDto.getPayableAmount())) {
                summaryDto.setPayableAmount(summaryDto.getPayableAmount());
            }
            List<StudentServiceFeeCost> serviceFeeCosts = studentServiceFeeCostsMap.get(summaryDto.getServiceFeeId());
            if (GeneralTool.isNotEmpty(serviceFeeCosts)) {
                List<StudentServiceFeeCostVo> serviceFeeCostDtos = Lists.newArrayList();
                StringJoiner serviceFeeCostsStrJoiner = new StringJoiner("<br>");
                for (StudentServiceFeeCost serviceFeeCost : serviceFeeCosts) {
                    StudentServiceFeeCostVo serviceFeeCostDto = BeanCopyUtils.objClone(serviceFeeCost, StudentServiceFeeCostVo::new);
                    Long fkBusinessProviderId = serviceFeeCostDto.getFkBusinessProviderId();
                    // 业务提供商名称
                    String businessProviderName = businessProviderMap.get(fkBusinessProviderId);
                    String fkCurrencyTypeNum = serviceFeeCostDto.getFkCurrencyTypeNum();
                    // 币种名称
                    String currencyTypeNumName = numsMap.get(fkCurrencyTypeNum);
                    serviceFeeCostDto.setFkCurrencyNumName(currencyTypeNumName);
                    StringBuilder feeCostDescriptionBuilder = new StringBuilder();
                    // 成本税前
                    BigDecimal amount = GeneralTool.isNotEmpty(serviceFeeCostDto.getAmount()) ? serviceFeeCostDto.getAmount() : BigDecimal.ZERO;
                    // 成本税金
                    BigDecimal taxes = GeneralTool.isNotEmpty(serviceFeeCostDto.getTaxes()) ? serviceFeeCostDto.getTaxes() : BigDecimal.ZERO;
                    if (StringUtils.isNotBlank(businessProviderName)) {
                        feeCostDescriptionBuilder.append(businessProviderName).append(":");
                    }
                    if (flag) { // 显示税金
                        feeCostDescriptionBuilder.append("【税前】").append(amount);
                        if (taxes.compareTo(BigDecimal.ZERO) > 0) {
                            feeCostDescriptionBuilder.append("+【税金】").append(taxes).append("=").append(amount.add(taxes));
                        }
                    } else { // 不显示税金
                        feeCostDescriptionBuilder.append(amount.add(taxes));
                    }
                    feeCostDescriptionBuilder.append(currencyTypeNumName);
                    serviceFeeCostDto.setFeeCostDescription(feeCostDescriptionBuilder.toString());
                    // 拼接成本支出描述
                    serviceFeeCostsStrJoiner.add(feeCostDescriptionBuilder.toString());

                    serviceFeeCostDtos.add(serviceFeeCostDto);
                }
                summaryDto.setServiceFeeCostDtos(serviceFeeCostDtos);
                summaryDto.setStudentServiceFeeCosts(serviceFeeCostsStrJoiner.toString());
            }
            //计算应付比例
            if (GeneralTool.isNotEmpty(summaryDto.getPayableAmount()) && GeneralTool.isNotEmpty(summaryDto.getReceivableAmount())) {
                BigDecimal payableAmount = new BigDecimal(summaryDto.getPayableAmount());
                BigDecimal receivableAmount = new BigDecimal(summaryDto.getReceivableAmount());
                BigDecimal ratio = payableAmount.multiply(new BigDecimal("100"))
                        .divide(receivableAmount, 2, RoundingMode.HALF_UP);
                String ratioStr = ratio.toString();
                summaryDto.setPayableRatio(ratioStr);
            }
        }
        return new ListResponseBo<>(serviceFeeSummary, BeanCopyUtils.objClone(page, Page::new), String.valueOf(ot2), String.valueOf(ft2));
    }

    /**
     * Author Cream
     * Description : //拼接国家信息
     * Date 2023/2/8 9:41
     * Params:
     * Return
     */
    private String getCountryName(String dtoCountryIds, Map<Long, String> data) {
        if (StringUtils.isNotBlank(dtoCountryIds)) {
            String[] split = dtoCountryIds.split(",");
            StringBuilder builder = new StringBuilder();
            for (String s : split) {
                builder.append(data.get(Long.valueOf(s))).append(",");
            }
            builder.delete(builder.length() - 1, builder.length());
            return builder.toString();
        }
        return null;
    }

    /**
     * Author Cream
     * Description : //创建应收应付
     * Date 2023/2/7 14:12
     * Params:
     * Return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public SaveResponseBo createARAP(ServiceFeeARAPDto serviceFeeARAPDto) {
        if (Objects.isNull(serviceFeeARAPDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        Set<Long> serviceFeeIds = serviceFeeARAPDto.getServiceFeeIds();
        List<StudentServiceFee> studentServiceFees = studentServiceFeeMapper.selectBatchIds(serviceFeeIds);
        List<ReceivablePlan> receivablePlans = new ArrayList<>();
        List<PayablePlan> payablePlans = new ArrayList<>();
        BigDecimal bigDecimal = new BigDecimal(100);
        boolean flag = serviceFeeARAPDto.getIsCreatePayable() != null && serviceFeeARAPDto.getIsCreatePayable();
        for (StudentServiceFee serviceFee : studentServiceFees) {
            Long serviceFeeId = serviceFee.getId();
            ReceivablePlan receivablePlan = new ReceivablePlan();
            receivablePlan.setFkTypeKey(TableEnum.SALE_STUDENT_SERVICE_FEE.key);
            receivablePlan.setFkTypeTargetId(serviceFeeId);
            receivablePlan.setFkCompanyId(serviceFeeARAPDto.getFkCompanyId());
            // 若发现有填入业务时间，将业务时间信息增加到应收计划摘要内如：业务发生时间：2024-08-30 至 2024-09-30
            if (GeneralTool.isNotEmpty(serviceFee.getBusinessStartTime()) && GeneralTool.isNotEmpty(serviceFee.getBusinessEndTime())) {
                String summary = "业务发生时间：" +
                        DateUtil.formatDate(serviceFee.getBusinessStartTime()) +
                        " 至 " +
                        DateUtil.formatDate(serviceFee.getBusinessEndTime());
                receivablePlan.setSummary(summary);
            }
            receivablePlan.setFkCurrencyTypeNum(serviceFeeARAPDto.getFkReceivableCurrencyNum());
//            receivablePlan.setTuitionAmount(serviceFee.getAmount());
//            receivablePlan.setCommissionRate();
//            receivablePlan.setCommissionAmount(serviceFeeARAPDto.getReceivableAmount());
            receivablePlan.setNetRate(bigDecimal);
            receivablePlan.setFixedAmount(serviceFeeARAPDto.getReceivableAmount());
            receivablePlan.setReceivableAmount(serviceFeeARAPDto.getReceivableAmount());
            receivablePlan.setStatus(1);
            utilService.setCreateInfo(receivablePlan);
            if (flag) {
                receivablePlanMapper.insert(receivablePlan);
            } else {
                receivablePlans.add(receivablePlan);
            }
            if (flag) {
                PayablePlan payablePlan = new PayablePlan();
                payablePlan.setFkTypeKey(TableEnum.SALE_STUDENT_SERVICE_FEE.key);
                payablePlan.setFkTypeTargetId(serviceFeeId);
                payablePlan.setFkCompanyId(serviceFeeARAPDto.getFkCompanyId());
                payablePlan.setFkCurrencyTypeNum(serviceFeeARAPDto.getFkPayableCurrencyNum());
                payablePlan.setFkReceivablePlanId(receivablePlan.getId());
//                payablePlan.setTuitionAmount(serviceFee.getAmount());
//                payablePlan.setCommissionRate();
//                payablePlan.setCommissionAmount(serviceFeeARAPDto.getPayableAmount());
                payablePlan.setFixedAmount(serviceFeeARAPDto.getPayableAmount());
                payablePlan.setSplitRate(bigDecimal);
                payablePlan.setPayableAmount(serviceFeeARAPDto.getPayableAmount());
                payablePlan.setStatus(1);
                payablePlan.setStatusSettlement(0);
                payablePlan.setIsPayInAdvance(false);
                utilService.setCreateInfo(payablePlan);
                payablePlans.add(payablePlan);
            }
        }
        if (!flag) {
            receivablePlanService.saveBatch(receivablePlans);
        }
        payablePlanService.saveBatch(payablePlans);
        return SaveResponseBo.ok(0L);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SaveResponseBo createAP(ServiceFeeAPDto serviceFeeAPDto) {
        if (Objects.isNull(serviceFeeAPDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        Set<Long> serviceFeeIds = serviceFeeAPDto.getServiceFeeIds();
        // 获取应付比率
        BigDecimal payableRatio = GeneralTool.isNotEmpty(serviceFeeAPDto.getPayableRatio()) ?
                serviceFeeAPDto.getPayableRatio().divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP) : BigDecimal.ONE;
        List<StudentServiceFee> studentServiceFees = studentServiceFeeMapper.selectBatchIds(serviceFeeIds);

        // 获取应收计划
        List<ReceivablePlan> receivablePlans = receivablePlanMapper.selectList(Wrappers.<ReceivablePlan>lambdaQuery()
                .eq(ReceivablePlan::getFkTypeKey, TableEnum.SALE_STUDENT_SERVICE_FEE.key)
                .in(ReceivablePlan::getFkTypeTargetId, serviceFeeIds)
                .eq(ReceivablePlan::getStatus, 1));
        if (GeneralTool.isEmpty(receivablePlans)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("receivable_plan_is_null"));
        }
        // key:学生留学服务费id value:对应的留学服务费应收计划
        Map<Long, List<ReceivablePlan>> receivablePlanMap = receivablePlans.stream().collect(Collectors.groupingBy(ReceivablePlan::getFkTypeTargetId));

        // 获取应付计划
        List<PayablePlan> payablePlanList = payablePlanMapper.selectList(Wrappers.<PayablePlan>lambdaQuery()
                .eq(PayablePlan::getFkTypeKey, TableEnum.SALE_STUDENT_SERVICE_FEE.key)
                .in(PayablePlan::getFkTypeTargetId, serviceFeeIds)
                .eq(PayablePlan::getStatus, 1));
        // key:学生留学服务费id value:对应的留学服务费应付计划
        Map<Long, List<PayablePlan>> payablePlanMap = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(payablePlanList)) {
            payablePlanMap = payablePlanList.stream().collect(Collectors.groupingBy(PayablePlan::getFkTypeTargetId));
        }

        // 获取留学服务费的成本金额 m_student_service_fee_cost
        List<StudentServiceFeeCost> studentServiceFeeCosts = studentServiceFeeCostMapper.selectList(Wrappers.<StudentServiceFeeCost>lambdaQuery()
                .eq(StudentServiceFeeCost::getStatus, 1)
                .in(StudentServiceFeeCost::getFkStudentServiceFeeId, serviceFeeIds)
        );
        // key:学生留学服务费id value:对应的留学服务费成本集合
        Map<Long, List<StudentServiceFeeCost>> feeCostMap = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(studentServiceFeeCosts)) {
            feeCostMap = studentServiceFeeCosts.stream().collect(Collectors.groupingBy(StudentServiceFeeCost::getFkStudentServiceFeeId));
        }

        // 新增的应付计划
        List<PayablePlan> addPayablePlans = Lists.newArrayList();
        // 更新的应付计划
        List<PayablePlan> updatePayablePlans = Lists.newArrayList();

        for (StudentServiceFee serviceFee : studentServiceFees) {
            // 服务费id
            Long serviceFeeId = serviceFee.getId();
            // 服务费币种
            String targetCurrency = serviceFee.getFkCurrencyTypeNum();

            // 成本金额
            BigDecimal feeCostAmount = BigDecimal.ZERO;
            List<StudentServiceFeeCost> feeCostList = feeCostMap.get(serviceFeeId);
            // 获取币种汇率
            if (GeneralTool.isNotEmpty(feeCostList)) {
                // 成本金额（m_student_service_fee_cost）涉及的币种
                Set<String> feeCostCurrency = feeCostList.stream().map(StudentServiceFeeCost::getFkCurrencyTypeNum).collect(Collectors.toSet());
                Result<Map<String, BigDecimal>> result = financeCenterClient.getBatchLastExchangeRate(feeCostCurrency, targetCurrency);
                if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                    Map<String, BigDecimal> rate = result.getData();
                    feeCostAmount = feeCostList.stream().map(feeCost -> {
                        String currencyTypeNum = feeCost.getFkCurrencyTypeNum();
                        BigDecimal amount = feeCost.getAmount();
                        return Optional.ofNullable(amount).orElse(BigDecimal.ZERO)
                                .multiply(Optional.ofNullable(rate.get(currencyTypeNum)).orElse(BigDecimal.ONE));
                    }).reduce(BigDecimal.ZERO, BigDecimal::add);
                }
            }
            // 服务费金额
            BigDecimal amount = GeneralTool.isNotEmpty(serviceFee.getAmount()) ? serviceFee.getAmount() : BigDecimal.ZERO;
            // [服务费金额税前-SUM(成本税前)] x 10%
            BigDecimal payableAmount = amount.subtract(feeCostAmount).multiply(payableRatio);

            // 当前服务费对应的应付计划
            List<PayablePlan> curPayablePlanList = payablePlanMap.get(serviceFeeId);
            if (GeneralTool.isNotEmpty(curPayablePlanList)) { // 当已经存在应付计划，在原来应付计划进行修改金额操作
                for (PayablePlan payablePlan : curPayablePlanList) { // 业务上一般只有一个应收计划，还是全部更新
                    payablePlan.setTuitionAmount(amount);
                    payablePlan.setSplitRate(new BigDecimal("100"));
                    payablePlan.setFixedAmount(null);
                    payablePlan.setCommissionRate(serviceFeeAPDto.getPayableRatio());
                    payablePlan.setCommissionAmount(payableAmount);
                    payablePlan.setPayableAmount(payableAmount);
                    utilService.setUpdateInfo(payablePlan);
                    updatePayablePlans.add(payablePlan);
                }
            } else { // 没有才新建应付计划
                // 获取当前服务费的应收计划
                List<ReceivablePlan> curReceivablePlanList = receivablePlanMap.get(serviceFeeId);
                if (GeneralTool.isEmpty(curReceivablePlanList)) {
                    continue;
                }
                // 根据应收计划创建应付计划
                for (ReceivablePlan receivablePlan : curReceivablePlanList) {
                    PayablePlan payablePlan = new PayablePlan();
                    payablePlan.setTuitionAmount(amount);
                    payablePlan.setFkCompanyId(receivablePlan.getFkCompanyId());
                    payablePlan.setFkTypeKey(TableEnum.SALE_STUDENT_SERVICE_FEE.key);
                    payablePlan.setFkTypeTargetId(receivablePlan.getFkTypeTargetId());
                    payablePlan.setFkCurrencyTypeNum(targetCurrency);
                    payablePlan.setFkReceivablePlanId(receivablePlan.getId());
                    payablePlan.setSplitRate(new BigDecimal("100"));
                    payablePlan.setCommissionRate(serviceFeeAPDto.getPayableRatio());
                    payablePlan.setCommissionAmount(payableAmount);
                    payablePlan.setPayableAmount(payableAmount);
                    payablePlan.setIsPayInAdvance(false);
                    payablePlan.setStatus(1);
                    payablePlan.setStatusSettlement(ProjectExtraEnum.UNSETTLED.key);
                    utilService.setCreateInfo(payablePlan);
                    addPayablePlans.add(payablePlan);
                }
            }
        }

        if (GeneralTool.isNotEmpty(addPayablePlans)) {
            payablePlanService.saveBatch(addPayablePlans);
        }
        if (GeneralTool.isNotEmpty(updatePayablePlans)) {
            for (PayablePlan updatePayablePlan : updatePayablePlans) {
                payablePlanMapper.updateByIdWithNull(updatePayablePlan);
            }
        }
        return SaveResponseBo.ok(0L);
    }


    /**
     * Author Cream
     * Description : // 收款单获取学生（客户）类型的应收计划
     * Date 2023/2/1 14:55
     * Params:
     * Return
     */
    @Override
    public List<BaseSelectEntity> getStudentServiceFeeReceivablePlan(Long targetId, Long receiptFormId, Integer pageNumber, Integer pageSize) {
        return studentServiceFeeMapper.getStudentServiceFeeReceivablePlan(targetId, receiptFormId, pageNumber, pageSize);
    }

    /**
     * Author Cream
     * Description : //获取留学服务费的学生id
     * Date 2023/2/13 12:02
     * Params:
     * Return
     */
    @Override
    public List<Long> getServiceFeeStudentIdsByIds(List<Long> ids) {
        if (ids.isEmpty()) {
            return Collections.emptyList();
        }
        return studentServiceFeeMapper.getServiceFeeStudentIdsByIds(ids);
    }

    @Override
    public Long getServiceFeeStudentIdsById(Long targetId) {
        if (Objects.isNull(targetId)) {
            return null;
        }
        return studentServiceFeeMapper.getServiceFeeStudentIdsById(targetId);
    }

    @Override
    public void mergeData(Long mergedStudentId, Long targetStudentId) {
        List<StudentServiceFee> studentServiceFees = studentServiceFeeMapper.selectList(Wrappers.<StudentServiceFee>lambdaQuery().eq(StudentServiceFee::getFkStudentId, mergedStudentId));
        if (GeneralTool.isNotEmpty(studentServiceFees)) {
            studentServiceFees.forEach(s -> s.setFkStudentId(targetStudentId));
            updateBatchById(studentServiceFees);
        }
    }

    @Override
    public void exportStudentServiceFeeSummary(HttpServletResponse response, StudentServiceFeeSummaryDto studentServiceFeeSummaryVo) {
        exportExcel(response, 1, BeanCopyUtils.objClone(studentServiceFeeSummaryVo, StudentFeeCommissionStatementExportDto::new));
    }

    @Override
    public void exportFeeCommissionStatement(HttpServletResponse response, StudentFeeCommissionStatementExportDto studentFeeCommissionStatementExportVo) {
        exportExcel(response, 2, studentFeeCommissionStatementExportVo);
    }

    /**
     * 导出excel
     *
     * @param response
     * @param type                                  1：导出学生服务费汇总 2：导出佣金提成结算表
     * @param studentFeeCommissionStatementExportVo 参数
     */
    private void exportExcel(HttpServletResponse response, Integer type, StudentFeeCommissionStatementExportDto studentFeeCommissionStatementExportVo) {
        StudentServiceFeeSummaryDto studentServiceFeeSummaryVo = BeanCopyUtils.objClone(studentFeeCommissionStatementExportVo, StudentServiceFeeSummaryDto::new);
        ListResponseBo<StudentServiceFeeSummaryVo> responseBo = (ListResponseBo<StudentServiceFeeSummaryVo>) serviceFeeSummary(studentServiceFeeSummaryVo, null);
        List<StudentServiceFeeSummaryVo> studentServiceFeeSummaryDtos = responseBo.getDatas();

        // 根据登录人的公司，若系统参数有设置值，就显示税金的方式，否则隐藏
        List<Long> showTaxesCompanyIds = Lists.newArrayList();
        Map<Long, String> configMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.STUDENT_SERVICE_FEE_TAXES.key, 1).getData();
        for (Map.Entry<Long, String> entry : configMap.entrySet()) {
            String value = entry.getValue();
            JSONObject taxesConfig = JSONObject.parseObject(value);
            if (!taxesConfig.isEmpty()) {
                showTaxesCompanyIds.add(entry.getKey());
            }
        }
        // 是否显示税金相关内容
        boolean flag = showTaxesCompanyIds.contains(SecureUtil.getFkCompanyId());

        // 结算币种，以该币种为准，用于后续的汇率计算
        String settlementCurrencyTypeNum = studentFeeCommissionStatementExportVo.getSettlementCurrencyTypeNum();
        BigDecimal royaltyRateNum = GeneralTool.isNotEmpty(studentFeeCommissionStatementExportVo.getRoyaltyRate()) ?
                studentFeeCommissionStatementExportVo.getRoyaltyRate() : BigDecimal.ZERO;
        BigDecimal royaltyRate = royaltyRateNum.divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);

        // 获取币种名称
        Map<String, String> currencyName = Maps.newHashMap();
        Result<Map<String, String>> currencyNameResult = financeCenterClient.getCurrencyNamesByNums(Collections.singleton(settlementCurrencyTypeNum));
        if (currencyNameResult.isSuccess() && GeneralTool.isNotEmpty(currencyNameResult.getData())) {
            currencyName = currencyNameResult.getData();
        }
        String settlementCurrencyTypeNumName = currencyName.getOrDefault(settlementCurrencyTypeNum, "");

        // 导出列表
        List<StudentServiceFeeExportVo> studentServiceFeeExportDtos = Lists.newArrayList();
        if (GeneralTool.isNotEmpty(studentServiceFeeSummaryDtos)) {
            // 获取币种汇率
            Map<String, BigDecimal> rate = Maps.newHashMap();
            // 服务费成本之和
            Map<Long, BigDecimal> feeCostAmountMap = Maps.newHashMap();
            if (type == 2) { // 导出佣金提成结算表才需要做的准备工作
                // 来源币种集合
                Set<String> sourceCurrencySet = Sets.newHashSet();
                // 留学服务费涉及的币种
                Set<String> serviceFeeCurrency = studentServiceFeeSummaryDtos.stream().map(StudentServiceFeeSummaryVo::getServiceFeeCurrencyNum).collect(Collectors.toSet());
                sourceCurrencySet.addAll(serviceFeeCurrency);
                // 服务费ids
                Set<Long> serviceFeeIds = studentServiceFeeSummaryDtos.stream().map(StudentServiceFeeSummaryVo::getServiceFeeId).collect(Collectors.toSet());
                // 获取留学服务费的成本金额 m_student_service_fee_cost amount 累加
                List<StudentServiceFeeCost> studentServiceFeeCosts = studentServiceFeeCostMapper.selectList(Wrappers.lambdaQuery(StudentServiceFeeCost.class)
                        .eq(StudentServiceFeeCost::getStatus, 1)
                        .in(StudentServiceFeeCost::getFkStudentServiceFeeId, serviceFeeIds)
                );
                if (GeneralTool.isNotEmpty(studentServiceFeeCosts)) {
                    // 成本金额（m_student_service_fee_cost）涉及的币种
                    Set<String> feeCostCurrency = studentServiceFeeCosts.stream().map(StudentServiceFeeCost::getFkCurrencyTypeNum).collect(Collectors.toSet());
                    sourceCurrencySet.addAll(feeCostCurrency);
                }
                if (GeneralTool.isNotEmpty(sourceCurrencySet)) {
                    Result<Map<String, BigDecimal>> result = financeCenterClient.getBatchLastExchangeRate(sourceCurrencySet, settlementCurrencyTypeNum);
                    if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                        rate = result.getData();
                    }
                }
                // 转币种汇率后再进行金额的累加
                if (GeneralTool.isNotEmpty(studentServiceFeeCosts)) {
                    Map<String, BigDecimal> finalRate = rate;
                       feeCostAmountMap = studentServiceFeeCosts.stream().collect(Collectors.groupingBy(
                               StudentServiceFeeCost::getFkStudentServiceFeeId,
                               Collectors.reducing(
                                       BigDecimal.ZERO,
                                       feeCost -> {
                                           String currencyTypeNum = feeCost.getFkCurrencyTypeNum();
                                           BigDecimal amount = GeneralTool.isNotEmpty(feeCost.getAmount()) ? feeCost.getAmount() : BigDecimal.ZERO;
                                           return amount.multiply(finalRate.get(currencyTypeNum));
                                       },
                                       BigDecimal::add
                               )
                       ));
//                    for (StudentServiceFeeCost feeCost : studentServiceFeeCosts) {
//                        if (feeCost == null) {
//                            continue;
//                        }
//
//                        Long feeId = feeCost.getFkStudentServiceFeeId();
//                        String currencyTypeNum = feeCost.getFkCurrencyTypeNum();
//
//                        // 检查 currencyTypeNum 是否存在
//                        if (!finalRate.containsKey(currencyTypeNum)) {
//                            currencyTypeNum = settlementCurrencyTypeNum;
//                        }
//
//                        // 处理金额为空的情况（与原逻辑一致）
//                        BigDecimal amount = feeCost.getAmount() != null ? feeCost.getAmount() : BigDecimal.ZERO;
//                        BigDecimal convertedAmount = amount.multiply(finalRate.get(currencyTypeNum));
//
//                        // 使用 merge 方法简化累加逻辑
//                        feeCostAmountMap.merge(
//                                feeId,
//                                convertedAmount,
//                                BigDecimal::add
//                        );
//                    }
                }
            }

            for (int i = 0; i < studentServiceFeeSummaryDtos.size(); i++) {
                StudentServiceFeeSummaryVo studentServiceFeeSummaryDto = studentServiceFeeSummaryDtos.get(i);
                StudentServiceFeeExportVo studentServiceFeeExportDto = BeanCopyUtils.objClone(studentServiceFeeSummaryDto, StudentServiceFeeExportVo::new);
                if (GeneralTool.isNotEmpty(studentServiceFeeExportDto)) {
                    studentServiceFeeExportDto.setReceiptAmount(ConvertUtils.toBigDecimal(studentServiceFeeSummaryDto.getReceiptAmount()));
                    studentServiceFeeExportDto.setReceivableAmount(ConvertUtils.toBigDecimal(studentServiceFeeSummaryDto.getReceivableAmount()));
                    studentServiceFeeExportDto.setPaidAmount(ConvertUtils.toBigDecimal(studentServiceFeeSummaryDto.getPaidAmount()));
                    studentServiceFeeExportDto.setPayableAmount(ConvertUtils.toBigDecimal(studentServiceFeeSummaryDto.getPayableAmount()));
                    studentServiceFeeExportDto.setPayableDiffAmount(ConvertUtils.toBigDecimal(studentServiceFeeSummaryDto.getPayableDiffAmount()));
                    studentServiceFeeExportDto.setPayableRatio(ConvertUtils.toBigDecimal(studentServiceFeeSummaryDto.getPayableRatio()));
                }

                // 销售日期
                if (GeneralTool.isNotEmpty(studentServiceFeeSummaryDto.getSalesTime())) {
                    studentServiceFeeExportDto.setSalesTimeStr(DateUtil.format(studentServiceFeeSummaryDto.getSalesTime(), "yyyy-MM-dd"));
                }
                // 收款日期
                if (GeneralTool.isNotEmpty(studentServiceFeeSummaryDto.getReceiptFormDtoList())) {
                    List<ReceiptFormVo> receiptFormDtoList = studentServiceFeeSummaryDto.getReceiptFormDtoList();
                    StringJoiner receiptDate = new StringJoiner("\n");
                    for (ReceiptFormVo receiptFormDto : receiptFormDtoList) {
                        if (GeneralTool.isNotEmpty(receiptFormDto.getReceiptDate())) {
                            receiptDate.add(DateUtil.format(receiptFormDto.getReceiptDate(), "yyyy-MM-dd"));
                        }
                    }
                    studentServiceFeeExportDto.setReceiptDate(receiptDate.toString());
                }
                // 付款情况
                if (GeneralTool.isNotEmpty(studentServiceFeeSummaryDto.getPayDetailList())) {
                    StringJoiner payDetail = new StringJoiner("\n");
                    payDetail.add("币种        |实付金额        |付款时间       ");
                    List<ServiceFeePayFormDetailVo> payDetailList = studentServiceFeeSummaryDto.getPayDetailList();
                    for (ServiceFeePayFormDetailVo payFormDetailDto : payDetailList) {
                        StringBuilder sb = new StringBuilder();
                        if (GeneralTool.isNotEmpty(payFormDetailDto.getPayableCurrencyTypeName())) {
                            sb.append(payFormDetailDto.getPayableCurrencyTypeName());
                        }
                        sb.append("|");
                        if (GeneralTool.isNotEmpty(payFormDetailDto.getActualPayableAmount())) {
                            sb.append(payFormDetailDto.getActualPayableAmount());
                        }
                        sb.append("|");
                        if (GeneralTool.isNotEmpty(payFormDetailDto.getActualPayTime())) {
                            sb.append(DateUtil.format(payFormDetailDto.getActualPayTime(), "yyyy-MM-dd HH:mm:ss"));
                        }
                        payDetail.add(sb.toString());
                    }
                    studentServiceFeeExportDto.setPayDetail(payDetail.toString());
                }
                // 收款方
                if (GeneralTool.isNotEmpty(studentServiceFeeSummaryDto.getFkTypeKeyReceivableName()) && GeneralTool.isNotEmpty(studentServiceFeeSummaryDto.getReceivableName())) {
                    studentServiceFeeExportDto.setPayeeName(studentServiceFeeSummaryDto.getFkTypeKeyReceivableName() + "：" + studentServiceFeeSummaryDto.getReceivableName());
                }
                // 成本支出
                if (GeneralTool.isNotEmpty(studentServiceFeeSummaryDto.getServiceFeeCostDtos())) {
                    // 成本支出列表
                    List<StudentServiceFeeCostVo> serviceFeeCostDtos = studentServiceFeeSummaryDto.getServiceFeeCostDtos();
                    StudentServiceFeeCostVo first = serviceFeeCostDtos.get(0);
                    serviceFeeCostDtos.remove(0);
                    studentServiceFeeExportDto.setStudentServiceFeeCosts(first.getFeeCostDescription());
                    studentServiceFeeExportDto.setFeeCostCurrencyTypeNumName(first.getFkCurrencyNumName());
                    BigDecimal feeCostPretaxAmount = GeneralTool.isNotEmpty(first.getAmount()) ? first.getAmount() : BigDecimal.ZERO;
                    BigDecimal feeCostTaxes = GeneralTool.isNotEmpty(first.getTaxes()) ? first.getTaxes() : BigDecimal.ZERO;
                    BigDecimal feeCostAmount = feeCostPretaxAmount.add(feeCostTaxes);
                    studentServiceFeeExportDto.setFeeCostAmount(feeCostAmount);
                    studentServiceFeeExportDto.setFeeCostPretaxAmount(feeCostPretaxAmount);
                    studentServiceFeeExportDto.setFeeCostTaxes(feeCostTaxes);
                    if (type == 2) {
                        // 当日汇率（成本）
                        BigDecimal curRateServiceFeeCost = rate.get(first.getFkCurrencyTypeNum());
                        if (GeneralTool.isNotEmpty(curRateServiceFeeCost)) {
                            studentServiceFeeExportDto.setCurRateServiceFeeCost(curRateServiceFeeCost);
                        }
                    }
                    int curIndex = i;
                    for (StudentServiceFeeCostVo serviceFeeCostDto : serviceFeeCostDtos) {
                        curIndex++;
                        // 若2个成本以上，需要显示多出的成本行，专门显示多出的成本
                        StudentServiceFeeSummaryVo cost = new StudentServiceFeeSummaryVo();
                        cost.setServiceFeeCostDtos(Lists.newArrayList(serviceFeeCostDto));
                        // 在当前记录后插入多出的成本记录
                        studentServiceFeeSummaryDtos.add(curIndex, cost);
                    }
                }
                // 绑定的项目成员
                List<StudentProjectRoleStaffVo> projectRoleStaffDtos = studentServiceFeeSummaryDto.getProjectRoleStaffDtos();
                if (GeneralTool.isNotEmpty(projectRoleStaffDtos)) {
                    StringJoiner projectRoleStaffInfo = new StringJoiner("/");
                    for (StudentProjectRoleStaffVo projectRoleStaffDto : projectRoleStaffDtos) {
                        projectRoleStaffInfo.add(projectRoleStaffDto.getRoleName() + "：" + projectRoleStaffDto.getStaffName());
                    }
                    studentServiceFeeExportDto.setProjectRoleStaffInfo(projectRoleStaffInfo.toString());
                }
                if (type == 2) {
                    // 当日汇率（服务费）
                    BigDecimal curRateServiceFee = rate.getOrDefault(studentServiceFeeSummaryDto.getServiceFeeCurrencyNum(), BigDecimal.ONE);
                    // 服务费税前
                    BigDecimal feePretaxAmount = GeneralTool.isNotEmpty(studentServiceFeeSummaryDto.getPretaxAmount()) ?
                            studentServiceFeeSummaryDto.getPretaxAmount().multiply(curRateServiceFee) : BigDecimal.ZERO;
                    // 员工+AN1奖金计数 = 服务费税前 - sum(成本税前)
                    BigDecimal an1 = feePretaxAmount.subtract(feeCostAmountMap.getOrDefault(studentServiceFeeSummaryDto.getServiceFeeId(), BigDecimal.ZERO))
                            .setScale(2, RoundingMode.HALF_UP);
                    studentServiceFeeExportDto.setAn1(an1);
                    // 比率
                    studentServiceFeeExportDto.setRoyaltyRate(royaltyRate);
                    // 奖金金额 = (员工+AN1奖金计数) * 比率
                    BigDecimal royaltyAmount = an1.multiply(royaltyRate).setScale(2, RoundingMode.HALF_UP);
                    studentServiceFeeExportDto.setRoyaltyAmount(royaltyAmount);
                    // 币种
                    studentServiceFeeExportDto.setSettlementCurrencyTypeNumName(settlementCurrencyTypeNumName);
                    // 汇率
                    studentServiceFeeExportDto.setCurRateServiceFee(curRateServiceFee);
                }

                //代理标签
                if (GeneralTool.isNotEmpty(studentServiceFeeSummaryDto.getAgentLabelVos())) {
                    studentServiceFeeExportDto.setAgentLabelNames(
                            studentServiceFeeSummaryDto.getAgentLabelVos().stream()
                                    // 给每个标签名称包裹【】符号
                                    .map(vo -> "【" + vo.getLabelName() + "】")
                                    // 用逗号拼接所有带符号的标签
                                    .collect(Collectors.joining(","))
                    );
                }

                studentServiceFeeExportDtos.add(studentServiceFeeExportDto);
            }
        }

        Set<String> ignoreFields = Sets.newHashSet();
        if (!flag) {
            ignoreFields.add("pretaxAmount");
            ignoreFields.add("serviceFeeTaxes");
            ignoreFields.add("feeCostPretaxAmount");
            ignoreFields.add("feeCostTaxes");
        }
        // 导出佣金提成结算表
        if (type != 2) {
            ignoreFields.add("an1");
            ignoreFields.add("royaltyRate");
            ignoreFields.add("royaltyAmount");
            ignoreFields.add("settlementCurrencyTypeNumName");
            ignoreFields.add("curRateServiceFee");
            ignoreFields.add("curRateServiceFeeCost");
        }

        Map<String, String> fileMap;
        if (GeneralTool.isNotEmpty(ignoreFields)) {
            fileMap = FileUtils.getFileMapIgnoreSomeField(StudentServiceFeeExportVo.class, ignoreFields);
        } else {
            fileMap = FileUtils.getFileMap(StudentServiceFeeExportVo.class);
        }

        String fileName = "StudentServiceFee";
        // 小计、总计所有的数据
        List<StudentServiceFeeExportVo> sumFeeExportDto = Lists.newArrayList();
        if (type == 2) {
            fileName = "StudentServiceFeeCommissionStatement";
            // 获取下级提成比率
            BigDecimal subordinateRoyaltyRateNum = GeneralTool.isNotEmpty(studentFeeCommissionStatementExportVo.getSubordinateRoyaltyRate()) ?
                    studentFeeCommissionStatementExportVo.getSubordinateRoyaltyRate() : BigDecimal.ZERO;
            BigDecimal subordinateRoyaltyRate = subordinateRoyaltyRateNum.divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
            /**
             * 组装总计下面一块内容
             */
            StudentServiceFeeExportVo first = new StudentServiceFeeExportVo();
            BigDecimal an1Sum = studentServiceFeeExportDtos.stream()
                    .map(StudentServiceFeeExportVo::getAn1)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .setScale(2, RoundingMode.HALF_UP);
            first.setAn1(an1Sum);
            first.setRoyaltyRate(royaltyRate);
            BigDecimal royaltyAmountSum = an1Sum.multiply(royaltyRate).setScale(2, RoundingMode.HALF_UP);
            first.setRoyaltyAmount(royaltyAmountSum);
            first.setSettlementCurrencyTypeNumName(settlementCurrencyTypeNumName);
            sumFeeExportDto.add(first);
            sumFeeExportDto.add(new StudentServiceFeeExportVo());

            // 按绑定项目成员进行分组，计算各自的小计，一条服务费如有多个项目成员，则取平均值
            Map<Long, BigDecimal> projectRoleStaffAn1 = Maps.newConcurrentMap();
            Map<Long, BigDecimal> projectRoleStaffRoyaltyAmount = Maps.newConcurrentMap();
            // 项目成员姓名
            Map<Long, String> projectRoleStaffName = Maps.newConcurrentMap();
            for (StudentServiceFeeExportVo studentServiceFeeExportDto : studentServiceFeeExportDtos) {
                List<StudentProjectRoleStaffVo> projectRoleStaffDtos = studentServiceFeeExportDto.getProjectRoleStaffDtos();
                if (GeneralTool.isNotEmpty(projectRoleStaffDtos)) {
                    int projectRoleStaffSize = projectRoleStaffDtos.size();
                    BigDecimal an1 = GeneralTool.isNotEmpty(studentServiceFeeExportDto.getAn1()) ? studentServiceFeeExportDto.getAn1() : BigDecimal.ZERO;
                    BigDecimal avgAn1 = an1.divide(BigDecimal.valueOf(projectRoleStaffSize), 2, RoundingMode.HALF_UP);
                    BigDecimal royaltyAmount = GeneralTool.isNotEmpty(studentServiceFeeExportDto.getRoyaltyAmount()) ? studentServiceFeeExportDto.getRoyaltyAmount() : BigDecimal.ZERO;
                    BigDecimal avgRoyaltyAmount = royaltyAmount.divide(BigDecimal.valueOf(projectRoleStaffSize), 2, RoundingMode.HALF_UP);
                    for (StudentProjectRoleStaffVo projectRoleStaffDto : projectRoleStaffDtos) {
                        Long fkStaffId = projectRoleStaffDto.getFkStaffId();
                        projectRoleStaffAn1.compute(fkStaffId, (k, v) -> (v == null) ? avgAn1 : v.add(avgAn1));
                        projectRoleStaffRoyaltyAmount.compute(fkStaffId, (k, v) -> (v == null) ? avgRoyaltyAmount : v.add(avgRoyaltyAmount));
                        projectRoleStaffName.put(fkStaffId, projectRoleStaffDto.getStaffName());
                    }
                }
            }

            // key: 项目成员id，value：下属id集合
            Map<Long, Set<Long>> staffFollowerIdsMap = Maps.newHashMap();
            for (Map.Entry<Long, String> projectStaff : projectRoleStaffName.entrySet()) {
                Long staffId = projectStaff.getKey();
                Set<Long> staffFollowerIds = Sets.newHashSet();
                Result<List<Long>> followerIdsResult = permissionCenterClient.getStaffFollowerIds(staffId);
                if (followerIdsResult.isSuccess() && GeneralTool.isNotEmpty(followerIdsResult.getData())) {
                    List<Long> followerIds = followerIdsResult.getData();
                    staffFollowerIds.addAll(followerIds);
                }
                staffFollowerIds.removeIf(Objects::isNull);
                staffFollowerIdsMap.put(staffId, staffFollowerIds);
            }

            // 所有项目成员小计
            List<StudentServiceFeeExportVo> projectSubtotalExportDtos = Lists.newArrayList();
            Set<Long> projectRoleStaffIds = projectRoleStaffName.keySet();
            for (Map.Entry<Long, String> projectStaff : projectRoleStaffName.entrySet()) {
                // 每个项目成员的小计
                List<StudentServiceFeeExportVo> singleSumFeeExportDtos = Lists.newArrayList();
                StudentServiceFeeExportVo exportDto = new StudentServiceFeeExportVo();
                Long staffId = projectStaff.getKey();
                String staffName = projectStaff.getValue();
                exportDto.setGmtCreate(staffName);
                exportDto.setAn1(projectRoleStaffAn1.get(staffId).setScale(2, RoundingMode.HALF_UP));
                exportDto.setRoyaltyRate(royaltyRate);
                exportDto.setRoyaltyAmount(projectRoleStaffRoyaltyAmount.get(staffId).setScale(2, RoundingMode.HALF_UP));
                exportDto.setSettlementCurrencyTypeNumName(settlementCurrencyTypeNumName);
                singleSumFeeExportDtos.add(exportDto);
                // 下属id集合
                Set<Long> followerIds = staffFollowerIdsMap.get(staffId);
                for (Long projectRoleStaffId : projectRoleStaffIds) {
                    if (followerIds.contains(projectRoleStaffId)) {
                        // 下属小计
                        StudentServiceFeeExportVo followerExportDto = new StudentServiceFeeExportVo();
                        followerExportDto.setGmtCreate(staffName + "/" + projectRoleStaffName.get(projectRoleStaffId));
                        BigDecimal an1 = projectRoleStaffAn1.getOrDefault(projectRoleStaffId, BigDecimal.ZERO).setScale(2, RoundingMode.HALF_UP);
                        followerExportDto.setAn1(an1);
                        followerExportDto.setRoyaltyRate(subordinateRoyaltyRate);
                        // 奖金金额 = (员工+AN1奖金计数) * 比率
                        BigDecimal royaltyAmount = an1.multiply(subordinateRoyaltyRate).setScale(2, RoundingMode.HALF_UP);
                        followerExportDto.setRoyaltyAmount(royaltyAmount);
                        followerExportDto.setSettlementCurrencyTypeNumName(settlementCurrencyTypeNumName);
                        singleSumFeeExportDtos.add(followerExportDto);
                    }
                }
                // 小计
                StudentServiceFeeExportVo subtotalExportDto = new StudentServiceFeeExportVo();
                subtotalExportDto.setGmtCreate(staffName);
                subtotalExportDto.setRoyaltyRate("小计");
                BigDecimal subtotalRoyaltyAmount = singleSumFeeExportDtos.stream()
                        .map(dto -> Optional.ofNullable(dto.getRoyaltyAmount()).orElse(BigDecimal.ZERO))
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                        .setScale(2, RoundingMode.HALF_UP);
                subtotalExportDto.setRoyaltyAmount(subtotalRoyaltyAmount);
                subtotalExportDto.setSettlementCurrencyTypeNumName(settlementCurrencyTypeNumName);
                singleSumFeeExportDtos.add(subtotalExportDto);
                // 用于求总计
                projectSubtotalExportDtos.add(subtotalExportDto);
                // 空行
                singleSumFeeExportDtos.add(new StudentServiceFeeExportVo());
                // 导出列表集合
                sumFeeExportDto.addAll(singleSumFeeExportDtos);
            }
            // 最后一条总计
            StudentServiceFeeExportVo lastTotalExportDto = new StudentServiceFeeExportVo();
            lastTotalExportDto.setRoyaltyRate("总计");
            BigDecimal lastTotalRoyaltyAmount = projectSubtotalExportDtos.stream()
                    .map(dto -> Optional.ofNullable(dto.getRoyaltyAmount()).orElse(BigDecimal.ZERO))
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .setScale(2, RoundingMode.HALF_UP);
            lastTotalExportDto.setRoyaltyAmount(lastTotalRoyaltyAmount);
            lastTotalExportDto.setSettlementCurrencyTypeNumName(settlementCurrencyTypeNumName);
            sumFeeExportDto.add(lastTotalExportDto);
        }
        List<StudentServiceFeeExportVo> res = Lists.newArrayList();
        res.addAll(studentServiceFeeExportDtos);
        res.addAll(sumFeeExportDto);
        // 文件对象
        BigExcelWriter writer = FileUtils.setExcelStyleNotWrapText(fileName, fileMap.size());
        //设置字段
        for (Map.Entry<String, String> field : fileMap.entrySet()) {
            writer.addHeaderAlias(field.getValue(), field.getKey());
        }
        writer.setOnlyAlias(true);

        // 表头导出
        writer.writeHeadRow(fileMap.keySet());
        // 表格内容导出
        for (StudentServiceFeeExportVo studentServiceFeeExportDto : res) {
            int curRow = writer.getCurrentRow();
            ExcelWriter curRowWriter = writer.setCurrentRow(curRow);
            curRowWriter.writeRow(studentServiceFeeExportDto, false);
            Workbook workbook = curRowWriter.getWorkbook();
            DataFormat dataFormat = workbook.createDataFormat();
            // 总计、小计部分的字体
            Font font = curRowWriter.createFont();
            font.setBold(true);
            font.setFontName("宋体");
            int col = 0;
            for (Map.Entry<String, String> field : fileMap.entrySet()) {
                String value = field.getValue();
                // 表格内容导出
                if (studentServiceFeeExportDtos.contains(studentServiceFeeExportDto)) {
                    // 新单元格样式
                    CellStyle newCellStyle = workbook.createCellStyle();
                    // 获取原来的单元格样式
                    CellStyle cellStyle = curRowWriter.getOrCreateCellStyle(col, curRow);
                    // 不能直接操作获取的cellStyle对象，先克隆，再赋值其他的样式，否则会覆盖其他的样式
                    newCellStyle.cloneStyleFrom(cellStyle);
                    // 比率，设置为百分比格式
                    if ("royaltyRate".equals(value)) {
                        newCellStyle.setDataFormat(dataFormat.getFormat("0%"));
                        curRowWriter.setStyle(newCellStyle, col, curRow);
                    }
                    // 汇率，保留四位小数
                    if ("curRateServiceFee".equals(value) || "curRateServiceFeeCost".equals(value)) {
                        newCellStyle.setDataFormat(dataFormat.getFormat("0.0000"));
                        curRowWriter.setStyle(newCellStyle, col, curRow);
                    }
                }
                // 总计、小计部分导出
                if (sumFeeExportDto.contains(studentServiceFeeExportDto)) {
                    // 创建新单元格样式
                    CellStyle cellStyle = curRowWriter.createCellStyle(col, curRow);
                    cellStyle.setFont(font);
                    if ("royaltyRate".equals(value)) {
                        Object royaltyRate_ = studentServiceFeeExportDto.getRoyaltyRate();
                        if (royaltyRate_ instanceof BigDecimal) {
                            cellStyle.setDataFormat(dataFormat.getFormat("0%"));
                            curRowWriter.setStyle(cellStyle, col, curRow);
                        }
                    }
                }
                col++;
            }
        }
        //大文件导出
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
        ServletOutputStream out = null;
        try {
            out = response.getOutputStream();
            writer.flush(out, true);
        } catch (IOException e) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("file_export_fail"));
        } finally {
            IoUtil.close(writer);
            IoUtil.close(out);
        }
    }

    @Override
    public List<BaseSelectEntity> getDepartmentSelect(Long companyId) {
        if (!SecureUtil.validateCompany(companyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
        }
        return studentServiceFeeMapper.getDepartmentSelect(companyId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseBo batchCreateReceiptForm(ServiceFeeReceiptFormDto serviceFeeReceiptFormDto) {
        if (GeneralTool.isEmpty(serviceFeeReceiptFormDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        Set<Boolean> receivableFlags = new HashSet<>(serviceFeeReceiptFormDto.getReceivableFlagList());
        // 勾选学生不能同时混合未创建应收计划或已创建应收计划的学生
        if (receivableFlags.size() > 1) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        String receivableType = serviceFeeReceiptFormDto.getReceivableType();
        Set<Long> serviceFeeIds = serviceFeeReceiptFormDto.getServiceFeeIds();
        List<StudentServiceFee> studentServiceFees = studentServiceFeeMapper.selectBatchIds(serviceFeeIds);
        if (GeneralTool.isEmpty(studentServiceFees)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }

        Long fkCompanyId = serviceFeeReceiptFormDto.getFkCompanyId();

        // 用于远程调用所需参数集合
        List<ReceiptFormParamDto> receiptFormParamVoList = new ArrayList<>();

        if (receivableFlags.contains(false)) { // 未创建应收计划
            if (ProjectKeyEnum.SERVICE_FEE_FULL_RECEIPT.key.equals(receivableType)) { // 按服务费金额创建应收计划及收款单
                for (StudentServiceFee serviceFee : studentServiceFees) {
                    ReceivablePlan receivablePlan = new ReceivablePlan();
                    receivablePlan.setFkCompanyId(fkCompanyId);
                    receivablePlan.setFkTypeKey(TableEnum.SALE_STUDENT_SERVICE_FEE.key);
                    receivablePlan.setFkTypeTargetId(serviceFee.getId());
                    // 若发现有填入业务时间，将业务时间信息增加到应收计划摘要内如：业务发生时间：2024-08-30 至 2024-09-30
                    if (GeneralTool.isNotEmpty(serviceFee.getBusinessStartTime()) && GeneralTool.isNotEmpty(serviceFee.getBusinessEndTime())) {
                        String summary = "业务发生时间：" +
                                DateUtil.formatDate(serviceFee.getBusinessStartTime()) +
                                " 至 " +
                                DateUtil.formatDate(serviceFee.getBusinessEndTime());
                        receivablePlan.setSummary(summary);
                    }
                    receivablePlan.setFkCurrencyTypeNum(serviceFee.getFkCurrencyTypeNum());
                    // 应收金额：amount+taxes
                    BigDecimal receivableAmount = Optional.ofNullable(serviceFee.getAmount()).orElse(BigDecimal.ZERO)
                            .add(Optional.ofNullable(serviceFee.getTaxes()).orElse(BigDecimal.ZERO));
                    receivablePlan.setFixedAmount(receivableAmount);
                    receivablePlan.setReceivableAmount(receivableAmount);
                    receivablePlan.setStatus(1);
                    utilService.setCreateInfo(receivablePlan);
                    receivablePlanMapper.insert(receivablePlan);

                    ReceiptFormParamDto receiptFormParamDto = new ReceiptFormParamDto();
                    receiptFormParamDto.setFkReceivablePlanId(receivablePlan.getId());
                    receiptFormParamDto.setFkStudentId(serviceFee.getFkStudentId());
                    receiptFormParamDto.setFkReceivableCurrencyNum(serviceFee.getFkCurrencyTypeNum());
                    receiptFormParamDto.setReceivableAmount(receivableAmount);
                    receiptFormParamVoList.add(receiptFormParamDto);
                }
                if (GeneralTool.isNotEmpty(receiptFormParamVoList)) {
                    // 插入远程调用所需参数
                    serviceFeeReceiptFormDto.setReceiptFormParamVoList(receiptFormParamVoList);
                    // 插入收款单
                    Result result = financeCenterClient.saveBatchReceiptForms(serviceFeeReceiptFormDto);
                    if (!result.isSuccess()) {
                        throw new GetServiceException(result.getMessage());
                    }
                }
            } else if (ProjectKeyEnum.CUSTOM_AMOUNT_RECEIPT.key.equals(receivableType)) {
                for (StudentServiceFee serviceFee : studentServiceFees) {
                    ReceivablePlan receivablePlan = new ReceivablePlan();
                    receivablePlan.setFkCompanyId(fkCompanyId);
                    receivablePlan.setFkTypeKey(TableEnum.SALE_STUDENT_SERVICE_FEE.key);
                    receivablePlan.setFkTypeTargetId(serviceFee.getId());
                    // 若发现有填入业务时间，将业务时间信息增加到应收计划摘要内如：业务发生时间：2024-08-30 至 2024-09-30
                    if (GeneralTool.isNotEmpty(serviceFee.getBusinessStartTime()) && GeneralTool.isNotEmpty(serviceFee.getBusinessEndTime())) {
                        String summary = "业务发生时间：" +
                                DateUtil.formatDate(serviceFee.getBusinessStartTime()) +
                                " 至 " +
                                DateUtil.formatDate(serviceFee.getBusinessEndTime());
                        receivablePlan.setSummary(summary);
                    }
                    receivablePlan.setFkCurrencyTypeNum(serviceFeeReceiptFormDto.getFkReceivableCurrencyNum());
                    receivablePlan.setFixedAmount(serviceFeeReceiptFormDto.getReceivableAmount());
                    receivablePlan.setReceivableAmount(serviceFeeReceiptFormDto.getReceivableAmount());
                    receivablePlan.setStatus(1);
                    utilService.setCreateInfo(receivablePlan);
                    receivablePlanMapper.insert(receivablePlan);

                    ReceiptFormParamDto receiptFormParamDto = new ReceiptFormParamDto();
                    receiptFormParamDto.setFkReceivablePlanId(receivablePlan.getId());
                    receiptFormParamDto.setFkStudentId(serviceFee.getFkStudentId());
                    receiptFormParamDto.setFkReceivableCurrencyNum(serviceFeeReceiptFormDto.getFkReceivableCurrencyNum());
                    receiptFormParamDto.setReceivableAmount(serviceFeeReceiptFormDto.getReceivableAmount());
                    receiptFormParamVoList.add(receiptFormParamDto);
                }
                if (GeneralTool.isNotEmpty(receiptFormParamVoList)) {
                    // 插入远程调用所需参数
                    serviceFeeReceiptFormDto.setReceiptFormParamVoList(receiptFormParamVoList);
                    // 插入收款单
                    Result result = financeCenterClient.saveBatchReceiptForms(serviceFeeReceiptFormDto);
                    if (!result.isSuccess()) {
                        throw new GetServiceException(result.getMessage());
                    }
                }
            }
        } else { // 已创建应收计划
            if (ProjectKeyEnum.RECEIVABLE_PLAN_RECEIPT.key.equals(receivableType)) {
                // 获取已创建的应收计划
                List<ReceivablePlan> receivablePlans = receivablePlanMapper.selectList(Wrappers.<ReceivablePlan>lambdaQuery()
                        .eq(ReceivablePlan::getFkTypeKey, TableEnum.SALE_STUDENT_SERVICE_FEE.key)
                        .in(ReceivablePlan::getFkTypeTargetId, serviceFeeIds)
                        .eq(ReceivablePlan::getStatus, 1));
                if (GeneralTool.isNotEmpty(receivablePlans)) {
                    // key：留学服务费id value：学生id
                    Map<Long, Long> studentIdMap = studentServiceFees.stream().collect(Collectors.toMap(StudentServiceFee::getId, StudentServiceFee::getFkStudentId));
                    // 应收计划ids
                    Set<Long> planIds = receivablePlans.stream().map(ReceivablePlan::getId).collect(Collectors.toSet());
                    List<ReceiptFormItemVo> receiptFormItemList = financeCenterClient.getReceiptFormItemListFeignByPlanIds(planIds);
                    // key：应收计划id value：实收金额之和
                    Map<Long, BigDecimal> amountMap = new HashMap<>();
                    if (GeneralTool.isNotEmpty(receiptFormItemList)) {
                        amountMap = receiptFormItemList.stream().collect(Collectors.groupingBy(
                                ReceiptFormItemVo::getFkReceivablePlanId,
                                Collectors.reducing(BigDecimal.ZERO, ReceiptFormItemVo::getAmountReceivable, BigDecimal::add)));
                    }

                    for (ReceivablePlan receivablePlan : receivablePlans) {
                        ReceiptFormParamDto receiptFormParamDto = new ReceiptFormParamDto();
                        receiptFormParamDto.setFkReceivablePlanId(receivablePlan.getId());
                        receiptFormParamDto.setFkStudentId(studentIdMap.get(receivablePlan.getFkTypeTargetId()));
                        receiptFormParamDto.setFkReceivableCurrencyNum(receivablePlan.getFkCurrencyTypeNum());
                        // 已收金额
                        BigDecimal amountReceived = amountMap.getOrDefault(receivablePlan.getId(), BigDecimal.ZERO);
                        // 剩余金额
                        BigDecimal residualAmount = Optional.ofNullable(receivablePlan.getReceivableAmount()).orElse(BigDecimal.ZERO)
                                .subtract(amountReceived);
                        if (residualAmount.compareTo(BigDecimal.ZERO) <= 0) { // 已收完
                            continue;
                        }
                        receiptFormParamDto.setReceivableAmount(residualAmount);
                        receiptFormParamVoList.add(receiptFormParamDto);
                    }
                    if (GeneralTool.isNotEmpty(receiptFormParamVoList)) {
                        // 插入远程调用所需参数
                        serviceFeeReceiptFormDto.setReceiptFormParamVoList(receiptFormParamVoList);
                        // 插入收款单
                        Result result = financeCenterClient.saveBatchReceiptForms(serviceFeeReceiptFormDto);
                        if (!result.isSuccess()) {
                            throw new GetServiceException(result.getMessage());
                        }
                    }
                }
            }
        }
        return SaveResponseBo.ok();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String batchCreatePaymentForm(ServiceFeePaymentFormDto serviceFeePaymentFormDto) {
        if (GeneralTool.isEmpty(serviceFeePaymentFormDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        Set<Boolean> payableFlags = new HashSet<>(serviceFeePaymentFormDto.getPayableFlagList());
        // 勾选学生不能同时混合未创建应付计划或已创建应付计划的学生
        if (payableFlags.size() > 1) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        String payableType = serviceFeePaymentFormDto.getPayableType();
        Set<Long> serviceFeeIds = serviceFeePaymentFormDto.getServiceFeeIds();
        Long fkCompanyId = serviceFeePaymentFormDto.getFkCompanyId();
        BigDecimal payableAmountRate = GeneralTool.isNotEmpty(serviceFeePaymentFormDto.getPayableAmountRate()) ?
                serviceFeePaymentFormDto.getPayableAmountRate().divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP) : BigDecimal.ONE;

        List<StudentServiceFee> studentServiceFees = studentServiceFeeMapper.selectBatchIds(serviceFeeIds);

        if (GeneralTool.isEmpty(studentServiceFees)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        Map<Long, StudentServiceFee> studentServiceFeeMap = studentServiceFees.stream().collect(Collectors.toMap(StudentServiceFee::getId, Function.identity()));
        // 用于远程调用所需参数集合
        List<PaymentFormParamDto> paymentFormParamVoList = new ArrayList<>();

        // 通过代理id查找首选的银行账户，用于后续创建付款单
        Map<Long, Long> agentContractAccountMap = new HashMap<>();
        Set<Long> agentIds = studentServiceFees.stream().map(StudentServiceFee::getFkAgentId).collect(Collectors.toSet());
        List<AgentContractAccount> agentContractAccounts = agentContractAccountMapper.selectList(Wrappers.<AgentContractAccount>lambdaQuery()
                .eq(AgentContractAccount::getIsActive, 1)
                .in(AgentContractAccount::getFkAgentId, agentIds));
        if (GeneralTool.isNotEmpty(agentContractAccounts)) {
            for (AgentContractAccount account : agentContractAccounts) {
                if (agentContractAccountMap.containsKey(account.getFkAgentId())) {
                    if (account.getIsDefault()) { // 优先使用首选的银行账户id
                        agentContractAccountMap.put(account.getFkAgentId(), account.getId());
                    }
                } else {
                    agentContractAccountMap.put(account.getFkAgentId(), account.getId());
                }
            }
        }
        // 代理名字
        Map<Long, String> agentNameMap = agentService.getAgentNamesByIds(agentIds);

        // 自定义提示语
        StringBuilder remindMsg = new StringBuilder();
        List<String> remindAgentNameList = new ArrayList<>();

        if (payableFlags.contains(false)) { // 未创建应付计划
            // 以传入的币种为目标币种，获取币种汇率，用于后续金额的计算
            String targetCurrency = serviceFeePaymentFormDto.getFkPayableCurrencyNum();
            Map<String, BigDecimal> rate = Maps.newHashMap();
            // 来源币种集合
            Set<String> sourceCurrencySet = Sets.newHashSet();
            // 留学服务费涉及的币种
            Set<String> serviceFeeCurrency = studentServiceFees.stream().map(StudentServiceFee::getFkCurrencyTypeNum).collect(Collectors.toSet());
            sourceCurrencySet.addAll(serviceFeeCurrency);

            // 获取应收计划
            List<ReceivablePlan> receivablePlans = receivablePlanMapper.selectList(Wrappers.<ReceivablePlan>lambdaQuery()
                    .eq(ReceivablePlan::getFkTypeKey, TableEnum.SALE_STUDENT_SERVICE_FEE.key)
                    .in(ReceivablePlan::getFkTypeTargetId, serviceFeeIds)
                    .eq(ReceivablePlan::getStatus, 1));
            // 获取留学服务费的成本金额 m_student_service_fee_cost amount 累加
            List<StudentServiceFeeCost> studentServiceFeeCosts = studentServiceFeeCostMapper.selectList(Wrappers.lambdaQuery(StudentServiceFeeCost.class)
                    .eq(StudentServiceFeeCost::getStatus, 1)
                    .in(StudentServiceFeeCost::getFkStudentServiceFeeId, serviceFeeIds)
            );
            if (GeneralTool.isNotEmpty(studentServiceFeeCosts)) {
                // 成本金额（m_student_service_fee_cost）涉及的币种
                Set<String> feeCostCurrency = studentServiceFeeCosts.stream().map(StudentServiceFeeCost::getFkCurrencyTypeNum).collect(Collectors.toSet());
                sourceCurrencySet.addAll(feeCostCurrency);
            }
            // 获取币种汇率
            if (GeneralTool.isNotEmpty(sourceCurrencySet)) {
                Result<Map<String, BigDecimal>> result = financeCenterClient.getBatchLastExchangeRate(sourceCurrencySet, targetCurrency);
                if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                    rate = result.getData();
                }
            }
            // 转币种汇率后再进行金额的累加
            Map<Long, BigDecimal> feeCostAmountMap = new HashMap<>();
            if (GeneralTool.isNotEmpty(studentServiceFeeCosts)) {
                Map<String, BigDecimal> finalRate = rate;
                feeCostAmountMap = studentServiceFeeCosts.stream().collect(Collectors.groupingBy(
                        StudentServiceFeeCost::getFkStudentServiceFeeId,
                        Collectors.reducing(
                                BigDecimal.ZERO,
                                feeCost -> {
                                    String currencyTypeNum = feeCost.getFkCurrencyTypeNum();
                                    BigDecimal amount = feeCost.getAmount();
                                    return amount.multiply(finalRate.get(currencyTypeNum));
                                },
                                BigDecimal::add
                        )
                ));
            }
            if (ProjectKeyEnum.SERVICE_FEE_RATE_PAYMENT.key.equals(payableType)) { // 按（服务费金额-成本金额）比率创建应付计划及付款单
                for (ReceivablePlan receivablePlan : receivablePlans) {
                    // 应付计划对应的留学服务费对象
                    StudentServiceFee serviceFee = studentServiceFeeMap.get(receivablePlan.getFkTypeTargetId());
                    // 代理银行账户id
                    if (!agentContractAccountMap.containsKey(serviceFee.getFkAgentId())) {
                        // 没有银行账户就跳过
                        String agentName = agentNameMap.get(serviceFee.getFkAgentId());
                        remindAgentNameList.add(agentName);
                        continue;
                    }
                    PayablePlan payablePlan = new PayablePlan();
                    payablePlan.setFkCompanyId(fkCompanyId);
                    payablePlan.setFkTypeKey(TableEnum.SALE_STUDENT_SERVICE_FEE.key);
                    payablePlan.setFkTypeTargetId(receivablePlan.getFkTypeTargetId());
                    payablePlan.setFkReceivablePlanId(receivablePlan.getId());
                    payablePlan.setFkCurrencyTypeNum(serviceFeePaymentFormDto.getFkPayableCurrencyNum());
                    // 成本金额
                    BigDecimal feeCostAmount = feeCostAmountMap.getOrDefault(receivablePlan.getFkTypeTargetId(), BigDecimal.ZERO);
                    // 服务费金额
                    BigDecimal amount = serviceFee.getAmount().multiply(rate.get(serviceFee.getFkCurrencyTypeNum()));
                    // 服务费金额-成本金额
                    BigDecimal tuitionAmount = amount.subtract(feeCostAmount);
//                    payablePlan.setTuitionAmount(tuitionAmount);
//                    payablePlan.setCommissionRate(serviceFeePaymentFormVo.getPayableAmountRate());
                    payablePlan.setSplitRate(new BigDecimal("100"));
                    BigDecimal payableAmount = tuitionAmount.multiply(payableAmountRate);
                    payablePlan.setFixedAmount(payableAmount);
//                    payablePlan.setCommissionAmount(payableAmount);
                    payablePlan.setPayableAmount(payableAmount);
                    payablePlan.setIsPayInAdvance(false);
                    payablePlan.setStatus(1);
                    payablePlan.setStatusSettlement(ProjectExtraEnum.UNSETTLED.key);
                    utilService.setCreateInfo(payablePlan);
                    payablePlanMapper.insert(payablePlan);

                    PaymentFormParamDto paymentFormParamDto = new PaymentFormParamDto();
                    paymentFormParamDto.setFkPayablePlanId(payablePlan.getId());
                    paymentFormParamDto.setFkAgentId(serviceFee.getFkAgentId());
                    paymentFormParamDto.setFkPayableCurrencyNum(serviceFeePaymentFormDto.getFkPayableCurrencyNum());
                    paymentFormParamDto.setPayableAmount(payableAmount);
                    paymentFormParamDto.setFkBankAccountId(agentContractAccountMap.get(serviceFee.getFkAgentId()));
                    paymentFormParamVoList.add(paymentFormParamDto);
                }
                if (GeneralTool.isNotEmpty(paymentFormParamVoList)) {
                    // 插入远程调用所需参数
                    serviceFeePaymentFormDto.setPaymentFormParamVoList(paymentFormParamVoList);
                    // 插入付款单
                    Result result = financeCenterClient.saveBatchPaymentForms(serviceFeePaymentFormDto);
                    if (!result.isSuccess()) {
                        throw new GetServiceException(result.getMessage());
                    }
                }
            } else if (ProjectKeyEnum.CUSTOM_AMOUNT_PAYMENT.key.equals(payableType)) {
                for (ReceivablePlan receivablePlan : receivablePlans) {
                    // 应付计划对应的留学服务费对象
                    StudentServiceFee serviceFee = studentServiceFeeMap.get(receivablePlan.getFkTypeTargetId());
                    // 代理银行账户id
                    if (!agentContractAccountMap.containsKey(serviceFee.getFkAgentId())) {
                        // 没有银行账户就跳过
                        String agentName = agentNameMap.get(serviceFee.getFkAgentId());
                        remindAgentNameList.add(agentName);
                        continue;
                    }
                    PayablePlan payablePlan = new PayablePlan();
                    payablePlan.setFkCompanyId(fkCompanyId);
                    payablePlan.setFkTypeKey(TableEnum.SALE_STUDENT_SERVICE_FEE.key);
                    payablePlan.setFkTypeTargetId(receivablePlan.getFkTypeTargetId());
                    payablePlan.setFkReceivablePlanId(receivablePlan.getId());
                    payablePlan.setFkCurrencyTypeNum(serviceFeePaymentFormDto.getFkPayableCurrencyNum());
                    // 成本金额
                    BigDecimal feeCostAmount = feeCostAmountMap.getOrDefault(receivablePlan.getFkTypeTargetId(), BigDecimal.ZERO);
                    // 服务费金额
                    BigDecimal amount = serviceFee.getAmount().multiply(rate.get(serviceFee.getFkCurrencyTypeNum()));
                    // 服务费金额-成本金额
                    BigDecimal tuitionAmount = amount.subtract(feeCostAmount);
//                    payablePlan.setTuitionAmount(tuitionAmount);
                    payablePlan.setSplitRate(new BigDecimal("100"));
                    BigDecimal payableAmount = serviceFeePaymentFormDto.getPayableAmount();
                    payablePlan.setFixedAmount(payableAmount);
                    payablePlan.setPayableAmount(payableAmount);
                    payablePlan.setIsPayInAdvance(false);
                    payablePlan.setStatus(1);
                    payablePlan.setStatusSettlement(ProjectExtraEnum.UNSETTLED.key);
                    utilService.setCreateInfo(payablePlan);
                    payablePlanMapper.insert(payablePlan);

                    PaymentFormParamDto paymentFormParamDto = new PaymentFormParamDto();
                    paymentFormParamDto.setFkPayablePlanId(payablePlan.getId());
                    paymentFormParamDto.setFkAgentId(serviceFee.getFkAgentId());
                    paymentFormParamDto.setFkPayableCurrencyNum(serviceFeePaymentFormDto.getFkPayableCurrencyNum());
                    paymentFormParamDto.setPayableAmount(payableAmount);
                    paymentFormParamDto.setFkBankAccountId(agentContractAccountMap.get(serviceFee.getFkAgentId()));
                    paymentFormParamVoList.add(paymentFormParamDto);
                }
                if (GeneralTool.isNotEmpty(paymentFormParamVoList)) {
                    // 插入远程调用所需参数
                    serviceFeePaymentFormDto.setPaymentFormParamVoList(paymentFormParamVoList);
                    // 插入付款单
                    Result result = financeCenterClient.saveBatchPaymentForms(serviceFeePaymentFormDto);
                    if (!result.isSuccess()) {
                        throw new GetServiceException(result.getMessage());
                    }
                }
            }
        } else { // 已创建应付计划
            if (ProjectKeyEnum.PAYABLE_PLAN_PAYMENT.key.equals(payableType)) {
                // 获取已创建的应付计划
                List<PayablePlan> payablePlans = payablePlanMapper.selectList(Wrappers.<PayablePlan>lambdaQuery()
                        .eq(PayablePlan::getFkTypeKey, TableEnum.SALE_STUDENT_SERVICE_FEE.key)
                        .in(PayablePlan::getFkTypeTargetId, serviceFeeIds)
                        .eq(PayablePlan::getStatus, 1));
                if (GeneralTool.isNotEmpty(payablePlans)) {
                    // 应付计划ids
                    Set<Long> planIds = payablePlans.stream().map(PayablePlan::getId).collect(Collectors.toSet());
//                    // 获取有服务费在途结算的应付计划
//                    List<PayablePlanSettlementInstallment> installmentList = payablePlanSettlementInstallmentMapper.selectList(Wrappers.<PayablePlanSettlementInstallment>lambdaQuery()
//                            .in(PayablePlanSettlementInstallment::getFkPayablePlanId, planIds)
//                            .and(w -> w.eq(PayablePlanSettlementInstallment::getStatus, 1)
//                                    .or().eq(PayablePlanSettlementInstallment::getStatus, 2)
//                            )
//                    );
//                    // 获取有服务费在途结算的应付计划ids
//                    Set<Long> installmentPayablePlanIds = installmentList.stream().map(PayablePlanSettlementInstallment::getFkPayablePlanId).collect(Collectors.toSet());
//                    Set<Long> planIdsCopy = new HashSet<>(planIds);
//                    // 做差集
//                    planIdsCopy.removeAll(installmentPayablePlanIds);
//                    // 第一步不算在途，如果第一步有分期表数据，在做实付时需要删除，因为已经直接实付结算
//                    if (GeneralTool.isNotEmpty(planIdsCopy)) {
//                        payablePlanSettlementInstallmentMapper.delete(Wrappers.<PayablePlanSettlementInstallment>lambdaQuery()
//                                .in(PayablePlanSettlementInstallment::getFkPayablePlanId, planIdsCopy)
//                                .eq(PayablePlanSettlementInstallment::getStatus, 0)
//                                .eq(PayablePlanSettlementInstallment::getStatusSettlement, 0));
//                    }

                    // 获取应付计划的实付金额
                    List<PaymentFormVo> paymentFormItemList = financeCenterClient.getPayFormListFeignByPlanIds(planIds);
                    // key：应付计划id value：实付金额之和
                    Map<Long, BigDecimal> amountMap = new HashMap<>();
                    if (GeneralTool.isNotEmpty(paymentFormItemList)) {
                        amountMap = paymentFormItemList.stream().collect(Collectors.groupingBy(
                                PaymentFormVo::getFkPayablePlanId,
                                Collectors.reducing(BigDecimal.ZERO, PaymentFormVo::getAmountPayable, BigDecimal::add)));
                    }
                    for (PayablePlan payablePlan : payablePlans) {
                        // 应付计划对应的留学服务费对象
                        StudentServiceFee serviceFee = studentServiceFeeMap.get(payablePlan.getFkTypeTargetId());
                        // 代理银行账户id
                        if (!agentContractAccountMap.containsKey(serviceFee.getFkAgentId())) {
                            // 没有银行账户就跳过
                            String agentName = agentNameMap.get(serviceFee.getFkAgentId());
                            remindAgentNameList.add(agentName);
                            continue;
                        }
                        PaymentFormParamDto paymentFormParamDto = new PaymentFormParamDto();
                        paymentFormParamDto.setFkPayablePlanId(payablePlan.getId());
                        paymentFormParamDto.setFkAgentId(serviceFee.getFkAgentId());
                        paymentFormParamDto.setFkPayableCurrencyNum(payablePlan.getFkCurrencyTypeNum());
                        // 已付金额
                        BigDecimal amountPaid = amountMap.getOrDefault(payablePlan.getId(), BigDecimal.ZERO);
                        // 剩余金额
                        BigDecimal residualAmount = payablePlan.getPayableAmount().subtract(amountPaid);
                        if (residualAmount.compareTo(BigDecimal.ZERO) <= 0) { // 已付完
                            continue;
                        }
                        paymentFormParamDto.setPayableAmount(residualAmount);
                        paymentFormParamDto.setFkBankAccountId(agentContractAccountMap.get(serviceFee.getFkAgentId()));
                        paymentFormParamVoList.add(paymentFormParamDto);
                    }
                    if (GeneralTool.isNotEmpty(paymentFormParamVoList)) {
                        // 插入远程调用所需参数
                        serviceFeePaymentFormDto.setPaymentFormParamVoList(paymentFormParamVoList);
                        //直接实付结算了，删除非在途佣金
                        serviceFeePaymentFormDto.setPayablePlanIds(planIds);
                        // 插入付款单
                        Result result = financeCenterClient.saveBatchPaymentForms(serviceFeePaymentFormDto);
                        if (!result.isSuccess()) {
                            throw new GetServiceException(result.getMessage());
                        }
                    }
                }
            }
        }
        //批量修改留学服务费的结算状态
        List<StudentServiceFee> studentServiceFees1 = studentServiceFeeMapper.selectList(Wrappers.<StudentServiceFee>lambdaQuery()
                .in(StudentServiceFee::getId, serviceFeeIds)
                .eq(StudentServiceFee::getSettlementStatus, 1));
        if (GeneralTool.isEmpty(studentServiceFees1)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        studentServiceFees1.forEach((serviceFee1) -> {
            serviceFee1.setSettlementStatus(2);
            utilService.setUpdateInfo(serviceFee1);
        });
        //批量更新 每次更新DEFAULT_BATCH_SIZE = 1000
        boolean b = updateBatchById(studentServiceFees1, DEFAULT_BATCH_SIZE);
        if (!b) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
        if (GeneralTool.isNotEmpty(remindAgentNameList)) {
            String agentName = String.join(",", remindAgentNameList);
            remindMsg.append("以下代理账户未创建银行账户，无法进行实付：【").append(agentName).append("】");
        }
        return remindMsg.toString();
    }

    @Override
    public SaveResponseBo cancelCompletedBusinessStatus(Long serviceFeeId) {
        if (GeneralTool.isEmpty(serviceFeeId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        StudentServiceFee studentServiceFee = studentServiceFeeMapper.selectById(serviceFeeId);
        if (GeneralTool.isEmpty(studentServiceFee)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("service_fee_does_not_exist"));
        }
//        Integer reCount = receivablePlanMapper.selectCount(Wrappers.<ReceivablePlan>lambdaQuery()
//                .eq(ReceivablePlan::getFkTypeTargetId, serviceFeeId)
//                .ne(ReceivablePlan::getStatus, 0)
//                .eq(ReceivablePlan::getFkTypeKey, TableEnum.SALE_STUDENT_SERVICE_FEE.key));
//        Integer pyCount = payablePlanMapper.selectCount(Wrappers.<PayablePlan>lambdaQuery()
//                .eq(PayablePlan::getFkTypeTargetId, serviceFeeId)
//                .ne(PayablePlan::getStatus, 0)
//                .eq(PayablePlan::getFkTypeKey, TableEnum.SALE_STUDENT_SERVICE_FEE.key));
//        if (reCount > 0 || pyCount > 0) {
//            throw new GetServiceException("改服务费存在应收或者应付，取消失败");
//        }
        studentServiceFee.setBusinessStatus(0);
        utilService.setUpdateInfo(studentServiceFee);
        studentServiceFeeMapper.updateById(studentServiceFee);
        return SaveResponseBo.ok(serviceFeeId);
    }

    @Override
    public ResponseBo batchCompleteBusinessStatus(Set<Long> serviceFeeIds) {
        if (GeneralTool.isEmpty(serviceFeeIds)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        List<StudentServiceFee> studentServiceFees = studentServiceFeeMapper.selectList(Wrappers.<StudentServiceFee>lambdaQuery()
                .in(StudentServiceFee::getId, serviceFeeIds)
                .eq(StudentServiceFee::getBusinessStatus, 0));
        if (GeneralTool.isEmpty(studentServiceFees)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        studentServiceFees.forEach((serviceFee) -> {
            serviceFee.setBusinessStatus(1);
            utilService.setUpdateInfo(serviceFee);
        });
        //批量更新 每次更新DEFAULT_BATCH_SIZE = 1000
        boolean b = updateBatchById(studentServiceFees, DEFAULT_BATCH_SIZE);
        if (!b) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
        return SaveResponseBo.ok();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SaveResponseBo cancelCompletedSettlementApproval(Long serviceFeeId) {
        if (GeneralTool.isEmpty(serviceFeeId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        StudentServiceFee studentServiceFee = studentServiceFeeMapper.selectById(serviceFeeId);
        if (GeneralTool.isEmpty(studentServiceFee)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("service_fee_does_not_exist"));
        }
        studentServiceFee.setApproveStatus(0);
        studentServiceFee.setApproveTime(null);
        studentServiceFee.setApproveUser(null);
        utilService.setUpdateInfo(studentServiceFee);
        studentServiceFeeMapper.updateByIdWithNull(studentServiceFee);
        return SaveResponseBo.ok(serviceFeeId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseBo batchCompleteSettlementApproval(Set<Long> serviceFeeIds) {
        if (GeneralTool.isEmpty(serviceFeeIds)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        List<StudentServiceFee> studentServiceFees = studentServiceFeeMapper.selectList(Wrappers.<StudentServiceFee>lambdaQuery()
                .in(StudentServiceFee::getId, serviceFeeIds)
                .eq(StudentServiceFee::getApproveStatus, 0));

        if (GeneralTool.isEmpty(studentServiceFees)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        studentServiceFees.forEach((serviceFee) -> {
            serviceFee.setApproveStatus(1);
            serviceFee.setApproveTime(new Date());
            serviceFee.setApproveUser(SecureUtil.getStaffInfo().getLoginId());
            utilService.setUpdateInfo(serviceFee);
        });
        //批量更新 每次更新DEFAULT_BATCH_SIZE = 1000
        boolean b = updateBatchById(studentServiceFees, DEFAULT_BATCH_SIZE);
        if (!b) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
        return SaveResponseBo.ok();
    }

    /**
     * 根据服务费id获取提供商id
     *
     * @param feeIds
     * @return
     */
    @Override
    public List<Long> getServiceFeeProviderIdsByFeeIds(List<Long> feeIds) {
        return studentServiceFeeMapper.getServiceFeeProviderIdsByFeeIds(feeIds);
    }
    /**
     * 批量已提交财务
     *
     * @param serviceFeeIds
     * @return
     */
    @Override
    public ResponseBo batchCompleteSettlementStatus(Set<Long> serviceFeeIds) {

        if (GeneralTool.isEmpty(serviceFeeIds)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        List<StudentServiceFee> studentServiceFees = studentServiceFeeMapper.selectList(Wrappers.<StudentServiceFee>lambdaQuery()
                .in(StudentServiceFee::getId, serviceFeeIds)
                .eq(StudentServiceFee::getSettlementStatus, 0));
        if (GeneralTool.isEmpty(studentServiceFees)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        studentServiceFees.forEach((serviceFee) -> {
            serviceFee.setSettlementStatus(1);
            utilService.setUpdateInfo(serviceFee);
        });
        //批量更新 每次更新DEFAULT_BATCH_SIZE = 1000
        boolean b = updateBatchById(studentServiceFees, DEFAULT_BATCH_SIZE);
        if (!b) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
        return SaveResponseBo.ok();
    }

    @Override
    public void createServiceFeePdf(Long id, HttpServletResponse response) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException("fkStudentServiceFeeId" + LocaleMessageUtils.getMessage("id_null"));
        }
        StudentServiceFeeSummaryDto studentServiceFeeSummaryDto = new StudentServiceFeeSummaryDto();
        studentServiceFeeSummaryDto.setFkStudentServiceFeeId(id);
        Long staffId = SecureUtil.getStaffId();
        //获取当前登录人的所有业务下属
        List<Long> staffIds = new ArrayList<>();
        Result<List<Long>> result_ = permissionCenterClient.getStaffFollowerIds(staffId);
        if (result_.isSuccess() && GeneralTool.isNotEmpty(result_.getData())) {
            staffIds.addAll(result_.getData());
        }
        staffIds.add(staffId);
        //查询
        StudentServiceFeeSummaryVo studentServiceFeeSummaryVo = studentServiceFeeMapper.exportStudentServiceFeeInfo(studentServiceFeeSummaryDto, staffIds, SecureUtil.getStaffInfo().getIsStudentAdmin(),SecureUtil.getPermissionGroupInstitutionIds(), SecureUtil.getStaffBoundBdIds());
        if (GeneralTool.isEmpty(studentServiceFeeSummaryVo)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("file_export_failed_no_service_fee"));
        }
        StudentServiceFee studentServiceFee = studentServiceFeeMapper.selectById(studentServiceFeeSummaryVo.getServiceFeeId());
        if (GeneralTool.isNotEmpty(studentServiceFee)) {
            /**
             * 获取收款单信息
             */
            Map<Long, List<ReceiptFormVo>> receiptFormMap = Maps.newHashMap();
            List<Long> feeIds = new ArrayList<>();
            feeIds.add(studentServiceFee.getId());
            Map<Long, List<Long>> reMap = receivablePlanService.getReceivablePlanIds(TableEnum.SALE_STUDENT_SERVICE_FEE.key, Sets.newHashSet(feeIds));
            Map<Long, List<Long>> paMap = payablePlanService.getPayablePlanIds(TableEnum.SALE_STUDENT_SERVICE_FEE.key, Sets.newHashSet(feeIds));

            Result<Map<Long, List<ReceiptFormVo>>> receiptFormToStudentResult = financeCenterClient.getReceiptFormsByFeeIds(feeIds);
            if (receiptFormToStudentResult.isSuccess() && GeneralTool.isNotEmpty(receiptFormToStudentResult.getData())) {
                receiptFormMap = receiptFormToStudentResult.getData();
            }
            List<Long> receivablePlanIds = reMap.getOrDefault(studentServiceFeeSummaryVo.getServiceFeeId(), Lists.newArrayList());
            List<Long> payablePlanIds = paMap.getOrDefault(studentServiceFeeSummaryVo.getServiceFeeId(), Lists.newArrayList());
            if (GeneralTool.isNotEmpty(payablePlanIds)) { // 一般应付计划只有一个，如果有多个取MIN(id)
                Long minPayablePlanId = payablePlanIds.stream().min(Long::compare).get();
                // 应付计划Id
                studentServiceFeeSummaryVo.setPayablePlanId(minPayablePlanId);
            }
            receivablePlanIds.addAll(payablePlanIds);
            studentServiceFeeSummaryVo.setARAPIds(receivablePlanIds);

            studentServiceFeeSummaryVo.setCollectionStatus(ProjectExtraEnum.getValueByKey(studentServiceFeeSummaryVo.getReceiveStatus(), ProjectExtraEnum.AR_STATUS));
            studentServiceFeeSummaryVo.setPaymentStatus(ProjectExtraEnum.getValueByKey(studentServiceFeeSummaryVo.getPayableStatus(), ProjectExtraEnum.AP_STATUS));
            //设置留学服务费的结算状态名称
            if (GeneralTool.isNotEmpty(studentServiceFeeSummaryVo.getSettlementStatus())) {
                // summaryDto.setSettlementStatusName(ProjectExtraEnum.getValueByKey(summaryDto.getSettlementStatus(), ProjectExtraEnum.SETTLEMENT_TYPE));
                studentServiceFeeSummaryVo.setSettlementStatusName(ProjectExtraEnum.getInitialValueByKey(studentServiceFeeSummaryVo.getSettlementStatus(), ProjectExtraEnum.STUDY_ABROAD_FEE_STATUS));
            }
            if (studentServiceFeeSummaryVo.getBusinessStatus().equals(0)) {
                studentServiceFeeSummaryVo.setBusinessStatusName("未完成");
            } else if (studentServiceFeeSummaryVo.getBusinessStatus().equals(1)) {
                studentServiceFeeSummaryVo.setBusinessStatusName("已完成");
            }
            // 结算状态审批
            if (studentServiceFeeSummaryVo.getApproveStatus().equals(0)) {
                studentServiceFeeSummaryVo.setApproveStatusName("未审批");
            } else if (studentServiceFeeSummaryVo.getApproveStatus().equals(1)) {
                studentServiceFeeSummaryVo.setApproveStatusName("已审批");
            }

            studentServiceFeeSummaryVo.setFkTypeKeyReceivableName(TableEnum.getValueByKey(studentServiceFeeSummaryVo.getFkTypeKeyReceivable(), TableEnum.TYPE_KEY_RECEIVABLE));
            studentServiceFeeSummaryVo.setReceiptFormDtoList(receiptFormMap.get(studentServiceFeeSummaryVo.getServiceFeeId()));
            List<ReceiptFormVo> receiptFormDtoList = studentServiceFeeSummaryVo.getReceiptFormDtoList();
            String serviceFeeDate = "";
            if (GeneralTool.isEmpty(receiptFormDtoList)){
                serviceFeeDate = "";
            }else {
                Optional<Date> latestReceiptDateOpt = receiptFormDtoList.stream()
                        .map(ReceiptFormVo::getReceiptDate)
                        .filter(date->date!=null)
                        .max(Comparator.naturalOrder());
                //处理时间显示格式dd/MM/yyyy
                serviceFeeDate = latestReceiptDateOpt.map(date ->
                        new SimpleDateFormat("dd/MM/yyyy").format(date)).orElse("");
            }


            //整理所需要的参数
            Map<String, Object> dataMap = new HashMap<>(8);
            if (studentServiceFeeSummaryVo.getServiceFeeNum().equals(studentServiceFee.getNum())) {
                String serviceFeeNum =String.format("%06d", studentServiceFee.getId()) ;
                dataMap.put("serviceFeeNum", serviceFeeNum);
                dataMap.put("serviceFeeAmount", formatDecimal(studentServiceFeeSummaryVo.getServiceFeeAmount()));
//                dataMap.put("serviceFeeCurrencyNum", studentServiceFeeSummaryVo.getServiceFeeTypeKey());
                dataMap.put("studentName", studentServiceFeeSummaryVo.getStudentName());
////                //税前金额
                dataMap.put("pretaxAmount", formatDecimal(studentServiceFeeSummaryVo.getPretaxAmount()));
////                //税金
                dataMap.put("serviceFeeTaxes", formatDecimal(studentServiceFeeSummaryVo.getServiceFeeTaxes()));
////                //应收计划的时间，最新的
                dataMap.put("serviceFeeDate", serviceFeeDate);

            }


            for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
                if (GeneralTool.isNull(entry.getValue())) {
                    dataMap.put(entry.getKey(), "");
                }
            }
            try {
                String fileName ="invoices"+"-" + dataMap.get("studentName")+"-"+ dataMap.get("serviceFeeNum");
                DocUtils.createPdf(dataMap, response, "/invoices.pdf", fileName);
            } catch (Exception e) {

                throw new GetServiceException(LocaleMessageUtils.getMessage("file_export_fail"));
            }
        }else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("file_export_failed_no_service_fee"));
        }


    }

    @Override
    public StudentServiceFeeSummaryVo getServiceFeeInfoById(Long id) {
        return studentServiceFeeMapper.getServiceFeeNumById(id);
    }

    // 在类中定义常量格式化工具（线程安全方式）
    private static final ThreadLocal<DecimalFormat> DECIMAL_FORMATTER = ThreadLocal.withInitial(
            () -> new DecimalFormat("#0.00")
    );

    // 数值格式化方法
    private static String formatDecimal(BigDecimal value) {
        if (value == null) {
            return "0.00";
        }
        return DECIMAL_FORMATTER.get().format(value.setScale(2, RoundingMode.HALF_UP));
    }


}

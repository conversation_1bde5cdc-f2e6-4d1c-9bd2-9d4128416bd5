package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.permissioncenter.vo.ResourceVo;
import com.get.salecenter.vo.StudentEventVo;
import com.get.salecenter.service.IStudentEventService;
import com.get.salecenter.dto.StudentEventDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/11/10
 * @TIME: 16:30
 * @Description:
 **/
@Api(tags = "学生事件管理")
@RestController
@RequestMapping("sale/studentEvent")
public class StudentEventServiceController {

    @Resource
    private IStudentEventService studentEventService;


    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentEventVo>
     * @Description: 列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "keyWord为关键词")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生事件管理/列表数据")
    @PostMapping("datas")
    public ResponseBo<StudentEventVo> datas(@RequestBody SearchBean<StudentEventDto> page) {
        List<StudentEventVo> datas = studentEventService.getStudentEvents(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentEventVo>
     * @Description: 详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/学生事件管理/详情")
    @GetMapping("/{id}")
    public ResponseBo<StudentEventVo> detail(@PathVariable("id") Long id) {
        StudentEventVo studentEventVo = studentEventService.findStudentEventById(id);
        return new ResponseBo<>(studentEventVo);
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 新增信息
     * @Param [studentEventDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生事件管理/新增信息")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(StudentEventDto.Add.class)  StudentEventDto studentEventDto) {
        return SaveResponseBo.ok(studentEventService.addStudentEvent(studentEventDto));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 删除
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为删除数据的ID")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/学生事件管理/删除接口")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        studentEventService.delete(id);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.common.entity.fegin.ResourceVo>
     * @Description: 修改信息
     * @Param [studentEventDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生事件管理/修改接口")
    @PostMapping("update")
    public ResponseBo<ResourceVo> update(@RequestBody @Validated(StudentEventDto.Update.class) StudentEventDto studentEventDto) {
        return UpdateResponseBo.ok(studentEventService.updateStudentEvent(studentEventDto));
    }


}

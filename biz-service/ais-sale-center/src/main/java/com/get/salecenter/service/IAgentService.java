package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.service.GetService;
import com.get.financecenter.dto.BatchDownloadAgentReconciliationDto;
import com.get.financecenter.dto.query.AgentSettlementQueryDto;
import com.get.financecenter.vo.AgentSettlementGrossAmountVo;
import com.get.institutioncenter.dto.NewEmailGetAgentDto;
import com.get.pmpcenter.dto.agent.AgentCommissionTypeAgentDto;
import com.get.salecenter.dto.*;
import com.get.salecenter.dto.query.AgentContractQueryDto;
import com.get.salecenter.dto.query.AgentQueryDto;
import com.get.salecenter.entity.Agent;
import com.get.salecenter.vo.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: Sea
 * @create: 2020/7/7 11:53
 * @verison: 1.0
 * @description: 代理安排管理接口
 */
public interface IAgentService extends GetService<Agent> {

    /**
     * 详情
     *
     * @param id
     * @return
     */
    AgentVo findAgentById(Long id);

    /**
     * 新增
     *
     * @param agentDto
     * @return
     */
    Long addAgent(AgentDto agentDto);

    Long addAgent(AgentAddDto agentAddDto);

    /**
     * 获取代理状态
     * @param id
     * @return
     */
    Boolean isActive(Long id);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 修改
     *
     * @param agentVo
     * @return
     */
    AgentVo updateAgent(AgentUpdateDto agentVo);

    /**
     * 列表，有业务上司业务下属的权限
     *
     * @param agentVo
     * @param page
     * @return
     */
    List<AgentVo> getAgents(AgentQueryDto agentVo, Page page);


    /**
     * 获取代理激活数量信息
     * @param agentVo
     * @return
     */
    ResponseBo<AgentBusinessInfoVo> getAgentActiveInfo(AgentQueryDto agentVo);


    Map<Long, Agent> getAgentsByIds(Set<Long> agentIds);

    /**
     * 列表，无业务上司业务下属的权限
     *
     * @param agentVo
     * @param page
     * @return
     * @
     */
    List<AgentVo> dataList(AgentQueryDto agentVo, Page page);

    /**
     * 根据输入的代理编号、代理名称 模糊查询对应的代理id
     *
     * @param agentNum
     * @param agentName
     * @return
     */
    List<Long> getAgentIds(String agentNum, String agentName);

    /**
     * 所属代理下拉框数据
     *
     * @return
     */
    List<BaseSelectEntity> getAgentList(Long companyId,Boolean isPersonalName);

    /**
     * 安全配置
     *
     * @param validList
     */
    void editAgentCompany(List<AgentCompanyDto> validList);

    /**
     * @return java.util.List<com.get.salecenter.vo.CompanyTreeVo>
     * @Description: 代理和公司的关系（数据回显）
     * @Param [agentId]
     * <AUTHOR>
     */
    List<CompanyTreeVo> getAgentCompanyRelation(Long agentId);

    /**
     * 获取代理附件
     *
     * @param attachedVo
     * @param page
     * @return
     */
    List<MediaAndAttachedVo> getAgentMedia(MediaAndAttachedDto attachedVo, Page page);


    /**
     * 解析代理营业执照信息
     */
    ResponseBo<BusinessLicenseResultDto> analysisOfBusinessLicense(MultipartFile file);


    /**
     * 解析代理身份证信息
     * @param file
     * @param type
     * @return
     */
    ResponseBo<String> verifyIdCard(MultipartFile file,Integer type);
    /**
     * 添加代理附件
     *
     * @param mediaAttachedVo
     * @return
     */
    List<MediaAndAttachedVo> addAgentMedia(List<MediaAndAttachedDto> mediaAttachedVo);

    /**
     * 获取代理联系人列表
     *
     * @param contactPersonVo
     * @param page
     * @return
     */
    List<ContactPersonVo> getAgentContactPersonDtos(ContactPersonDto contactPersonVo, Page page);

    /**
     * 新增代理联系人
     *
     * @param contactPersonVo
     * @return
     * @
     */
    Long addAgentContactPerson(ContactPersonDto contactPersonVo);


    void validateAddContractPerson(ContactPersonDto contactPersonVo);

    /**
     * @return java.util.List<com.get.salecenter.vo.AgentContractVo>
     * @Description: 查询单个代理所有合同
     * @Param [data, searchBean]
     * <AUTHOR>
     */
    List<AgentContractVo> getAgentContractDtos(AgentContractQueryDto data, Page searchBean);

    /**
     * @return java.lang.Long
     * @Description: 新增代理合同
     * @Param [contractVo]
     * <AUTHOR>
     */
    Long addAgentContract(AgentContractDto contractVo);

    Long addApprovedAgentContract(AgentContractDto contractVo);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 查询代理下级ids
     * @Param [agentId]
     * <AUTHOR>
     */
    List<Long> getAgentFollowerIds(Long agentId);

    /**
     * @Description: 根据ids一次性查询代理下级ids
     * @Author: Jerry
     * @Date:10:10 2021/8/18
     */
    List<Long> getAgentFollowerIdsByIds(String agentIds);

    /**
     * 查询下一级代理ids
     *
     * @Date 12:24 2021/8/12
     * <AUTHOR>
     */
    List<Long> getSubordinateAgentIds(List<Long> agentIds);

    /**
     * @return java.lang.String
     * @Description: 根据代理id查询名称
     * @Param [agentId]
     * <AUTHOR>
     */
    String getNameById(Long agentId);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 模糊查询
     * @Param [name]
     * <AUTHOR>
     */
    List<Long> getAgentListIds(String name);

    /**
     * @Description :根据代理ids查找代理名称map
     * @Param [agentIds]
     * <AUTHOR>
     */
    Map<Long, String> getAgentNamesByIds(Set<Long> agentIds);

    /**
     * @Description：查询所有代理
     * @Param
     * @Date 12:32 2021/5/11
     * <AUTHOR>
     */
    List<AgentVo> getAllAgent();

    /**
     * @Date 14:56 2021/12/20
     * <AUTHOR>
     */
    List<Long> getAgentIdList();

    /**
     * feign财务佣金汇总列表
     *
     * @return
     * @Date 16:08 2021/12/24
     * <AUTHOR>
     */
    CommissionSummaryPageVo commissionSummary(CommissionSummaryDto commissionSummaryDto, Page page);

    /**
     * feign 根据财务结算批次号获取代理应付计划
     *
     * @Date 14:30 2021/12/28
     * <AUTHOR>
     */
    List<PayablePlanVo> getAgentPayablePlanByNumSettlementBatch(String numSettlementBatch);

    /**
     * feign 代理佣金结算列表
     *
     * @Date 11:15 2021/12/21
     * <AUTHOR>
     */
    AgentSettlementPageVo agentSettlementList(AgentSettlementQueryDto data, Page page);


    /**
     * 获取结算的ids
     *
     * @param agentSettlementVo
     * @param local
     * @param staffId
     * @param exportFlag
     * @return
     */
    List<Long> getAgentSettlementIds(AgentSettlementQueryDto agentSettlementVo, String local, Long staffId, boolean payInAdvanceFlag, boolean exportFlag);
    /**
     * feign 根据代理id获取该代理下级非结算口的代理ids
     *
     * @return
     * @Date 11:15 2021/12/21
     * <AUTHOR>
     */
    List<Long> getSubordinateNotPortAgentIdsById(Long agentId);


//    /**
//     * 代理对账单确认列表
//     *
//     * @Date 11:15 2021/12/21
//     * <AUTHOR>
//     */
//    List<AgencyStatementDto> agencyStatementDatas(AgencyStatementVo data, Page page);

    /**
     * 代理关键代理过期
     */
    Boolean agentIsKeyExpired();

    Map<Long, String> getIsExistAgent(Long companyId, Long id, String name, String taxCode, String nature, String legalPerson,
                                      String idCard);

    Integer getAgentCompanyIdById(Long id);

    /**
     * 根据代理ID获取ISSUE用户信息
     *
     * @param fkAgentId
     * @return
     */
//    List<UserInfoDto> getIssueUserByAgentId(Long fkAgentId);

    /**
     * @Description:
     * @Param: page
     * @return:
     * @Author: Walker
     * @Date: 2022/3/15
     */
    List<AgentSourceVo> getSource(AgentSourceDto agentSourceDto, Page page);

    void exportAgentSourceExcel(HttpServletResponse response, AgentSourceDto agentSourceDto);

    AgentSourceVo getSumSourceByCppIdOrBmsId(String cpp_id, String bms_id, String json);

    /**
     * 联系人代理下拉框
     *
     * @param companyId
     * @return
     */
    List<BaseSelectEntity> getContactPersonAgentList(Long companyId);

    /**
     * 导出代理列表Excel
     *
     * @param response
     * @param agentVo
     */
    void exportAgentDatasExcel(HttpServletResponse response, AgentQueryDto agentVo);

    /**
     * Author Cream
     * Description : 获取代理id
     * Date 2022/5/7 14:24
     * Params: Long payablePlanId
     * Return
     */
    Long doGetAgentIdByPayablePlanId(Long payablePlanId);

    /**
     * 所属代理下拉框(百度式搜索)
     * @param companyIds
     * @param agentName
     * @return
     */
    List<AgentSubVo> getAgentListByName(List<Long> companyIds, String agentName);

    /**
     * 获取代理联系人
     *
     * @param id
     * @return
     */
    List<ContactPersonVo> getContactPersonInfo(Long id);

    /**
     * 获取代理合同联系人
     *
     * @param id
     * @return
     */
    List<ContactPersonVo> getAgentContactPersonInfo(Long id);

    /**
     * 根据id返回
     *
     * @param fkAgentId
     * @return
     */
    Agent getAgentById(Long fkAgentId);

    /**
     * 绑定默认项目成员列表
     *
     * @return
     */
    List<StudentProjectRoleVo> getAgentDefaultProjectRole(Long companyId);

    Agent getIaeAgentById(Long agentId);

    /**
     * 检查代理资料是否完善
     *
     * @Date 15:12 2022/11/28
     * <AUTHOR>
     */
    boolean checkAgentData(List<BatchDownloadAgentReconciliationDto> batchDownloadAgentReconciliationVoList);


    /**
     * 查询代理
     * @param targetName
     * @return
     */
    List<BaseSelectEntity> getAgentByTargetName(String targetName);

    List<ContactPersonVo> getAgentContactPersonByAgentId(Long id);

    List<AgentListVo> getAgentListNew(Long companyId, Boolean isPersonalName);

    /**
     * 峰会参会人员编辑代理下拉框
     * @param companyId
     * @return
     */
    List<BaseSelectEntity> getConventionAgentList(Long companyId);

    Map<Long, Long> getAgentCompanyIdByIds(Set<Long> ids);

   // void deleteAgent(Long id);

    /**
     * 验证是否显示渠道代理
     * @return
     */
    Boolean validateCustomerChannelRequired();

    /**
     * 渠道代理下拉
     * @param companyId
     * @return
     */
    List<BaseSelectEntity> getCustomerChannelAgentSelect(Long companyId);


    List<AgentVo> getAgentListAll();

    List<BaseSelectEntity> getAgentSelect(String name);

    List<AgentSettlementGrossAmountVo> getAgentListToExport(AgentSettlementQueryDto agentSettlementVo);

    Set<String> getNewAgentEmails(NewEmailGetAgentDto newEmailGetAgentDto);

    Set<String> getNewAgentAllEmails(Long newsId, Integer type);

    List<Long> getAgentIdByEmail(String email);

    /**
     * 列表，有业务上司业务下属的权限
     */
    AgenCommissionAndAgentSearchVo getAgentCommissionTypeAndAgentIsBind(AgentCommissionTypeAgentDto agentCommissionTypeAgentDto ,Page page);

    /**
     * 代理合同续约
     *
     * @return
     */
    void sendEmailRenewalEmail(AgentContractRenewalDto agentContractRenewalDto);

}

package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.ConventionPersonAgentMapper;
import com.get.salecenter.vo.ConventionPersonVo;
import com.get.salecenter.entity.ConventionPersonAgent;
import com.get.salecenter.service.IConventionPersonAgentService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: Hardy
 * @create: 2022/7/14 11:29
 * @verison: 1.0
 * @description:
 */
@Service
public class ConventionPersonAgentServiceImpl extends ServiceImpl<ConventionPersonAgentMapper, ConventionPersonAgent> implements IConventionPersonAgentService {

    @Resource
    private ConventionPersonAgentMapper conventionPersonAgentMapper;


    @Override
    public Map<Long, String> getConventionPersonAgentNameMapByIds(List<Long> personAgentIds) {
        Map<Long, String> map = new HashMap<>();
        List<ConventionPersonVo> conventionPersonVos = conventionPersonAgentMapper.getConventionPersonAgentNameMapByIds(personAgentIds);
        if (GeneralTool.isEmpty(conventionPersonVos)){
            return null;
        }
        for (ConventionPersonVo conventionPersonVo : conventionPersonVos) {
            map.put(conventionPersonVo.getId(), conventionPersonVo.getCompany());
        }
        return map;
    }
}

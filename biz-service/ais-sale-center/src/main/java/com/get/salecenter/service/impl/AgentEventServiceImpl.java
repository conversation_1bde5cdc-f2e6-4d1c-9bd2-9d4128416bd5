package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.AgentEventMapper;
import com.get.salecenter.vo.AgentEventTypeVo;
import com.get.salecenter.vo.AgentEventVo;
import com.get.salecenter.entity.AgentEvent;
import com.get.salecenter.service.IAgentEventService;
import com.get.salecenter.service.IAgentEventTypeService;
import com.get.salecenter.dto.AgentEventDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/9/11
 * @TIME: 17:16
 * @Description:
 **/
@Service
public class AgentEventServiceImpl implements IAgentEventService {
    @Resource
    private AgentEventMapper agentEventMapper;
    @Autowired
    private UtilService utilService;
    @Autowired
    private IAgentEventTypeService typeService;

    @Override
    public Long addAgentEvent(AgentEventDto agentEventDto) {
        AgentEvent agentEvent = BeanCopyUtils.objClone(agentEventDto, AgentEvent::new);
        utilService.updateUserInfoToEntity(agentEvent);
        agentEventMapper.insertSelective(agentEvent);
        return agentEvent.getId();
    }

    @Override
    public List<AgentEventVo> getAgentEventDtos(AgentEventDto agentEventDto, Page page) {
        if (GeneralTool.isEmpty(agentEventDto.getFkAgentId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
//        Example example = new Example(AgentEvent.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkAgentId", agentEventDto.getFkAgentId());
//        if (GeneralTool.isNotEmpty(agentEventDto.getFkAgentEventTypeId())) {
//            criteria.andEqualTo("fkAgentEventTypeId", agentEventDto.getFkAgentEventTypeId());
//        }
//        if (GeneralTool.isNotEmpty(agentEventDto.getKeyWord())) {
//            criteria.andLike("description", "%" + agentEventDto.getKeyWord() + "%");
//        }
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        List<AgentEvent> agentEvents = agentEventMapper.selectByExample(example);
//        page.restPage(agentEvents);

        LambdaQueryWrapper<AgentEvent> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(AgentEvent::getFkAgentId, agentEventDto.getFkAgentId());
        if (GeneralTool.isNotEmpty(agentEventDto.getFkAgentEventTypeId())) {
            lambdaQueryWrapper.eq(AgentEvent::getFkAgentEventTypeId, agentEventDto.getFkAgentEventTypeId());
        }
        if (GeneralTool.isNotEmpty(agentEventDto.getKeyWord())) {
            lambdaQueryWrapper.like(AgentEvent::getDescription, agentEventDto.getKeyWord());
        }
        IPage<AgentEvent> iPage = agentEventMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
        List<AgentEvent> agentEvents = iPage.getRecords();
        page.setAll((int) iPage.getTotal());

        List<AgentEventVo> agentEventVos = agentEvents.stream().map(agentEvent ->
                BeanCopyUtils.objClone(agentEvent, AgentEventVo::new)).collect(Collectors.toList());
        setTypeName(agentEventVos);
        return agentEventVos;
    }

    /**
     * @return void
     * @Description: 设置事件名称
     * @Param [agentEventVos]
     * <AUTHOR>
     */
    private void setTypeName(List<AgentEventVo> agentEventVos) {
        if (GeneralTool.isEmpty(agentEventVos)) {
            return;
        }
        Map<Long, String> typeMap = getTypeMap();
        for (AgentEventVo agentEventVo : agentEventVos) {
            if (GeneralTool.isNotEmpty(agentEventVo.getFkAgentEventTypeId())) {
                agentEventVo.setTypeName(typeMap.get(agentEventVo.getFkAgentEventTypeId()));
            }
        }
    }

    private Map<Long, String> getTypeMap() {
        List<AgentEventTypeVo> eventTypeDtos = getAllEventType();
        return eventTypeDtos.stream().collect(Collectors.toMap(AgentEventTypeVo::getId, AgentEventTypeVo::getTypeName));
    }


    @Override
    public AgentEventVo findAgentEventById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        AgentEvent agentEvent = agentEventMapper.selectById(id);
        return BeanCopyUtils.objClone(agentEvent, AgentEventVo::new);
    }

    @Override
    public AgentEventVo updateAgentEvent(AgentEventDto agentEventDto) {
        AgentEvent agentEvent = BeanCopyUtils.objClone(agentEventDto, AgentEvent::new);
        utilService.updateUserInfoToEntity(agentEvent);
        agentEventMapper.updateById(agentEvent);
        return findAgentEventById(agentEvent.getId());
    }

    @Override
    public void deleteAgentEvent(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        agentEventMapper.deleteById(id);
    }

    @Override
    public List<AgentEventTypeVo> getAllEventType() {
        return typeService.getAllEventType();
    }
}

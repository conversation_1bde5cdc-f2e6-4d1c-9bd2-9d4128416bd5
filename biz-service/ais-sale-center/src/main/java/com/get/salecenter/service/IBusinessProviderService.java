package com.get.salecenter.service;

import com.get.common.result.SearchBean;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.tool.api.Result;
import com.get.permissioncenter.vo.tree.CompanyTreeVo;
import com.get.salecenter.vo.BusinessProviderSelectVo;
import com.get.salecenter.vo.BusinessProviderVo;
import com.get.salecenter.vo.MediaAndAttachedVo;
import com.get.salecenter.vo.SelItem;
import com.get.salecenter.dto.BusinessProviderDto;
import com.get.salecenter.dto.MediaAndAttachedDto;
import com.get.salecenter.dto.query.BusinessProviderQueryDto;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * author:Neil
 * Time: 16:57
 * Date: 2022/6/20
 * Description:
 */
public interface IBusinessProviderService {
    List<BusinessProviderVo> getBusinessProviderDtos(BusinessProviderQueryDto businessProviderVo, SearchBean<BusinessProviderQueryDto> page);

    void addBusinessProvider(ValidList<BusinessProviderDto> businessProviderDto);

    BusinessProviderVo updateBusinessProvider(BusinessProviderDto businessProviderDto);

    void deleteBusinessProvider(Long id);
    /**
     * 查询byID
     *
     * @param id
     * @return
     * @
     */
    BusinessProviderVo findBusinessProviderById(Long id);

    public Map<Long,String> getNamesByIds(Set<Long> ids);

    String getBusinessProviderNameById(Long id);

    /**
     * 获取公司信息
     * @param id
     * @return
     */
    SelItem getBusinessProviderCompanyInfo(Long id);

    /**
     * 获取留学住宿提供商id
     * @param targetName
     * @return
     */
    List<Long> getBusinessProviderId(String targetName);


    /**
     * 获取应收信息
     * @param targetId,receiptFormId
     * @returnr
     */
    List<BaseSelectEntity> getPlanAndTargetName(Long targetId,Long receiptFormId);
    /**
     * 根据公司id获取留学住宿提供商下拉业务信息
     * @param companyId
     * @return
     */
    List<BaseSelectEntity> getBusinessObjectSelection(Long companyId);

    List<BaseSelectEntity> providerSelect(String tableName,Long oldId,Long companyIds);

    /**
     * 获取业务提供商下拉
     * @param targetName
     * @return
     */
    List<BaseSelectEntity> getBusinessProviderByTargetName(String targetName);

    List<BaseSelectEntity> getClientSourceProviderSelect(String tableName,Long fkCompanyId);

    List<MediaAndAttachedVo> addBusinessProviderMedia(ValidList<MediaAndAttachedDto> mediaAttachedVo);

    List<MediaAndAttachedVo> getBusinessProviderMedia(MediaAndAttachedDto data, SearchBean<MediaAndAttachedDto> voSearchBean);

    /**
     * 根据fk_type_key（业务类型关键字）和公司获取业务提供商下拉
     *
     * @param fkTypeKey   业务类型关键字
     * @param fkCompanyId 公司Id
     * @return
     */
    List<BusinessProviderSelectVo> getBusinessProviderSelect(String fkTypeKey, Long fkCompanyId);

    List<CompanyTreeVo> allCompany(Long id);

    List<CompanyTreeVo> getProviderCompanyRelation(Long providerId);

    void editProviderCompanyRelation(List<BusinessProviderDto> businessProviderDtos);
    /**
     * 根据fk_type_key（业务类型关键字）和公司获取业务提供商下拉
     *
     * @param companyId 公司ID
     * @param typeKey   业务类型关键字
     * @return
     */
    List<BaseSelectEntity> getBusinessProviderSelectByTypeKey(Long companyId, String typeKey);

    /**
     * 获取留学保险提供商可绑定的应收计划
     * @param targetId
     * @param receiptFormId
     * @return
     */
    List<BaseSelectEntity> getPlanIdsByBusinessProviderId(Long targetId, Long receiptFormId);

    /**
     * 获取留学保险提供商名称
     * @param ids
     * @return
     */
    Map<Long, String> getInsuranceProviderNameByIds(Set<Long> ids);
}

package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.dao.sale.AgentCompanyMapper;
import com.get.salecenter.dao.sale.AgentStaffMapper;
import com.get.salecenter.dao.sale.BusinessProviderMapper;
import com.get.salecenter.dao.sale.ClientMapper;
import com.get.salecenter.dao.sale.ClientSourceMapper;
import com.get.salecenter.dao.sale.RStudentToClientApprovalMapper;
import com.get.salecenter.dao.sale.StudentMapper;
import com.get.salecenter.dao.sale.StudentOfferItemMapper;
import com.get.salecenter.dao.sale.StudentServiceFeeMapper;
import com.get.salecenter.entity.StudentServiceFee;
import com.get.salecenter.vo.ClientSourceListVo;
import com.get.salecenter.vo.ClientSourceStudentVo;
import com.get.salecenter.entity.Agent;
import com.get.salecenter.entity.AgentCompany;
import com.get.salecenter.entity.AgentStaff;
import com.get.salecenter.entity.BusinessProvider;
import com.get.salecenter.entity.Client;
import com.get.salecenter.entity.ClientSource;
import com.get.salecenter.entity.RStudentToClientApproval;
import com.get.salecenter.entity.Student;
import com.get.salecenter.entity.StudentOfferItem;
import com.get.salecenter.service.IAgentService;
import com.get.salecenter.service.IClientSourceService;
import com.get.salecenter.strategy.ClientSourceStrategy;
import com.get.salecenter.dto.ClientSourceAddDto;
import com.get.salecenter.dto.ClientSourceListDto;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: Hardy
 * @create: 2023/12/12 12:02
 * @verison: 1.0
 * @description:
 */
@Service
public class ClientSourceServiceImpl implements IClientSourceService {

    @Resource
    private ClientSourceMapper clientSourceMapper;

    @Resource
    private IAgentService agentService;

    @Resource
    private StudentMapper studentMapper;

    @Resource
    private StudentOfferItemMapper studentOfferItemMapper;

    @Resource
    private UtilService utilService;

    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Resource
    private AgentStaffMapper agentStaffMapper;

    @Resource
    private BusinessProviderMapper businessProviderMapper;

    @Resource(name = "clientSourceStrategiesMap")
    private Map<String, ClientSourceStrategy> clientSourceStrategiesMap;
    @Resource
    private RStudentToClientApprovalMapper rStudentToClientApprovalMapper;
    @Resource
    private AgentCompanyMapper agentCompanyMapper;
    @Resource
    private ClientMapper clientMapper;
    @Autowired
    private StudentServiceFeeMapper studentServiceFeeMapper;

    @Override
    public List<ClientSourceListVo> getClientSources(ClientSourceListDto clientSourceListDto, Page page) {
        IPage<ClientSourceListVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<ClientSourceListVo> clientSourceListVos = clientSourceMapper.getClientSources(iPage, clientSourceListDto);
        page.setAll((int) iPage.getTotal());
        if (GeneralTool.isEmpty(clientSourceListVos)){
            return Collections.emptyList();
        }
        Set<Long> agentIds = clientSourceListVos.stream().map(ClientSourceListVo::getFkAgentId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Long, String> agentNamesByIds = agentService.getAgentNamesByIds(agentIds);
        Set<Long> staffIds = clientSourceListVos.stream().map(ClientSourceListVo::getFkStaffId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Long, String> staffNamesByIds = Maps.newHashMap();
        if(GeneralTool.isNotEmpty(staffIds)){
            staffNamesByIds = permissionCenterClient.getStaffNamesByIds(staffIds);
        }
        Set<Long> providerIds = clientSourceListVos.stream().filter(clientSourceListVo -> ProjectKeyEnum.CLIENT_SOURCE_TYPE_BUSINESS_PROVIDER.key.equals(clientSourceListVo.getFkTableName())).map(ClientSourceListVo::getFkTableId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Long, String> businessProviderNameMap = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(providerIds)){
            List<BusinessProvider> businessProviders = businessProviderMapper.selectBatchIds(providerIds);
            if (GeneralTool.isNotEmpty(businessProviders)){
                businessProviderNameMap = businessProviders.stream().collect(Collectors.toMap(BusinessProvider::getId, BusinessProvider::getName));
            }
        }

        for (ClientSourceListVo clientSourceListVo : clientSourceListVos) {
            clientSourceListVo.setAgentName(agentNamesByIds.get(clientSourceListVo.getFkAgentId()));
            clientSourceListVo.setSourceTypeName(ProjectKeyEnum.getInitialValue(clientSourceListVo.getFkTableName(),ProjectKeyEnum.CLIENT_SOURCE_TYPE));
            if (GeneralTool.isNotEmpty(clientSourceListVo.getFkStaffId())){
                clientSourceListVo.setBdName(staffNamesByIds.get(clientSourceListVo.getFkStaffId()));
            }
            if (ProjectKeyEnum.CLIENT_SOURCE_TYPE_BUSINESS_PROVIDER.key.equals(clientSourceListVo.getFkTableName())&&GeneralTool.isNotEmpty(clientSourceListVo.getFkTableId())){
                if(GeneralTool.isNotEmpty(businessProviderNameMap.get(clientSourceListVo.getFkTableId()))){
                    clientSourceListVo.setFkTableValue(businessProviderNameMap.get(clientSourceListVo.getFkTableId())+"（"+ clientSourceListVo.getFkTableValue()+"）");
                }
            }
        }
        return clientSourceListVos;
    }

    /**
     * 删除
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Long id) {
        ClientSource clientSource = clientSourceMapper.selectOne(Wrappers.<ClientSource>lambdaQuery().eq(ClientSource::getId, id));
        rStudentToClientApprovalMapper.delete(Wrappers.<RStudentToClientApproval>lambdaQuery()
                .eq(RStudentToClientApproval::getFkClientId, clientSource.getFkClientId()));
        clientSourceMapper.deleteById(id);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addClientSource(ClientSourceAddDto clientSourceAddDto) {
        //根据类型获取策略
        ClientSourceStrategy clientSourceStrategy = clientSourceStrategiesMap.get(clientSourceAddDto.getFkTableName());
        //若类型没有对应策略 则抛异常
        if (GeneralTool.isEmpty(clientSourceStrategy)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        clientSourceStrategy.addClientSource(clientSourceAddDto,this);
    }


    /**
     * bms推荐来源类型
     * @param clientSourceAddDto
     */
    @Override
    public void addBmsClientSource(ClientSourceAddDto clientSourceAddDto){
        String stuNum = clientSourceAddDto.getFkTableValue();
        List<Student> students = studentMapper.selectList(Wrappers.lambdaQuery(Student.class)
                .eq(Student::getNum, stuNum));
        if (GeneralTool.isEmpty(students)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("STUDENT_NOT_QUALIFIED"));
        }
        Long studentId = students.get(0).getId();
        //先删
        clientSourceMapper.delete(Wrappers.lambdaQuery(ClientSource.class)
                .eq(ClientSource::getFkClientId, clientSourceAddDto.getFkClientId())
        );
        Map<Long, List<ClientSourceStudentVo>> studentsMap = getStudentsMap(Lists.newArrayList(studentId),ProjectKeyEnum.CLIENT_SOURCE_TYPE_BMS.key);

        List<ClientSourceStudentVo> clientSourceStudentVos = studentsMap.get(studentId);
        if (GeneralTool.isNotEmpty(clientSourceStudentVos)){

            Map<Long, List<AgentStaff>> agnetStaffListMap = Maps.newHashMap();
            Set<Long> agentIds = clientSourceStudentVos.stream().map(ClientSourceStudentVo::getFkAgentId).collect(Collectors.toSet());
            if (GeneralTool.isNotEmpty(agentIds)){
                List<AgentStaff> agentStaffs = agentStaffMapper.selectList(Wrappers.lambdaQuery(AgentStaff.class)
                        .in(AgentStaff::getFkAgentId, agentIds)
                        .eq(AgentStaff::getIsActive, true));
                if (GeneralTool.isNotEmpty(agentStaffs)){
                    agnetStaffListMap = agentStaffs.stream().collect(Collectors.groupingBy(AgentStaff::getFkAgentId));
                }
            }
            List<ClientSource> clientSources = BeanCopyUtils.copyListProperties(clientSourceStudentVos, ClientSource::new);
            Client client = clientMapper.selectById(clientSourceAddDto.getFkClientId());
            Long fkCompanyId = client.getFkCompanyId();
            for (ClientSource clientSource : clientSources) {
                List<AgentStaff> agentStaffs = agnetStaffListMap.get(clientSource.getFkAgentId());
                clientSource.setFkClientId(clientSourceAddDto.getFkClientId());
                clientSource.setFkTableId(studentId);
                if (GeneralTool.isNotEmpty(agentStaffs)){
                    clientSource.setFkStaffId(agentStaffs.get(0).getFkStaffId());
                }
                utilService.setCreateInfo(clientSource);
                clientSourceMapper.insert(clientSource);
                // 增加共享代理
                List<AgentCompany> list = agentCompanyMapper.selectList(Wrappers.<AgentCompany>lambdaQuery()
                        .eq(AgentCompany::getFkAgentId, clientSource.getFkAgentId())
                        .eq(AgentCompany::getFkCompanyId, fkCompanyId));
                if (GeneralTool.isEmpty(list)) {
                    AgentCompany agentCompany = new AgentCompany();
                    agentCompany.setFkAgentId(clientSource.getFkAgentId());
                    agentCompany.setFkCompanyId(fkCompanyId);
                    utilService.setCreateInfo(agentCompany);
                    agentCompanyMapper.insert(agentCompany);
                }
            }
            // 获取审批人id
            Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.STUDENT_TO_CLIENT_APPROVER_DEFAULT.key, 1).getData();
            String configValue1 = companyConfigMap.get(SecureUtil.getFkCompanyId());
            Long approvalId = Long.valueOf(configValue1);

            // 增加默认审批的数据
            RStudentToClientApproval rStudentToClientApproval = new RStudentToClientApproval();
            rStudentToClientApproval.setFkCompanyId(fkCompanyId);
            rStudentToClientApproval.setFkStudentId(studentId);
            rStudentToClientApproval.setFkClientId(clientSourceAddDto.getFkClientId());
            rStudentToClientApproval.setFkStaffIdApply(SecureUtil.getStaffId());
            rStudentToClientApproval.setApprovalStatus(1);
            rStudentToClientApproval.setGmtModified(new Date());
            rStudentToClientApproval.setApprovalOpinion("同意");
            if (approvalId > 0) {
                rStudentToClientApproval.setFkStaffIdApproval(approvalId);
            }
            utilService.setCreateInfo(rStudentToClientApproval);
            rStudentToClientApprovalMapper.insert(rStudentToClientApproval);
        }else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("students_do_not_meet_the_requirements"));
        }
    }

    /**
     * crm推荐来源类型
     * @param clientSourceAddDto
     */
    @Override
    public void addCrmClientSource(ClientSourceAddDto clientSourceAddDto) {
        if (GeneralTool.isEmpty(clientSourceAddDto.getFkTableValue())&&GeneralTool.isEmpty(clientSourceAddDto.getFkAgentId())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }

        //先删
        clientSourceMapper.delete(Wrappers.lambdaQuery(ClientSource.class)
                .eq(ClientSource::getFkClientId, clientSourceAddDto.getFkClientId())
        );
        List<AgentStaff> agentStaffs = agentStaffMapper.selectList(Wrappers.lambdaQuery(AgentStaff.class)
                .eq(AgentStaff::getFkAgentId, clientSourceAddDto.getFkAgentId())
                .eq(AgentStaff::getIsActive, true));

        ClientSource clientSource = BeanCopyUtils.objClone(clientSourceAddDto, ClientSource::new);
        clientSource.setFkClientId(clientSourceAddDto.getFkClientId());
        clientSource.setFkTableId(null);
        clientSource.setFkAgentId(clientSourceAddDto.getFkAgentId());
        if (GeneralTool.isNotEmpty(agentStaffs)){
            clientSource.setFkStaffId(agentStaffs.get(0).getFkStaffId());
        }
        utilService.setCreateInfo(clientSource);
        clientSourceMapper.insert(clientSource);
    }

    /**
     * 渠道代理推荐来源类型
     * @param clientSourceAddDto
     */
    @Override
    @Transactional
    public void addAgentClientSource(ClientSourceAddDto clientSourceAddDto) {
        if (GeneralTool.isEmpty(clientSourceAddDto.getFkAgentId())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        Agent agent = agentService.getAgentById(clientSourceAddDto.getFkAgentId());
        if (GeneralTool.isEmpty(agent)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        //a. 判断这个学生编号是否存在学生
        //b. 判断这个学生是否只有服务费业务数据，若还有申请计划数据，属于不符合条件
        //c. 判断服务费所对应的代理是否和选择的渠道代理一致。否则属于不符合条件
        if (GeneralTool.isNotEmpty(clientSourceAddDto.getStudentNum())) {
            Student student = studentMapper.selectOne(Wrappers.<Student>lambdaQuery().eq(Student::getNum, clientSourceAddDto.getStudentNum().trim()));
            if (GeneralTool.isEmpty(student)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("STUDENT_NOT_QUALIFIED"));
            }
            List<StudentOfferItem> studentOfferItems = studentOfferItemMapper.selectList(Wrappers.<StudentOfferItem>lambdaQuery().eq(StudentOfferItem::getFkStudentId, student.getId()).eq(StudentOfferItem::getStatus, 1));
            if (GeneralTool.isNotEmpty(studentOfferItems)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("STUDENT_NOT_QUALIFIED"));
            }
            List<StudentServiceFee> studentServiceFees = studentServiceFeeMapper.selectList(Wrappers.<StudentServiceFee>lambdaQuery()
                    .eq(StudentServiceFee::getFkStudentId, student.getId())
                    .eq(StudentServiceFee::getFkAgentId, clientSourceAddDto.getFkAgentId())
                    .eq(StudentServiceFee::getStatus, 1));
            if (GeneralTool.isEmpty(studentServiceFees)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("STUDENT_NOT_QUALIFIED"));
            }

            // 获取审批人id
            Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.STUDENT_TO_CLIENT_APPROVER_DEFAULT.key, 1).getData();
            String configValue1 = companyConfigMap.get(SecureUtil.getFkCompanyId());
            Long approvalId = Long.valueOf(configValue1);
            Client client = clientMapper.selectById(clientSourceAddDto.getFkClientId());
            Long fkCompanyId = client.getFkCompanyId();
            // 增加默认审批的数据
            RStudentToClientApproval rStudentToClientApproval = new RStudentToClientApproval();
            rStudentToClientApproval.setFkCompanyId(fkCompanyId);
            rStudentToClientApproval.setFkStudentId(student.getId());
            rStudentToClientApproval.setFkClientId(clientSourceAddDto.getFkClientId());
            rStudentToClientApproval.setFkStaffIdApply(SecureUtil.getStaffId());
            rStudentToClientApproval.setApprovalStatus(1);
            rStudentToClientApproval.setGmtModified(new Date());
            rStudentToClientApproval.setApprovalOpinion("同意");
            if (approvalId > 0) {
                rStudentToClientApproval.setFkStaffIdApproval(approvalId);
            }
            utilService.setCreateInfo(rStudentToClientApproval);
            rStudentToClientApprovalMapper.insert(rStudentToClientApproval);

        }

        //先删
        clientSourceMapper.delete(Wrappers.lambdaQuery(ClientSource.class)
                .eq(ClientSource::getFkClientId, clientSourceAddDto.getFkClientId())
        );

        List<AgentStaff> agentStaffs = agentStaffMapper.selectList(Wrappers.lambdaQuery(AgentStaff.class)
                .eq(AgentStaff::getFkAgentId, clientSourceAddDto.getFkAgentId())
                .eq(AgentStaff::getIsActive, true));

        ClientSource clientSource = BeanCopyUtils.objClone(clientSourceAddDto, ClientSource::new);
        clientSource.setFkClientId(clientSourceAddDto.getFkClientId());
        clientSource.setFkTableId(clientSourceAddDto.getFkAgentId());
        clientSource.setFkAgentId(clientSourceAddDto.getFkAgentId());
        clientSource.setFkTableValue(clientSourceAddDto.getStudentNum());
        if (GeneralTool.isNotEmpty(agentStaffs)){
            clientSource.setFkStaffId(agentStaffs.get(0).getFkStaffId());
        }
        utilService.setCreateInfo(clientSource);
        clientSourceMapper.insert(clientSource);
    }

    /**
     * bms not os推荐来源类型
     * @param clientSourceAddDto
     */
    @Override
    public void addBmsNotOsClientSource(ClientSourceAddDto clientSourceAddDto) {
        String stuNum = clientSourceAddDto.getFkTableValue();
        List<Student> students = studentMapper.selectList(Wrappers.lambdaQuery(Student.class)
                .eq(Student::getNum, stuNum));
        if (GeneralTool.isEmpty(students)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("STUDENT_NOT_QUALIFIED"));
        }
        Long studentId = students.get(0).getId();
        //先删
        clientSourceMapper.delete(Wrappers.lambdaQuery(ClientSource.class)
                .eq(ClientSource::getFkClientId, clientSourceAddDto.getFkClientId())
        );

        Map<Long, List<ClientSourceStudentVo>> studentsMap = getStudentsMap(Lists.newArrayList(studentId),ProjectKeyEnum.CLIENT_SOURCE_TYPE_BMS_NOT_OS.key);

        List<ClientSourceStudentVo> clientSourceStudentVos = studentsMap.get(studentId);
        if (GeneralTool.isNotEmpty(clientSourceStudentVos)){

            Map<Long, List<AgentStaff>> agnetStaffListMap = Maps.newHashMap();
            Set<Long> agentIds = clientSourceStudentVos.stream().map(ClientSourceStudentVo::getFkAgentId).collect(Collectors.toSet());
            if (GeneralTool.isNotEmpty(agentIds)){
                List<AgentStaff> agentStaffs = agentStaffMapper.selectList(Wrappers.lambdaQuery(AgentStaff.class)
                        .in(AgentStaff::getFkAgentId, agentIds)
                        .eq(AgentStaff::getIsActive, true));
                if (GeneralTool.isNotEmpty(agentStaffs)){
                    agnetStaffListMap = agentStaffs.stream().collect(Collectors.groupingBy(AgentStaff::getFkAgentId));
                }
            }
            List<ClientSource> clientSources = BeanCopyUtils.copyListProperties(clientSourceStudentVos, ClientSource::new);
            Client client = clientMapper.selectById(clientSourceAddDto.getFkClientId());
            Long fkCompanyId = client.getFkCompanyId();
            for (ClientSource clientSource : clientSources) {
                List<AgentStaff> agentStaffs = agnetStaffListMap.get(clientSource.getFkAgentId());
                clientSource.setFkClientId(clientSourceAddDto.getFkClientId());
                clientSource.setFkTableId(studentId);
                if (GeneralTool.isNotEmpty(agentStaffs)){
                    clientSource.setFkStaffId(agentStaffs.get(0).getFkStaffId());
                }
                utilService.setCreateInfo(clientSource);
                clientSourceMapper.insert(clientSource);
                // 增加共享代理
                List<AgentCompany> list = agentCompanyMapper.selectList(Wrappers.<AgentCompany>lambdaQuery()
                        .eq(AgentCompany::getFkAgentId, clientSource.getFkAgentId())
                        .eq(AgentCompany::getFkCompanyId, fkCompanyId));
                if (GeneralTool.isEmpty(list)) {
                    AgentCompany agentCompany = new AgentCompany();
                    agentCompany.setFkAgentId(clientSource.getFkAgentId());
                    agentCompany.setFkCompanyId(fkCompanyId);
                    utilService.setCreateInfo(agentCompany);
                    agentCompanyMapper.insert(agentCompany);
                }
            }
            // 获取审批人id
            Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.STUDENT_TO_CLIENT_APPROVER_DEFAULT.key, 1).getData();
            String configValue1 = companyConfigMap.get(SecureUtil.getFkCompanyId());
            Long approvalId = Long.valueOf(configValue1);

            // 增加默认审批的数据
            RStudentToClientApproval rStudentToClientApproval = new RStudentToClientApproval();
            rStudentToClientApproval.setFkCompanyId(fkCompanyId);
            rStudentToClientApproval.setFkStudentId(studentId);
            rStudentToClientApproval.setFkClientId(clientSourceAddDto.getFkClientId());
            rStudentToClientApproval.setFkStaffIdApply(SecureUtil.getStaffId());
            rStudentToClientApproval.setApprovalStatus(1);
            rStudentToClientApproval.setGmtModified(new Date());
            rStudentToClientApproval.setApprovalOpinion("同意");
            if (approvalId > 0) {
                rStudentToClientApproval.setFkStaffIdApproval(approvalId);
            }
            utilService.setCreateInfo(rStudentToClientApproval);
            rStudentToClientApprovalMapper.insert(rStudentToClientApproval);
        }else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("students_do_not_meet_the_requirements"));
        }
    }

    /**
     * 市场部 推荐来源类型
     * @param clientSourceAddDto
     */
    @Override
    public void addMarketingDpClientSource(ClientSourceAddDto clientSourceAddDto) {
        if (GeneralTool.isEmpty(clientSourceAddDto.getFkTableValue())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }

        //先删
        clientSourceMapper.delete(Wrappers.lambdaQuery(ClientSource.class)
                .eq(ClientSource::getFkClientId, clientSourceAddDto.getFkClientId())
        );
        ClientSource clientSource = BeanCopyUtils.objClone(clientSourceAddDto, ClientSource::new);
        assert clientSource != null;
        clientSource.setFkClientId(clientSourceAddDto.getFkClientId());
        clientSource.setFkTableId(null);
        clientSource.setFkAgentId(null);
        utilService.setCreateInfo(clientSource);
        clientSourceMapper.insert(clientSource);
    }

    /**
     * 同学介绍 推荐来源类型
     * @param clientSourceAddDto
     */
    @Override
    public void addClassmatesClientSource(ClientSourceAddDto clientSourceAddDto) {
        if (GeneralTool.isEmpty(clientSourceAddDto.getFkTableValue())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        //先删
        clientSourceMapper.delete(Wrappers.lambdaQuery(ClientSource.class)
                .eq(ClientSource::getFkClientId, clientSourceAddDto.getFkClientId())
        );
        ClientSource clientSource = BeanCopyUtils.objClone(clientSourceAddDto, ClientSource::new);
        assert clientSource != null;
        clientSource.setFkClientId(clientSourceAddDto.getFkClientId());
        clientSource.setFkTableId(null);
        clientSource.setFkAgentId(null);
        utilService.setCreateInfo(clientSource);
        clientSourceMapper.insert(clientSource);
    }

    /**
     * 其他 推荐来源类型
     * @param clientSourceAddDto
     */
    @Override
    public void addOtherClientSource(ClientSourceAddDto clientSourceAddDto) {
        if (GeneralTool.isEmpty(clientSourceAddDto.getFkTableValue())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        //先删
        clientSourceMapper.delete(Wrappers.lambdaQuery(ClientSource.class)
                .eq(ClientSource::getFkClientId, clientSourceAddDto.getFkClientId())
        );
        ClientSource clientSource = BeanCopyUtils.objClone(clientSourceAddDto, ClientSource::new);
        assert clientSource != null;
        clientSource.setFkClientId(clientSourceAddDto.getFkClientId());
        clientSource.setFkTableId(null);
        clientSource.setFkAgentId(null);
        utilService.setCreateInfo(clientSource);
        clientSourceMapper.insert(clientSource);
    }

    /**
     * 本地市场来源 推荐来源类型
     * @param clientSourceAddDto
     */
    @Override
    public void addBusinessProviderClientSource(ClientSourceAddDto clientSourceAddDto) {
        if (GeneralTool.isEmpty(clientSourceAddDto.getFkTableValue())||GeneralTool.isEmpty(clientSourceAddDto.getFkTableId())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        //先删
        clientSourceMapper.delete(Wrappers.lambdaQuery(ClientSource.class)
                .eq(ClientSource::getFkClientId, clientSourceAddDto.getFkClientId())
        );
        ClientSource clientSource = BeanCopyUtils.objClone(clientSourceAddDto, ClientSource::new);
        assert clientSource != null;
        clientSource.setFkClientId(clientSourceAddDto.getFkClientId());
        clientSource.setFkAgentId(null);
        utilService.setCreateInfo(clientSource);
        clientSourceMapper.insert(clientSource);
    }

    /**
     * 验证学生资源来源
     * @param studentNum
     * @return
     */
    @Override
    public Boolean validateClientSource(String studentNum) {
        List<ClientSource> clientSources = clientSourceMapper.selectList(Wrappers.lambdaQuery(ClientSource.class)
                .eq(ClientSource::getFkTableName, "bms_student_num")
                .eq(ClientSource::getFkTableValue, studentNum)
        );
        return GeneralTool.isNotEmpty(clientSources);
    }


    /**
     * 获取符合条件的学生资源
     * @param studentIds
     * @return
     */
    private Map<Long,List<ClientSourceStudentVo>> getStudentsMap(List<Long> studentIds, String typeKey){
        Map<Long,List<ClientSourceStudentVo>> result = Maps.newHashMap();
        List<Student> students = studentMapper.selectBatchIds(studentIds);
        if (GeneralTool.isEmpty(students)){
            return Collections.emptyMap();
        }
        Map<Long, Student> studentMap = students.stream().collect(Collectors.toMap(Student::getId, Function.identity()));

        LambdaQueryWrapper<StudentOfferItem> wrapper = Wrappers.lambdaQuery(StudentOfferItem.class);
        wrapper.in(StudentOfferItem::getFkAreaCountryId, SecureUtil.getCountryIds());
//        if (GeneralTool.isNotEmpty(typeKey)&&ProjectKeyEnum.CLIENT_SOURCE_TYPE_BMS.key.equals(typeKey)){
//            List<Long> stepIds = Lists.newArrayList(6L,7L,8L,10L);
//            wrapper.in(StudentOfferItem::getFkStudentOfferItemStepId, stepIds);
//        }
        List<StudentOfferItem> studentOfferItems = studentOfferItemMapper.selectList(wrapper
                .in(StudentOfferItem::getFkStudentId, studentIds)
                .eq(StudentOfferItem::getStatus, 1)
        );

        if (GeneralTool.isEmpty(studentOfferItems)){
            return Collections.emptyMap();
        }
        Map<Long, List<StudentOfferItem>> studentOfferItemsMap = studentOfferItems.stream().collect(Collectors.groupingBy(StudentOfferItem::getFkStudentId));

        if (GeneralTool.isNotEmpty(studentOfferItemsMap)){

            for (Map.Entry<Long, List<StudentOfferItem>> entry : studentOfferItemsMap.entrySet()) {
                Long studentId = entry.getKey();
                Student student = studentMap.get(studentId);
                String studentNum = student.getNum();

                List<ClientSourceStudentVo> clientSourceStudentVos = Lists.newArrayList();
                List<StudentOfferItem> offerItems = entry.getValue();
                Map<Long, List<StudentOfferItem>> offerItemGroupByAgent = offerItems.stream().collect(Collectors.groupingBy(StudentOfferItem::getFkAgentId));
                for (Map.Entry<Long, List<StudentOfferItem>> offerItemByAgentEntry : offerItemGroupByAgent.entrySet()) {
                    ClientSourceStudentVo clientSourceStudentVo = new ClientSourceStudentVo();
                    clientSourceStudentVo.setFkAgentId(offerItemByAgentEntry.getKey());
                    clientSourceStudentVo.setFkTableName(typeKey);
                    clientSourceStudentVo.setFkTableValue(studentNum);
                    clientSourceStudentVos.add(clientSourceStudentVo);
                }
                result.put(studentId, clientSourceStudentVos);
            }

        }
        return result;
    }



}

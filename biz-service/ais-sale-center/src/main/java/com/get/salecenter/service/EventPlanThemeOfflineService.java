package com.get.salecenter.service;

import com.get.core.mybatis.base.BaseService;
import com.get.core.mybatis.utils.ValidList;
import com.get.salecenter.vo.EventPlanThemeOfflineVo;
import com.get.salecenter.entity.EventPlanThemeOffline;
import com.get.salecenter.dto.EventPlanThemeOfflineDto;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12
 */
public interface EventPlanThemeOfflineService extends BaseService<EventPlanThemeOffline> {

    /**
     * 列表数据
     * <AUTHOR>
     * @DateTime 2023/12/14 10:43
     */
    List<EventPlanThemeOfflineVo> getEventPlanThemeOfflines(Long fkEventPlanId);

    /**
     * 根据主题获取线下活动列表数据
     * <AUTHOR>
     * @DateTime 2023/12/14 17:56
     */
    List<EventPlanThemeOfflineVo> getOfflinesByThemeId(Long fkEventPlanThemeId);

    /**
     * 批量新增
     * <AUTHOR>
     * @DateTime 2023/12/14 16:33
     */
    void batchAdd(ValidList<EventPlanThemeOfflineDto> offlineVos);

    /**
     * 详情
     * <AUTHOR>
     * @DateTime 2023/12/19 11:35
     */
    EventPlanThemeOfflineVo findOfflineById(Long id);

    /**
     * 激活
     * <AUTHOR>
     * @DateTime 2023/12/25 15:56
     */
    void activate(EventPlanThemeOfflineDto offlineVo);

    /**
     * 新增
     * <AUTHOR>
     * @DateTime 2023/12/18 11:24
     */
    Long addEventPlanThemeOffline(EventPlanThemeOfflineDto offlineVo);

    /**
     * 修改
     * <AUTHOR>
     * @DateTime 2023/12/19 11:28
     */
    EventPlanThemeOfflineVo updateOffline(EventPlanThemeOfflineDto offlineVo);


    /**
     * 删除
     * <AUTHOR>
     * @DateTime 2023/12/14 10:58
     */
    void delete(Long id);


    /**
     * 移动
     * <AUTHOR>
     * @DateTime 2023/12/14 11:02
     */
    void movingOrder(Long fkEventPlanThemeId,Integer start,Integer end);


}

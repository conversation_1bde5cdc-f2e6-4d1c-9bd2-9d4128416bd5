package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.salecenter.vo.AgentEventTypeVo;
import com.get.salecenter.dto.AgentEventTypeDto;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/10/19
 * @TIME: 15:26
 * @Description:
 **/
public interface IAgentEventTypeService {

    /**
     * @return java.util.List<com.get.salecenter.vo.AgentEventTypeVo>
     * @Description: 查询所有事件类型（下拉框）
     * @Param []
     * <AUTHOR>
     */
    List<AgentEventTypeVo> getAllEventType();


    /**
     * @return java.lang.Long
     * @Description: 新增事件
     * @Param [agentEventTypeDto]
     * <AUTHOR>
     */
    void addAgentEventType(List<AgentEventTypeDto> agentEventTypeDto);

    /**
     * @return java.util.List<com.get.salecenter.vo.AgentEventTypeVo>
     * @Description: 查询所有事件
     * @Param [agentEventTypeDto, page]
     * <AUTHOR>
     */
    List<AgentEventTypeVo> getAgentEventTypeDtos(AgentEventTypeDto agentEventTypeDto, Page page);


    /**
     * @return com.get.salecenter.vo.AgentEventTypeVo
     * @Description: 修改
     * @Param [agentEventTypeDto]
     * <AUTHOR>
     */
    AgentEventTypeVo updateAgentEventType(AgentEventTypeDto agentEventTypeDto);

    /**
     * @return void
     * @Description: 删除
     * @Param [id]
     * <AUTHOR>
     */
    void deleteAgentEventType(Long id);

    /**
     * 查询byID
     *
     * @param id
     * @return
     * @
     */
    AgentEventTypeVo findAgentEventTypeById(Long id);


    /**
     * @return void
     * @Description: 上移下移
     * @Param [areaCountryVos]
     * <AUTHOR>
     */
    void movingOrder(List<AgentEventTypeDto> agentEventTypeDtoList);


}

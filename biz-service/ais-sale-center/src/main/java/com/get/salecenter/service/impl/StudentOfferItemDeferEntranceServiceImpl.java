package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.StudentOfferItemDeferEntranceTimeMapper;
import com.get.salecenter.entity.StudentOfferItemDeferEntranceTime;
import com.get.salecenter.service.StudentOfferItemDeferEntranceService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class StudentOfferItemDeferEntranceServiceImpl implements StudentOfferItemDeferEntranceService {

    @Resource
    private StudentOfferItemDeferEntranceTimeMapper studentOfferItemDeferEntranceTimeMapper;


    @Override
    public Map<Long, Date> getMaxDeferEntranceTimesMap(Set<Long> ids) {
        Map<Long, Date> maxDeferEntranceTimesMap = new HashMap<>();
        if (GeneralTool.isEmpty(ids)){
            return maxDeferEntranceTimesMap;
        }
        List<StudentOfferItemDeferEntranceTime> studentOfferItemDeferEntranceTimes = studentOfferItemDeferEntranceTimeMapper.selectList(new LambdaQueryWrapper<StudentOfferItemDeferEntranceTime>().in(StudentOfferItemDeferEntranceTime::getFkStudentOfferItemId, ids));
        if (GeneralTool.isNotEmpty(studentOfferItemDeferEntranceTimes)){
            maxDeferEntranceTimesMap = studentOfferItemDeferEntranceTimes.stream().collect(Collectors.toMap(StudentOfferItemDeferEntranceTime::getFkStudentOfferItemId,StudentOfferItemDeferEntranceTime::getDeferEntranceTime,(a1, a2) -> a1.compareTo(a2) > 0 ? a1 : a2));
        }
        return maxDeferEntranceTimesMap;
    }
}

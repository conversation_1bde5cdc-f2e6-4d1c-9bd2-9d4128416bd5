package com.get.salecenter.controller;


import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.vo.BusinessProviderTypeVo;
import com.get.salecenter.service.BusinessProviderTypeService;
import com.get.salecenter.dto.BusinessProviderTypeListDto;
import com.get.salecenter.dto.BusinessProviderTypeDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "业务提供商类型管理")
@RestController
@RequestMapping("sale/businessProviderType")
public class BusinessProviderTypeController {

    @Resource
    private BusinessProviderTypeService businessProviderTypeService;

    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/业务提供商类型管理/查询")
    @PostMapping("datas")
    public ListResponseBo<BusinessProviderTypeVo> datas(@RequestBody SearchBean<BusinessProviderTypeListDto> page) {
        List<BusinessProviderTypeVo> datas = businessProviderTypeService.getBusinessProviderTypeDtos(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/业务提供商类型管理/新增")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody @Validated(BusinessProviderTypeDto.Add.class) ValidList<BusinessProviderTypeDto> businessProviderTypeDto) {
        businessProviderTypeService.addBusinessProviderType(businessProviderTypeDto);
        return ResponseBo.ok();
    }



    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/业务提供商类型管理/更新")
    @PostMapping("update")
    public ResponseBo<BusinessProviderTypeVo> update(@RequestBody @Validated(BusinessProviderTypeDto.Update.class) BusinessProviderTypeDto businessProviderTypeDto) {
        return UpdateResponseBo.ok(businessProviderTypeService.updateBusinessProviderType(businessProviderTypeDto));
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description:删除信息
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/业务提供商类型管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        businessProviderTypeService.deleteBusinessProviderType(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description:上移下移
     * @Param [enrolFailureReasonVoList]
     * <AUTHOR>
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/业务提供商类型管理/排序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<BusinessProviderTypeDto> businessProviderTypeDto) {
        businessProviderTypeService.movingOrder(businessProviderTypeDto);
        return ResponseBo.ok();
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "业务提供商类型下拉", notes = "")
    @PostMapping("selectBusinessProviderType")
    public ResponseBo<BaseSelectEntity> selectBusinessProviderType() {
        return new ListResponseBo<>(businessProviderTypeService.selectBusinessProviderType());
    }
}

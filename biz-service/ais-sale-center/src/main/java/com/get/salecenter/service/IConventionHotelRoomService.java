package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.salecenter.vo.ConventionHotelRoomVo;
import com.get.salecenter.dto.ConventionHotelRoomDto;

import java.util.List;

/**
 * @author: Sea
 * @create: 2020/8/18 11:39
 * @verison: 1.0
 * @description: 酒店房间管理接口
 */
public interface IConventionHotelRoomService {

    /**
     * 详情
     *
     * @param id
     * @return
     */
    ConventionHotelRoomVo findConventionHotelRoomById(Long id);

    /**
     * 新增
     *
     * @param conventionHotelRoomDto
     */
    Long addConventionHotelRoom(ConventionHotelRoomDto conventionHotelRoomDto);

    /**
     * 批量新增
     *
     * @param conventionHotelRoomDto
     */
    void batchAdd(ConventionHotelRoomDto conventionHotelRoomDto);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 列表
     *
     * @param conventionHotelRoomDto
     * @param page
     * @return
     */
    List<ConventionHotelRoomVo> getConventionHotelRooms(ConventionHotelRoomDto conventionHotelRoomDto, Page page);

    /**
     * 通过酒店房型id 查找对应房间ids
     *
     * @param conventionHotelId
     * @param systemRoomNum
     */
    List<Long> getRoomIdsByHotelId(Long conventionHotelId, String systemRoomNum);

    /**
     * 获取入住时间集合
     *
     * @param conventionHotelIds
     * @return
     */
    List<String> getDates(List<Long> conventionHotelIds);

    /**
     * 根据房型id 查出分类后所有该房型id开得房间信息(可按条件查找)
     *
     * @param conventionHotelIds
     * @param conventionHotelRoomDto
     * @return
     */
    List<ConventionHotelRoomVo> getConventionHotelRoomDtoList(List<Long> conventionHotelIds, ConventionHotelRoomDto conventionHotelRoomDto, List<String> bdCodes, Long companyId);

    /**
     * 修改酒店房号（管理员）
     *
     * @param conventionHotelRoomDto
     */
    void updateHotelRoomNum(ConventionHotelRoomDto conventionHotelRoomDto);

    /**
     * 修改酒店房号（非管理员）
     *
     * @param conventionHotelRoomDto
     */
    void updateHotelRoomNumLimited(ConventionHotelRoomDto conventionHotelRoomDto);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :通过日期和酒店id查找房间ids
     * @Param [id, date]
     * <AUTHOR>
     */
    List<Long> getRoomIds(Long id, String date);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :根据房型ids（可选条件-日期） 查找所有房型所开得房间id
     * @Param [conventionHotelIds, roomDate]
     * <AUTHOR>
     */
    List<Long> getAllRoomIds(List<Long> conventionHotelIds, String roomDate);

    /**
     * @return Long
     * @Description :新增单个房间
     * @Param [conventionHotelRoomDto]
     * <AUTHOR>
     */
    Long addSingleConventionHotelRoom(ConventionHotelRoomDto conventionHotelRoomDto);

}

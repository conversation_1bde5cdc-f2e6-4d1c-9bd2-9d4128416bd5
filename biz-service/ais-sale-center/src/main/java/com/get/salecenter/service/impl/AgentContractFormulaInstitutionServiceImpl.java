package com.get.salecenter.service.impl;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.salecenter.dao.sale.AgentContractFormulaInstitutionMapper;
import com.get.salecenter.entity.AgentContractFormulaInstitution;
import com.get.salecenter.service.IAgentContractFormulaInstitutionService;
import com.get.salecenter.dto.AgentContractFormulaInstitutionDto;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * @author: Sea
 * @create: 2021/4/22 17:59
 * @verison: 1.0
 * @description:
 */
@Service
public class AgentContractFormulaInstitutionServiceImpl implements IAgentContractFormulaInstitutionService {
    @Resource
    private AgentContractFormulaInstitutionMapper agentContractFormulaInstitutionMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;

    @Override
    public Long addAgentContractFormulaInstitution(AgentContractFormulaInstitutionDto agentContractFormulaInstitutionDto) {
        AgentContractFormulaInstitution agentContractFormulaInstitution = BeanCopyUtils.objClone(agentContractFormulaInstitutionDto, AgentContractFormulaInstitution::new);
        utilService.updateUserInfoToEntity(agentContractFormulaInstitution);
        agentContractFormulaInstitutionMapper.insertSelective(agentContractFormulaInstitution);
        return agentContractFormulaInstitution.getId();
    }

    @Override
    public void deleteByFkid(Long agentContractFormulaId) {
//        Example example = new Example(AgentContractFormulaInstitution.class);
//        example.createCriteria().andEqualTo("fkAgentContractFormulaId", agentContractFormulaId);
//        agentContractFormulaInstitutionMapper.deleteByExample(example);

        agentContractFormulaInstitutionMapper.delete(Wrappers.<AgentContractFormulaInstitution>lambdaQuery().eq(AgentContractFormulaInstitution::getFkAgentContractFormulaId, agentContractFormulaId));
    }

    @Override
    public Map<Long, String> getInstitutionNameMapByFkids(List<Long> agentContractFormulaIds) {
        //关系map
        Map<Long, List<Long>> idMap = new HashMap<>();
        Map<Long, String> nameMap = new HashMap<>();
        //全部institutionId集合
        Set<Long> institutionIdSet = new HashSet<>();
        for (Long agentContractFormulaId : agentContractFormulaIds) {
            //通过agentContractFormulaId获取对应所有学校id
            List<Long> institutionIds = getInstitutionIdListByFkid(agentContractFormulaId);
            institutionIdSet.addAll(institutionIds);
            //agentContractFormulaId和institutionIds一一对应关系map
            idMap.put(agentContractFormulaId, institutionIds);
        }
        institutionIdSet.removeIf(Objects::isNull);
        //feign调用一次查出 institutionId和institutionName对应关系map
        Map<Long, String> institutionNameMap = new HashMap<>();
        Result<Map<Long, String>> result = institutionCenterClient.getInstitutionNamesByIds(institutionIdSet);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            institutionNameMap = result.getData();
            //map由agentContractFormulaId 对应 institutionIds 转成 agentContractFormulaId 对应 institutionNames
            for (Map.Entry<Long, List<Long>> agentContractFormula : idMap.entrySet()) {
                List<String> institutionNames = new ArrayList<>();
                List<Long> institutionIds = agentContractFormula.getValue();
                for (Long institutionId : institutionIds) {
                    institutionNames.add(institutionNameMap.get(institutionId));
                }
                nameMap.put(agentContractFormula.getKey(), StringUtils.join(institutionNames, "，"));
            }
        }
        return nameMap;
    }

    @Override
    public List<Long> getInstitutionIdListByFkid(Long agentContractFormulaId) {
        return agentContractFormulaInstitutionMapper.getInstitutionIdsByFkid(agentContractFormulaId);
    }
}

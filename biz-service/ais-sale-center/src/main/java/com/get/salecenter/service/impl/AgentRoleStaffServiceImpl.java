package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.service.impl.GetServiceImpl;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.dao.sale.AgentMapper;
import com.get.salecenter.dao.sale.AgentRoleStaffMapper;
import com.get.salecenter.dao.sale.AgentStaffMapper;
import com.get.salecenter.dao.sale.StudentProjectRoleMapper;
import com.get.salecenter.vo.AgentRoleStaffVo;
import com.get.salecenter.vo.AgentVo;
import com.get.salecenter.vo.StaffBdCodeVo;
import com.get.salecenter.entity.Agent;
import com.get.salecenter.entity.AgentRoleStaff;
import com.get.salecenter.entity.StudentProjectRole;
import com.get.salecenter.service.AgentRoleStaffService;
import com.get.salecenter.service.IStudentProjectRoleService;
import com.get.salecenter.dto.AgentRoleStaffDto;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.StringJoiner;
import java.util.stream.Collectors;

/**
 * 代理项目成员配置管理逻辑处理类
 *
 * <AUTHOR>
 * @date 2021/8/2 11:14
 */
@Service
public class AgentRoleStaffServiceImpl extends GetServiceImpl<AgentRoleStaffMapper,AgentRoleStaff> implements AgentRoleStaffService {

    @Resource
    private AgentRoleStaffMapper agentRoleStaffMapper;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private IStudentProjectRoleService studentProjectRoleService;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    private UtilService utilService;
    @Resource
    private AgentStaffMapper agentStaffMapper;
    @Resource
    private StudentProjectRoleMapper studentProjectRoleMapper;
    @Resource
    private AgentMapper agentMapper;


    /**
     * 代理项目成员配置管理列表
     *
     * @Date 11:35 2021/8/2
     * <AUTHOR>
     */
    @Override
    public List<AgentVo> getAgentRoleStaffs(AgentRoleStaffDto agentRoleStaffDto, Page page) {
        if (!SecureUtil.validateCompany(agentRoleStaffDto.getFkCompanyId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
        }
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        List<AgentVo> agentVos = agentRoleStaffMapper.getAgentRoleStaffs(agentRoleStaffDto);
//        page.restPage(agentVos);
        IPage<AgentVo> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<AgentVo> agentVos = agentRoleStaffMapper.getAgentRoleStaffs(pages, agentRoleStaffDto);
        page.setAll((int) pages.getTotal());

        List<Long> agentCountryIds = agentRoleStaffMapper.getCountryIds();
        agentCountryIds.removeIf(Objects::isNull);

        Set<Long> companyIds = new HashSet<>();
        Set<Long> staffIds = new HashSet<>();
        //国家ids
        Set<Long> countryIds = agentVos.stream().map(AgentVo::getFkAreaCountryId).collect(Collectors.toSet());
        //州省ids
        Set<Long> stateIds = agentVos.stream().map(AgentVo::getFkAreaStateId).collect(Collectors.toSet());
        //城市ids
        Set<Long> cityIds = agentVos.stream().map(AgentVo::getFkAreaCityId).collect(Collectors.toSet());
        countryIds.addAll(agentCountryIds);
        //bd员工ids
        Set<Long> bdStaffIds = agentVos.stream().map(AgentVo::getFkStaffId).collect(Collectors.toSet());
        Map<Long, String> bdNamesMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(bdStaffIds)) {
//            bdNamesMap = permissionCenterClient.getStaffNameMap(bdStaffIds);
            Result<Map<Long, String>> staffNameResult = permissionCenterClient.getStaffNameMap(bdStaffIds);
            if (staffNameResult.isSuccess() && GeneralTool.isNotEmpty(staffNameResult)) {
                bdNamesMap = staffNameResult.getData();
            }
        }

//        根据国家ids获取国家名称
        Map<Long, String> countryNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(countryIds)) {
//            countryNamesByIds =
            Result<Map<Long, String>> result = institutionCenterClient.getCountryNamesByIds(countryIds);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                countryNamesByIds = result.getData();
            }
        }
        //根据州省ids获取州省名称
        Map<Long, String> stateNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(stateIds)) {
            Result<Map<Long, String>> result = institutionCenterClient.getStateFullNamesByIds(stateIds);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                stateNamesByIds = result.getData();
            }
        }
        //根据州省ids获取州省名称
        Map<Long, String> cityNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(cityIds)) {
            Result<Map<Long, String>> result = institutionCenterClient.getCityFullNamesByIds(cityIds);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                cityNamesByIds = result.getData();
            }
        }


//        以下不处理
//        Set<Long> agentIds = agentVos.stream().map(AgentVo::getId).collect(Collectors.toSet());
//        if (GeneralTool.isNotEmpty(GeneralTool.isNotEmpty(agentIds))) {
//            Example example = new Example(AgentStaff.class);
//            example.createCriteria().andIn("fkAgentId", agentIds);
//            List<AgentStaff> agentStaffList = agentStaffMapper.selectByExample(example);
//
//        }
        Map<Object, Object> typeKeyNamesByTypeKey = new HashMap<>();
        List<Map<String, Object>> maps = TableEnum.enumsTranslation2Arrays(TableEnum.BUSINESS_TYPE);
        for (Map<String, Object> map : maps) {
            typeKeyNamesByTypeKey.put(map.get("key"), map.get("value"));
        }


        for (AgentVo agentVo : agentVos) {
            if (GeneralTool.isNotEmpty(agentVo.getCompanyIdStr())) {
                String[] split = agentVo.getCompanyIdStr().split(",");
                for (String companyId : split) {
                    companyIds.add(Long.valueOf(companyId));
                }
                if (GeneralTool.isNotEmpty(agentVo.getRoleStaffStr())) {
                    String[] roleStaffs = agentVo.getRoleStaffStr().split(",");
                    for (String roleStaffStr : roleStaffs) {
                        split = roleStaffStr.split("-");
                        staffIds.add(Long.valueOf(split[1]));
                    }
                }
            }
            agentVo.setFkAgentId(agentVo.getId());
        }
        Map<Long, String> companyNameMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(countryIds)) {
//            companyNameMap = permissionCenterClient.getCompanyNamesByIds(companyIds);
            Result<Map<Long, String>> result = permissionCenterClient.getCompanyNamesByIds(companyIds);
            if (result.isSuccess() && result.getData() != null) {
                companyNameMap = result.getData();
            }
        }
        Map<Long, String> staffNamesMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(staffIds)) {
//            staffNamesMap = permissionCenterClient.getStaffNameMap(staffIds);
            Result<Map<Long, String>> result = permissionCenterClient.getStaffNameMap(staffIds);
            if (result.isSuccess() && result.getData() != null) {
                staffNamesMap = result.getData();
            }
        }
        Map<Long, String> allRoleName = studentProjectRoleService.getAllRoleName();
        for (AgentVo agentVo : agentVos) {
            if (GeneralTool.isNotEmpty(agentVo.getCompanyIdStr())) {
                StringJoiner stringJoiner = new StringJoiner(",");
                for (String companyId : agentVo.getCompanyIdStr().split(",")) {
                    String companyName = companyNameMap.get(Long.valueOf(companyId));
                    stringJoiner.add(companyName);
                }
                agentVo.setCompanyName(stringJoiner.toString());
            }
            if (GeneralTool.isNotEmpty(agentVo.getRoleStaffStr())) {
                List<AgentRoleStaffVo> agentRoleStaffVoLis = new ArrayList<>();
                List<AgentRoleStaffVo> agentRoleStaffVoSorted = new ArrayList<>();
                String[] roleStaffs = agentVo.getRoleStaffStr().split(",");
                for (String roleStaffStr : roleStaffs) {
                    AgentRoleStaffVo agentRoleStaffVo = new AgentRoleStaffVo();
                    String[] split = roleStaffStr.split("-");
                    //角色id
                    String roleId = split[0];
                    //员工id
                    String staffId = split[1];
                    //角色viewOrder
                    String roleViewOrder = split[2];
                    if (split.length >= 4) {
                        String companyId = split[3];
                        agentRoleStaffVo.setAreaCountryName(countryNamesByIds.get(Long.valueOf(companyId)));
                        agentRoleStaffVo.setFkAreaCountryId(Long.valueOf(companyId));
                    } else {
                        agentRoleStaffVo.setFkAreaCountryId(0L);
                    }
                    String typeKey = split[4];
                    //按业务类型过滤
                    if (GeneralTool.isNotEmpty(agentRoleStaffDto.getFkTypeKey())) {
                        if (!typeKey.equals(agentRoleStaffDto.getFkTypeKey())) {
                            continue;
                        }
                    }
                    agentRoleStaffVo.setFkStudentProjectRoleName(allRoleName.get(Long.valueOf(roleId)));
                    agentRoleStaffVo.setFkStudentProjectRoleId(Long.valueOf(roleId));
                    agentRoleStaffVo.setFkStaffName(staffNamesMap.get(Long.valueOf(staffId)));
                    agentRoleStaffVo.setRoleViewOrder(Integer.valueOf(roleViewOrder));
                    agentRoleStaffVo.setFkTypeKeyName((String) typeKeyNamesByTypeKey.get(typeKey));
                    agentRoleStaffVoLis.add(agentRoleStaffVo);
                    //先按roleId排序再按国家id排序
                    agentRoleStaffVoSorted = agentRoleStaffVoLis.stream().sorted(Comparator.comparing(AgentRoleStaffVo::getRoleViewOrder).reversed().thenComparing(AgentRoleStaffVo::getFkAreaCountryId)).collect(Collectors.toList());

                }
                agentVo.setAgentRoleStaffDtoList(agentRoleStaffVoSorted);
            }
            if (GeneralTool.isNotEmpty(agentVo.getFkAreaCountryId())) {
                //设置国家名称
                agentVo.setCountryName(countryNamesByIds.get(agentVo.getFkAreaCountryId()));
            }
            if (GeneralTool.isNotEmpty(agentVo.getFkAreaStateId())) {
                //设置州省名称
                agentVo.setStateName(stateNamesByIds.get(agentVo.getFkAreaStateId()));
            }
            if (GeneralTool.isNotEmpty(agentVo.getFkAreaCityId())) {
                //设置城市名称
                agentVo.setCityName(cityNamesByIds.get(agentVo.getFkAreaCityId()));
            }
//            以下不处理
//            if (GeneralTool.isNotEmpty(bdNamesMap) && GeneralTool.isNotEmpty(agentVo.getFkStaffId())) {
//                agentVo.setBdName(bdNamesMap.get(agentVo.getFkStaffId()));
//            }
            //设置bd名字
            StaffBdCodeVo staffBdCodeVo = agentStaffMapper.selectAgentStaffBdInfo(agentVo.getId());
            if (GeneralTool.isNotEmpty(staffBdCodeVo)) {
                agentVo.setBdCode(staffBdCodeVo.getBdCode());
                Result<String> result = permissionCenterClient.getStaffName(staffBdCodeVo.getFkStaffId());
                if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                    agentVo.setBdName(result.getData());
                }

            }
        }
        return agentVos;
    }

    /**
     * 批量分配项目成员
     *
     * @Date 15:47 2021/8/2
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchUpdate(AgentRoleStaffDto agentRoleStaffDto) {
        List<AgentVo> agentVos = agentRoleStaffMapper.getAgentRoleStaffs(null, agentRoleStaffDto);
        List<Long> agentIds = agentVos.stream().map(AgentVo::getId).collect(Collectors.toList());
        if (GeneralTool.isEmpty(agentIds)) {
            agentIds.add(0L);
        }

        for (AgentRoleStaffDto roleStaffVo : agentRoleStaffDto.getAgentRoleStaffVoList()) {
            List<Long> fkAreaCountryIds = roleStaffVo.getFkAreaCountryIds();
//            Example example = new Example(AgentRoleStaff.class);
//            Example.Criteria criteria = example.createCriteria().andIn("fkAgentId", agentIds).andEqualTo("fkStudentProjectRoleId", roleStaffVo.getFkStudentProjectRoleId());
//            if (GeneralTool.isNotEmpty(fkAreaCountryIds)) {
//                criteria.andIn("fkAreaCountryId", fkAreaCountryIds);
//            } else {
//                criteria.andIsNull("fkAreaCountryId");
//            }
            LambdaQueryWrapper<AgentRoleStaff> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.in(AgentRoleStaff::getFkAgentId, agentIds);
            lambdaQueryWrapper.eq(AgentRoleStaff::getFkStudentProjectRoleId, roleStaffVo.getFkStudentProjectRoleId());
            lambdaQueryWrapper.eq(AgentRoleStaff::getFkStudentProjectRoleId, roleStaffVo.getFkStaffId());
            if (GeneralTool.isNotEmpty(fkAreaCountryIds)) {
                lambdaQueryWrapper.in(AgentRoleStaff::getFkAreaCountryId, fkAreaCountryIds);
            } else {
                lambdaQueryWrapper.isNull(AgentRoleStaff::getFkAreaCountryId);
            }
            agentRoleStaffMapper.delete(lambdaQueryWrapper);

            for (AgentVo agentVo : agentVos) {
                if (GeneralTool.isNotEmpty(fkAreaCountryIds)) {
                    for (Long fkAreaCountryId : fkAreaCountryIds) {
                        AgentRoleStaff agentRoleStaff = new AgentRoleStaff();
                        agentRoleStaff.setFkAgentId(agentVo.getId());
                        agentRoleStaff.setFkStudentProjectRoleId(roleStaffVo.getFkStudentProjectRoleId());
                        agentRoleStaff.setFkStaffId(roleStaffVo.getFkStaffId());
                        agentRoleStaff.setFkAreaCountryId(fkAreaCountryId);
                        agentRoleStaff.setFkCompanyId(agentRoleStaffDto.getFkCompanyId());
                        agentRoleStaff.setFkTypeKey(roleStaffVo.getBatchUpdateTypeKey());
                        utilService.updateUserInfoToEntity(agentRoleStaff);
                        agentRoleStaffMapper.insert(agentRoleStaff);
                    }
                } else {
                    AgentRoleStaff agentRoleStaff = new AgentRoleStaff();
                    agentRoleStaff.setFkAgentId(agentVo.getId());
                    agentRoleStaff.setFkStudentProjectRoleId(roleStaffVo.getFkStudentProjectRoleId());
                    agentRoleStaff.setFkStaffId(roleStaffVo.getFkStaffId());
                    agentRoleStaff.setFkCompanyId(agentRoleStaffDto.getFkCompanyId());
                    agentRoleStaff.setFkTypeKey(roleStaffVo.getBatchUpdateTypeKey());
                    utilService.updateUserInfoToEntity(agentRoleStaff);
                    agentRoleStaffMapper.insert(agentRoleStaff);
                }
            }
        }

    }

    /**
     * 代理项目成员配置详情
     *
     * @return
     * @Date 16:20 2021/8/2
     * <AUTHOR>
     */
    @Override
    public List<AgentRoleStaffVo> detail(Long agentId, Long countryId, Long companyId, String fkTypeKey) {
        List<AgentRoleStaff> agentRoleStaffs = agentRoleStaffMapper.selectAgentRoleStaffInfo(agentId, countryId, companyId, fkTypeKey);

        Set<Long> staffIds = agentRoleStaffs.stream().map(AgentRoleStaff::getFkStaffId).collect(Collectors.toSet());
        Set<Long> countryIdS = agentRoleStaffs.stream().map(AgentRoleStaff::getFkAreaCountryId).collect(Collectors.toSet());
        Map<Long, String> staffNamesMap = new HashMap<>();
        Result<Map<Long, String>> result = permissionCenterClient.getStaffNameMap(staffIds);
        if (result.isSuccess() && result.getData() != null) {
            staffNamesMap = result.getData();
        }
        Map<Long, String> allRoleName = studentProjectRoleService.getAllRoleName();
        Map<Long, String> countryNames = null;
        if (GeneralTool.isNotEmpty(countryIdS)) {
//            countryNames = institutionCenterClient.getCountryNamesByIds(countryIdS);
            Result<Map<Long, String>> countryNamesResult = institutionCenterClient.getCountryNamesByIds(countryIdS);
            if (countryNamesResult.isSuccess() && GeneralTool.isNotEmpty(countryNamesResult.getData())) {
                countryNames = countryNamesResult.getData();
            }
        }
        List<AgentRoleStaffVo> list = new ArrayList<>();
        if (GeneralTool.isNotEmpty(agentRoleStaffs)) {
            for (AgentRoleStaff agentRoleStaff : agentRoleStaffs) {
                AgentRoleStaffVo agentRoleStaffVo = BeanCopyUtils.objClone(agentRoleStaff, AgentRoleStaffVo::new);
                agentRoleStaffVo.setFkStaffName(staffNamesMap.get(agentRoleStaff.getFkStaffId()));
                agentRoleStaffVo.setFkStudentProjectRoleName(allRoleName.get(agentRoleStaff.getFkStudentProjectRoleId()));
                if (GeneralTool.isNotEmpty(agentRoleStaff.getFkAreaCountryId())) {
                    if (GeneralTool.isNotEmpty(countryNames)) {
                        agentRoleStaffVo.setAreaCountryName(countryNames.get(agentRoleStaff.getFkAreaCountryId()));
                    }
                }
                list.add(agentRoleStaffVo);
            }
        }
        return list;

    }

    /**
     * 获取代理项目成员配置
     *
     * @Date 14:46 2022/1/28
     * <AUTHOR>
     */
    @Override
    public List<AgentRoleStaffVo> getAgentRoleStaff(Long agentId, Long countryId, Long companyId, String fkTypeKey) {
        Long agentCountryId;
        Long agentStateId;
        if (GeneralTool.isNotEmpty(agentId)) {
            Agent agent = agentMapper.selectById(agentId);
            if (GeneralTool.isEmpty(agent)) {
                agentCountryId = null;
                agentStateId = null;
            } else {
                agentCountryId = agent.getFkAreaCountryId();
                agentStateId = agent.getFkAreaStateId();
            }
        } else {
            agentCountryId = null;
            agentStateId = null;
        }

         /*
        A. 根据公司所有角色做循环
        B. 查找该角色下，按以下顺寻进行优先匹配
        (1)对应的公司Id+目标类型+代理Id+代理区域Id=NULL+国家线Id是否设置了员工，有就设置。
        (2)没有按公司Id+目标类型+代理Id+代理区域Id=NULL+国家线Id=NULL，有或多条，随机分配设置。
        (3)没有按公司Id+目标类型+代理Id=NULL+代理国家id+区域Id(OR NULL)+国家线Id，有或多条，随机分配设置。
        (4)没有按公司Id+目标类型+代理Id=NULL+代理国家id+区域Id(OR NULL)+国家线Id=NULL，有或多条，随机分配设置。
        (5)没有按公司Id+目标类型+代理Id=NULL+代理国家id=NULL+区域Id=NULL+国家线Id，有或多条，随机分配设置。
        (6)没有按公司Id+目标类型+代理Id=NULL+代理国家id=NULL+区域Id=NULL+国家线Id=NULL，有或多条，随机分配设置。
        (7)以上条件都找不到，不进行分配设置，跳出该角色循环。
        */

        //公司所配置的学生项目角色  公司所有的角色
        List<StudentProjectRole> studentProjectRoles = studentProjectRoleMapper.selectList(Wrappers.<StudentProjectRole>lambdaQuery()
                .eq(StudentProjectRole::getFkCompanyId, companyId).orderByDesc(StudentProjectRole::getViewOrder));
        //角色要排序
        List<AgentRoleStaff> agentRoleStaffList = new ArrayList<>();

        //先获取代理项目成员配置
//        List<AgentRoleStaff> agentRoleStaffs = agentRoleStaffMapper.selectAgentRoleStaff(agentId, countryId, companyId, fkTypeKey,agentCountryId,agentStateId);

        for (StudentProjectRole studentProjectRole : studentProjectRoles) {
            //如果有多个 随机一个
            List<AgentRoleStaff> randomAgentRoleStaffList = new ArrayList<>();
            //(1)对应的公司Id+目标类型+代理Id+代理区域Id=NULL+国家线Id是否设置了员工，有就设置。
            if (GeneralTool.isEmpty(randomAgentRoleStaffList)) {
                List<AgentRoleStaff> agentRoleStaffs = agentRoleStaffMapper.selectList(Wrappers.<AgentRoleStaff>lambdaQuery()
                        .eq(AgentRoleStaff::getFkCompanyId, companyId)
                        .eq(AgentRoleStaff::getFkTypeKey, fkTypeKey)
                        .eq(AgentRoleStaff::getFkAgentId, agentId)
                        .isNull(AgentRoleStaff::getFkAreaStateIdAgent)
                        .eq(AgentRoleStaff::getFkAreaCountryId, countryId)
                        .eq(AgentRoleStaff::getFkStudentProjectRoleId, studentProjectRole.getId()));
                if (GeneralTool.isNotEmpty(agentRoleStaffs)) {
                    randomAgentRoleStaffList.addAll(agentRoleStaffs);
                }
            }
            //(2)没有按公司Id+目标类型+代理Id+代理区域Id=NULL+国家线Id=NULL，有或多条，随机分配设置。
            if (GeneralTool.isEmpty(randomAgentRoleStaffList)) {
                List<AgentRoleStaff> agentRoleStaffs = agentRoleStaffMapper.selectList(Wrappers.<AgentRoleStaff>lambdaQuery()
                        .eq(AgentRoleStaff::getFkCompanyId, companyId)
                        .eq(AgentRoleStaff::getFkTypeKey, fkTypeKey)
                        .eq(AgentRoleStaff::getFkAgentId, agentId)
                        .isNull(AgentRoleStaff::getFkAreaStateIdAgent)
                        .isNull(AgentRoleStaff::getFkAreaCountryId)
                        .eq(AgentRoleStaff::getFkStudentProjectRoleId, studentProjectRole.getId()));

                if (GeneralTool.isNotEmpty(agentRoleStaffs)) {
                    randomAgentRoleStaffList.addAll(agentRoleStaffs);
                }
            }
            //(3)没有按公司Id+目标类型+代理Id=NULL+代理国家id+区域Id(OR NULL)+国家线Id，有或多条，随机分配设置。
            if (GeneralTool.isEmpty(randomAgentRoleStaffList)) {
                LambdaQueryWrapper<AgentRoleStaff> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                lambdaQueryWrapper.eq(AgentRoleStaff::getFkCompanyId, companyId)
                        .eq(AgentRoleStaff::getFkTypeKey, fkTypeKey)
                        .isNull(AgentRoleStaff::getFkAgentId)
                        .eq(AgentRoleStaff::getFkAreaCountryIdAgent, agentCountryId)
                        .eq(AgentRoleStaff::getFkAreaCountryId, countryId)
                        .eq(AgentRoleStaff::getFkStudentProjectRoleId, studentProjectRole.getId());

                lambdaQueryWrapper.and(wrapper -> wrapper.eq(AgentRoleStaff::getFkAreaStateIdAgent, agentStateId).or().isNull(AgentRoleStaff::getFkAreaStateIdAgent));

                List<AgentRoleStaff> agentRoleStaffs = agentRoleStaffMapper.selectList(lambdaQueryWrapper);
                if (GeneralTool.isNotEmpty(agentRoleStaffs)) {
                    randomAgentRoleStaffList.addAll(agentRoleStaffs);
                }
            }
            //(4)没有按公司Id+目标类型+代理Id=NULL+代理国家id+区域Id(OR NULL)+国家线Id=NULL，有或多条，随机分配设置。
            if (GeneralTool.isEmpty(randomAgentRoleStaffList)) {
                LambdaQueryWrapper<AgentRoleStaff> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                lambdaQueryWrapper.eq(AgentRoleStaff::getFkCompanyId, companyId)
                        .eq(AgentRoleStaff::getFkTypeKey, fkTypeKey)
                        .isNull(AgentRoleStaff::getFkAgentId)
                        .eq(AgentRoleStaff::getFkAreaCountryIdAgent, agentCountryId)
                        .isNull(AgentRoleStaff::getFkAreaCountryId)
                        .eq(AgentRoleStaff::getFkStudentProjectRoleId, studentProjectRole.getId());

                lambdaQueryWrapper.and(wrapper -> wrapper.eq(AgentRoleStaff::getFkAreaStateIdAgent, agentStateId).or().isNull(AgentRoleStaff::getFkAreaStateIdAgent));

                List<AgentRoleStaff> agentRoleStaffs = agentRoleStaffMapper.selectList(lambdaQueryWrapper);

                if (GeneralTool.isNotEmpty(agentRoleStaffs)) {
                    randomAgentRoleStaffList.addAll(agentRoleStaffs);
                }
            }
            //(5)没有按公司Id+目标类型+代理Id=NULL+代理国家id=NULL+区域Id=NULL+国家线Id，有或多条，随机分配设置。
            if (GeneralTool.isEmpty(randomAgentRoleStaffList)) {
                List<AgentRoleStaff> agentRoleStaffs = agentRoleStaffMapper.selectList(Wrappers.<AgentRoleStaff>lambdaQuery()
                        .eq(AgentRoleStaff::getFkCompanyId, companyId)
                        .eq(AgentRoleStaff::getFkTypeKey, fkTypeKey)
                        .isNull(AgentRoleStaff::getFkAgentId)
                        .isNull(AgentRoleStaff::getFkAreaCountryIdAgent)
                        .isNull(AgentRoleStaff::getFkAreaStateIdAgent)
                        .eq(AgentRoleStaff::getFkAreaCountryId, countryId)
                        .eq(AgentRoleStaff::getFkStudentProjectRoleId, studentProjectRole.getId()));

                if (GeneralTool.isNotEmpty(agentRoleStaffs)) {
                    randomAgentRoleStaffList.addAll(agentRoleStaffs);
                }
            }
            //(6)没有按公司Id+目标类型+代理Id=NULL+代理国家id=NULL+区域Id=NULL+国家线Id=NULL，有或多条，随机分配设置。
            if (GeneralTool.isEmpty(randomAgentRoleStaffList)) {
                List<AgentRoleStaff> agentRoleStaffs = agentRoleStaffMapper.selectList(Wrappers.<AgentRoleStaff>lambdaQuery()
                        .eq(AgentRoleStaff::getFkCompanyId, companyId)
                        .eq(AgentRoleStaff::getFkTypeKey, fkTypeKey)
                        .isNull(AgentRoleStaff::getFkAgentId)
                        .isNull(AgentRoleStaff::getFkAreaCountryIdAgent)
                        .isNull(AgentRoleStaff::getFkAreaStateIdAgent)
                        .isNull(AgentRoleStaff::getFkAreaCountryId)
                        .eq(AgentRoleStaff::getFkStudentProjectRoleId, studentProjectRole.getId()));

                if (GeneralTool.isNotEmpty(agentRoleStaffs)) {
                    randomAgentRoleStaffList.addAll(agentRoleStaffs);
                }
            }
            //(7)以上条件都找不到，不进行分配设置，跳出该角色循环。
            if (GeneralTool.isNotEmpty(randomAgentRoleStaffList)) {
                SecureRandom randomIndex = new SecureRandom();
                int j = randomIndex.nextInt(randomAgentRoleStaffList.size());
                agentRoleStaffList.add(randomAgentRoleStaffList.get(j));
            }
        }


//        agentRoleStaffs = agentRoleStaffList;

        if (GeneralTool.isEmpty(agentRoleStaffList)) {
            return Collections.emptyList();
        }
        Set<Long> staffIds = agentRoleStaffList.stream().map(AgentRoleStaff::getFkStaffId).filter(Objects::nonNull).collect(Collectors.toSet());
        Set<Long> countryIdS = agentRoleStaffList.stream().map(AgentRoleStaff::getFkAreaCountryId).collect(Collectors.toSet());
        Map<Long, String> staffNamesMap = permissionCenterClient.getStaffNamesByIds(staffIds);
        Map<Long, String> allRoleName = studentProjectRoleService.getAllRoleName();
        Map<Long, String> countryNames = null;
        if (GeneralTool.isNotEmpty(countryIdS)) {
            countryNames = institutionCenterClient.getCountryNamesByIds(countryIdS).getData();
        }
        List<AgentRoleStaffVo> list = new ArrayList<>();
        if (GeneralTool.isNotEmpty(agentRoleStaffList)) {
            for (AgentRoleStaff agentRoleStaff : agentRoleStaffList) {
                AgentRoleStaffVo agentRoleStaffVo = BeanCopyUtils.objClone(agentRoleStaff, AgentRoleStaffVo::new);
                agentRoleStaffVo.setFkStaffName(staffNamesMap.get(agentRoleStaff.getFkStaffId()));
                agentRoleStaffVo.setFkStudentProjectRoleName(allRoleName.get(agentRoleStaff.getFkStudentProjectRoleId()));
                if (GeneralTool.isNotEmpty(agentRoleStaff.getFkAreaCountryId())) {
                    if (GeneralTool.isNotEmpty(countryNames)) {
                        agentRoleStaffVo.setAreaCountryName(countryNames.get(agentRoleStaff.getFkAreaCountryId()));
                    }
                }
                list.add(agentRoleStaffVo);
            }
        }
        return list;
    }

    /**
     * 更新代理项目成员配置
     *
     * @Date 16:40 2021/8/2
     * <AUTHOR>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(List<AgentRoleStaffDto> agentRoleStaffDtoList) {
        Set<Long> ids = new HashSet<>();//作为删除标记

        for (AgentRoleStaffDto agentRoleStaffDto : agentRoleStaffDtoList) {
            LambdaQueryWrapper<AgentRoleStaff> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(AgentRoleStaff::getFkAgentId, agentRoleStaffDto.getFkAgentId());
            if (GeneralTool.isNotEmpty(agentRoleStaffDto.getFkAreaCountryIds())) {
                lambdaQueryWrapper.in(AgentRoleStaff::getFkAreaCountryId, agentRoleStaffDto.getFkAreaCountryIds());
            } else {
                lambdaQueryWrapper.isNull(AgentRoleStaff::getFkAreaCountryId);
            }
            lambdaQueryWrapper.eq(AgentRoleStaff::getFkTypeKey, agentRoleStaffDto.getFkTypeKey());
            List<AgentRoleStaff> agentRoleStaffs = agentRoleStaffMapper.selectList(lambdaQueryWrapper);
            if (GeneralTool.isNotEmpty(agentRoleStaffs)) {
                ids.addAll(agentRoleStaffs.stream().map(AgentRoleStaff::getId).collect(Collectors.toSet()));
            }
        }

        for (AgentRoleStaffDto agentRoleStaffDto : agentRoleStaffDtoList) {
            List<Long> fkAreaCountryIds = agentRoleStaffDto.getFkAreaCountryIds();

            //先删除关系
//            Example example = new Example(AgentRoleStaff.class);
//            Example.Criteria criteria = example.createCriteria().andEqualTo("fkAgentId", agentRoleStaffDto.getFkAgentId())
//                    .andEqualTo("fkStudentProjectRoleId", agentRoleStaffDto.getFkStudentProjectRoleId());
//            if (GeneralTool.isNotEmpty(fkAreaCountryIds)) {
//                criteria.andIn("fkAreaCountryId", fkAreaCountryIds);
//            } else {
//                criteria.andIsNull("fkAreaCountryId");
//            }
//            agentRoleStaffMapper.deleteByExample(example);

//            lambdaQueryWrapper.eq(AgentRoleStaff::getFkStudentProjectRoleId, agentRoleStaffDto.getFkStudentProjectRoleId());
//            lambdaQueryWrapper.eq(AgentRoleStaff::getFkStaffId, agentRoleStaffDto.getFkStaffId());
//            if (GeneralTool.isNotEmpty(fkAreaCountryIds)) {
//                lambdaQueryWrapper.in(AgentRoleStaff::getFkAreaCountryId, fkAreaCountryIds);
//            } else {
//                lambdaQueryWrapper.isNull(AgentRoleStaff::getFkAreaCountryId);
//            }
//            if (GeneralTool.isNotEmpty(agentRoleStaffDto.getBatchUpdateTypeKey())) {
//                lambdaQueryWrapper.eq(AgentRoleStaff::getFkTypeKey, agentRoleStaffDto.getBatchUpdateTypeKey());
//            }
//            List<AgentRoleStaff> agentRoleStaffs = agentRoleStaffMapper.selectList(lambdaQueryWrapper);
//            if (GeneralTool.isNotEmpty(agentRoleStaffs)) {
//                continue;
////                throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
//            }

//            lambdaQueryWrapper = new LambdaQueryWrapper<>();
//            lambdaQueryWrapper.eq(AgentRoleStaff::getFkAgentId, agentRoleStaffDto.getFkAgentId());
            /**
             * 去掉同一国家线下单一人员的限制
             */
//            if (GeneralTool.isNotEmpty(fkAreaCountryIds)) {
//                criteria.andIn("fkAreaCountryId", fkAreaCountryIds);
//            } else {
//            if (GeneralTool.isEmpty(fkAreaCountryIds)) {
//                lambdaQueryWrapper.isNull(AgentRoleStaff::getFkAreaCountryId);
//            }
//            agentRoleStaffMapper.delete(lambdaQueryWrapper);

            if (GeneralTool.isNotEmpty(fkAreaCountryIds)) {
                for (Long fkAreaCountryId : fkAreaCountryIds) {
                    AgentRoleStaff agentRoleStaff = new AgentRoleStaff();
                    agentRoleStaff.setFkCompanyId(agentRoleStaffDto.getFkCompanyId());
                    if (GeneralTool.isNotEmpty(agentRoleStaffDto.getFkTypeKey())){
                        agentRoleStaff.setFkTypeKey(agentRoleStaffDto.getFkTypeKey());
                    }else {
                        agentRoleStaff.setFkTypeKey(agentRoleStaffDto.getBatchUpdateTypeKey());
                    }

                    agentRoleStaff.setFkAgentId(agentRoleStaffDto.getFkAgentId());
                    agentRoleStaff.setFkStudentProjectRoleId(agentRoleStaffDto.getFkStudentProjectRoleId());
                    agentRoleStaff.setFkStaffId(agentRoleStaffDto.getFkStaffId());
                    agentRoleStaff.setFkAreaCountryId(fkAreaCountryId);
                    utilService.updateUserInfoToEntity(agentRoleStaff);
                    agentRoleStaffMapper.insert(agentRoleStaff);
                }
            } else {
                AgentRoleStaff agentRoleStaff = new AgentRoleStaff();
                agentRoleStaff.setFkCompanyId(agentRoleStaffDto.getFkCompanyId());
                if (GeneralTool.isNotEmpty(agentRoleStaffDto.getFkTypeKey())){
                    agentRoleStaff.setFkTypeKey(agentRoleStaffDto.getFkTypeKey());
                }else {
                    agentRoleStaff.setFkTypeKey(agentRoleStaffDto.getBatchUpdateTypeKey());
                }
                agentRoleStaff.setFkAgentId(agentRoleStaffDto.getFkAgentId());
                agentRoleStaff.setFkStudentProjectRoleId(agentRoleStaffDto.getFkStudentProjectRoleId());
                agentRoleStaff.setFkStaffId(agentRoleStaffDto.getFkStaffId());
                utilService.updateUserInfoToEntity(agentRoleStaff);
                agentRoleStaffMapper.insert(agentRoleStaff);
            }

        }
        if (GeneralTool.isNotEmpty(ids)){
            agentRoleStaffMapper.deleteBatchIds(ids);
        }
    }

    /**
     * 删除绑定关系
     *
     * @Date 16:17 2021/8/3
     * <AUTHOR>
     */
    @Override
    public void delete(AgentRoleStaffDto agentRoleStaffDto) {
//        LambdaQueryWrapper<AgentRoleStaff> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//        lambdaQueryWrapper.eq(AgentRoleStaff::getFkAgentId, agentRoleStaffDto.getFkAgentId());
//        lambdaQueryWrapper.eq(AgentRoleStaff::getFkStudentProjectRoleId, agentRoleStaffDto.getFkStudentProjectRoleId());
//        lambdaQueryWrapper.eq(AgentRoleStaff::getFkStaffId, agentRoleStaffDto.getFkStaffId());
//        if (GeneralTool.isNotEmpty(agentRoleStaffDto.getAgentCountryId())) {
//            lambdaQueryWrapper.eq(AgentRoleStaff::getFkAreaCountryId, agentRoleStaffDto.getAgentCountryId());
//        } else {
//            lambdaQueryWrapper.isNull(AgentRoleStaff::getFkAreaCountryId);
//        }
//        if (GeneralTool.isNotEmpty(agentRoleStaffDto.getFkTypeKey())) {
//            lambdaQueryWrapper.eq(AgentRoleStaff::getFkTypeKey, agentRoleStaffDto.getFkTypeKey());
//        } else {
//            lambdaQueryWrapper.isNull(AgentRoleStaff::getFkTypeKey);
//        }
//
//        agentRoleStaffMapper.delete(lambdaQueryWrapper);
//

        if(GeneralTool.isEmpty(agentRoleStaffDto.getId()))
        {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        agentRoleStaffMapper.deleteById(agentRoleStaffDto.getId());
    }

    /**
     * 批量移除项目成员
     *
     * @Date 10:09 2021/8/23
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchRemoveUpdate(AgentRoleStaffDto agentRoleStaffDto) {
        List<AgentVo> agentVos = agentRoleStaffMapper.getAgentRoleStaffs(null, agentRoleStaffDto);
        List<Long> agentIds = agentVos.stream().map(AgentVo::getId).collect(Collectors.toList());
        for (AgentRoleStaffDto roleStaffVo : agentRoleStaffDto.getAgentRoleStaffVoList()) {
            List<Long> fkAreaCountryIds = roleStaffVo.getFkAreaCountryIds();

            LambdaQueryWrapper<AgentRoleStaff> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.in(AgentRoleStaff::getFkAgentId, agentIds);
            lambdaQueryWrapper.eq(AgentRoleStaff::getFkStudentProjectRoleId, roleStaffVo.getFkStudentProjectRoleId());

            if (GeneralTool.isNotEmpty(roleStaffVo.getFkAreaCountryIds())) {
                lambdaQueryWrapper.in(AgentRoleStaff::getFkAreaCountryId, fkAreaCountryIds);
            } else {
                lambdaQueryWrapper.isNull(AgentRoleStaff::getFkAreaCountryId);
            }
            //业务类型条件
            if (GeneralTool.isNotEmpty(roleStaffVo.getBatchUpdateTypeKey())) {
                lambdaQueryWrapper.eq(AgentRoleStaff::getFkTypeKey, roleStaffVo.getBatchUpdateTypeKey());
            } else {
                lambdaQueryWrapper.isNull(AgentRoleStaff::getFkTypeKey);
            }
            //公司id条件
            if (GeneralTool.isNotEmpty(agentRoleStaffDto.getFkCompanyId())) {
                lambdaQueryWrapper.eq(AgentRoleStaff::getFkCompanyId, agentRoleStaffDto.getFkCompanyId());
            } else {
                lambdaQueryWrapper.isNull(AgentRoleStaff::getFkCompanyId);
            }
            //员工id条件
            if (GeneralTool.isNotEmpty(roleStaffVo.getFkStaffId())) {
                lambdaQueryWrapper.eq(AgentRoleStaff::getFkStaffId, roleStaffVo.getFkStaffId());
            } else {
                lambdaQueryWrapper.isNull(AgentRoleStaff::getFkStaffId);
            }
            agentRoleStaffMapper.delete(lambdaQueryWrapper);
        }
//
//
//
//        List<AgentVo> agentVos = agentRoleStaffMapper.getAgentRoleStaffs(null,agentRoleStaffDto);
//        List<Long> agentIds = agentVos.stream().map(AgentVo::getId).collect(Collectors.toList());
//        for (AgentRoleStaffDto roleStaffVo : agentRoleStaffDto.getAgentRoleStaffVoList()) {
////            List<Long> fkAreaCountryIds = roleStaffVo.getFkAreaCountryIds();
////            Example example = new Example(AgentRoleStaff.class);
////            Example.Criteria criteria = example.createCriteria().andIn("fkAgentId", agentIds).andEqualTo("fkStudentProjectRoleId", roleStaffVo.getFkStudentProjectRoleId());
////            if (GeneralTool.isNotEmpty(roleStaffVo.getFkAreaCountryIds())) {
////                criteria.andIn("fkAreaCountryId", fkAreaCountryIds);
////            } else {
////                criteria.andIsNull("fkAreaCountryId");
////            }
////            //业务类型条件
////            if (GeneralTool.isNotEmpty(roleStaffVo.getBatchUpdateTypeKey())) {
////                criteria.andEqualTo("fkTypeKey", roleStaffVo.getBatchUpdateTypeKey());
////            } else {
////                criteria.andIsNull("fkTypeKey");
////            }
////            //公司id条件
////            if (GeneralTool.isNotEmpty(agentRoleStaffDto.getFkCompanyId())){
////                criteria.andEqualTo("fkCompanyId", roleStaffVo.getFkCompanyId());
////            }else {
////                criteria.andIsNull("fkCompanyId");
////            }
//
//
//            List<Long> fkAreaCountryIds = roleStaffVo.getFkAreaCountryIds();
////            Example example = new Example(AgentRoleStaff.class);
////            Example.Criteria criteria = example.createCriteria().andIn("fkAgentId", agentIds).andEqualTo("fkStudentProjectRoleId", roleStaffVo.getFkStudentProjectRoleId());
////            if (GeneralTool.isNotEmpty(roleStaffVo.getFkAreaCountryIds())) {
////                criteria.andIn("fkAreaCountryId", fkAreaCountryIds);
////            } else {
////                criteria.andIsNull("fkAreaCountryId");
////            }
////            agentRoleStaffMapper.deleteByExample(example);
//
//            LambdaQueryWrapper<AgentRoleStaff> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//            lambdaQueryWrapper.in(AgentRoleStaff::getFkAgentId,agentIds);
//            lambdaQueryWrapper.eq(AgentRoleStaff::getFkStudentProjectRoleId, roleStaffVo.getFkStudentProjectRoleId());
//            if (GeneralTool.isNotEmpty(roleStaffVo.getFkAreaCountryIds())) {
//                lambdaQueryWrapper.in(AgentRoleStaff::getFkAreaCountryId, fkAreaCountryIds);
//            } else {
//                lambdaQueryWrapper.eq(AgentRoleStaff::getFkAreaCountryId,null);
//            }
//            //业务类型条件
//            if (GeneralTool.isNotEmpty(agentRoleStaffDto.getFkTypeKey())) {
//                lambdaQueryWrapper.eq(AgentRoleStaff::getFkTypeKey, agentRoleStaffDto.getFkTypeKey());
//            } else {
//                lambdaQueryWrapper.eq(AgentRoleStaff::getFkTypeKey, null);
//            }
//            //公司id条件
//            if (GeneralTool.isNotEmpty(agentRoleStaffDto.getFkCompanyId())){
//                lambdaQueryWrapper.eq(AgentRoleStaff::getFkCompanyId, agentRoleStaffDto.getFkCompanyId());
//            }else {
//                lambdaQueryWrapper.eq(AgentRoleStaff::getFkCompanyId, null);
//            }
//            List<AgentRoleStaff> agentRoleStaffs = agentRoleStaffMapper.selectList(lambdaQueryWrapper);
//            if(GeneralTool.isEmpty(agentRoleStaffs))
//            {
//                throw new GetServiceException("该条件没有可移除的项目成员");
//            }
//            agentRoleStaffMapper.delete(lambdaQueryWrapper);
//        }
    }

    /**
     * 通用项目成员配置列表
     *
     * @param agentRoleStaffDto
     * @param page
     * @return
     */
    @Override
    public List<AgentRoleStaffVo> getCommonAgentRoleStaffs(AgentRoleStaffDto agentRoleStaffDto, Page page) {
        if (!SecureUtil.validateCompany(agentRoleStaffDto.getFkCompanyId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
        }
        IPage<AgentRoleStaffVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<AgentRoleStaffVo> agentRoleStaffVos = agentRoleStaffMapper.getCommonAgentRoleStaffs(iPage, agentRoleStaffDto);
        page.setAll((int) iPage.getTotal());
        if (GeneralTool.isEmpty(agentRoleStaffVos)) {
            return agentRoleStaffVos;
        }
        //公司ids
        Set<Long> companyIds = agentRoleStaffVos.stream().map(AgentRoleStaffVo::getFkCompanyId).collect(Collectors.toSet());
        //员工ids
        Set<Long> staffIds = agentRoleStaffVos.stream().map(AgentRoleStaffVo::getFkStaffId).collect(Collectors.toSet());
        //国家ids
        Set<Long> countryIds = agentRoleStaffVos.stream().map(AgentRoleStaffVo::getFkAreaCountryId).collect(Collectors.toSet());

        Set<Long> agentCountryIds = agentRoleStaffVos.stream().map(AgentRoleStaffVo::getFkAreaCountryIdAgent).collect(Collectors.toSet());
        countryIds.addAll(agentCountryIds);

        Set<Long> agentStateIds = agentRoleStaffVos.stream().map(AgentRoleStaffVo::getFkAreaStateIdAgent).collect(Collectors.toSet());


        //根据国家ids获取国家名称
        Map<Long, String> companyNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(companyIds)) {
//            companyNamesByIds = permissionCenterClient.getCompanyNamesByIds(companyIds);
            Result<Map<Long, String>> result = permissionCenterClient.getCompanyNamesByIds(companyIds);
            if (result.isSuccess() && result.getData() != null) {
                companyNamesByIds = result.getData();
            }
        }
        //根据员工ids获取员工名称
        Map<Long, String> staffNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(staffIds)) {
//            staffNamesByIds = permissionCenterClient.getStaffNameMap(staffIds);
            Result<Map<Long, String>> staffNameResult = permissionCenterClient.getStaffNameMap(staffIds);
            if (staffNameResult.isSuccess() && GeneralTool.isNotEmpty(staffNameResult)) {
                staffNamesByIds = staffNameResult.getData();
            }
        }
        //根据国家ids获取国家名称
        Map<Long, String> countryNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(countryIds)) {
            Result<Map<Long, String>> result = institutionCenterClient.getCountryNamesByIds(countryIds);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result)) {
                countryNamesByIds = result.getData();
            }
        }

        //根据国家ids获取国家名称
        Map<Long, String> stateNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(agentStateIds)) {
            stateNamesByIds = institutionCenterClient.getStateFullNamesByIds(agentStateIds).getData();
        }

        Map<Object, Object> typeKeyNamesByTypeKey = new HashMap<>();
        List<Map<String, Object>> maps = TableEnum.enumsTranslation2Arrays(TableEnum.BUSINESS_TYPE);
        for (Map<String, Object> map : maps) {
            typeKeyNamesByTypeKey.put(map.get("key"), map.get("value"));
        }

        Map<Long, String> allRoleName = studentProjectRoleService.getAllRoleName();

        for (AgentRoleStaffVo agentRoleStaffVo : agentRoleStaffVos) {
            agentRoleStaffVo.setFkCompanyName(companyNamesByIds.get(agentRoleStaffVo.getFkCompanyId()));
            agentRoleStaffVo.setFkStaffName(staffNamesByIds.get(agentRoleStaffVo.getFkStaffId()));
            if (GeneralTool.isNotEmpty(agentRoleStaffVo.getFkAreaCountryId())) {
                agentRoleStaffVo.setAreaCountryName(countryNamesByIds.get(agentRoleStaffVo.getFkAreaCountryId()));
            }
            agentRoleStaffVo.setFkStudentProjectRoleName(allRoleName.get(agentRoleStaffVo.getFkStudentProjectRoleId()));
            agentRoleStaffVo.setFkTypeKeyName((String) typeKeyNamesByTypeKey.get(agentRoleStaffVo.getFkTypeKey()));
            agentRoleStaffVo.setFkAreaCountryIdAgentName(countryNamesByIds.get(agentRoleStaffVo.getFkAreaCountryIdAgent()));
            agentRoleStaffVo.setFkAreaStateIdAgentName(stateNamesByIds.get(agentRoleStaffVo.getFkAreaStateIdAgent()));
        }
        return agentRoleStaffVos;
    }

    /**
     * 新增通用项目成员配置
     *
     * @param agentRoleStaffDto
     * @return
     */
    @Override
    public void addCommonAgentRoleStaff(AgentRoleStaffDto agentRoleStaffDto) {
        if (GeneralTool.isNotEmpty(agentRoleStaffDto.getFkCompanyId())
                && GeneralTool.isNotEmpty(agentRoleStaffDto.getFkTypeKey())
                && GeneralTool.isNotEmpty(agentRoleStaffDto.getFkAreaCountryId())
                && GeneralTool.isNotEmpty(agentRoleStaffDto.getFkStudentProjectRoleId())
                && GeneralTool.isNotEmpty(agentRoleStaffDto.getFkStaffId())) {
            List<AgentRoleStaffVo> agentRoleStaffVos = agentRoleStaffMapper.getCommonAgentRoleStaffs(null, agentRoleStaffDto);
            if (GeneralTool.isNotEmpty(agentRoleStaffVos)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
            }
        }
        if (GeneralTool.isNotEmpty(agentRoleStaffDto.getFkAreaCountryIds())) {
            //多选业务国家
            for (Long fkAreaCountryId : agentRoleStaffDto.getFkAreaCountryIds()) {
                if (GeneralTool.isNotEmpty(agentRoleStaffDto.getFkAreaStateIdAgentList())) {
                    //代理区域不为空
                    for (Long stateId : agentRoleStaffDto.getFkAreaStateIdAgentList()) {
                        AgentRoleStaff agentRoleStaff = new AgentRoleStaff();
                        agentRoleStaffDto.setFkAreaCountryId(fkAreaCountryId);
                        agentRoleStaffDto.setFkAreaStateIdAgent(stateId);
                        BeanUtils.copyProperties(agentRoleStaffDto, agentRoleStaff);
                        utilService.updateUserInfoToEntity(agentRoleStaff);
                        int i = agentRoleStaffMapper.insertSelective(agentRoleStaff);
                        if (i <= 0) {
                            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
                        }
                    }
                } else {
                    //代理区域为空
                    AgentRoleStaff agentRoleStaff = new AgentRoleStaff();
                    agentRoleStaffDto.setFkAreaCountryId(fkAreaCountryId);
                    BeanUtils.copyProperties(agentRoleStaffDto, agentRoleStaff);
                    utilService.updateUserInfoToEntity(agentRoleStaff);
                    int i = agentRoleStaffMapper.insertSelective(agentRoleStaff);
                    if (i <= 0) {
                        throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
                    }
                }
            }
        } else {
            //不选业务国家
            //代理区域不为空
            if (GeneralTool.isNotEmpty(agentRoleStaffDto.getFkAreaStateIdAgentList())) {
                for (Long stateId : agentRoleStaffDto.getFkAreaStateIdAgentList()) {
                    AgentRoleStaff agentRoleStaff = new AgentRoleStaff();
                    BeanUtils.copyProperties(agentRoleStaffDto, agentRoleStaff);
                    agentRoleStaff.setFkAreaStateIdAgent(stateId);
                    utilService.updateUserInfoToEntity(agentRoleStaff);
                    int i = agentRoleStaffMapper.insertSelective(agentRoleStaff);
                    if (i <= 0) {
                        throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
                    }
                }
            } else {
                //代理区域为空
                AgentRoleStaff agentRoleStaff = new AgentRoleStaff();
                BeanUtils.copyProperties(agentRoleStaffDto, agentRoleStaff);
                utilService.updateUserInfoToEntity(agentRoleStaff);
                int i = agentRoleStaffMapper.insertSelective(agentRoleStaff);
                if (i <= 0) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
                }
            }
        }
    }

    /**
     * 修改通用项目成员配置
     *
     * @param agentRoleStaffDto
     * @return
     */
    @Override
    public AgentRoleStaffVo updateCommonAgentRoleStaff(AgentRoleStaffDto agentRoleStaffDto) {
//        if (GeneralTool.isAllNotEmpty(agentRoleStaffDto.getFkCompanyId(),agentRoleStaffDto.getFkTypeKey(),agentRoleStaffDto.getFkAreaCountryId(),
//                agentRoleStaffDto.getFkStudentProjectRoleId(),agentRoleStaffDto.getFkStaffId())){
        if (GeneralTool.isNotEmpty(agentRoleStaffDto.getFkCompanyId())
                && GeneralTool.isNotEmpty(agentRoleStaffDto.getFkTypeKey())
                && GeneralTool.isNotEmpty(agentRoleStaffDto.getFkAreaCountryId())
                && GeneralTool.isNotEmpty(agentRoleStaffDto.getFkStudentProjectRoleId())
                && GeneralTool.isNotEmpty(agentRoleStaffDto.getFkStaffId())) {
            List<AgentRoleStaffVo> agentRoleStaffVos = agentRoleStaffMapper.getCommonAgentRoleStaffs(null, agentRoleStaffDto);
            if (GeneralTool.isNotEmpty(agentRoleStaffVos)) {
                //过滤出id不为本身的其他数据
                //TODO 改过
                //List<AgentRoleStaffVo> collect = agentRoleStaffVos.stream().filter(agentRoleStaffDto -> !agentRoleStaffDto.getId().equals(agentRoleStaffDto.getId())).collect(Collectors.toList());
                List<AgentRoleStaffVo> collect = agentRoleStaffVos.stream().filter(agentRoleStaffVo -> !agentRoleStaffDto.getId().equals(agentRoleStaffDto.getId())).collect(Collectors.toList());
                if (GeneralTool.isNotEmpty(collect)) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
                }
            }
        }
        AgentRoleStaff agentRoleStaff = agentRoleStaffMapper.selectById(agentRoleStaffDto.getId());
        BeanCopyUtils.copyProperties(agentRoleStaffDto, agentRoleStaff);
        utilService.updateUserInfoToEntity(agentRoleStaff);
        agentRoleStaffMapper.updateByPrimaryKey(agentRoleStaff);
        return findCommonAgentRoleStaffById(agentRoleStaffDto.getId());
    }

    /**
     * 通用项目成员配置详情
     *
     * @param id
     * @return
     * @
     */
    @Override
    public AgentRoleStaffVo findCommonAgentRoleStaffById(Long id) {
        AgentRoleStaff agentRoleStaff = agentRoleStaffMapper.selectById(id);
        if (GeneralTool.isEmpty(agentRoleStaff)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        Map<Long, String> allRoleName = studentProjectRoleService.getAllRoleName();
        AgentRoleStaffVo agentRoleStaffVo = new AgentRoleStaffVo();
        BeanCopyUtils.copyProperties(agentRoleStaff, agentRoleStaffVo);

        Result<String> result = permissionCenterClient.getCompanyNameById(agentRoleStaff.getFkCompanyId());
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            agentRoleStaffVo.setFkCompanyName(result.getData());
        }

        Result<String> fkStaffNameResult = permissionCenterClient.getStaffName(agentRoleStaff.getFkStaffId());
        if (fkStaffNameResult.isSuccess() && fkStaffNameResult.getData() != null) {
            agentRoleStaffVo.setFkStaffName(fkStaffNameResult.getData());
        }
        if (GeneralTool.isNotEmpty(agentRoleStaff.getFkAreaCountryId())) {
            Result<String> strResult = institutionCenterClient.getCountryNameById(agentRoleStaff.getFkAreaCountryId());
            if (strResult.isSuccess() && GeneralTool.isNotEmpty(strResult.getData())) {
                agentRoleStaffVo.setAreaCountryName(strResult.getData());
            }
        }
        Map<Object, Object> typeKeyNamesByTypeKey = new HashMap<>();
        List<Map<String, Object>> maps = TableEnum.enumsTranslation2Arrays(TableEnum.BUSINESS_TYPE);
        for (Map<String, Object> map : maps) {
            typeKeyNamesByTypeKey.put(map.get("key"), map.get("value"));
        }
        agentRoleStaffVo.setFkStudentProjectRoleName(allRoleName.get(agentRoleStaff.getFkStudentProjectRoleId()));
        agentRoleStaffVo.setFkTypeKeyName((String) typeKeyNamesByTypeKey.get(agentRoleStaff.getFkTypeKey()));
        return agentRoleStaffVo;
    }

    @Override
    public List<BaseSelectEntity> getStaffNameSelect(Long fkCompanyId) {
        List<AgentRoleStaff> agentRoleStaffs = agentRoleStaffMapper.selectList(Wrappers.<AgentRoleStaff>query().lambda().eq(AgentRoleStaff::getFkCompanyId, fkCompanyId));
        List<BaseSelectEntity> baseSelectEntityList = new ArrayList<>();
        if (GeneralTool.isEmpty(agentRoleStaffs)) {
            return baseSelectEntityList;
        }
        Set<Long> staffIds = agentRoleStaffs.stream().map(AgentRoleStaff::getFkStaffId).collect(Collectors.toSet());
        staffIds.removeIf(Objects::isNull);
        if (GeneralTool.isEmpty(staffIds)) {
            return baseSelectEntityList;
        }
        Map<Long, String> staffNamesByIds = new HashMap<>();
        Result<Map<Long, String>> staffNameResult = permissionCenterClient.getStaffNameMap(staffIds);
        if (staffNameResult.isSuccess() && GeneralTool.isNotEmpty(staffNameResult)) {
            staffNamesByIds = staffNameResult.getData();
        }
        for (Long staffId : staffIds) {
            BaseSelectEntity baseSelectEntity = new BaseSelectEntity();
            baseSelectEntity.setId(staffId);
            baseSelectEntity.setName(staffNamesByIds.get(staffId));
            baseSelectEntityList.add(baseSelectEntity);
        }
        return baseSelectEntityList;
    }

    @Override
    public void deleteCommonAgentRoleStaff(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (agentRoleStaffMapper.selectById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        agentRoleStaffMapper.deleteById(id);
    }

}

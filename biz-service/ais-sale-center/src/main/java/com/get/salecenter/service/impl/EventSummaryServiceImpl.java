package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.dao.sale.EventSummaryMapper;
import com.get.salecenter.vo.EventSummaryListVo;
import com.get.salecenter.vo.EventSummaryVo;
import com.get.salecenter.entity.EventSummary;
import com.get.salecenter.service.IEventSummaryService;
import com.get.salecenter.dto.EventSummaryListDto;
import com.get.salecenter.dto.EventSummaryUpdateDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: Hardy
 * @create: 2022/6/7 15:07
 * @verison: 1.0
 * @description:
 */
@Service
public class EventSummaryServiceImpl extends ServiceImpl<EventSummaryMapper, EventSummary> implements IEventSummaryService {

    @Resource
    private EventSummaryMapper eventSummaryMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Override
    public Long addEventSummary(EventSummaryUpdateDto eventSummaryVo) {
        if (GeneralTool.isEmpty(eventSummaryVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        EventSummary eventSummary = new EventSummary();
        BeanCopyUtils.copyProperties(eventSummaryVo,eventSummary);
        Integer maxViewOrder = eventSummaryMapper.getMaxViewOrder();
        eventSummary.setViewOrder(maxViewOrder);
        utilService.setCreateInfo(eventSummary);
        eventSummaryMapper.insertSelective(eventSummary);
        return eventSummary.getId();
    }


    @Override
    public EventSummaryVo findEventSummaryById(Long id) {
        if (GeneralTool.isEmpty(id)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        EventSummary eventSummary = eventSummaryMapper.selectById(id);
        EventSummaryVo eventSummaryVo = new EventSummaryVo();
        BeanCopyUtils.copyProperties(eventSummary, eventSummaryVo);
        return eventSummaryVo;
    }

    @Override
    public List<EventSummaryListVo> getEventSummaries(EventSummaryListDto eventSummaryListDto, Page page) {
        if (GeneralTool.isEmpty(eventSummaryListDto)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        if (GeneralTool.isEmpty(eventSummaryListDto.getFkCompanyId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        IPage<EventSummaryListVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<EventSummaryListVo> eventSummaryListVos = eventSummaryMapper.getEventSummaries(iPage, eventSummaryListDto);
        page.setAll((int) iPage.getTotal());

        if (GeneralTool.isEmpty(eventSummaryListVos)){
            return eventSummaryListVos;
        }

        Set<Long> companyIdset = eventSummaryListVos.stream().map(EventSummaryListVo::getFkCompanyId).collect(Collectors.toSet());
        Map<Long, String> companyNameMap = permissionCenterClient.getCompanyNamesByIds(companyIdset).getData();
        eventSummaryListVos.forEach(eventSummaryListVo -> {
            if (GeneralTool.isNotEmpty(companyNameMap)&&GeneralTool.isNotEmpty(eventSummaryListVo.getFkCompanyId())){
                eventSummaryListVo.setFkCompanyName(companyNameMap.get(eventSummaryListVo.getFkCompanyId()));
            }
        });
        return eventSummaryListVos;
    }

    @Override
    public EventSummaryVo updateeventSummary(EventSummaryUpdateDto eventSummaryUpdateDto) {
        if (GeneralTool.isEmpty(eventSummaryUpdateDto.getId())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }

        EventSummary eventSummary = eventSummaryMapper.selectById(eventSummaryUpdateDto.getId());
        if (GeneralTool.isEmpty(eventSummary)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
        BeanCopyUtils.copyProperties(eventSummaryUpdateDto,eventSummary);
        utilService.setUpdateInfo(eventSummary);
        eventSummaryMapper.updateById(eventSummary);
        return findEventSummaryById(eventSummaryUpdateDto.getId());
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        eventSummaryMapper.deleteById(id);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void movingOrder(List<EventSummaryUpdateDto> eventSummaryUpdateDtos) {
        if (GeneralTool.isEmpty(eventSummaryUpdateDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        EventSummary ro = BeanCopyUtils.objClone(eventSummaryUpdateDtos.get(0), EventSummary::new);
        Integer oneorder = ro.getViewOrder();
        EventSummary rt = BeanCopyUtils.objClone(eventSummaryUpdateDtos.get(1), EventSummary::new);
        Integer twoorder = rt.getViewOrder();
        ro.setViewOrder(twoorder);
        utilService.setUpdateInfo(ro);
        rt.setViewOrder(oneorder);
        utilService.setUpdateInfo(rt);
        eventSummaryMapper.updateById(ro);
        eventSummaryMapper.updateById(rt);
    }

    @Override
    public List<BaseSelectEntity> getEventSummariesSelect(Long fkCompanyId) {
        List<EventSummary> eventSummaries = eventSummaryMapper.selectList(Wrappers.<EventSummary>lambdaQuery()
                .eq(EventSummary::getFkCompanyId, fkCompanyId)
                .orderByDesc(EventSummary::getViewOrder));
        if (GeneralTool.isEmpty(eventSummaries)){
            return Collections.emptyList();
        }

        List<BaseSelectEntity> selectEntities = eventSummaries.stream().map(e -> {
            BaseSelectEntity baseSelectEntity = new BaseSelectEntity();
            baseSelectEntity.setId(e.getId());
            baseSelectEntity.setName(e.getEventSummary());
            return baseSelectEntity;
        }).collect(Collectors.toList());

        return selectEntities;
    }

    @Override
    public List<EventSummary> getEventSummariesByCondition(LambdaQueryWrapper<EventSummary> lambdaQueryWrapper) {
        return eventSummaryMapper.selectList(lambdaQueryWrapper);
    }

}

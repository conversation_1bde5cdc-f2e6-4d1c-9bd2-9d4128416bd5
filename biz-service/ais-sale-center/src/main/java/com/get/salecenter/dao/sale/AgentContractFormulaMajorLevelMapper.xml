<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.AgentContractFormulaMajorLevelMapper">
  <resultMap id="BaseResultMap" type="com.get.salecenter.entity.AgentContractFormulaMajorLevel">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_agent_contract_formula_id" jdbcType="BIGINT" property="fkAgentContractFormulaId" />
    <result column="fk_major_level_id" jdbcType="BIGINT" property="fkMajorLevelId" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
  <insert id="insertSelective" parameterType="com.get.salecenter.entity.AgentContractFormulaMajorLevel" keyProperty="id" useGeneratedKeys="true">
    insert into r_agent_contract_formula_major_level
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkAgentContractFormulaId != null">
        fk_agent_contract_formula_id,
      </if>
      <if test="fkMajorLevelId != null">
        fk_major_level_id,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkAgentContractFormulaId != null">
        #{fkAgentContractFormulaId,jdbcType=BIGINT},
      </if>
      <if test="fkMajorLevelId != null">
        #{fkMajorLevelId,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <select id="getMajorLevelIdsByFkid" resultType="java.lang.Long">
    select
     fk_major_level_id
    from
     r_agent_contract_formula_major_level
    where
     fk_agent_contract_formula_id = #{agentContractFormulaId}
  </select>
</mapper>
package com.get.salecenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.common.result.Page;
import com.get.filecenter.dto.FileDto;
import com.get.salecenter.vo.MediaAndAttachedVo;
import com.get.salecenter.entity.SaleMediaAndAttached;
import com.get.salecenter.dto.MediaAndAttachedDto;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: Sea
 * @create: 2020/8/14 9:57
 * @verison: 1.0
 * @description:
 */
public interface IMediaAndAttachedService extends IService<SaleMediaAndAttached> {
    /**
     * 上传文件
     *
     * @Date 11:42 2021/7/5
     * <AUTHOR>
     */
    List<FileDto> upload(MultipartFile[] multipartFiles, String type);

    /**
     * 上传文件
     *
     * @param multipartFiles
     * @return
     * @
     */
    List<FileDto> upload(MultipartFile[] multipartFiles);

    /**
     * 上传附件
     *
     * @param multipartFiles
     * @return
     * @
     */
    List<FileDto> uploadAppendix(MultipartFile[] multipartFiles);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    void deleteMediaAttached(Long id);

    /**
     * 保存附件
     *
     * @param mediaAttachedVo
     * @return
     */
    MediaAndAttachedVo addMediaAndAttached(MediaAndAttachedDto mediaAttachedVo);

    /**
     * 批量保存附件
     *
     * @param mediaAttachedVos
     * @return
     */
    Boolean saveBatchMediaAndAttached(List<MediaAndAttachedDto> mediaAttachedVos);

    /**
     * 通过附件GUID获取DTO
     *
     * @param attachedVo
     * @return
     * @
     */
    List<MediaAndAttachedVo> getMediaAndAttachedDto(MediaAndAttachedDto attachedVo);

    /**
     * @Description: 根据表ids获取批量的附件
     * @Author: Jerry
     * @Date:9:31 2021/9/7
     */
    Map<Long, List<MediaAndAttachedVo>> getMediaAndAttachedDtoByFkTableIds(String fkTableName, Set<Long> fkTableIds);

    /**
     * 获取是否存在文件
     * @param fkTypeKey
     * @param fkTableIds
     * @return
     */
    List<Long> isExist(String fkTypeKey, Set<Long> fkTableIds);
    /**
     * @return int
     * @Description:获取附件数量
     * @Param [attachedVo]
     * <AUTHOR>
     */
    int getItemMediaCount(SaleMediaAndAttached attached);

    /**
     * 通过附件GUID分页获取DTO
     *
     * @param attachedVo
     * @return
     * @
     */
    List<MediaAndAttachedVo> getMediaAndAttachedDto(MediaAndAttachedDto attachedVo, Page page);

    public List<MediaAndAttachedVo> getMediaAndAttachedDto(MediaAndAttachedDto attachedVo, Page page, String... typeKeys);


    List<MediaAndAttachedVo> getMediaAndAttachedDtos(List<Long> fkTableIds, String fkTableName, String fkTypeKey);


    List<MediaAndAttachedVo> getMediaAndAttachedDtos(List<Long> fkTableIds, String fkTableName, String fkTypeKey, Page page);
    /**
     * 根据媒体附件id修改表id
     *
     * @param id
     * @param tableId
     */
    void updateTableId(Long id, Long tableId);

    /**
     * 根据表id删除对应媒体附件
     *
     * @param tableId
     * @return
     */
    void deleteMediaAndAttachedByTableId(Long tableId, String tableName);

    /**
     * 获取代理附件类型
     *
     * @return
     */
    List<Map<String, Object>> findAgentMediaType();


    /**
     * 获取代理合同附件类型
     *
     * @return
     */
    List<Map<String, Object>> findContractMediaType();


    /**
     * @return void
     * @Description :上移下移
     * @Param [mediaAttachedVos]
     * <AUTHOR>
     */
    void movingOrder(List<MediaAndAttachedDto> mediaAttachedVos);

    List<MediaAndAttachedVo> getFileMedia(List<SaleMediaAndAttached> mediaAndAttachedList);

    /**
     * @Description:删除媒体文件
     * @Param
     * @Date 16:40 2021/5/13
     * <AUTHOR>
     */
    void deleteMediaAndAttached(String fkTableName, Long fkTableId);

    List<SaleMediaAndAttached> getMediaAndAttachedByIaeCrm();

    Boolean updateMediaAndAttachedById(SaleMediaAndAttached mediaAndAttached);

    List<MediaAndAttachedVo> getMediaAndAttachedByAgentIds(List<String> fkAgentIds_);

    /**
     * 删除学生附件
     * @param mergedStudentId
     */
    void deleteByStudentId(Long mergedStudentId);

    List<FileDto> uploadHtiPublicFile(MultipartFile[] files);

    /**
     * 批量新增附件信息
     * @param mediaAttachedVos
     * @return
     */
    List<MediaAndAttachedVo> addMediaAndAttachedList(List<MediaAndAttachedDto> mediaAttachedVos);

    /**
     * 复制partner学生附件
     * @param studentId
     * @param partnerStudentId
     * @return
     */
    Boolean copyPartnerStudentAttached(Long studentId, Long partnerStudentId);
}

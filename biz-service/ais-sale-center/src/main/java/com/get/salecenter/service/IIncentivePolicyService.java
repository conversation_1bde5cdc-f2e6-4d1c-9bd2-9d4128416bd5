package com.get.salecenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.common.result.Page;
import com.get.common.result.SearchBean;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.salecenter.vo.CommentVo;
import com.get.salecenter.vo.IncentivePolicyVo;
import com.get.salecenter.vo.MediaAndAttachedVo;
import com.get.salecenter.entity.IncentivePolicy;
import com.get.salecenter.dto.CommentDto;
import com.get.salecenter.dto.IncentivePolicyDto;
import com.get.salecenter.dto.MediaAndAttachedDto;
import com.get.salecenter.dto.query.IncentivePolicyQueryDto;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-13
 */
public interface IIncentivePolicyService extends IService<IncentivePolicy> {

    IncentivePolicyVo selectById(Long id);

    Long addIncentivePolicy(IncentivePolicyDto incentivePolicyDto);

    void deleteDataById(Long id);

    IncentivePolicyVo updateIncentivePolicy(IncentivePolicyDto incentivePolicyDto);

    List<IncentivePolicyVo> getIncentivePolicys(IncentivePolicyQueryDto data, SearchBean<IncentivePolicyQueryDto> page);

    void updateStatus(Long id);

    void movingOrder(List<IncentivePolicyDto> voList);

    IncentivePolicyVo copyPolicy(Long id);

    List<MediaAndAttachedVo> addAgentMedia(List<MediaAndAttachedDto> mediaAttachedVo);

    List<MediaAndAttachedVo> getPolicyMedia(MediaAndAttachedDto attachedVo, Page page);

    Long editComment(CommentDto commentDto);

    List<CommentVo> getComments(CommentDto commentDto, Page page);

    void movingOrderSelect(int end, int start);

    List<BaseSelectEntity> getInstitutionProviderSelect(Long companyId);

    List<BaseSelectEntity> getInstitutionListByProviderId(Long providerId);
}

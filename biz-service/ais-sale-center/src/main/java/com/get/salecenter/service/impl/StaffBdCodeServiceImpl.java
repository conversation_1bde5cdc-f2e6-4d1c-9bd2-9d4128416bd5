package com.get.salecenter.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.vo.AreaRegionVo;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.dao.sale.AgentMapper;
import com.get.salecenter.dao.sale.StaffBdCodeMapper;
import com.get.salecenter.vo.BdSelectVo;
import com.get.salecenter.vo.SaleAreaRegionVo;
import com.get.salecenter.vo.StaffBdCodeVo;
import com.get.salecenter.entity.Agent;
import com.get.salecenter.entity.StaffBdCode;
import com.get.salecenter.service.IAgentStaffService;
import com.get.salecenter.service.IDeleteService;
import com.get.salecenter.service.IStaffBdCodeService;
import com.get.salecenter.dto.StaffBdCodeDto;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/11/17
 * @TIME: 11:17
 * @Description:
 **/
@Service
public class StaffBdCodeServiceImpl extends ServiceImpl<StaffBdCodeMapper, StaffBdCode> implements IStaffBdCodeService {
    @Resource
    private StaffBdCodeMapper staffBdCodeMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IDeleteService deleteService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private IAgentStaffService agentStaffService;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    private AgentMapper agentMapper;

    @Override
    public StaffBdCodeVo findStaffBdCodeById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        StaffBdCodeVo staffBdCodeVo = BeanCopyUtils.objClone(staffBdCodeMapper.selectById(id), StaffBdCodeVo::new);
        if (GeneralTool.isNotEmpty(staffBdCodeVo.getFkAreaRegionId())) {
            List<SaleAreaRegionVo> areaRegionDtos = new ArrayList<>();
            Set<Long> fkAreaRegionIds = new HashSet<>();
            //获取多个业务区域
            String[] split = staffBdCodeVo.getFkAreaRegionId().split(",");
            for (String s : split) {
                fkAreaRegionIds.add(Long.valueOf(s));
            }
            //先获取业务区域集合里面的所有对象
//            Map<Long, AreaRegionVo> areaRegionDtoByIdsMap = institutionCenterClient.getAreaRegionDtoByIds(fkAreaRegionIds);
            Result<Map<Long, AreaRegionVo>> mapResult = institutionCenterClient.getAreaRegionDtoByIds(fkAreaRegionIds);
            if (mapResult.isSuccess() && GeneralTool.isNotEmpty(mapResult.getData())) {
                Map<Long, AreaRegionVo> areaRegionDtoByIdsMap = mapResult.getData();
                //遍历获取对象id所对应的对象
                for (String s : split) {
                    //从map集合中获取这个对象
                    if (areaRegionDtoByIdsMap.containsKey(Long.valueOf(s))) {
                        AreaRegionVo areaRegionVo = areaRegionDtoByIdsMap.get(Long.valueOf(s));
                        areaRegionDtos.add(BeanCopyUtils.objClone(areaRegionVo, SaleAreaRegionVo::new));
                    }
                }
                staffBdCodeVo.setAreaRegionDto(areaRegionDtos);

            }

        }
        return staffBdCodeVo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(List<StaffBdCodeDto> staffBdCodeDtos) {
        if (GeneralTool.isEmpty(staffBdCodeDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        //获取所有的员工id
        Set<Long> fkStaffIds = staffBdCodeDtos.stream().map(StaffBdCodeDto::getFkStaffId).collect(Collectors.toSet());
        //获取所有的bd编号
        Set<String> bdCodes = staffBdCodeDtos.stream().map(StaffBdCodeDto::getBdCode).collect(Collectors.toSet());
        //校验员工或者bd编号是否存在
        validateStaffIdAndBdCode(fkStaffIds, bdCodes, false, null);
        for (StaffBdCodeDto staffBdCodeDto : staffBdCodeDtos) {
            StaffBdCode staffBdCode = BeanCopyUtils.objClone(staffBdCodeDto, StaffBdCode::new);
            utilService.updateUserInfoToEntity(staffBdCode);
            staffBdCodeMapper.insertSelective(staffBdCode);
        }
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        StaffBdCode staffBdCode = staffBdCodeMapper.selectById(id);
        if (staffBdCode == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        //删除数据校验
        deleteService.deleteValidateStaffBdCode(staffBdCode.getFkStaffId());
        staffBdCodeMapper.deleteById(id);
    }

    @Override
    public StaffBdCodeVo updateStaffBdCode(StaffBdCodeDto staffBdCodeDto) {
        if (staffBdCodeDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        //获取所有的员工id
        Set<Long> fkStaffIds = new HashSet<>();
        fkStaffIds.add(staffBdCodeDto.getFkStaffId());
        //获取所有的bd编号
        Set<String> bdCodes = new HashSet<>();
        bdCodes.add(staffBdCodeDto.getBdCode());
        //校验员工或者bd编号是否存在
        validateStaffIdAndBdCode(fkStaffIds, bdCodes, true, staffBdCodeDto.getId());
        StaffBdCode result = staffBdCodeMapper.selectById(staffBdCodeDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        StaffBdCode staffBdCode = BeanCopyUtils.objClone(staffBdCodeDto, StaffBdCode::new);
        utilService.updateUserInfoToEntity(staffBdCode);
        staffBdCodeMapper.updateById(staffBdCode);
        return findStaffBdCodeById(staffBdCode.getId());
    }

    @Override
    public List<StaffBdCodeVo> getStaffBdCodes(StaffBdCodeDto staffBdCodeDto, Page page) {
//        LambdaQueryWrapper<StaffBdCode> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        //不选所属公司时
        List<Long> companyIdList = new ArrayList<>();
        List<Long> staffIdList = new ArrayList<>();
        if (GeneralTool.isEmpty(staffBdCodeDto) || GeneralTool.isEmpty(staffBdCodeDto.getFkCompanyId())) {
           companyIdList = getCompanyIds();
//            lambdaQueryWrapper.in(StaffBdCode::getFkCompanyId, companyIds);
        }
        if (GeneralTool.isNotEmpty(staffBdCodeDto)) {
            //查询条件-所属公司
            if (GeneralTool.isNotEmpty(staffBdCodeDto.getFkCompanyId())) {
                if (!SecureUtil.validateCompany(staffBdCodeDto.getFkCompanyId())) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
                }

//                lambdaQueryWrapper.eq(StaffBdCode::getFkCompanyId, staffBdCodeDto.getFkCompanyId());
            }

            if (GeneralTool.isNotEmpty(staffBdCodeDto.getKeyWord())) {
                staffIdList = permissionCenterClient.getStaffIdsByNameKeyOrEnNameKey(staffBdCodeDto.getKeyWord()).getData();
//                Result<List<Long>> result = permissionCenterClient.getStaffIdsByNameKeyOrEnNameKey(staffBdCodeDto.getKeyWord());
//                if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
//                    staffIds = result.getData();
//                    lambdaQueryWrapper.and(wrapper -> wrapper.in(StaffBdCode::getFkStaffId, staffIds)
//                            .or()
//                            .like(StaffBdCode::getBdCode, staffBdCodeDto.getKeyWord()));
//                }
            }
        }
        List<StaffBdCode> staffBdCodes = new ArrayList<>();
        if (page==null){
            staffBdCodes = staffBdCodeMapper.selectStaffBdCodeAll(null,staffBdCodeDto, companyIdList,staffIdList);
        }else {
//            lambdaQueryWrapper.orderByAsc(StaffBdCode::getBdCode);
            IPage<StaffBdCode> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
            staffBdCodes = staffBdCodeMapper.selectStaffBdCodeAll(iPage,staffBdCodeDto, companyIdList,staffIdList);
//            staffBdCodes = iPage.getRecords();
            page.setAll((int) iPage.getTotal());
        }

        List<StaffBdCodeVo> convertDatas = new ArrayList<>();
        //员工id集合
        Set<Long> staffIds = new HashSet<>();
        //所属公司id集合
        Set<Long> companyIds = new HashSet<>();
        //获取各自集合的值
        for (StaffBdCode staffBdCode : staffBdCodes) {
            staffIds.add(staffBdCode.getFkStaffId());
            companyIds.add(staffBdCode.getFkCompanyId());
        }
        //feign调用获取对应map
        Map<Long, String> staffNameMap = getStaffNameMap(staffIds);
        Map<Long, String> companyNameMap = getCompanyNameMap(companyIds);

        //获取集合中所有的业务区域ids
        Map<Long, AreaRegionVo> areaRegionDtoByIdsMap = new HashMap<>();
        Set<Long> fkAreaRegionIdsSet = new HashSet<>();
        for (StaffBdCode staffBdCode : staffBdCodes) {
            if (GeneralTool.isNotEmpty(staffBdCode.getFkAreaRegionId())) {
                String[] split = staffBdCode.getFkAreaRegionId().split(",");
                for (String s : split) {
                    fkAreaRegionIdsSet.add(Long.valueOf(s));
                }
            }
        }
        if (GeneralTool.isNotEmpty(fkAreaRegionIdsSet)) {
            //先获取业务区域集合里面的所有对象
//            areaRegionDtoByIdsMap = institutionCenterClient.getAreaRegionDtoByIds(fkAreaRegionIdsSet);
            Result<Map<Long, AreaRegionVo>> mapResult = institutionCenterClient.getAreaRegionDtoByIds(fkAreaRegionIdsSet);
            if (mapResult.isSuccess() && GeneralTool.isNotEmpty(mapResult.getData())) {
                areaRegionDtoByIdsMap = mapResult.getData();
            }
        }

        for (StaffBdCode staffBdCode : staffBdCodes) {
            StaffBdCodeVo staffBdCodeVo = BeanCopyUtils.objClone(staffBdCode, StaffBdCodeVo::new);
            //feign调用返回的map 根据key-id获取对应value-名称 设置返回给前端
            staffBdCodeVo.setCompanyName(companyNameMap.get(staffBdCode.getFkCompanyId()));
            staffBdCodeVo.setBdName(staffNameMap.get(staffBdCode.getFkStaffId()));
            //获取代理数
            List<Agent> agentsCount = agentStaffService.getBdCount(staffBdCode.getFkStaffId());
            agentsCount.removeIf(Objects::isNull);
            int bdCount = 0;
            int effectiveAgentCount = 0;
            int invalidAgentCount = 0;
            if (GeneralTool.isNotEmpty(agentsCount)) {
                bdCount = agentsCount.size();
                for (Agent agent : agentsCount) {
                    if (agent.getIsActive()) {
                        effectiveAgentCount++;
                    } else {
                        invalidAgentCount++;
                    }

                }
            }
            //设置值返回前端
            staffBdCodeVo.setBdCount(bdCount);
            staffBdCodeVo.setEffectiveAgentCount(effectiveAgentCount);
            staffBdCodeVo.setInvalidAgentCount(invalidAgentCount);
            if (GeneralTool.isNotEmpty(staffBdCode.getFkAreaRegionId())) {
                //先清空集合
                List<AreaRegionVo> areaRegionVos = new ArrayList<>();
                String[] split = staffBdCode.getFkAreaRegionId().split(",");
                //遍历获取对象id所对应的对象
                for (String s : split) {
                    //从map集合中获取这个对象
                    if (areaRegionDtoByIdsMap.containsKey(Long.valueOf(s))) {
                        areaRegionVos.add(areaRegionDtoByIdsMap.get(Long.valueOf(s)));
                    }
                }
                staffBdCodeVo.setAreaRegionDto(BeanCopyUtils.copyListProperties(areaRegionVos, SaleAreaRegionVo::new));
            }
            convertDatas.add(staffBdCodeVo);
        }
        return convertDatas;
    }

    @Override
    public String getBDbyStaffId(Long staffId) {
        if (GeneralTool.isEmpty(staffId)) {
            return null;
        }
//        Example example = new Example(StaffBdCode.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkStaffId", staffId);

        List<StaffBdCode> staffBdCodes = staffBdCodeMapper.selectList(Wrappers.<StaffBdCode>lambdaQuery().eq(StaffBdCode::getFkStaffId, staffId));
        if (GeneralTool.isEmpty(staffBdCodes)) {
            return null;
        }
        List<String> collect = staffBdCodes.stream().map(StaffBdCode::getBdCode).collect(Collectors.toList());
        return collect.get(0);
    }


    @Override
    public Map<Long, StaffBdCode> getBDbyStaffIds(Set<Long> staffIds) {
        Map<Long, StaffBdCode> map = new HashMap<>();
        if (GeneralTool.isEmpty(staffIds)) {
            return map;
        }
//        Example example = new Example(StaffBdCode.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andIn("fkStaffId", staffIds);
//
//        List<StaffBdCode> staffBdCodes = staffBdCodeMapper.selectByExample(example);
        List<StaffBdCode> staffBdCodes = staffBdCodeMapper.selectList(Wrappers.<StaffBdCode>lambdaQuery().in(StaffBdCode::getFkStaffId, staffIds));

        if (GeneralTool.isEmpty(staffBdCodes)) {
            return map;
        }
        for (StaffBdCode staffBdCode : staffBdCodes) {
            map.put(staffBdCode.getFkStaffId(), staffBdCode);
        }
        return map;
    }

    @Override
    public List<StaffBdCodeVo> getBDbyAgentIds(Set<Long> fkAgentIds){
        if (GeneralTool.isEmpty(fkAgentIds)) {
            return null;
        }
        return staffBdCodeMapper.getBDbyAgentIds(fkAgentIds);
    }

    @Override
    public Map<Long, String> getCompanyNamesByStaffIds(Set<Long> staffIds) {
        staffIds.removeIf(Objects::isNull);
        Result<Map<Long, String>> result = permissionCenterClient.getCompanyNamesByStaffIds(staffIds);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            return result.getData();
        }
        return Maps.newHashMap();
    }

    /**
     * 根据公司id获取bd员工下拉框数据
     *
     * @Date 12:55 2021/8/20
     * <AUTHOR>
     */
    @Override
    public List<BaseSelectEntity> getStaffByBdCompanyIds(Long companyId) {
//        Example example = new Example(StaffBdCode.class);
//        example.createCriteria().andEqualTo("fkCompanyId", companyId);
//        List<StaffBdCode> staffBdCodes = staffBdCodeMapper.selectByExample(example);
        List<StaffBdCode> staffBdCodes = staffBdCodeMapper.selectList(Wrappers.<StaffBdCode>lambdaQuery().in(StaffBdCode::getFkCompanyId, companyId));
        if (GeneralTool.isEmpty(staffBdCodes)) {
            return null;
        }

        Map<Long, String> map = new HashMap<>();
        for (StaffBdCode staffBdCode : staffBdCodes) {
            map.put(staffBdCode.getFkStaffId(), staffBdCode.getBdCode());
        }
        Set<Long> staffIds = staffBdCodes.stream().map(StaffBdCode::getFkStaffId).collect(Collectors.toSet());
        List<BaseSelectEntity> staffList = new ArrayList<>();
        Result<List<BaseSelectEntity>> result = permissionCenterClient.getStaffByStaffIds(staffIds);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            staffList.addAll(result.getData());
        }
        for (BaseSelectEntity baseSelectEntity : staffList) {
            String fullName = baseSelectEntity.getFullName() + "(" + map.get(baseSelectEntity.getId()) + ")";
            baseSelectEntity.setFullName(fullName);
            baseSelectEntity.setNum(map.get(baseSelectEntity.getId()));
        }
        List<BaseSelectEntity> end = staffList.stream().filter(s -> s.getStatus() == 0).collect(Collectors.toList());
        List<BaseSelectEntity> first = staffList.stream().filter(s -> s.getStatus() == 1).collect(Collectors.toList());
        List<BaseSelectEntity> entities = first.stream().sorted(Comparator.comparing(BaseSelectEntity::getNum)).collect(Collectors.toList());
        List<BaseSelectEntity> c2 = end.stream().sorted(Comparator.comparing(BaseSelectEntity::getNum)).collect(Collectors.toList());
        entities.addAll(c2);
        return entities;
    }

    /**
     * @Description: 获取BD团队配置IAE下的所有大区ids
     * @Author: Jerry
     * @Date:12:16 2021/9/1
     */
    @Override
    public Set<Long> getAreaRegionIdsByCompanyId() {
        Set<Long> areaRegionIds = new HashSet<>();
//        Example example = new Example(StaffBdCode.class);
//        example.createCriteria().andEqualTo("fkCompanyId", 3);//固定3，代表IAE
//        List<StaffBdCode> staffBdCodes = staffBdCodeMapper.selectByExample(example);
        List<StaffBdCode> staffBdCodes = staffBdCodeMapper.selectList(Wrappers.<StaffBdCode>lambdaQuery().in(StaffBdCode::getFkCompanyId, 3));

        if (GeneralTool.isEmpty(staffBdCodes)) {
            return areaRegionIds;
        }
        for (StaffBdCode staffBdCode : staffBdCodes) {
            if (GeneralTool.isNotEmpty(staffBdCode.getFkAreaRegionId())) {
                String[] fkAreaRegionIds = staffBdCode.getFkAreaRegionId().split(",");
                for (String fkAreaRegionId : fkAreaRegionIds) {
                    areaRegionIds.add(Long.valueOf(fkAreaRegionId));
                }
            }
        }
        return areaRegionIds;
    }


    /**
     * @return java.util.List<java.lang.Long>
     * @Description :获取登录人对应所有公司id集合
     * @Param []
     * <AUTHOR>
     */
    private List<Long> getCompanyIds() {
        List<Long> companyIds = SecureUtil.getCompanyIdsByStaffId(GetAuthInfo.getStaffId());
        if (GeneralTool.isEmpty(companyIds)) {
            companyIds.add(0L);
        }
        return companyIds;
    }

    /**
     * @Description :feign调用一次 查出全部对应公司名称
     * @Param [companyIds]
     * <AUTHOR>
     */
    private Map<Long, String> getCompanyNameMap(Set<Long> companyIds) {
        companyIds.removeIf(Objects::isNull);
        //feign调用一次查出全部对应公司名称
//        return permissionCenterClient.getCompanyNamesByIds(companyIds);

        Result<Map<Long, String>> result = permissionCenterClient.getCompanyNamesByIds(companyIds);
        if (result.isSuccess() && result.getData() != null) {
            return result.getData();
        }
        return new HashMap<>();
    }

    /**
     * @Description :feign调用一次查出全部负责人对应名称
     * @Param [staffIds]
     * <AUTHOR>
     */
    private Map<Long, String> getStaffNameMap(Set<Long> staffIds) {
        staffIds.removeIf(Objects::isNull);
        //feign调用一次查出全部对应员工名称
        Result<Map<Long, String>> staffResult = permissionCenterClient.getStaffNameMap(staffIds);
        Map<Long, String> staffNameMap = new HashMap<>();
        if (staffResult.isSuccess() && staffResult.getData() != null) {
            staffNameMap = staffResult.getData();
        }
        return staffNameMap;
    }


    /**
     * 校验员工或者bd编号是否存在
     *
     * @param fkStaffIds
     * @param bdCodes
     * @param isUpdate
     * @param id
     */
    private void validateStaffIdAndBdCode(Set<Long> fkStaffIds, Set<String> bdCodes, boolean isUpdate, Long id) {
        LambdaQueryWrapper<StaffBdCode> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        //查询BD编号或者员工名称是否重复
        lambdaQueryWrapper.and(wrapper -> wrapper.in(StaffBdCode::getFkStaffId, fkStaffIds).or().in(StaffBdCode::getBdCode, bdCodes));
        //如果是修改的话要判断是否是保存原数据的
        if (isUpdate) {
            lambdaQueryWrapper.and(wrapper -> wrapper.ne(StaffBdCode::getId, id));
        }
        List<StaffBdCode> staffBdCodes = staffBdCodeMapper.selectList(lambdaQueryWrapper);
        if (GeneralTool.isNotEmpty(staffBdCodes)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("staff_or_bacode_exists"));
        }
    }

    @Override
    public Long getStaffIdByBdCode(String bdCode) {
        List<StaffBdCode> staffBdCodes = staffBdCodeMapper.selectList(Wrappers.<StaffBdCode>lambdaQuery().eq(StaffBdCode::getBdCode, bdCode));
        if (GeneralTool.isEmpty(staffBdCodes)) {
            return null;
        }
        List<Long> collect = staffBdCodes.stream().map(StaffBdCode::getFkStaffId).collect(Collectors.toList());
        return collect.get(0);
    }

    @Override
    public Long getAreaRegionIdIdByBdCode(String bdCode) {
        List<StaffBdCode> staffBdCodes = staffBdCodeMapper.selectList(Wrappers.<StaffBdCode>lambdaQuery().eq(StaffBdCode::getBdCode, bdCode));
        if (GeneralTool.isEmpty(staffBdCodes)) {
            return null;
        }
        List<Long> ids = new ArrayList<>();
        for (StaffBdCode staffBdCode : staffBdCodes) {
            String fkAreaRegionId = staffBdCode.getFkAreaRegionId();
            ids.add(Long.valueOf(fkAreaRegionId));
        }
        return ids.get(0);
    }

    @Override
    public List<BaseSelectEntity> getStaffByBdName(List<Long> companyIds, String bdName) {
//        List<BaseSelectEntity> datas = staffBdCodeMapper.getStaffByBdName(companyId,bdName);
//        List<StaffBdCode> staffBdCodes = staffBdCodeMapper.selectList(Wrappers.<StaffBdCode>lambdaQuery().in(StaffBdCode::getFkCompanyId, companyId));
//        if (GeneralTool.isEmpty(staffBdCodes)) {
//            return null;
//        }
//
//        Map<Long, String> map = new HashMap<>();
//        for (StaffBdCode staffBdCode : staffBdCodes) {
//            map.put(staffBdCode.getFkStaffId(), staffBdCode.getBdCode());
//        }
//        Set<Long> staffIds = staffBdCodes.stream().map(StaffBdCode::getFkStaffId).collect(Collectors.toSet());
//        List<BaseSelectEntity> staffList = new ArrayList<>();
//        Result<List<BaseSelectEntity>> result = permissionCenterClient.getStaffByStaffIds(staffIds);
//        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
//            staffList.addAll(result.getData());
//        }
//        for (BaseSelectEntity baseSelectEntity : staffList) {
//            String fullName = baseSelectEntity.getFullName() + "(" + map.get(baseSelectEntity.getId()) + ")";
//            baseSelectEntity.setFullName(fullName);
//            baseSelectEntity.setNum(map.get(baseSelectEntity.getId()));
//        }
        return staffBdCodeMapper.getStaffByBdName(companyIds,bdName);
    }
    @Override
    public List<Long> getStaffIdsByFkAreaRegionId(Long fkAreaRegionId){
        if(GeneralTool.isEmpty(fkAreaRegionId)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        return staffBdCodeMapper.getStaffIdsByFkAreaRegionId(fkAreaRegionId);
    }

    /**
     * 代理申请列表  不计算离职
     * @param companyId
     * @return
     */
    @Override
    public List<BdSelectVo> getBdSelectByCompanyId(Long companyId) {
        if (GeneralTool.isEmpty(companyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        // BD_DDL_FILTER BD下拉过滤配置，value1=通用字符过滤，value2=BDCode过滤
        Map<Long, String> charFilterMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.BD_DDL_FILTER.key, 1).getData();
        Map<Long, String> bdCodeFilterMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.BD_DDL_FILTER.key, 2).getData();
        String configValue1 = charFilterMap.get(companyId);
        String configValue2 = bdCodeFilterMap.get(companyId);
        JSONArray charFilter = JSONObject.parseArray(configValue1);
        JSONArray bdCodeFilter = JSONObject.parseArray(configValue2);
        List<String> charFilterList = charFilter.toJavaList(String.class);
        List<Long> bdCodeFilterList = bdCodeFilter.toJavaList(Long.class);

        // 获取指定公司的bd列表，并排除指定bd，过滤离职
        return staffBdCodeMapper.getBdSelect(companyId, charFilterList, bdCodeFilterList);
    }

    @Override
    public List<BdSelectVo> getBdSelectAgentOnlineForm(Long companyId) {
        if (GeneralTool.isEmpty(companyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        // BD_DDL_FILTER BD下拉过滤配置，value1=通用字符过滤，value2=BDCode过滤
        Map<Long, String> charFilterMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.BD_DDL_FILTER.key, 1).getData();
        Map<Long, String> bdCodeFilterMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.BD_DDL_FILTER.key, 2).getData();
        String configValue1 = charFilterMap.get(companyId);
        String configValue2 = bdCodeFilterMap.get(companyId);
        JSONArray charFilter = JSONObject.parseArray(configValue1);
        JSONArray bdCodeFilter = JSONObject.parseArray(configValue2);
        List<String> charFilterList = charFilter.toJavaList(String.class);
        List<Long> bdCodeFilterList = bdCodeFilter.toJavaList(Long.class);


        // 获取所有公司的bd列表，并排除指定bd，过滤离职
        List<BdSelectVo> bdSelect = staffBdCodeMapper.getBdSelect(companyId, charFilterList, bdCodeFilterList);
        if (GeneralTool.isEmpty(bdSelect)) {
            return Collections.emptyList();
        }

        // 获取BD所属的大区
        List<String> fkAreaRegionIds = bdSelect.stream().map(BdSelectVo::getFkAreaRegionId)
                .filter(GeneralTool::isNotBlank).collect(Collectors.toList());
        Set<Long> areaRegionIds = fkAreaRegionIds.stream()
                .flatMap(str -> Arrays.stream(str.split(","))) // 按逗号分割字符串
                .filter(GeneralTool::isNotBlank)
                .map(Long::valueOf) // 将分割后的字符串转换为Long类型
                .collect(Collectors.toSet());
        Result<Map<Long, AreaRegionVo>> areaRegionDtoByIds = institutionCenterClient.getAreaRegionDtoByIds(areaRegionIds);
        // key：大区Id value：大区编号
        Map<Long, String> areaRegionNumMap = Maps.newHashMap();
        if (areaRegionDtoByIds.isSuccess() && GeneralTool.isNotEmpty(areaRegionDtoByIds.getData())) {
            areaRegionDtoByIds.getData().forEach((k, v) -> areaRegionNumMap.put(k, v.getNum()));
        }
        for (BdSelectVo bdSelectVo : bdSelect) {
            Long fkCompanyId = bdSelectVo.getFkCompanyId();
            String fkAreaRegionId = bdSelectVo.getFkAreaRegionId();
            List<String> nums = Lists.newArrayList();
            if (GeneralTool.isNotBlank(fkAreaRegionId)) {
                List<Long> ids = Arrays.stream(fkAreaRegionId.split(","))
                        .filter(GeneralTool::isNotBlank)
                        .map(Long::valueOf)
                        .collect(Collectors.toList());
                nums = ids.stream().map(areaRegionNumMap::get).filter(GeneralTool::isNotBlank).collect(Collectors.toList());
            }
            /*
             * 国内BD：公司id=3 并且 所属大区编号!=RG000031
             * 国外BD：!(公司id=3 并且 所属大区编号!=RG000031)
             */
            bdSelectVo.setIsChinaAgent(fkCompanyId == 3 && !nums.contains("RG000031"));
        }
        return bdSelect;
    }

    @Override
    public Map<String, String> getAreaRegionBybdCode(Set<String> bdCodes) {
        Map<String, String> map = new HashMap<>();
        List<StaffBdCode> staffBdCodes = staffBdCodeMapper.selectList(new LambdaQueryWrapper<StaffBdCode>().in(StaffBdCode::getBdCode, bdCodes));
        if (GeneralTool.isNotEmpty(staffBdCodes)){
            Set<Long> areaRegionIds = new HashSet<>();
            Set<String> areaRegions = staffBdCodes.stream().map(StaffBdCode::getFkAreaRegionId).collect(Collectors.toSet());
            for (String areaRegion : areaRegions) {
                String[] split = areaRegion.split(",");

                for (String sp : split) {
                    if (GeneralTool.isNotEmpty(sp)) {
                        Long areaRegionId = Long.valueOf(sp);
                        areaRegionIds.add(areaRegionId);
                    }
                }
            }
            Map<Long, String> areaRegionNameMap = institutionCenterClient.getAreaRegionNameByIds(areaRegionIds);
            for (StaffBdCode staffBdCode : staffBdCodes) {
                String fkAreaRegionId = staffBdCode.getFkAreaRegionId();
                if (GeneralTool.isEmpty(fkAreaRegionId)){
                    continue;
                }
                String[] split = fkAreaRegionId.split(",");

                for (String sp : split) {
                    if (GeneralTool.isNotEmpty(sp)) {
                        Long areaRegionId = Long.valueOf(sp);
                        if (GeneralTool.isNotEmpty(areaRegionNameMap) && GeneralTool.isNotEmpty(areaRegionNameMap.get(areaRegionId))){
                            map.put(staffBdCode.getBdCode(),areaRegionNameMap.get(areaRegionId));
                        }
                    }
                }
            }
        }
        return map;
    }

    @Override
    public List<BaseSelectEntity> getStaff(List<Long> companyIds) {
        return staffBdCodeMapper.getStaff(companyIds);
    }
}

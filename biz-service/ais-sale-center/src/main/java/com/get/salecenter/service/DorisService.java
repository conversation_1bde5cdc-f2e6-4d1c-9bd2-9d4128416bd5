package com.get.salecenter.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.salecenter.vo.PayablePlanNewVo;
import com.get.salecenter.vo.ReceivablePlanNewVo;
import com.get.salecenter.dto.ReceivablePlanNewDto;
import com.get.salecenter.dto.query.PayablePlanNewQueryDto;

import java.util.List;

public interface DorisService {

    List<ReceivablePlanNewVo> getDorisReceivablePlanNewDtos(ReceivablePlanNewDto receivablePlanNewDto, IPage<ReceivablePlanNewVo> iPage);

    List<PayablePlanNewVo> getDorisPayablePlanNewDtos(PayablePlanNewQueryDto payablePlanNewVo, IPage<PayablePlanNewVo> iPage);
}

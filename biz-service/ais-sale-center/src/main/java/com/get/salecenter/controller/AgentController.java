package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.utils.GeneralTool;
import com.get.pmpcenter.dto.agent.AgentCommissionTypeAgentDto;
import com.get.salecenter.vo.*;
import com.get.salecenter.service.IAgentService;
import com.get.salecenter.dto.*;
import com.get.salecenter.dto.query.AgentContractQueryDto;
import com.get.salecenter.dto.query.AgentQueryDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: Sea
 * @create: 2020/7/7 11:52
 * @verison: 1.0
 * @description: 代理安排管理控制器
 */
@Api(tags = "代理管理")
@RestController
@RequestMapping("sale/agent")
public class AgentController {
    @Resource
    private IAgentService agentService;

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "代理详情", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/代理管理/代理详情")
    @GetMapping("/{id}")
    public ResponseBo<AgentVo> detail(@PathVariable("id") Long id) {
        AgentVo data = agentService.findAgentById(id);
        return new ResponseBo<>(data);
    }

    /**
     * 新增信息
     *
     * @param agentDto
     * @return
     * @
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/代理管理/新增代理")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(AgentDto.Add.class) AgentDto agentDto) {
        return SaveResponseBo.ok(agentService.addAgent(agentDto));
    }


    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/代理管理/新增代理&联系人")
    @PostMapping("addAgent")
    public ResponseBo<Long> addAgent(@RequestBody @Validated(AgentAddDto.Add.class)   AgentAddDto agentAddDto) {
        return new ResponseBo<>(agentService.addAgent(agentAddDto));
    }

    /**
     * 删除信息
     *
     * @param id
     * @return
     * @
     */
//    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
//    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/代理管理/删除代理")
//    @PostMapping("delete/{id}")
//    public ResponseBo delete(@PathVariable("id") Long id) {
//        agentService.deleteAgent(id);
//        return DeleteResponseBo.ok();
//    }

    /**
     * 修改信息
     *
     * @param agentVo
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/代理管理/更新代理")
    @PostMapping("update")
    public ResponseBo<AgentVo> update(@RequestBody @Validated(AgentUpdateDto.Add.class) AgentUpdateDto agentVo) {
        return UpdateResponseBo.ok(agentService.updateAgent(agentVo));
    }

    /**
     * 列表数据
     *
     * @param page
     * @return
     */
    @ApiOperation(value = "列表数据,有业务上司业务下属的权限", notes = "num代理编号 name代理名称")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/代理管理/查询代理")
    @PostMapping("datas")
    public ResponseBo<AgentVo> datas(@RequestBody SearchBean<AgentQueryDto> page) {
        List<AgentVo> datas = agentService.getAgents(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    @ApiOperation(value = "获取代理激活数量信息", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/代理管理/获取代理激活数量信息")
    @PostMapping("getAgentActiveInfo")
    public ResponseBo<AgentBusinessInfoVo> getAgentActiveInfo(@RequestBody AgentQueryDto agentVo){
        return agentService.getAgentActiveInfo(agentVo);
    }

    /**
     * 列表数据
     *
     * @param page
     * @return
     */
    @ApiOperation(value = "列表数据，无业务上司业务下属的权限", notes = "num代理编号 name代理名称")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/代理管理/查询代理")
    @PostMapping("dataList")
    public ResponseBo<AgentVo> dataList(@RequestBody SearchBean<AgentQueryDto> page) {
        List<AgentVo> datas = agentService.dataList(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 代理合同列表数据
     *
     * @param searchBean
     * @return
     * @
     */
    @ApiOperation(value = "代理合同列表数据")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/代理管理/查询代理合同")
    @PostMapping("getAgentContract")
    public ResponseBo<AgentContractVo> getAgentContractDtos(@RequestBody SearchBean<AgentContractQueryDto> searchBean) {
        List<AgentContractVo> datas = agentService.getAgentContractDtos(searchBean.getData(), searchBean);
        Page p = BeanCopyUtils.objClone(searchBean, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 新增代理合同
     * @Param [contractVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增代理合同", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/代理管理/新增")
    @PostMapping("addAgentContract")
    public ResponseBo addAgentContract(@RequestBody @Validated(AgentContractDto.Add.class)  AgentContractDto contractVo) {
        return SaveResponseBo.ok(agentService.addApprovedAgentContract(contractVo));
    }


    /**
     * 查询代理附件
     *
     * @param voSearchBean
     * @return
     * @
     */
    @ApiOperation(value = "查询代理附件", notes = "keyWord为关键词")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/代理管理/查询代理附件")
    @PostMapping("getAgentMedia")
    public ResponseBo<MediaAndAttachedVo> getAgentMedia(@RequestBody SearchBean<MediaAndAttachedDto> voSearchBean) {
        List<MediaAndAttachedVo> staffMedia = agentService.getAgentMedia(voSearchBean.getData(), voSearchBean);
        Page page = BeanCopyUtils.objClone(voSearchBean, Page::new);
        return new ListResponseBo<>(staffMedia, page);
    }

    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "解析营业执照", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/代理管理/解析营业执照")
    @PostMapping("analysisOfBusinessLicense")
    public ResponseBo<BusinessLicenseResultDto> analysisOfBusinessLicense(MultipartFile file) {
       return agentService.analysisOfBusinessLicense(file);
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "解析代理身份证信息", notes = "")
    @VerifyLogin(IsVerify = false)
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/代理管理/解析代理身份证信息")
    @PostMapping("verifyIdCard")
    public ResponseBo<String> verifyIdCard(@RequestParam("file") MultipartFile file,@RequestParam("type") Integer type) {
        return agentService.verifyIdCard(file,type);
    }
    /**
     * 代理附件保存接口
     *
     * @param mediaAttachedVo
     * @return
     * @
     */

    @ApiOperation(value = "代理附件保存接口")
    @OperationLogger(module = LoggerModulesConsts.SYSTEMCENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/代理管理/代理附件保存接口")
    @PostMapping("upload")
    public ResponseBo<MediaAndAttachedVo> addAgentMedia(@RequestBody @Validated(MediaAndAttachedDto.Add.class) ValidList<MediaAndAttachedDto> mediaAttachedVo) {
        return new ListResponseBo<>(agentService.addAgentMedia(mediaAttachedVo));
    }

    /**
     * 所属代理下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "所属代理下拉框", notes = "isPersonalName是否获取PersonalName的名称 默认不是")
    @GetMapping("getAgentList")
    public ResponseBo<BaseSelectEntity> getAgentList(@RequestParam("companyId") Long companyId,@RequestParam(value = "isPersonalName",required = false)Boolean isPersonalName) {
        List<BaseSelectEntity> datas = agentService.getAgentList(companyId,isPersonalName);
        return new ListResponseBo<>(datas);
    }

    /**
     * 峰会参会人员编辑代理下拉框
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "峰会参会人员编辑代理下拉框", notes = "")
    @GetMapping("getConventionAgentList")
        public ResponseBo<BaseSelectEntity> getConventionAgentList(@RequestParam("companyId") Long companyId) {
        List<BaseSelectEntity> datas = agentService.getConventionAgentList(companyId);
        return new ListResponseBo<>(datas);
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "所属代理下拉框(字段延展)", notes = "isPersonalName是否获取PersonalName的名称 默认不是")
    @GetMapping("getAgentListNew")
    public ResponseBo<AgentListVo> getAgentListNew(@RequestParam("companyId") Long companyId, @RequestParam(value = "isPersonalName",required = false)Boolean isPersonalName) {
        List<AgentListVo> datas = agentService.getAgentListNew(companyId,isPersonalName);
        return new ListResponseBo<>(datas);
    }

    /**
     * 代理联系人列表
     *
     * @param searchBean
     * @return
     * @
     */
    @ApiOperation(value = "代理联系人列表")
    @VerifyLogin(IsVerify = false)
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/联系人管理/代理联系人列表")
    @PostMapping("getAgentContactPerson")
    public ResponseBo<ContactPersonVo> getAgentContactPerson(@RequestBody SearchBean<ContactPersonDto> searchBean) {
        List<ContactPersonVo> contactPersonVos = agentService.getAgentContactPersonDtos(searchBean.getData(), searchBean);
        Page p = BeanCopyUtils.objClone(searchBean, Page::new);
        return new ListResponseBo<>(contactPersonVos, p);
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 新增代理联系人
     * @Param [contactPersonVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增代理联系人", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/代理管理/新增代理联系人")
    @PostMapping("addContactPerson")
    public ResponseBo addContactPerson(@RequestBody @Validated(ContactPersonDto.Add.class) ContactPersonDto contactPersonVo) {
        return SaveResponseBo.ok(agentService.addAgentContactPerson(contactPersonVo));
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 代理公司安全配置
     * @Param [validList]
     * <AUTHOR>
     */
    @ApiOperation(value = "代理-公司安全配置")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/代理管理/安全配置")
    @PostMapping("editAgentCompanyRelation")
    public ResponseBo editAgentCompanyRelation(@RequestBody   @Validated(AgentCompanyDto.Add.class)  ValidList<AgentCompanyDto> validList) {
        agentService.editAgentCompany(validList);
        return UpdateResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.CompanyTreeVo>
     * @Description: 回显代理和公司的关系
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "回显代理和公司的关系", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/学生代理合同管理/代理和公司的关系（数据回显）")
    @GetMapping("getAgentRelation/{agentId}")
    public ResponseBo<CompanyTreeVo> getContractCompanyRelation(@PathVariable("agentId") Long id) {
        List<CompanyTreeVo> contractCompanyRelation = agentService.getAgentCompanyRelation(id);
        return new ListResponseBo<>(contractCompanyRelation);
    }

    /**
     * @Description：查询所有代理
     * @Param
     * @Date 12:32 2021/5/11
     * <AUTHOR>
     */
    @ApiOperation(value = "查询所有代理", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/代理管理/查询所有代理列表")
    @GetMapping("getAllAgent")
    @VerifyLogin(IsVerify = false)
    public ResponseBo<AgentVo> getAllAgent() {
        List<AgentVo> datas = agentService.getAllAgent();
        return new ListResponseBo<>(datas);
    }

    @ApiIgnore
    @GetMapping("getAgentNameById/{agentId}")
    @VerifyLogin(IsVerify = false)
    public String getAgentNameById(@PathVariable("agentId") Long agentId) {
        return agentService.getNameById(agentId);
    }

    @ApiIgnore
    @GetMapping("getAgentIds")
    @VerifyLogin(IsVerify = false)
    public List<Long> getAgentListIds(@RequestParam String name) {
        return agentService.getAgentListIds(name);
    }

    /**
     * @Description :feign调用 根据ids查找代理名称map
     * @Param [agentIds]
     * <AUTHOR>
     */
    @ApiIgnore
    @PostMapping("getAgentNamesByIds")
    @VerifyLogin(IsVerify = false)
    public Map<Long, String> getAgentNamesByIds(@RequestBody Set<Long> agentIds) {
        return agentService.getAgentNamesByIds(agentIds);
    }

//    @ApiIgnore
//    @PostMapping("commissionSummaryForIfile")
//    public List<CommissionSummaryBatchItemVo> commissionSummaryForIfile(@RequestBody CommissionSummaryBatchDto commissionSummaryBatchDto) {
//        return agentService.commissionSummaryForIfile(commissionSummaryBatchDto);
//    }

    /**
     * @ Description :
     * @ Param [studentName, value, type]
     * @ return com.get.common.result.ResponseBo
     * @ author LEO
     */
    @ApiOperation("检测是否存在相同代理")
    @GetMapping("getIsExistAgent")
    @VerifyPermission(IsVerify = false)
    public ResponseBo getIsExistAgent(@RequestParam("companyId") Long companyId, @RequestParam(value = "idCard", required = false) String idCard,
                                      @RequestParam(value = "id", required = false) Long id,
                                      @RequestParam(value = "name", required = false) String name,
                                      @RequestParam(value = "taxCode", required = false) String taxCode,
                                      @RequestParam(value = "nature", required = false) String nature,
                                      @RequestParam(value = "legalPerson", required = false) String legalPerson) {
        Map<Long, String> map = agentService.getIsExistAgent(companyId, id, name, taxCode, nature, legalPerson, idCard);
        ResponseBo responseBo = new ResponseBo();
        if (GeneralTool.isNotEmpty(map)) {
            responseBo.setData(map.entrySet().stream().findFirst().get().getKey());
            responseBo.setMessage(map.entrySet().stream().findFirst().get().getValue());
        }
        return responseBo;
    }

//    @GetMapping("/getIssueUserByAgentId")
//    @ApiOperation("根据代理id获取ISSUE账号信息")
//    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/代理管理/根据代理id获取ISSUE账号信息")
//    public ResponseBo getIssueUserByAgentId(@RequestParam("fkAgentId") Long fkAgentId) {
//        return new ResponseBo(agentService.getIssueUserByAgentId(fkAgentId));
//    }


    /**
     * @Description:
     * @Param: page
     * @return:
     * @Author: Walker
     * @Date: 2022/3/15
     */
    @ApiOperation(value = "查询代理积分列表", notes = "现在是2025的逻辑")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/代理管理/查询代理积分列表")
    @PostMapping("/getSource")
    public ResponseBo<AgentSourceVo> getSource(@RequestBody SearchBean<AgentSourceDto> page) {
        List<AgentSourceVo> datas = agentService.getSource(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @Description:导出代理积分
     * @Param:AgentSourceVo
     * @return:
     * @Author: Walker
     * @Date: 2022/3/15
     */
    @ApiOperation(value = "导出代理积分")
    @PostMapping("/exportSource")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/代理管理/导出代理积分")
    @ResponseBody
    public void exportConventionRegistrationExcel(HttpServletResponse response, @RequestBody AgentSourceDto agentSourceDto) {
        agentService.exportAgentSourceExcel(response, agentSourceDto);
    }

    /**
     * @Description:根据cpp_id或者bms_id获取代理的总积分
     * @Param:cpp_id，bms_id
     * @return:
     * @Author: Walker
     * @Date: 2022/3/21
     */
    @ApiOperation(value = "根据cpp_id或者bms_id获取代理的总积分")
    //@PostMapping("/getSumSourceByCppIdOrBmsId")
    @RequestMapping(value = "/getSumSourceByCppIdOrBmsId", method = RequestMethod.GET)
    public ResponseBo getSumSourceByCppIdOrBmsId(@RequestParam(value = "cpp_id", required = false) String cpp_id, @RequestParam(value = "bms_id", required = false) String bms_id) {
        AgentSourceVo agentSourceVo = agentService.getSumSourceByCppIdOrBmsId(cpp_id, bms_id, null);
        return new ResponseBo(agentSourceVo);
    }

    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "旧issue获取 根据cpp_id或者bms_id获取代理的总积分")
    //@PostMapping("/getSumSourceByCppIdOrBmsId")
    @RequestMapping(value = "/getOldSumSourceByCppIdOrBmsId", method = RequestMethod.POST)
    public ResponseBo getOldSumSourceByCppIdOrBmsId(HttpServletRequest request) throws Exception {
        BufferedReader reader = request.getReader();
        String str, wholeStr = "";
        while ((str = reader.readLine()) != null) {
            wholeStr += str;
        }
        AgentSourceVo agentSourceVo = agentService.getSumSourceByCppIdOrBmsId(null, null, wholeStr);
        return new ResponseBo(agentSourceVo);
    }


    /**
     * 代理联系人所属代理下拉框数据
     *
     * @return
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "代理联系人所属代理下拉框数据", notes = "")
    @GetMapping("getContactPersonAgentList")
    public ResponseBo<BaseSelectEntity> getContactPersonAgentList(@RequestParam("companyId") Long companyId) {
        List<BaseSelectEntity> datas = agentService.getContactPersonAgentList(companyId);
        return new ListResponseBo<>(datas);
    }

    /**
     * 导出代理列表Excel
     *
     * @param response
     * @param agentVo
     */
    @ApiOperation(value = "导出代理列表Excel", notes = "")
    @PostMapping("/exportAgentDatasExcel")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/代理管理/导出代理列表Excel")
    @ResponseBody
    public void exportAgentDatasExcel(HttpServletResponse response, @RequestBody AgentQueryDto agentVo) {
        agentService.exportAgentDatasExcel(response, agentVo);
    }

    /**
     * 所属代理下拉框(百度式搜索)
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "所属代理下拉框(百度式搜索)", notes = "")
    @PostMapping("getAgentListByName")
    public ResponseBo<AgentSubVo> getAgentListByName(@RequestBody AgentCompanyAgentNameDto agentCompanyAgentNameDto) {
        List<AgentSubVo> datas = agentService.getAgentListByName(agentCompanyAgentNameDto.getCompanyIds(), agentCompanyAgentNameDto.getAgentName());
        return new ListResponseBo<>(datas);
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "根据代理名称或者编号查询代理下拉", notes = "")
    @PostMapping("getAgentListAll")
    public ResponseBo<AgentVo> getAgentListAll() {
        List<AgentVo> datas = agentService.getAgentListAll();
        return new ListResponseBo<>(datas);
    }

    /**
     * 代理联系人信息
     *
     * @return
     * @
     */
//    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "代理联系人信息", notes = "")
    @PostMapping("getContactPersonInfo")
    public ResponseBo<ContactPersonVo> getContactPersonInfo(@RequestParam("id") Long id) {
        List<ContactPersonVo> datas = agentService.getContactPersonInfo(id);
        return new ListResponseBo<>(datas);
    }

    /**
     * 绑定默认项目成员列表
     *
     * @return
     */
    @ApiOperation(value = "绑定默认项目成员列表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/代理管理/绑定默认项目成员列表")
    @PostMapping("getAgentDefaultProjectRole")
    public ResponseBo<StudentProjectRoleVo> getAgentDefaultProjectRole(@RequestParam("companyId")Long companyId) {
        List<StudentProjectRoleVo> datas = agentService.getAgentDefaultProjectRole(companyId);
        return new ListResponseBo<>(datas);
    }

    @ApiOperation("代理联系人")
    @GetMapping("getAgentContactPersonInfoByAgentId")
    public ResponseBo<ContactPersonVo> getAgentContactPersonByAgentId(@RequestParam("id")Long id){
        return new ListResponseBo<>(agentService.getAgentContactPersonByAgentId(id));
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "验证是否显示渠道代理",notes = "true显示/false不用显示")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/代理管理/验证是否显示渠道代理")
    @PostMapping("validateCustomerChannelRequired")
    public ResponseBo<Boolean> validateCustomerChannelRequired() {
        Boolean data = agentService.validateCustomerChannelRequired();
        return new ResponseBo<>(data);
    }

    @ApiOperation(value = "获取代理下拉框数据（名称/名称备注/公司）", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/代理管理/获取代理下拉框数据（名称/名称备注/公司）")
    @PostMapping("getAgentSelect")
    @VerifyPermission(IsVerify = false)
    public ResponseBo<BaseSelectEntity> getAgentSelect(@RequestParam("name") String name) {
        List<BaseSelectEntity> datas = agentService.getAgentSelect(name);
        return new ListResponseBo<>(datas);
    }

    /**
     * 所属代理下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "根据邮箱获取代理人id", notes = "根据邮箱获取代理人id")
    @GetMapping("getAgentIdByEmail")
    public ResponseBo<Long> getAgentIdByEmail(@RequestParam(value = "email")String email) {
        List<Long> datas = agentService.getAgentIdByEmail(email);
        return new ListResponseBo<>(datas);
    }

    @ApiOperation(value = "列表数据,有业务上司业务下属的权限", notes = "num代理编号 name代理名称")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/代理管理/查询代理")
    @PostMapping("getAgentCommissionTypeAndAgentIsBind")
    public ListResponseBo<AgentVo> getAgentCommissionTypeAndAgentIsBind(@RequestBody SearchBean<AgentCommissionTypeAgentDto> page) {
        AgenCommissionAndAgentSearchVo agentCommissionTypeAndAgentIsBind = agentService.getAgentCommissionTypeAndAgentIsBind(page.getData(), page);
        return new ListResponseBo<>(agentCommissionTypeAndAgentIsBind.getAgentCommissionAndAgentList());
    }

    /**
     * 代理合同续约
     *
     * @return
     */
    @ApiOperation(value = "代理合同续约", notes = "代理合同续约")
    @VerifyPermission(IsVerify = false)
    @PostMapping("renewalContract")
    public ResponseBo renewalContract(@RequestBody AgentContractRenewalDto agentContractRenewalDto) {
        this.agentService.sendEmailRenewalEmail(agentContractRenewalDto);
        return ResponseBo.ok();
    }

}

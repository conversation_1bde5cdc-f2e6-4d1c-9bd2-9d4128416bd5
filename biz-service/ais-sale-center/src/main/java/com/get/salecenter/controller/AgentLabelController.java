package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.salecenter.dto.AgentLabelDto;
import com.get.salecenter.service.AgentLabelService;
import com.get.salecenter.vo.AgentLabelVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

@Api(tags = "代理及代理联系人邮箱 标记")
@RestController
@RequestMapping("sale/agentLable")
public class AgentLabelController {
    @Resource
    private AgentLabelService agentLabelService;

    /**
     * 新增代理及代理联系人邮箱标记
     *
     * @param
     * @return
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/代理及代理联系人邮箱标记/新增标记")
    @PostMapping("add")
    public ResponseBo<Long> addAgentLabel(@RequestBody @Validated AgentLabelDto agentLabelDto) {
        List<Long> ids = agentLabelService.addAgentLabel(agentLabelDto);
        return new ListResponseBo<>(ids);
    }

    @ApiOperation(value = "删除接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/代理及代理联系人邮箱标记/删除标记")
    @DeleteMapping("delete/{id}")
    public ResponseBo deleteAgentLabel(@PathVariable("id") Long id) {
        agentLabelService.deleteAgentLabel(id);
        return SaveResponseBo.ok();
    }

    @ApiOperation(value = "查询接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/代理及代理联系人邮箱标记/查询标记")
    @PostMapping("selectAgentLabel")
    public ResponseBo<AgentLabelVo> getAgentLabelList(@RequestBody SearchBean<AgentLabelDto> page) {
        List<AgentLabelVo> datas = agentLabelService.dataList(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);

    }

    @ApiOperation(value = "查询接口", notes = "其他服务调用使用")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/代理及代理联系人邮箱标记/查询标记")
    @PostMapping("selectAgentLabelsByAgentIdsAndEmails")
    public ResponseBo<AgentLabelVo> getAgentLabelListByAgentIdsAndEmails(@RequestParam("agentIds") Set<Long> agentIds,@RequestParam("labelEmails") Set<String> labelEmails) {
        List<AgentLabelVo> datas = agentLabelService.getAgentLabelListByAgentDto(agentIds, labelEmails);
        return new ListResponseBo<>(datas);

    }

    @ApiOperation(value = "获取标签信息接口", notes = "服务调用")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/代理及代理联系人邮箱标记/查询标签")
    @PostMapping("selectLabels")
    public ResponseBo getLabels(@RequestBody SearchBean<AgentLabelDto> page) {
        return agentLabelService.getLabels(page);
    }
}


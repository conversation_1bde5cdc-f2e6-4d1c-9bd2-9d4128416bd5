package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.service.impl.GetServiceImpl;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.StaffCommissionInstitutionMapper;
import com.get.salecenter.vo.StaffCommissionInstitutionVo;
import com.get.salecenter.entity.StaffCommissionInstitution;
import com.get.salecenter.service.IStaffCommissionInstitutionService;
import com.get.salecenter.dto.StaffCommissionInstitutionDto;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: Hardy
 * @create: 2023/2/7 16:42
 * @verison: 1.0
 * @description:
 */
@Service
public class StaffCommissionInstitutionServiceImpl extends GetServiceImpl<StaffCommissionInstitutionMapper, StaffCommissionInstitution> implements IStaffCommissionInstitutionService {

    @Resource
    private UtilService utilService;

    /**
     * 新增
     * @param staffCommissionInstitutionDto
     * @return
     */
    @Override
    public Long addStaffCommissionInstitution(StaffCommissionInstitutionDto staffCommissionInstitutionDto) {
        if (GeneralTool.isEmpty(staffCommissionInstitutionDto)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        if (GeneralTool.isEmpty(staffCommissionInstitutionDto.getFkInstitutionId())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        List<StaffCommissionInstitution> staffCommissionInstitutions = list(Wrappers.lambdaQuery(StaffCommissionInstitution.class).eq(StaffCommissionInstitution::getFkInstitutionId, staffCommissionInstitutionDto.getFkInstitutionId()));
        if (GeneralTool.isNotEmpty(staffCommissionInstitutions)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
        }
        StaffCommissionInstitution staffCommissionInstitution = BeanCopyUtils.objClone(staffCommissionInstitutionDto, StaffCommissionInstitution::new);
        assert staffCommissionInstitution != null;
        utilService.setCreateInfo(staffCommissionInstitution);
        boolean b = save(staffCommissionInstitution);
        if (!b){
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
        return staffCommissionInstitution.getId();
    }

    /**
     * 批量新增
     * @param staffCommissionInstitutionDtos
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(ValidList<StaffCommissionInstitutionDto> staffCommissionInstitutionDtos) {
        Set<Long> institutionIds = staffCommissionInstitutionDtos.stream().map(StaffCommissionInstitutionDto::getFkInstitutionId).collect(Collectors.toSet());
        List<StaffCommissionInstitution> staffCommissionInstitutionList = list(Wrappers.lambdaQuery(StaffCommissionInstitution.class).in(StaffCommissionInstitution::getFkInstitutionId, institutionIds));
        if (GeneralTool.isNotEmpty(staffCommissionInstitutionList)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
        }
        List<StaffCommissionInstitution> staffCommissionInstitutions = BeanCopyUtils.copyListProperties(staffCommissionInstitutionDtos, StaffCommissionInstitution::new);
        staffCommissionInstitutions.forEach(staffCommissionInstitution -> utilService.setCreateInfo(staffCommissionInstitution));
        boolean b = saveBatch(staffCommissionInstitutions);
        if (!b){
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
    }

    /**
     * 删除
     * @param id
     */
    @Override
    public void delete(Long id) {
        LambdaQueryWrapper<StaffCommissionInstitution> lambdaQueryWrapper = Wrappers.lambdaQuery(StaffCommissionInstitution.class);
        lambdaQueryWrapper.eq(StaffCommissionInstitution::getFkInstitutionId, id);
        boolean b = remove(lambdaQueryWrapper);
        if (!b){
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }

    /**
     * 获取学校是否激活提成
     * @param institutionIds
     * @return
     */
    @Override
    public Map<Long, Boolean> getCommissionActiveStatusByInstitutionIds(Set<Long> institutionIds) {
        if (institutionIds.isEmpty()) {
            return Collections.emptyMap();
        }
        Map<Long, Boolean> map = Maps.newHashMap();
        for (Long institutionId : institutionIds) {
            map.put(institutionId,false);
        }
        List<StaffCommissionInstitution> staffCommissionInstitutions = list(Wrappers.lambdaQuery(StaffCommissionInstitution.class).in(StaffCommissionInstitution::getFkInstitutionId, institutionIds));
        if (GeneralTool.isEmpty(staffCommissionInstitutions)){
            return map;
        }
        for (StaffCommissionInstitution staffCommissionInstitution : staffCommissionInstitutions) {
            map.put(staffCommissionInstitution.getFkInstitutionId(),true);
        }
        return map;
    }

    /**
     * 列表
     * @param staffCommissionInstitutionDto
     * @param page
     * @return
     */
    @Override
    public List<StaffCommissionInstitutionVo> getStaffCommissionInstitutionDtos(StaffCommissionInstitutionDto staffCommissionInstitutionDto, Page page) {
        if (GeneralTool.isEmpty(staffCommissionInstitutionDto)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        IPage<StaffCommissionInstitutionVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<StaffCommissionInstitutionVo> staffCommissionInstitutionVos = this.baseMapper.getStaffCommissionInstitutionDtos(iPage, staffCommissionInstitutionDto);
        page.setAll((int) iPage.getTotal());
        if (GeneralTool.isEmpty(staffCommissionInstitutionVos)){
            return Collections.emptyList();
        }
        for (StaffCommissionInstitutionVo staffCommissionInstitutionVo : staffCommissionInstitutionVos) {
            //不要关系表id 设置为学校id
            staffCommissionInstitutionVo.setId(staffCommissionInstitutionVo.getFkInstitutionId());
        }
        return staffCommissionInstitutionVos;
    }
}

package com.get.salecenter.controller;


import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.UpdateResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.salecenter.vo.KpiPlanGroupItemVo;
import com.get.salecenter.service.KpiPlanGroupItemService;
import com.get.salecenter.dto.KpiPlanGroupItemDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api(tags = "KPI方案组别子项管理")
@RestController
@RequestMapping("sale/kpiPlanGroupItem")
public class KpiPlanGroupItemController {

    @Resource
    private KpiPlanGroupItemService kpiPlanGroupItemService;

    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/KPI方案组别子项管理/新增")
    @PostMapping("add")
    public ResponseBo add(@RequestBody  @Validated(KpiPlanGroupItemDto.Add.class) KpiPlanGroupItemDto vo) {
        return SaveResponseBo.ok(kpiPlanGroupItemService.addKpiPlanGroupItem(vo));
    }

    @ApiOperation(value = "详情", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/KPI方案组别子项管理/详情")
    @GetMapping("/{id}")
    public ResponseBo<KpiPlanGroupItemVo> detail(@PathVariable("id") Long id) {
        KpiPlanGroupItemVo data = kpiPlanGroupItemService.findKpiPlanGroupItemById(id);
        return new ResponseBo<>(data);
    }

    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/KPI方案组别子项管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        kpiPlanGroupItemService.delete(id);
        return SaveResponseBo.ok();
    }

    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/KPI方案组别子项管理/更新")
    @PostMapping("update")
    public ResponseBo update(@RequestBody @Validated(KpiPlanGroupItemDto.Update.class) KpiPlanGroupItemDto vo) {
        kpiPlanGroupItemService.updateKpiPlanGroupItem(vo);
        return UpdateResponseBo.ok();
    }


    @ApiOperation(value = "拖拽", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/KPI方案组别子项管理/拖拽")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestParam("fkKpiPlanGroupId") Long fkKpiPlanGroupId,
                                  @RequestParam("start")Integer start,
                                  @RequestParam("end")Integer end) {
        kpiPlanGroupItemService.movingOrder(fkKpiPlanGroupId,start,end);
        return ResponseBo.ok();
    }

}

package com.get.salecenter.dao.insurance;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.salecenter.dto.InsuranceOrderDto;
import com.get.salecenter.entity.InsuranceOrder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Mapper
@DS("appInsurance")
public interface InsuranceOrderMapper extends BaseMapper<InsuranceOrder> {

    /**
     * 获取澳小保下拉框
     * @param companyIds
     * @param keyWord
     * @return
     */
    List<BaseSelectEntity> getInsuranceOrder(@Param("companyIds")List<Long> companyIds, @Param("keyWord") String keyWord);

    /**
     * 获取收款单绑定 澳小保应收计划对象信息
     * @return
     */
    List<BaseSelectEntity> getPlanInfoByTargetId(@Param("targetId") Long targetId, @Param("receiptFormId") Long receiptFormId );

    /**
     * 获取澳小保业务信息
     * @param insuranceOrderId
     * @return
     */
    InsuranceOrderDto getInsuranceOrderInformation(@Param("insuranceOrderId") Long insuranceOrderId);

    /**
     * 获取澳小保业务信息
     * @param insuranceOrderIds
     * @return
     */
    List<InsuranceOrderDto> getInsuranceOrderInformationList(@RequestBody List<Long> insuranceOrderIds);
}
package com.get.salecenter.service.impl;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.redis.cache.GetRedis;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.partnercenter.dto.RegisterPartnerUserDto;
import com.get.partnercenter.feign.IPartnerCenterClient;
import com.get.partnercenter.vo.RegisterPartnerUserVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.StaffVo;
import com.get.remindercenter.enums.EmailTemplateEnum;
import com.get.remindercenter.feign.IReminderCenterClient;
import com.get.salecenter.dao.sale.AgentContractApprovalMapper;
import com.get.salecenter.dto.AgentContractApprovalDto;
import com.get.salecenter.dto.EmailSendContext;
import com.get.salecenter.entity.Agent;
import com.get.salecenter.entity.AgentContract;
import com.get.salecenter.entity.AgentContractApprovalEntity;
import com.get.salecenter.entity.SaleContactPerson;
import com.get.salecenter.enums.AgentContractApprovalStatusEnum;
import com.get.salecenter.enums.ContactPersonTypeEnum;
import com.get.salecenter.enums.MiniProgramPageEnum;
import com.get.salecenter.service.IAgentContractApprovalService;
import com.get.salecenter.service.IAgentContractService;
import com.get.salecenter.service.IAgentService;
import com.get.salecenter.service.IAppAgentService;
import com.get.salecenter.service.IContactPersonService;
import com.get.salecenter.utils.EmailSenderUtils;
import com.get.salecenter.vo.AgentContractApprovalVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 合同审批 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AgentContractApprovalServiceImpl extends
        ServiceImpl<AgentContractApprovalMapper, AgentContractApprovalEntity> implements IAgentContractApprovalService {

    private final AgentContractApprovalMapper agentContractApprovalMapper;

    @Resource
    private UtilService utilService;

    @Resource
    private IReminderCenterClient reminderCenterClient;

    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Lazy
    @Resource
    private IAgentContractService agentContractService;

    @Resource
    private GetRedis getRedis;

    @Resource
    private IAgentService agentService;

    @Resource
    private IContactPersonService contactPersonService;

    @Resource
    private IPartnerCenterClient partnerCenterClient;

    @Resource
    private EmailSenderUtils emailSenderUtils;

    @Resource
    private IAppAgentService appAgentService;


    /**
     * 发送邮件
     *
     * @param id 审批记录ID
     */
    @Override
    public void sendEmail(Long id) {
        log.info("开始发送合同审批邮件，审批记录ID: {}", id);
        
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("approval_id_required"));
        }
        
        // 1. 查询审批记录
        AgentContractApprovalEntity approvalEntity = this.getById(id);
        if (GeneralTool.isEmpty(approvalEntity)) {
            log.error("审批记录不存在，审批记录ID: {}", id);
            throw new GetServiceException(LocaleMessageUtils.getFormatMessage("approval_record_not_found", id));
        }
        
        // 2. 构建审批DTO用于后续处理
        AgentContractApprovalDto approvalDto = BeanCopyUtils.objClone(approvalEntity, AgentContractApprovalDto::new);
        
        // 3. 根据审批状态发送相应邮件
        if (AgentContractApprovalStatusEnum.APPROVED.getCode().equals(approvalEntity.getApprovalStatus())) {
            processApprovalPassed(approvalDto);
            log.info("发送审核通过邮件完成，审批记录ID: {}", id);
        } else if (AgentContractApprovalStatusEnum.REJECTED.getCode().equals(approvalEntity.getApprovalStatus())) {
            processApprovalRejected(approvalDto);
            log.info("发送审核驳回邮件完成，审批记录ID: {}", id);
        } else {
            log.warn("审批状态不需要发送邮件，审批记录ID: {}, 状态: {}", id, approvalEntity.getApprovalStatus());
            throw new GetServiceException(LocaleMessageUtils.getFormatMessage("approval_status_no_email_required", approvalEntity.getApprovalStatus()));
        }
        
        log.info("合同审批邮件发送完成，审批记录ID: {}", id);
    }


    /**
     * 保存并发送邮件
     *
     * @param agentContractApprovalDto 合同审批DTO
     * @return 审批记录ID
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long saveAndSendEmail(AgentContractApprovalDto agentContractApprovalDto) {
        log.info("开始处理合同审批保存并发送邮件，合同ID: {}", agentContractApprovalDto.getFkAgentContractId());

        // 1. 验证并获取合同信息
        AgentContract contract = validateAndGetContract(agentContractApprovalDto.getFkAgentContractId());

        // 2. 处理未签署状态的合同
        processUnsignedContract(contract, agentContractApprovalDto);

        // 3. 保存审批记录并发送邮件
        Long approvalId = saveRecordAndSendEmail(agentContractApprovalDto);

        log.info("合同审批保存并发送邮件完成，合同ID: {}, 审批ID: {}",
                agentContractApprovalDto.getFkAgentContractId(), approvalId);
        return approvalId;
    }

    private Long saveRecordAndSendEmail(AgentContractApprovalDto agentContractApprovalDto) {
        log.info("开始处理合同审批，合同ID: {}, 审批状态: {}", 
                agentContractApprovalDto.getFkAgentContractId(), agentContractApprovalDto.getApprovalStatus());

        // 1. 参数校验
        if (GeneralTool.isEmpty(agentContractApprovalDto.getFkAgentContractId())) {
            log.error("合同Id为空");
            throw new GetServiceException(LocaleMessageUtils.getMessage("contract_id_required"));
        }
        if (GeneralTool.isEmpty(agentContractApprovalDto.getApprovalStatus())) {
            log.error("审批状态为空, 合同id是 : {}", agentContractApprovalDto.getFkAgentContractId());
            throw new GetServiceException(LocaleMessageUtils.getMessage("approval_status_required"));
        }

        // 2. 保存审批记录
        Long approvalId = saveApprovalRecord(agentContractApprovalDto);
        log.info("审批记录保存成功，审批ID: {}", approvalId);

        // 3. 处理审核通过的情况
        if (AgentContractApprovalStatusEnum.APPROVED.getCode().equals(agentContractApprovalDto.getApprovalStatus())) {
            processApprovalPassed(agentContractApprovalDto);
        } else if (AgentContractApprovalStatusEnum.REJECTED.getCode().equals(agentContractApprovalDto.getApprovalStatus())) {
            // 4. 处理审核驳回的情况
            processApprovalRejected(agentContractApprovalDto);
        }
        return approvalId;
    }

    /**
     * 合同审批记录列表查询
     * 
     * 功能说明：
     * 1. 根据合同ID查询该合同的所有审批记录
     * 2. 支持分页查询，按创建时间倒序排列
     * 3. 返回审批记录的详细信息
     *
     * @param agentContractApprovalDto 查询条件DTO，必须包含合同ID(fkAgentContractId)
     * @param page                     分页参数，包含当前页码和每页显示数量
     * @return 审批记录VO列表，包含审批人、审批意见、审批状态等信息
     * @throws GetServiceException 当合同ID为空时抛出参数缺失异常
     */
    @Override
    public List<AgentContractApprovalVo> getAgentContractApprovals(AgentContractApprovalDto agentContractApprovalDto,
            Page page) {
        // 参数校验：合同ID不能为空
        if (GeneralTool.isEmpty(agentContractApprovalDto.getFkAgentContractId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }

        // 构建查询条件
        LambdaQueryWrapper<AgentContractApprovalEntity> wrapper = Wrappers
                .lambdaQuery(AgentContractApprovalEntity.class);
        // 根据合同ID查询审批记录
        wrapper.eq(AgentContractApprovalEntity::getFkAgentContractId, agentContractApprovalDto.getFkAgentContractId());
        // 按创建时间倒序排列，最新的审批记录在前
        wrapper.orderByDesc(AgentContractApprovalEntity::getGmtCreate);

        // 执行分页查询
        IPage<AgentContractApprovalEntity> pages = this.page(
                GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<AgentContractApprovalEntity> agentContractApprovals = pages.getRecords();
        // 设置分页总数
        page.setAll((int) pages.getTotal());

        // 实体转换为VO对象
        List<AgentContractApprovalVo> agentContractApprovalVos = BeanCopyUtils
                .copyListProperties(agentContractApprovals, AgentContractApprovalVo::new);

        return agentContractApprovalVos;
    }

    /**
     * 保存审批记录
     *
     * @param agentContractApprovalDto 合同审批DTO
     * @return 审批记录ID
     */
    private Long saveApprovalRecord(AgentContractApprovalDto agentContractApprovalDto) {
        log.info("开始保存合同审批意见，合同ID：{}", agentContractApprovalDto.getFkAgentContractId());
        Long agentContractId = agentContractApprovalDto.getFkAgentContractId();
        
        // 验证合同是否存在
        AgentContract agentContract = this.agentContractService.getById(agentContractId);
        if (ObjectUtil.isEmpty(agentContract)) {
            log.error("代理合同不存在, 保存失败, id是 : {}", agentContractId);
            throw new GetServiceException(LocaleMessageUtils.getFormatMessage("contract_not_found", agentContractId));
        }

        // 使用BeanCopyUtils进行对象拷贝
        AgentContractApprovalEntity agentContractApproval = BeanCopyUtils.objClone(agentContractApprovalDto,
                AgentContractApprovalEntity::new);
        
        // 设置审批人信息
        agentContractApproval.setFkStaffId(SecureUtil.getStaffId());
        agentContractApproval.setApprovalTime(new Date());
        
        // 使用utilService设置用户信息
        utilService.updateUserInfoToEntity(agentContractApproval);

        // 保存审批记录
        boolean saveFlag = save(agentContractApproval);
        if (!saveFlag) {
            log.error("保存合同审批意见失败");
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
        
        // 更新合同状态
        agentContract.setContractApprovalStatus(agentContractApprovalDto.getApprovalStatus());
        boolean updateFlag = agentContractService.updateById(agentContract);
        if (!updateFlag) {
            log.error("更新合同审批状态失败，合同ID: {}", agentContractId);
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }

        log.info("保存合同审批意见成功，审批记录ID：{}", agentContractApproval.getId());
        return agentContractApproval.getId();
    }

    /**
     * 处理审核通过的业务逻辑
     *
     * @param agentContractApprovalDto 合同审批DTO
     */
    private void processApprovalPassed(AgentContractApprovalDto agentContractApprovalDto) {
        log.info("开始处理审核通过逻辑，合同ID: {}", agentContractApprovalDto.getFkAgentContractId());

        // 1. 获取合同信息
        AgentContract agentContract = getAgentContract(agentContractApprovalDto.getFkAgentContractId());
        
        // 2. 获取代理信息
        Agent agent = getAgent(agentContract.getFkAgentId());
        
        // 3. 获取联系人信息
        List<SaleContactPerson> contactPersons = getContactPersons(agent.getId());
        
        // 4. 获取BD员工信息（用于邮件国际化）
        StaffVo bdStaff = getBdStaffInfo(agent.getId());
        
        // 5. 筛选佣金结算负责人并注册账户
        processCommissionContacts(contactPersons, agent, agentContract, bdStaff.getId());
        
        // 6. 发送审批通过邮件给ADMIN联系人
        sendApprovalPassedEmailsToAdmins(contactPersons, agent, agentContract.getId(), bdStaff.getId());
        
        log.info("审核通过逻辑处理完成，合同ID: {}", agentContractApprovalDto.getFkAgentContractId());
    }

    /**
     * 处理审核驳回的业务逻辑
     *
     * @param agentContractApprovalDto 合同审批DTO
     */
    private void processApprovalRejected(AgentContractApprovalDto agentContractApprovalDto) {
        log.info("开始处理审核驳回逻辑，合同ID: {}", agentContractApprovalDto.getFkAgentContractId());

        // 1. 获取合同信息
        AgentContract agentContract = getAgentContract(agentContractApprovalDto.getFkAgentContractId());
        
        // 2. 获取代理信息
        Agent agent = getAgent(agentContract.getFkAgentId());
        
        // 3. 获取联系人信息
        List<SaleContactPerson> contactPersons = getContactPersons(agent.getId());
        
        // 4. 获取BD员工信息
        StaffVo bdStaff = getBdStaffInfo(agent.getId());
        
        // 5. 发送驳回邮件
        sendRejectionEmails(contactPersons, bdStaff, agent, agentContract.getId());
        
        log.info("审核驳回逻辑处理完成，合同ID: {}", agentContractApprovalDto.getFkAgentContractId());
    }

    /**
     * 获取合同信息
     *
     * @param contractId 合同ID
     * @return 合同信息
     */
    private AgentContract getAgentContract(Long contractId) {
        log.info("开始获取合同信息，合同ID: {}", contractId);
        
        AgentContract agentContract = agentContractService.getById(contractId);
        if (GeneralTool.isEmpty(agentContract)) {
            log.error("合同不存在，合同ID: {}", contractId);
            throw new GetServiceException(LocaleMessageUtils.getFormatMessage("contract_not_found", contractId));
        }
        
        log.info("成功获取合同信息，合同ID: {}, 代理ID: {}", contractId, agentContract.getFkAgentId());
        return agentContract;
    }

    /**
     * 获取代理信息
     *
     * @param agentId 代理ID
     * @return 代理信息
     */
    private Agent getAgent(Long agentId) {
        log.info("开始获取代理信息，代理ID: {}", agentId);
        
        Agent agent = agentService.getById(agentId);
        if (GeneralTool.isEmpty(agent)) {
            log.error("代理不存在，代理ID: {}", agentId);
            throw new GetServiceException(LocaleMessageUtils.getFormatMessage("agent_not_found", agentId));
        }
        
        log.info("成功获取代理信息，代理ID: {}, 代理名称: {}", agentId, agent.getName());
        return agent;
    }

    /**
     * 获取联系人信息
     *
     * @param agentId 代理ID
     * @return 联系人列表
     */
    private List<SaleContactPerson> getContactPersons(Long agentId) {
        log.info("开始获取联系人信息，代理ID: {}", agentId);
        
        // 构建查询条件
        LambdaQueryWrapper<SaleContactPerson> wrapper = Wrappers.lambdaQuery(SaleContactPerson.class);
        wrapper.eq(SaleContactPerson::getFkTableName, TableEnum.SALE_AGENT.key);
        wrapper.eq(SaleContactPerson::getFkTableId, agentId);
        
        List<SaleContactPerson> contactPersons = contactPersonService.list(wrapper);
        if (CollectionUtils.isEmpty(contactPersons)) {
            log.warn("未找到联系人信息，代理ID: {}", agentId);
            return new ArrayList<>();
        }
        
        log.info("成功获取联系人信息，代理ID: {}, 联系人数量: {}", agentId, contactPersons.size());
        return contactPersons;
    }

    /**
     * 处理佣金结算负责人并注册伙伴中心账户
     *
     * @param contactPersons 联系人列表
     * @param agent 代理信息
     * @param agentContract 合同信息
     * @param staffId BD员工ID（用于邮件国际化）
     */
    private void processCommissionContacts(List<SaleContactPerson> contactPersons, Agent agent, AgentContract agentContract, Long staffId) {
        log.info("开始处理佣金结算负责人，代理ID: {}", agent.getId());

        if (CollectionUtils.isEmpty(contactPersons)) {
            log.warn("联系人列表为空，跳过佣金结算负责人处理，代理ID: {}", agent.getId());
            return;
        }

        // 筛选佣金结算负责人
        List<SaleContactPerson> commissionContacts = contactPersons.stream()
                .filter(contactPerson -> appAgentService.isEligibleContactPerson(contactPerson, ContactPersonTypeEnum.COMMISSION))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(commissionContacts)) {
            log.warn("未找到佣金结算负责人，跳过账户注册，代理ID: {}", agent.getId());
            return;
        }

        log.info("找到佣金结算负责人，代理ID: {}, 数量: {}", agent.getId(), commissionContacts.size());

        // 获取BD员工信息
        StaffVo bdStaff = getBdStaffInfo(agent.getId());

        // 构建注册用户列表
        List<RegisterPartnerUserDto> registerList = commissionContacts.stream()
                .map(contactPerson -> buildRegisterDto(contactPerson, bdStaff, agent, agentContract))
                .collect(Collectors.toList());

        // 注册伙伴中心用户
        registerPartnerUsers(registerList, agent.getId(), agentContract.getId(), staffId);
    }


    /**
     * 获取BD员工信息
     *
     * @param agentId 代理ID
     * @return BD员工信息
     */
    private StaffVo getBdStaffInfo(Long agentId) {
        log.info("开始获取BD员工信息，代理ID: {}", agentId);

        if (GeneralTool.isEmpty(agentId)) {
            log.error("代理ID为空，无法获取BD员工信息");
            throw new GetServiceException(LocaleMessageUtils.getMessage("agent_id_null"));
        }

        // 通过AppAgentService获取BD员工信息
        Set<Long> agentIds = Collections.singleton(agentId);
        Map<Long, StaffVo> staffInfoMap = appAgentService.getStaffInfoSkipEmptyEmailByAgentIds(agentIds);
        
        if (GeneralTool.isEmpty(staffInfoMap) || !staffInfoMap.containsKey(agentId)) {
            log.error("未获取到代理对应的BD员工信息，代理ID: {}", agentId);
            throw new GetServiceException(LocaleMessageUtils.getFormatMessage("bd_staff_info_not_found", agentId));
        }

        StaffVo staffVo = staffInfoMap.get(agentId);
        
        // 验证邮件信息 - getStaffInfoSkipEmptyEmailByAgentIds已经过滤了空邮箱，但为了安全起见再检查一次
        if (StringUtils.isBlank(staffVo.getEmail())) {
            log.error("BD员工邮件地址为空，代理ID: {}, 员工ID: {}", agentId, staffVo.getId());
            throw new GetServiceException(LocaleMessageUtils.getFormatMessage("bd_staff_email_empty", staffVo.getId()));
        }

        log.info("成功获取BD员工信息，代理ID: {}, 员工ID: {}, 邮箱: {}", agentId, staffVo.getId(), staffVo.getEmail());
        return staffVo;
    }

    /**
     * 构建注册用户DTO
     *
     * @param contactPerson 联系人
     * @param bdStaff BD员工
     * @param agent 代理信息
     * @param agentContract 合同信息
     * @return 注册用户DTO
     */
    private RegisterPartnerUserDto buildRegisterDto(SaleContactPerson contactPerson, StaffVo bdStaff, 
                                                   Agent agent, AgentContract agentContract) {
        log.info("构建注册用户DTO，联系人: {}, 代理ID: {}", contactPerson.getName(), agent.getId());

        RegisterPartnerUserDto registerDto = new RegisterPartnerUserDto();

        // 设置发件人信息
        registerDto.setFromUser(bdStaff.getName());
        registerDto.setFromEmail(bdStaff.getEmail());
        registerDto.setEmailPassword(bdStaff.getEmailPassword());

        // 设置收件人信息
        registerDto.setToUser(contactPerson.getName());
        String[] emails = contactPerson.getEmail().split("; ");
        if (ArrayUtil.isNotEmpty(emails)) {
            registerDto.setToEmail(emails[0]);
        }

        // 设置业务信息
        registerDto.setAgentId(agent.getId());
        registerDto.setCompanyId(null);
        registerDto.setAgentName(agent.getName());
        registerDto.setApplyAgentName(agent.getName());
        registerDto.setLoginId(SecureUtil.getLoginId());

        // 设置角色信息 - 佣金结算负责人
        ContactPersonTypeEnum commissionType = ContactPersonTypeEnum.COMMISSION;
        registerDto.setPartnerRoleCode(commissionType.getPartnerRoleCode());
        registerDto.setPartnerRoleId(commissionType.getPartnerRoleId());
        registerDto.setTemplateKey(getTemplateKey(commissionType));

        return registerDto;
    }

    /**
     * 获取邮件模板键
     *
     * @param contactType 联系人类型
     * @return 模板键
     */
    private String getTemplateKey(ContactPersonTypeEnum contactType) {
        // 这里可以根据联系人类型返回不同的模板键
        // 目前统一使用邀请注册模板
        return "INVITE_TO_REGISTER";
    }

    /**
     * 注册伙伴中心用户并发送邮件
     *
     * @param registerList 注册用户列表
     * @param agentId 代理ID
     * @param contractId 合同ID
     * @param staffId BD员工ID（用于邮件国际化）
     */
    private void registerPartnerUsers(List<RegisterPartnerUserDto> registerList, Long agentId, Long contractId, Long staffId) {
        log.info("开始注册伙伴中心用户，代理ID: {}, 注册用户数: {}", agentId, registerList.size());

        if (CollectionUtils.isEmpty(registerList)) {
            log.warn("注册用户列表为空，跳过注册，代理ID: {}", agentId);
            return;
        }

        try {
            // 调用公共方法进行用户注册
            List<RegisterPartnerUserVo> registerResults = appAgentService.callPartnerCenterRegister(registerList, agentId);

            // 发送邮件通知
            sendRegistrationEmails(registerList, registerResults, agentId, contractId, staffId);

        } catch (Exception e) {
            log.error("注册伙伴中心用户异常，代理ID: {}", agentId, e);
            throw new GetServiceException(LocaleMessageUtils.getMessage("partner_user_register_error"));
        }
    }

    /**
     * 发送注册邮件通知
     *
     * @param registerList 注册用户列表
     * @param registerResults 注册结果列表
     * @param agentId 代理ID
     * @param contractId 合同ID
     * @param staffId BD员工ID（用于邮件国际化）
     */
    private void sendRegistrationEmails(List<RegisterPartnerUserDto> registerList, 
                                      List<RegisterPartnerUserVo> registerResults, Long agentId, Long contractId, Long staffId) {
        log.info("开始发送注册邮件通知，代理ID: {}", agentId);

        List<EmailSendContext> hasAccountContexts = new ArrayList<>();
        List<EmailSendContext> noAccountContexts = new ArrayList<>();

        boolean hasRegisterResults = !CollectionUtils.isEmpty(registerResults);

        for (RegisterPartnerUserDto register : registerList) {
            String email = register.getToEmail();
            
            if (!hasRegisterResults) {
                // 没有注册结果，发送无账号邮件
                EmailSendContext emailContext = buildEmailSendContext(register, null, 
                        EmailTemplateEnum.CONTRACT_APPROVAL_PASSED_NO_ACCOUNT, contractId, staffId);
                noAccountContexts.add(emailContext);
                continue;
            }

            // 查找匹配的注册结果
            List<RegisterPartnerUserVo> matchedResults = registerResults.stream()
                    .filter(result -> email.equals(result.getAccount()))
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(matchedResults)) {
                // 未找到匹配结果，发送无账号邮件
                EmailSendContext emailContext = buildEmailSendContext(register, null, 
                        EmailTemplateEnum.CONTRACT_APPROVAL_PASSED_NO_ACCOUNT, contractId, staffId);
                noAccountContexts.add(emailContext);
                continue;
            }

            RegisterPartnerUserVo registerResult = matchedResults.get(0);
            String password = registerResult.getPassword();

            if (StringUtils.isNotBlank(password)) {
                // 有密码说明是新创建的账户，发送有账号邮件
                EmailSendContext emailContext = buildEmailSendContext(register, password, 
                        EmailTemplateEnum.CONTRACT_APPROVAL_PASSED_HAS_ACCOUNT, contractId, staffId);
                hasAccountContexts.add(emailContext);
            } else {
                // 无密码说明账户已存在，发送无账号邮件
                EmailSendContext emailContext = buildEmailSendContext(register, null, 
                        EmailTemplateEnum.CONTRACT_APPROVAL_PASSED_NO_ACCOUNT, contractId, staffId);
                noAccountContexts.add(emailContext);
            }
        }


        // 给BD员工发送相同内容的邮件副本
        sendBdEmailCopies(hasAccountContexts, noAccountContexts, agentId, contractId, staffId);

        // 批量发送邮件
        if (!hasAccountContexts.isEmpty()) {
            emailSenderUtils.sendBatchEmails(hasAccountContexts, agentId);
            log.info("成功发送有账号邮件，代理ID: {}, 邮件数量: {}", agentId, hasAccountContexts.size());
        }

        if (!noAccountContexts.isEmpty()) {
            emailSenderUtils.sendBatchEmails(noAccountContexts, agentId);
            log.info("成功发送无账号邮件，代理ID: {}, 邮件数量: {}", agentId, noAccountContexts.size());
        }
    }

    /**
     * 构建邮件发送上下文
     *
     * @param register 注册用户DTO
     * @param password 密码（可能为空）
     * @param emailTemplate 邮件模板
     * @param contractId 合同ID
     * @return 邮件发送上下文
     */
    private EmailSendContext buildEmailSendContext(RegisterPartnerUserDto register, String password, 
                                                  EmailTemplateEnum emailTemplate, Long contractId, Long staffId) {
        // 构建邮件参数
        Map<String, String> emailParams = new HashMap<>();
        emailParams.put("personalName", register.getToUser()); // 收件人姓名
        emailParams.put("name", register.getToUser()); // 收件人姓名（与personalName保持一致）
        emailParams.put("account", register.getToEmail()); // 账号
        emailParams.put("password", password != null ? password : ""); // 密码
        emailParams.put("id", String.valueOf(register.getAgentId())); // ID参数
        
        // 构建二维码路径（将在邮件处理组件中转换为实际二维码）
        String qrcodePath = MiniProgramPageEnum.LOGIN.getPath();
        emailParams.put("qrcode", qrcodePath);
        
        // 添加staffId用于邮件国际化
        if (staffId != null) {
            emailParams.put("staffId", staffId.toString());
        }

        // 构建邮件上下文
        return EmailSendContext.builder()
                .projectKey(ProjectKeyEnum.SALE_CENTER)
                .tableName(TableEnum.AGENT_CONTRACT)
                .tableId(contractId)
                .recipient(register.getToEmail())
                .emailTemplate(emailTemplate)
                .parameters(emailParams)
                .build();
    }

    /**
     * 发送驳回邮件通知
     *
     * @param contactPersons 联系人列表
     * @param bdStaff BD员工信息
     * @param agent 代理信息
     * @param contractId 合同ID
     */
    private void sendRejectionEmails(List<SaleContactPerson> contactPersons, StaffVo bdStaff, Agent agent, Long contractId) {
        log.info("开始发送审核驳回邮件通知，代理ID: {}, 合同ID: {}", agent.getId(), contractId);

        List<EmailSendContext> emailContexts = new ArrayList<>();

        // 1. 发送给ADMIN类型的联系人
        if (!CollectionUtils.isEmpty(contactPersons)) {
            List<SaleContactPerson> commissionContacts = contactPersons.stream()
                    .filter(contactPerson -> appAgentService.isEligibleContactPerson(contactPerson, ContactPersonTypeEnum.ADMIN))
                    .collect(Collectors.toList());

            for (SaleContactPerson contactPerson : commissionContacts) {
                EmailSendContext emailContext = buildRejectionEmailContext(
                        contactPerson.getEmail(),
                        contactPerson.getName(),
                        agent.getId(),
                        contractId,
                        bdStaff.getId()
                );
                emailContexts.add(emailContext);
                log.info("构建联系人驳回邮件，收件人: {}, 邮箱: {}", contactPerson.getName(), contactPerson.getEmail());
            }
        }


        // 2. 给BD员工发送相同内容的驳回邮件副本
        sendBdRejectionEmailCopy(emailContexts, bdStaff, agent, contractId);

        // 3. 批量发送邮件
        if (!emailContexts.isEmpty()) {
            emailSenderUtils.sendBatchEmails(emailContexts, agent.getId());
            log.info("成功发送审核驳回邮件，代理ID: {}, 邮件数量: {}", agent.getId(), emailContexts.size());
        }
    }

    /**
     * 构建驳回邮件发送上下文
     *
     * @param recipient 收件人邮箱
     * @param personalName 收件人姓名
     * @param agentId 代理ID
     * @param contractId 合同ID
     * @return 邮件发送上下文
     */
    private EmailSendContext buildRejectionEmailContext(String recipient, String personalName, 
                                                       Long agentId, Long contractId, Long staffId) {
        // 构建邮件参数
        Map<String, String> emailParams = new HashMap<>();
        emailParams.put("personalName", personalName); // 收件人姓名
        emailParams.put("name", personalName); // 收件人姓名（与personalName保持一致）
        emailParams.put("id", String.valueOf(agentId)); // ID参数
        
        // 构建二维码路径（将在邮件处理组件中转换为实际二维码）
        String qrcodePath = MiniProgramPageEnum.LOGIN.getPath();
        emailParams.put("qrcode", qrcodePath);
        
        // 添加staffId用于邮件国际化
        if (staffId != null) {
            emailParams.put("staffId", staffId.toString());
        }

        // 构建邮件上下文
        return EmailSendContext.builder()
                .projectKey(ProjectKeyEnum.SALE_CENTER)
                .tableName(TableEnum.AGENT_CONTRACT)
                .tableId(contractId)
                .recipient(recipient)
                .emailTemplate(EmailTemplateEnum.CONTRACT_APPROVAL_REJECTED)
                .parameters(emailParams)
                .build();
    }

    /**
     * 保存合同审批意见
     *
     * @param agentContractApprovalDto 合同审批DTO
     * @return 审批记录ID
     */
    @Override
    public Long addAgentContractApproval(AgentContractApprovalDto agentContractApprovalDto) {
        // 只保存审批记录，不发送邮件
        return saveApprovalRecord(agentContractApprovalDto);
    }



    /**
     * 发送审批通过邮件给ADMIN联系人
     *
     * @param contactPersons 联系人列表
     * @param agent 代理信息
     * @param contractId 合同ID
     * @param staffId BD员工ID（用于邮件国际化）
     */
    private void sendApprovalPassedEmailsToAdmins(List<SaleContactPerson> contactPersons, Agent agent, Long contractId, Long staffId) {
        log.info("开始发送审批通过邮件给ADMIN联系人，代理ID: {}", agent.getId());

        if (CollectionUtils.isEmpty(contactPersons)) {
            log.warn("联系人列表为空，跳过ADMIN邮件发送，代理ID: {}", agent.getId());
            return;
        }

        // 筛选ADMIN类型联系人
        List<SaleContactPerson> adminContacts = contactPersons.stream()
                .filter(contactPerson -> appAgentService.isEligibleContactPerson(contactPerson, ContactPersonTypeEnum.ADMIN))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(adminContacts)) {
            log.warn("未找到ADMIN类型联系人，跳过ADMIN邮件发送，代理ID: {}", agent.getId());
            return;
        }

        List<SaleContactPerson> commissionContacts = contactPersons.stream()
                .filter(contactPerson -> appAgentService.isEligibleContactPerson(contactPerson, ContactPersonTypeEnum.COMMISSION))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(commissionContacts)) {
            log.warn("未找到佣金结算负责人, 给ADMIN发送审批通过邮件，代理ID: {}", agent.getId());
        }
        // 构建佣金结算负责人邮箱映射
        Set<String> commissionEmails = commissionContacts.stream()
                .filter(commissionContact -> StringUtils.isNotBlank(commissionContact.getEmail()))
                .map(SaleContactPerson :: getEmail)
                .collect(Collectors.toSet());
        // 排除佣金结算邮箱和ADMIN邮箱相同的
        if (!CollectionUtils.isEmpty(commissionEmails)) {
            adminContacts = adminContacts.stream().filter(adminContact -> StringUtils.isNotBlank(adminContact.getEmail())
                    && !commissionEmails.contains(adminContact.getEmail())
            ).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(adminContacts)) {
            log.warn("ADMIN邮箱和佣金结算负责人邮箱相同，跳过ADMIN邮件发送，代理ID: {}", agent.getId());
            return;
        }

        log.info("找到ADMIN类型联系人，代理ID: {}, 数量: {}", agent.getId(), adminContacts.size());

        // 构建邮件上下文列表
        List<EmailSendContext> emailContexts = new ArrayList<>();
        
        for (SaleContactPerson adminContact : adminContacts) {
            String emailStr = adminContact.getEmail();
            if (StringUtils.isBlank(emailStr)) {
                continue;
            }
            String[] emails = emailStr.split("; ");
            if (ArrayUtil.isEmpty(emails) || StringUtils.isBlank(emails[0])) {
                continue;
            }
            String email = emails[0];
            EmailSendContext emailContext = buildAdminApprovalEmailContext(
                    email,
                    adminContact.getName(),
                    agent.getId(),
                    contractId,
                    staffId
            );
            emailContexts.add(emailContext);
            log.debug("构建ADMIN审批通过邮件上下文，收件人: {}, 代理ID: {}", adminContact.getEmail(), agent.getId());
        }

        // 给BD员工发送相同内容的ADMIN邮件副本
//        sendBdAdminEmailCopy(emailContexts, agent, contractId, staffId);

        // 批量发送邮件
        if (!emailContexts.isEmpty()) {
            emailSenderUtils.sendBatchEmails(emailContexts, agent.getId());
            log.info("成功发送ADMIN审批通过邮件，代理ID: {}, 邮件数量: {}", agent.getId(), emailContexts.size());
        }
    }

    /**
     * 构建ADMIN审批通过邮件上下文
     *
     * @param recipientEmail 收件人邮箱
     * @param recipientName 收件人姓名
     * @param agentId 代理ID
     * @param contractId 合同ID
     * @param staffId 员工ID
     * @return 邮件发送上下文
     */
    private EmailSendContext buildAdminApprovalEmailContext(String recipientEmail, String recipientName, 
                                                          Long agentId, Long contractId, Long staffId) {
        // 构建邮件参数
        Map<String, String> emailParams = new HashMap<>();
        emailParams.put("personalName", recipientName); // 收件人姓名
        emailParams.put("name", recipientName); // 收件人姓名（与personalName相同）
        emailParams.put("id", String.valueOf(agentId)); // 代理ID
        
        // 构建二维码路径（将在邮件处理组件中转换为实际二维码）
        String qrcodePath = MiniProgramPageEnum.LOGIN.getPath();
        emailParams.put("qrcode", qrcodePath);
        
        // 添加staffId用于邮件国际化
        if (staffId != null) {
            emailParams.put("staffId", staffId.toString());
        }

        // 构建邮件上下文
        return EmailSendContext.builder()
                .projectKey(ProjectKeyEnum.SALE_CENTER)
                .tableName(TableEnum.AGENT_CONTRACT)
                .tableId(contractId)
                .recipient(recipientEmail)
                .emailTemplate(EmailTemplateEnum.AGENT_CONTRACT_APPROVAL_PASSED)
                .parameters(emailParams)
                .build();
    }

    /**
     * 给BD员工发送注册邮件副本
     *
     * @param hasAccountContexts 有账号邮件上下文列表
     * @param noAccountContexts 无账号邮件上下文列表
     * @param agentId 代理ID
     * @param contractId 合同ID
     * @param staffId BD员工ID
     */
    private void sendBdEmailCopies(List<EmailSendContext> hasAccountContexts, List<EmailSendContext> noAccountContexts, 
                                  Long agentId, Long contractId, Long staffId) {
        try {
            // 获取BD员工信息
            Result<StaffVo> staffResult = permissionCenterClient.getStaffById(staffId);
            if (!staffResult.isSuccess() || staffResult.getData() == null) {
                log.warn("获取BD员工信息失败，跳过BD邮件副本发送，员工ID: {}", staffId);
                return;
            }
            
            StaffVo bdStaff = staffResult.getData();
            if (StringUtils.isBlank(bdStaff.getEmail())) {
                log.warn("BD员工邮箱为空，跳过BD邮件副本发送，员工ID: {}", staffId);
                return;
            }

            List<EmailSendContext> bdEmailContexts = new ArrayList<>();

            // 复制有账号邮件给BD
            for (EmailSendContext originalContext : hasAccountContexts) {
                EmailSendContext bdContext = createBdEmailCopy(originalContext, bdStaff);
                bdEmailContexts.add(bdContext);
            }

            // 复制无账号邮件给BD
            for (EmailSendContext originalContext : noAccountContexts) {
                EmailSendContext bdContext = createBdEmailCopy(originalContext, bdStaff);
                bdEmailContexts.add(bdContext);
            }

            // 发送BD邮件副本
            if (!bdEmailContexts.isEmpty()) {
                emailSenderUtils.sendBatchEmails(bdEmailContexts, agentId);
                log.info("成功发送BD注册邮件副本，代理ID: {}, 邮件数量: {}", agentId, bdEmailContexts.size());
            }

        } catch (Exception e) {
            log.error("发送BD注册邮件副本异常，代理ID: {}, 错误: {}", agentId, e.getMessage());
        }
    }

    /**
     * 给BD员工发送驳回邮件副本
     *
     * @param originalContexts 原始邮件上下文列表
     * @param bdStaff BD员工信息
     * @param agent 代理信息
     * @param contractId 合同ID
     */
    private void sendBdRejectionEmailCopy(List<EmailSendContext> originalContexts, StaffVo bdStaff, Agent agent, Long contractId) {
        try {
            if (StringUtils.isBlank(bdStaff.getEmail())) {
                log.warn("BD员工邮箱为空，跳过BD驳回邮件副本发送，员工ID: {}", bdStaff.getId());
                return;
            }

            List<EmailSendContext> bdEmailContexts = new ArrayList<>();

            // 复制驳回邮件给BD
            for (EmailSendContext originalContext : originalContexts) {
                EmailSendContext bdContext = createBdEmailCopy(originalContext, bdStaff);
                bdEmailContexts.add(bdContext);
            }

            // 发送BD邮件副本
            if (!bdEmailContexts.isEmpty()) {
                emailSenderUtils.sendBatchEmails(bdEmailContexts, agent.getId());
                log.info("成功发送BD驳回邮件副本，代理ID: {}, 邮件数量: {}", agent.getId(), bdEmailContexts.size());
            }

        } catch (Exception e) {
            log.error("发送BD驳回邮件副本异常，代理ID: {}, 错误: {}", agent.getId(), e.getMessage());
        }
    }

    /**
     * 给BD员工发送ADMIN邮件副本
     *
     * @param originalContexts 原始邮件上下文列表
     * @param agent 代理信息
     * @param contractId 合同ID
     * @param staffId BD员工ID
     */
    private void sendBdAdminEmailCopy(List<EmailSendContext> originalContexts, Agent agent, Long contractId, Long staffId) {
        try {
            // 获取BD员工信息
            Result<StaffVo> staffResult = permissionCenterClient.getStaffById(staffId);
            if (!staffResult.isSuccess() || staffResult.getData() == null) {
                log.warn("获取BD员工信息失败，跳过BD ADMIN邮件副本发送，员工ID: {}", staffId);
                return;
            }
            
            StaffVo bdStaff = staffResult.getData();
            if (StringUtils.isBlank(bdStaff.getEmail())) {
                log.warn("BD员工邮箱为空，跳过BD ADMIN邮件副本发送，员工ID: {}", staffId);
                return;
            }

            List<EmailSendContext> bdEmailContexts = new ArrayList<>();

            // 复制ADMIN邮件给BD
            for (EmailSendContext originalContext : originalContexts) {
                EmailSendContext bdContext = createBdEmailCopy(originalContext, bdStaff);
                bdEmailContexts.add(bdContext);
            }

            // 发送BD邮件副本
            if (!bdEmailContexts.isEmpty()) {
                emailSenderUtils.sendBatchEmails(bdEmailContexts, agent.getId());
                log.info("成功发送BD ADMIN邮件副本，代理ID: {}, 邮件数量: {}", agent.getId(), bdEmailContexts.size());
            }

        } catch (Exception e) {
            log.error("发送BD ADMIN邮件副本异常，代理ID: {}, 错误: {}", agent.getId(), e.getMessage());
        }
    }

    /**
     * 创建BD员工的邮件副本
     *
     * @param originalContext 原始邮件上下文
     * @param bdStaff BD员工信息
     * @return BD员工邮件上下文
     */
    private EmailSendContext createBdEmailCopy(EmailSendContext originalContext, StaffVo bdStaff) {
        // 完全复制原始邮件参数，不做任何修改
        Map<String, String> bdEmailParams = new HashMap<>(originalContext.getParameters());

        // 构建BD员工邮件上下文
        return EmailSendContext.builder()
                .projectKey(originalContext.getProjectKey())
                .tableName(originalContext.getTableName())
                .tableId(originalContext.getTableId())
                .recipient(bdStaff.getEmail()) // 只改变收件人邮箱
                .emailTemplate(originalContext.getEmailTemplate()) // 使用相同的邮件模板
                .parameters(bdEmailParams) // 参数完全不变
                .build();
    }

    /**
     * 验证并获取合同信息
     *
     * @param contractId 合同ID
     * @return 合同信息
     * @throws GetServiceException 当合同ID为空或合同不存在时抛出异常
     */
    private AgentContract validateAndGetContract(Long contractId) {
        log.info("开始验证并获取合同信息，合同ID: {}", contractId);

        // 1. 验证合同ID
        if (GeneralTool.isEmpty(contractId)) {
            log.error("合同ID为空");
            throw new GetServiceException(LocaleMessageUtils.getMessage("contract_id_required"));
        }

        // 2. 获取合同信息
        AgentContract contract = agentContractService.getById(contractId);
        if (ObjectUtil.isEmpty(contract)) {
            log.error("合同不存在，合同ID: {}", contractId);
            throw new GetServiceException(LocaleMessageUtils.getFormatMessage("contract_not_found", contractId));
        }

        log.info("成功获取合同信息，合同ID: {}, 合同状态: {}", contractId, contract.getContractApprovalStatus());
        return contract;
    }

    /**
     * 处理未签署状态的合同
     *
     * 功能说明：
     * 1. 判断合同是否为未签署状态（UNSIGNED = 2）
     * 2. 如果是未签署状态且有附件，保存附件到合同
     * 3. 自动设置审批状态为通过（APPROVED = 4）
     *
     * @param contract 合同信息
     * @param approvalDto 审批DTO
     */
    private void processUnsignedContract(AgentContract contract, AgentContractApprovalDto approvalDto) {
        log.info("开始处理未签署合同，合同ID: {}, 当前状态: {}",
                contract.getId(), contract.getContractApprovalStatus());

        // 1. 判断合同是否为未签署状态
        if (!AgentContractApprovalStatusEnum.UNSIGNED.getCode().equals(contract.getContractApprovalStatus())) {
            log.info("合同状态不是未签署，跳过未签署处理逻辑，合同ID: {}, 状态: {}",
                    contract.getId(), contract.getContractApprovalStatus());
            return;
        }

        log.info("检测到未签署状态合同，开始处理，合同ID: {}", contract.getId());

        // 2. 处理附件保存
        if (GeneralTool.isNotEmpty(approvalDto.getMediaAttachedVos())) {
            try {
                log.info("开始保存合同附件，合同ID: {}, 附件数量: {}",
                        contract.getId(), approvalDto.getMediaAttachedVos().size());

                // 调用合同服务保存附件
                agentContractService.addAgentContractMedia(approvalDto.getMediaAttachedVos());

                log.info("合同附件保存成功，合同ID: {}", contract.getId());
            } catch (Exception e) {
                log.error("保存合同附件失败，合同ID: {}", contract.getId(), e);
                throw new GetServiceException(LocaleMessageUtils.getFormatMessage("contract_media_save_failed", contract.getId()));
            }
        } else {
            log.error("未签署合同缺少必要附件，合同ID: {}", contract.getId());
            throw new GetServiceException(LocaleMessageUtils.getFormatMessage("unsigned_contract_media_required", contract.getId()));
        }

        // 3. 设置审批状态为通过
        approvalDto.setApprovalStatus(AgentContractApprovalStatusEnum.APPROVED.getCode());
        log.info("未签署合同自动设置为审批通过，合同ID: {}", contract.getId());
    }

}
package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.permissioncenter.vo.tree.CompanyTreeVo;
import com.get.salecenter.vo.BusinessChannelVo;
import com.get.salecenter.dto.BusinessChannelDto;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2022/1/7
 * @TIME: 15:46
 * @Description:
 **/
public interface IBusinessChannelService {


    /**
     * @return java.lang.Long
     * @Description: 新增业务渠道
     * @Param [businessChannelDto]
     * <AUTHOR>
     */
    void addBusinessChannel(List<BusinessChannelDto> businessChannelDto);

    /**
     * @return java.util.List<com.get.salecenter.vo.BusinessChannelVo>
     * @Description: 查询业务渠道
     * @Param [businessChannelDto, page]
     * <AUTHOR>
     */
    List<BusinessChannelVo> getBusinessChannelDtos(BusinessChannelDto businessChannelDto, Page page);


    public Map<Long,String> getNamesByIds(Set<Long> ids);

    /**
     * @return com.get.salecenter.vo.BusinessChannelVo
     * @Description: 修改
     * @Param [businessChannelDto]
     * <AUTHOR>
     */
    BusinessChannelVo updateBusinessChannel(BusinessChannelDto businessChannelDto);

    /**
     * @return void
     * @Description: 删除
     * @Param [id]
     * <AUTHOR>
     */
    void deleteBusinessChannel(Long id);

    /**
     * 查询byID
     *
     * @param id
     * @return
     * @
     */
    BusinessChannelVo findBusinessChannelById(Long id);

    /**
     * @Description:类型下拉框数据
     * @Param
     * @Date 15:49 2021/5/12
     * <AUTHOR>
     */
    List<Map<String, Object>> getModuleKey();

    List<BaseSelectEntity> channelSelect(String tableName,Long companyId);

    List<BaseSelectEntity> getTargetName(String tableName, Long companyId);


    List<Long> getChannelIds(String tableName, String channelName);


    Set<Long> getBusinessId(Map<String,Set<Long>> params);


    Set<Long> getBusinessProviderId(Set<Long> ids);
    /**
     * @return java.util.Map<java.lang.Long, java.lang.String>
     * @Description :fegin调用 根据ids 查询名称map
     * @Param [ids]
     * <AUTHOR>
     */
    Map<Long, String> getChannelNamesByIds(Set<Long> ids);

    /**
     * @param tableName
     * @param channelId
     * @return
     */
    List<BaseSelectEntity> getPlanIdsByTableNameAndChannelId(String tableName, Long channelId,Long receiptFormId,String fkTypeKey,Integer pageNumber,Integer pageSize);

    List<CompanyTreeVo> allCompany(Long companyId);

    List<CompanyTreeVo> getChannelCompanyRelation(Long channelId);

    void editChannelCompanyRelation(List<BusinessChannelDto> businessChannelDtos);
}

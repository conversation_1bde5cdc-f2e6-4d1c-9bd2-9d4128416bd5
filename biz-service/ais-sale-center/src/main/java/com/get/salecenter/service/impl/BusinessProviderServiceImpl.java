package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.DataConverter;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.tree.CompanyTreeVo;
import com.get.salecenter.dao.insurance.InsuranceOrderMapper;
import com.get.salecenter.dao.sale.*;
import com.get.salecenter.dto.BusinessProviderDto;
import com.get.salecenter.dto.MediaAndAttachedDto;
import com.get.salecenter.dto.query.BusinessProviderQueryDto;
import com.get.salecenter.entity.*;
import com.get.salecenter.service.IBusinessProviderService;
import com.get.salecenter.service.IMediaAndAttachedService;
import com.get.salecenter.service.IStudentInsuranceService;
import com.get.salecenter.utils.MyStringUtils;
import com.get.salecenter.vo.BusinessProviderSelectVo;
import com.get.salecenter.vo.BusinessProviderVo;
import com.get.salecenter.vo.MediaAndAttachedVo;
import com.get.salecenter.vo.SelItem;
import com.get.salecenter.vo.StudentInsuranceVo;
import com.google.common.collect.Maps;
import org.apache.commons.lang.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * author:Neil
 * Time: 16:58
 * Date: 2022/6/20
 * Description:
 */
@Service
public class BusinessProviderServiceImpl implements IBusinessProviderService {

    @Resource
    private BusinessProviderMapper businessProviderlMapper;

    @Resource
    private UtilService utilService;
    @Lazy
    @Resource
    private IStudentInsuranceService studentInsuranceService;
    @Resource
    private InsuranceOrderMapper insuranceOrderMapper;
    @Resource
    private IPermissionCenterClient feignPermissionService;

    @Resource
    private IMediaAndAttachedService attachedService;
    @Resource
    private StudentAccommodationMapper studentAccommodationMapper;
    @Resource
    private StudentServiceFeeCostMapper studentServiceFeeCostMapper;
    @Resource
    private ClientSourceMapper clientSourceMapper;
    @Resource
    private BusinessProviderAccountMapper businessProviderAccountMapper;
    @Resource
    private BusinessProviderContactPersonMapper businessProviderContactPersonMapper;
    @Resource
    private BusinessProviderTypeMapper businessProviderTypeMapper;
    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Resource
    BusinessProviderCompanyMapper businessProviderCompanyMapper;

    @Override
    public List<BusinessProviderVo> getBusinessProviderDtos(BusinessProviderQueryDto businessProviderVo, SearchBean<BusinessProviderQueryDto> page) {
        Map<Long, String> companyMap = getCompanyMap();
        //获取下属员工所有部门
        Set<Long> fkDepartIds = permissionCenterClient.getStaffDepartmentsById(SecureUtil.getStaffId()).getData();
        IPage<BusinessProviderQueryDto> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<BusinessProviderVo> dtos = businessProviderlMapper.getDatas(iPage,businessProviderVo,fkDepartIds);
        page.setAll((int) iPage.getTotal());
        for (BusinessProviderVo businessProviderDto : dtos) {
            String[] fkTypeKeyNameList = businessProviderDto.getFkTypeKey().split(",");
            StringBuilder fkTypeNameStr = new StringBuilder();
            for (String fkTypeName : fkTypeKeyNameList) {
                fkTypeNameStr.append(ProjectKeyEnum.getValueByKey(fkTypeName, ProjectKeyEnum.BUSINESS_TYPE)).append(",");
            }
            fkTypeNameStr.deleteCharAt(fkTypeNameStr.length() - 1);
            businessProviderDto.setFkTypeKeyName(fkTypeNameStr.toString());
            List<BusinessProviderCompany> companyList  = businessProviderCompanyMapper.selectList(
                    Wrappers.<BusinessProviderCompany>lambdaQuery()
                            .eq(BusinessProviderCompany::getFkBusinessProviderId, businessProviderDto.getId())

            );
            StringBuilder builder = new StringBuilder();
            List<Long> companyIdList = new ArrayList<>();
            for (BusinessProviderCompany businessProviderCompany : companyList) {
                String name = companyMap.get(businessProviderCompany.getFkCompanyId());
                builder.append(name).append("，");
                businessProviderDto.setCompanyName(sub(builder));


                companyIdList.add(businessProviderCompany.getFkCompanyId());

//                Result<String> result = feignPermissionService.getCompanyNameById(businessProviderCompany.getFkCompanyId());
//                if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
//                    businessProviderDto.setCompanyName(result.getData());
//                }
            }
            businessProviderDto.setFkCompanyId(companyIdList);
        }
        return dtos;
    }


    private String sub(StringBuilder sb) {
        if (GeneralTool.isEmpty(sb)) {
            return null;
        }
        String substring = null;
        int i = sb.lastIndexOf("，");
        if (i != -1) {
            substring = sb.substring(0, i);
        }
        return substring;
    }

    private Map<Long, String> getCompanyMap() {
        Result<List<CompanyTreeVo>> result = permissionCenterClient.getAllCompanyDto();
        if (!result.isSuccess()) {
            throw new GetServiceException(result.getMessage());
        }
        List<CompanyTreeVo> companyTreeVos = result.getData();
        //初始为5的map
        Map<Long, String> companyMap = new HashMap<>(5);
        if (GeneralTool.isNotEmpty(companyTreeVos)) {
            companyMap = companyTreeVos.stream().collect(Collectors.toMap(CompanyTreeVo::getId, CompanyTreeVo::getShortName));
        }
        return companyMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addBusinessProvider(ValidList<BusinessProviderDto> businessProviderDto) {
        if (GeneralTool.isEmpty(businessProviderDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        for (BusinessProviderDto businessProvidervo : businessProviderDto) {
            if (GeneralTool.isEmpty(businessProvidervo.getId())) {
                BusinessProvider businessProvider = BeanCopyUtils.objClone(businessProvidervo, BusinessProvider::new);
                if (businessProvider.getFkDepartmentIds() == ""){
                    businessProvider.setFkDepartmentIds(null);
                }
                utilService.updateUserInfoToEntity(businessProvider);
                businessProviderlMapper.insert(businessProvider);
                businessProvider.setNum(MyStringUtils.getBusinessProviderNum(businessProvider.getFkTypeKey(), businessProvider.getId()));
                businessProviderlMapper.updateById(businessProvider);
                if (GeneralTool.isNotEmpty(businessProvider.getId())) {
                    BusinessProviderCompany businessProviderCompany = new BusinessProviderCompany();
                    businessProviderCompany.setFkCompanyId(businessProvidervo.getFkCompanyId());
                    businessProviderCompany.setFkBusinessProviderId(businessProvider.getId());
                    utilService.setCreateInfo(businessProviderCompany);
                    businessProviderCompanyMapper.insert(businessProviderCompany);
                }
            } else {
                BusinessProvider businessProvider = BeanCopyUtils.objClone(businessProviderDto, BusinessProvider::new);
                utilService.updateUserInfoToEntity(businessProvider);
                businessProviderlMapper.updateById(businessProvider);
            }

        }
    }

    @Override
    public BusinessProviderVo updateBusinessProvider(BusinessProviderDto businessProviderDto) {
        BusinessProvider businessProvider = BeanCopyUtils.objClone(businessProviderDto, BusinessProvider::new);
        if (businessProvider.getFkDepartmentIds() == ""){
            businessProvider.setFkDepartmentIds(null);
        }
        utilService.updateUserInfoToEntity(businessProvider);
        businessProviderlMapper.updateById(businessProvider);
        return findBusinessProviderById(businessProvider.getId());
    }

    @Override
    public void deleteBusinessProvider(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        List<StudentAccommodation> studentAccommodations = studentAccommodationMapper.selectList(Wrappers.<StudentAccommodation>lambdaQuery().eq(StudentAccommodation::getFkBusinessProviderId, id));
        List<StudentServiceFeeCost> studentServiceFeeCosts = studentServiceFeeCostMapper.selectList(Wrappers.<StudentServiceFeeCost>lambdaQuery().eq(StudentServiceFeeCost::getFkBusinessProviderId, id));
        List<ClientSource> clientSources = clientSourceMapper.selectList(Wrappers.<ClientSource>lambdaQuery().eq(ClientSource::getFkTableId, id).eq(ClientSource::getFkTableName, ProjectKeyEnum.CLIENT_SOURCE_TYPE_BUSINESS_PROVIDER.key));
        List<BusinessProviderAccount> businessProviderAccounts = businessProviderAccountMapper.selectList(Wrappers.<BusinessProviderAccount>lambdaQuery().eq(BusinessProviderAccount::getFkBusinessProviderId, id));
        List<BusinessProviderContactPerson> businessProviderContactPeople = businessProviderContactPersonMapper.selectList(Wrappers.<BusinessProviderContactPerson>lambdaQuery().eq(BusinessProviderContactPerson::getFkBusinessProviderId, id));
        if (GeneralTool.isNotEmpty(studentAccommodations) || GeneralTool.isNotEmpty(studentServiceFeeCosts) || GeneralTool.isNotEmpty(clientSources) || GeneralTool.isNotEmpty(businessProviderAccounts) || GeneralTool.isNotEmpty(businessProviderContactPeople)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("BUSINESS_PROVIDER_DATA_ASSOCIATION"));
        }
        businessProviderlMapper.deleteById(id);
    }

//    @Override
//    public BusinessProviderVo findBusinessProviderByBatchIds(String ids) {
//        if (GeneralTool.isEmpty(ids) || ids.isEmpty()) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
//        }
//        List<Long> idList = Arrays.stream(ids.split(","))
//                .map(Long::parseLong)
//                .collect(Collectors.toList());
//        // 结果列表
//        List<BusinessProviderVo> businessProviderVoList = new ArrayList<>();
//
//        // 查询所有的 BusinessProvider
//        List<BusinessProvider> businessProviders = businessProviderlMapper.selectBatchIds(idList);
//
//        for (BusinessProvider businessProvider : businessProviders) {
//            List<BusinessProviderCompany> businessProviderCompanies = businessProviderCompanyMapper.selectList(
//                    Wrappers.<BusinessProviderCompany>lambdaQuery().eq(BusinessProviderCompany::getFkBusinessProviderId, businessProvider.getId())
//            );
//
//            BusinessProviderVo businessProviderVo = BeanCopyUtils.objClone(businessProvider, BusinessProviderVo::new);
//            String companyName = "";
//
//            for (BusinessProviderCompany businessProviderCompany : businessProviderCompanies) {
//                // 检查 companyName 是否为空，如果为空则不添加逗号
//                companyName += (companyName.isEmpty() ? "" : ",")
//                        + feignPermissionService.getCompanyNameById(businessProviderCompany.getFkCompanyId()).getData();
//            }
//            businessProviderVo.setCompanyName(companyName);
//
//            if (GeneralTool.isNotEmpty(businessProvider.getFkDepartmentIds())) {
//                List<String> departmentNameList = new ArrayList<>();
//                String[] departmentIds = businessProvider.getFkDepartmentIds().split(",");
//                Long[] departmentIdsLongs = Arrays.stream(departmentIds)
//                        .map(Long::valueOf)
//                        .toArray(Long[]::new);
//                Set<Long> departmentIdSet = new HashSet<>(Arrays.asList(departmentIdsLongs));
//                Map<Long, String> departmentNames = feignPermissionService.getDepartmentNamesByIds(departmentIdSet).getData();
//
//                if (GeneralTool.isNotEmpty(departmentNames)) {
//                    departmentNames.forEach((k, v) -> {
//                        departmentNameList.add(v);
//                    });
//                }
//                businessProviderVo.setDepartmentNameList(departmentNameList);
//            }
//
//            List<String> fkTypeKeyNameList = new ArrayList<>();
//            for (String fkTypeKey : businessProviderVo.getFkTypeKey().split(",")) {
//                fkTypeKeyNameList.add(ProjectKeyEnum.getValueByKey(fkTypeKey, ProjectKeyEnum.BUSINESS_TYPE));
//            }
//            businessProviderVo.setFkTypeKeyNameList(fkTypeKeyNameList);
//
//            BusinessProviderType businessProviderType = businessProviderTypeMapper.selectById(businessProviderVo.getFkBusinessProviderTypeId());
//            businessProviderVo.setFkBusinessProviderTypeName(businessProviderType.getTypeName());
//
//            // 将每个 BusinessProviderVo 添加到结果列表中
//            businessProviderVoList.add(businessProviderVo);
//        }
//
//        return (BusinessProviderVo) businessProviderVoList;
//    }


    @Override
    public BusinessProviderVo findBusinessProviderById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        BusinessProvider businessProvider = businessProviderlMapper.selectById(id);
        List<BusinessProviderCompany> businessProviderCompanies = businessProviderCompanyMapper.selectList(Wrappers.<BusinessProviderCompany>lambdaQuery().eq(BusinessProviderCompany::getFkBusinessProviderId, businessProvider.getId()));
        BusinessProviderVo businessProviderVo = BeanCopyUtils.objClone(businessProvider, BusinessProviderVo::new);
        String companyName = "";
        List<Long> companyIdList = new ArrayList<>();
        for (BusinessProviderCompany businessProviderCompany : businessProviderCompanies) {
            // 检查 companyName 是否为空，如果为空则不添加逗号
            companyName += (companyName.isEmpty() ? "" : ",")
                    + feignPermissionService.getCompanyNameById(businessProviderCompany.getFkCompanyId()).getData();

            companyIdList.add(businessProviderCompany.getFkCompanyId());

        }
        businessProviderVo.setCompanyName(companyName);
        businessProviderVo.setFkCompanyId(companyIdList);
//        businessProviderVo.setFkDepartmentIds(businessProvider.getFkDepartmentIds() == ""? null : businessProvider.getFkDepartmentIds());
        if (GeneralTool.isNotEmpty(businessProvider.getFkDepartmentIds())) {
            List<String> departmentNameList = new ArrayList<>();
            String[] departmentIds = businessProvider.getFkDepartmentIds().split(",");

            Long[] departmentIdsLongs = Arrays.stream(departmentIds)
                    .map(Long::valueOf)
                    .toArray(Long[]::new);
            Set<Long> departmentIdSet = new HashSet<>(Arrays.asList(departmentIdsLongs));
            Map<Long, String> departmentNames = feignPermissionService.getDepartmentNamesByIds(departmentIdSet).getData();
            if (GeneralTool.isNotEmpty(departmentNames)) {
                departmentNames.forEach((k, v) -> {
                    departmentNameList.add(v);
                });
            }
            businessProviderVo.setDepartmentNameList(departmentNameList);
        }
        List<String> fkTypeKeyNameList = new ArrayList<>();
        for (String fkTypeKey : businessProviderVo.getFkTypeKey().split(",")) {
            fkTypeKeyNameList.add(ProjectKeyEnum.getValueByKey(fkTypeKey, ProjectKeyEnum.BUSINESS_TYPE));
        }
        businessProviderVo.setFkTypeKeyNameList(fkTypeKeyNameList);
        BusinessProviderType businessProviderType = businessProviderTypeMapper.selectById(businessProviderVo.getFkBusinessProviderTypeId());
        businessProviderVo.setFkBusinessProviderTypeName(businessProviderType.getTypeName());
        return businessProviderVo;
    }

    @Override
    public Map<Long,String> getNamesByIds(Set<Long> ids){
        if (GeneralTool.isEmpty(ids)) {
            return Maps.newHashMap();
        }
        List<BusinessProviderVo> namesByIds = businessProviderlMapper.getNamesByIds(ids);
        if (GeneralTool.isNotEmpty(namesByIds)) {
            Map<Long,String> map=new HashMap<>();
            for (BusinessProviderVo namesById : namesByIds) {
                map.put(namesById.getId(),namesById.getName());
            }
            return map;
        }
        return Maps.newHashMap();
    }

    /**
     * Author Cream
     * Description : //获取业务提供商名称
     * Date 2023/2/15 12:43
     * Params:
     * Return
     */
    @Override
    public String getBusinessProviderNameById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        BusinessProvider businessProvider = businessProviderlMapper.selectById(id);
        return businessProvider.getNameChn() + "（" + businessProvider.getName() + "）";
    }

    @Override
    public SelItem getBusinessProviderCompanyInfo(Long id) {
        return businessProviderlMapper.getBusinessProviderCompanyInfoById(id);
    }

    @Override
    public List<BaseSelectEntity> providerSelect(String tableName,Long oldId,Long companyId) {
//        List<Long> companyIds = SecureUtil.getCompanyIds();
        //获取下属员工所有部门
        Set<Long> fkDepartIds = permissionCenterClient.getStaffDepartmentsById(SecureUtil.getStaffId()).getData();
        List<BaseSelectEntity> entities = businessProviderlMapper.providerSelect(tableName, companyId,fkDepartIds);
        if (oldId!=null && oldId>0) {
            List<BaseSelectEntity> provider = businessProviderlMapper.getProviderById(oldId,fkDepartIds);
            entities.addAll(provider);
        }
        entities = entities.stream().distinct().collect(Collectors.toList());
        return entities;
    }

    /**
     * 获取业务提供商下拉
     * @param targetName
     * @return
     */
    @Override
    public List<BaseSelectEntity> getBusinessProviderByTargetName(String targetName) {
        return businessProviderlMapper.getBusinessProviderByTargetName(DataConverter.stringManipulation(targetName));
    }

    @Override
    public List<BaseSelectEntity> getClientSourceProviderSelect(String tableName,Long fkCompanyId) {
        //获取下属员工所有部门
        Set<Long> fkDepartIds = permissionCenterClient.getStaffDepartmentsById(SecureUtil.getStaffId()).getData();
        return businessProviderlMapper.getClientSourceProviderSelect(tableName,fkCompanyId,fkDepartIds);
    }

    @Override
    public List<MediaAndAttachedVo> addBusinessProviderMedia(ValidList<MediaAndAttachedDto> mediaAttachedVos) {
        if (GeneralTool.isEmpty(mediaAttachedVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        List<MediaAndAttachedVo> mediaAndAttachedVos = new ArrayList<>();
        for (MediaAndAttachedDto mediaAndAttachedDto : mediaAttachedVos) {
            //设置插入的表
            mediaAndAttachedDto.setFkTableName(TableEnum.SALE_BUSINESS_PROVIDER.key);
            mediaAndAttachedVos.add(attachedService.addMediaAndAttached(mediaAndAttachedDto));
        }
        return mediaAndAttachedVos;
    }

    @Override
    public List<MediaAndAttachedVo> getBusinessProviderMedia(MediaAndAttachedDto attachedVo, SearchBean<MediaAndAttachedDto> page) {
        if (GeneralTool.isEmpty(attachedVo.getFkTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
//        verifyDataPermissionsUtils.verifyByBusinessId(attachedVo.getFkTableId(),VerifyDataPermissionsUtils.AGENT_O);
        attachedVo.setFkTableName(TableEnum.SALE_BUSINESS_PROVIDER.key);
        return attachedService.getMediaAndAttachedDto(attachedVo, page, FileTypeEnum.SALE_BUSINESS_PROVIDER_AGREEMENT.key,FileTypeEnum.SALE_BUSINESS_PROVIDER_AGREEMENT_FILE.key);
    }

    @Override
    public List<BusinessProviderSelectVo> getBusinessProviderSelect(String fkTypeKey, Long fkCompanyId) {
        if (!SecureUtil.validateCompany(fkCompanyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
        }
        //获取下属员工所有部门
        Set<Long> fkDepartIds = permissionCenterClient.getStaffDepartmentsById(SecureUtil.getStaffId()).getData();
        List<BusinessProviderSelectVo> businessProviderSelect = businessProviderlMapper.getBusinessProviderSelect(fkDepartIds, fkTypeKey, fkCompanyId);
        for (BusinessProviderSelectVo select : businessProviderSelect) {
            String productInfo = select.getProductInfo();
            if (GeneralTool.isNotEmpty(productInfo)) {
                String[] split = productInfo.split(",");
                select.setProductList(Arrays.asList(split));
            }
        }
        return businessProviderSelect;
    }

    @Override
    public List<CompanyTreeVo> allCompany(Long companyId) {
        return permissionCenterClient.getCompanyTree(companyId).getData();
    }

    @Override
    public List<CompanyTreeVo> getProviderCompanyRelation(Long providerId) {
        List<BusinessProviderCompany> businessProviderCompanies = businessProviderCompanyMapper.selectList(
                Wrappers.<BusinessProviderCompany>lambdaQuery().eq(BusinessProviderCompany::getFkBusinessProviderId, providerId));
        if (GeneralTool.isEmpty(providerId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        //获取公司
        List<CompanyTreeVo> companyTreeVo = permissionCenterClient.getAllCompanyDto().getData();
        if (GeneralTool.isEmpty(companyTreeVo)) {
            return null;
        }
        for (CompanyTreeVo treeDto : companyTreeVo) {
            for (BusinessProviderCompany businessProviderCompany : businessProviderCompanies) {
                if (treeDto.getId().longValue() == (businessProviderCompany.getFkCompanyId().longValue())) {
                    treeDto.setFlag(true);
                }
            }

        }

        return getTreeList(companyTreeVo);

    }

    private List<CompanyTreeVo> getTreeList(List<CompanyTreeVo> entityList) {
        List<CompanyTreeVo> resultList = new ArrayList<>();
        // 获取顶层元素集合
        String parentId;
        for (CompanyTreeVo entity : entityList) {
            parentId = String.valueOf(entity.getFkParentCompanyId());
            if (parentId == null || "0".equals(parentId)) {
                //获取父节点的部门信息
                resultList.add(entity);
            }
        }
        //假如没有父节点
        if (GeneralTool.isEmpty(resultList)) {
            //获取最小节点
            CompanyTreeVo minTreeNode = entityList.stream().min(Comparator.comparing(CompanyTreeVo::getFkParentCompanyId)).get();
            resultList.add(minTreeNode);
            if (GeneralTool.isNotEmpty(minTreeNode)) {
                //获取相同的最小节点
                List<CompanyTreeVo> minTreeNodes = entityList.stream().filter(treeDto ->
                        treeDto.getFkParentCompanyId().equals(minTreeNode.getFkParentCompanyId()) &&
                                treeDto.getId().longValue() != (minTreeNode.getId().longValue())).distinct().collect(Collectors.toList());
                resultList.addAll(minTreeNodes);
            }
        }
        // 获取每个顶层元素的子数据集合
        for (CompanyTreeVo entity : resultList) {
            entity.setChildList(getSubList(entity.getId(), entityList));
        }
        return resultList;
    }
    private List<CompanyTreeVo> getSubList(Long id, List<CompanyTreeVo> entityList) {
        List<CompanyTreeVo> childList = new ArrayList<>();
        // 子集的直接子对象
        for (CompanyTreeVo entity : entityList) {
            if (id.longValue() == (entity.getFkParentCompanyId())) {
                //获取子节点的部门信息
                childList.add(entity);
            }
        }
        // 子集的间接子对象
        for (CompanyTreeVo entity : childList) {
            //递归调用
            entity.setChildList(getSubList(entity.getId(), entityList));
        }
        // 递归退出条件
        if (childList.size() == 0) {
            return null;
        }
        return childList;
    }



    @Override
    public void editProviderCompanyRelation(List<BusinessProviderDto> businessProviderDtos) {
        if (GeneralTool.isEmpty(businessProviderDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }

        List<Long> collect = businessProviderDtos.stream()
                .filter(Objects::nonNull)
                .map(BusinessProviderDto::getFkCompanyId).collect(Collectors.toList());

        //移除空元素
        collect.removeIf(Objects::isNull);
        List<BusinessProviderCompany> companyList  = businessProviderCompanyMapper.selectList(
                    Wrappers.<BusinessProviderCompany>lambdaQuery()
                            .eq(BusinessProviderCompany::getFkBusinessProviderId, businessProviderDtos.get(0).getFkBusinessProviderId())

            );
        List<Long> companyIds = companyList.stream().map(BusinessProviderCompany::getFkCompanyId).collect(Collectors.toList());

        LambdaQueryWrapper<BusinessProviderCompany> wrapper = new LambdaQueryWrapper();
        wrapper.eq(BusinessProviderCompany::getFkBusinessProviderId, businessProviderDtos.get(0).getFkBusinessProviderId()).in(BusinessProviderCompany::getFkCompanyId,companyIds);
        businessProviderCompanyMapper.delete(wrapper);

        if (GeneralTool.isNotEmpty(collect)) {
            List<BusinessProviderCompany> providerCompanies = businessProviderDtos.stream()
                    .map(businessProviderDto ->
                            BeanCopyUtils.objClone(businessProviderDto, BusinessProviderCompany::new)).collect(Collectors.toList());
            //循环插入
            providerCompanies.forEach(businessProviderCompany -> {
                utilService.setCreateInfo(businessProviderCompany);
                businessProviderCompanyMapper.insert(businessProviderCompany);
            });
        }
    }

    @Override
    public List<Long> getBusinessProviderId(String targetName) {
        if (StringUtils.isBlank(targetName)) {
            return null;
        }
        return businessProviderlMapper.getBusinessProviderIdByTargetName(targetName);
    }

    @Override
    public List<BaseSelectEntity> getPlanAndTargetName(Long targetId,Long receiptFormId) {
        if (GeneralTool.isNull(targetId) || GeneralTool.isNull(receiptFormId)) {
            return Collections.emptyList();
        }
        Set<Long> ids = businessProviderlMapper.getInvoicePlanId(targetId, receiptFormId);
        if (GeneralTool.isNotEmpty(ids)) {
            List<BusinessProviderVo> providerDtos = businessProviderlMapper.getBusinessProviderInfoByPlanIds(ids);
            List<BaseSelectEntity> baseSelectEntities = new ArrayList<>();
            for (BusinessProviderVo providerDto : providerDtos) {
                BaseSelectEntity entity = new BaseSelectEntity();
                entity.setId(providerDto.getPlanId());
                StringBuilder stringBuilder = new StringBuilder();
                if (StringUtils.isNotBlank(providerDto.getName())) {
                    stringBuilder.append(providerDto.getName());
                }
                if (StringUtils.isNotBlank(providerDto.getNameChn())) {
                    stringBuilder.append("（").append(providerDto.getNameChn()).append("）");
                }
                if (StringUtils.isNotBlank(providerDto.getNum())) {
                    stringBuilder.append("（").append(providerDto.getNum()).append("）");
                }
                entity.setName(stringBuilder.toString());
                baseSelectEntities.add(entity);
            }
            return baseSelectEntities;
        }
        return Collections.emptyList();
    }

    @Override
    public List<BaseSelectEntity> getBusinessObjectSelection(Long companyId) {
        if (GeneralTool.isNull(companyId)) {
            return Collections.emptyList();
        }
        return businessProviderlMapper.providerSelectByCompanyId(companyId);
    }

    @Override
    public List<BaseSelectEntity> getBusinessProviderSelectByTypeKey(Long companyId, String typeKey) {
        if (GeneralTool.isNull(companyId)) {
            return Collections.emptyList();
        }
        return businessProviderlMapper.getBusinessProviderSelectByTypeKey(companyId, typeKey);
    }

    /**
     * 获取留学保险提供商可绑定的应收计划
     * @param targetId
     * @param receiptFormId
     * @return
     */
    @Override
    public List<BaseSelectEntity> getPlanIdsByBusinessProviderId(Long targetId, Long receiptFormId) {
        List<BaseSelectEntity> baseSelectEntities = new ArrayList<>();
        //保险
        List<ReceivablePlan> receivablePlanList = businessProviderlMapper.getPlanIdsByBusinessProviderId(targetId, receiptFormId);
        for (ReceivablePlan receivablePlan : receivablePlanList) {
            BaseSelectEntity baseSelectEntity = new BaseSelectEntity();
            StudentInsuranceVo studentInsuranceById = studentInsuranceService.findStudentInsuranceById(receivablePlan.getFkTypeTargetId());
            String targetName = studentInsuranceById.getStudentName();
            String agentName = studentInsuranceById.getFkAgentName();

            StringJoiner stringJoiner = new StringJoiner("/");
            if (GeneralTool.isNotEmpty(studentInsuranceById.getNum())) {
                stringJoiner.add("【" + studentInsuranceById.getNum() + "】");
            }
            if (GeneralTool.isNotEmpty(targetName)) {
                stringJoiner.add(targetName);
            }
            if (GeneralTool.isNotEmpty(studentInsuranceById.getFkAreaCountryName())) {
                stringJoiner.add(studentInsuranceById.getFkAreaCountryName());
            }
            if (GeneralTool.isNotEmpty(studentInsuranceById.getChannelName())) {
                stringJoiner.add(studentInsuranceById.getChannelName());
            }
            if (GeneralTool.isNotEmpty(agentName)) {
                stringJoiner.add(agentName);
            }
            baseSelectEntity.setName(stringJoiner.toString());
            baseSelectEntity.setId(receivablePlan.getId());
            baseSelectEntities.add(baseSelectEntity);
        }


        //澳小保
        List<BaseSelectEntity> insuranceOrderSelect =  insuranceOrderMapper.getPlanInfoByTargetId(targetId, receiptFormId);
        if (GeneralTool.isNotEmpty(insuranceOrderSelect))
        baseSelectEntities.addAll(insuranceOrderSelect);
        return baseSelectEntities;
    }

    /**
     * 获取留学保险提供商名称
     * @param ids
     * @return
     */
    @Override
    public Map<Long, String> getInsuranceProviderNameByIds(Set<Long> ids) {
        List<BusinessProviderVo> namesByIds = businessProviderlMapper.getNamesByIds(ids);
        if (GeneralTool.isNotEmpty(namesByIds)) {
            return namesByIds.stream().collect(Collectors.toMap(BusinessProviderVo::getId, BusinessProviderVo::getName));
        }
        return Collections.emptyMap();
    }
}

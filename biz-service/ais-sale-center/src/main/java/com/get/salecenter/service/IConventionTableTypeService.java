package com.get.salecenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.common.result.Page;
import com.get.salecenter.entity.ConventionTableType;
import com.get.salecenter.vo.ConventionTableTypeVo;
import com.get.salecenter.dto.ConventionTableTypeDto;

import java.util.List;

/**
 * @author: Sea
 * @create: 2020/8/25 15:01
 * @verison: 1.0
 * @description:
 */
public interface IConventionTableTypeService extends IService<ConventionTableType> {

    /**
     * 详情
     *
     * @param id
     * @return
     */
    ConventionTableTypeVo findTableTypeById(Long id);

    /**
     * 批量新增
     *
     * @param tableTypeVos
     */
    void batchAdd(List<ConventionTableTypeDto> tableTypeVos);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 修改
     *
     * @param tableTypeVo
     * @return
     */
    ConventionTableTypeVo updateTableType(ConventionTableTypeDto tableTypeVo);

    /**
     * 列表
     *
     * @param tableTypeVo
     * @param page
     * @return
     */
    List<ConventionTableTypeVo> getTableTypes(ConventionTableTypeDto tableTypeVo, Page page);

    /**
     * 上移下移
     *
     * @param tableTypeVos
     */
    void movingOrder(List<ConventionTableTypeDto> tableTypeVos);

    /**
     * 桌台类型下拉框数据
     *
     * @return
     */
    List<ConventionTableTypeVo> getTableTypeList();
}

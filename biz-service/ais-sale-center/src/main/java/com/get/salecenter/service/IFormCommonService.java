package com.get.salecenter.service;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.ConventionPersonRegistration;

/**
 * @author: <PERSON>
 * @create: 2021/7/13 17:04
 * @verison: 1.0
 * @description:
 */
public interface IFormCommonService {

    /**
     * @return ConventionPersonRegistration
     * @Description :根据参会人id获取中间表
     * @Param [conventionPersonId]
     * <AUTHOR>
     */
    ConventionPersonRegistration getConventionPersonRegistration(Long conventionPersonId);

    /**
     * @return void
     * @Description :关联表删除：参会人和报名名册
     * @Param [id]
     * <AUTHOR>
     */
    void deleteTable(Long id);

    /**
     * @return void
     * @Description :设置新增信息
     * @Param [yEntity]
     * <AUTHOR>
     */
    void addUserInfoToEntity(BaseEntity entity);

    /**
     * @return void
     * @Description :设置更新信息
     * @Param [yEntity]
     * <AUTHOR>
     */
    void updateUserInfoToEntity(BaseEntity entity);
}

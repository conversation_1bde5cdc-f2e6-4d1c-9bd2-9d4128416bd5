package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.eunms.ErrorCodeEnum;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.vo.StudentOfferNoticeListVo;
import com.get.salecenter.vo.StudentOfferNoticeListItemVo;
import com.get.salecenter.service.StudentOfferNoticeService;
import com.get.salecenter.dto.*;
import com.get.salecenter.dto.BatchStudentOfferNoticeDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.get.core.log.annotation.OperationLogger;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.PostMapping;
import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 *  前端接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-13
 */

@Api(tags = "入读意向通知管理")
@RestController
@RequestMapping("sale/studentOfferNotice")
public class StudentOfferNoticeController {


    @Resource
    private StudentOfferNoticeService studentOfferNoticeService;


    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentOfferItemStepVo>
     * @Description :列表数据
     * @Param [page]
     */
    @ApiOperation(value = "邮件统计按钮", notes = "邮件统计按钮")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/入读意向通知管理/邮件统计按钮")
    @PostMapping("emailStatistics")
    public ResponseBo emailStatistics(@RequestBody @Validated EmailStatisticsDto emailStatisticsDto) {
        return studentOfferNoticeService.doEmailStatistics(emailStatisticsDto);
    }


    @ApiOperation(value = "列表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/入读意向通知管理/列表")
    @PostMapping("datas")
    public ResponseBo<StudentOfferNoticeListVo> datas(@RequestBody SearchBean<StudentOfferNoticeListDto> page) {
        List<StudentOfferNoticeListVo> datas = studentOfferNoticeService.getStudentOfferNotices(page.getData(),page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo(datas,p);
    }



    @ApiOperation(value = "批量发送邮件", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/入读意向通知管理/批量发送邮件")
    @PostMapping("batchSendStudentOfferNotice")
    public ResponseBo batchSendStudentOfferNotice(@RequestBody BatchStudentOfferNoticeDto batchStudentOfferNoticeDto) {
        studentOfferNoticeService.batchSendStudentOfferNotice(batchStudentOfferNoticeDto);
        return ResponseBo.ok(ErrorCodeEnum.REQUEST_OK.getCode(),"后台邮件发送中，请稍后注意查看发送状态");
    }


    @ApiOperation(value = "批量删除", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/入读意向通知管理/批量删除")
    @PostMapping("batchDeleteStudentOfferNotice")
    public ResponseBo batchDeleteStudentOfferNotice(@RequestBody BatchStudentOfferNoticeDto batchStudentOfferNoticeDto) {
        studentOfferNoticeService.batchDeleteStudentOfferNotice(batchStudentOfferNoticeDto);
        return DeleteResponseBo.ok();
    }

    @ApiOperation(value = "批量匹配", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/入读意向通知管理/批量匹配")
    @PostMapping("batchMatchStudentOfferNotice")
    public ResponseBo batchMatchStudentOfferNotice(@RequestBody BatchStudentOfferNoticeDto batchStudentOfferNoticeDto) {
        studentOfferNoticeService.batchMatchStudentOfferNotice(batchStudentOfferNoticeDto);
        return ResponseBo.ok();
    }


    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "发送状态下拉", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/入读意向通知管理/发送状态下拉")
    @PostMapping("getMailStatusSelect")
    public ResponseBo getMailStatusSelect() {
        return new ListResponseBo<>(ProjectExtraEnum.enumsTranslation2Arrays(ProjectExtraEnum.MAIL_STATUS));
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentProjectRoleVo>
     * @Description :修改信息
     * @Param [studentProjectRoleVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/入读意向通知管理/更新")
    @PostMapping("update")
    public ResponseBo<StudentOfferNoticeListVo> update(@RequestBody StudentOfferNoticeUpdateDto studentOfferNoticeUpdateDto) {
        return UpdateResponseBo.ok(studentOfferNoticeService.updateStudentOfferNotice(studentOfferNoticeUpdateDto));
    }

    @ApiOperation(value = "二级列表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/入读意向通知管理/二级列表")
    @PostMapping("getDatasItem")
    public ResponseBo<StudentOfferNoticeListItemVo> getDatasItem(@RequestBody StudentOfferNoticeListItemDto studentOfferNoticeListItemDto) {
        List<StudentOfferNoticeListItemVo> datas = studentOfferNoticeService.getDatasItem(studentOfferNoticeListItemDto);
        return new ListResponseBo(datas);
    }


    @ApiOperation(value = "轮询进度查询接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/入读意向通知管理/轮询进度查询接口")
    @PostMapping("getEmailStatisticsStatus")
    public ResponseBo getEmailStatisticsStatus() {
        Integer status = studentOfferNoticeService.getEmailStatisticsStatus();
        return new ResponseBo(status);
    }

//    @ApiOperation(value = "批量发送邮件", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/入读意向通知管理/批量发送邮件")
//    @PostMapping("batchSendStudentOfferNotice")
//    public ResponseBo (@RequestBody BatchStudentOfferNoticeDto batchStudentOfferNoticeVo) {
//        studentOfferNoticeService.batchSendStudentOfferNotice(batchStudentOfferNoticeVo);
//        return ResponseBo.ok(ErrorCodeEnum.REQUEST_OK.getCode(),"后台邮件发送中，请稍后注意查看发送状态");
//    }

    @ApiOperation(value = "一键全发送邮件", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/入读意向通知管理/一键全发送邮件")
    @PostMapping("sendAllWithOneClick")
    public ResponseBo sendAllWithOneClick() throws InterruptedException {
        studentOfferNoticeService.sendAllWithOneClick();
        return ResponseBo.ok(ErrorCodeEnum.REQUEST_OK.getCode(),"后台邮件发送中，请稍后注意查看发送状态");
    }
}

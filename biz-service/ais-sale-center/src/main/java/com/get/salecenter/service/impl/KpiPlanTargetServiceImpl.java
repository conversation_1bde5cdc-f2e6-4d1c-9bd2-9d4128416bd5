package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.KpiPlanTargetMapper;
import com.get.salecenter.entity.KpiPlanTarget;
import com.get.salecenter.service.KpiPlanTargetService;
import com.get.salecenter.dto.KpiPlanTargetDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
@Service
public class KpiPlanTargetServiceImpl extends ServiceImpl<KpiPlanTargetMapper, KpiPlanTarget> implements KpiPlanTargetService {

    @Resource
    private KpiPlanTargetMapper kpiPlanTargetMapper;

    @Resource
    private UtilService utilService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addKpiPlanTarget(KpiPlanTargetDto kpiPlanTargetDto) {
        if (GeneralTool.isEmpty(kpiPlanTargetDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        //清除已经设置KPI的配置
        kpiPlanTargetMapper.delete(Wrappers.<KpiPlanTarget>lambdaQuery()
                .eq(KpiPlanTarget::getFkKpiPlanGroupItemId, kpiPlanTargetDto.getFkKpiPlanGroupItemId())
                .eq(KpiPlanTarget::getFkKpiPlanStaffId, kpiPlanTargetDto.getFkKpiPlanStaffId()));
        KpiPlanTarget kpiPlanTarget = BeanCopyUtils.objClone(kpiPlanTargetDto, KpiPlanTarget::new);
        utilService.setCreateInfo(kpiPlanTarget);
        kpiPlanTargetMapper.insert(kpiPlanTarget);
        return kpiPlanTarget.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(List<KpiPlanTargetDto> kpiPlanTargetDtos) {
        if (GeneralTool.isEmpty(kpiPlanTargetDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        List<KpiPlanTarget> kpiPlanTargets = new ArrayList<>();
        for(KpiPlanTargetDto vo : kpiPlanTargetDtos){
            //清除已经设置KPI的配置
            kpiPlanTargetMapper.delete(Wrappers.<KpiPlanTarget>lambdaQuery()
                    .eq(KpiPlanTarget::getFkKpiPlanGroupItemId, vo.getFkKpiPlanGroupItemId())
                    .eq(KpiPlanTarget::getFkKpiPlanStaffId, vo.getFkKpiPlanStaffId()));
            KpiPlanTarget kpiPlanTarget = BeanCopyUtils.objClone(vo, KpiPlanTarget::new);
            utilService.setCreateInfo(kpiPlanTarget);
            kpiPlanTargets.add(kpiPlanTarget);
        }

        this.saveBatch(kpiPlanTargets);
    }



}

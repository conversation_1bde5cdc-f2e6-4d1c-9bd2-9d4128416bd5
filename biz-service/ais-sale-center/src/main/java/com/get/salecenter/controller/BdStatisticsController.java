package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.CommonUtil;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.redis.lock.RedisLock;
import com.get.core.secure.UserInfo;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.tool.utils.RequestContextUtil;
import com.get.salecenter.dto.AgentAnnualSummaryDto;
import com.get.salecenter.dto.BdStudentBonusDto;
import com.get.salecenter.dto.BdStudentStatisticalComparisonDto;
import com.get.salecenter.service.BdStatisticsService;
import com.get.salecenter.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * bd统计控制类
 *
 * <AUTHOR>
 * @date 2023/3/16 10:01
 */
@Api(tags = "学生管理")
@RestController
@RequestMapping("sale/bdStatistics")
public class BdStatisticsController {

    @Resource
    private BdStatisticsService bdStatisticsService;

    @ApiOperation(value = "BD学生统计对比表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生管理/BD学生统计对比表")
    @PostMapping("bdStudentStatisticalComparison")
    public ResponseBo<BdStudentStatisticalComparisonVo> bdStudentStatisticalComparison(@RequestBody @Validated SearchBean<BdStudentStatisticalComparisonDto> page) {
        //此处无法适用异步调用放到外部
        if (!SecureUtil.validateCompanys(page.getData().getFkCompanyIds())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
        }
        List<BdStudentStatisticalComparisonVo> datas = bdStatisticsService.bdStudentStatisticalComparison(page.getData(), page, SecureUtil.getCountryIds(), SecureUtil.getStaffId());
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    @ApiOperation(value = "获取角色提成规则", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生管理/项目成员对比结算表/获取角色提成规则")
    @PostMapping("getBdStaffCommissionPolicy")
    public ResponseBo<StaffCommissionPolicyVo> getBdStaffCommissionPolicy(@RequestParam("projectRoleKey") String projectRoleKey) {
//        if (!SecureUtil.validateCompany(fkCompanyId)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
//        }
        List<StaffCommissionPolicyVo> datas = bdStatisticsService.getBdStaffCommissionPolicy(0L, projectRoleKey);
        return new ListResponseBo<>(datas);
    }

    @ApiOperation(value = "项目成员角色提成结算", notes = "")
    @RedisLock(value = "get:bdStaffCommissionPolicySettleAccountsLock", param = "#bdStudentStatisticalComparisonDto.projectRoleKey", waitTime = 10L, leaseTime = 600L)
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生管理/项目成员对比结算表/项目成员角色提成结算")
    @PostMapping("bdStaffCommissionPolicySettleAccounts")
    public ResponseBo bdStaffCommissionPolicySettleAccounts(@RequestBody @Validated BdStudentStatisticalComparisonDto bdStudentStatisticalComparisonDto) {
        // 权限 校验

        if (!SecureUtil.validateCompanys(bdStudentStatisticalComparisonDto.getFkCompanyIds())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
        }
        // 结算日志
        if (GeneralTool.isEmpty(bdStudentStatisticalComparisonDto.getSettlementDate())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        bdStatisticsService.bdStaffCommissionPolicySettleAccounts(bdStudentStatisticalComparisonDto);
        return ResponseBo.ok();

    }

    @ApiOperation(value = "下载BD学生统计对比表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生管理/下载BD学生统计对比表")
    @PostMapping("downloadBdStudentStatisticalComparison")
    public void downloadBdStudentStatisticalComparison(@RequestBody BdStudentStatisticalComparisonDto data, HttpServletResponse response) {
        //此处无法适用异步调用放到外部
        if (!SecureUtil.validateCompanys(data.getFkCompanyIds())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
        }
        CommonUtil.ok(response);
        Map<String, String> headerMap = RequestContextUtil.getHeaderMap();
        UserInfo user = GetAuthInfo.getUser();
        String locale = SecureUtil.getLocale();
        bdStatisticsService.downloadBdStudentStatisticalComparison(data, headerMap, locale, user, SecureUtil.getCountryIds(), SecureUtil.getStaffId());
    }
    @ApiOperation(value = "下载代理统计对比表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生管理/下载BD学生统计对比表")
    @PostMapping("downloadAgentStatisticsComparison")
    public void downloadAgentStatisticsComparison(@RequestBody AgentAnnualSummaryDto annualSummaryVo, HttpServletResponse response) {
        if (!SecureUtil.validateCompany(annualSummaryVo.getFkCompanyId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
        }
        CommonUtil.ok(response);
        Map<String, String> headerMap = RequestContextUtil.getHeaderMap();
        UserInfo user = GetAuthInfo.getUser();
        String locale = SecureUtil.getLocale();
        bdStatisticsService.downloadAgentStatisticsComparison(annualSummaryVo, headerMap, locale, user, SecureUtil.getCountryIds(), SecureUtil.getInstitutionIds());
    }

    @ApiOperation(value = "BD学生统计对比表 bd比对统计总计", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生管理/BD学生统计对比表 bd比对统计总计")
    @PostMapping("bdStudentStatisticalComparisonTotal")
    public ResponseBo<BdStudentStatisticalComparisonVo> bdStudentStatisticalComparisonTotal(@RequestBody @Validated BdStudentStatisticalComparisonDto bdStudentStatisticalComparisonDto) {
        List<BdStudentStatisticalComparisonVo> datas = bdStatisticsService.bdStudentStatisticalComparisonTotal(bdStudentStatisticalComparisonDto, SecureUtil.getCountryIds(), SecureUtil.getStaffId());
        return new ListResponseBo<>(datas);
    }

    @ApiOperation(value = "BD学生统计对比表 条件回显", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生管理/BD学生统计对比表 条件回显")
    @PostMapping("bdStudentStatisticalComparisonConditionalEcho")
    public ResponseBo bdStudentStatisticalComparisonConditionalEcho(@RequestBody BdStudentStatisticalComparisonDto bdStudentStatisticalComparisonDto) {

        BdStudentStatisticalConditionalEchoVo data = bdStatisticsService.bdStudentStatisticalComparisonConditionalEcho(bdStudentStatisticalComparisonDto);
        return new ResponseBo<>(data);
    }

    @ApiOperation(value = "代理统计对比表 条件回显", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生管理/BD学生统计对比表 条件回显")
    @PostMapping("agentComparisonConditionalEcho")
    public ResponseBo agentComparisonConditionalEcho(@RequestBody AgentAnnualSummaryDto AgentAnnualSummaryDto) {
        AgentStatisticalConditionalEchoVo data = bdStatisticsService.agentComparisonConditionalEcho(AgentAnnualSummaryDto);
        return new ResponseBo<>(data);
    }

    @ApiOperation(value = "hti首页学生统计柱状图", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/首页/hti首页学生统计柱状图")
    @PostMapping("htiHomeBarChart")
    public ResponseBo<StudentStatistical> htiHomeBarChart(@RequestBody @Validated BdStudentStatisticalComparisonDto bdStudentStatisticalComparisonDto) {
        List<StudentStatistical> datas = bdStatisticsService.htiHomeBarChart(bdStudentStatisticalComparisonDto);
        return new ListResponseBo<>(datas);
    }


    @ApiOperation(value = "代理年度总表对比统计", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生管理/代理年度对比统计")
    @PostMapping("agentAnnualSummaryStatistics")
    public ResponseBo<AgentAnnualSummaryStatisticsVo> agentAnnualSummaryStatistics(@RequestBody @Validated AgentAnnualSummaryDto agentAnnualSummaryDto) {
        //此处无法适用异步调用放到外部
        if (!SecureUtil.validateCompany(agentAnnualSummaryDto.getFkCompanyId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
        }
        List<AgentAnnualSummaryStatisticsVo> datas = bdStatisticsService.agentAnnualSummaryStatistics(agentAnnualSummaryDto, SecureUtil.getCountryIds(), GetAuthInfo.getStaffId(), SecureUtil.getInstitutionIds());
        return new ListResponseBo<>(datas);
    }

    @ApiOperation(value = "代理年度时间区间对比统计", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生管理/代理年度时间区间对比统计")
    @PostMapping("agentAnnualIntervalComparisonStatistics")
    public ResponseBo<AgentAnnualSummaryStatisticsVo> agentAnnualIntervalComparisonStatistics(@RequestBody @Validated AgentAnnualSummaryDto agentAnnualSummaryDto) {
        //此处无法适用异步调用放到外部
        if (!SecureUtil.validateCompany(agentAnnualSummaryDto.getFkCompanyId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
        }
        List<AgentAnnualSummaryStatisticsVo> datas = bdStatisticsService.agentAnnualIntervalComparisonStatistics(agentAnnualSummaryDto, SecureUtil.getCountryIds(), GetAuthInfo.getStaffId(), SecureUtil.getInstitutionIds());
        return new ListResponseBo<>(datas);
    }


    @ApiOperation(value = "BD学生奖金预统计", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生管理/BD学生奖金预统计")
    @PostMapping("bdStudentBonusPreStatistics")
    public ResponseBo<BdStudentBonusVo> BdStudentBonusPreStatistics(@RequestBody SearchBean<BdStudentBonusDto> page) {
        return new ListResponseBo<>( bdStatisticsService.bdStudentBonusPreStatistics(page), BeanCopyUtils.objClone(page, Page::new));
    }

    @ApiOperation(value = "BD下属下拉", notes = "登录人下属(一层)")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生管理/BD下属下拉")
    @VerifyPermission(IsVerify = false)
    @GetMapping("bdSubordinate")
    public ResponseBo<BaseSelectEntity> bdSubordinate() {
        return new ListResponseBo<>( bdStatisticsService.bdSubordinate());
    }
}

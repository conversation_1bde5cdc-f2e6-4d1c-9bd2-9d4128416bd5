package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.feign.IFinanceCenterClient;
import com.get.salecenter.dao.sale.BusinessProviderAccountMapper;
import com.get.salecenter.vo.BusinessProviderAccountVo;
import com.get.salecenter.entity.BusinessProviderAccount;
import com.get.salecenter.service.BusinessProviderAccountService;
import com.get.salecenter.dto.BusinessProviderAccountDto;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class BusinessProviderAccountServiceImpl extends BaseServiceImpl<BusinessProviderAccountMapper,BusinessProviderAccount> implements BusinessProviderAccountService {


    @Resource
    private BusinessProviderAccountMapper businessProviderAccountMapper;

    @Resource
    private UtilService<Object> utilService;

    @Resource
    private IFinanceCenterClient financeCenterClient;

    /**
     * Author Cream
     * Description : //获取业务提供商账户列表
     * Date 2023/2/14 15:30
     * Params:
     * Return
     */
    @Override
    public ResponseBo<BusinessProviderAccountVo> getBusinessProviderAccounts(BusinessProviderAccountDto businessProviderAccountDto, Page page) {
        if (GeneralTool.isNull(businessProviderAccountDto.getFkBusinessProviderId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        IPage<BusinessProviderAccountVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<BusinessProviderAccountVo> providerAccountDtoList = businessProviderAccountMapper.getBusinessProviderAccountsById(iPage, businessProviderAccountDto.getFkBusinessProviderId());
        page.setAll((int) iPage.getTotal());
        if (providerAccountDtoList.isEmpty()) {
            return new ResponseBo<>();
        }
        Set<String> nums = providerAccountDtoList.stream().map(BusinessProviderAccountVo::getFkCurrencyTypeNum).collect(Collectors.toSet());
        Map<String, String> numsMap = financeCenterClient.getCurrencyNamesByNums(nums).getData();
        for (BusinessProviderAccountVo accountDto : providerAccountDtoList) {
            accountDto.setAccountCardTypeName(ProjectExtraEnum.getInitialValueByKey(accountDto.getAccountCardType(),ProjectExtraEnum.cardType));
            accountDto.setCurrencyTypeName(numsMap.get(accountDto.getFkCurrencyTypeNum()));
        }
        return new ListResponseBo<>(providerAccountDtoList, BeanCopyUtils.objClone(page,Page::new));
    }

    /**
     * Author Cream
     * Description : //新增
     * Date 2023/2/14 15:45
     * Params:
     * Return
     */
    @Override
    public SaveResponseBo add(BusinessProviderAccountDto businessProviderAccountDto) {
        if (GeneralTool.isNull(businessProviderAccountDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        BusinessProviderAccount businessProviderAccount = new BusinessProviderAccount();
        BeanUtils.copyProperties(businessProviderAccountDto,businessProviderAccount);
        utilService.setCreateInfo(businessProviderAccount);
        businessProviderAccountMapper.insert(businessProviderAccount);
        return SaveResponseBo.ok(businessProviderAccount.getId());
    }

    /**
     * Author Cream
     * Description : //快捷设置首选合同
     * Date 2023/2/16 14:28
     * Params:
     * Return
     */
    @Override
    public SaveResponseBo quickFirstContractAccount(Long businessProviderId, Long accountId) {
        if (GeneralTool.isNull(businessProviderId) || GeneralTool.isNull(accountId) ) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        List<BusinessProviderAccount> providerAccounts = businessProviderAccountMapper
                .selectList(Wrappers.<BusinessProviderAccount>lambdaQuery().eq(BusinessProviderAccount::getFkBusinessProviderId, businessProviderId));
        if (GeneralTool.isNotEmpty(providerAccounts)) {
            List<BusinessProviderAccount> updateList = new ArrayList<>();
            for (BusinessProviderAccount providerAccount : providerAccounts) {
                if (providerAccount.getIsDefault()!=null && providerAccount.getIsDefault()) {
                    providerAccount.setIsDefault(false);
                    updateList.add(providerAccount);
                }
                if (accountId.equals(providerAccount.getId())) {
                    providerAccount.setIsDefault(true);
                    updateList.add(providerAccount);
                }
            }
            if (GeneralTool.isNotEmpty(updateList)) {
                updateBatchById(updateList);
            }
        }
        return new SaveResponseBo(accountId);
    }

    /**
     * Author Cream
     * Description : //快速激活或屏蔽合同账户
     * Date 2023/2/16 14:28
     * Params:
     * Return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public SaveResponseBo quickActivationOrMask(Long businessProviderId, Long accountId, Boolean status) {
        if (GeneralTool.isNull(businessProviderId) || GeneralTool.isNull(accountId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        BusinessProviderAccount providerAccount = businessProviderAccountMapper.selectById(accountId);
        if (providerAccount!=null) {
            List<BusinessProviderAccount> institutionProviderAccounts = businessProviderAccountMapper.selectList(Wrappers.<BusinessProviderAccount>lambdaQuery().eq(BusinessProviderAccount::getFkBusinessProviderId, businessProviderId)
                    .eq(BusinessProviderAccount::getFkCurrencyTypeNum, providerAccount.getFkCurrencyTypeNum()));
            if (GeneralTool.isNotEmpty(institutionProviderAccounts)) {
                List<BusinessProviderAccount> updateList = new ArrayList<>();
                if (status) {
                    for (BusinessProviderAccount account : institutionProviderAccounts) {
                        if (account.getIsActive()!=null && account.getIsActive()) {
                            account.setIsActive(false);
                            updateList.add(account);
                        }
                        if (accountId.equals(account.getId())) {
                            account.setIsActive(true);
                            updateList.add(account);
                        }
                    }
                    if (GeneralTool.isNotEmpty(updateList)) {
                        updateBatchById(updateList);
                    }
                }else {
                    providerAccount.setIsActive(false);
                    businessProviderAccountMapper.updateById(providerAccount);
                }
            }
        }
        return SaveResponseBo.ok(accountId);
    }

    /**
     * Author Cream
     * Description : //详情
     * Date 2023/2/14 15:52
     * Params:
     * Return
     */
    @Override
    public BusinessProviderAccountVo findInfoById(Long id) {
        if (GeneralTool.isNull(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        BusinessProviderAccount businessProviderAccount = businessProviderAccountMapper.selectById(id);
        BusinessProviderAccountVo businessProviderAccountVo = new BusinessProviderAccountVo();
        BeanUtils.copyProperties(businessProviderAccount, businessProviderAccountVo);
        String currencyName = financeCenterClient.getCurrencyNameByNum(businessProviderAccount.getFkCurrencyTypeNum()).getData();
        businessProviderAccountVo.setCurrencyTypeName(currencyName);
        businessProviderAccountVo.setAccountCardTypeName(ProjectExtraEnum.getInitialValueByKey(businessProviderAccountVo.getAccountCardType(),ProjectExtraEnum.cardType));
        return businessProviderAccountVo;
    }

    /**
     * Author Cream
     * Description : //更新
     * Date 2023/2/14 15:51
     * Params:
     * Return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public BusinessProviderAccountVo update(BusinessProviderAccountDto businessProviderAccountDto) {
        if (GeneralTool.isNull(businessProviderAccountDto) || GeneralTool.isNull(businessProviderAccountDto.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        BusinessProviderAccount businessProviderAccount = businessProviderAccountMapper.selectById(businessProviderAccountDto.getId());
        if (GeneralTool.isNull(businessProviderAccount)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
        BeanUtils.copyProperties(businessProviderAccountDto,businessProviderAccount);
        utilService.setUpdateInfo(businessProviderAccount);
        businessProviderAccountMapper.updateById(businessProviderAccount);
        return findInfoById(businessProviderAccount.getId());
    }

    /**
     * Author Cream
     * Description : //删除
     * Date 2023/2/14 15:55
     * Params:
     * Return
     */
    @Override
    public SaveResponseBo delete(Long id) {
        if (GeneralTool.isNull(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        BusinessProviderAccount businessProviderAccount = businessProviderAccountMapper.selectById(id);
        if (GeneralTool.isNull(businessProviderAccount)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
        businessProviderAccountMapper.deleteById(id);
        return SaveResponseBo.ok(id);
    }

    /**
     * Author Cream
     * Description : //业务提供商合同账户列表账户重复提示
     * Date 2023/2/15 16:04
     * Params:
     * Return
     */
    @Override
    public ResponseBo<String> getBusinessProviderContractAccountExist(Long id, Long businessProviderId, String bankAccount, String bankAccountNum) {
        List<BusinessProviderAccountVo> b1 = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        if (GeneralTool.isNotEmpty(bankAccount)) {
            b1 = businessProviderAccountMapper.getContractAccountExist(businessProviderId, bankAccount, null);
        }
        List<BusinessProviderAccountVo> b2 = new ArrayList<>();
        if (GeneralTool.isNotEmpty(bankAccountNum)) {
            b2 = businessProviderAccountMapper.getContractAccountExist(businessProviderId, null, bankAccountNum);
        }
        if (GeneralTool.isNotEmpty(b1)) {
            if (GeneralTool.isNotEmpty(id)) {
                b1.stream().filter(d -> !d.getId().equals(id)).forEach(dd -> sb.append(LocaleMessageUtils.getMessage("AGENT")).append("：")
                        .append(dd.getBusinessProviderName()).append("，").append(LocaleMessageUtils.getMessage("AGENT_ACCOUNT_NAME")).append("：").append(dd.getBankAccount())
                        .append("，").append(LocaleMessageUtils.getMessage("STUDENT_EXIST")).append("！<br/>"));

            } else {
                b1.forEach(dd -> sb.append(LocaleMessageUtils.getMessage("AGENT")).append("：")
                        .append(dd.getBusinessProviderName()).append("，").append(LocaleMessageUtils.getMessage("AGENT_ACCOUNT_NAME")).append("：").append(dd.getBankAccount())
                        .append("，").append(LocaleMessageUtils.getMessage("STUDENT_EXIST")).append("！<br/>"));
            }
        }
        if (GeneralTool.isNotEmpty(b2)) {
            if (GeneralTool.isNotEmpty(id)) {
                b2.stream().filter(d -> !d.getId().equals(id)).forEach(dd -> sb.append(LocaleMessageUtils.getMessage("AGENT")).append("：")
                        .append(dd.getBusinessProviderName()).append("，").append(LocaleMessageUtils.getMessage("AGENT_ACCOUNT_NO")).append("：").append(dd.getBankAccountNum())
                        .append("，").append(LocaleMessageUtils.getMessage("STUDENT_EXIST")).append("！<br/>"));
            } else {
                b2.forEach(dd -> sb.append(LocaleMessageUtils.getMessage("AGENT")).append("：")
                        .append(dd.getBusinessProviderName()).append("，").append(LocaleMessageUtils.getMessage("AGENT_ACCOUNT_NO")).append("：").append(dd.getBankAccountNum())
                        .append("，").append(LocaleMessageUtils.getMessage("STUDENT_EXIST")).append("！<br/>"));
            }
        }
        return new ResponseBo<>(sb.toString());
    }

    /**
     * Author Cream
     * Description : //获取业务提供商账户列表
     * Date 2023/2/27 14:47
     * Params:
     * Return
     */
    @Override
    public List<BaseSelectEntity> getBusinessProviderAccountList(Long fkTargetId) {
        if (Objects.isNull(fkTargetId)) {
            return Collections.emptyList();
        }
        return businessProviderAccountMapper.getBusinessProviderAccountListByFkTargetId(fkTargetId);
    }


    /**
     * Author Cream
     * Description : //获取业务提供商账
     * Date 2023/2/27 17:45
     * Params:
     * Return
     */
    @Override
    public String getBusinessProviderBankAccountNameById(Long fkBankAccountId) {
        if (Objects.isNull(fkBankAccountId)) {
            return null;
        }
        return businessProviderAccountMapper.getBusinessProviderBankAccountNameById(fkBankAccountId);
    }
}

package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.eunms.TableEnum;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.vo.EventPlanVo;
import com.get.salecenter.vo.NameLabelVo;
import com.get.salecenter.service.NameLabelService;
import com.get.salecenter.dto.NameLabelSearchDto;
import com.get.salecenter.dto.NameLabelDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2024/3/25
 * @TIME: 10:52
 * @Description:
 **/
@Api(tags = "业务标签管理")
@RestController
@RequestMapping("sale/nameLabel")
@VerifyPermission(IsVerify = false)
public class NameLabelController {

    @Resource
    private NameLabelService nameLabelService;

    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/业务标签管理/列表数据")
    @PostMapping("datas")
    public ResponseBo<NameLabelVo> datas(@RequestBody SearchBean<NameLabelSearchDto> page) {
        List<NameLabelVo> datas = nameLabelService.datas(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    @ApiOperation(value = "详情", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/业务标签管理/详情")
    @GetMapping("/{id}")
    public ResponseBo<NameLabelVo> detail(@PathVariable("id") Long id) {
        NameLabelVo data = nameLabelService.findNameLabelById(id);
        return new ResponseBo<>(data);
    }

    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/业务标签管理/新增")
    @PostMapping("add")
    public ResponseBo add(@RequestBody  @Validated(NameLabelDto.Add.class) NameLabelDto vo) {
        return SaveResponseBo.ok(nameLabelService.addNameLabel(vo));
    }

    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/业务标签管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        nameLabelService.delete(id);
        return DeleteResponseBo.ok();
    }

    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/业务标签管理/更新")
    @PostMapping("update")
    public ResponseBo<EventPlanVo> update(@RequestBody  @Validated(NameLabelDto.Update.class) NameLabelDto vo) {
        return UpdateResponseBo.ok(nameLabelService.updateNameLabel(vo));
    }

    @ApiOperation(value = "拖拽", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/业务标签管理/拖拽")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestParam("start")Integer start, @RequestParam("end")Integer end) {
        nameLabelService.movingOrder(start,end);
        return ResponseBo.ok();
    }

    @ApiOperation("业务标签类型下拉")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/业务标签管理/业务标签类型下拉")
    @GetMapping("getNameLabelType")
    public ResponseBo getNameLabelType() {
        return new ListResponseBo<>(TableEnum.enums2Arrays(TableEnum.NAME_LABEL));
    }



}

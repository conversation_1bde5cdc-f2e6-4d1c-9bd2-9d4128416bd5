package com.get.salecenter.service;

import com.get.salecenter.vo.SponsorshipConfigVo;

import java.util.List;
import java.util.Map;

/**
 * @author: Sea
 * @create: 2021/4/30 17:22
 * @verison: 1.0
 * @description:
 */
public interface ISponsorshipConfigService {
    /**
     * @return java.util.List<com.get.salecenter.vo.SponsorshipConfigVo>
     * @Description :获取赞助列表信息以及每个赞助是否售空
     * @Param []
     * <AUTHOR>
     */
    List<Map<String, List<SponsorshipConfigVo>>> getSponsorshipConfig();

    /**
     * @return java.lang.String
     * @Description :根据id获取赞助类型
     * @Param [fkSponsorshipConfigId]
     * <AUTHOR>
     */
    String getSponsorshipConfigType(Long fkSponsorshipConfigId);
}

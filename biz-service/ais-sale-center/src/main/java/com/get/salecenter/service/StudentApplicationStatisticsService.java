package com.get.salecenter.service;

import com.get.core.secure.StaffInfo;
import com.get.salecenter.vo.StatisticsVo;
import com.get.salecenter.vo.StudentApplicationStatisticsVo;
import com.get.salecenter.dto.StatisticsDto;
import com.get.salecenter.dto.StudentApplicationStatisticsDto;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE: 2022/3/30
 * @TIME: 0:19
 * @Description:
 **/
public interface StudentApplicationStatisticsService {

   void generateStudentApplicationStatisticsReportSale(StudentApplicationStatisticsDto studentApplicationStatisticsDto, Map<String, String> headerMap, StaffInfo staffInfo);

    /**
     * 获取业绩统计
     * <AUTHOR>
     * @DateTime 2022/11/22 18:13
     */
    List<StudentApplicationStatisticsVo> getStatisticsDtos(StatisticsDto statisticsDto, Boolean isStudentOfferItemFinancialHiding, Boolean isStudentAdmin);


    /**
     * 构造返回结果
     *
     * @param dateStartOne
     * @param dateEndOne
     * @param dateStartTwo
     * @param dateEndTwo
     * @param fkAreaCountryIds
     * @param oneYearMonthStatisticsDtoMap
     * @param twoYearMonthStatisticsDtoMap
     * @return
     */
    List<StatisticsVo> constructResout(Date dateStartOne,
                                       Date dateEndOne,
                                       Date dateStartTwo,
                                       Date dateEndTwo,
                                       Date cumulativeDateStartOne,
                                       Date cumulativeDateEndOne,
                                       Date cumulativeDateStartTwo,
                                       Date cumulativeDateEndTwo,
                                       List<Long> fkAreaCountryIds,
                                       Boolean hasCumulative,
                                       Integer queryType,
                                       Map<Long, StudentApplicationStatisticsVo> oneYearMonthStatisticsDtoMap,
                                       Map<Long, StudentApplicationStatisticsVo> twoYearMonthStatisticsDtoMap,
                                       Map<Long, StudentApplicationStatisticsVo> oneYearCumulativeStatisticsDtoMap,
                                       Map<Long, StudentApplicationStatisticsVo> twoYearCumulativeStatisticsDtoMap);

    /**
     * 构造返回结果(自定义)
     *
     * @param dateStartOne
     * @param dateEndOne
     * @param dateStartTwo
     * @param dateEndTwo
     * @param data1
     * @param data2
     * @return
     */
    StatisticsVo constructSubtotalResout(String title,
                                         Date dateStartOne,
                                         Date dateEndOne,
                                         Date dateStartTwo,
                                         Date dateEndTwo,
                                         Date cumulativeDateStartOne,
                                         Date cumulativeDateEndOne,
                                         Date cumulativeDateStartTwo,
                                         Date cumulativeDateEndTwo,
                                         Boolean hasCumulative,
                                         Integer queryType,
                                         List<StudentApplicationStatisticsVo> data1,
                                         List<StudentApplicationStatisticsVo> data2,
                                         List<StudentApplicationStatisticsVo> data3,
                                         List<StudentApplicationStatisticsVo> data4);
}

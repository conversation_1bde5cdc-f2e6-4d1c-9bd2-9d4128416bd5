<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.ReceivablePlanContractFormulaMapper">
  <resultMap id="BaseResultMap" type="com.get.salecenter.entity.ReceivablePlanContractFormula">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_receivable_plan_id" jdbcType="BIGINT" property="fkReceivablePlanId" />
    <result column="fk_contract_formula_id" jdbcType="BIGINT" property="fkContractFormulaId" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
  <insert id="insert" parameterType="com.get.salecenter.entity.ReceivablePlanContractFormula">
    insert into r_receivable_plan_contract_formula (id, fk_receivable_plan_id, fk_contract_formula_id, 
      gmt_create, gmt_create_user, gmt_modified, 
      gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkReceivablePlanId,jdbcType=BIGINT}, #{fkContractFormulaId,jdbcType=BIGINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, 
      #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.salecenter.entity.ReceivablePlanContractFormula">
    insert into r_receivable_plan_contract_formula
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkReceivablePlanId != null">
        fk_receivable_plan_id,
      </if>
      <if test="fkContractFormulaId != null">
        fk_contract_formula_id,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkReceivablePlanId != null">
        #{fkReceivablePlanId,jdbcType=BIGINT},
      </if>
      <if test="fkContractFormulaId != null">
        #{fkContractFormulaId,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="isExistByFormulaId" resultType="java.lang.Boolean">
    SELECT IFNULL(max(id),0) id from r_receivable_plan_contract_formula where fk_contract_formula_id=#{id}
  </select>
  <select id="getReceivableTotalAmount" resultType="java.math.BigDecimal">
      SELECT
        SUM(mrp.receivable_amount)
      FROM
        m_receivable_plan AS mrp
          INNER JOIN r_receivable_plan_contract_formula AS rpcf ON rpcf.fk_receivable_plan_id = mrp.id
          INNER JOIN ais_institution_center.m_contract_formula AS mcf ON mcf.id = rpcf.fk_contract_formula_id
          INNER JOIN m_student_offer_item AS msoi ON msoi.id = mrp.fk_type_target_id
          AND mrp.fk_type_key = 'm_student_offer_item'
        WHERE msoi.id = #{offerItemId}  AND mcf.id = #{fkContractFormulaId}
    </select>

  <select id="getPayableTotalAmount" resultType="java.math.BigDecimal">
    SELECT
      SUM(mpp.payable_amount)
    FROM
      m_payable_plan AS mpp
        INNER JOIN r_payable_plan_contract_formula AS rppcf ON rppcf.fk_payable_plan_id = mpp.id
        INNER JOIN ais_institution_center.m_contract_formula AS mcf ON mcf.id = rppcf.fk_contract_formula_id
        INNER JOIN m_student_offer_item AS msoi ON msoi.id = mpp.fk_type_target_id
        AND mpp.fk_type_key = 'm_student_offer_item'
    WHERE msoi.id = #{offerItemId} AND mcf.id = #{fkContractFormulaId}
  </select>
</mapper>
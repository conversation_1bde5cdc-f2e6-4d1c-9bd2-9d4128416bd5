package com.get.salecenter.controller;


import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.vo.AplOrderVo;
import com.get.salecenter.service.IOldIssueService;
import com.get.salecenter.dto.AplOldIssueOrderDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2022/3/15 20:14
 */
@Api(tags = "旧系统Issue")
@RestController
@RequestMapping("sale/oldIssue")
public class OldIssueController {


    @Resource
    private IOldIssueService iOldIssueService;

    //TODO 恢复ISSUE相关功能 lucky  2024/12/24
    @VerifyPermission(IsVerify = false)
    @PostMapping("getRpaTableList")
    @ApiOperation("ai反馈表")
    public ListResponseBo<AplOrderVo> getRpaTableList(@RequestBody SearchBean<AplOldIssueOrderDto> page) {
        List<AplOrderVo> rpaTableList = iOldIssueService.getRpaTableList(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo(rpaTableList, p);
    }

    //TODO 注释ISSUE相关功能 lucky  2024/12/23
//
//    @PostMapping("exportRobotTable")
//    @VerifyPermission(IsVerify = false)
//    @ApiOperation("导出ai反馈表")
//    public void exportRobotTable(HttpServletResponse response, @RequestBody SearchBean<AplOldIssueOrderDto> page) {
//        iOldIssueService.exportRobotTable(response, page);
//
//    }


}

package com.get.salecenter.service.impl;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.tool.utils.RequestContextUtil;
import com.get.file.utils.FileUtils;
import com.get.filecenter.dto.FileDto;
import com.get.filecenter.dto.SaleFileDto;
import com.get.filecenter.feign.IFileCenterClient;
import com.get.filecenter.vo.FileVo;
import com.get.salecenter.dao.convention.GoproNucleicAcidMapper;
import com.get.salecenter.dao.convention.ConventionMediaAndAttachedMapper;
import com.get.salecenter.vo.GoproNucleicAcidExportAgentVo;
import com.get.salecenter.vo.GoproNucleicAcidExportInstitutionVo;
import com.get.salecenter.vo.MediaAndAttachedVo;
import com.get.salecenter.entity.GoproNucleicAcid;
import com.get.salecenter.entity.SaleMediaAndAttached;
import com.get.salecenter.service.AsyncExportService;
import com.get.salecenter.service.IGoproNucleicAcidService;
import com.get.salecenter.service.IMediaAndAttachedService;
import com.get.salecenter.dto.GoproNucleicAcidUpdateDto;
import com.get.salecenter.dto.MediaAndAttachedDto;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JsonConfig;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * @author: Hardy
 * @create: 2022/5/25 12:29
 * @verison: 1.0
 * @description:
 */
@Slf4j
@Service
public class GoproNucleicAcidServiceImpl  extends ServiceImpl<GoproNucleicAcidMapper, GoproNucleicAcid> implements IGoproNucleicAcidService {

    @Resource
    private GoproNucleicAcidMapper goproNucleicAcidMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IMediaAndAttachedService attachedService;
    @Resource
    private IFileCenterClient fileCenterClient;
    @Resource
    private ConventionMediaAndAttachedMapper conventionMediaAndAttachedMapper;
    @Resource
    private AsyncExportService asyncExportService;

    @DSTransactional
    @Override
    public Long addGoproNucleicAcid(GoproNucleicAcidUpdateDto goproNucleicAcidUpdateDto) {
        if (GeneralTool.isEmpty(goproNucleicAcidUpdateDto)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        if (GeneralTool.isEmpty(goproNucleicAcidUpdateDto.getFkCompanyId())){
            goproNucleicAcidUpdateDto.setFkCompanyId(2L);
        }
        GoproNucleicAcid goproNucleicAcid = new GoproNucleicAcid();
        BeanCopyUtils.copyProperties(goproNucleicAcidUpdateDto,goproNucleicAcid);

        Integer maxOrderId = null;
        if (GeneralTool.isNotEmpty(goproNucleicAcidUpdateDto.getBdName())){
            maxOrderId = goproNucleicAcidMapper.getMaxOrderId("agent");
        }else {
            maxOrderId = goproNucleicAcidMapper.getMaxOrderId("institution");
        }
        goproNucleicAcid.setOrderId(maxOrderId);
        utilService.setCreateInfo(goproNucleicAcid);
        goproNucleicAcid.setGmtCreateUser("[form]");
        goproNucleicAcidMapper.insertSelective(goproNucleicAcid);


        if (GeneralTool.isNotEmpty(goproNucleicAcidUpdateDto.getMediaAttachedVos())){
            List<MediaAndAttachedVo> mediaAndAttachedVos = new ArrayList<>();
            for (MediaAndAttachedDto mediaAndAttachedDto : goproNucleicAcidUpdateDto.getMediaAttachedVos()) {
                //设置插入的表
                mediaAndAttachedDto.setFkTableName(TableEnum.CONVENTION_GOPRO_NUCLEIC_ACID.key);
                mediaAndAttachedDto.setFkTableId(goproNucleicAcid.getId());
                mediaAndAttachedDto.setTypeKey(FileTypeEnum.CONVENTION_GOPRO_NUCLEIC_ACID_FILE.key);
                mediaAndAttachedDto.setGmtCreateUser("[form]");
                mediaAndAttachedVos.add(addMediaAndAttached(mediaAndAttachedDto));
            }
        }
        return goproNucleicAcid.getId();
    }

    @Override
    public List<MediaAndAttachedVo> addItemMedia(ValidList<MediaAndAttachedDto> mediaAttachedVos) {
        if (GeneralTool.isEmpty(mediaAttachedVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        List<MediaAndAttachedVo> mediaAndAttachedVos = new ArrayList<>();
        for (MediaAndAttachedDto mediaAndAttachedDto : mediaAttachedVos) {
            //设置插入的表
            mediaAndAttachedDto.setFkTableName(TableEnum.CONVENTION_GOPRO_NUCLEIC_ACID.key);
            mediaAndAttachedVos.add(addMediaAndAttached(mediaAndAttachedDto));
        }
        return mediaAndAttachedVos;
    }
    private MediaAndAttachedVo addMediaAndAttached(MediaAndAttachedDto mediaAttachedVo) {
        if (GeneralTool.isEmpty(mediaAttachedVo.getFkTableName())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("表名不能为空"));
        }
        SaleMediaAndAttached andAttached = BeanCopyUtils.objClone(mediaAttachedVo, SaleMediaAndAttached::new);
        Integer nextIndexKey = conventionMediaAndAttachedMapper.getNextIndexKey(mediaAttachedVo.getFkTableId(), mediaAttachedVo.getFkTableName());
        nextIndexKey = GeneralTool.isNotEmpty(nextIndexKey) ? nextIndexKey : 0;
        //实例化对象
        andAttached.setIndexKey(nextIndexKey);
        utilService.updateUserInfoToEntity(andAttached);
        conventionMediaAndAttachedMapper.insert(andAttached);
        MediaAndAttachedVo mediaAndAttachedVo = BeanCopyUtils.objClone(andAttached, MediaAndAttachedVo::new);
        mediaAndAttachedVo.setFilePath(mediaAttachedVo.getFilePath());
        mediaAndAttachedVo.setFileNameOrc(mediaAttachedVo.getFileNameOrc());
        mediaAndAttachedVo.setTypeValue(FileTypeEnum.getValue(mediaAttachedVo.getTypeKey()));
        mediaAndAttachedVo.setId(andAttached.getId());
        mediaAndAttachedVo.setFileKey(mediaAttachedVo.getFileKey());
        return mediaAndAttachedVo;
    }

    @Override
    public List<Map<String, Object>> getRegionSelect() {
        return ProjectExtraEnum.enumsTranslation2Arrays(ProjectExtraEnum.GOPRO_REGION);
    }

    @Override
    public List<Map<String, Object>> getStaffSelect(Integer id) {
        List<Map<String, Object>> maps = new ArrayList<>();

        switch(id){
            case 0:
                Map<String, Object> map0_1 = new HashMap<>(2);map0_1.put("key",1);map0_1.put("value","Alina邹娜");maps.add(map0_1);
                break;
            case 1:
                Map<String, Object> map1_1 = new HashMap<>(2);map1_1.put("key",1);map1_1.put("value","Nina 黄春娜");maps.add(map1_1);
                Map<String, Object> map1_2 = new HashMap<>(2);map1_2.put("key",2);map1_2.put("value","Wendy 邬丹");maps.add(map1_2);
                Map<String, Object> map1_3 = new HashMap<>(2);map1_3.put("key",3);map1_3.put("value","Sylvia 黄思圆");maps.add(map1_3);
                Map<String, Object> map1_4 = new HashMap<>(2);map1_4.put("key",4);map1_4.put("value","Eva 谢珍");maps.add(map1_4);
                Map<String, Object> map1_5 = new HashMap<>(2);map1_5.put("key",5);map1_5.put("value","Maggie 廖丽君");maps.add(map1_5);
                Map<String, Object> map1_6 = new HashMap<>(2);map1_6.put("key",6);map1_6.put("value","Janice 魏远珍");maps.add(map1_6);
                break;
            case 2:
                Map<String, Object> map2_1 = new HashMap<>(2);map2_1.put("key",1);map2_1.put("value","Joanna 高敏");maps.add(map2_1);
                Map<String, Object> map2_2 = new HashMap<>(2);map2_2.put("key",2);map2_2.put("value","Eric 刘祥俊");maps.add(map2_2);
                Map<String, Object> map2_3 = new HashMap<>(2);map2_3.put("key",3);map2_3.put("value","Lety 汪倩蓉");maps.add(map2_3);
                Map<String, Object> map2_4 = new HashMap<>(2);map2_4.put("key",4);map2_4.put("value","Alfred 古川");maps.add(map2_4);
                break;
            case 3:
                Map<String, Object> map3_1 = new HashMap<>(2);map3_1.put("key",1);map3_1.put("value","Daniel 杜爽");maps.add(map3_1);
                Map<String, Object> map3_2 = new HashMap<>(2);map3_2.put("key",2);map3_2.put("value","David 陈乐");maps.add(map3_2);
                Map<String, Object> map3_3 = new HashMap<>(2);map3_3.put("key",3);map3_3.put("value","Brenda 董露");maps.add(map3_3);
                break;
            case 4:
                Map<String, Object> map4_1 = new HashMap<>(2);map4_1.put("key",1);map4_1.put("value","Alice 冯霞");maps.add(map4_1);
                Map<String, Object> map4_2 = new HashMap<>(2);map4_2.put("key",2);map4_2.put("value","Claire 陈楚芸");maps.add(map4_2);
                break;
            case 5:
                Map<String, Object> map5_1 = new HashMap<>(2);map5_1.put("key",1);map5_1.put("value","Rain 王雨");maps.add(map5_1);
                Map<String, Object> map5_2 = new HashMap<>(2);map5_2.put("key",2);map5_2.put("value","Mavis 张琳林");maps.add(map5_2);
                Map<String, Object> map5_3 = new HashMap<>(2);map5_3.put("key",3);map5_3.put("value","Scarlett 胡蔚尊");maps.add(map5_3);
                break;
            case 6:
                Map<String, Object> map6_1 = new HashMap<>(2);map6_1.put("key",1);map6_1.put("value","Bob 高波");maps.add(map6_1);
                Map<String, Object> map6_2 = new HashMap<>(2);map6_2.put("key",2);map6_2.put("value","Shana 朱莎娜");maps.add(map6_2);
                break;
            case 7:
                Map<String, Object> map7_1 = new HashMap<>(2);map7_1.put("key",1);map7_1.put("value","James 段昭宁");maps.add(map7_1);
                Map<String, Object> map7_2 = new HashMap<>(2);map7_2.put("key",2);map7_2.put("value","Donna 焦雁");maps.add(map7_2);
                Map<String, Object> map7_3 = new HashMap<>(2);map7_3.put("key",3);map7_3.put("value","Elva 孟博");maps.add(map7_3);
                Map<String, Object> map7_4 = new HashMap<>(2);map7_4.put("key",4);map7_4.put("value","Sara 李晓光");maps.add(map7_4);
                Map<String, Object> map7_5 = new HashMap<>(2);map7_5.put("key",5);map7_5.put("value","Jenny 宋俐佳");maps.add(map7_5);
                break;
            default:
                break;
        }
        return maps;
    }

    @Override
    public void exportInfo(HttpServletResponse response) throws Exception {

//        //下载压缩包
//        response.setContentType("application/zip");
//        response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode("附件.zip", "UTF-8"));
        List<GoproNucleicAcid> goproNucleicAcids = goproNucleicAcidMapper.selectList(Wrappers.<GoproNucleicAcid>lambdaQuery()
                .isNotNull(GoproNucleicAcid::getOrderId));
        List<SaleMediaAndAttached> saleMediaAndAttacheds = conventionMediaAndAttachedMapper.selectList(Wrappers.<SaleMediaAndAttached>lambdaQuery()
                .eq(SaleMediaAndAttached::getFkTableName, TableEnum.CONVENTION_GOPRO_NUCLEIC_ACID.key)
                .eq(SaleMediaAndAttached::getTypeKey, FileTypeEnum.CONVENTION_GOPRO_NUCLEIC_ACID_FILE.key)
        );
        List<String> guids = saleMediaAndAttacheds.stream().map(SaleMediaAndAttached::getFkFileGuid).collect(Collectors.toList());
        Map<String, String> fileKey = fileCenterClient.getSaleFileKeyByGuids(guids).getData();
        Map<String, String> fileNameOrc = fileCenterClient.getSaleFileNameOrcByGuids(guids).getData();

        Map<Long, List<SaleMediaAndAttached>> listMap = saleMediaAndAttacheds.stream().collect(Collectors.groupingBy(SaleMediaAndAttached::getFkTableId));

        if (GeneralTool.isNotEmpty(goproNucleicAcids)){
            //zip输出流
            response.setContentType("application/zip");
            response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode("附件.zip", "UTF-8"));
            ZipOutputStream zipOutputStream = new ZipOutputStream(response.getOutputStream());

            //创建一个文件夹
            zipOutputStream.putNextEntry(new ZipEntry( "校代版"+" /"));
            zipOutputStream.closeEntry();
            zipOutputStream.putNextEntry(new ZipEntry( "代理版"+" /"));
            zipOutputStream.closeEntry();
            String path = null;


            List<GoproNucleicAcidExportAgentVo> goproNucleicAcidExportAgentVos = new ArrayList<>();
            List<GoproNucleicAcidExportInstitutionVo> goproNucleicAcidExportInstitutionVos = new ArrayList<>();

            for (GoproNucleicAcid goproNucleicAcid : goproNucleicAcids) {
                List<SaleFileDto> fileList = new ArrayList();
                List<SaleMediaAndAttached> mediaAndAttacheds = listMap.get(goproNucleicAcid.getId());

                if (GeneralTool.isNotEmpty(goproNucleicAcid.getBdName())){
                    path = "代理版";
                }else {
                    path = "校代版";
                }

                if (GeneralTool.isNotEmpty(mediaAndAttacheds)){
                    for (SaleMediaAndAttached itemMedia : mediaAndAttacheds) {
                        if (GeneralTool.isNotEmpty(fileKey.get(itemMedia.getFkFileGuid()))){
                            FileVo fileVo = new FileVo();
                            fileVo.setFileNameOrc(fileNameOrc.get(itemMedia.getFkFileGuid()));
                            fileVo.setFileKey(fileKey.get(itemMedia.getFkFileGuid()));
                            Result<SaleFileDto> result = fileCenterClient.getDownloadFile(fileVo);
                            SaleFileDto data = result.getData();
                            fileList.add(data);
                        }
                    }
                    // 创建 ZipEntry 对象
                    int i = 0;
                    for (SaleFileDto saleFileDto:fileList){
                        String fileName = saleFileDto.getFileName();
                        String[] split = fileName.split("\\.");
                        if (!"png".equals(split[1])&&!"jpg".equals(split[1])&&!"pdf".equals(split[1])&&!"PDF".equals(split[1])){
                            fileName = split[0]+"_"+goproNucleicAcid.getId()+"_"+i+"."+"png";
                        }else {
                            fileName = split[0]+"_"+goproNucleicAcid.getId()+"_"+i+"."+split[1];
                        }

                        if (i==0){
                            zipOutputStream.putNextEntry(new ZipEntry( path+" /"+goproNucleicAcid.getOrderId()+" "+goproNucleicAcid.getName()+" /"));
                            zipOutputStream.closeEntry();
                        }
                        ZipEntry zipEntry =  new ZipEntry(path+" /"+goproNucleicAcid.getOrderId()+" "+goproNucleicAcid.getName()+" /"+fileName);
                        zipOutputStream.putNextEntry(zipEntry);
                        byte[] outBytes = saleFileDto.getBytes();
                        zipOutputStream.write(outBytes);
                        i++;
                    }


                    if (GeneralTool.isNotEmpty(goproNucleicAcid.getBdName())){
                        GoproNucleicAcidExportAgentVo goproNucleicAcidExportAgentVo =  new GoproNucleicAcidExportAgentVo();
                        BeanCopyUtils.copyProperties(goproNucleicAcid, goproNucleicAcidExportAgentVo);
                        goproNucleicAcidExportAgentVos.add(goproNucleicAcidExportAgentVo);
                    }else {
                        GoproNucleicAcidExportInstitutionVo goproNucleicAcidExportInstitutionVo =  new GoproNucleicAcidExportInstitutionVo();
                        BeanCopyUtils.copyProperties(goproNucleicAcid, goproNucleicAcidExportInstitutionVo);
                        goproNucleicAcidExportInstitutionVos.add(goproNucleicAcidExportInstitutionVo);
                    }

                }

            }


            //将导出的excel加入压缩包
            ZipEntry z =  new ZipEntry("代理版"+" /"+"代理版核酸上传资料信息汇总.xlsx");
            zipOutputStream.putNextEntry(z);
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            FileUtils.getExcelNotWrapTextStream(os, goproNucleicAcidExportAgentVos,"info", GoproNucleicAcidExportAgentVo.class);
            os.writeTo(zipOutputStream);

            ZipEntry z1 =  new ZipEntry("校代版"+" /"+"校代版版核酸上传资料信息汇总.xlsx");
            zipOutputStream.putNextEntry(z1);
            ByteArrayOutputStream os2 = new ByteArrayOutputStream();
            FileUtils.getExcelNotWrapTextStream(os2, goproNucleicAcidExportInstitutionVos,"info", GoproNucleicAcidExportInstitutionVo.class);
            os2.writeTo(zipOutputStream);


            try {
                if (os != null) {
                    os.flush();
                    os.close();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

            try {
                if (os2 != null) {
                    os2.flush();
                    os2.close();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

            try {
                if (zipOutputStream != null) {
                    zipOutputStream.flush();
                    zipOutputStream.closeEntry();
                    zipOutputStream.close();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

        }
    }

    @Override
    public void exportAllInfo(HttpServletResponse response, GoproNucleicAcidUpdateDto goproNucleicAcidUpdateDto) throws Exception {

//        //下载压缩包
//        response.setContentType("application/zip");
//        response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode("附件.zip", "UTF-8"));
        List<GoproNucleicAcid> goproNucleicAcids = goproNucleicAcidMapper.selectList(Wrappers.<GoproNucleicAcid>lambdaQuery()
                .eq(GoproNucleicAcid::getRetreatTypeName, goproNucleicAcidUpdateDto.getRetreatTypeName())
                .isNotNull(GoproNucleicAcid::getOrderId));
        List<SaleMediaAndAttached> saleMediaAndAttacheds = conventionMediaAndAttachedMapper.selectList(Wrappers.<SaleMediaAndAttached>lambdaQuery()
                .eq(SaleMediaAndAttached::getFkTableName, TableEnum.CONVENTION_GOPRO_NUCLEIC_ACID.key)
                .eq(SaleMediaAndAttached::getTypeKey, FileTypeEnum.CONVENTION_GOPRO_NUCLEIC_ACID_FILE.key)
        );
        List<String> guids = saleMediaAndAttacheds.stream().map(SaleMediaAndAttached::getFkFileGuid).collect(Collectors.toList());
        Map<String, String> fileKey = fileCenterClient.getSaleFileKeyByGuids(guids).getData();
        Map<String, String> fileNameOrc = fileCenterClient.getSaleFileNameOrcByGuids(guids).getData();

        Map<Long, List<SaleMediaAndAttached>> listMap = saleMediaAndAttacheds.stream().collect(Collectors.groupingBy(SaleMediaAndAttached::getFkTableId));

        if (GeneralTool.isNotEmpty(goproNucleicAcids)){
            //zip输出流
            response.setContentType("application/zip");
            response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode("附件.zip", "UTF-8"));
            ZipOutputStream zipOutputStream = new ZipOutputStream(response.getOutputStream());

            //创建一个文件夹
//            zipOutputStream.putNextEntry(new ZipEntry( "校代版"+" /"));
//            zipOutputStream.closeEntry();
            zipOutputStream.putNextEntry(new ZipEntry( "活动资料"+" /"));
            zipOutputStream.closeEntry();
            String path = null;


            List<GoproNucleicAcidExportAgentVo> goproNucleicAcidExportAgentVos = new ArrayList<>();
//            List<GoproNucleicAcidExportInstitutionVo>  goproNucleicAcidExportInstitutionDtos = new ArrayList<>();

            for (GoproNucleicAcid goproNucleicAcid : goproNucleicAcids) {
                List<SaleFileDto> fileList = new ArrayList();
                List<SaleMediaAndAttached> mediaAndAttacheds = listMap.get(goproNucleicAcid.getId());
                path = "活动资料";
//                if (GeneralTool.isNotEmpty(goproNucleicAcid.getBdName())){
//                    path = "代理版";
//                }else {
//                    path = "校代版";
//                }

                if (GeneralTool.isNotEmpty(mediaAndAttacheds)){
                    for (SaleMediaAndAttached itemMedia : mediaAndAttacheds) {
                        if (GeneralTool.isNotEmpty(fileKey.get(itemMedia.getFkFileGuid()))){
                            FileVo fileVo = new FileVo();
                            fileVo.setFileNameOrc(fileNameOrc.get(itemMedia.getFkFileGuid()));
                            fileVo.setFileKey(fileKey.get(itemMedia.getFkFileGuid()));
                            Result<SaleFileDto> result = fileCenterClient.getDownloadFile(fileVo);
                            SaleFileDto data = result.getData();
                            fileList.add(data);
                        }
                    }
                    // 创建 ZipEntry 对象
                    int i = 0;
                    for (SaleFileDto saleFileDto:fileList){
                        String fileName = saleFileDto.getFileName();
                        String[] split = fileName.split("\\.");
                        if (!"png".equals(split[1])&&!"jpg".equals(split[1])&&!"pdf".equals(split[1])&&!"PDF".equals(split[1])){
                            fileName = split[0]+"_"+goproNucleicAcid.getId()+"_"+i+"."+"png";
                        }else {
                            fileName = split[0]+"_"+goproNucleicAcid.getId()+"_"+i+"."+split[1];
                        }

                        if (i==0){
                            zipOutputStream.putNextEntry(new ZipEntry( path+" /"+goproNucleicAcid.getOrderId()+" "+goproNucleicAcid.getName()+" /"));
                            zipOutputStream.closeEntry();
                        }
                        ZipEntry zipEntry =  new ZipEntry(path+" /"+goproNucleicAcid.getOrderId()+" "+goproNucleicAcid.getName()+" /"+fileName);
                        zipOutputStream.putNextEntry(zipEntry);
                        byte[] outBytes = saleFileDto.getBytes();
                        zipOutputStream.write(outBytes);
                        i++;
                    }

                    GoproNucleicAcidExportAgentVo goproNucleicAcidExportAgentVo =  new GoproNucleicAcidExportAgentVo();
                    BeanCopyUtils.copyProperties(goproNucleicAcid, goproNucleicAcidExportAgentVo);
                    if (GeneralTool.isNotEmpty(goproNucleicAcid.getGender())){
                        if (goproNucleicAcid.getGender().equals(0)){
                            goproNucleicAcidExportAgentVo.setGenderName("女");
                        }else {
                            goproNucleicAcidExportAgentVo.setGenderName("男");
                        }
                    }
                    goproNucleicAcidExportAgentVos.add(goproNucleicAcidExportAgentVo);


                }

            }


            //将导出的excel加入压缩包
            ZipEntry z =  new ZipEntry("活动资料"+" /"+"核酸上传资料信息汇总.xlsx");
            zipOutputStream.putNextEntry(z);
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            FileUtils.getExcelNotWrapTextStream(os, goproNucleicAcidExportAgentVos,"info", GoproNucleicAcidExportAgentVo.class);
            os.writeTo(zipOutputStream);

//            ZipEntry z1 =  new ZipEntry("校代版"+" /"+"校代版版核酸上传资料信息汇总.xlsx");
//            zipOutputStream.putNextEntry(z1);
//            ByteArrayOutputStream os2 = new ByteArrayOutputStream();
//            FileUtils.getExcelNotWrapTextStream(os2,goproNucleicAcidExportInstitutionDtos,"info",GoproNucleicAcidExportInstitutionVo.class);
//            os2.writeTo(zipOutputStream);


            try {
                if (os != null) {
                    os.flush();
                    os.close();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

            try {
                if (zipOutputStream != null) {
                    zipOutputStream.flush();
                    zipOutputStream.closeEntry();
                    zipOutputStream.close();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

        }
    }

    @Override
    public void exportAllExcelInfo(HttpServletResponse response, GoproNucleicAcidUpdateDto goproNucleicAcidUpdateDto) throws Exception {

        List<GoproNucleicAcid> goproNucleicAcids = goproNucleicAcidMapper.selectList(Wrappers.<GoproNucleicAcid>lambdaQuery()
                .eq(GoproNucleicAcid::getRetreatTypeName, goproNucleicAcidUpdateDto.getRetreatTypeName())
                .isNotNull(GoproNucleicAcid::getOrderId));

        List<GoproNucleicAcidExportAgentVo> goproNucleicAcidExportAgentVos = new ArrayList<>();
        for (GoproNucleicAcid goproNucleicAcid : goproNucleicAcids) {
            GoproNucleicAcidExportAgentVo goproNucleicAcidExportAgentVo =  new GoproNucleicAcidExportAgentVo();
            BeanCopyUtils.copyProperties(goproNucleicAcid, goproNucleicAcidExportAgentVo);
            if (GeneralTool.isNotEmpty(goproNucleicAcid.getGender())){
                if (goproNucleicAcid.getGender().equals(0)){
                    goproNucleicAcidExportAgentVo.setGenderName("女");
                }else {
                    goproNucleicAcidExportAgentVo.setGenderName("男");
                }
            }
            goproNucleicAcidExportAgentVos.add(goproNucleicAcidExportAgentVo);
        }
        FileUtils.exportExcelNotWrapText(response, goproNucleicAcidExportAgentVos, "GoproNucleicAcid", GoproNucleicAcidExportAgentVo.class);
    }


    @Override
    public void exportAllImageInfo(HttpServletResponse response, GoproNucleicAcidUpdateDto goproNucleicAcidUpdateDto) throws Exception {

        Map<String, String> headerMap = RequestContextUtil.getHeaderMap();
        com.get.core.secure.UserInfo user = GetAuthInfo.getUser();
        String locale = SecureUtil.getLocale();
        asyncExportService.exportAllImageInfo(goproNucleicAcidUpdateDto,headerMap,user,locale);
    }


    public List<MediaAndAttachedVo> getItemMedia(MediaAndAttachedDto attachedVo) {
        if (GeneralTool.isEmpty(attachedVo.getFkTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        attachedVo.setFkTableName(TableEnum.CONVENTION_GOPRO_NUCLEIC_ACID.key);
        return getMediaAndAttachedDto(attachedVo);
    }



    public List<MediaAndAttachedVo> getMediaAndAttachedDto(MediaAndAttachedDto attachedVo) {
        if (GeneralTool.isEmpty(attachedVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        LambdaQueryWrapper<SaleMediaAndAttached> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(attachedVo.getTypeKey())) {
            lambdaQueryWrapper.eq(SaleMediaAndAttached::getTypeKey, attachedVo.getTypeKey());
        }
        lambdaQueryWrapper.eq(SaleMediaAndAttached::getFkTableName,attachedVo.getFkTableName());
        lambdaQueryWrapper.eq(SaleMediaAndAttached::getFkTableId, attachedVo.getFkTableId());
        lambdaQueryWrapper.orderByDesc(SaleMediaAndAttached::getIndexKey);
        List<SaleMediaAndAttached> mediaAndAttacheds = conventionMediaAndAttachedMapper.selectList(lambdaQueryWrapper);
        return getFileMedia(mediaAndAttacheds);
    }


    private List<MediaAndAttachedVo> getFileMedia(List<SaleMediaAndAttached> mediaAndAttachedList) {
        if (GeneralTool.isEmpty(mediaAndAttachedList)) {
            return null;
        }
        //获取guid集合
        List<String> guidList = mediaAndAttachedList.stream().map(SaleMediaAndAttached::getFkFileGuid).collect(Collectors.toList());
        //转换成DTO
        List<MediaAndAttachedVo> mediaAndAttachedVos = mediaAndAttachedList.stream()
                .map(mediaAndAttached -> BeanCopyUtils.objClone(mediaAndAttached, MediaAndAttachedVo::new)).collect(Collectors.toList());
        //根据GUID服务调用查询
        List<FileDto> fileDtos = Lists.newArrayList();
        Map<String, List<String>> guidListWithTypeMap = new HashMap<>();
        guidListWithTypeMap.put(LoggerModulesConsts.CONVENTIONCENTER, guidList);
        Result<List<FileDto>> fileDtoResult = fileCenterClient.findFileByGuid(guidListWithTypeMap);
        if (fileDtoResult.isSuccess() && GeneralTool.isNotEmpty(fileDtoResult.getData())) {
            JSONArray jsonArray = JSONArray.fromObject(fileDtoResult.getData());
            fileDtos.addAll(JSONArray.toList(jsonArray, new FileDto(), new JsonConfig()));
        }
        //返回结果不为空时
        List<MediaAndAttachedVo> collect = null;
        mediaAndAttachedVos.removeIf(Objects::isNull);
        fileDtos.removeIf(Objects::isNull);
        if (GeneralTool.isNotEmpty(fileDtos) && GeneralTool.isNotEmpty(mediaAndAttachedVos)) {
            //遍历查询GUID是否一致
            collect = mediaAndAttachedVos.stream().map(mediaAndAttachedDto -> fileDtos
                    .stream()
                    .filter(fileDto -> fileDto.getFileGuid().equals(mediaAndAttachedDto.getFkFileGuid()))
                    .findFirst()
                    .map(fileDto -> {
                        mediaAndAttachedDto.setFilePath(fileDto.getFilePath());
                        mediaAndAttachedDto.setFileNameOrc(fileDto.getFileNameOrc());
                        mediaAndAttachedDto.setTypeValue(FileTypeEnum.getValue(mediaAndAttachedDto.getTypeKey()));
                        mediaAndAttachedDto.setFileKey(fileDto.getFileKey());
                        /*mediaAndAttachedDto.setFkTableName(null);*/
                        return mediaAndAttachedDto;
                    }).orElse(null)
            ).collect(Collectors.toList());
        }
        return collect;
    }

}

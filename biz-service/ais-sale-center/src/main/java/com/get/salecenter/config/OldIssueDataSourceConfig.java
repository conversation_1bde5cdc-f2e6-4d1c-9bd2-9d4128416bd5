//package com.get.salecenter.config;
//
//import com.alibaba.druid.pool.DruidDataSource;
//import org.apache.ibatis.session.SqlSessionFactory;
//import org.mybatis.spring.SqlSessionFactoryBean;
//import org.mybatis.spring.SqlSessionTemplate;
//import org.mybatis.spring.annotation.MapperScan;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
//import org.springframework.jdbc.datasource.DataSourceTransactionManager;
//
//import javax.sql.DataSource;
//
///**
// * @version 1.0
// * @Author: LEO
// * @Date: 2022/3/15 19:49
// */
//@Configuration
//@MapperScan(basePackages = "com.get.salecenter.dao.oldissue", sqlSessionTemplateRef = "oldissueSqlSessionTemplate")
//public class OldIssueDataSourceConfig {
//    @Bean(name = "oldIssueDataSource")
//    @ConfigurationProperties(prefix = "oldissuedb.datasource")
//    public DataSource testDataSource() {
//        return new DruidDataSource();
//    }
//
//    @Bean(name = "oldappIssueSqlSessionFactory")
//    public SqlSessionFactory testSqlSessionFactory(@Qualifier("oldIssueDataSource") DataSource dataSource) throws Exception {
//        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
//        bean.setDataSource(dataSource);
//        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath:mapper/OldIssue/*.xml"));
//        //mybatis 数据库字段与实体类属性驼峰映射配置
//        bean.getObject().getConfiguration().setMapUnderscoreToCamelCase(true);
//        return bean.getObject();
//    }
//
//    @Bean(name = "oldIssueTransactionManager")
//    public DataSourceTransactionManager testTransactionManager(@Qualifier("oldIssueDataSource") DataSource dataSource) {
//        return new DataSourceTransactionManager(dataSource);
//    }
//
//    @Bean(name = "oldissueSqlSessionTemplate")
//    public SqlSessionTemplate testSqlSessionTemplate(@Qualifier("oldappIssueSqlSessionFactory") SqlSessionFactory sqlSessionFactory) throws Exception {
//        return new SqlSessionTemplate(sqlSessionFactory);
//    }
//}

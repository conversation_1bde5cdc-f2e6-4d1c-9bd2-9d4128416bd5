package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.cache.utils.CacheUtil;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.service.impl.GetServiceImpl;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.file.utils.FileUtils;
import com.get.financecenter.feign.IFinanceCenterClient;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.dao.sale.*;
import com.get.salecenter.dto.*;
import com.get.salecenter.dto.query.AccommodationSummaryQueryDto;
import com.get.salecenter.entity.*;
import com.get.salecenter.service.*;
import com.get.salecenter.utils.MyStringUtils;
import com.get.salecenter.utils.VerifyDataPermissionsUtils;
import com.get.salecenter.utils.sale.GetAgentLabelDataUtils;
import com.get.salecenter.vo.*;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.get.common.cache.CacheNames.STAFF_FOLLOWER_IDS_CACHE;

/**
 * <AUTHOR>
 * @DATE: 2022/1/10
 * @TIME: 15:27
 * @Description:留学住宿
 **/
@Service
public class StudentAccommodationServiceImpl extends GetServiceImpl<StudentAccommodationMapper,StudentAccommodation> implements IStudentAccommodationService {
    @Resource
    private UtilService utilService;
    @Resource
    private IMediaAndAttachedService attachedService;
    @Resource
    private StudentAccommodationMapper studentAccommodationMapper;
    @Resource
    private VerifyDataPermissionsUtils verifyDataPermissionsUtils;
    @Resource
    private ICommentService commentService;
    @Resource
    @Lazy
    private IAgentService agentService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private IFinanceCenterClient financeCenterClient;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    private BusinessChannelMapper businessChannelMapper;
    @Resource
    private IBusinessChannelService businessChannelService;
    @Resource
    private IBusinessProviderService businessProviderService;
    @Resource
    private IStudentProjectRoleStaffService projectRoleStaffService;
    @Resource
    private StudentProjectRoleStaffMapper studentProjectRoleStaffMapper;
    @Resource
    private PayablePlanMapper payablePlanMapper;
    @Resource
    private ReceivablePlanMapper receivablePlanMapper;
    @Resource
    @Lazy
    private IStudentService studentService;
    @Resource
    @Lazy
    private IReceivablePlanService receivablePlanService;
    @Resource
    @Lazy
    private IPayablePlanService payablePlanService;
    @Resource
    private GetAgentLabelDataUtils getAgentLabelDataUtils;

    @Override
    public void addStudentAccommodation(StudentAccommodationDto studentAccommodationDto) {
        if (GeneralTool.isEmpty(studentAccommodationDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        if (GeneralTool.isEmpty(studentAccommodationDto.getId())) {
            StudentAccommodation studentAccommodation = BeanCopyUtils.objClone(studentAccommodationDto, StudentAccommodation::new);
            utilService.updateUserInfoToEntity(studentAccommodation);
            studentAccommodationMapper.insertSelective(studentAccommodation);
            studentAccommodation.setNum(MyStringUtils.getAccommodationNum(studentAccommodation.getId()));
            studentAccommodationMapper.updateById(studentAccommodation);
            setProjectRoleStaff(studentAccommodationDto, studentAccommodation);
        } else {
            StudentAccommodation studentAccommodation = BeanCopyUtils.objClone(studentAccommodationDto, StudentAccommodation::new);
            utilService.updateUserInfoToEntity(studentAccommodation);
            studentAccommodationMapper.updateById(studentAccommodation);
        }
    }

    @Override
    public List<StudentAccommodationVo> getStudentAccommodationDtos(StudentAccommodationDto studentAccommodationDto, Page page) {
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public StudentAccommodationVo updateStudentAccommodation(StudentAccommodationDto studentAccommodationDto) {
        StudentAccommodation studentAccommodation = BeanCopyUtils.objClone(studentAccommodationDto, StudentAccommodation::new);
        utilService.updateUserInfoToEntity(studentAccommodation);
        studentAccommodationMapper.updateById(studentAccommodation);
        if (GeneralTool.isNotEmpty(studentAccommodationDto.getRoleStaffVo())){
            //先删后增
            studentProjectRoleStaffMapper.delete(Wrappers.lambdaQuery(StudentProjectRoleStaff.class)
                    .eq(StudentProjectRoleStaff::getFkTableId, studentAccommodationDto.getId())
                    .eq(StudentProjectRoleStaff::getFkTableName,TableEnum.SALE_STUDENT_ACCOMMODATION.key)
            );
            setProjectRoleStaff(studentAccommodationDto,studentAccommodation);
        }
        return findStudentAccommodationById(studentAccommodation.getId());
    }

    private void setProjectRoleStaff(StudentAccommodationDto studentAccommodationDto, StudentAccommodation studentAccommodation) {
        if (GeneralTool.isNotEmpty(studentAccommodationDto.getRoleStaffVo())) {
            List<ProjectRoleStaffDto> roleStaffVos = studentAccommodationDto.getRoleStaffVo();
            for (ProjectRoleStaffDto roleStaffVo : roleStaffVos) {
                StudentProjectRoleStaffDto projectRoleStaffVo = new StudentProjectRoleStaffDto();

                projectRoleStaffVo.setFkTableId(studentAccommodation.getId());
                projectRoleStaffVo.setFkTableName(TableEnum.SALE_STUDENT_ACCOMMODATION.key);
                projectRoleStaffVo.setFkStaffId(roleStaffVo.getFkStaffId());
                projectRoleStaffVo.setFkStudentProjectRoleId(roleStaffVo.getFkRoleId());
                projectRoleStaffVo.setIsActive(true);

                projectRoleStaffService.addProjectRoleStaff(projectRoleStaffVo);
            }
        }
    }

    @Override
    public void deleteStudentAccommodation(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        studentAccommodationMapper.deleteById(id);
    }

    @Override
    public StudentAccommodationVo findStudentAccommodationById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        verifyDataPermissionsUtils.verifyByBusinessId(id,VerifyDataPermissionsUtils.ACC_O);
        StudentAccommodation studentAccommodation = studentAccommodationMapper.selectById(id);
        StudentAccommodationVo studentAccommodationVo = BeanCopyUtils.objClone(studentAccommodation, StudentAccommodationVo::new);

        Set<Long> countryIds = new HashSet<>();
        countryIds.add(studentAccommodationVo.getFkAreaCountryId());
        Set<Long> stateIds = new HashSet<>();
        stateIds.add(studentAccommodationVo.getFkAreaStateId());
        Set<Long> cityIds = new HashSet<>();
        cityIds.add(studentAccommodationVo.getFkAreaCityId());
        Set<Long> fkAgentIds = new HashSet<>();
        fkAgentIds.add(studentAccommodationVo.getFkAgentId());
        Set<Long> fkStaffIds = new HashSet<>();
        fkStaffIds.add(studentAccommodationVo.getFkStaffId());
        //根据代理ids获取名称
        Map<Long, String> agentNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkAgentIds)) {
            agentNamesByIds = agentService.getAgentNamesByIds(fkAgentIds);
        }
        //根据员工ids获取姓名
        Map<Long, String> staffNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkStaffIds)) {
            Result<Map<Long, String>> result = permissionCenterClient.getStaffNameMap(fkStaffIds);
            if (result.isSuccess() && result.getData() != null) {
                staffNamesByIds = result.getData();
            }
        }
        //根据国家ids获取国家名称
        Map<Long, String> countryNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(countryIds)) {
            Result<Map<Long, String>> countryNameResult = institutionCenterClient.getCountryNamesByIds(countryIds);
            if (countryNameResult.isSuccess() && GeneralTool.isNotEmpty(countryNameResult.getData())) {
                countryNamesByIds = countryNameResult.getData();
            }
        }
        //根据州省ids获取州省名称
        Map<Long, String> stateNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(stateIds)) {
            Result<Map<Long, String>> result = institutionCenterClient.getStateFullNamesByIds(stateIds);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                stateNamesByIds = result.getData();
            }
        }
        //根据城市ids获取城市名称
        Map<Long, String> cityNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(cityIds)) {
            Result<Map<Long, String>> result = institutionCenterClient.getCityNamesByIds(cityIds);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                cityNamesByIds = result.getData();
            }
        }
        Set<String> currencyTypeNumAccommodations = new HashSet<>();
        currencyTypeNumAccommodations.add(studentAccommodationVo.getFkCurrencyTypeNumAccommodation());
        Set<String> currencyTypeNumCommissions = new HashSet<>();
        currencyTypeNumCommissions.add(studentAccommodationVo.getFkCurrencyTypeNumCommission()
        );
        //根据币种ids获取名称
        Map<String, String> currencyAccommodationNamesByNums = new HashMap<>();
        if (GeneralTool.isNotEmpty(currencyTypeNumAccommodations)) {
            Result<Map<String, String>> result = financeCenterClient.getCurrencyNamesByNums(currencyTypeNumAccommodations);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                currencyAccommodationNamesByNums = result.getData();
            }
        }
        //根据币种ids获取名称
        Map<String, String> currencyCommissionsNamesByNums = new HashMap<>();
        if (GeneralTool.isNotEmpty(currencyTypeNumCommissions)) {
            Result<Map<String, String>> result = financeCenterClient.getCurrencyNamesByNums(currencyTypeNumCommissions);
            if (result.isSuccess() && result.getData() != null) {
                currencyCommissionsNamesByNums = result.getData();
            }
        }
        Map<Long, String> companyNamesByIds = new HashMap<>();
        Map<Long, List<Long>> empty = new HashMap<>();
        List<StudentAccommodationVo> list = new ArrayList<>();
        list.add(studentAccommodationVo);
        setNameList(list, countryNamesByIds, empty, empty, companyNamesByIds, stateNamesByIds, cityNamesByIds,
                agentNamesByIds, staffNamesByIds, currencyAccommodationNamesByNums, currencyCommissionsNamesByNums,null,null);
//        if (GeneralTool.isNotEmpty(studentAccommodationVo.getFkStudentId())) {
//            studentAccommodationVo.setStudentName(studentService.getStudentNameById(studentAccommodationVo.getFkStudentId()));
//        }
        return studentAccommodationVo;
    }

    @Override
    public void closeStudentAccommodationById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        StudentAccommodation studentAccommodation = studentAccommodationMapper.selectById(id);
        if (GeneralTool.isEmpty(studentAccommodation)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        studentAccommodation.setStatus(0);
        utilService.updateUserInfoToEntity(studentAccommodation);
        studentAccommodationMapper.updateById(studentAccommodation);
    }

    @Override
    public List<Map<String, Object>> findMediaAndAttachedType() {
        return FileTypeEnum.enumsTranslation2Arrays(FileTypeEnum.STUDENTACCOMMODATION);
    }

    @Override
    public List<MediaAndAttachedVo> addStudentAccommodationMedia(List<MediaAndAttachedDto> mediaAttachedVos) {
        if (GeneralTool.isEmpty(mediaAttachedVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        List<MediaAndAttachedVo> mediaAndAttachedVos = new ArrayList<>();
        for (MediaAndAttachedDto mediaAndAttachedDto : mediaAttachedVos) {
            //设置插入的表
            String tableName = TableEnum.SALE_STUDENT_ACCOMMODATION.key;
            mediaAndAttachedDto.setFkTableName(tableName);
            mediaAndAttachedVos.add(attachedService.addMediaAndAttached(mediaAndAttachedDto));
        }
        return mediaAndAttachedVos;
    }


    @Override
    public List<MediaAndAttachedVo> getStudentAccommodationMedia(MediaAndAttachedDto attachedVo, Page page) {
        if (GeneralTool.isEmpty(attachedVo.getFkTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        verifyDataPermissionsUtils.verifyByBusinessId(attachedVo.getFkTableId(),VerifyDataPermissionsUtils.ACC_O);
        attachedVo.setFkTableName(TableEnum.SALE_STUDENT_ACCOMMODATION.key);
        return attachedService.getMediaAndAttachedDto(attachedVo, page);
    }

    @Override
    public List<StudentAccommodationVo> getStudentAccommodationList(StudentAccommodationDto studentAccommodationDto, Page page) {
        IPage<StudentAccommodationVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<StudentAccommodationVo> collect = studentAccommodationMapper.getStudentAccommodationList(iPage, studentAccommodationDto);
        //获取学生集合的所有国家ids
        Set<Long> countryIds = collect.stream().map(StudentAccommodationVo::getFkAreaCountryId).collect(Collectors.toSet());
        //获取学生集合的所有州省ids
        Set<Long> stateIds = collect.stream().map(StudentAccommodationVo::getFkAreaStateId).collect(Collectors.toSet());
        //获取学生集合的所有城市ids
        Set<Long> cityIds = collect.stream().map(StudentAccommodationVo::getFkAreaCityId).collect(Collectors.toSet());
        Set<Long> fkAgentIds = collect.stream().map(StudentAccommodationVo::getFkAgentId).collect(Collectors.toSet());
        Set<Long> fkStaffIds = collect.stream().map(StudentAccommodationVo::getFkStaffId).collect(Collectors.toSet());
        // 代理标签
        Map<Long, List<AgentLabelVo>> agentLabelMap = new HashMap<>();
        //根据代理ids获取名称
        Map<Long, String> agentNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkAgentIds)) {
            agentNamesByIds = agentService.getAgentNamesByIds(fkAgentIds);
            agentLabelMap = getAgentLabelDataUtils.getAgentLabelMap(fkAgentIds).getAgentLabelMap();
        }
        //根据员工ids获取姓名
        Map<Long, String> staffNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkStaffIds)) {
            Result<Map<Long, String>> staffNameResult = permissionCenterClient.getStaffNameMap(fkStaffIds);
            if (staffNameResult.isSuccess() && GeneralTool.isNotEmpty(staffNameResult)) {
                staffNamesByIds = staffNameResult.getData();
            }
        }
        //根据国家ids获取国家名称
        Map<Long, String> countryNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(countryIds)) {
            Result<Map<Long, String>> result = institutionCenterClient.getCountryNamesByIds(countryIds);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result)) {
                countryNamesByIds = result.getData();
            }
        }
        //根据州省ids获取州省名称
        Map<Long, String> stateNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(stateIds)) {
            Result<Map<Long, String>> result = institutionCenterClient.getStateFullNamesByIds(stateIds);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                stateNamesByIds = result.getData();
            }
        }
        //根据城市ids获取城市名称
        Map<Long, String> cityNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(cityIds)) {
            Result<Map<Long, String>> result = institutionCenterClient.getCityNamesByIds(cityIds);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                cityNamesByIds = result.getData();
            }
        }
        Set<String> currencyTypeNumAccommodations = collect.stream().map(StudentAccommodationVo::getFkCurrencyTypeNumAccommodation).collect(Collectors.toSet());
        Set<String> currencyTypeNumCommissions = collect.stream().map(StudentAccommodationVo::getFkCurrencyTypeNumCommission).collect(Collectors.toSet());
        //根据币种ids获取名称
        Map<String, String> currencyAccommodationNamesByNums = new HashMap<>();
        if (GeneralTool.isNotEmpty(currencyTypeNumAccommodations)) {
            Result<Map<String, String>> result = financeCenterClient.getCurrencyNamesByNums(currencyTypeNumAccommodations);
            if (result.isSuccess() && result.getData() != null) {
                currencyAccommodationNamesByNums = result.getData();
            }
        }
        //根据币种ids获取名称
        Map<String, String> currencyCommissionsNamesByNums = new HashMap<>();
        if (GeneralTool.isNotEmpty(currencyTypeNumCommissions)) {
            Result<Map<String, String>> result = financeCenterClient.getCurrencyNamesByNums(currencyTypeNumCommissions);
            if (result.isSuccess() && result.getData() != null) {
                currencyCommissionsNamesByNums = result.getData();
            }
        }
        Map<Long, String> companyNamesByIds = new HashMap<>();
        Map<Long, List<Long>> empty = new HashMap<>();
        setNameList(collect, countryNamesByIds, empty, empty, companyNamesByIds, stateNamesByIds, cityNamesByIds,
                agentNamesByIds, staffNamesByIds, currencyAccommodationNamesByNums, currencyCommissionsNamesByNums,null,agentLabelMap);
//        for (StudentAccommodationVo studentAccommodationDto : collect) {
//            setNameList(studentAccommodationDto, countryNamesByIds, stateNamesByIds, cityNamesByIds,
//                    agentNamesByIds, staffNamesByIds, currencyAccommodationNamesByNums, currencyCommissionsNamesByNums);
//            if (GeneralTool.isNotEmpty(studentAccommodationDto.getFkStudentId())) {
//                studentAccommodationDto.setStudentName(studentService.getStudentNameById(studentAccommodationDto.getFkStudentId()));
//            }
//        }
        page.setAll((int) iPage.getTotal());
        return collect;
    }


    @Override
    public Long editComment(CommentDto commentDto) {
        SaleComment comment = BeanCopyUtils.objClone(commentDto, SaleComment::new);
        if (GeneralTool.isNotEmpty(commentDto)) {
            if (GeneralTool.isNotEmpty(commentDto.getId())) {
                comment.setFkTableName(TableEnum.SALE_STUDENT_ACCOMMODATION.key);
                commentService.updateComment(comment);
            } else {
                comment.setFkTableName(TableEnum.SALE_STUDENT_ACCOMMODATION.key);
                commentService.addComment(comment);
            }
        }
        return comment.getId();
    }

    @Override
    public List<CommentVo> getComments(CommentDto commentDto, Page page) {
        commentDto.setFkTableName(TableEnum.SALE_STUDENT_ACCOMMODATION.key);
        return commentService.datas(commentDto, page);
    }


    private void setNameList(List<StudentAccommodationVo> studentAccommodationVos, Map<Long, String> countryNamesByIds,
                             Map<Long, List<Long>> receivablePlanIds,
                             Map<Long, List<Long>> payablePlanIds,
                             Map<Long, String> companyNamesByIds,
                             Map<Long, String> stateNamesByIds, Map<Long, String> cityNamesByIds,
                             Map<Long, String> agentNamesByIds, Map<Long, String> staffNamesByIds, Map<String, String> currencyAccommodationNamesByNums, Map<String, String> currencyCommissionsNamesByNums,
                             Map<Long, List<AccommodationPayFormDetailVo>> payFormDetailMap,Map<Long, List<AgentLabelVo>> agentLabelMap) {

        List<Long> rps;
        List<Long> pps;
        Long fkAgentId;
        String nameNote;
        Set<Long> agentIds = studentAccommodationVos.stream().map(StudentAccommodationVo::getFkAgentId).filter(Objects::nonNull).collect(Collectors.toSet());
//        Map<Long, Agent> agentsByIds = agentService.getAgentsByIds(agentIds);
        Set<Long> studentIds = studentAccommodationVos.stream().map(StudentAccommodationVo::getFkStudentId).collect(Collectors.toSet());
        Map<Long, String> studentNameByIds = studentService.getStudentNameByIds(studentIds);
        Set<Long> bChannelIds = studentAccommodationVos.stream().map(StudentAccommodationVo::getFkBusinessChannelId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Long, String> businessChannelServiceNamesByIds = businessChannelService.getNamesByIds(bChannelIds);
        //业务提供商
        Set<Long> bProviderIds = studentAccommodationVos.stream().map(StudentAccommodationVo::getFkBusinessProviderId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Long, String> businessProviderServiceNamesByIds = businessProviderService.getNamesByIds(bProviderIds);

        Set<Long> ids = studentAccommodationVos.stream().map(StudentAccommodationVo::getId).filter(Objects::nonNull).collect(Collectors.toSet());
        List<StudentProjectRoleStaffVo> studentProjectRoleStaffVos = studentProjectRoleStaffMapper.selectProjectStaff(TableEnum.SALE_STUDENT_ACCOMMODATION.key, new ArrayList<>(ids));
        Map<Long, List<StudentProjectRoleStaffVo>> studentProjectRoleStaffMap =
                studentProjectRoleStaffVos.stream().collect(Collectors.groupingBy(StudentProjectRoleStaffVo::getFkTableId));
        for (StudentAccommodationVo studentAccommodationVo : studentAccommodationVos) {
            rps = receivablePlanIds.get(studentAccommodationVo.getId());
            if (rps == null) {
                rps = Collections.emptyList();
            }
            pps = payablePlanIds.get(studentAccommodationVo.getId());
            if (pps != null) {
                rps.addAll(pps);
            }
            studentAccommodationVo.setStudentName(studentNameByIds.get(studentAccommodationVo.getFkStudentId()));
            studentAccommodationVo.setARAPIds(rps);
            String cityName = cityNamesByIds.get(studentAccommodationVo.getFkAreaCityId());
            String countryName = countryNamesByIds.get(studentAccommodationVo.getFkAreaCountryId());
            String stateName = stateNamesByIds.get(studentAccommodationVo.getFkAreaStateId());

            String staffName = staffNamesByIds.get(studentAccommodationVo.getFkStaffId());
            String agentName = agentNamesByIds.get(studentAccommodationVo.getFkAgentId());
            String companyName = companyNamesByIds.get(studentAccommodationVo.getFkCompanyId());
            studentAccommodationVo.setFkCompanyName(companyName);
            String accommodationNames = currencyAccommodationNamesByNums.get(studentAccommodationVo.getFkCurrencyTypeNumAccommodation());
            String commissionsNames = currencyCommissionsNamesByNums.get(studentAccommodationVo.getFkCurrencyTypeNumCommission());
            studentAccommodationVo.setAreaCityName(cityName);
            studentAccommodationVo.setFkAreaCountryName(countryName);
            studentAccommodationVo.setAreaStateName(stateName);
            studentAccommodationVo.setStaffName(staffName);
            studentAccommodationVo.setFkAgentName(agentName);
            if (GeneralTool.isNotEmpty(agentLabelMap)){
                studentAccommodationVo.setAgentLabelVos(agentLabelMap.get(studentAccommodationVo.getFkAgentId()));
            }
//            if (agentsByIds!=null) {
//                fkAgentId = studentAccommodationVo.getFkAgentId();
//                if (agentsByIds.containsKey(fkAgentId)) {
//                    nameNote = agentsByIds.get(fkAgentId).getNameNote();
//                    if (StringUtils.isNotBlank(nameNote)) {
//                        studentAccommodationVo.setFkAgentName(studentAccommodationVo.getFkAgentName() + "(" + nameNote + ")");
//                    }
//                }
//            }
            String locale = SecureUtil.getLocale();
            studentAccommodationVo.setArStatusName(ProjectExtraEnum.getValueByKey(studentAccommodationVo.getArStatus(), ProjectExtraEnum.AR_STATUS,locale));
            studentAccommodationVo.setApStatusName(ProjectExtraEnum.getValueByKey(studentAccommodationVo.getApStatus(), ProjectExtraEnum.AP_STATUS,locale));
            studentAccommodationVo.setSettlementStatusName(ProjectExtraEnum.getValueByKey(studentAccommodationVo.getSettlementStatus(), ProjectExtraEnum.SETTLEMENT_TYPE,locale));
            if (GeneralTool.isNotEmpty(payFormDetailMap)) {
                //币种，实付金额，付款时间
                List<AccommodationPayFormDetailVo> paymentFormItems = new ArrayList<>();
                if (GeneralTool.isNotEmpty(payFormDetailMap)&&GeneralTool.isNotEmpty(payFormDetailMap.get(studentAccommodationVo.getId()))){
                    paymentFormItems = payFormDetailMap.get(studentAccommodationVo.getId());
                }
//            List<PayFormDetailVo> paymentFormItemList = studentOfferItemMapper.getPaidAmount(studentOfferItemListDto.getId());
                List<Map<String,Object>> payDetailList = new ArrayList<>();
                for (AccommodationPayFormDetailVo paymentFormItem : paymentFormItems) {
                    Map<String,Object> map = new HashMap<>();
                    map.put("payableCurrencyTypeName", studentAccommodationVo.getPayableCurrencyTypeName());
                    map.put("actualPayableAmount", paymentFormItem.getActualPayableAmount());
                    map.put("gmtCreate", paymentFormItem.getActualPayTime());
                    payDetailList.add(map);
                }
                studentAccommodationVo.setPayDetailList(payDetailList);
            }
            studentAccommodationVo.setFkCurrencyTypeNumAccommodationName(accommodationNames);
            studentAccommodationVo.setFkCurrencyTypeNumCommissionName(commissionsNames);
            studentAccommodationVo.setChannelName(businessChannelServiceNamesByIds.get(studentAccommodationVo.getFkBusinessChannelId()));
            if (GeneralTool.isNotEmpty(businessProviderServiceNamesByIds)){
                studentAccommodationVo.setProviderName(businessProviderServiceNamesByIds.get(studentAccommodationVo.getFkBusinessProviderId()));
            }
            if (GeneralTool.isNotEmpty(studentAccommodationVo.getFixedAmountPayable())) {
                if (GeneralTool.isNotEmpty(studentAccommodationVo.getFkAgentId())) {
                    studentAccommodationVo.setAmountPayable(studentAccommodationVo.getFixedAmountPayable());
                }
            }
//            if (GeneralTool.isNotEmpty(studentAccommodationVo.getFixedAmountReceivable())) {
//                studentAccommodationVo.setAmountReceivable(studentAccommodationVo.getFixedAmountReceivable());
//            }
            if (GeneralTool.isNotEmpty(studentAccommodationVo.getCommissionRatePayable())) {
                if (GeneralTool.isNotEmpty(studentAccommodationVo.getFkAgentId())) {
                    studentAccommodationVo.setAmountPayable(studentAccommodationVo.getCommissionRatePayable().divide(new BigDecimal("100")).multiply(studentAccommodationVo.getAccommodationAmount()));
                }
            }

//            if (GeneralTool.isNotEmpty(studentAccommodationVo.getCommissionRateReceivable())) {
//                studentAccommodationVo.setAmountReceivable(studentAccommodationVo.getCommissionRateReceivable().divide(new BigDecimal("100")).multiply(studentAccommodationVo.getAccommodationAmount()));
//            }
//            //获取绑定的项目成员
//            for (StudentProjectRoleStaffVo studentProjectRoleStaffDto : studentProjectRoleStaffVos) {
//                String[] staffIdStr = studentProjectRoleStaffDto.getStaffIdStr().split(",");
//                Set<Long> staffIds = Arrays.stream(staffIdStr).map(Long::valueOf).collect(Collectors.toSet());
//                Map<Long, String> staffNameMap = new HashMap<>();
//                Result<Map<Long, String>> result = permissionCenterClient.getStaffNameMap(staffIds);
//                if (result.isSuccess() && GeneralTool.isNotEmpty(result)) {
//                    staffNameMap = result.getData();
//                }
//                studentProjectRoleStaffDto.setStaffName(StringUtils.join(staffNameMap.values(), ","));
//            }
            //绑定项目成员

            List<StudentProjectRoleStaffVo> roleStaffDtos = Lists.newArrayList();
            List<StudentProjectRoleStaffVo> projectRoleStaffDtos = studentProjectRoleStaffMap.get(studentAccommodationVo.getId());
            if (GeneralTool.isNotEmpty(projectRoleStaffDtos)){
                for (StudentProjectRoleStaffVo projectRoleStaffDto : projectRoleStaffDtos) {
                    String[] staffIdIdArray = projectRoleStaffDto.getStaffIdStr().split(",");
                    String[] staffNameArray = projectRoleStaffDto.getStaffName().split(",");
                    int n = staffIdIdArray.length;
                    if (staffIdIdArray.length!=staffNameArray.length){
                        throw new GetServiceException(LocaleMessageUtils.getMessage("data_exception"));
                    }
                    for (int i = 0; i < n; i++) {
                        StudentProjectRoleStaffVo studentProjectRoleStaffVo = BeanCopyUtils.objClone(projectRoleStaffDto, StudentProjectRoleStaffVo::new);
                        assert studentProjectRoleStaffVo != null;
                        studentProjectRoleStaffVo.setFkStaffId(Long.parseLong(staffIdIdArray[i]));
                        studentProjectRoleStaffVo.setStaffName(staffNameArray[i]);
                        roleStaffDtos.add(studentProjectRoleStaffVo);
                    }
                }
            }
            if (GeneralTool.isNotEmpty(roleStaffDtos)){
                studentAccommodationVo.setStudentProjectRoleStaffDtos(roleStaffDtos);
                StringBuilder sb = new StringBuilder();
                for (StudentProjectRoleStaffVo studentProjectRoleStaffVo : roleStaffDtos) {
                    sb.append(studentProjectRoleStaffVo.getRoleName()).append(":").append(studentProjectRoleStaffVo.getStaffName()).append(",");
                }
                studentAccommodationVo.setProjectRoleName(sb.toString());
            }

//            List<StudentProjectRoleStaffVo> studentProjectRoleStaffDtoList = studentProjectRoleStaffMap.get(studentAccommodationVo.getId());
//            studentAccommodationVo.setStudentProjectRoleStaffDtos(studentProjectRoleStaffDtoList);


            if (GeneralTool.isNotEmpty(studentAccommodationVo.getGender())) {
                if (studentAccommodationVo.getGender().equals(1)) {
                    studentAccommodationVo.setGenderName("男");
                } else if (studentAccommodationVo.getGender().equals(0)) {
                    studentAccommodationVo.setGenderName("女");
                }
            }
            if (GeneralTool.isNotEmpty(studentAccommodationVo.getStatus())) {
                if (studentAccommodationVo.getStatus().equals(1)) {
                    studentAccommodationVo.setStatusName("有效");
                } else if (studentAccommodationVo.getStatus().equals(0)) {
                    studentAccommodationVo.setStatusName("作废");
                } else if (studentAccommodationVo.getStatus().equals(2)) {
                    studentAccommodationVo.setStatusName("成功");
                } else if (studentAccommodationVo.getStatus().equals(3)) {
                    studentAccommodationVo.setStatusName("延期");
                } else if (studentAccommodationVo.getStatus().equals(4)) {
                    studentAccommodationVo.setStatusName("失败");
                }
            }
            if (GeneralTool.isNotEmpty(studentAccommodationVo.getGender())) {
                if (studentAccommodationVo.getGender().equals(1)) {
                    studentAccommodationVo.setGenderName("男");
                } else if (studentAccommodationVo.getGender().equals(0)) {
                    studentAccommodationVo.setGenderName("女");
                }
            }
//            if (GeneralTool.isNotEmpty(studentProjectRoleStaffDtoList)) {
//                StringBuilder sb = new StringBuilder();
//                for (StudentProjectRoleStaffVo studentProjectRoleStaffDto : studentProjectRoleStaffDtoList) {
//                    sb.append(studentProjectRoleStaffDto.getRoleName()).append(":").append(studentProjectRoleStaffDto.getStaffName()).append(",");
//                }
//                studentAccommodationVo.setProjectRoleName(sb.toString());
//            }
        }
    }

    @Override
    public List<Map<String, Object>> getAccommodationExpenseType() {
        return ProjectExtraEnum.enums2Arrays(ProjectExtraEnum.ACCOMMODATION_EXPENSE_TYPE);
    }


    @Override
    public List<StudentAccommodationVo> getStudentAccommodationSummary(AccommodationSummaryQueryDto studentAccommodation, Page page, String[] times) {
        long startTime = System.currentTimeMillis();

        Long staffId = SecureUtil.getStaffId();

        List<Long> staffFollowerIds = Lists.newArrayList();
        List<Long> followerIds = CacheUtil.get(
                STAFF_FOLLOWER_IDS_CACHE,
                "staffId:",
                staffId,
                ()->permissionCenterClient.getStaffFollowerIds(staffId).getData()
        );
        //员工id + 业务下属员工ids
//        List<Long> staffFollowerIds = new ArrayList<>();
//        Result<List<Long>> result_ = permissionCenterClient.getStaffFollowerIds(staffId);
//        if (result_.isSuccess() && GeneralTool.isNotEmpty(result_.getData())) {
//            staffFollowerIds.addAll(result_.getData().stream().filter(Objects::nonNull).distinct().collect(Collectors.toList()));
//        }
        if (GeneralTool.isNotEmpty(followerIds)) {
            staffFollowerIds.addAll(followerIds.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList()));
        }
        staffFollowerIds.add(staffId);

        long endTime1 = System.currentTimeMillis();

        IPage<StudentAccommodationVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        String studentName = studentAccommodation.getStudentName();
        if (StringUtils.isNotBlank(studentName)) {
            studentAccommodation.setStudentName(studentName.replace(" ", "").trim());
        }

        //针对like的字段转为小写
        if(GeneralTool.isNotEmpty(studentAccommodation.getStudentName()))
        {
            studentAccommodation.setStudentName(studentAccommodation.getStudentName().toLowerCase());
        }
        if(GeneralTool.isNotEmpty(studentAccommodation.getAgentName()))
        {
            studentAccommodation.setAgentName(studentAccommodation.getAgentName().toLowerCase());
        }
        if(GeneralTool.isNotEmpty(studentAccommodation.getStaffName()))
        {
            studentAccommodation.setStaffName(studentAccommodation.getStaffName().toLowerCase());
        }
        if(GeneralTool.isNotEmpty(studentAccommodation.getMemberName()))
        {
            studentAccommodation.setMemberName(studentAccommodation.getMemberName().toLowerCase());
        }
        if(GeneralTool.isNotEmpty(studentAccommodation.getApartmentName()))
        {
            studentAccommodation.setApartmentName(studentAccommodation.getApartmentName().toLowerCase());
        }

        List<StudentAccommodationVo> collect = studentAccommodationMapper.getStudentAccommodationSummary(iPage, studentAccommodation, staffFollowerIds, SecureUtil.getStaffInfo().getIsStudentAdmin(), SecureUtil.getPermissionGroupInstitutionIds(), SecureUtil.getStaffBoundBdIds());
        page.setAll((int) iPage.getTotal());
        long endTime2 = System.currentTimeMillis();
        if (GeneralTool.isEmpty(collect)){
            return Collections.emptyList();
        }

        //获取学生集合的所有国家ids
        Set<Long> countryIds = collect.stream().map(StudentAccommodationVo::getFkAreaCountryId).collect(Collectors.toSet());
        //获取学生集合的所有州省ids
        Set<Long> stateIds = collect.stream().map(StudentAccommodationVo::getFkAreaStateId).collect(Collectors.toSet());
        //获取学生集合的所有城市ids
        Set<Long> cityIds = collect.stream().map(StudentAccommodationVo::getFkAreaCityId).collect(Collectors.toSet());
        Set<Long> fkAgentIds = collect.stream().map(StudentAccommodationVo::getFkAgentId).collect(Collectors.toSet());
        Set<Long> fkStaffIds = collect.stream().map(StudentAccommodationVo::getFkStaffId).collect(Collectors.toSet());
        Set<String> currencyTypeNumAccommodations = collect.stream().map(StudentAccommodationVo::getFkCurrencyTypeNumAccommodation).collect(Collectors.toSet());
        Set<String> currencyTypeNumCommissions = collect.stream().map(StudentAccommodationVo::getFkCurrencyTypeNumCommission).collect(Collectors.toSet());
        Set<String> currencyTypeNums = new HashSet<>();
        currencyTypeNums.addAll(currencyTypeNumAccommodations);
        currencyTypeNums.addAll(currencyTypeNumCommissions);

        Map<String, String> currencyTypeNameMap = null;
        if (GeneralTool.isNotEmpty(currencyTypeNums)){
            currencyTypeNameMap = financeCenterClient.getCurrencyNamesByNums(currencyTypeNums).getData();
        }
        //调用一次就行

//        //根据币种ids获取名称
//        Map<String, String> currencyAccommodationNamesByNums = new HashMap<>();
//        if (GeneralTool.isNotEmpty(currencyTypeNumAccommodations)) {
//            Result<Map<String, String>> result = financeCenterClient.getCurrencyNamesByNums(currencyTypeNumAccommodations);
//            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
//                currencyAccommodationNamesByNums = result.getData();
//            }
//        }
//        //根据币种ids获取名称
//        Map<String, String> currencyCommissionsNamesByNums = new HashMap<>();
//        if (GeneralTool.isNotEmpty(currencyTypeNumCommissions)) {
//            Result<Map<String, String>> result = financeCenterClient.getCurrencyNamesByNums(currencyTypeNumCommissions);
//            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
//                currencyCommissionsNamesByNums = result.getData();
//            }
//        }
        Map<String, String> currencyAccommodationNamesByNums = currencyTypeNameMap;
        Map<String, String> currencyCommissionsNamesByNums = currencyTypeNameMap;
        //根据代理ids获取名称
        Map<Long, String> agentNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkAgentIds)) {
            agentNamesByIds = agentService.getAgentNamesByIds(fkAgentIds);
        }
        //代理标签
        Map<Long, List<AgentLabelVo>> agentLabelMap = getAgentLabelDataUtils.getAgentLabelMap(fkAgentIds).getAgentLabelMap();
        //根据员工ids获取姓名
        Map<Long, String> staffNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkStaffIds)) {
            Result<Map<Long, String>> staffNameResult = permissionCenterClient.getStaffNameMap(fkStaffIds);
            if (staffNameResult.isSuccess() && GeneralTool.isNotEmpty(staffNameResult)) {
                staffNamesByIds = staffNameResult.getData();
            }
        }
        //根据国家ids获取国家名称
        Map<Long, String> countryNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(countryIds)) {
            Result<Map<Long, String>> result1 = institutionCenterClient.getCountryNamesByIds(countryIds);
            if (result1.isSuccess() && GeneralTool.isNotEmpty(result1.getData())) {
                countryNamesByIds = result1.getData();
            }
        }
        //根据州省ids获取州省名称
        Map<Long, String> stateNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(stateIds)) {
            Result<Map<Long, String>> result = institutionCenterClient.getStateFullNamesByIds(stateIds);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                stateNamesByIds = result.getData();
            }
        }
        Set<Long> companyIds = collect.stream().map(StudentAccommodationVo::getFkCompanyId).collect(Collectors.toSet());
        //根据公司ids获取公司名称
        Map<Long, String> companyNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(companyIds)) {
            Result<Map<Long, String>> result = permissionCenterClient.getCompanyNamesByIds(companyIds);
            if (result.isSuccess() && result.getData() != null) {
                companyNamesByIds = result.getData();
            }
        }
        //根据城市ids获取城市名称
        Map<Long, String> cityNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(cityIds)) {
            Result<Map<Long, String>> result = institutionCenterClient.getCityFullNamesByIds(cityIds);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                cityNamesByIds = result.getData();
            }
        }
        Set<Long> targetIds = collect.stream().map(StudentAccommodationVo::getId).collect(Collectors.toSet());
        Set<Long> businessIds = collect.stream().map(StudentAccommodationVo::getFkBusinessChannelId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Long, List<Long>> receivablePlanIds = receivablePlanService.getReceivablePlanIds(TableEnum.SALE_STUDENT_ACCOMMODATION.key, targetIds);
        Map<Long, List<Long>> payablePlanIds = payablePlanService.getPayablePlanIds(TableEnum.SALE_STUDENT_ACCOMMODATION.key, targetIds);
        receivablePlanIds.putAll(receivablePlanService.getReceivablePlanIds(TableEnum.BUSINESS_CHANNEL_ACC.key, businessIds));
        payablePlanIds.putAll(payablePlanService.getPayablePlanIds(TableEnum.BUSINESS_CHANNEL_ACC.key, businessIds));
        if (GeneralTool.isEmpty(targetIds)){
            targetIds.add(0L);
        }
//        Set<Long> itemIds = collect.stream().map(StudentAccommodationVo::getId).collect(Collectors.toSet());
        List<AccommodationPayFormDetailVo> paymentFormItemList = studentAccommodationMapper.getPaidAmountByIds(targetIds);
        Map<Long, List<AccommodationPayFormDetailVo>> payFormDetailMap = null;
        if (GeneralTool.isNotEmpty(paymentFormItemList)){
            payFormDetailMap = paymentFormItemList.stream().collect(Collectors.groupingBy(AccommodationPayFormDetailVo::getAccommodationId));
        }


        setNameList(collect, countryNamesByIds, receivablePlanIds, payablePlanIds, companyNamesByIds,
                stateNamesByIds, cityNamesByIds,
                agentNamesByIds, staffNamesByIds, currencyAccommodationNamesByNums, currencyCommissionsNamesByNums,payFormDetailMap,agentLabelMap);
//        for (StudentAccommodationVo studentAccommodationDto : collect) {
//            setNameList(studentAccommodationDto, countryNamesByIds, stateNamesByIds, cityNamesByIds,
//                    agentNamesByIds, staffNamesByIds, currencyAccommodationNamesByNums, currencyCommissionsNamesByNums);
//            String companyName = companyNamesByIds.get(studentAccommodationDto.getFkCompanyId());
//            studentAccommodationDto.setFkCompanyName(companyName);
//            if (GeneralTool.isNotEmpty(studentAccommodationDto.getFkStudentId())) {
//                studentAccommodationDto.setStudentName(studentNameByIds.get(studentAccommodationDto.getFkStudentId()));
//            }
////            List<Long> receivablePlanIds = receivablePlanService.getReceivablePlanId(TableEnum.SALE_STUDENT_ACCOMMODATION.key, studentAccommodationDto.getId());
////            List<Long> payablePlanIds = payablePlanService.getPayablePlanId(TableEnum.SALE_STUDENT_ACCOMMODATION.key, studentAccommodationDto.getId());
//            List<Long> ids = new ArrayList<>();
//            rps = receivablePlanIds.get(studentAccommodationDto.getId());
//            if (GeneralTool.isNotEmpty(rps)) {
//                ids.addAll(rps);
//            }
//            pps = payablePlanIds.get(studentAccommodationDto.getId());
//            if (GeneralTool.isNotEmpty(pps)) {
//                ids.addAll(pps);
//            }
//            studentAccommodationDto.setARAPIds(ids);
//        }

        long endTime = System.currentTimeMillis();
        if (GeneralTool.isNotEmpty(times)) {
            times[0] = String.valueOf(endTime2 - endTime1);
            times[1] = String.valueOf((endTime - startTime) - (endTime2 - endTime1));
        }
        return collect;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createARAP(AccommodationSummaryDto accommodationSummaryDto) {
        if (GeneralTool.isEmpty(accommodationSummaryDto.getAccommodationIds())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (GeneralTool.isEmpty(accommodationSummaryDto.getCommissionRatePayable()) && GeneralTool.isEmpty(accommodationSummaryDto.getCommissionRateReceivable()) &&
                GeneralTool.isEmpty(accommodationSummaryDto.getFixedAmountPayable()) && GeneralTool.isEmpty(accommodationSummaryDto.getFixedAmountReceivable()) && GeneralTool.isEmpty(accommodationSummaryDto.getFkCompanyId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        for (Long id : accommodationSummaryDto.getAccommodationIds()) {
            StudentAccommodation studentAccommodation = studentAccommodationMapper.selectById(id);
            if (GeneralTool.isEmpty(studentAccommodation)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
            }
            studentAccommodation.setCommissionRatePayable(accommodationSummaryDto.getCommissionRatePayable());
            studentAccommodation.setCommissionRateReceivable(accommodationSummaryDto.getCommissionRateReceivable());
            studentAccommodation.setFixedAmountPayable(accommodationSummaryDto.getFixedAmountPayable());
            studentAccommodation.setFixedAmountReceivable(accommodationSummaryDto.getFixedAmountReceivable());
            if (GeneralTool.isEmpty(accommodationSummaryDto.getFkCurrencyTypeNumCommission())) {
                studentAccommodation.setFkCurrencyTypeNumCommission(studentAccommodation.getFkCurrencyTypeNumCommission());
            } else {
                studentAccommodation.setFkCurrencyTypeNumCommission(accommodationSummaryDto.getFkCurrencyTypeNumCommission());
            }
            utilService.updateUserInfoToEntity(studentAccommodation);
            studentAccommodationMapper.updateById(studentAccommodation);

            Long fkReceivablePlanId = null;
            if (GeneralTool.isNotEmpty(accommodationSummaryDto.getCommissionRateReceivable()) || GeneralTool.isNotEmpty(accommodationSummaryDto.getFixedAmountReceivable())) {
                ReceivablePlan receivablePlan = new ReceivablePlan();
                receivablePlan.setFkTypeKey(TableEnum.SALE_STUDENT_ACCOMMODATION.key);
                receivablePlan.setFkTypeTargetId(studentAccommodation.getId());
                if (GeneralTool.isNotEmpty(studentAccommodation.getFkCurrencyTypeNumCommission())) {
                    receivablePlan.setFkCurrencyTypeNum(studentAccommodation.getFkCurrencyTypeNumCommission());
                } else {
                    receivablePlan.setFkCurrencyTypeNum(studentAccommodation.getFkCurrencyTypeNumAccommodation());
                }
                receivablePlan.setTuitionAmount(studentAccommodation.getAccommodationAmount());
                receivablePlan.setCommissionRate(studentAccommodation.getCommissionRateReceivable());
                if (GeneralTool.isNotEmpty(accommodationSummaryDto.getCommissionRateReceivable())) {
                    receivablePlan.setCommissionAmount(studentAccommodation.getAccommodationAmount().divide(new BigDecimal("100")).multiply(studentAccommodation.getCommissionRateReceivable()));
                    receivablePlan.setReceivableAmount(studentAccommodation.getAccommodationAmount().divide(new BigDecimal("100")).multiply(studentAccommodation.getCommissionRateReceivable()));
                } else {
                    receivablePlan.setCommissionAmount(studentAccommodation.getFixedAmountReceivable());
                    receivablePlan.setReceivableAmount(studentAccommodation.getFixedAmountReceivable());
                }
                receivablePlan.setFixedAmount(studentAccommodation.getFixedAmountReceivable());
                receivablePlan.setStatus(1);
                receivablePlan.setNetRate(new BigDecimal(100));
                receivablePlan.setFkCompanyId(accommodationSummaryDto.getFkCompanyId());
                utilService.updateUserInfoToEntity(receivablePlan);
                receivablePlanMapper.insertSelective(receivablePlan);
                fkReceivablePlanId = receivablePlan.getId();
            }
            if (GeneralTool.isNotEmpty(accommodationSummaryDto.getCommissionRatePayable()) || GeneralTool.isNotEmpty(accommodationSummaryDto.getFixedAmountPayable())) {
                if (GeneralTool.isNotEmpty(studentAccommodation.getFkAgentId())) {
                    PayablePlan payablePlan = new PayablePlan();
                    payablePlan.setFkTypeKey(TableEnum.SALE_STUDENT_ACCOMMODATION.key);
                    payablePlan.setFkTypeTargetId(studentAccommodation.getId());
                    if (GeneralTool.isNotEmpty(studentAccommodation.getFkCurrencyTypeNumCommission())) {
                        payablePlan.setFkCurrencyTypeNum(studentAccommodation.getFkCurrencyTypeNumCommission());
                    } else {
                        payablePlan.setFkCurrencyTypeNum(studentAccommodation.getFkCurrencyTypeNumAccommodation());
                    }
                    payablePlan.setTuitionAmount(studentAccommodation.getAccommodationAmount());
                    payablePlan.setCommissionRate(studentAccommodation.getCommissionRatePayable());
                    if (GeneralTool.isNotEmpty(accommodationSummaryDto.getCommissionRatePayable())) {
                        payablePlan.setCommissionAmount(studentAccommodation.getAccommodationAmount().divide(new BigDecimal("100")).multiply(studentAccommodation.getCommissionRatePayable()));
                        payablePlan.setPayableAmount(studentAccommodation.getAccommodationAmount().divide(new BigDecimal("100")).multiply(studentAccommodation.getCommissionRatePayable()));
                    } else {
                        payablePlan.setCommissionAmount(studentAccommodation.getFixedAmountPayable());
                        payablePlan.setPayableAmount(studentAccommodation.getFixedAmountPayable());
                    }
                    payablePlan.setFixedAmount(studentAccommodation.getFixedAmountPayable());
                    payablePlan.setStatus(1);
                    payablePlan.setStatusSettlement(ProjectExtraEnum.UNSETTLED.key);
                    if (fkReceivablePlanId != null) {
                        payablePlan.setFkReceivablePlanId(fkReceivablePlanId);
                    }
                    payablePlan.setSplitRate(new BigDecimal(100));
                    payablePlan.setFkCompanyId(accommodationSummaryDto.getFkCompanyId());
                    payablePlan.setIsPayInAdvance(false);
                    utilService.updateUserInfoToEntity(payablePlan);
                    payablePlanMapper.insert(payablePlan);
                }
            }
        }
    }

    @Override
    public void activationStudentAccommodationById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        StudentAccommodation StudentInsurance = studentAccommodationMapper.selectById(id);
        if (GeneralTool.isEmpty(StudentInsurance)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        StudentInsurance.setStatus(1);
        utilService.updateUserInfoToEntity(StudentInsurance);
        studentAccommodationMapper.updateById(StudentInsurance);
    }

    @Override
    public Long getAccommodationAgentId(Long targetId) {
        return studentAccommodationMapper.queryAccommodationAgentId(targetId);
    }

    @Override
    public Long getStudentAccommodationId(Long targetId) {
        return studentAccommodationMapper.getIdByTargetId(targetId);
    }

    @Override
    public List<Map<String, Object>> getAccommodationStatusSelect() {
        return ProjectExtraEnum.enumsTranslation2Arrays(ProjectExtraEnum.ACC_STATUS);
    }

    @Override
    public Map<Long, String> getNumByIds(Set<Long> accommodationIds) {
        if (GeneralTool.isEmpty(accommodationIds)) {
            return new HashMap<>();
        }
        List<StudentAccommodation> studentAccommodations = studentAccommodationMapper.selectBatchIds(accommodationIds);
        if (GeneralTool.isEmpty(studentAccommodations)) {
            return null;
        }
        Map<Long, String> map = new HashMap<>();
        for (StudentAccommodation studentAccommodation : studentAccommodations) {
            map.put(studentAccommodation.getId(), studentAccommodation.getNum());
        }
        return map;
    }

    /**
     * Author Cream
     * Description : //合并留学住宿数据
     * Date 2023/5/11 11:43
     * Params:
     * Return
     */
    @Override
    public void mergeData(Long mergedStudentId, Long targetStudentId) {
        List<StudentAccommodation> accommodations = studentAccommodationMapper.selectList(Wrappers.<StudentAccommodation>lambdaQuery().eq(StudentAccommodation::getFkStudentId, mergedStudentId));
        if (GeneralTool.isNotEmpty(accommodations)) {
            accommodations.forEach(s->s.setFkStudentId(targetStudentId));
            updateBatchById(accommodations);
        }
    }

    @Override
    public StudentAccommodation getStudentAccommodationById(Long id) {
        return studentAccommodationMapper.selectById(id);
    }

    @Override
    public void exportExcel(HttpServletResponse response, AccommodationSummaryQueryDto studentAccommodation) {
        Long staffId = SecureUtil.getStaffId();
        //员工id + 业务下属员工ids
        List<Long> staffFollowerIds = new ArrayList<>();
        Result<List<Long>> result_ = permissionCenterClient.getStaffFollowerIds(staffId);
        if (result_.isSuccess() && GeneralTool.isNotEmpty(result_.getData())) {
            staffFollowerIds.addAll(result_.getData().stream().filter(Objects::nonNull).distinct().collect(Collectors.toList()));
        }
        staffFollowerIds.add(staffId);
        studentAccommodation.setStaffFollowerIds(staffFollowerIds);

        //针对like的字段转为小写
        if(GeneralTool.isNotEmpty(studentAccommodation.getStudentName()))
        {
            studentAccommodation.setStudentName(studentAccommodation.getStudentName().toLowerCase());
        }
        if(GeneralTool.isNotEmpty(studentAccommodation.getAgentName()))
        {
            studentAccommodation.setAgentName(studentAccommodation.getAgentName().toLowerCase());
        }
        if(GeneralTool.isNotEmpty(studentAccommodation.getStaffName()))
        {
            studentAccommodation.setStaffName(studentAccommodation.getStaffName().toLowerCase());
        }
        if(GeneralTool.isNotEmpty(studentAccommodation.getMemberName()))
        {
            studentAccommodation.setMemberName(studentAccommodation.getMemberName().toLowerCase());
        }
        if(GeneralTool.isNotEmpty(studentAccommodation.getApartmentName()))
        {
            studentAccommodation.setApartmentName(studentAccommodation.getApartmentName().toLowerCase());
        }

        List<StudentAccommodationVo> collect = studentAccommodationMapper.getStudentAccommodationSummary(null, studentAccommodation, staffFollowerIds, SecureUtil.getStaffInfo().getIsStudentAdmin(), SecureUtil.getPermissionGroupInstitutionIds(), SecureUtil.getStaffBoundBdIds());
        //获取学生集合的所有国家ids
        Set<Long> countryIds = collect.stream().map(StudentAccommodationVo::getFkAreaCountryId).collect(Collectors.toSet());
        //获取学生集合的所有州省ids
        Set<Long> stateIds = collect.stream().map(StudentAccommodationVo::getFkAreaStateId).collect(Collectors.toSet());
        //获取学生集合的所有城市ids
        Set<Long> cityIds = collect.stream().map(StudentAccommodationVo::getFkAreaCityId).collect(Collectors.toSet());
        Set<Long> fkAgentIds = collect.stream().map(StudentAccommodationVo::getFkAgentId).collect(Collectors.toSet());
        Set<Long> fkStaffIds = collect.stream().map(StudentAccommodationVo::getFkStaffId).collect(Collectors.toSet());
        Set<String> currencyTypeNumAccommodations = collect.stream().map(StudentAccommodationVo::getFkCurrencyTypeNumAccommodation).collect(Collectors.toSet());
        Set<String> currencyTypeNumCommissions = collect.stream().map(StudentAccommodationVo::getFkCurrencyTypeNumCommission).collect(Collectors.toSet());
        //根据币种ids获取名称
        Map<String, String> currencyAccommodationNamesByNums = new HashMap<>();
        if (GeneralTool.isNotEmpty(currencyTypeNumAccommodations)) {
            Result<Map<String, String>> result = financeCenterClient.getCurrencyNamesByNums(currencyTypeNumAccommodations);
            if (result.isSuccess() && result.getData() != null) {
                currencyAccommodationNamesByNums = result.getData();
            }
        }
        //根据币种ids获取名称
        Map<String, String> currencyCommissionsNamesByNums = new HashMap<>();
        if (GeneralTool.isNotEmpty(currencyTypeNumCommissions)) {
            Result<Map<String, String>> result = financeCenterClient.getCurrencyNamesByNums(currencyTypeNumCommissions);
            if (result.isSuccess() && result.getData() != null) {
                currencyCommissionsNamesByNums = result.getData();
            }
        }
        //根据代理ids获取名称
        Map<Long, String> agentNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkAgentIds)) {
            agentNamesByIds = agentService.getAgentNamesByIds(fkAgentIds);
        }
        //代理标签
        Map<Long, List<AgentLabelVo>> agentLabelMap = getAgentLabelDataUtils.getAgentLabelMap(fkAgentIds).getAgentLabelMap();
        //根据员工ids获取姓名
        Map<Long, String> staffNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkStaffIds)) {
            Result<Map<Long, String>> staffNameResult = permissionCenterClient.getStaffNameMap(fkStaffIds);
            if (staffNameResult.isSuccess() && GeneralTool.isNotEmpty(staffNameResult)) {
                staffNamesByIds = staffNameResult.getData();
            }
        }
        //根据国家ids获取国家名称
        Map<Long, String> countryNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(countryIds)) {
            Result<Map<Long, String>> result = institutionCenterClient.getCountryNamesByIds(countryIds);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                countryNamesByIds = result.getData();
            }
        }
        //根据州省ids获取州省名称
        Map<Long, String> stateNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(stateIds)) {
            Result<Map<Long, String>> result = institutionCenterClient.getStateFullNamesByIds(stateIds);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                stateNamesByIds = result.getData();
            }
        }
        Set<Long> companyIds = collect.stream().map(StudentAccommodationVo::getFkCompanyId).collect(Collectors.toSet());
        //根据公司ids获取公司名称
        Map<Long, String> companyNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(companyIds)) {
            Result<Map<Long, String>> result = permissionCenterClient.getCompanyNamesByIds(companyIds);
            if (result.isSuccess() && result.getData() != null) {
                companyNamesByIds = result.getData();
            }
        }
        //根据城市ids获取城市名称
        Map<Long, String> cityNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(cityIds)) {
            Result<Map<Long, String>> result = institutionCenterClient.getCityFullNamesByIds(cityIds);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                cityNamesByIds = result.getData();
            }
        }
        Map<Long, List<Long>> empty = new HashMap<>();
        Set<Long> ids = collect.stream().map(StudentAccommodationVo::getId).filter(Objects::nonNull).collect(Collectors.toSet());
        List<AccommodationPayFormDetailVo> paymentFormItemList = studentAccommodationMapper.getPaidAmountByIds(ids);
        Map<Long, List<AccommodationPayFormDetailVo>> payFormDetailMap = null;
        if (GeneralTool.isNotEmpty(paymentFormItemList)){
            payFormDetailMap = paymentFormItemList.stream().collect(Collectors.groupingBy(AccommodationPayFormDetailVo::getAccommodationId));
        }

        setNameList(collect, countryNamesByIds, empty, empty, companyNamesByIds, stateNamesByIds, cityNamesByIds,
                agentNamesByIds, staffNamesByIds, currencyAccommodationNamesByNums, currencyCommissionsNamesByNums,payFormDetailMap,agentLabelMap);
//        for (StudentAccommodationVo studentAccommodationDto : collect) {
//            setNameList(studentAccommodationDto, countryNamesByIds, stateNamesByIds, cityNamesByIds,
//                    agentNamesByIds, staffNamesByIds, currencyAccommodationNamesByNums, currencyCommissionsNamesByNums);
//            String companyName = companyNamesByIds.get(studentAccommodationDto.getFkCompanyId());
//            studentAccommodationDto.setFkCompanyName(companyName);
//            if (GeneralTool.isNotEmpty(studentAccommodationDto.getFkStudentId())) {
//                studentAccommodationDto.setStudentName(studentService.getStudentNameById(studentAccommodationDto.getFkStudentId()));
//            }
//        }，
        List<StudentAccommodationExportVo> insuranceExportDtos = collect.stream().map(c -> {
            StudentAccommodationExportVo studentAccommodationExportVo = BeanCopyUtils.objClone(c, StudentAccommodationExportVo::new);
            if (GeneralTool.isNotEmpty(c.getAgentLabelVos())) {
                studentAccommodationExportVo.setAgentLabelNames(
                        c.getAgentLabelVos().stream()
                                // 给每个标签名称包裹【】符号
                                .map(vo -> "【" + vo.getLabelName() + "】")
                                // 用逗号拼接所有带符号的标签
                                .collect(Collectors.joining(" "))
                );
            }
            return studentAccommodationExportVo;
        }).collect(Collectors.toList());
        FileUtils.exportExcelNotWrapText(response, insuranceExportDtos, "StudentAccommodation", StudentAccommodationExportVo.class);
    }
}

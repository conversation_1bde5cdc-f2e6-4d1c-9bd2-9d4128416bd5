package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.service.GetService;
import com.get.salecenter.vo.*;
import com.get.salecenter.entity.StaffCommissionAction;
import com.get.salecenter.dto.*;

import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2023/2/6 10:52
 * @verison: 1.0
 * @description:
 */
public interface IStaffCommissionActionService extends GetService<StaffCommissionAction> {
    /**
     * 确认结算
     * @param staffCommissionActionDtos
     */
    void confirmSettlement(List<StaffCommissionActionDto> staffCommissionActionDtos);

    /**
     * 匹配规则
     * @param studentOfferItemCommissionInfoDto
     * @return
     */
    List<StaffCommissionConfirmVo> getStudentOfferItemCommissionInfo(StudentOfferItemCommissionInfoDto studentOfferItemCommissionInfoDto);

    /**
     * 未确认/已确认/已结算列表信息查看
     * studentOfferItemCommissionInfoDto
     * @param studentOfferItemCommissionInfoDto
     * @return
     */
    SettlementInfoVo getSettlementInfo(StudentOfferItemCommissionInfoDto studentOfferItemCommissionInfoDto);

    /**
     * 取消结算
     * @param studentId
     * @param commissionStep
     */
    void cancelSettlement(Long studentId, String commissionStep);

    /**
     * 结算汇总
     * @param staffCommissionSummaryDto
     * @return
     */
    List<StaffCommissionSummaryVo> getStaffCommissionSummary(StaffCommissionSummaryDto staffCommissionSummaryDto, List<StaffCommissionDatasVo> staffCommissionDatasVos);

    /**
     * 获取表头数据
     * @return
     */
    List<StaffCommissionDatasVo> getStaffCommissionStepTitles(Long companyId);

    /**
     * 汇总列表取消结算
     * @param studentId
     */
    void cancelSettlementInfo(Long studentId);

    /**
     * 汇总列表结案
     * @param studentId
     */
    void completeSettlement(Long studentId);

    /**
     * 提成员工列表
     * @param commissionStaffDatasDto
     * @return
     */
    List<CommissionStaffDatasVo> getCommissionStaffDatas(CommissionStaffDatasDto commissionStaffDatasDto, List<StaffCommissionDatasVo> staffCommissionDatasVos);

    /**
     * 批量结算
     * @param batchSettlementCommissionDtos
     */
    Boolean batchSettlementCommission(List<BatchSettlementCommissionDto> batchSettlementCommissionDtos);

    /**
     * 代理下拉
     * @param companyId
     * @return
     */
    List<BaseSelectEntity> getStaffCommissionAgentList(Long companyId);

    /**
     * 跟进项目员工下拉
     * @param companyId
     * @return
     */
    List<BaseSelectEntity> getStaffCommissionStaffList(Long companyId);

    /**
     * 取消结算单条
     */
    void cancelSettlementSingle(Long studentId, Long studentOfferItemId, String commissionStep);

    /**
     * 员工统计
     * @param staffSettlementStatisticsDto
     * @param staffCommissionDatasVos
     * @return
     */
    StaffSettlementStatisticsVo getStaffSettlementStatistics(StaffSettlementStatisticsDto staffSettlementStatisticsDto, List<StaffCommissionDatasVo> staffCommissionDatasVos);

    /**
     * 员工结算统计导出
     * @param response
     * @param staffSettlementStatisticsDto
     * @param staffCommissionDatasVos
     */
    void exportStaffSettlementStatistics(HttpServletResponse response, StaffSettlementStatisticsDto staffSettlementStatisticsDto, List<StaffCommissionDatasVo> staffCommissionDatasVos);

    /**
     * getStaffSettlementRefundList
     * @param staffCommissionRefundDto
     * @param page
     * @return
     */
    List<StaffCommissionRefundVo> getStaffSettlementRefundList(StaffCommissionRefundDto staffCommissionRefundDto, Page page) throws ParseException;

    /**
     * 结算日期下拉框
     * @param companyId
     * @return
     */
    List<BaseSelectEntity> getSettlementDateSelect(Long companyId);

    /**
     * 批量退款审核
     * @param staffCommissionRefundVo
     */
    void batchRefundReview(StaffCommissionBatchRefundDto staffCommissionRefundVo);

    /**
     * 批量退款结算
     * @param staffCommissionRefundVo
     */
    void batchRefundSettlement(StaffCommissionBatchRefundDto staffCommissionRefundVo);

    /**
     * 项目成员业绩结算列表Excel
     * @param response
     * @param staffCommissionRefundDto
     */
    void exportStaffSettlementRefundExcel(HttpServletResponse response, StaffCommissionRefundDto staffCommissionRefundDto) throws ParseException;

    /**
     * 合并学生业绩结算数据
     * @param mergedStudentId
     * @param targetStudentId
     */
    void mergeData(Long mergedStudentId, Long targetStudentId);

    StaffCommissionRefundEchoVo refundSettlementConditionalEcho(StaffCommissionRefundDto staffCommissionRefundDto);

    StaffCommissionRefundSummaryVo getStaffSettlementRefundSummaryList(StaffCommissionRefundDto staffCommissionRefundDto);

    void downStaffSettlementRefundSummaryList(StaffCommissionRefundDto staffCommissionRefundDto, HttpServletResponse response);

    StaffCommissionRefundRoleVo getStaffSettlementRefundRoleSumarry(StaffCommissionRefundDto staffCommissionRefundDto) throws ParseException;
}

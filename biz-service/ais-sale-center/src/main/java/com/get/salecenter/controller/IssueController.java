package com.get.salecenter.controller;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.jackson.JsonUtil;
import com.get.salecenter.service.IIssueService;
import com.get.salecenter.dto.AppUserDto;
import com.get.salecenter.dto.NewIssueUserSuperiorDto;
import com.get.salecenter.dto.OrderDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Api(tags = "ISSUE系统接口管理")
@RestController
@RequestMapping("sale/issuesys")
@VerifyPermission(IsVerify = false)
public class IssueController {
    //TODO 注释ISSUE相关功能 lucky  2024/12/23

    /**
     * ISSUE请求地址
     */
//    @Value("${issue.url}")
//    private String url;
//
//    /**
//     * 新ISSUE请求地址
//     */
//    @Value("${newissue.url}")
//    private String newIssueUrl;
//
//    /**
//     * GEAR请求地址
//     */
//    @Value("${gear.url}")
//    private String gearUrl;
//
//    @Resource
//    private IIssueService iIssueService;
//
//
//    @ApiOperation(value = "添加备注接口", notes = "添加备注")
//    @GetMapping("/stu_appl/updateBakupByOrderId")
//    public String updateBakupByOrderId(@RequestParam(value = "order_id", required = false) String order_id,
//                                       @RequestParam(value = "backup", required = false) String backup) {
//        Map<String, Object> queries = new HashMap<>();
//        queries.put("order_id", order_id);
//        queries.put("backup", backup);
//        return HttpUtil.get(url + "/stu_appl/updateBakupByOrderId", queries, 60000);//1分钟
//    }
//
//    @ApiOperation(value = "一键申请(接旧issue)接口", notes = "一键申请(接旧issue)接口")
//    @GetMapping("/isutocpp/sendTORobot")
//    public String sendTORobot(@RequestParam(value = "createrid", required = false) Integer createrid,
//                              @RequestParam(value = "cdet_id", required = false) String cdet_id,
//                              @RequestParam(value = "stu_source", required = false) String stu_source) {
//        Map<String, Object> queries = new HashMap<>();
//        queries.put("createrid", createrid);
//        queries.put("cdet_id", cdet_id);
//        queries.put("stu_source", stu_source);
//        return HttpUtil.get(url + "/isutocpp/sendTORobot", queries, 60000);//1分钟
//    }
//
//
//    @ApiOperation(value = "资料有误接口", notes = "资料有误")
//    @GetMapping("/isutocpp/sendRejectMsg")
//    public String sendRejectMsg(@RequestParam(value = "result_msg", required = false) String result_msg,
//                                @RequestParam(value = "cdet_id", required = false) String cdet_id,
//                                @RequestParam(value = "courseplan_id", required = false) String courseplan_id,
//                                @RequestParam(value = "bmsContactPersonEmail", required = false) String bmsContactPersonEmail,
//                                @RequestParam(value = "disposeType", required = false) String disposeType) {
//        Map<String, Object> queries = new HashMap<>();
//        queries.put("result_msg", result_msg);
//        queries.put("cdet_id", cdet_id);
//        queries.put("courseplan_id", courseplan_id);
//        queries.put("bmsContactPersonEmail", bmsContactPersonEmail);
//        queries.put("disposeType", disposeType);
//        return HttpUtil.get(url + "/isutocpp/sendRejectMsg", queries);
//    }
//
//
//    @ApiOperation(value = "一键停止申请接口", notes = "一键停止申请")
//    @GetMapping("/isutocpp/stopOrderByOrderId")
//    public String stopOrderByOrderId(@RequestParam(value = "orderId", required = false) String orderId,
//                                     @RequestParam(value = "infoMsg", required = false) String infoMsg) {
//        Map<String, Object> queries = new HashMap<>();
//        queries.put("orderId", orderId);
//        queries.put("infoMsg", infoMsg);
//        return HttpUtil.get(url + "/isutocpp/stopOrderByOrderId", queries);
//    }
//
//    @ApiOperation(value = "一键创建issue账号接口校验接口", notes = "一键创建issue账号接口校验")
//    @GetMapping("/isutocpp/createIsuAgeng")
//    public String createIsuAgeng(@RequestParam(value = "agencyId", required = false) String agencyId,
//                                 @RequestParam(value = "type", required = false) String type) {
//        Map<String, Object> queries = new HashMap<>();
//        queries.put("agencyId", agencyId);
//        queries.put("type", type);
//        return HttpUtil.get(url + "/isutocpp/createIsuAgeng", queries);
//    }
//
//
//    @ApiOperation(value = "查看issue账号密码接口", notes = "查看issue账号密码")
//    @GetMapping("/isutocpp/getAccountToBms")
//    public String getAccountToBms(@RequestParam(value = "bmsAgentId", required = false) Integer bmsAgentId,
//                                  @RequestParam(value = "cppAgentId", required = false) Integer cppAgentId) {
//        Map<String, Object> queries = new HashMap<>();
//        queries.put("bmsAgentId", bmsAgentId);
//        queries.put("cppAgentId", cppAgentId);
//        queries.put("callback", "callback");
//        return HttpUtil.get(url + "/isutocpp/getAccountToBms", queries);
//    }
//
//
//    @ApiOperation(value = "获取新issue区号", notes = "获取新issue区号")
//    @GetMapping("/issue/studentCountry/getPhoneRegion")
//    public ResponseBo getPhoneRegion() {
//        return JSONObject.parseObject(HttpUtil.get(newIssueUrl + "/issue/studentCountry/getPhoneRegion"), ResponseBo.class);
//    }
//
//    @ApiOperation(value = "新issue一键网申", notes = "新issue一键网申")
//    @PostMapping("/issue/robot/getStartRobot")
//    public String getStartRobot(@RequestBody OrderDto orderDto) {
//
//        JSONObject jsonObject = new JSONObject();
//        jsonObject.put("issueCourseId", orderDto.getIssueCourseId());
//        jsonObject.put("key", orderDto.getKey());
//        jsonObject.put("itemId", orderDto.getItemId());
//        jsonObject.put("creatId", orderDto.getCreatId());
//        jsonObject.put("creatName", orderDto.getCreatName());
//        jsonObject.put("fkInstitutionId", orderDto.getFkInstitutionId());
//        jsonObject.put("fkInstitutionFacultyId", orderDto.getFkInstitutionFacultyId());
//        jsonObject.put("fkInstitutionZoneId", orderDto.getFkInstitutionZoneId());
//        jsonObject.put("stuContPerEmailG", orderDto.getStuContPerEmailG());
//        jsonObject.put("stuContPerNameG", orderDto.getStuContPerNameG());
//        jsonObject.put("stuContPerEmail", orderDto.getStuContPerEmail());
//        jsonObject.put("stuContPerName", orderDto.getStuContPerName());
//        jsonObject.put("stuSource", orderDto.getStuSource());
//        Optional.ofNullable(orderDto.getStuSource()).ifPresent(d -> jsonObject.put("stuSource", d));
//        HttpRequest httpRequest = new HttpRequest(newIssueUrl + "/issue/robot/getStartRobot");
//        String body = httpRequest.body(jsonObject.toJSONString().toString()).header("Content-Type", "application/json").execute().body();
//        return body;
//
//    }
//
//    @ApiOperation(value = "新issue一键网申停止", notes = "新issue一键网申")
//    @GetMapping("/issue/robot/getStopRobot")
//    public String getStopRpa(@RequestParam("fkRpaOrderId") Integer fkRpaOrderId,
//                             @RequestParam("infoMsg") String infoMsg, @RequestParam("stuSource") String stuSource) {
//        Map<String, Object> queries = new HashMap<>();
//        queries.put("fkRpaOrderId", fkRpaOrderId);
//        queries.put("infoMsg", infoMsg);
//        queries.put("stuSource", stuSource);
//        return HttpUtil.get(newIssueUrl + "/issue/robot/getStopRobot", queries);
//        /* return JSONObject.parseObject(s, ResponseBo.class);*/
//
//    }
//
//
//    @ApiOperation(value = "新issue返回课程链路信息infoJson", notes = "")
//    @PostMapping("/issue/robot/getJsonInfo")
//    public String getJsonInfo(@RequestBody OrderDto orderDto) {
//        JSONObject jsonObject = new JSONObject();
//        jsonObject.put("issueCourseId", orderDto.getIssueCourseId());
//        jsonObject.put("stuSource", orderDto.getStuSource());
//        Optional.ofNullable(orderDto.getStuSource()).ifPresent(d -> jsonObject.put("stuSource", d));
//        HttpRequest httpRequest = new HttpRequest(newIssueUrl + "/issue/robot/getJsonInfo");
//        String body = httpRequest.body(jsonObject.toJSONString().toString()).header("Content-Type", "application/json").execute().body();
//        return body;
//    }
//
//    @ApiOperation(value = "返回BMS是否需要转换纯图PDF的标记", notes = "")
//    @GetMapping("/issue/robot/getInstitutionCoverImageFlag")
//    public String getInstitutionCoverImageFlag(@RequestParam("fkInstitutionId") Long fkInstitutionId) {
//        Map<String, Object> queries = new HashMap<>();
//        queries.put("fkInstitutionId", fkInstitutionId);
//        return HttpUtil.get(newIssueUrl + "/issue/robot/getInstitutionCoverImageFlag", queries);
//    }
//
//    @ApiOperation("添加备注")
//    @GetMapping("/issue/robot/updateBakupByNewIssueOrderId")
//    public String updateBakupByNewIssueOrderId(@RequestParam("orderId") Integer orderId, @RequestParam("msg") String msg) {
//        Map<String, Object> queries = new HashMap<>();
//        queries.put("orderId", orderId);
//        queries.put("msg", msg);
//        return HttpUtil.get(newIssueUrl + "/issue/robot/updateBakupByNewIssueOrderId", queries);
//    }
//
//    @ApiOperation("学生入学申请页全部表单回显")
//    @GetMapping("/issue/student/getStudentApply")
//    public ResponseBo getStudentApply(@RequestParam("fkStudentId") Long stuId) {
//        Map<String, Object> queries = new HashMap<>();
//        queries.put("fkStudentId", stuId);
//        return JSONObject.parseObject(HttpUtil.get(newIssueUrl + "/issue/student/getStudentApply", queries), ResponseBo.class);
//    }
//
//
//    @ApiOperation("个人信息获取母语下拉框")
//    @GetMapping("/issue/student/getModerLanguageData")
//    public ResponseBo getModerLanguageData() {
//        return JSONObject.parseObject(HttpUtil.get(newIssueUrl + "/issue/student/getModerLanguageData"), ListResponseBo.class);
//    }
//
//    @ApiOperation("名族血统下拉框")
//    @GetMapping("/issue/student/getStudentAncestry")
//    public ResponseBo getStudentAncestry() {
//        return JSONObject.parseObject(HttpUtil.get(newIssueUrl + "/issue/student/getStudentAncestry"), ListResponseBo.class);
//    }
//
//    @ApiOperation("修改学生提交状态")
//    @GetMapping("/issue/student/updateStudentStatus")
//    public ResponseBo updateStudentStatus(@RequestParam(value = "isEmail", required = false) boolean isEmail,
//                                          @RequestParam(value = "email", required = false) String email,
//                                          @RequestParam("fkStudentId") Long fkStudentId,
//                                          @RequestParam(value = "msg", required = false) String msg,
//                                          @RequestParam(value = "stuSource") String stuSource) {
//        Map<String, Object> queries = new HashMap<>();
//        queries.put("email", email);
//        queries.put("fkStudentId", fkStudentId);
//        queries.put("msg", msg);
//        queries.put("isEmail", isEmail);
//        queries.put("stuSource", stuSource);
//        ResponseBo responseBo = JSONObject.parseObject(HttpUtil.get(newIssueUrl + "/issue/student/updateStudentStatus", queries), ResponseBo.class);
//        return responseBo;
//    }
//
//    @ApiOperation("app国家下拉框")
//    @GetMapping("/issue/student/getAppCountry")
//    public ResponseBo getAppCountry() {
//        return JSONObject.parseObject(HttpUtil.get(newIssueUrl + "/issue/student/getAppCountry"), ListResponseBo.class);
//    }
//
//    @ApiOperation("注册issue账号")
//    @PostMapping("/issue/student/getIssueRegister")
//    public String getIssueRegister(@RequestBody AppUserDto appUserDto) {
//        HttpRequest httpRequest = new HttpRequest(newIssueUrl + "/appUser/issueRegister");
//        return httpRequest.body(JsonUtil.toJson(appUserDto)).header("Content-Type", "application/json").execute().body();
//    }
//
//    @ApiOperation("注册gear账号")
//    @PostMapping("/issue/student/getGearRegister")
//    public String getGearRegister(@RequestBody AppUserDto appUserDto) {
//        HttpRequest httpRequest = new HttpRequest(gearUrl + "/appUser/gearRegister");
//        return httpRequest.body(JsonUtil.toJson(appUserDto)).header("Content-Type", "application/json").execute().body();
//    }
//
//
//    @ApiOperation(value = "更改旧issue学生代理", notes = "更改旧issue学生代理")
//    @GetMapping("/stu_appl/updateStudentAgent")
//    public ResponseBo updateStudentAgent(@RequestParam("fkAgentId") Integer fkAgentId, @RequestParam("fkSutdentId") Integer fkSutdentId) {
//        Map<String, Object> queries = new HashMap<>();
//        queries.put("fkAgentId", fkAgentId);
//        queries.put("fkSutdentId", fkSutdentId);
//        HttpUtil.get(url + "/stu_appl/updateStudentAgent", queries);
//        return ResponseBo.ok();
//    }
//
//
//    @PostMapping("getSetIssueUserSuperior")
//    @ApiOperation("设置issue用户上级")
//    public ResponseBo getSetIssueUserSuperior(@RequestParam("fkUserId") Long fkUserId,
//                                              @RequestParam("fkCompanyId") Long fkCompanyId,
//                                              @RequestBody List<NewIssueUserSuperiorDto> userSuperiorVos) {
//        iIssueService.getSetIssueUserSuperior(fkUserId, userSuperiorVos, fkCompanyId);
//        return ResponseBo.ok();
//    }
//
////    @GetMapping("getIssueUserSuperior")
////    @ApiOperation("查看user能绑定与已绑定的上司列表")
////    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/代理管理/绑定issue上下级")
////    public ResponseBo getIssueUserSuperior(@RequestParam("fkUserId") Long fkUserId,
////                                           @RequestParam("fkAgentId") Long fkAgentId,
////                                           @RequestParam("fkCompanyId") Long fkCompanyId) {
////        NewIssueUserSuperiorVo issueUserSuperior = iIssueService.getIssueUserSuperior(fkUserId, fkAgentId, fkCompanyId);
////        return new ResponseBo(issueUserSuperior);
////    }
//
//    @PostMapping("getRemoveIssueUserSuperior")
//    @ApiOperation("解除Issue用户上级")
//    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/代理管理/解除issue上下级")
//    public ResponseBo getRemoveIssueUserSuperior(@RequestParam("fkUserId") Long fkUserId,
//                                                 @RequestParam("fkCompanyId") Long fkCompanyId,
//                                                 @RequestBody List<NewIssueUserSuperiorDto> userSuperiorVos) {
//        iIssueService.getRemoveIssueUserSuperior(fkUserId, userSuperiorVos, fkCompanyId);
//        return ResponseBo.ok();
//    }
//
////    @GetMapping("getIssueUserSubordinate")
////    @ApiOperation("查看issue用户下属")
////    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/代理管理/查看issue用户下属")
////    public ResponseBo getIssueUserSubordinate(@RequestParam("fkUserId") long fkUserId,
////                                              @RequestParam("fkAgentId") long fkAgentId,
////                                              @RequestParam("fkCompanyId") Long fkCompanyId) {
////        return new ListResponseBo(iIssueService.getIssueUserSubordinate(fkUserId, fkAgentId, fkCompanyId));
////    }
//
//    @GetMapping("getUpdateIssueStudentAgent")
//    @ApiOperation("更新issue学生代理")
//    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/ISSUE系统接口管理/更新issue学生代理")
//    public ResponseBo getUpdateIssueStudentAgent(@RequestParam("fkUserId") Long fkUserId,
//                                                 @RequestParam("fkAgentId") Long fkAgentId,
//                                                 @RequestParam("fkIssueStudentId") Long fkIssueStudentId,
//                                                 @RequestParam("stuSource") String stuSource) {
//        Map<String, Object> queries = new HashMap<>();
//        queries.put("fkUserId", fkUserId);
//        queries.put("fkAgentId", fkAgentId);
//        queries.put("fkIssueStudentId", fkIssueStudentId);
//        queries.put("stuSource", stuSource);
//        return JSONObject.parseObject(HttpUtil.get(newIssueUrl + "/issue/student/getUpdateIssueStudentAgent", queries), ResponseBo.class);
//    }


}

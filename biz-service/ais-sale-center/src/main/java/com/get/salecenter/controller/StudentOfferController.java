package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.redis.lock.RedisLock;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dto.StudentInfoDto;
import com.get.salecenter.dto.StudentOfferDto;
import com.get.salecenter.dto.StudentOfferProjectUpdateDto;
import com.get.salecenter.dto.query.StudentOfferListQueryDto;
import com.get.salecenter.service.IStudentOfferService;
import com.get.salecenter.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/11/13
 * @TIME: 17:27
 * @Description:
 **/

@Api(tags = "学生申请方案管理")
@RestController
@RequestMapping("sale/studentOffer")
@Slf4j
public class StudentOfferController {
    @Resource
    private IStudentOfferService offerService;

    @ApiOperation(value = "同国家线和代理的验证")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER)
    @PostMapping("verifyStudentOffer")
    public ResponseBo<Boolean> verifyStudentOffer(@RequestBody StudentOfferDto offerVo) {
        return new ResponseBo<>(offerService.verifyStudentOffer(offerVo));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentOfferVo>
     * @Description: 查询学生学习申请方案列表
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "num代理编号 name代理名称")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生申请方案管理/查询学生学习申请方案列表")
    @PostMapping("datas")
    public ResponseBo<StudentOfferVo> datas(@RequestBody SearchBean<StudentOfferDto> page) {
        log.info("===>用户id{}姓名{},请求StudentOfferController的datas接口：{}", SecureUtil.getStaffId(), SecureUtil.getStaffName(), GeneralTool.toJson(page));
        List<StudentOfferVo> datas = offerService.getStudentOfferNew(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }


    /**
     *
     * @param studentId
     * @Description: 状态变更详情
     * @return
     */
    @ApiOperation(value = "状态变更详情", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生申请方案管理/状态变更详情")
    @GetMapping("/getStatusChangeDetails")
    public ResponseBo<StudentStepHistoryVo> datas(@RequestParam(value = "studentId") Long studentId) {
        List<StudentStepHistoryVo> datas = offerService.getStatusChangeDetails(studentId);
        return new ListResponseBo<>(datas);
    }



    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 新增信息
     * @Param [StudentOfferVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生申请方案管理/新增学生")
    @PostMapping("add")
    @RedisLock(value="fzh:offerAddLock",param = "#offerVo.fkStudentId",waitTime = 10L)
    public ResponseBo add(@RequestBody @Validated(StudentOfferDto.Add.class) StudentOfferDto offerVo) {
        return SaveResponseBo.ok(offerService.addOffer(offerVo));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentOfferVo>
     * @Description: 修改信息
     * @Param [StudentOfferVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生申请方案管理/更新学生")
    @PostMapping("update")
    public ResponseBo<StudentOfferVo> update(@RequestBody @Validated(StudentOfferDto.Update.class) StudentOfferDto offerVo) {
        return UpdateResponseBo.ok(offerService.updateOffer(offerVo));
    }


    /**
     * @Description: feign调用 修改学生申请方案状态
     * @Author: Jerry
     * @Date:17:35 2021/11/3
     */
    @ApiIgnore
    @PostMapping("/updateStudentOffer")
    @VerifyPermission(IsVerify = false)
    public void updateStudentOffer(@RequestBody StudentOfferVo studentOffer) {
        offerService.updateStudentOffer(studentOffer);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentOfferVo>
     * @Description: 详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id；返回字段status：0关闭/1打开/2终止/3成功结案")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/学生申请方案管理/学生详情")
    @GetMapping("/{id}")
    public ResponseBo<StudentOfferVo> detail(@PathVariable("id") Long id) {
        StudentOfferVo data = offerService.findOfferById(id);
        return new ResponseBo<>(data);
    }

    /**
     * 代理联系人邮箱下拉
     *
     * @param fkAgentId
     * @return
     */
    @ApiOperation(value = "代理联系人邮箱下拉", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生申请方案管理/代理联系人邮箱下拉")
    @GetMapping("/getContactPersonEmailSelect")
    @VerifyPermission(IsVerify = false)
    public ResponseBo<BaseSelectEntity> getContactPersonEmailSelect(@RequestParam(value = "fkAgentId") Long fkAgentId) {
        return new ListResponseBo(offerService.getContactPersonEmailSelect(fkAgentId));
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "代理联系人邮箱下拉", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生申请方案管理/代理联系人邮箱下拉（新）")
    @GetMapping("/getAgentEmailSelect")
    public ResponseBo<String> getAgentEmailSelect(@RequestParam(value = "fkAgentId") Long fkAgentId) {
        return new ListResponseBo<>(offerService.getAgentEmailSelect(fkAgentId));
    }


    @ApiOperation(value = "代理联系人电话区号下拉", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生申请方案管理/代理联系人电话区号下拉")
    @GetMapping("/getContactPersonMobileAreaCodeSelect")
    @VerifyPermission(IsVerify = false)
    public ResponseBo<BaseSelectEntity> getContactPersonMobileAreaCodeSelect(@RequestParam(value = "fkAgentId") Long fkAgentId) {
        return new ListResponseBo(offerService.getContactPersonMobileAreaCodeSelect(fkAgentId));
    }


    @ApiOperation(value = "代理联系人电话下拉", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生申请方案管理/代理联系人电话下拉")
    @GetMapping("/getContactPersonMobileSelect")
    @VerifyPermission(IsVerify = false)
    public ResponseBo<ContactPersonMobileSelectVo> getContactPersonMobileSelect(@RequestParam(value = "fkAgentId") Long fkAgentId) {
        return new ListResponseBo(offerService.getContactPersonMobileSelect(fkAgentId));
    }

    /**
     * @Description: feign调用 详情
     * @Author: Jerry
     * @Date:17:35 2021/11/3
     */
    @ApiIgnore
    @GetMapping("/getStudentOfferDetail")
    @VerifyPermission(IsVerify = false)
    public StudentOfferVo getStudentOfferDetail(@RequestParam("id") Long id) {
        return offerService.getStudentOfferDetail(id);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 删除信息
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/学生申请方案管理/删除学生")
    @GetMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        offerService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 关闭方案
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "关闭方案", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/学生申请方案管理/关闭方案")
    @PostMapping("unableOffer")
    public ResponseBo unableOffer(@RequestParam("id") Long id, @RequestParam("status") Long status) {
        offerService.unableOffer(id, status);
        return DeleteResponseBo.ok();
    }

    @ApiOperation(value = "激活方案检验", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER)
    @GetMapping("activationVerifyOffer/{id}")
    public ResponseBo<Boolean> activationVerifyOffer(@PathVariable("id") Long id) {
        return new ResponseBo<>(offerService.activationVerifyOffer(id));
    }

    @ApiOperation(value = "激活方案", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生申请方案管理/激活方案")
    @GetMapping("activationOffer/{id}")
    public ResponseBo activationOffer(@PathVariable("id") Long id) {
        offerService.activationOffer(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 学生申请方案下拉
     * @Param [providerId]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "学生申请方案下拉", notes = "")
    @PostMapping("getStudentOfferSelect")
    public ResponseBo<BaseSelectEntity> getStudentOfferSelect(@RequestParam(value = "studentId") Long studentId,
                                                              @RequestParam(value = "typeKey") String typeKey,
                                                              @RequestParam(required= false,value = "fkCompanyId") Long fkCompanyId) {
        return new ListResponseBo<>(offerService.getStudentOfferSelect(studentId, typeKey,fkCompanyId));
    }

    /**
     * @return
     * @Description：获取学生绑定的代理列表
     * @Param
     * @Date 12:30 2021/4/25
     * <AUTHOR>
     */
    @ApiOperation(value = "代理下拉", notes = "id为学生的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生申请方案管理/代理下拉")
    @VerifyPermission(IsVerify = false)
    @GetMapping("getAgentListByStudentId/{studentId}")
    public ResponseBo<AgentAndAgentLabelVo> getAgentListByStudentId(@PathVariable(value = "studentId") Long studentId) {
        List<AgentAndAgentLabelVo> datas = offerService.getAgentListByStudentId(studentId);
        return new ListResponseBo<>(datas);
    }

    /**
     * 角色员工下拉(角色联动)
     *
     * @Date 15:27 2021/7/6
     * <AUTHOR>
     */
    @ApiOperation(value = "角色员工下拉(角色联动)", notes = "roleId为角色id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生申请方案管理/角色员工下拉")
    @VerifyPermission(IsVerify = false)
    @GetMapping("getRoleStaffByRoleSelect/{roleId}")
    public ResponseBo<BaseSelectEntity> getRoleStaffByRoleSelect(@PathVariable(value = "roleId") Long roleId,@RequestParam(value = "fkAreaCountryId",required = false) Long fkAreaCountryId) {
        List<BaseSelectEntity> datas = offerService.getRoleStaffByRoleSelect(roleId,fkAreaCountryId);
        return new ListResponseBo<>(datas);
    }


    /**
     * @Description: 发起学生申请方案流程
     * @Author: Jerry
     * @Date:17:58 2021/11/3
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "发起学生申请方案流程", notes = "businessKey为申请方案id；procdefKey固定填写m_student_offer；buttonType：0代表申请终止按钮  1代表申请结案按钮")
    @GetMapping("startStudentOfferFlow")
    public ResponseBo startStudentOfferFlow(@RequestParam("businessKey") String businessKey,
                                            @RequestParam("procdefKey") String procdefKey,
                                            @RequestParam("companyId") String companyId,
                                            @RequestParam("buttonType") String buttonType,
                                            @RequestParam(value = "submitReason",required = false) String submitReason,
                                            @RequestParam(value = "fkCancelOfferReasonId",required = false) Long fkCancelOfferReasonId) throws GetServiceException {
        if (businessKey == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        offerService.startStudentOfferFlow(businessKey, procdefKey, companyId, buttonType, submitReason,fkCancelOfferReasonId);
        return ResponseBo.ok();
    }


    /**
     * @Description: 重新提交或放弃流程
     * @Author: Jerry
     * @Date:9:35 2021/11/5
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation("重新提交或放弃流程")
    @GetMapping("getUserSubmit")
    public ResponseBo getUserSubmit(@RequestParam("businessKey") String businessKey, @RequestParam("procdefKey") String procdefKey, @RequestParam("status") String status) throws GetServiceException {
        offerService.getUserSubmit(businessKey, procdefKey, status);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentOfferVo>
     * @Description: 查询学生学习申请方案列表
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "申请列表数据", notes = "num代理编号 name代理名称")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生申请方案管理/查询学生学习申请方案列表")
    @PostMapping("workFolwDatas")
    public ListResponseBo<StudentOfferVo> workFolwDatas(@RequestBody @Validated StudentOfferListQueryDto studentOfferListQueryDto) {
        String[] times = {"0", "0"};//[0]-o-主SQL执行时间,[1]-f-远程调用时间
        List<StudentOfferVo> datas = offerService.getStudentOfferWorkFolwDatas(studentOfferListQueryDto, times);
        return new ListResponseBo<>(datas, times[0], times[1]);
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "留学方案列表分页信息", notes = "num代理编号 name代理名称")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/学生申请方案管理/查询学生学习申请方案列表")
    @PostMapping("getStudentOfferWorkFlowPaginationInfo")
    public ResponseBo getStudentOfferWorkFlowPaginationInfo(@RequestBody SearchBean<StudentOfferListQueryDto> page) {
        return offerService.doGetStudentOfferWorkFlowPaginationInfo(page.getData(), page);
    }



    /**
     * @Description: 绑定代理下拉框
     * @Author: Jerry
     * @Date:12:14 2021/11/11
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation("绑定代理下拉框")
    @GetMapping("getAgentSelect")
    public ResponseBo<BaseSelectEntity> getAgentSelect(@RequestParam("fkCompanyId") Long fkCompanyId) {
        return new ListResponseBo<>(offerService.getAgentSelect(fkCompanyId));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentOfferVo>
     * @Description: 关闭其他申请方案
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "关闭其他申请方案", notes = "关闭其他申请方案")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生申请方案管理/关闭其他申请方案")
    @PostMapping("closeOtherStudentOffers")
    public ResponseBo closeOtherStudentOffers(@RequestParam("studentId") Long studentId) {
        offerService.closeOtherStudentOffers(studentId);
        return ResponseBo.ok();
    }

    /**
     * 国家线下拉框
     *
     * @return
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "国家线下拉框数据", notes = "")
    @GetMapping("getAreaCountryList/{companyId}")
    public ResponseBo getAreaCountryList(@PathVariable("companyId") Long companyId) {
        List<BaseSelectEntity> datas = offerService.getAreaCountryList(companyId);
        return new ListResponseBo<>(datas);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentOfferVo>
     * @Description: 学习申请方案批量分配项目成员
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "学习申请方案批量分配项目成员", notes = "学习申请方案批量分配项目成员")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生申请方案管理/学习申请方案批量分配项目成员")
    @PostMapping("batchDistributeProjectRole")
    public ResponseBo batchDistributeProjectRole(@RequestBody StudentOfferListQueryDto studentOfferListQueryDto) {
        return new ResponseBo<>(offerService.batchDistributeProjectRole(studentOfferListQueryDto));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentOfferVo>
     * @Description: 批量移除项目成员
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "批量移除项目成员", notes = "批量移除项目成员")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生申请方案管理/批量移除项目成员")
    @PostMapping("deletebatchDistributeProjectRole")
    public ResponseBo deletebatchDistributeProjectRole(@RequestBody StudentOfferListQueryDto studentOfferVo) {
        return new ResponseBo<>(offerService.deletebatchDistributeProjectRole(studentOfferVo));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 复制申请方案
     * @Param [StudentOfferVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "复制申请方案", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生申请方案项目管理/复制申请方案")
    @PostMapping("copyOffer")
    public ResponseBo copyOffer(@RequestParam("id") Long id) {
        return SaveResponseBo.ok(offerService.copyOffer(id));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 复制申请方案
     * @Param [StudentOfferVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "撤销学生申请方案流程", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生申请方案项目管理/撤销学生申请方案流程")
    @PostMapping("stopStudentOfferWorkFlow")
    public ResponseBo stopStudentOfferWorkFlow(@RequestParam("id") Long id) {
        offerService.stopStudentOfferWorkFlow(id);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "校验方案下学习计划课程信息")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生申请方案项目管理/校验方案下学习计划课程信息")
    @PostMapping("verifyStudentOfferItemCourse")
    @VerifyPermission(IsVerify = false)
    public ListResponseBo verifyStudentOfferItemCourse(@RequestParam("id") Long id) {
        List<StudentOfferItemVerifyInfoVo> datas =offerService.verifyStudentOfferItemCourse(id);
        return new ListResponseBo<>(datas);
    }

    @ApiOperation(value = "批量学生申请方案项目成员", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生申请方案项目管理/批量学生申请方案项目成员")
    @PostMapping("batchUpdateStudentOfferProjectRole")
    public ResponseBo batchUpdateStudentOfferProjectRole(@RequestBody @Validated StudentOfferProjectUpdateDto studentOfferProjectUpdateDto) {
        offerService.batchUpdateStudentOfferProjectRole(studentOfferProjectUpdateDto);
        return ResponseBo.ok();
    }


    @ApiOperation(value = "补充offer的联系人数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生申请方案项目管理/补充offer的联系人数据")
    @PostMapping("syncContacts")
    @VerifyPermission(IsVerify = false)
    public ResponseBo syncContacts() {
        offerService.syncContacts();
        return ResponseBo.ok();
    }

    @ApiOperation(value = "一键设置缺材料状态", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生申请方案项目管理/一键设置缺材料状态")
    @PostMapping("batchMaterialStatus")
    public ResponseBo batchMaterialStatus(@RequestParam("studentId")Long studentId) {
        offerService.batchMaterialStatus(studentId);
        return ResponseBo.ok();
    }


    @ApiOperation(value = "根据学生名称获取学生绑定方案信息",notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生申请方案项目管理/根据学生名称获取学生绑定方案信息")
    @PostMapping("getOfferBindingList")
    @VerifyLogin(IsVerify = false)
    public ResponseBo<StudentOfferBindingVo> getOfferBindingList(@Validated @RequestBody SearchBean<StudentInfoDto> page){
        List<StudentOfferBindingVo> datas = offerService.getOfferBindingList(page.getData(),page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas,p);
    }
}

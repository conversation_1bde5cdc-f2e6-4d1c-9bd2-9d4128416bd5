package com.get.salecenter.utils;

import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.dao.sale.AgentContractMapper;
import com.get.salecenter.dao.sale.AgentMapper;
import com.get.salecenter.dao.sale.StudentAccommodationMapper;
import com.get.salecenter.dao.sale.StudentInsuranceMapper;
import com.get.salecenter.dao.sale.StudentMapper;
import com.get.salecenter.dao.sale.StudentOfferItemMapper;
import com.get.salecenter.dao.sale.StudentOfferMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class VerifyDataPermissionsUtils {

    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Resource
    private StudentMapper studentMapper;

    @Resource
    private StudentOfferMapper offerMapper;

    @Resource
    private StudentOfferItemMapper offerItemMapper;

    @Resource
    private StudentAccommodationMapper accommodationMapper;

    @Resource
    private StudentInsuranceMapper insuranceMapper;

    @Resource
    private AgentMapper agentMapper;

    @Resource
    private AgentContractMapper agentContractMapper;

    /**
     * 学生
     */
    public final static String STUDENT_O = "STUDENT_O";

    /**
     * 申请方案
     */
    public final static String OFFER_O = "OFFER_O";

    /**
     * 申请计划
     */
    public final static String OFFER_ITEM_O = "OFFER_ITEM_O";

    /**
     * 留学住宿
     */
    public final static String ACC_O = "ACC_O";

    /**
     * 留学保险
     */
    public final static String INS_O = "INS_O";

    /**
     * 代理
     */
    public final static String AGENT_O = "AGENT_O";
    /**
     * 代理合同
     */
    public final static String AGENT_CONTRACT_O = "AGENT_CONTRACT_O";
    /**
     * Author Cream
     * Description :  校验权限防止有老六偷数据
     * Date 2022/6/29 17:31
     * Params: businessId, operation
     * Return: void
     */
    public void verifyByBusinessId(Long businessId, String operation) {
//        Long staffId = SecureUtil.getStaffId();
//        if (GeneralTool.isEmpty(staffId)){
//            throw new GetServiceException(LocaleMessageUtils.getMessage("staff_id_null"));
//        }
//        List<Long> staffFollowerIds = new ArrayList<>();
//        //员工id + 业务下属员工ids
//        Result<List<Long>> result = permissionCenterClient.getStaffFollowerIds(staffId);
//        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
//            staffFollowerIds = result.getData().stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
//        }
//        staffFollowerIds.add(staffId);
//        Long obj;
//        switch (operation) {
//            case STUDENT_O:
//                obj = studentMapper.verifyBDPermission(businessId, staffFollowerIds);
//                break;
//            case OFFER_O:
//                obj = offerMapper.verifyOfferPermissions(businessId,staffFollowerIds);
//                break;
//            case OFFER_ITEM_O:
//                obj = offerItemMapper.verifyOfferItemPermissions(businessId,staffFollowerIds);
//                break;
//            case ACC_O:
//                obj = accommodationMapper.verifyAccPermissions(businessId,staffFollowerIds);
//                break;
//            case INS_O:
//                obj = insuranceMapper.verifyInsPermissions(businessId,staffFollowerIds);
//                break;
//            case AGENT_O:
//                obj = agentMapper.verifyAgentPermissions(businessId,staffFollowerIds);
//                break;
//            case AGENT_CONTRACT_O:
//                obj = agentContractMapper.verifyAgentContractsPermissions(businessId,staffFollowerIds);
//                break;
//            default:
//                throw new GetServiceException(LocaleMessageUtils.getMessage("permissions_insufficient"));
//        }
//        if (obj == null) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("permissions_insufficient"));
//        }
    }


}

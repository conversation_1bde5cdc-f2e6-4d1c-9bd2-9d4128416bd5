package com.get.salecenter.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.core.mybatis.service.GetService;
import com.get.salecenter.entity.EventBillEventSummary;

import java.util.List;

/**
 * @author: Hardy
 * @create: 2022/6/7 18:06
 * @verison: 1.0
 * @description:
 */
public interface IEventBillEventSummaryService extends GetService<EventBillEventSummary> {

    /**
     * 批量新增
     *
     * @param eventBillEventSummaries
     * @return
     */
    Boolean batchAddByIds(List<EventBillEventSummary> eventBillEventSummaries);

    /**
     * 根据条件获取list
     *
     * @param lambdaQueryWrapper
     * @return
     */
    List<EventBillEventSummary> getEventBillEventSummariesByCondition(LambdaQueryWrapper<EventBillEventSummary> lambdaQueryWrapper);
}

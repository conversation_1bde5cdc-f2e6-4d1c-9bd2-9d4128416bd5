package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseService;
import com.get.salecenter.vo.AgentContractAccountVo;
import com.get.salecenter.vo.MediaAndAttachedVo;
import com.get.salecenter.vo.PayablePlanSettlementAgentAccountVo;
import com.get.salecenter.entity.AgentContractAccount;
import com.get.salecenter.dto.AgentContractAccountDto;
import com.get.salecenter.dto.MediaAndAttachedDto;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/9/14
 * @TIME: 18:44
 * @Description:
 **/
public interface IAgentContractAccountService extends BaseService<AgentContractAccount> {

    /**
     * @return java.util.List<com.get.salecenter.vo.AgentContractAccountVo>
     * @Description:查询
     * @Param [agentContractAccountDto, page]
     * <AUTHOR>
     **/
    List<AgentContractAccountVo> getAgentContractAccount(AgentContractAccountDto agentContractAccountDto, Page page);


    /**
     * 根据id查询 详情
     *
     * @param id
     * @return
     */
    AgentContractAccountVo findAgentContractAccountById(Long id);


    /**
     * 新增账户
     *
     * @param contractAccountVo
     * @return
     * @
     */
    Long addContractAccount(AgentContractAccountDto contractAccountVo);

    /**
     * 编辑账户
     *
     * @param contractAccountVo
     * @return
     * @
     */
    AgentContractAccountVo updateContractAccount(AgentContractAccountDto contractAccountVo);

    /**
     * 删除账户
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 根据代理ids 获取银行账户
     *
     * @return
     * @Date 16:55 2021/12/22
     * <AUTHOR>
     */
    Map<Long, List<AgentContractAccountVo>> getAgentContractAccountByAgentIds(List<Long> agentIds);

//    /**
//     * 根据应付计划ids 获取结算标记
//     *
//     * @Date 12:07 2022/1/11
//     * <AUTHOR>
//     */
//    Map<Long, List<PayablePlanSettlementAgentAccountVo>> getSettlementMarkByPayablePlanIds(List<Long> payablePlanIds);


    /**
     * 银行账户下拉框
     * @param fkTypeKey
     * @param fkTargetId
     * @return
     */
    List<BaseSelectEntity> getContractAccountSelect(String fkTypeKey, Long fkTargetId);

    List<AgentContractAccountVo> getAgentContractAccountSelectById(Long agentId);
    /**
     * feign 根据代理银行账号ids获取银行账号信息
     *
     * @return
     * @Date 16:42 2022/1/6
     * <AUTHOR>
     */
    Map<Long, AgentContractAccountVo> getAgentContractAccountByAccountIds(List<Long> accountIds);

    /**
     * 获取第一条合同信息
     * @param agentId
     * @return
     */
    AgentContractAccount getFirstAgentContractAccount(Long agentId);
    /**
     * feign调用，获取代理账户名称
     *
     * @param id
     * @return
     */
    String getAgentContractBankAccountNameById(Long id);

    String getAgentContractAccountExist(Long id, Long companyId, String bankAccount, String bankAccountNum);

    /**
     * 快捷首选合同
     * @param agentId
     * @param accountId
     * @return
     */
    ResponseBo<Void> quickFirstContractAccount(Long agentId,Long accountId);


    /**
     * 快速激活或屏蔽合同账户
     * @param agentId
     * @param accountId
     * @param status
     * @return
     */
    ResponseBo<Void> quickActivationOrMask(Long agentId,Long accountId,Boolean status);

    Map<Long, Object> getAgentContractPersonMobileByAgentId(Set<Long> agentIds);

    String getContractBankAccountNameById(Long fkBankAccountId, String fkTypeKey);

    List<MediaAndAttachedVo> addMedia(List<MediaAndAttachedDto> mediaAndAttachedDtos);
}

package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.service.GetService;
import com.get.salecenter.vo.AppAgentContractAccountVo;
import com.get.salecenter.vo.MediaAndAttachedVo;
import com.get.salecenter.entity.AppAgentContractAccount;
import com.get.salecenter.dto.AppAgentContractAccountAddDto;
import com.get.salecenter.dto.AppAgentContractAccountListDto;
import com.get.salecenter.dto.AppAgentContractAccountUpdateDto;
import com.get.salecenter.dto.MediaAndAttachedDto;

import java.util.List;

/**
 * @author: Hardy
 * @create: 2022/11/18 11:00
 * @verison: 1.0
 * @description:
 */
public interface IAppAgentContractAccountService extends GetService<AppAgentContractAccount> {

    /**
     * 查询列表
     * @param appAgentContractAccountListDto
     * @param page
     * @return
     */
    List<AppAgentContractAccount> getAppAgentContractAccounts(AppAgentContractAccountListDto appAgentContractAccountListDto, Page page);

    /**
     * 新增
     * @param appAgentContractAccountAddDto
     * @return
     */
    Long addAppAgentContractAccount(AppAgentContractAccountAddDto appAgentContractAccountAddDto);

    /**
     * 详情
     * @param id
     * @return
     */
    AppAgentContractAccountVo findAppAgentContractAccountById(Long id);

    /**
     * 编辑接口
     * @param appAgentContractAccountUpdateDto1
     * @return
     */
    AppAgentContractAccountVo updateAppAgentContractAccount(AppAgentContractAccountUpdateDto appAgentContractAccountUpdateDto1);

    /**
     * 附件上传
     * @param mediaAttachedVo
     * @return
     */
    List<MediaAndAttachedVo> addMedia(List<MediaAndAttachedDto> mediaAttachedVo);
}

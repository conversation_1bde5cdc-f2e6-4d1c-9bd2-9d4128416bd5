package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.salecenter.vo.ConventionPersonVo;
import com.get.salecenter.vo.ConventionTableVo;
import com.get.salecenter.service.IConventionTableService;
import com.get.salecenter.dto.ConventionPersonDto;
import com.get.salecenter.dto.ConventionTableDto;
import com.get.salecenter.vo.TableCharacterSubtotal;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/8/27 11:34
 * @verison: 1.0
 * @description:
 */
@Api(tags = "峰会桌台管理")
@RestController
@RequestMapping("sale/conventionTable")
public class ConventionTableController {

    @Resource
    private IConventionTableService conventionTableService;

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/峰会桌台管理/峰会桌台详情")
    @GetMapping("/{id}")
    public ResponseBo<ConventionTableVo> detail(@PathVariable("id") Long id) {
        ConventionTableVo data = conventionTableService.findConventionTableById(id);
        return new ResponseBo<>(data);
    }

    /**
     * 批量新增信息
     *
     * @param conventionTableDto
     * @return
     * @
     */
    @ApiOperation(value = "批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/峰会桌台管理/新增峰会桌台")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody @Validated(ConventionTableDto.Add.class) ConventionTableDto conventionTableDto) {
        conventionTableService.batchAdd(conventionTableDto);
        return SaveResponseBo.ok();
    }


    /**
     * 删除信息
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/峰会桌台管理/删除峰会桌台")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        conventionTableService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * 列表数据
     *
     * @param conventionTableDto
     * @return
     * @
     */
    @ApiOperation(value = "列表数据", notes = "fkTableTypeKey桌台类型 tableNum桌台编号")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/峰会桌台管理/查询峰会桌台")
    @PostMapping("datas")
    public ResponseBo<ConventionTableVo> datas(@RequestBody ConventionTableDto conventionTableDto) {
        List<ConventionTableVo> datas = conventionTableService.getConventionTables(conventionTableDto);
        return new ListResponseBo<>(datas);
    }

    /**
     * 导出晚宴桌台安排
     * @param conventionTableDto
     * @return
     */
    @ApiOperation(value = "导出晚宴桌台安排")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/峰会桌台管理/导出晚宴桌台安排")
    @PostMapping("/exportConventionTableExcel")
    public void exportConventionTableExcel(HttpServletResponse response , @RequestBody ConventionTableDto conventionTableDto) {
        conventionTableService.exportConventionTableExcel(response , conventionTableDto);
    }

    /**
     * vip桌设置
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "vip桌设置", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/峰会桌台管理/vip桌设置")
    @GetMapping("setVip/{id}/{isVip}")
    public ResponseBo setVip(@PathVariable("id") Long id, @PathVariable("isVip") Integer isVip) {
        return UpdateResponseBo.ok(conventionTableService.setVip(id, isVip));
    }

    /**
     * 新增桌台座位数
     *
     * @param id
     * @param seatCount
     * @return
     * @
     */
    @ApiOperation(value = "新增桌台座位数", notes = "id桌台id，seatCount座位数")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/峰会桌台管理/新增桌台座位数")
    @PostMapping("seatCountUp")
    public ResponseBo seatCountUp(@RequestParam("id") Long id, @RequestParam("seatCount") Integer seatCount) {
        conventionTableService.seatCountUp(id, seatCount);
        return UpdateResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :减少桌台座位数
     * @Param [id, seatCount]
     * <AUTHOR>
     */
    @ApiOperation(value = "减少桌台座位数", notes = "id桌台id，seatCount座位数")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/峰会桌台管理/减少桌台座位数")
    @PostMapping("seatCountDown")
    public ResponseBo seatCountDown(@RequestParam("id") Long id, @RequestParam("seatCount") Integer seatCount) {
        conventionTableService.seatCountDown(id, seatCount);
        return UpdateResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.ConventionPersonVo>
     * @Description :桌台未安排人员详细
     * @Param [conventionId, type]
     * <AUTHOR>
     */
    @ApiOperation(value = "桌台未安排人员详细", notes = "fkConventionId峰会id(必传),type人员类型，nameKey人员姓名")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/峰会桌台管理/查询桌台未安排人员详细")
    @PostMapping("getNotArrangedPersonList")
    public ResponseBo<ConventionPersonVo> getNotArrangedPersonList(@RequestBody SearchBean<ConventionPersonDto> page, @RequestParam("type") String type) {
        List<ConventionPersonVo> datas = conventionTableService.getNotArrangedPersonList(page.getData(), page, type);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    @ApiOperation(value = "获取桌子角色小计", notes = "fkTableTypeKey:桌子类型")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/峰会桌台管理/获取桌子角色小计")
    @PostMapping("getTableCharacterSubtotal")
    public ResponseBo<TableCharacterSubtotal> getTableCharacterSubtotal(@RequestBody ConventionTableDto conventionTableVo) {
        List<TableCharacterSubtotal> tableCharacterSubtotalList = conventionTableService.getTableCharacterSubtotal(conventionTableVo);
        return new ListResponseBo<>(tableCharacterSubtotalList);
    }


}

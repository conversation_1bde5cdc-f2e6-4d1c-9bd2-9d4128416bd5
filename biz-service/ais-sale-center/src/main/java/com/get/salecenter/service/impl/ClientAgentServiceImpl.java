package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.ClientAgentMapper;
import com.get.salecenter.vo.ClientAgentVo;
import com.get.salecenter.entity.ClientAgent;
import com.get.salecenter.service.IAgentService;
import com.get.salecenter.service.IClientAgentService;
import com.get.salecenter.dto.ClientAgentDto;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * author:Neil
 * Time: 14:04
 * Date: 2022/8/17
 * Description:
 */
@Service
public class ClientAgentServiceImpl implements IClientAgentService {

    @Resource
    private ClientAgentMapper clientAgentMapper;

    @Resource
    private UtilService<Object> utilService;


    @Resource
    private IAgentService agentService;

    @Override
    public Map<Long, String> getAgentNameByClientIds(Set<Long> clientIds) {
        Map<Long, String> map = new HashMap<>();
//        if (GeneralTool.isEmpty(clientIds)) {
//            return map;
//        }
//        List<ClientAgentVo> agentNameByClientIds = clientAgentMapper.getAgentNameByClientIds(clientIds);
//        for (ClientAgentVo agentNameByClientId : agentNameByClientIds) {
//            map.put(agentNameByClientId.getFkClientId(), agentNameByClientId.getAgentName());
//        }
        return map;
    }

    @Override
    public Map<Long, String> getBdCodeByClientIds(Set<Long> clientIds) {
        Map<Long, String> map = new HashMap<>();
//        if (GeneralTool.isEmpty(clientIds)) {
//            return map;
//        }
//        List<ClientAgentVo> bdCodeByClientIds = clientAgentMapper.getBdCodeByClientIds(clientIds);
//        for (ClientAgentVo bdCodeByClientId : bdCodeByClientIds) {
//            map.put(bdCodeByClientId.getFkClientId(), bdCodeByClientId.getBdCode());
//        }
        return map;
    }

    /**
     * Author Cream
     * Description : // 添加客户与代理绑定关系
     * Date 2022/8/23 11:27
     * Params:
     * Return
     */
    @Override
    public ResponseBo<Long> addClientAgent(ClientAgentDto clientAgentDto) {
        if (GeneralTool.isEmpty(clientAgentDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        ClientAgent clientAgent = new ClientAgent();
        BeanUtils.copyProperties(clientAgentDto,clientAgent);
        Integer exits = clientAgentMapper.exitsClientAgent(clientAgentDto.getFkAgentId(), clientAgentDto.getFkClientId(), clientAgentDto.getIsActive());
        if (exits > 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("agent_bound_exits"));
        }
        Boolean active = agentService.isActive(clientAgentDto.getFkAgentId());
        if (active==null || !active) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("agent_no_active"));
        }
        clientAgent.setActiveDate(new Date());
        utilService.setCreateInfo(clientAgent);
        clientAgentMapper.insert(clientAgent);
        return new ResponseBo<>(clientAgent.getId());
    }

    /**
     * Author Cream
     * Description : // 更新客户和代理的绑定关系
     * Date 2022/8/22 14:31
     * Params:
     * Return
     */
    @Override
    public ResponseBo<ClientAgent> updateClientAgent(ClientAgentDto clientAgentDto) {
        if (GeneralTool.isEmpty(clientAgentDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        Long fkAgentId = clientAgentDto.getFkAgentId();
        Long fkClientId = clientAgentDto.getFkClientId();
        ClientAgent clientAgent = clientAgentMapper.selectOne(Wrappers.<ClientAgent>lambdaQuery().eq(ClientAgent::getFkAgentId, fkAgentId)
                .eq(ClientAgent::getFkClientId, fkClientId)
        );
        if (clientAgent!=null) {
            BeanUtils.copyProperties(clientAgentDto, clientAgent);
            if (clientAgentDto.getIsActive()){
                clientAgent.setActiveDate(new Date());
                clientAgent.setUnactiveDate(null);
            }else {
                clientAgent.setActiveDate(null);
                clientAgent.setUnactiveDate(new Date());
            }
            utilService.setUpdateInfo(clientAgent);
            clientAgentMapper.updateByIdWithNull(clientAgent);
        }
        return new ResponseBo<>(clientAgent);
    }

    /**
     * Author Cream
     * Description : // 获取客户代理列表
     * Date 2022/8/23 11:26
     * Params:
     * Return
     */
    @Override
    public ResponseBo<ClientAgentVo> getClientAgentList(ClientAgentDto clientAgentDto, Page page) {
        if (GeneralTool.isEmpty(clientAgentDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        IPage<ClientAgent> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<ClientAgentVo> datas = clientAgentMapper.datas(iPage, clientAgentDto);
        page.setAll((int)iPage.getTotal());
        return new ListResponseBo<>(datas,page);
    }
}

package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.salecenter.dao.sale.EventBillStaffNoticeMapper;
import com.get.salecenter.entity.EventBillStaffNotice;
import com.get.salecenter.service.IEventBillStaffNoticeService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: Hardy
 * @create: 2022/5/9 16:54
 * @verison: 1.0
 * @description:
 */
@Service
public class EventBillStaffNoticeServiceImpl extends ServiceImpl<EventBillStaffNoticeMapper, EventBillStaffNotice> implements IEventBillStaffNoticeService {


    /**
     * 批量新增
     *
     * @param eventBillStaffNotices
     * @return
     */
    @Override
    public Boolean batchAddByIds(List<EventBillStaffNotice> eventBillStaffNotices) {
        return saveBatch(eventBillStaffNotices);
    }
}

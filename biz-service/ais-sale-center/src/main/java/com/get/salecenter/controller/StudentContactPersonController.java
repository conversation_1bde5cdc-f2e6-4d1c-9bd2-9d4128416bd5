package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.salecenter.vo.StudentContactPersonVo;
import com.get.salecenter.service.IStudentContactPersonService;
import com.get.salecenter.dto.StudentContactPersonDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/11/11
 * @TIME: 14:38
 * @Description:
 **/

@Api(tags = "学生联系人管理")
@RestController
@RequestMapping("sale/studentContactPerson")
public class StudentContactPersonController {
    @Resource
    private IStudentContactPersonService contactPersonService;


    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 新增信息
     * @Param [StudentContactPersonDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生联系人管理/新增")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(StudentContactPersonDto.Add.class) StudentContactPersonDto contactPersonVo) {
        return SaveResponseBo.ok(contactPersonService.addContactPerson(contactPersonVo));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentContactPersonVo>
     * @Description: 列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生联系人管理/查询")
    @PostMapping("datas")
    public ResponseBo<StudentContactPersonVo> datas(@RequestBody SearchBean<StudentContactPersonDto> page) {
        List<StudentContactPersonVo> datas = contactPersonService.getContactPersons(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 删除信息
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/学生联系人管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        contactPersonService.delete(id);
        return DeleteResponseBo.ok();
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentContactPersonVo>
     * @Description: 修改信息
     * @Param [StudentContactPersonDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生联系人管理/更新")
    @PostMapping("update")
    public ResponseBo<StudentContactPersonVo> update(@RequestBody @Validated(StudentContactPersonDto.Update.class) StudentContactPersonDto contactPersonVo) {
        return UpdateResponseBo.ok(contactPersonService.updateContactPerson(contactPersonVo));
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentContactPersonVo>
     * @Description: 详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/学生联系人管理/详情")
    @GetMapping("/{id}")
    public ResponseBo<StudentContactPersonVo> detail(@PathVariable("id") Long id) {
        StudentContactPersonVo data = contactPersonService.findContactPersonById(id);
        return new ResponseBo<>(data);
    }

    /**
     * 根据学生Id获取所有联系人列表
     *
     * @param studentId
     * @return
     */
    @ApiOperation(value = "根据学生Id获取所有联系人列表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生联系人管理/查询")
    @PostMapping("getStudentContactPersonByStudentId")
    public List<StudentContactPersonVo> getStudentContactPersonByStudentId(@PathVariable("studentId") Long studentId) {
        return contactPersonService.getStudentContactPersonByStudentId(studentId);
    }

}

package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.UpdateResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.vo.EventPlanThemeVo;
import com.get.salecenter.service.EventPlanThemeService;
import com.get.salecenter.dto.EventPlanThemeDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import java.util.Map;

/**
 * <p>
 *  前端接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12
 */

@Api(tags = "活动年度计划主题管理")
@RestController
@RequestMapping("sale/eventPlanTheme")
@VerifyPermission(IsVerify = false)
public class EventPlanThemeController {

    @Resource
    private EventPlanThemeService eventPlanThemeService;

    @ApiOperation(value = "列表数据")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/活动年度计划主题管理/列表数据")
    @PostMapping("datas")
    public ResponseBo<EventPlanThemeVo> datas(@RequestParam("fkEventPlanId") Long fkEventPlanId){
        return new ListResponseBo(eventPlanThemeService.getEventPlanThemes(fkEventPlanId));
    }

    @ApiOperation(value = "详情", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/活动年度计划主题管理/详情")
    @GetMapping("/{id}")
    public ResponseBo<EventPlanThemeVo> detail(@PathVariable("id") Long id) {
        EventPlanThemeVo data = eventPlanThemeService.findEventPlanThemeById(id);
        return new ResponseBo<>(data);
    }

    @ApiOperation(value = "新增数据")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER,type = LoggerOptTypeConst.EDIT,description = "销售中心/活动年度计划主题管理/新增")
    @PostMapping("add")
    public ResponseBo add(@Validated @RequestBody EventPlanThemeDto vo){
        return SaveResponseBo.ok(eventPlanThemeService.addEventPlanTheme(vo));
    }

    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/活动年度计划主题管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo<EventPlanThemeVo> delete(@PathVariable("id") Long id) {
        eventPlanThemeService.delete(id);
        return DeleteResponseBo.ok();
    }

    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/活动年度计划主题管理/更新")
    @PostMapping("update")
    public ResponseBo<EventPlanThemeVo> update(@RequestBody  @Validated(EventPlanThemeDto.Update.class) EventPlanThemeDto vo) {
        return UpdateResponseBo.ok(eventPlanThemeService.updateEventPlanTheme(vo));
    }


    @ApiOperation(value = "拖拽", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/活动年度计划主题管理/拖拽")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestParam("fkEventPlanId") Long fkEventPlanId,@RequestParam("start")Integer start,@RequestParam("end")Integer end) {
        eventPlanThemeService.movingOrder(fkEventPlanId,start,end);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "活动年度计划主题类型下拉", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/代理账户管理/获取合同账户卡类型")
    @VerifyLogin(IsVerify = false)
    @GetMapping("getDisplayType")
    public ListResponseBo<Map<String, Object>> getDisplayType() {
        return new ListResponseBo<>(ProjectExtraEnum.enumsTranslation2Arrays(ProjectExtraEnum.DISPLAY_TYPE));
    }

}

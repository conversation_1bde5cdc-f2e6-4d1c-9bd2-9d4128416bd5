package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.vo.*;
import com.get.salecenter.vo.ReceivablePlanVo;
import com.get.salecenter.service.IReceivablePlanService;
import com.get.salecenter.dto.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/11/20
 * @TIME: 14:19
 * @Description:
 **/

@Api(tags = "应收计划管理")
@RestController
@RequestMapping("sale/receivablePlan")
public class ReceivablePlanController {

    @Resource
    private IReceivablePlanService planService;


    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.ReceivablePlanVo>
     * @Description: 列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/应收计划管理/列表数据")
    @PostMapping("datas")
    public ListResponseBo<ReceivablePlanVo> datas(@RequestBody SearchBean<ReceivablePlanDto> page) {
        String[] times = {"0", "0"};//[0]-o-主SQL执行时间,[1]-f-远程调用时间
        List<ReceivablePlanVo> datas = planService.getReceivablePlan(page.getData(), page, times);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p, times[0], times[1]);
    }

    @ApiOperation("应收计划列表数据")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/应收计划管理/应收计划列表数据")
    @PostMapping("receivablePlanDatas")
    public ListResponseBo<ReceivablePlanNewVo> receivablePlanDatas(@RequestBody SearchBean<ReceivablePlanNewDto> page) {
        String[] times = {"0", "0"};//[0]-o-主SQL执行时间,[1]-f-远程调用时间
        List<ReceivablePlanNewVo> datas = planService.getReceivablePlanNew(page.getData(), page,times);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p,times[0], times[1]);
    }


    /**
     * @Description: 提供商应收汇总统计明细
     * @Author: Jerry
     * @Date:11:35 2021/11/22
     */
    @ApiOperation(value = "提供商应收汇总统计明细")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/应收计划管理/提供商应收汇总统计明细")
    @PostMapping("institutionProviderReceivableSumDetail")
    public ResponseBo<ReceivablePlanVo> institutionProviderReceivableSumDetail(@RequestBody SearchBean<InstitutionProviderReceivableSumDetailDto> page) {
        List<ReceivablePlanVo> datas = planService.institutionProviderReceivableSumDetail(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "根据应付计划信息获取对应应收计划下拉框数据", notes = "")
    @GetMapping("getReceivablePlanByPayablePlanInfo")
    public ResponseBo<BaseSelectEntity> getReceivablePlanByPayablePlanInfo(@RequestParam("fkTypeKey") String fkTypeKey,
                                                                           @RequestParam("fkTypeTargetId") Long fkTypeTargetId) {
        return new ListResponseBo<>(planService.getReceivablePlanByPayablePlanInfo(fkTypeKey, fkTypeTargetId));
    }

    /**
     * @Description: 学生应收汇总统计明细
     * @Author: Jerry
     * @Date:11:35 2021/11/22
     */
    @ApiOperation(value = "学生应收汇总统计明细")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/应收计划管理/学生应收汇总统计明细")
    @PostMapping("studentReceivableSumDetail")
    public ResponseBo<ReceivablePlanVo> studentReceivableSumDetail(@RequestBody SearchBean<StudentReceivableSumDetailDto> page) {
        List<ReceivablePlanVo> datas = planService.studentReceivableSumDetail(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.ReceivableReasonVo>
     * @Description: 详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/应收计划管理/详情")
    @GetMapping("/{id}")
    public ResponseBo<ReceivablePlanVo> detail(@PathVariable("id") Long id) {
        ReceivablePlanVo data = planService.findReceivablePlan(id);
        return new ResponseBo<>(data);
    }

    @ApiOperation(value = "获取应收应付", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/应收计划管理/获取应收应付")
    @GetMapping("/getARAPInformation")
    public ResponseBo<ARAPDto> getARAPInformation(@RequestParam("itemId") Long itemId, @RequestParam("typeKey")String typeKey){
        return new ResponseBo<>(planService.getARAPInformation(itemId,typeKey));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 新增接口
     * @Param [receivableReasonVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/应收计划管理/新增")
    @PostMapping("add")
    public ResponseBo add(@RequestBody  @Validated(ReceivablePlanDto.Add.class) ReceivablePlanDto receivablePlanDto) {
        return SaveResponseBo.ok(planService.addReceivablePlan(receivablePlanDto));
    }

    @ApiOperation(value = "获取应收计划收款时间列表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/应收计划管理/获取应收计划收款时间列表")
    @GetMapping("getReceivablePlanDateList")
    public ListResponseBo<ReceivablePlanDateVo> getReceivablePlanDateList(@RequestParam("receivablePlanId") Long receivablePlanId) {
        return new ListResponseBo<>(planService.getReceivablePlanDateList(receivablePlanId));
    }

    @ApiOperation(value = "添加应收计划收款时间", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/应收计划管理/添加应收计划收款时间")
    @PostMapping("addReceivablePlanDate")
    public SaveResponseBo addReceivablePlanDate(@RequestParam("receivablePlanId")Long receivablePlanId,@RequestParam("receivablePlanDate") String receivablePlanDate){
        return planService.addReceivablePlanDate(receivablePlanId,receivablePlanDate);
    }

    @ApiOperation(value = "批量添加应收计划收款时间", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/应收计划管理/批量添加应收计划收款时间")
    @PostMapping("batchAddReceivablePlanDate")
    public ResponseBo batchAddReceivablePlanDate(@RequestBody BatchReceivablePlanDateAddDto batchReceivablePlanDateAddDto){
        planService.batchAddReceivablePlanDate(batchReceivablePlanDateAddDto.getReceivablePlanIdList(), batchReceivablePlanDateAddDto.getReceivablePlanDate());
        return ResponseBo.ok();
    }

    @ApiOperation(value = "更新应收计划收款时间", notes = "fkReceivablePlanDateId为计划收款时间主键id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/应收计划管理/更新应收计划收款时间")
    @PostMapping("updateReceivablePlanDate")
    public SaveResponseBo updateReceivablePlanDate(@RequestParam("fkReceivablePlanDateId") Long fkReceivablePlanDateId,@RequestParam("receivablePlanDate") String receivablePlanDate) {
        return planService.updateReceivablePlanDate(fkReceivablePlanDateId,receivablePlanDate);
    }

    @ApiOperation(value = "删除应收计划收款时间", notes = "fkReceivablePlanDateId为计划收款时间主键id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/应收计划管理/删除应收计划收款时间")
    @PostMapping("deleteReceivablePlanDate")
    public ResponseBo deleteReceivablePlanDate(@RequestParam("fkReceivablePlanDateId") Long fkReceivablePlanDateId){
        return planService.deleteReceivablePlanDate(fkReceivablePlanDateId);
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.ReceivableReasonVo>
     * @Description: 修改信息
     * @Param [receivablePlanDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/应收计划管理/更新")
    @PostMapping("update")
    public ResponseBo<ReceivablePlanVo> update(@RequestBody @Validated(ReceivablePlanDto.Update.class) ReceivablePlanDto receivablePlanDto) {
        return UpdateResponseBo.ok(planService.updateReceivablePlan(receivablePlanDto));
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 删除
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为删除数据的ID")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/应收计划管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        planService.delete(id);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.MediaAndAttachedDto>
     * @Description: 查询应收计划附件
     * @Param [voSearchBean]
     * <AUTHOR>
     */
    @ApiOperation(value = "查询应收计划附件", notes = "keyWord为关键词")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/应收计划管理/查询代理附件")
    @PostMapping("getReceivableMedia")
    public ResponseBo<MediaAndAttachedVo> getReceivableMedia(@RequestBody SearchBean<MediaAndAttachedDto> voSearchBean) {
        List<MediaAndAttachedVo> staffMedia = planService.getMedia(voSearchBean.getData(), voSearchBean);
        Page page = BeanCopyUtils.objClone(voSearchBean, Page::new);
        return new ListResponseBo<>(staffMedia, page);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.MediaAndAttachedDto>
     * @Description: 保存应收计划附件
     * @Param [mediaAttachedVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "保存应收计划附件")
    @OperationLogger(module = LoggerModulesConsts.SYSTEMCENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/应收计划管理/代理附件保存接口")
    @PostMapping("upload")
    public ResponseBo<MediaAndAttachedVo> addMedia(@RequestBody  @Validated(MediaAndAttachedDto.Add.class)  ValidList<MediaAndAttachedDto> mediaAttachedVo) {
        return new ListResponseBo<>(planService.addMedia(mediaAttachedVo));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 关闭方案
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "关闭应收计划", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/应收计划管理/关闭")
    @PostMapping("unableReceivable")
    public ResponseBo unableOffer(@RequestParam("id") Long id, @RequestParam("status") Long status,@RequestParam("sycPay")Boolean syncPay) {
        planService.unableReceivable(id, status,syncPay);
        return UpdateResponseBo.ok();
    }

    @ApiOperation(value = "批量关闭应收计划", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/应收计划管理/批量关闭应收计划")
    @PostMapping("batchUnableReceivablePlan")
    public ResponseBo<StudentOfferItemVo> batchUnableReceivablePlan(@RequestBody Set<Long> receivableIds){
        return planService.batchUnableReceivablePlan(receivableIds);
    }


    /**
     * @return java.util.List<java.lang.Long>
     * @Description: feign
     * @Param [typeKey, targetId]
     * <AUTHOR>
     */
    @ApiIgnore
    @PostMapping("getReceivablePlanIds")
    public List<Long> getReceivablePlanId(@RequestParam(value = "typeKey", required = false) String typeKey,
                                          @RequestParam(value = "targetId", required = false) Long targetId) {
        return planService.getReceivablePlanId(typeKey, targetId);
    }


    /**
     * @return
     * @Description：feign 根据应收计划id查询应收计划应收金额
     * @Param
     * @Date 13:00 2021/4/23
     * <AUTHOR>
     */
    @ApiIgnore
    @PostMapping("getReceivablePlanAmountById")
    public BigDecimal getReceivablePlanAmountById(@RequestParam(value = "id") Long id) {
        return planService.getReceivablePlanAmountById(id);
    }

    /**
     * feign调用，根据ids获取应收计划列表
     *
     * @param ids
     * @return
     * @
     */
    @ApiIgnore
    @PostMapping("getReceivablePlanByIds")
    public List<ReceivablePlanVo> getReceivablePlanByIds(@RequestBody Set<Long> ids) {
        return planService.getReceivablePlanByIds(ids);

    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.ReceivableReasonVo>
     * @Description: feign调用ids详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiIgnore
    @PostMapping("getReceivablePlansDetail")
    public List<ReceivablePlanVo> getReceivablePlansDetail(@RequestBody Set<Long> ids) {
        return planService.getReceivablePlansDetail(ids);
    }


    /**
     * @return void
     * @Description :导出应收计划Excel
     * @Param [response, conventionSponsorVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "导出应收计划Excel")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/应收计划管理/导出应收计划Excel")
    @PostMapping("/exportReceivablePlanExcel")
    @ResponseBody
    public void exportReceivablePlanExcel(HttpServletResponse response, @RequestBody ReceivablePlanNewDto receivablePlanNewDto) {
        planService.exportReceivablePlanExcel(response, receivablePlanNewDto);
    }

    @ApiOperation(value = "查询发票可新绑定应收计划下拉数据")
    @PostMapping("/getInvoiceNewReceivablePlanList")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/发票管理/查询发票可新绑定应收计划下拉数据")
    @ResponseBody
    public ResponseBo<ReceivablePlanVo> getInvoiceNewReceivablePlanList(@RequestBody @Validated ReceivablePlanBatchDto receivablePlanBatchDto){
        //[0]-o-主SQL执行时间,[1]-f-远程调用时间
        String[] times = {"0", "0"};
        return new ListResponseBo<>(planService.doGetInvoiceNewReceivablePlan(receivablePlanBatchDto,times),times[0],times[1]);
    }


    @ApiOperation(value = "获取发票新绑定应收计划分页信息", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/发票管理/获取发票新绑定应收计划分页信息")
    @PostMapping("getInvoiceRPPaginationInfo")
    @VerifyPermission(IsVerify = false)
    public ResponseBo getInvoiceRPPaginationInfo(@RequestBody SearchBean<ReceivablePlanBatchDto> page) {
        return planService.doGetInvoiceRPPaginationInfo(page.getData(), page);
    }
    @ApiOperation(value = "应收应付对冲", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/发票管理/应收应付对冲")
    @PostMapping("ArApHedging")
    public SaveResponseBo arApHedging(@RequestBody @Validated ArApHedgingDto arApHedgingDto) {
        return planService.arApHedging(arApHedgingDto);
    }
    @ApiOperation(value = "获取最新的三条学费", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/应收计划管理/获取最新的三条学费")
    @GetMapping("getRePlanTheLatestThreeTuitionFees")
    @VerifyPermission(IsVerify = false)
    public ResponseBo<String> getRePlanTheLatestThreeTuitionFees(@RequestParam("fkCompanyId") Long fkCompanyId) {
        return new ListResponseBo<>(planService.getRePlanTheLatestThreeTuitionFees(fkCompanyId));
    }
}

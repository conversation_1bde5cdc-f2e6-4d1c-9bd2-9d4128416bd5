package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.salecenter.vo.ConventionProcedureVo;
import com.get.salecenter.vo.MediaAndAttachedVo;
import com.get.salecenter.dto.ConventionProcedureCopyDto;
import com.get.salecenter.dto.ConventionProcedureDto;
import com.get.salecenter.dto.MediaAndAttachedDto;

import java.util.List;
import java.util.Map;

/**
 * @author: Sea
 * @create: 2020/7/2 11:01
 * @verison: 1.0
 * @description: 峰会流程管理业务接口
 */
public interface IConventionProcedureService {
    /**
     * 详情
     *
     * @param id
     * @return
     */
    ConventionProcedureVo findConventionProcedureById(Long id);

    /**
     * 新增
     *
     * @param conventionProcedureDto
     * @return
     */
    Long addConventionProcedure(ConventionProcedureDto conventionProcedureDto);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 修改
     *
     * @param conventionProcedureDto
     * @return
     */
    ConventionProcedureVo updateConventionProcedure(ConventionProcedureDto conventionProcedureDto);

    /**
     * 列表
     *
     * @param conventionProcedureDto
     * @param page
     * @return
     */
    List<ConventionProcedureVo> getConventionProcedures(ConventionProcedureDto conventionProcedureDto, Page page);

    /**
     * 上移下移
     *
     * @param conventionProcedureDtos
     */
    void movingOrder(List<ConventionProcedureDto> conventionProcedureDtos);

    /**
     * 峰会id查找对应峰会流程
     *
     * @param conventionId
     * @return
     */
    List<ConventionProcedureVo> findConventionProcedureByFkId(Long conventionId);

    /**
     * 保存文件   String tableName = "m_convention_procedure"
     *
     * @param mediaAttachedVo
     * @return
     * @
     */
    MediaAndAttachedVo addConventionMedia(MediaAndAttachedDto mediaAttachedVo);

    /**
     * 获取峰会流程附件类型
     *
     * @return
     */
    List<Map<String, Object>> findMediaAndAttachedType();

    /**
     * 根据桌子获取峰会流程ids
     *
     * @Date 15:49 2021/6/29
     * <AUTHOR>
     */
    List<Long> getProcedureIdsByTable(String tableTypeKey, Long conventionId);

    /**
     * 复制峰会活动流程
     * @param conventionProcedureCopyDto
     */
    void copyConventionProcedure(ConventionProcedureCopyDto conventionProcedureCopyDto);

    void summitPersonnelAllocation();
}

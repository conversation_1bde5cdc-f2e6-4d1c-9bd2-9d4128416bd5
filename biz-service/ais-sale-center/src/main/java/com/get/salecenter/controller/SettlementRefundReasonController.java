package com.get.salecenter.controller;


import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.entity.SettlementRefundReason;
import com.get.salecenter.service.SettlementRefundReasonService;
import com.get.salecenter.dto.SettlementRefundReasonDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "结算退款原因管理")
@RestController
@RequestMapping("sale/settlementRefundReason")
public class SettlementRefundReasonController {

    @Resource
    private SettlementRefundReasonService settlementRefundReasonService;


    @ApiOperation(value = "列表数据")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/结算退款原因管理/列表数据")
    @PostMapping("datas")
    public ListResponseBo<SettlementRefundReason> datas(@RequestBody SearchBean<SettlementRefundReasonDto> page) {
        List<SettlementRefundReason> datas = settlementRefundReasonService.datas(page.getData(), page);
        return new ListResponseBo<>(datas);
    }

    @PostMapping("/add")
    @ApiOperation(value = "新增退款原因类型", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/结算退款原因管理/新增退款原因类型")
    public SaveResponseBo save(@Validated(value = {SettlementRefundReasonDto.Add.class})  @RequestBody SettlementRefundReasonDto settlementRefundReasonDto) {
        return settlementRefundReasonService.save(settlementRefundReasonDto);
    }

    @ApiOperation(value = "批量新增退款原因类型", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/结算退款原因管理/批量新增退款原因类型")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody  @Validated(SettlementRefundReasonDto.Add.class) ValidList<SettlementRefundReasonDto> settlementRefundReasonDtos) {
        settlementRefundReasonService.batchAdd(settlementRefundReasonDtos);
        return SaveResponseBo.ok();
    }

    @GetMapping("/findInfoById/{id}")
    @ApiOperation(value = "获取详情", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/结算退款原因管理/获取详情")
    public ResponseBo<SettlementRefundReason> findInfoById(@PathVariable("id") Long id) {
        return settlementRefundReasonService.findInfoById(id);
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新退款原因类型", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/结算退款原因管理/更新服务费类型")
    public ResponseBo update(@RequestBody  @Validated(value = {SettlementRefundReasonDto.Update.class})  SettlementRefundReasonDto settlementRefundReasonDto) {
        return settlementRefundReasonService.update(settlementRefundReasonDto);
    }

    @PostMapping("/movingOrder")
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/结算退款原因管理/上移下移")
    public ResponseBo movingOrder(@RequestBody List<SettlementRefundReasonDto> feeTypeVos) {
        return settlementRefundReasonService.movingOrder(feeTypeVos);
    }

    @PostMapping("/delete/{id}")
    @ApiOperation(value = "删除退款原因类型", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/结算退款原因管理/删除退款原因类型")
    public ResponseBo delete(@PathVariable("id") Long id) {
        return settlementRefundReasonService.delete(id);
    }

    @GetMapping("/getSettlementRefundReasonTypeList")
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "服务费类型下拉", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/结算退款原因管理/结算退款原因类型下拉")
    public ListResponseBo<BaseSelectEntity> getSettlementRefundReasonTypeList() {
        return new ListResponseBo<>(settlementRefundReasonService.getServiceTypeList());
    }


}

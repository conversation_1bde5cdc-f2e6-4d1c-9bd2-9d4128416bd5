package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.dao.sale.VipConfigMapper;
import com.get.salecenter.vo.VipConfigVo;
import com.get.salecenter.entity.VipConfig;
import com.get.salecenter.service.VipConfigService;
import com.get.salecenter.dto.VipConfigDto;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2022/11/9
 * @TIME: 16:53
 * @Description:
 **/
@Service
public class VipConfigServiceImpl implements VipConfigService {

    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Resource
    private VipConfigMapper vipConfigMapper;

    @Resource
    private IInstitutionCenterClient institutionCenterClient;

    @Resource
    private UtilService utilService;

    @Override
    public List<VipConfigVo> getVipConfigs(VipConfigDto vipConfigDto, SearchBean<VipConfigDto> page) {
        List<Long> countryIds = null;
        List<Long> providerIds = null;
        if(GeneralTool.isNotEmpty(vipConfigDto.getStatisticsName())){
            countryIds = institutionCenterClient.getCountryByName(vipConfigDto.getStatisticsName()).getData();
            providerIds = institutionCenterClient.getInstitutionProviderIds(vipConfigDto.getStatisticsName());
        }

        IPage<VipConfig> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<VipConfig> vipConfigList = vipConfigMapper.getVipConfigs(iPage, vipConfigDto,countryIds,providerIds);
        if(GeneralTool.isEmpty(vipConfigList)){
            return null;
        }
        page.setAll((int) iPage.getTotal());

        List<VipConfigVo> datas = vipConfigList.stream().map(v -> BeanCopyUtils.objClone(v, VipConfigVo::new)).collect(Collectors.toList());

        //公司名称
        Set<Long> fkCompanyIds = datas.stream().map(VipConfigVo::getFkCompanyId).collect(Collectors.toSet());
        Map<Long, String> companyNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkCompanyIds)) {
            Result<Map<Long, String>> result = permissionCenterClient.getCompanyNamesByIds(fkCompanyIds);
            if (result.isSuccess() && result.getData() != null) {
                companyNamesByIds = result.getData();
            }
        }
        //统计项名称
        Set<Long> fkInstitutionProviderIds = datas.stream().filter(v->"m_institution_provider".equals(v.getFkTableName()))
                .map(VipConfigVo::getFkTableId).collect(Collectors.toSet());
        Set<Long> fkAreaCountryIds = datas.stream().filter(v->"u_area_country".equals(v.getFkTableName()))
                .map(VipConfigVo::getFkTableId).collect(Collectors.toSet());
        Map<Long, String> institutionProviderMap = institutionCenterClient.getInstitutionProviderNamesByIds(fkInstitutionProviderIds).getData();
        Map<Long, String> countryMap = institutionCenterClient.getCountryChnNameByIds(fkAreaCountryIds).getData();


        for (VipConfigVo vipConfigVo : datas) {
            vipConfigVo.setFkCompanyName(companyNamesByIds.get(vipConfigVo.getFkCompanyId()));
            switch (vipConfigVo.getFkTableName()) {
                case "m_institution_provider":
                    vipConfigVo.setTypeName("学校提供商");
                    vipConfigVo.setStatisticsName(institutionProviderMap.get(vipConfigVo.getFkTableId()));
                    break;
                case "u_area_country":
                    vipConfigVo.setTypeName("国家");
                    vipConfigVo.setStatisticsName(countryMap.get(vipConfigVo.getFkTableId()));
                    break;
            }
        }
        return datas;
    }

    @Override
    public void add(VipConfigDto vipConfigDto) {
        VipConfig vipConfig = BeanCopyUtils.objClone(vipConfigDto,VipConfig::new);
        utilService.setCreateInfo(vipConfig);
        //设置最大排序值
        vipConfig.setViewOrder(vipConfigMapper.getMaxViewOrder());
        vipConfigMapper.insert(vipConfig);
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        vipConfigMapper.deleteById(id);
    }

    @Override
    public VipConfigVo findVipConfigById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        VipConfig vipConfig = vipConfigMapper.selectById(id);
        return BeanCopyUtils.objClone(vipConfig, VipConfigVo::new);
    }

    @Override
    public void updateVipConfig(VipConfigDto vipConfigDto) {
        VipConfig vipConfig = BeanCopyUtils.objClone(vipConfigDto,VipConfig::new);
        utilService.setCreateInfo(vipConfig);
        vipConfigMapper.updateByIdWithNull(vipConfig);
    }

    @Override
    public void movingOrder(List<VipConfigDto> vipConfigDtos) {
        if (GeneralTool.isEmpty(vipConfigDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        VipConfig ro = BeanCopyUtils.objClone(vipConfigDtos.get(0), VipConfig::new);
        Integer oneorder = ro.getViewOrder();
        VipConfig rt = BeanCopyUtils.objClone(vipConfigDtos.get(1), VipConfig::new);
        Integer twoorder = rt.getViewOrder();
        ro.setViewOrder(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setViewOrder(oneorder);
        utilService.updateUserInfoToEntity(rt);
        vipConfigMapper.updateById(ro);
        vipConfigMapper.updateById(rt);
    }
}

package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.service.IMediaAndAttachedService;
import com.get.salecenter.dto.MediaAndAttachedDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * @author: Sea
 * @create: 2020/8/14 9:59
 * @verison: 1.0
 * @description:
 */
@Api(tags = "附件管理")
@RestController
@RequestMapping("sale/media")
public class MediaAndAttachedController {
    @Resource
    private IMediaAndAttachedService attachedService;


    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 删除
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除文件")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/峰会管理/删除文件")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        attachedService.deleteMediaAttached(id);
        return ResponseBo.ok();
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "上传文件")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/峰会管理/上传文件")
    @PostMapping("upload")
    public ResponseBo upload(@RequestParam("files") @NotNull(message = "文件不能为空") MultipartFile[] files) {
        ResponseBo responseBo = new ResponseBo();
        responseBo.put("data", attachedService.upload(files));
        return responseBo;
    }

    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "上传附件")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/峰会管理/上传文件")
    @PostMapping("uploadAttached")
    public ResponseBo uploadAttached(@RequestParam("files") @NotNull(message = "文件不能为空") MultipartFile[] files) {
        ResponseBo responseBo = new ResponseBo();
        responseBo.put("data", attachedService.uploadAppendix(files));
        return responseBo;
    }


    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "上传HTI公开桶附件接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FILECENTER, type = LoggerOptTypeConst.ADD, description = "文件中心/上传HTI公开桶附件接口")
    @PostMapping(value = "uploadHtiPublicFile")
    public ResponseBo uploadHtiPublicFile(@RequestParam("files") @NotNull(message = "文件不能为空") MultipartFile[] files){
        ResponseBo responseBo = new ResponseBo();
        responseBo.put("data", attachedService.uploadHtiPublicFile(files));
        return responseBo;
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 代理附件类型
     * @Param []
     * <AUTHOR>
     */
    @ApiOperation(value = "代理附件类型", notes = "")
    @GetMapping("getAgentMediaType")
    public ResponseBo findAgentMediaType() {
        List<Map<String, Object>> datas = attachedService.findAgentMediaType();
        return new ListResponseBo<>(datas);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 代理附件类型
     * @Param []
     * <AUTHOR>
     */
    @ApiOperation(value = "合同附件类型", notes = "")
    @GetMapping("getContractMediaType")
    public ResponseBo findContractMediaType() {
        List<Map<String, Object>> datas = attachedService.findContractMediaType();
        return new ListResponseBo<>(datas);
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 上移下移
     * @Param [mediaAttachedVos]
     * <AUTHOR>
     **/
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/附件管理/移动顺序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<MediaAndAttachedDto> mediaAttachedVos) {
        attachedService.movingOrder(mediaAttachedVos);
        return ResponseBo.ok();
    }
}

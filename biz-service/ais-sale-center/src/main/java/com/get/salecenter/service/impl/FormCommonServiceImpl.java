package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseEntity;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.ConventionPersonRegistrationMapper;
import com.get.salecenter.entity.ConventionPersonRegistration;
import com.get.salecenter.service.IFormCommonService;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2021/7/13 17:04
 * @verison: 1.0
 * @description:
 */
@Service
public class FormCommonServiceImpl implements IFormCommonService {

    @Resource
    private ConventionPersonRegistrationMapper conventionPersonRegistrationMapper;

    /**
     * 根据参会人id获取中间表
     *
     * @param conventionPersonId
     * @return
     */
    @Override
    public ConventionPersonRegistration getConventionPersonRegistration(Long conventionPersonId) {
//        Example example = new Example(ConventionPersonRegistration.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkConventionPersonId", conventionPersonId);
//        List<ConventionPersonRegistration> conventionPersonRegistrations = conventionPersonRegistrationMapper.selectByExample(example);

        List<ConventionPersonRegistration> conventionPersonRegistrations = conventionPersonRegistrationMapper.selectList(Wrappers.<ConventionPersonRegistration>lambdaQuery().eq(ConventionPersonRegistration::getFkConventionPersonId, conventionPersonId));
        if (conventionPersonRegistrations.size() <= 0) {
            return null;
        }
        return conventionPersonRegistrations.get(0);
    }

    @Override
    public void deleteTable(Long id) {
//        Example example = new Example(ConventionPersonRegistration.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkConventionPersonId", id);
//        List<ConventionPersonRegistration> conventionPersonRegistrations = conventionPersonRegistrationMapper.selectByExample(example);

        List<ConventionPersonRegistration> conventionPersonRegistrations = conventionPersonRegistrationMapper.selectList(Wrappers.<ConventionPersonRegistration>lambdaQuery().eq(ConventionPersonRegistration::getFkConventionPersonId, id));

        if (GeneralTool.isEmpty(conventionPersonRegistrations)) {
            return;
        }
        conventionPersonRegistrationMapper.delete(Wrappers.<ConventionPersonRegistration>lambdaQuery().eq(ConventionPersonRegistration::getFkConventionPersonId, id));
    }

    @Override
    public void addUserInfoToEntity(BaseEntity entity) {
        String user = "[form]";
        try {
            BeanUtils.setProperty(entity, "gmtCreate", new Date());
            BeanUtils.setProperty(entity, "gmtCreateUser", user);
        } catch (Exception e) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("failed_to_set_creator_creation_time") + e.getMessage());
        }
    }

    @Override
    public void updateUserInfoToEntity(BaseEntity entity) {
        String user = "[form]";
        try {
            BeanUtils.setProperty(entity, "gmtModified", new Date());
            BeanUtils.setProperty(entity, "gmtModifiedUser", user);
        } catch (Exception e) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("failed_to_set_the_update_person_update_time") + e.getMessage());
        }
    }
}

package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.vo.EventPlanThemeWorkshopVo;
import com.get.salecenter.service.EventPlanThemeWorkshopService;
import com.get.salecenter.dto.EventPlanThemeWorkshopDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 *  前端接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12
 */

@Api(tags = "活动年度计划线下专访类型项目管理")
@RestController
@RequestMapping("sale/eventPlanThemeWorkshop")
@VerifyPermission(IsVerify = false)
public class EventPlanThemeWorkshopController {
    
    @Resource
    private EventPlanThemeWorkshopService eventPlanThemeWorkshopService;


    @ApiOperation(value = "列表数据")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/活动年度计划线下专访类型项目/列表数据")
    @PostMapping("datas")
    public ResponseBo<EventPlanThemeWorkshopVo> datas(@RequestParam("fkEventPlanId") Long fkEventPlanId){
        return new ListResponseBo(eventPlanThemeWorkshopService.getEventPlanThemeWorkshops(fkEventPlanId));
    }

    @ApiOperation(value = "根据主题获取线下专访活动列表数据")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/活动年度计划线下专访类型项目/根据主题获取线上专访活动列表数据")
    @PostMapping("getWorkshopsByThemeId")
    public ResponseBo<EventPlanThemeWorkshopVo> getWorkshopsByThemeId(@RequestParam("fkEventPlanThemeId") Long fkEventPlanThemeId){
        return new ListResponseBo(eventPlanThemeWorkshopService.getWorkshopsByThemeId(fkEventPlanThemeId));
    }

    @ApiOperation(value = "激活", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/活动年度计划线下专访类型项目/激活")
    @PostMapping("activate")
    public ResponseBo activate(@RequestBody  @Validated(EventPlanThemeWorkshopDto.Update.class)  EventPlanThemeWorkshopDto workshopVo) {
        eventPlanThemeWorkshopService.activate(workshopVo);
        return SaveResponseBo.ok();
    }


    @ApiOperation(value = "批量新增/批量修改", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/活动年度计划线下专访类型项目/批量新增或修改")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody @Validated(EventPlanThemeWorkshopDto.Add.class)  ValidList<EventPlanThemeWorkshopDto> workshopVos) {
        eventPlanThemeWorkshopService.batchAdd(workshopVos);
        return SaveResponseBo.ok();
    }


    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/活动年度计划线下专访类型项目/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        eventPlanThemeWorkshopService.delete(id);
        return DeleteResponseBo.ok();
    }


    @ApiOperation(value = "拖拽", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/活动年度计划线下专访类型项目/拖拽")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestParam("fkEventPlanThemeId") Long fkEventPlanThemeId,@RequestParam("start")Integer start,@RequestParam("end")Integer end) {
        eventPlanThemeWorkshopService.movingOrder(fkEventPlanThemeId,start,end);
        return ResponseBo.ok();
    }


}

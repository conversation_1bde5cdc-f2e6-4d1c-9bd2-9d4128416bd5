package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.vo.ClientOfferStepVo;
import com.get.salecenter.service.IClientOfferStepService;
import com.get.salecenter.dto.ClientOfferStepAddDto;
import com.get.salecenter.dto.ClientOfferStepListDto;
import com.get.salecenter.dto.ClientOfferStepDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2023/12/13 10:33
 * @verison: 1.0
 * @description:
 */
@Api(tags = "资源申请方案状态步骤管理")
@RestController
@RequestMapping("sale/clientOfferStep")
public class ClientOfferStepController {

    @Resource
    private IClientOfferStepService clientOfferStepService;


    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 新增信息
     * @Param [ClientEventDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/资源申请方案状态步骤管理/新增")
    @PostMapping("add")
    public ResponseBo add(@RequestBody ClientOfferStepAddDto clientOfferStepAddDto) {
        return SaveResponseBo.ok(clientOfferStepService.addClientOfferStep(clientOfferStepAddDto));
    }


    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/资源申请方案状态步骤管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        clientOfferStepService.delete(id);
        return DeleteResponseBo.ok();
    }



    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentOfferItemStepVo>
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/资源申请方案状态步骤管理/详情")
    @GetMapping("/{id}")
    public ResponseBo<ClientOfferStepVo> detail(@PathVariable("id") Long id) {
        ClientOfferStepVo data = clientOfferStepService.findClientOfferStepById(id);
        return new ResponseBo<>(data);
    }


    @ApiOperation(value = "列表")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/资源申请方案状态步骤管理/列表")
    @PostMapping("datas")
    public ResponseBo<ClientOfferStepVo> datas(@RequestBody SearchBean<ClientOfferStepListDto> page) {
        List<ClientOfferStepVo> datas = clientOfferStepService.getClientOfferSteps(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }


    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/资源申请方案状态步骤管理/移动顺序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<ClientOfferStepDto> clientOfferStepDtos) {
        clientOfferStepService.movingOrder(clientOfferStepDtos);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.ClientVo>
     * @Description: 修改信息
     * @Param [clientUpdateVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/资源申请方案状态步骤管理/更新")
    @PostMapping("update")
    public ResponseBo<ClientOfferStepVo> update(@RequestBody ClientOfferStepAddDto clientOfferStepAddDto) {
        return UpdateResponseBo.ok(clientOfferStepService.updateClientOfferStep(clientOfferStepAddDto));
    }


    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "资源申请方案状态步骤下拉", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/资源申请方案状态步骤管理/资源申请方案状态步骤下拉")
    @GetMapping("getClientOfferStepSelect")
    public ResponseBo<ClientOfferStepVo> getClientOfferStepSelect() {
        return new ListResponseBo<>(clientOfferStepService.getClientOfferStepSelect());
    }
}

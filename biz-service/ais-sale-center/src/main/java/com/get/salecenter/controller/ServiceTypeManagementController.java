package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseVoEntity;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.permissioncenter.vo.tree.CompanyTreeVo;
import com.get.salecenter.vo.StudentServiceFeeTypeVo;
import com.get.salecenter.service.ServiceTypeManagementService;
import com.get.salecenter.dto.StudentServiceFeeTypeDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Api(tags = "服务费类型管理")
@RestController
@RequestMapping("sale/serviceType")
public class ServiceTypeManagementController {

    @Resource
    private ServiceTypeManagementService serviceTypeManagementService;

    @PostMapping("/datas")
    @ApiOperation(value = "获取服务类型管理列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/服务费类型管理/获取服务类型管理列表数据")
    public ListResponseBo<StudentServiceFeeTypeVo> datas(@Validated @RequestBody SearchBean<StudentServiceFeeTypeDto> searchBean){
       return serviceTypeManagementService.datas(searchBean.getData(),searchBean);
    }

    @PostMapping("/add")
    @ApiOperation(value = "新增服务费类型", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/服务费类型管理/新增服务费类型")
    public SaveResponseBo save(@Validated(value = {BaseVoEntity.Add.class}) @RequestBody StudentServiceFeeTypeDto studentServiceFeeTypeDto) {
        return serviceTypeManagementService.save(studentServiceFeeTypeDto);
    }

    @GetMapping("/findInfoById/{id}")
    @ApiOperation(value = "获取详情", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/服务费类型管理/获取详情")
    public ResponseBo<StudentServiceFeeTypeVo> findInfoById(@PathVariable("id") Long id) {
        return serviceTypeManagementService.findInfoById(id);
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新服务费类型", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/服务费类型管理/更新服务费类型")
    public ResponseBo update(@RequestBody @Validated(value = {BaseVoEntity.Update.class}) StudentServiceFeeTypeDto studentServiceFeeTypeDto) {
        return serviceTypeManagementService.update(studentServiceFeeTypeDto);
    }

    @PostMapping("/delete/{id}")
    @ApiOperation(value = "删除服务费类型", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/服务费类型管理/删除服务费类型")
    public ResponseBo delete(@PathVariable("id") Long id) {
        return serviceTypeManagementService.delete(id);
    }

    @PostMapping("/movingOrder")
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/服务费类型管理/上移下移")
    public ResponseBo movingOrder(@RequestBody List<StudentServiceFeeTypeDto> feeTypeVos) {
        return serviceTypeManagementService.movingOrder(feeTypeVos);
    }

    @PostMapping("/securityDistribution")
    @ApiOperation(value = "安全操作", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/服务费类型管理/安全操作")
    public ResponseBo securityDistribution(@RequestBody StudentServiceFeeTypeDto studentServiceFeeTypeDto) {
        return serviceTypeManagementService.securityDistribution(studentServiceFeeTypeDto);
    }


    @GetMapping("/getTypeCompanyRelation")
    @ApiOperation(value = "回显类型和公司的关系", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/服务费类型管理/回显类型和公司的关系")
    public ResponseBo<CompanyTreeVo> getTypeCompanyRelation(@RequestParam("typeId") Long typeId) {
        return serviceTypeManagementService.getTypeCompanyRelation(typeId);
    }

    @GetMapping("/getServiceTypeList")
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "服务费类型下拉", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/服务费类型管理/服务费类型下拉")
    public ListResponseBo<BaseSelectEntity> getServiceTypeList(@RequestParam("companyId") Long companyId) {
        return new ListResponseBo<>(serviceTypeManagementService.getServiceTypeList(companyId));
    }

}

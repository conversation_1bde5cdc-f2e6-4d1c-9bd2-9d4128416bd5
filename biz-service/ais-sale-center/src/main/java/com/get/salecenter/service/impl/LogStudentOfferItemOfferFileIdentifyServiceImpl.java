package com.get.salecenter.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.get.common.consts.CacheKeyConstants;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.HttpUtils;
import com.get.core.redis.cache.GetRedis;
import com.get.institutioncenter.vo.InstitutionCourseVo;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.salecenter.dao.sale.LogStudentOfferItemOfferFileIdentifyMapper;
import com.get.salecenter.dao.sale.StudentOfferIdentifyMapper;
import com.get.salecenter.dao.sale.StudentOfferItemMapper;
import com.get.salecenter.dao.sale.StudentOfferMapper;
import com.get.salecenter.vo.AnalyzeOfferVo;
import com.get.salecenter.vo.OfferVo;
import com.get.salecenter.vo.StudentOfferItemUploadVo;
import com.get.salecenter.entity.LogStudentOfferItemOfferFileIdentify;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.filecenter.dto.SaleFileDto;
import com.get.filecenter.feign.IFileCenterClient;
import com.get.filecenter.vo.FileVo;
import com.get.salecenter.entity.StudentOfferIdentify;
import com.get.salecenter.entity.StudentOfferItem;
import com.get.salecenter.service.LogStudentOfferItemOfferFileIdentifyService;
import lombok.extern.slf4j.Slf4j;
import me.xdrop.fuzzywuzzy.FuzzySearch;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @date 2024/8/14 14:28
 */
@Service
@Slf4j
public class LogStudentOfferItemOfferFileIdentifyServiceImpl extends ServiceImpl<LogStudentOfferItemOfferFileIdentifyMapper, LogStudentOfferItemOfferFileIdentify> implements LogStudentOfferItemOfferFileIdentifyService {

    @Resource
    StudentOfferMapper studentOfferMapper;
    @Resource
    LogStudentOfferItemOfferFileIdentifyMapper logStudentOfferItemOfferFileIdentifyMapper;
    @Resource
    private IFileCenterClient fileCenterClient;

    @Value(value = "${analyzeOfferAddress}")
    private String analyzeOfferAddress;

    @Resource
    private UtilService utilService;

    @Resource
    private StudentOfferIdentifyMapper studentOfferIdentifyMapper;

    @Resource
    private StudentOfferItemMapper studentOfferItemMapper;

    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    private GetRedis redisClient;


    @Override
    @Async
    public void analyzeOffer() {
        Boolean lock = redisClient.setNx(CacheKeyConstants.STUDENT_ANALYZE_OFFER_LOCK_KEY, 1, 3600L);
        if (!lock) {
            log.info("offer解析锁定");
            return;
        }
        log.info("获取解析数据。。。。");
            List<StudentOfferItemUploadVo> studentOffers = studentOfferMapper.getStudentOffer();
            if (GeneralTool.isEmpty(studentOffers)){
                return;
            }
        Set<Long> courseIds = studentOffers.stream().filter(item -> item.getFkInstitutionCourseId() != -1).map(StudentOfferItemUploadVo::getFkInstitutionCourseId).collect(Collectors.toSet());
        Map<Long, String> courseEnNameMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(courseIds)) {
            Result<Map<Long, String>> result = institutionCenterClient.getCourseEnNameByIds(courseIds);
            if (result.isSuccess()) {
                courseEnNameMap = result.getData();
            } else {
                log.error("解析offer获取课程名异常");
                return;
            }
        }

        for (StudentOfferItemUploadVo studentOffer : studentOffers) {
//                LocalTime currentTime = LocalTime.now();
//                // 判断当前时间是否在晚上8点到早上8点之间
//                if (!currentTime.isAfter(LocalTime.of(20, 0)) || currentTime.isBefore(LocalTime.of(8, 0))) {
//                    log.info("当前时间不在允许运行的时间范围内，停止处理...");
//                    break;
//                }
                FileVo fileVo = BeanCopyUtils.objClone(studentOffer, FileVo::new);
                fileVo.setGuid(studentOffer.getFileGuid());
                fileVo.setTypeValue(studentOffer.getTypeKey());
                log.info("获取文件信息。。。。");
                Result<SaleFileDto> downloadFile = fileCenterClient.getDownloadFile(fileVo);
                AnalyzeOfferVo analyzeOfferVo =new AnalyzeOfferVo();
                if (GeneralTool.isNotEmpty(downloadFile.getData())){
                    log.info("获取文件数据。。。。");
                    analyzeOfferVo = analyzeOfferClient(downloadFile.getData());
                }
                if (GeneralTool.isEmpty(analyzeOfferVo)){
                    log.info("解析失败。。。。");
                    break;
                }
                analyzeOfferVo.setText(null);
                LogStudentOfferItemOfferFileIdentify fileIdentify= new LogStudentOfferItemOfferFileIdentify();
                fileIdentify.setJson(JSONObject.toJSONString(analyzeOfferVo));
                fileIdentify.setFkFileGuid(studentOffer.getFileGuid());
                fileIdentify.setFkStudentId(studentOffer.getFkStudentId());
                fileIdentify.setFkStudentOfferItemId(studentOffer.getId());
                fileIdentify.setFkStudentId(studentOffer.getFkStudentId());
                fileIdentify.setGmtCreate(new Date());
                fileIdentify.setGmtCreateUser("admin");
                fileIdentify.setFkCompanyId(studentOffer.getFkCompanyId());
                if (!"success".equals(analyzeOfferVo.getStatus())){
                    fileIdentify.setStatus(0);
                    fileIdentify.setErrorMsg(analyzeOfferVo.getAttention());
                    logStudentOfferItemOfferFileIdentifyMapper.insert(fileIdentify);
                    continue;
                }
                fileIdentify.setStatus(1);
                if (GeneralTool.isNotEmpty(analyzeOfferVo.getOffer())){
                    Map<String,Object> map = new HashMap<>();
                    StudentOfferItem studentOfferItem = studentOfferItemMapper.selectById(studentOffer.getId());
                    for (OfferVo offerVo : analyzeOfferVo.getOffer()) {
                        fileIdentify.setCourseName(offerVo.getCourse());
                        fileIdentify.setOpeningTime(offerVo.getStart_time());
                        fileIdentify.setFkCurrencyTypeNum(offerVo.getFee_type());
                        fileIdentify.setTuitionAmount(offerVo.getFee());
                        if (GeneralTool.isEmpty(offerVo.getCourse())){
                            fileIdentify.setStatus(0);
                            fileIdentify.setCourseName(null);
                            try {
                                logStudentOfferItemOfferFileIdentifyMapper.insert(fileIdentify);
                            } catch (Exception e) {
                                log.error("添加解析offer信息失败。。。"+fileIdentify.getFkStudentOfferItemId());
                            }
                            continue;
                        }
                        log.info("添加数据offer。。。");
                        String courseName =studentOfferItem.getOldCourseCustomName();
                        if (studentOfferItem.getFkInstitutionCourseId()!=-1){
                            courseName = courseEnNameMap.get(studentOfferItem.getFkInstitutionCourseId());
                        }
                        boolean courseNameFlag = GeneralTool.isNotEmpty(offerVo.getCourse()) && GeneralTool.isNotEmpty(courseName);
                        if (!courseNameFlag) {
                            log.error("课程名无法匹配<|>itemId:{}<|>oldCourseCustomName:{}<|>courseEnName:{}", studentOffer.getId(), studentOfferItem.getOldCourseCustomName(), courseEnNameMap.get(studentOfferItem.getFkInstitutionCourseId()));
                        }
                        if (courseNameFlag){
                            int num = FuzzySearch.tokenSortPartialRatio(courseName, offerVo.getCourse());
                            fileIdentify.setScore(new BigDecimal(num));
                            if (num>=65) {
                                if (GeneralTool.isEmpty(map.get("num"))){
                                    map.put("num",num);
                                    map.put("fkCurrencyTypeNum", offerVo.getFee_type());
                                    map.put("tuitionAmount", offerVo.getFee());
                                }else if (Integer.parseInt(map.get("num").toString())<num && GeneralTool.isNotEmpty(map.get("num"))){
                                    map.put("num",num);
                                    map.put("fkCurrencyTypeNum", offerVo.getFee_type());
                                    map.put("tuitionAmount", offerVo.getFee());
                                }
                            }
                        }
                        try {
                            logStudentOfferItemOfferFileIdentifyMapper.insert(fileIdentify);
                        } catch (Exception e) {
                            log.error("添加解析offer信息失败。。。"+fileIdentify.getFkStudentOfferItemId());
                        }
                    }
                    StudentOfferIdentify studentOfferIdentify = new StudentOfferIdentify();
                    Boolean flag=false;
                    if (GeneralTool.isNotEmpty(map.get("fkCurrencyTypeNum")) && GeneralTool.isEmpty(studentOfferItem.getFkCurrencyTypeNum())){
                        studentOfferItem.setFkCurrencyTypeNum(map.get("fkCurrencyTypeNum").toString());
                        studentOfferIdentify.setFkCurrencyTypeNum(studentOfferItem.getFkCurrencyTypeNum());
                        flag=true;
                    }
                    if(GeneralTool.isNotEmpty(map.get("tuitionAmount")) && GeneralTool.isEmpty(studentOfferItem.getTuitionAmount()) && map.get("tuitionAmount")!="-1.00"){
                        studentOfferItem.setTuitionAmount(new BigDecimal(String.valueOf(map.get("tuitionAmount"))));
                        studentOfferIdentify.setTuitionAmount(studentOfferItem.getTuitionAmount());

                        flag=true;
                    }
                    if (flag){
                        studentOfferIdentify.setFkStudentOfferItemId(studentOffer.getId());
                        studentOfferIdentify.setGmtCreate(new Date());
                        studentOfferIdentify.setGmtCreateUser("admin");
                        studentOfferIdentifyMapper.insert(studentOfferIdentify);
                        studentOfferItem.setGmtModifiedUser("suni");
                        studentOfferItem.setGmtModified(new Date());
                        studentOfferItemMapper.updateById(studentOfferItem);
                    }
                }else{
                    log.info("添加数据。。。");
                    logStudentOfferItemOfferFileIdentifyMapper.insert(fileIdentify);
                }
            }
        log.info("数据处理完成。。。");
        redisClient.del(CacheKeyConstants.STUDENT_ANALYZE_OFFER_LOCK_KEY);
    }
    public AnalyzeOfferVo analyzeOfferClient(SaleFileDto saleFileDto) {
        try {
            String fileName = saleFileDto.getFileName();
            byte[] fileBytes = saleFileDto.getBytes();
            MultipartFile multipartFile = new MockMultipartFile(fileName, fileName, "application/octet-stream", fileBytes);

            JSONObject jsonObject = HttpUtils.sendFile(analyzeOfferAddress, multipartFile);
            if (Objects.isNull(jsonObject)) {
                int i = 0;
                while (jsonObject==null && i<=20){
                    jsonObject = HttpUtils.sendFile(analyzeOfferAddress, multipartFile);
                    i++;
                }
            }
            if (GeneralTool.isEmpty(jsonObject)){
                log.info("获取数据失败");
                return null;
            }
            // 解析返回的 JSON 数据
            ObjectMapper objectMapper = new ObjectMapper();
            AnalyzeOfferVo analyzeOfferVo = objectMapper.readValue(jsonObject.toJSONString(), AnalyzeOfferVo.class);
            log.info("获取文件数据结束。。。。");
            return analyzeOfferVo;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
}

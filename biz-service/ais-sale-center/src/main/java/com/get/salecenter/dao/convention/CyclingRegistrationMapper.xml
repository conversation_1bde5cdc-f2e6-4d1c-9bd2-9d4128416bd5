<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.convention.CyclingRegistrationMapper">
  <insert id="insert" parameterType="com.get.salecenter.entity.CyclingRegistration">
    insert into m_cycling_registration (id, fk_convention_id, name, 
      gender, mobile, id_type, 
      id_num, company, pay_amount, 
      pay_order_num, pay_status, gmt_create, 
      gmt_create_user, gmt_modified, gmt_modified_user
      )
    values (#{id,jdbcType=BIGINT}, #{fkConventionId,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, 
      #{gender,jdbcType=INTEGER}, #{mobile,jdbcType=VARCHAR}, #{idType,jdbcType=INTEGER}, 
      #{idNum,jdbcType=VARCHAR}, #{company,jdbcType=VARCHAR}, #{payAmount,jdbcType=DECIMAL}, 
      #{payOrderNum,jdbcType=VARCHAR}, #{payStatus,jdbcType=INTEGER}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.get.salecenter.entity.CyclingRegistration">
    insert into m_cycling_registration
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkConventionId != null">
        fk_convention_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="gender != null">
        gender,
      </if>
      <if test="mobile != null">
        mobile,
      </if>
      <if test="idType != null">
        id_type,
      </if>
      <if test="idNum != null">
        id_num,
      </if>
      <if test="company != null">
        company,
      </if>
      <if test="payAmount != null">
        pay_amount,
      </if>
      <if test="payOrderNum != null">
        pay_order_num,
      </if>
      <if test="payStatus != null">
        pay_status,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkConventionId != null">
        #{fkConventionId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        #{gender,jdbcType=INTEGER},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="idType != null">
        #{idType,jdbcType=INTEGER},
      </if>
      <if test="idNum != null">
        #{idNum,jdbcType=VARCHAR},
      </if>
      <if test="company != null">
        #{company,jdbcType=VARCHAR},
      </if>
      <if test="payAmount != null">
        #{payAmount,jdbcType=DECIMAL},
      </if>
      <if test="payOrderNum != null">
        #{payOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="payStatus != null">
        #{payStatus,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateById" parameterType="com.get.salecenter.entity.CyclingRegistration">
    update m_cycling_registration
    <set>
      <if test="fkConventionId != null">
        fk_convention_id = #{fkConventionId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        gender = #{gender,jdbcType=INTEGER},
      </if>
      <if test="mobile != null">
        mobile = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="idType != null">
        id_type = #{idType,jdbcType=INTEGER},
      </if>
      <if test="idNum != null">
        id_num = #{idNum,jdbcType=VARCHAR},
      </if>
      <if test="company != null">
        company = #{company,jdbcType=VARCHAR},
      </if>
      <if test="payAmount != null">
        pay_amount = #{payAmount,jdbcType=DECIMAL},
      </if>
      <if test="payOrderNum != null">
        pay_order_num = #{payOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="payStatus != null">
        pay_status = #{payStatus,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.get.salecenter.entity.CyclingRegistration">
    update m_cycling_registration
    set fk_convention_id = #{fkConventionId,jdbcType=BIGINT},
      name = #{name,jdbcType=VARCHAR},
      gender = #{gender,jdbcType=INTEGER},
      mobile = #{mobile,jdbcType=VARCHAR},
      id_type = #{idType,jdbcType=INTEGER},
      id_num = #{idNum,jdbcType=VARCHAR},
      company = #{company,jdbcType=VARCHAR},
      pay_amount = #{payAmount,jdbcType=DECIMAL},
      pay_order_num = #{payOrderNum,jdbcType=VARCHAR},
      pay_status = #{payStatus,jdbcType=INTEGER},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
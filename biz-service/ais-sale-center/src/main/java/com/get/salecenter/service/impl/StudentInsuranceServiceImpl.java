package com.get.salecenter.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.GetDateUtil;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.service.impl.GetServiceImpl;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.file.utils.FileUtils;
import com.get.financecenter.feign.IFinanceCenterClient;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.dao.sale.*;
import com.get.salecenter.dto.*;
import com.get.salecenter.dto.query.InsuranceSummaryQueryDto;
import com.get.salecenter.entity.*;
import com.get.salecenter.service.*;
import com.get.salecenter.utils.MyStringUtils;
import com.get.salecenter.utils.VerifyDataPermissionsUtils;
import com.get.salecenter.utils.sale.GetAgentLabelDataUtils;
import com.get.salecenter.vo.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2022/1/12
 * @TIME: 15:33
 * @Description:
 **/
@Service
public class StudentInsuranceServiceImpl extends GetServiceImpl<StudentInsuranceMapper,StudentInsurance> implements IStudentInsuranceService {
    @Resource
    private UtilService utilService;
    @Resource
    private IMediaAndAttachedService attachedService;
    @Resource
    private StudentInsuranceMapper studentInsuranceMapper;
    @Resource
    private VerifyDataPermissionsUtils verifyDataPermissionsUtils;
    @Resource
    private ICommentService commentService;
    @Resource
    @Lazy
    private IAgentService agentService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    private IStudentProjectRoleStaffService projectRoleStaffService;
    @Resource
    private StudentProjectRoleStaffMapper studentProjectRoleStaffMapper;
    @Resource
    private IFinanceCenterClient financeCenterClient;
    @Resource
    private PayablePlanMapper payablePlanMapper;
    @Resource
    private ReceivablePlanMapper receivablePlanMapper;
    @Resource
    private BusinessChannelMapper businessChannelMapper;

    @Resource
    private IBusinessChannelService businessChannelService;
    @Resource
    @Lazy
    private IStudentService studentService;
    @Resource
    @Lazy
    private IPayablePlanService payablePlanService;
    @Resource
    @Lazy
    private IReceivablePlanService receivablePlanService;
    @Resource
    private AgentMapper agentMapper;
    @Resource
    private AgentStaffMapper agentStaffMapper;
    @Resource
    private IBusinessProviderService businessProviderService;
    @Resource
    private GetAgentLabelDataUtils getAgentLabelDataUtils;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addStudentInsurance(StudentInsuranceDto studentInsuranceDto) {
        // 参数校验
        paramCheck(studentInsuranceDto);

        if (GeneralTool.isEmpty(studentInsuranceDto.getId())) {
            StudentInsurance studentInsurance = BeanCopyUtils.objClone(studentInsuranceDto, StudentInsurance::new);
            utilService.updateUserInfoToEntity(studentInsurance);
            studentInsuranceMapper.insert(studentInsurance);
            studentInsurance.setNum(MyStringUtils.getInsuranceNum(studentInsurance.getId()));
            studentInsuranceMapper.updateById(studentInsurance);
            setProjectRoleStaff(studentInsuranceDto, studentInsurance);
        } else {
            StudentInsurance studentInsurance = BeanCopyUtils.objClone(studentInsuranceDto, StudentInsurance::new);
            utilService.updateUserInfoToEntity(studentInsurance);
            studentInsuranceMapper.updateById(studentInsurance);
        }
    }

    @Override
    public List<StudentInsuranceVo> getStudentInsuranceDtos(StudentInsuranceDto StudentInsuranceDto, Page page) {
        return null;
    }

    /**
     * 操作限制，参数为空校验
     *
     * @param studentInsuranceDto 参数
     */
    private void paramCheck(StudentInsuranceDto studentInsuranceDto) {
        if (GeneralTool.isEmpty(studentInsuranceDto) || GeneralTool.isEmpty(studentInsuranceDto.getFkStudentId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        // 留学保险详细页，操作限制，显示【服务提供商/产品】【支付方式】录入，value1=0/1为总开关，0不开启，1开启
        boolean flag;
        Map<Long, String> flagMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.STUDENT_INSURANCE_DETAIL_OPT_LIMIT.key, 1).getData();
        String configValue1 = flagMap.get(SecureUtil.getFkCompanyId());
        flag = configValue1.equals("1");
        if (flag) {
            if (GeneralTool.isEmpty(studentInsuranceDto.getFkBusinessProviderId()) ||
                    GeneralTool.isEmpty(studentInsuranceDto.getBusinessProviderProduct()) ||
                    GeneralTool.isEmpty(studentInsuranceDto.getPaymentMethod())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
            }
        }
        //判断学生是否填写护照信息
        StudentVo studentVo = studentService.findStudentById(studentInsuranceDto.getFkStudentId());
        if (GeneralTool.isEmpty(studentVo) || (GeneralTool.isEmpty(studentVo.getPassportNum()) && studentInsuranceDto.getType() == 0)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("add_student_insurance_error"));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public StudentInsuranceVo updateStudentInsurance(StudentInsuranceDto studentInsuranceDto) {
        // 参数校验
        paramCheck(studentInsuranceDto);

        StudentInsurance studentInsurance = BeanCopyUtils.objClone(studentInsuranceDto, StudentInsurance::new);
        utilService.updateUserInfoToEntity(studentInsurance);
        studentInsuranceMapper.updateById(studentInsurance);

        if (GeneralTool.isNotEmpty(studentInsuranceDto.getRoleStaffVo())){
            //先删后增
            studentProjectRoleStaffMapper.delete(Wrappers.lambdaQuery(StudentProjectRoleStaff.class)
                    .eq(StudentProjectRoleStaff::getFkTableId, studentInsuranceDto.getId())
                    .eq(StudentProjectRoleStaff::getFkTableName,TableEnum.SALE_STUDENT_INSURANCE.key)
            );
            setProjectRoleStaff(studentInsuranceDto,studentInsurance);
        }
        return findStudentInsuranceById(studentInsurance.getId());
    }

    private void setProjectRoleStaff(StudentInsuranceDto studentInsuranceDto, StudentInsurance studentInsurance) {
        if (GeneralTool.isNotEmpty(studentInsuranceDto.getRoleStaffVo())) {
            List<ProjectRoleStaffDto> roleStaffVos = studentInsuranceDto.getRoleStaffVo();
            for (ProjectRoleStaffDto roleStaffVo : roleStaffVos) {
                StudentProjectRoleStaffDto projectRoleStaffVo = new StudentProjectRoleStaffDto();

                projectRoleStaffVo.setFkTableId(studentInsurance.getId());
                projectRoleStaffVo.setFkTableName(TableEnum.SALE_STUDENT_INSURANCE.key);
                projectRoleStaffVo.setFkStaffId(roleStaffVo.getFkStaffId());
                projectRoleStaffVo.setFkStudentProjectRoleId(roleStaffVo.getFkRoleId());
                projectRoleStaffVo.setIsActive(true);

                projectRoleStaffService.addProjectRoleStaff(projectRoleStaffVo);
            }
        }
    }

    @Override
    public void deleteStudentInsurance(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        studentInsuranceMapper.deleteById(id);
    }

    @Override
    public StudentInsuranceVo findStudentInsuranceById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        verifyDataPermissionsUtils.verifyByBusinessId(id,VerifyDataPermissionsUtils.INS_O);
        StudentInsurance StudentInsurance = studentInsuranceMapper.selectById(id);
        StudentInsuranceVo studentInsuranceVo = BeanCopyUtils.objClone(StudentInsurance, StudentInsuranceVo::new);
        Set<Long> countryIds = new HashSet<>();
        countryIds.add(studentInsuranceVo.getFkAreaCountryId());
        Set<Long> fkAgentIds = new HashSet<>();
        fkAgentIds.add(studentInsuranceVo.getFkAgentId());
        Set<Long> fkStaffIds = new HashSet<>();
        fkStaffIds.add(studentInsuranceVo.getFkStaffId());
        //根据代理ids获取名称
        Map<Long, String> agentNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkAgentIds)) {
            agentNamesByIds = agentService.getAgentNamesByIds(fkAgentIds);
        }
        //根据员工ids获取姓名
        Map<Long, String> staffNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkStaffIds)) {
            Result<Map<Long, String>> staffNameResult = permissionCenterClient.getStaffNameMap(fkStaffIds);
            if (staffNameResult.isSuccess() && GeneralTool.isNotEmpty(staffNameResult)) {
                staffNamesByIds = staffNameResult.getData();
            }
        }
        //根据国家ids获取国家名称
        Map<Long, String> countryNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(countryIds)) {
            Result<Map<Long, String>> countryNameByIdsResult = institutionCenterClient.getCountryNamesByIds(countryIds);
            if (countryNameByIdsResult.isSuccess() && GeneralTool.isNotEmpty(countryNameByIdsResult.getData())) {
                countryNamesByIds = countryNameByIdsResult.getData();
            }
        }
        Set<String> currencyTypeNumAccommodations = new HashSet<>();
        currencyTypeNumAccommodations.add(studentInsuranceVo.getFkCurrencyTypeNumInsurance());
        Set<String> currencyTypeNumCommissions = new HashSet<>();
        currencyTypeNumCommissions.add(studentInsuranceVo.getFkCurrencyTypeNumCommission()
        );
        //根据币种ids获取名称
        Map<String, String> currencyAccommodationNamesByNums = new HashMap<>();
        if (GeneralTool.isNotEmpty(currencyTypeNumAccommodations)) {
            Result<Map<String, String>> result = financeCenterClient.getCurrencyNamesByNums(currencyTypeNumAccommodations);
            if (result.isSuccess() && result.getData() != null) {
                currencyAccommodationNamesByNums = result.getData();
            }
        }
        //根据币种ids获取名称
        Map<String, String> currencyCommissionsNamesByNums = new HashMap<>();
        if (GeneralTool.isNotEmpty(currencyTypeNumCommissions)) {
            Result<Map<String, String>> result = financeCenterClient.getCurrencyNamesByNums(currencyTypeNumCommissions);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                currencyCommissionsNamesByNums = result.getData();
            }
        }
        if (GeneralTool.isNotEmpty(studentInsuranceVo.getFkStudentId())) {
            studentInsuranceVo.setStudentName(studentService.getStudentNameById(studentInsuranceVo.getFkStudentId()));
        }

        //是否是无代理（无需结算）留学保险计划
        //留学保险设置无代理（无需结算）
        LambdaQueryWrapper<Agent> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Agent::getName,"NR");
        wrapper.eq(Agent::getIsActive,1);
        Agent agent = agentMapper.selectOne(wrapper);
        if (GeneralTool.isNotEmpty(agent) && GeneralTool.isNotEmpty(studentInsuranceVo.getFkAgentId()) && agent.getId().longValue() ==  studentInsuranceVo.getFkAgentId().longValue()){
            studentInsuranceVo.setIsNosettlement(true);
        }

        Map<Long, String> companyNamesByIds = new HashMap<>();
        Map<Long, List<Long>> empty = new HashMap<>();
        List<StudentInsuranceVo> list = new ArrayList<>();
        list.add(studentInsuranceVo);
        Set<Long> ids = list.stream().map(StudentInsuranceVo::getId).filter(Objects::nonNull).collect(Collectors.toSet());
        setNameList(list, countryNamesByIds, companyNamesByIds, empty, empty, ids,
                agentNamesByIds, staffNamesByIds, currencyAccommodationNamesByNums, currencyCommissionsNamesByNums,null,null);
//        setNameList(studentInsuranceVo, countryNamesByIds,
//                agentNamesByIds, staffNamesByIds, currencyAccommodationNamesByNums, currencyCommissionsNamesByNums);

        return studentInsuranceVo;
    }

    @Override
    public void closeStudentInsuranceById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        StudentInsurance StudentInsurance = studentInsuranceMapper.selectById(id);
        if (GeneralTool.isEmpty(StudentInsurance)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        StudentInsurance.setStatus(0);
        utilService.updateUserInfoToEntity(StudentInsurance);
        studentInsuranceMapper.updateById(StudentInsurance);
    }

    @Override
    public void activationStudentInsuranceById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        StudentInsurance StudentInsurance = studentInsuranceMapper.selectById(id);
        if (GeneralTool.isEmpty(StudentInsurance)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        StudentInsurance.setStatus(1);
        utilService.updateUserInfoToEntity(StudentInsurance);
        studentInsuranceMapper.updateById(StudentInsurance);
    }

    @Override
    public List<Map<String, Object>> findMediaAndAttachedType() {
        return FileTypeEnum.enumsTranslation2Arrays(FileTypeEnum.STUDENTINSURANCE);
    }

    @Override
    public List<MediaAndAttachedVo> addStudentInsuranceMedia(List<MediaAndAttachedDto> mediaAttachedVos) {
        if (GeneralTool.isEmpty(mediaAttachedVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        List<MediaAndAttachedVo> mediaAndAttachedVos = new ArrayList<>();
        for (MediaAndAttachedDto mediaAndAttachedDto : mediaAttachedVos) {
            //设置插入的表
            String tableName = TableEnum.SALE_STUDENT_INSURANCE.key;
            mediaAndAttachedDto.setFkTableName(tableName);
            mediaAndAttachedVos.add(attachedService.addMediaAndAttached(mediaAndAttachedDto));
        }
        return mediaAndAttachedVos;
    }


    @Override
    public List<MediaAndAttachedVo> getStudentInsuranceMedia(MediaAndAttachedDto attachedVo, Page page) {
        if (GeneralTool.isEmpty(attachedVo.getFkTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        verifyDataPermissionsUtils.verifyByBusinessId(attachedVo.getFkTableId(),VerifyDataPermissionsUtils.INS_O);
        attachedVo.setFkTableName(TableEnum.SALE_STUDENT_INSURANCE.key);
        return attachedService.getMediaAndAttachedDto(attachedVo, page);
    }

    @Override
    public List<StudentInsuranceVo> getStudentInsuranceList(StudentInsuranceDto StudentInsuranceDto, Page page) {
        IPage<StudentInsuranceVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<StudentInsuranceVo> collect = studentInsuranceMapper.getStudentInsuranceList(iPage, StudentInsuranceDto);
        //获取学生集合的所有国家ids
        Set<Long> countryIds = collect.stream().map(StudentInsuranceVo::getFkAreaCountryId).collect(Collectors.toSet());
        Set<Long> fkAgentIds = collect.stream().map(StudentInsuranceVo::getFkAgentId).collect(Collectors.toSet());
        Set<Long> fkStaffIds = collect.stream().map(StudentInsuranceVo::getFkStaffId).collect(Collectors.toSet());
        //根据代理ids获取名称
        Map<Long, String> agentNamesByIds = new HashMap<>();
//        代理标签
        Map<Long, List<AgentLabelVo>> agentLabelMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkAgentIds)) {
            agentNamesByIds = agentService.getAgentNamesByIds(fkAgentIds);
            agentLabelMap = getAgentLabelDataUtils.getAgentLabelMap(fkAgentIds).getAgentLabelMap();
        }
        //根据员工ids获取姓名
        Map<Long, String> staffNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkStaffIds)) {
            Result<Map<Long, String>> staffNameResult = permissionCenterClient.getStaffNameMap(fkStaffIds);
            if (staffNameResult.isSuccess() && GeneralTool.isNotEmpty(staffNameResult)) {
                staffNamesByIds = staffNameResult.getData();
            }
        }
        //根据国家ids获取国家名称
        Map<Long, String> countryNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(countryIds)) {
            Result<Map<Long, String>> result = institutionCenterClient.getCountryNamesByIds(countryIds);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                countryNamesByIds = result.getData();
            }
        }
        Set<String> currencyTypeNumAccommodations = collect.stream().map(StudentInsuranceVo::getFkCurrencyTypeNumInsurance).collect(Collectors.toSet());
        Set<String> currencyTypeNumCommissions = collect.stream().map(StudentInsuranceVo::getFkCurrencyTypeNumCommission).collect(Collectors.toSet());
        //根据币种ids获取名称
        Map<String, String> currencyAccommodationNamesByNums = new HashMap<>();
        if (GeneralTool.isNotEmpty(currencyTypeNumAccommodations)) {
            Result<Map<String, String>> result = financeCenterClient.getCurrencyNamesByNums(currencyTypeNumAccommodations);
            if (result.isSuccess() && result.getData() != null) {
                currencyAccommodationNamesByNums = result.getData();
            }
        }
        //根据币种ids获取名称
        Map<String, String> currencyCommissionsNamesByNums = new HashMap<>();
        if (GeneralTool.isNotEmpty(currencyTypeNumCommissions)) {
            Result<Map<String, String>> result = financeCenterClient.getCurrencyNamesByNums(currencyTypeNumCommissions);
            if (result.isSuccess() && result.getData() != null) {
                currencyCommissionsNamesByNums = result.getData();
            }
        }
        Map<Long, String> companyNamesByIds = new HashMap<>();
        Map<Long, List<Long>> empty = new HashMap<>();
        Set<Long> ids = collect.stream().map(StudentInsuranceVo::getId).filter(Objects::nonNull).collect(Collectors.toSet());
        setNameList(collect, countryNamesByIds, companyNamesByIds, empty, empty, ids,
                agentNamesByIds, staffNamesByIds, currencyAccommodationNamesByNums, currencyCommissionsNamesByNums,null,agentLabelMap);
//        for (StudentInsuranceVo studentInsuranceDto : collect) {
//            setNameList(studentInsuranceDto, countryNamesByIds,
//                    agentNamesByIds, staffNamesByIds, currencyAccommodationNamesByNums, currencyCommissionsNamesByNums);
//            if (GeneralTool.isNotEmpty(studentInsuranceDto.getFkStudentId())) {
//                studentInsuranceDto.setStudentName(studentService.getStudentNameById(studentInsuranceDto.getFkStudentId()));
//            }
//        }
        page.setAll((int) iPage.getTotal());
        return collect;
    }


    @Override
    public Long editComment(CommentDto commentDto) {
        SaleComment comment = BeanCopyUtils.objClone(commentDto, SaleComment::new);
        if (GeneralTool.isNotEmpty(commentDto)) {
            if (GeneralTool.isNotEmpty(commentDto.getId())) {
                comment.setFkTableName(TableEnum.SALE_STUDENT_INSURANCE.key);
                commentService.updateComment(comment);
            } else {
                comment.setFkTableName(TableEnum.SALE_STUDENT_INSURANCE.key);
                commentService.addComment(comment);
            }
        }
        return comment.getId();
    }

    @Override
    public List<CommentVo> getComments(CommentDto commentDto, Page page) {
        commentDto.setFkTableName(TableEnum.SALE_STUDENT_INSURANCE.key);
        return commentService.datas(commentDto, page);
    }

    @Override
    public List<StudentInsuranceVo> getStudentInsuranceSummary(InsuranceSummaryQueryDto studentInsurance, Page page, String[] times) {
        if (GeneralTool.isNotEmpty(studentInsurance.getFkCompanyId())) {
            if (!SecureUtil.validateCompany(studentInsurance.getFkCompanyId())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
            }
        } else {
            if (!SecureUtil.validateCompanys(studentInsurance.getFkCompanyIds())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
            }
        }
        long startTime = System.currentTimeMillis();

        Long staffId = SecureUtil.getStaffId();
        //获取当前登录人的所有业务下属
        List<Long> staffIds = new ArrayList<>();
        Result<List<Long>> result_ = permissionCenterClient.getStaffFollowerIds(staffId);
        if (result_.isSuccess() && GeneralTool.isNotEmpty(result_.getData())) {
            staffIds.addAll(result_.getData());
        }
        staffIds.add(staffId);

        long endTime1 = System.currentTimeMillis();

        IPage<StudentInsuranceVo> iPage = null;
        if (GeneralTool.isNotEmpty(page)) {
            iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        }
        String studentName = studentInsurance.getStudentName();
        if (StringUtils.isNotBlank(studentName)) {
            studentInsurance.setStudentName(studentName.replace(" ", "").trim());
        }

        //针对like的字段转为小写
        if(GeneralTool.isNotEmpty(studentInsurance.getStudentName()))
        {
            studentInsurance.setStudentName(studentInsurance.getStudentName().toLowerCase());
        }
        if(GeneralTool.isNotEmpty(studentInsurance.getAgentName()))
        {
            studentInsurance.setAgentName(studentInsurance.getAgentName().toLowerCase());
        }
        if(GeneralTool.isNotEmpty(studentInsurance.getStaffName()))
        {
            studentInsurance.setStaffName(studentInsurance.getStaffName().toLowerCase());
        }
        if(GeneralTool.isNotEmpty(studentInsurance.getMemberName()))
        {
            studentInsurance.setMemberName(studentInsurance.getMemberName().toLowerCase());
        }
        if(GeneralTool.isNotEmpty(studentInsurance.getInsuranceNum()))
        {
            studentInsurance.setInsuranceNum(studentInsurance.getInsuranceNum().toLowerCase());
        }

        studentInsurance.setIsHidden(SecureUtil.getStaffInfo().getIsStudentOfferItemFinancialHiding());
        List<StudentInsuranceVo> collect = studentInsuranceMapper.getStudentInsuranceSummary(iPage, studentInsurance, staffIds, SecureUtil.getStaffInfo().getIsStudentAdmin(), SecureUtil.getPermissionGroupInstitutionIds(), SecureUtil.getStaffBoundBdIds());

        long endTime2 = System.currentTimeMillis();

        //获取学生集合的所有国家ids
        Set<Long> countryIds = collect.stream().map(StudentInsuranceVo::getFkAreaCountryId).collect(Collectors.toSet());
        Set<Long> fkAgentIds = collect.stream().map(StudentInsuranceVo::getFkAgentId).collect(Collectors.toSet());
        Set<Long> fkStaffIds = collect.stream().map(StudentInsuranceVo::getFkStaffId).collect(Collectors.toSet());
        Set<Long> companyIds = collect.stream().map(StudentInsuranceVo::getFkCompanyId).collect(Collectors.toSet());
        //根据代理ids获取名称
        Map<Long, String> agentNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkAgentIds)) {
            agentNamesByIds = agentService.getAgentNamesByIds(fkAgentIds);
        }
        //代理标签
        Map<Long, List<AgentLabelVo>> agentLabelMap = getAgentLabelDataUtils.getAgentLabelMap(fkAgentIds).getAgentLabelMap();
        //根据员工ids获取姓名
        Map<Long, String> staffNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkStaffIds)) {
            Result<Map<Long, String>> result = permissionCenterClient.getStaffNameMap(fkStaffIds);
            if (result.isSuccess() && result.getData() != null) {
                staffNamesByIds = result.getData();
            }
        }
        //根据公司ids获取公司名称
        Map<Long, String> companyNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(companyIds)) {
            Result<Map<Long, String>> result1 = permissionCenterClient.getCompanyNamesByIds(companyIds);
            if (result1.isSuccess()) {
                companyNamesByIds = result1.getData();
            }
        }
        //根据国家ids获取国家名称
        Map<Long, String> countryNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(countryIds)) {
            Result<Map<Long, String>> countryNameResult = institutionCenterClient.getCountryNamesByIds(new HashSet<>(countryIds));
            if (countryNameResult.isSuccess() && GeneralTool.isNotEmpty(countryNameResult.getData())) {
                countryNamesByIds = countryNameResult.getData();
            }
        }
        Set<String> currencyTypeNumAccommodations = collect.stream().map(StudentInsuranceVo::getFkCurrencyTypeNumInsurance).collect(Collectors.toSet());
        Set<String> currencyTypeNumCommissions = collect.stream().map(StudentInsuranceVo::getFkCurrencyTypeNumCommission).collect(Collectors.toSet());
        //根据币种ids获取名称
        Map<String, String> currencyAccommodationNamesByNums = new HashMap<>();
        if (GeneralTool.isNotEmpty(currencyTypeNumAccommodations)) {
            Result<Map<String, String>> result = financeCenterClient.getCurrencyNamesByNums(currencyTypeNumAccommodations);
            if (result.isSuccess() && result.getData() != null) {
                currencyAccommodationNamesByNums = result.getData();
            }
        }
        //根据币种ids获取名称
        Map<String, String> currencyCommissionsNamesByNums = new HashMap<>();
        if (GeneralTool.isNotEmpty(currencyTypeNumCommissions)) {
            Result<Map<String, String>> result = financeCenterClient.getCurrencyNamesByNums(currencyTypeNumCommissions);
            if (result.isSuccess() && result.getData() != null) {
                currencyCommissionsNamesByNums = result.getData();
            }
        }
        Set<Long> ids = collect.stream().map(StudentInsuranceVo::getId).filter(Objects::nonNull).collect(Collectors.toSet());
        Set<Long> businessIds = collect.stream().map(StudentInsuranceVo::getFkBusinessChannelId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Long, List<Long>> reMap = receivablePlanService.getReceivablePlanIds(TableEnum.SALE_STUDENT_INSURANCE.key, ids);
        Map<Long, List<Long>> paMap = payablePlanService.getPayablePlanIds(TableEnum.SALE_STUDENT_INSURANCE.key, ids);
        reMap.putAll(receivablePlanService.getReceivablePlanIds(TableEnum.BUSINESS_CHANNEL_INS.key, businessIds));
        paMap.putAll(payablePlanService.getPayablePlanIds(TableEnum.BUSINESS_CHANNEL_INS.key, businessIds));
//        for (StudentInsuranceVo StudentInsuranceVo : collect) {
//            setNameList(StudentInsuranceVo, countryNamesByIds,
//                    agentNamesByIds, staffNamesByIds, currencyAccommodationNamesByNums, currencyCommissionsNamesByNums);
//            String companyName = companyNamesByIds.get(StudentInsuranceVo.getFkCompanyId());
//            StudentInsuranceVo.setFkCompanyName(companyName);
//        }
        List<InsurancePayFormDetailVo> paymentFormItemList = studentInsuranceMapper.getPaidAmountByIds(ids);
        Map<Long, List<InsurancePayFormDetailVo>> payFormDetailMap = null;
        if (GeneralTool.isNotEmpty(paymentFormItemList)){
            payFormDetailMap = paymentFormItemList.stream().collect(Collectors.groupingBy(InsurancePayFormDetailVo::getInsuranceId));
        }
        setNameList(collect, countryNamesByIds, companyNamesByIds, reMap, paMap, ids,
                agentNamesByIds, staffNamesByIds, currencyAccommodationNamesByNums, currencyCommissionsNamesByNums,payFormDetailMap,agentLabelMap);
        if (GeneralTool.isNotEmpty(page) && GeneralTool.isNotEmpty(iPage)) {
            page.setAll((int) iPage.getTotal());
        }
        long endTime = System.currentTimeMillis();
        if (GeneralTool.isNotEmpty(times)) {
            times[0] = String.valueOf(endTime2 - endTime1);
            times[1] = String.valueOf((endTime - startTime) - (endTime2 - endTime1));
        }
        return collect;
    }

    private void setNameList(List<StudentInsuranceVo> studentInsuranceVos, Map<Long, String> countryNamesByIds, Map<Long, String> companyNamesByIds,
                             Map<Long, List<Long>> reMap,
                             Map<Long, List<Long>> paMap,
                             Set<Long> ids,
                             Map<Long, String> agentNamesByIds, Map<Long, String> staffNamesByIds,
                             Map<String, String> currencyAccommodationNamesByNums,
                             Map<String, String> currencyCommissionsNamesByNums,
                             Map<Long, List<InsurancePayFormDetailVo>> payFormDetailMap,Map<Long, List<AgentLabelVo>> agentLabelMap
    ) {
//        Long fkAgentId;
//        String nameNote;
//        Set<Long> agentIds = studentInsuranceVos.stream().map(StudentInsuranceVo::getFkAgentId).filter(Objects::nonNull).collect(Collectors.toSet());
//        Map<Long, Agent> agentsByIds = agentService.getAgentsByIds(agentIds);
        Set<Long> studentIds = studentInsuranceVos.stream().map(StudentInsuranceVo::getFkStudentId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Long, String> studentNameByIds = studentService.getStudentNameByIds(studentIds);
        Set<Long> bChannelIds = studentInsuranceVos.stream().map(StudentInsuranceVo::getFkBusinessChannelId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Long, String> businessChannelServiceNamesByIds = businessChannelService.getNamesByIds(bChannelIds);
        List<StudentProjectRoleStaffVo> studentProjectRoleStaffVos = studentProjectRoleStaffMapper.selectProjectStaff(TableEnum.SALE_STUDENT_INSURANCE.key, new ArrayList<>(ids));
        Map<Long, List<StudentProjectRoleStaffVo>> studentProjectRoleStaffMap =
                studentProjectRoleStaffVos.stream().collect(Collectors.groupingBy(StudentProjectRoleStaffVo::getFkTableId));
        // 获取服务提供商名称
        Set<Long> fkBusinessProviderIds = studentInsuranceVos.stream().map(StudentInsuranceVo::getFkBusinessProviderId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Long, String> businessProviderNameMap = businessProviderService.getNamesByIds(fkBusinessProviderIds);

        for (StudentInsuranceVo studentInsuranceVo : studentInsuranceVos) {
            List<Long> l1 = reMap.get(studentInsuranceVo.getId());
            if (l1 == null) {
                l1 = new ArrayList<>();
            }
            List<Long> l2 = paMap.get(studentInsuranceVo.getId());
            if (l2 != null) {
                l1.addAll(l2);
            }
            studentInsuranceVo.setARAPIds(l1);
            studentInsuranceVo.setStudentName(studentNameByIds.get(studentInsuranceVo.getFkStudentId()));
            String countryName = countryNamesByIds.get(studentInsuranceVo.getFkAreaCountryId());
            String companyName = companyNamesByIds.get(studentInsuranceVo.getFkCompanyId());
            studentInsuranceVo.setFkCompanyName(companyName);
            String accommodationNames = currencyAccommodationNamesByNums.get(studentInsuranceVo.getFkCurrencyTypeNumInsurance());
            String commissionsNames = currencyCommissionsNamesByNums.get(studentInsuranceVo.getFkCurrencyTypeNumCommission());
            studentInsuranceVo.setFkCurrencyTypeNumInsuranceName(accommodationNames);
            studentInsuranceVo.setFkCurrencyTypeNumCommissionName(commissionsNames);
            String staffName = staffNamesByIds.get(studentInsuranceVo.getFkStaffId());
            String agentName = agentNamesByIds.get(studentInsuranceVo.getFkAgentId());
            studentInsuranceVo.setFkAreaCountryName(countryName);
            studentInsuranceVo.setStaffName(staffName);
            studentInsuranceVo.setFkAgentName(agentName);
            if (GeneralTool.isNotEmpty(agentLabelMap)){
                studentInsuranceVo.setAgentLabelVos(agentLabelMap.get(studentInsuranceVo.getFkAgentId()));
            }

//            if (agentsByIds != null) {
//                fkAgentId = studentInsuranceVo.getFkAgentId();
//                if (agentsByIds.containsKey(fkAgentId)) {
//                    nameNote = agentsByIds.get(fkAgentId).getNameNote();
//                    if (StringUtils.isNotBlank(nameNote)) {
//                        studentInsuranceVo.setFkAgentName(studentInsuranceVo.getFkAgentName() + "(" + nameNote + ")");
//                    }
//                }
//            }
            String locale = SecureUtil.getLocale();
            studentInsuranceVo.setArStatusName(ProjectExtraEnum.getValueByKey(studentInsuranceVo.getArStatus(), ProjectExtraEnum.AR_STATUS,locale));
            studentInsuranceVo.setApStatusName(ProjectExtraEnum.getValueByKey(studentInsuranceVo.getApStatus(), ProjectExtraEnum.AP_STATUS,locale));
            studentInsuranceVo.setSettlementStatusName(ProjectExtraEnum.getValueByKey(studentInsuranceVo.getSettlementStatus(), ProjectExtraEnum.SETTLEMENT_TYPE,locale));
            if (GeneralTool.isNotEmpty(payFormDetailMap)) {
                      //币种，实付金额，付款时间
                List<InsurancePayFormDetailVo> paymentFormItems = new ArrayList<>();
                if (GeneralTool.isNotEmpty(payFormDetailMap)&&GeneralTool.isNotEmpty(payFormDetailMap.get(studentInsuranceVo.getId()))){
                    paymentFormItems = payFormDetailMap.get(studentInsuranceVo.getId());
                }
//            List<PayFormDetailVo> paymentFormItemList = studentOfferItemMapper.getPaidAmount(studentOfferItemListDto.getId());
                JSONArray payDetailList = new JSONArray();
                for (InsurancePayFormDetailVo paymentFormItem : paymentFormItems) {
                    JSONObject map = new JSONObject();
                    map.put("payableCurrencyTypeName", studentInsuranceVo.getPayableCurrencyTypeName());
                    map.put("actualPayableAmount", paymentFormItem.getActualPayableAmount());
                    map.put("gmtCreate", paymentFormItem.getActualPayTime());
                    payDetailList.add(map);
                }
                studentInsuranceVo.setPayDetailList(payDetailList);
            }
            if (GeneralTool.isNotEmpty(studentInsuranceVo.getFixedAmountPayable())) {
                if (GeneralTool.isNotEmpty(studentInsuranceVo.getFkAgentId())) {
                    studentInsuranceVo.setAmountPayable(studentInsuranceVo.getFixedAmountPayable());
                }
            }
//            if (GeneralTool.isNotEmpty(studentInsuranceVo.getFixedAmountReceivable())) {
//                studentInsuranceVo.setAmountReceivable(studentInsuranceVo.getFixedAmountReceivable());
//            }
            if (GeneralTool.isNotEmpty(studentInsuranceVo.getCommissionRatePayable())) {
                if (GeneralTool.isNotEmpty(studentInsuranceVo.getFkAgentId())) {
                    studentInsuranceVo.setAmountPayable(studentInsuranceVo.getCommissionRatePayable().divide(new BigDecimal("100")).multiply(studentInsuranceVo.getInsuranceAmount()));
                }
            }
//            if (GeneralTool.isNotEmpty(studentInsuranceVo.getCommissionRateReceivable())) {
//                studentInsuranceVo.setAmountReceivable(studentInsuranceVo.getCommissionRateReceivable().divide(new BigDecimal("100")).multiply(studentInsuranceVo.getInsuranceAmount()));
//            }
            //获取绑定的项目成员
            studentInsuranceVo.setChannelName(businessChannelServiceNamesByIds.get(studentInsuranceVo.getFkBusinessChannelId()));

//            List<Long> offerIds = new ArrayList<>();
//            offerIds.add(studentInsuranceVo.getId());
//            List<StudentProjectRoleStaffVo> studentProjectRoleStaffVos = studentProjectRoleStaffMapper.selectProjectStaff(TableEnum.SALE_STUDENT_INSURANCE.key, offerIds);
//            for (StudentProjectRoleStaffVo studentProjectRoleStaffDto : studentProjectRoleStaffVos) {
//                String[] staffIdStr = studentProjectRoleStaffDto.getStaffIdStr().split(",");
//                Set<Long> staffIds = Arrays.stream(staffIdStr).map(Long::valueOf).collect(Collectors.toSet());
//                Map<Long, String> staffNameMap = new HashMap<>();
//                Result<Map<Long, String>> result = permissionCenterClient.getStaffNameMap(staffIds);
//                if (result.isSuccess() && result.getData() != null) {
//                    staffNameMap.putAll(result.getData());
//                }
//                studentProjectRoleStaffDto.setStaffName(StringUtils.join(staffNameMap.values(), ","));
//            }
            //绑定项目成员

            List<StudentProjectRoleStaffVo> roleStaffDtos = Lists.newArrayList();
            List<StudentProjectRoleStaffVo> projectRoleStaffDtos = studentProjectRoleStaffMap.get(studentInsuranceVo.getId());
            if (GeneralTool.isNotEmpty(projectRoleStaffDtos)){
                for (StudentProjectRoleStaffVo projectRoleStaffDto : projectRoleStaffDtos) {
                    String[] staffIdIdArray = projectRoleStaffDto.getStaffIdStr().split(",");
                    String[] staffNameArray = projectRoleStaffDto.getStaffName().split(",");
                    int n = staffIdIdArray.length;
                    if (staffIdIdArray.length!=staffNameArray.length){
                        throw new GetServiceException(LocaleMessageUtils.getMessage("data_exception"));
                    }
                    for (int i = 0; i < n; i++) {
                        StudentProjectRoleStaffVo studentProjectRoleStaffVo = BeanCopyUtils.objClone(projectRoleStaffDto, StudentProjectRoleStaffVo::new);
                        assert studentProjectRoleStaffVo != null;
                        studentProjectRoleStaffVo.setFkStaffId(Long.parseLong(staffIdIdArray[i]));
                        studentProjectRoleStaffVo.setStaffName(staffNameArray[i]);
                        roleStaffDtos.add(studentProjectRoleStaffVo);
                    }
                }
            }
            if (GeneralTool.isNotEmpty(roleStaffDtos)){
                studentInsuranceVo.setStudentProjectRoleStaffDtos(roleStaffDtos);
                StringBuilder sb = new StringBuilder();
                for (StudentProjectRoleStaffVo studentProjectRoleStaffVo : roleStaffDtos) {
                    if (sb.length() > 0) {
                        sb.append("<br>");
                    }
                    sb.append(studentProjectRoleStaffVo.getRoleName()).append("：").append(studentProjectRoleStaffVo.getStaffName());
                }
                studentInsuranceVo.setProjectRoleName(sb.toString());
            }
            //区分保险0学生保险/1陪读人保险
            if (GeneralTool.isNotEmpty(studentInsuranceVo.getType())){
                if (studentInsuranceVo.getType() == 0){
                    if (GeneralTool.isNotEmpty(studentInsuranceVo.getGender())) {
                        if (studentInsuranceVo.getGender().equals(1)) {
                            studentInsuranceVo.setGenderName("男");
                        } else if (studentInsuranceVo.getGender().equals(0)) {
                            studentInsuranceVo.setGenderName("女");
                        }
                    }
                    if (GeneralTool.isNotEmpty(studentInsuranceVo.getFirstName()) && GeneralTool.isNotEmpty(studentInsuranceVo.getLastName())) {
                        studentInsuranceVo.setEnglishName(studentInsuranceVo.getFirstName() + " " + studentInsuranceVo.getLastName());
                    }
                } else if (studentInsuranceVo.getType() == 1) {
                    if (GeneralTool.isNotEmpty(studentInsuranceVo.getInsurantGender())) {
                        if (studentInsuranceVo.getInsurantGender().equals(1)) {
                            studentInsuranceVo.setGenderName("男");
                        } else if (studentInsuranceVo.getInsurantGender().equals(0)) {
                            studentInsuranceVo.setGenderName("女");
                        }
                    }
                    studentInsuranceVo.setPassportNum(studentInsuranceVo.getInsurantPassportNum());
                    studentInsuranceVo.setBirthday(studentInsuranceVo.getInsurantBirthday());
                    studentInsuranceVo.setStudentName(studentInsuranceVo.getInsurantName());
                    if (GeneralTool.isNotEmpty(studentInsuranceVo.getInsurantFirstName()) && GeneralTool.isNotEmpty(studentInsuranceVo.getInsurantLastName())){
                        studentInsuranceVo.setEnglishName(studentInsuranceVo.getInsurantFirstName() + " " + studentInsuranceVo.getInsurantLastName());
                    }
                    StringBuffer stringBuffer = new StringBuffer();
                    if (GeneralTool.isNotEmpty(studentInsuranceVo.getInsurantFirstName()) && GeneralTool.isNotEmpty(studentInsuranceVo.getInsurantLastName())) {
                        stringBuffer.append(studentInsuranceVo.getInsurantName()).append('（').append(studentInsuranceVo.getInsurantFirstName()).append(studentInsuranceVo.getInsurantLastName()).append('）');
                    } else if (GeneralTool.isEmpty(studentInsuranceVo.getInsurantLastName()) && GeneralTool.isNotEmpty(studentInsuranceVo.getInsurantFirstName())) {
                        stringBuffer.append(studentInsuranceVo.getInsurantName()).append('（').append(studentInsuranceVo.getInsurantFirstName()).append('）');
                    } else {
                        stringBuffer.append(studentInsuranceVo.getInsurantName());
                    }
                    studentInsuranceVo.setStudentName(stringBuffer.toString());
                }
            }

            if (GeneralTool.isNotEmpty(studentInsuranceVo.getStatus())) {
                if (studentInsuranceVo.getStatus().equals(1)) {
                    studentInsuranceVo.setStatusName("有效");
                } else if (studentInsuranceVo.getStatus().equals(0)) {
                    studentInsuranceVo.setStatusName("作废");
                }
            }

            // 服务提供商名称
            if (GeneralTool.isNotEmpty(studentInsuranceVo.getFkBusinessProviderId()) &&
                    businessProviderNameMap.containsKey(studentInsuranceVo.getFkBusinessProviderId())) {
                studentInsuranceVo.setFkBusinessProviderName(businessProviderNameMap.get(studentInsuranceVo.getFkBusinessProviderId()));
            }
            // 服务提供商/产品
            StringBuilder sb = new StringBuilder();
            if (GeneralTool.isNotEmpty(studentInsuranceVo.getFkBusinessProviderName())) {
                sb.append(studentInsuranceVo.getFkBusinessProviderName()).append("/");
            }
            if (GeneralTool.isNotEmpty(studentInsuranceVo.getBusinessProviderProduct())) {
                sb.append(studentInsuranceVo.getBusinessProviderProduct());
            }
            studentInsuranceVo.setBusinessProviderAndProductName(sb.toString());
            // 支付方式名称
            if (GeneralTool.isNotEmpty(studentInsuranceVo.getPaymentMethod())) {
                studentInsuranceVo.setPaymentMethodName(ProjectExtraEnum.getValueByKey(studentInsuranceVo.getPaymentMethod(), ProjectExtraEnum.INSURANCE_PAYMENT_METHOD));
            }
//            if (GeneralTool.isNotEmpty(studentProjectRoleStaffVos)) {
//                List<StudentProjectRoleStaffVo> roleStaffDto = studentProjectRoleStaffMap.get(studentInsuranceVo.getId());
//                if (GeneralTool.isNotEmpty(roleStaffDto)) {
//                    StringBuilder sb = new StringBuilder();
//                    for (StudentProjectRoleStaffVo studentProjectRoleStaffDto : roleStaffDto) {
//                        sb.append(studentProjectRoleStaffDto.getRoleName()).append(":").append(studentProjectRoleStaffDto.getStaffName()).append(",");
//                    }
//                    studentInsuranceVo.setProjectRoleName(sb.toString());
//                }
//            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createReceivablePlan(InsuranceSummaryReDto insuranceSummaryReDto) {
        log.debug("收取佣金比例 commissionRateReceivable" +insuranceSummaryReDto.getCommissionRateReceivable());
        if (GeneralTool.isEmpty(insuranceSummaryReDto.getInsuranceIds())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (GeneralTool.isEmpty(insuranceSummaryReDto.getCommissionRateReceivable()) &&
                GeneralTool.isEmpty(insuranceSummaryReDto.getFixedAmountReceivable())
                && GeneralTool.isEmpty(insuranceSummaryReDto.getFkCompanyId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        List<ReceivablePlan> receivablePlanList = new ArrayList<>();
        List<StudentInsurance> upList = new ArrayList<>();
        for (Long id : insuranceSummaryReDto.getInsuranceIds()) {
            StudentInsurance studentInsurance = studentInsuranceMapper.selectById(id);
            if (GeneralTool.isEmpty(studentInsurance)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
            }
            studentInsurance.setCommissionRateReceivable(insuranceSummaryReDto.getCommissionRateReceivable());
            studentInsurance.setFixedAmountReceivable(insuranceSummaryReDto.getFixedAmountReceivable());
            studentInsurance.setFkCurrencyTypeNumCommission(insuranceSummaryReDto.getFkCurrencyTypeNumCommission());
            utilService.updateUserInfoToEntity(studentInsurance);
            upList.add(studentInsurance);
//            studentInsuranceMapper.updateById(studentInsurance);
            if (GeneralTool.isNotEmpty(insuranceSummaryReDto.getCommissionRateReceivable()) || GeneralTool.isNotEmpty(insuranceSummaryReDto.getFixedAmountReceivable())) {
                ReceivablePlan receivablePlan = new ReceivablePlan();
                receivablePlan.setFkTypeKey(TableEnum.SALE_STUDENT_INSURANCE.key);
                receivablePlan.setFkTypeTargetId(studentInsurance.getId());
                if (GeneralTool.isNotEmpty(insuranceSummaryReDto.getFkCurrencyTypeNumCommission())) {
                    receivablePlan.setFkCurrencyTypeNum(insuranceSummaryReDto.getFkCurrencyTypeNumCommission());
                } else {
                    receivablePlan.setFkCurrencyTypeNum(studentInsurance.getFkCurrencyTypeNumInsurance());
                }
                receivablePlan.setTuitionAmount(studentInsurance.getInsuranceAmount());
                receivablePlan.setCommissionRate(insuranceSummaryReDto.getCommissionRateReceivable());
                if (GeneralTool.isNotEmpty(insuranceSummaryReDto.getCommissionRateReceivable())) {
                    receivablePlan.setCommissionAmount(studentInsurance.getInsuranceAmount().divide(new BigDecimal("100")).multiply(insuranceSummaryReDto.getCommissionRateReceivable()));
                    receivablePlan.setReceivableAmount(studentInsurance.getInsuranceAmount().divide(new BigDecimal("100")).multiply(insuranceSummaryReDto.getCommissionRateReceivable()));
                } else {
                    receivablePlan.setCommissionAmount(insuranceSummaryReDto.getFixedAmountReceivable());
                    receivablePlan.setReceivableAmount(insuranceSummaryReDto.getFixedAmountReceivable());
                }
                receivablePlan.setNetRate(new BigDecimal(100));
                receivablePlan.setFixedAmount(insuranceSummaryReDto.getFixedAmountReceivable());
                receivablePlan.setStatus(1);
                receivablePlan.setFkCompanyId(insuranceSummaryReDto.getFkCompanyId());

                utilService.updateUserInfoToEntity(receivablePlan);
                receivablePlanList.add(receivablePlan);
            }
        }
        if (GeneralTool.isNotEmpty(receivablePlanList)) {
            receivablePlanService.saveBatch(receivablePlanList);
        }
        if (GeneralTool.isNotEmpty(upList)) {
            updateBatchById(upList);
        }
    }
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createPayablePlan(InsuranceSummaryPayDto insuranceSummaryPayDto) {
        if (GeneralTool.isEmpty(insuranceSummaryPayDto.getInsuranceIds())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (GeneralTool.isEmpty(insuranceSummaryPayDto.getCommissionRatePayable()) &&
                GeneralTool.isEmpty(insuranceSummaryPayDto.getFixedAmountPayable())
                && GeneralTool.isEmpty(insuranceSummaryPayDto.getFkCompanyId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        List<StudentInsurance> upList = new ArrayList<>();
        List<StudentInsurance> studentInsurances = studentInsuranceMapper.selectList(Wrappers.<StudentInsurance>lambdaQuery().in(StudentInsurance::getId, insuranceSummaryPayDto.getInsuranceIds()));
        Map<Long, StudentInsurance> insuranceMap = studentInsurances.stream().collect(Collectors.toMap(StudentInsurance::getId, Function.identity()));
        List<ReceivablePlan> receivablePlanList = receivablePlanMapper.selectList(Wrappers.<ReceivablePlan>lambdaQuery().eq(ReceivablePlan::getFkTypeKey, TableEnum.SALE_STUDENT_INSURANCE.key)
                .in(ReceivablePlan::getFkTypeTargetId, insuranceSummaryPayDto.getInsuranceIds()));
        Map<Long, List<ReceivablePlan>> reMap = receivablePlanList.stream().collect(Collectors.groupingBy(ReceivablePlan::getFkTypeTargetId));
        List<PayablePlan> planList = new ArrayList<>();
        for (Long id : insuranceSummaryPayDto.getInsuranceIds()) {
            StudentInsurance studentInsurance = insuranceMap.get(id);
            if (GeneralTool.isEmpty(studentInsurance)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
            }
            studentInsurance.setCommissionRatePayable(insuranceSummaryPayDto.getCommissionRatePayable());
            studentInsurance.setFixedAmountPayable(insuranceSummaryPayDto.getFixedAmountPayable());
            if (GeneralTool.isEmpty(insuranceSummaryPayDto.getFkCurrencyTypeNumCommission())) {
                studentInsurance.setFkCurrencyTypeNumCommission(insuranceSummaryPayDto.getFkCurrencyTypeNumCommission());
            } else {
                studentInsurance.setFkCurrencyTypeNumCommission(studentInsurance.getFkCurrencyTypeNumInsurance());
            }
            utilService.updateUserInfoToEntity(studentInsurance);
            upList.add(studentInsurance);
            if (GeneralTool.isNotEmpty(insuranceSummaryPayDto.getCommissionRatePayable()) || GeneralTool.isNotEmpty(insuranceSummaryPayDto.getFixedAmountPayable())) {
                if (GeneralTool.isNotEmpty(studentInsurance.getFkAgentId())) {
                    PayablePlan payablePlan = new PayablePlan();
                    payablePlan.setFkTypeKey(TableEnum.SALE_STUDENT_INSURANCE.key);
                    payablePlan.setFkTypeTargetId(studentInsurance.getId());
                    if (GeneralTool.isNotEmpty(insuranceSummaryPayDto.getFkCurrencyTypeNumCommission())) {
                        payablePlan.setFkCurrencyTypeNum(insuranceSummaryPayDto.getFkCurrencyTypeNumCommission());
                    } else {
                        payablePlan.setFkCurrencyTypeNum(studentInsurance.getFkCurrencyTypeNumInsurance());
                    }
                    payablePlan.setTuitionAmount(studentInsurance.getInsuranceAmount());
                    payablePlan.setCommissionRate(insuranceSummaryPayDto.getCommissionRatePayable());
                    if (GeneralTool.isNotEmpty(insuranceSummaryPayDto.getCommissionRatePayable())) {
                        payablePlan.setCommissionAmount(studentInsurance.getInsuranceAmount().divide(new BigDecimal("100")).multiply(insuranceSummaryPayDto.getCommissionRatePayable()));
                        payablePlan.setPayableAmount(studentInsurance.getInsuranceAmount().divide(new BigDecimal("100")).multiply(insuranceSummaryPayDto.getCommissionRatePayable()));
                    } else {
                        payablePlan.setCommissionAmount(insuranceSummaryPayDto.getFixedAmountPayable());
                        payablePlan.setPayableAmount(insuranceSummaryPayDto.getFixedAmountPayable());
                        payablePlan.setFixedAmount(insuranceSummaryPayDto.getFixedAmountPayable());
                    }

                    payablePlan.setFixedAmount(insuranceSummaryPayDto.getFixedAmountPayable());
                    payablePlan.setStatus(1);
                    payablePlan.setStatusSettlement(ProjectExtraEnum.UNSETTLED.key);
                    List<ReceivablePlan> receivablePlans = reMap.get(id);
                    payablePlan.setSplitRate(new BigDecimal(100));
                    payablePlan.setFkCompanyId(insuranceSummaryPayDto.getFkCompanyId());
                    utilService.setCreateInfo(payablePlan);
                    payablePlan.setIsPayInAdvance(false);
                    for (ReceivablePlan receivablePlan : receivablePlans) {
                        if (receivablePlan.getId() != null) {
                            payablePlan.setFkReceivablePlanId(receivablePlan.getId());
                            planList.add(payablePlan);
                        }
                    }
                }
            }
        }
        if (GeneralTool.isNotEmpty(planList)) {
            payablePlanService.saveBatch(planList);
        }
        if (GeneralTool.isNotEmpty(upList)) {
            updateBatchById(upList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createARAP(InsuranceSummaryDto insuranceSummaryDto) {
        if (GeneralTool.isEmpty(insuranceSummaryDto.getInsuranceIds())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (GeneralTool.isEmpty(insuranceSummaryDto.getCommissionRatePayable()) && GeneralTool.isEmpty(insuranceSummaryDto.getCommissionRateReceivable()) &&
                GeneralTool.isEmpty(insuranceSummaryDto.getFixedAmountPayable()) && GeneralTool.isEmpty(insuranceSummaryDto.getFixedAmountReceivable()) && GeneralTool.isEmpty(insuranceSummaryDto.getFkCompanyId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        for (Long id : insuranceSummaryDto.getInsuranceIds()) {
            StudentInsurance studentInsurance = studentInsuranceMapper.selectById(id);
            if (GeneralTool.isEmpty(studentInsurance)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
            }
            studentInsurance.setCommissionRatePayable(insuranceSummaryDto.getCommissionRatePayable());
            studentInsurance.setCommissionRateReceivable(insuranceSummaryDto.getCommissionRateReceivable());
            studentInsurance.setFixedAmountPayable(insuranceSummaryDto.getFixedAmountPayable());
            studentInsurance.setFixedAmountReceivable(insuranceSummaryDto.getFixedAmountReceivable());
            if (GeneralTool.isEmpty(insuranceSummaryDto.getFkCurrencyTypeNumCommission())) {
                studentInsurance.setFkCurrencyTypeNumCommission(studentInsurance.getFkCurrencyTypeNumCommission());
            } else {
                studentInsurance.setFkCurrencyTypeNumCommission(insuranceSummaryDto.getFkCurrencyTypeNumCommission());
            }
            utilService.updateUserInfoToEntity(studentInsurance);
            studentInsuranceMapper.updateById(studentInsurance);

            Long fkReceivablePlanId = null;
            if (GeneralTool.isNotEmpty(insuranceSummaryDto.getCommissionRateReceivable()) || GeneralTool.isNotEmpty(insuranceSummaryDto.getFixedAmountReceivable())) {
                ReceivablePlan receivablePlan = new ReceivablePlan();
                receivablePlan.setFkTypeKey(TableEnum.SALE_STUDENT_INSURANCE.key);
                receivablePlan.setFkTypeTargetId(studentInsurance.getId());
                if (GeneralTool.isNotEmpty(studentInsurance.getFkCurrencyTypeNumCommission())) {
                    receivablePlan.setFkCurrencyTypeNum(studentInsurance.getFkCurrencyTypeNumCommission());
                } else {
                    receivablePlan.setFkCurrencyTypeNum(studentInsurance.getFkCurrencyTypeNumInsurance());
                }
                receivablePlan.setTuitionAmount(studentInsurance.getInsuranceAmount());
                receivablePlan.setCommissionRate(studentInsurance.getCommissionRateReceivable());
                if (GeneralTool.isNotEmpty(insuranceSummaryDto.getCommissionRatePayable())) {
                    receivablePlan.setCommissionAmount(studentInsurance.getInsuranceAmount().divide(new BigDecimal("100")).multiply(studentInsurance.getCommissionRateReceivable()));
                    receivablePlan.setReceivableAmount(studentInsurance.getInsuranceAmount().divide(new BigDecimal("100")).multiply(studentInsurance.getCommissionRateReceivable()));
                } else {
                    receivablePlan.setCommissionAmount(studentInsurance.getFixedAmountReceivable());
                    receivablePlan.setReceivableAmount(studentInsurance.getFixedAmountReceivable());
                }
                receivablePlan.setNetRate(new BigDecimal(100));
                receivablePlan.setFixedAmount(studentInsurance.getFixedAmountReceivable());
                receivablePlan.setStatus(1);
                receivablePlan.setFkCompanyId(insuranceSummaryDto.getFkCompanyId());
                utilService.updateUserInfoToEntity(receivablePlan);
                receivablePlanMapper.insertSelective(receivablePlan);
                fkReceivablePlanId = receivablePlan.getId();
            }

            if (GeneralTool.isNotEmpty(insuranceSummaryDto.getCommissionRatePayable()) || GeneralTool.isNotEmpty(insuranceSummaryDto.getFixedAmountPayable())) {
                if (GeneralTool.isNotEmpty(studentInsurance.getFkAgentId())) {
                    PayablePlan payablePlan = new PayablePlan();
                    payablePlan.setFkTypeKey(TableEnum.SALE_STUDENT_INSURANCE.key);
                    payablePlan.setFkTypeTargetId(studentInsurance.getId());
                    if (GeneralTool.isNotEmpty(studentInsurance.getFkCurrencyTypeNumCommission())) {
                        payablePlan.setFkCurrencyTypeNum(studentInsurance.getFkCurrencyTypeNumCommission());
                    } else {
                        payablePlan.setFkCurrencyTypeNum(studentInsurance.getFkCurrencyTypeNumInsurance());
                    }
                    payablePlan.setTuitionAmount(studentInsurance.getInsuranceAmount());
                    payablePlan.setCommissionRate(studentInsurance.getCommissionRatePayable());
                    if (GeneralTool.isNotEmpty(insuranceSummaryDto.getCommissionRatePayable())) {
                        payablePlan.setCommissionAmount(studentInsurance.getInsuranceAmount().divide(new BigDecimal("100")).multiply(studentInsurance.getCommissionRatePayable()));
                        payablePlan.setPayableAmount(studentInsurance.getInsuranceAmount().divide(new BigDecimal("100")).multiply(studentInsurance.getCommissionRatePayable()));
                    } else {
                        payablePlan.setCommissionAmount(studentInsurance.getFixedAmountPayable());
                        payablePlan.setPayableAmount(studentInsurance.getFixedAmountPayable());
                        payablePlan.setFixedAmount(studentInsurance.getFixedAmountPayable());
                    }

                    payablePlan.setFixedAmount(studentInsurance.getFixedAmountPayable());
                    payablePlan.setStatus(1);
                    payablePlan.setStatusSettlement(ProjectExtraEnum.UNSETTLED.key);
                    if (fkReceivablePlanId != null) {
                        payablePlan.setFkReceivablePlanId(fkReceivablePlanId);
                    }
                    payablePlan.setSplitRate(new BigDecimal(100));
                    payablePlan.setFkCompanyId(insuranceSummaryDto.getFkCompanyId());
                    utilService.updateUserInfoToEntity(payablePlan);
                    payablePlan.setIsPayInAdvance(false);
                    payablePlanMapper.insert(payablePlan);
                }
            }

        }
    }

    /**
     * Author Cream
     * Description : //todo 获取留学保险id
     * Date 2022/5/10 18:25
     * Params:
     * Return
     */
    @Override
    public Long getInsuranceAgentId(Long targetId) {
        return studentInsuranceMapper.getAgentIdByTargetId(targetId);
    }

    /**
     * Author Cream
     * Description : 获取留学保险id
     * Date 2022/5/11 10:22
     * Params:
     * Return
     */
    @Override
    public Long getStudentInsuranceId(Long targetId) {
        return studentInsuranceMapper.getIdByTargetId(targetId);
    }

    @Override
    public Map<Long, String> getNumByIds(Set<Long> insuranceIds) {
        if (GeneralTool.isEmpty(insuranceIds)) {
            return new HashMap<>();
        }
        List<StudentInsurance> studentInsurances = studentInsuranceMapper.selectBatchIds(insuranceIds);
        if (GeneralTool.isEmpty(studentInsurances)) {
            return null;
        }
        Map<Long, String> map = new HashMap<>();
        for (StudentInsurance studentInsurance : studentInsurances) {
            map.put(studentInsurance.getId(), studentInsurance.getNum());
        }
        return map;
    }

    /**
     * 设置无代理（无需结算）接口
     * @param id
     */
    @Override
    public void updateNoAgent(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        //校验是否有创建应付计划
        LambdaQueryWrapper<PayablePlan> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(PayablePlan::getFkTypeKey,TableEnum.SALE_STUDENT_INSURANCE.key).eq(PayablePlan::getFkTypeTargetId,id);
        lambdaQueryWrapper.ne(PayablePlan::getStatus,0);
        List<PayablePlan> payablePlans = payablePlanMapper.selectList(lambdaQueryWrapper);
        if (GeneralTool.isNotEmpty(payablePlans)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("insurance_payable_data_association"));
        }

        //留学保险设置无代理（无需结算）
        LambdaQueryWrapper<Agent> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Agent::getName,"NR");
        wrapper.eq(Agent::getIsActive,1);
        Agent agent = agentMapper.selectOne(wrapper);

        if (GeneralTool.isEmpty(agent)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }

        LambdaQueryWrapper<AgentStaff> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AgentStaff::getFkAgentId,agent.getId());
        queryWrapper.eq(AgentStaff::getIsActive,1);
        List<AgentStaff> agentStaffs = agentStaffMapper.selectList(queryWrapper);

        LambdaQueryWrapper<StudentInsurance> lambdaQueryWrappers = new LambdaQueryWrapper<>();
        lambdaQueryWrappers.eq(StudentInsurance::getId,id);
        StudentInsurance studentInsurance = new StudentInsurance();
        studentInsurance.setFkAgentId(agent.getId());
        if (GeneralTool.isNotEmpty(agentStaffs)){
            studentInsurance.setFkStaffId(agentStaffs.get(0).getFkStaffId());
        }
        studentInsuranceMapper.update(studentInsurance,lambdaQueryWrappers);
    }

    @Override
    public void recoveryAgent(InsuranceAgentUpdateDto insuranceAgentUpdateDto) {
        if (GeneralTool.isEmpty(insuranceAgentUpdateDto.getId())&&GeneralTool.isEmpty(insuranceAgentUpdateDto.getFkAgentId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        LambdaQueryWrapper<AgentStaff> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AgentStaff::getFkAgentId, insuranceAgentUpdateDto.getFkAgentId());
        queryWrapper.eq(AgentStaff::getIsActive,1);
        List<AgentStaff> agentStaffs = agentStaffMapper.selectList(queryWrapper);

        StudentInsurance studentInsurance = new StudentInsurance();
        studentInsurance.setFkAgentId(insuranceAgentUpdateDto.getFkAgentId());
        if (GeneralTool.isNotEmpty(agentStaffs)){
            studentInsurance.setFkStaffId(agentStaffs.get(0).getFkStaffId());
        }
        LambdaQueryWrapper<StudentInsurance> lambdaQueryWrappers = new LambdaQueryWrapper<>();
        lambdaQueryWrappers.eq(StudentInsurance::getId, insuranceAgentUpdateDto.getId());

        studentInsuranceMapper.update(studentInsurance,lambdaQueryWrappers);

    }

    /**
     * Author Cream
     * Description : //合并留学保险数据
     * Date 2023/5/11 11:43
     * Params:
     * Return
     */
    @Override
    public void mergeData(Long mergedStudentId, Long targetStudentId) {
        List<StudentInsurance> insurances = studentInsuranceMapper.selectList(Wrappers.<StudentInsurance>lambdaQuery().eq(StudentInsurance::getFkStudentId, mergedStudentId));
        if (GeneralTool.isNotEmpty(insurances)) {
            insurances.forEach(s->s.setFkStudentId(targetStudentId));
            updateBatchById(insurances);
        }
    }

    @Override
    public StudentInsurance getStudentInsuranceById(Long id) {
        return studentInsuranceMapper.selectById(id);
    }

    @Override
    public void exportExcel(HttpServletResponse response, InsuranceSummaryQueryDto studentInsurance) {
        List<StudentInsuranceVo> studentInsurances = getStudentInsuranceSummary(studentInsurance, null, null);

        List<StudentInsuranceExportVo> insuranceExports = new ArrayList<>();
        for (StudentInsuranceVo insurance : studentInsurances) {
            StudentInsuranceExportVo export = BeanCopyUtils.objClone(insurance, StudentInsuranceExportVo::new);
            // 付款情况
            if (GeneralTool.isNotEmpty(insurance.getPayDetailList())) {
                JSONArray payDetailList = insurance.getPayDetailList();
                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < payDetailList.size(); i++) {
                    JSONObject json = payDetailList.getJSONObject(i);
                    String payableCurrencyTypeName = json.getString("payableCurrencyTypeName");
                    BigDecimal actualPayableAmount = json.getBigDecimal("actualPayableAmount");
                    Date gmtCreate = json.getDate("gmtCreate");
                    String gmtCreateStr = GetDateUtil.formatDate(gmtCreate, "yyyy-MM-dd HH:mm:ss");
                    if (sb.length() > 0) {
                        sb.append("\n");
                    }
                    sb.append("币种：").append(payableCurrencyTypeName).append("| ")
                            .append("实付金额：").append(actualPayableAmount).append("| ")
                            .append("付款时间：").append(gmtCreateStr);
                }
                export.setPayDetail(sb.toString());
            }
            // 绑定项目成员
            if (GeneralTool.isNotEmpty(export.getProjectRoleName())) {
                export.setProjectRoleName(export.getProjectRoleName().replace("<br>", "\n"));
            }
            insuranceExports.add(export);
            if (GeneralTool.isNotEmpty(insurance.getAgentLabelVos())) {
                export.setAgentLabelNames(
                        insurance.getAgentLabelVos().stream()
                                // 给每个标签名称包裹【】符号
                                .map(vo -> "【" + vo.getLabelName() + "】")
                                // 用逗号拼接所有带符号的标签
                                .collect(Collectors.joining(","))
                );
            }
        }

        // 排除的列
        Set<String> ignoreFields = Sets.newHashSet();

        // 留学保险详细页，操作限制，显示【服务提供商/产品】【支付方式】录入，value1=0/1为总开关，0不开启，1开启
        boolean flag;
        Map<Long, String> flagMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.STUDENT_INSURANCE_DETAIL_OPT_LIMIT.key, 1).getData();
        String configValue1 = flagMap.get(SecureUtil.getFkCompanyId());
        flag = configValue1.equals("1");
        if (!flag) {
            ignoreFields.add("businessProviderAndProductName");
            ignoreFields.add("paymentMethodName");
        }
        if (GeneralTool.isNotEmpty(ignoreFields)) {
            Map<String, String> fileMap = FileUtils.getFileMapIgnoreSomeField(StudentInsuranceExportVo.class, ignoreFields);
            FileUtils.exportExcelNotWrapText(response, insuranceExports, "StudentInsurance", fileMap);
        } else {
            FileUtils.exportExcelNotWrapText(response, insuranceExports, "StudentInsurance", StudentInsuranceExportVo.class);
        }
    }
}

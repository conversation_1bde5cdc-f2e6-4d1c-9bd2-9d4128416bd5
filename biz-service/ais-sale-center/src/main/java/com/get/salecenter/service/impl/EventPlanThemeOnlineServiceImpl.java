package com.get.salecenter.service.impl;

import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.EventPlanThemeOnlineMapper;
import com.get.salecenter.vo.EventPlanThemeOnlineVo;
import com.get.salecenter.entity.EventPlanRegistration;
import com.get.salecenter.entity.EventPlanRegistrationEvent;
import com.get.salecenter.entity.EventPlanTheme;
import com.get.salecenter.entity.EventPlanThemeOnline;
import com.get.salecenter.service.EventPlanRegistrationEventService;
import com.get.salecenter.service.EventPlanRegistrationService;
import com.get.salecenter.service.EventPlanThemeOnlineService;
import com.get.salecenter.service.EventPlanThemeService;
import com.get.salecenter.dto.EventPlanThemeOnlineDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12
 */
@Service
public class EventPlanThemeOnlineServiceImpl extends BaseServiceImpl<EventPlanThemeOnlineMapper, EventPlanThemeOnline> implements EventPlanThemeOnlineService {

    @Resource
    private EventPlanThemeService eventPlanThemeService;

    @Resource
    private EventPlanThemeOnlineMapper eventPlanThemeOnlineMapper;

    @Resource
    private EventPlanRegistrationService eventPlanRegistrationService;

    @Resource
    private EventPlanRegistrationEventService eventService;

    @Resource
    private UtilService utilService;

    @Override
    public List<EventPlanThemeOnlineVo> getEventPlanThemeOnlines(Long fkEventPlanId) {
        if(GeneralTool.isEmpty(fkEventPlanId)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }

        List<EventPlanThemeOnlineVo> eventPlanThemeOnlinesDtoList= eventPlanThemeOnlineMapper.getEventPlanThemeOnlines(fkEventPlanId, ProjectExtraEnum.EVENT_PLAN_THEME_ONLINE.key);
        if(GeneralTool.isEmpty(eventPlanThemeOnlinesDtoList)){
            return Collections.emptyList();
        }

        //报名名册
        List<EventPlanRegistration> registrations = eventPlanRegistrationService.list(Wrappers.<EventPlanRegistration>lambdaQuery()
                .eq(EventPlanRegistration::getFkEventPlanId, fkEventPlanId));
        List<EventPlanRegistrationEvent> registrationlist = new ArrayList<>();
        if(GeneralTool.isNotEmpty(registrations)){
            Set<Long> fkRegistrationIds = registrations.stream().map(EventPlanRegistration::getId).collect(Collectors.toSet());
            List<Long> onlineIds = eventPlanThemeOnlinesDtoList.stream().map(EventPlanThemeOnlineVo::getId).collect(Collectors.toList());
            registrationlist = eventService.list(Wrappers.<EventPlanRegistrationEvent>lambdaQuery()
                    .in(EventPlanRegistrationEvent::getFkEventPlanRegistrationId,fkRegistrationIds)
                    .eq(EventPlanRegistrationEvent::getFkTableName, TableEnum.EVENT_PLAN_THEME_ONLINE.key)
                    .in(EventPlanRegistrationEvent::getFkTableId,onlineIds));
        }
        //报名名册合计
        for(EventPlanThemeOnlineVo dto:eventPlanThemeOnlinesDtoList){
            List<EventPlanRegistrationEvent> regList = registrationlist.stream().filter(r -> dto.getId().equals(r.getFkTableId())).collect(Collectors.toList());
            dto.setRegistrationCount(regList.size());
        }

        return eventPlanThemeOnlinesDtoList;
    }

    @Override
    public List<EventPlanThemeOnlineVo> getOnlinesByThemeId(Long fkEventPlanThemeId) {
        if(GeneralTool.isEmpty(fkEventPlanThemeId)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }

        EventPlanTheme theme = eventPlanThemeService.getById(fkEventPlanThemeId);
        List<EventPlanThemeOnline> eventPlanThemeOnlines = eventPlanThemeOnlineMapper.selectList(Wrappers.<EventPlanThemeOnline>lambdaQuery()
                .eq(EventPlanThemeOnline::getFkEventPlanThemeId, fkEventPlanThemeId)
                .orderByDesc(EventPlanThemeOnline::getViewOrder));
        if(GeneralTool.isEmpty(eventPlanThemeOnlines)){
            return Collections.emptyList();
        }
        List<EventPlanThemeOnlineVo> eventPlanThemeOnlineVos = BeanCopyUtils.copyListProperties(eventPlanThemeOnlines, EventPlanThemeOnlineVo::new);
        eventPlanThemeOnlineVos.forEach(e->e.setMainTitle(theme.getMainTitle()));
        return eventPlanThemeOnlineVos;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void activate(EventPlanThemeOnlineDto onlineVo){
        if (GeneralTool.isEmpty(onlineVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        EventPlanThemeOnline online = BeanCopyUtils.objClone(onlineVo, EventPlanThemeOnline::new);
        utilService.updateUserInfoToEntity(online);
        eventPlanThemeOnlineMapper.updateById(online);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(ValidList<EventPlanThemeOnlineDto> onlineVos) {
        if (GeneralTool.isEmpty(onlineVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        for (EventPlanThemeOnlineDto onlineVo : onlineVos) {
            if (GeneralTool.isEmpty(onlineVo.getId())) {
                EventPlanThemeOnline online = BeanCopyUtils.objClone(onlineVo, EventPlanThemeOnline::new);
                Integer maxViewOrder = eventPlanThemeOnlineMapper.getMaxViewOrder(onlineVo.getFkEventPlanThemeId());
                maxViewOrder = GeneralTool.isEmpty(maxViewOrder) ? 0 : maxViewOrder;
                online.setViewOrder(maxViewOrder);
                utilService.updateUserInfoToEntity(online);
                eventPlanThemeOnlineMapper.insert(online);
            } else {
                EventPlanThemeOnline online = BeanCopyUtils.objClone(onlineVo, EventPlanThemeOnline::new);
                utilService.updateUserInfoToEntity(online);
                eventPlanThemeOnlineMapper.updateById(online);
            }
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        EventPlanThemeOnline online = eventPlanThemeOnlineMapper.selectById(id);
        if (GeneralTool.isEmpty(online)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        //存在注册不能删除
        List<EventPlanRegistrationEvent> registrations = eventService.list(Wrappers.<EventPlanRegistrationEvent>lambdaQuery()
                .eq(EventPlanRegistrationEvent::getFkTableName, TableEnum.EVENT_PLAN_THEME_ONLINE.key)
                .eq(EventPlanRegistrationEvent::getFkTableId, id));

        if(GeneralTool.isNotEmpty(registrations)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_online_fail"));
        }

        int delete = eventPlanThemeOnlineMapper.deleteById(id);
        if (delete <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void movingOrder(Long fkEventPlanThemeId,Integer start,Integer end) {
        LambdaQueryWrapper<EventPlanThemeOnline> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (end > start){
            lambdaQueryWrapper.between(EventPlanThemeOnline::getViewOrder,start,end).orderByDesc(EventPlanThemeOnline::getViewOrder);
        }else {
            lambdaQueryWrapper.between(EventPlanThemeOnline::getViewOrder,end,start).orderByDesc(EventPlanThemeOnline::getViewOrder);

        }
        lambdaQueryWrapper.eq(EventPlanThemeOnline::getFkEventPlanThemeId,fkEventPlanThemeId);
        List<EventPlanThemeOnline> eventPlanThemeOnlines = list(lambdaQueryWrapper);

        //从下上移 列表倒序排序
        List<EventPlanThemeOnline> updateList = Lists.newArrayList();
        if (end>start){
            int finalEnd = end;
            List<EventPlanThemeOnline> sortedList = Lists.newArrayList();
            EventPlanThemeOnline online = eventPlanThemeOnlines.get(eventPlanThemeOnlines.size() - 1);
            sortedList.add(online);
            eventPlanThemeOnlines.remove(eventPlanThemeOnlines.size() - 1);
            sortedList.addAll(eventPlanThemeOnlines);
            for (EventPlanThemeOnline themeOnline : sortedList) {
                themeOnline.setViewOrder(finalEnd);
                finalEnd--;
            }
            updateList.addAll(sortedList);
        }else {
            int finalStart = start;
            List<EventPlanThemeOnline> sortedList = Lists.newArrayList();
            EventPlanThemeOnline online = eventPlanThemeOnlines.get(0);
            eventPlanThemeOnlines.remove(0);
            sortedList.addAll(eventPlanThemeOnlines);
            sortedList.add(online);
            for (EventPlanThemeOnline themeOnline : sortedList) {
                themeOnline.setViewOrder(finalStart);
                finalStart--;
            }
            updateList.addAll(sortedList);
        }

        if (GeneralTool.isNotEmpty(updateList)){
            boolean batch = updateBatchById(updateList);
            if (!batch){
                throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
            }
        }
    }
}

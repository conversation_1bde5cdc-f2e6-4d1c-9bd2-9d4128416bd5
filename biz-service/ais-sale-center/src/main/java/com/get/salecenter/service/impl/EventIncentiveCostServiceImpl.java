package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.feign.IFinanceCenterClient;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.salecenter.dao.sale.EventBillReceivablePlanMapper;
import com.get.salecenter.dao.sale.EventIncentiveCostMapper;
import com.get.salecenter.vo.EventBillVo;
import com.get.salecenter.vo.EventIncentiveCostVo;
import com.get.salecenter.entity.EventBillReceivablePlan;
import com.get.salecenter.entity.EventCost;
import com.get.salecenter.entity.EventIncentive;
import com.get.salecenter.entity.EventIncentiveCost;
import com.get.salecenter.service.IEventBillService;
import com.get.salecenter.service.IEventCostService;
import com.get.salecenter.service.IEventIncentiveCostService;
import com.get.salecenter.service.IEventIncentiveService;
import com.get.salecenter.dto.EventIncentiveCostListDto;
import com.get.salecenter.dto.EventIncentiveCostUpdateDto;
import com.google.common.collect.Maps;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: Hardy
 * @create: 2022/7/19 14:46
 * @verison: 1.0
 * @description:
 */
@Service
public class EventIncentiveCostServiceImpl extends ServiceImpl<EventIncentiveCostMapper, EventIncentiveCost> implements IEventIncentiveCostService {

    @Resource
    private EventIncentiveCostMapper eventIncentiveCostMapper;
    @Resource
    private IEventIncentiveService eventIncentiveService;
    @Resource
    private UtilService utilService;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    private IFinanceCenterClient financeCenterClient;
    @Resource
    private IEventBillService eventBillService;
    @Lazy
    @Resource
    private IEventCostService eventCostService;
    @Resource
    private EventBillReceivablePlanMapper eventBillReceivablePlanMapper;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long addEventIncentiveCost(EventIncentiveCostUpdateDto eventIncentiveCostUpdateDto) {
        if (GeneralTool.isEmpty(eventIncentiveCostUpdateDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }

        // TODO: 2022/7/19 检验币种是否相同
        EventIncentive eventIncentive = eventIncentiveService.getById(eventIncentiveCostUpdateDto.getFkEventIncentiveId());
        if (GeneralTool.isEmpty(eventIncentive)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }

        if (GeneralTool.isNotEmpty(eventIncentive.getFkCurrencyTypeNum())){
            //检验币种是否相同
            validateCurrency(eventIncentiveCostUpdateDto.getFkCurrencyTypeNum(),eventIncentive.getFkCurrencyTypeNum());
        }

        EventIncentiveCost eventIncentiveCost = new EventIncentiveCost();
        BeanUtils.copyProperties(eventIncentiveCostUpdateDto,eventIncentiveCost);
        utilService.setCreateInfo(eventIncentiveCost);
        int i = eventIncentiveCostMapper.insert(eventIncentiveCost);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
        if (GeneralTool.isEmpty(eventIncentive.getFkCurrencyTypeNum())){
            eventIncentive.setFkCurrencyTypeNum(eventIncentiveCost.getFkCurrencyTypeNum());
            utilService.setUpdateInfo(eventIncentive);
            boolean save = eventIncentiveService.updateById(eventIncentive);
            if (!save) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
            }
        }
        return eventIncentiveCost.getId();
    }


    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (eventIncentiveCostMapper.selectById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        int i = eventIncentiveCostMapper.deleteById(id);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }


    @Override
    public EventIncentiveCostVo updateEventIncentiveCost(EventIncentiveCostUpdateDto eventIncentiveCostUpdateDto) {
        if (GeneralTool.isEmpty(eventIncentiveCostUpdateDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        EventIncentiveCost eventIncentiveCost = eventIncentiveCostMapper.selectById(eventIncentiveCostUpdateDto.getId());
        if (GeneralTool.isEmpty(eventIncentiveCost)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }


        EventIncentive eventIncentive = eventIncentiveService.getById(eventIncentiveCostUpdateDto.getFkEventIncentiveId());
        if (GeneralTool.isEmpty(eventIncentive)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }

        if (GeneralTool.isNotEmpty(eventIncentive.getFkCurrencyTypeNum())){
            //检验币种是否相同
            validateCurrency(eventIncentiveCostUpdateDto.getFkCurrencyTypeNum(),eventIncentive.getFkCurrencyTypeNum());
        }

        BeanUtils.copyProperties(eventIncentiveCostUpdateDto,eventIncentiveCost);
        utilService.setCreateInfo(eventIncentiveCost);
        int i = eventIncentiveCostMapper.updateById(eventIncentiveCost);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
        return findEventIncentiveCostById(eventIncentiveCostUpdateDto.getId());
    }


    @Override
    public EventIncentiveCostVo findEventIncentiveCostById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        EventIncentiveCost eventIncentiveCost = eventIncentiveCostMapper.selectById(id);
        if (GeneralTool.isEmpty(eventIncentiveCost)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        EventIncentiveCostVo eventIncentiveCostVo = new EventIncentiveCostVo();
        BeanUtils.copyProperties(eventIncentiveCost, eventIncentiveCostVo);
        return eventIncentiveCostVo;
    }


    @Override
    public List<EventIncentiveCostVo> getEventIncentiveCosts(EventIncentiveCostListDto eventIncentiveCostListDto, Page page) {
        LambdaQueryWrapper<EventIncentiveCost> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(eventIncentiveCostListDto)) {
            if (GeneralTool.isNotEmpty(eventIncentiveCostListDto.getFkEventIncentiveId())) {
                lambdaQueryWrapper.eq(EventIncentiveCost::getFkEventIncentiveId, eventIncentiveCostListDto.getFkEventIncentiveId());
            } else {
                throw new GetServiceException(LocaleMessageUtils.getMessage("event_id_null"));
            }
        }

        IPage<EventIncentiveCost> pages = eventIncentiveCostMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
        List<EventIncentiveCost> eventIncentiveCosts = pages.getRecords();
        page.setAll((int) pages.getTotal());

        List<EventIncentiveCostVo> convertDatas = new ArrayList<>();
        //学校提供商id集合
        Set<Long> institutionProviderIds = new HashSet<>();
        //币种编号集合
        Set<String> currencyTypeNums = new HashSet<>();
        //获取各自集合的值
        for (EventIncentiveCost eventIncentiveCost : eventIncentiveCosts) {
            institutionProviderIds.add(eventIncentiveCost.getFkInstitutionProviderId());
            currencyTypeNums.add(eventIncentiveCost.getFkCurrencyTypeNum());
            currencyTypeNums.add("CNY");
        }

        Map<Long, String> institutionProviderNameMap = getInstitutionProviderNameMap(institutionProviderIds);
        Map<String, String> currencyNameMap = getCurrencyNameMap(currencyTypeNums);

        //发票对应收款单的收款情况  eventcost-》eventBill-》invoice-》receiptForm-》item
        Set<Long> eventIncentiveCostIds = eventIncentiveCosts.stream().map(EventIncentiveCost::getId).collect(Collectors.toSet());
        List<EventIncentiveCostVo> eventIncentiveCostVos =  eventIncentiveCostMapper.getReceiptFormInfo(eventIncentiveCostIds);
        Map<Long, String> infoMap = eventIncentiveCostVos.stream().filter(e -> GeneralTool.isNotEmpty(e.getReceiptFormInfo())).collect(Collectors.toMap(EventIncentiveCostVo::getId, EventIncentiveCostVo::getReceiptFormInfo));


        for (EventIncentiveCost eventIncentiveCost : eventIncentiveCosts) {
            EventIncentiveCostVo eventIncentiveCostVo = BeanCopyUtils.objClone(eventIncentiveCost, EventIncentiveCostVo::new);
            //feign调用返回的map 根据key-id获取对应value-名称 设置返回给前端
            eventIncentiveCostVo.setInstitutionProviderName(institutionProviderNameMap.get(eventIncentiveCostVo.getFkInstitutionProviderId()));
            eventIncentiveCostVo.setCurrencyTypeName(currencyNameMap.get(eventIncentiveCostVo.getFkCurrencyTypeNum()));
            eventIncentiveCostVo.setAmountRmbCurrencyTypeName(currencyNameMap.get("CNY"));
            if (GeneralTool.isNotEmpty(infoMap)&&GeneralTool.isNotEmpty(infoMap.get(eventIncentiveCostVo.getId()))){
                eventIncentiveCostVo.setReceiptFormInfo(infoMap.get(eventIncentiveCostVo.getId()));
            }

            convertDatas.add(eventIncentiveCostVo);
        }
        return convertDatas;
    }


    @Override
    public List<EventBillVo> getEventBillSelect(Long institutionProviderId, Long companyId, Long eventIncentiveCostId) {

        List<EventBillVo> eventBillVos = eventBillService.getEventBillByProviderIdAndEventIncentiveCostId(institutionProviderId, companyId,eventIncentiveCostId);
        if (GeneralTool.isEmpty(eventBillVos)) {
            return Collections.emptyList();
        }

        Set<Long> eventBillIds = eventBillVos.stream().map(EventBillVo::getId).collect(Collectors.toSet());
        Map<Long, List<EventBillReceivablePlan>> eventBillReceivablePlansMap = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(eventBillIds)){
            List<EventBillReceivablePlan> eventBillReceivablePlans = eventBillReceivablePlanMapper.selectList(Wrappers.lambdaQuery(EventBillReceivablePlan.class)
                    .in(EventBillReceivablePlan::getFkEventBillId, eventBillIds));
            if (GeneralTool.isNotEmpty(eventBillReceivablePlans)){
                eventBillReceivablePlansMap = eventBillReceivablePlans.stream().collect(Collectors.groupingBy(EventBillReceivablePlan::getFkEventBillId));
            }
        }

        for (EventBillVo eventBillVo : eventBillVos) {
            List<EventBillReceivablePlan> eventBillReceivablePlans = eventBillReceivablePlansMap.get(eventBillVo.getId());
            if (GeneralTool.isEmpty(eventBillReceivablePlans)){
                //应收计划id为空就是没创建财务单据的
                eventBillVo.setSelectName("【未创建财务单据】"+ eventBillVo.getSelectName());
            }
        }


        Set<Long> ids = eventBillVos.stream().map(EventBillVo::getId).collect(Collectors.toSet());
        ids.removeIf(Objects::isNull);
        if (GeneralTool.isEmpty(ids)) {
            ids.add(0L);
        }

        // TODO: 2022/7/22 也要计算eventcost的
        LambdaQueryWrapper<EventIncentiveCost> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(EventIncentiveCost::getFkEventBillId, ids);
        if (GeneralTool.isNotEmpty(eventIncentiveCostId)) {
            lambdaQueryWrapper.ne(EventIncentiveCost::getId, eventIncentiveCostId);
        }
        List<EventIncentiveCost> eventIncentiveCosts = eventIncentiveCostMapper.selectList(lambdaQueryWrapper);
        Map<Long, List<EventIncentiveCost>> eventIncentiveCostsMap = eventIncentiveCosts.stream().collect(Collectors.groupingBy(EventIncentiveCost::getFkEventBillId));

        List<EventCost> eventCosts = eventCostService.list(Wrappers.<EventCost>lambdaQuery().in(EventCost::getFkEventBillId, ids));
        Map<Long, List<EventCost>> eventCostsMap = eventCosts.stream().collect(Collectors.groupingBy(EventCost::getFkEventBillId));

        List<EventBillVo> resultList = new ArrayList<>();
        for (EventBillVo eventBillVo : eventBillVos) {
            List<EventIncentiveCost> eventIncentiveCostList = eventIncentiveCostsMap.get(eventBillVo.getId());
            List<EventCost> eventCostList = eventCostsMap.get(eventBillVo.getId());

            BigDecimal subtractResult = eventBillVo.getEventAmount();

            if (GeneralTool.isNotEmpty(eventIncentiveCostList)) {
                for (EventIncentiveCost eventIncentiveCost : eventIncentiveCostList) {
                    if (GeneralTool.isEmpty(eventIncentiveCost.getAmountReceivable())) {
                        eventIncentiveCost.setAmountReceivable(BigDecimal.ZERO);
                    }
                    subtractResult = subtractResult.subtract(eventIncentiveCost.getAmountReceivable());
                }
            }

            if (GeneralTool.isNotEmpty(eventCostList)) {
                for (EventCost eventCost : eventCostList) {
                    if (GeneralTool.isEmpty(eventCost.getAmountReceivable())) {
                        eventCost.setAmountReceivable(BigDecimal.ZERO);
                    }
                    subtractResult = subtractResult.subtract(eventCost.getAmountReceivable());
                }
            }

            eventBillVo.setEventBillBalance(subtractResult);
            //余额大于等于0
            if (subtractResult.compareTo(BigDecimal.ZERO) > 0) {
                resultList.add(eventBillVo);
            }
        }

        //编辑的全部显示
        if (GeneralTool.isNotEmpty(eventIncentiveCostId)) {
            return eventBillVos;
        }
        return resultList;
    }

    @Override
    public BigDecimal getEventIncentiveCostSubtotal(EventIncentiveCostUpdateDto eventIncentiveCostVo) {
        if (GeneralTool.isEmpty(eventIncentiveCostVo.getFkEventIncentiveId())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("event_id_null"));
        }
        List<EventIncentiveCost> eventIncentiveCosts = eventIncentiveCostMapper.selectList(Wrappers.<EventIncentiveCost>lambdaQuery().eq(EventIncentiveCost::getFkEventIncentiveId, eventIncentiveCostVo.getFkEventIncentiveId()));
        if (GeneralTool.isEmpty(eventIncentiveCosts)){
            return BigDecimal.ZERO;
        }
        BigDecimal subtotal = BigDecimal.ZERO;
        for (EventIncentiveCost eventIncentiveCost : eventIncentiveCosts) {
            subtotal = subtotal.add(GeneralTool.isNotEmpty(eventIncentiveCost.getAmountRmb())?eventIncentiveCost.getAmountRmb():BigDecimal.ZERO);
        }
        return subtotal;
    }


    private void validateCurrency(String eventIncentiveCostCurrency, String eventIncentiveCurrency) {
        if (!eventIncentiveCostCurrency.equals(eventIncentiveCurrency)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("event_currency_not_same"));
        }
    }

    /**
     * @return java.util.Map<java.lang.Long, java.lang.String>
     * @Description :feign调用一次查出全部学校提供商id对应的名称
     * @Param [institutionProviderIds]
     * <AUTHOR>
     */
    private Map<Long, String> getInstitutionProviderNameMap(Set<Long> institutionProviderIds) {
        institutionProviderIds.removeIf(Objects::isNull);
        Result<Map<Long, String>> institutionProviderNameResult = institutionCenterClient.getInstitutionProviderNamesByIds(institutionProviderIds);
        if (institutionProviderNameResult.isSuccess() && GeneralTool.isNotEmpty(institutionProviderNameResult.getData())) {
            return institutionProviderNameResult.getData();
        }
        return Maps.newHashMap();
    }

    /**
     * @return java.util.Map<java.lang.String, java.lang.String>
     * @Description :feign调用一次查出全部对应币种名称
     * @Param [events]
     * <AUTHOR>
     */
    private Map<String, String> getCurrencyNameMap(Set<String> currencyTypeNums) {
        currencyTypeNums.removeIf(Objects::isNull);
        //feign调用一次查出全部对应币种名称
        Result<Map<String, String>> result = financeCenterClient.getCurrencyNamesByNums(currencyTypeNums);
        if (result.isSuccess() && result.getData() != null) {
            return result.getData();
        }
        return new HashMap<>();
    }


}

package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.service.GetService;
import com.get.core.mybatis.utils.ValidList;
import com.get.salecenter.vo.StaffCommissionInstitutionVo;
import com.get.salecenter.entity.StaffCommissionInstitution;
import com.get.salecenter.dto.StaffCommissionInstitutionDto;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: Hardy
 * @create: 2023/2/7 16:41
 * @verison: 1.0
 * @description:
 */
public interface IStaffCommissionInstitutionService extends GetService<StaffCommissionInstitution> {
    /**
     * 新增
     * @param staffCommissionInstitutionDto
     * @return
     */
    Long addStaffCommissionInstitution(StaffCommissionInstitutionDto staffCommissionInstitutionDto);

    /**
     * 批量新增
     * @param staffCommissionInstitutionDtos
     */
    void batchAdd(ValidList<StaffCommissionInstitutionDto> staffCommissionInstitutionDtos);

    /**
     * 删除
     * @param id
     */
    void delete(Long id);

    /**
     * feign获取学校是否激活提成
     * @param institutionIds
     * @return
     */
    Map<Long, Boolean> getCommissionActiveStatusByInstitutionIds(Set<Long> institutionIds);

    /**
     * 设置名称
     * @param staffCommissionInstitutionDto
     * @param page
     * @return
     */
    List<StaffCommissionInstitutionVo> getStaffCommissionInstitutionDtos(StaffCommissionInstitutionDto staffCommissionInstitutionDto, Page page);
}

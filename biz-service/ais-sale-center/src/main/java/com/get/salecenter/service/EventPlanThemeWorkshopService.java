package com.get.salecenter.service;

import com.get.core.mybatis.base.BaseService;
import com.get.core.mybatis.utils.ValidList;
import com.get.salecenter.vo.EventPlanThemeWorkshopVo;
import com.get.salecenter.entity.EventPlanThemeWorkshop;
import com.get.salecenter.dto.EventPlanThemeWorkshopDto;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12
 */
public interface EventPlanThemeWorkshopService extends BaseService<EventPlanThemeWorkshop> {
    /**
     * 列表数据
     * <AUTHOR>
     * @DateTime 2023/12/14 10:43
     */
    List<EventPlanThemeWorkshopVo> getEventPlanThemeWorkshops(Long fkEventPlanId);

    /**
     * 根据主题获取线下专访活动列表数据
     * <AUTHOR>
     * @DateTime 2023/12/14 17:56
     */
    List<EventPlanThemeWorkshopVo> getWorkshopsByThemeId(Long fkEventPlanThemeId);

    /**
     * 激活
     * <AUTHOR>
     * @DateTime 2023/12/25 15:56
     */
    void activate(EventPlanThemeWorkshopDto workshopVo);

    /**
     * 批量新增
     * <AUTHOR>
     * @DateTime 2023/12/14 16:33
     */
    void batchAdd(ValidList<EventPlanThemeWorkshopDto> workshopVos);


    /**
     * 删除
     * <AUTHOR>
     * @DateTime 2023/12/14 10:58
     */
    void delete(Long id);



    /**
     * 移动
     * <AUTHOR>
     * @DateTime 2023/12/14 11:02
     */
    void movingOrder( Long fkEventPlanThemeId,Integer start,Integer end);

}

package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.salecenter.vo.NameLabelVo;
import com.get.salecenter.entity.NameLabel;
import com.get.salecenter.dto.NameLabelSearchDto;
import com.get.salecenter.dto.NameLabelDto;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE: 2024/3/25
 * @TIME: 10:59
 * @Description:
 **/
public interface NameLabelService {
    /**
     * 列表数据
     * <AUTHOR>
     * @DateTime 2024/3/25 12:45
     */
    List<NameLabelVo> datas(NameLabelSearchDto nameLabelSearchDto, Page page);

    /**
     * 详情
     * <AUTHOR>
     * @DateTime 2024/3/25 12:45
     */
    NameLabelVo findNameLabelById(Long id);

    /**
     * 新增
     * <AUTHOR>
     * @DateTime 2024/3/25 12:46
     */
    Long addNameLabel(NameLabelDto nameLabelSearchVo);

    /**
     * 删除
     * <AUTHOR>
     * @DateTime 2023/12/13 17:29
     */
    void delete(Long id);


    /**
     * 修改
     * <AUTHOR>
     * @DateTime 2023/12/13 17:29
     */
    NameLabelVo updateNameLabel(NameLabelDto vo);

    /**
     * 拖拽
     * <AUTHOR>
     * @DateTime 2024/3/25 16:55
     */
    void movingOrder(Integer start,Integer end);


    /**
     * 根据表名获取标签列表
     * <AUTHOR>
     * @DateTime 2024/3/25 15:25
     */
    Map<Long, NameLabel> getNameLabelListByFkTableName(String fkTableName);

}

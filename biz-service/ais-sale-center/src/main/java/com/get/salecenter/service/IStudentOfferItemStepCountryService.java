package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseService;
import com.get.salecenter.vo.StudentOfferItemStepCountryVo;
import com.get.salecenter.entity.StudentOfferItemStepCountry;
import com.get.salecenter.dto.StudentOfferItemStepCountryDto;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-22
 */
public interface IStudentOfferItemStepCountryService extends BaseService<StudentOfferItemStepCountry> {

    Long add(StudentOfferItemStepCountryDto studentOfferItemStepCountryDto);

    void delete(Long id);

    List<StudentOfferItemStepCountryVo> getStudentOfferItemStepCountryDtos(StudentOfferItemStepCountryDto studentOfferItemStepCountryDto, Page page);
}

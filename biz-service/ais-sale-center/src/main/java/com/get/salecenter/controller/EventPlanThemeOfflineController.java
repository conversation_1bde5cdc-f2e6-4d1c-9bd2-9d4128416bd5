package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.UpdateResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.vo.EventPlanThemeOfflineVo;
import com.get.salecenter.service.EventPlanThemeOfflineService;
import com.get.salecenter.dto.EventPlanThemeOfflineDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 *  前端接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12
 */

@Api(tags = "活动年度计划线下活动类型项目管理")
@RestController
@RequestMapping("sale/eventPlanThemeOffline")
@VerifyPermission(IsVerify = false)
public class EventPlanThemeOfflineController {
    @Resource
    private EventPlanThemeOfflineService eventPlanThemeOfflineService;


    @ApiOperation(value = "列表数据")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/活动年度计划线下活动类型项目管理/列表数据")
    @PostMapping("datas")
    public ResponseBo<EventPlanThemeOfflineVo> datas(@RequestParam("fkEventPlanId") Long fkEventPlanId){
        return new ListResponseBo(eventPlanThemeOfflineService.getEventPlanThemeOfflines(fkEventPlanId));
    }

//    @ApiOperation(value = "根据主题获取线下活动列表数据")
//    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/活动年度计划线下活动类型项目管理/根据主题获取线下活动列表数据")
//    @PostMapping("getOfflinesByThemeId")
//    public ResponseBo<EventPlanThemeOfflineVo> getOfflinesByThemeId(@RequestParam("fkEventPlanThemeId") Long fkEventPlanThemeId){
//        return new ListResponseBo(eventPlanThemeOfflineService.getOfflinesByThemeId(fkEventPlanThemeId));
//    }

//    @ApiOperation(value = "批量新增/批量修改", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/活动年度计划线下活动类型项目管理/批量新增或修改")
//    @PostMapping("batchAdd")
//    public ResponseBo batchAdd(@RequestBody @Validated(EventPlanThemeOfflineDto.Add.class) ValidList<EventPlanThemeOfflineDto> offlineVos) {
//        eventPlanThemeOfflineService.batchAdd(offlineVos);
//        return SaveResponseBo.ok();
//    }

    @ApiOperation(value = "详情", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/活动年度计划线下活动类型项目管理/详情")
    @GetMapping("/{id}")
    public ResponseBo<EventPlanThemeOfflineVo> detail(@PathVariable("id") Long id) {
        EventPlanThemeOfflineVo data = eventPlanThemeOfflineService.findOfflineById(id);
        return new ResponseBo<>(data);
    }

    @ApiOperation(value = "激活", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/活动年度计划线下活动类型项目管理/激活")
    @PostMapping("activate")
    public ResponseBo activate(@RequestBody  @Validated(EventPlanThemeOfflineDto.Update.class) EventPlanThemeOfflineDto offlineVo) {
        eventPlanThemeOfflineService.activate(offlineVo);
        return SaveResponseBo.ok();
    }

    @ApiOperation(value = "新增", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/活动年度计划线下活动类型项目管理/新增")
    @PostMapping("add")
    public ResponseBo add(@RequestBody  @Validated(EventPlanThemeOfflineDto.Add.class) EventPlanThemeOfflineDto offlineVo) {

        return SaveResponseBo.ok(eventPlanThemeOfflineService.addEventPlanThemeOffline(offlineVo));
    }

    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/活动年度计划线下活动类型项目管理/修改")
    @PostMapping("update")
    public ResponseBo<EventPlanThemeOfflineVo> update(@RequestBody @Validated(EventPlanThemeOfflineDto.Update.class) EventPlanThemeOfflineDto offlineVo) {
        return UpdateResponseBo.ok(eventPlanThemeOfflineService.updateOffline(offlineVo));
    }


    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/活动年度计划线下活动类型项目管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        eventPlanThemeOfflineService.delete(id);
        return DeleteResponseBo.ok();
    }



    @ApiOperation(value = "拖拽", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/活动年度计划线下活动类型项目管理/拖拽")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestParam("fkEventPlanThemeId") Long fkEventPlanThemeId,@RequestParam("start")Integer start,@RequestParam("end")Integer end) {
        eventPlanThemeOfflineService.movingOrder(fkEventPlanThemeId,start,end);
        return ResponseBo.ok();
    }

}

package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.salecenter.vo.EventTypeVo;
import com.get.salecenter.dto.EventTypeDto;

import java.util.List;

/**
 * @author: Sea
 * @create: 2020/12/7 11:27
 * @verison: 1.0
 * @description:
 */
public interface IEventTypeService {
    /**
     * @return com.get.salecenter.vo.EventTypeVo
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    EventTypeVo findEventTypeById(Long id);

    /**
     * @return void
     * @Description :批量新增
     * @Param [eventTypeDtos]
     * <AUTHOR>
     */
    void batchAdd(List<EventTypeDto> eventTypeDtos);

    /**
     * @return void
     * @Description :删除
     * @Param [id]
     * <AUTHOR>
     */
    void delete(Long id);

    /**
     * @return com.get.salecenter.vo.EventTypeVo
     * @Description :修改
     * @Param [eventTypeDto]
     * <AUTHOR>
     */
    EventTypeVo updateEventType(EventTypeDto eventTypeDto);

    /**
     * @return java.util.List<com.get.salecenter.vo.EventTypeVo>
     * @Description :列表
     * @Param [eventTypeDto, page]
     * <AUTHOR>
     */
    List<EventTypeVo> getEventTypes(EventTypeDto eventTypeDto, Page page);

    /**
     * @return void
     * @Description :上移下移
     * @Param [eventTypeDtos]
     * <AUTHOR>
     */
    void movingOrder(List<EventTypeDto> eventTypeDtos);

    /**
     * @return java.util.List<com.get.salecenter.vo.EventTypeVo>
     * @Description :活动类型下拉框数据
     * @Param []
     * <AUTHOR>
     */
    List<EventTypeVo> getEventTypeList();

    /**
     * @return java.lang.String
     * @Description :通过id查找对应活动类型
     * @Param [fkEventTypeId]
     * <AUTHOR>
     */
    String getEventTypeNameById(Long eventTypeId);
}

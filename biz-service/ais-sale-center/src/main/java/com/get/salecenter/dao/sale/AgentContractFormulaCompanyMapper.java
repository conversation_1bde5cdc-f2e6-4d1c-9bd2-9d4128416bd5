package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.entity.AgentContractFormulaCompany;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: Sea
 * @create: 2021/1/6 12:21
 * @verison: 1.0
 * @description:
 */
@Mapper
public interface AgentContractFormulaCompanyMapper extends BaseMapper<AgentContractFormulaCompany> {

    /**
     * @return int
     * @Description :新增
     * @Param [record]
     * <AUTHOR>
     */
    int insertSelective(AgentContractFormulaCompany record);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :查找agentContractFormulaId对应公司ids
     * @Param [agentContractFormulaId]
     * <AUTHOR>
     */
    List<Long> getCompanyIdsByFkid(Long agentContractFormulaId);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :通过公司ids 查找对应代理合同公式ids
     * @Param [companyIds]
     * <AUTHOR>
     */
    List<Long> getFormulaIdByCompanyIds(@Param("companyIds") List<Long> companyIds);
}
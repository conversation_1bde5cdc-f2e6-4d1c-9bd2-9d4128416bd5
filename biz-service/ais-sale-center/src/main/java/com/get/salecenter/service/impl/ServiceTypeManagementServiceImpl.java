package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.dao.sale.ServiceTypeManagementMapper;
import com.get.salecenter.dao.sale.StudentServiceFeeMapper;
import com.get.salecenter.dao.sale.StudentServiceFeeTypeCompanyMapper;
import com.get.permissioncenter.vo.tree.CompanyTreeVo;
import com.get.salecenter.vo.SelItem;
import com.get.salecenter.vo.StudentServiceFeeTypeVo;
import com.get.salecenter.entity.StudentServiceFee;
import com.get.salecenter.entity.StudentServiceFeeType;
import com.get.salecenter.entity.StudentServiceFeeTypeCompany;
import com.get.salecenter.service.ServiceTypeManagementService;
import com.get.salecenter.service.StudentServiceFeeTypeCompanyService;
import com.get.salecenter.utils.ConvertUtils;
import com.get.salecenter.dto.StudentServiceFeeTypeDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class ServiceTypeManagementServiceImpl extends ServiceImpl<ServiceTypeManagementMapper,StudentServiceFeeType> implements ServiceTypeManagementService {

    @Resource
    private ServiceTypeManagementMapper serviceTypeManagementMapper;

    @Resource
    private StudentServiceFeeTypeCompanyMapper studentServiceFeeTypeCompanyMapper;

    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Resource
    private StudentServiceFeeMapper studentServiceFeeMapper;

    @Resource
    private StudentServiceFeeTypeCompanyService studentServiceFeeTypeCompanyService;

    @Resource
    private UtilService<Object> utilService;
    /**
     * Author Cream
     * Description : //获取服务类型管理列表数据
     * Date 2023/1/17 11:23
     * Params:
     * Return
     */
    @Override
    public ListResponseBo<StudentServiceFeeTypeVo> datas(StudentServiceFeeTypeDto studentServiceFeeTypeDto, Page<StudentServiceFeeTypeDto> page) {
        if (Objects.isNull(studentServiceFeeTypeDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        IPage<StudentServiceFeeTypeVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<StudentServiceFeeTypeVo> data = serviceTypeManagementMapper.datas(iPage, studentServiceFeeTypeDto);
        page.setAll((int) iPage.getTotal());
        if (data.isEmpty()) {
            return new ListResponseBo<>(data,page);
        }
        List<Long> companyIds = SecureUtil.getCompanyIds();
        Set<Long> ids = data.stream().map(StudentServiceFeeTypeVo::getId).collect(Collectors.toSet());
        List<SelItem> selItems = studentServiceFeeTypeCompanyMapper.getServiceFeeTypeCompanyInfo(ids,companyIds);
        Map<Long, Object> convert = ConvertUtils.convert(selItems);
        for (StudentServiceFeeTypeVo datum : data) {
            datum.setFkCompanyName(String.valueOf(convert.get(datum.getId())));
        }
        return new ListResponseBo<>(data,BeanCopyUtils.objClone(page,Page::new));
    }

    /**
     * Author Cream
     * Description : //新增服务类型
     * Date 2023/1/17 11:26
     * Params:
     * Return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public SaveResponseBo save(StudentServiceFeeTypeDto studentServiceFeeTypeDto) {
        if (Objects.isNull(studentServiceFeeTypeDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        StudentServiceFeeType studentServiceFeeType = BeanCopyUtils.objClone(studentServiceFeeTypeDto, StudentServiceFeeType::new);
        utilService.setCreateInfo(studentServiceFeeType);
        Integer orderView = serviceTypeManagementMapper.getMaxOrder() + 1;
        studentServiceFeeType.setViewOrder(orderView);
        serviceTypeManagementMapper.insert(studentServiceFeeType);
        StudentServiceFeeTypeCompany feeTypeCompany = new StudentServiceFeeTypeCompany(studentServiceFeeType.getId(), studentServiceFeeTypeDto.getFkCompanyId());
        studentServiceFeeTypeCompanyService.add(feeTypeCompany);
        return SaveResponseBo.ok(studentServiceFeeType.getId());
    }

    /**
     * Author Cream
     * Description : //获取详情
     * Date 2023/1/17 11:47
     * Params:
     * Return
     */
    @Override
    public ResponseBo<StudentServiceFeeTypeVo> findInfoById(Long id) {
        if (Objects.isNull(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        StudentServiceFeeType serviceFeeType = serviceTypeManagementMapper.selectById(id);
        StudentServiceFeeTypeVo serviceFeeTypeDto = BeanCopyUtils.objClone(serviceFeeType, StudentServiceFeeTypeVo::new);
        serviceFeeTypeDto.setFkCompanyName(studentServiceFeeTypeCompanyMapper.getServiceFeeTypeCompanyInfoById(id));
        return new ResponseBo<>(serviceFeeTypeDto);
    }

    /**
     * Author Cream
     * Description : // 更新服务费类型
     * Date 2023/1/17 11:37
     * Params:
     * Return
     * @return
     */
    @Override
    public ResponseBo update(StudentServiceFeeTypeDto studentServiceFeeTypeDto) {
        if (Objects.isNull(studentServiceFeeTypeDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        StudentServiceFeeType studentServiceFeeType = BeanCopyUtils.objClone(studentServiceFeeTypeDto, StudentServiceFeeType::new);
        utilService.setUpdateInfo(studentServiceFeeType);
        serviceTypeManagementMapper.updateById(studentServiceFeeType);
//        studentServiceFeeTypeCompanyMapper.delete(Wrappers.<StudentServiceFeeTypeCompany>lambdaQuery()
//                .eq(StudentServiceFeeTypeCompany::getFkStudentServiceFeeTypeId,studentServiceFeeType.getId()));
//        StudentServiceFeeTypeCompany studentServiceFeeTypeCompany = new StudentServiceFeeTypeCompany(studentwaServiceFeeType.getId(),studentServiceFeeTypeDto.getFkCompanyId());
//        studentServiceFeeTypeCompanyService.add(studentServiceFeeTypeCompany);
        return new ResponseBo<>();
    }

    /**
     * 删除服务费类型
     * @param id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseBo delete(Long id) {
        if (Objects.isNull(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        int count = studentServiceFeeMapper.selectCount(Wrappers.<StudentServiceFee>lambdaQuery().eq(StudentServiceFee::getFkStudentServiceFeeTypeId, id));
        if (count>0){
//            throw new GetServiceException(LocaleMessageUtils.getMessage("该类型正在使用删除失败"));
            throw new GetServiceException(LocaleMessageUtils.getMessage("service_type_use"));

        }
        if (GeneralTool.isNotEmpty(serviceTypeManagementMapper.selectById(id))) {
            serviceTypeManagementMapper.deleteById(id);
            studentServiceFeeTypeCompanyMapper.delete(Wrappers.<StudentServiceFeeTypeCompany>lambdaQuery().eq(StudentServiceFeeTypeCompany::getFkStudentServiceFeeTypeId,id));
        }
        return new ResponseBo<>();
    }

    /**
     * Author Cream
     * Description : // 上移下移
     * Date 2023/1/17 12:02
     * Params:
     * Return
     * @return
     */
    @Override
    public ResponseBo movingOrder(List<StudentServiceFeeTypeDto> feeTypeVos) {
        if (Objects.isNull(feeTypeVos) || feeTypeVos.size()!=2) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        StudentServiceFeeTypeDto first = feeTypeVos.get(0);
        StudentServiceFeeTypeDto last = feeTypeVos.get(1);
        StudentServiceFeeType fis = serviceTypeManagementMapper.selectById(first.getId());
        fis.setViewOrder(last.getViewOrder());
        StudentServiceFeeType las = serviceTypeManagementMapper.selectById(last.getId());
        las.setViewOrder(first.getViewOrder());
        List<StudentServiceFeeType> upList = new ArrayList<>(2);
        utilService.setUpdateInfo(fis);
        utilService.setUpdateInfo(las);
        upList.add(fis);
        upList.add(las);
        updateBatchById(upList);
        return new ResponseBo<>();
    }

    /**
     * Author Cream
     * Description : //安全分配公司
     * Date 2023/1/17 12:06
     * Params:
     * Return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseBo securityDistribution(StudentServiceFeeTypeDto studentServiceFeeTypeDto) {
        Long id = studentServiceFeeTypeDto.getId();
        if (Objects.isNull(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        List<Long> fkCompanyIds = studentServiceFeeTypeDto.getFkCompanyIds();
        if (GeneralTool.isNotEmpty(fkCompanyIds)) {
            StudentServiceFeeType studentServiceFeeType = serviceTypeManagementMapper.selectById(id);
            if (GeneralTool.isNotEmpty(studentServiceFeeType)) {
                List<Long> companyIds = SecureUtil.getCompanyIds();
                studentServiceFeeTypeCompanyMapper.delete(Wrappers.<StudentServiceFeeTypeCompany>lambdaQuery().eq(StudentServiceFeeTypeCompany::getFkStudentServiceFeeTypeId,id).in(StudentServiceFeeTypeCompany::getFkCompanyId,companyIds));
                List<StudentServiceFeeTypeCompany> studentServiceFeeTypeCompanies = new ArrayList<>(fkCompanyIds.size());
                for (Long fkCompanyId : fkCompanyIds) {
                    StudentServiceFeeTypeCompany feeTypeCompany = new StudentServiceFeeTypeCompany(id,fkCompanyId);
                    utilService.setCreateInfo(feeTypeCompany);
                    studentServiceFeeTypeCompanies.add(feeTypeCompany);
                }
                studentServiceFeeTypeCompanyService.batchInsert(studentServiceFeeTypeCompanies);
            }
        }
        return new ResponseBo<>();
    }

    /**
     * Author Cream
     * Description : //回显类型和公司的关系
     * Date 2023/2/6 10:29
     * Params:
     * Return
     */
    @Override
    public ListResponseBo<CompanyTreeVo> getTypeCompanyRelation(Long typeId) {
        if (Objects.isNull(typeId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        List<Long> companyId = studentServiceFeeTypeCompanyMapper.getServiceFeeTypeCompanyId(typeId);
        Result<List<CompanyTreeVo>> allCompanyDto = permissionCenterClient.getAllCompanyDto();
        List<CompanyTreeVo> data = allCompanyDto.getData();
        for (CompanyTreeVo treeDto : data) {
            if (companyId.contains(treeDto.getId())) {
                treeDto.setFlag(true);
            }
        }
        return new ListResponseBo<>(data);
    }

    /**
     * Author Cream
     * Description : //服务费类型下拉
     * Date 2023/2/6 16:58
     * Params:
     * Return
     */
    @Override
    public List<BaseSelectEntity> getServiceTypeList(Long companyId) {
        return serviceTypeManagementMapper.getServiceTypeList(companyId);
    }
}

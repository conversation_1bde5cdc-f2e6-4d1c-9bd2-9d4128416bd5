package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.salecenter.dao.sale.StudentOfferItemStepCountryMapper;
import com.get.salecenter.vo.StudentOfferItemStepCountryVo;
import com.get.salecenter.entity.StudentOfferItemStep;
import com.get.salecenter.entity.StudentOfferItemStepCountry;
import com.get.salecenter.service.IStudentOfferItemStepCountryService;
import com.get.salecenter.service.IStudentOfferItemStepService;
import com.get.salecenter.dto.StudentOfferItemStepCountryDto;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Service;
import com.get.core.mybatis.base.BaseServiceImpl;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-22
 */
@Service
public class StudentOfferItemStepCountryServiceImpl extends BaseServiceImpl<StudentOfferItemStepCountryMapper, StudentOfferItemStepCountry> implements IStudentOfferItemStepCountryService {

    @Resource
    private UtilService utilService;

    @Resource
    private IStudentOfferItemStepService studentOfferItemStepService;

    @Resource
    private IInstitutionCenterClient institutionCenterClient;

    @Override
    public Long add(StudentOfferItemStepCountryDto studentOfferItemStepCountryDto) {
        validatedAdd(studentOfferItemStepCountryDto);
        StudentOfferItemStepCountry studentOfferItemStepCountry = BeanCopyUtils.objClone(studentOfferItemStepCountryDto, StudentOfferItemStepCountry::new);
        String fkStudentOfferItemStepIdPrecondition = studentOfferItemStepCountryDto.getFkStudentOfferItemStepIdsPrecondition().stream().map(String::valueOf).collect(Collectors.joining(","));
        String fkAreaCountryIds = studentOfferItemStepCountryDto.getFkAreaCountryIdList().stream().map(String::valueOf).collect(Collectors.joining(","));
        studentOfferItemStepCountry.setFkStudentOfferItemStepIdPrecondition(fkStudentOfferItemStepIdPrecondition);
        studentOfferItemStepCountry.setFkAreaCountryIds(fkAreaCountryIds);
        utilService.setCreateInfo(studentOfferItemStepCountry);
        baseMapper.insert(studentOfferItemStepCountry);
        return studentOfferItemStepCountry.getId();
    }

    @Override
    public void delete(Long id) {
        boolean b = removeById(id);
        if (!b) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }

    @Override
    public List<StudentOfferItemStepCountryVo> getStudentOfferItemStepCountryDtos(StudentOfferItemStepCountryDto studentOfferItemStepCountryDto, Page page) {

        IPage<StudentOfferItemStepCountry> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<StudentOfferItemStepCountry> studentOfferItemStepCountries = page(iPage, Wrappers.lambdaQuery(StudentOfferItemStepCountry.class)
                .eq(StudentOfferItemStepCountry::getFkStudentOfferItemStepId, studentOfferItemStepCountryDto.getFkStudentOfferItemStepId())).getRecords();
        if (GeneralTool.isEmpty(studentOfferItemStepCountries)){
            return Collections.emptyList();
        }
        page.setAll((int) iPage.getTotal());

        Set<Long> fkAreaCountryIds = studentOfferItemStepCountries.stream()
                .flatMap(s -> Arrays.stream(s.getFkAreaCountryIds().split(",")))
                .map(Long::valueOf).collect(Collectors.toSet());

        Map<Long, String> countryNameMap = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(fkAreaCountryIds)){
            Result<Map<Long, String>> result = institutionCenterClient.getCountryFullNamesByIds(fkAreaCountryIds);
            if (result.isSuccess()&&GeneralTool.isNotEmpty(result.getData())){
                countryNameMap = result.getData();
            }
        }

        Set<Long> stepIds = studentOfferItemStepCountries.stream()
                .flatMap(s -> Arrays.stream(s.getFkStudentOfferItemStepIdPrecondition().split(",")))
                .map(Long::valueOf).collect(Collectors.toSet());

        List<StudentOfferItemStep> offerItemSteps = studentOfferItemStepService.list(Wrappers.lambdaQuery(StudentOfferItemStep.class).in(StudentOfferItemStep::getId, stepIds));
        Map<Long, String> stepNameMap = offerItemSteps.stream().collect(Collectors.toMap(StudentOfferItemStep::getId, StudentOfferItemStep::getStepName));

        List<StudentOfferItemStepCountryVo> studentOfferItemStepCountryVos = BeanCopyUtils.copyListProperties(studentOfferItemStepCountries, StudentOfferItemStepCountryVo::new);
        for (StudentOfferItemStepCountryVo studentOfferItemStepCountryVo : studentOfferItemStepCountryVos) {
            Map<Long, String> finalCountryNameMap = countryNameMap;
            String countryNames = Arrays.stream(studentOfferItemStepCountryVo.getFkAreaCountryIds().split(",")).map(countryId -> finalCountryNameMap.get(Long.valueOf(countryId)))
                    .filter(Objects::nonNull).collect(Collectors.joining(","));
            studentOfferItemStepCountryVo.setFkAreaCountryNames(countryNames);

            String stepNames = Arrays.stream(studentOfferItemStepCountryVo.getFkStudentOfferItemStepIdPrecondition().split(",")).map(stepId -> stepNameMap.get(Long.valueOf(stepId)))
                    .filter(Objects::nonNull).collect(Collectors.joining(","));
            studentOfferItemStepCountryVo.setFkStudentOfferItemStepNamesPrecondition(stepNames);
        }

        return studentOfferItemStepCountryVos;
    }

    private void validatedAdd(StudentOfferItemStepCountryDto studentOfferItemStepCountryDto) {
        LambdaQueryWrapper<StudentOfferItemStepCountry> wrapper = Wrappers.lambdaQuery(StudentOfferItemStepCountry.class)
                .eq(StudentOfferItemStepCountry::getFkStudentOfferItemStepId, studentOfferItemStepCountryDto.getFkStudentOfferItemStepId());

        wrapper.and(w -> {
                    for (Long countryId : studentOfferItemStepCountryDto.getFkAreaCountryIdList()) {
                        w.or().apply("FIND_IN_SET('"+countryId+"',fk_area_country_ids)");
                    }
                }
        );
        List<StudentOfferItemStepCountry> studentOfferItemStepCountries = list(wrapper);
        if (GeneralTool.isNotEmpty(studentOfferItemStepCountries)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
        }
    }

}

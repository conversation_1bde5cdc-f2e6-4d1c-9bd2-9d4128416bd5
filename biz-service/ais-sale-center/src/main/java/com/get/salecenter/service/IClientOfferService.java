package com.get.salecenter.service;


import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.salecenter.vo.ClientOfferVo;
import com.get.salecenter.vo.StudentProjectRoleStaffVo;
import com.get.salecenter.entity.StudentProjectRoleStaff;
import com.get.salecenter.dto.*;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * author:Neil
 * Time: 14:09
 * Date: 2022/8/17
 * Description:
 */
public interface IClientOfferService {
    /**
     * 获取客户申请方案
     * @param data
     * @param page
     * @return
     */
    List<ClientOfferVo> getClientOfferList(ClientOfferDto data, Page page);

    /**
     * 绑定项目成员新增
     * @param projectRoleStaffVo
     * @return
     */
    Long addProjectRoleStaff(StudentProjectRoleStaff projectRoleStaffVo);

    void updateStatus(ClientOfferUpdateDto clientOfferVo);
    /**
     * 获取客户方案详情
     * @param id
     * @return
     */
    ResponseBo<ClientOfferInfoDto> getClientInfoById(Long id);

    /**
     * 新增客户资源方案
     * @param clientOfferAddDto
     * @return
     */
    ResponseBo<Long> addClientOffer(ClientOfferAddDto clientOfferAddDto);




    /**
     * 更新客户资源方案
     * @param clientOfferAddDto
     * @return
     */
    ResponseBo<ClientOfferInfoDto> updateClientOffer(ClientOfferAddDto clientOfferAddDto);

    /**
     * 获取客户代理下拉列表
     * @return
     */
    ResponseBo<BaseSelectEntity> getClientAgentSelection(Long fkClientId);
    /**
     * 客户申请方案下拉
     * @param clientId
     * @return
     */
    List<BaseSelectEntity> getClientOfferSelect(Long clientId);

    /**
     * 解绑项目成员
     * @param projectRoleStaffVo
     * @return
     */
    StudentProjectRoleStaffVo updateProjectRoleStaff(StudentProjectRoleStaffUpdateDto projectRoleStaffVo);

    /**
     * 绑定项目成员列表数据
     * @param clientProjectRoleStaffDto
     * @param page
     * @return
     */
    List<StudentProjectRoleStaffVo> getProjectRoleStaff(ClientProjectRoleStaffDto clientProjectRoleStaffDto, Page page);

    Map<Long, String> getNumByIds(Set<Long> clientOfferIds);

    /**
     * 角色员工下拉(角色联动)
     * @return
     */
    List<BaseSelectEntity> getRoleStaffByRoleSelect();

    /**
     * 更新步骤
     * @param clientOfferVo
     */
    void updateStep(ClientOfferUpdateDto clientOfferVo);

    /**
     * 代理id
     * @param clientId
     * @return
     */
    List<BaseSelectEntity> getAgentSelect(Long clientId);
}

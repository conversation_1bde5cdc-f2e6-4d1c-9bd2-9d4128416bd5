package com.get.salecenter.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.poi.excel.BigExcelWriter;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.cache.utils.CacheUtil;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.service.impl.GetServiceImpl;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.DateUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.file.utils.FileUtils;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.dao.sale.StaffCommissionActionMapper;
import com.get.salecenter.vo.*;
import com.get.salecenter.entity.*;
import com.get.salecenter.service.IAgentService;
import com.get.salecenter.service.IRStudentOfferItemStepService;
import com.get.salecenter.service.IStaffCommissionActionService;
import com.get.salecenter.service.IStaffCommissionInstitutionService;
import com.get.salecenter.service.IStaffCommissionPolicyService;
import com.get.salecenter.service.IStaffCommissionStepService;
import com.get.salecenter.service.IStaffCommissionStudentService;
import com.get.salecenter.service.IStudentOfferItemService;
import com.get.salecenter.service.IStudentOfferItemStepService;
import com.get.salecenter.service.IStudentOfferService;
import com.get.salecenter.service.IStudentProjectRoleService;
import com.get.salecenter.service.IStudentProjectRoleStaffService;
import com.get.salecenter.service.ServiceTypeManagementService;
import com.get.salecenter.service.StudentServiceFeeService;
import com.get.salecenter.dto.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.StringJoiner;
import java.util.stream.Collectors;

import static com.get.common.cache.CacheNames.STAFF_FOLLOWER_IDS_CACHE;

/**
 * @author: Hardy
 * @create: 2023/2/6 10:53
 * @verison: 1.0
 * @description:
 */
@Service
public class StaffCommissionActionServiceImpl extends GetServiceImpl<StaffCommissionActionMapper, StaffCommissionAction> implements IStaffCommissionActionService {

    @Resource
    private UtilService utilService;
    @Lazy
    @Resource
    private IStaffCommissionPolicyService staffCommissionPolicyService;
    @Lazy
    @Resource
    private IStudentOfferItemService studentOfferItemService;
    @Lazy
    @Resource
    private IStudentOfferItemStepService studentOfferItemStepService;
    @Lazy
    @Resource
    private IStaffCommissionStepService staffCommissionStepService;
    @Lazy
    @Resource
    private IStudentProjectRoleService studentProjectRoleService;
    @Lazy
    @Resource
    private StudentServiceFeeService studentServiceFeeService;
    @Lazy
    @Resource
    private ServiceTypeManagementService serviceTypeManagementService;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Lazy
    @Resource
    private IStaffCommissionInstitutionService staffCommissionInstitutionService;
    @Lazy
    @Resource
    private IStaffCommissionStudentService staffCommissionStudentService;
    @Lazy
    @Resource
    private IStudentProjectRoleStaffService studentProjectRoleStaffService;
    @Lazy
    @Resource
    private IAgentService agentService;
    @Resource
    private StaffCommissionActionMapper staffCommissionActionMapper;
    @Resource
    private IRStudentOfferItemStepService rStudentOfferItemStepService;
    @Resource
    private IStudentOfferService studentOfferService;

    /**
     * 确认结算
     * @param staffCommissionActionDtos
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void confirmSettlement(List<StaffCommissionActionDto> staffCommissionActionDtos) {

        //查出结算的员工
        List<StaffCommissionActionDto> newStaffCommissionActionDtos = getSettlementStaff(staffCommissionActionDtos);
        //插入记录
        doBatchAddStaffCommissionAction(newStaffCommissionActionDtos);
    }

    /**
     * 批量插入
     * @param newStaffCommissionActionDtos
     */
    private void doBatchAddStaffCommissionAction(List<StaffCommissionActionDto> newStaffCommissionActionDtos) {
        if (GeneralTool.isEmpty(newStaffCommissionActionDtos)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        List<StaffCommissionAction> staffCommissionActions = BeanCopyUtils.copyListProperties(newStaffCommissionActionDtos, StaffCommissionAction::new);
        staffCommissionActions.forEach(staffCommissionAction -> {
            utilService.setCreateInfo(staffCommissionAction);
        });
        boolean b = saveBatch(staffCommissionActions);
        if (!b){
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
    }

    /**
     * 查询结算员工并设置到vo
     * @param staffCommissionActionDtos
     */
    private List<StaffCommissionActionDto> getSettlementStaff(List<StaffCommissionActionDto> staffCommissionActionDtos) {
        if (GeneralTool.isEmpty(staffCommissionActionDtos)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }

        List<StaffCommissionActionDto> maxStaffCommissionActionDtos = Lists.newArrayList();
        Map<String, List<StaffCommissionActionDto>> roleKeyMap = staffCommissionActionDtos.stream().collect(Collectors.groupingBy(StaffCommissionActionDto::getFkStudentProjectRoleKey));
        for (String s : roleKeyMap.keySet()) {
            List<StaffCommissionActionDto> commissionActionVos = roleKeyMap.get(s);
            Optional<StaffCommissionActionDto> max = commissionActionVos.stream().max(Comparator.comparing(StaffCommissionActionDto::getCommissionAmount));
            max.ifPresent(maxStaffCommissionActionDtos::add);
        }

        //币种
        Set<Long> studentIds = maxStaffCommissionActionDtos.stream().map(StaffCommissionActionDto::getFkStaffId).collect(Collectors.toSet());
        Map<Long, String> currencyTypeNumMap = Maps.newHashMap();
        StudentServiceFeeType studentServiceFeeType = serviceTypeManagementService.getOne(Wrappers.lambdaQuery(StudentServiceFeeType.class).eq(StudentServiceFeeType::getTypeKey, ProjectKeyEnum.COMMISSION_STUDENT_SERVICE_FEE.key));
        if (GeneralTool.isNotEmpty(studentServiceFeeType)){
            List<StudentServiceFee> studentServiceFees = studentServiceFeeService.list(Wrappers.lambdaQuery(StudentServiceFee.class)
                    .in(StudentServiceFee::getFkStudentId, studentIds)
                    .eq(StudentServiceFee::getStatus, 1).eq(StudentServiceFee::getFkStudentServiceFeeTypeId, studentServiceFeeType.getId()));
            if (GeneralTool.isNotEmpty(studentServiceFees)){
                currencyTypeNumMap = studentServiceFees.stream().collect(HashMap::new, (m, v) -> m.put(v.getFkStudentId(), v.getFkCurrencyTypeNum()), HashMap::putAll);
            }
        }

        Set<String> roleKeys = maxStaffCommissionActionDtos.stream().map(StaffCommissionActionDto::getFkStudentProjectRoleKey).collect(Collectors.toSet());
        Set<Long> itemIds = maxStaffCommissionActionDtos.stream().map(StaffCommissionActionDto::getFkStudentOfferItemId).collect(Collectors.toSet());
        List<StaffCommissionActionVo> staffCommissionActions = this.baseMapper.getSettlementStaffByRoleKeysAndItemIds(roleKeys,itemIds);

        List<StaffCommissionActionDto> newStaffCommissionActionDtos = Lists.newArrayList();
        for (StaffCommissionActionDto staffCommissionActionDto : maxStaffCommissionActionDtos) {

            Set<Long> staffIds = staffCommissionActions.stream().filter(staffCommissionActionVo -> staffCommissionActionVo.getFkStudentOfferItemId().equals(staffCommissionActionDto.getFkStudentOfferItemId())
                    && staffCommissionActionVo.getFkStudentProjectRoleKey().equals(staffCommissionActionDto.getFkStudentProjectRoleKey())).map(StaffCommissionActionVo::getFkStaffId).collect(Collectors.toSet());
            //一个项目角色多个人的情况 实际业务情况不会有多个人
            for (Long staffId : staffIds) {
                StaffCommissionActionDto newStaffCommissionActionDto = BeanCopyUtils.objClone(staffCommissionActionDto, StaffCommissionActionDto::new);
                assert newStaffCommissionActionDto != null;
                newStaffCommissionActionDto.setFkStaffId(staffId);
                String currencyTypeNum = GeneralTool.isEmpty(currencyTypeNumMap.get(newStaffCommissionActionDto.getFkStudentId())) ? "CNY" : currencyTypeNumMap.get(newStaffCommissionActionDto.getFkStudentId());
                newStaffCommissionActionDto.setFkCurrencyTypeNum(currencyTypeNum);
                newStaffCommissionActionDtos.add(newStaffCommissionActionDto);
            }
        }

        return newStaffCommissionActionDtos;
    }

    /**
     *
     * @param studentOfferItemCommissionInfoDto
     * @return
     */
    @Override
    public List<StaffCommissionConfirmVo> getStudentOfferItemCommissionInfo(StudentOfferItemCommissionInfoDto studentOfferItemCommissionInfoDto) {
        if (GeneralTool.isEmpty(studentOfferItemCommissionInfoDto.getFkStudentId())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }

        List<StaffCommissionConfirmVo> staffCommissionConfirmVos = checkStudentOfferItem(studentOfferItemCommissionInfoDto.getFkStudentId());
        return staffCommissionConfirmVos;

//        //提成步骤key、学生id获取对应的申请计划
//        List<StudentOfferItemCommissionVo> studentOfferItemCommissionDtos = getStudentOfferItemsByCommissionStepAndStudentKey(studentOfferItemCommissionInfoDto.getFkStudentId(),studentOfferItemCommissionInfoDto.getFkStaffCommissionStepKey());
//        //按国家和提成步骤初步筛选规则
//        List<StaffCommissionPolicy> policyList = getPoliciesByCountryAndCommissionStep(studentOfferItemCommissionDtos,studentOfferItemCommissionInfoDto.getFkStaffCommissionStepKey());
//        //申请计划匹配规则，组装列表
//        List<StaffCommissionConfirmVo> staffCommissionConfirmVos = getStudentOfferItemsHavePolicy(studentOfferItemCommissionDtos,policyList);
//        return staffCommissionConfirmVos;
    }

    /**
     * 获取学习计划
     * @param fkStudentId
     * @return
     */
    private List<StaffCommissionConfirmVo> checkStudentOfferItem(Long fkStudentId) {
        List<StudentOfferItem> studentOfferItems = studentOfferItemService.list(Wrappers.lambdaQuery(StudentOfferItem.class)
                .eq(StudentOfferItem::getFkStudentId, fkStudentId)
                .eq(StudentOfferItem::getStatus, 1)
                .ne(StudentOfferItem::getIsFollow,1));
        if (GeneralTool.isEmpty(studentOfferItems)){
            return Collections.emptyList();
        }
        List<StaffCommissionConfirmVo> staffCommissionConfirmVos = Lists.newArrayList();
        for (StudentOfferItem studentOfferItem : studentOfferItems) {
            StaffCommissionConfirmVo staffCommissionConfirmVo = BeanCopyUtils.objClone(studentOfferItem, StaffCommissionConfirmVo::new);
            staffCommissionConfirmVo.setFkStudentOfferItemId(studentOfferItem.getId());
            staffCommissionConfirmVo.setStepId(studentOfferItem.getFkStudentOfferItemStepId());
            assert staffCommissionConfirmVo != null;
            if (GeneralTool.isNotEmpty(staffCommissionConfirmVo.getFkInstitutionCourseId())&& staffCommissionConfirmVo.getFkInstitutionCourseId()==-1L){
                staffCommissionConfirmVo.setFkInstitutionCourseName(studentOfferItem.getOldCourseCustomName());
                staffCommissionConfirmVo.setCourseTypeGroupNames(studentOfferItem.getOldCourseTypeName());
            }
            if (GeneralTool.isEmpty(studentOfferItem.getFkInstitutionCourseMajorLevelIds())){
                staffCommissionConfirmVo.setFkInstitutionCourseMajorLevelName(studentOfferItem.getOldCourseMajorLevelName());
            }
            staffCommissionConfirmVos.add(staffCommissionConfirmVo);
        }
        setStaffCommissionConfirmDtosName(staffCommissionConfirmVos,true);
        return staffCommissionConfirmVos;
    }

//    /**
//     * 获取有对应规则的学习计划
//     * @param studentOfferItemCommissionDtos
//     * @param policyList
//     * @return
//     */
//    private List<StaffCommissionConfirmVo> getStudentOfferItemsHavePolicy(List<StudentOfferItemCommissionVo> studentOfferItemCommissionDtos, List<StaffCommissionPolicy> policyList) {
//        if (GeneralTool.isEmpty(studentOfferItemCommissionDtos)||GeneralTool.isEmpty(policyList)){
//            return Collections.emptyList();
//        }
//        List<Long> studentOfferItemIds = Lists.newArrayList();
//        List<StaffCommissionConfirmVo> resultList = Lists.newArrayList();
//        //步骤名称
//        List<StudentOfferItemStep> studentOfferItemSteps = studentOfferItemStepService.list();
//        Map<Long, String> offerItemStepNameMap = studentOfferItemSteps.stream().collect(Collectors.toMap(StudentOfferItemStep::getId, StudentOfferItemStep::getStepName));
//        //角色名称
//        List<StudentProjectRole> studentProjectRoles = studentProjectRoleService.list();
//        Map<Long, String> roleKeyMap = studentProjectRoles.stream().collect(Collectors.toMap(StudentProjectRole::getId, StudentProjectRole::getRoleKey));
//
//        //课程等级
//        List<String> majorLevelIdsStr = studentOfferItemCommissionDtos.stream().map(StudentOfferItemCommissionVo::getFkInstitutionCourseMajorLevelIds)
//                .filter(GeneralTool::isNotEmpty).collect(Collectors.toList());
//        Set<Long> majorLevelIds = Sets.newHashSet();
//        if(GeneralTool.isNotEmpty(majorLevelIdsStr)){
//            for (String s : majorLevelIdsStr) {
//                String[] majorLevelIdArray = s.split(",");
//                Set<Long> majorLevelIdSet = Arrays.stream(majorLevelIdArray).map(Long::valueOf).collect(Collectors.toSet());
//                majorLevelIds.addAll(majorLevelIdSet);
//            }
//        }
//        Map<Long, String> majorLevelNameMap = institutionCenterClient.getMajorLevelNamesByIds(majorLevelIds).getData();
//
//        //申请计划匹配规则
//        for (StudentOfferItemCommissionVo studentOfferItemCommissionDto : studentOfferItemCommissionDtos) {
//            if (studentOfferItemIds.contains(studentOfferItemCommissionDto.getId())){
//                continue;
//            }
//            List<StaffCommissionPolicy> suitablePolicies = policyList.stream().filter(staffCommissionPolicy -> {
//                if (!staffCommissionPolicy.getFkAreaCountryId().equals(studentOfferItemCommissionDto.getFkAreaCountryId())) {
//                    return false;
//                }
//                if (GeneralTool.isNotEmpty(staffCommissionPolicy.getFkInstitutionId()) && !staffCommissionPolicy.getFkInstitutionId().equals(studentOfferItemCommissionDto.getFkInstitutionId())) {
//                    return false;
//                }
//                if (GeneralTool.isNotEmpty(staffCommissionPolicy.getFkStudentProjectRoleKey()) && !staffCommissionPolicy.getFkStudentProjectRoleKey().equals(roleKeyMap.get(studentOfferItemCommissionDto.getFkStudentProjectRoleId()))) {
//                    return false;
//                }
//                if (GeneralTool.isNotEmpty(staffCommissionPolicy.getFkMajorLevelId()) && GeneralTool.isEmpty(studentOfferItemCommissionDto.getFkInstitutionCourseMajorLevelIds())) {
//                    return false;
//                }
//                if (GeneralTool.isNotEmpty(studentOfferItemCommissionDto.getFkInstitutionCourseMajorLevelIds())) {
//                    String[] levelIdArray = studentOfferItemCommissionDto.getFkInstitutionCourseMajorLevelIds().split(",");
//                    List<String> levelIds = Arrays.stream(levelIdArray).collect(Collectors.toList());
//                    if (GeneralTool.isNotEmpty(staffCommissionPolicy.getFkMajorLevelId()) && !levelIds.contains(staffCommissionPolicy.getFkMajorLevelId().toString())) {
//                        return false;
//                    }
//                }
//                return true;
//            }).collect(Collectors.toList());
//
//            if (GeneralTool.isNotEmpty(suitablePolicies)){
//                studentOfferItemIds.add(studentOfferItemCommissionDto.getId());
//            }
//        }
//
//        List<StudentOfferItem> studentOfferItems = studentOfferItemService.list(Wrappers.lambdaQuery(StudentOfferItem.class).in(StudentOfferItem::getId, studentOfferItemIds).orderByDesc(StudentOfferItem::getGmtCreate));
//        for (StudentOfferItem studentOfferItem : studentOfferItems) {
//            StaffCommissionConfirmVo staffCommissionConfirmDto = BeanCopyUtils.objClone(studentOfferItem, StaffCommissionConfirmVo::new);
//            assert staffCommissionConfirmDto != null;
//            if (studentOfferItem.getFkInstitutionCourseId()==-1L){
//                staffCommissionConfirmDto.setFkInstitutionCourseName(studentOfferItem.getOldCourseCustomName());
//                staffCommissionConfirmDto.setCourseTypeGroupNames(studentOfferItem.getOldCourseTypeName());
//            }
//
//            if (GeneralTool.isNotEmpty(staffCommissionConfirmDto.getFkInstitutionCourseMajorLevelIds())){
//                String[] majorLevelIdArray = staffCommissionConfirmDto.getFkInstitutionCourseMajorLevelIds().split(",");
//                StringJoiner sj = new StringJoiner(",");
//                for (String s : majorLevelIdArray) {
//                    String majorLevelName = majorLevelNameMap.get(Long.valueOf(s));
//                    if (GeneralTool.isNotEmpty(majorLevelName)){
//                        sj.add(majorLevelName);
//                    }
//                }
//                staffCommissionConfirmDto.setFkInstitutionCourseMajorLevelName(sj.toString());
//            }
//
//            staffCommissionConfirmDto.setStepId(studentOfferItem.getFkStudentOfferItemStepId());
//            staffCommissionConfirmDto.setStepName(offerItemStepNameMap.get(studentOfferItem.getFkStudentOfferItemStepId()));
//            resultList.add(staffCommissionConfirmDto);
//        }
//        //设置名称
//        setStaffCommissionConfirmDtosName(resultList,true);
//        return resultList;
//    }

    /**
     * 未确认/已确认/已结算列表信息查看
     * @param studentOfferItemCommissionInfoDto
     * @return
     */
    @Override
    public SettlementInfoVo getSettlementInfo(StudentOfferItemCommissionInfoDto studentOfferItemCommissionInfoDto) {
        //提成步骤key、学生id获取对应的申请计划
        List<StudentOfferItemCommissionVo> studentOfferItemCommissionVos = getStudentOfferItemsByCommissionStepAndStudentKey(studentOfferItemCommissionInfoDto.getFkStudentId(), studentOfferItemCommissionInfoDto.getFkStaffCommissionStepKey());
        //按国家和提成步骤初步筛选规则
        List<StaffCommissionPolicy> policyList = getPoliciesByCountryAndCommissionStep(studentOfferItemCommissionVos, studentOfferItemCommissionInfoDto.getFkStaffCommissionStepKey());
        //申请计划匹配规则，组装列表
        SettlementInfoVo settlementInfoVo = getStaffCommissionConfirmDtos(studentOfferItemCommissionVos,policyList, studentOfferItemCommissionInfoDto);

        return settlementInfoVo;
    }

    /**
     * 取消结算
     * @param studentId
     * @param commissionStep
     */
    @Override
    public void cancelSettlement(Long studentId, String commissionStep) {
        boolean b = remove(Wrappers.lambdaQuery(StaffCommissionAction.class).eq(StaffCommissionAction::getFkStudentId, studentId)
                .eq(StaffCommissionAction::getFkStaffCommissionStepKey, commissionStep));
        if (!b){
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }

    /**
     *
     * @param staffCommissionSummaryDto
     * @return
     */
    @Override
    public List<StaffCommissionSummaryVo> getStaffCommissionSummary(StaffCommissionSummaryDto staffCommissionSummaryDto, List<StaffCommissionDatasVo> staffCommissionDatasVos) {
        Long staffId = SecureUtil.getStaffId();
        List<Long> staffFollowerIds = Lists.newArrayList();
        List<Long> followerIds = CacheUtil.get(
                STAFF_FOLLOWER_IDS_CACHE,
                "staffId:",
                staffId,
                ()->permissionCenterClient.getStaffFollowerIds(staffId).getData()
        );
        if (GeneralTool.isNotEmpty(followerIds)) {
            assert followerIds != null;
            staffFollowerIds.addAll(followerIds.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList()));
        }
        staffFollowerIds.add(staffId);

        List<StaffCommissionSummaryVo> staffCommissionSummaryVos = this.baseMapper.getStaffCommissionSummary(staffCommissionSummaryDto,staffFollowerIds,SecureUtil.getCountryIds());

        if (GeneralTool.isNotEmpty(staffCommissionDatasVos)){
            List<String> commissionStepKeys = staffCommissionDatasVos.stream().map(StaffCommissionDatasVo::getKey).collect(Collectors.toList());

            //固定数据筛选出学生id
            List<Long> studentIdList = staffCommissionSummaryVos.stream().map(StaffCommissionSummaryVo::getFkStudentId).collect(Collectors.toList());
            if(GeneralTool.isEmpty(studentIdList)){
                studentIdList.add(0L);
            }
            List<StaffCommissionSummaryVo> staffCommissionSummaryVoList = this.baseMapper.getStaffCommissionDynamicSummary(staffCommissionSummaryDto,commissionStepKeys,staffFollowerIds,SecureUtil.getCountryIds(),studentIdList);

//            Set<Long> studentIds = Sets.newHashSet();
//            if (GeneralTool.isNotEmpty(staffCommissionSummaryVoList)){
//                studentIds = staffCommissionSummaryVoList.stream().map(StaffCommissionSummaryVo::getFkStudentId).collect(Collectors.toSet());
//                if (GeneralTool.isEmpty(studentIds)){
//                    studentIds.add(0L);
//                }
//            }

//            List<StaffCommissionSummaryVo> staffCommissionSummaryPolicyStatusList = this.baseMapper.getStaffCommissionDynamicSummaryPolicyStatus(staffCommissionSummaryDto,studentIds);
//            Map<Long, List<StaffCommissionSummaryVo>> policyStatusListMap = Maps.newHashMap();
//            if (GeneralTool.isNotEmpty(staffCommissionSummaryPolicyStatusList)){
//                policyStatusListMap = staffCommissionSummaryPolicyStatusList.stream().collect(Collectors.groupingBy(StaffCommissionSummaryVo::getFkStudentId));
//            }
            Map<Long, List<StaffCommissionSummaryVo>> staffCommissionSummaryDtosMap = Maps.newHashMap();
            if (GeneralTool.isNotEmpty(staffCommissionSummaryVoList)){
                staffCommissionSummaryDtosMap = staffCommissionSummaryVoList.stream().collect(Collectors.groupingBy(StaffCommissionSummaryVo::getFkStudentId));
            }
            for (StaffCommissionSummaryVo staffCommissionSummaryVo : staffCommissionSummaryVos) {
                List<StaffCommissionDatasVo<Integer>> result = Lists.newArrayList();
                List<StaffCommissionSummaryVo> summaryDtoList = staffCommissionSummaryDtosMap.get(staffCommissionSummaryVo.getFkStudentId());
                Set<String> keys = Sets.newHashSet();
                Map<String, StaffCommissionSummaryVo> summaryDtoMap = Maps.newHashMap();
                if (GeneralTool.isNotEmpty(summaryDtoList)){
                    keys = summaryDtoList.stream().map(StaffCommissionSummaryVo::getFkStaffCommissionStepKey).collect(Collectors.toSet());
                    summaryDtoMap = summaryDtoList.stream().filter(s -> GeneralTool.isNotEmpty(s.getFkStaffCommissionStepKey())).collect(HashMap::new, (m, v) -> m.put(v.getFkStaffCommissionStepKey(), v), HashMap::putAll);
                }
//                List<StaffCommissionSummaryVo> policyStatusList = policyStatusListMap.get(staffCommissionSummaryVo.getFkStudentId());

                for (String commissionStepKey : commissionStepKeys) {
                    StaffCommissionDatasVo<Integer> staffCommissionDatasVo = new StaffCommissionDatasVo();
                    staffCommissionDatasVo.setKey(commissionStepKey);
                    if (!keys.contains(commissionStepKey)){
//                        if (GeneralTool.isNotEmpty(policyStatusList)){
//                            List<StaffCommissionSummaryVo> policyStatus = policyStatusList.stream().filter(s -> commissionStepKey.equals(s.getFkStaffCommissionStepKey())).collect(Collectors.toList());
//                            if (GeneralTool.isNotEmpty(policyStatus)){
//                                if (policyStatus.get(0).getPolicyStatus()==1){
//                                    staffCommissionDatasVo.setValue(5);
//                                }
//                                if (policyStatus.get(0).getPolicyStatus()==0){
//                                    staffCommissionDatasVo.setValue(4);
//                                }
//                            }else {
//                                staffCommissionDatasVo.setValue(4);
//                            }
//                        }else {
//                            staffCommissionDatasVo.setValue(4);
//                        }
                        staffCommissionDatasVo.setValue(4);
                    }else {
                        StaffCommissionSummaryVo summaryDto = summaryDtoMap.get(commissionStepKey);
                        staffCommissionDatasVo.setValue(summaryDto.getSettlementStatus());
//                        List<StaffCommissionSummaryVo> policyStatus = policyStatusList.stream().filter(s -> commissionStepKey.equals(s.getFkStaffCommissionStepKey())).collect(Collectors.toList());
//                        if (summaryDto.getSettlementStatus() == 0){
//                             if (policyStatus.get(0).getPolicyStatus()==0){
//                                 staffCommissionDatasVo.setValue(4);
//                             }else if (policyStatus.get(0).getPolicyStatus()==1){
//                                 if (summaryDto.getOfferItemStatus()==1){
//                                    staffCommissionDatasVo.setValue(0);
//                                 }else {
//                                     staffCommissionDatasVo.setValue(5);
//                                 }
//                             }
//                        }else {
//                            staffCommissionDatasVo.setValue(summaryDto.getSettlementStatus());
//                        }
//
//                        if (commissionStepKey.equals(ProjectKeyEnum.COMMISSION_STEP_OTHER_INSTITUTION.key)){
//                            if (0 == summaryDto.getOfferItemStatus()){
//                                staffCommissionDatasVo.setValue(4);
//                            }
//                        }
//                        if (commissionStepKey.equals(ProjectKeyEnum.COMMISSION_STEP_OTHER_MAJOR.key)){
//                            if (0 == summaryDto.getOfferItemStatus()){
//                                staffCommissionDatasVo.setValue(4);
//                            }
//                        }
//                        if (commissionStepKey.equals(ProjectKeyEnum.COMMISSION_STEP_ADDAPP.key)){
//                            if (0 == summaryDto.getAddAppStatus()){
//                                staffCommissionDatasVo.setValue(4);
//                            }
//                        }
//                        if (commissionStepKey.equals(ProjectKeyEnum.COMMISSION_STEP_INSTITUTION.key)){
//                            if (0 == summaryDto.getInstitutionStatus()){
//                                staffCommissionDatasVo.setValue(4);
//                            }
//                        }
                    }
                    result.add(staffCommissionDatasVo);
                }
                staffCommissionSummaryVo.setStaffCommissionSteps(result);
            }
        }

        setStaffCommissionSummaryListName(staffCommissionSummaryVos);

        return staffCommissionSummaryVos;
    }

    /**
     * 设置名称
     * @param staffCommissionSummaryVos
     */
    private void setStaffCommissionSummaryListName(List<StaffCommissionSummaryVo> staffCommissionSummaryVos) {
        Set<Long> companyIdds = staffCommissionSummaryVos.stream().map(StaffCommissionSummaryVo::getFkCompanyId).collect(Collectors.toSet());
        Map<Long, String> companyMap = permissionCenterClient.getCompanyNamesByIds(companyIdds).getData();

        Set<Long> studentIds = staffCommissionSummaryVos.stream().map(StaffCommissionSummaryVo::getFkStudentId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Long, Integer> studentStatusMap = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(studentIds)){

            List<StaffCommissionStudent> staffCommissionStudents = staffCommissionStudentService.list(Wrappers.lambdaQuery(StaffCommissionStudent.class).in(StaffCommissionStudent::getFkStudentId, studentIds));
            if (GeneralTool.isNotEmpty(staffCommissionStudents)){
                studentStatusMap = staffCommissionStudents.stream().collect(Collectors.toMap(StaffCommissionStudent::getFkStudentId, StaffCommissionStudent::getStatus));
            }
        }

        for (StaffCommissionSummaryVo staffCommissionSummaryVo : staffCommissionSummaryVos) {
            String commissionStatusName = staffCommissionSummaryVo.getCommissionStatus() == 1 ? "申请中" : "结案";
            staffCommissionSummaryVo.setCommissionStatusName(commissionStatusName);
            staffCommissionSummaryVo.setReceiveStatusName(ProjectExtraEnum.getInitialValueByKey(staffCommissionSummaryVo.getReceiveStatus(),ProjectExtraEnum.AR_STATUS));

            String studentName = staffCommissionSummaryVo.getName()+"（"
                    +(GeneralTool.isEmpty(staffCommissionSummaryVo.getLastName())?"": staffCommissionSummaryVo.getLastName())
                    +(GeneralTool.isEmpty(staffCommissionSummaryVo.getFirstName())?"": staffCommissionSummaryVo.getFirstName())
                    +")";
            staffCommissionSummaryVo.setStudentName(studentName);

            List<StaffCommissionDatasVo<Integer>> staffCommissionSteps = staffCommissionSummaryVo.getStaffCommissionSteps();
            if (GeneralTool.isNotEmpty(staffCommissionSteps)){
                Set<Integer> values = staffCommissionSteps.stream().map(StaffCommissionDatasVo<Integer>::getValue).collect(Collectors.toSet());
                //结案按钮 无未确认  且学生结算状态==1
                if (!values.contains(1)
                        &&GeneralTool.isNotEmpty(studentStatusMap.get(staffCommissionSummaryVo.getFkStudentId()))
                        &&studentStatusMap.get(staffCommissionSummaryVo.getFkStudentId()).equals(1)){
                    staffCommissionSummaryVo.setFinishedButtonType(true);
                }else {
                    staffCommissionSummaryVo.setFinishedButtonType(false);
                }

                //取消结算 无关系表
                if (!values.contains(1)&&!values.contains(2)&&!values.contains(3)
                        &&GeneralTool.isNotEmpty(studentStatusMap.get(staffCommissionSummaryVo.getFkStudentId()))
                        &&studentStatusMap.get(staffCommissionSummaryVo.getFkStudentId()).equals(1)){
                    staffCommissionSummaryVo.setCancelSettlementButtonType(true);
                }else {
                    staffCommissionSummaryVo.setCancelSettlementButtonType(false);
                }
            }

            staffCommissionSummaryVo.setFkCompanyName(companyMap.get(staffCommissionSummaryVo.getFkCompanyId()));

        }

    }

    /**
     * 标题
     * @param companyId
     * @return
     */
    @Override
    public List<StaffCommissionDatasVo> getStaffCommissionStepTitles(Long companyId) {
        if (GeneralTool.isEmpty(companyId)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        List<StaffCommissionStep> staffCommissionSteps = staffCommissionStepService.list(Wrappers.lambdaQuery(StaffCommissionStep.class).eq(StaffCommissionStep::getFkCompanyId, companyId).orderByDesc(StaffCommissionStep::getStepOrder));
        if (GeneralTool.isEmpty(staffCommissionSteps)){
            return Collections.emptyList();
        }
        List<StaffCommissionDatasVo> staffCommissionDatasVos = Lists.newArrayList();
        for (StaffCommissionStep staffCommissionStep : staffCommissionSteps) {
            StaffCommissionDatasVo staffCommissionDatasVo = new StaffCommissionDatasVo();
            staffCommissionDatasVo.setTitle(staffCommissionStep.getStepName());
            staffCommissionDatasVo.setTitleChn(staffCommissionStep.getStepName());
            staffCommissionDatasVo.setKey(staffCommissionStep.getStepKey());
            staffCommissionDatasVo.setViewOrder(staffCommissionStep.getStepOrder());
            staffCommissionDatasVos.add(staffCommissionDatasVo);
        }
        return staffCommissionDatasVos;
    }

    /**
     * 汇总列表取消结算
     * @param studentId
     */
    @Override
    public void cancelSettlementInfo(Long studentId) {
        List<StaffCommissionAction> staffCommissionActions = list(Wrappers.lambdaQuery(StaffCommissionAction.class).eq(StaffCommissionAction::getFkStudentId, studentId).last(" limit 1"));
        if (GeneralTool.isEmpty(staffCommissionActions)){
            boolean b = staffCommissionStudentService.remove(Wrappers.lambdaQuery(StaffCommissionStudent.class)
                    .eq(StaffCommissionStudent::getFkStudentId, studentId));
            if (!b){
                throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
            }
        }

    }

    /**
     * 汇总列表结案
     * @param studentId
     */
    @Override
    public void completeSettlement(Long studentId) {
        List<StaffCommissionStudent> staffCommissionStudents = staffCommissionStudentService.list(Wrappers.lambdaQuery(StaffCommissionStudent.class)
                .eq(StaffCommissionStudent::getFkStudentId, studentId).eq(StaffCommissionStudent::getStatus, 1));
        if (GeneralTool.isEmpty(staffCommissionStudents)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        for (StaffCommissionStudent staffCommissionStudent : staffCommissionStudents) {
            staffCommissionStudent.setStatus(2);
            utilService.setUpdateInfo(staffCommissionStudent);
        }
        boolean b = staffCommissionStudentService.updateBatchById(staffCommissionStudents);
        if (!b){
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
    }

    /**
     * 提成员工列表
     * @param commissionStaffDatasDto
     * @return
     */
    @Override
    public List<CommissionStaffDatasVo> getCommissionStaffDatas(CommissionStaffDatasDto commissionStaffDatasDto, List<StaffCommissionDatasVo> staffCommissionDatasVos) {
        List<CommissionStaffDatasVo> result = Lists.newArrayList();
        List<String> commissionStepKeys = staffCommissionDatasVos.stream().map(StaffCommissionDatasVo::getKey).collect(Collectors.toList());
        List<CommissionStaffDatasItemVo> commissionStaffDatasItemVos = this.baseMapper.getCommissionStaffDatas(commissionStaffDatasDto);
        if (GeneralTool.isEmpty(commissionStaffDatasItemVos)){
            return Collections.emptyList();
        }
        Map<Long, List<CommissionStaffDatasItemVo>> commissionStaffDatasItemDtoMap = commissionStaffDatasItemVos.stream().collect(Collectors.groupingBy(CommissionStaffDatasItemVo::getFkStaffId));
        List<Long> staffIds = commissionStaffDatasItemVos.stream().map(CommissionStaffDatasItemVo::getFkStaffId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        //员工名称
        Map<Long, String> staffNamesMap = permissionCenterClient.getStaffNamesByIds(Sets.newHashSet(staffIds));
        //角色列表
        List<StudentProjectRole> studentProjectRoles = studentProjectRoleService.list();
        Map<Long, String> projectRoleNameMap= studentProjectRoles.stream().collect(Collectors.toMap(StudentProjectRole::getId, StudentProjectRole::getRoleName));
        Map<Long, String> projectRoleKeyMap= studentProjectRoles.stream().collect(Collectors.toMap(StudentProjectRole::getId, StudentProjectRole::getRoleKey));


        for (Long staffId : staffIds) {
//            Map<String, BigDecimal> sumAmoutMap = Maps.newHashMap();
//            for (String commissionStepKey : commissionStepKeys) {
//                sumAmoutMap.put(commissionStepKey,BigDecimal.ZERO);
//            }
            CommissionStaffDatasVo commissionStaffDatasVo = new CommissionStaffDatasVo();
            commissionStaffDatasVo.setFkStaffId(staffId);
//            //总结算
//            List<StaffCommissionDatasVo<BigDecimal>> sumCommissionDatas = Lists.newArrayList();
            List<CommissionStaffDatasItemVo> dataItemDtos = Lists.newArrayList();
            //同一个员工下
            List<CommissionStaffDatasItemVo> commissionStaffDatasItemVoList = commissionStaffDatasItemDtoMap.get(staffId);
            if (GeneralTool.isEmpty(commissionStaffDatasItemVoList)){
                continue;
            }
            //设置角色ids
            commissionStaffDatasVo.setFkStudentProjectRoleIds(commissionStaffDatasItemVoList.get(0).getFkStudentProjectRoleIds());

            //根据学生纬度区分
            List<Long> studentIds = commissionStaffDatasItemVoList.stream().map(CommissionStaffDatasItemVo::getFkStudentId).distinct().collect(Collectors.toList());
            Map<Long, List<CommissionStaffDatasItemVo>> itemDtoGroupByStudent = commissionStaffDatasItemVoList.stream().collect(Collectors.groupingBy(CommissionStaffDatasItemVo::getFkStudentId));

            for (Long studentId : studentIds) {
                //学生下的数据
                List<CommissionStaffDatasItemVo> datasItemDtoList = itemDtoGroupByStudent.get(studentId);
                if (GeneralTool.isEmpty(datasItemDtoList)){
                    continue;
                }
                CommissionStaffDatasItemVo commissionStaffDatasItemVo = BeanCopyUtils.objClone(datasItemDtoList.get(0), CommissionStaffDatasItemVo::new);
                assert commissionStaffDatasItemVo != null;
                commissionStaffDatasItemVo.setFkStaffCommissionStepKey(null);
                commissionStaffDatasItemVo.setCommissionAmount(null);
                List<CommissionStaffDatasItemVo> notNullList = datasItemDtoList.stream().filter(c -> GeneralTool.isNotEmpty(c.getFkStaffCommissionStepKey())).collect(Collectors.toList());
                Map<String, List<CommissionStaffDatasItemVo>> commissionStaffDatasItemDtoByStepKey = Maps.newHashMap();
                if (GeneralTool.isNotEmpty(notNullList)){
                    commissionStaffDatasItemDtoByStepKey = notNullList.stream().collect(Collectors.groupingBy(CommissionStaffDatasItemVo::getFkStaffCommissionStepKey));
                }


                //动态步骤的数据
                List<StaffCommissionDatasVo<CommissionStaffDetailVo>> dynamicDatasDtos = Lists.newArrayList();
                for (String commissionStepKey : commissionStepKeys) {
                    StaffCommissionDatasVo<CommissionStaffDetailVo> staffCommissionDatasVo = new StaffCommissionDatasVo<CommissionStaffDetailVo>();
                    staffCommissionDatasVo.setKey(commissionStepKey);
                    List<CommissionStaffDetailVo> commissionStaffDetailVos = Lists.newArrayList();
                    if (commissionStaffDatasItemDtoByStepKey.containsKey(commissionStepKey)){
                        List<CommissionStaffDatasItemVo> datasItemDtos = commissionStaffDatasItemDtoByStepKey.get(commissionStepKey);

                        List<CommissionStaffDatasItemVo> noSettlementList = datasItemDtos.stream().filter(c -> c.getSettlementStatus() == 0).collect(Collectors.toList());
                        List<CommissionStaffDatasItemVo> settlementList = datasItemDtos.stream().filter(c -> c.getSettlementStatus() == 1).collect(Collectors.toList());
                        if (GeneralTool.isNotEmpty(noSettlementList)){
                            BigDecimal noCommissionAmount = noSettlementList.stream().map(CommissionStaffDatasItemVo::getCommissionAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                            CommissionStaffDetailVo noCommissionStaffDetailVo = new CommissionStaffDetailVo();
                            noCommissionStaffDetailVo.setAmount(noCommissionAmount);
                            noCommissionStaffDetailVo.setType(noSettlementList.get(0).getSettlementStatus());
                            commissionStaffDetailVos.add(noCommissionStaffDetailVo);
                        }
                        if (GeneralTool.isNotEmpty(settlementList)){
                            BigDecimal commissionAmount = settlementList.stream().map(CommissionStaffDatasItemVo::getCommissionAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                            CommissionStaffDetailVo commissionStaffDetailVo = new CommissionStaffDetailVo();
                            commissionStaffDetailVo.setAmount(commissionAmount);
                            commissionStaffDetailVo.setType(settlementList.get(0).getSettlementStatus());
                            commissionStaffDetailVos.add(commissionStaffDetailVo);
                        }
//                        for (CommissionStaffDatasItemVo datasItemDto : datasItemDtos) {
//                            CommissionStaffDetailVo commissionStaffDetailDto = new CommissionStaffDetailVo();
//                            commissionStaffDetailDto.setAmount(datasItemDto.getCommissionAmount());
//                            commissionStaffDetailDto.setType(datasItemDto.getSettlementStatus());
//                            commissionStaffDetailVos.add(commissionStaffDetailDto);
//                        }

                    }else {
                        CommissionStaffDetailVo commissionStaffDetailVo = new CommissionStaffDetailVo();
                        commissionStaffDetailVo.setAmount(BigDecimal.ZERO);
                        commissionStaffDetailVo.setType(0);
                        commissionStaffDetailVos.add(commissionStaffDetailVo);
                    }

//                    //单个步骤结算
//                    BigDecimal sum = commissionStaffDetailVos.stream().filter(c -> c.getType() == 0).map(CommissionStaffDetailVo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
//                    sumAmoutMap.put(commissionStepKey,sumAmoutMap.get(commissionStepKey).add(sum));

                    staffCommissionDatasVo.setValue(commissionStaffDetailVos);
                    dynamicDatasDtos.add(staffCommissionDatasVo);
                }
                //设置动态数据
                commissionStaffDatasItemVo.setStaffCommissionDatasDtos(dynamicDatasDtos);
                dataItemDtos.add(commissionStaffDatasItemVo);
            }


//            for (String commissionStepKey : commissionStepKeys) {
//                //单个步骤结算
//                StaffCommissionDatasVo<BigDecimal> sumDataItemDto = new StaffCommissionDatasVo<BigDecimal>();
////                BigDecimal sum = commissionStaffDetailDtos.stream().filter(c -> c.getType() == 0).map(CommissionStaffDetailVo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
//                BigDecimal sum = sumAmoutMap.get(commissionStepKey);
//                sumDataItemDto.setValue(sum);
//                sumDataItemDto.setKey(commissionStepKey);
//                sumCommissionDatas.add(sumDataItemDto);
//            }
//            commissionStaffDatasVo.setSumCommissionData(sumCommissionDatas);

            commissionStaffDatasVo.setCommissionStaffDatasItemDtos(dataItemDtos);

            //单个员工数据
            result.add(commissionStaffDatasVo);
        }

        for (CommissionStaffDatasVo commissionStaffDatasVo : result) {
            String fkStudentProjectRoleIds = commissionStaffDatasVo.getFkStudentProjectRoleIds();
            String[] split = fkStudentProjectRoleIds.split(",");
            StringJoiner sj = new StringJoiner(",");
            List<String> roleKeys = Lists.newArrayList();
            for (String s : split) {
                sj.add(projectRoleNameMap.get(Long.valueOf(s)));
                roleKeys.add(projectRoleKeyMap.get(Long.valueOf(s)));
            }
            //设置角色名称
            commissionStaffDatasVo.setFkStudentProjectRoleName(sj.toString());
            //员工名称
            commissionStaffDatasVo.setFkStaffName(staffNamesMap.get(commissionStaffDatasVo.getFkStaffId()));
            //角色key
            commissionStaffDatasVo.setFkStudentProjectRoleKeys(roleKeys);
        }

        //设置属性
        setCommissionStaffDatasDtoListName(result);

        return result;
    }


    /**
     * 设置属性
     * @param result
     */
    private void setCommissionStaffDatasDtoListName(List<CommissionStaffDatasVo> result) {
        if (GeneralTool.isEmpty(result)){
            return;
        }
        List<CommissionStaffDatasItemVo> commissionStaffDatasItemVos = Lists.newArrayList();

        for (CommissionStaffDatasVo commissionStaffDatasVo : result) {
            commissionStaffDatasItemVos.addAll(commissionStaffDatasVo.getCommissionStaffDatasItemDtos());
        }
        Set<Long> companyIds = commissionStaffDatasItemVos.stream().map(CommissionStaffDatasItemVo::getFkCompanyId).collect(Collectors.toSet());
        Map<Long, String> companyNamesMap = permissionCenterClient.getCompanyNamesByIds(companyIds).getData();

        for (CommissionStaffDatasVo commissionStaffDatasVo : result) {
            if (GeneralTool.isNotEmpty(commissionStaffDatasVo.getCommissionStaffDatasItemDtos())){
                for (CommissionStaffDatasItemVo commissionStaffDatasItemVo : commissionStaffDatasVo.getCommissionStaffDatasItemDtos()) {
                    commissionStaffDatasItemVo.setFkCompanyName(companyNamesMap.get(commissionStaffDatasItemVo.getFkCompanyId()));
                    commissionStaffDatasItemVo.setCommissionStatusName(commissionStaffDatasItemVo.getCommissionStatus()==1?"申请中":"结案");
                }
            }
        }
    }

    /**
     * 批量结算
     * @param batchSettlementCommissionDtos
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean batchSettlementCommission(List<BatchSettlementCommissionDto> batchSettlementCommissionDtos) {
        if (GeneralTool.isEmpty(batchSettlementCommissionDtos)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        for (BatchSettlementCommissionDto batchSettlementCommissionDto : batchSettlementCommissionDtos) {
            if (GeneralTool.isEmpty(batchSettlementCommissionDto.getFkStaffId())||GeneralTool.isEmpty(batchSettlementCommissionDto.getFkStudentIds())){
                throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
            }
            StaffCommissionAction staffCommissionAction = new StaffCommissionAction();
            staffCommissionAction.setStatus(1);
            utilService.setUpdateInfo(staffCommissionAction);
            boolean b = update(staffCommissionAction, Wrappers.lambdaUpdate(StaffCommissionAction.class)
                    .eq(StaffCommissionAction::getFkStaffId, batchSettlementCommissionDto.getFkStaffId())
                    .in(StaffCommissionAction::getFkStudentId, batchSettlementCommissionDto.getFkStudentIds())
                    .eq(StaffCommissionAction::getStatus, 0));
            if (!b){
                return false;
            }
        }
        return true;
    }

    /**
     * 代理下拉
     * @param companyId
     * @return
     */
    @Override
    public List<BaseSelectEntity> getStaffCommissionAgentList(Long companyId) {
        List<Agent> agents = this.baseMapper.getStaffCommissionAgentList(companyId);
        if (GeneralTool.isEmpty(agents)){
            return Collections.emptyList();
        }
        Map<Long, String> nameNoteMap = agents.stream().collect(HashMap::new, (m, v) -> m.put(v.getId(), v.getNameNote()), HashMap::putAll);
        List<BaseSelectEntity> resultList = agents.stream().map(agent -> BeanCopyUtils.objClone(agent, BaseSelectEntity::new)).collect(Collectors.toList());
        for (BaseSelectEntity entity : resultList) {
            if (GeneralTool.isNotEmpty(nameNoteMap)&&GeneralTool.isNotEmpty(nameNoteMap.get(entity.getId()))){
                entity.setName(entity.getName()+"（"+nameNoteMap.get(entity.getId())+"）");
            }
            entity.setFullName(nameNoteMap.get(entity.getId()));
        }
        return resultList;
    }

    /**
     * 员工下拉
     * @param companyId
     * @return
     */
    @Override
    public List<BaseSelectEntity> getStaffCommissionStaffList(Long companyId) {
//        List<StaffCommissionAction> staffCommissionActions = list(Wrappers.lambdaQuery(StaffCommissionAction.class).select(StaffCommissionAction::getFkStaffId).eq(StaffCommissionAction::getFkCompanyId, companyId));
        List<String> departmentIdStrList = this.baseMapper.getStaffCommissionStaffIds(companyId);
        List<Long> departmentIds = Lists.newArrayList();
        if (GeneralTool.isNotEmpty(departmentIdStrList)){
            for (String s : departmentIdStrList) {
                Set<Long> ids = Arrays.stream(s.split(",")).filter(Objects::nonNull).map(Long::valueOf).collect(Collectors.toSet());
                departmentIds.addAll(ids);
            }
        }
        if (GeneralTool.isEmpty(departmentIds)){
            departmentIds.add(0L);
        }
        List<BaseSelectEntity> data = permissionCenterClient.getStaffByDepartmentIds(departmentIds).getData();
        return data;
//        if (GeneralTool.isEmpty(staffCommissionActions)) {
//            return Collections.emptyList();
//        }
//        Set<Long> staffIds = staffCommissionActions.stream().map(StaffCommissionAction::getFkStaffId).collect(Collectors.toSet());
//        List<BaseSelectEntity> staffList = Lists.newArrayList();
//        Result<List<BaseSelectEntity>> result = permissionCenterClient.getStaffByStaffIds(staffIds);
//        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
//            staffList.addAll(result.getData());
//        }
//
//        for (BaseSelectEntity baseSelectEntity : staffList) {
//            baseSelectEntity.setFullName(baseSelectEntity.getFullName());
//        }
//        List<BaseSelectEntity> first = staffList.stream().filter(s -> s.getStatus() == 1).collect(Collectors.toList());
//        List<BaseSelectEntity> entities = first.stream().sorted(Comparator.comparing(BaseSelectEntity::getName)).collect(Collectors.toList());
//        return entities;
    }

    /**
     * 取消结算单条
     * @param studentId
     * @param studentOfferItemId
     * @param commissionStep
     */
    @Override
    public void cancelSettlementSingle(Long studentId, Long studentOfferItemId, String commissionStep) {
        boolean b = remove(Wrappers.lambdaQuery(StaffCommissionAction.class).eq(StaffCommissionAction::getFkStudentId, studentId)
                .eq(StaffCommissionAction::getFkStaffCommissionStepKey, commissionStep)
                .eq(StaffCommissionAction::getFkStudentOfferItemId,studentOfferItemId));
        if (!b){
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }

    /**
     * 员工结算统计
     * @param staffSettlementStatisticsDto
     * @param staffCommissionDatasVos
     * @return
     */
    @Override
    public StaffSettlementStatisticsVo getStaffSettlementStatistics(StaffSettlementStatisticsDto staffSettlementStatisticsDto, List<StaffCommissionDatasVo> staffCommissionDatasVos) {
        //最终返回的数据结构
        StaffSettlementStatisticsVo result = new StaffSettlementStatisticsVo();

        if (GeneralTool.isEmpty(staffSettlementStatisticsDto.getFkCompanyId())
                ||GeneralTool.isEmpty(staffSettlementStatisticsDto.getFkStaffId())
                ||GeneralTool.isEmpty(staffSettlementStatisticsDto.getSettlementDate())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        String staffName = permissionCenterClient.getStaffName(staffSettlementStatisticsDto.getFkStaffId()).getData();
        result.setStaffName(staffName);

        //总计
        List<Map<String,Object>> totalAmount = Lists.newArrayList();

        //提成步骤keys
        List<String> commissionStepKeys = staffCommissionDatasVos.stream().map(StaffCommissionDatasVo::getKey).collect(Collectors.toList());

        //获取列表数据 同时统计总计数据
        result.setStaffSettlementStatisticsItemDtos(getStaffSettlementStatisticsItemDtos(staffSettlementStatisticsDto,commissionStepKeys,totalAmount));

        //设置总计数据
        result.setTotalAmount(totalAmount);

        //获取结算金额数据
        result.setSumSettlementAmount(getSumSettlementAmounts(staffSettlementStatisticsDto,commissionStepKeys));

        //设置表头中文
        setTitleName(result,commissionStepKeys);

        //统计的结算日期
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy年MM月");
        result.setSumSettlementDate(simpleDateFormat.format(staffSettlementStatisticsDto.getSettlementDate()));

        return result;
    }

    /**
     *
     * @param response
     * @param staffSettlementStatisticsDto
     * @param staffCommissionDatasVos
     */
    @Override
    public void exportStaffSettlementStatistics(HttpServletResponse response, StaffSettlementStatisticsDto staffSettlementStatisticsDto, List<StaffCommissionDatasVo> staffCommissionDatasVos) {
        if (GeneralTool.isEmpty(staffCommissionDatasVos)){
            return;
        }
        //查询导出的数据
        StaffSettlementStatisticsVo settlementStatisticsDto = getStaffSettlementStatistics(staffSettlementStatisticsDto, staffCommissionDatasVos);
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + "StaffSettlement" + ".xlsx");
        ServletOutputStream out = null;
        try {
            out = response.getOutputStream();

            ExcelWriter writer = ExcelUtil.getWriter();
            //动态的标题
            List<String> title = staffCommissionDatasVos.stream().map(StaffCommissionDatasVo::getTitle).collect(Collectors.toList());
            //未结算的单元格数据 用于修改字体颜色
            List<Map<String,Object>> notSettlementStyleCell = Lists.newArrayList();
            //已结算的单元格数据 用于修改字体颜色
            List<Map<String,Object>> settlementStyleCell = Lists.newArrayList();
            // 表头处理
            List<String> rowHead = CollUtil.newLinkedList("序号", "学生名称","入学年份","来源/代理","申请国家","目标学历","申请大类","收费情况");
            //固定数据部分的数量 用于设置表头第二行 区分动态表头
            int fixedTitleSize = rowHead.size();
            //动态的提成步骤
            rowHead.addAll(title);
            // 第二行表头
            LinkedList<Object> header2 = CollUtil.newLinkedList();
            for (int i = 0; i < fixedTitleSize-1; i++) {
                header2.add("");
            }
            header2.add("服务费");
            header2.add("收款时间");
            for (int i = 0; i < title.size(); i++) {
                header2.add("结算日期");
                header2.add("提成金额");
            }
            // 标题
            writer.merge(header2.size(), settlementStatisticsDto.getStaffName()+"提成表");
            // 写入表头
            writer.writeHeadRow(rowHead);
            writer.writeHeadRow(header2);
            // 表头合并
            for (int i = 0; i < header2.size(); i++) {
                if (i < fixedTitleSize-1) {
                    writer.merge(1, 2, i, i, null, true);
                }
            }

            //表格数据
            List<List<Object>> rows = new LinkedList<>();
            int column = fixedTitleSize-1;
            for (int i = 0; i <rowHead.size(); i++) {
                if (i==fixedTitleSize-1){
                    //合并
                    writer.merge(1, 1, column, column+1, "收费情况", true);
                    column = column+2;
                }else if (i>fixedTitleSize-1){
                    //合并
                    writer.merge(1, 1, column, column+1, title.get(i-fixedTitleSize), true);
                    //修改背景色
                    writeCellBackgroundColor(writer,1,column,title.get(i-fixedTitleSize),IndexedColors.YELLOW.getIndex());
                    column = column+2;
                }
            }
            writer.merge(1, 2, column, column,"合计提成金额", true);
            //修改背景色
            writeCellBackgroundColor(writer,1,column,"合计提成金额",IndexedColors.SKY_BLUE.getIndex());

            int columnOffSet = 9;
            for (int i = 0; i < title.size(); i++) {
                writeCellBackgroundColor(writer,2,i+columnOffSet,"结算日期",IndexedColors.YELLOW.getIndex());
                writeCellBackgroundColor(writer,2,i+columnOffSet+1,"提成金额",IndexedColors.YELLOW.getIndex());
                columnOffSet = columnOffSet+1;
            }



            // 数据整理
            //合计数据  用于修改样式时使用
            List<Map<String,Object>> totalCommissionAmounts = Lists.newArrayList();
            List<StaffSettlementStatisticsItemVo> dataList = settlementStatisticsDto.getStaffSettlementStatisticsItemDtos();
            if (dataList.size() > 0) {
                for (int i = 0; i < dataList.size(); i++) {
                    //合计提成金额
                    Map<String,Object> totalCommissionAmountMap = Maps.newHashMap();

                    StaffSettlementStatisticsItemVo item = dataList.get(i);
                    List<StaffCommissionDatasVo<StaffCommissionStatisticsDateAndAmountVo>> dynamicDatas = item.getDynamicDatas();
                    List<String> settlementAmountList = Lists.newArrayList();
                    List<String> settlementDateList = Lists.newArrayList();
                    List<String> notSettlementAmountList = Lists.newArrayList();
                    List<String> notSettlementDateList = Lists.newArrayList();
                    for (StaffCommissionDatasVo<StaffCommissionStatisticsDateAndAmountVo> dynamicData : dynamicDatas) {
                        String settlementAmount = "";
                        String settlementDate = "";
                        String notSettlementAmount = "";
                        String notSettlementDate = "";
                        StaffCommissionStatisticsDateAndAmountVo value = dynamicData.getValue();
                        List<Map<String, Object>> settlementAmounts = value.getSettlementAmounts();
                        List<Map<String, Object>> settlementDates = value.getSettlementDates();
                        if (GeneralTool.isNotEmpty(settlementAmounts)){
                            for (Map<String, Object> settlementAmountMap : settlementAmounts) {
                                Object amount = settlementAmountMap.get("value");
                                Object type = settlementAmountMap.get("type");
                                if (Integer.parseInt(type.toString())==0){
                                    notSettlementAmount = amount.toString();
                                }else if (Integer.parseInt(type.toString())==1){
                                    settlementAmount = amount.toString();
                                }
                            }
                        }
                        if (GeneralTool.isNotEmpty(settlementDates)){
                            for (Map<String, Object> settlementDateMap : settlementDates) {
                                Object date = settlementDateMap.get("value");
                                Object type = settlementDateMap.get("type");
                                if (Integer.parseInt(type.toString())==0){
                                    notSettlementDate = GeneralTool.isEmpty(date)?"":date.toString();
                                }else if (Integer.parseInt(type.toString())==1){
                                    settlementDate = GeneralTool.isEmpty(date)?"":date.toString();
                                }
                            }
                        }
                        settlementAmountList.add(settlementAmount);
                        settlementDateList.add(settlementDate);
                        notSettlementAmountList.add(notSettlementAmount);
                        notSettlementDateList.add(notSettlementDate);
                    }

                    LinkedList<Object> row = CollUtil.newLinkedList(
                            i + 1
                            , item.getStudentName()
                            , item.getOpeningTimes()
                            , item.getAgentName()
                            , item.getFkAreaCountryNames()
                            , item.getFkInstitutionCourseMajorLevelNames()
                            , item.getFkInstitutionCourseTypeGroupNames()
                            , item.getReceiptAmount()
                            , item.getReceiptDate()
                    );

                    //记录行数

                    int notSettlementRow = rows.size()+3;
                    for (int j = 0; j < notSettlementAmountList.size(); j++) {
                        row.add(notSettlementDateList.get(j));
                        row.add(notSettlementAmountList.get(j));

                        Map<String,Object> notSettlementDateCell = Maps.newHashMap();
                        notSettlementDateCell.put("row",notSettlementRow);
                        notSettlementDateCell.put("column",row.size()-2);
                        notSettlementDateCell.put("value",notSettlementDateList.get(j));

                        Map<String,Object> notSettlementAmountCell = Maps.newHashMap();
                        notSettlementAmountCell.put("row",notSettlementRow);
                        notSettlementAmountCell.put("column",row.size()-1);
                        notSettlementAmountCell.put("value",notSettlementAmountList.get(j));

                        notSettlementStyleCell.add(notSettlementDateCell);
                        notSettlementStyleCell.add(notSettlementAmountCell);
                    }
                    row.add(item.getTotalCommissionAmount());
                    totalCommissionAmountMap.put("row",notSettlementRow);
                    totalCommissionAmountMap.put("column",row.size()-1);
                    totalCommissionAmountMap.put("value",item.getTotalCommissionAmount());
                    totalCommissionAmounts.add(totalCommissionAmountMap);


                    //记录行数
                    int settlementRow = rows.size()+4;
                    LinkedList<Object> row2 = CollUtil.newLinkedList();
                    for (int j = 0; j < row.size(); j++) {
                        if (j<9){
                            row2.add("");
                        }
                    }
                    for (int j = 0; j < settlementAmountList.size(); j++) {
                        row2.add(settlementDateList.get(j));
                        row2.add(settlementAmountList.get(j));

                        Map<String,Object> settlementDateCell = Maps.newHashMap();
                        settlementDateCell.put("row",settlementRow);
                        settlementDateCell.put("column",row2.size()-2);
                        settlementDateCell.put("value",settlementDateList.get(j));

                        Map<String,Object> settlementAmountCell = Maps.newHashMap();
                        settlementAmountCell.put("row",settlementRow);
                        settlementAmountCell.put("column",row2.size()-1);
                        settlementAmountCell.put("value",settlementAmountList.get(j));

                        settlementStyleCell.add(settlementDateCell);
                        settlementStyleCell.add(settlementAmountCell);
                    }
                    row2.add("");

                    rows.add(row);
                    rows.add(row2);
                }
            }
            int rowNum = writer.getCurrentRow();
            writer.write(rows, true);

            //合并单元格
            for (List<Object> row : rows) {
                if (GeneralTool.isNotEmpty(row.get(0))){
                    for (int i = 0; i < row.size(); i++) {
                        if (i<9||i==row.size()-1){
                            writer.merge(rowNum, rowNum+1, i, i, row.get(i), false);
                        }
                    }
                    rowNum = rowNum + 2;
                }
            }

            //总计
            List<Map<String, Object>> totalAmounts = settlementStatisticsDto.getTotalAmount();

            //清空数组
            rows.clear();
            if (GeneralTool.isNotEmpty(totalAmounts)){
                LinkedList<Object> row = CollUtil.newLinkedList();
                for (int i = 0; i < 9; i++) {
                    row.add("");
                }

                for (int i = 0; i < totalAmounts.size(); i++) {
                    if (i!=totalAmounts.size()-1){
                        row.add("");
                    }
                    row.add(totalAmounts.get(i).get("value"));
                }
                rows.add(row);

                writer.write(rows, true);
                writer.merge(writer.getCurrentRow()-1, writer.getCurrentRow()-1, 0, 8,"合计", true);

                for (int i = 0; i < rows.get(0).size(); i++) {
                    if (i==0){
                        writeCellBackgroundColor(writer,writer.getCurrentRow()-1,0,"合计",IndexedColors.SKY_BLUE.getIndex());
                    }
                    if (i>=9){
                        writeCellBackgroundColor(writer,writer.getCurrentRow()-1,i,rows.get(0).get(i).toString(),IndexedColors.SKY_BLUE.getIndex());
                    }
                }
            }


            //结算日期总计
            List<Map<String, Object>> sumSettlementAmounts = settlementStatisticsDto.getSumSettlementAmount();

            //清空数组 防止前面的数据重复导入
            rows.clear();
            if (GeneralTool.isNotEmpty(sumSettlementAmounts)){
                LinkedList<Object> row = CollUtil.newLinkedList();
                for (int i = 0; i < 9; i++) {
                    row.add("");
                }

                for (int i = 0; i < sumSettlementAmounts.size(); i++) {
                    if (i !=sumSettlementAmounts.size()-1){
                        row.add(sumSettlementAmounts.get(i).get("title"));
                    }
                    row.add(sumSettlementAmounts.get(i).get("value"));
                }
                rows.add(row);

                writer.write(rows, true);
                SimpleDateFormat sf = new SimpleDateFormat("yyyy年MM月");
                writer.merge(writer.getCurrentRow()-1, writer.getCurrentRow()-1, 0, 8,sf.format(staffSettlementStatisticsDto.getSettlementDate())+" 结算", true);
                for (int i = 0; i < rows.get(0).size(); i++) {
                    if (i==0){
                        writeCellBackgroundColor(writer,writer.getCurrentRow()-1,0,settlementStatisticsDto.getSumSettlementDate()+" 结算",IndexedColors.YELLOW.getIndex());
                    }
                    if (i>=9){
                        writeCellBackgroundColor(writer,writer.getCurrentRow()-1,i,rows.get(0).get(i).toString(),IndexedColors.YELLOW.getIndex());
                    }
                }

                for (Map<String, Object> totalCommissionAmount : totalCommissionAmounts) {
                    writeCellBackgroundColor(writer,Integer.parseInt(totalCommissionAmount.get("row").toString()),Integer.parseInt(totalCommissionAmount.get("column").toString()),totalCommissionAmount.get("value").toString(),IndexedColors.SKY_BLUE.getIndex());
                }
            }

            //设置内容字体
            Font allFont = writer.createFont();
            allFont.setFontHeightInPoints((short) 10);
            //第二个参数表示是否忽略头部样式
            writer.getStyleSet().setFont(allFont, true);
            // 自动换行
            writer.getStyleSet().setWrapText();
            CellStyle headCellStyle = writer.getHeadCellStyle();
            //设置表头自动换行
            headCellStyle.setWrapText(true);
            // 设置头部样式
            Cell cell = writer.getCell(0, 0);
            CellStyle cellStyle = cell.getCellStyle();
            Font font = writer.createFont();
            font.setFontHeightInPoints((short) 10);
            font.setBold(true);
            cellStyle.setFont(font);
            cell.setCellStyle(cellStyle);

            //设置未结算字体
            if (GeneralTool.isNotEmpty(notSettlementStyleCell)){
                for (Map<String, Object> map : notSettlementStyleCell) {
                    writeCell(writer,Integer.parseInt(map.get("row").toString()),Integer.parseInt(map.get("column").toString()),map.get("value").toString(),0);
                }
            }
            //设置结算字体
            if (GeneralTool.isNotEmpty(settlementStyleCell)) {
                for (Map<String, Object> map : settlementStyleCell) {
                    writeCell(writer,Integer.parseInt(map.get("row").toString()),Integer.parseInt(map.get("column").toString()),map.get("value").toString(),1);
                }
            }

            writeTitleCell(writer,0,0,settlementStatisticsDto.getStaffName()+"提成表");
            //out为OutputStream，需要写出到的目标流
            writer.flush(out);
            // 关闭writer，释放内存
            writer.close();
        } catch (Exception e) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("export_data_error"));
        }
    }

    @Override
    public List<StaffCommissionRefundVo> getStaffSettlementRefundList(StaffCommissionRefundDto staffCommissionRefundDto, Page page) throws ParseException {

        if (GeneralTool.isEmpty(staffCommissionRefundDto)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        Long staffId = SecureUtil.getStaffId();
        List<Long> staffFollowerIds = new ArrayList<>();
        //员工id + 业务下属员工ids
        if (GeneralTool.isNotEmpty(permissionCenterClient.getStaffFollowerIds(staffId).getData())) {
            staffFollowerIds = permissionCenterClient.getStaffFollowerIds(staffId).getData().stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        }
        //设置查询参数
        setQueryParameters(staffCommissionRefundDto);

        List<StaffCommissionRefundVo> staffSettlementRefundList = new ArrayList<>();
        Boolean isBd = studentOfferService.getIsBd(staffId);
        if (page == null){
            staffSettlementRefundList = staffCommissionActionMapper.getStaffSettlementRefundList(null, staffCommissionRefundDto,staffFollowerIds,SecureUtil.getStaffInfoByStaffId(staffId).getIsStudentAdmin(),SecureUtil.getCountryIds(),isBd, SecureUtil.getPermissionGroupInstitutionIds(), SecureUtil.getStaffBoundBdIds());
        }else {
            IPage<StaffCommissionRefundVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
            staffSettlementRefundList = staffCommissionActionMapper.getStaffSettlementRefundList(iPage, staffCommissionRefundDto,staffFollowerIds,SecureUtil.getStaffInfoByStaffId(staffId).getIsStudentAdmin(),SecureUtil.getCountryIds(),isBd, SecureUtil.getPermissionGroupInstitutionIds(), SecureUtil.getStaffBoundBdIds());
            page.setAll((int) iPage.getTotal());
        }

        if (GeneralTool.isEmpty(staffSettlementRefundList)){
            return Collections.emptyList();
        }
        Set<Long> courseIds = staffSettlementRefundList.stream().map(StaffCommissionRefundVo::getFkInstitutionCourseId).collect(Collectors.toSet());
        Map<Long, String> courseNameMap = institutionCenterClient.getCourseNameByIds(courseIds).getData();

        Set<Long> channelIds = staffSettlementRefundList.stream().map(StaffCommissionRefundVo::getFkInstitutionChannelId).collect(Collectors.toSet());
        Map<Long, String> channelNameMap = institutionCenterClient.getInstitutionProviderChannelByIds(channelIds).getData();

        Set<Long> providerIds = staffSettlementRefundList.stream().map(StaffCommissionRefundVo::getFkInstitutionProviderId).collect(Collectors.toSet());
        Map<Long, String> providerNameMap = institutionCenterClient.getInstitutionProviderNamesByIds(providerIds).getData();

        List<BaseSelectEntity> itemStepSelect = studentOfferItemStepService.getItemStepSelect();
        Map<Long, String> itemStepSelectMap = itemStepSelect.stream().collect(Collectors.toMap(BaseSelectEntity::getId, BaseSelectEntity::getName));

        List<BaseSelectEntity> cancelOfferReasonSelect = staffCommissionStepService.getCancelOfferReasonSelect(0L);
        Map<String, String> cancelOfferReasonSelectMap = cancelOfferReasonSelect.stream().collect(Collectors.toMap(BaseSelectEntity::getNum, BaseSelectEntity::getName));
        Set<Long> contryIds = staffSettlementRefundList.stream().map(StaffCommissionRefundVo::getFkAreaCountryId).collect(Collectors.toSet());
        Map<Long, String> contryNameMap = institutionCenterClient.getCountryNamesByIds(contryIds).getData();
        Set<Long> staffIds = staffSettlementRefundList.stream().map(StaffCommissionRefundVo::getFkStaffIdRefundReview).collect(Collectors.toSet());
        Map<Long, String> staffNameMap = permissionCenterClient.getStaffNamesByIds(staffIds);

        //获取入学失败时间

        Set<Long> failureIds = staffSettlementRefundList.stream().filter(e -> e.getFkStudentOfferItemStepId() == 9).map(StaffCommissionRefundVo::getFkStudentOfferItemId).collect(Collectors.toSet());
        Map<Long, Date> failureDateMap = rStudentOfferItemStepService.failureIds(failureIds);

        setStaffSettlementRefundName(staffSettlementRefundList,courseNameMap,channelNameMap,providerNameMap,itemStepSelectMap,cancelOfferReasonSelectMap,contryNameMap,staffNameMap,failureDateMap);

        return staffSettlementRefundList;
    }

    @Override
    public List<BaseSelectEntity> getSettlementDateSelect(Long companyId) {
        return staffCommissionActionMapper.getSettlementDateSelect(companyId);
    }

    @Override
    public void batchRefundReview(StaffCommissionBatchRefundDto staffCommissionRefundVo) {
        if (GeneralTool.isEmpty(staffCommissionRefundVo)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }

        StaffCommissionAction staffCommissionAction = new StaffCommissionAction();
        staffCommissionAction.setRefundReviewContent(staffCommissionRefundVo.getRefundReviewContent());
        staffCommissionAction.setRefundReviewStatus(staffCommissionRefundVo.getRefundReviewStatus());
        if (staffCommissionRefundVo.getRefundReviewStatus() == 1){
            staffCommissionAction.setRefundSettlementStatus(1);
            staffCommissionAction.setFkStaffIdRefundSettlement(SecureUtil.getStaffId());
            staffCommissionAction.setRefundSettlementTime(new Date());
        }
        staffCommissionAction.setFkSettlementRefundReasonId(staffCommissionRefundVo.getFkSettlementRefundReasonId());
        staffCommissionAction.setRefundReviewTime(new Date());
        //staffCommissionAction.setSettlementDate(DateUtil.format(new Date(),"yyyyMM"));
        staffCommissionAction.setFkStaffIdRefundReview(SecureUtil.getStaffId());
        utilService.setUpdateInfo(staffCommissionAction);
        LambdaUpdateWrapper<StaffCommissionAction> staffCommissionActionLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        staffCommissionActionLambdaUpdateWrapper.in(StaffCommissionAction::getId,staffCommissionRefundVo.getIds());
//        staffCommissionActionLambdaUpdateWrapper.ne(StaffCommissionAction::getRefundSettlementStatus,2);
        staffCommissionActionLambdaUpdateWrapper.and(wrapper -> wrapper.isNull(StaffCommissionAction::getRefundSettlementStatus).or().ne(StaffCommissionAction::getRefundSettlementStatus,2));

        if (staffCommissionRefundVo.getRefundReviewStatus() == 2) {
            staffCommissionActionLambdaUpdateWrapper.set(StaffCommissionAction::getRefundSettlementStatus,null).set(StaffCommissionAction::getFkStaffIdRefundSettlement,null).set(StaffCommissionAction::getRefundSettlementTime, null);
        }
        staffCommissionActionMapper.update(staffCommissionAction,staffCommissionActionLambdaUpdateWrapper);

    }

    @Override
    public void batchRefundSettlement(StaffCommissionBatchRefundDto staffCommissionRefundVo) {
        if (GeneralTool.isEmpty(staffCommissionRefundVo.getIds())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        StaffCommissionAction staffCommissionAction = new StaffCommissionAction();
        staffCommissionAction.setRefundSettlementStatus(2);
        staffCommissionAction.setRefundSettlementTime(new Date());
        staffCommissionAction.setFkStaffIdRefundSettlement(SecureUtil.getStaffId());
        utilService.setUpdateInfo(staffCommissionAction);
        staffCommissionActionMapper.update(staffCommissionAction,new LambdaUpdateWrapper<StaffCommissionAction>().in(StaffCommissionAction::getId,staffCommissionRefundVo.getIds()).eq(StaffCommissionAction::getRefundReviewStatus,2));
    }

    @Override
    public void exportStaffSettlementRefundExcel(HttpServletResponse response, StaffCommissionRefundDto staffCommissionRefundDto) throws ParseException {
        List<StaffCommissionRefundVo> staffSettlementRefundList = getStaffSettlementRefundList(staffCommissionRefundDto, null);
        ArrayList<StaffCommissionRefundExportVo> staffCommissionRefundExportVos = new ArrayList<>();
        for (StaffCommissionRefundVo staffCommissionRefundVo : staffSettlementRefundList) {
            StaffCommissionRefundExportVo staffCommissionRefundExportVo = BeanCopyUtils.objClone(staffCommissionRefundVo, StaffCommissionRefundExportVo::new);
            StringBuilder refundSettlementTime = new StringBuilder();
            StringBuilder refundReviewTime = new StringBuilder();
            if (GeneralTool.isNotEmpty(staffCommissionRefundVo.getRefundReviewTime())){
                refundReviewTime.append("("+DateUtil.format(staffCommissionRefundVo.getRefundReviewTime(), "yyyy-MM-dd")+")");
            }
            if (GeneralTool.isEmpty(staffCommissionRefundVo.getRefundReviewStatus()) || staffCommissionRefundVo.getRefundReviewStatus()==0){
                staffCommissionRefundExportVo.setRefundReviewStatusName("未审核");
            }else if (staffCommissionRefundVo.getRefundReviewStatus()==1){
                staffCommissionRefundExportVo.setRefundReviewStatusName("无需退款"+"("+refundReviewTime+")");
            } else if (staffCommissionRefundVo.getRefundReviewStatus()==2) {
                staffCommissionRefundExportVo.setRefundReviewStatusName("确认退款"+"("+refundReviewTime+")");
            }

            if (GeneralTool.isNotEmpty(staffCommissionRefundVo.getRefundSettlementTime())){
                refundSettlementTime.append("("+DateUtil.format(staffCommissionRefundVo.getRefundSettlementTime(), "yyyy-MM-dd")+")");
            }
            if (GeneralTool.isEmpty(staffCommissionRefundVo.getRefundSettlementStatus()) || staffCommissionRefundVo.getRefundSettlementStatus() == 0){
                staffCommissionRefundExportVo.setRefundSettlementStatusName("未结算");
            } else if (staffCommissionRefundVo.getRefundSettlementStatus() == 1) {
                staffCommissionRefundExportVo.setRefundSettlementStatusName("不结算"+refundSettlementTime);
            } else if (staffCommissionRefundVo.getRefundSettlementStatus() == 2) {
                staffCommissionRefundExportVo.setRefundSettlementStatusName("已结算"+refundSettlementTime);
            }

            if (GeneralTool.isNotEmpty(staffCommissionRefundVo.getItemStatus())){
                staffCommissionRefundExportVo.setItemStatusName(staffCommissionRefundVo.getItemStatus() ==1?"有效":"作废");
            }
            if (GeneralTool.isNotEmpty(staffCommissionRefundVo.getDeferOpeningTime())){
                staffCommissionRefundExportVo.setDeferOpeningTime(staffCommissionRefundVo.getDeferOpeningTime());
            }
            staffCommissionRefundExportVos.add(staffCommissionRefundExportVo);
        }
        FileUtils.exportExcelNotWrapText(response, staffCommissionRefundExportVos, "StaffCommissionRefund", StaffCommissionRefundExportVo.class);
    }

    /**
     * Author Cream
     * Description : //合并学生业绩结算数据
     * Date 2023/5/11 11:50
     * Params:
     * Return
     */
    @Override
    public void mergeData(Long mergedStudentId, Long targetStudentId) {
        List<StaffCommissionAction> staffCommissionActions = staffCommissionActionMapper.selectList(Wrappers.<StaffCommissionAction>lambdaQuery().eq(StaffCommissionAction::getFkStudentId, mergedStudentId));
        if (GeneralTool.isNotEmpty(staffCommissionActions)) {
            staffCommissionActions.forEach(s->s.setFkStudentId(targetStudentId));
            updateBatchById(staffCommissionActions);
        }
    }

    @Override
    public StaffCommissionRefundEchoVo refundSettlementConditionalEcho(StaffCommissionRefundDto staffCommissionRefundDto) {
        if (GeneralTool.isEmpty(staffCommissionRefundDto)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }

        StaffCommissionRefundEchoVo staffCommissionRefundEchoVo = BeanCopyUtils.objClone(staffCommissionRefundDto, StaffCommissionRefundEchoVo::new);
        //只生成确认退款状态
        if (GeneralTool.isNotEmpty(staffCommissionRefundDto.getRefundReviewStatus())){
            if (staffCommissionRefundDto.getRefundReviewStatus()==0){
                staffCommissionRefundEchoVo.setRefundReviewStatusName("未审核");
            }else if (staffCommissionRefundDto.getRefundReviewStatus()==1){
                staffCommissionRefundEchoVo.setRefundReviewStatusName("无需退款");
            } else if (staffCommissionRefundDto.getRefundReviewStatus()==2) {
                staffCommissionRefundEchoVo.setRefundReviewStatusName("确认退款");
            }
        }

        if (GeneralTool.isNotEmpty(staffCommissionRefundDto.getRefundSettlementStatus())){
            if (staffCommissionRefundDto.getRefundSettlementStatus() == 0){
                staffCommissionRefundEchoVo.setRefundSettlementStatusName("未结算");
            } else if (staffCommissionRefundDto.getRefundSettlementStatus() == 1) {
                staffCommissionRefundEchoVo.setRefundSettlementStatusName("不结算");
            } else if (staffCommissionRefundDto.getRefundSettlementStatus() == 2) {
                staffCommissionRefundEchoVo.setRefundSettlementStatusName("已结算");
            }
        }
        StringBuilder companyName = new StringBuilder();
        Map<Long, String> companyNameMap = permissionCenterClient.getCompanyNamesByIds(new HashSet<>(staffCommissionRefundDto.getFkCompanyIds())).getData();
        if (GeneralTool.isNotEmpty(companyNameMap)){
            companyNameMap.forEach((k,v)->{
                companyName.append(v).append(",");
            });
            companyName.deleteCharAt(companyName.length()-1);
        }
        staffCommissionRefundEchoVo.setFkCompanyName(companyName.toString());
        return staffCommissionRefundEchoVo;
    }

    @Override
    public StaffCommissionRefundSummaryVo getStaffSettlementRefundSummaryList(StaffCommissionRefundDto staffCommissionRefundDto) {
        if (GeneralTool.isEmpty(staffCommissionRefundDto)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        Long staffId = SecureUtil.getStaffId();
        List<Long> staffFollowerIds = new ArrayList<>();
        //员工id + 业务下属员工ids
        if (GeneralTool.isNotEmpty(permissionCenterClient.getStaffFollowerIds(staffId).getData())) {
            staffFollowerIds = permissionCenterClient.getStaffFollowerIds(staffId).getData().stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        }
        //设置查询参数
        Boolean isBd = studentOfferService.getIsBd(staffId);
        setQueryParameters(staffCommissionRefundDto);
        List<StaffCommissionRefundDetailVo> list = staffCommissionActionMapper.getStaffSettlementRefundSummaryList(staffCommissionRefundDto,staffFollowerIds,SecureUtil.getStaffInfoByStaffId(staffId).getIsStudentAdmin(),SecureUtil.getCountryIds(),isBd, SecureUtil.getPermissionGroupInstitutionIds(), SecureUtil.getStaffBoundBdIds());

        if (GeneralTool.isEmpty(list)){
            return null;
        }

        Set<Long> staffIds = list.stream().map(StaffCommissionRefundDetailVo::getFkStaffId).collect(Collectors.toSet());
        Map<Long, String> staffNameMap = permissionCenterClient.getStaffNameDepartMentMap(staffIds).getData();
        Map<Long, String> staffNameMapNew = permissionCenterClient.getStaffNameMap(staffIds).getData();

        StaffCommissionRefundSummaryVo staffCommissionRefundSummaryVo = setStaffSettlementRefundSummaryListName(list, staffNameMap,staffNameMapNew);
        staffCommissionRefundSummaryVo.setRefundSummaryCreate(new Date());
        return staffCommissionRefundSummaryVo;
    }

    private void setQueryParameters(StaffCommissionRefundDto staffCommissionRefundDto) {
        //studentName
        if (GeneralTool.isNotEmpty(staffCommissionRefundDto.getStudentName())) {
            staffCommissionRefundDto.setStudentName(staffCommissionRefundDto.getStudentName().toLowerCase());
        }
        //staffName
        if (GeneralTool.isNotEmpty(staffCommissionRefundDto.getStaffName())) {
            staffCommissionRefundDto.setStaffName(staffCommissionRefundDto.getStaffName().toLowerCase());
        }
        //institutionName
        if (GeneralTool.isNotEmpty(staffCommissionRefundDto.getInstitutionName())) {
            staffCommissionRefundDto.setInstitutionName(staffCommissionRefundDto.getInstitutionName().toLowerCase());
        }
        //agentName
        if (GeneralTool.isNotEmpty(staffCommissionRefundDto.getAgentName())){
            staffCommissionRefundDto.setAgentName(staffCommissionRefundDto.getAgentName().toLowerCase());
        }
    }

    @Override
    public void downStaffSettlementRefundSummaryList(StaffCommissionRefundDto staffCommissionRefundDto, HttpServletResponse response) {
        //条件
        StaffCommissionRefundEchoVo staffCommissionRefundEchoVo = refundSettlementConditionalEcho(staffCommissionRefundDto);
        StaffCommissionRefundSummaryVo staffSettlementRefundSummaryList = getStaffSettlementRefundSummaryList(staffCommissionRefundDto);

        try {
            ArrayList<List<String>> rows = new ArrayList<>();
            BigExcelWriter writer = FileUtils.setExcelStyleNotWrapText("applicationStatistics", rows.size());
            //设置表头
            setHead(staffCommissionRefundEchoVo,rows, writer);
            //写入数据
            setData(staffSettlementRefundSummaryList,rows,writer);
            writer.write(rows, true);
            // 设置头部样式
            CellStyle c1 = getLeft(writer, (short) 14, true);
            Cell cell = writer.getCell(0, 0);
            cell.setCellStyle(c1);
            FileUtils.doExportExcel(response, writer, "SettlementRefundSummary");
        }catch (Exception e){
            throw new GetServiceException(LocaleMessageUtils.getMessage("export_data_error"));
        }

    }

    @Override
    public StaffCommissionRefundRoleVo getStaffSettlementRefundRoleSumarry(StaffCommissionRefundDto staffCommissionRefundDto) throws ParseException {
        StaffCommissionRefundRoleVo staffCommissionRefundRoleVo = new StaffCommissionRefundRoleVo();
        List<StaffCommissionRefundVo> staffSettlementRefundList = getStaffSettlementRefundList(staffCommissionRefundDto, null);

        if (GeneralTool.isNotEmpty(staffSettlementRefundList)) {
            Set<Long> studentIds = staffSettlementRefundList.stream()
                    .filter(e -> e.getFkStaffCommissionStepKey().equals("STEP_OFFER_SELECTION"))
                    .map(StaffCommissionRefundVo::getFkStudentId)
                    .collect(Collectors.toSet());

            staffCommissionRefundRoleVo.setStudentTotal(studentIds.size());
            staffCommissionRefundRoleVo.setUnOsStudentTotal(staffSettlementRefundList.stream()
                    .map(StaffCommissionRefundVo::getFkStudentId)
                    .collect(Collectors.toSet())
                    .size() - studentIds.size());

            List<StaffCommissionRefundVo> stepOfferSelection1 = staffSettlementRefundList.stream()
                    .filter(e -> e.getFkStaffCommissionStepKey().equals("STEP_OFFER_SELECTION"))
                    .collect(Collectors.toList());

            List<StaffCommissionRefundVo> stepOfferSelection2 = staffSettlementRefundList.stream()
                    .filter(e -> !e.getFkStaffCommissionStepKey().equals("STEP_OFFER_SELECTION"))
                    .collect(Collectors.toList());

            List<StaffCommissionRefundVo> allRefunds = staffSettlementRefundList;

            List<StaffCommissionRefundRoleVo.RoleAmountDto> roleAmountDtos = new ArrayList<>();

            // 【OS退费】
            StaffCommissionRefundRoleVo.RoleAmountDto roleAmount1 = createRoleAmountDto("【OS退费】", stepOfferSelection1);
            roleAmountDtos.add(roleAmount1);

            // 【非OS退费】
            StaffCommissionRefundRoleVo.RoleAmountDto roleAmount2 = createRoleAmountDto("【非OS退费】", stepOfferSelection2);
            roleAmountDtos.add(roleAmount2);

            // 【总退费】
            StaffCommissionRefundRoleVo.RoleAmountDto roleAmount3 = createRoleAmountDto("【总退费】", allRefunds);
            roleAmountDtos.add(roleAmount3);

            staffCommissionRefundRoleVo.setRoleAmountList(roleAmountDtos);
        }

        return staffCommissionRefundRoleVo;
    }

    private StaffCommissionRefundRoleVo.RoleAmountDto createRoleAmountDto(String roleName, List<StaffCommissionRefundVo> refunds) {
        BigDecimal confirmRefundAmount = calculateRefundAmount(refunds, 2);
        BigDecimal noRefundRequired = calculateRefundAmount(refunds, 1);
        BigDecimal unapprovedAmount = calculateRefundAmount(refunds, 0);

        StaffCommissionRefundRoleVo.RoleAmountDto roleAmount = new StaffCommissionRefundRoleVo.RoleAmountDto();
        roleAmount.setRoleName(roleName);
        roleAmount.setConfirmRefundAmount(confirmRefundAmount);
        roleAmount.setNoRefundRequired(noRefundRequired);
        roleAmount.setUnapprovedAmount(unapprovedAmount);

        return roleAmount;
    }

    private BigDecimal calculateRefundAmount(List<StaffCommissionRefundVo> refunds, int refundReviewStatus) {
        return refunds.stream()
                .filter(e -> GeneralTool.isNotEmpty(e.getRefundReviewStatus()) && e.getRefundReviewStatus() == refundReviewStatus)
                .map(StaffCommissionRefundVo::getCommissionAmount)
                .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO);
    }

    private void setData(StaffCommissionRefundSummaryVo staffSettlementRefundSummaryList, ArrayList<List<String>> rows, BigExcelWriter writer) {

        // 数据整理
        List<StaffCommissionRefundDetailVo> staffCommissionRefundList = staffSettlementRefundSummaryList.getStaffCommissionRefundList();
        StaffCommissionRefundSummaryVo.StaffCommissionRefundTotal staffCommissionRefundSummary = staffSettlementRefundSummaryList.getStaffCommissionRefundSummary();
        for (int i = 0; i < staffCommissionRefundList.size(); i++) {
            LinkedList<String> row = CollUtil.newLinkedList();
        }
        for (StaffCommissionRefundDetailVo staffCommissionRefundDetailVo : staffCommissionRefundList) {
            LinkedList<String> row = CollUtil.newLinkedList(staffCommissionRefundDetailVo.getPosition());
            row.add(staffCommissionRefundDetailVo.getFkStaffName());
            row.add(staffCommissionRefundDetailVo.getOsUnSettlementQuantity() + "/"+ staffCommissionRefundDetailVo.getOsSettlementQuantity());
            row.add(staffCommissionRefundDetailVo.getVgUnSettlementQuantity()+"/"+ staffCommissionRefundDetailVo.getVgSettlementQuantity());
            row.add(staffCommissionRefundDetailVo.getSeUnSettlementQuantity()+"/"+ staffCommissionRefundDetailVo.getSeSettlementQuantity());
            row.add(staffCommissionRefundDetailVo.getVgbUnSettlementQuantity()+"/"+ staffCommissionRefundDetailVo.getVgbSettlementQuantity());
            row.add(staffCommissionRefundDetailVo.getUnCommissionAmount()+"/"+ staffCommissionRefundDetailVo.getCommissionAmount());
            rows.add(row);
        }
        LinkedList<String> rowEnd = CollUtil.newLinkedList(staffCommissionRefundSummary.getPosition());
        rowEnd.add(staffCommissionRefundSummary.getFkStaffName());
        rowEnd.add(staffCommissionRefundSummary.getOsUnSettlementAmount() + "/"+ staffCommissionRefundSummary.getOsSettlementAmount());
        rowEnd.add(staffCommissionRefundSummary.getVgUnSettlementAmount()+"/"+staffCommissionRefundSummary.getVgSettlementAmount());
        rowEnd.add(staffCommissionRefundSummary.getSeUnSettlementAmount()+"/"+staffCommissionRefundSummary.getSeSettlementAmount());
        rowEnd.add(staffCommissionRefundSummary.getVgbUnSettlementAmount()+"/"+staffCommissionRefundSummary.getVgbSettlementAmount());
        rowEnd.add(staffCommissionRefundSummary.getUnCommissionAmount()+"/"+staffCommissionRefundSummary.getCommissionAmount());
        rows.add(rowEnd);

    }

    private CellStyle getLeft(BigExcelWriter workbook, short fontSize, boolean isBold) {
        CellStyle cellStyle = workbook.getCellStyle();
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        Font font = workbook.createFont();
        font.setFontHeightInPoints(fontSize);
        font.setFontName("宋体");
        //字号
        font.setBold(isBold);
        //加粗
        cellStyle.setFont(font);
        workbook.setColumnWidth(0, 50);
        for (int i = 1; i < 7; i++) {
            workbook.setColumnWidth(i, 25);
        }

        return cellStyle;
    }

    private void setHead(StaffCommissionRefundEchoVo staffCommissionRefundEchoVo, List<List<String>> rows, BigExcelWriter writer) {
        List<String> title = new ArrayList<>();
        title.add("项目成员业绩结算退款统计表");
        rows.add(title);
        List<String> filterCriteria = new ArrayList<>();
        filterCriteria.add("筛选条件：");
        rows.add(filterCriteria);
        List<String> row3 = new ArrayList<>();
        row3.add("【所属分公司】 "+ staffCommissionRefundEchoVo.getFkCompanyName());
        rows.add(row3);
        if (GeneralTool.isNotEmpty(staffCommissionRefundEchoVo.getCreateEndTime()) || GeneralTool.isNotEmpty(staffCommissionRefundEchoVo.getCreateBeginTime())){
            List<String> row4 = new ArrayList<>();
            StringBuilder time = new StringBuilder();
            if (GeneralTool.isNotEmpty(staffCommissionRefundEchoVo.getCreateBeginTime())){
                time.append(DateUtil.format(staffCommissionRefundEchoVo.getCreateBeginTime(), "yyyy-MM-dd"));
            }
            time.append("到");
            if (GeneralTool.isNotEmpty(staffCommissionRefundEchoVo.getCreateEndTime())){
                time.append(DateUtil.format(staffCommissionRefundEchoVo.getCreateEndTime(), "yyyy-MM-dd"));
            }
            row4.add("【退款结算操作时间】"+time.toString());
            rows.add(row4);
        }

        if (GeneralTool.isNotEmpty(staffCommissionRefundEchoVo.getRefundReviewBeginTime()) || GeneralTool.isNotEmpty(staffCommissionRefundEchoVo.getRefundReviewEndTime())){
            List<String> row5 = new ArrayList<>();
            StringBuilder time = new StringBuilder();
            if (GeneralTool.isNotEmpty(staffCommissionRefundEchoVo.getRefundReviewBeginTime())){
                time.append(DateUtil.format(staffCommissionRefundEchoVo.getRefundReviewBeginTime(), "yyyy-MM-dd"));
            }
            time.append("到");
            if (GeneralTool.isNotEmpty(staffCommissionRefundEchoVo.getRefundReviewEndTime())){
                time.append(DateUtil.format(staffCommissionRefundEchoVo.getRefundReviewEndTime(), "yyyy-MM-dd"));
            }
            row5.add("【退款审核操作时间】"+time.toString());
            rows.add(row5);
        }

        if (GeneralTool.isNotEmpty(staffCommissionRefundEchoVo.getStepFailureBeginTime()) || GeneralTool.isNotEmpty(staffCommissionRefundEchoVo.getStepFailureEndTime())){
            List<String> row6 = new ArrayList<>();
            StringBuilder time = new StringBuilder();
            if (GeneralTool.isNotEmpty(staffCommissionRefundEchoVo.getStepFailureBeginTime())){
                time.append(DateUtil.format(staffCommissionRefundEchoVo.getStepFailureBeginTime(), "yyyy-MM-dd"));
            }
            time.append("到");
            if (GeneralTool.isNotEmpty(staffCommissionRefundEchoVo.getStepFailureEndTime())){
                time.append(DateUtil.format(staffCommissionRefundEchoVo.getStepFailureEndTime(), "yyyy-MM-dd"));
            }
            row6.add("【入学失败操作时间】"+time.toString());
            rows.add(row6);
        }

        if (GeneralTool.isNotEmpty(staffCommissionRefundEchoVo.getSettlementDate())){

            List<String> row7 = new ArrayList<>();
            row7.add("【奖金结算日期】"+ staffCommissionRefundEchoVo.getSettlementDate());
            rows.add(row7);
        }
        if (GeneralTool.isNotEmpty(staffCommissionRefundEchoVo.getRefundReviewStatusName())){
            List<String> row8 = new ArrayList<>();
            row8.add("【退款审核状态(确认退款)】"+ staffCommissionRefundEchoVo.getRefundReviewStatusName());
            rows.add(row8);
        }

        if (GeneralTool.isNotEmpty(staffCommissionRefundEchoVo.getRefundSettlementStatusName())){
            List<String> row9 = new ArrayList<>();
            row9.add("【退款结算状态】"+ staffCommissionRefundEchoVo.getRefundSettlementStatusName());
            rows.add(row9);
        }
        if (GeneralTool.isNotEmpty(staffCommissionRefundEchoVo.getStaffName())){
            List<String> row10 = new ArrayList<>();
            row10.add("【结算角色/成员名称】"+ staffCommissionRefundEchoVo.getStaffName());
            rows.add(row10);
        }
        if (GeneralTool.isNotEmpty(staffCommissionRefundEchoVo.getAgentName())){
            List<String> row11 = new ArrayList<>();
            row11.add("【代理名称】"+ staffCommissionRefundEchoVo.getAgentName());
            rows.add(row11);
        }
        if (GeneralTool.isNotEmpty(staffCommissionRefundEchoVo.getStudentName())){
            List<String> row12 = new ArrayList<>();
            row12.add("【学生名称】"+ staffCommissionRefundEchoVo.getStudentName());
            rows.add(row12);
        }

        if (GeneralTool.isNotEmpty(staffCommissionRefundEchoVo.getInstitutionName())){
            List<String> row13 = new ArrayList<>();
            row13.add("【学校名称】"+ staffCommissionRefundEchoVo.getInstitutionName());
            rows.add(row13);
        }
        List<String> row14 = new ArrayList<>();
        row14.add("");
        rows.add(row14);

        int lastRowNum = rows.size();
        List<String> rowHead = CollUtil.newLinkedList("岗位","姓名","退款（未结算/已结算）");
        rows.add(rowHead);
        // 第二行表头
        LinkedList<String> rowHead2 = CollUtil.newLinkedList();
        rowHead2.add("");
        rowHead2.add("");
        rowHead2.add("OS数");
        rowHead2.add("签证数");
        rowHead2.add("入学数");
        rowHead2.add("后补签证数");
        rowHead2.add("结算金额");
        rows.add(rowHead2);
        writer.merge(lastRowNum, lastRowNum+1, 0, 0, null, false);
        writer.merge(lastRowNum, lastRowNum+1, 1, 1, null, false);
        writer.merge(lastRowNum, lastRowNum, 2, 6, null, false);
    }

    private StaffCommissionRefundSummaryVo setStaffSettlementRefundSummaryListName(List<StaffCommissionRefundDetailVo> list, Map<Long, String> staffNameMap, Map<Long, String> staffNameMapNew) {

        //总计
        BigDecimal commissionAmount = BigDecimal.ZERO;
        BigDecimal unCommissionAmount = BigDecimal.ZERO;
        Integer osSettlementQuantity = new Integer(0);
        Integer osUnSettlementQuantity = new Integer(0);
        Integer vgSettlementQuantity = new Integer(0);
        Integer vgUnSettlementQuantity = new Integer(0);
        Integer seSettlementQuantity = new Integer(0);
        Integer seUnSettlementQuantity = new Integer(0);
        Integer vgbSettlementQuantity = new Integer(0);
        Integer vgbUnSettlementQuantity = new Integer(0);
        //结算总金额
        BigDecimal commissionAmountTotal = BigDecimal.ZERO;
        BigDecimal unCommissionAmountTotal = BigDecimal.ZERO;
        BigDecimal osSettlementAmountTotal = BigDecimal.ZERO;
        BigDecimal osUnSettlementAmountTotal = BigDecimal.ZERO;
        BigDecimal vgSettlementAmountTotal = BigDecimal.ZERO;
        BigDecimal vgUnSettlementAmountTotal = BigDecimal.ZERO;
        BigDecimal seSettlementAmountTotal = BigDecimal.ZERO;
        BigDecimal seUnSettlementAmountTotal = BigDecimal.ZERO;
        BigDecimal vgbSettlementAmountTotal = BigDecimal.ZERO;
        BigDecimal vgbUnSettlementAmountTotal = BigDecimal.ZERO;
        for (StaffCommissionRefundDetailVo staffCommissionRefundDetailVo : list) {
            if (GeneralTool.isNotEmpty(staffNameMap) && GeneralTool.isNotEmpty(staffNameMap.get(staffCommissionRefundDetailVo.getFkStaffId()))){
                staffCommissionRefundDetailVo.setPosition(staffCommissionRefundDetailVo.getPosition()+"/"+staffNameMap.get(staffCommissionRefundDetailVo.getFkStaffId()));
            }
            if (GeneralTool.isNotEmpty(staffNameMapNew) && GeneralTool.isNotEmpty(staffNameMapNew.get(staffCommissionRefundDetailVo.getFkStaffId()))){
                staffCommissionRefundDetailVo.setFkStaffName(staffNameMapNew.get(staffCommissionRefundDetailVo.getFkStaffId()));
            }
            //总计
            commissionAmount = commissionAmount.add(staffCommissionRefundDetailVo.getCommissionAmount());
            unCommissionAmount = unCommissionAmount.add(staffCommissionRefundDetailVo.getUnCommissionAmount());
            osSettlementQuantity += staffCommissionRefundDetailVo.getOsSettlementQuantity();
            osUnSettlementQuantity += staffCommissionRefundDetailVo.getOsUnSettlementQuantity();
            vgSettlementQuantity += staffCommissionRefundDetailVo.getVgSettlementQuantity();
            vgUnSettlementQuantity += staffCommissionRefundDetailVo.getVgUnSettlementQuantity();
            seSettlementQuantity += staffCommissionRefundDetailVo.getSeSettlementQuantity();
            seUnSettlementQuantity += staffCommissionRefundDetailVo.getSeUnSettlementQuantity();
            vgbSettlementQuantity += staffCommissionRefundDetailVo.getVgbSettlementQuantity();
            vgbUnSettlementQuantity += staffCommissionRefundDetailVo.getVgbUnSettlementQuantity();
            //结算总金额
            commissionAmountTotal = commissionAmountTotal.add(staffCommissionRefundDetailVo.getCommissionAmount());
            unCommissionAmountTotal = unCommissionAmountTotal.add(staffCommissionRefundDetailVo.getUnCommissionAmount());
            osSettlementAmountTotal = osSettlementAmountTotal.add(staffCommissionRefundDetailVo.getOsSettlementAmount());
            osUnSettlementAmountTotal = osUnSettlementAmountTotal.add(staffCommissionRefundDetailVo.getOsUnSettlementAmount());
            vgSettlementAmountTotal = vgSettlementAmountTotal.add(staffCommissionRefundDetailVo.getVgSettlementAmount());
            vgUnSettlementAmountTotal = vgUnSettlementAmountTotal.add(staffCommissionRefundDetailVo.getVgUnSettlementAmount());
            seSettlementAmountTotal = seSettlementAmountTotal.add(staffCommissionRefundDetailVo.getSeSettlementAmount());
            seUnSettlementAmountTotal = seUnSettlementAmountTotal.add(staffCommissionRefundDetailVo.getSeUnSettlementAmount());
            vgbSettlementAmountTotal = vgbSettlementAmountTotal.add(staffCommissionRefundDetailVo.getVgbSettlementAmount());
            vgbUnSettlementAmountTotal = vgbUnSettlementAmountTotal.add(staffCommissionRefundDetailVo.getVgbUnSettlementAmount());
        }

        //总计
        StaffCommissionRefundDetailVo staffCommissionRefundSummaryDtoSummary = new StaffCommissionRefundDetailVo();
        staffCommissionRefundSummaryDtoSummary.setFkStaffName("总计");
        staffCommissionRefundSummaryDtoSummary.setCommissionAmount(commissionAmount);
        staffCommissionRefundSummaryDtoSummary.setUnCommissionAmount(unCommissionAmount);
        staffCommissionRefundSummaryDtoSummary.setOsSettlementQuantity(osSettlementQuantity);
        staffCommissionRefundSummaryDtoSummary.setOsUnSettlementQuantity(osUnSettlementQuantity);
        staffCommissionRefundSummaryDtoSummary.setVgSettlementQuantity(vgSettlementQuantity);
        staffCommissionRefundSummaryDtoSummary.setVgUnSettlementQuantity(vgUnSettlementQuantity);
        staffCommissionRefundSummaryDtoSummary.setSeSettlementQuantity(seSettlementQuantity);
        staffCommissionRefundSummaryDtoSummary.setSeUnSettlementQuantity(seUnSettlementQuantity);
        staffCommissionRefundSummaryDtoSummary.setVgbSettlementQuantity(vgbSettlementQuantity);
        staffCommissionRefundSummaryDtoSummary.setVgbUnSettlementQuantity(vgbUnSettlementQuantity);

        list.add(staffCommissionRefundSummaryDtoSummary);

        //结算总金额
        StaffCommissionRefundSummaryVo.StaffCommissionRefundTotal staffCommissionRefundTotal = new StaffCommissionRefundSummaryVo.StaffCommissionRefundTotal();
        staffCommissionRefundTotal.setFkStaffName("结算总金额");
        staffCommissionRefundTotal.setCommissionAmount(commissionAmountTotal);
        staffCommissionRefundTotal.setCommissionAmount(commissionAmountTotal);
        staffCommissionRefundTotal.setUnCommissionAmount(unCommissionAmountTotal);
        staffCommissionRefundTotal.setOsSettlementAmount(osSettlementAmountTotal);
        staffCommissionRefundTotal.setOsUnSettlementAmount(osUnSettlementAmountTotal);
        staffCommissionRefundTotal.setVgSettlementAmount(vgSettlementAmountTotal);
        staffCommissionRefundTotal.setVgUnSettlementAmount(vgUnSettlementAmountTotal);
        staffCommissionRefundTotal.setSeSettlementAmount(seSettlementAmountTotal);
        staffCommissionRefundTotal.setSeUnSettlementAmount(seUnSettlementAmountTotal);
        staffCommissionRefundTotal.setVgbSettlementAmount(vgbSettlementAmountTotal);
        staffCommissionRefundTotal.setVgbUnSettlementAmount(vgbUnSettlementAmountTotal);

        StaffCommissionRefundSummaryVo staffCommissionRefundSummaryVo = new StaffCommissionRefundSummaryVo();
        staffCommissionRefundSummaryVo.setStaffCommissionRefundList(list);
        staffCommissionRefundSummaryVo.setStaffCommissionRefundSummary(staffCommissionRefundTotal);
        return staffCommissionRefundSummaryVo;


    }

    private void setStaffSettlementRefundName(List<StaffCommissionRefundVo> staffSettlementRefundList, Map<Long, String> courseNameMap, Map<Long, String> channelNameMap,
                                              Map<Long, String> providerNameMap, Map<Long, String> itemStepSelectMap, Map<String, String> cancelOfferReasonSelectMap,
                                              Map<Long, String> contryNameMap, Map<Long, String> staffNameMap, Map<Long, Date> failureDateMap) throws ParseException {
        List<Long> companyIds = staffSettlementRefundList.stream().map(StaffCommissionRefundVo::getFkCompanyId).collect(Collectors.toList());
        Map<Long, String> companyNameMap = permissionCenterClient.getCompanyNamesByIds(new HashSet<>(companyIds)).getData();

        for (StaffCommissionRefundVo staffCommissionRefundVo : staffSettlementRefundList) {
            if (GeneralTool.isNotEmpty(courseNameMap)){
                staffCommissionRefundVo.setCourseFullName(courseNameMap.get(staffCommissionRefundVo.getFkInstitutionCourseId()));
            }
            StringBuilder providerchannelName = new StringBuilder();
            if (GeneralTool.isNotEmpty(channelNameMap)){
                if (GeneralTool.isNotEmpty(channelNameMap.get(staffCommissionRefundVo.getFkInstitutionChannelId()))){
                    providerchannelName.append("【").append(channelNameMap.get(staffCommissionRefundVo.getFkInstitutionChannelId())).append("】");
                }
                staffCommissionRefundVo.setFkInstitutionChannelName(channelNameMap.get(staffCommissionRefundVo.getFkInstitutionChannelId()));
            }
            if (GeneralTool.isNotEmpty(providerNameMap)){
                providerchannelName.append(providerNameMap.get(staffCommissionRefundVo.getFkInstitutionProviderId()));
                staffCommissionRefundVo.setFkInstitutionProviderName(providerNameMap.get(staffCommissionRefundVo.getFkInstitutionProviderId()));
            }
            staffCommissionRefundVo.setFkInstitutionProviderChannelName(providerchannelName.toString());
            if (GeneralTool.isNotEmpty(itemStepSelectMap)){
                String stepName = itemStepSelectMap.get(staffCommissionRefundVo.getFkStudentOfferItemStepId());

                if (staffCommissionRefundVo.getFkStudentOfferItemStepId()==9){
                    Date failureDate = failureDateMap.get(staffCommissionRefundVo.getFkStudentOfferItemId());
                    if (GeneralTool.isNotEmpty(failureDate)){
                        stepName = stepName + "("+DateUtil.format(failureDate,"yyyy-MM-dd")+")" ;
                    }
                }
                staffCommissionRefundVo.setStepName(stepName);
            }
            if (GeneralTool.isNotEmpty(cancelOfferReasonSelectMap)){
                staffCommissionRefundVo.setStaffCommissionActionStepName(cancelOfferReasonSelectMap.get(staffCommissionRefundVo.getFkStaffCommissionStepKey()));
            }
            if (GeneralTool.isNotEmpty(contryNameMap)){
                staffCommissionRefundVo.setFkAreaCountryName(contryNameMap.get(staffCommissionRefundVo.getFkAreaCountryId()));
            }


            StringBuilder refundReviewStaffContent = new StringBuilder();
            if (GeneralTool.isNotEmpty(staffNameMap)){
                if (GeneralTool.isNotEmpty(staffNameMap.get(staffCommissionRefundVo.getFkStaffIdRefundReview()))){
                    refundReviewStaffContent.append(staffNameMap.get(staffCommissionRefundVo.getFkStaffIdRefundReview()));
                }
            }
            if (GeneralTool.isNotEmpty(staffCommissionRefundVo.getRefundReviewContent())){
                refundReviewStaffContent.append("：").append(staffCommissionRefundVo.getRefundReviewContent());
            }
            staffCommissionRefundVo.setRefundReviewStaffContent(refundReviewStaffContent.toString());
            staffCommissionRefundVo.setFkCompanyName(companyNameMap.get(staffCommissionRefundVo.getFkCompanyId()));
        }
    }


    /**
     * 设置单元格文字样式
     * @param writer
     * @param row 行
     * @param column 列
     * @param cellValue 值
     * @param type 0未结算/1已结算
     */
    private void writeCell(ExcelWriter writer,int row ,int column, Object cellValue,int type){
        // 根据x,y轴设置单元格内容
        writer.writeCellValue(column , row, cellValue);
        Font font = writer.createFont();
        //设置颜色
        if (type == 0){
            font.setColor(HSSFColor.ORANGE.index);
        }
        if (type == 1){
            font.setColor(HSSFColor.GREEN.index);
        }
        // 根据x,y轴获取当前单元格样式
        CellStyle cellStyle = writer.createCellStyle(column, row);
        // 内容水平居中
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        // 内容垂直居中
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 设置边框
        if (type == 0){
            cellStyle.setBorderTop(BorderStyle.THIN);
            cellStyle.setBorderBottom(BorderStyle.NONE);
        }
        if (type == 1){
            cellStyle.setBorderTop(BorderStyle.NONE);
            cellStyle.setBorderBottom(BorderStyle.THIN);
        }
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        // 字体颜色标红
        cellStyle.setFont(font);
    }

    /**
     * 设置单元格文字样式
     * @param writer
     * @param row 行
     * @param column 列
     * @param color 颜色
     */
    private void writeCellBackgroundColor(ExcelWriter writer,int row ,int column,Object cellValue,short color){
        // 根据x,y轴设置单元格内容
        writer.writeCellValue(column , row, cellValue);
        Font font = writer.createFont();
        font.setFontHeightInPoints((short) 10);
        font.setBold(true);
        // 根据x,y轴获取当前单元格样式
        CellStyle cellStyle = writer.createCellStyle(column, row);
        //设置颜色
        //eg：IndexedColors.SKY_BLUE.getIndex()
        cellStyle.setFillForegroundColor(color);
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        // 内容水平居中
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        // 内容垂直居中
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        //设置边框
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        //自动换行
        cellStyle.setWrapText(true);
        //设置字体
        cellStyle.setFont(font);
    }

    /**
     * 设置单元格文字样式
     * @param writer
     * @param row 行
     * @param column 列
     */
    private void writeTitleCell(ExcelWriter writer,int row ,int column,Object cellValue){
        // 根据x,y轴设置单元格内容
        writer.writeCellValue(column , row, cellValue);
        //设置高度
        writer.setRowHeight(row,25);
        Font font = writer.createFont();
        font.setFontHeightInPoints((short) 20);
        font.setBold(true);
        // 根据x,y轴获取当前单元格样式
        CellStyle cellStyle = writer.createCellStyle(column, row);
        // 内容水平居中
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        // 内容垂直居中
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        //自动换行
        cellStyle.setWrapText(true);
        //设置字体
        cellStyle.setFont(font);
    }




    /**
     * 设置表头中文
     * @param result
     * @param commissionStepKeys
     */
    private void setTitleName(StaffSettlementStatisticsVo result, List<String> commissionStepKeys) {
        List<StaffCommissionStep> staffCommissionSteps = staffCommissionStepService.list(Wrappers.lambdaQuery(StaffCommissionStep.class)
                .in(StaffCommissionStep::getStepKey, commissionStepKeys));
        Map<String, StaffCommissionStep> staffCommissionStepByStepKeyMap = staffCommissionSteps.stream().collect(HashMap::new, (m, v) -> m.put(v.getStepKey(), v), HashMap::putAll);


        if (GeneralTool.isNotEmpty(result.getSumSettlementAmount())){
            for (Map<String, Object> map : result.getSumSettlementAmount()) {
                if (GeneralTool.isNotEmpty(staffCommissionStepByStepKeyMap.get(map.get("key").toString()))){
                    map.put("title",staffCommissionStepByStepKeyMap.get(map.get("key").toString()).getStepName());
                    map.put("titleChn",staffCommissionStepByStepKeyMap.get(map.get("key").toString()).getStepName());
                }else if("totalCommissionAmount".equals(map.get("key").toString())){
                    map.put("title","合计提成金额");
                    map.put("titleChn","合计提成金额");
                }
            }
        }
        if (GeneralTool.isNotEmpty(result.getTotalAmount())){
            for (Map<String, Object> map : result.getTotalAmount()) {
                if (GeneralTool.isNotEmpty(staffCommissionStepByStepKeyMap.get(map.get("key").toString()))){
                    map.put("title",staffCommissionStepByStepKeyMap.get(map.get("key").toString()).getStepName());
                    map.put("titleChn",staffCommissionStepByStepKeyMap.get(map.get("key").toString()).getStepName());
                }else if("totalCommissionAmount".equals(map.get("key").toString())){
                    map.put("title","合计提成金额");
                    map.put("titleChn","合计提成金额");
                }
            }
        }
    }

    /**
     * 总计金额
     * @param staffSettlementStatisticsDto
     * @param commissionStepKeys
     * @return
     */
    private List<Map<String, Object>> getSumSettlementAmounts(StaffSettlementStatisticsDto staffSettlementStatisticsDto, List<String> commissionStepKeys) {
        if (GeneralTool.isEmpty(staffSettlementStatisticsDto.getSettlementDate())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        if (GeneralTool.isEmpty(commissionStepKeys)){
            return Collections.emptyList();
        }
        List<Map<String, Object>> result = Lists.newArrayList();
        List<StaffCommissionActionVo> staffCommissionActionVos = this.baseMapper.getSumSettlementAmounts(staffSettlementStatisticsDto);
        Map<String, StaffCommissionActionVo> staffCommissionActionDtoMap = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(staffCommissionActionVos)){
            staffCommissionActionDtoMap = staffCommissionActionVos.stream().collect(HashMap::new,(m, v)->m.put(v.getFkStaffCommissionStepKey(),v),HashMap::putAll);
        }
        BigDecimal totalCommissionAmount = BigDecimal.ZERO.setScale(2,RoundingMode.HALF_UP);
        for (String commissionStepKey : commissionStepKeys) {
            Map<String, Object> amountMap = Maps.newHashMap();
            amountMap.put("key",commissionStepKey);
            if (GeneralTool.isEmpty(staffCommissionActionDtoMap.get(commissionStepKey))){
                amountMap.put("value",BigDecimal.ZERO.setScale(2,RoundingMode.HALF_UP));
            }else {
                StaffCommissionActionVo staffCommissionActionVo = staffCommissionActionDtoMap.get(commissionStepKey);
                amountMap.put("value", staffCommissionActionVo.getCommissionAmount());
                totalCommissionAmount = totalCommissionAmount.add(staffCommissionActionVo.getCommissionAmount()).setScale(2,RoundingMode.HALF_UP);
            }
            result.add(amountMap);
        }
        Map<String, Object> totalMap = Maps.newHashMap();
        totalMap.put("key","totalCommissionAmount");
        totalMap.put("value",totalCommissionAmount);
        result.add(totalMap);

        return result;
    }

    /**
     * 获取列表数据
     * @param staffSettlementStatisticsDto
     * @param commissionStepKeys
     * @return
     */
    private List<StaffSettlementStatisticsItemVo> getStaffSettlementStatisticsItemDtos(StaffSettlementStatisticsDto staffSettlementStatisticsDto, List<String> commissionStepKeys, List<Map<String,Object>> totalAmount) {

        //获取固定的数据部分
        List<StaffSettlementStatisticsItemVo> fixedDatas = getStaffSettlementStatisticsFixedDatas(staffSettlementStatisticsDto);

        if (GeneralTool.isEmpty(fixedDatas)){
            if (GeneralTool.isNotEmpty(commissionStepKeys)){
                for (String commissionStepKey : commissionStepKeys) {
                    Map<String,Object> totalAmountByStepKey = Maps.newHashMap();
                    totalAmountByStepKey.put("key",commissionStepKey);
                    totalAmountByStepKey.put("value",BigDecimal.ZERO.setScale(2,RoundingMode.HALF_UP));
                    totalAmount.add(totalAmountByStepKey);
                }
                Map<String,Object> totalAmountByStepKey = Maps.newHashMap();
                totalAmountByStepKey.put("key","totalCommissionAmount");
                totalAmountByStepKey.put("value",BigDecimal.ZERO.setScale(2,RoundingMode.HALF_UP));
                totalAmount.add(totalAmountByStepKey);
            }
            return Collections.emptyList();
        }

        //获取动态的数据部分 以学生分组
        Map<Long,List<StaffCommissionDatasVo<StaffCommissionStatisticsDateAndAmountVo>>> totalDynamicDatasMap = getStaffSettlementStatisticsTotalDynamicDatas(staffSettlementStatisticsDto,fixedDatas,commissionStepKeys,totalAmount);

        //合并固定和动态部分数据
        List<StaffSettlementStatisticsItemVo> result = mergeStaffSettlementStatistics(fixedDatas,totalDynamicDatasMap);


        //设置名称
        setStaffSettlementStatisticsItemDtoListName(result);

        return result;
    }

    /**
     * 员工结算统计列表数据部分设置名称
     * @param result
     */
    private void setStaffSettlementStatisticsItemDtoListName(List<StaffSettlementStatisticsItemVo> result) {
        if (GeneralTool.isEmpty(result)){
            return;
        }

        Set<Long> agentIdSet = Sets.newHashSet();
        Set<Long> fkAreaCountryIdSet = Sets.newHashSet();
        Set<Long> fkInstitutionCourseMajorLevelIdSet = Sets.newHashSet();
        Set<Long> fkInstitutionCourseTypeGroupIdSet = Sets.newHashSet();

        for (StaffSettlementStatisticsItemVo staffSettlementStatisticsItemVo : result) {
            if (GeneralTool.isNotEmpty(staffSettlementStatisticsItemVo.getAgentIds())){
                List<Long> agentIds = Arrays.stream(staffSettlementStatisticsItemVo.getAgentIds().split(",")).map(Long::valueOf).collect(Collectors.toList());
                agentIdSet.addAll(agentIds);
            }
            if (GeneralTool.isNotEmpty(staffSettlementStatisticsItemVo.getFkAreaCountryIds())){
                List<Long> fkAreaCountryIds = Arrays.stream(staffSettlementStatisticsItemVo.getFkAreaCountryIds().split(",")).map(Long::valueOf).collect(Collectors.toList());
                fkAreaCountryIdSet.addAll(fkAreaCountryIds);
            }

            if (GeneralTool.isNotEmpty(staffSettlementStatisticsItemVo.getFkInstitutionCourseMajorLevelIds())){
                List<Long> fkInstitutionCourseMajorLevelIds = Arrays.stream(staffSettlementStatisticsItemVo.getFkInstitutionCourseMajorLevelIds().split(",")).map(Long::valueOf).collect(Collectors.toList());
                fkInstitutionCourseMajorLevelIdSet.addAll(fkInstitutionCourseMajorLevelIds);
            }
            if (GeneralTool.isNotEmpty(staffSettlementStatisticsItemVo.getFkInstitutionCourseTypeGroupIds())){
                List<Long> fkInstitutionCourseTypeGroupIds = Arrays.stream(staffSettlementStatisticsItemVo.getFkInstitutionCourseTypeGroupIds().split(",")).map(Long::valueOf).collect(Collectors.toList());
                fkInstitutionCourseTypeGroupIdSet.addAll(fkInstitutionCourseTypeGroupIds);
            }
        }

        //代理名称
        Map<Long, String> agentNamesByIds = agentService.getAgentNamesByIds(agentIdSet);
        //国家名称
        Map<Long, String> countryNameMap = institutionCenterClient.getCountryChnNameByIds(fkAreaCountryIdSet).getData();
        //课程等级名称
        Map<Long, String> courseMajorLevelNameMap = institutionCenterClient.getMajorLevelNameChnsByIds(fkInstitutionCourseMajorLevelIdSet).getData();
        //课程大类名称
        Map<Long, String> courseTypeGroupIdSetNameMap = institutionCenterClient.getCourseGroupTypeNameChnByIds(fkInstitutionCourseTypeGroupIdSet).getData();


        for (StaffSettlementStatisticsItemVo staffSettlementStatisticsItemVo : result) {
            String studentName = staffSettlementStatisticsItemVo.getStudentName()+"（"
                    +(GeneralTool.isEmpty(staffSettlementStatisticsItemVo.getLastName())?"": staffSettlementStatisticsItemVo.getLastName())
                    +(GeneralTool.isEmpty(staffSettlementStatisticsItemVo.getFirstName())?"": staffSettlementStatisticsItemVo.getFirstName())
                    +")";
            staffSettlementStatisticsItemVo.setStudentName(studentName);

            if (GeneralTool.isNotEmpty(staffSettlementStatisticsItemVo.getAgentIds())){
                String agentNames = Arrays.stream(staffSettlementStatisticsItemVo.getAgentIds().split(",")).map(agentId -> agentNamesByIds.get(Long.valueOf(agentId))).filter(Objects::nonNull).collect(Collectors.joining("、"));
                staffSettlementStatisticsItemVo.setAgentName(agentNames);
            }
            if (GeneralTool.isNotEmpty(staffSettlementStatisticsItemVo.getFkAreaCountryIds())){
                String fkAreaCountryNames = Arrays.stream(staffSettlementStatisticsItemVo.getFkAreaCountryIds().split(",")).map(fkAreaCountryIds -> countryNameMap.get(Long.valueOf(fkAreaCountryIds))).filter(Objects::nonNull).collect(Collectors.joining("、"));
                staffSettlementStatisticsItemVo.setFkAreaCountryNames(fkAreaCountryNames);
            }

            if (GeneralTool.isNotEmpty(staffSettlementStatisticsItemVo.getFkInstitutionCourseMajorLevelIds())){
                String fkInstitutionCourseMajorLevelNames = Arrays.stream(staffSettlementStatisticsItemVo.getFkInstitutionCourseMajorLevelIds().split(",")).map(fkInstitutionCourseMajorLevelId -> courseMajorLevelNameMap.get(Long.valueOf(fkInstitutionCourseMajorLevelId))).filter(Objects::nonNull).collect(Collectors.joining("、"));
                staffSettlementStatisticsItemVo.setFkInstitutionCourseMajorLevelNames(fkInstitutionCourseMajorLevelNames);
            }
            if (GeneralTool.isNotEmpty(staffSettlementStatisticsItemVo.getFkInstitutionCourseTypeGroupIds())){
                String fkInstitutionCourseTypeGroupNames = Arrays.stream(staffSettlementStatisticsItemVo.getFkInstitutionCourseTypeGroupIds().split(",")).map(fkInstitutionCourseTypeGroupId -> courseTypeGroupIdSetNameMap.get(Long.valueOf(fkInstitutionCourseTypeGroupId))).filter(Objects::nonNull).collect(Collectors.joining("、"));
                staffSettlementStatisticsItemVo.setFkInstitutionCourseTypeGroupNames(fkInstitutionCourseTypeGroupNames);

            }

            BigDecimal totalCommissionAmount = BigDecimal.ZERO.setScale(2,RoundingMode.HALF_UP);
            List<StaffCommissionDatasVo<StaffCommissionStatisticsDateAndAmountVo>> dynamicDatas = staffSettlementStatisticsItemVo.getDynamicDatas();
            for (StaffCommissionDatasVo<StaffCommissionStatisticsDateAndAmountVo> dynamicData : dynamicDatas) {
                StaffCommissionStatisticsDateAndAmountVo value = dynamicData.getValue();
                List<Map<String, Object>> settlementAmounts = value.getSettlementAmounts();
                if (GeneralTool.isNotEmpty(settlementAmounts)){
                    for (Map<String, Object> settlementAmount : settlementAmounts) {
                        BigDecimal commissionAmount = new BigDecimal(GeneralTool.isEmpty(settlementAmount.get("value"))?"0": settlementAmount.get("value").toString());
                        totalCommissionAmount = totalCommissionAmount.add(commissionAmount).setScale(2,RoundingMode.HALF_UP);
                    }
                }
            }

            staffSettlementStatisticsItemVo.setTotalCommissionAmount(totalCommissionAmount);

        }
    }

    /**
     * 合并固定和动态部分数据
     * @param fixedDatas
     * @param totalDynamicDatasMap
     * @return
     */
    private List<StaffSettlementStatisticsItemVo> mergeStaffSettlementStatistics(List<StaffSettlementStatisticsItemVo> fixedDatas, Map<Long,List<StaffCommissionDatasVo<StaffCommissionStatisticsDateAndAmountVo>>> totalDynamicDatasMap) {
        if (GeneralTool.isEmpty(fixedDatas)){
            return Collections.emptyList();
        }
        for (StaffSettlementStatisticsItemVo fixedData : fixedDatas) {
            if (GeneralTool.isEmpty(totalDynamicDatasMap.get(fixedData.getFkStudentId()))){
                fixedData.setDynamicDatas(Lists.newArrayList());
            }else {
                fixedData.setDynamicDatas(totalDynamicDatasMap.get(fixedData.getFkStudentId()));
            }
        }
        return fixedDatas;
    }


    /**
     * 员工统计动态的数据部分
     * @param staffSettlementStatisticsDto
     * @param commissionStepKeys
     * @return
     */
    private Map<Long,List<StaffCommissionDatasVo<StaffCommissionStatisticsDateAndAmountVo>>> getStaffSettlementStatisticsTotalDynamicDatas(StaffSettlementStatisticsDto staffSettlementStatisticsDto, List<StaffSettlementStatisticsItemVo> fixedDatas, List<String> commissionStepKeys, List<Map<String,Object>> totalAmount) {
        if (GeneralTool.isEmpty(commissionStepKeys)||GeneralTool.isEmpty(fixedDatas)){
            return Maps.newHashMap();
        }
        //k,v-步骤key，总合计金额
        Map<String,BigDecimal> totalAmountByStepKey = Maps.newHashMap();

        //最终返回数据结构 k,v - 学生id，动态数据
        Map<Long,List<StaffCommissionDatasVo<StaffCommissionStatisticsDateAndAmountVo>>> resultMap = Maps.newHashMap();
        //查询动态数据部分
        List<StaffCommissionActionVo> staffCommissionActionVos = this.baseMapper.getStaffSettlementStatisticsTotalDynamicDatas(staffSettlementStatisticsDto);

//        if (GeneralTool.isEmpty(staffCommissionActionVos)){
//            return Maps.newHashMap();
//        }
        Map<Long, List<StaffCommissionActionVo>> actionDtosMap = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(staffCommissionActionVos)){
            actionDtosMap = staffCommissionActionVos.stream().collect(Collectors.groupingBy(StaffCommissionActionVo::getFkStudentId));
        }
        Set<Long> studentIds = fixedDatas.stream().map(StaffSettlementStatisticsItemVo::getFkStudentId).collect(Collectors.toSet());
        for (Long studentId : studentIds) {
            List<StaffCommissionDatasVo<StaffCommissionStatisticsDateAndAmountVo>> result = Lists.newArrayList();
            Map<String, List<StaffCommissionActionVo>> staffCommissionActionDtosMap = Maps.newHashMap();
            List<StaffCommissionActionVo> actionDtos = actionDtosMap.get(studentId);
            if (GeneralTool.isNotEmpty(actionDtos)){
                staffCommissionActionDtosMap = actionDtos.stream().collect(Collectors.groupingBy(StaffCommissionActionVo::getFkStaffCommissionStepKey));
            }
            for (String commissionStepKey : commissionStepKeys) {
                //单个步骤合计
                if (GeneralTool.isEmpty(totalAmountByStepKey.get(commissionStepKey))){
                    totalAmountByStepKey.put(commissionStepKey,BigDecimal.ZERO.setScale(2,RoundingMode.HALF_UP));
                }
                //所有步骤合计
                if (GeneralTool.isEmpty(totalAmountByStepKey.get("totalCommissionAmount"))){
                    totalAmountByStepKey.put("totalCommissionAmount",BigDecimal.ZERO.setScale(2,RoundingMode.HALF_UP));
                }

                List<StaffCommissionActionVo> commissionActionDtos = staffCommissionActionDtosMap.get(commissionStepKey);
                StaffCommissionDatasVo<StaffCommissionStatisticsDateAndAmountVo> staffCommissionDatasVo = new StaffCommissionDatasVo<>();
                staffCommissionDatasVo.setKey(commissionStepKey);
                //日期和金额信息
                StaffCommissionStatisticsDateAndAmountVo value = new StaffCommissionStatisticsDateAndAmountVo();
                if (GeneralTool.isEmpty(commissionActionDtos)){
                    //如果步骤不存在
                    value.setSettlementAmounts(Lists.newArrayList());
                    value.setSettlementDates(Lists.newArrayList());

                }else {
                    //设置金额和日期
                    value = generateStaffCommissionStatisticsDateAndAmountDto(commissionActionDtos);
                    for (Map<String, Object> settlementAmount : value.getSettlementAmounts()) {
                        totalAmountByStepKey.put(commissionStepKey,totalAmountByStepKey.get(commissionStepKey).add(new BigDecimal(settlementAmount.get("value").toString())).setScale(2,RoundingMode.HALF_UP));
                        totalAmountByStepKey.put("totalCommissionAmount",totalAmountByStepKey.get("totalCommissionAmount").add(new BigDecimal(settlementAmount.get("value").toString())).setScale(2,RoundingMode.HALF_UP));
                    }
                }
                staffCommissionDatasVo.setValue(value);
                result.add(staffCommissionDatasVo);
            }
            resultMap.put(studentId,result);
        }

        if (GeneralTool.isNotEmpty(totalAmountByStepKey)){
            for (String commissionStepKey : commissionStepKeys) {
                Map<String,Object> stepTotalAmountMap = Maps.newHashMap();
                stepTotalAmountMap.put("key",commissionStepKey);
                stepTotalAmountMap.put("value",totalAmountByStepKey.get(commissionStepKey));
                totalAmount.add(stepTotalAmountMap);
            }
            Map<String,Object> stepTotalAmountMap = Maps.newHashMap();
            stepTotalAmountMap.put("key","totalCommissionAmount");
            stepTotalAmountMap.put("value",totalAmountByStepKey.get("totalCommissionAmount"));
            totalAmount.add(stepTotalAmountMap);
        }
        return resultMap;
    }

    /**
     * 封装成StaffCommissionStatisticsDateAndAmountDto对象
     * @param staffCommissionActionVos
     * @return
     */
    private StaffCommissionStatisticsDateAndAmountVo generateStaffCommissionStatisticsDateAndAmountDto(List<StaffCommissionActionVo> staffCommissionActionVos) {
        StaffCommissionStatisticsDateAndAmountVo result = new StaffCommissionStatisticsDateAndAmountVo();
        List<Map<String,Object>> settlementDates = Lists.newArrayList();
        List<Map<String,Object>> settlementAmounts = Lists.newArrayList();
        if (GeneralTool.isEmpty(staffCommissionActionVos)){
            result.setSettlementDates(settlementDates);
            result.setSettlementAmounts(settlementAmounts);
            return result;
        }

        for (StaffCommissionActionVo staffCommissionActionVo : staffCommissionActionVos) {
            Map<String,Object> settlementDateItem = Maps.newHashMap();
            settlementDateItem.put("value", staffCommissionActionVo.getSettlementDate());
            settlementDateItem.put("type", staffCommissionActionVo.getStatus());
            settlementDates.add(settlementDateItem);

            Map<String,Object> settlementAmountItem = Maps.newHashMap();
            settlementAmountItem.put("value", staffCommissionActionVo.getCommissionAmount());
            settlementAmountItem.put("type", staffCommissionActionVo.getStatus());
            settlementAmounts.add(settlementAmountItem);
        }
        result.setSettlementDates(settlementDates);
        result.setSettlementAmounts(settlementAmounts);
        return result;
    }


    /**
     * 员工统计固定的数据部分
     * @param staffSettlementStatisticsDto
     * @return
     */
    private List<StaffSettlementStatisticsItemVo> getStaffSettlementStatisticsFixedDatas(StaffSettlementStatisticsDto staffSettlementStatisticsDto) {
        if (GeneralTool.isEmpty(staffSettlementStatisticsDto.getFkStaffId())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        List<StaffSettlementStatisticsItemVo> fixedDatas = this.baseMapper.getStaffSettlementStatisticsFixedDatas(staffSettlementStatisticsDto);
        if (GeneralTool.isEmpty(fixedDatas)){
            return Collections.emptyList();
        }

        //学生已结案，且当月（选择）没有可结算的记录时，无需显示
        fixedDatas = fixedDatas.stream().filter(staffSettlementStatisticsItemVo -> {
            if (staffSettlementStatisticsItemVo.getStatus()!=2){
                return true;
            }
            if (staffSettlementStatisticsItemVo.getStatus()==2&&GeneralTool.isNotEmpty(staffSettlementStatisticsItemVo.getSettlementDates())){
                String[] settlementDateArray = staffSettlementStatisticsItemVo.getSettlementDates().split(",");
                List<String> settlementDateList = Lists.newArrayList(settlementDateArray);
                SimpleDateFormat sf = new SimpleDateFormat("yyyyMM");
                if (settlementDateList.contains(sf.format(staffSettlementStatisticsDto.getSettlementDate()))){
                    return true;
                }
            }
            return false;
        }).collect(Collectors.toList());

        return fixedDatas;
    }


    /**
     * 申请计划匹配规则，组装列表
     * @param studentOfferItemCommissionVos
     * @param policyList
     * @return
     */
    private SettlementInfoVo getStaffCommissionConfirmDtos(List<StudentOfferItemCommissionVo> studentOfferItemCommissionVos, List<StaffCommissionPolicy> policyList, StudentOfferItemCommissionInfoDto studentOfferItemCommissionInfoDto) {
        //空数据时返回数据结构
        if (GeneralTool.isEmpty(studentOfferItemCommissionVos)||GeneralTool.isEmpty(policyList)){
            SettlementInfoVo settlementInfoVo = new SettlementInfoVo();
            settlementInfoVo.setTitleInfos(Lists.newArrayList());
            settlementInfoVo.setStaffCommissionTotalDto(null);
            return settlementInfoVo;
        }

        //最终返回结构
        SettlementInfoVo settlementInfoVo = new SettlementInfoVo();
        //提成步骤key
        String fkStaffCommissionStepKey = studentOfferItemCommissionInfoDto.getFkStaffCommissionStepKey();
        //每一条匹配规则的申请计划数据
        List<StaffCommissionConfirmVo> resultList = Lists.newArrayList();
        //项目角色key
        Set<String> studentProjectRoleKeys = Sets.newHashSet();
        //用于保存最优规则的金额
        Map<String,BigDecimal> maxAmount = Maps.newHashMap();
        //用于记录匹配最优规则的计划id
        Map<String,Long> offerItemIdOfMaxAmount = Maps.newHashMap();
        //用于记录其他和加申步骤的已结算总金额
        Map<String,BigDecimal> sumSettlementAmount = Maps.newHashMap();
        //用于记录其他和加申步骤的未结算总金额
        Map<String,BigDecimal> sumNotSettlementAmount = Maps.newHashMap();
        //用于记录其他和加申步骤的已结算申请计划ids
        Map<String,List<Long>> settlementItemIdMap = Maps.newHashMap();
        //用于记录其他和加申步骤的未结算申请计划ids
        Map<String,List<Long>> notSettlementItemIdMap = Maps.newHashMap();
//        //结算日期map key-项目角色key value-结算日期
//        Map<String,String> settlementDateByProjectRoleKeyMap = Maps.newHashMap();
//        //结算日期map key-项目角色key value-结算日期
//        Map<String,String> notSettlementDateByProjectRoleKeyMap = Maps.newHashMap();

        //结算日期list
        List<String> settlementDates = Lists.newArrayList();
        //未结算日期list
        List<String> notSettlementDates = Lists.newArrayList();

        //服务费获取 用于获取币种和规则为比例的情况计算提成金额
        BigDecimal serviceFee = getServiceFeeByStudentId(studentOfferItemCommissionVos.get(0).getFkStudentId());
        //初始化staffCommissionDatasDtoListMap key设置申请计划id value设置一个空数组
        Map<Long, List<StaffCommissionDatasVo<BigDecimal>>> staffCommissionDatasDtoListMap = Maps.newHashMap();
        for (StudentOfferItemCommissionVo studentOfferItemCommissionVo : studentOfferItemCommissionVos) {
            if (GeneralTool.isEmpty(staffCommissionDatasDtoListMap.get(studentOfferItemCommissionVo.getId()))){
                staffCommissionDatasDtoListMap.put(studentOfferItemCommissionVo.getId(),Lists.newArrayList());
            }
        }
        //获取项目角色信息map
        List<StudentProjectRole> studentProjectRoles = studentProjectRoleService.list();
        Map<Long, StudentProjectRole> roleByRoleIdMap = studentProjectRoles.stream().collect(HashMap::new,(m,v)->m.put(v.getId(),v),HashMap::putAll);
        Map<String, StudentProjectRole> roleByRoleKeyMap = studentProjectRoles.stream().collect(HashMap::new,(m,v)->m.put(v.getRoleKey(),v),HashMap::putAll);

        //申请计划ids
        Set<Long> studentOfferItemIds = studentOfferItemCommissionVos.stream().map(StudentOfferItemCommissionVo::getId).collect(Collectors.toSet());
        //查询所有符合条件申请计划的结算记录
        List<StaffCommissionAction> actions = list(Wrappers.lambdaQuery(StaffCommissionAction.class).in(StaffCommissionAction::getFkStudentOfferItemId, studentOfferItemIds));
        //已结算,已确认的结算记录 按申请计划分组
        Map<Long, List<StaffCommissionAction>> stableAmountMap = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(actions)){
            stableAmountMap = actions.stream().collect(Collectors.groupingBy(StaffCommissionAction::getFkStudentOfferItemId));
        }

        //初始化结算状态map 0未确认/1已确认/2已结算 key设置申请计划 value设置状态
        Map<Long, Integer> settlementStatusListMap = initializeSettlementStatusListMap(fkStaffCommissionStepKey, studentOfferItemIds, actions);

        for (StudentOfferItemCommissionVo studentOfferItemCommissionVo : studentOfferItemCommissionVos) {
            List<StaffCommissionAction> stableAmountActions = stableAmountMap.get(studentOfferItemCommissionVo.getId());

            //获取匹配的规则
            List<StaffCommissionPolicy> suitablePolicies = doMatchPolicy(policyList, roleByRoleIdMap, studentOfferItemCommissionVo);

            if (GeneralTool.isNotEmpty(suitablePolicies)){
                //优先级最高的规则
                Optional<StaffCommissionPolicy> max = suitablePolicies.stream().max(Comparator.comparing(StaffCommissionPolicy::getPriority, Integer::compareTo));
                if (max.isPresent()){
                    StaffCommissionPolicy staffCommissionPolicy = max.get();
                    //设置角色和金额
                    StaffCommissionDatasVo<BigDecimal> staffCommissionDatasVo = new StaffCommissionDatasVo();
                    List<StaffCommissionAction> actionList = Lists.newArrayList();
                    BigDecimal value = BigDecimal.ZERO;
                    if (GeneralTool.isNotEmpty(stableAmountActions)){
                        actionList = stableAmountActions.stream().filter(s -> s.getFkStaffCommissionStepKey().equals(fkStaffCommissionStepKey)
                                && s.getFkStudentProjectRoleKey().equals(staffCommissionPolicy.getFkStudentProjectRoleKey())).collect(Collectors.toList());
                    }

                    if (GeneralTool.isNotEmpty(actionList)){
                        value = actionList.get(0).getCommissionAmount();

                        String settlementDate = actionList.stream().filter(a->a.getStatus()==1).map(StaffCommissionAction::getSettlementDate).filter(Objects::nonNull).collect(Collectors.joining(","));
                        String notSettlementDate = actionList.stream().filter(a->a.getStatus()==0).map(StaffCommissionAction::getSettlementDate).filter(Objects::nonNull).collect(Collectors.joining(","));

                        if (GeneralTool.isNotEmpty(settlementDate)){
                            if (!settlementDates.contains(settlementDate)){
                                settlementDates.add(settlementDate);
                            }
                        }
                        if (GeneralTool.isNotEmpty(notSettlementDate)){
                            if (!notSettlementDates.contains(notSettlementDate)){
                                notSettlementDates.add(notSettlementDate);
                            }
                        }

//                        if (GeneralTool.isEmpty(settlementDateByProjectRoleKeyMap.get(staffCommissionPolicy.getFkStudentProjectRoleKey()))&&GeneralTool.isNotEmpty(settlementDate)) {
//                            settlementDateByProjectRoleKeyMap.put(staffCommissionPolicy.getFkStudentProjectRoleKey(),settlementDate);
//                        } else {
//                            if (GeneralTool.isNotEmpty(settlementDate)){
//                                settlementDateByProjectRoleKeyMap.put(staffCommissionPolicy.getFkStudentProjectRoleKey(),settlementDateByProjectRoleKeyMap.get(staffCommissionPolicy.getFkStudentProjectRoleKey())+","+settlementDate);
//                            }
//                        }
//
//                        if (GeneralTool.isEmpty(notSettlementDateByProjectRoleKeyMap.get(staffCommissionPolicy.getFkStudentProjectRoleKey()))&&GeneralTool.isNotEmpty(notSettlementDate)) {
//                            notSettlementDateByProjectRoleKeyMap.put(staffCommissionPolicy.getFkStudentProjectRoleKey(),notSettlementDate);
//                        } else {
//                            if (GeneralTool.isNotEmpty(notSettlementDate)){
//                                notSettlementDateByProjectRoleKeyMap.put(staffCommissionPolicy.getFkStudentProjectRoleKey(),notSettlementDateByProjectRoleKeyMap.get(staffCommissionPolicy.getFkStudentProjectRoleKey())+","+notSettlementDate);
//                            }
//                        }
                    }else if (GeneralTool.isNotEmpty(staffCommissionPolicy.getFixedAmount())){
                        value = staffCommissionPolicy.getFixedAmount();
                    }else if (GeneralTool.isNotEmpty(staffCommissionPolicy.getCommissionRate())){
                        value = serviceFee.multiply(staffCommissionPolicy.getCommissionRate().divide(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP)).setScale(2,RoundingMode.HALF_UP);
                    }
                    staffCommissionDatasVo.setValue(value);
                    staffCommissionDatasVo.setKey(staffCommissionPolicy.getFkStudentProjectRoleKey());
                    staffCommissionDatasVo.setType(settlementStatusListMap.get(studentOfferItemCommissionVo.getId()));
                    if (ProjectKeyEnum.COMMISSION_STEP_OTHER_INSTITUTION.key.equals(fkStaffCommissionStepKey)
                            ||ProjectKeyEnum.COMMISSION_STEP_OTHER_MAJOR.key.equals(fkStaffCommissionStepKey)
                            ||ProjectKeyEnum.COMMISSION_STEP_ADDAPP.key.equals(fkStaffCommissionStepKey)) {
                        if (staffCommissionDatasVo.getType() == 1){
                            if (GeneralTool.isEmpty(notSettlementItemIdMap.get(staffCommissionPolicy.getFkStudentProjectRoleKey()))) {
                                notSettlementItemIdMap.put(staffCommissionPolicy.getFkStudentProjectRoleKey(),Lists.newArrayList(studentOfferItemCommissionVo.getId()));
                            }else {
                                notSettlementItemIdMap.get(staffCommissionPolicy.getFkStudentProjectRoleKey()).add(studentOfferItemCommissionVo.getId());
                            }

                            if (GeneralTool.isEmpty(sumNotSettlementAmount.get(staffCommissionPolicy.getFkStudentProjectRoleKey()))){
                                sumNotSettlementAmount.put(staffCommissionPolicy.getFkStudentProjectRoleKey(), staffCommissionDatasVo.getValue());
                            }else {
                                sumNotSettlementAmount.put(staffCommissionPolicy.getFkStudentProjectRoleKey(),sumNotSettlementAmount.get(staffCommissionPolicy.getFkStudentProjectRoleKey()).add(staffCommissionDatasVo.getValue()));
                            }
                        } else if (staffCommissionDatasVo.getType() == 2){
                            if (GeneralTool.isEmpty(settlementItemIdMap.get(staffCommissionPolicy.getFkStudentProjectRoleKey()))) {
                                settlementItemIdMap.put(staffCommissionPolicy.getFkStudentProjectRoleKey(),Lists.newArrayList(studentOfferItemCommissionVo.getId()));
                            }else {
                                settlementItemIdMap.get(staffCommissionPolicy.getFkStudentProjectRoleKey()).add(studentOfferItemCommissionVo.getId());
                            }

                            if (GeneralTool.isEmpty(sumSettlementAmount.get(staffCommissionPolicy.getFkStudentProjectRoleKey()))){
                                sumSettlementAmount.put(staffCommissionPolicy.getFkStudentProjectRoleKey(), staffCommissionDatasVo.getValue());
                            }else {
                                sumSettlementAmount.put(staffCommissionPolicy.getFkStudentProjectRoleKey(),sumSettlementAmount.get(staffCommissionPolicy.getFkStudentProjectRoleKey()).add(staffCommissionDatasVo.getValue()));
                            }
                        }
                    }
                    studentProjectRoleKeys.add(staffCommissionPolicy.getFkStudentProjectRoleKey());
                    if (GeneralTool.isEmpty(maxAmount.get(staffCommissionPolicy.getFkStudentProjectRoleKey()))){
                        maxAmount.put(staffCommissionPolicy.getFkStudentProjectRoleKey(), staffCommissionDatasVo.getValue());
                        offerItemIdOfMaxAmount.put(staffCommissionPolicy.getFkStudentProjectRoleKey(), studentOfferItemCommissionVo.getId());
                    }else {
                        if (maxAmount.get(staffCommissionPolicy.getFkStudentProjectRoleKey()).compareTo(staffCommissionDatasVo.getValue())<0){
                            maxAmount.put(staffCommissionPolicy.getFkStudentProjectRoleKey(), staffCommissionDatasVo.getValue());
                            offerItemIdOfMaxAmount.put(staffCommissionPolicy.getFkStudentProjectRoleKey(), studentOfferItemCommissionVo.getId());
                        }

                    }
                    staffCommissionDatasDtoListMap.get(studentOfferItemCommissionVo.getId()).add(staffCommissionDatasVo);
                }
            }
        }


        List<StudentOfferItem> studentOfferItems = studentOfferItemService.list(Wrappers.lambdaQuery(StudentOfferItem.class).in(StudentOfferItem::getId, studentOfferItemIds));


        Map<Long, String> settlementDateByMultipleStep = Maps.newHashMap();
        if (fkStaffCommissionStepKey.equals(ProjectKeyEnum.COMMISSION_STEP_OTHER_INSTITUTION.key)
                ||fkStaffCommissionStepKey.equals(ProjectKeyEnum.COMMISSION_STEP_OTHER_MAJOR.key)
                ||fkStaffCommissionStepKey.equals(ProjectKeyEnum.COMMISSION_STEP_ADDAPP.key)){

            List<StaffCommissionAction> staffCommissionActionsToGetDate = list(Wrappers.lambdaQuery(StaffCommissionAction.class)
                    .eq(StaffCommissionAction::getFkStaffCommissionStepKey,fkStaffCommissionStepKey)
                    .in(StaffCommissionAction::getFkStudentOfferItemId, studentOfferItemIds));
            if (GeneralTool.isNotEmpty(staffCommissionActionsToGetDate)){
                Map<Long, List<StaffCommissionAction>> dateListMap = staffCommissionActionsToGetDate.stream().collect(Collectors.groupingBy(StaffCommissionAction::getFkStudentOfferItemId));
                if (GeneralTool.isNotEmpty(dateListMap)){
                    for (Long fkStudentOfferItemId : dateListMap.keySet()) {
                        List<StaffCommissionAction> staffCommissionActions = dateListMap.get(fkStudentOfferItemId);
                        if (GeneralTool.isNotEmpty(staffCommissionActions)){
                            String settlementDate = staffCommissionActions.stream().map(StaffCommissionAction::getSettlementDate).distinct().collect(Collectors.joining(","));
                            settlementDateByMultipleStep.put(fkStudentOfferItemId,settlementDate);
                        }

                    }

                }
            }
        }


        Set<Long> offerIds = studentOfferItems.stream().map(StudentOfferItem::getFkStudentOfferId).filter(Objects::nonNull).collect(Collectors.toSet());
        //计划id找方案id
        Map<Long, Long> itemToOfferIdMap = studentOfferItems.stream().collect(Collectors.toMap(StudentOfferItem::getId, StudentOfferItem::getFkStudentOfferId));

        //项目成员
        List<StudentProjectRoleStaff> studentProjectRoleStaffs = studentProjectRoleStaffService.list(Wrappers.lambdaQuery(StudentProjectRoleStaff.class)
                .eq(StudentProjectRoleStaff::getFkTableName, TableEnum.SALE_STUDENT_OFFER.key)
                .in(StudentProjectRoleStaff::getFkTableId, offerIds)
                .eq(StudentProjectRoleStaff::getIsActive, true));

        Map<Long, List<StudentProjectRoleStaff>> studentProjectRoleStaffMap = Maps.newHashMap();
        Map<Long, String> staffNamesMap = Maps.newHashMap();
        Set<Long> staffIds = Sets.newHashSet();
        if (GeneralTool.isNotEmpty(studentProjectRoleStaffs)){
            studentProjectRoleStaffMap = studentProjectRoleStaffs.stream().collect(Collectors.groupingBy(StudentProjectRoleStaff::getFkTableId));
            staffIds = studentProjectRoleStaffs.stream().map(StudentProjectRoleStaff::getFkStaffId).collect(Collectors.toSet());
        }
        if (GeneralTool.isNotEmpty(actions)){
            Set<Long> actionStaffIds = actions.stream().map(StaffCommissionAction::getFkStaffId).collect(Collectors.toSet());
            staffIds.addAll(actionStaffIds);
        }
        if (GeneralTool.isNotEmpty(staffIds)){
            staffNamesMap = permissionCenterClient.getStaffNamesByIds(staffIds);
        }

        for (StudentOfferItem studentOfferItem : studentOfferItems) {
            List<StaffCommissionDatasVo<BigDecimal>> staffCommissionDatasVos = staffCommissionDatasDtoListMap.get(studentOfferItem.getId());
            if (GeneralTool.isEmpty(staffCommissionDatasVos)){
                continue;
            }
            StaffCommissionConfirmVo staffCommissionConfirmVo = BeanCopyUtils.objClone(studentOfferItem, StaffCommissionConfirmVo::new);
            assert staffCommissionConfirmVo != null;
            staffCommissionConfirmVo.setSettlementDate(settlementDateByMultipleStep.get(studentOfferItem.getId()));
            staffCommissionConfirmVo.setFkStudentOfferItemId(studentOfferItem.getId());
            staffCommissionConfirmVo.setFkStudentOfferId(studentOfferItem.getFkStudentOfferId());
            staffCommissionConfirmVo.setStepId(studentOfferItem.getFkStudentOfferItemStepId());
            if (studentOfferItem.getFkInstitutionCourseId()==-1L){
                staffCommissionConfirmVo.setFkInstitutionCourseName(studentOfferItem.getOldCourseCustomName());
                staffCommissionConfirmVo.setCourseTypeGroupNames(studentOfferItem.getOldCourseTypeName());
            }

            Set<String> currentProjectRoleKeys = staffCommissionDatasVos.stream().map(StaffCommissionDatasVo::getKey).collect(Collectors.toSet());
            if (currentProjectRoleKeys.size()<studentProjectRoleKeys.size()){
                for (String studentProjectRoleKey : studentProjectRoleKeys) {
                    if (currentProjectRoleKeys.contains(studentProjectRoleKey)){
                        continue;
                    }
                    StaffCommissionDatasVo<BigDecimal> staffCommissionDatasVo = new StaffCommissionDatasVo();
                    staffCommissionDatasVo.setKey(studentProjectRoleKey);
                    staffCommissionDatasVo.setValue(BigDecimal.ZERO);
                    staffCommissionDatasVo.setType(settlementStatusListMap.get(staffCommissionConfirmVo.getFkStudentOfferItemId()));

                    if (staffCommissionDatasVo.getType() == 1){
                        if (GeneralTool.isEmpty(sumNotSettlementAmount.get(studentProjectRoleKey))){
                            sumNotSettlementAmount.put(studentProjectRoleKey, staffCommissionDatasVo.getValue());
                        }else {

                            sumNotSettlementAmount.put(studentProjectRoleKey,sumNotSettlementAmount.get(studentProjectRoleKey).add(staffCommissionDatasVo.getValue()));
                        }
                    } else if (staffCommissionDatasVo.getType() == 2){
                        if (GeneralTool.isEmpty(sumSettlementAmount.get(studentProjectRoleKey))){
                            sumSettlementAmount.put(studentProjectRoleKey, staffCommissionDatasVo.getValue());
                        }else {
                            sumSettlementAmount.put(studentProjectRoleKey,sumSettlementAmount.get(studentProjectRoleKey).add(staffCommissionDatasVo.getValue()));
                        }
                    }
                    staffCommissionDatasVos.add(staffCommissionDatasVo);
                }
            }

            if (GeneralTool.isEmpty(studentOfferItem.getFkInstitutionCourseMajorLevelIds())){
                staffCommissionConfirmVo.setFkInstitutionCourseMajorLevelName(studentOfferItem.getOldCourseMajorLevelName());
            }
            staffCommissionConfirmVo.setDynamicDatas(staffCommissionDatasVos);
            resultList.add(staffCommissionConfirmVo);
        }
        if (fkStaffCommissionStepKey.equals(ProjectKeyEnum.COMMISSION_STEP_OTHER_MAJOR.key)){
            List<Long> offerItemIds = resultList.stream().map(StaffCommissionConfirmVo::getFkStudentOfferItemId).collect(Collectors.toList());
            List<StaffCommissionConfirmVo> staffCommissionConfirmVoList = this.baseMapper.getCourseTypeGroupAndSettlementStatus(offerItemIds);
            Map<Long, String> courseTypeGroupNamesMap = staffCommissionConfirmVoList.stream().collect(Collectors.toMap(StaffCommissionConfirmVo::getFkStudentOfferItemId, StaffCommissionConfirmVo::getCourseTypeGroupNames));

            for (StaffCommissionConfirmVo staffCommissionConfirmVo : resultList) {
                staffCommissionConfirmVo.setCourseTypeGroupNames(courseTypeGroupNamesMap.get(staffCommissionConfirmVo.getFkStudentOfferItemId()));
            }

        }


        //设置名称
        setStaffCommissionConfirmDtosName(resultList,null);

        for (StaffCommissionConfirmVo staffCommissionConfirmVo : resultList) {
            Integer settlementStatus = settlementStatusListMap.get(staffCommissionConfirmVo.getFkStudentOfferItemId());
            staffCommissionConfirmVo.setSettlementStatus(settlementStatus);
            switch (settlementStatus){
                case 0: staffCommissionConfirmVo.setSettlementStatusName("未确认");
                        staffCommissionConfirmVo.setConfirmSettlementButtonType(true);
                        break;
                case 1: staffCommissionConfirmVo.setSettlementStatusName("已确认");
                        staffCommissionConfirmVo.setCancelSettlementButtonType(true);
                        break;
                case 2: staffCommissionConfirmVo.setSettlementStatusName("已结算");
                default: break;
            }
        }


        List<StaffCommissionDatasVo<String>> titleInfos = Lists.newArrayList();
        List<StaffCommissionSpecificVo> staffCommissionSpecificVos = Lists.newArrayList();
        for (String studentProjectRoleKey : studentProjectRoleKeys) {
            //设置动态表头的数据
            StaffCommissionDatasVo<String> staffCommissionDatasVo = new StaffCommissionDatasVo();
            staffCommissionDatasVo.setKey(studentProjectRoleKey);
            staffCommissionDatasVo.setTitle(roleByRoleKeyMap.get(studentProjectRoleKey).getRoleName());
            staffCommissionDatasVo.setTitleChn(roleByRoleKeyMap.get(studentProjectRoleKey).getRoleName());
            staffCommissionDatasVo.setTitleChn(roleByRoleKeyMap.get(studentProjectRoleKey).getRoleName());
            staffCommissionDatasVo.setViewOrder(roleByRoleKeyMap.get(studentProjectRoleKey).getViewOrder());
            titleInfos.add(staffCommissionDatasVo);

            if (ProjectKeyEnum.COMMISSION_STEP_OTHER_INSTITUTION.key.equals(fkStaffCommissionStepKey)
                    ||ProjectKeyEnum.COMMISSION_STEP_OTHER_MAJOR.key.equals(fkStaffCommissionStepKey)
                    ||ProjectKeyEnum.COMMISSION_STEP_ADDAPP.key.equals(fkStaffCommissionStepKey)) {
                List<StaffCommissionAction> currentStepActions = Lists.newArrayList();
                if (GeneralTool.isNotEmpty(actions)){
                    currentStepActions = actions.stream().filter(a -> a.getFkStaffCommissionStepKey().equals(fkStaffCommissionStepKey)
                            &&a.getFkStudentProjectRoleKey().equals(studentProjectRoleKey)).collect(Collectors.toList());
                }

                //未结算的总金额信息
                GetStaffCommissionSpecificDtoAndSetNameContext getStaffCommissionSpecificDtoAndSetNameContext = new GetStaffCommissionSpecificDtoAndSetNameContext();
                getStaffCommissionSpecificDtoAndSetNameContext.setSumSettlementAmountMap(sumNotSettlementAmount)
                        .setSettlementItemIdMap(notSettlementItemIdMap)
                        .setRoleByRoleKeyMap(roleByRoleKeyMap)
                        .setItemToOfferIdMap(itemToOfferIdMap)
                        .setStudentProjectRoleStaffMap(studentProjectRoleStaffMap)
                        .setStaffNamesMap(staffNamesMap)
                        .setStudentProjectRoleKey(studentProjectRoleKey)
                        .setCurrentStepActions(currentStepActions)
                        .setType(0);

                StaffCommissionSpecificVo sumNotStaffCommissionSpecificVo = getStaffCommissionSpecificDtoAndSetName(getStaffCommissionSpecificDtoAndSetNameContext);
//                sumNotStaffCommissionSpecificVo.setSettlementDate(notSettlementDateByProjectRoleKeyMap.get(studentProjectRoleKey));
                if (!sumNotStaffCommissionSpecificVo.getAmount().equals(BigDecimal.ZERO)){
                    staffCommissionSpecificVos.add(sumNotStaffCommissionSpecificVo);
                }

                //已结算的总金额信息
                getStaffCommissionSpecificDtoAndSetNameContext.setSumSettlementAmountMap(sumSettlementAmount)
                        .setSettlementItemIdMap(settlementItemIdMap)
                        .setType(1);

                StaffCommissionSpecificVo sumStaffCommissionSpecificVo = getStaffCommissionSpecificDtoAndSetName(getStaffCommissionSpecificDtoAndSetNameContext);
//                sumStaffCommissionSpecificVo.setSettlementDate(settlementDateByProjectRoleKeyMap.get(studentProjectRoleKey));
                if (!sumStaffCommissionSpecificVo.getAmount().equals(BigDecimal.ZERO)){
                    staffCommissionSpecificVos.add(sumStaffCommissionSpecificVo);
                }

            }else {

                List<StaffCommissionAction> commissionActions = Lists.newArrayList();
                if (GeneralTool.isNotEmpty(stableAmountMap.get(offerItemIdOfMaxAmount.get(studentProjectRoleKey)))){
                    commissionActions = stableAmountMap.get(offerItemIdOfMaxAmount.get(studentProjectRoleKey));
                }

                StaffCommissionSpecificVo staffCommissionSpecificVo = new StaffCommissionSpecificVo();
                staffCommissionSpecificVo.setAmount(maxAmount.get(studentProjectRoleKey));
                staffCommissionSpecificVo.setStudentProjectRoleKey(studentProjectRoleKey);
                staffCommissionSpecificVo.setStudentProjectRoleName(roleByRoleKeyMap.get(studentProjectRoleKey).getRoleName());
                staffCommissionSpecificVo.setStudentOfferItemId(offerItemIdOfMaxAmount.get(studentProjectRoleKey));

//                if (GeneralTool.isNotEmpty(notSettlementDateByProjectRoleKeyMap.get(studentProjectRoleKey))){
//                    staffCommissionSpecificVo.setSettlementDate(notSettlementDateByProjectRoleKeyMap.get(studentProjectRoleKey));
//                }else if (GeneralTool.isNotEmpty(settlementDateByProjectRoleKeyMap.get(studentProjectRoleKey))){
//                    staffCommissionSpecificVo.setSettlementDate(settlementDateByProjectRoleKeyMap.get(studentProjectRoleKey));
//                }

                Long offerId = itemToOfferIdMap.get(staffCommissionSpecificVo.getStudentOfferItemId());
                List<StudentProjectRoleStaff> projectRoleStaffs = studentProjectRoleStaffMap.get(offerId);
                if (GeneralTool.isNotEmpty(projectRoleStaffs)){
                    List<StudentProjectRoleStaff> roleStaffs = projectRoleStaffs.stream().filter(p -> p.getFkStudentProjectRoleId().equals(roleByRoleKeyMap.get(studentProjectRoleKey).getId())).collect(Collectors.toList());

                    List<StaffCommissionAction> currentActions = commissionActions.stream().filter(a -> a.getFkStudentProjectRoleKey().equals(studentProjectRoleKey)
                            && a.getFkStaffCommissionStepKey().equals(fkStaffCommissionStepKey)).collect(Collectors.toList());

                    if (GeneralTool.isNotEmpty(currentActions)){
                        staffCommissionSpecificVo.setFkStaffId(currentActions.get(0).getFkStaffId());
                        staffCommissionSpecificVo.setFkStaffName(staffNamesMap.get(currentActions.get(0).getFkStaffId()));
                    } else if (GeneralTool.isNotEmpty(roleStaffs)){
                        StudentProjectRoleStaff studentProjectRoleStaff = roleStaffs.get(0);
                        staffCommissionSpecificVo.setFkStaffId(studentProjectRoleStaff.getFkStaffId());
                        staffCommissionSpecificVo.setFkStaffName(staffNamesMap.get(studentProjectRoleStaff.getFkStaffId()));
                    }
                }

                staffCommissionSpecificVo.setViewOrder(roleByRoleKeyMap.get(studentProjectRoleKey).getViewOrder());
                staffCommissionSpecificVos.add(staffCommissionSpecificVo);
            }
        }
        titleInfos = titleInfos.stream().sorted(Comparator.comparing(StaffCommissionDatasVo<String>::getViewOrder).reversed()).collect(Collectors.toList());
        settlementInfoVo.setTitleInfos(titleInfos);

        StaffCommissionTotalVo staffCommissionTotalVo = new StaffCommissionTotalVo();
        staffCommissionTotalVo.setStaffCommissionConfirmDtos(resultList);
        staffCommissionSpecificVos = staffCommissionSpecificVos.stream().sorted(Comparator.comparing(StaffCommissionSpecificVo::getViewOrder).reversed()).collect(Collectors.toList());
        staffCommissionTotalVo.setStaffCommissionSpecificDtos(staffCommissionSpecificVos);


        List<Map<String,Object>> settlementDateMapList = Lists.newArrayList();
        if (GeneralTool.isNotEmpty(settlementDates)){
            List<String> settlementDateList = Lists.newArrayList();
            for (String settlementDate : settlementDates) {
                String[] settlementDateArray = settlementDate.split(",");
                for (String settlementDateItem : settlementDateArray) {
                    if (!settlementDateList.contains(settlementDateItem)){
                        settlementDateList.add(settlementDateItem);
                    }
                }
            }
            String settlementDate = settlementDateList.stream().sorted().collect(Collectors.joining(","));

            Map<String, Object> map = Maps.newHashMap();
            map.put("type",1);
            map.put("statusName","已结算");
            map.put("settlementDate",settlementDate);
            settlementDateMapList.add(map);
        }

        if (GeneralTool.isNotEmpty(notSettlementDates)){
            List<String> notSettlementDateList = Lists.newArrayList();
            for (String notSettlementDate : notSettlementDates) {
                String[] notSettlementDateArray = notSettlementDate.split(",");
                for (String notSettlementDateItem : notSettlementDateArray) {
                    if (!notSettlementDateList.contains(notSettlementDateItem)){
                        notSettlementDateList.add(notSettlementDateItem);
                    }
                }
            }

            String notSettlementDate = notSettlementDateList.stream().sorted().collect(Collectors.joining(","));
                Map<String, Object> map = Maps.newHashMap();
                map.put("type",0);
                map.put("statusName","未结算");
                map.put("settlementDate",notSettlementDate);
                settlementDateMapList.add(map);
        }

        staffCommissionTotalVo.setSettlementDates(settlementDateMapList);

        settlementInfoVo.setStaffCommissionTotalDto(staffCommissionTotalVo);

        return settlementInfoVo;
    }


    /**
     * 获取总结算和设置名称
     * @param getStaffCommissionSpecificDtoAndSetNameContext
     * @return
     */
    private StaffCommissionSpecificVo getStaffCommissionSpecificDtoAndSetName(GetStaffCommissionSpecificDtoAndSetNameContext getStaffCommissionSpecificDtoAndSetNameContext) {
        //结算总金额map k,v - 角色key，总金额
        Map<String, BigDecimal> sumSettlementAmountMap = getStaffCommissionSpecificDtoAndSetNameContext.getSumSettlementAmountMap();
        //结算申请计划id map k,v - 角色key，申请计划ids
        Map<String, List<Long>> settlementItemIdMap = getStaffCommissionSpecificDtoAndSetNameContext.getSettlementItemIdMap();
        //项目角色实体 map k,v - 角色key，项目角色实体
        Map<String, StudentProjectRole> roleByRoleKeyMap = getStaffCommissionSpecificDtoAndSetNameContext.getRoleByRoleKeyMap();
        //申请计划id申请方案id map k,v - 申请计划id，申请方案id
        Map<Long, Long> itemToOfferIdMap = getStaffCommissionSpecificDtoAndSetNameContext.getItemToOfferIdMap();
        //员工id-项目成员和员工关系实体map k,v - 员工id，项目成员和员工关系实体
        Map<Long, List<StudentProjectRoleStaff>> studentProjectRoleStaffMap = getStaffCommissionSpecificDtoAndSetNameContext.getStudentProjectRoleStaffMap();
        //员工名称map k,v - 员工id，员工名称
        Map<Long, String> staffNamesMap = getStaffCommissionSpecificDtoAndSetNameContext.getStaffNamesMap();
        //角色key
        String studentProjectRoleKey = getStaffCommissionSpecificDtoAndSetNameContext.getStudentProjectRoleKey();
        //当前结算记录
        List<StaffCommissionAction> currentStepActions = getStaffCommissionSpecificDtoAndSetNameContext.getCurrentStepActions();
        //结算记录类型 0未结算/1已结算
        Integer type = getStaffCommissionSpecificDtoAndSetNameContext.getType();

        StaffCommissionSpecificVo sumStaffCommissionSpecificVo = new StaffCommissionSpecificVo();
        sumStaffCommissionSpecificVo.setAmount(GeneralTool.isEmpty(sumSettlementAmountMap.get(studentProjectRoleKey))?BigDecimal.ZERO: sumSettlementAmountMap.get(studentProjectRoleKey));
        sumStaffCommissionSpecificVo.setStudentProjectRoleKey(studentProjectRoleKey);
        sumStaffCommissionSpecificVo.setStudentProjectRoleName(roleByRoleKeyMap.get(studentProjectRoleKey).getRoleName());
        sumStaffCommissionSpecificVo.setType(type);
        sumStaffCommissionSpecificVo.setViewOrder(roleByRoleKeyMap.get(studentProjectRoleKey).getViewOrder());

        List<Long> notSettlementItemIds = settlementItemIdMap.get(studentProjectRoleKey);
        if (GeneralTool.isNotEmpty(notSettlementItemIds)){
            Set<Long> notSettlementOfferIds = Sets.newHashSet();
            for (Long itemId : notSettlementItemIds) {
                Long offerId = itemToOfferIdMap.get(itemId);
                notSettlementOfferIds.add(offerId);
            }
            if (GeneralTool.isNotEmpty(notSettlementOfferIds)){
                List<StudentProjectRoleStaff> projectRoleStaffs = Lists.newArrayList();
                for (Long settlementOfferId : notSettlementOfferIds) {
                    projectRoleStaffs.addAll(studentProjectRoleStaffMap.get(settlementOfferId));
                }

                Set<Long> settlementStaffSet = Sets.newHashSet();
                if (GeneralTool.isNotEmpty(currentStepActions)){
                    settlementStaffSet = currentStepActions.stream().filter(a-> a.getStatus().equals(type)).map(StaffCommissionAction::getFkStaffId).collect(Collectors.toSet());
                } else if (GeneralTool.isNotEmpty(projectRoleStaffs)){
                    List<StudentProjectRoleStaff> roleStaffs = projectRoleStaffs.stream().filter(p -> p.getFkStudentProjectRoleId().equals(roleByRoleKeyMap.get(studentProjectRoleKey).getId())).collect(Collectors.toList());
                    settlementStaffSet = roleStaffs.stream().map(StudentProjectRoleStaff::getFkStaffId).collect(Collectors.toSet());

                }
                if (settlementStaffSet.size() == 1 ){
                    sumStaffCommissionSpecificVo.setFkStaffId((long)settlementStaffSet.toArray()[0]);
                    sumStaffCommissionSpecificVo.setFkStaffName(staffNamesMap.get((long)settlementStaffSet.toArray()[0]));
                }else {
                    StringJoiner sj = new StringJoiner(",");
                    for (Long staffId : settlementStaffSet) {
                        sj.add(staffNamesMap.get(staffId));
                    }
                    sumStaffCommissionSpecificVo.setFkStaffName(sj.toString());
                }
            }
        }
        return sumStaffCommissionSpecificVo;
    }

    /**
     * 获取符合条件的提成规则
     * @param policyList
     * @param roleByRoleIdMap
     * @param studentOfferItemCommissionVo
     * @return
     */
    private List<StaffCommissionPolicy> doMatchPolicy(List<StaffCommissionPolicy> policyList, Map<Long, StudentProjectRole> roleByRoleIdMap, StudentOfferItemCommissionVo studentOfferItemCommissionVo) {
        List<StaffCommissionPolicy> suitablePolicies = policyList.stream().filter(staffCommissionPolicy -> {
            if (!staffCommissionPolicy.getFkAreaCountryId().equals(studentOfferItemCommissionVo.getFkAreaCountryId())) {
                return false;
            }
            if (GeneralTool.isNotEmpty(staffCommissionPolicy.getFkInstitutionId()) && !staffCommissionPolicy.getFkInstitutionId().equals(studentOfferItemCommissionVo.getFkInstitutionId())) {
                return false;
            }
            if (GeneralTool.isNotEmpty(staffCommissionPolicy.getFkStudentProjectRoleKey())
                    && ((GeneralTool.isNotEmpty(studentOfferItemCommissionVo.getFkStudentProjectRoleId())&&!staffCommissionPolicy.getFkStudentProjectRoleKey().equals(roleByRoleIdMap.get(studentOfferItemCommissionVo.getFkStudentProjectRoleId()).getRoleKey()))
                    ||GeneralTool.isEmpty(studentOfferItemCommissionVo.getFkStudentProjectRoleId()))) {
                return false;
            }
            if (GeneralTool.isNotEmpty(staffCommissionPolicy.getFkMajorLevelId()) && GeneralTool.isEmpty(studentOfferItemCommissionVo.getFkInstitutionCourseMajorLevelIds())) {
                return false;
            }
            if (GeneralTool.isNotEmpty(studentOfferItemCommissionVo.getFkInstitutionCourseMajorLevelIds())) {
                String[] levelIdArray = studentOfferItemCommissionVo.getFkInstitutionCourseMajorLevelIds().split(",");
                List<String> levelIds = Arrays.stream(levelIdArray).collect(Collectors.toList());
                if (GeneralTool.isNotEmpty(staffCommissionPolicy.getFkMajorLevelId()) && !levelIds.contains(staffCommissionPolicy.getFkMajorLevelId().toString())) {
                    return false;
                }
            }
            return true;
        }).collect(Collectors.toList());
        return suitablePolicies;
    }

    /**
     * 初始结算状态map
     * @param fkStaffCommissionStepKey
     * @param studentOfferItemIds
     * @param actions
     * @return
     */
    private Map<Long, Integer> initializeSettlementStatusListMap(String fkStaffCommissionStepKey, Set<Long> studentOfferItemIds, List<StaffCommissionAction> actions) {
        //结算状态map 0未确认/1已确认/2已结算
        Map<Long,Integer> settlementStatusListMap = Maps.newHashMap();
        for (Long studentOfferItemId : studentOfferItemIds) {
            settlementStatusListMap.put(studentOfferItemId,0);
        }
        if (ProjectKeyEnum.COMMISSION_STEP_OTHER_INSTITUTION.key.equals(fkStaffCommissionStepKey)
                ||ProjectKeyEnum.COMMISSION_STEP_OTHER_MAJOR.key.equals(fkStaffCommissionStepKey)
                ||ProjectKeyEnum.COMMISSION_STEP_ADDAPP.key.equals(fkStaffCommissionStepKey)){
            if (GeneralTool.isNotEmpty(actions)){
                Map<Long, List<StaffCommissionAction>> staffCommissionActionMap = actions.stream().filter(a->a.getFkStaffCommissionStepKey().equals(fkStaffCommissionStepKey)).collect(Collectors.groupingBy(StaffCommissionAction::getFkStudentOfferItemId));

                for (Long studentOfferItemId : staffCommissionActionMap.keySet()) {
                    List<StaffCommissionAction> commissionActions = staffCommissionActionMap.get(studentOfferItemId);
                    Set<Integer> statusSet = commissionActions.stream().map(StaffCommissionAction::getStatus).collect(Collectors.toSet());
                    if (statusSet.contains(0)){
                        settlementStatusListMap.put(studentOfferItemId,1);
                    }else if (statusSet.contains(1)){
                        settlementStatusListMap.put(studentOfferItemId,2);
                    }
                }
            }
        }
        return settlementStatusListMap;
    }

    /**
     * 获取学生的申请服务费
     * @param fkStudentId
     * @return
     */
    private BigDecimal getServiceFeeByStudentId(Long fkStudentId) {
        BigDecimal serviceFee = BigDecimal.ZERO;
        StudentServiceFeeType studentServiceFeeType = serviceTypeManagementService.getOne(Wrappers.lambdaQuery(StudentServiceFeeType.class).eq(StudentServiceFeeType::getTypeKey, ProjectKeyEnum.COMMISSION_STUDENT_SERVICE_FEE.key));
        if (GeneralTool.isNotEmpty(studentServiceFeeType)){
            StudentServiceFee studentServiceFee = studentServiceFeeService.getOne(Wrappers.lambdaQuery(StudentServiceFee.class)
                    .eq(StudentServiceFee::getFkStudentId, fkStudentId)
                    .eq(StudentServiceFee::getStatus, 1).eq(StudentServiceFee::getFkStudentServiceFeeTypeId, studentServiceFeeType.getId()));
            if (GeneralTool.isNotEmpty(studentServiceFee)){
                serviceFee = serviceFee.add(studentServiceFee.getAmount());
            }
        }
        return serviceFee;
    }

    /**
     * 设置名称
     * @param staffCommissionConfirmVos
     */
    private void setStaffCommissionConfirmDtosName(List<StaffCommissionConfirmVo> staffCommissionConfirmVos, Boolean isCheck) {
        if (GeneralTool.isEmpty(staffCommissionConfirmVos)){
            return;
        }
        //国家
        Set<Long> countryIds = staffCommissionConfirmVos.stream().map(StaffCommissionConfirmVo::getFkAreaCountryId).collect(Collectors.toSet());
        Map<Long, String> countryNameMap = institutionCenterClient.getCountryNamesByIds(countryIds).getData();
        //学校
        Set<Long> institutionIds = staffCommissionConfirmVos.stream().map(StaffCommissionConfirmVo::getFkInstitutionId).collect(Collectors.toSet());
        Map<Long, String> institutionNameMap = institutionCenterClient.getInstitutionNamesByIds(institutionIds).getData();
        //课程
        Set<Long> institutionCourseIds = staffCommissionConfirmVos.stream().map(StaffCommissionConfirmVo::getFkInstitutionCourseId).collect(Collectors.toSet());
        Map<Long, String> institutionCourseNameMap = institutionCenterClient.getInstitutionCourseNamesByIds(institutionCourseIds).getData();
        //课程等级
        List<String> majorLevelIdsStr = staffCommissionConfirmVos.stream().map(StaffCommissionConfirmVo::getFkInstitutionCourseMajorLevelIds)
                .filter(GeneralTool::isNotEmpty).collect(Collectors.toList());
        Set<Long> majorLevelIds = Sets.newHashSet();
        if(GeneralTool.isNotEmpty(majorLevelIdsStr)){
            for (String s : majorLevelIdsStr) {
                String[] majorLevelIdArray = s.split(",");
                Set<Long> majorLevelIdSet = Arrays.stream(majorLevelIdArray).map(Long::valueOf).collect(Collectors.toSet());
                majorLevelIds.addAll(majorLevelIdSet);
            }
        }
        Map<Long, String> majorLevelNameMap = institutionCenterClient.getMajorLevelNamesByIds(majorLevelIds).getData();
        //申请步骤
        Set<Long> offerItemStepIds = staffCommissionConfirmVos.stream().map(StaffCommissionConfirmVo::getStepId).collect(Collectors.toSet());
        List<StudentOfferItemStep> studentOfferItemSteps = studentOfferItemStepService.list(Wrappers.lambdaQuery(StudentOfferItemStep.class).in(StudentOfferItemStep::getId, offerItemStepIds));
        Map<Long, String> stepNameMap = studentOfferItemSteps.stream().collect(Collectors.toMap(StudentOfferItemStep::getId, StudentOfferItemStep::getStepName));

        Map<Long, Boolean> isInstitutionCommissionMap = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(isCheck)&&isCheck){
            List<StaffCommissionInstitution> staffCommissionInstitutions = staffCommissionInstitutionService.list(Wrappers.lambdaQuery(StaffCommissionInstitution.class)
                    .in(StaffCommissionInstitution::getFkInstitutionId, institutionIds).eq(StaffCommissionInstitution::getStatus, 1));
            if (GeneralTool.isNotEmpty(staffCommissionInstitutions)){
                isInstitutionCommissionMap = staffCommissionInstitutions.stream().collect(HashMap::new, (m, v) -> m.put(v.getFkInstitutionId(), true), HashMap::putAll);
            }
        }

        for (StaffCommissionConfirmVo staffCommissionConfirmVo : staffCommissionConfirmVos) {
            if (GeneralTool.isNotEmpty(staffCommissionConfirmVo.getFkAreaCountryId())){
                staffCommissionConfirmVo.setFkAreaCountryName(countryNameMap.get(staffCommissionConfirmVo.getFkAreaCountryId()));
            }
            if (GeneralTool.isNotEmpty(staffCommissionConfirmVo.getFkInstitutionId())){
                staffCommissionConfirmVo.setFkInstitutionName(institutionNameMap.get(staffCommissionConfirmVo.getFkInstitutionId()));
            }
            if (GeneralTool.isNotEmpty(staffCommissionConfirmVo.getFkInstitutionCourseId())&& staffCommissionConfirmVo.getFkInstitutionCourseId()!=-1L){
                staffCommissionConfirmVo.setFkInstitutionCourseName(institutionCourseNameMap.get(staffCommissionConfirmVo.getFkInstitutionCourseId()));
            }
            if (GeneralTool.isNotEmpty(staffCommissionConfirmVo.getFkInstitutionCourseMajorLevelIds())){
                String[] majorLevelIdArray = staffCommissionConfirmVo.getFkInstitutionCourseMajorLevelIds().split(",");
                StringJoiner sj = new StringJoiner(",");
                for (String s : majorLevelIdArray) {
                    String majorLevelName = majorLevelNameMap.get(Long.valueOf(s));
                    if (GeneralTool.isNotEmpty(majorLevelName)){
                        sj.add(majorLevelName);
                    }
                }
                staffCommissionConfirmVo.setFkInstitutionCourseMajorLevelName(sj.toString());
            }
            if (GeneralTool.isNotEmpty(staffCommissionConfirmVo.getStepId())){
                staffCommissionConfirmVo.setStepName(stepNameMap.get(staffCommissionConfirmVo.getStepId()));
            }

            if (GeneralTool.isNotEmpty(isCheck)&&isCheck){
                staffCommissionConfirmVo.setIsInstitutionCommission(isInstitutionCommissionMap.get(staffCommissionConfirmVo.getFkInstitutionId()) != null);
                staffCommissionConfirmVo.setIsInstitutionCommissionName(staffCommissionConfirmVo.getIsInstitutionCommission()?"是":"否");
            }
            Boolean isAddApp = staffCommissionConfirmVo.getIsAddApp() != null ? staffCommissionConfirmVo.getIsAddApp() : false;
            staffCommissionConfirmVo.setIsAddAppName(isAddApp?"是":"否");
        }
    }

    /**
     * 获取规则
     * @param studentOfferItemCommissionVos
     * @param fkStaffCommissionStepKey
     * @return
     */
    private List<StaffCommissionPolicy> getPoliciesByCountryAndCommissionStep(List<StudentOfferItemCommissionVo> studentOfferItemCommissionVos, String fkStaffCommissionStepKey) {
        if (GeneralTool.isEmpty(studentOfferItemCommissionVos)){
            return Collections.emptyList();
        }
        Long fkCompanyId = studentOfferItemCommissionVos.get(0).getFkCompanyId();
        Set<Long> countryIds = studentOfferItemCommissionVos.stream().map(StudentOfferItemCommissionVo::getFkAreaCountryId).filter(Objects::nonNull).collect(Collectors.toSet());
        List<StaffCommissionPolicy> staffCommissionPolicies = staffCommissionPolicyService.list(Wrappers.lambdaQuery(StaffCommissionPolicy.class)
                .eq(StaffCommissionPolicy::getFkCompanyId, fkCompanyId)
                .in(StaffCommissionPolicy::getFkAreaCountryId, countryIds)
                .eq(StaffCommissionPolicy::getFkStaffCommissionStepKey, fkStaffCommissionStepKey));
        if (GeneralTool.isEmpty(staffCommissionPolicies)){
            return Collections.emptyList();
        }
        return staffCommissionPolicies;
    }

    /**
     * 根据学生和提成步骤获取申请计划
     * @param fkStudentId
     * @param fkStaffCommissionStepKey
     * @return
     */
    private List<StudentOfferItemCommissionVo> getStudentOfferItemsByCommissionStepAndStudentKey(Long fkStudentId, String fkStaffCommissionStepKey) {
        if (GeneralTool.isEmpty(fkStudentId)||GeneralTool.isEmpty(fkStaffCommissionStepKey)){
            return Collections.emptyList();
        }
        StaffCommissionStep commissionStep = staffCommissionStepService.getOne(Wrappers.lambdaQuery(StaffCommissionStep.class).eq(StaffCommissionStep::getStepKey, fkStaffCommissionStepKey));
        //会过滤后续课程
        List<StudentOfferItemCommissionVo> studentOfferItemCommissionVos = this.baseMapper.getStudentOfferItemsByCommissionStepAndStudentKey(fkStudentId,commissionStep.getId());
        return studentOfferItemCommissionVos;
    }


}

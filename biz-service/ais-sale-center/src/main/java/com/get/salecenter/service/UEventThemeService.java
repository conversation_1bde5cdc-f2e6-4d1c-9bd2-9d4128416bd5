package com.get.salecenter.service;

import com.get.common.eunms.ErrorCodeEnum;
import com.get.common.result.Page;
import com.get.common.result.SearchBean;
import com.get.core.mybatis.base.BaseService;
import com.get.salecenter.dto.query.UEventThemeQueryDto;
import com.get.salecenter.entity.UEventTheme;
import com.get.salecenter.vo.UEventThemeVo;

import java.util.List;

public interface UEventThemeService extends BaseService<UEventTheme> {
    UEventThemeVo findUEventThemeById(Long id);

    Long addUEventTheme(UEventThemeQueryDto uEventThemeQueryDto);

    UEventThemeVo updateUEventTheme(UEventThemeQueryDto uEventThemeQueryDto);

    List<UEventThemeVo> getUEventThemes(UEventThemeQueryDto data, Page page);

    void deleteUEventTheme(Long id);

//    Boolean check(UEventThemeQueryDto uEventThemeQueryDto);
}

package com.get.salecenter.strategy.config;

import com.get.salecenter.strategy.CompanyConfigStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * @author: <PERSON>
 * @create: 2024/2/4 10:44
 * @verison: 1.0
 * @description:
 */
@Configuration
public class CompanyConfigStrategyConfig {

    @Autowired
    private List<CompanyConfigStrategy> companyConfigStrategies;


    public List<CompanyConfigStrategy> getCompanyConfigStrategies() {
        return companyConfigStrategies;
    }

}

package com.get.salecenter.service;

import com.get.common.result.FocExportVo;
import com.get.core.secure.StaffInfo;
import com.get.core.secure.UserInfo;
import com.get.salecenter.dto.*;
import com.get.salecenter.dto.query.StudentListQueryDto;
import com.get.salecenter.dto.query.StudentOfferItemListQueryDto;

import java.util.List;
import java.util.Map;

public interface AsyncExportService {

    /**
     * 导出学生列表
     *
     * @param studentListQueryDto
     * @param headerMap
     * @param user
     * @param locale
     * @param countryIds
     */
    void exportStudentList(StudentListQueryDto studentListQueryDto, List<FocExportVo> focExportVos, Map<String, String> headerMap, UserInfo user, String locale, List<Long> countryIds);

    /**
     * 导出学习计划
     *
     * @param studentOfferItemVo
     * @param headerMap
     * @param user
     * @param locale
     * @param secureCountryIds
     * @param secureInstitutionIds
     * @param securePermissionGroupInstitutionIds
     * @param secureStaffBoundBdIds
     */
    void exportStudyInfoList(StudentOfferItemCollectDto studentOfferItemVo, List<FocExportVo> focExportVos, Map<String, String> headerMap, UserInfo user, String locale, List<Long> secureCountryIds, List<Long> secureInstitutionIds, List<Long> securePermissionGroupInstitutionIds, List<Long> secureStaffBoundBdIds);

    /**
     * 导出核酸图片
     *
     * @param goproNucleicAcidUpdateDto
     */
    void exportAllImageInfo(GoproNucleicAcidUpdateDto goproNucleicAcidUpdateDto, Map<String, String> headerMap, UserInfo user, String locale);

    /**
     * 导出申请结算汇总
     *
     * @param studentOfferItemListVo
     * @param headerMap
     * @param user
     * @param locale
     */
    void doExportedCustomerListForMarket(StudentOfferItemListQueryDto studentOfferItemListVo, Map<String, String> headerMap, StaffInfo staffInfo, String locale, List<Long> countryIds);

    /**
     * 导出售前资源列表
     * @param clientDto
     * @param focExportVos
     * @param headerMap
     * @param staffInfo
     * @param locale
     */
    void exportClientResources(ClientDto clientDto, List<FocExportVo> focExportVos, Map<String, String> headerMap, StaffInfo staffInfo, String locale);

    /**
     * 导出KPI代理排名
     * @param headerMap
     * @param locale
     * @param user
     * @param kpiPlanStatisticsDto
     */
    void asyncExportKpiAgentRankExcel(Map<String, String> headerMap, String locale, UserInfo user, KpiPlanStatisticsDto kpiPlanStatisticsDto);
}

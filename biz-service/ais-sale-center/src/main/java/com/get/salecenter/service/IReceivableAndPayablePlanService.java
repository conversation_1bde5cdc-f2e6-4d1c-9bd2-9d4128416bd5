package com.get.salecenter.service;

import com.get.common.result.ResponseBo;
import com.get.salecenter.vo.ReceivableAndPayablePlanVo;
import com.get.salecenter.dto.IncentiveReceivableAndPayablePlanDto;
import com.get.salecenter.dto.ReceivableAndPayablePlanDto;

import java.util.List;
import java.util.Set;

/**
 * @author: Hardy
 * @create: 2021/12/8 12:02
 * @verison: 1.0
 * @description:
 */
public interface IReceivableAndPayablePlanService {

    /**
     * 同时新增应收/应付计划
     *
     * @param receivableAndPayablePlanDto
     * @return
     */
    void addReceivableAndPayablePlan(ReceivableAndPayablePlanDto receivableAndPayablePlanDto);

    /**
     * 奖励批量新增应收/应付计划
     *
     * @param incentiveReceivableAndPayablePlanDtoList
     * @return
     */
    void addReceivableAndPayablePlans(List<IncentiveReceivableAndPayablePlanDto> incentiveReceivableAndPayablePlanDtoList);


    /**
     * 编辑
     * @param receivableAndPayablePlanDto
     * @return
     */
    ResponseBo<ReceivableAndPayablePlanVo> update(ReceivableAndPayablePlanDto receivableAndPayablePlanDto);


    /**
     * 详情
     * @param receivablePlanId
     * @param payablePlanId
     * @return
     */
    ResponseBo<ReceivableAndPayablePlanVo> details(Long receivablePlanId, Long payablePlanId);

    /**
     * 删除应收应付
     * @param receivablePlanIds
     * @param payablePlanIds
     * @return
     */
    Boolean delete(Set<Long> receivablePlanIds, Set<Long> payablePlanIds);
}

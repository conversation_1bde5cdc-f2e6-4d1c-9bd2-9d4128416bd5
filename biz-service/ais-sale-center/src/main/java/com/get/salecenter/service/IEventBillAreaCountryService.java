package com.get.salecenter.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.get.salecenter.entity.EventBillAreaCountry;

import java.util.List;

/**
 * @author: Hardy
 * @create: 2022/5/9 16:45
 * @verison: 1.0
 * @description:
 */
public interface IEventBillAreaCountryService extends IService<EventBillAreaCountry> {

    /**
     * 批量新增
     *
     * @param eventBillAreaCountries
     * @return
     */
    Boolean batchAddByIds(List<EventBillAreaCountry> eventBillAreaCountries);

    List<EventBillAreaCountry> getEventBillAreaCountriesByCondition(LambdaQueryWrapper<EventBillAreaCountry> countryLambdaQueryWrapper);
}

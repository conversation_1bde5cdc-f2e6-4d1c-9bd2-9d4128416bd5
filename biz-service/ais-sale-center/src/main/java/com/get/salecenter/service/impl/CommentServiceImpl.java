package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.service.impl.GetServiceImpl;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.vo.StaffVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.dao.sale.CommentMapper;
import com.get.salecenter.vo.CommentVo;
import com.get.salecenter.entity.SaleComment;
import com.get.salecenter.service.ICommentService;
import com.get.salecenter.dto.CommentDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/11/5
 * @TIME: 15:44
 * @Description:
 **/
@Service
public class CommentServiceImpl extends GetServiceImpl<CommentMapper,SaleComment> implements ICommentService {

    @Resource
    private CommentMapper commentMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Override
    public List<CommentVo> datas(CommentDto commentDto, Page page) {
//        Example example = new Example(Comment.class);
//        Example.Criteria criteria = example.createCriteria();
        LambdaQueryWrapper<SaleComment> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(commentDto)) {
            if (GeneralTool.isNotEmpty(commentDto.getFkTableName())) {
//                criteria.andEqualTo("fkTableName", commentDto.getFkTableName());
                lambdaQueryWrapper.eq(SaleComment::getFkTableName, commentDto.getFkTableName());
            }
            if (GeneralTool.isNotEmpty(commentDto.getFkTableId())) {
//                criteria.andEqualTo("fkTableId", commentDto.getFkTableId());
                lambdaQueryWrapper.eq(SaleComment::getFkTableId, commentDto.getFkTableId());
            }
        }
//        example.orderBy("gmtCreate").desc();
        lambdaQueryWrapper.orderByDesc(SaleComment::getGmtCreate);
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
        IPage<SaleComment> pages = commentMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
        List<SaleComment> comments = pages.getRecords();
        page.setAll((int) pages.getTotal());
//        page.restPage(comments);
        List<CommentVo> commentVos = comments.stream().map(comment -> BeanCopyUtils.objClone(comment, CommentVo::new)).collect(Collectors.toList());

        //返回创建人员工id
        Set<String> createUsers = commentVos.stream().map(CommentVo::getGmtCreateUser).collect(Collectors.toSet());

        Result<List<StaffVo>> result = permissionCenterClient.getStaffByCreateUsers(createUsers);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            List<StaffVo> staffVos = result.getData();
            Map<String, Long> staffMap = new HashMap<>();
            for (StaffVo staffVo : staffVos) {
                staffMap.put(staffVo.getLoginId(), staffVo.getId());
            }
            for (CommentVo commentVo : commentVos) {
                commentVo.setFkStaffId(staffMap.get(commentVo.getGmtCreateUser()));
            }
        }

        return commentVos;
    }

    @Override
    public void addComment(SaleComment comment) {
        utilService.updateUserInfoToEntity(comment);
        commentMapper.insertSelective(comment);
    }

    @Override
    public Map<Long, List<String>> getComment(List<Long> ids, String typeKey) {
        if (GeneralTool.isEmpty(ids) || GeneralTool.isEmpty(typeKey)) {
            return Collections.emptyMap();
        }
        List<SaleComment> comments = commentMapper.selectList(Wrappers.<SaleComment>lambdaQuery().in(SaleComment::getFkTableId, ids).eq(SaleComment::getFkTableName, typeKey));
        if (GeneralTool.isNotEmpty(comments)) {
            Map<Long, List<SaleComment>> listMap = comments.stream().collect(Collectors.groupingBy(SaleComment::getFkTableId));
            Map<Long, List<String>> map = new HashMap<>(listMap.size());
            for (Map.Entry<Long, List<SaleComment>> entry : listMap.entrySet()) {
                map.put(entry.getKey(),entry.getValue().stream().map(SaleComment::getComment).collect(Collectors.toList()));
            }
            return map;
        }
        return Collections.emptyMap();
    }

    @Override
    public void updateComment(SaleComment comment) {
        utilService.updateUserInfoToEntity(comment);
        commentMapper.updateById(comment);
    }

    @Override
    public void delete(Long id) {
        commentMapper.deleteById(id);
    }


    /**
     * Author Cream
     * Description : //删除学生备注
     * Date 2023/5/11 14:20
     * Params:
     * Return
     */
    @Override
    public void deleteByStudentId(Long mergedStudentId) {
        if (GeneralTool.isNotEmpty(mergedStudentId)) {
            if (commentMapper.selectCount(Wrappers.<SaleComment>lambdaQuery().eq(SaleComment::getFkTableName, TableEnum.SALE_STUDENT.key)
                    .eq(SaleComment::getFkTableId, mergedStudentId)) > 0) {
                commentMapper.delete(Wrappers.<SaleComment>lambdaQuery().eq(SaleComment::getFkTableName, TableEnum.SALE_STUDENT.key)
                        .eq(SaleComment::getFkTableId, mergedStudentId));
            }
        }
    }

    /**
     * 合并学生备注
     * @param mergedStudentId
     * @param targetStudentId
     */
    @Override
    public void mergeData(Long mergedStudentId, Long targetStudentId) {
        List<SaleComment> saleComments = commentMapper.selectList(Wrappers.<SaleComment>lambdaQuery().eq(SaleComment::getFkTableName, TableEnum.SALE_STUDENT.key)
                .eq(SaleComment::getFkTableId, mergedStudentId));
        if (GeneralTool.isNotEmpty(saleComments)) {
            saleComments.forEach(s->s.setFkTableId(targetStudentId));
            updateBatchById(saleComments);
        }
    }
}

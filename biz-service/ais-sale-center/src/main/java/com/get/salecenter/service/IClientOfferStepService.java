package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.salecenter.vo.ClientOfferStepVo;
import com.get.salecenter.dto.ClientOfferStepAddDto;
import com.get.salecenter.dto.ClientOfferStepListDto;
import com.get.salecenter.dto.ClientOfferStepDto;

import java.util.List;

/**
 * @author: Hardy
 * @create: 2023/12/13 10:37
 * @verison: 1.0
 * @description:
 */
public interface IClientOfferStepService {
    /**
     * 新增
     * @param clientOfferStepAddDto
     * @return
     */
    Long addClientOfferStep(ClientOfferStepAddDto clientOfferStepAddDto);

    /**
     * 删除
     * @param id
     */
    void delete(Long id);

    /**
     * 详情
     * @param id
     * @return
     */
    ClientOfferStepVo findClientOfferStepById(Long id);

    /**
     * 列表
     * @param clientOfferStepListDto
     * @param page
     * @return
     */
    List<ClientOfferStepVo> getClientOfferSteps(ClientOfferStepListDto clientOfferStepListDto, Page page);

    /**
     * 移动顺序
     * @param clientOfferStepDtos
     */
    void movingOrder(List<ClientOfferStepDto> clientOfferStepDtos);

    /**
     * 更新
     * @param clientOfferStepAddDto
     * @return
     */
    ClientOfferStepVo updateClientOfferStep(ClientOfferStepAddDto clientOfferStepAddDto);

    /**
     * 步骤下拉
     * @return
     */
    List<ClientOfferStepVo> getClientOfferStepSelect();
}

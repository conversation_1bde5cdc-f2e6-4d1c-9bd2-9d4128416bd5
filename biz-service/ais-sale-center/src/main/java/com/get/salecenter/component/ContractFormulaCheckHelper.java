package com.get.salecenter.component;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.entity.ContractFormula;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.salecenter.dao.sale.StudentOfferItemMapper;
import com.get.salecenter.dao.sale.StudentOfferItemPreInstitutionGroupMapper;
import com.get.salecenter.dao.sale.StudentOfferItemPreInstitutionMapper;
import com.get.salecenter.dao.sale.StudentOfferItemPreMajorLevelMapper;
import com.get.salecenter.entity.Student;
import com.get.salecenter.entity.StudentOfferItem;
import com.get.salecenter.entity.StudentOfferItemPreInstitution;
import com.get.salecenter.entity.StudentOfferItemPreInstitutionGroup;
import com.get.salecenter.entity.StudentOfferItemPreMajorLevel;
import com.get.salecenter.service.IReceivablePlanService;
import com.google.common.collect.Lists;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 检查合同公式条件
 *
 * <AUTHOR>
 * @date 2021/7/19 12:02
 */
@Component
public class ContractFormulaCheckHelper {
    @Resource
    private StudentOfferItemMapper offerItemMapper;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    @Lazy
    private IReceivablePlanService receivablePlanService;
    @Resource
    private StudentOfferItemPreInstitutionMapper studentOfferItemPreInstitutionMapper;
    @Resource
    private StudentOfferItemPreInstitutionGroupMapper studentOfferItemPreInstitutionGroupMapper;
    @Resource
    private StudentOfferItemPreMajorLevelMapper studentOfferItemPreMajorLevelMapper;

    /**
     * 学习计划、合同公式匹配排查
     *
     * @Date 14:29 2021/7/19
     * <AUTHOR>
     */
    public void checkContractFormula(String num, Long contractFormulaId) {
//        Example example = new Example(StudentOfferItem.class);
//        example.createCriteria().andEqualTo("num", num);
//        List<StudentOfferItem> studentOfferItems = offerItemMapper.selectByExample(example);
        List<StudentOfferItem> studentOfferItems = offerItemMapper.selectList(Wrappers.<StudentOfferItem>lambdaQuery().eq(StudentOfferItem::getNum, num));
        if (GeneralTool.isEmpty(studentOfferItems)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        StudentOfferItem studentOfferItem = studentOfferItems.get(0);
        Student student = receivablePlanService.getStudent(studentOfferItem);
        StudentOfferItem commonStudentOfferItem = BeanCopyUtils.objClone(studentOfferItem, StudentOfferItem::new);
        //合同公式初步匹配
        String msg = "";
        Long fkInstitutionCourseId = commonStudentOfferItem.getFkInstitutionCourseId();
        Result<String> msgResult = institutionCenterClient.checkContractFormula(student.getFkCompanyId(), student.getFkAreaCountryId(), fkInstitutionCourseId, contractFormulaId);
        if (msgResult.isSuccess() && GeneralTool.isNotEmpty(msgResult.getData())) {
            msg = msgResult.getData();
        }
        if (GeneralTool.isNotEmpty(msg)) {
            throw new GetServiceException(msg);
        }
        ContractFormula contractFormula = new ContractFormula();
        Result<ContractFormula> contractFormulaResult = institutionCenterClient.getContractFormulaByFormulaId(contractFormulaId);
        if (contractFormulaResult.isSuccess() && GeneralTool.isNotEmpty(contractFormulaResult.getData())) {
            contractFormula = contractFormulaResult.getData();
        }
        //业务场景筛选
        String studentConditionType = student.getConditionType();
        String studentOfferItemConditionType = studentOfferItem.getConditionType();
        if (GeneralTool.isNotEmpty(contractFormula.getConditionType())) {
            switch (Objects.requireNonNull(ProjectExtraEnum.getEnum(Integer.parseInt(contractFormula.getConditionType()), ProjectExtraEnum.CONDITION_TYPE))) {
                case TRANSFER_AGENT_STUDENT:
//                case PERCENTAGE_OF_SCHOLARSHIP_FEES:
//                    if (!contractFormula.getConditionType().equals(studentConditionType)) {
//                        throw new GetServiceException("合同公式业务场景不匹配");
//                    }
//                    break;
                case READ_ONLY_PHASE_ONE:
                case READ_ONLY_PHASE_TWO:
                case READ_ONLY_PHASE_THREE:
                case READ_ONLY_PHASE_FOUR:
                    if (!contractFormula.getConditionType().equals(studentOfferItemConditionType)) {
                        throw new GetServiceException(LocaleMessageUtils.getMessage("contract_formula_read_only"));
                    }
                    break;
                default:
                    throw new GetServiceException(LocaleMessageUtils.getMessage("student_condition_type_null"));
            }
        }
        //前置条件筛选
        //合同公式前置学校
        List<Long> contractFormulaPreInstitutionIds = null;
        Result<List<Long>> contractFormulaPreInstitutionIdsResult = institutionCenterClient.getContractFormulaPreInstitutionByContractFormulaId(contractFormula.getId());
        if (contractFormulaPreInstitutionIdsResult.isSuccess() && GeneralTool.isNotEmpty(contractFormulaPreInstitutionIdsResult.getData())) {
            contractFormulaPreInstitutionIds = contractFormulaPreInstitutionIdsResult.getData();
        }

//        example.clear();
//        example = new Example(StudentOfferItemPreInstitution.class);
//        example.createCriteria().andEqualTo("fkStudentOfferItemId", studentOfferItem.getId());
        //学习计划前置学校条件
        List<StudentOfferItemPreInstitution> studentOfferItemPreInstitutions = studentOfferItemPreInstitutionMapper.selectList(Wrappers.<StudentOfferItemPreInstitution>lambdaQuery().eq(StudentOfferItemPreInstitution::getFkStudentOfferItemId, studentOfferItem.getId()));
        if (GeneralTool.isNotEmpty(contractFormulaPreInstitutionIds)) {
            boolean contractFormulaPreInstitutionFlag = false;
            for (Long contractFormulaPreInstitutionId : contractFormulaPreInstitutionIds) {
                if (GeneralTool.isNotEmpty(studentOfferItemPreInstitutions)) {
                    for (StudentOfferItemPreInstitution studentOfferItemPreInstitution : studentOfferItemPreInstitutions) {
                        if (contractFormulaPreInstitutionId.equals(studentOfferItemPreInstitution.getFkInstitutionId())) {
                            contractFormulaPreInstitutionFlag = true;
                        }
                    }
                } else {
                    contractFormulaPreInstitutionFlag = false;
                }
            }
            if (!contractFormulaPreInstitutionFlag) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("pre_school_conditions_do_not_match"));
            }

            //如果合同公式没有限制前置学校，但是学生有前置学校条件 -->不匹配
        } else if (GeneralTool.isNotEmpty(studentOfferItemPreInstitutions)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("students_have_prerequisite_school_conditions"));
        }
        //合同公式前置集团
        List<Long> contractFormulaPreInstitutionGroupIds = Lists.newArrayList();
        Result<List<Long>> contractFormulaPreInstitutionGroupIdsResult = institutionCenterClient.getPreInstitutionGroupByContractFormulaId(contractFormula.getId());
        if (contractFormulaPreInstitutionGroupIdsResult.isSuccess() && GeneralTool.isNotEmpty(contractFormulaPreInstitutionGroupIdsResult.getData())) {
            contractFormulaPreInstitutionGroupIds.addAll(contractFormulaPreInstitutionGroupIdsResult.getData());
        }
//        example.clear();
//        example = new Example(StudentOfferItemPreInstitutionGroup.class);
//        example.createCriteria().andEqualTo("fkStudentOfferItemId", studentOfferItem.getId());
        //学习计划前置集团条件
        List<StudentOfferItemPreInstitutionGroup> studentOfferItemPreInstitutionGroups = studentOfferItemPreInstitutionGroupMapper.selectList(Wrappers.<StudentOfferItemPreInstitutionGroup>lambdaQuery().eq(StudentOfferItemPreInstitutionGroup::getFkStudentOfferItemId, studentOfferItem.getId()));
        if (GeneralTool.isNotEmpty(contractFormulaPreInstitutionGroupIds)) {
            boolean contractFormulaPreInstitutionGroupFlag = false;
            for (Long contractFormulaPreInstitutionGroupId : contractFormulaPreInstitutionGroupIds) {
                if (GeneralTool.isNotEmpty(studentOfferItemPreInstitutionGroups)) {
                    for (StudentOfferItemPreInstitutionGroup studentOfferItemPreInstitutionGroup : studentOfferItemPreInstitutionGroups) {
                        if (contractFormulaPreInstitutionGroupId.equals(studentOfferItemPreInstitutionGroup.getFkInstitutionGroupId())) {
                            contractFormulaPreInstitutionGroupFlag = true;
                        }
                    }
                } else {
                    contractFormulaPreInstitutionGroupFlag = false;
                }
            }
            if (!contractFormulaPreInstitutionGroupFlag) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("pre_group_mismatch"));
            }

            //如果合同公式没有限制前置集团，但是学生有前置集团条件 -->不匹配
        } else if (GeneralTool.isNotEmpty(studentOfferItemPreInstitutionGroups)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("students_have_prerequisite_group_conditions"));
        }

        //合同公式前置课程等级
        List<Long> preMajorLevelIds = Lists.newArrayList();
        Result<List<Long>> preMajorLevelIdsResult = institutionCenterClient.getPreMajorLevelByContractFormulaId(contractFormula.getId());
        if (preMajorLevelIdsResult.isSuccess() && GeneralTool.isNotEmpty(preMajorLevelIdsResult.getData())) {
            preMajorLevelIds.addAll(preMajorLevelIdsResult.getData());
        }

//        example.clear();
//        example = new Example(StudentOfferItemPreMajorLevel.class);
//        example.createCriteria().andEqualTo("fkStudentOfferItemId", studentOfferItem.getId());
        //学习计划前置课程等级条件
        List<StudentOfferItemPreMajorLevel> studentOfferItemPreMajorLevels = studentOfferItemPreMajorLevelMapper.selectList(Wrappers.<StudentOfferItemPreMajorLevel>lambdaQuery().eq(StudentOfferItemPreMajorLevel::getFkStudentOfferItemId, studentOfferItem.getId()));
        if (GeneralTool.isNotEmpty(preMajorLevelIds)) {
            boolean preMajorLevelFlag = false;
            for (Long preMajorLevelId : preMajorLevelIds) {
                if (GeneralTool.isNotEmpty(studentOfferItemPreMajorLevels)) {
                    StudentOfferItemPreMajorLevel studentOfferItemPreMajorLevel = studentOfferItemPreMajorLevels.get(0);
                    if (preMajorLevelId.equals(studentOfferItemPreMajorLevel.getFkMajorLevelId())) {
                        preMajorLevelFlag = true;
                    }
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("study_plan_has_no_prerequisite_course_conditions"));
                }
            }
            if (!preMajorLevelFlag) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("pre_course_mismatch"));
            }

            //如果合同公式没有限制前置课程等级，但是学生有前置课程等级 -->不匹配
        } else if (GeneralTool.isNotEmpty(studentOfferItemPreMajorLevels)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("students_have_prerequisite_course_requirements"));
        }

//        //无统计类型
//        if (GeneralTool.isEmpty(contractFormula.getCountType())) {
//            return;
//            //按累计学生人数统计
//        } else if (contractFormula.getCountType().equals(ProjectExtraEnum.NUMBER_OF_STUDENTS.key)) {
//            ContractFormulaFeignVo contractFormulaConfig = null;
//            Result<ContractFormulaFeignVo> contractFormulaFeignDtoResult = institutionCenterClient.getContractFormulaConfigByContractFormulaId(contractFormula.getId());
//            if(contractFormulaFeignDtoResult.isSuccess() && GeneralTool.isNotEmpty(contractFormulaFeignDtoResult.getData()))
//            {
//                contractFormulaConfig = contractFormulaFeignDtoResult.getData();
//            }
//            //符合合公式条件的学习计划
//            List<Long> studentOfferIdList = studentOfferItemMapper.getOferItemByContractFormula(contractFormulaConfig).stream().map(StudentOfferItem::getFkStudentOfferId).collect(Collectors.toList());
//            BigDecimal studentCount;
//            if (GeneralTool.isNotEmpty(studentOfferIdList)) {
//                studentCount = new BigDecimal(studentOfferMapper.getStudentCountByOfferIds(studentOfferIdList));
//            } else {
//                studentCount = new BigDecimal(0);
//            }
//            if (!(studentCount.compareTo(contractFormula.getCountValeMin()) > -1 && studentCount.compareTo(contractFormula.getCountValeMax()) < 1)) {
//                throw new GetServiceException("累计学生数统计不匹配");
//            }
//            //按学费统计
//        } else if (contractFormula.getCountType().equals(ProjectExtraEnum.FEE_OF_STUDENTS.key)) {
//            if (!(contractFormula.getCountValeMin().compareTo(studentOfferItem.getTuitionAmount()) < 1 && contractFormula.getCountValeMax().compareTo(studentOfferItem.getTuitionAmount()) > -1)) {
//                throw new GetServiceException("学费统计不匹配");
//            }
//
//            //课程长度（周）
//        } else if (contractFormula.getCountType().equals(ProjectExtraEnum.COURSE_DURATION_WEEK.key) && (GeneralTool.isNotEmpty(studentOfferItem.getDurationType()) && 0 == studentOfferItem.getDurationType())) {
//            if (!(contractFormula.getCountValeMin().compareTo(studentOfferItem.getDuration()) < 1 && contractFormula.getCountValeMax().compareTo(studentOfferItem.getDuration()) > -1)) {
//                throw new GetServiceException("课程长度（周）不匹配");
//            }
//            //课程长度（月）
//        } else if (contractFormula.getCountType().equals(ProjectExtraEnum.COURSE_DURATION_MONTH.key) && (GeneralTool.isNotEmpty(studentOfferItem.getDurationType()) && 1 == studentOfferItem.getDurationType())) {
//            if (!(contractFormula.getCountValeMin().compareTo(studentOfferItem.getDuration()) < 1 && contractFormula.getCountValeMax().compareTo(studentOfferItem.getDuration()) > -1)) {
//                throw new GetServiceException("课程长度（月）不匹配");
//            }
//            //课程长度（年）
//        } else if (contractFormula.getCountType().equals(ProjectExtraEnum.COURSE_DURATION_YEAR.key) && (GeneralTool.isNotEmpty(studentOfferItem.getDurationType()) && 2 == studentOfferItem.getDurationType())) {
//            if (!(contractFormula.getCountValeMin().compareTo(studentOfferItem.getDuration()) < 1 && contractFormula.getCountValeMax().compareTo(studentOfferItem.getDuration()) > -1)) {
//                throw new GetServiceException("课程长度（年）不匹配");
//            }
//            //课程长度(学期)
//        } else if (contractFormula.getCountType().equals(ProjectExtraEnum.COURSE_DURATION_SEMESTER.key) && (GeneralTool.isNotEmpty(studentOfferItem.getDurationType()) && 3 == studentOfferItem.getDurationType())) {
//            if (!(contractFormula.getCountValeMin().compareTo(studentOfferItem.getDuration()) < 1 && contractFormula.getCountValeMax().compareTo(studentOfferItem.getDuration()) > -1)) {
//                throw new GetServiceException("课程长度(学期)不匹配");
//            }
//            //累计学费
//        } else if (contractFormula.getCountType().equals(ProjectExtraEnum.ACCUMULATED_TUITION.key)) {
//            ContractFormulaFeignVo contractFormulaConfig = null;
//            Result<ContractFormulaFeignVo> contractFormulaFeignDtoResult = institutionCenterClient.getContractFormulaConfigByContractFormulaId(contractFormula.getId());
//            if(contractFormulaFeignDtoResult.isSuccess() && GeneralTool.isNotEmpty(contractFormulaFeignDtoResult.getData()))
//            {
//                contractFormulaConfig = contractFormulaFeignDtoResult.getData();
//                List<StudentOfferItem> studentOfferItemList = studentOfferItemMapper.getOferItemByContractFormula(contractFormulaConfig);
//                //累计学费
//                BigDecimal totalFee = new BigDecimal(0);
//                for (StudentOfferItem offerItem : studentOfferItemList) {
//                    totalFee = totalFee.add(offerItem.getTuitionAmount());
//                }
//                if (!(totalFee.compareTo(contractFormula.getCountValeMin()) > -1 && totalFee.compareTo(contractFormula.getCountValeMax()) < 1)) {
//                    throw new GetServiceException("累计学费统计不匹配");
//                }
//            }
//
//        }
    }
}

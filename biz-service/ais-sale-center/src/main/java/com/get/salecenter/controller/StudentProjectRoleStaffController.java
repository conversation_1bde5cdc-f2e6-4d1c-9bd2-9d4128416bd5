package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.result.UpdateResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.salecenter.vo.StudentProjectRoleStaffVo;
import com.get.salecenter.service.IStudentProjectRoleStaffService;
import com.get.salecenter.dto.StudentProjectRoleStaffDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @DATE: 2020/11/16
 * @TIME: 15:05
 * @Description:
 **/
@Api(tags = "绑定项目成员管理")
@RestController
@RequestMapping("sale/studentProjectRoleStaff")
public class StudentProjectRoleStaffController {
    @Resource
    private IStudentProjectRoleStaffService roleStaffService;

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentVo>
     * @Description: 修改信息
     * @Param [studentVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/绑定项目成员管理/更新学生")
    @PostMapping("update")
    public ResponseBo<StudentProjectRoleStaffVo> update(@RequestBody  @Validated(StudentProjectRoleStaffDto.Update.class)
                                                        StudentProjectRoleStaffDto projectRoleStaffVo) {
        return UpdateResponseBo.ok(roleStaffService.updateProjectRoleStaff(projectRoleStaffVo));
    }

    @ApiOperation(value = "获取绑定项目成员管理日志", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/绑定项目成员管理/获取绑定项目成员管理日志")
    @GetMapping("getStudentProjectRoleStaffList")
    public ListResponseBo<StudentProjectRoleStaffVo> getStudentProjectRoleStaffList(@RequestParam("fkStudentOfferId") Long fkStudentOfferId){
        return new ListResponseBo<>(roleStaffService.getStudentProjectRoleStaffList(fkStudentOfferId));
    }


}

package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.vo.AnnualHotelReservationVo;
import com.get.salecenter.vo.ConventionHotelVo;
import com.get.salecenter.vo.ConventionRegistrationVo;
import com.get.salecenter.service.IAnnualHotelReservationService;
import com.get.salecenter.dto.AnnualHotelReservationDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2021/6/21 18:22
 * @verison: 1.0
 * @description: 年会酒店预订控制器
 */
@Api(tags = "峰会参展人员预定表单")
@RestController
@RequestMapping("sale/AnnualHotelReservation")
public class AnnualHotelReservationController {

    @Resource
    private IAnnualHotelReservationService annualHotelReservationService;

//    /**
//     * 新增信息
//     *
//     * @param request
//     * @return
//     * @
//     */
//    @VerifyPermission(IsVerify = false)
//    @VerifyLogin(IsVerify = false)
//    @ApiOperation(value = "新增接口", notes = "")
//    @PostMapping("add")
//    public ResponseBo add(HttpServletRequest request)  {
//        String data = request.getParameter("data");
//        JSONObject jsonData = JSONObject.parseObject(data);
//        JSONObject annualHotelReservationVoJSON = jsonData.getJSONObject("annualHotelReservationVo");
//        AnnualHotelReservationDto annualHotelReservationVo = annualHotelReservationVoJSON.toJavaObject(AnnualHotelReservationDto.class);
//        annualHotelReservationService.addAnnualHotelReservation(annualHotelReservationVo);
//        return SaveResponseBo.ok();
//    }


    /**
     * 批量新增信息
     *
     * @param annualHotelReservationDtos
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "批量新增接口", notes = "")
    @PostMapping("batchAdd")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/峰会/gea峰会报名")
    public ResponseBo batchAdd(@RequestBody List<AnnualHotelReservationDto> annualHotelReservationDtos) {
        //回执码获取峰会活动
//        String data = request.getParameter("data");
//        JSONObject jsonData = JSONObject.parseObject(data);
//        JSONArray voList = jsonData.getJSONArray("annualHotelReservationDtos");
//        List<AnnualHotelReservationDto> annualHotelReservationDtos = JSONArray.parseArray(voList.toJSONString(), AnnualHotelReservationDto.class);
        annualHotelReservationService.batchAddAnnualHotelReservation(annualHotelReservationDtos);
        return SaveResponseBo.ok();
    }

    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "房型下拉框", notes = "")
    @GetMapping("getConventionHotel")
    public ResponseBo getConventionHotel() {
        List<ConventionHotelVo> datas = annualHotelReservationService.getConventionHotel();
        return new ListResponseBo<>(datas);
    }

    /**
     * 机构名下拉框
     *
     * @param receiptCode
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "机构名下拉框", notes = "")
    @GetMapping("getInstitutionNameSelect/{receiptCode}")
    public ResponseBo getInstitutionNameSelect(@PathVariable("receiptCode") String receiptCode, @RequestParam(value = "fkConventionId", required = true) Long fkConventionId) {
        List<ConventionRegistrationVo> datas = annualHotelReservationService.getInstitutionNameSelect(receiptCode, fkConventionId);
        return new ListResponseBo<>(datas);
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.AnnualHotelReservationVo>
     * @Description :详情
     * @Param [annualConferenceRegistrationVo]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "配置详情接口", notes = "")
    @GetMapping("getAnnualConferenceRegistrationDto")
    public ResponseBo<AnnualHotelReservationVo> getAnnualConferenceRegistrationDto(@RequestParam("receiptCode") String receiptCode) {
        List<AnnualHotelReservationVo> data = annualHotelReservationService.getAnnualHotelReservationDto(receiptCode);
        return new ListResponseBo<>(data);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.AnnualHotelReservationVo>
     * @Description :删除接口
     * @Param [annualConferenceRegistrationVo]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "删除接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/峰会/gea报名表单删除峰会人员")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id,@RequestParam("receiptCode")String receiptCode) {
        annualHotelReservationService.deleteConventionPerson(id,receiptCode);
        return DeleteResponseBo.ok();
    }

}

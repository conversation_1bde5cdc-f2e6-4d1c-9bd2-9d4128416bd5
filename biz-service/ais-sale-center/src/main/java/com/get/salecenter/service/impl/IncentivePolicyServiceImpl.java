package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.tool.utils.StringUtil;
import com.get.financecenter.feign.IFinanceCenterClient;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.dao.sale.MIncentivePolicyMapper;
import com.get.salecenter.dao.sale.MediaAndAttachedMapper;
import com.get.salecenter.vo.*;
import com.get.salecenter.entity.*;
import com.get.salecenter.service.*;
import com.get.salecenter.utils.MyStringUtils;
import com.get.salecenter.dto.CommentDto;
import com.get.salecenter.dto.IncentivePolicyDto;
import com.get.salecenter.dto.MediaAndAttachedDto;
import com.get.salecenter.dto.query.IncentivePolicyQueryDto;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023-03-13
 */
@Service
public class IncentivePolicyServiceImpl extends BaseServiceImpl<MIncentivePolicyMapper, IncentivePolicy> implements IIncentivePolicyService {

    @Resource
    private MIncentivePolicyMapper incentivePolicyMapper;
    @Resource
    private UtilService utilService;
    @Resource
    @Lazy
    private IIncentivePolicyStudentOfferItemService incentivePolicyStudentOfferItemService;
    @Resource
    @Lazy
    private IStudentOfferItemStepService studentOfferItemStepService;
    @Resource
    @Lazy
    private IIncentiveRewardService incentiveRewardService;
    @Resource
    private IMediaAndAttachedService attachedService;
    @Resource
    private ICommentService commentService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    private IFinanceCenterClient financeCenterClient;
    @Resource
    private MediaAndAttachedMapper attachedMapper;

    @Override
    public IncentivePolicyVo selectById(Long id) {
        IncentivePolicyVo incentivePolicyVo = null;
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        IncentivePolicy incentivePolicy = this.getById(id);
        if(GeneralTool.isEmpty(incentivePolicy))
        {
            return null;
        }
        incentivePolicyVo = BeanCopyUtils.objClone(incentivePolicy, IncentivePolicyVo::new);

        //处理子项详情名称： 符合奖励状态/主课：课程，类型，等级/子课：课程，类型，等级/后续课程：课程，类型，等级，学校提供商，学校/应收币种/应付币种/其他奖品奖励
        //课程
        Set<Long> courseIds = new HashSet<>();
        if(GeneralTool.isNotEmpty(incentivePolicyVo.getConditionJson()) && GeneralTool.isNotEmpty(incentivePolicyVo.getConditionJson().getMainCourseIds()))
        {
            courseIds.addAll(incentivePolicyVo.getConditionJson().getMainCourseIds());
        }
        if(GeneralTool.isNotEmpty(incentivePolicyVo.getConditionJson()) && GeneralTool.isNotEmpty(incentivePolicyVo.getConditionJson().getSubCourseIds()))
        {
            courseIds.addAll(incentivePolicyVo.getConditionJson().getSubCourseIds());
        }
        if(GeneralTool.isNotEmpty(incentivePolicyVo.getConditionJson()) && GeneralTool.isNotEmpty(incentivePolicyVo.getConditionJson().getLaterUpCourseIds()))
        {
            courseIds.addAll(incentivePolicyVo.getConditionJson().getLaterUpCourseIds());
        }
        if(GeneralTool.isNotEmpty(courseIds))
        {
            Result<Map<Long, String>> result3 = institutionCenterClient.getCourseNameByIds(courseIds);
            Map<Long, String> institutionCourseNameMap = result3.getData();
            if(GeneralTool.isNotEmpty(institutionCourseNameMap))
            {
                //写入课程名称
                if(GeneralTool.isNotEmpty(incentivePolicyVo.getConditionJson().getMainCourseIds()))
                {
                    List<Long> mainCourseIds = incentivePolicyVo.getConditionJson().getMainCourseIds();
                    incentivePolicyVo.getConditionJson().setMainCourseNames(institutionCourseNameMap.entrySet().stream()
                            .filter(entry -> mainCourseIds.contains(entry.getKey()))
                            .map(Map.Entry::getValue)
                            .collect(Collectors.toList()));
                }
                if(GeneralTool.isNotEmpty(incentivePolicyVo.getConditionJson().getSubCourseIds()))
                {
                    List<Long> subCourseIds = incentivePolicyVo.getConditionJson().getSubCourseIds();
                    incentivePolicyVo.getConditionJson().setSubCourseNames(institutionCourseNameMap.entrySet().stream()
                            .filter(entry -> subCourseIds.contains(entry.getKey()))
                            .map(Map.Entry::getValue)
                            .collect(Collectors.toList()));
                }
                if(GeneralTool.isNotEmpty(incentivePolicyVo.getConditionJson().getLaterUpCourseIds()))
                {
                    List<Long> laterCourseIds = incentivePolicyVo.getConditionJson().getLaterUpCourseIds();
                    incentivePolicyVo.getConditionJson().setLaterUpCourseNames(institutionCourseNameMap.entrySet().stream()
                            .filter(entry -> laterCourseIds.contains(entry.getKey()))
                            .map(Map.Entry::getValue)
                            .collect(Collectors.toList()));
                }
            }
        }
        //类型
        Set<Long> courseTypeIds = new HashSet<>();
        if(GeneralTool.isNotEmpty(incentivePolicyVo.getConditionJson()) && GeneralTool.isNotEmpty(incentivePolicyVo.getConditionJson().getMainCourseTypeIds()))
        {
            courseTypeIds.addAll(incentivePolicyVo.getConditionJson().getMainCourseTypeIds());
        }
        if(GeneralTool.isNotEmpty(incentivePolicyVo.getConditionJson()) && GeneralTool.isNotEmpty(incentivePolicyVo.getConditionJson().getSubCourseTypeIds()))
        {
            courseTypeIds.addAll(incentivePolicyVo.getConditionJson().getSubCourseTypeIds());
        }
        if(GeneralTool.isNotEmpty(incentivePolicyVo.getConditionJson()) && GeneralTool.isNotEmpty(incentivePolicyVo.getConditionJson().getLaterUpCourseTypeIds()))
        {
            courseTypeIds.addAll(incentivePolicyVo.getConditionJson().getLaterUpCourseTypeIds());
        }
        if(GeneralTool.isNotEmpty(courseTypeIds))
        {
            Result<Map<Long, String>> result = institutionCenterClient.getCourseGroupTypeNameByIds(courseTypeIds);
            Map<Long, String> courseTypeNameMap = result.getData();
            if(GeneralTool.isNotEmpty(courseTypeNameMap))
            {
                //写入课程类型名称
                if(GeneralTool.isNotEmpty(incentivePolicyVo.getConditionJson().getMainCourseTypeIds()))
                {
                    List<Long> mainCourseTypeIds = incentivePolicyVo.getConditionJson().getMainCourseTypeIds();
                    incentivePolicyVo.getConditionJson().setMainCourseTypeNames(courseTypeNameMap.entrySet().stream()
                            .filter(entry -> mainCourseTypeIds.contains(entry.getKey()))
                            .map(Map.Entry::getValue)
                            .collect(Collectors.toList()));
                }
                if(GeneralTool.isNotEmpty(incentivePolicyVo.getConditionJson().getSubCourseTypeIds()))
                {
                    List<Long> subCourseTypeIds = incentivePolicyVo.getConditionJson().getSubCourseTypeIds();
                    incentivePolicyVo.getConditionJson().setSubCourseTypeNames(courseTypeNameMap.entrySet().stream()
                            .filter(entry -> subCourseTypeIds.contains(entry.getKey()))
                            .map(Map.Entry::getValue)
                            .collect(Collectors.toList()));
                }
                if(GeneralTool.isNotEmpty(incentivePolicyVo.getConditionJson().getLaterUpCourseTypeIds()))
                {
                    List<Long> laterCourseTypeIds = incentivePolicyVo.getConditionJson().getLaterUpCourseTypeIds();
                    incentivePolicyVo.getConditionJson().setLaterUpCourseTypeNames(courseTypeNameMap.entrySet().stream()
                            .filter(entry -> laterCourseTypeIds.contains(entry.getKey()))
                            .map(Map.Entry::getValue)
                            .collect(Collectors.toList()));
                }
            }
        }
        //等级
        Set<Long> majorLevelIds = new HashSet<>();
        if(GeneralTool.isNotEmpty(incentivePolicyVo.getConditionJson()) && GeneralTool.isNotEmpty(incentivePolicyVo.getConditionJson().getMainMajorLevelIds()))
        {
            majorLevelIds.addAll(incentivePolicyVo.getConditionJson().getMainMajorLevelIds());
        }
        if(GeneralTool.isNotEmpty(incentivePolicyVo.getConditionJson()) && GeneralTool.isNotEmpty(incentivePolicyVo.getConditionJson().getSubMajorLevelIds()))
        {
            majorLevelIds.addAll(incentivePolicyVo.getConditionJson().getSubMajorLevelIds());
        }
        if(GeneralTool.isNotEmpty(incentivePolicyVo.getConditionJson()) && GeneralTool.isNotEmpty(incentivePolicyVo.getConditionJson().getLaterUpMajorLevelIds()))
        {
            majorLevelIds.addAll(incentivePolicyVo.getConditionJson().getLaterUpMajorLevelIds());
        }
        if(GeneralTool.isNotEmpty(majorLevelIds))
        {
            Result<Map<Long, String>> result = institutionCenterClient.getMajorLevelNamesByIds(majorLevelIds);
            Map<Long, String> majorLevelNameMap = result.getData();
            if(GeneralTool.isNotEmpty(majorLevelNameMap))
            {
                //写入课程等级名称
                if(GeneralTool.isNotEmpty(incentivePolicyVo.getConditionJson().getMainMajorLevelIds()))
                {
                    List<Long> mainMajorLevelIds = incentivePolicyVo.getConditionJson().getMainMajorLevelIds();
                    incentivePolicyVo.getConditionJson().setMainMajorLevelNames(majorLevelNameMap.entrySet().stream()
                            .filter(entry -> mainMajorLevelIds.contains(entry.getKey()))
                            .map(Map.Entry::getValue)
                            .collect(Collectors.toList()));
                }
                if(GeneralTool.isNotEmpty(incentivePolicyVo.getConditionJson().getSubMajorLevelIds()))
                {
                    List<Long> subMajorLevelIds = incentivePolicyVo.getConditionJson().getSubMajorLevelIds();
                    incentivePolicyVo.getConditionJson().setSubMajorLevelNames(majorLevelNameMap.entrySet().stream()
                            .filter(entry -> subMajorLevelIds.contains(entry.getKey()))
                            .map(Map.Entry::getValue)
                            .collect(Collectors.toList()));
                }
                if(GeneralTool.isNotEmpty(incentivePolicyVo.getConditionJson().getLaterUpMajorLevelIds()))
                {
                    List<Long> laterMajorLevelIds = incentivePolicyVo.getConditionJson().getLaterUpMajorLevelIds();
                    incentivePolicyVo.getConditionJson().setLaterUpMajorLevelNames(majorLevelNameMap.entrySet().stream()
                            .filter(entry -> laterMajorLevelIds.contains(entry.getKey()))
                            .map(Map.Entry::getValue)
                            .collect(Collectors.toList()));
                }
            }
        }
        //后续课程学校提供商（单）
        if(GeneralTool.isNotEmpty(incentivePolicyVo.getConditionJson()) && GeneralTool.isNotEmpty(incentivePolicyVo.getConditionJson().getLaterInstitutionProviderId()))
        {
            Set<Long> ids = new HashSet<>();
            ids.add(incentivePolicyVo.getConditionJson().getLaterInstitutionProviderId());
            Result<String> result = institutionCenterClient.getInstitutionProviderName(incentivePolicyVo.getConditionJson().getLaterInstitutionProviderId());
            String providerName = result.getData();
            if(GeneralTool.isNotEmpty(providerName))
            {
                incentivePolicyVo.getConditionJson().setLaterInstitutionProviderName(providerName);
            }
        }
        //后续学校ids的name集合
        if(GeneralTool.isNotEmpty(incentivePolicyVo.getConditionJson()) && GeneralTool.isNotEmpty(incentivePolicyVo.getConditionJson().getLaterInstitutionIds()))
        {
            Set<Long> laterInstitutionIds = incentivePolicyVo.getConditionJson().getLaterInstitutionIds().stream().collect(Collectors.toSet());
            Result<Map<Long, String>> result = institutionCenterClient.getInstitutionNamesByIds(laterInstitutionIds);
            Map<Long, String> institutionNamesByIds = result.getData();
            if(GeneralTool.isNotEmpty(institutionNamesByIds))
            {
                incentivePolicyVo.getConditionJson().setLaterInstitutionNames(institutionNamesByIds.entrySet().stream().map(Map.Entry::getValue).collect(Collectors.toList()));
            }
        }
        //符合奖励状态
        List<String> stepOrderNames = new ArrayList<>();
        if(GeneralTool.isNotEmpty(incentivePolicyVo.getConditionJson()) && GeneralTool.isNotEmpty(incentivePolicyVo.getConditionJson().getStepOrderIds()))
        {
            //获取步骤id和步骤名
            List<StudentOfferItemStep> studentOfferItemSteps = studentOfferItemStepService.listByIds(incentivePolicyVo.getConditionJson().getStepOrderIds());
            if(GeneralTool.isNotEmpty(studentOfferItemSteps))
            {
                stepOrderNames = studentOfferItemSteps.stream().map(StudentOfferItemStep::getStepName).collect(Collectors.toList());
            }
            if(GeneralTool.isNotEmpty(stepOrderNames))
            {
                incentivePolicyVo.getConditionJson().setStepOrderNames(stepOrderNames);
            }
        }
        //其他奖品奖励
        if(GeneralTool.isNotEmpty(incentivePolicyVo.getRewardJson()) &&  GeneralTool.isNotEmpty(incentivePolicyVo.getRewardJson().getRewardIds()))
        {
            List<IncentiveReward> incentiveRewards = incentiveRewardService.listByIds(incentivePolicyVo.getRewardJson().getRewardIds());
            if(GeneralTool.isNotEmpty(incentiveRewards))
            {
                incentivePolicyVo.getRewardJson().setRewardNames(incentiveRewards.stream().map(IncentiveReward::getRewardName).distinct().collect(Collectors.toList()));
            }
        }
        //应收币种/应付币种
        if(GeneralTool.isNotEmpty(incentivePolicyVo.getRewardJson()) && GeneralTool.isNotEmpty(incentivePolicyVo.getRewardJson().getRewardDetails()))
        {
            Set<String> fkCurrencyTypeNums = new HashSet<>();
            fkCurrencyTypeNums.addAll(incentivePolicyVo.getRewardJson().getRewardDetails().stream().map(IncentivePolicyRewardVo.RewardDetail::getPayableCurrencyTypeNum).collect(Collectors.toSet()));
            fkCurrencyTypeNums.addAll(incentivePolicyVo.getRewardJson().getRewardDetails().stream().map(IncentivePolicyRewardVo.RewardDetail::getReceivableCurrencyTypeNum).collect(Collectors.toSet()));
            if(GeneralTool.isNotEmpty(fkCurrencyTypeNums))
            {
                Map<String, String> currencyNumsMap = financeCenterClient.getCurrencyNamesByNums(fkCurrencyTypeNums).getData();
                if(GeneralTool.isNotEmpty(currencyNumsMap))
                {
                    for(IncentivePolicyRewardVo.RewardDetail rewardDetail : incentivePolicyVo.getRewardJson().getRewardDetails())
                    {
                        rewardDetail.setPayableCurrencyTypeName(currencyNumsMap.get(rewardDetail.getPayableCurrencyTypeNum()));
                        rewardDetail.setReceivableCurrencyTypeName(currencyNumsMap.get(rewardDetail.getReceivableCurrencyTypeNum()));
                    }
                }
            }
        }

        List<IncentivePolicyVo> incentivePolicyVos = new ArrayList<>();
        incentivePolicyVos.add(incentivePolicyVo);
        this.doSetDtoName(incentivePolicyVos);

        //添加附件列表
        MediaAndAttachedDto attachedVo = new MediaAndAttachedDto();
        attachedVo.setFkTableId(id);
        attachedVo.setFkTableName(TableEnum.SALE_INCENTIVE_POLICY.key);
        Page page = new Page();
        page.setShowCount(100);//取100，一般奖励政策的附件不会超出这个数字
        page.setCurrentPage(1);
        List<MediaAndAttachedVo> mediaAndAttachedVos = this.getPolicyMedia(attachedVo,page);

        IncentivePolicyVo incentivePolicyVo_ = null;
        if(GeneralTool.isNotEmpty(incentivePolicyVos))
        {
            incentivePolicyVo_ = incentivePolicyVos.get(0);
            if(GeneralTool.isNotEmpty(incentivePolicyVo_))
            {
                incentivePolicyVo_.setMediaAndAttachedDtos(mediaAndAttachedVos);
            }
        }
        return incentivePolicyVo_;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addIncentivePolicy(IncentivePolicyDto incentivePolicyDto) {
        IncentivePolicy incentivePolicy = BeanCopyUtils.objClone(incentivePolicyDto, IncentivePolicy::new);
        if(GeneralTool.isNotEmpty(incentivePolicyDto.getConditionJson()))
        {
            IncentivePolicyConditionVo incentivePolicyConditionVo = BeanCopyUtils.objClone(incentivePolicyDto.getConditionJson(), IncentivePolicyConditionVo::new);
            incentivePolicy.setConditionJson(incentivePolicyConditionVo);
        }
        if(GeneralTool.isNotEmpty(incentivePolicyDto.getRewardJson()))
        {
            IncentivePolicyRewardVo incentivePolicyRewardVo = BeanCopyUtils.objClone(incentivePolicyDto.getRewardJson(), IncentivePolicyRewardVo::new);
            incentivePolicy.setRewardJson(incentivePolicyRewardVo);
        }

        //获取最大的排序值,这种方式在高并发下会有问题，普通操作不影响
        QueryWrapper<IncentivePolicy> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("max(view_order) as viewOrder");

        IncentivePolicy incentivePolicy_ = incentivePolicyMapper.selectOne(queryWrapper);
        Integer maxOrder = 0;
        if (GeneralTool.isNotEmpty(incentivePolicy_) && GeneralTool.isNotEmpty(incentivePolicy_.getViewOrder())) {
            maxOrder = incentivePolicy_.getViewOrder();
        }

        incentivePolicy.setViewOrder(maxOrder+1);
        incentivePolicy.setStatus(1);//新增默认有效状态
        utilService.setCreateInfo(incentivePolicy);
        incentivePolicyMapper.insert(incentivePolicy);

        //根据主键生成编号 奖励策略编号：ICP+6位ID数字，例：ICP000001
        incentivePolicy.setNum(MyStringUtils.getIncentivePolicyNum(incentivePolicy.getId()));
        incentivePolicyMapper.updateById(incentivePolicy);

        List<MediaAndAttachedDto> mediaAndAttachedDtos = incentivePolicyDto.getMediaAndAttachedVos();
        if(GeneralTool.isNotEmpty(incentivePolicyDto.getMediaAndAttachedVos()))
        {
            mediaAndAttachedDtos.stream().forEach(item ->{
                item.setFkTableId(incentivePolicy.getId());
            });
            this.addAgentMedia(mediaAndAttachedDtos);
        }
        return incentivePolicy.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteDataById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        IncentivePolicy incentivePolicy = this.getById(id);
        if(GeneralTool.isEmpty(incentivePolicy))
        {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        //判断策略是否在使用中（是否有已结算数据）
        if(incentivePolicyIsUsed(id))
        {
            throw new GetServiceException(LocaleMessageUtils.getMessage("there_is_settlement_data_for_the_strategy"));//策略正在使用，如有未结算数据，是否允许删除，如允许的话，删除策略是否需要同时删除统计列表数据。
        }
        //删除策略
        incentivePolicyMapper.deleteById(id);
        //删除策略关联的学生统计列表数据
        incentivePolicyStudentOfferItemService.remove(Wrappers.<IncentivePolicyStudentOfferItem>lambdaQuery()
                .eq(IncentivePolicyStudentOfferItem::getFkIncentivePolicyId,id));
    }

    @Override
    public IncentivePolicyVo updateIncentivePolicy(IncentivePolicyDto incentivePolicyDto) {
        if (incentivePolicyDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        IncentivePolicy oldIncentivePolicy = this.getById(incentivePolicyDto.getId());
        if(GeneralTool.isEmpty(oldIncentivePolicy))
        {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        IncentivePolicy incentivePolicy = BeanCopyUtils.objClone(incentivePolicyDto, IncentivePolicy::new);
        if(GeneralTool.isNotEmpty(incentivePolicyDto.getConditionJson()))
        {
            IncentivePolicyConditionVo incentivePolicyConditionVo = BeanCopyUtils.objClone(incentivePolicyDto.getConditionJson(), IncentivePolicyConditionVo::new);
            incentivePolicyConditionVo.setMainCourseNames(null);
            incentivePolicyConditionVo.setMainCourseTypeNames(null);
            incentivePolicyConditionVo.setMainMajorLevelNames(null);
            incentivePolicyConditionVo.setSubCourseNames(null);
            incentivePolicyConditionVo.setSubMajorLevelNames(null);
            incentivePolicyConditionVo.setSubCourseTypeNames(null);
            incentivePolicyConditionVo.setLaterInstitutionNames(null);
            incentivePolicyConditionVo.setLaterUpCourseNames(null);
            incentivePolicyConditionVo.setLaterUpCourseTypeNames(null);
            incentivePolicyConditionVo.setLaterUpMajorLevelNames(null);
            incentivePolicyConditionVo.setLaterInstitutionProviderName("");
            incentivePolicy.setConditionJson(incentivePolicyConditionVo);
        }
        if(GeneralTool.isNotEmpty(incentivePolicyDto.getRewardJson()))
        {
            IncentivePolicyRewardVo incentivePolicyRewardVo = BeanCopyUtils.objClone(incentivePolicyDto.getRewardJson(), IncentivePolicyRewardVo::new);
            incentivePolicyRewardVo.setRewardNames(null);
            incentivePolicy.setRewardJson(incentivePolicyRewardVo);
        }
        //判断策略是否在使用中（是否有已结算数据）
        if(incentivePolicyIsUsed(incentivePolicy.getId()))
        {
            throw new GetServiceException(LocaleMessageUtils.getMessage("there_is_settlement_data_for_the_strategy"));
        }
        utilService.updateUserInfoToEntity(incentivePolicy);
        incentivePolicyMapper.updateByIdWithNull(incentivePolicy);

        //更新附件
        List<MediaAndAttachedDto> mediaAndAttachedDtos = incentivePolicyDto.getMediaAndAttachedVos();
        if(GeneralTool.isNotEmpty(incentivePolicyDto.getMediaAndAttachedVos()))
        {
            mediaAndAttachedDtos = mediaAndAttachedDtos.stream().filter(i->GeneralTool.isEmpty(i.getId())).collect(Collectors.toList());
            if(GeneralTool.isNotEmpty(mediaAndAttachedDtos))
            {
                mediaAndAttachedDtos.stream().forEach(item ->{
                    item.setFkTableId(incentivePolicy.getId());
                });
                //获取附件id为空的，表示新增的附件，如已有id的附件已存在不处理，前端编辑策略的时候可以手动逐个删除
                this.addAgentMedia(mediaAndAttachedDtos);
            }
        }

        return selectById(incentivePolicy.getId());
    }

    @Override
    public List<IncentivePolicyVo> getIncentivePolicys(IncentivePolicyQueryDto incentivePolicyVo, SearchBean<IncentivePolicyQueryDto> page) {
        if(GeneralTool.isEmpty(SecureUtil.getCountryIds()))
        {
            return null;
        }
        LambdaQueryWrapper<IncentivePolicy> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(incentivePolicyVo)) {
            lambdaQueryWrapper.and(wrapper_ ->
                    wrapper_.isNull(IncentivePolicy::getFkAreaCountryId).or()
                            .in(IncentivePolicy::getFkAreaCountryId,SecureUtil.getCountryIds()));
//            lambdaQueryWrapper.in(IncentivePolicy::getFkAreaCountryId,SecureUtil.getCountryIds());
            if (GeneralTool.isNotEmpty(incentivePolicyVo.getFkCompanyId())) {
                lambdaQueryWrapper.eq(IncentivePolicy::getFkCompanyId, incentivePolicyVo.getFkCompanyId());
            }
            if (GeneralTool.isNotEmpty(incentivePolicyVo.getStatus())) {
                lambdaQueryWrapper.eq(IncentivePolicy::getStatus, incentivePolicyVo.getStatus());
            }
            if (GeneralTool.isNotEmpty(incentivePolicyVo.getYear())) {
                lambdaQueryWrapper.eq(IncentivePolicy::getYear, incentivePolicyVo.getYear());
            }
            if (GeneralTool.isNotEmpty(incentivePolicyVo.getFkAreaCountryId())) {
                lambdaQueryWrapper.eq(IncentivePolicy::getFkAreaCountryId, incentivePolicyVo.getFkAreaCountryId());
            }
            if (GeneralTool.isNotEmpty(incentivePolicyVo.getFkInstitutionProviderId())) {
                lambdaQueryWrapper.eq(IncentivePolicy::getFkInstitutionProviderId, incentivePolicyVo.getFkInstitutionProviderId());
            }
            if (GeneralTool.isNotEmpty(incentivePolicyVo.getConditionNote())) {
                lambdaQueryWrapper.like(IncentivePolicy::getConditionNote, incentivePolicyVo.getConditionNote());
            }
            if (GeneralTool.isNotEmpty(incentivePolicyVo.getRemark())) {
                lambdaQueryWrapper.like(IncentivePolicy::getRemark, incentivePolicyVo.getRemark());
            }
            //学校ids，暂时只支持传入单个学校查询
            if (GeneralTool.isNotEmpty(incentivePolicyVo.getFkInstitutionIds())) {
                String sql = " FIND_IN_SET('"+incentivePolicyVo.getFkInstitutionIds()+"', fk_institution_ids) > 0";
                lambdaQueryWrapper.apply(sql);
            }
            //公开对象，暂时只支持传入单个对象查询
            if (GeneralTool.isNotEmpty(incentivePolicyVo.getPublicLevel())) {
                String sql = " FIND_IN_SET('"+incentivePolicyVo.getPublicLevel()+"', public_level) > 0";
                lambdaQueryWrapper.apply(sql);
            }
        }
        lambdaQueryWrapper.orderByDesc(IncentivePolicy::getViewOrder);

//        List<IncentivePolicy> incentivePolicies = incentivePolicyMapper.selectList(lambdaQueryWrapper);
        IPage<IncentivePolicy> iPage = incentivePolicyMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
        List<IncentivePolicy> incentivePolicies = iPage.getRecords();
        page.setAll((int) iPage.getTotal());

        List<IncentivePolicyVo> incentivePolicyVos = BeanCopyUtils.copyListProperties(incentivePolicies, IncentivePolicyVo::new);

        //统计已结算总数
        List<Long> incentivePolicyIds = incentivePolicyVos.stream().map(IncentivePolicyVo::getId).collect(Collectors.toList());
        if(GeneralTool.isNotEmpty(incentivePolicyIds))
        {
            List<IncentivePolicyStudentOfferItem> incentivePolicyStudentOfferItems = incentivePolicyStudentOfferItemService.list(Wrappers.<IncentivePolicyStudentOfferItem>lambdaQuery()
                    .in(IncentivePolicyStudentOfferItem::getFkIncentivePolicyId,incentivePolicyIds)
                    .eq(IncentivePolicyStudentOfferItem::getFinanceStatus,1));//已结算
            if(GeneralTool.isNotEmpty(incentivePolicyStudentOfferItems))
            {
                //按照策略ID统计学生数
                Map<Long, Long> countByPolicyId = incentivePolicyStudentOfferItems.stream()
                        .collect(Collectors.groupingBy(IncentivePolicyStudentOfferItem::getFkIncentivePolicyId, Collectors.counting()));
                for (IncentivePolicyVo incentivePolicyDto: incentivePolicyVos)
                {
                    Long count = countByPolicyId.get(incentivePolicyDto.getId());
                    incentivePolicyDto.setCommissionNum(GeneralTool.isNotEmpty(count) && count>0 ? count.intValue():0);
                }
            }else
            {
                for (IncentivePolicyVo incentivePolicyDto: incentivePolicyVos)
                {
                    incentivePolicyDto.setCommissionNum(0);
                }
            }

        }
        //列表处理：公司名称/公共对象名称/国家名称/提供商名称/学校名称 参数
        this.doSetDtoName(incentivePolicyVos);
        return incentivePolicyVos;
    }

    /**
     * 设置属性
     * @param dtoList
     */
    private void doSetDtoName(List<IncentivePolicyVo> dtoList) {
        if(GeneralTool.isEmpty(dtoList)){
            return;
        }

        Set<Long> fkComanyIds = dtoList.stream().map(IncentivePolicyVo::getFkCompanyId).collect(Collectors.toSet());
        //根据公司ids获取名称
        Map<Long, String> companyNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkComanyIds)) {
            Result<Map<Long, String>> result = permissionCenterClient.getCompanyNamesByIds(fkComanyIds);
            if (result.isSuccess() && result.getData() != null) {
                companyNamesByIds = result.getData();
            }
        }

        //学校ids集合
        Set<String> institutionIds_str = dtoList.stream().map(IncentivePolicyVo::getFkInstitutionIds).collect(Collectors.toSet());
        Set<Long> institutionIds = new HashSet<>();
        if(GeneralTool.isNotEmpty(institutionIds_str))
        {
            for(String item:institutionIds_str)
            {
                if(GeneralTool.isNotEmpty(item))
                {
                    List<String> values = Arrays.asList(item.split(","));
                    if(GeneralTool.isNotEmpty(values) && values.size()>0)
                    {
                        Set<Long> sets = values.stream().map(Long::valueOf).collect(Collectors.toSet());
                        institutionIds.addAll(sets);
                    }
                }
            }
        }
        //根据学校ids获取名称
        Map<Long, String> institutionNamesByIds = new HashMap<>();
        if(GeneralTool.isNotEmpty(institutionIds) && institutionIds.size()>0)
        {
            if (GeneralTool.isNotEmpty(institutionIds)) {
                Result<Map<Long, String>> result2 = institutionCenterClient.getInstitutionNamesByIds(institutionIds);
                if (!result2.isSuccess()) {
                    throw new GetServiceException(result2.getMessage());
                }
                institutionNamesByIds = result2.getData();
            }
        }

        //根据国家ids获取国家名称
        Set<Long> countryIds = dtoList.stream().map(IncentivePolicyVo::getFkAreaCountryId).collect(Collectors.toSet());
        Map<Long, String> countryNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(countryIds)) {
            Result<Map<Long, String>> result1 = institutionCenterClient.getCountryNamesByIds(countryIds);
            if (result1.isSuccess() && GeneralTool.isNotEmpty(result1.getData())) {
                countryNamesByIds = result1.getData();
            }
        }

        //提供商
        Set<Long> institutionProviderIds = dtoList.stream().map(IncentivePolicyVo::getFkInstitutionProviderId).collect(Collectors.toSet());
        Map<Long, String> providerNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(institutionProviderIds)) {
            Result<Map<Long, String>> resultProviderNamesByIds = institutionCenterClient.getInstitutionProviderNamesByIds(institutionProviderIds);
            if (resultProviderNamesByIds.isSuccess()) {
                providerNamesByIds = resultProviderNamesByIds.getData();
            }
        }

        for(IncentivePolicyVo incentivePolicyVo : dtoList)
        {
            //公司名字
            incentivePolicyVo.setFkCompanyName(companyNamesByIds.get(incentivePolicyVo.getFkCompanyId()));

            //公共对象
            StringJoiner stringJoiner = new StringJoiner(" ");
            if (GeneralTool.isNotEmpty(incentivePolicyVo.getPublicLevel())) {
                List<String> result = Arrays.asList(incentivePolicyVo.getPublicLevel().split(","));
                for (String name : result) {
                    stringJoiner.add(ProjectExtraEnum.getValueByKey(Integer.valueOf(name), ProjectExtraEnum.PUBLIC_OBJECTS));
                }
                incentivePolicyVo.setPublicLevelName(stringJoiner.toString());
            }

            //国家名称
            if(GeneralTool.isNotEmpty(incentivePolicyVo.getFkAreaCountryId()))
            {
                incentivePolicyVo.setCountryName(countryNamesByIds.get(incentivePolicyVo.getFkAreaCountryId()));
            }
            //提供商名称
            if(GeneralTool.isNotEmpty(incentivePolicyVo.getFkInstitutionProviderId()))
            {
                incentivePolicyVo.setInstitutionProviderName(providerNamesByIds.get(incentivePolicyVo.getFkInstitutionProviderId()));
            }

            //学校名称
            if (GeneralTool.isNotEmpty(incentivePolicyVo.getFkInstitutionIds())) {
                List<String> values = Arrays.asList(incentivePolicyVo.getFkInstitutionIds().split(","));
                List<String> names = new ArrayList<>();
                if(GeneralTool.isNotEmpty(values) && values.size()>0)
                {
                    Set<Long> sets = values.stream().map(Long::valueOf).collect(Collectors.toSet());
                    institutionIds.addAll(sets);
                    for(String value:values)
                    {
                        String name = institutionNamesByIds.get(Long.valueOf(value));
                        if(GeneralTool.isNotEmpty(name))
                        {
                            names.add(name);
                        }
                    }
                }
                if(GeneralTool.isNotEmpty(names))
                {
                    incentivePolicyVo.setInstitutionName(StringUtil.join(names));
                }
            }

        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        IncentivePolicy incentivePolicy = this.getById(id);
        if(GeneralTool.isEmpty(incentivePolicy))
        {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        //判断策略是否在使用中（是否有已结算数据）
        if(incentivePolicyIsUsed(id))
        {
            throw new GetServiceException(LocaleMessageUtils.getMessage("there_is_settlement_data_for_the_strategy"));
        }
        incentivePolicy.setStatus(0);//0作废
        incentivePolicyMapper.updateById(incentivePolicy);
        //同时删除统计列表数据
        incentivePolicyStudentOfferItemService.remove(Wrappers.<IncentivePolicyStudentOfferItem>lambdaQuery()
                .eq(IncentivePolicyStudentOfferItem::getFkIncentivePolicyId,id));
    }

    @Override
    public void movingOrder(List<IncentivePolicyDto> voList) {
        if (GeneralTool.isEmpty(voList)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        //如果前端是跨越多个行拖动，那么前端需要排列最新的值集合，传递给后端。
        List<IncentivePolicy> incentivePolicies = BeanCopyUtils.copyListProperties(voList, IncentivePolicy::new);
        this.saveOrUpdateBatch(incentivePolicies);
    }

    /**
     * 拷贝的逻辑，即传入策略ID，返回策略详情，前端通过编辑后再新增
     * @param id
     * @return
     */
    @Override
    public IncentivePolicyVo copyPolicy(Long id) {
        return selectById(id);
    }

    /**
     * 根据策略ID查询学生统计列表是否有已结算的数据
     * @param incentivePolicyId
     * @return
     */
    private Boolean incentivePolicyIsUsed(Long incentivePolicyId)
    {
        Wrapper<IncentivePolicyStudentOfferItem> wrapper = Wrappers.<IncentivePolicyStudentOfferItem>lambdaQuery()
                .eq(IncentivePolicyStudentOfferItem::getFkIncentivePolicyId,incentivePolicyId)
                .eq(IncentivePolicyStudentOfferItem::getFinanceStatus,1);//已结算
        List<IncentivePolicyStudentOfferItem> list = incentivePolicyStudentOfferItemService.list(wrapper);
        return GeneralTool.isNotEmpty(list) && list.size() > 0;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<MediaAndAttachedVo> addAgentMedia(List<MediaAndAttachedDto> mediaAttachedVos) {
        if (GeneralTool.isEmpty(mediaAttachedVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        List<MediaAndAttachedVo> mediaAndAttachedVos = new ArrayList<>();
        for (MediaAndAttachedDto mediaAndAttachedDto : mediaAttachedVos) {
            //设置插入的表
            mediaAndAttachedDto.setFkTableName(TableEnum.SALE_INCENTIVE_POLICY.key);
            mediaAndAttachedVos.add(attachedService.addMediaAndAttached(mediaAndAttachedDto));
        }
        return mediaAndAttachedVos;
    }


    @Override
    public List<MediaAndAttachedVo> getPolicyMedia(MediaAndAttachedDto attachedVo, Page page) {
        if (GeneralTool.isEmpty(attachedVo.getFkTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        attachedVo.setFkTableName(TableEnum.SALE_INCENTIVE_POLICY.key);
        return attachedService.getMediaAndAttachedDto(attachedVo, page, FileTypeEnum.INCENTIVE_POLICY_FILE.key);
    }


    @Override
    public Long editComment(CommentDto commentDto) {
        SaleComment comment = BeanCopyUtils.objClone(commentDto, SaleComment::new);
        if (GeneralTool.isNotEmpty(commentDto)) {
            if (GeneralTool.isNotEmpty(commentDto.getId())) {
                comment.setFkTableName(TableEnum.SALE_INCENTIVE_POLICY.key);
                commentService.updateComment(comment);
            } else {
                comment.setFkTableName(TableEnum.SALE_INCENTIVE_POLICY.key);
                commentService.addComment(comment);
            }
        }
        return comment.getId();
    }

    @Override
    public List<CommentVo> getComments(CommentDto commentDto, Page page) {
        commentDto.setFkTableName(TableEnum.SALE_INCENTIVE_POLICY.key);
        return commentService.datas(commentDto, page);
    }

    @Override
    public void movingOrderSelect(int end, int start) {
        LambdaQueryWrapper<IncentivePolicy> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.orderByAsc(IncentivePolicy::getViewOrder);
        List<IncentivePolicy> viewOrders = incentivePolicyMapper.selectList(lambdaQueryWrapper);
        //将数据放到目标数据下面。
        if (start < end) {
            end++;
        }

        viewOrders.add(end, viewOrders.get(start));
        if (start > end) {
            viewOrders.remove(start + 1);
        } else {
            viewOrders.remove(start);
        }
        for (int i = 0; i < viewOrders.size(); i++) {
            IncentivePolicy incentivePolicy = viewOrders.get(i);
            incentivePolicy.setViewOrder(i);
        }
        this.updateBatchById(viewOrders);
    }

    @Override
    public List<BaseSelectEntity> getInstitutionProviderSelect(Long companyId) {
        return incentivePolicyMapper.getInstitutionProviderSelect(companyId);
    }

    @Override
    public List<BaseSelectEntity> getInstitutionListByProviderId(Long providerId) {
        return incentivePolicyMapper.getInstitutionListByProviderId(providerId);
    }
}

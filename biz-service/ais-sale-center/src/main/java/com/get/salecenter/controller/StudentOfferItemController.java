package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.eunms.*;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.FocExportVo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.CommonUtil;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.redis.lock.RedisLock;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.BeanUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dto.InstitutionApplicationMetricsDto;
import com.get.institutioncenter.vo.InstitutionApplicationMetricsVo;
import com.get.institutioncenter.vo.InstitutionApplicationStatisticsVo;
import com.get.institutioncenter.dto.CaseStudyResultsDto;
import com.get.institutioncenter.dto.query.InstitutionApplicationStaticsQueryDto;
import com.get.reportcenter.entity.ReportSale;
import com.get.salecenter.component.NettyPushHelper;
import com.get.salecenter.vo.*;
import com.get.salecenter.vo.PayablePlanVo;
import com.get.salecenter.entity.EnrolFailureReason;
import com.get.salecenter.service.DataImportService;
import com.get.salecenter.service.IStudentOfferItemService;
import com.get.salecenter.service.LogStudentOfferItemOfferFileIdentifyService;
import com.get.salecenter.dto.*;
import com.get.salecenter.dto.query.AgentApplicationRankingQueryDto;
import com.get.salecenter.dto.query.StudentOfferItemListQueryDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/11/18
 * @TIME: 15:47
 * @Description:
 **/

@Api(tags = "学生申请方案项目管理")
@RestController
@RequestMapping("sale/studentOfferItem")
public class StudentOfferItemController {

    @Resource
    private IStudentOfferItemService offerItemService;
    @Resource
    private NettyPushHelper nettyPushHelper;
    @Resource
    private LogStudentOfferItemOfferFileIdentifyService logStudentOfferItemOfferFileIdentifyService;

    @PostMapping("analyzeOffer")
    public ResponseBo analyzeOffer() {
        logStudentOfferItemOfferFileIdentifyService.analyzeOffer();
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentOfferItemVo>
     * @Description: 列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "num代理编号 name代理名称")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生申请方案项目管理/列表数据")
    @PostMapping("datas")
    public ResponseBo<StudentOfferItemVo> datas(@RequestBody SearchBean<StudentOfferItemDto> page) {
        List<StudentOfferItemVo> datas = offerItemService.getStudentOfferItem(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }



    @ApiOperation(value = "获取子计划及后续课程 课程和开学部分信息", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生申请方案项目管理/获取子计划及后续课程,课程和开学部分信息")
    @GetMapping("getChildOfferItemPartInfo")
    public ListResponseBo<StudentOfferItemVo> getChildOfferItemPartInfo(@RequestParam("offerItemId") Long offerItemId){
        return new ListResponseBo<>(offerItemService.getChildOfferItemPartInfo(offerItemId));
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 新增信息
     * @Param [StudentOfferVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生申请方案项目管理/新增学习计划")
    @PostMapping("add")
    @RedisLock(value="fzh:offerItemAddLock",param = "#itemVo.fkStudentId+#itemVo.fkStudentOfferId",waitTime = 10L)
    public ResponseBo add(@RequestBody @Validated(StudentOfferItemDto.Add.class) StudentOfferItemDto itemVo) {
        return SaveResponseBo.ok(offerItemService.addOfferItem(itemVo));
    }

    @ApiOperation("检测不同代理的申请方案是否存在相同申请计划")
    @PostMapping("getIsExistStudentOfferItem")
    @VerifyPermission(IsVerify = false)
    public ResponseBo<ExistStudentOfferItemVo> getIsExistStudentOfferItem(@RequestBody StudentOfferItemDto studentOfferItemDto) {
        return new ResponseBo<>(offerItemService.getIsExistStudentOfferItem(studentOfferItemDto));
    }




    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 复制学习计划
     * @Param [StudentOfferVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "复制学习计划", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生申请方案项目管理/复制学习计划")
    @PostMapping("copy")
    public ResponseBo copy(@RequestParam("id") Long id) {
        return SaveResponseBo.ok(offerItemService.copyOfferItem(id));
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 删除信息
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/学生申请方案项目管理/删除学生")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        offerItemService.delete(id);
        return DeleteResponseBo.ok();
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentOfferItemVo>
     * @Description: 修改信息
     * @Param [itemVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生申请方案项目管理/更新学生")
    @PostMapping("update")
    public ResponseBo<StudentOfferItemVo> update(@RequestBody @Validated(EventOfferPlanDto.Update.class)  EventOfferPlanDto itemVo) {
        return UpdateResponseBo.ok(offerItemService.updateOfferItem(itemVo));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentOfferItemVo>
     * @Description: 修改信息
     * @Param [itemVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "ISSUE申请计划校验申请数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生申请方案项目管理/更新学生--校验ISSUE申请信息")
    @PostMapping("getIssueApplyDataInfoResult")
    public ResponseBo<Boolean> getIssueApplyDataInfoResult(@RequestBody @Validated(EventOfferPlanDto.Update.class) EventOfferPlanDto itemVo) {
        return UpdateResponseBo.ok(offerItemService.getIssueApplyDataInfoResult(itemVo));
    }

    @ApiOperation(value = "更新开学时间", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生申请方案项目管理/更新开学时间")
    @PostMapping("updateOpenTime")
    public ResponseBo<StudentOfferItemVo> updateOpenTime(@RequestBody StudentOfferItemDto studentOfferItemDto){
        return new ResponseBo<>(offerItemService.updateOpenTime(studentOfferItemDto));
    }

    @ApiOperation(value = "根据课程ID获取课程等级类型", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/学生申请方案项目管理/根据课程ID获取课程等级类型")
    @GetMapping("getCourseMajorLevelAndTypeById")
    public ResponseBo getCourseMajorLevelAndTypeById(@RequestParam("fkInstitutionCourseId") Long fkInstitutionCourseId) {
        return new ResponseBo(offerItemService.getCourseMajorLevelAndTypeById(fkInstitutionCourseId));
    }


    /**
     * @Description: 创建应收应付编辑接口
     * @Author: Jerry
     * @Date:9:41 2021/11/19
     */
    @ApiOperation(value = "创建应收应付编辑接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生申请方案项目管理/创建应收应付编辑接口")
    @PostMapping("receivablePlanUpdate")
    public ResponseBo receivablePlanUpdate(@RequestBody @Validated(StudentOfferItemReceivablePlanDto.Update.class) StudentOfferItemReceivablePlanDto studentOfferItemReceivablePlanDto) {
        offerItemService.receivablePlanUpdate(studentOfferItemReceivablePlanDto);
        return UpdateResponseBo.ok();
    }

    @ApiOperation(value = "确认创建应收应付(废弃)", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生申请方案项目管理/确认创建应收应付(废弃)")
    @PostMapping("generatePlan/{id}")
    public ResponseBo generatePlan(@RequestBody @Validated(StudentOfferItemReceivablePlanDto.Update.class) StudentOfferItemReceivablePlanDto studentOfferItemReceivablePlanDto) {
        return offerItemService.generatePlan(studentOfferItemReceivablePlanDto);
    }

    @ApiOperation(value = "获取匹配的合同公式", notes = "id为学习计划id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生申请方案项目管理/获取匹配的合同公式")
    @GetMapping("matchingContractFormula/{id}")
    public ResponseBo<ContractFormulaMatchingVo> matchingContractFormula(@PathVariable("id") Long studentOfferItemId) {
        List<ContractFormulaMatchingVo> contractFormulaMatchingVoList = offerItemService.matchingContractFormula(studentOfferItemId);
        return new ListResponseBo<>(contractFormulaMatchingVoList);
    }

    @ApiOperation(value = "生成应收应付", notes = "id为学习计划id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生申请方案项目管理/生成应收应付")
    @PostMapping("generateMatchingPlan")
    public ResponseBo generateMatchingPlan(@RequestBody @Validated GenerateMatchingPlanListDto generateMatchingPlanListDto) {
        offerItemService.generateMatchingPlan(generateMatchingPlanListDto);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "更新应收应付（废弃)", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生申请方案项目管理/更新应收应付")
    @PostMapping("updateGeneratePlan/{id}")
    public ResponseBo updateGeneratePlan(@RequestBody @Validated(StudentOfferItemReceivablePlanDto.Update.class) StudentOfferItemReceivablePlanDto studentOfferItemReceivablePlanDto) {
        return offerItemService.updateGeneratePlan(studentOfferItemReceivablePlanDto);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentOfferItemVo>
     * @Description: 详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/学生申请方案项目管理/学生详情")
    @GetMapping("/{id}")
    public ResponseBo<StudentOfferItemVo> detail(@PathVariable("id") Long id) {
        StudentOfferItemVo data = offerItemService.findOfferItemById(id);
        return new ResponseBo<>(data);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentOfferItemVo>
     * @Description: 详情
     * @Param [id]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "根据目标对象获取学生信息", notes = "id为此条数据id")
    @GetMapping("findStudentItemById")
    public ResponseBo<StudentOfferItemVo> findStudentItemById(@RequestParam("id") Long id, @RequestParam("type") String type) {
        StudentOfferItemVo data = offerItemService.findStudentItemById(id, type);
        return new ResponseBo<>(data);
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "获取应收应付历史佣金费率", notes = "")
    @GetMapping("getHistoryCommissionRatio")
    public ResponseBo<BaseSelectEntity> getHistoryCommissionRatio(@RequestParam(value = "fkInstitutionId",required = true)Long fkInstitutionId,
                                                            @RequestParam(value = "fkInstitutionCourseId",required = true)Long fkInstitutionCourseId,
                                                            @RequestParam(value = "flag",required = true)Boolean flag){
        return new ListResponseBo<>(offerItemService.getHistoryCommissionRatio(fkInstitutionId,fkInstitutionCourseId,flag));
    }

    /**
     * @return int
     * @Description:查询学习计划附件数量（凭证数）
     * @Param []
     * <AUTHOR>
     */
    @ApiOperation(value = "查询学习计划附件数量（凭证数）", notes = "fkStudentOfferItemId为学生申请方案项目Id，fkStudentOfferItemStepId为步骤Id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/申请方案详情/记录申请步骤")
    @PostMapping("getItemMediaCount")
    public ResponseBo<Integer> getItemMediaCount(@RequestParam("fkStudentOfferItemId") Long fkStudentOfferItemId, @RequestParam("fkStudentOfferItemStepId") Long fkStudentOfferItemStepId) {
        return new ResponseBo(offerItemService.getItemMediaCount(fkStudentOfferItemId, fkStudentOfferItemStepId));
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.MediaAndAttachedDto>
     * @Description: 查询学习计划附件
     * @Param [voSearchBean]
     * <AUTHOR>
     */
    @ApiOperation(value = "查询学习计划附件", notes = "keyWord为关键词")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生申请方案项目管理/查询附件")
    @PostMapping("getItemMedia")
    public ResponseBo<MediaAndAttachedVo> getItemMedia(@RequestBody SearchBean<MediaAndAttachedDto> voSearchBean) {
        List<MediaAndAttachedVo> staffMedia = offerItemService.getItemMedia(voSearchBean.getData(), voSearchBean);
        Page page = BeanCopyUtils.objClone(voSearchBean, Page::new);
        return new ListResponseBo<>(staffMedia, page);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.MediaAndAttachedDto>
     * @Description: 保存学习计划附件
     * @Param [mediaAttachedVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "保存学习计划附件")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生申请方案项目管理/保存接口")
    @PostMapping("upload")
    public ResponseBo<MediaAndAttachedVo> addAgentMedia(@RequestBody  ValidList<MediaAndAttachedDto> mediaAttachedVo) {
        return new ListResponseBo<>(offerItemService.addItemMedia(mediaAttachedVo));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 编辑评论
     * @Param [commentDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "编辑评论")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生申请方案项目管理/编辑评论")
    @PostMapping("editComment")
    public ResponseBo editComment(@RequestBody  CommentDto commentDto) {
        return SaveResponseBo.ok(offerItemService.editComment(commentDto));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.AgentContractVo>
     * @Description: 评论列表数据
     * @Param [searchBean]
     * <AUTHOR>
     */
    @ApiOperation(value = "评论列表数据")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生申请方案项目管理/查询")
    @PostMapping("getComments")
    public ResponseBo<CommentVo> getComment(@RequestBody SearchBean<CommentDto> searchBean) {
        List<CommentVo> datas = offerItemService.getComments(searchBean.getData(), searchBean);
        Page p = BeanCopyUtils.objClone(searchBean, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 关闭方案
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "关闭方案项目", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/学生申请方案项目管理/关闭方案项目")
    @PostMapping("unableOfferItem")
    public ResponseBo unableOffer(@RequestParam("id") Long id, @RequestParam("status") Long status) {
        offerItemService.unableOfferItem(id, status);
        return UpdateResponseBo.ok();
    }


    @ApiOperation(value = "更新学校提供商", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/学生申请方案项目管理/更新学校提供商")
    @PostMapping("updateInstitutionProvider")
    public SaveResponseBo modifyOfferItemInstitutionProvider(@RequestBody StudentOfferItemDto studentOfferItemDto){
        return offerItemService.modifyOfferItemInstitutionProvider(studentOfferItemDto.getId(), studentOfferItemDto.getFkInstitutionProviderId(), studentOfferItemDto.getFkInstitutionId(), studentOfferItemDto.getFkInstitutionChannelId());
    }

    /**
     * 激活学习计划
     *
     * @Date 12:37 2021/8/13
     * <AUTHOR>
     */
    @ApiOperation(value = "激活学习计划", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生申请方案项目管理/激活学习计划")
    @GetMapping("activationOfferItem/{id}")
    public ResponseBo activationOfferItem(@PathVariable("id") Long id) {
        offerItemService.activationOfferItem(id);
        return UpdateResponseBo.ok();
    }

    /**
     * 应收类型下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "应收类型下拉框数据", notes = "")
    @GetMapping("findReceivableTargetTypeSelect")
    public ResponseBo findReceivableTargetTypeSelect() {
        List<Map<String, Object>> datas = offerItemService.findReceivableTargetTypeSelect();
        return new ListResponseBo<>(datas);
    }


    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "新申请状态下拉", notes = "")
    @GetMapping("getNewAppStatusSelection")
    public ResponseBo<Map<String,Object>> getNewAppStatusSelection(){
        return new ListResponseBo<>(offerItemService.doGetNewAppStatusSelection());
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "新申请状态下拉", notes = "")
    @GetMapping("getItemNewAppStatusSelection")
    public ResponseBo<Map<String,Object>> getItemNewAppStatusSelection(){
        return new ListResponseBo<>(offerItemService.getItemNewAppStatusSelection());
    }

    /**
     * 应收\应付类型下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "应收应付类型下拉框数据", notes = "")
    @GetMapping("findTypeSelect")
    public ResponseBo findTypeSelect() {
        return new ListResponseBo<>(TableEnum.enumsTranslation2Arrays(TableEnum.SALE_TARGET_TYPE));
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "应付类型下拉框数据", notes = "")
    @GetMapping("findPayableTargetTypeSelect")
    public ResponseBo findPayableTargetTypeSelect() {
        List<Map<String, Object>> datas = offerItemService.findPayableTargetTypeSelect();
        return new ListResponseBo<>(datas);
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "佣金应付类型下拉框数据", notes = "")
    @GetMapping("findCommissionPayableTargetTypeSelect")
    public ResponseBo findCommissionPayableTargetTypeSelect() {
        List<Map<String, Object>> datas = offerItemService.findCommissionPayableTargetTypeSelect();
        return new ListResponseBo<>(datas);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 学生申请方案下拉
     * @Param [providerId]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "学生申请方案项目下拉", notes = "")
    @PostMapping("getStudentOfferItemSelect")
    public ResponseBo<BaseSelectEntity> getStudentOfferItemSelect(@RequestParam(value = "companyId", required = false) Long companyId,
                                                                  @RequestParam(value = "fkTypeKey") String fkTypeKey,
                                                                  @RequestParam(value = "studentId", required = false) Long studentId,
                                                                  @RequestParam(value = "keyWord", required = false) String keyWord) {
        return new ListResponseBo<>(offerItemService.getStudentOfferItemSelect(companyId, fkTypeKey, studentId, keyWord));
    }

    @VerifyPermission(IsVerify = false)
    @ApiIgnore
    @PostMapping("getOfferItemSelectByAgentId")
    public ResponseBo<BaseSelectEntity> getOfferItemSelectByAgentId(@RequestParam(value = "tableName") String tableName,
                                                                    @RequestParam(value = "agentId") Long agentId) {
        return new ListResponseBo<>(offerItemService.getOfferItemSelectByAgentId(tableName, agentId));
    }

    @VerifyPermission(IsVerify = false)
    @ApiIgnore
    @PostMapping("getOfferItemSelectByProviderId")
    public ResponseBo<BaseSelectEntity> getOfferItemSelectByProviderId(@RequestParam(value = "tableName") String tableName,
                                                                       @RequestParam(value = "providerId") Long providerId) {
        return new ListResponseBo<>(offerItemService.getOfferItemSelectByProviderId(tableName, providerId));
    }

    /**
     * 学习计划业务状态下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "学习计划业务状态下拉框数据", notes = "")
    @GetMapping("getConditionTypeSelect")
    public ResponseBo getConditionTypeSelect() {
        return new ListResponseBo<>(ProjectExtraEnum.enumsTranslation2Arrays(ProjectExtraEnum.LEARNING_PLAN_BUSINESS_STATUS));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description:入学失败原因下拉数据
     * @Param []
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "入学失败原因下拉数据", notes = "")
    @GetMapping("getEnrolFailureReason")
    public ResponseBo<EnrolFailureReason> getEnrolFailureReason() {
        return new ListResponseBo<>(offerItemService.getEnrolFailureReason());
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description:设置延迟入学 （废弃）
     * @Param [studentOfferItemVo]
     * <AUTHOR>
     */
//    @VerifyPermission(IsVerify = false)
//    @ApiOperation(value = "设置延迟入学", notes = "")
//    @PostMapping("updateDeferEntrance")
//    public ResponseBo updateDeferEntrance(@RequestBody StudentOfferItemDto studentOfferItemVo) {
//        return UpdateResponseBo.ok(offerItemService.updateDeferEntrance(studentOfferItemVo));
//    }

    /**
     * 设置延迟入学时间
     *
     * @Date 11:12 2023/3/3
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "设置延迟入学时间", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学习计划详情/设置延迟入学时间")
    @PostMapping("updateDeferEntranceTime")
    public ResponseBo updateDeferEntranceTime(@RequestBody @Validated DeferEntranceTimeUpdateDto deferEntranceTimeUpdateDto) {
        offerItemService.updateDeferEntranceTime(deferEntranceTimeUpdateDto);
        return UpdateResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description:附件类型下拉
     * @Param []
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "附件类型下拉", notes = "")
    @PostMapping("getMediaType")
    public ResponseBo getMediaType() {
        return new ListResponseBo<>(MediaTypeEnum.enumsTranslation2Arrays(MediaTypeEnum.MEDIA_TYPE));
    }

    /**
     * 学习计划学校tab
     *
     * @Date 16:21 2021/7/7
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "学习计划学校tab", notes = "offerId为学生申请方案id")
    @GetMapping("getOfferItemInstitutionTab/{offerId}")
    public ResponseBo<OfferItemInstitutionTabVo> getOfferItemInstitutionTab(@PathVariable("offerId") Long offerId) {
        List<OfferItemInstitutionTabVo> datas = offerItemService.getOfferItemInstitutionTab(offerId);
        return new ListResponseBo<>(datas);
    }

    /**
     * 学习计划、合同公式匹配排查
     *
     * @Date 16:04 2021/7/16
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "学习计划、合同公式匹配排查", notes = "num为学习计划编号,contractId为合同公式id")
    @GetMapping("checkItemConditionByItemNum")
    public ResponseBo<String> checkItemConditionByItemNum(@RequestParam("num") String num, @RequestParam("contractFormulaId") Long contractFormulaId) {
        String msg = offerItemService.checkItemConditionByItemNum(num, contractFormulaId);
        return new ResponseBo<>(msg);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 学习模式下拉
     * @Param []
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "学习模式下拉", notes = "")
    @GetMapping("findLearningModel")
    public ResponseBo findLearningModel() {
        List<Map<String, Object>> datas = offerItemService.findLearningModel();
        return new ListResponseBo<>(datas);
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "保险购买方式下拉", notes = "")
    @GetMapping("getInsuranceSelect")
    public ResponseBo getInsuranceSelect() {
        return new ListResponseBo<>(ProjectExtraEnum.enumsTranslation2Arrays(ProjectExtraEnum.INSURANCE_PURCHASE_METHOD));
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "导出学习计划Excel表头选项")
    @GetMapping("/getStudyPlanOptions")
    public ListResponseBo<FocExportVo> getStudyPlanOptions(@RequestParam(value = "fkCompanyId",required = false) Long fkCompanyId,@RequestParam(value = "fkCompanyIds",required = false) List<Long> fkCompanyIds) throws Exception {
        return new ListResponseBo<>(offerItemService.getStudyPlanOptions(fkCompanyId,fkCompanyIds));
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "导出学习计划Excel表头选项")
    @GetMapping("/getDLTOptions")
    public ListResponseBo<FocExportVo> getDLTOptions() throws Exception {
        List<Long> fkCompanyIds = new ArrayList<>(2);
        List<FocExportVo> studyPlanOptions = offerItemService.getStudyPlanOptions(null, fkCompanyIds);
        List<FocExportVo> list = new ArrayList<>();
        studyPlanOptions.stream().forEach(focExportVo -> {
            if (!focExportVo.getFiledName().equals("agentName") &&
                    !focExportVo.getFiledName().equals("bdRegion") &&
                    !focExportVo.getFiledName().equals("bdName") &&
                    !focExportVo.getFiledName().equals("outreachName") &&
                    !focExportVo.getFiledName().equals("adName") &&
                    !focExportVo.getFiledName().equals("consultingSupport") &&
                    !focExportVo.getFiledName().equals("fkInstitutionProviderName") &&
                    !focExportVo.getFiledName().equals("fkInstitutionGroupName") &&
                    !focExportVo.getFiledName().equals("studentFrom") &&
                    !focExportVo.getFiledName().equals("rcName") &&
                    !focExportVo.getFiledName().equals("arcName") &&
                    !focExportVo.getFiledName().equals("consultingDirector") &&
                    !focExportVo.getFiledName().equals("consultant") &&
                    !focExportVo.getFiledName().equals("consultantManager") &&
                    !focExportVo.getFiledName().equals("agentEmail")
            ) {
                list.add(focExportVo);
            }
        });
        return new ListResponseBo<>(list);
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentOfferItemVo>
     * @Description: 列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "学习计划汇总表", notes = "num代理编号 name代理名称")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生申请方案项目管理/学习计划汇总表")
    @PostMapping("getStudentOfferItemList")
    public ResponseBo<StudentOfferItemListVo> getStudentOfferItemList(@RequestBody @Validated StudentOfferItemCollectDto studentOfferItemListVo) {
        String[] times = {"0", "0"};//[0]-o-主SQL执行时间,[1]-f-远程调用时间
        List<StudentOfferItemListVo> datas = offerItemService.getStudentOfferItemList(studentOfferItemListVo, times);
        return new ListResponseBo<>(datas, times[0], times[1]);
    }

    @ApiOperation(value = "高级搜索匹配", notes = "高级搜索匹配")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生申请方案项目管理/学习计划汇总表高级搜索匹配")
    @PostMapping("getStudentOfferItemMismatchedList")
    public ResponseBo<StudentOfferItemMismatchedListVo> getStudentOfferItemMismatchedList(@RequestBody @Validated StudentOfferItemCollectDto studentOfferItemListVo) {
        String[] times = {"0", "0"};//[0]-o-主SQL执行时间,[1]-f-远程调用时间
        return new ResponseBo<>( offerItemService.getStudentOfferItemMismatchedList(studentOfferItemListVo, times));
    }

    @ApiOperation(value = "学习计划汇总表(DTL版)", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生申请方案项目管理/学习计划汇总表(DTL版)")
    @PostMapping("getDlt")
    public ResponseBo<StudentOfferItemListDLTVo> getDtl(@RequestBody @Validated StudentOfferItemDLTDto studentOfferItemDLTDto) {
        StudentOfferItemCollectDto copy = BeanUtil.copy(studentOfferItemDLTDto, StudentOfferItemCollectDto.class);
        copy.setChannelName("DLT");
        List<Long> institutionIds = new ArrayList<>();
        institutionIds.add(85L);
        institutionIds.add(88L);
        institutionIds.add(2069L);
        if (GeneralTool.isEmpty(copy.getInstitutionIds())) {
            copy.setInstitutionIds(institutionIds);
        } else {
            copy.setInstitutionIds(institutionIds.stream().filter(id -> copy.getInstitutionIds().contains(id)).collect(Collectors.toList()));
        }
        String[] times = {"0", "0"};//[0]-o-主SQL执行时间,[1]-f-远程调用时间
        List<StudentOfferItemListVo> datas = offerItemService.getStudentOfferItemList(copy, times);
        List<StudentOfferItemListDLTVo> copyList = new ArrayList<>();
        datas.stream().forEach(studentOfferItemListDto -> {
            StudentOfferItemListDLTVo copyDto = BeanUtil.copy(studentOfferItemListDto, StudentOfferItemListDLTVo.class);
            copyList.add(copyDto);
        });
        return new ListResponseBo<>(copyList, times[0], times[1]);
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "获取申请计划分页信息", notes = "num代理编号 name代理名称")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/学生申请方案项目管理/学习计划汇总表")
    @PostMapping("getStudentOfferPaginationInfo")
    public ResponseBo getStudentOfferPaginationInfo(@RequestBody SearchBean<StudentOfferItemCollectDto> page) {
        return offerItemService.doGetStudentOfferPaginationInfo(page.getData(), page);
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "获取申请计划分页信息(DTL版)", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/学生申请方案项目管理/学习计划汇总表(DTL版)")
    @PostMapping("getDLTPaginationInfo")
    public ResponseBo getDLTPaginationInfo(@RequestBody SearchBean<StudentOfferItemDLTDto> page) {
        SearchBean<StudentOfferItemCollectDto> pageCopy = new SearchBean<>();
        StudentOfferItemCollectDto copy = BeanUtil.copy(page.getData(), StudentOfferItemCollectDto.class);
        copy.setChannelName("DLT");
        List<Long> institutionIds = new ArrayList<>();
        institutionIds.add(85L);
        institutionIds.add(88L);
        institutionIds.add(2069L);
        if (GeneralTool.isEmpty(copy.getInstitutionIds())) {
            copy.setInstitutionIds(institutionIds);
        } else {
            copy.setInstitutionIds(institutionIds.stream().filter(id -> copy.getInstitutionIds().contains(id)).collect(Collectors.toList()));
        }
        pageCopy.setData(copy);
        pageCopy.setCurrentPage(page.getCurrentPage());
        pageCopy.setShowCount(page.getShowCount());
        pageCopy.setTotalResult(page.getTotalResult());
        pageCopy.setCurrentResult(page.getCurrentResult());
        pageCopy.setTotalPage(page.getTotalPage());
        return offerItemService.doGetStudentOfferPaginationInfo(pageCopy.getData(), pageCopy);
    }

    @Resource
    private DataImportService dataImportService;

    /**
     * 不要调用
     */
    @GetMapping("/updateFee")
    public void importData() {
//        dataImportService.updateData();
//        dataImportService.import2(names);
    }


    /**
     * feign调用 首页推送
     *
     * @Date 16:24 2021/11/17
     * <AUTHOR>
     */
    @ApiIgnore
    @VerifyLogin(IsVerify = false)
    @GetMapping("nettyPush")
    public void nettyPush(@RequestParam Long itemId) {
        nettyPushHelper.nettyPush(itemId);
    }

    /**
     * 导出学习计划汇总列表Excel
     *
     * @return
     * @
     */
    @ApiOperation(value = "导出学习计划汇总列表Excel", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生申请方案项目管理/导出学习计划汇总列表Excel")
    @PostMapping("/exportStudentOfferCollectExcel")
    @ResponseBody
    public void exportStudentOfferCollectExcel(HttpServletResponse response, @RequestBody StudentOfferItemCollectDto studentOfferItemVo) {
        offerItemService.exportStudentOfferCollectExcel(response, studentOfferItemVo);
    }



    /**
     * @Description: 验证课程逻辑删除
     * @Author: jack
     * @Date:11:13 2021/8/27
     */
    @ApiIgnore
    @GetMapping("deleteValidateCourse")
    public Boolean deleteValidateCourse(@RequestParam(value = "courseId") Long courseId) {
        return offerItemService.deleteValidateCourse(courseId);
    }

    /**
     * ISSUE一键申请状态下拉框
     *
     * @param
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "ISSUE一键申请状态下拉框", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生申请方案项目管理/ISSUE一键申请状态下拉框")
    @PostMapping("getIssueStatusSelect")
    public ResponseBo<Map<String, Object>> getIssueStatusSelect() {
        return new ListResponseBo<>(ProjectKeyEnum.enumsTranslation2Arrays(ProjectKeyEnum.ISSUE_STATUS));
    }

    /**
     * 学生来源下拉框
     *
     * @param
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "学生来源下拉框", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生申请方案项目管理/学生来源下拉框")
    @PostMapping("getStudentSourceSelect")
    public ResponseBo<Map<String, Object>> getStudentSourceSelect() {
        return new ListResponseBo<>(ProjectKeyEnum.enums2Arrays(ProjectKeyEnum.STUDENT_SOURCE));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentOfferItemVo>
     * @Description: 列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "成功客户列表", notes = "num代理编号 name代理名称")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/成功客户列表/查询")
    @PostMapping("getSuccessfulCustomersList")
    public ResponseBo<StudentOfferItemListVo> getSuccessfulCustomersList(@RequestBody @Validated StudentOfferItemListQueryDto studentOfferItemListVo) {
        String[] times = {"0", "0"};//[0]-o-主SQL执行时间,[1]-f-远程调用时间
        if (GeneralTool.isEmpty(studentOfferItemListVo.getStepOrderList())) {
            //步骤id总不会改了吧
            List<Integer> stepOrderList = new ArrayList<>();
            stepOrderList.add(5);
            stepOrderList.add(6);
            stepOrderList.add(7);
            stepOrderList.add(8);
            stepOrderList.add(10);
            studentOfferItemListVo.setStepOrderList(stepOrderList);
        }
        studentOfferItemListVo.setIsSuccess(true);
        List<StudentOfferItemListVo> datas = offerItemService.getCustomersList(studentOfferItemListVo, times);
        return new ListResponseBo<>(datas, times[0], times[1]);
    }


    @ApiOperation(value = "失败客户列表", notes = "num代理编号 name代理名称")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/失败客户列表/查询")
    @PostMapping("getFailureCustomersList")
    public ResponseBo<StudentOfferItemListVo> getFailureCustomersList(@RequestBody @Validated StudentOfferItemListQueryDto studentOfferItemListVo) {
        //[0]-o-主SQL执行时间,[1]-f-远程调用时间
        String[] times = {"0", "0"};

        //步骤id总不会改了吧
        List<Integer> stepOrderList = new ArrayList<>();
        stepOrderList.add(9);
        stepOrderList.add(15);
        studentOfferItemListVo.setStepOrderList(stepOrderList);
        studentOfferItemListVo.setIsSuccess(false);
        List<StudentOfferItemListVo> datas = offerItemService.getCustomersList(studentOfferItemListVo, times);
        return new ListResponseBo<>(datas, times[0], times[1]);
    }

    @ApiOperation(value = "失败客户列表分页信息", notes = "num代理编号 name代理名称")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DETAIL, description = "财务中心/失败客户列表/查询")
    @PostMapping("getFailureCustomersListPaginationInfo")
    @VerifyPermission(IsVerify = false)
    public ResponseBo getFailureCustomersListPaginationInfo(@RequestBody SearchBean<StudentOfferItemListQueryDto> page) {
        StudentOfferItemListQueryDto offerItemVo = page.getData();

        //步骤id总不会改了吧
        List<Integer> stepOrderList = new ArrayList<>();
        stepOrderList.add(9);
        offerItemVo.setStepOrderList(stepOrderList);
        offerItemVo.setIsSuccess(false);
        return offerItemService.doGetCustomersListPaginationInfo(offerItemVo, page);
    }

    @ApiOperation(value = "成功客户列表分页信息", notes = "num代理编号 name代理名称")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DETAIL, description = "财务中心/成功客户列表/查询")
    @PostMapping("getSuccessfulCustomersListPaginationInfo")
    @VerifyPermission(IsVerify = false)
    public ResponseBo getSuccessfulCustomersListPaginationInfo(@RequestBody SearchBean<StudentOfferItemListQueryDto> page) {
        StudentOfferItemListQueryDto offerItemVo = page.getData();
        if (GeneralTool.isEmpty(offerItemVo.getStepOrderList())) {
            //步骤id总不会改了吧
            List<Integer> stepOrderList = new ArrayList<>();
            stepOrderList.add(6);
            stepOrderList.add(7);
            stepOrderList.add(8);
            stepOrderList.add(10);
            offerItemVo.setStepOrderList(stepOrderList);
        }
        offerItemVo.setIsSuccess(true);
        return offerItemService.doGetCustomersListPaginationInfo(offerItemVo, page);
    }

    /**
     * @Description: 列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "申请结算列表（市场部）", notes = "num代理编号 name代理名称")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/成功客户列表（市场部）/查询")
    @PostMapping("getSuccessfulCustomersListForMarket")
    public ListResponseBo<StudentOfferItemListVo> getSuccessfulCustomersListForMarket(@RequestBody @Validated StudentOfferItemListQueryDto studentOfferItemListVo) {
        String[] times = {"0", "0"};//[0]-o-主SQL执行时间,[1]-f-远程调用时间
        List<StudentOfferItemListVo> datas = offerItemService.getSuccessfulCustomersListForMarket(studentOfferItemListVo, times, SecureUtil.getStaffInfo());
        return new ListResponseBo<>(datas, times[0], times[1]);
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "申请结算列表分页信息", notes = "num代理编号 name代理名称")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DETAIL, description = "财务中心/成功客户列表（市场部）/查询")
    @PostMapping("getSuccessfulCustomersForMarketPaginationInfo")
    public ResponseBo getSuccessfulCustomersListForMarketPaginationInfo(@RequestBody SearchBean<StudentOfferItemListQueryDto> page) {
        return offerItemService.doGetSuccessfulCustomersListForMarketPaginationInfo(page.getData(), page);
    }


    /**
     * 成功客户列表导出
     *
     * @Date 15:22 2022/3/2
     * <AUTHOR>
     */
    @ApiOperation(value = "成功客户列表导出", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生申请方案项目管理/成功客户列表导出")
    @PostMapping("successfullyExportedCustomerList")
    public void successfullyExportedCustomerList(@RequestBody StudentOfferItemDto studentOfferItemDto, HttpServletResponse httpServletResponse) {
        if (GeneralTool.isEmpty(studentOfferItemDto.getStepOrderList())) {
            //步骤id总不会改了吧
            List<Integer> stepOrderList = new ArrayList<>();
            stepOrderList.add(6);
            stepOrderList.add(7);
            stepOrderList.add(8);
            stepOrderList.add(10);
            studentOfferItemDto.setStepOrderList(stepOrderList);
        }
        studentOfferItemDto.setIsSuccess(true);
        offerItemService.ExportedCustomerList(studentOfferItemDto, httpServletResponse, "成功客户列表");
    }

    /**
     * 成功客户列表导出
     *
     * @Date 15:22 2022/3/2
     * <AUTHOR>
     */
    @ApiOperation(value = "失败客户列表导出", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生申请方案项目管理/失败客户列表导出")
    @PostMapping("failureExportedCustomerList")
    public void failureExportedCustomerList(@RequestBody StudentOfferItemDto studentOfferItemDto, HttpServletResponse httpServletResponse) {
        List<Integer> stepOrderList = new ArrayList<>();
        stepOrderList.add(9);
        studentOfferItemDto.setCurrentState(null);
        studentOfferItemDto.setStepOrderList(stepOrderList);
        studentOfferItemDto.setIsSuccess(false);
        offerItemService.ExportedCustomerList(studentOfferItemDto, httpServletResponse, "失败客户列表");
    }

    @ApiOperation(value = "成功客户列表设置入学失败")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生申请方案项目管理/成功客户列表设置入学失败")
    @PostMapping("enrolFailure")
    public ResponseBo enrolFailure(@RequestParam("itemId") Long itemId, @RequestParam("reasonId") Long reasonId, @RequestParam(value = "reason", required = false) String reason) {
        offerItemService.enrolFailure(itemId, reasonId, reason);
        return ResponseBo.ok();
    }


    //一键失败校验
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "一键失败校验", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/学生申请方案项目管理/一键失败校验")
    @PostMapping("enrolFailureCheck")
    public ResponseBo<EnrolFailureCheckVo> enrolFailureCheck(@RequestBody EnrolFailureCheckDto enrolFailureCheckDto) {
        return new ResponseBo<>(offerItemService.enrolFailureCheck(enrolFailureCheckDto));
    }

    @ApiOperation(value = "成功客户列表设置申请退押金")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生申请方案项目管理/成功客户列表设置申请退押金")
    @PostMapping("setStepApplyRefund")
    public ResponseBo<RStudentOfferItemStepSaveVo> setStepApplyRefund(@RequestParam("itemId") Long itemId) {
        RStudentOfferItemStepSaveVo rStudentOfferItemStepSaveVo = offerItemService.setStepApplyRefund(itemId);
        return new ResponseBo(rStudentOfferItemStepSaveVo);
    }

    /**
     * 创建应收应付状态
     *
     * @return
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "创建应收应付状态", notes = "")
    @GetMapping("getAPARStatus")
    public ResponseBo getAPARStatus() {
        List<Map<String, Object>> datas = offerItemService.getAPARStatus();
        return new ListResponseBo<>(datas);
    }

    /**
     * feign调用
     *
     * @Date 16:24 2021/11/17
     * <AUTHOR>
     */
    @ApiIgnore
    @GetMapping("findOfferItemByPayIds")
    public Map<Long, PayablePlanVo> findOfferItemByPayIds(@RequestParam(value = "ids") Set<Long> ids) {
        return offerItemService.findOfferItemByPayIds(ids);
    }

    /**
     * feign调用
     *
     * @Date 16:24 2021/11/17
     * <AUTHOR>
     */
    @ApiIgnore
    @GetMapping("findOfferItemByReceivableIds")
    public Map<Long, ReceivablePlanVo> findOfferItemByReceivableIds(@RequestParam(value = "ids") Set<Long> ids) {
        return offerItemService.findOfferItemByReceivableIds(ids);
    }

    /**
     * 更新Issue order Id
     *
     * @Date 11:18 2022/3/12
     * <AUTHOR>
     */
    @ApiOperation(value = "更新Issue order Id", notes = "id: 学习计划id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生申请方案项目管理/更新Issue order Id")
    @PostMapping("updateIssueRpaOrderId")
    public ResponseBo updateIssueRpaOrderId(@RequestBody IssueRpaDto issueRpaDto) {
        offerItemService.updateIssueRpaOrderId(issueRpaDto);
        return ResponseBo.ok();
    }


    @ApiOperation(value = "批量生成应收应付计划")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生申请方案项目管理/批量生成应收应付")
    @PostMapping("batchGenerateMatchingPlan")
    public ResponseBo batchGenerateMatchingPlan(@RequestBody @Validated BatchGenerateMatchingPlanDto batchGenerateMatchingPlanDto) {
        offerItemService.batchGenerateMatchingPlan(batchGenerateMatchingPlanDto);
        return ResponseBo.ok();
    }


    /**
     * @return void
     * @Description :导出学习计划Excel
     * @Param [response, conventionSponsorVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "导出学习计划Excel")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生申请方案项目管理/导出学习计划Excel")
    @PostMapping("/exportStudentOfferItemExcel")
    public void exportStudentOfferItemExcel(HttpServletResponse response, @RequestBody @Validated StudentOfferItemExportDto studentOfferItemExportDto) throws Exception {
        if (GeneralTool.isNotEmpty(studentOfferItemExportDto.getStudentOfferItemCollectVo().getFkCompanyId())) {
            if (!SecureUtil.validateCompany(studentOfferItemExportDto.getStudentOfferItemCollectVo().getFkCompanyId())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
            }
        } else if (GeneralTool.isNotEmpty(studentOfferItemExportDto.getStudentOfferItemCollectVo().getFkCompanyIds())) {
            if (!SecureUtil.validateCompanys(studentOfferItemExportDto.getStudentOfferItemCollectVo().getFkCompanyIds())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
            }
        } else {
            studentOfferItemExportDto.getStudentOfferItemCollectVo().setFkCompanyId(SecureUtil.getFkCompanyId());
        }
        CommonUtil.ok(response);

        offerItemService.exportStudentOfferItemExcel(studentOfferItemExportDto.getStudentOfferItemCollectVo(), studentOfferItemExportDto.getFocExportVos());
    }


    @ApiOperation(value = "导出学习计划Excel(DTL版)", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生申请方案项目管理/导出学习计划Excel(DTL版)")
    @PostMapping("/exportDLTExcel")
    @ResponseBody
    public void exportDLTExcel(HttpServletResponse response, @RequestBody DLTExportDto dltExportDto) {
        CommonUtil.ok(response);
        StudentOfferItemExportDto studentOfferItemExportDto = new StudentOfferItemExportDto();
        StudentOfferItemCollectDto copy = BeanUtil.copy(dltExportDto.getStudentOfferItemDLTVo(), StudentOfferItemCollectDto.class);
        copy.setChannelName("DLT");
        List<Long> institutionIds = new ArrayList<>();
        institutionIds.add(85L);
        institutionIds.add(88L);
        institutionIds.add(2069L);
        if (GeneralTool.isEmpty(copy.getInstitutionIds())) {
            copy.setInstitutionIds(institutionIds);
        } else {
            copy.setInstitutionIds(institutionIds.stream().filter(id -> copy.getInstitutionIds().contains(id)).collect(Collectors.toList()));
        }

        studentOfferItemExportDto.setStudentOfferItemCollectVo(copy);
        studentOfferItemExportDto.setFocExportVos(dltExportDto.getFocExportVos());
        offerItemService.exportStudentOfferItemExcel(studentOfferItemExportDto.getStudentOfferItemCollectVo(), studentOfferItemExportDto.getFocExportVos());
    }

//    /**
//     * @return void
//     * @Description :导出学习计划Excel
//     * @Param [response,conventionSponsorVo]
//     * <AUTHOR>
//     */
//    @VerifyPermission(IsVerify = false)
//    @ApiOperation(value = "导出学习计划Excel")
//    @GetMapping("/exportStudentOfferItemExcel")
//    @ResponseBody
//    public void exportStudentOfferItemExcel(HttpServletResponse response) throws YException {
//        StudentOfferItemDto studentOfferItemVo = new StudentOfferItemDto();
//        studentOfferItemVo.setFkCompanyId(1L);
//        offerItemService.exportStudentOfferItemExcel(response, studentOfferItemVo);
//    }

    /**
     * @return void
     * @Description :更新学习计划
     * @Param [response, conventionSponsorVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "更新学习计划")
    @PostMapping("/updateStudentOfferItem")
    @ResponseBody
    public ResponseBo updateStudentOfferItem(@RequestBody StudentOfferItemDto studentOfferItemDto) {
        offerItemService.updateStudentOfferItem(studentOfferItemDto);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "上一次申请学生周、月、季度、年度统计", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/学生申请方案项目管理/学习计划周、月、季度、年度统计")
    @PostMapping("LastStudentApplicationStatistics")
    public ResponseBo<ReportSale> LastStudentApplicationStatistics() {
        return offerItemService.lastStudentApplicationStatistics();
    }

    @ApiOperation(value = "申请学生周、月、季度、年度统计报表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/学生申请方案项目管理/学习计划周、月、季度、年度统计")
    @PostMapping("studentApplicationStatistics")
    public ResponseBo<ReportSale> studentApplicationStatistics(@RequestBody StudentApplicationStatisticsDto studentApplicationStatisticsDto) {
        return offerItemService.studentApplicationStatistics(studentApplicationStatisticsDto);
    }

    @ApiOperation(value = "导出学生周、月、季度、年度统计Excel")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "导出学生周、月、季度、年度统计Excel")
    @PostMapping("/exportStudentApplicationStatistics")
    @ResponseBody
    public void exportStudentApplicationStatisticsExcel(@RequestBody StudentApplicationStatisticsDto studentApplicationStatisticsDto, HttpServletResponse response) {
        offerItemService.exportStudentApplicationStatisticsExcel(studentApplicationStatisticsDto, response);
    }

    @ApiOperation(value = "业绩统计报表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/学生申请方案项目管理/业绩统计报表")
    @PostMapping("performanceStatistics")
    public ResponseBo<StatisticsVo> performanceStatistics(@RequestBody StatisticsDto statisticsDto) {
        return new ResponseBo(offerItemService.performanceStatistics(statisticsDto));
    }


    @ApiOperation(value = "代理省份送生统计表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/学生申请方案项目管理/代理省份送生统计表")
    @PostMapping("getPushStudentStatistics")
    public ResponseBo<StudentApplicationStatisticsVo> getPushStudentStatistics(@RequestBody PushStudentStatisticsDto pushStudentStatisticsDto) {
        return new ResponseBo(offerItemService.getPushStudentStatistics(pushStudentStatisticsDto));
    }

    @ApiOperation(value = "导出代理省份送生统计表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生申请方案项目管理/代理省份送生统计表")
    @PostMapping("exportPushStudentStatistics")
    public void exportPushStudentStatistics(@RequestBody PushStudentStatisticsDto pushStudentStatisticsDto,
                                            HttpServletResponse response) {
        offerItemService.exportPushStudentStatistics(pushStudentStatisticsDto, response);
    }

    @ApiOperation(value = "代理申请排行排序类型下拉")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生申请方案项目管理/代理申请排行排序类型下拉")
    @PostMapping("getAgentApplicationRankingSortTypeSelect")
    public ResponseBo<BaseSelectEntity> getAgentApplicationRankingSortTypeSelect() {
        return new ListResponseBo(ProjectExtraEnum.enumsTranslation2Arrays(ProjectExtraEnum.AGENT_APPLICATION_RANKING_SORT_TYPE));
    }


    @ApiOperation(value = "代理申请排行")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生申请方案项目管理/代理申请排行")
    @PostMapping("getAgentApplicationRanking")
    public ResponseBo<AgentApplicationRankingVo> getAgentApplicationRanking(@RequestBody SearchBean<AgentApplicationRankingQueryDto> page) {
        List<AgentApplicationRankingVo> datas = offerItemService.getAgentApplicationRanking(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    @ApiOperation(value = "导出代理申请排行Excel")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生申请方案项目管理/代理申请排行")
    @PostMapping("exportAgentApplicationRanking")
    public void exportAgentApplicationRanking(@RequestBody AgentApplicationRankingQueryDto applicationRankingQueryVo, HttpServletResponse response) {
           offerItemService.exportAgentApplicationRanking(applicationRankingQueryVo,response);
    }


    @ApiOperation(value = "VIP统计")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生申请方案项目管理/VIP统计")
    @PostMapping("getVipStatistics")
    public ResponseBo<VipStatisticsVo> getVipStatistics(@RequestBody SearchBean<VipStatisticsDto> page){
        List<VipStatisticsVo> datas = offerItemService.getVipStatistics(page.getData(),page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }


    @ApiOperation(value = "代理业绩统计")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER,type = LoggerOptTypeConst.LIST,description = "销售中心/学生申请方案项目管理/代理业绩统计")
    @PostMapping("agentPerformanceStatistics")
    public ResponseBo<AgentPerformanceStatisticsVo> agentPerformanceStatistics(@Valid @RequestBody AgentPerformanceStatisticsDto agentPerformanceStatisticsDto){
        return new ResponseBo<>(offerItemService.agentPerformanceStatistics(agentPerformanceStatisticsDto));
    }



    @ApiOperation(value = "学校申请统计")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER,type = LoggerOptTypeConst.LIST,description = "销售中心/学生申请方案项目管理/学生申请统计")
    @PostMapping("institutionApplicationStatistics")
    public ResponseBo<InstitutionApplicationStatisticsVo> institutionApplicationStatistics( @RequestBody SearchBean<InstitutionApplicationStaticsQueryDto> page){
        return offerItemService.institutionApplicationStatistics(page.getData(), page);
    }


    @ApiOperation(value = "学校各申请指标统计", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生申请方案项目管理/学校各申请指标统计")
    @PostMapping("institutionApplicationMetrics")
    public ResponseBo<InstitutionApplicationMetricsVo> institutionApplicationMetrics(@Valid @RequestBody SearchBean<InstitutionApplicationMetricsDto> page) {
        List<InstitutionApplicationMetricsVo> datas = offerItemService.institutionApplicationMetrics(page.getData(),page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    @ApiOperation(value = "导出学校各申请指标统计", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生申请方案项目管理/学校各申请指标统计导出")
    @PostMapping("exportInstitutionApplicationMetrics")
    public void exportInstitutionApplicationMetrics(HttpServletResponse response,@RequestBody InstitutionApplicationMetricsDto metricsDto) {
        offerItemService.exportInstitutionApplicationMetrics(response,metricsDto);
    }


    @ApiOperation(value = "导出学校申请统计")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER,type = LoggerOptTypeConst.LIST,description = "销售中心/学生申请方案项目管理/学生申请统计")
    @PostMapping("exportInstitutionApplicationStatistics")
    public void exportInstitutionApplicationStatistics(@Valid @RequestBody InstitutionApplicationStaticsQueryDto institutionApplicationStaticsVo, HttpServletResponse response){
        offerItemService.exportInstitutionApplicationStatistics(institutionApplicationStaticsVo,response);
    }


    @ApiOperation(value = "学校入学年度转化率统计")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER,type = LoggerOptTypeConst.LIST,description = "销售中心/学生申请方案项目管理/学校入学年度转化率统计")
    @PostMapping("getInstitutionEnrolledConversionRate")
    public IEConversionRateResultVo<InstitutionEnrolledConversionRateVo> getInstitutionEnrolledConversionRate(@Valid @RequestBody SearchBean<InstitutionEnrolledConversionRateDto> page){
        return offerItemService.getInstitutionEnrolledConversionRate(page.getData(),page);
    }

    @ApiOperation(value = "学校入学年度转化率年度下拉")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER,type = LoggerOptTypeConst.LIST,description = "销售中心/学生申请方案项目管理/学校入学年度转化率年度下拉")
    @PostMapping("getYearSelect")
    public ResponseBo getYearSelect(){
        return new ListResponseBo<>(offerItemService.getYearSelect());
    }


    @ApiOperation(value = "学校入学年度转化率学校下拉")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER,type = LoggerOptTypeConst.LIST,description = "销售中心/学生申请方案项目管理/学校入学年度转化率学校下拉")
    @PostMapping("getInstitutionSelect")
    public ResponseBo getInstitutionSelect(@RequestBody InstitutionSelectDto institutionSelectDto){
        return new ListResponseBo<>(offerItemService.getInstitutionSelect(institutionSelectDto));
    }


    @ApiOperation(value = "导出学校入学年度转化率统计")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER,type = LoggerOptTypeConst.LIST,description = "销售中心/学生申请方案项目管理/导出学校入学年度转化率统计")
    @PostMapping("exportInstitutionEnrolledConversionRate")
    public void exportInstitutionEnrolledConversionRate(@Valid @RequestBody InstitutionEnrolledConversionRateDto rateVo, HttpServletResponse response){
        offerItemService.exportInstitutionEnrolledConversionRate(rateVo,response);
    }


    /**
     * 其他金额下拉框
     *
     * @return
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "其他金额下拉框", notes = "")
    @GetMapping("getBonusTypeSelect")
    public ResponseBo getBonusTypeSelect() {
        List<Map<String, Object>> datas = offerItemService.getBonusTypeSelect();
        return new ListResponseBo<>(datas);
    }

    /**
     * 结算状态下拉框
     *
     * @return
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "结算状态下拉框", notes = "")
    @GetMapping("getSettlementTypeSelect")
    public ResponseBo getSettlementTypeSelect() {
        List<Map<String, Object>> datas = offerItemService.getSettlementTypeSelect();
        return new ListResponseBo<>(datas);
    }

    @PostMapping("updateInstitutionProviderId")
    @ApiOperation(value = "批量更新应收/应付学校供应商、渠道信息")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER,type = LoggerOptTypeConst.LIST,description = "销售中心/应收计划管理/批量修改信息")
    public ResponseBo updateInstitutionProviderId(@RequestBody BatchUpdateReceivableDto updateReceivableVo) {
        ResponseBo responseBo = offerItemService.updateInstitutionProviderId(updateReceivableVo);
        return responseBo;
    }


    @ApiOperation("设置无佣金")
    @VerifyPermission(IsVerify = false)
    @PostMapping("updateNoCommission")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER,type = LoggerOptTypeConst.EDIT,description = "销售中心/成功客户列表/设置无佣金")
    public ResponseBo updateNoCommission(@RequestParam("id") Long id,@RequestParam(value = "appRemark",required = false)String appRemark) {
        offerItemService.updateNoCommission(id,appRemark);
        return ResponseBo.ok();
    }

    @ApiOperation("设置无佣金校验")
    @VerifyPermission(IsVerify = false)
    @PostMapping("checkUpdateNoCommission")
    public ResponseBo<Boolean> checkUpdateNoCommission(@RequestParam("id") Long id){
        offerItemService.checkUpdateNoCommission(id);
        return new ResponseBo(offerItemService.checkUpdateNoCommission(id));
    }

    @ApiOperation("设置取消无佣金")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生管理/设置取消无佣金")
    @PostMapping("updateCommission")
    public ResponseBo updateCommission(@RequestParam("id") Long id,@RequestParam(value = "activePayablePlan",required = false) Boolean activePayablePlan) {
        offerItemService.updateCommission(id,activePayablePlan);
        return ResponseBo.ok();
    }


    /**
     * 申请结算汇总导出
     *
     * @Date 15:22 2022/3/2
     * <AUTHOR>
     */
    @ApiOperation(value = "申请结算汇总导出", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生管理/申请结算汇总导出")
    @PostMapping("exportedCustomerListForMarket")
    public void exportedCustomerListForMarket(@RequestBody StudentOfferItemListQueryDto studentOfferItemListVo, HttpServletResponse httpServletResponse) {
        CommonUtil.ok(httpServletResponse);
        offerItemService.exportedCustomerListForMarket(studentOfferItemListVo);
    }

    @ApiOperation(value = "院校推荐", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生管理/院校推荐")
    @PostMapping("recommendedViewing")
    public ListResponseBo<RecommendedViewingResultDto> recommendedViewing(@RequestBody RecommendedViewingQueryDto queryVo){
        return offerItemService.recommendedViewing(queryVo);
    }

    @ApiOperation(value = "获取案例分析统计", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生管理/获取案例分析统计")
    @PostMapping("getCaseStudyStatistics")
    public ResponseBo<CaseStudyResultsDto> caseStudyStatistics(@RequestBody RecommendedViewingQueryDto queryVo){
        return offerItemService.caseStudyStatistics(queryVo);
    }

    @ApiOperation(value = "院校推荐详情", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生管理/院校推荐详情")
    @PostMapping("getRecommendedViewingDetails")
    public ListResponseBo<RecommendedViewingResultDto> getRecommendedViewingDetails(@RequestBody CaseStudyDetailsQueryDto queryVo){
        return offerItemService.getRecommendedViewingDetails(queryVo);
    }

    @ApiOperation("批量修改学习计划(课程、渠道、提供商)")
    @PostMapping("updateBatchStudentOfferItem")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER,type = LoggerOptTypeConst.EDIT,description = "销售中心/数据处理/申请计划/批量修改学习计划(课程、渠道、提供商)")
    public ResponseBo updateBatchStudentOfferItem(@RequestBody StudentOfferItemBatchProcessingDto studentOfferItemBatchProcessingDto) {
        offerItemService.updateBatchStudentOfferItem(studentOfferItemBatchProcessingDto);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "获取最新的三条学费", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/学生申请方案项目管理/获取最新的三条学费")
    @GetMapping("getApplicationPlanTheLatestThreeTuitionFees")
    @VerifyPermission(IsVerify = false)
    public ResponseBo<String> getApplicationPlanTheLatestThreeTuitionFees(@RequestParam("fkCompanyId") Long fkCompanyId,
        @RequestParam("institutionId")Long institutionId,@RequestParam("courseId")Long courseId,@RequestParam(value = "oldCourseName",required = false)String oldCourseName) {
        return new ListResponseBo<>(offerItemService.getApplicationPlanTheLatestThreeTuitionFees(fkCompanyId,institutionId,courseId,oldCourseName));
    }

    @ApiOperation(value = "获取OS编辑权限限制")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER,type = LoggerOptTypeConst.DETAIL,description = "销售中心/学生申请方案项目管理/获取OS编辑权限限制")
    @GetMapping("getOsPermissionRestrictions")
    @VerifyPermission(IsVerify = false)
    public ResponseBo getOsPermissionRestrictions(@RequestParam("id") Long id) {
        return new ResponseBo<>(offerItemService.getOsPermissionRestrictions(id));
    }


    @ApiModelProperty(value = "通过申请计划获取方案联系人邮箱",notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生申请方案项目管理/批量学生申请方案项目成员")
    @GetMapping("getContactPersonEmailMap")
    public ResponseBo getContactPersonEmailMap(@RequestParam("id") Long id) {
        return new ResponseBo<>(offerItemService.getContactPersonEmailMap(id));
    }

    @ApiModelProperty(value = "学习计划截止时间提醒",notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生申请方案项目管理/学习计划截止时间提醒")
    @PostMapping("dataImportItemEmail")
    public ResponseBo dataImportItemEmail() {
        //offerItemService.dataImportItemEmail();
        return new ResponseBo<>();
    }


    @ApiModelProperty(value = "补充设置信息",notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生申请方案项目管理/补充设置信息")
    @PostMapping("updateDepositTuitionTime")
    public ResponseBo updateDepositTuitionTime(@RequestBody DepositTuitionTimeDto depositTuitionTimeDto) {
        offerItemService.updateDepositTuitionTime(depositTuitionTimeDto);
        return ResponseBo.ok();
    }

    @ApiModelProperty(value = "取消 / 设置跟随父计划步骤绑定",notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生申请方案项目管理/取消或设置跟随父计划步骤绑定")
    @PostMapping("updateIsStepFollow")
    public ResponseBo updateIsStepFollow(@RequestParam("id") Long id,@RequestParam("isStepFollow") Boolean isStepFollow) {
        offerItemService.updateIsStepFollow(id,isStepFollow);
        return ResponseBo.ok();
    }

    @VerifyPermission(IsVerify = false)
    @ApiModelProperty(value = "支付方式下拉",notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生申请方案项目管理/支付方式下拉")
    @PostMapping("getPaymentMethodSelect")
    public ResponseBo<BaseSelectEntity> getPaymentMethodSelect() {
        return new ListResponseBo(ProjectExtraEnum.enumsTranslation2Arrays(ProjectExtraEnum.PAYMENT_METHOD));
    }

    @ApiOperation(value = "获取学习计划配置", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/学生申请方案项目状态步骤管理/获取学习计划配置")
    @GetMapping("getItemLimtit")
    public ResponseBo<OfferItemLimitConfigVo> getItemLimtit(@RequestParam(value = "fkStudentOfferItemId",required = false) Long fkStudentOfferItemId) {
        return new ResponseBo<>(offerItemService.getItemLimtit(fkStudentOfferItemId));
    }

    @ApiOperation(value = "创建任务", notes = "根据申请计划汇总搜索结果创建任务")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生申请方案项目管理/创建任务")
    @PostMapping("createTask")
    public ResponseBo createTask(@RequestBody @Validated StudentOfferItemCollectCreateTaskDto studentOfferItemCollectCreateTaskDto) {
        offerItemService.createTask(studentOfferItemCollectCreateTaskDto);
        return ResponseBo.ok();
    }

    @VerifyPermission(IsVerify = false)
    @ApiModelProperty(value = "申请国家下拉",notes = "根据学生id获取所申请的国家列表")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生资源管理/学生资源新增/学生来源绑定列表/申请国家下拉")
    @PostMapping("getAreaCountrySelect")
    public ResponseBo<BaseSelectEntity> getAreaCountrySelect(@RequestParam("studentId") Long studentId) {
        return new ListResponseBo<>(offerItemService.getAreaCountrySelect(studentId));
    }

    @ApiModelProperty(value = "使用父申请计划凭证",notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生申请方案项目步骤记录管理/使用父申请计划凭证")
    @PostMapping("useParentApplicationPlanVoucher")
    public ResponseBo<BaseSelectEntity> useParentApplicationPlanVoucher(@RequestBody @Validated OfferItemVoucherDto offerItemVoucherDto) {
        offerItemService.useParentApplicationPlanVoucher(offerItemVoucherDto);
        return ResponseBo.ok();
    }

    @ApiModelProperty(value = "检测父申请计划凭证",notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生申请方案项目步骤记录管理/检测父申请计划凭证")
    @PostMapping("checkParentApplicationPlanVoucher")
    public ResponseBo<Boolean> checkParentApplicationPlanVoucher(@RequestParam("parentItemId") Long parentItemId, @RequestParam("fkStudentOfferItemStepId") Long fkStudentOfferItemStepId) {
        ResponseBo responseBo = new ResponseBo(true);
        responseBo.setCode(ErrorCodeEnum.REQUEST_OK.getCode());
        responseBo.setData(offerItemService.checkParentApplicationPlanVoucher(parentItemId, fkStudentOfferItemStepId));
        return responseBo;
    }

    @ApiModelProperty(value = "发起费用代付", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/申请计划汇总/申请计划详情/发起费用代付")
    @PostMapping("initiatingPaymentFee")
    public ResponseBo initiatingFeePayment(@RequestBody @Validated OfferItemPaymentFeeDto offerItemPaymentFeeDto) {
        offerItemService.initiatingFeePayment(offerItemPaymentFeeDto);
        return ResponseBo.ok();
    }

    @VerifyPermission(IsVerify = false)
    @ApiModelProperty(value = "代付类型下拉", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/申请计划汇总/申请计划详情/发起费用代付/代付类型下拉")
    @PostMapping("getPaymentTypeSelect")
    public ResponseBo getPaymentTypeSelect() {
        return new ListResponseBo<>(ProjectExtraEnum.enums2Arrays(ProjectExtraEnum.PAYMENT_TYPE));
    }

    @VerifyPermission(IsVerify = false)
    @ApiModelProperty(value = "代付状态下拉", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/申请计划汇总/申请计划详情/代付费用日志/代付状态下拉")
    @PostMapping("getPaymentStatusSelect")
    public ResponseBo getPaymentStatusSelect() {
        return new ListResponseBo<>(ProjectExtraEnum.enums2Arrays(ProjectExtraEnum.PAYMENT_STATUS));
    }

}

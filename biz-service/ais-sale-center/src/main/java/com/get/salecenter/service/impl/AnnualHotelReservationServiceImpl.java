package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.consts.CacheKeyConstants;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseEntity;
import com.get.core.redis.cache.GetRedis;
import com.get.core.tool.utils.BeanUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.ConventionHotelMapper;
import com.get.salecenter.dao.sale.ConventionHotelRoomMapper;
import com.get.salecenter.dao.sale.ConventionHotelRoomPersonMapper;
import com.get.salecenter.dao.sale.ConventionPersonMapper;
import com.get.salecenter.dao.sale.ConventionPersonRegistrationMapper;
import com.get.salecenter.dao.sale.ConventionRegistrationMapper;
import com.get.salecenter.service.*;
import com.get.salecenter.vo.AnnualHotelReservationVo;
import com.get.salecenter.vo.ConventionHotelVo;
import com.get.salecenter.vo.ConventionRegistrationVo;
import com.get.salecenter.entity.ConventionHotel;
import com.get.salecenter.entity.ConventionHotelRoom;
import com.get.salecenter.entity.ConventionHotelRoomPerson;
import com.get.salecenter.entity.ConventionPerson;
import com.get.salecenter.entity.ConventionPersonRegistration;
import com.get.salecenter.entity.ConventionRegistration;
import com.get.salecenter.utils.GetDayUtils;
import com.get.salecenter.utils.MyDateUtils;
import com.get.salecenter.utils.MyStringUtils;
import com.get.salecenter.dto.AnnualHotelReservationDto;
import com.get.salecenter.dto.ConventionHotelRoomDto;
import com.get.salecenter.dto.ConventionHotelRoomPersonDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.StringJoiner;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @author: Hardy
 * @create: 2021/6/22 11:11
 * @verison: 1.0
 * @description:
 */
@Service
public class AnnualHotelReservationServiceImpl implements IAnnualHotelReservationService {
    @Resource
    private ConventionRegistrationMapper conventionRegistrationMapper;
    @Resource
    private ConventionPersonMapper conventionPersonMapper;
    @Resource
    private ConventionPersonRegistrationMapper conventionPersonRegistrationMapper;
    @Resource
    private IConventionHotelService conventionHotelService;
    @Resource
    private IConventionHotelRoomService conventionHotelRoomService;
    @Resource
    private ConventionHotelRoomMapper conventionHotelRoomMapper;
    @Resource
    private ConventionHotelRoomPersonMapper conventionHotelRoomPersonMapper;
    @Resource
    private IFormCommonService formCommonService;
    @Resource
    private ConventionHotelMapper conventionHotelMapper;
    @Resource
    private GetRedis getRedis;
    @Resource
    private IAnnualReservationFormService annualReservationFormService;
    @Resource
    private IConventionPersonService conventionPersonService;

    /**
     * 新增或修改表单信息
     *
     * @param annualHotelReservationDto
     * @
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addAnnualHotelReservation(AnnualHotelReservationDto annualHotelReservationDto) {
        if (GeneralTool.isEmpty(annualHotelReservationDto.getReceiptCode())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("receipt_code_error"));
        }
        Boolean telFlag = annualReservationFormService.validatedTel(annualHotelReservationDto.getFkConventionId(), annualHotelReservationDto.getTel(), null);
        if (!telFlag) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("REPEATED_PHONE_NUMBER"));
        }

        //区分新增还是修改
//        if (GeneralTool.isNotEmpty(annualHotelReservationDto.getId())) {
//            //修改-更新参会人信息
//            String roomTypeName = annualHotelReservationDto.getRoomTypeName();
//            Long conventionId = conventionRegistrationMapper.getConventionIdByReceiptCode(annualHotelReservationDto.getReceiptCode());
//            //根据峰会id查出所有房间类型，获取其中指定房型的id
//            Long roomTypeId = conventionHotelService.getRoomTypeId(conventionId, roomTypeName);
//
//            ConventionPerson conventionPerson = conventionPersonMapper.selectById(annualHotelReservationDto.getId());
//            ConventionPerson personForUpdate = BeanCopyUtils.objClone(conventionPerson, ConventionPerson::new);
//            if (GeneralTool.isEmpty(conventionPerson)) {
//                throw new GetServiceException(LocaleMessageUtils.getMessage("convention_person_not_found"));
//            }
////            conventionPerson.setRemark("");
//            setInfo(annualHotelReservationDto, conventionPerson);
//            //修改展位名（不管是否修改都重新添加）
//            if (annualHotelReservationDto.getConventionRegistrationId() == 0L) {
////                conventionPerson.setRemark(annualHotelReservationDto.getInstitutionName());
//                conventionPerson.setCompany(annualHotelReservationDto.getInstitutionName());
//                //添加人信息
//                updateUserInfoToEntity(conventionPerson);
//                conventionPersonMapper.updateById(conventionPerson);
//            } else {
//                //单个展位机构名加备注
//                if (annualHotelReservationDto.getInstitutionName().equals(annualHotelReservationDto.getBoothName())) {
////                    conventionPerson.setRemark(annualHotelReservationDto.getInstitutionName());
//                    conventionPerson.setCompany(annualHotelReservationDto.getInstitutionName());
//                }
//                ConventionRegistration conventionRegistration = conventionRegistrationMapper.selectById(annualHotelReservationDto.getConventionRegistrationId());
//                //选择【；】其中一种
//                if (!conventionRegistration.getBoothName().equals(annualHotelReservationDto.getBoothName())) {
////                    conventionPerson.setRemark(annualHotelReservationDto.getBoothName());
////                    conventionPerson.setRemark(annualHotelReservationDto.getInstitutionName());
//                    conventionPerson.setCompany(annualHotelReservationDto.getInstitutionName());
//                }
//                //单个展位、多展位非机构名-加中间表
//                //添加人信息
//                addUserInfoToEntity(conventionPerson);
//                conventionPersonMapper.updateById(conventionPerson);
//                deleteTable(conventionPerson.getId());
//                insertTable(conventionPerson.getId(), annualHotelReservationDto);
//            }
//
//            //更新安排房间-安排床位
//            long voDays = Long.parseLong(String.valueOf(MyDateUtils.differentDays(annualHotelReservationDto.getCheckInDate(), annualHotelReservationDto.getCheckOutDate())));
//            ConventionHotelRoomDto conventionHotelRoomVo = getConventionHotelRoomVo(annualHotelReservationDto, conventionId, roomTypeId, voDays);
//            if (GeneralTool.isEmpty(conventionHotelRoomVo)) {
//                throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
//            }
//            if (0 == conventionHotelRoomVo.getRooms() || 0 == conventionHotelRoomVo.getDays()) {
//                throw new GetServiceException(LocaleMessageUtils.getMessage("room_num_stay_day"));
//            }
//
//            //修改床位信息
//            List<ConventionHotelRoom> hotelRoom = getConventionHotelRoom(conventionPerson.getId());
//            if (GeneralTool.isNotEmpty(hotelRoom)) {
//                ConventionHotelRoom firstHotelRoom = hotelRoom.get(0);
//                long dbDays = Long.parseLong(String.valueOf(MyDateUtils.differentDays(personForUpdate.getCheckInTime(), personForUpdate.getCheckOutTime())));
//                //判断日期 - 开始日期相同
//                if (annualHotelReservationDto.getCheckInDate().compareTo(personForUpdate.getCheckInTime()) != 0 || annualHotelReservationDto.getCheckOutDate().compareTo(personForUpdate.getCheckOutTime()) != 0) {
//                    //覆盖日期
//                    if (voDays == dbDays) {
//                        //替换房间日期
//                        updateConventionHotelRoom(annualHotelReservationDto, hotelRoom);
//                    }
//                    //退房日期推后
//                    if (voDays > dbDays) {
//                        //替换房间日期
//                        updateConventionHotelRoom(annualHotelReservationDto, hotelRoom);
//                        //新增多出日期的房间数和床位
//                        for (int j = hotelRoom.size(); j < voDays; j++) {
//                            Date date = GetDayUtils.addDays(annualHotelReservationDto.getCheckInDate(), (long) j);
//                            ConventionHotelRoom conventionHotelRoom = new ConventionHotelRoom();
//                            conventionHotelRoom.setFkConventionHotelId(firstHotelRoom.getFkConventionHotelId());
//                            conventionHotelRoom.setSystemRoomNum(firstHotelRoom.getSystemRoomNum());
//                            conventionHotelRoom.setStayDate(date);
//                            if (GeneralTool.isNotEmpty(firstHotelRoom.getHotelRoomNum())) {
//                                conventionHotelRoom.setHotelRoomNum(firstHotelRoom.getHotelRoomNum());
//                            }
//                            addUserInfoToEntity(conventionHotelRoom);
//                            conventionHotelRoomMapper.insertSelective(conventionHotelRoom);
//                            long roomId = conventionHotelRoom.getId();
//                            ConventionHotelRoomPersonDto conventionHotelRoomPersonVo = new ConventionHotelRoomPersonDto();
//                            //添加-床位
//                            conventionHotelRoomPersonVo.setKey(false);
//                            //conventionHotelId 房型id；fkConventionHotelRoomId 房间id；fkConventionPersonId 住房人id
//                            conventionHotelRoomPersonVo.setConventionHotelId(conventionHotelRoomVo.getFkConventionHotelId());
//                            conventionHotelRoomPersonVo.setFkConventionPersonId(conventionPerson.getId());
//                            conventionHotelRoomPersonVo.setFkConventionHotelRoomId(roomId);
//                            ConventionHotelRoomPerson conventionHotelRoomPerson = BeanCopyUtils.objClone(conventionHotelRoomPersonVo, ConventionHotelRoomPerson::new);
//                            addUserInfoToEntity(conventionHotelRoomPerson);
//                            conventionHotelRoomPersonMapper.insertSelective(conventionHotelRoomPerson);
//                        }
//                    }
//                    if (voDays < dbDays) {
//                        //替换房间日期
//                        for (int i = 0; i < (int) voDays; i++) {
//                            Date date = GetDayUtils.addDays(annualHotelReservationDto.getCheckInDate(), (long) i);
//                            hotelRoom.get(i).setStayDate(date);
//                            updateUserInfoToEntity(hotelRoom.get(i));
//                            conventionHotelRoomMapper.updateById(hotelRoom.get(i));
//                        }
//                        //删除多余的房间和床位
//                        for (int j = (int) voDays; j < hotelRoom.size(); j++) {
//                            ConventionHotelRoom conventionHotelRoom = hotelRoom.get(j);
//                            //删除对应床位
//                            deleteConventionHotelRoomPerson(conventionPerson, conventionHotelRoom);
//                            conventionHotelRoomMapper.deleteById(conventionHotelRoom);
//                        }
//                    }
//                }
//            }
//        } else {

        try {
            Boolean flag = getRedis.setNx(CacheKeyConstants.CONVENTION_REGISTRATION_CACHE_KEY, 1, 600L);
            int i = 0;
            while (!flag) {
                if (i >= 5) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("SYSTEM_BUSY"));
                }
                Thread.sleep(2000);
                flag = getRedis.setNx(CacheKeyConstants.CONVENTION_REGISTRATION_CACHE_KEY, 1, 600L);
                i = i + 1;
            }
            //新增
            String roomTypeName = annualHotelReservationDto.getRoomTypeName();
            ConventionPerson conventionPerson = new ConventionPerson();
            //根据回执码获取峰会id
            Long conventionId = conventionRegistrationMapper.getConventionIdByReceiptCode(annualHotelReservationDto.getReceiptCode());

            //根据峰会id查出所有房间类型，获取其中指定房型的id
//            ConventionHotel conventionHotel = conventionHotelService.getRoomTypeId(conventionId, roomTypeName);
            Long roomTypeId = null;
            if (annualHotelReservationDto.getIsBookHotel()) {
                ConventionHotel conventionHotel = conventionHotelMapper.selectOne(Wrappers.<ConventionHotel>lambdaQuery().eq(ConventionHotel::getFkConventionId, conventionId).eq(ConventionHotel::getId, annualHotelReservationDto.getFkConventionHotelId()));
                roomTypeId = conventionHotel.getId();

                //房间数限制
                Long totalRooms = conventionHotelRoomMapper.getHotelRoomCountByRoomTypeId(roomTypeId);
                if (totalRooms >= conventionHotel.getQuotaLimit()) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("ROOM_IS_FULL"));
                }
            }

            conventionPerson.setFkConventionId(conventionId);
            conventionPerson.setRemark(annualHotelReservationDto.getRemark());
            setInfo(annualHotelReservationDto, conventionPerson);
            //多个展位的纯机构名-不保存关系表-机构名添加备注
            if (annualHotelReservationDto.getConventionRegistrationId() == 0L) {
                //                conventionPerson.setRemark(annualHotelReservationDto.getInstitutionName());
                conventionPerson.setCompany(annualHotelReservationDto.getInstitutionName());
                //添加人信息
                addUserInfoToEntity(conventionPerson);
                conventionPersonMapper.insertSelective(conventionPerson);
            } else {
                //单个展位机构名加备注
                if (annualHotelReservationDto.getInstitutionName().equals(annualHotelReservationDto.getBoothName())) {
//                    conventionPerson.setRemark(annualHotelReservationDto.getInstitutionName());
                    conventionPerson.setCompany(annualHotelReservationDto.getInstitutionName());
                }
                ConventionRegistration conventionRegistration = conventionRegistrationMapper.selectById(annualHotelReservationDto.getConventionRegistrationId());
                //选择【；】其中一种
                if (!conventionRegistration.getBoothName().equals(annualHotelReservationDto.getBoothName())) {
//                    conventionPerson.setRemark(annualHotelReservationDto.getBoothName());
//                    conventionPerson.setRemark(annualHotelReservationDto.getInstitutionName());
                    conventionPerson.setCompany(annualHotelReservationDto.getInstitutionName());
                }
                //单个展位、多展位非机构名-加中间表
                //添加人信息
                addUserInfoToEntity(conventionPerson);
                conventionPersonMapper.insertSelective(conventionPerson);
                insertTable(conventionPerson.getId(), annualHotelReservationDto);
            }
            //添加人信息
            long personId = conventionPerson.getId();
            //设置参会人员编号
            conventionPerson.setNum(MyStringUtils.getConventionPersonNum(conventionPerson.getId()));
            updateUserInfoToEntity(conventionPerson);
            conventionPersonMapper.updateById(conventionPerson);
            if (annualHotelReservationDto.getIsBookHotel()) {
                //安排房间-安排床位
                long voDays = Long.parseLong(String.valueOf(MyDateUtils.differentDays(annualHotelReservationDto.getCheckInDate(), annualHotelReservationDto.getCheckOutDate())));
                ConventionHotelRoomDto conventionHotelRoomDto = getConventionHotelRoomVo(annualHotelReservationDto, conventionId, roomTypeId, voDays);
                if (GeneralTool.isEmpty(conventionHotelRoomDto)) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
                }
                if (0 == conventionHotelRoomDto.getRooms() || 0 == conventionHotelRoomDto.getDays()) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("room_num_stay_day"));
                }
                //住店日期备份记录下
                Date stayDate = conventionHotelRoomDto.getStayDate();
                //通过房型fkid获取已存在的最大系统房号(带前序号的字符串)
                String maxSystemRoomNumString = conventionHotelRoomMapper.getMaxSystemRoomNum(conventionHotelRoomDto.getFkConventionHotelId());
                //最大系统房号(不带前序号的字符串)
                String maxSystemRoomNum;
                //添加的总记录数=房间数*住店天数
                for (long rooms = 1; rooms <= conventionHotelRoomDto.getRooms(); rooms++) {
                    //通过房型id获取该酒店房型前序号(房号)
                    ConventionHotelVo conventionHotelVo = conventionHotelService.findConventionHotelById(conventionHotelRoomDto.getFkConventionHotelId());
                    String preNum = conventionHotelVo.getPreNum();
                    if (GeneralTool.isEmpty(maxSystemRoomNumString)) {
                        maxSystemRoomNum = "0";
                    } else {
                        //根据前序号的长度，开始截取
                        int length = preNum.length();
                        maxSystemRoomNum = maxSystemRoomNumString.substring(length);
                    }
                    //得到自动生成的系统房号 = 前序号 + 最大系统房号 + rooms(要看创建了几间房)
                    String systemRoomNum = MyStringUtils.getSystemRoomNum(preNum, new Long(maxSystemRoomNum) + rooms);
                    conventionHotelRoomDto.setSystemRoomNum(systemRoomNum);
                    //将住店日期通过备份还原
                    conventionHotelRoomDto.setStayDate(stayDate);
                    for (long days = 1; days <= conventionHotelRoomDto.getDays(); days++) {
                        //日期加1
                        Date date = GetDayUtils.addDay(conventionHotelRoomDto.getStayDate(), days);
                        conventionHotelRoomDto.setStayDate(date);
                        ConventionHotelRoom conventionHotelRoom = BeanCopyUtils.objClone(conventionHotelRoomDto, ConventionHotelRoom::new);
                        ConventionHotelRoomPersonDto conventionHotelRoomPersonDto = new ConventionHotelRoomPersonDto();
                        addUserInfoToEntity(conventionHotelRoom);
                        conventionHotelRoomMapper.insert(conventionHotelRoom);
                        long roomId = conventionHotelRoom.getId();
                        //安排房间-床位
                        conventionHotelRoomPersonDto.setKey(false);
                        //conventionHotelId 房型id；fkConventionHotelRoomId 房间id；fkConventionPersonId 住房人id
                        conventionHotelRoomPersonDto.setConventionHotelId(conventionHotelRoomDto.getFkConventionHotelId());
                        conventionHotelRoomPersonDto.setFkConventionPersonId(personId);
                        conventionHotelRoomPersonDto.setFkConventionHotelRoomId(roomId);
                        ConventionHotelRoomPerson conventionHotelRoomPerson = BeanCopyUtils.objClone(conventionHotelRoomPersonDto, ConventionHotelRoomPerson::new);
                        addUserInfoToEntity(conventionHotelRoomPerson);
                        conventionHotelRoomPersonMapper.insertSelective(conventionHotelRoomPerson);
                    }
                }
            }
            //        }
        } catch (Exception e) {
            e.printStackTrace();
            throw new GetServiceException(e.getMessage());
        } finally {
            getRedis.del(CacheKeyConstants.CONVENTION_REGISTRATION_CACHE_KEY);
        }
    }

    private ConventionHotelRoomDto getConventionHotelRoomVo(AnnualHotelReservationDto annualHotelReservationDto, Long conventionId, Long roomTypeId, long voDays) {
        ConventionHotelRoomDto conventionHotelRoomDto = new ConventionHotelRoomDto();
        conventionHotelRoomDto.setConventionId(conventionId);
        conventionHotelRoomDto.setDays(voDays);
        conventionHotelRoomDto.setStayDate(annualHotelReservationDto.getCheckInDate());
        conventionHotelRoomDto.setFkConventionHotelId(roomTypeId);
        conventionHotelRoomDto.setRooms(1L);
        return conventionHotelRoomDto;
    }

    /**
     * 删除床位
     *
     * @param conventionPerson
     * @param conventionHotelRoom
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteConventionHotelRoomPerson(ConventionPerson conventionPerson, ConventionHotelRoom conventionHotelRoom) throws GetServiceException {
//        Example example = new Example(ConventionHotelRoomPerson.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkConventionHotelRoomId", conventionHotelRoom.getId());
//        criteria.andEqualTo("fkConventionPersonId", conventionPerson.getId());
//        List<ConventionHotelRoomPerson> conventionHotelRoomPeople = conventionHotelRoomPersonMapper.selectByExample(example);

        LambdaQueryWrapper<ConventionHotelRoomPerson> lambdaQueryWrapper = Wrappers.<ConventionHotelRoomPerson>lambdaQuery()
                .eq(ConventionHotelRoomPerson::getFkConventionHotelRoomId, conventionHotelRoom.getId())
                .eq(ConventionHotelRoomPerson::getFkConventionPersonId, conventionPerson.getId());
        List<ConventionHotelRoomPerson> conventionHotelRoomPeople = conventionHotelRoomPersonMapper.selectList(lambdaQueryWrapper);
        if (GeneralTool.isEmpty(conventionHotelRoomPeople)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("convention_hotel_room_person"));
        }
//        conventionHotelRoomPersonMapper.deleteByExample(example);
        conventionHotelRoomPersonMapper.delete(lambdaQueryWrapper);
    }

    /**
     * 覆盖原有房间日期
     *
     * @param annualHotelReservationDto
     * @param hotelRoom
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateConventionHotelRoom(AnnualHotelReservationDto annualHotelReservationDto, List<ConventionHotelRoom> hotelRoom) {
        for (int i = 0; i < hotelRoom.size(); i++) {
            Date date = GetDayUtils.addDays(annualHotelReservationDto.getCheckInDate(), (long) i);
            hotelRoom.get(i).setStayDate(date);
            updateUserInfoToEntity(hotelRoom.get(i));
            conventionHotelRoomMapper.updateById(hotelRoom.get(i));
        }
    }


    /**
     * 根据PersonId获取房间信息
     *
     * @param id
     * @return
     */
    private List<ConventionHotelRoom> getConventionHotelRoom(Long id) {
        //后面添加峰会过滤
        return conventionHotelRoomMapper.getConventionHotelRoom(id);
    }


    /**
     * 床位关联表删除
     *
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteTableByPersonId(Long id) {
//        Example example = new Example(ConventionHotelRoomPerson.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkConventionPersonId", id);
//        List<ConventionHotelRoomPerson> conventionHotelRoomPeople = conventionHotelRoomPersonMapper.selectByExample(example);

        LambdaQueryWrapper<ConventionHotelRoomPerson> lambdaQueryWrapper = Wrappers.<ConventionHotelRoomPerson>lambdaQuery()
                .eq(ConventionHotelRoomPerson::getFkConventionPersonId, id);
        List<ConventionHotelRoomPerson> conventionHotelRoomPeople = conventionHotelRoomPersonMapper.selectList(lambdaQueryWrapper);

        if (GeneralTool.isEmpty(conventionHotelRoomPeople)) {
            return;
        }
        List<Long> conventionHotelRoomIds = new ArrayList<>();
        for (ConventionHotelRoomPerson conventionHotelRoomPerson : conventionHotelRoomPeople) {
            conventionHotelRoomIds.add(conventionHotelRoomPerson.getFkConventionHotelRoomId());
        }
//        conventionHotelRoomPersonMapper.deleteByExample(example);
        conventionHotelRoomPersonMapper.delete(lambdaQueryWrapper);
        for (Long conventionHotelRoomId : conventionHotelRoomIds) {
            conventionHotelRoomMapper.deleteById(conventionHotelRoomId);
        }
    }

    /**
     * 关联表保存
     *
     * @param id
     * @param annualHotelReservationDto
     */
    public void insertTable(Long id, AnnualHotelReservationDto annualHotelReservationDto) {
        //校方-r_convention_person_registration 中间表对象
        ConventionPersonRegistration conventionPersonRegistration = new ConventionPersonRegistration();
        //参展人员id为刚插入成功返回的id
        conventionPersonRegistration.setFkConventionPersonId(id);
        //所属报名名册的id
        conventionPersonRegistration.setFkConventionRegistrationId(annualHotelReservationDto.getConventionRegistrationId());
        addUserInfoToEntity(conventionPersonRegistration);
        conventionPersonRegistrationMapper.insert(conventionPersonRegistration);
    }

    /**
     * 关联表删除
     *
     * @param id
     */
    public void deleteTable(Long id) {
        formCommonService.deleteTable(id);
    }


    /**
     * 设置属性
     *
     * @param annualHotelReservationDto
     * @param conventionPerson
     */
    private void setInfo(AnnualHotelReservationDto annualHotelReservationDto, ConventionPerson conventionPerson) {
        //校代为类型0
        conventionPerson.setType(0);
        conventionPerson.setNameChn(annualHotelReservationDto.getNameChn());
        conventionPerson.setName(annualHotelReservationDto.getName());
        conventionPerson.setGender(annualHotelReservationDto.getGender());
        conventionPerson.setTitle(annualHotelReservationDto.getTitle());
        conventionPerson.setEmail(annualHotelReservationDto.getEmail());
        conventionPerson.setTel(annualHotelReservationDto.getTel());
        conventionPerson.setIdCardNum(annualHotelReservationDto.getIdCardNum());
        conventionPerson.setPassportNum(annualHotelReservationDto.getPassportNum());
        conventionPerson.setCompany(annualHotelReservationDto.getInstitutionName());
        conventionPerson.setCheckInTime(annualHotelReservationDto.getCheckInDate());
        conventionPerson.setCheckOutTime(annualHotelReservationDto.getCheckOutDate());
        conventionPerson.setRemarkJson(annualHotelReservationDto.getRemarkJson());
        conventionPerson.setIsAttendDinner(annualHotelReservationDto.getIsAttendDinner());
        conventionPerson.setIsBookHotel(annualHotelReservationDto.getIsBookHotel());
        conventionPerson.setFkConventionHotelId(annualHotelReservationDto.getFkConventionHotelId());
        StringJoiner areaName = new StringJoiner(",");
        if (GeneralTool.isNotEmpty(annualHotelReservationDto.getFkAreaStateName())){
            areaName.add(annualHotelReservationDto.getFkAreaStateName());
        }
        if (GeneralTool.isNotEmpty(annualHotelReservationDto.getFkAreaCityName())){
            areaName.add(annualHotelReservationDto.getFkAreaCityName());
        }
        if (GeneralTool.isEmpty(conventionPerson.getRemark())&&GeneralTool.isNotEmpty(areaName.toString())){
            conventionPerson.setRemark("所在区域："+areaName.toString());
        }else if (GeneralTool.isNotEmpty(conventionPerson.getRemark())&&GeneralTool.isNotEmpty(areaName.toString())){
            conventionPerson.setRemark(conventionPerson.getRemark()+";所在区域:"+areaName.toString());
        }

    }

    /**
     * 批量新增
     *
     * @param annualHotelReservationDtos
     * @
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAddAnnualHotelReservation(List<AnnualHotelReservationDto> annualHotelReservationDtos) {
        for (AnnualHotelReservationDto annualHotelReservationDto : annualHotelReservationDtos) {
            addAnnualHotelReservation(annualHotelReservationDto);
        }
    }

    /**
     * 机构下拉框
     *
     * @param receiptCode
     * @param fkConventionId
     * @return
     * @
     */
    @Override
    public List<ConventionRegistrationVo> getInstitutionNameSelect(String receiptCode, Long fkConventionId) {
        List<ConventionRegistrationVo> selectResult = new ArrayList<>();
        //判断回执码下，存在多少个展位
        List<ConventionRegistrationVo> conventionRegistrationVoList = conventionRegistrationMapper.getInstitutionNameSelect(receiptCode, fkConventionId);
        if (conventionRegistrationVoList.isEmpty()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_is_empty"));
        }
        ConventionRegistrationVo conventionRegistrationVo = conventionRegistrationVoList.get(0);
        String providerName = conventionRegistrationVoList.get(0).getProviderName();
        selectResult.add(conventionRegistrationVo);
        for (ConventionRegistrationVo registrationDto : conventionRegistrationVoList) {
            if (!providerName.equals(registrationDto.getProviderName())){
                selectResult.add(registrationDto);
            }
        }
        return selectResult;
//        //分割符号
//        String splitter = ";";
//        //单个展位
//        if (conventionRegistrationVoList.size() == 1) {
//            ConventionRegistrationVo conventionRegistrationVo = conventionRegistrationVoList.get(0);
//            //提供商名称和展位名相等
//            if (GeneralTool.isNotEmpty(conventionRegistrationVo.getProviderName()) && GeneralTool.isNotEmpty(conventionRegistrationVo.getBoothName())) {
//
//                if (conventionRegistrationVo.getProviderName().equals(conventionRegistrationVo.getBoothName())) {
//                    selectResult.add(conventionRegistrationVo);
//                } else {
//                    //判断是否有分号";"的情况
//                    if (conventionRegistrationVo.getBoothName().contains(splitter)) {
//                        String[] split = conventionRegistrationVo.getBoothName().split(splitter);
//                        for (String s : split) {
//                            ConventionRegistrationVo booth = BeanCopyUtils.objClone(conventionRegistrationVo, ConventionRegistrationVo::new);
//                            booth.setBoothName(s);
//                            selectResult.add(booth);
//                        }
//                    } else {
//                        ConventionRegistrationVo institution = BeanCopyUtils.objClone(conventionRegistrationVo, ConventionRegistrationVo::new);
//                        institution.setBoothName(institution.getProviderName());
//                        selectResult.add(institution);
//                        selectResult.add(conventionRegistrationVo);
//                    }
//                }
//            }
//
//        }
//        //多个展位
//        if (conventionRegistrationVoList.size() > 1) {
//            boolean isContainSplitter = false;
//            for (ConventionRegistrationVo conventionRegistrationVo : conventionRegistrationVoList) {
//                //判断展位名称数据是否包含【;】
//                isContainSplitter = conventionRegistrationVo.getBoothName().contains(splitter);
//                break;
//            }
//            //包含【;】
//            if (isContainSplitter) {
//                //将机构名先加入
//                ConventionRegistrationVo institution = BeanCopyUtils.objClone(conventionRegistrationVoList.get(0), ConventionRegistrationVo::new);
//                institution.setBoothName(institution.getProviderName());
//                institution.setId(0L);
//                selectResult.add(institution);
//                for (ConventionRegistrationVo conventionRegistrationVo : conventionRegistrationVoList) {
//                    if (conventionRegistrationVo.getBoothName().contains(splitter)) {
//                        //按【；】切割在分别加入下拉框中
//                        String[] split = conventionRegistrationVo.getBoothName().split(splitter);
//                        for (String s : split) {
//                            ConventionRegistrationVo booth = BeanCopyUtils.objClone(conventionRegistrationVo, ConventionRegistrationVo::new);
//                            booth.setBoothName(s);
//                            selectResult.add(booth);
//                        }
//                    } else {
//                        //返回机构名+展位名
//                        selectResult.add(conventionRegistrationVo);
//                    }
//                }
//            } else {
//                //将机构名先加入
//                ConventionRegistrationVo institution = BeanCopyUtils.objClone(conventionRegistrationVoList.get(0), ConventionRegistrationVo::new);
//                institution.setBoothName(institution.getProviderName());
//                institution.setId(0L);
//                selectResult.add(institution);
//                //加入展位名
//                for (ConventionRegistrationVo conventionRegistrationVo : conventionRegistrationVoList) {
//                    selectResult.add(conventionRegistrationVo);
//                }
//
//            }
//        }
//        return selectResult;
    }

    /**
     * 获取回显信息
     *
     * @param receiptCode
     * @param
     * @return
     * @
     */
    @Override
    public List<AnnualHotelReservationVo> getAnnualHotelReservationDto(String receiptCode) {
        List<AnnualHotelReservationVo> annualHotelReservationVos = new ArrayList<>();
        //根据回执码获取峰会id
        Long conventionId = conventionRegistrationMapper.getConventionIdByReceiptCode(receiptCode);
        List<Long> conventionRegistrationIds = new ArrayList<>();
//        Example example = new Example(ConventionRegistration.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("receiptCode", receiptCode);
//        List<ConventionRegistration> conventionRegistrations = conventionRegistrationMapper.selectByExample(example);

        List<ConventionRegistration> conventionRegistrations = conventionRegistrationMapper.selectList(Wrappers.<ConventionRegistration>lambdaQuery().eq(ConventionRegistration::getReceiptCode, receiptCode));
        for (ConventionRegistration conventionRegistration : conventionRegistrations) {
            conventionRegistrationIds.add(conventionRegistration.getId());
        }
        //根据峰会id和房间类型查询预订并入住成功的参会人员ids
        List<Long> conventionPersonIds = conventionHotelRoomPersonMapper.getconventionPersonIds(conventionId, conventionRegistrationIds);
        //纯机构名无绑定报名名册的
        List<Long> personIds = conventionHotelRoomPersonMapper.getPersonIds(conventionId, conventionRegistrationIds);
        List<Long> ids = Stream.of(conventionPersonIds, personIds)
                .flatMap(Collection::stream)
                .distinct().sorted()
                .collect(Collectors.toList());

        if (ids.size() <= 0) {
            return annualHotelReservationVos;
        }
        for (Long conventionPersonId : ids) {
            //设置信息
            ConventionPerson conventionPerson = conventionPersonMapper.selectById(conventionPersonId);
            AnnualHotelReservationVo annualHotelReservationVo = setAnnualHotelReservationDto(conventionPerson);
            if (GeneralTool.isNotEmpty(annualHotelReservationVo) && GeneralTool.isNotEmpty(conventionPerson.getFkConventionHotelId())) {
                ConventionHotel conventionHotel = conventionHotelMapper.selectById(conventionPerson.getFkConventionHotelId());
                annualHotelReservationVo.setFkConventionHotelId(conventionPerson.getFkConventionHotelId());
                annualHotelReservationVo.setRoomTypeName(conventionHotel.getRoomType());
            }
            //查询参会人-报名名册中间表
            ConventionPersonRegistration conventionPersonRegistration = getConventionPersonRegistration(conventionPersonId);
            if (GeneralTool.isEmpty(conventionPersonRegistration)) {
                annualHotelReservationVo.setConventionRegistrationId(0L);
            } else {
                if (GeneralTool.isEmpty(conventionPersonRegistration.getFkConventionRegistrationId())) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("convention_registration_id_is_null"));
                }
                annualHotelReservationVo.setConventionRegistrationId(conventionPersonRegistration.getFkConventionRegistrationId());
            }
            //纯机构名展位的机构名 - 绑定了报名名册的机构名
//            String institutionName = getInstitutionName(annualHotelReservationVo.getConventionRegistrationId(), receiptCode);
//            annualHotelReservationVo.setInstitutionName(institutionName);
            annualHotelReservationVo.setInstitutionName(conventionPerson.getCompany());
            annualHotelReservationVo.setId(conventionPersonId);
            annualHotelReservationVo.setReceiptCode(receiptCode);
            annualHotelReservationVos.add(annualHotelReservationVo);
        }
        return annualHotelReservationVos;
    }


    /**
     * 根据参会人员的id删除信息
     *
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteConventionPerson(Long id,String receiptCode) {
        if (GeneralTool.isEmpty(receiptCode)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
        Long conventionId = conventionRegistrationMapper.getConventionIdByReceiptCode(receiptCode);
        if (GeneralTool.isEmpty(conventionId)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
        //删除床位房间等中间表
        deleteTableByPersonId(id);
        //删除报名表
        deleteTable(id);
        //删除参会人表
        ConventionPerson conventionPerson = conventionPersonMapper.selectById(id);
        if (GeneralTool.isEmpty(conventionPerson)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("convention_person_not_found"));
        }
        if (!conventionId.equals(conventionPerson.getFkConventionId())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_without_permission"));
        }
        conventionPersonMapper.deleteById(id);
        // 在删除参会人员时，同步删除关系表数据
        conventionPersonService.deleteAssociationTable(id, conventionPerson);
    }

    /**
     * 房型下拉框
     *
     * @Date 11:31 2024/1/3
     * <AUTHOR>
     */
    @Override
    public List<ConventionHotelVo> getConventionHotel() {
        //先写死 每年需求不一样 不适用不同峰会
        List<ConventionHotel> conventionHotels = conventionHotelMapper.selectList(Wrappers.<ConventionHotel>lambdaQuery()
                .eq(ConventionHotel::getFkConventionId, 37L));
        List<ConventionHotelVo> conventionHotelVos = new ArrayList<>();

        for (ConventionHotel conventionHotel : conventionHotels) {
            ConventionHotelVo copy = BeanUtil.copy(conventionHotel, ConventionHotelVo.class);
            Long totalRooms = conventionHotelRoomMapper.getHotelRoomCountByRoomTypeId(copy.getId());
            copy.setRemainingQuantity(copy.getQuotaLimit() - totalRooms);
            conventionHotelVos.add(copy);
        }
        return conventionHotelVos;
    }

    /**
     * 根据报名名册id获取机构名
     *
     * @param conventionRegistrationId
     * @return
     */
    private String getInstitutionName(Long conventionRegistrationId, String receiptCode) {
        String institutionName = "";
        //纯机构名无绑定报名名册的情况
        if (conventionRegistrationId == 0L) {
            //通过回执码查询
//            Example example = new Example(ConventionRegistration.class);
//            Example.Criteria criteria = example.createCriteria();
//            criteria.andEqualTo("receiptCode", receiptCode);
//            List<ConventionRegistration> conventionRegistrations = conventionRegistrationMapper.selectByExample(example);
            List<ConventionRegistration> conventionRegistrations = conventionRegistrationMapper.selectList(Wrappers.<ConventionRegistration>lambdaQuery().eq(ConventionRegistration::getReceiptCode, receiptCode));

            if (GeneralTool.isEmpty(conventionRegistrations)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("record_not_found"));
            }
            if (GeneralTool.isEmpty(conventionRegistrations.get(0).getProviderName())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("provider_name_is_null"));
            }
            institutionName = conventionRegistrations.get(0).getProviderName();
        } else {
            ConventionRegistration conventionRegistration = conventionRegistrationMapper.selectById(conventionRegistrationId);
            if (GeneralTool.isEmpty(conventionRegistration.getProviderName())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("provider_name_is_null"));
            }
            institutionName = conventionRegistration.getProviderName();
        }
        return institutionName;
    }

    /**
     * 根据参会人id获取中间表
     *
     * @param conventionPersonId
     * @return
     */
    private ConventionPersonRegistration getConventionPersonRegistration(Long conventionPersonId) {
        return formCommonService.getConventionPersonRegistration(conventionPersonId);
    }

    /**
     * 回显设置参会人信息
     *
     * @param conventionPerson
     * @return
     */
    private AnnualHotelReservationVo setAnnualHotelReservationDto(ConventionPerson conventionPerson) {
        AnnualHotelReservationVo annualHotelReservationVo = new AnnualHotelReservationVo();
        String remark = conventionPerson.getRemark();

        if (GeneralTool.isNotEmpty(remark) && remark.contains("所在区域")){
            if (remark.contains(";")){
                int i = remark.lastIndexOf(";");
                int j = remark.lastIndexOf(":");
                String sub = remark.substring(0, i);
                annualHotelReservationVo.setRemark(sub);

                String area = remark.substring(j+1, remark.length());
                String[] split = area.split(",");
                if (split.length == 2){
                    annualHotelReservationVo.setFkAreaStateName(split[0]);
                    annualHotelReservationVo.setFkAreaCityName(split[1]);
                }else if (split.length == 1){
                    annualHotelReservationVo.setFkAreaStateName(split[0]);
                    annualHotelReservationVo.setFkAreaCityName(null);
                }
            }else {
                annualHotelReservationVo.setRemark(null);
            }
        }

        if (GeneralTool.isNotEmpty(conventionPerson.getName())){
            annualHotelReservationVo.setName(conventionPerson.getName());
        }

        if (GeneralTool.isNotEmpty(conventionPerson.getNameChn())){
            annualHotelReservationVo.setNameChn(conventionPerson.getNameChn());
        }
//        if (GeneralTool.isNotEmpty(conventionPerson.getName())) {
//            String personName = conventionPerson.getName();
//            String[] s = personName.trim().split(" ");
//            annualHotelReservationVo.setFirstName(s[0]);
//            annualHotelReservationVo.setLastName(s[1]);
//        }
//        if (GeneralTool.isNotEmpty(conventionPerson.getNameChn())) {
//            String personNameChn = conventionPerson.getNameChn();
//            String[] s = personNameChn.trim().split(" ");
//            if (s.length >= 2) {
//                annualHotelReservationVo.setFirstNameChn(s[1]);
//                annualHotelReservationVo.setLastNameChn(s[0]);
//            } else {
//                annualHotelReservationVo.setFirstNameChn(personNameChn);
//            }
//        }
        annualHotelReservationVo.setEmail(conventionPerson.getEmail());
        annualHotelReservationVo.setGender(conventionPerson.getGender());
        annualHotelReservationVo.setTitle(conventionPerson.getTitle());
        annualHotelReservationVo.setTel(conventionPerson.getTel());
        annualHotelReservationVo.setIsBookHotel(conventionPerson.getIsBookHotel());
        annualHotelReservationVo.setIsAttendDinner(conventionPerson.getIsAttendDinner());
        annualHotelReservationVo.setRemark(conventionPerson.getRemark());
        //日期设置
        annualHotelReservationVo.setCheckInDate(conventionPerson.getCheckInTime());
        annualHotelReservationVo.setCheckOutDate(conventionPerson.getCheckOutTime());
        Long conventionRegistrationId = conventionPersonRegistrationMapper.getConventionRegistrationId(conventionPerson.getId());
        annualHotelReservationVo.setConventionRegistrationId(conventionRegistrationId);
        //设置展位名
        if (GeneralTool.isNotEmpty(remark)) {
            annualHotelReservationVo.setBoothName(remark);
        } else {
//            Long conventionRegistrationId = conventionPersonRegistrationMapper.getConventionRegistrationId(conventionPerson.getId());
            String boothName = conventionRegistrationMapper.getBoothNameById(conventionRegistrationId);
            annualHotelReservationVo.setBoothName(boothName);
        }
        annualHotelReservationVo.setIdCardNum(conventionPerson.getIdCardNum());
        annualHotelReservationVo.setPassportNum(conventionPerson.getPassportNum());
        return annualHotelReservationVo;
    }

    /**
     * 设置创建信息
     */
    public void addUserInfoToEntity(BaseEntity entity) {
        formCommonService.addUserInfoToEntity(entity);
    }

    /**
     * 设置更新信息
     *
     * @
     */
    public void updateUserInfoToEntity(BaseEntity entity) {
        formCommonService.updateUserInfoToEntity(entity);
    }

}

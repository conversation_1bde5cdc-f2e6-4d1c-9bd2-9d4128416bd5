package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.EventTargetAreaCountryMapper;
import com.get.salecenter.entity.EventTargetAreaCountry;
import com.get.salecenter.service.IEventTargetAreaCountryService;
import com.get.salecenter.dto.EventTargetAreaCountryDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: Sea
 * @create: 2020/12/7 17:31
 * @verison: 1.0
 * @description:
 */
@Service
public class EventTargetAreaCountryServiceImpl implements IEventTargetAreaCountryService {
    @Resource
    private EventTargetAreaCountryMapper eventTargetAreaCountryMapper;
    @Resource
    private UtilService utilService;

    @Override
    public Long addEventTargetAreaCountry(EventTargetAreaCountryDto eventTargetAreaCountryDto) {
        if (GeneralTool.isEmpty(eventTargetAreaCountryDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        EventTargetAreaCountry eventTargetAreaCountry = BeanCopyUtils.objClone(eventTargetAreaCountryDto, EventTargetAreaCountry::new);
        utilService.updateUserInfoToEntity(eventTargetAreaCountry);
        int i = eventTargetAreaCountryMapper.insertSelective(eventTargetAreaCountry);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
        return eventTargetAreaCountry.getId();
    }

    @Override
    public void deleteByEventId(Long eventId) {
//        Example example = new Example(EventTargetAreaCountry.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkEventId", eventId);

        LambdaQueryWrapper<EventTargetAreaCountry> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(EventTargetAreaCountry::getFkEventId, eventId);
        if (GeneralTool.isNotEmpty(eventTargetAreaCountryMapper.selectList(lambdaQueryWrapper))) {
            int i = eventTargetAreaCountryMapper.delete(lambdaQueryWrapper);
            if (i <= 0) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
            }
        }
    }

    @Override
    public List<Long> getEventIdsByCountryId(Long fkAreaCountryId) {
        List<Long> result = new ArrayList<>();
//        Example example = new Example(EventTargetAreaCountry.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkAreaCountryId", fkAreaCountryId);
        List<EventTargetAreaCountry> eventTargetAreaCountrys = eventTargetAreaCountryMapper.selectList(Wrappers.<EventTargetAreaCountry>lambdaQuery().eq(EventTargetAreaCountry::getFkAreaCountryId, fkAreaCountryId));
        if (GeneralTool.isEmpty(eventTargetAreaCountrys)) {
            result.add(0L);
            return result;
        }
        return eventTargetAreaCountrys.stream().map(EventTargetAreaCountry::getFkEventId).collect(Collectors.toList());
    }

    @Override
    public List<Long> getCountryIdsByEventIds(List<Long> eventIds) {
        if (GeneralTool.isEmpty(eventIds)) {
            eventIds.add(0L);
        }
        return eventTargetAreaCountryMapper.getCountryIdsByEventIds(eventIds);
    }

    @Override
    public List<Long> getEventIdsByCountryIds(List<Long> eventTargetCountryList) {
        List<Long> result = new ArrayList<>();
        List<EventTargetAreaCountry> eventTargetAreaCountrys = eventTargetAreaCountryMapper.selectList(Wrappers.<EventTargetAreaCountry>lambdaQuery().in(EventTargetAreaCountry::getFkAreaCountryId, eventTargetCountryList));
        if (GeneralTool.isEmpty(eventTargetAreaCountrys)) {
            result.add(0L);
            return result;
        }
        return eventTargetAreaCountrys.stream().map(EventTargetAreaCountry::getFkEventId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
    }
}

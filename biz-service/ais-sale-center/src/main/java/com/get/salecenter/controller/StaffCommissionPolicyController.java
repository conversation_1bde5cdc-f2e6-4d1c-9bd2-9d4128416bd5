package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseVoEntity;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.vo.StaffCommissionPolicyVo;
import com.get.salecenter.service.IStaffCommissionPolicyService;
import com.get.salecenter.dto.StaffCommissionPolicyDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2023/2/6 14:37
 * @verison: 1.0
 * @description:
 */
@Api(tags = "提成规则管理")
@RestController
@RequestMapping("sale/staffCommissionPolicy")
public class StaffCommissionPolicyController {

    @Resource
    private IStaffCommissionPolicyService staffCommissionPolicyService;


    @ApiOperation(value = "新增", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/提成规则管理/新增")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated({BaseVoEntity.Add.class}) StaffCommissionPolicyDto staffCommissionPolicyDto) {
        return SaveResponseBo.ok(staffCommissionPolicyService.add(staffCommissionPolicyDto));
    }

    @ApiOperation(value = "删除", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/提成规则管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        staffCommissionPolicyService.delete(id);
        return DeleteResponseBo.ok();
    }

    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/提成规则管理/修改接口")
    @PostMapping("update")
    public ResponseBo<StaffCommissionPolicyVo> update(@RequestBody @Validated({BaseVoEntity.Update.class}) StaffCommissionPolicyDto staffCommissionPolicyDto) {
        return UpdateResponseBo.ok(staffCommissionPolicyService.updateStaffCommissionPolicy(staffCommissionPolicyDto));
    }

    @ApiOperation(value = "详情", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/提成规则管理/详情")
    @GetMapping("/{id}")
    public ResponseBo<StaffCommissionPolicyVo> detail(@PathVariable("id")Long id) {
        return new ResponseBo<>(staffCommissionPolicyService.findStaffCommissionPolicyById(id));
    }

    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/提成规则管理/查询")
    @PostMapping("datas")
    public ListResponseBo<StaffCommissionPolicyVo> datas(@RequestBody SearchBean<StaffCommissionPolicyDto> page) {
        List<StaffCommissionPolicyVo> datas = staffCommissionPolicyService.getStaffCommissionPolicyDtos(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }


    @ApiOperation(value = "排序（拖拽）", notes = "传入拖拽后的列表")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/提成规则管理/排序（拖拽）")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestParam("end")Integer end,@RequestParam("start")Integer start) {
        staffCommissionPolicyService.movingOrder(end,start);
        return ResponseBo.ok();

    }


    /**
     * 学校下拉框数据包括简称
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "学校下拉框数据包括简称", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/提成规则管理/提成规则学校下拉")
    @GetMapping("getInstitutionsSelect")
    public ResponseBo<BaseSelectEntity> getInstitutionsSelect(@RequestParam("fkCompanyId") Long fkCompanyId) {
        List<BaseSelectEntity> datas = staffCommissionPolicyService.getInstitutionsSelect(fkCompanyId);
        return new ListResponseBo<>(datas);
    }


    /**
     * 学校下拉框数据包括简称
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "项目成员角色下拉", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/提成规则管理/项目成员角色下拉")
    @GetMapping("getProjectRoleSelect")
    public ResponseBo<BaseSelectEntity> getProjectRoleSelect(@RequestParam("fkCompanyId") Long fkCompanyId) {
        List<BaseSelectEntity> datas = staffCommissionPolicyService.getProjectRoleSelect(fkCompanyId);
        return new ListResponseBo<>(datas);
    }


}

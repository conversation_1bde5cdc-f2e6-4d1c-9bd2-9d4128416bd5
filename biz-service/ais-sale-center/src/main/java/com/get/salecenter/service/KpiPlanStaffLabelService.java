package com.get.salecenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.salecenter.dto.KpiPlanStaffLabelDto;
import com.get.salecenter.entity.KpiPlanStaffLabel;
import com.get.salecenter.vo.KpiPlanStaffLabelVo;

/**
 * 服务类
 */
public interface KpiPlanStaffLabelService extends IService<KpiPlanStaffLabel> {

    /**
     * 批量新增考核人员标签
     *
     * @param kpiPlanStaffLabelVo 参数
     * @return
     */
    void batchAdd(KpiPlanStaffLabelDto kpiPlanStaffLabelVo);

    /**
     * 删除考核人员标签
     *
     * @param id 考核人员标签Id
     */
    void delete(Long id);
}

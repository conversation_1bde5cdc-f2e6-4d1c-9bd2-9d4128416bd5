package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.api.ResultCode;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.ConventionPersonMapper;
import com.get.salecenter.dao.sale.ConventionPersonProcedureMapper;
import com.get.salecenter.dao.sale.ConventionProcedureMapper;
import com.get.salecenter.dao.sale.ConventionTableMapper;
import com.get.salecenter.dao.sale.ConventionTablePersonMapper;
import com.get.salecenter.vo.ConventionProcedureVo;
import com.get.salecenter.vo.MediaAndAttachedVo;
import com.get.salecenter.entity.ConventionPerson;
import com.get.salecenter.entity.ConventionPersonProcedure;
import com.get.salecenter.entity.ConventionProcedure;
import com.get.salecenter.entity.ConventionTable;
import com.get.salecenter.entity.ConventionTablePerson;
import com.get.salecenter.service.IConventionProcedureService;
import com.get.salecenter.service.IDeleteService;
import com.get.salecenter.service.IMediaAndAttachedService;
import com.get.salecenter.service.TranslationMappingService;
import com.get.salecenter.dto.ConventionProcedureCopyDto;
import com.get.salecenter.dto.ConventionProcedureDto;
import com.get.salecenter.dto.MediaAndAttachedDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: Sea
 * @create: 2020/7/2 11:05
 * @verison: 1.0
 * @description: 峰会流程管理业务实现类
 */
@Service
public class ConventionProcedureServiceImpl implements IConventionProcedureService {
    @Resource
    private ConventionProcedureMapper conventionProcedureMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IMediaAndAttachedService attachedService;
    @Resource
    private IDeleteService deleteService;
    @Resource
    private TranslationMappingService translationMappingService;
    @Resource
    private ConventionPersonMapper conventionPersonMapper;
    @Resource
    private ConventionTablePersonMapper conventionTablePersonMapper;
    @Resource
    private ConventionPersonProcedureMapper conventionPersonProcedureMapper;
    @Resource
    private ConventionTableMapper conventionTableMapper;

    @Override
    public ConventionProcedureVo findConventionProcedureById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ConventionProcedure conventionProcedure = conventionProcedureMapper.selectById(id);
        if (GeneralTool.isEmpty(conventionProcedure)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        ConventionProcedureVo conventionProcedureVo = BeanCopyUtils.objClone(conventionProcedure, ConventionProcedureVo::new);
        MediaAndAttachedDto attachedVo = new MediaAndAttachedDto();
        attachedVo.setFkTableName(TableEnum.SALE_CONVENTION_PROCEDURE.key);
        attachedVo.setFkTableId(id);
        List<MediaAndAttachedVo> mediaAndAttachedVos = attachedService.getMediaAndAttachedDto(attachedVo);
        conventionProcedureVo.setMediaAndAttachedDtos(mediaAndAttachedVos);
        return conventionProcedureVo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long addConventionProcedure(ConventionProcedureDto conventionProcedureDto) {
        if (GeneralTool.isEmpty(conventionProcedureDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        ConventionProcedure conventionProcedure = BeanCopyUtils.objClone(conventionProcedureDto, ConventionProcedure::new);
        //获取该峰会中流程步骤索引最大值
        Integer maxStepIndex = conventionProcedureMapper.getMaxStepIndex(conventionProcedureDto.getFkConventionId());
        if (GeneralTool.isEmpty(maxStepIndex)) {
            conventionProcedure.setStepIndex(0);
        } else {
            //新增的步骤索引  是maxStepIndex+1
            conventionProcedure.setStepIndex(maxStepIndex + 1);
        }
        utilService.updateUserInfoToEntity(conventionProcedure);
        conventionProcedureMapper.insertSelective(conventionProcedure);
        return conventionProcedure.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (findConventionProcedureById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        //判断该峰会流程能否删除
        deleteService.deleteValidateProcedure(id);
        conventionProcedureMapper.deleteById(id);
        //同时删除该表id下的所有媒体附件
        attachedService.deleteMediaAndAttachedByTableId(id, TableEnum.SALE_CONVENTION_PROCEDURE.key);

        //删除翻译表
        translationMappingService.deleteTranslations(TableEnum.SALE_CONVENTION_PROCEDURE.key, id);
    }

    @Override
    public ConventionProcedureVo updateConventionProcedure(ConventionProcedureDto conventionProcedureDto) {
        if (conventionProcedureDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        ConventionProcedure result = conventionProcedureMapper.selectById(conventionProcedureDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        ConventionProcedure conventionProcedure = BeanCopyUtils.objClone(conventionProcedureDto, ConventionProcedure::new);
        utilService.updateUserInfoToEntity(conventionProcedure);
        conventionProcedureMapper.updateById(conventionProcedure);
        return findConventionProcedureById(conventionProcedure.getId());
    }

    @Override
    public List<ConventionProcedureVo> getConventionProcedures(ConventionProcedureDto conventionProcedureDto, Page page) {
//        Example example = new Example(ConventionProcedure.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkConventionId", conventionProcedureDto.getFkConventionId());
//        example.orderBy("stepIndex").asc();
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        List<ConventionProcedure> conventionProcedures = conventionProcedureMapper.selectByExample(example);
//        page.restPage(conventionProcedures);

        LambdaQueryWrapper<ConventionProcedure> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ConventionProcedure::getFkConventionId, conventionProcedureDto.getFkConventionId());
        lambdaQueryWrapper.orderByAsc(ConventionProcedure::getStepIndex);
        IPage<ConventionProcedure> pages = conventionProcedureMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
        List<ConventionProcedure> conventionProcedures = pages.getRecords();
        page.setAll((int) pages.getTotal());

        List<ConventionProcedureVo> convertDatas = new ArrayList<>();
        for (ConventionProcedure conventionProcedure : conventionProcedures) {
            //根据峰会流程id 查询对应的参加人数personProcedureCount
            Long personProcedureCount = conventionProcedureMapper.getPersonProcedureCount(conventionProcedure.getId());
            //将结果统一到ConventionProcedureDto 返回给前端
            ConventionProcedureVo conventionProcedureVo = BeanCopyUtils.objClone(conventionProcedure, ConventionProcedureVo::new);
            conventionProcedureVo.setPersonProcedureCount(personProcedureCount);
            convertDatas.add(conventionProcedureVo);
        }
        return convertDatas;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void movingOrder(List<ConventionProcedureDto> conventionProcedureDtos) {
        if (GeneralTool.isEmpty(conventionProcedureDtos)) {
            throw new GetServiceException(ResultCode.INVALID_PARAM, "传入值为空");
        }
        ConventionProcedure ro = BeanCopyUtils.objClone(conventionProcedureDtos.get(0), ConventionProcedure::new);
        Integer oneorder = ro.getStepIndex();
        ConventionProcedure rt = BeanCopyUtils.objClone(conventionProcedureDtos.get(1), ConventionProcedure::new);
        Integer twoorder = rt.getStepIndex();
        ro.setStepIndex(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setStepIndex(oneorder);
        utilService.updateUserInfoToEntity(rt);
        conventionProcedureMapper.updateById(ro);
        conventionProcedureMapper.updateById(rt);
    }

    @Override
    public List<ConventionProcedureVo> findConventionProcedureByFkId(Long conventionId) {
//        Example example = new Example(ConventionProcedure.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkConventionId", conventionId);
//        example.orderBy("stepIndex").asc();
//        List<ConventionProcedure> conventionProcedures = conventionProcedureMapper.selectByExample(example);

        List<ConventionProcedure> conventionProcedures = conventionProcedureMapper.selectList(Wrappers.<ConventionProcedure>lambdaQuery().eq(ConventionProcedure::getFkConventionId, conventionId).orderByAsc(ConventionProcedure::getStepIndex));
        List<ConventionProcedureVo> list = new ArrayList<>();
        for (ConventionProcedure conventionProcedure : conventionProcedures) {
            ConventionProcedureVo conventionProcedureVo = BeanCopyUtils.objClone(conventionProcedure, ConventionProcedureVo::new);
            list.add(conventionProcedureVo);
        }
        return list;
    }

    @Override
    public MediaAndAttachedVo addConventionMedia(MediaAndAttachedDto mediaAttachedVo) {
        if (GeneralTool.isEmpty(mediaAttachedVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        //设置插入的表
        mediaAttachedVo.setFkTableName(TableEnum.SALE_CONVENTION_PROCEDURE.key);
        return attachedService.addMediaAndAttached(mediaAttachedVo);
    }

    @Override
    public List<Map<String, Object>> findMediaAndAttachedType() {
        return FileTypeEnum.enumsTranslation2Arrays(FileTypeEnum.CONVENTIONPROCEDURE);
    }

    /**
     * 根据桌子获取峰会流程ids
     *
     * @Date 15:49 2021/6/29
     * <AUTHOR>
     */
    @Override
    public List<Long> getProcedureIdsByTable(String tableTypeKey, Long conventionId) {
//        Example example = new Example(ConventionProcedure.class);
//        example.createCriteria().andEqualTo("fkConventionId", conventionId).andEqualTo("fkTableTypeKey", tableTypeKey);
//        List<ConventionProcedure> conventionProcedures = conventionProcedureMapper.selectByExample(example);

        List<ConventionProcedure> conventionProcedures = conventionProcedureMapper.selectList(Wrappers.<ConventionProcedure>lambdaQuery()
                .eq(ConventionProcedure::getFkConventionId, conventionId)
                .eq(ConventionProcedure::getFkTableTypeKey, tableTypeKey));

        if (GeneralTool.isNotEmpty(conventionProcedures)) {
            return conventionProcedures.stream().map(ConventionProcedure::getId).collect(Collectors.toList());
        } else {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
            return null;
        }
    }

    @Override
    public void copyConventionProcedure(ConventionProcedureCopyDto conventionProcedureCopyDto) {
        //查询所要复制峰会活动流程
        LambdaQueryWrapper<ConventionProcedure> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ConventionProcedure::getFkConventionId, conventionProcedureCopyDto.getCopyConventionId());
        lambdaQueryWrapper.orderByAsc(ConventionProcedure::getStepIndex);
        List<ConventionProcedure> conventionProcedures = conventionProcedureMapper.selectList(lambdaQueryWrapper);
        if (GeneralTool.isNotEmpty(conventionProcedures)){
            for (ConventionProcedure conventionProcedure : conventionProcedures) {
                conventionProcedure.setFkConventionId(conventionProcedureCopyDto.getId());
                conventionProcedure.setGmtModified(null);
                conventionProcedure.setGmtModifiedUser(null);
                utilService.setCreateInfo(conventionProcedure);
                //获取该峰会中流程步骤索引最大值
                Integer maxStepIndex = conventionProcedureMapper.getMaxStepIndex(conventionProcedureCopyDto.getId());
                if (GeneralTool.isEmpty(maxStepIndex)) {
                    conventionProcedure.setStepIndex(0);
                } else {
                    //新增的步骤索引  是maxStepIndex+1
                    conventionProcedure.setStepIndex(maxStepIndex + 1);
                }
                conventionProcedureMapper.insert(conventionProcedure);
            }
        }
    }


    @Override
    @Transactional
    public void summitPersonnelAllocation() {
        List<ConventionPerson> conventionPeople = conventionPersonMapper.selectList(Wrappers.<ConventionPerson>lambdaQuery().eq(ConventionPerson::getFkConventionId, 37));

        //非代理
        List<Long> nonProxyIds = conventionPeople.stream().filter(d -> d.getType() != 1 ).map(ConventionPerson::getId).collect(Collectors.toList());
        //普通代理
        List<Long> rdinaryAgnetIds = new ArrayList<>();
        //vip代理
        List<Long> vipIds = new ArrayList<>();

        for (ConventionPerson conventionPerson : conventionPeople) {
            if (conventionPerson.getType() == 1) {
                if (conventionPerson.getIsVip()) {
                    vipIds.add(conventionPerson.getId());
                } else {
                    rdinaryAgnetIds.add(conventionPerson.getId());
                }
            }
        }
        List<Long> allAgentIds = new ArrayList<>();
        allAgentIds.addAll(vipIds);
        allAgentIds.addAll(rdinaryAgnetIds);

        List<ConventionProcedure> conventionProcedures = conventionProcedureMapper.selectList(Wrappers.<ConventionProcedure>lambdaQuery().eq(ConventionProcedure::getFkConventionId, 37));
        List<ConventionPersonProcedure> conventionPersonProcedures = new ArrayList<>();
        for (ConventionProcedure conventionProcedure : conventionProcedures) {

            if (conventionProcedure.getId() == 237 || conventionProcedure.getId() == 239 || conventionProcedure.getId() == 241
                    || conventionProcedure.getId() == 263 || conventionProcedure.getId() == 259 || conventionProcedure.getId() == 261) {
                switch (conventionProcedure.getId().toString()) {
                    case "237":
                    case "263":
                        //普通代理
                        for (Long rdinaryAgnetId : rdinaryAgnetIds) {
                            ConventionPersonProcedure conventionPersonProcedure = new ConventionPersonProcedure();
                            conventionPersonProcedure.setFkConventionProcedureId(conventionProcedure.getId());
                            conventionPersonProcedure.setFkConventionPersonId(rdinaryAgnetId);
                            conventionPersonProcedure.setGmtCreate(new Date());
                            conventionPersonProcedure.setGmtCreateUser("summer!!");
                            conventionPersonProcedures.add(conventionPersonProcedure);
                        }

                        break;
                    case "239":
                    case "241":
                    case "259":
                    case "261":
                        //VIP代理+校代+员工+嘉宾
                        for (Long nonProxyId : nonProxyIds) {
                            ConventionPersonProcedure conventionPersonProcedure = new ConventionPersonProcedure();
                            conventionPersonProcedure.setFkConventionProcedureId(conventionProcedure.getId());
                            conventionPersonProcedure.setFkConventionPersonId(nonProxyId);
                            conventionPersonProcedure.setGmtCreate(new Date());
                            conventionPersonProcedure.setGmtCreateUser("summer!!");
                            conventionPersonProcedures.add(conventionPersonProcedure);
                        }
                        for (Long vipId : vipIds) {
                            ConventionPersonProcedure conventionPersonProcedure = new ConventionPersonProcedure();
                            conventionPersonProcedure.setFkConventionProcedureId(conventionProcedure.getId());
                            conventionPersonProcedure.setFkConventionPersonId(vipId);
                            conventionPersonProcedure.setGmtCreate(new Date());
                            conventionPersonProcedure.setGmtCreateUser("summer!!");
                            conventionPersonProcedures.add(conventionPersonProcedure);
                        }
                        break;
                }
            } else {
                //ALL
                for (ConventionPerson conventionPerson : conventionPeople) {
                    ConventionPersonProcedure conventionPersonProcedure = new ConventionPersonProcedure();
                    conventionPersonProcedure.setFkConventionProcedureId(conventionProcedure.getId());
                    conventionPersonProcedure.setFkConventionPersonId(conventionPerson.getId());
                    conventionPersonProcedure.setGmtCreate(new Date());
                    conventionPersonProcedure.setGmtCreateUser("summer!!");
                    conventionPersonProcedures.add(conventionPersonProcedure);
                }
            }
        }
//        conventionPersonProcedureMapper.insertBatchSomeColumn(conventionPersonProcedures);


        List<ConventionTablePerson> conventionTablePersonList = new ArrayList<>();

        List<ConventionTable> conventionTables = conventionTableMapper.selectList(Wrappers.<ConventionTable>lambdaQuery().eq(ConventionTable::getFkConventionId, 37).eq(ConventionTable::getFkTableTypeKey, "CONVENTION_TRAINING_TABLE").like(ConventionTable::getTableNum, "_11_"));
        // 打乱列表顺序
        Collections.shuffle(allAgentIds);
        int batchSize = 8;
        int index = 0;
        int tableNum = 0;
        for (ConventionTable conventionTable : conventionTables) {
            tableNum++;
            if (tableNum > 22) {
                batchSize = 7;
            }
            while (index < allAgentIds.size()) {
                List<Long> batchIds = allAgentIds.subList(index, Math.min(index + batchSize, allAgentIds.size()));
                for (Long batchId : batchIds) {
                    ConventionTablePerson conventionTablePerson = new ConventionTablePerson();
                    conventionTablePerson.setFkConventionTableId(conventionTable.getId());
                    conventionTablePerson.setFkConventionPersonId(batchId);
                    conventionTablePerson.setGmtCreate(new Date());
                    conventionTablePerson.setGmtCreateUser("summer!!");
                    conventionTablePersonList.add(conventionTablePerson);
                }
                index += batchSize;
                break;
            }
        }



        conventionTables = conventionTableMapper.selectList(Wrappers.<ConventionTable>lambdaQuery().eq(ConventionTable::getFkConventionId, 37).eq(ConventionTable::getFkTableTypeKey, "CONVENTION_TRAINING_TABLE").like(ConventionTable::getTableNum, "_12_"));
        // 打乱列表顺序
        Collections.shuffle(allAgentIds);
        batchSize = 8;
        index = 0;
        tableNum = 0;
        for (ConventionTable conventionTable : conventionTables) {
            tableNum++;
            if (tableNum > 22) {
                batchSize = 7;
            }
            while (index < allAgentIds.size()) {
                List<Long> batchIds = allAgentIds.subList(index, Math.min(index + batchSize, allAgentIds.size()));
                for (Long batchId : batchIds) {
                    ConventionTablePerson conventionTablePerson = new ConventionTablePerson();
                    conventionTablePerson.setFkConventionTableId(conventionTable.getId());
                    conventionTablePerson.setFkConventionPersonId(batchId);
                    conventionTablePerson.setGmtCreate(new Date());
                    conventionTablePerson.setGmtCreateUser("summer!!");
                    conventionTablePersonList.add(conventionTablePerson);
                }
                index += batchSize;
                break;
            }
        }
        conventionTablePersonMapper.insertBatchSomeColumn(conventionTablePersonList);


    }





}

package com.get.salecenter.service.impl;

import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.GetDateUtil;
import com.get.core.secure.StaffInfo;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.tool.utils.RequestHeaderHandler;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.reportcenter.feign.IReportCenterClient;
import com.get.salecenter.vo.PeriodicStatisticsVo;
import com.get.salecenter.vo.StatisticsVo;
import com.get.salecenter.vo.StudentApplicationStatisticsVo;
import com.get.salecenter.service.AsyncStatisticsService;
import com.get.salecenter.service.StudentApplicationStatisticsService;
import com.get.salecenter.dto.ApplicationStatisticsDto;
import com.get.salecenter.dto.StatisticsDto;
import com.get.salecenter.dto.StudentApplicationStatisticsDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @DATE: 2022/3/30
 * @TIME: 0:18
 * @Description:
 **/
@Slf4j
@Service
public class StudentApplicationStatisticsImpl implements StudentApplicationStatisticsService {
    @Resource
    private IInstitutionCenterClient institutionCenterClient;

    @Resource
    private AsyncStatisticsService asyncStatisticsService;

    @Resource
    private IReportCenterClient reportCenterClient;

    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Async
    @Override
    public void generateStudentApplicationStatisticsReportSale(StudentApplicationStatisticsDto studentApplicationStatisticsDto, Map<String, String> headerMap, StaffInfo staffInfo){
        //将认证头部信息写入到现成请求头中
        RequestHeaderHandler.setHeaderMap(headerMap);
        //新增报表记录
        Long fkReportSaleId = reportCenterClient.addReportSale(studentApplicationStatisticsDto).getData();

        //国家(英国、美国、加拿大、澳洲、新西兰、爱尔兰、荷兰、瑞士)
        List<Long> fkAreaCountryIds = new ArrayList<>(Arrays.asList(7L,4L,5L,6L,8L,23L,20L,21L));

        Date date = new Date();
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int year = cal.get(Calendar.YEAR);
        int month = cal.get(Calendar.MONTH) + 1;
        int week = Calendar.FRIDAY;
        //当前季度
        int quarter = GetDateUtil.getQuarter(date);
        //季度开始月份
        int quarterStartMonth = 0;
        //季度结束月份
        int quarterEndMonth = 0;
        //前一季度
        switch (quarter) {
            case 1:
                quarterStartMonth = 1;
                quarterEndMonth = 3;
                break;
            case 2:
                quarterStartMonth = 4;
                quarterEndMonth = 6;
                break;
            case 3:
                quarterStartMonth = 7;
                quarterEndMonth = 9;
                break;
            case 4:
                quarterStartMonth = 10;
                quarterEndMonth = 12;
                break;
        }
        //获取员工以及旗下员工所创建的代理ids
        Long staffId = staffInfo.getStaffId();
        List<Long> staffFollowerIds = new ArrayList<>();
        Result<List<Long>> result = permissionCenterClient.getStaffFollowerIds(staffId);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            staffFollowerIds.addAll(result.getData());
        }
        staffFollowerIds.add(staffId);
        studentApplicationStatisticsDto.setStaffFollowerIds(staffFollowerIds);

        //业务国家
        List<String> fkAreaCountryKeyList = permissionCenterClient.getStaffAreaCountryKeysByfkStaffId(staffId).getData();
        List<Long> fkAreaCountryIdList = institutionCenterClient.getCountryIdByKey(fkAreaCountryKeyList).getData();
        studentApplicationStatisticsDto.setFkAreaCountryIdList(fkAreaCountryIdList);
        PeriodicStatisticsVo periodicStatisticsVo = new PeriodicStatisticsVo();
        List<StatisticsVo> weekStatisticsVos = getWeekStatisticsDtos(staffInfo,fkAreaCountryIds, date, week, studentApplicationStatisticsDto);
        List<StatisticsVo> monthStatisticsVos = getMonthStatisticsDtos(staffInfo,fkAreaCountryIds, year, month, studentApplicationStatisticsDto);
        List<StatisticsVo> quarterStatisticsVos = getQuarterStatisticsDtos(staffInfo,fkAreaCountryIds, year, quarterStartMonth, quarterEndMonth, studentApplicationStatisticsDto);
        List<StatisticsVo> yearStatisticsVos = getYearStatisticsDtos(staffInfo,fkAreaCountryIds, year, studentApplicationStatisticsDto);
        periodicStatisticsVo.setWeekStatistics(weekStatisticsVos);
        Date weekDateEnd = weekStatisticsVos.get(0).getWeekDateEnd();
        Date weekStatisticsChangeDate = GetDateUtil.getDateAfterDays(weekDateEnd,7);
        periodicStatisticsVo.setWeekStatisticsChangeDate(weekStatisticsChangeDate);
        periodicStatisticsVo.setMonthStatistics(monthStatisticsVos);
        Date monthDateEnd = GetDateUtil.getEndTime(year,month);
        periodicStatisticsVo.setMonthStatisticsChangeDate(monthDateEnd);
        periodicStatisticsVo.setQuarterStatistics(quarterStatisticsVos);
        periodicStatisticsVo.setYearStatistics(yearStatisticsVos);
        periodicStatisticsVo.setTestDate(new Date());
        reportCenterClient.updateReportSale(periodicStatisticsVo,fkReportSaleId);
    }
    /**
     * 周报统计
     */
    public List<StatisticsVo> getWeekStatisticsDtos(StaffInfo staffInfo, List<Long> fkAreaCountryIds, Date date, int week, StudentApplicationStatisticsDto studentApplicationStatisticsDto) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_WEEK, Calendar.FRIDAY);
        calendar.set(Calendar.HOUR_OF_DAY, 12);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        //获取周五十二点零分零秒的时间
        Date friday = calendar.getTime();
        //开始时间
        Date weekStartTime = null;
        //结束时间
        Date weekEndTime = null;
        if (friday.compareTo(date) > 0) {
            Date startTime = GetDateUtil.getWeekDayAfterWeekNum(date, -2, week);
            calendar.clear();
            calendar.setTime(startTime);
            calendar.set(Calendar.HOUR_OF_DAY, 12);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            weekStartTime = calendar.getTime();

            Date endTime = GetDateUtil.getWeekDayAfterWeekNum(date, -1, week);
            calendar.clear();
            calendar.setTime(endTime);
            calendar.set(Calendar.HOUR_OF_DAY, 11);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);
            weekEndTime = calendar.getTime();
        } else {
            Date startTime = GetDateUtil.getWeekDayAfterWeekNum(date, -1, week);
            calendar.clear();
            calendar.setTime(startTime);
            calendar.set(Calendar.HOUR_OF_DAY, 12);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            weekStartTime = calendar.getTime();

            calendar.clear();
            calendar.setTime(friday);
            calendar.set(Calendar.HOUR_OF_DAY, 11);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);
            weekEndTime = calendar.getTime();
        }
        //所有业务国家统计数据
        StatisticsDto weekStatisticsDto = new StatisticsDto();
        BeanCopyUtils.copyProperties(studentApplicationStatisticsDto, weekStatisticsDto, StatisticsDto.class);
        weekStatisticsDto.setStartTime(weekStartTime);
        weekStatisticsDto.setEndTime(weekEndTime);
        weekStatisticsDto.setQueryType(3);
        weekStatisticsDto.setIsDelay(true);
        List<StudentApplicationStatisticsVo> weekStatisticsDtos = getStatisticsDtos(weekStatisticsDto, staffInfo.getIsStudentOfferItemFinancialHiding(), staffInfo.getIsStudentAdmin());

        //小计
        StatisticsDto weekVo = new StatisticsDto();
        BeanCopyUtils.copyProperties(studentApplicationStatisticsDto, weekVo, StatisticsDto.class);
        weekVo.setStartTime(weekStartTime);
        weekVo.setEndTime(weekEndTime);
        weekVo.setQueryType(4);
        weekVo.setIsDelay(true);
        List<StudentApplicationStatisticsVo> weekStatisticsCountDtos = getStatisticsDtos(weekVo, staffInfo.getIsStudentOfferItemFinancialHiding(), staffInfo.getIsStudentAdmin());
        //构造完整数据结构返回
        Map<Long, StudentApplicationStatisticsVo> weekStatisticsDtoMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(weekStatisticsDtos)) {
            weekStatisticsDtoMap = weekStatisticsDtos.stream()
                    .collect(Collectors.toMap(StudentApplicationStatisticsVo::getFkAreaCountryId, Function.identity()));
        }
        //有统计数据的业务国家+固定8个国家（去重）
        List<Long> countryIdList = Stream.of(weekStatisticsDtoMap.keySet(),fkAreaCountryIds)
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());

        List<StatisticsVo> resout = constructResout(weekStartTime,
                weekEndTime, null, null, null, null, null, null, countryIdList, false,3, weekStatisticsDtoMap, null, null, null);

        //小计
        StatisticsVo statisticsVo = constructSubtotalResout(
                "小计",
                weekStartTime,
                weekEndTime,
                null,
                null,
                null,
                null,
                null,
                null,
                false,
                4,
                weekStatisticsCountDtos,
                null,
                null,
                null
        );
        //小计的国家列表
        statisticsVo.setFkAreaCountryIds(countryIdList);
        resout.add(statisticsVo);
        for(StatisticsVo dto:resout){
            dto.setWeekDateStart(weekStartTime);
            dto.setWeekDateEnd(weekEndTime);
        }
        return resout;

    }




    /**
     * 月报统计（同比）
     */
    public List<StatisticsVo> getMonthStatisticsDtos(StaffInfo staffInfo, List<Long> fkAreaCountryIds, int year, int month, StudentApplicationStatisticsDto studentApplicationStatisticsDto) {
        //当前年，月份对应为当前时间对应上一个月份
        Date thisYearMonthStartTime = GetDateUtil.getBeginTime((month-1) > 0 ? year : year-1, (month-1) > 0 ? (month-1) : 12 );
        Date thisYearMonthEndTime = GetDateUtil.getEndTime((month-1) > 0 ? year : year-1, (month-1) > 0 ? (month-1) : 12);
        StatisticsDto thisYearMonthStatisticsDto = new StatisticsDto();
        BeanCopyUtils.copyProperties(studentApplicationStatisticsDto, thisYearMonthStatisticsDto, StatisticsDto.class);
        thisYearMonthStatisticsDto.setStartTime(thisYearMonthStartTime);
        thisYearMonthStatisticsDto.setEndTime(thisYearMonthEndTime);
        thisYearMonthStatisticsDto.setQueryType(2);
        thisYearMonthStatisticsDto.setIsDelay(true);
        List<StudentApplicationStatisticsVo> thisYearMonthStatisticsDtos = getStatisticsDtos(thisYearMonthStatisticsDto, staffInfo.getIsStudentOfferItemFinancialHiding(), staffInfo.getIsStudentAdmin());
        //小计
        StatisticsDto thisYearVo = new StatisticsDto();
        BeanCopyUtils.copyProperties(studentApplicationStatisticsDto, thisYearVo, StatisticsDto.class);
        thisYearVo.setStartTime(thisYearMonthStartTime);
        thisYearVo.setEndTime(thisYearMonthEndTime);
        thisYearVo.setQueryType(1);
        thisYearVo.setIsDelay(true);
        List<StudentApplicationStatisticsVo> thisYearMonthStatisticsCountDtos = getStatisticsDtos(thisYearVo, staffInfo.getIsStudentOfferItemFinancialHiding(), staffInfo.getIsStudentAdmin());



        //上一年，月份对应为当前时间对应上一个月份
        Date lastYearMonthStartTime = GetDateUtil.getBeginTime((month-1) > 0 ? year-1 : year-2,(month-1) > 0 ? (month-1) : 12);
        Date lastYearMonthEndTime = GetDateUtil.getEndTime((month-1) > 0 ? year-1 : year-2,(month-1) > 0 ? (month-1) : 12);
        StatisticsDto lastYearMonthStatisticsDto = new StatisticsDto();
        BeanCopyUtils.copyProperties(studentApplicationStatisticsDto, lastYearMonthStatisticsDto, StatisticsDto.class);
        lastYearMonthStatisticsDto.setStartTime(lastYearMonthStartTime);
        lastYearMonthStatisticsDto.setEndTime(lastYearMonthEndTime);
        lastYearMonthStatisticsDto.setQueryType(2);
        lastYearMonthStatisticsDto.setIsDelay(true);
        List<StudentApplicationStatisticsVo> lastYearMonthStatisticsDtos = getStatisticsDtos(lastYearMonthStatisticsDto, staffInfo.getIsStudentOfferItemFinancialHiding(), staffInfo.getIsStudentAdmin());
        //小计
        StatisticsDto lastYearVo = new StatisticsDto();
        BeanCopyUtils.copyProperties(studentApplicationStatisticsDto, lastYearVo, StatisticsDto.class);
        lastYearVo.setStartTime(lastYearMonthStartTime);
        lastYearVo.setEndTime(lastYearMonthEndTime);
        lastYearVo.setQueryType(1);
        lastYearVo.setIsDelay(true);
        List<StudentApplicationStatisticsVo> lastYearMonthStatisticsCountDtos = getStatisticsDtos(lastYearVo, staffInfo.getIsStudentOfferItemFinancialHiding(), staffInfo.getIsStudentAdmin());



        //当前年累计，上一个月对应年份的1月1号至上一个月份的最后一天
        Date thisYearCumulativeStartTime = GetDateUtil.getBeginTime((month-1) > 0 ? year : year-1 , 1);
        Date thisYearCumulativeEndTime = GetDateUtil.getEndTime((month-1) > 0 ? year : year-1 , (month-1) > 0 ? (month-1) : 12);
        StatisticsDto thisYearCumulativeStatisticsDto = new StatisticsDto();
        BeanCopyUtils.copyProperties(studentApplicationStatisticsDto, thisYearCumulativeStatisticsDto, StatisticsDto.class);
        thisYearCumulativeStatisticsDto.setStartTime(thisYearCumulativeStartTime);
        thisYearCumulativeStatisticsDto.setEndTime(thisYearCumulativeEndTime);
        thisYearCumulativeStatisticsDto.setQueryType(2);
        thisYearCumulativeStatisticsDto.setIsDelay(true);
        List<StudentApplicationStatisticsVo> thisYearCumulativeStatisticsDtos = getStatisticsDtos(thisYearCumulativeStatisticsDto, staffInfo.getIsStudentOfferItemFinancialHiding(), staffInfo.getIsStudentAdmin());
        //小计
        StatisticsDto thisYearCumulativeVo = new StatisticsDto();
        BeanCopyUtils.copyProperties(studentApplicationStatisticsDto, thisYearCumulativeVo, StatisticsDto.class);
        thisYearCumulativeVo.setStartTime(thisYearCumulativeStartTime);
        thisYearCumulativeVo.setEndTime(thisYearCumulativeEndTime);
        thisYearCumulativeVo.setQueryType(1);
        thisYearCumulativeVo.setIsDelay(true);
        List<StudentApplicationStatisticsVo> thisYearCumulativeStatisticsCountDtos = getStatisticsDtos(thisYearCumulativeVo, staffInfo.getIsStudentOfferItemFinancialHiding(), staffInfo.getIsStudentAdmin());


        //上一年累计
        Date lastYearCumulativeStartTime = GetDateUtil.getBeginTime((month-1) > 0 ? year-1 : year-2, 1);
        Date lastYearCumulativeEndTime = GetDateUtil.getEndTime((month-1) > 0 ? year-1 : year-2,(month-1) > 0 ? (month-1) : 12);
        StatisticsDto lastYearCumulativeStatisticsDto = new StatisticsDto();
        BeanCopyUtils.copyProperties(studentApplicationStatisticsDto, lastYearCumulativeStatisticsDto, StatisticsDto.class);
        lastYearCumulativeStatisticsDto.setStartTime(lastYearCumulativeStartTime);
        lastYearCumulativeStatisticsDto.setEndTime(lastYearCumulativeEndTime);
        lastYearCumulativeStatisticsDto.setQueryType(2);
        lastYearCumulativeStatisticsDto.setIsDelay(true);
        List<StudentApplicationStatisticsVo> lastYearCumulativeStatisticsDtos = getStatisticsDtos(lastYearCumulativeStatisticsDto, staffInfo.getIsStudentOfferItemFinancialHiding(), staffInfo.getIsStudentAdmin());
        //小计
        StatisticsDto lastYearCumulativeVo = new StatisticsDto();
        BeanCopyUtils.copyProperties(studentApplicationStatisticsDto, lastYearCumulativeVo, StatisticsDto.class);
        lastYearCumulativeVo.setStartTime(lastYearCumulativeStartTime);
        lastYearCumulativeVo.setEndTime(lastYearCumulativeEndTime);
        lastYearCumulativeVo.setQueryType(1);
        lastYearCumulativeVo.setIsDelay(true);
        List<StudentApplicationStatisticsVo> lastYearCumulativeStatisticsCountDtos = getStatisticsDtos(lastYearCumulativeVo, staffInfo.getIsStudentOfferItemFinancialHiding(), staffInfo.getIsStudentAdmin());

        //构造完整数据结构返回
        Map<Long, StudentApplicationStatisticsVo> thisYearMonthStatisticsDtoMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(thisYearMonthStatisticsDtos)) {
            thisYearMonthStatisticsDtoMap = thisYearMonthStatisticsDtos.stream()
                    .collect(Collectors.toMap(StudentApplicationStatisticsVo::getFkAreaCountryId, Function.identity()));
        }
        Map<Long, StudentApplicationStatisticsVo> lastYearMonthStatisticsDtoMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(lastYearMonthStatisticsDtos)) {
            lastYearMonthStatisticsDtoMap = lastYearMonthStatisticsDtos.stream()
                    .collect(Collectors.toMap(StudentApplicationStatisticsVo::getFkAreaCountryId, Function.identity()));
        }
        Map<Long, StudentApplicationStatisticsVo> thisYearCumulativeStatisticsDtoMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(thisYearCumulativeStatisticsDtos)) {
            thisYearCumulativeStatisticsDtoMap = thisYearCumulativeStatisticsDtos.stream()
                    .collect(Collectors.toMap(StudentApplicationStatisticsVo::getFkAreaCountryId, Function.identity()));
        }
        Map<Long, StudentApplicationStatisticsVo> lastYearCumulativeStatisticsDtoMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(lastYearCumulativeStatisticsDtos)) {
            lastYearCumulativeStatisticsDtoMap = lastYearCumulativeStatisticsDtos.stream()
                    .collect(Collectors.toMap(StudentApplicationStatisticsVo::getFkAreaCountryId, Function.identity()));
        }

        //有统计数据的业务国家+固定8个国家（去重）
        List<Long> countryIdList = Stream.of(
                thisYearMonthStatisticsDtoMap.keySet(),
                lastYearMonthStatisticsDtoMap.keySet(),
                thisYearCumulativeStatisticsDtoMap.keySet(),
                lastYearCumulativeStatisticsDtoMap.keySet(),
                fkAreaCountryIds)
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
        List<StatisticsVo> resout = constructResout(
                lastYearMonthStartTime,
                lastYearMonthEndTime,
                thisYearMonthStartTime,
                thisYearMonthEndTime,
                lastYearCumulativeStartTime,
                lastYearCumulativeEndTime,
                thisYearCumulativeStartTime,
                thisYearCumulativeEndTime,
                countryIdList,
                true,
                2,
                lastYearMonthStatisticsDtoMap,
                thisYearMonthStatisticsDtoMap,
                lastYearCumulativeStatisticsDtoMap,
                thisYearCumulativeStatisticsDtoMap);
        //小计
        StatisticsVo statisticsVo = constructSubtotalResout(
                "小计",
                lastYearMonthStartTime,
                lastYearMonthEndTime,
                thisYearMonthStartTime,
                thisYearMonthEndTime,
                lastYearCumulativeStartTime,
                lastYearCumulativeEndTime,
                thisYearCumulativeStartTime,
                thisYearCumulativeEndTime,
                true,
                1,
                lastYearMonthStatisticsCountDtos,
                thisYearMonthStatisticsCountDtos,
                lastYearCumulativeStatisticsCountDtos,
                thisYearCumulativeStatisticsCountDtos
        );
        //小计的国家列表
        statisticsVo.setFkAreaCountryIds(countryIdList);
        resout.add(statisticsVo);
        return resout;
    }

    /**
     * 季报统计
     */
    public List<StatisticsVo> getQuarterStatisticsDtos(StaffInfo staffInfo, List<Long> fkAreaCountryIds, int year, int quarterStartMonth, int quarterEndMonth, StudentApplicationStatisticsDto studentApplicationStatisticsDto) {
        //往前推一个季度-3个月
        Date oneQuarterStartTime = GetDateUtil.getBeginTime((quarterStartMonth-3) > 0 ? year : (year -1) , (quarterStartMonth-3) > 0 ? (quarterStartMonth-3) : 10 );
        //当前季度结束时间
        Date oneQuarterEndTime = GetDateUtil.getEndTime((quarterEndMonth-3) > 0 ? year : (year -1), (quarterEndMonth-3) > 0 ? (quarterEndMonth-3) : 12);
        StatisticsDto oneQuarterStatisticsDto = new StatisticsDto();
        BeanCopyUtils.copyProperties(studentApplicationStatisticsDto, oneQuarterStatisticsDto, StatisticsDto.class);
        oneQuarterStatisticsDto.setStartTime(oneQuarterStartTime);
        oneQuarterStatisticsDto.setEndTime(oneQuarterEndTime);
        oneQuarterStatisticsDto.setQueryType(2);
        oneQuarterStatisticsDto.setIsDelay(true);
        List<StudentApplicationStatisticsVo> oneQuarterStatisticsDtos = getStatisticsDtos(oneQuarterStatisticsDto, staffInfo.getIsStudentOfferItemFinancialHiding(), staffInfo.getIsStudentAdmin());
        //小计
        StatisticsDto oneQuarterVo = new StatisticsDto();
        BeanCopyUtils.copyProperties(studentApplicationStatisticsDto, oneQuarterVo, StatisticsDto.class);
        oneQuarterVo.setStartTime(oneQuarterStartTime);
        oneQuarterVo.setEndTime(oneQuarterEndTime);
        oneQuarterVo.setQueryType(1);
        oneQuarterVo.setIsDelay(true);
        List<StudentApplicationStatisticsVo> oneQuarterStatisticsCountDtos = getStatisticsDtos(oneQuarterVo, staffInfo.getIsStudentOfferItemFinancialHiding(), staffInfo.getIsStudentAdmin());

        //同比上一年
        Date twoQuarterStartTime = GetDateUtil.getBeginTime((quarterStartMonth-3) > 0 ? (year -1) : (year -2), (quarterStartMonth-3) > 0 ? (quarterStartMonth-3) : 10);
        Date twoQuarterEndTime = GetDateUtil.getEndTime((quarterEndMonth-3) > 0 ? (year -1) : (year -2), (quarterEndMonth-3) > 0 ? (quarterEndMonth-3) : 12);
        StatisticsDto twoQuarterStatisticsDto = new StatisticsDto();
        BeanCopyUtils.copyProperties(studentApplicationStatisticsDto, twoQuarterStatisticsDto, StatisticsDto.class);
        twoQuarterStatisticsDto.setStartTime(twoQuarterStartTime);
        twoQuarterStatisticsDto.setEndTime(twoQuarterEndTime);
        twoQuarterStatisticsDto.setQueryType(2);
        twoQuarterStatisticsDto.setIsDelay(true);
        List<StudentApplicationStatisticsVo> twoQuarterStatisticsDtos = getStatisticsDtos(twoQuarterStatisticsDto, staffInfo.getIsStudentOfferItemFinancialHiding(), staffInfo.getIsStudentAdmin());
        //小计
        StatisticsDto twoQuarterVo = new StatisticsDto();
        BeanCopyUtils.copyProperties(studentApplicationStatisticsDto, twoQuarterVo, StatisticsDto.class);
        twoQuarterVo.setStartTime(twoQuarterStartTime);
        twoQuarterVo.setEndTime(twoQuarterEndTime);
        twoQuarterVo.setQueryType(1);
        twoQuarterVo.setIsDelay(true);
        List<StudentApplicationStatisticsVo> twoQuarterStatisticsCountDtos = getStatisticsDtos(twoQuarterVo, staffInfo.getIsStudentOfferItemFinancialHiding(), staffInfo.getIsStudentAdmin());



        //年累计
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(oneQuarterStartTime);
        int oneCumulativeYear = calendar.get(Calendar.YEAR);
        Date oneCumulativeQuarterStartTime = GetDateUtil.getBeginTime(oneCumulativeYear, 1);
        Date oneCumulativeQuarterEndTime = oneQuarterEndTime;
        StatisticsDto oneCumulativeQuarterStatisticsDto = new StatisticsDto();
        BeanCopyUtils.copyProperties(studentApplicationStatisticsDto, oneCumulativeQuarterStatisticsDto, StatisticsDto.class);
        oneCumulativeQuarterStatisticsDto.setStartTime(oneCumulativeQuarterStartTime);
        oneCumulativeQuarterStatisticsDto.setEndTime(oneCumulativeQuarterEndTime);
        oneCumulativeQuarterStatisticsDto.setQueryType(2);
        oneCumulativeQuarterStatisticsDto.setIsDelay(true);
        List<StudentApplicationStatisticsVo> oneCumulativeQuarterStatisticsDtos = getStatisticsDtos(oneCumulativeQuarterStatisticsDto, staffInfo.getIsStudentOfferItemFinancialHiding(), staffInfo.getIsStudentAdmin());
        //小计
        StatisticsDto oneCumulativeQuarterVo = new StatisticsDto();
        BeanCopyUtils.copyProperties(studentApplicationStatisticsDto, oneCumulativeQuarterVo, StatisticsDto.class);
        oneCumulativeQuarterVo.setStartTime(oneCumulativeQuarterStartTime);
        oneCumulativeQuarterVo.setEndTime(oneCumulativeQuarterEndTime);
        oneCumulativeQuarterVo.setQueryType(1);
        oneCumulativeQuarterVo.setIsDelay(true);
        List<StudentApplicationStatisticsVo> oneCumulativeQuarterStatisticsCountDtos = getStatisticsDtos(oneCumulativeQuarterVo, staffInfo.getIsStudentOfferItemFinancialHiding(), staffInfo.getIsStudentAdmin());



        //同比年累计
        calendar.clear();
        calendar.setTime(twoQuarterStartTime);
        int twoCumulativeYear = calendar.get(Calendar.YEAR);
        Date twoCumulativeQuarterStartTime = GetDateUtil.getBeginTime(twoCumulativeYear, 1);
        Date twoCumulativeQuarterEndTime = twoQuarterEndTime;
        StatisticsDto twoCumulativeQuarterStatisticsDto = new StatisticsDto();
        BeanCopyUtils.copyProperties(studentApplicationStatisticsDto, twoCumulativeQuarterStatisticsDto, StatisticsDto.class);
        twoCumulativeQuarterStatisticsDto.setStartTime(twoCumulativeQuarterStartTime);
        twoCumulativeQuarterStatisticsDto.setEndTime(twoCumulativeQuarterEndTime);
        twoCumulativeQuarterStatisticsDto.setQueryType(2);
        twoCumulativeQuarterStatisticsDto.setIsDelay(true);
        List<StudentApplicationStatisticsVo> twoCumulativeQuarterStatisticsDtos = getStatisticsDtos(twoCumulativeQuarterStatisticsDto, staffInfo.getIsStudentOfferItemFinancialHiding(), staffInfo.getIsStudentAdmin());
        //小计
        StatisticsDto twoCumulativeQuarterVo = new StatisticsDto();
        BeanCopyUtils.copyProperties(studentApplicationStatisticsDto, twoCumulativeQuarterVo, StatisticsDto.class);
        twoCumulativeQuarterVo.setStartTime(twoCumulativeQuarterStartTime);
        twoCumulativeQuarterVo.setEndTime(twoCumulativeQuarterEndTime);
        twoCumulativeQuarterVo.setQueryType(1);
        twoCumulativeQuarterVo.setIsDelay(true);
        List<StudentApplicationStatisticsVo> twoCumulativeQuarterStatisticsCountDtos = getStatisticsDtos(twoCumulativeQuarterVo, staffInfo.getIsStudentOfferItemFinancialHiding(), staffInfo.getIsStudentAdmin());

        //构造完整数据结构返回
        Map<Long, StudentApplicationStatisticsVo> lastYearQuarterStatisticsDtoMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(oneQuarterStatisticsDtos)) {
            lastYearQuarterStatisticsDtoMap = oneQuarterStatisticsDtos.stream()
                    .collect(Collectors.toMap(StudentApplicationStatisticsVo::getFkAreaCountryId, Function.identity()));
        }
        Map<Long, StudentApplicationStatisticsVo> lastTwoYearQuarterStatisticsDtoMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(twoQuarterStatisticsDtos)) {
            lastTwoYearQuarterStatisticsDtoMap = twoQuarterStatisticsDtos.stream()
                    .collect(Collectors.toMap(StudentApplicationStatisticsVo::getFkAreaCountryId, Function.identity()));
        }
        Map<Long, StudentApplicationStatisticsVo> lastYearCumulativeQuarterStatisticsDtoMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(oneCumulativeQuarterStatisticsDtos)) {
            lastYearCumulativeQuarterStatisticsDtoMap = oneCumulativeQuarterStatisticsDtos.stream()
                    .collect(Collectors.toMap(StudentApplicationStatisticsVo::getFkAreaCountryId, Function.identity()));
        }
        Map<Long, StudentApplicationStatisticsVo> lastTwoYearCumulativeQuarterStatisticsDtoMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(twoCumulativeQuarterStatisticsDtos)) {
            lastTwoYearCumulativeQuarterStatisticsDtoMap = twoCumulativeQuarterStatisticsDtos.stream()
                    .collect(Collectors.toMap(StudentApplicationStatisticsVo::getFkAreaCountryId, Function.identity()));
        }

        //有统计数据的业务国家+固定8个国家（去重）
        List<Long> countryIdList = Stream.of(
                lastYearQuarterStatisticsDtoMap.keySet(),
                lastTwoYearQuarterStatisticsDtoMap.keySet(),
                lastYearCumulativeQuarterStatisticsDtoMap.keySet(),
                lastTwoYearCumulativeQuarterStatisticsDtoMap.keySet(),
                fkAreaCountryIds)
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());

        List<StatisticsVo> resout = constructResout(
                twoQuarterStartTime,
                twoQuarterEndTime,
                oneQuarterStartTime,
                oneQuarterEndTime,
                twoCumulativeQuarterStartTime,
                twoCumulativeQuarterEndTime,
                oneCumulativeQuarterStartTime,
                oneCumulativeQuarterEndTime,
                countryIdList,
                true,
                2,
                lastTwoYearQuarterStatisticsDtoMap,
                lastYearQuarterStatisticsDtoMap,
                lastTwoYearCumulativeQuarterStatisticsDtoMap,
                lastYearCumulativeQuarterStatisticsDtoMap);
        //小计
        StatisticsVo statisticsVo = constructSubtotalResout(
                "小计",
                twoQuarterStartTime,
                twoQuarterEndTime,
                oneQuarterStartTime,
                oneQuarterEndTime,
                twoCumulativeQuarterStartTime,
                twoCumulativeQuarterEndTime,
                oneCumulativeQuarterStartTime,
                oneCumulativeQuarterEndTime,
                true,
                1,
                twoQuarterStatisticsCountDtos,
                oneQuarterStatisticsCountDtos,
                twoCumulativeQuarterStatisticsCountDtos,
                oneCumulativeQuarterStatisticsCountDtos
        );
        //小计的国家列表
        statisticsVo.setFkAreaCountryIds(countryIdList);
        resout.add(statisticsVo);
        return resout;
    }


    /**
     * 年报统计
     */
    public List<StatisticsVo> getYearStatisticsDtos(StaffInfo staffInfo, List<Long> fkAreaCountryIds, int year, StudentApplicationStatisticsDto studentApplicationStatisticsDto) {

        //上一年
        Date lastYearStartTime = GetDateUtil.getBeginTime(year - 1, 1);
        Date lastYearEndTime = GetDateUtil.getEndTime(year - 1, 12);
        StatisticsDto lastYearStatisticsDto = new StatisticsDto();
        BeanCopyUtils.copyProperties(studentApplicationStatisticsDto, lastYearStatisticsDto, StatisticsDto.class);
        lastYearStatisticsDto.setStartTime(lastYearStartTime);
        lastYearStatisticsDto.setEndTime(lastYearEndTime);
        lastYearStatisticsDto.setQueryType(2);
        lastYearStatisticsDto.setIsDelay(true);
        List<StudentApplicationStatisticsVo> lastYearStatisticsDtos = getStatisticsDtos(lastYearStatisticsDto, staffInfo.getIsStudentOfferItemFinancialHiding(), staffInfo.getIsStudentAdmin());
        //小计
        StatisticsDto lastYearVo = new StatisticsDto();
        BeanCopyUtils.copyProperties(studentApplicationStatisticsDto, lastYearVo, StatisticsDto.class);
        lastYearVo.setStartTime(lastYearStartTime);
        lastYearVo.setEndTime(lastYearEndTime);
        lastYearVo.setQueryType(1);
        lastYearVo.setIsDelay(true);
        List<StudentApplicationStatisticsVo> lastYearStatisticsCountDtos = getStatisticsDtos(lastYearVo, staffInfo.getIsStudentOfferItemFinancialHiding(), staffInfo.getIsStudentAdmin());


        //上两年
        Date lastTwoYearStartTime = GetDateUtil.getBeginTime(year - 2, 1);
        Date lastTwoYearEndTime = GetDateUtil.getEndTime(year - 2, 12);
        StatisticsDto lastYearTwoStatisticsDto = new StatisticsDto();
        BeanCopyUtils.copyProperties(studentApplicationStatisticsDto, lastYearTwoStatisticsDto, StatisticsDto.class);
        lastYearTwoStatisticsDto.setStartTime(lastTwoYearStartTime);
        lastYearTwoStatisticsDto.setEndTime(lastTwoYearEndTime);
        lastYearTwoStatisticsDto.setQueryType(2);
        lastYearTwoStatisticsDto.setIsDelay(true);
        List<StudentApplicationStatisticsVo> lastYearTwoStatisticsDtos = getStatisticsDtos(lastYearTwoStatisticsDto, staffInfo.getIsStudentOfferItemFinancialHiding(), staffInfo.getIsStudentAdmin());

        //小计
        StatisticsDto lastYearTwoVo = new StatisticsDto();
        BeanCopyUtils.copyProperties(studentApplicationStatisticsDto, lastYearTwoVo, StatisticsDto.class);
        lastYearTwoVo.setStartTime(lastTwoYearStartTime);
        lastYearTwoVo.setEndTime(lastTwoYearEndTime);
        lastYearTwoVo.setQueryType(1);
        lastYearTwoVo.setIsDelay(true);
        List<StudentApplicationStatisticsVo> lastYearTwoStatisticsCountDtos = getStatisticsDtos(lastYearTwoVo, staffInfo.getIsStudentOfferItemFinancialHiding(), staffInfo.getIsStudentAdmin());

        //构造完整数据结构返回
        Map<Long, StudentApplicationStatisticsVo> lastYearStatisticsDtoMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(lastYearStatisticsDtos)) {
            lastYearStatisticsDtoMap = lastYearStatisticsDtos.stream()
                    .collect(Collectors.toMap(StudentApplicationStatisticsVo::getFkAreaCountryId, Function.identity()));
        }
        Map<Long, StudentApplicationStatisticsVo> lastTwoYearStatisticsDtoMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(lastYearTwoStatisticsDtos)) {
            lastTwoYearStatisticsDtoMap = lastYearTwoStatisticsDtos.stream()
                    .collect(Collectors.toMap(StudentApplicationStatisticsVo::getFkAreaCountryId, Function.identity()));
        }
        //有统计数据的业务国家+固定8个国家（去重）
        List<Long> countryIdList = Stream.of(
                lastYearStatisticsDtoMap.keySet(),
                lastTwoYearStatisticsDtoMap.keySet(),
                fkAreaCountryIds)
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());

        List<StatisticsVo> resout = constructResout(
                lastTwoYearStartTime,
                lastTwoYearEndTime,
                lastYearStartTime,
                lastYearEndTime,
                null,
                null,
                null,
                null,
                countryIdList,
                false,
                2,
                lastTwoYearStatisticsDtoMap,lastYearStatisticsDtoMap, null, null);
        //小计
        StatisticsVo statisticsVo = constructSubtotalResout(
                "小计",
                lastTwoYearStartTime,
                lastTwoYearEndTime,
                lastYearStartTime,
                lastYearEndTime,
                null,
                null,
                null,
                null,
                false,
                1,
                lastYearTwoStatisticsCountDtos,
                lastYearStatisticsCountDtos,
                null,
                null
        );
        //小计的国家列表
        statisticsVo.setFkAreaCountryIds(countryIdList);
        resout.add(statisticsVo);
        return resout;
    }

    /**
     * 获取统计
     * <AUTHOR>
     * @DateTime 2022/11/22 14:37
     */
    @Override
    public List<StudentApplicationStatisticsVo> getStatisticsDtos(StatisticsDto statisticsDto, Boolean isStudentOfferItemFinancialHiding, Boolean isStudentAdmin){
        List<StudentApplicationStatisticsVo> list = new ArrayList<>();
        List<StudentApplicationStatisticsVo> stateIsNulllist = new ArrayList<>();
        List<StudentApplicationStatisticsVo> createStatisticsDtos = asyncStatisticsService.getCreateStatisticsDtos(statisticsDto,"createStatistics",isStudentOfferItemFinancialHiding,isStudentAdmin);
       //防止服务器cpu过高，延迟1秒执行
        if(statisticsDto.getIsDelay()){
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

        List<StudentApplicationStatisticsVo> applicationStatisticsDtos = asyncStatisticsService.getApplicationStatisticsDtos(statisticsDto,"applicationStatistics",isStudentOfferItemFinancialHiding,isStudentAdmin);
        if(statisticsDto.getIsDelay()){
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        List<StudentApplicationStatisticsVo> confirmationStatisticsDtos = asyncStatisticsService.getConfirmationStatisticsDtos(statisticsDto,"confirmationStatistics",isStudentOfferItemFinancialHiding,isStudentAdmin);
        if(statisticsDto.getIsDelay()){
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        List<StudentApplicationStatisticsVo> successStatisticsDtos = asyncStatisticsService.getSuccessStatisticsDtos(statisticsDto,"successStatistics",isStudentOfferItemFinancialHiding,isStudentAdmin);
        if(statisticsDto.getIsDelay()){
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

        //统计按学生
        List<StudentApplicationStatisticsVo> confirmationByStudentStatisticsDtos = null;
        List<StudentApplicationStatisticsVo> successByStudentStatisticsDtos = null;
        if(statisticsDto.getQueryType().equals(6)){
            //代理省份送生统计
            confirmationByStudentStatisticsDtos = asyncStatisticsService.getAgentStateApplicationStatisticsByStudent(statisticsDto,"confirmationByStudentStatistics", isStudentOfferItemFinancialHiding,isStudentAdmin);
            successByStudentStatisticsDtos = asyncStatisticsService.getAgentStateApplicationStatisticsByStudent(statisticsDto,"successByStudentStatistics", isStudentOfferItemFinancialHiding,isStudentAdmin);
        }else{
            confirmationByStudentStatisticsDtos = asyncStatisticsService.getConfirmationByStudentStatisticsDtos(statisticsDto,"confirmationByStudentStatistics", isStudentOfferItemFinancialHiding,isStudentAdmin);
            if(statisticsDto.getIsDelay()){
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
            successByStudentStatisticsDtos = asyncStatisticsService.getSuccessByStudentStatisticsDtos(statisticsDto,"successByStudentStatistics", isStudentOfferItemFinancialHiding,isStudentAdmin);
            if(statisticsDto.getIsDelay()){
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }


        BigDecimal stateAndCountryIsNullCreateStatistics = new BigDecimal(0);
        BigDecimal stateAndCountryIsNullApplicationStatistics = new BigDecimal(0);
        BigDecimal stateAndCountryIsNullConfirmationStatistics = new BigDecimal(0);
        BigDecimal stateAndCountryIsNullSuccessStatistics = new BigDecimal(0);
        BigDecimal stateAndCountryIsNullconfirmationByStudentStatistics = new BigDecimal(0);
        BigDecimal stateAndCountryIsNullSuccessByStudentStatistics = new BigDecimal(0);
        try {

            if(statisticsDto.getQueryType().equals(1) || statisticsDto.getQueryType().equals(4) || statisticsDto.getQueryType().equals(5)){
                StudentApplicationStatisticsVo dto = new StudentApplicationStatisticsVo();
                dto.setCreateCount(createStatisticsDtos.get(0).getCreateCount());
                dto.setApplicationCount(applicationStatisticsDtos.get(0).getApplicationCount());
                dto.setConfirmationCount(confirmationStatisticsDtos.get(0).getConfirmationCount());
                dto.setSuccessCount(successStatisticsDtos.get(0).getSuccessCount());
                dto.setConfirmationCountByStudent(confirmationByStudentStatisticsDtos.get(0).getConfirmationCountByStudent());
                dto.setSuccessCountByStudent(successByStudentStatisticsDtos.get(0).getSuccessCountByStudent());
                list.add(dto);
            } else {
                Map<Long, List<StudentApplicationStatisticsVo>> createStatisticsMap = null;
                Map<Long,List<StudentApplicationStatisticsVo>> applicationStatisticsMap = null;
                Map<Long,List<StudentApplicationStatisticsVo>> confirmationStatisticsMap = null;
                Map<Long,List<StudentApplicationStatisticsVo>> successStatisticsMap = null;
                Map<Long,List<StudentApplicationStatisticsVo>> confirmationByStudentStatisticsMap = null;
                Map<Long,List<StudentApplicationStatisticsVo>> successByStudentStatisticsMap = null;
                Map<Long, StudentApplicationStatisticsVo> stateCreateIsNullMap = null;
                Map<Long, StudentApplicationStatisticsVo> stateIsNullApplicationMap = null;
                Map<Long, StudentApplicationStatisticsVo> stateIsNullConfirmationMap = null;
                Map<Long, StudentApplicationStatisticsVo> stateIsNullSuccessMap = null;
                Map<Long, StudentApplicationStatisticsVo> stateIsNullConfirmationByStudentMap = null;
                Map<Long, StudentApplicationStatisticsVo> stateIsNullSuccessByStudentMap = null;
                //代理省份送生
                if(statisticsDto.getQueryType().equals(6)){
                    if(GeneralTool.isNotEmpty(createStatisticsDtos)){
                        stateCreateIsNullMap = dealWithPropertiesIsNull(createStatisticsDtos,stateCreateIsNullMap,stateAndCountryIsNullCreateStatistics,"createStatistics");
                        createStatisticsMap =  createStatisticsDtos.stream().filter(s->GeneralTool.isNotEmpty(s.getFkAreaStateId()))
                                .collect(Collectors.groupingBy(StudentApplicationStatisticsVo::getFkAreaStateId));
                    }
                    if(GeneralTool.isNotEmpty(applicationStatisticsDtos)){
                        stateIsNullApplicationMap = dealWithPropertiesIsNull(applicationStatisticsDtos,stateIsNullApplicationMap,stateAndCountryIsNullApplicationStatistics,"applicationStatistics");
                        applicationStatisticsMap =  applicationStatisticsDtos.stream().filter(s->GeneralTool.isNotEmpty(s.getFkAreaStateId()))
                                .collect(Collectors.groupingBy(StudentApplicationStatisticsVo::getFkAreaStateId));
                    }
                    if(GeneralTool.isNotEmpty(confirmationStatisticsDtos)){
                        stateIsNullConfirmationMap = dealWithPropertiesIsNull(confirmationStatisticsDtos,stateIsNullConfirmationMap,stateAndCountryIsNullConfirmationStatistics,"confirmationStatistics");
                        confirmationStatisticsMap =  confirmationStatisticsDtos.stream().filter(s->GeneralTool.isNotEmpty(s.getFkAreaStateId()))
                                .collect(Collectors.groupingBy(StudentApplicationStatisticsVo::getFkAreaStateId));
                    }
                    if(GeneralTool.isNotEmpty(successStatisticsDtos)){
                        stateIsNullSuccessMap = dealWithPropertiesIsNull(successStatisticsDtos,stateIsNullSuccessMap,stateAndCountryIsNullSuccessStatistics,"successStatistics");
                        successStatisticsMap =  successStatisticsDtos.stream().filter(s->GeneralTool.isNotEmpty(s.getFkAreaStateId()))
                                .collect(Collectors.groupingBy(StudentApplicationStatisticsVo::getFkAreaStateId));
                    }
                    if(GeneralTool.isNotEmpty(confirmationByStudentStatisticsDtos)){
                        stateIsNullConfirmationByStudentMap = dealWithPropertiesIsNull(confirmationByStudentStatisticsDtos,stateIsNullConfirmationByStudentMap,stateAndCountryIsNullconfirmationByStudentStatistics,"confirmationByStudentStatistics");
                        confirmationByStudentStatisticsMap =  confirmationByStudentStatisticsDtos.stream().filter(s->GeneralTool.isNotEmpty(s.getFkAreaStateId()))
                                .collect(Collectors.groupingBy(StudentApplicationStatisticsVo::getFkAreaStateId));
                    }
                    if(GeneralTool.isNotEmpty(successByStudentStatisticsDtos)){
                        stateIsNullSuccessByStudentMap = dealWithPropertiesIsNull(successByStudentStatisticsDtos,stateIsNullSuccessByStudentMap,stateAndCountryIsNullSuccessByStudentStatistics,"successByStudentStatistics");
                        successByStudentStatisticsMap =  successByStudentStatisticsDtos.stream().filter(s->GeneralTool.isNotEmpty(s.getFkAreaStateId())).
                    collect(Collectors.groupingBy(StudentApplicationStatisticsVo::getFkAreaStateId));
                    }

                }else{
                    //根据国家分组
                    if(GeneralTool.isNotEmpty(createStatisticsDtos)){
                        createStatisticsMap =  createStatisticsDtos.stream().collect(Collectors.groupingBy(StudentApplicationStatisticsVo::getFkAreaCountryId));
                    }
                    if(GeneralTool.isNotEmpty(applicationStatisticsDtos)){
                        applicationStatisticsMap =  applicationStatisticsDtos.stream().collect(Collectors.groupingBy(StudentApplicationStatisticsVo::getFkAreaCountryId));
                    }
                    if(GeneralTool.isNotEmpty(confirmationStatisticsDtos)){
                        confirmationStatisticsMap =  confirmationStatisticsDtos.stream().collect(Collectors.groupingBy(StudentApplicationStatisticsVo::getFkAreaCountryId));
                    }
                    if(GeneralTool.isNotEmpty(successStatisticsDtos)){
                        successStatisticsMap =  successStatisticsDtos.stream().collect(Collectors.groupingBy(StudentApplicationStatisticsVo::getFkAreaCountryId));
                    }
                    if(GeneralTool.isNotEmpty(confirmationByStudentStatisticsDtos)){
                        confirmationByStudentStatisticsMap =  confirmationByStudentStatisticsDtos.stream().collect(Collectors.groupingBy(StudentApplicationStatisticsVo::getFkAreaCountryId));
                    }
                    if(GeneralTool.isNotEmpty(successByStudentStatisticsDtos)){
                        successByStudentStatisticsMap =  successByStudentStatisticsDtos.stream().collect(Collectors.groupingBy(StudentApplicationStatisticsVo::getFkAreaCountryId));
                    }
                }

                //将所有统计值合并到一个对象
                Set<String> countryAndStateIdSet = new HashSet<>();
                mergeStatistics(createStatisticsMap,list,countryAndStateIdSet, statisticsDto.getQueryType(),"createStatistics");
                mergeStatistics(applicationStatisticsMap,list,countryAndStateIdSet, statisticsDto.getQueryType(),"applicationStatistics");
                mergeStatistics(confirmationStatisticsMap,list,countryAndStateIdSet, statisticsDto.getQueryType(),"confirmationStatistics");
                mergeStatistics(successStatisticsMap,list,countryAndStateIdSet, statisticsDto.getQueryType(),"successStatistics");
                mergeStatistics(confirmationByStudentStatisticsMap,list,countryAndStateIdSet, statisticsDto.getQueryType(),"confirmationByStudentStatistics");
                mergeStatistics(successByStudentStatisticsMap,list,countryAndStateIdSet, statisticsDto.getQueryType(),"successByStudentStatistics");


                //合并国家不为空,省份为空的数据
                Set<Long> fkAreaCountryIdSet = new HashSet<>();
                mergeStateNullStatistics(stateCreateIsNullMap,stateIsNulllist,fkAreaCountryIdSet,"createStatistics");
                mergeStateNullStatistics(stateIsNullApplicationMap,stateIsNulllist,fkAreaCountryIdSet,"applicationStatistics");
                mergeStateNullStatistics(stateIsNullConfirmationMap,stateIsNulllist,fkAreaCountryIdSet,"confirmationStatistics");
                mergeStateNullStatistics(stateIsNullSuccessMap,stateIsNulllist,fkAreaCountryIdSet,"successStatistics");
                mergeStateNullStatistics(stateIsNullConfirmationByStudentMap,stateIsNulllist,fkAreaCountryIdSet,"confirmationByStudentStatistics");
                mergeStateNullStatistics(stateIsNullSuccessByStudentMap,stateIsNulllist,fkAreaCountryIdSet,"successByStudentStatistics");
                if(GeneralTool.isNotEmpty(stateIsNulllist)){
                    list.addAll(stateIsNulllist);
                }

            }

        }catch (Exception e) {
            e.printStackTrace();
            log.error("异步报表统计异常：" + e.getMessage());
        }
        return list;
    }

    /**
     * 代理省份属性为空处理
     * <AUTHOR>
     * @DateTime 2022/11/25 19:00
     */
    public Map<Long, StudentApplicationStatisticsVo> dealWithPropertiesIsNull(List<StudentApplicationStatisticsVo> statisticsDtos, Map<Long, StudentApplicationStatisticsVo> stateIsNullStatistics, BigDecimal stateAndCountryIsNullStatistics, String type){
       List<StudentApplicationStatisticsVo> stateIsNullStatisticsList = statisticsDtos.stream()
                .filter(s->GeneralTool.isNotEmpty(s.getFkAreaCountryId()) && GeneralTool.isEmpty(s.getFkAreaStateId())).collect(Collectors.toList());
       if(GeneralTool.isNotEmpty(stateIsNullStatisticsList)){
           switch (type) {
               case "createStatistics":
                   stateIsNullStatistics = stateIsNullStatisticsList.stream().collect(Collectors.toMap(StudentApplicationStatisticsVo::getFkAreaCountryId, Function.identity()));
                   break;
               case "applicationStatistics":
                   stateIsNullStatistics = stateIsNullStatisticsList.stream().collect(Collectors.toMap(StudentApplicationStatisticsVo::getFkAreaCountryId, Function.identity()));
                   break;
               case "confirmationStatistics":
                   stateIsNullStatistics = stateIsNullStatisticsList.stream().collect(Collectors.toMap(StudentApplicationStatisticsVo::getFkAreaCountryId, Function.identity()));
                   break;
               case "successStatistics":
                   stateIsNullStatistics = stateIsNullStatisticsList.stream().collect(Collectors.toMap(StudentApplicationStatisticsVo::getFkAreaCountryId, Function.identity()));
                   break;
               case "confirmationByStudentStatistics":
                   stateIsNullStatistics = stateIsNullStatisticsList.stream().collect(Collectors.toMap(StudentApplicationStatisticsVo::getFkAreaCountryId, Function.identity()));
                   break;
               case "successByStudentStatistics":
                   stateIsNullStatistics = stateIsNullStatisticsList.stream().collect(Collectors.toMap(StudentApplicationStatisticsVo::getFkAreaCountryId, Function.identity()));
                   break;
           }
       }

        List<BigDecimal> stateAndCountryIsNullStatisticsList =  statisticsDtos.stream()
                .filter(s->GeneralTool.isEmpty(s.getFkAreaCountryId()) && GeneralTool.isEmpty(s.getFkAreaStateId()))
                .map(StudentApplicationStatisticsVo::getCreateCount).collect(Collectors.toList());
        stateAndCountryIsNullStatisticsList.forEach(i->stateAndCountryIsNullStatistics.add(GeneralTool.isEmpty(i) ? new BigDecimal(0):i ));
        return stateIsNullStatistics;
    }

    /**
     * 将所有统计值合并到一个对象
     * <AUTHOR>
     * @DateTime 2023/9/14 18:03
     */
    private void mergeStatistics(Map<Long,List<StudentApplicationStatisticsVo>> statisticsMap, List<StudentApplicationStatisticsVo> list, Set<String> idSet, Integer queryType, String type){
        if(GeneralTool.isNotEmpty(statisticsMap)) {
            Iterator<Map.Entry<Long, List<StudentApplicationStatisticsVo>>> it = statisticsMap.entrySet().iterator();
            while (it.hasNext()) {
                Map.Entry<Long, List<StudentApplicationStatisticsVo>> entry = it.next();
                for(StudentApplicationStatisticsVo dto:entry.getValue()){
                    StringBuffer sf = new StringBuffer();
                    sf.append(GeneralTool.isEmpty(dto.getFkAreaCountryId()) ? "" : dto.getFkAreaCountryId()).append(",").append(entry.getKey());
                    String key = queryType.equals(6) ? sf.toString() : String.valueOf(entry.getKey());

                    if (!idSet.contains(key)) {
                        idSet.add(key);
                        StudentApplicationStatisticsVo statisticsDto = new StudentApplicationStatisticsVo();
                        statisticsDto.setFkAreaStateId(entry.getKey());
                        statisticsDto.setFkAreaCountryId(dto.getFkAreaCountryId());
                        setProperties(dto,statisticsDto,type);

                        list.add(dto);
                    } else {
                        for(StudentApplicationStatisticsVo statisticsDto:list){
                            //代理省份统计
                            if(queryType.equals(6)){
                                if(GeneralTool.isEmpty(dto.getFkAreaCountryId())){
                                    if(GeneralTool.isEmpty(statisticsDto.getFkAreaCountryId()) && dto.getFkAreaStateId().equals(statisticsDto.getFkAreaStateId())){
                                        setProperties(dto,statisticsDto,type);
                                    }
                                }else{
                                    if(dto.getFkAreaCountryId().equals(statisticsDto.getFkAreaCountryId()) && dto.getFkAreaStateId().equals(statisticsDto.getFkAreaStateId())){
                                        setProperties(dto,statisticsDto,type);
                                    }
                                }
                            }else{
                                if(dto.getFkAreaCountryId().equals(statisticsDto.getFkAreaCountryId())){
                                    setProperties(dto, statisticsDto, type);
                                }
                            }

                        }
                    }

                }
            }
        }
    }


    /**
     * 合并国家不为空,省份为空的数据
     * <AUTHOR>
     * @DateTime 2022/11/25 19:21
     */
    public void mergeStateNullStatistics(Map<Long, StudentApplicationStatisticsVo> stateIsNullMap, List<StudentApplicationStatisticsVo> list, Set<Long> IdSet, String type){
        if(GeneralTool.isNotEmpty(stateIsNullMap)) {
            Iterator<Map.Entry<Long, StudentApplicationStatisticsVo>> it = stateIsNullMap.entrySet().iterator();
            while (it.hasNext()) {
                Map.Entry<Long, StudentApplicationStatisticsVo> entry = it.next();
                if (!IdSet.contains(entry.getKey())) {
                    IdSet.add(entry.getKey());
                    StudentApplicationStatisticsVo dto = new StudentApplicationStatisticsVo();
                    dto.setFkAreaCountryId(entry.getKey());
                    setProperties(dto, entry.getValue(), type);
                    list.add(dto);
                } else {
                    Map<Long, StudentApplicationStatisticsVo> dataMap = list.stream().collect(Collectors.toMap(StudentApplicationStatisticsVo::getFkAreaCountryId, Function.identity()));
                    StudentApplicationStatisticsVo dto = dataMap.get(entry.getKey());
                    dto.setFkAreaCountryId(entry.getKey());
                    setProperties(dto, entry.getValue(), type);
                }
            }
        }
    }

    /**
     * 统计量属性设置
     * <AUTHOR>
     * @DateTime 2022/11/25 19:25
     */
    public StudentApplicationStatisticsVo setProperties(StudentApplicationStatisticsVo newDto, StudentApplicationStatisticsVo oldDto, String type){
        switch (type){
            case "createStatistics":
                oldDto.setCreateCount(newDto.getCreateCount());
                break;
            case "applicationStatistics":
                oldDto.setApplicationCount(newDto.getApplicationCount());
                break;
            case "confirmationStatistics":
                oldDto.setConfirmationCount(newDto.getConfirmationCount());
                break;
            case "successStatistics":
                oldDto.setSuccessCount(newDto.getSuccessCount());
                break;
            case "confirmationByStudentStatistics":
                oldDto.setConfirmationCountByStudent(newDto.getConfirmationCountByStudent());
                break;
            case "successByStudentStatistics":
                oldDto.setSuccessCountByStudent(newDto.getSuccessCountByStudent());
                break;
        }
        return oldDto;
    }

    /**
     * 构造返回结果结构
     *
     * @param dateStartOne
     * @param dateStartTwo
     * @param fkAreaCountryIds
     * @param oneYearMonthStatisticsDtoMap
     * @param twoYearMonthStatisticsDtoMap
     * @return
     */
    @Override
    public List<StatisticsVo> constructResout(Date dateStartOne,
                                              Date dateEndOne,
                                              Date dateStartTwo,
                                              Date dateEndTwo,
                                              Date cumulativeDateStartOne,
                                              Date cumulativeDateEndOne,
                                              Date cumulativeDateStartTwo,
                                              Date cumulativeDateEndTwo,
                                              List<Long> fkAreaCountryIds,
                                              Boolean hasCumulative,
                                              Integer queryType,
                                              Map<Long, StudentApplicationStatisticsVo> oneYearMonthStatisticsDtoMap,
                                              Map<Long, StudentApplicationStatisticsVo> twoYearMonthStatisticsDtoMap,
                                              Map<Long, StudentApplicationStatisticsVo> oneYearCumulativeStatisticsDtoMap,
                                              Map<Long, StudentApplicationStatisticsVo> twoYearCumulativeStatisticsDtoMap) {

        List<StatisticsVo> statisticsVos = new ArrayList<>();
        if (GeneralTool.isEmpty(fkAreaCountryIds)) {
            fkAreaCountryIds.add(0L);
        }
        HashSet set = new HashSet(fkAreaCountryIds);
        Result<Map<Long, String>> result = institutionCenterClient.getCountryChnNameByIds(set);
        Map<Long, String> fkAreaCountryNameMap = result.getData();
        for (Long fkAreaCountryId : fkAreaCountryIds) {
            List<ApplicationStatisticsDto> applicationStatisticsDtos = new ArrayList<>();
            StudentApplicationStatisticsVo oneYearDto = oneYearMonthStatisticsDtoMap.get(fkAreaCountryId);
            StudentApplicationStatisticsVo twoYearDto = null;
            if (GeneralTool.isNotEmpty(twoYearMonthStatisticsDtoMap)) {
                twoYearDto = twoYearMonthStatisticsDtoMap.get(fkAreaCountryId);
            }
            StudentApplicationStatisticsVo oneYearCumulativeDto = null;
            StudentApplicationStatisticsVo twoYearCumulativeDto = null;
            if (GeneralTool.isNotEmpty(oneYearCumulativeStatisticsDtoMap)) {
                oneYearCumulativeDto = oneYearCumulativeStatisticsDtoMap.get(fkAreaCountryId);
            }
            if (GeneralTool.isNotEmpty(twoYearCumulativeStatisticsDtoMap)) {
                twoYearCumulativeDto = twoYearCumulativeStatisticsDtoMap.get(fkAreaCountryId);
            }

            //新建学生
            ApplicationStatisticsDto createStatisticsDto = new ApplicationStatisticsDto();
            createStatisticsDto.setTitle("新建学生");
            createStatisticsDto.setStatisticalType(ProjectKeyEnum.CREATE_COUNT.key);
            if(queryType.equals(3)){
                createStatisticsDto.setJumpState(2);
            }else{
                createStatisticsDto.setJumpState(1);
            }
            //处理申请（含加申）
            ApplicationStatisticsDto applicationStatisticsDto = new ApplicationStatisticsDto();
            applicationStatisticsDto.setTitle("处理申请（含加申）");
            applicationStatisticsDto.setStatisticalType(ProjectKeyEnum.APPLICATION_COUNT.key);
            if(queryType.equals(3)){
                applicationStatisticsDto.setJumpState(2);
            }else{
                applicationStatisticsDto.setJumpState(1);
            }
            //定校量（按学校）
            ApplicationStatisticsDto confirmationStatisticsDto = new ApplicationStatisticsDto();
            confirmationStatisticsDto.setTitle("定校量（按学校）");
            confirmationStatisticsDto.setStatisticalType(ProjectKeyEnum.CONFIRMATION_COUNT.key);
            if(queryType.equals(3)){
                confirmationStatisticsDto.setJumpState(2);
            }else{
                confirmationStatisticsDto.setJumpState(1);
            }
            //成功入学量（按学校）
            ApplicationStatisticsDto successStatisticsDto = new ApplicationStatisticsDto();
            successStatisticsDto.setTitle("成功入学量（按学校）");
            successStatisticsDto.setStatisticalType(ProjectKeyEnum.SUCCESS_COUNT.key);
            if(queryType.equals(3)){
                successStatisticsDto.setJumpState(2);
            }else{
                successStatisticsDto.setJumpState(1);
            }
            //定校量（按学生）
            ApplicationStatisticsDto confirmationStatisticsByStudentDto = new ApplicationStatisticsDto();
            confirmationStatisticsByStudentDto.setTitle("定校量（按学生）");
            confirmationStatisticsByStudentDto.setStatisticalType(ProjectKeyEnum.CONFIRMATION_BY_STUDENT_COUNT.key);
            if(queryType.equals(3)){
                confirmationStatisticsByStudentDto.setJumpState(5);
            }else{
                confirmationStatisticsByStudentDto.setJumpState(3);
            }
            //成功入学量（按学生）
            ApplicationStatisticsDto successStatisticsByStudentDto = new ApplicationStatisticsDto();
            successStatisticsByStudentDto.setTitle("成功入学量（按学生）");
            successStatisticsByStudentDto.setStatisticalType(ProjectKeyEnum.SUCCESS_BY_STUDENT_COUNT.key);
            if(queryType.equals(3)){
                successStatisticsByStudentDto.setJumpState(5);
            }else{
                successStatisticsByStudentDto.setJumpState(3);
            }


            //结果1
            BigDecimal oneYearCreateCount = new BigDecimal(0);
            BigDecimal oneYearApplicationCount = new BigDecimal(0);
            BigDecimal oneYearConfirmationCount = new BigDecimal(0);
            BigDecimal oneYearSuccessCount = new BigDecimal(0);
            BigDecimal oneYearConfirmationCountByStudent = new BigDecimal(0);
            BigDecimal oneYearSuccessCountByStudent = new BigDecimal(0);
            if (GeneralTool.isNotEmpty(oneYearDto)) {
                if(GeneralTool.isNotEmpty(oneYearDto.getCreateCount())){
                    oneYearCreateCount = oneYearDto.getCreateCount();
                }

                if (GeneralTool.isNotEmpty(oneYearDto.getApplicationCount())) {
                    oneYearApplicationCount = oneYearDto.getApplicationCount();
                }

                if (GeneralTool.isNotEmpty(oneYearDto.getConfirmationCount())) {
                    oneYearConfirmationCount = oneYearDto.getConfirmationCount();
                }

                if (GeneralTool.isNotEmpty(oneYearDto.getSuccessCount())) {
                    oneYearSuccessCount = oneYearDto.getSuccessCount();
                }

                if(GeneralTool.isNotEmpty(oneYearDto.getConfirmationCountByStudent())){
                    oneYearConfirmationCountByStudent = oneYearDto.getConfirmationCountByStudent();
                }

                if(GeneralTool.isNotEmpty(oneYearDto.getSuccessCountByStudent())){
                    oneYearSuccessCountByStudent = oneYearDto.getSuccessCountByStudent();
                }
                createStatisticsDto.setResultOne(oneYearCreateCount);
                applicationStatisticsDto.setResultOne(oneYearApplicationCount);
                confirmationStatisticsDto.setResultOne(oneYearConfirmationCount);
                successStatisticsDto.setResultOne(oneYearSuccessCount);
                confirmationStatisticsByStudentDto.setResultOne(oneYearConfirmationCountByStudent);
                successStatisticsByStudentDto.setResultOne(oneYearSuccessCountByStudent);
            }


            //结果2
            BigDecimal twoYearCreateCount = new BigDecimal(0);
            BigDecimal twoYearApplicationCount = new BigDecimal(0);
            BigDecimal twoYearConfirmationCount = new BigDecimal(0);
            BigDecimal twoYearSuccessCount = new BigDecimal(0);
            BigDecimal twoYearConfirmationCountByStudent = new BigDecimal(0);
            BigDecimal twoYearSuccessCountByStudent = new BigDecimal(0);
            if (GeneralTool.isNotEmpty(twoYearDto)) {
                //赋值
                if(GeneralTool.isNotEmpty(twoYearDto.getCreateCount())){
                    twoYearCreateCount = twoYearDto.getCreateCount();
                }

                if (GeneralTool.isNotEmpty(twoYearDto.getApplicationCount())) {
                    twoYearApplicationCount = twoYearDto.getApplicationCount();
                }

                if (GeneralTool.isNotEmpty(twoYearDto.getConfirmationCount())) {
                    twoYearConfirmationCount = twoYearDto.getConfirmationCount();
                }

                if (GeneralTool.isNotEmpty(twoYearDto.getSuccessCount())) {
                    twoYearSuccessCount = twoYearDto.getSuccessCount();
                }

                if(GeneralTool.isNotEmpty(twoYearDto.getConfirmationCountByStudent())){
                    twoYearConfirmationCountByStudent = twoYearDto.getConfirmationCountByStudent();
                }

                if(GeneralTool.isNotEmpty(twoYearDto.getSuccessCountByStudent())){
                    twoYearSuccessCountByStudent = twoYearDto.getSuccessCountByStudent();
                }
                createStatisticsDto.setResultTwo(twoYearCreateCount);
                applicationStatisticsDto.setResultTwo(twoYearApplicationCount);
                confirmationStatisticsDto.setResultTwo(twoYearConfirmationCount);
                successStatisticsDto.setResultTwo(twoYearSuccessCount);
                confirmationStatisticsByStudentDto.setResultTwo(twoYearConfirmationCountByStudent);
                successStatisticsByStudentDto.setResultTwo(twoYearSuccessCountByStudent);

            }

            //================结果1与结果2对比================
            //变化比例
            createStatisticsDto.setChangeRatio(getChangeRatio(oneYearCreateCount,twoYearCreateCount));
            applicationStatisticsDto.setChangeRatio(getChangeRatio(oneYearApplicationCount, twoYearApplicationCount));
            confirmationStatisticsDto.setChangeRatio(getChangeRatio(oneYearConfirmationCount, twoYearConfirmationCount));
            successStatisticsDto.setChangeRatio(getChangeRatio(oneYearSuccessCount, twoYearSuccessCount));
            confirmationStatisticsByStudentDto.setChangeRatio(getChangeRatio(oneYearConfirmationCountByStudent,twoYearConfirmationCountByStudent));
            successStatisticsByStudentDto.setChangeRatio(getChangeRatio(oneYearSuccessCountByStudent,twoYearSuccessCountByStudent));
            //变化状态
            createStatisticsDto.setChangeStatus(getChangeStatus(oneYearCreateCount,twoYearCreateCount));
            applicationStatisticsDto.setChangeStatus(getChangeStatus(oneYearApplicationCount, twoYearApplicationCount));
            confirmationStatisticsDto.setChangeStatus(getChangeStatus(oneYearConfirmationCount, twoYearConfirmationCount));
            successStatisticsDto.setChangeStatus(getChangeStatus(oneYearSuccessCount, twoYearSuccessCount));
            confirmationStatisticsByStudentDto.setChangeStatus(getChangeStatus(oneYearConfirmationCountByStudent,twoYearConfirmationCountByStudent));
            successStatisticsByStudentDto.setChangeStatus(getChangeStatus(oneYearSuccessCountByStudent,twoYearSuccessCountByStudent));
            //列表
            applicationStatisticsDtos.add(createStatisticsDto);
            applicationStatisticsDtos.add(applicationStatisticsDto);
            applicationStatisticsDtos.add(confirmationStatisticsDto);
            applicationStatisticsDtos.add(successStatisticsDto);
            applicationStatisticsDtos.add(confirmationStatisticsByStudentDto);
            applicationStatisticsDtos.add(successStatisticsByStudentDto);


            if (hasCumulative) {
                //年累计新建学生
                ApplicationStatisticsDto createCumulativeStatisticsDto = new ApplicationStatisticsDto();
                createCumulativeStatisticsDto.setTitle("年累计新建学生");
                createCumulativeStatisticsDto.setStatisticalType(ProjectKeyEnum.CREATE_COUNT.key);
                if(queryType.equals(3)){
                    createCumulativeStatisticsDto.setJumpState(2);
                }else{
                    createCumulativeStatisticsDto.setJumpState(1);
                }
                //年累计处理申请（含加申）
                ApplicationStatisticsDto applicationCumulativeStatisticsDto = new ApplicationStatisticsDto();
                applicationCumulativeStatisticsDto.setTitle("年累计处理申请（含加申）");
                applicationCumulativeStatisticsDto.setStatisticalType(ProjectKeyEnum.APPLICATION_COUNT.key);
                if(queryType.equals(3)){
                    applicationCumulativeStatisticsDto.setJumpState(2);
                }else{
                    applicationCumulativeStatisticsDto.setJumpState(1);
                }
                //年累计定校量（按学校）
                ApplicationStatisticsDto confirmationCumulativeStatisticsDto = new ApplicationStatisticsDto();
                confirmationCumulativeStatisticsDto.setTitle("年累计定校量（按学校） ");
                confirmationCumulativeStatisticsDto.setStatisticalType(ProjectKeyEnum.CONFIRMATION_COUNT.key);
                if(queryType.equals(3)){
                    confirmationCumulativeStatisticsDto.setJumpState(2);
                }else{
                    confirmationCumulativeStatisticsDto.setJumpState(1);
                }
                //年累计成功入学量（按学校）
                ApplicationStatisticsDto successCumulativeStatisticsDto = new ApplicationStatisticsDto();
                successCumulativeStatisticsDto.setTitle("年累计成功入学量（按学校）");
                successCumulativeStatisticsDto.setStatisticalType(ProjectKeyEnum.SUCCESS_COUNT.key);
                if(queryType.equals(3)){
                    successCumulativeStatisticsDto.setJumpState(2);
                }else{
                    successCumulativeStatisticsDto.setJumpState(1);
                }
                //年累计定校量（按学生）
                ApplicationStatisticsDto confirmationCumulativeStatisticsByStudentDto = new ApplicationStatisticsDto();
                confirmationCumulativeStatisticsByStudentDto.setTitle("年累计定校量（按学生）");
                confirmationCumulativeStatisticsByStudentDto.setStatisticalType(ProjectKeyEnum.CONFIRMATION_BY_STUDENT_COUNT.key);
                if(queryType.equals(3)){
                    confirmationCumulativeStatisticsByStudentDto.setJumpState(5);
                }else{
                    confirmationCumulativeStatisticsByStudentDto.setJumpState(3);
                }
                //年累计成功入学量（按学校）
                ApplicationStatisticsDto successCumulativeStatisticsByStudentDto = new ApplicationStatisticsDto();
                successCumulativeStatisticsByStudentDto.setTitle("年累计成功入学量（按学生）");
                successCumulativeStatisticsByStudentDto.setStatisticalType(ProjectKeyEnum.SUCCESS_BY_STUDENT_COUNT.key);
                if(queryType.equals(3)){
                    successCumulativeStatisticsByStudentDto.setJumpState(5);
                }else{
                    successCumulativeStatisticsByStudentDto.setJumpState(3);
                }

                //累计结果1
                BigDecimal oneYearCumulativeCreateCount = new BigDecimal(0);
                BigDecimal oneYearCumulativeApplicationCount = new BigDecimal(0);
                BigDecimal oneYearCumulativeConfirmationCount = new BigDecimal(0);
                BigDecimal oneYearCumulativeSuccessCount = new BigDecimal(0);
                BigDecimal oneYearCumulativeConfirmationCountByStudent = new BigDecimal(0);
                BigDecimal oneYearCumulativeSuccessCountByStudent = new BigDecimal(0);

                if (GeneralTool.isNotEmpty(oneYearCumulativeDto)) {
                    if(GeneralTool.isNotEmpty(oneYearCumulativeDto.getCreateCount())){
                        oneYearCumulativeCreateCount = oneYearCumulativeDto.getCreateCount();
                    }

                    if (GeneralTool.isNotEmpty(oneYearCumulativeDto.getApplicationCount())) {
                        oneYearCumulativeApplicationCount = oneYearCumulativeDto.getApplicationCount();
                    }

                    if (GeneralTool.isNotEmpty(oneYearCumulativeDto.getConfirmationCount())) {
                        oneYearCumulativeConfirmationCount = oneYearCumulativeDto.getConfirmationCount();
                    }

                    if (GeneralTool.isNotEmpty(oneYearCumulativeDto.getSuccessCount())) {
                        oneYearCumulativeSuccessCount = oneYearCumulativeDto.getSuccessCount();
                    }

                    if(GeneralTool.isNotEmpty(oneYearCumulativeDto.getConfirmationCountByStudent())){
                        oneYearCumulativeConfirmationCountByStudent = oneYearCumulativeDto.getConfirmationCountByStudent();
                    }

                    if(GeneralTool.isNotEmpty(oneYearCumulativeDto.getSuccessCountByStudent())){
                        oneYearCumulativeSuccessCountByStudent = oneYearCumulativeDto.getSuccessCountByStudent();
                    }
                    createCumulativeStatisticsDto.setResultOne(oneYearCumulativeCreateCount);
                    applicationCumulativeStatisticsDto.setResultOne(oneYearCumulativeApplicationCount);
                    confirmationCumulativeStatisticsDto.setResultOne(oneYearCumulativeConfirmationCount);
                    successCumulativeStatisticsDto.setResultOne(oneYearCumulativeSuccessCount);
                    confirmationCumulativeStatisticsByStudentDto.setResultOne(oneYearCumulativeConfirmationCountByStudent);
                    successCumulativeStatisticsByStudentDto.setResultOne(oneYearCumulativeSuccessCountByStudent);
                }

                //累计结果2
                BigDecimal twoYearCumulativeCreateCount = new BigDecimal(0);
                BigDecimal twoYearCumulativeApplicationCount = new BigDecimal(0);
                BigDecimal twoYearCumulativeConfirmationCount = new BigDecimal(0);
                BigDecimal twoYearCumulativeSuccessCount = new BigDecimal(0);
                BigDecimal twoYearCumulativeConfirmationCountByStudent = new BigDecimal(0);
                BigDecimal twoYearCumulativeSuccessCountByStudent = new BigDecimal(0);
                if (GeneralTool.isNotEmpty(twoYearCumulativeDto)) {
                    if(GeneralTool.isNotEmpty(twoYearCumulativeDto.getCreateCount())){
                        twoYearCumulativeCreateCount = twoYearCumulativeDto.getCreateCount();
                    }

                    if (GeneralTool.isNotEmpty(twoYearCumulativeDto.getApplicationCount())) {
                        twoYearCumulativeApplicationCount = twoYearCumulativeDto.getApplicationCount();
                    }

                    if (GeneralTool.isNotEmpty(twoYearCumulativeDto.getConfirmationCount())) {
                        twoYearCumulativeConfirmationCount = twoYearCumulativeDto.getConfirmationCount();
                    }

                    if (GeneralTool.isNotEmpty(twoYearCumulativeDto.getSuccessCount())) {
                        twoYearCumulativeSuccessCount = twoYearCumulativeDto.getSuccessCount();
                    }

                    if(GeneralTool.isNotEmpty(twoYearCumulativeDto.getConfirmationCountByStudent())){
                        twoYearCumulativeConfirmationCountByStudent = twoYearCumulativeDto.getConfirmationCountByStudent();
                    }

                    if(GeneralTool.isNotEmpty(twoYearCumulativeDto.getSuccessCountByStudent())){
                        twoYearCumulativeSuccessCountByStudent = twoYearCumulativeDto.getSuccessCountByStudent();
                    }
                    createCumulativeStatisticsDto.setResultTwo(twoYearCumulativeCreateCount);
                    applicationCumulativeStatisticsDto.setResultTwo(twoYearCumulativeApplicationCount);
                    confirmationCumulativeStatisticsDto.setResultTwo(twoYearCumulativeConfirmationCount);
                    successCumulativeStatisticsDto.setResultTwo(twoYearCumulativeSuccessCount);
                    confirmationCumulativeStatisticsByStudentDto.setResultTwo(twoYearCumulativeConfirmationCountByStudent);
                    successCumulativeStatisticsByStudentDto.setResultTwo(twoYearCumulativeSuccessCountByStudent);
                }
                //================累计结果1与累计结果2对比================
                //累计变化比例
                createCumulativeStatisticsDto.setChangeRatio(getChangeRatio(oneYearCumulativeCreateCount,twoYearCumulativeCreateCount));
                applicationCumulativeStatisticsDto.setChangeRatio(getChangeRatio(oneYearCumulativeApplicationCount, twoYearCumulativeApplicationCount));
                confirmationCumulativeStatisticsDto.setChangeRatio(getChangeRatio(oneYearCumulativeConfirmationCount, twoYearCumulativeConfirmationCount));
                successCumulativeStatisticsDto.setChangeRatio(getChangeRatio(oneYearCumulativeSuccessCount, twoYearCumulativeSuccessCount));
                confirmationCumulativeStatisticsByStudentDto.setChangeRatio(getChangeRatio(oneYearCumulativeConfirmationCountByStudent,twoYearCumulativeConfirmationCountByStudent));
                successCumulativeStatisticsByStudentDto.setChangeRatio(getChangeRatio(oneYearCumulativeSuccessCountByStudent,twoYearCumulativeSuccessCountByStudent));

                //累计变化状态
                createCumulativeStatisticsDto.setChangeStatus(getChangeStatus(oneYearCumulativeCreateCount,twoYearCumulativeCreateCount));
                applicationCumulativeStatisticsDto.setChangeStatus(getChangeStatus(oneYearCumulativeApplicationCount, twoYearCumulativeApplicationCount));
                confirmationCumulativeStatisticsDto.setChangeStatus(getChangeStatus(oneYearCumulativeConfirmationCount, twoYearCumulativeConfirmationCount));
                successCumulativeStatisticsDto.setChangeStatus(getChangeStatus(oneYearCumulativeSuccessCount, twoYearCumulativeSuccessCount));
                confirmationCumulativeStatisticsByStudentDto.setChangeStatus(getChangeStatus(oneYearCumulativeConfirmationCountByStudent,twoYearCumulativeConfirmationCountByStudent));
                successCumulativeStatisticsByStudentDto.setChangeStatus(getChangeStatus(oneYearCumulativeSuccessCountByStudent,twoYearCumulativeSuccessCountByStudent));
                //列表
                applicationStatisticsDtos.add(createCumulativeStatisticsDto);
                applicationStatisticsDtos.add(applicationCumulativeStatisticsDto);
                applicationStatisticsDtos.add(confirmationCumulativeStatisticsDto);
                applicationStatisticsDtos.add(successCumulativeStatisticsDto);
                applicationStatisticsDtos.add(confirmationCumulativeStatisticsByStudentDto);
                applicationStatisticsDtos.add(successCumulativeStatisticsByStudentDto);
            }

            //结果列表对象赋值
            StatisticsVo statisticsVo = new StatisticsVo();
            statisticsVo.setFkAreaCountryId(fkAreaCountryId);
            statisticsVo.setFkAreaCountryName(fkAreaCountryNameMap.get(fkAreaCountryId));
            statisticsVo.setDateStartOne(dateStartOne);
            statisticsVo.setDateEndOne(dateEndOne);
            statisticsVo.setDateStartTwo(dateStartTwo);
            statisticsVo.setDateEndTwo(dateEndTwo);
            //年累计时间
            statisticsVo.setCumulativeDateStartOne(cumulativeDateStartOne);
            statisticsVo.setCumulativeDateEndOne(cumulativeDateEndOne);
            statisticsVo.setCumulativeDateStartTwo(cumulativeDateStartTwo);
            statisticsVo.setCumulativeDateEndTwo(cumulativeDateEndTwo);
            statisticsVo.setApplicationStatisticsDtoList(applicationStatisticsDtos);

            statisticsVos.add(statisticsVo);
        }
        return statisticsVos;
    }

    /**
     * 构造小计对象
     * @param title 标题-例如：“合计”、“总计”
     * @param dateStartOne 开始时间
     * @param dateEndOne   结束时间
     * @param dateStartTwo 开始时间（副）
     * @param dateEndTwo   开始时间（副）
     * @param cumulativeDateStartOne 累计开始时间
     * @param cumulativeDateEndOne   累计结束时间
     * @param cumulativeDateStartTwo  累计开始时间（副）
     * @param cumulativeDateEndTwo    累计开始时间（副）
     * @param hasCumulative  是否存在累计
     * @param data1   对象1
     * @param data2   对象2
     * @param data3   累计对象1
     * @param data4   累计对象2
     * @return
     */
    @Override
    public StatisticsVo constructSubtotalResout(String title,
                                                Date dateStartOne,
                                                Date dateEndOne,
                                                Date dateStartTwo,
                                                Date dateEndTwo,
                                                Date cumulativeDateStartOne,
                                                Date cumulativeDateEndOne,
                                                Date cumulativeDateStartTwo,
                                                Date cumulativeDateEndTwo,
                                                Boolean hasCumulative,
                                                Integer queryType,
                                                List<StudentApplicationStatisticsVo> data1,
                                                List<StudentApplicationStatisticsVo> data2,
                                                List<StudentApplicationStatisticsVo> data3,
                                                List<StudentApplicationStatisticsVo> data4
    ) {
        List<ApplicationStatisticsDto> applicationStatisticsDtos = new ArrayList<>();
        BigDecimal createCountTotalOne = new BigDecimal(0);
        BigDecimal applicationCountTotalOne = new BigDecimal(0);
        BigDecimal confirmationTotalOne = new BigDecimal(0);
        BigDecimal successCountTotalOne = new BigDecimal(0);
        BigDecimal confirmationTotalOneByStudent = new BigDecimal(0);
        BigDecimal successCountTotalOneByStudent = new BigDecimal(0);
        if(GeneralTool.isNotEmpty(data1)){
            createCountTotalOne = data1.stream().filter(s->s.getCreateCount() != null)
                    .map(StudentApplicationStatisticsVo::getCreateCount)
                    .reduce(BigDecimal.ZERO,BigDecimal::add);
            applicationCountTotalOne = data1.stream().filter(s -> s.getApplicationCount() != null)
                    .map(StudentApplicationStatisticsVo::getApplicationCount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            confirmationTotalOne = data1.stream().filter(s -> s.getConfirmationCount() != null)
                    .map(StudentApplicationStatisticsVo::getConfirmationCount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            successCountTotalOne = data1.stream().filter(s -> s.getSuccessCount() != null)
                    .map(StudentApplicationStatisticsVo::getSuccessCount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            confirmationTotalOneByStudent = data1.stream().filter(s->s.getConfirmationCountByStudent() != null)
                    .map(StudentApplicationStatisticsVo::getConfirmationCountByStudent)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            successCountTotalOneByStudent = data1.stream().filter(s->s.getSuccessCountByStudent() != null)
                    .map(StudentApplicationStatisticsVo::getSuccessCountByStudent)
                    .reduce(BigDecimal.ZERO,BigDecimal::add);
        }


        BigDecimal createCountTotalTwo = new BigDecimal(0);
        BigDecimal applicationCountTotalTwo = new BigDecimal(0);
        BigDecimal confirmationTotalTwo = new BigDecimal(0);
        BigDecimal successCountTotalTwo = new BigDecimal(0);
        BigDecimal confirmationTotalTwoByStudent = new BigDecimal(0);
        BigDecimal successCountTotalTwoByStudent = new BigDecimal(0);
        if(GeneralTool.isNotEmpty(data2)){
             createCountTotalTwo = data2.stream().filter(s->s.getCreateCount() != null)
                    .map(StudentApplicationStatisticsVo::getCreateCount)
                    .reduce(BigDecimal.ZERO,BigDecimal::add);
             applicationCountTotalTwo = data2.stream().filter(s -> s.getApplicationCount() != null)
                    .map(StudentApplicationStatisticsVo::getApplicationCount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
             confirmationTotalTwo = data2.stream().filter(s -> s.getConfirmationCount() != null)
                    .map(StudentApplicationStatisticsVo::getConfirmationCount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
             successCountTotalTwo = data2.stream().filter(s -> s.getSuccessCount() != null)
                    .map(StudentApplicationStatisticsVo::getSuccessCount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            confirmationTotalTwoByStudent = data2.stream().filter(s->s.getConfirmationCountByStudent() != null)
                    .map(StudentApplicationStatisticsVo::getConfirmationCountByStudent)
                    .reduce(BigDecimal.ZERO,BigDecimal::add);
            successCountTotalTwoByStudent = data2.stream().filter(s->s.getSuccessCountByStudent() != null)
                    .map(StudentApplicationStatisticsVo::getSuccessCountByStudent)
                    .reduce(BigDecimal.ZERO,BigDecimal::add);
        }


        //新建学生
        ApplicationStatisticsDto createStatisticsDto = new ApplicationStatisticsDto();
        createStatisticsDto.setTitle("新建学生");
        createStatisticsDto.setStatisticalType(ProjectKeyEnum.CREATE_COUNT.key);
        if(queryType.equals(4)){
            createStatisticsDto.setJumpState(2);
        }else{
            createStatisticsDto.setJumpState(1);
        }
        createStatisticsDto.setResultOne(createCountTotalOne);
        createStatisticsDto.setResultTwo(createCountTotalTwo);
        //处理申请（含加申）
        ApplicationStatisticsDto applicationStatisticsDto = new ApplicationStatisticsDto();
        applicationStatisticsDto.setTitle("处理申请（含加申）");
        applicationStatisticsDto.setStatisticalType(ProjectKeyEnum.APPLICATION_COUNT.key);
        if(queryType.equals(4)){
            applicationStatisticsDto.setJumpState(2);
        }else{
            applicationStatisticsDto.setJumpState(1);
        }
        applicationStatisticsDto.setResultOne(applicationCountTotalOne);
        applicationStatisticsDto.setResultTwo(applicationCountTotalTwo);
        //定校量（按学校）
        ApplicationStatisticsDto confirmationStatisticsDto = new ApplicationStatisticsDto();
        confirmationStatisticsDto.setTitle("定校量（按学校） ");
        confirmationStatisticsDto.setStatisticalType(ProjectKeyEnum.CONFIRMATION_COUNT.key);
        if(queryType.equals(4)){
            confirmationStatisticsDto.setJumpState(2);
        }else{
            confirmationStatisticsDto.setJumpState(1);
        }
        confirmationStatisticsDto.setResultOne(confirmationTotalOne);
        confirmationStatisticsDto.setResultTwo(confirmationTotalTwo);
        //成功入学量（按学校）
        ApplicationStatisticsDto successStatisticsDto = new ApplicationStatisticsDto();
        successStatisticsDto.setTitle("成功入学量（按学校）");
        successStatisticsDto.setStatisticalType(ProjectKeyEnum.SUCCESS_COUNT.key);
        if(queryType.equals(4)){
            successStatisticsDto.setJumpState(2);
        }else{
            successStatisticsDto.setJumpState(1);
        }
        successStatisticsDto.setResultOne(successCountTotalOne);
        successStatisticsDto.setResultTwo(successCountTotalTwo);
        //定校量(按学生)
        ApplicationStatisticsDto confirmationStatisticsByStudentDto = new ApplicationStatisticsDto();
        confirmationStatisticsByStudentDto.setTitle("定校量（按学生）");
        confirmationStatisticsByStudentDto.setStatisticalType(ProjectKeyEnum.CONFIRMATION_BY_STUDENT_COUNT.key);
        if(queryType.equals(4)){
            confirmationStatisticsByStudentDto.setJumpState(7);
        } else {
            confirmationStatisticsByStudentDto.setJumpState(6);
        }
        confirmationStatisticsByStudentDto.setResultOne(confirmationTotalOneByStudent);
        confirmationStatisticsByStudentDto.setResultTwo(confirmationTotalTwoByStudent);
        //成功入学量（按学生）
        ApplicationStatisticsDto successStatisticsByStudentDto = new ApplicationStatisticsDto();
        successStatisticsByStudentDto.setTitle("成功入学量（按学生）");
        successStatisticsByStudentDto.setStatisticalType(ProjectKeyEnum.SUCCESS_BY_STUDENT_COUNT.key);
        if(queryType.equals(4)){
            successStatisticsByStudentDto.setJumpState(7);
        } else {
            successStatisticsByStudentDto.setJumpState(6);
        }
        successStatisticsByStudentDto.setResultOne(successCountTotalOneByStudent);
        successStatisticsByStudentDto.setResultTwo(successCountTotalTwoByStudent);

        //变化比例
        createStatisticsDto.setChangeRatio(getChangeRatio(createCountTotalOne,createCountTotalTwo));
        applicationStatisticsDto.setChangeRatio(getChangeRatio(applicationCountTotalOne, applicationCountTotalTwo));
        confirmationStatisticsDto.setChangeRatio(getChangeRatio(confirmationTotalOne, confirmationTotalTwo));
        successStatisticsDto.setChangeRatio(getChangeRatio(successCountTotalOne, successCountTotalTwo));
        confirmationStatisticsByStudentDto.setChangeRatio(getChangeRatio(confirmationTotalOneByStudent,confirmationTotalTwoByStudent));
        successStatisticsByStudentDto.setChangeRatio(getChangeRatio(successCountTotalOneByStudent,successCountTotalTwoByStudent));

        //变化状态
        createStatisticsDto.setChangeStatus(getChangeStatus(createCountTotalOne,createCountTotalTwo));
        applicationStatisticsDto.setChangeStatus(getChangeStatus(applicationCountTotalOne, applicationCountTotalTwo));
        confirmationStatisticsDto.setChangeStatus(getChangeStatus(confirmationTotalOne, confirmationTotalTwo));
        successStatisticsDto.setChangeStatus(getChangeStatus(successCountTotalOne, successCountTotalTwo));
        confirmationStatisticsByStudentDto.setChangeStatus(getChangeStatus(confirmationTotalOneByStudent,confirmationTotalTwoByStudent));
        successStatisticsByStudentDto.setChangeStatus(getChangeStatus(successCountTotalOneByStudent,successCountTotalTwoByStudent));

        //列表
        applicationStatisticsDtos.add(createStatisticsDto);
        applicationStatisticsDtos.add(applicationStatisticsDto);
        applicationStatisticsDtos.add(confirmationStatisticsDto);
        applicationStatisticsDtos.add(successStatisticsDto);
        applicationStatisticsDtos.add(confirmationStatisticsByStudentDto);
        applicationStatisticsDtos.add(successStatisticsByStudentDto);



        //年累计
        if (hasCumulative) {
            BigDecimal createCumulativeTotalOne = data3.stream().filter(s -> s.getCreateCount() != null)
                    .map(StudentApplicationStatisticsVo::getCreateCount)
                    .reduce(BigDecimal.ZERO,BigDecimal::add);
            BigDecimal applicationCumulativeTotalOne = data3.stream().filter(s -> s.getApplicationCount() != null)
                    .map(StudentApplicationStatisticsVo::getApplicationCount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal confirmationCumulativeTotalOne = data3.stream().filter(s -> s.getConfirmationCount() != null)
                    .map(StudentApplicationStatisticsVo::getConfirmationCount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal successCumulativeTotalOne = data3.stream().filter(s -> s.getSuccessCount() != null)
                    .map(StudentApplicationStatisticsVo::getSuccessCount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal confirmationCumulativeTotalOneByStudent = data3.stream().filter(s -> s.getConfirmationCountByStudent() != null)
                    .map(StudentApplicationStatisticsVo::getConfirmationCountByStudent)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal successCumulativeTotalOneByStudent = data3.stream().filter(s -> s.getSuccessCountByStudent() != null)
                    .map(StudentApplicationStatisticsVo::getSuccessCountByStudent)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);


            BigDecimal createCumulativeTotalTwo = data4.stream().filter(s -> s.getCreateCount() != null)
                    .map(StudentApplicationStatisticsVo::getCreateCount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal applicationCumulativeTotalTwo = data4.stream().filter(s -> s.getApplicationCount() != null)
                    .map(StudentApplicationStatisticsVo::getApplicationCount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal confirmationCumulativeTotalTwo = data4.stream().filter(s -> s.getConfirmationCount() != null)
                    .map(StudentApplicationStatisticsVo::getConfirmationCount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal successCumulativeTotalTwo = data4.stream().filter(s -> s.getSuccessCount() != null)
                    .map(StudentApplicationStatisticsVo::getSuccessCount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal confirmationCumulativeTotalTwoByStudent = data4.stream().filter(s -> s.getConfirmationCountByStudent() != null)
                    .map(StudentApplicationStatisticsVo::getConfirmationCountByStudent)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal successCumulativeTotalTwoByStudent = data4.stream().filter(s -> s.getSuccessCountByStudent() != null)
                    .map(StudentApplicationStatisticsVo::getSuccessCountByStudent)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);


            //年累计新建学生
            ApplicationStatisticsDto createCumulativeStatisticsDto = new ApplicationStatisticsDto();
            createCumulativeStatisticsDto.setTitle("年累计新建学生");
            createCumulativeStatisticsDto.setStatisticalType(ProjectKeyEnum.CREATE_COUNT.key);
            if(queryType.equals(4)){
                createCumulativeStatisticsDto.setJumpState(2);
            }else{
                createCumulativeStatisticsDto.setJumpState(1);
            }
            createCumulativeStatisticsDto.setResultOne(createCumulativeTotalOne);
            createCumulativeStatisticsDto.setResultTwo(createCumulativeTotalTwo);
            //年累计处理申请（含加申）
            ApplicationStatisticsDto applicationCumulativeStatisticsDto = new ApplicationStatisticsDto();
            applicationCumulativeStatisticsDto.setTitle("年累计处理申请（含加申）");
            applicationCumulativeStatisticsDto.setStatisticalType(ProjectKeyEnum.APPLICATION_COUNT.key);
            if(queryType.equals(4)){
                applicationCumulativeStatisticsDto.setJumpState(2);
            }else{
                applicationCumulativeStatisticsDto.setJumpState(1);
            }
            applicationCumulativeStatisticsDto.setResultOne(applicationCumulativeTotalOne);
            applicationCumulativeStatisticsDto.setResultTwo(applicationCumulativeTotalTwo);
            //年累计定校量（按学校）
            ApplicationStatisticsDto confirmationCumulativeStatisticsDto = new ApplicationStatisticsDto();
            confirmationCumulativeStatisticsDto.setTitle("年累计定校量（按学校）");
            confirmationCumulativeStatisticsDto.setStatisticalType(ProjectKeyEnum.CONFIRMATION_COUNT.key);
            if(queryType.equals(4)){
                confirmationCumulativeStatisticsDto.setJumpState(2);
            }else{
                confirmationCumulativeStatisticsDto.setJumpState(1);
            }
            confirmationCumulativeStatisticsDto.setResultOne(confirmationCumulativeTotalOne);
            confirmationCumulativeStatisticsDto.setResultTwo(confirmationCumulativeTotalTwo);
            //年累计成功入学量（按学校）
            ApplicationStatisticsDto successCumulativeStatisticsDto = new ApplicationStatisticsDto();
            successCumulativeStatisticsDto.setTitle("年累计成功入学量（按学校）");
            successCumulativeStatisticsDto.setStatisticalType(ProjectKeyEnum.SUCCESS_COUNT.key);
            if(queryType.equals(4)){
                successCumulativeStatisticsDto.setJumpState(2);
            }else{
                successCumulativeStatisticsDto.setJumpState(1);
            }
            successCumulativeStatisticsDto.setResultOne(successCumulativeTotalOne);
            successCumulativeStatisticsDto.setResultTwo(successCumulativeTotalTwo);
            //年累计定校量（按学生）
            ApplicationStatisticsDto confirmationCumulativeStatisticsByStudentDto = new ApplicationStatisticsDto();
            confirmationCumulativeStatisticsByStudentDto.setTitle("年累计定校量（按学生）");
            confirmationCumulativeStatisticsByStudentDto.setStatisticalType(ProjectKeyEnum.CONFIRMATION_BY_STUDENT_COUNT.key);
            if(queryType.equals(4)){
                confirmationCumulativeStatisticsByStudentDto.setJumpState(7);
            } else {
                confirmationCumulativeStatisticsByStudentDto.setJumpState(6);
            }
            confirmationCumulativeStatisticsByStudentDto.setResultOne(confirmationCumulativeTotalOneByStudent);
            confirmationCumulativeStatisticsByStudentDto.setResultTwo(confirmationCumulativeTotalTwoByStudent);
            //年累计成功入学量（按学生）
            ApplicationStatisticsDto successCumulativeStatisticsByStudentDto = new ApplicationStatisticsDto();
            successCumulativeStatisticsByStudentDto.setTitle("年累计成功入学量（按学生）");
            successCumulativeStatisticsByStudentDto.setStatisticalType(ProjectKeyEnum.SUCCESS_BY_STUDENT_COUNT.key);
            if(queryType.equals(4)){
                successCumulativeStatisticsByStudentDto.setJumpState(7);
            } else {
                successCumulativeStatisticsByStudentDto.setJumpState(6);
            }
            successCumulativeStatisticsByStudentDto.setResultOne(successCumulativeTotalOneByStudent);
            successCumulativeStatisticsByStudentDto.setResultTwo(successCumulativeTotalTwoByStudent);

            //变化比例
            createCumulativeStatisticsDto.setChangeRatio(getChangeRatio(createCumulativeTotalOne,createCountTotalTwo));
            applicationCumulativeStatisticsDto.setChangeRatio(getChangeRatio(applicationCumulativeTotalOne, applicationCumulativeTotalTwo));
            confirmationCumulativeStatisticsDto.setChangeRatio(getChangeRatio(confirmationCumulativeTotalOne, confirmationCumulativeTotalTwo));
            successCumulativeStatisticsDto.setChangeRatio(getChangeRatio(successCumulativeTotalOne, successCumulativeTotalTwo));
            confirmationCumulativeStatisticsByStudentDto.setChangeRatio(getChangeRatio(confirmationCumulativeTotalOneByStudent,confirmationCumulativeTotalTwoByStudent));
            successCumulativeStatisticsByStudentDto.setChangeRatio(getChangeRatio(successCumulativeTotalOneByStudent, successCumulativeTotalTwoByStudent));

            //变化状态
            createCumulativeStatisticsDto.setChangeStatus(getChangeStatus(createCumulativeTotalOne,createCountTotalTwo));
            applicationCumulativeStatisticsDto.setChangeStatus(getChangeStatus(applicationCumulativeTotalOne, applicationCumulativeTotalTwo));
            confirmationCumulativeStatisticsDto.setChangeStatus(getChangeStatus(confirmationCumulativeTotalOne, confirmationCumulativeTotalTwo));
            successCumulativeStatisticsDto.setChangeStatus(getChangeStatus(successCumulativeTotalOne, successCumulativeTotalTwo));
            confirmationCumulativeStatisticsByStudentDto.setChangeStatus(getChangeStatus(confirmationCumulativeTotalOneByStudent, confirmationCumulativeTotalTwoByStudent));
            successCumulativeStatisticsByStudentDto.setChangeStatus(getChangeStatus(successCumulativeTotalOneByStudent, successCumulativeTotalTwoByStudent));

            //列表
            applicationStatisticsDtos.add(createCumulativeStatisticsDto);
            applicationStatisticsDtos.add(applicationCumulativeStatisticsDto);
            applicationStatisticsDtos.add(confirmationCumulativeStatisticsDto);
            applicationStatisticsDtos.add(successCumulativeStatisticsDto);
            applicationStatisticsDtos.add(confirmationCumulativeStatisticsByStudentDto);
            applicationStatisticsDtos.add(successCumulativeStatisticsByStudentDto);
        }

        //结果列表对象赋值
        StatisticsVo statisticsVo = new StatisticsVo();
        statisticsVo.setFkAreaCountryName(title);
        statisticsVo.setDateStartOne(dateStartOne);
        statisticsVo.setDateEndOne(dateEndOne);
        statisticsVo.setDateStartTwo(dateStartTwo);
        statisticsVo.setDateEndTwo(dateEndTwo);

        //年累计时间
        statisticsVo.setCumulativeDateStartOne(cumulativeDateStartOne);
        statisticsVo.setCumulativeDateEndOne(cumulativeDateEndOne);
        statisticsVo.setCumulativeDateStartTwo(cumulativeDateStartTwo);
        statisticsVo.setCumulativeDateEndTwo(cumulativeDateEndTwo);

        statisticsVo.setApplicationStatisticsDtoList(applicationStatisticsDtos);
        return statisticsVo;
    }

    /**
     * 变化状态，1升，-1降，0无变化
     *
     * @param count1
     * @param count2
     * @return
     */
    public int getChangeStatus(BigDecimal count1, BigDecimal count2) {
        if (count1.compareTo(count2) > 0) {
            return -1;
        } else if (count1.compareTo(count2) < 0) {
            return 1;
        } else {
            return 0;
        }
    }

    /**
     * 升降比例
     *
     * @param count1
     * @param count2
     * @return
     */
    public String getChangeRatio(BigDecimal count1, BigDecimal count2) {
        StringBuffer stringBuffer = new StringBuffer();
        if (count1.compareTo(BigDecimal.ZERO) == 0) {
            stringBuffer.append(count2);
            return stringBuffer.toString();
        }
        if (count2.compareTo(BigDecimal.ZERO) == 0) {
            stringBuffer.append(count1);
            return stringBuffer.toString();
        }
        BigDecimal successCumulativeChangeRatio = (count2.subtract(count1))
                .divide(count1, 4, BigDecimal.ROUND_HALF_UP)
                .multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        stringBuffer.append(successCumulativeChangeRatio).append("%");
        return stringBuffer.toString();
    }


}

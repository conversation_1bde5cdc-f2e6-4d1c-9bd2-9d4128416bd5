package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.service.GetService;
import com.get.salecenter.vo.CommentVo;
import com.get.salecenter.vo.MediaAndAttachedVo;
import com.get.salecenter.vo.StudentInsuranceVo;
import com.get.salecenter.entity.StudentInsurance;
import com.get.salecenter.dto.*;
import com.get.salecenter.dto.query.InsuranceSummaryQueryDto;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2022/1/12
 * @TIME: 14:53
 * @Description:
 **/
public interface IStudentInsuranceService extends GetService<StudentInsurance> {


    /**
     * @return java.lang.Long
     * @Description: 新增留学保险
     * @Param [studentAccommodationVo]
     * <AUTHOR>
     */
    void addStudentInsurance(StudentInsuranceDto studentInsuranceDto);

    /**
     * @return java.util.List<com.get.salecenter.vo.BusinessChannelVo>
     * @Description: 查询留学保险
     * @Param [businessChannelVo, page]
     * <AUTHOR>
     */
    List<StudentInsuranceVo> getStudentInsuranceDtos(StudentInsuranceDto studentInsuranceDto, Page page);


    /**
     * @return com.get.salecenter.vo.BusinessChannelVo
     * @Description: 修改
     * @Param [businessChannelVo]
     * <AUTHOR>
     */
    StudentInsuranceVo updateStudentInsurance(StudentInsuranceDto studentInsuranceDto);

    /**
     * @return void
     * @Description: 删除
     * @Param [id]
     * <AUTHOR>
     */
    void deleteStudentInsurance(Long id);

    /**
     * 查询byID
     *
     * @param id
     * @return
     * @
     */
    StudentInsuranceVo findStudentInsuranceById(Long id);

    /**
     * 关闭
     *
     * @param id
     * @return
     * @
     */
    void closeStudentInsuranceById(Long id);

    /**
     * 激活
     *
     * @param id
     * @return
     * @
     */
    void activationStudentInsuranceById(Long id);

    /**
     * 保存附件
     *
     * @param mediaAttachedVo
     * @return
     */
    List<MediaAndAttachedVo> addStudentInsuranceMedia(List<MediaAndAttachedDto> mediaAttachedVo);

    /**
     * 获取附件类型
     *
     * @return
     */
    List<Map<String, Object>> findMediaAndAttachedType();

    /**
     * 查询附件
     *
     * @param data
     * @param page
     * @return
     * @
     */
    List<MediaAndAttachedVo> getStudentInsuranceMedia(MediaAndAttachedDto data, Page page);

    /**
     * @return java.util.List<com.get.salecenter.vo.StudentAccommodationVo>
     * @Description: 获取留学保险列表数据 （学生详情获取）
     * @Param [studentId, keyWord]
     * <AUTHOR>
     */
    List<StudentInsuranceVo> getStudentInsuranceList(StudentInsuranceDto studentInsuranceDto, Page page);

    /**
     * @return java.lang.Long
     * @Description: 增加或者更新评论
     * @Param [commentDto]
     * <AUTHOR>
     **/
    Long editComment(CommentDto commentDto);

    /**
     * @return java.util.List<com.get.salecenter.vo.CommentVo>
     * @Description: 获取所有评论
     * @Param [commentDto, page]
     * <AUTHOR>
     */
    List<CommentVo> getComments(CommentDto commentDto, Page page);

    /**
     * @return java.util.List<com.get.salecenter.vo.StudentInsuranceVo>
     * @Description: 获取所有保险汇总
     * @Param [commentVo, page]
     * <AUTHOR>
     */
    List<StudentInsuranceVo> getStudentInsuranceSummary(InsuranceSummaryQueryDto studentInsurance, Page page, String[] times);

    /**
     * @return
     * @Description: 创建应收应付
     * @Param [accommodationSummaryVo]
     * <AUTHOR>
     */
    void createARAP(InsuranceSummaryDto insuranceSummaryDto);

    void createReceivablePlan(InsuranceSummaryReDto insuranceSummaryReDto);

    void createPayablePlan(InsuranceSummaryPayDto insuranceSummaryPayDto);

    void exportExcel(HttpServletResponse response, InsuranceSummaryQueryDto studentInsurance);

    /**
     *  获取留学保险代理id
     * @param targetId
     * @return
     */
    Long getInsuranceAgentId(Long targetId);

    /**
     * 获取留学保险id
     * @param targetId
     * @return
     */
    Long getStudentInsuranceId(Long targetId);

    /**
     * 根据id获取保险方案编号
     * @param insuranceIds
     * @return
     */
    Map<Long, String> getNumByIds(Set<Long> insuranceIds);

    /**
     * 设置无代理（无需结算）接口
     * @param id
     */
    void updateNoAgent(Long id);

    /**
     * 恢复代理接口
     * @param insuranceAgentUpdateDto
     */
    void recoveryAgent(InsuranceAgentUpdateDto insuranceAgentUpdateDto);

    /**
     * 合并留学保险数据
     * @param mergedStudentId
     * @param targetStudentId
     */
    void mergeData(Long mergedStudentId, Long targetStudentId);

    StudentInsurance getStudentInsuranceById(Long id);
}

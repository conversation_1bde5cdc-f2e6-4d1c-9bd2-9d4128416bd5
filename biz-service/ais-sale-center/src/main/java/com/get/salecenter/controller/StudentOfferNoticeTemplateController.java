package com.get.salecenter.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.get.core.log.annotation.OperationLogger;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import javax.annotation.Resource;

/**
 * <p>
 *  前端接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-13
 */

@Api(tags = "入读意向通知模板管理")
@RestController
@RequestMapping("sale/studentOfferNoticeTemplate")
public class StudentOfferNoticeTemplateController {

}

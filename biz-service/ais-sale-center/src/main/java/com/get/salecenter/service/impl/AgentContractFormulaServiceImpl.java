package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.api.ResultCode;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.remindercenter.feign.IReminderCenterClient;
import com.get.salecenter.dao.sale.AgentContractFormulaCommissionMapper;
import com.get.salecenter.dao.sale.AgentContractFormulaMapper;
import com.get.salecenter.dao.sale.AgentMapper;
import com.get.salecenter.vo.AgentContractFormulaVo;
import com.get.salecenter.vo.CompanyTreeVo;
import com.get.salecenter.entity.Agent;
import com.get.salecenter.entity.AgentContractFormula;
import com.get.salecenter.entity.AgentContractFormulaCommission;
import com.get.salecenter.service.IAgentCompanyService;
import com.get.salecenter.service.IAgentContractFormulaAreaCountryService;
import com.get.salecenter.service.IAgentContractFormulaCommissionService;
import com.get.salecenter.service.IAgentContractFormulaCompanyService;
import com.get.salecenter.service.IAgentContractFormulaCourseTypeService;
import com.get.salecenter.service.IAgentContractFormulaInstitutionCourseService;
import com.get.salecenter.service.IAgentContractFormulaInstitutionService;
import com.get.salecenter.service.IAgentContractFormulaMajorLevelService;
import com.get.salecenter.service.IAgentContractFormulaService;
import com.get.salecenter.dto.*;
import com.get.salecenter.dto.AgentContractFormulaAreaCountryDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * @author: Sea
 * @create: 2021/1/6 12:21
 * @verison: 1.0
 * @description:
 */
@Service
public class AgentContractFormulaServiceImpl implements IAgentContractFormulaService {
    @Resource
    private AgentContractFormulaMapper agentContractFormulaMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IAgentContractFormulaAreaCountryService agentContractFormulaAreaCountryService;
    @Resource
    private IAgentContractFormulaCourseTypeService agentContractFormulaCourseTypeService;
    @Resource
    private IAgentContractFormulaMajorLevelService agentContractFormulaMajorLevelService;
    @Resource
    private IAgentContractFormulaCompanyService agentContractFormulaCompanyService;
    @Resource
    private IReminderCenterClient reminderCenterClient;
    @Resource
    private AgentMapper agentMapper;
    @Resource
    private AgentContractFormulaCommissionMapper agentContractFormulaCommissionMapper;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private IAgentCompanyService agentCompanyService;
    @Resource
    private IAgentContractFormulaInstitutionService agentContractFormulaInstitutionService;
    @Resource
    private IAgentContractFormulaInstitutionCourseService agentContractFormulaInstitutionCourseService;
    @Resource
    private IAgentContractFormulaCommissionService agentContractFormulaCommissionService;

    @Override
    public AgentContractFormulaVo findAgentContractFormulaById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        AgentContractFormula agentContractFormula = agentContractFormulaMapper.selectById(id);
        if (GeneralTool.isEmpty(agentContractFormula)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        AgentContractFormulaVo agentContractFormulaVo = BeanCopyUtils.objClone(agentContractFormula, AgentContractFormulaVo::new);

        agentContractFormulaVo.setCountryIdList(agentContractFormulaAreaCountryService.getCountryIdListByFkid(id));
        agentContractFormulaVo.setCourseTypeIdList(agentContractFormulaCourseTypeService.getCourseTypeIdListByFkid(id));
        agentContractFormulaVo.setMajorLevelIdList(agentContractFormulaMajorLevelService.getMajorLevelIdListByFkid(id));
        agentContractFormulaVo.setCompanyIdList(agentContractFormulaCompanyService.getCompanyIdListByFkid(id));
        agentContractFormulaVo.setInstitutionIdList(agentContractFormulaInstitutionService.getInstitutionIdListByFkid(id));
        agentContractFormulaVo.setCourseIdList(agentContractFormulaInstitutionCourseService.getInstitutionCourseIdListByFkid(id));
        agentContractFormulaVo.setAgentContractFormulaCommissionDtos(agentContractFormulaCommissionService.getAgentContractFormulaCommissionByFkid(id));
        return agentContractFormulaVo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long addAgentContractFormula(AgentContractFormulaDto agentContractFormulaDto) {
        if (GeneralTool.isEmpty(agentContractFormulaDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        AgentContractFormula agentContractFormula = BeanCopyUtils.objClone(agentContractFormulaDto, AgentContractFormula::new);
        utilService.updateUserInfoToEntity(agentContractFormula);
        agentContractFormulaMapper.insertSelective(agentContractFormula);
        Long agentContractFormulaId = agentContractFormula.getId();
        //设置最大排序值
        agentContractFormula.setViewOrder(agentContractFormulaMapper.getMaxViewOrder(agentContractFormulaDto.getFkAgentId()));
        agentContractFormulaMapper.updateById(agentContractFormula);
        //同时插入中间表
        insertTable(agentContractFormulaDto, agentContractFormulaId);
        //默认为该代理安全配置的公司
        List<AgentContractFormulaCompanyDto> list = new ArrayList<>();
        List<Long> agentCompanyIds = agentCompanyService.getRelationByAgentId(agentContractFormulaDto.getFkAgentId());
        if (GeneralTool.isEmpty(agentCompanyIds)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("edit_companyId"));
        }
        for (Long companyId : agentCompanyIds) {
            AgentContractFormulaCompanyDto agentContractFormulaCompanyDto = new AgentContractFormulaCompanyDto();
            agentContractFormulaCompanyDto.setFkAgentContractFormulaId(agentContractFormulaId);
            agentContractFormulaCompanyDto.setFkCompanyId(companyId);
            agentContractFormulaCompanyDto.setAgentId(agentContractFormula.getFkAgentId());
            list.add(agentContractFormulaCompanyDto);
        }
        agentContractFormulaCompanyService.editAgentContractFormulaCompany(list);


        Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.REMINDER_FINANCE_EMAIL.key, 1).getData();
        String configStr = companyConfigMap.get(agentCompanyIds.get(0));

        Agent agent = agentMapper.selectById(agentContractFormulaDto.getFkAgentId());
        List<Map<String, String>> paramList = new ArrayList<>();
        Map<String, String> map = new HashMap<>();
        map.put("agentName", agent.getName());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (GeneralTool.isNotEmpty(agentContractFormulaDto.getStartTime())) {
            map.put("startTime", sdf.format(agentContractFormulaDto.getStartTime()));
        } else {
            map.put("startTime", "-");
        }
        if (GeneralTool.isNotEmpty(agentContractFormulaDto.getEndTime())) {
            map.put("endTime", sdf.format(agentContractFormulaDto.getEndTime()));
        } else {
            map.put("endTime", "-");
        }

        map.put("oldTime", "");

        if (GeneralTool.isNotEmpty(agentContractFormulaDto.getLimitAmountAg())) {
            map.put("limitAmountAg", agentContractFormulaDto.getLimitAmountAg().toString());
        } else {
            map.put("limitAmountAg", "-");
        }
        map.put("oldLimitAmountAg", "");
        StringBuilder sb = new StringBuilder();
        for (AgentContractFormulaCommissionDto agentContractFormulaCommissionDto : agentContractFormulaDto.getAgentContractFormulaCommissionVos()) {
            sb.append("<div class=\"wrap alone\"> <span class=\"wrap-content\">");
            if (GeneralTool.isNotEmpty(agentContractFormulaCommissionDto.getCommissionRateAg())) {
                sb.append("学费百分比，").append(agentContractFormulaCommissionDto.getCommissionRateAg()).append("%，上限：").append(GeneralTool.isEmpty(agentContractFormulaCommissionDto.getLimitAmountAg()) ? " - " : agentContractFormulaCommissionDto.getLimitAmountAg());
            }

            if (GeneralTool.isNotEmpty(agentContractFormulaCommissionDto.getReceivableRateAg())) {
                sb.append("应收百分比，").append(agentContractFormulaCommissionDto.getReceivableRateAg());
            }

            if (GeneralTool.isNotEmpty(agentContractFormulaCommissionDto.getFixedAmountAg())) {
                sb.append("固定金额，").append(agentContractFormulaCommissionDto.getFixedAmountAg());
            }
            sb.append("</span> </div>");
        }
        map.put("stage", sb.toString());
        map.put("statusName", agentContractFormulaDto.getIsActive() ? "有效" : "无效");
        map.put("oldStatusName", "");
        map.put("email",configStr);
        map.put("title","代理合同公式变更通知");
        paramList.add(map);
        reminderCenterClient.batchSendEmail(paramList, ProjectKeyEnum.AGENT_CONTRACT_UPDATE_NOTICE.key);
        return agentContractFormulaId;
    }


    private static <T> boolean compareValues(T oldValue, T newValue) {
        if (oldValue == null && newValue == null) {
            return true; // Both values are null, consider them equal
        } else if (oldValue == null || newValue == null) {
            return false; // One of the values is null, they're not equal
        } else {
            // Perform type-specific comparisons
            if (oldValue instanceof Date && newValue instanceof Date) {
                return ((Date) oldValue).equals((Date) newValue);
            } else if (oldValue instanceof Integer && newValue instanceof Integer) {
                return ((Integer) oldValue).equals((Integer) newValue);
            } else if (oldValue instanceof BigDecimal && newValue instanceof BigDecimal) {
                return ((BigDecimal) oldValue).equals((BigDecimal) newValue);
            } else if (oldValue instanceof String && newValue instanceof String) {
                return ((String) oldValue).equals((String) newValue);
            } else if (oldValue instanceof Boolean && newValue instanceof Boolean) {
                return ((Boolean) oldValue).equals((Boolean) newValue);
            } else {
                throw new IllegalArgumentException("Unsupported value types: " + oldValue.getClass() + ", " + newValue.getClass());
            }
        }
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (agentContractFormulaMapper.selectById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        //删除中间表(公司关联表单独放这)
        agentContractFormulaCompanyService.deleteByFkid(id);
        deleteTable(id);
        agentContractFormulaMapper.deleteById(id);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public AgentContractFormulaVo updateAgentContractFormula(AgentContractFormulaDto agentContractFormulaDto) {
        if (agentContractFormulaDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        AgentContractFormula result = agentContractFormulaMapper.selectById(agentContractFormulaDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        AgentContractFormula agentContractFormulaInitial = agentContractFormulaMapper.selectById(agentContractFormulaDto.getId());
        List<AgentContractFormulaCommission> agentContractFormulaCommissionListInitial = agentContractFormulaCommissionMapper.selectList(Wrappers.<AgentContractFormulaCommission>lambdaQuery().eq(AgentContractFormulaCommission::getFkAgentContractFormulaId, agentContractFormulaInitial.getId()));

        AgentContractFormula agentContractFormula = BeanCopyUtils.objClone(agentContractFormulaDto, AgentContractFormula::new);
        //修改中间表 先删在增
        deleteTable(agentContractFormula.getId());
        insertTable(agentContractFormulaDto, agentContractFormulaDto.getId());

        utilService.updateUserInfoToEntity(agentContractFormula);
        agentContractFormulaMapper.updateById(agentContractFormula);


        Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.REMINDER_FINANCE_EMAIL.key, 1).getData();
        List<Long> agentCompanyIds = agentCompanyService.getRelationByAgentId(agentContractFormulaDto.getFkAgentId());
        String configStr = companyConfigMap.get(agentCompanyIds.get(0));

        Agent agent = agentMapper.selectById(agentContractFormulaDto.getFkAgentId());
        List<Map<String, String>> paramList = new ArrayList<>();
        Map<String, String> map = new HashMap<>();
        map.put("agentName", agent.getName());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (GeneralTool.isNotEmpty(agentContractFormulaDto.getStartTime())) {
            map.put("startTime", sdf.format(agentContractFormulaDto.getStartTime()));
        } else {
            map.put("startTime", "-");
        }
        if (GeneralTool.isNotEmpty(agentContractFormulaDto.getEndTime())) {
            map.put("endTime", sdf.format(agentContractFormulaDto.getEndTime()));
        } else {
            map.put("endTime", "-");
        }

        StringBuilder sb = new StringBuilder();
        if (
                !compareValues(agentContractFormulaInitial.getStartTime(), agentContractFormula.getStartTime())
                        || !compareValues(agentContractFormulaInitial.getEndTime(), agentContractFormula.getEndTime())
        ) {
            sb.append("（原：");
            if (GeneralTool.isNotEmpty(agentContractFormulaInitial.getStartTime())) {
                sb.append(sdf.format(agentContractFormulaInitial.getStartTime())).append(" 至 ");
            } else {
                sb.append("-");
            }
            if (GeneralTool.isNotEmpty(agentContractFormulaDto.getEndTime())) {
                sb.append(sdf.format(agentContractFormulaDto.getEndTime()));
            } else {
                sb.append("-");
            }
            sb.append("）");
        }
        map.put("oldTime", sb.toString());


        if (GeneralTool.isNotEmpty(agentContractFormulaDto.getLimitAmountAg())) {
            map.put("limitAmountAg", agentContractFormulaDto.getLimitAmountAg().toString());
        } else {
            map.put("limitAmountAg", "");
        }
        if (GeneralTool.isNotEmpty(agentContractFormulaInitial.getLimitAmountAg()) && !compareValues(agentContractFormulaInitial.getLimitAmountAg(), agentContractFormula.getLimitAmountAg())) {
            map.put("oldLimitAmountAg", "（原："+ agentContractFormulaInitial.getLimitAmountAg() + " ）");
        } else {
            map.put("oldLimitAmountAg", "");
        }



        sb = new StringBuilder();
        int stage = Math.max(agentContractFormulaCommissionListInitial.size(), agentContractFormulaDto.getAgentContractFormulaCommissionVos().size());
        for (int i = 0; i < stage; i++) {
            AgentContractFormulaCommissionDto agentContractFormulaCommissionDto = agentContractFormulaDto.getAgentContractFormulaCommissionVos().size() >= i + 1 ? agentContractFormulaDto.getAgentContractFormulaCommissionVos().get(i) : null;
            AgentContractFormulaCommission agentContractFormulaCommissionInitial = agentContractFormulaCommissionListInitial.size() >= i + 1 ? agentContractFormulaCommissionListInitial.get(i) : null;
            sb.append("<div class=\"wrap alone\"> <span class=\"wrap-content\">");
            if (GeneralTool.isNotEmpty(agentContractFormulaCommissionDto)) {
                if (GeneralTool.isNotEmpty(agentContractFormulaCommissionDto.getCommissionRateAg())) {
                    sb.append("学费百分比，").append(agentContractFormulaCommissionDto.getCommissionRateAg()).append("%，上限：").append(GeneralTool.isEmpty(agentContractFormulaCommissionDto.getLimitAmountAg()) ? " - " : agentContractFormulaCommissionDto.getLimitAmountAg());
                }

                if (GeneralTool.isNotEmpty(agentContractFormulaCommissionDto.getReceivableRateAg())) {
                    sb.append("应收百分比，").append(agentContractFormulaCommissionDto.getReceivableRateAg()).append("%，上限：").append(GeneralTool.isEmpty(agentContractFormulaCommissionDto.getLimitAmountAg()) ? " - " : agentContractFormulaCommissionDto.getLimitAmountAg());
                }

                if (GeneralTool.isNotEmpty(agentContractFormulaCommissionDto.getFixedAmountAg())) {
                    sb.append("固定金额，").append(agentContractFormulaCommissionDto.getFixedAmountAg()).append("，上限：").append(GeneralTool.isEmpty(agentContractFormulaCommissionDto.getLimitAmountAg()) ? " - " : agentContractFormulaCommissionDto.getLimitAmountAg());

                }

                if (GeneralTool.isNotEmpty(agentContractFormulaCommissionInitial) && GeneralTool.isNotEmpty(agentContractFormulaCommissionInitial.getCommissionRateAg()) && (!compareValues(agentContractFormulaCommissionInitial.getCommissionRateAg(), agentContractFormulaCommissionDto.getCommissionRateAg()) || !compareValues(agentContractFormulaCommissionInitial.getLimitAmountAg(), agentContractFormulaCommissionDto.getLimitAmountAg()))) {
                    sb.append("<span class=\"orange\">（原：学费百分比，").append(agentContractFormulaCommissionInitial.getCommissionRateAg()).append("%，上限：").append(GeneralTool.isEmpty(agentContractFormulaCommissionInitial.getLimitAmountAg()) ? " - " : agentContractFormulaCommissionInitial.getLimitAmountAg()).append(")</span>");
                }
                if (GeneralTool.isNotEmpty(agentContractFormulaCommissionInitial) && GeneralTool.isNotEmpty(agentContractFormulaCommissionInitial.getFixedAmountAg()) && (!compareValues(agentContractFormulaCommissionInitial.getFixedAmountAg(), agentContractFormulaCommissionDto.getFixedAmountAg()) || !compareValues(agentContractFormulaCommissionInitial.getLimitAmountAg(), agentContractFormulaCommissionDto.getLimitAmountAg()))) {
                    sb.append("<span class=\"orange\">（原：固定金额，").append(agentContractFormulaCommissionInitial.getFixedAmountAg()).append("，上限：").append(GeneralTool.isEmpty(agentContractFormulaCommissionInitial.getLimitAmountAg()) ? " - " : agentContractFormulaCommissionInitial.getLimitAmountAg()).append(")</span>");
                }
                if (GeneralTool.isNotEmpty(agentContractFormulaCommissionInitial) && GeneralTool.isNotEmpty(agentContractFormulaCommissionInitial.getReceivableRateAg()) && (!compareValues(agentContractFormulaCommissionInitial.getReceivableRateAg(), agentContractFormulaCommissionDto.getReceivableRateAg()) || !compareValues(agentContractFormulaCommissionInitial.getLimitAmountAg(), agentContractFormulaCommissionDto.getLimitAmountAg()))) {
                    sb.append("<span class=\"orange\">（原：应收百分比，").append(agentContractFormulaCommissionInitial.getReceivableRateAg()).append("%，上限：").append(GeneralTool.isEmpty(agentContractFormulaCommissionInitial.getLimitAmountAg()) ? " - " : agentContractFormulaCommissionInitial.getLimitAmountAg()).append(")</span>");
                }
            } else {
                if (GeneralTool.isNotEmpty(agentContractFormulaCommissionInitial.getCommissionRateAg())) {
                    sb.append("<span class=\"orange\">（原：学费百分比，").append(agentContractFormulaCommissionInitial.getCommissionRateAg()).append("%，上限：").append(GeneralTool.isEmpty(agentContractFormulaCommissionInitial.getLimitAmountAg()) ? " - " : agentContractFormulaCommissionInitial.getLimitAmountAg()).append(",删除").append("）</span>");
                }
                if (GeneralTool.isNotEmpty(agentContractFormulaCommissionInitial.getReceivableRateAg())) {
                    sb.append("<span class=\"orange\">（原：应收百分比，").append(agentContractFormulaCommissionInitial.getReceivableRateAg()).append("%，上限：").append(GeneralTool.isEmpty(agentContractFormulaCommissionInitial.getLimitAmountAg()) ? " - " : agentContractFormulaCommissionInitial.getLimitAmountAg()).append(",删除").append("）</span>");
                }
                if (GeneralTool.isNotEmpty(agentContractFormulaCommissionInitial.getFixedAmountAg())) {
                    sb.append("<span class=\"orange\">（原：固定金额，").append(agentContractFormulaCommissionInitial.getFixedAmountAg()).append("%，上限：").append(GeneralTool.isEmpty(agentContractFormulaCommissionInitial.getLimitAmountAg()) ? " - " : agentContractFormulaCommissionInitial.getLimitAmountAg()).append(",删除").append("）</span>");
                }
            }
            sb.append("</span> </div>");
        }
        map.put("stage", sb.toString());
        map.put("statusName", agentContractFormulaDto.getIsActive() ? "有效" : "无效");
        if (!compareValues(agentContractFormulaDto.getIsActive(), agentContractFormulaInitial.getIsActive())) {
            sb = new StringBuilder();
            sb.append("(原：");
            map.put("oldStatusName",agentContractFormulaInitial.getIsActive() ? "（原：有效）" : "（原：无效）");
        } else {
            map.put("oldStatusName", "");
        }
        map.put("email",configStr);
        map.put("title","代理合同公式变动通知");
        paramList.add(map);
        reminderCenterClient.batchSendEmail(paramList, ProjectKeyEnum.AGENT_CONTRACT_UPDATE_NOTICE.key);

        return findAgentContractFormulaById(agentContractFormulaDto.getId());
    }

    @Override
    public List<AgentContractFormulaVo> getAgentContractFormulas(AgentContractFormulaDto agentContractFormulaDto, Page page) {
//        Example example = new Example(AgentContractFormula.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkAgentId", agentContractFormulaDto.getFkAgentId());
//        example.orderBy("viewOrder").desc();
//        /*//根据安全配置筛选
//        List<Long> agentContractFormulaIdList = getFormulaIdByCompanyIds(agentContractFormulaDto.getFkAgentId());
//        criteria.andIn("id", agentContractFormulaIdList);*/
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        List<AgentContractFormula> agentContractFormulas = agentContractFormulaMapper.selectByExample(example);
//        page.restPage(agentContractFormulas);

        IPage<AgentContractFormula> pages = agentContractFormulaMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())),
                Wrappers.<AgentContractFormula>lambdaQuery().eq(AgentContractFormula::getFkAgentId, agentContractFormulaDto.getFkAgentId()).orderByDesc(AgentContractFormula::getViewOrder));
        List<AgentContractFormula> agentContractFormulas = pages.getRecords();
        page.setAll((int) pages.getTotal());

        //代理合同公式id集合
        List<Long> agentContractFormulaIds = new ArrayList<>();
        //学校提供商id集合
        Set<Long> institutionProviderIds = new HashSet<>();
        //合同公式id集合
        Set<Long> contractFormulaIds = new HashSet<>();
        //各自的值
        for (AgentContractFormula agentContractFormula : agentContractFormulas) {
            agentContractFormulaIds.add(agentContractFormula.getId());
            institutionProviderIds.add(agentContractFormula.getFkInstitutionProviderId());
            contractFormulaIds.add(agentContractFormula.getFkContractFormulaId());
        }
        //学校提供商map
        institutionProviderIds.removeIf(Objects::isNull);
        Map<Long, String> institutionProviderNameMap = new HashMap<>();
        Result<Map<Long, String>> institutionProviderNameResult = institutionCenterClient.getInstitutionProviderNamesByIds(institutionProviderIds);
        if (institutionProviderNameResult.isSuccess() && GeneralTool.isNotEmpty(institutionProviderNameResult.getData())) {
            institutionProviderNameMap = institutionProviderNameResult.getData();
        }
        //合同公式map
        contractFormulaIds.removeIf(Objects::isNull);
        Map<Long, String> contractFormulaMap = new HashMap<>();
        Result<Map<Long, String>> contractFormulaResult = institutionCenterClient.getContractFormulasByIds(contractFormulaIds);
        if (contractFormulaResult.isSuccess() && GeneralTool.isNotEmpty(contractFormulaResult.getData())) {
            contractFormulaMap = contractFormulaResult.getData();
        }
        //对应国家名称map
        Map<Long, String> countryNameMap = agentContractFormulaAreaCountryService.getCountryNameMapByFkids(agentContractFormulaIds);
        //对应课程类型map
        Map<Long, String> courseTypeNameMap = agentContractFormulaCourseTypeService.getCourseTypeNameMapByFkids(agentContractFormulaIds);
        //对应课程等级map
        Map<Long, String> majorLevelNameMap = agentContractFormulaMajorLevelService.getMajorLevelNameMapByFkids(agentContractFormulaIds);
        //对应公司名称map
        Map<Long, String> companyNameMap = agentContractFormulaCompanyService.getCompanyNameMapByFkids(agentContractFormulaIds);
        //对应学校名称map
        Map<Long, String> institutionNameMap = agentContractFormulaInstitutionService.getInstitutionNameMapByFkids(agentContractFormulaIds);
        //对应课程名称map
        Map<Long, String> courseNameMap = agentContractFormulaInstitutionCourseService.getInstitutionCourseNameMapByFkids(agentContractFormulaIds);

        List<AgentContractFormulaVo> convertDatas = new ArrayList<>();
        for (AgentContractFormula agentContractFormula : agentContractFormulas) {
            AgentContractFormulaVo agentContractFormulaVo = BeanCopyUtils.objClone(agentContractFormula, AgentContractFormulaVo::new);
            //设置返回结果
            agentContractFormulaVo.setCountryName(countryNameMap.get(agentContractFormulaVo.getId()));
            agentContractFormulaVo.setCourseTypeName(courseTypeNameMap.get(agentContractFormulaVo.getId()));
            agentContractFormulaVo.setMajorLevelName(majorLevelNameMap.get(agentContractFormulaVo.getId()));
            agentContractFormulaVo.setCompanyName(companyNameMap.get(agentContractFormulaVo.getId()));
//            agentContractFormulaVo.setInstitutionProviderName(institutionProviderNameMap.get(agentContractFormulaVo.getFkInstitutionProviderId()));
            agentContractFormulaVo.setContractFormula(contractFormulaMap.get(agentContractFormulaVo.getFkContractFormulaId()));
            agentContractFormulaVo.setInstitutionName(institutionNameMap.get(agentContractFormulaVo.getId()));
            agentContractFormulaVo.setCourseName(courseNameMap.get(agentContractFormulaVo.getId()));
            agentContractFormulaVo.setAgentContractFormulaCommissionDtos(agentContractFormulaCommissionService.getAgentContractFormulaCommissionByFkid(agentContractFormulaVo.getId()));
            convertDatas.add(agentContractFormulaVo);
        }
        return convertDatas;
    }

    /*private List<Long> getFormulaIdByCompanyIds(Long agentId) {
        //获取该代理安全配置的公司ids
        List<Long> agentCompanyIds = agentCompanyService.getRelationByAgentId(agentId);
        List<Long> agentContractFormulaIdList = agentContractFormulaCompanyService.getFormulaIdByCompanyIds(agentCompanyIds);
        if (GeneralTool.isEmpty(agentContractFormulaIdList)) {
            agentContractFormulaIdList = new ArrayList<>();
            agentContractFormulaIdList.add(0L);
        }
        return agentContractFormulaIdList;
    }*/

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void movingOrder(List<AgentContractFormulaDto> agentContractFormulaDtos) {
        if (GeneralTool.isEmpty(agentContractFormulaDtos)) {
            throw new GetServiceException(ResultCode.INVALID_PARAM, "传入值为空");
        }
        AgentContractFormula ro = BeanCopyUtils.objClone(agentContractFormulaDtos.get(0), AgentContractFormula::new);
        Integer oneorder = ro.getViewOrder();
        AgentContractFormula rt = BeanCopyUtils.objClone(agentContractFormulaDtos.get(1), AgentContractFormula::new);
        Integer twoorder = rt.getViewOrder();
        ro.setViewOrder(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setViewOrder(oneorder);
        utilService.updateUserInfoToEntity(rt);
        agentContractFormulaMapper.updateById(ro);
        agentContractFormulaMapper.updateById(rt);
    }

    @Override
    public void editAgentContractFormulaCompany(List<AgentContractFormulaCompanyDto> agentContractFormulaCompanyDtos) {
        agentContractFormulaCompanyService.editAgentContractFormulaCompany(agentContractFormulaCompanyDtos);
    }

    @Override
    public List<CompanyTreeVo> getAgentContractFormulaCompany(Long agentContractFormulaId) {
        if (GeneralTool.isEmpty(agentContractFormulaId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        return agentContractFormulaCompanyService.getAgentContractFormulaCompany(agentContractFormulaId);

    }

    private void insertTable(AgentContractFormulaDto agentContractFormulaDto, Long agentContractFormulaId) {
        if (GeneralTool.isNotEmpty(agentContractFormulaDto.getCountryIdList())) {
            for (Long areaCountryId : agentContractFormulaDto.getCountryIdList()) {
                AgentContractFormulaAreaCountryDto agentContractFormulaAreaCountryDto = new AgentContractFormulaAreaCountryDto();
                agentContractFormulaAreaCountryDto.setFkAgentContractFormulaId(agentContractFormulaId);
                agentContractFormulaAreaCountryDto.setFkAreaCountryId(areaCountryId);
                agentContractFormulaAreaCountryService.addAgentContractFormulaAreaCountry(agentContractFormulaAreaCountryDto);
            }
        }
        if (GeneralTool.isNotEmpty(agentContractFormulaDto.getCourseTypeIdList())) {
            for (Long courseTypeId : agentContractFormulaDto.getCourseTypeIdList()) {
                AgentContractFormulaCourseTypeDto agentContractFormulaCourseTypeDto = new AgentContractFormulaCourseTypeDto();
                agentContractFormulaCourseTypeDto.setFkAgentContractFormulaId(agentContractFormulaId);
                agentContractFormulaCourseTypeDto.setFkCourseTypeId(courseTypeId);
                agentContractFormulaCourseTypeService.addAgentContractFormulaCourseType(agentContractFormulaCourseTypeDto);
            }
        }
        if (GeneralTool.isNotEmpty(agentContractFormulaDto.getMajorLevelIdList())) {
            for (Long majorLevelId : agentContractFormulaDto.getMajorLevelIdList()) {
                AgentContractFormulaMajorLevelDto agentContractFormulaMajorLevelDto = new AgentContractFormulaMajorLevelDto();
                agentContractFormulaMajorLevelDto.setFkAgentContractFormulaId(agentContractFormulaId);
                agentContractFormulaMajorLevelDto.setFkMajorLevelId(majorLevelId);
                agentContractFormulaMajorLevelService.addAgentContractFormulaMajorLevel(agentContractFormulaMajorLevelDto);
            }
        }
        if (GeneralTool.isNotEmpty(agentContractFormulaDto.getInstitutionIdList())) {
            for (Long institutionId : agentContractFormulaDto.getInstitutionIdList()) {
                AgentContractFormulaInstitutionDto agentContractFormulaInstitutionDto = new AgentContractFormulaInstitutionDto();
                agentContractFormulaInstitutionDto.setFkAgentContractFormulaId(agentContractFormulaId);
                agentContractFormulaInstitutionDto.setFkInstitutionId(institutionId);
                agentContractFormulaInstitutionService.addAgentContractFormulaInstitution(agentContractFormulaInstitutionDto);
            }
        }
        if (GeneralTool.isNotEmpty(agentContractFormulaDto.getCourseIdList())) {
            for (Long courseId : agentContractFormulaDto.getCourseIdList()) {
                AgentContractFormulaInstitutionCourseDto agentContractFormulaInstitutionCourseDto = new AgentContractFormulaInstitutionCourseDto();
                agentContractFormulaInstitutionCourseDto.setFkAgentContractFormulaId(agentContractFormulaId);
                agentContractFormulaInstitutionCourseDto.setFkInstitutionCourseId(courseId);
                agentContractFormulaInstitutionCourseService.addAgentContractFormulaInstitutionCourse(agentContractFormulaInstitutionCourseDto);
            }
        }
        if (GeneralTool.isNotEmpty(agentContractFormulaDto.getAgentContractFormulaCommissionVos())) {
            for (AgentContractFormulaCommissionDto agentContractFormulaCommissionDto : agentContractFormulaDto.getAgentContractFormulaCommissionVos()) {
                agentContractFormulaCommissionDto.setFkAgentContractFormulaId(agentContractFormulaId);
                agentContractFormulaCommissionService.addContractFormulaCommission(agentContractFormulaCommissionDto);
            }
        }
    }
    @Transactional(rollbackFor = Exception.class)
    public void deleteTable(Long id) {
        agentContractFormulaAreaCountryService.deleteByFkid(id);
        agentContractFormulaCourseTypeService.deleteByFkid(id);
        agentContractFormulaMajorLevelService.deleteByFkid(id);
        agentContractFormulaInstitutionService.deleteByFkid(id);
        agentContractFormulaInstitutionCourseService.deleteByFkid(id);
        agentContractFormulaCommissionService.deleteByFkid(id);
    }
}

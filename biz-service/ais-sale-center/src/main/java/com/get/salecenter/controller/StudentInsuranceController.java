package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.vo.CommentVo;
import com.get.salecenter.vo.MediaAndAttachedVo;
import com.get.salecenter.vo.StudentInsuranceVo;
import com.get.salecenter.service.IStudentInsuranceService;
import com.get.salecenter.dto.*;
import com.get.salecenter.dto.CommentDto;
import com.get.salecenter.dto.query.InsuranceSummaryQueryDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE: 2022/1/12
 * @TIME: 16:06
 * @Description:
 **/
@Api(tags = "留学保险管理")
@RestController
@RequestMapping("sale/studentInsurance")
public class StudentInsuranceController {
    @Resource
    private IStudentInsuranceService studentInsuranceService;

    /**
     * @return com.get.common.result.ListResponseBo<com.get.salecenter.vo.AgentEventTypeVo>
     * @Description: 列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "汇总数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/保险管理/保险汇总")
    @PostMapping("datas")
    public ListResponseBo<StudentInsuranceVo> datas(@RequestBody SearchBean<InsuranceSummaryQueryDto> page) {
        String[] times = {"0", "0"};//[0]-o-主SQL执行时间,[1]-f-远程调用时间
        List<StudentInsuranceVo> datas = studentInsuranceService.getStudentInsuranceSummary(page.getData(), page, times);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p, times[0], times[1]);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 新增信息
     * @Param [agentEventVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/留学保险管理/新增留学保险")
    @PostMapping("add")
    public ResponseBo add(@RequestBody  @Validated(StudentInsuranceDto.Add.class)  StudentInsuranceDto StudentInsuranceDto) {
        studentInsuranceService.addStudentInsurance(StudentInsuranceDto);
        return ResponseBo.ok();
    }

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/留学保险管理/详情")
    @GetMapping("/{id}")
    public ResponseBo<StudentInsuranceVo> detail(@PathVariable("id") Long id) {
        StudentInsuranceVo data = studentInsuranceService.findStudentInsuranceById(id);
        return new ResponseBo<>(data);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.AgentEventTypeVo>
     * @Description: 修改信息
     * @Param [agentEventVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/留学保险管理/更新")
    @PostMapping("update")
    public ResponseBo<StudentInsuranceVo> update(@RequestBody  @Validated(StudentInsuranceDto.Update.class) StudentInsuranceDto StudentInsuranceDto) {
        return UpdateResponseBo.ok(studentInsuranceService.updateStudentInsurance(StudentInsuranceDto));
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 删除信息
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/留学保险管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        studentInsuranceService.deleteStudentInsurance(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @param mediaAttachedVo
     * @return
     * @
     */

    @ApiOperation(value = "保存文件")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/留学保险管理/保存附件资料")
    @PostMapping("addMediaAndAttached")
    public ResponseBo addMediaAndAttached(@RequestBody  @Validated(MediaAndAttachedDto.Add.class) List<MediaAndAttachedDto> mediaAttachedVo) {
        return UpdateResponseBo.ok(studentInsuranceService.addStudentInsuranceMedia(mediaAttachedVo));
    }

    /**
     * 查询学校资讯附件
     *
     * @param voSearchBean
     * @return
     * @
     */
    @ApiOperation(value = "查询留学保险附件")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/留学保险管理/查询留学保险附件")
    @PostMapping("getStudentInsuranceMedia")
    public ResponseBo<MediaAndAttachedVo> getStudentInsuranceMedia(@RequestBody SearchBean<MediaAndAttachedDto> voSearchBean) {
        List<MediaAndAttachedVo> accommodationMedia = studentInsuranceService.getStudentInsuranceMedia(voSearchBean.getData(), voSearchBean);
        Page page = BeanCopyUtils.objClone(voSearchBean, Page::new);
        return new ListResponseBo<>(accommodationMedia, page);
    }

    /**
     * 附件类型下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "附件类型下拉框数据", notes = "")
    @GetMapping("findMediaAndAttachedType")
    public ResponseBo findMediaAndAttachedType() {
        List<Map<String, Object>> datas = studentInsuranceService.findMediaAndAttachedType();
        return new ListResponseBo<>(datas);
    }


    /**
     * @param commentDto
     * @return
     * @
     */
    @ApiOperation(value = "编辑评论")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/留学保险管理/编辑评论")
    @PostMapping("editComment")
    public ResponseBo editComment(@RequestBody @Validated(CommentDto.Add.class) CommentDto commentDto) {
        return SaveResponseBo.ok(studentInsuranceService.editComment(commentDto));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.AgentContractVo>
     * @Description: 评论列表数据
     * @Param [searchBean]
     * <AUTHOR>
     */
    @ApiOperation(value = "评论列表数据")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/留学保险管理/查询评论")
    @PostMapping("getComments")
    public ResponseBo<CommentVo> getComment(@RequestBody SearchBean<CommentDto> searchBean) {
        List<CommentVo> datas = studentInsuranceService.getComments(searchBean.getData(), searchBean);
        Page p = BeanCopyUtils.objClone(searchBean, Page::new);
        return new ListResponseBo<>(datas, p);
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.AgentContractVo>
     * @Description: 批量创建应收应付
     * @Param [searchBean]
     * <AUTHOR>
     */
    @ApiOperation(value = "批量创建应收应付")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/留学保险管理/批量创建应收应付")
    @PostMapping("createARAP")
    public ResponseBo createARAP(@RequestBody InsuranceSummaryDto insuranceSummaryDto) {
        studentInsuranceService.createARAP(insuranceSummaryDto);
        return UpdateResponseBo.ok();
    }

    @ApiOperation(value = "批量创建应收")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/留学保险管理/批量创建应收")
    @PostMapping("createReceivablePlan")
    public ResponseBo createReceivablePlan(@RequestBody InsuranceSummaryReDto insuranceSummaryReDto) {
        studentInsuranceService.createReceivablePlan(insuranceSummaryReDto);
        return SaveResponseBo.ok();
    }


    @ApiOperation(value = "批量创建应付")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/留学保险管理/批量创建应付")
    @PostMapping("createPayablePlan")
    public ResponseBo createPayablePlan(@RequestBody InsuranceSummaryPayDto insuranceSummaryPayDto) {
        studentInsuranceService.createPayablePlan(insuranceSummaryPayDto);
        return SaveResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 删除信息
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "关闭接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/留学保险管理/关闭")
    @PostMapping("close/{id}")
    public ResponseBo close(@PathVariable("id") Long id) {
        studentInsuranceService.closeStudentInsuranceById(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 删除信息
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "激活接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/留学保险管理/激活")
    @PostMapping("activation/{id}")
    public ResponseBo activation(@PathVariable("id") Long id) {
        studentInsuranceService.activationStudentInsuranceById(id);
        return DeleteResponseBo.ok();
    }


    /**
     * @Description: 导出留学住宿列表
     * @Author: jack
     * @Date:16:13 2021/12/20
     */
    @ApiOperation(value = "导出留学保险列表", notes = "")
    @PostMapping("/exportExcel")
    @ResponseBody
    public void exportExcel(HttpServletResponse response, @RequestBody InsuranceSummaryQueryDto studentInsurance) {
        studentInsuranceService.exportExcel(response, studentInsurance);
    }

    @ApiOperation(value = "设置无代理（无需结算）接口", notes = "id为此条数据的id")
    @PostMapping("/updateAgent/{id}")
    public ResponseBo updateNoAgent(@PathVariable("id") Long id){
        studentInsuranceService.updateNoAgent(id);
        return UpdateResponseBo.ok();
    }

    @ApiOperation(value = "恢复代理接口")
    @PostMapping("recoveryAgent")
    public ResponseBo recoveryAgent(@RequestBody InsuranceAgentUpdateDto insuranceAgentUpdateDto){
        studentInsuranceService.recoveryAgent(insuranceAgentUpdateDto);
        return UpdateResponseBo.ok();
    }

    @VerifyPermission(IsVerify = false)
    @ApiModelProperty(value = "支付方式下拉", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/留学保险管理/支付方式下拉")
    @PostMapping("getPaymentMethodSelect")
    public ResponseBo<BaseSelectEntity> getPaymentMethodSelect() {
        return new ListResponseBo(ProjectExtraEnum.enumsTranslation2Arrays(ProjectExtraEnum.INSURANCE_PAYMENT_METHOD));
    }
}

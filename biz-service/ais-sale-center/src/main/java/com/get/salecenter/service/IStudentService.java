package com.get.salecenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.common.result.FocExportVo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.salecenter.vo.*;
import com.get.salecenter.entity.Student;
import com.get.salecenter.dto.*;
import com.get.salecenter.dto.query.StudentListQueryDto;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/9/29
 * @TIME: 14:28
 * @Description:
 **/
public interface IStudentService  extends IService<Student> {

    /**
     * @return java.lang.Long
     * @Description: 新增
     * @Param [studentDto]
     * <AUTHOR>
     */
    Long addStudent(StudentDto studentDto);

    /**
     * @return java.util.List<com.get.salecenter.vo.StudentVo>
     * @Description: 列表
     * @Param [studentVo, page]
     * <AUTHOR>
     */
    List<StudentVo> getStudents(StudentListQueryDto studentListQueryDto, String[] times, Long staffId, String local, List<Long> countryIds);


    ResponseBo getStudentPaginationInfo(StudentListQueryDto studentListQueryDto, Page page);
    /**
     * 学生查询
     *
     * @param studentDto
     * @return
     * @
     */
    List<StudentVo> getStudentsByIds(StudentDto studentDto, List<Long> studentIdList);

    /**
     * @return com.get.salecenter.vo.StudentVo
     * @Description: 详情
     * @Param [id]
     * <AUTHOR>
     */
    StudentVo findStudentById(Long id);

    /**
     * @return void
     * @Description: 删除
     * @Param [id]
     * <AUTHOR>
     */
    void delete(Long id);

    /**
     * @return com.get.salecenter.vo.StudentVo
     * @Description: 修改
     * @Param [studentDto]
     * <AUTHOR>
     */
    StudentVo updateStudent(StudentDto studentDto);


    /**
     * @return java.lang.Long
     * @Description: 新增
     * @Param [projectRoleStaffVo]
     * <AUTHOR>
     */
    Long addProjectRoleStaff(StudentProjectRoleStaffDto projectRoleStaffVo);


    /**
     * @return java.util.List<com.get.salecenter.vo.StudentProjectRoleStaffVo>
     * @Description: 列表
     * @Param [projectRoleStaffVo, page]
     * <AUTHOR>
     */
    List<StudentProjectRoleStaffVo> getProjectRoleStaff(StudentProjectRoleStaffDto projectRoleStaffVo, Page page);

    /**
     * @return java.lang.Long
     * @Description: 增加或者更新评论
     * @Param [commentDto]
     * <AUTHOR>
     **/
    Long editComment(CommentDto commentDto);

    /**
     * @return java.util.List<com.get.salecenter.vo.CommentVo>
     * @Description: 获取所有评论
     * @Param [commentDto, page]
     * <AUTHOR>
     */
    List<CommentVo> getComments(CommentDto commentDto, Page page);

    /**
     * @return java.util.List<com.get.salecenter.vo.MediaAndAttachedDto>
     * @Description: 添加附件
     * @Param [mediaAttachedVo]
     * <AUTHOR>
     */
    List<MediaAndAttachedVo> addMedia(List<MediaAndAttachedDto> mediaAttachedVo);

    /**
     * @return java.util.List<com.get.salecenter.vo.MediaAndAttachedDto>
     * @Description: 获取附件
     * @Param [andAttachedVo, page]
     * <AUTHOR>
     */
    List<MediaAndAttachedVo> getMedia(MediaAndAttachedDto andAttachedVo, Page page);


    /**
     * 根据输入的名称 模糊查询对应的学生id
     *
     * @param name
     * @return
     */
    List<Long> getStudentIds(String name);

    /**
     * feign调用 根据输入的学生id 模糊查询对应的名称
     *
     * @param id
     * @return
     */
    String getStudentNameById(Long id);


    /**
     * feign调用 根据输入学生ids 查询对应的名称map
     *
     * @param ids
     * @return
     * @
     */
    Map<Long, String> getStudentNameByIds(Set<Long> ids);

    /**
     * 学生下拉框数据
     *
     * @return
     */
    List<BaseSelectEntity> getStudentList(Long companyId);


    /**
     * 导出学生信息Excel
     *
     * @
     */
    void exportStudentInfoExcel(StudentListQueryDto studentListQueryDto, List<FocExportVo> focExportVos);

    /**
     * 导出学生信息Excel表头选项
     * @return
     */
    List<FocExportVo> getStudentOptions();

    /**
     * 学生步骤状态数据列表
     *
     * @Date 10:07 2021/7/21
     * <AUTHOR>
     */
    List<StudentOfferItemStatisticalStatusVo> getStudentsStepState(StudentOfferItemStatisticalStatusDto studentOfferItemStatisticalStatusDto);

    /**
     * 学生步骤状态数据列表(NEW)
     *
     * @Date 10:07 2021/7/21
     * <AUTHOR>
     */
    List<StudentOfferItemStatisticalStatusVo> getStudentsStepStateNew(StudentOfferItemStatisticalStatusDto studentOfferItemStatisticalStatusDto);


    /**
     * 验证学生唯一性false不唯一
     *
     * @param studentDto
     * @return
     */
    Long validateStudent(StudentDto studentDto);

    /**
     * 验证手机唯一性false不唯一
     *
     * @param studentId
     * @param phone
     * @return
     */
    Boolean validatePhone(Long studentId, String phone);

    /**
     * 验证邮箱唯一性false不唯一
     *
     * @param studentId
     * @param email
     * @return
     */
    Boolean validateEmail(Long studentId, String email);

    /**
     * @return java.util.List<com.get.salecenter.vo.StudentAccommodationVo>
     * @Description: 留学住宿列表
     * @Param [data, page]
     * <AUTHOR>
     */
    List<StudentAccommodationVo> getStudentAccommodationList(StudentAccommodationDto studentAccommodationDto, Page page);

    /**
     * @return java.util.List<com.get.salecenter.vo.StudentAccommodationVo>
     * @Description: 留学保险列表
     * @Param [data, page]
     * <AUTHOR>
     */
    List<StudentInsuranceVo> getStudentInsuranceList(StudentInsuranceDto studentInsuranceDto, Page page);

    ExistStudentVo getIsExistStudent(String studentName, String email, String mobile, String passpost, String birthday,
                                     Long id, String companyIds);

    /**
     * 用于学生资源详情中，绑定业务学生
     * 根据学生资源所属分公司 + 学生名称 + 学生生日。查找对应业务学生数据，若无，显示：暂无可以绑定学生数据。
     *
     * @param companyId   学生资源所属分公司id
     * @param studentName 学生名称
     * @param birthday    学生生日
     * @return
     */
    List<ExistStudentByBoundVo> getStudentByBoundBusinessStudent(Long companyId, String studentName, String birthday);

    /**
     * 获取中文名称的拼音
     *
     * @param studentName
     * @return
     */
    PinyinNameVo getNameToPinyin(String studentName);

    /**
     * 设置一键入学失败
     *
     * @param studentId
     * @param reasonId
     */
    void batchEnrolFailure(Long studentId, Long reasonId, String reason, Long studentOfferId);

    /**
     * 判断学生是否完善护照信息
     *
     * @param fkStudentId
     * @return
     */
    Boolean hasPassportNum(Long fkStudentId);

    /**
     * 成绩类型
     *
     * @return
     */
    List<Map<String, Object>> getTestTypeSelect();

    /**
     * 获取学生绑定信息
     * @param fkStudentNum
     * @return
     */
    StudentAgentBindingVo getStudentAgentBindingByStudent(String fkStudentNum);

    /**
     * 批量修改学生绑定信息
     * @param studentAgentBindingDto
     */
    void updateStudentAgentBinding(StudentAgentBindingDto studentAgentBindingDto);

    /**
     * 学生最终申请状态统计
     *
     * @Date 12:00 2022/6/30
     * <AUTHOR>
     */
    List<CurrentStudentApplicationStatusVo> getCurrentStudentApplicationStatus(CurrentStudentApplicationStatusListDto currentStudentApplicationStatusListDto);

    /**
     * 我的学生申请状态统计
     *
     * @Date 11:47 2022/7/14
     * <AUTHOR>
     */
    List<CurrentStudentApplicationStatusStatisticsVo> getCurrentStudentApplicationStatusStatistics(CurrentStudentApplicationStatusStatisticsDto currentStudentApplicationStatusStatisticsDto);


    /**
     * 获取学生所有毕业国家选项
     * @param companyId
     * @return
     */
    List<BaseSelectEntity> getStudentAllGraduatedCountrySelect(Long companyId);

    /**
     * 获取学生毕业国家对应的毕业周省
     * @param id
     * @return
     */
    List<BaseSelectEntity> getStudentGraduateCountryMpStateSelect(Long id,Long companyId);

    /**
     * 获取学生毕业国家及省份下的毕业学校
     * @param companyId
     * @param countryId
     * @param stateId
     * @return
     */
    List<BaseSelectEntity> getStudentGraduateSchool(Long companyId,Long countryId,Long stateId);


    /**
     * 获取学生申请计划国家下拉
     * @param companyId
     * @return
     */
    List<BaseSelectEntity> getStudentStudyPlanCountrySelect(Long companyId);


    /**
     * 获取学生申请计划国家下的申请院校
     * @param id
     * @param companyId
     * @return
     */
    List<BaseSelectEntity> getStudentStudyPlanSchoolSelect(Long companyId,Long id);

    /**
     * 获取学生课程统计信息
     * @param statisticsSearchVo
     * @param page
     * @return
     */
    StudentCourseStatisticsListDto getStudentCourseStatisticsInfoList(StudentCourseStatisticsSearchDto statisticsSearchVo, Page page);

    /**
     * 获取学生毕业背景信息
     * @param statisticsSearchVo
     * @return
     */
    StudentGraduationBackgroundVo getStudentGraduationBackgroundInfo(StudentCourseStatisticsSearchDto statisticsSearchVo);

    /**
     * 学生课程信息导出
     * @param statisticsSearchVo
     * @param response
     */
    void exportStudentCourseStatisticsInfo(StudentCourseStatisticsSearchDto statisticsSearchVo, HttpServletResponse response);

    /**
     * 获取学历
     * @return
     */
    List<BaseSelectEntity> getEducationDropDown();

    /**
     * 根据学生名称获取学生绑定信息
     * @return
     */
    List<StudentAgentBindingNewVo> getStudentAgentBindingByStudentName(StudentInfoDto studentInfoDto, Page page);

    List<StudentAgentEmailVo> getStudentInfoByEmail(String email, Long companyId);


    void updatePassportNum(Long id, String passportNum);


    /**
     * 学生信息合并
     * @param mergedStudentId
     * @param targetStudentId
     * @param stuSource
     * @return
     */
    SaveResponseBo mergeStudentInformation(Long mergedStudentId,Long targetStudentId,String stuSource);

    /**
     * 通过学生编号查询学生
     * @param num
     * @return
     */
    StudentVo getStudentByNum(String num);

    /**
     * 互换学生
     * @param mergedStudentId
     * @param targetStudentId
     * @return
     */
    void mergeStudentIssueId(Long mergedStudentId, Long targetStudentId);

    List<StudentVo> getClientStudent(ClientStudentDto clientStudentDto, Page page);

    /**
     * 一键激活失败
     * @param studentId
     * @param studentOfferId
     */
    void batchActivateEnrolFailure(Long studentId, Long studentOfferId);

    /**
     * 验证共享链接是否必填
     * @return
     */
    Boolean validateShareParthRequired();

    ReceiveApplyDataTimeConfigVo getReceiveApplyDataTimeConfig(Long fkStudentId);

    void editReceiveApplyDataTimeConfig(ReceiveApplyDataTimeDto vo);

    List<BaseSelectEntity> getStudentSelect(String name, List<Long> companyIds, Long studentId);

    Student selectById(Long studentId);

    /**
     * AI获取学生信息
     *
     * @param studentName
     * @return
     */
    List<AiStudentDto> getAiStudentInfo(String studentName);
}

package com.get.salecenter.service;


import com.get.common.result.ResponseBo;
import com.get.salecenter.vo.ConventionTablePersonVo;
import com.get.salecenter.entity.ConventionTable;
import com.get.salecenter.entity.ConventionTablePerson;
import com.get.salecenter.dto.ConventionTablePersonDto;

import java.util.List;

/**
 * @author: Sea
 * @create: 2020/8/28 16:10
 * @verison: 1.0
 * @description:
 */
public interface IConventionTablePersonService {

    /**
     * 配置桌台-晚宴桌
     *
     * @param conventionTablePersonDto
     * @return
     */
    Long configurationTable(ConventionTablePersonDto conventionTablePersonDto);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 通过桌台id 查询 晚宴桌-参会人员 中间表对应参会人员信息
     *
     * @param id
     * @return
     */
    List<ConventionTablePersonVo> getPersonByTableId(Long id);

    /**
     * 通过桌台id 查询该桌台对应的数据list
     *
     * @param id
     * @return
     */
    List<ConventionTablePerson> getTablePersonListById(Long id);

    /**
     * @return java.lang.String
     * @Description :通过参会人员id 获取对应桌台信息
     * @Param [id]
     * <AUTHOR>
     */
    List<ConventionTable> getTableByPersonId(Long id);


    ResponseBo validateConventionPerson(Long conventionPersonId,Long conventionId);
}

package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.CollectionUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.registrationcenter.entity.User;
import com.get.registrationcenter.feign.IRegistrationCenterClient;
import com.get.salecenter.dao.newissue.IssueUserContactPersonMapper;
import com.get.salecenter.dao.sale.ContactPersonMapper;
import com.get.salecenter.dao.sale.ContactPersonTypeMapper;
import com.get.salecenter.dto.ContactPersonCompanyDto;
import com.get.salecenter.dto.ContactPersonDto;
import com.get.salecenter.entity.ContactPersonType;
import com.get.salecenter.entity.SaleContactPerson;
import com.get.salecenter.entity.UserContactPerson;
import com.get.salecenter.enums.ContactPersonTypeEnum;
import com.get.salecenter.service.IAgentCompanyService;
import com.get.salecenter.service.IAgentContractService;
import com.get.salecenter.service.IAgentService;
import com.get.salecenter.service.ICompanyRelationService;
import com.get.salecenter.service.IContactPersonCompanyService;
import com.get.salecenter.service.IContactPersonService;
import com.get.salecenter.service.IContactPersonTypeService;
import com.get.salecenter.utils.MyStringUtils;
import com.get.salecenter.utils.sale.GetAgentLabelDataUtils;
import com.get.salecenter.vo.AgentLabelVo;
import com.get.salecenter.vo.CompanyTreeVo;
import com.get.salecenter.vo.ContactPersonMobileSelectVo;
import com.get.salecenter.vo.ContactPersonTypeVo;
import com.get.salecenter.vo.ContactPersonVo;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Resource;
import net.sf.json.JSONArray;
import net.sf.json.JsonConfig;
import org.apache.commons.lang.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @DATE: 2020/8/17
 * @TIME: 11:11
 * @Description: 联系人管理实现类
 **/
@Service("saleContactPersonService")
public class ContactPersonServiceImpl extends ServiceImpl<ContactPersonMapper, SaleContactPerson>
        implements IContactPersonService {
    @Resource
    private ContactPersonMapper personMapper;
    // @Resource
    // private IReminderCenterClient reminderCenterClient;
    @Resource
    private IContactPersonCompanyService personCompanyService;
    @Resource
    private ICompanyRelationService companyRelationService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Lazy
    @Resource
    private IAgentService agentService;
    @Resource
    private IAgentContractService contractService;
    @Resource
    private IAgentCompanyService agentCompanyService;
    @Resource
    private UtilService utilService;
    @Resource
    private ContactPersonTypeMapper contactPersonTypeMapper;
    @Resource
    private IContactPersonTypeService contactPersonTypeService;
    @Resource
    private IRegistrationCenterClient registrationCenterClient;
    @Resource
    private IssueUserContactPersonMapper userContactPersonMapper;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    private GetAgentLabelDataUtils getAgentLabelDataUtils;

    @Override
    public List<ContactPersonVo> getContactPersonDtos(ContactPersonDto contactPersonVo, Page page) {
        if (GeneralTool.isEmpty(contactPersonVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        if (GeneralTool.isNotEmpty(contactPersonVo.getQueryWord())) {
            contactPersonVo.setQueryWord(contactPersonVo.getQueryWord().trim());
        }

        Long staffId = SecureUtil.getStaffId();
        List<Long> staffFollowerIds = new ArrayList<>();
        // 员工id + 业务下属员工ids
        if (staffId != -1L){
            staffFollowerIds = permissionCenterClient.getStaffFollowerIds(staffId).getData().stream()
                    .filter(Objects::nonNull).distinct().collect(Collectors.toList());
            staffFollowerIds.add(staffId);
            // 去重
            staffFollowerIds = staffFollowerIds.stream().distinct().collect(Collectors.toList());
        }

        if (GeneralTool.isNotEmpty(contactPersonVo.getFkContactPersonTypeKey())
                && contactPersonVo.getFkContactPersonTypeKey().equals("CONTACT_AGENT_CONTRACT")) {
            contactPersonVo.setIsContractContact(true);
        }

        // LambdaQueryWrapper<SaleContactPerson> lambdaQueryWrapper = new
        // LambdaQueryWrapper<>();
        // List<Long> allIds = new ArrayList<>();
        // if (GeneralTool.isEmpty(contactPersonVo.getFkCompanyId())) {
        // List<Long> companyIds =
        // SecureUtil.getCompanyIdsByStaffId(GetAuthInfo.getStaffId());
        // companyIds.removeIf(Objects::isNull);
        // List<Long> companyId = getCompanyId(companyIds);
        // allIds.addAll(companyId);
        // }
        // if (GeneralTool.isNotEmpty(contactPersonVo.getFkCompanyId())) {
        // SecureUtil.validateCompany(contactPersonVo.getFkCompanyId());
        // List<Long> queryCompanyId = new ArrayList<>();
        // queryCompanyId.add(contactPersonVo.getFkCompanyId());
        // List<Long> contactIds = getCompanyId(queryCompanyId);
        // allIds.addAll(contactIds);
        // }
        // if(GeneralTool.isNotEmpty(allIds))
        // {
        // lambdaQueryWrapper.in(SaleContactPerson::getId,
        // allIds.stream().distinct().collect(Collectors.toList()));
        // }
        // if (GeneralTool.isNotEmpty(contactPersonVo.getFkTableName())) {
        // lambdaQueryWrapper.eq(SaleContactPerson::getFkTableName,contactPersonVo.getFkTableName());
        // }
        // if (GeneralTool.isNotEmpty(contactPersonVo.getFkTableId())) {
        // lambdaQueryWrapper.eq(SaleContactPerson::getFkTableId,contactPersonVo.getFkTableId());
        // }
        // //联系人类型查询
        // if (GeneralTool.isNotEmpty(contactPersonVo.getFkContactPersonTypeKey())) {
        // lambdaQueryWrapper.like(SaleContactPerson::getFkContactPersonTypeKey,contactPersonVo.getFkContactPersonTypeKey());
        // }
        // //名称查询/公司名称查询
        // if (GeneralTool.isNotEmpty(contactPersonVo.getKeyWord())) {
        // lambdaQueryWrapper.and(wrapper->wrapper.like(SaleContactPerson::getName,contactPersonVo.getKeyWord())
        // .or().like(SaleContactPerson::getWechat,contactPersonVo.getKeyWord())
        // .or().like(SaleContactPerson::getCompany,contactPersonVo.getKeyWord()));
        // }
        // lambdaQueryWrapper.orderByDesc(SaleContactPerson::getGmtCreate);
        // IPage<SaleContactPerson> pages =
        // personMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(),page.getShowCount())),lambdaQueryWrapper);
        // List<SaleContactPerson> contactPeoples = pages.getRecords();
        List<ContactPersonVo> contactPersonVos = new ArrayList<>();
        if (GeneralTool.isEmpty(page)) {
            contactPersonVos = personMapper.getContactPersons(null, contactPersonVo, staffFollowerIds,
                    SecureUtil.getCompanyIds());
        } else {
            IPage<SaleContactPerson> pages = GetCondition
                    .getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
            contactPersonVos = personMapper.getContactPersons(pages, contactPersonVo, staffFollowerIds,
                    SecureUtil.getCompanyIds());
            page.setAll((int) pages.getTotal());
        }

        // List<ContactPersonDto> contactPersonVos = contactPeoples.stream()
        // .map(contactPerson -> BeanCopyUtils.objClone(contactPerson,
        // ContactPersonDto::new)).collect(Collectors.toList());
        // 设置联系人属性
        setContactPersonList(contactPersonVos);

        Set<Long> fkTableIds = contactPersonVos.stream().map(ContactPersonVo::getFkTableId).collect(Collectors.toSet());
        Set<String> agentLabelEmailList = contactPersonVos.stream().map(ContactPersonVo::getEmail)
                .collect(Collectors.toSet());

        Map<String, List<AgentLabelVo>> agentEamailLabelMap = getAgentLabelDataUtils
                .getAgentEmailLabelMap(agentLabelEmailList).getAgentEmailLabelMap();
        // 根据代理合同ids查询合同编号
        Map<Long, String> agentContractByIds = new HashMap<>();
        // 根据代理合同ids查询名称
        Map<Long, String> agentNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkTableIds)) {
            agentContractByIds = contractService.findContractNumByAgentContractIds(fkTableIds);
            agentNamesByIds = agentService.getAgentNamesByIds(fkTableIds);
        }

        Map<String, String> companyMap = getCompanyMap();
        Set<Long> contactIds = contactPersonVos.stream().map(ContactPersonVo::getId).collect(Collectors.toSet());
        // 根据联系人ids查询公司ids
        Map<Long, Set<Long>> relationByContactIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(contactIds)) {
            relationByContactIds = personCompanyService.getRelationByContactIds(contactIds);
        }

        for (ContactPersonVo contactPersonDto : contactPersonVos) {
            // 设置目标名称
            setTargetName(contactPersonDto, agentContractByIds, agentNamesByIds, companyMap, relationByContactIds);
            if (GeneralTool.isNotEmpty(contactPersonDto.getEmail())) {
                contactPersonDto.setAgentEmailLabelVos(agentEamailLabelMap.get(contactPersonDto.getEmail()));
            }
        }
        return contactPersonVos;
    }

    @Override
    public ContactPersonVo finContactPersonById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        SaleContactPerson contactPerson = personMapper.selectById(id);
        ContactPersonVo contactPersonVo = BeanCopyUtils.objClone(contactPerson, ContactPersonVo::new);
        if (GeneralTool.isNotEmpty(contactPersonVo)) {
            // 设置联系人属性
            List<ContactPersonVo> contactPersonVoList = new ArrayList<>();
            contactPersonVoList.add(contactPersonVo);
            setContactPersonList(contactPersonVoList);

            Set<Long> fkTableIds = new HashSet<>();
            fkTableIds.add(contactPersonVo.getFkTableId());
            // 根据代理合同ids查询合同编号
            Map<Long, String> agentContractByIds = new HashMap<>();
            // 根据代理合同ids查询名称
            Map<Long, String> agentNamesByIds = new HashMap<>();
            if (GeneralTool.isNotEmpty(fkTableIds)) {
                agentContractByIds = contractService.findContractNumByAgentContractIds(fkTableIds);
                agentNamesByIds = agentService.getAgentNamesByIds(fkTableIds);
            }

            Map<String, String> companyMap = getCompanyMap();
            Set<Long> contactIds = new HashSet<>();
            contactIds.add(contactPersonVo.getId());
            // 根据联系人ids查询公司ids
            Map<Long, Set<Long>> relationByContactIds = new HashMap<>();
            if (GeneralTool.isNotEmpty(contactIds)) {
                relationByContactIds = personCompanyService.getRelationByContactIds(contactIds);
            }

            setTargetName(contactPersonVo, agentContractByIds, agentNamesByIds, companyMap, relationByContactIds);

            String fkAreaCountryIdsNews = contactPersonVo.getFkAreaCountryIdsNews();
            if (GeneralTool.isNotEmpty(fkAreaCountryIdsNews)) {
                List<String> countryIds = Arrays.asList(fkAreaCountryIdsNews.split(","));
                Set<Long> cids = countryIds.stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toSet());
                Map<Long, String> countryMap = institutionCenterClient.getCountryChnNameByIds(cids).getData();
                ArrayList<String> countryNameList = new ArrayList<>(countryMap.values());
                String countryNames = StringUtils.join(countryNameList, ",");
                contactPersonVo.setFkAreaCountryNamesNews(countryNames);
            }

        }
        return contactPersonVo;
    }

    @Override
    public Long addContactPerson(ContactPersonDto contactPersonVo) {
        // 转回html特殊字符
        MyStringUtils.UnescapeHtml(contactPersonVo);
        SaleContactPerson contactPerson = createContactPerson(contactPersonVo);
        // 添加到中间表
        Long companyId;
        if (GeneralTool.isNotEmpty(contactPersonVo.getFkCompanyId())) {
            companyId = contactPersonVo.getFkCompanyId();
        } else {
            companyId = SecureUtil.getFkCompanyId();
        }
        ContactPersonCompanyDto relation = new ContactPersonCompanyDto();
        relation.setFkCompanyId(companyId);
        relation.setFkContactPersonId(contactPerson.getId());
        personCompanyService.addRelation(relation);
        return contactPerson.getId();
    }

    @Override
    public ContactPersonVo updateContactPerson(ContactPersonDto contactPersonVo) {
        this.agentService.validateAddContractPerson(contactPersonVo);

        if (GeneralTool.isEmpty(contactPersonVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        // 转回html特殊字符
        MyStringUtils.UnescapeHtml(contactPersonVo);
        SaleContactPerson contactPerson = BeanCopyUtils.objClone(contactPersonVo, SaleContactPerson::new);
        utilService.updateUserInfoToEntity(contactPerson);
        personMapper.updateById(contactPerson);
        // 有ISSUE关联帐号，需要对m_user信息同步
        List<UserContactPerson> userContactPeople = userContactPersonMapper.selectList(Wrappers
                .<UserContactPerson>lambdaQuery().eq(UserContactPerson::getFkContactPersonId, contactPersonVo.getId()));
        if (GeneralTool.isNotEmpty(userContactPeople)) {
            List<Long> userIds = userContactPeople.stream().map(UserContactPerson::getFkUserId)
                    .collect(Collectors.toList());
            contactPersonVo.setFkUserIds(userIds);
            registrationCenterClient.updateIssueContactPerson(contactPersonVo);

            // 如和ISSUE用户关联，则需要更新ISSUE的权限角色
            String fkContactPersonTypeKey = "CONTACT_AGENT_SALES";
            // 如联系人角色不为空，则按权限最高的角色授权
            // CONTACT_AGENT_VP、CONTACT_AGENT_FINANCE、CONTACT_AGENT_SALES
            if (GeneralTool.isNotEmpty(contactPersonVo.getFkContactPersonTypeKey())) {
                if (contactPersonVo.getFkContactPersonTypeKey().contains("CONTACT_AGENT_VP")) {
                    fkContactPersonTypeKey = "CONTACT_AGENT_VP";
                } else if (contactPersonVo.getFkContactPersonTypeKey().contains("CONTACT_AGENT_FINANCE")) {
                    fkContactPersonTypeKey = "CONTACT_AGENT_FINANCE";
                }
            }
            // TODO 无用库
            // Boolean result =
            // platformConfigCenterClient.updateIssueUserContactPersonTypeKeyByPersonId(contactPerson.getId(),fkContactPersonTypeKey);
            // if(result == null || !result)
            // {
            // log.error("更新ISSUE联系人角色类型失败，联系人ID："+contactPerson.getId());
            // }
        }
        return finContactPersonById(contactPerson.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteContactPerson(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        // 判断申请方案是否有数据关联(取消，关联字段已经删除)
        SaleContactPerson saleContactPerson = personMapper.selectById(id);
        if (GeneralTool.isNotEmpty(saleContactPerson)) {
            // LambdaQueryWrapper<StudentOffer> lambdaQueryWrapper = new
            // LambdaQueryWrapper();
            // lambdaQueryWrapper.eq(StudentOffer::getFkContactPersonId,id);
            // List<StudentOffer> studentOffers =
            // studentOfferMapper.selectList(lambdaQueryWrapper);
            // if (GeneralTool.isNotEmpty(studentOffers)){
            // throw new GetServiceException("已有申请方案绑定该代理联系人，不可删除！");
            // }
        }
        personMapper.deleteById(id);
        personCompanyService.deletePersonRelation(id);
    }

    @Override
    public void editContactPersonCompany(List<ContactPersonCompanyDto> validList) {
        personCompanyService.editContactPersonCompany(validList);
    }

    @Override
    public List<CompanyTreeVo> getContactRelation(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        return companyRelationService.getContactCompanyRelation(id);
    }

    @Override
    public List<Map<String, Object>> findTargetType() {
        return TableEnum.enumsTranslation2Arrays(TableEnum.SALE_TYPE);
    }

    private List<ContactPersonTypeVo> getContactPersonType() {
        return contactPersonTypeService.getContactPersonTypes();
    }

    @Override
    public void addAgentContactPerson(List<ContactPersonDto> contactPersonVo) {
        if (GeneralTool.isEmpty(contactPersonVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        List<SaleContactPerson> personList = contactPersonVo.stream()
                .map(c -> BeanCopyUtils.objClone(c, SaleContactPerson::new)).collect(Collectors.toList());
        List<Long> companyIds = agentCompanyService.getRelationByAgentId(contactPersonVo.get(0).getFkTableId());
        List<ContactPersonCompanyDto> list = new ArrayList<>();
        for (SaleContactPerson person : personList) {
            utilService.setCreateInfo(person);
            personMapper.insert(person);
            if (GeneralTool.isNotEmpty(companyIds)) {
                // 添加中间表
                for (Long companytId : companyIds) {
                    ContactPersonCompanyDto relation = new ContactPersonCompanyDto();
                    relation.setFkCompanyId(companytId);
                    relation.setFkContactPersonId(person.getId());
                    list.add(relation);
                }
            }
        }
        if (!CollectionUtils.isEmpty(list)) {
            personCompanyService.addBatchRelation(list);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addAgentContactPerson(ContactPersonDto contactPersonVo) {
        SaleContactPerson contactPerson = createContactPerson(contactPersonVo);
        List<Long> companyIds = agentCompanyService.getRelationByAgentId(contactPersonVo.getFkTableId());
        // 跟随提供商的公司关系
        if (GeneralTool.isNotEmpty(companyIds)) {
            // 添加中间表
            for (Long companytId : companyIds) {
                ContactPersonCompanyDto relation = new ContactPersonCompanyDto();
                relation.setFkCompanyId(companytId);
                relation.setFkContactPersonId(contactPerson.getId());
                personCompanyService.addRelation(relation);
            }
        }
        return contactPerson.getId();
    }

    private SaleContactPerson createContactPerson(ContactPersonDto contactPersonVo) {
        if (GeneralTool.isEmpty(contactPersonVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }

        SaleContactPerson contactPerson = BeanCopyUtils.objClone(contactPersonVo, SaleContactPerson::new);
        utilService.setCreateInfo(contactPerson);
        // int i = personMapper.insertSelective(contactPerson);
        int i = personMapper.insert(contactPerson);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
        return contactPerson;
    }

    private void setTargetName(ContactPersonVo contactPersonVo, Map<Long, String> agentContractByIds,
            Map<Long, String> agentNamesByIds, Map<String, String> companyMap,
            Map<Long, Set<Long>> relationByContactIds) {
        if (GeneralTool.isNotEmpty(contactPersonVo)) {
            if (TableEnum.SALE_CONTRACT.key.equals(contactPersonVo.getFkTableName())) {
                contactPersonVo.setTargetName(agentContractByIds.get(contactPersonVo.getFkTableId()));
            }
            if (TableEnum.SALE_AGENT.key.equals(contactPersonVo.getFkTableName())) {
                contactPersonVo.setTargetName(agentNamesByIds.get(contactPersonVo.getFkTableId()));
            }
            // 设置目标类型（表名）
            contactPersonVo.setTargetTypeName(TableEnum.getValue(contactPersonVo.getFkTableName()));
            // 设置公司名字
            setCompanyName(contactPersonVo, companyMap, relationByContactIds);
        }
    }

    /**
     * 设置属性
     *
     * @param contactPersonVos
     * @
     */
    private void setContactPersonList(List<ContactPersonVo> contactPersonVos) {
        // List<ContactPersonTypeDto> contactPersonType = getContactPersonType();
        // //当联系人类型不为空
        // if (GeneralTool.isNotEmpty(contactPersonType) &&
        // GeneralTool.isNotEmpty(contactPersonVos)) {
        // //遍历设置属性
        // contactPersonVos.forEach(contactPersonDto ->
        // contactPersonDto.setContactPersonTypeName(getContactType(contactPersonDto.getFkContactPersonTypeKey(),
        // contactPersonType)));
        // }

        List<ContactPersonType> contactPersonTypes = contactPersonTypeMapper
                .selectList(Wrappers.<ContactPersonType>lambdaQuery());
        // 当联系人类型不为空
        if (GeneralTool.isNotEmpty(contactPersonTypes) && GeneralTool.isNotEmpty(contactPersonVos)) {
            // 遍历设置属性
            contactPersonVos.forEach(contactPersonDto -> contactPersonDto.setContactPersonTypeName(
                    getContactType(contactPersonDto.getFkContactPersonTypeKey(), contactPersonTypes)));
        }
    }

    /**
     * 获取联系人类型
     *
     * @param contactPersonType
     * @param typeList
     * @return
     */
    private List<String> getContactType(String contactPersonType, List<ContactPersonType> typeList) {
        if (GeneralTool.isEmpty(contactPersonType)) {
            return null;
        }
        String[] typeSplit = contactPersonType.split(",");
        // 转成流对象
        Stream<String> stream = Arrays.stream(typeSplit);
        // 获取类型
        List<String> collect = stream.map(s -> typeList.stream()
                .filter(contactPersonTypeDto -> contactPersonTypeDto.getTypeKey().equals(s))
                .findFirst()
                .map(ContactPersonType::getTypeName).orElse(null))
                .collect(Collectors.toList());
        collect.removeIf(Objects::isNull);
        return collect;
    }

    private List<Long> getCompanyId(List<Long> companyId) {
        List<Long> companyIds = SecureUtil.getCompanyIdsByStaffId(GetAuthInfo.getStaffId());
        companyIds.removeIf(Objects::isNull);
        List<Long> relation = personCompanyService.getRelationByCompanyId(companyId);
        if (GeneralTool.isEmpty(relation)) {
            relation = new ArrayList<>();
            relation.add(0L);
        }
        return relation;
    }

    private void setCompanyName(ContactPersonVo contactPersonVo, Map<String, String> companyMap,
            Map<Long, Set<Long>> relationByContactIds) {
        if (GeneralTool.isNotEmpty(contactPersonVo)) {
            StringBuilder builder = new StringBuilder();
            Set<Long> companyIds = relationByContactIds.get(contactPersonVo.getId());
            if (GeneralTool.isNotEmpty(companyIds)) {
                for (Long companyId : companyIds) {
                    String name = companyMap.get(String.valueOf(companyId));
                    builder.append(name).append("，");
                }
                contactPersonVo.setCompanyName(sub(builder));
            }
        }
    }

    private String sub(StringBuilder sb) {
        if (GeneralTool.isEmpty(sb)) {
            return null;
        }
        String substring = null;
        int i = sb.lastIndexOf("，");
        if (i != -1) {
            substring = sb.substring(0, i);
        }
        return substring;
    }

    private Map<String, String> getCompanyMap() {
        Map<String, String> companyMap = new HashMap<>(5);
        Result<List<com.get.permissioncenter.vo.tree.CompanyTreeVo>> result = permissionCenterClient.getAllCompanyDto();
        if (result.isSuccess() && CollectionUtil.isNotEmpty(result.getData())) {
            JsonConfig config = new JsonConfig();
            config.setExcludes(new String[] { "departmentTree", "totalNum" });
            JSONArray jsonArray = JSONArray.fromObject(result.getData(), config);
            List<CompanyTreeVo> companyTreeVos = JSONArray.toList(jsonArray, new CompanyTreeVo(), new JsonConfig());
            // 初始为5的map
            if (GeneralTool.isNotEmpty(companyTreeVos)) {
                companyMap = companyTreeVos.stream()
                        .collect(Collectors.toMap(CompanyTreeVo::getId, CompanyTreeVo::getShortName));
            }
        }
        return companyMap;
    }

    // 内部调用
    @Override
    public List<SaleContactPerson> getContactPersonByFkTableId(String fkTableName, Long fkTableId) {
        // Example example = new Example(ContactPerson.class);
        // example.createCriteria().andEqualTo("fkTableName",
        // fkTableName).andEqualTo("fkTableId", fkTableId);
        // List<ContactPerson> contactPeople = personMapper.selectByExample(example);
        List<SaleContactPerson> contactPeople = personMapper.selectList(Wrappers.<SaleContactPerson>lambdaQuery()
                .eq(SaleContactPerson::getFkTableName, fkTableName)
                .eq(SaleContactPerson::getFkTableId, fkTableId));
        return contactPeople;
    }

    @Override
    public Map<Long, String> getContactPersonEmail(Set<Long> ids) {
        if (GeneralTool.isNotEmpty(ids)) {
            // Example example = new Example(ContactPerson.class);
            // example.createCriteria().andIn("id", ids);
            List<SaleContactPerson> contactPeoples = personMapper.selectBatchIds(ids);
            Map data = new HashMap();
            if (GeneralTool.isNotEmpty(contactPeoples)) {
                for (SaleContactPerson contactPerson : contactPeoples) {
                    data.put(contactPerson.getId(), contactPerson.getEmail());
                }
            }

            return data;
        }
        return null;
    }

    @Override
    public Map<Long, String> getContactPersonEmailByFkTableIdAndName(Set<Long> ids, String fkTableName) {
        if (GeneralTool.isNotEmpty(ids)) {
            // Example example = new Example(ContactPerson.class);
            // example.createCriteria().andIn("fkTableId", ids);
            // example.createCriteria().andEqualTo("fkTableName", fkTableName);
            List<SaleContactPerson> contactPeoples = personMapper.selectList(Wrappers.<SaleContactPerson>lambdaQuery()
                    .in(SaleContactPerson::getFkTableId, ids).eq(SaleContactPerson::getFkTableName, fkTableName));
            Map data = new HashMap();
            if (GeneralTool.isNotEmpty(contactPeoples)) {
                for (SaleContactPerson contactPerson : contactPeoples) {
                    // 如非空，则添加，否则直接put
                    if (GeneralTool.isNotEmpty(data.get(contactPerson.getFkTableId()))) {
                        String value = data.get(contactPerson.getFkTableId()) + "," + contactPerson.getEmail();
                        data.put(contactPerson.getId(), value);
                    } else {
                        data.put(contactPerson.getId(), contactPerson.getEmail());
                    }

                }
            }
            return data;
        }
        return null;
    }

    @Override
    public List<BaseSelectEntity> getContactPersonEmailSelect(Long fkAgentId) {
        if (GeneralTool.isEmpty(fkAgentId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        return personMapper.getContactPersonEmailSelect(TableEnum.SALE_AGENT.key, fkAgentId);
    }

    @Override
    public String getExistContactPersonPM(Long id, String email, String tel, String mobile, Long companyId) {
        StringBuilder sb = new StringBuilder();
        List<ContactPersonVo> emailList = null;
        if (GeneralTool.isNotEmpty(email)) {
            emailList = personMapper.getExistContactPersonPM(email, null, null, companyId);
        }
        List<ContactPersonVo> tellList = null;
        if (GeneralTool.isNotEmpty(tel)) {
            tellList = personMapper.getExistContactPersonPM(null, tel, null, companyId);
        }
        List<ContactPersonVo> mobileList = null;
        if (GeneralTool.isNotEmpty(mobile)) {
            mobileList = personMapper.getExistContactPersonPM(null, null, mobile, companyId);
        }
        if (GeneralTool.isNotEmpty(mobileList)) {
            // if (GeneralTool.isNotEmpty(id)) {
            // mobileList.stream().filter(d -> !d.getId().equals(id)).forEach(dd ->
            // sb.append(LocaleMessageUtils.getMessage("AGENT")).append("：")
            // .append(dd.getName()).append("，").append(LocaleMessageUtils.getMessage("agent_company")).append("：").append(dd.getCompanyName()).append("，").append(LocaleMessageUtils.getMessage("CONTACT_PHONE")).append("：").append(dd.getMobile())
            // .append("，").append(LocaleMessageUtils.getMessage("STUDENT_EXIST")).append("！<br/>"));
            // } else {
            // mobileList.stream().forEach(dd ->
            // sb.append(LocaleMessageUtils.getMessage("AGENT")).append("：")
            // .append(dd.getName()).append("，").append(LocaleMessageUtils.getMessage("agent_company")).append("：").append(dd.getCompanyName()).append("，").append(LocaleMessageUtils.getMessage("CONTACT_PHONE")).append("：").append(dd.getMobile())
            // .append("，").append(LocaleMessageUtils.getMessage("STUDENT_EXIST")).append("！<br/>"));
            // }
            appendDuplicateInfo(mobileList, id, sb, "CONTACT_PHONE", ContactPersonVo::getMobile);
        }
        if (GeneralTool.isNotEmpty(emailList)) {
            // if (GeneralTool.isNotEmpty(id)) {
            // emailList.stream().filter(d -> !d.getId().equals(id)).forEach(dd ->
            // sb.append(LocaleMessageUtils.getMessage("AGENT")).append("：")
            // .append(dd.getName()).append("，").append(LocaleMessageUtils.getMessage("agent_company")).append("：").append(dd.getCompanyName()).append("，").append(LocaleMessageUtils.getMessage("CONTACT_EMAIL")).append("：").append(dd.getEmail())
            // .append("，").append(LocaleMessageUtils.getMessage("STUDENT_EXIST")).append("！<br/>"));
            // } else {
            // emailList.stream().forEach(dd ->
            // sb.append(LocaleMessageUtils.getMessage("AGENT")).append("：")
            // .append(dd.getName()).append("，").append(LocaleMessageUtils.getMessage("agent_company")).append("：").append(dd.getCompanyName()).append("，").append(LocaleMessageUtils.getMessage("CONTACT_EMAIL")).append("：").append(dd.getEmail())
            // .append("，").append(LocaleMessageUtils.getMessage("STUDENT_EXIST")).append("！<br/>"));
            // }

            appendDuplicateInfo(emailList, id, sb, "CONTACT_EMAIL", ContactPersonVo::getEmail);
        }
        if (GeneralTool.isNotEmpty(tellList)) {
            // if (GeneralTool.isNotEmpty(id)) {
            // tellList.stream().filter(d -> !d.getId().equals(id)).forEach(dd ->
            // sb.append(LocaleMessageUtils.getMessage("AGENT")).append("：")
            // .append(dd.getName()).append("，").append(LocaleMessageUtils.getMessage("agent_company")).append("：").append(dd.getCompanyName()).append("，").append(LocaleMessageUtils.getMessage("CONTACT_TEL")).append("：").append(dd.getTel())
            // .append("，").append(LocaleMessageUtils.getMessage("STUDENT_EXIST")).append("！<br/>"));
            // } else {
            // tellList.stream().forEach(dd ->
            // sb.append(LocaleMessageUtils.getMessage("AGENT")).append("：")
            // .append(dd.getName()).append("，").append(LocaleMessageUtils.getMessage("agent_company")).append("：").append(dd.getCompanyName()).append("，").append(LocaleMessageUtils.getMessage("CONTACT_TEL")).append("：").append(dd.getTel())
            // .append("，").append(LocaleMessageUtils.getMessage("STUDENT_EXIST")).append("！<br/>"));
            // }

            appendDuplicateInfo(tellList, id, sb, "CONTACT_TEL", ContactPersonVo::getTel);

        }
        return sb.toString();
    }

    private void appendDuplicateInfo(List<? extends ContactPersonVo> list,
            Long currentId,
            StringBuilder sb,
            String contactTypeKey,
            Function<ContactPersonVo, String> contactValueGetter) {
        if (GeneralTool.isNotEmpty(list)) {
            Stream<? extends ContactPersonVo> stream = list.stream();
            if (GeneralTool.isNotEmpty(currentId)) {
                stream = stream.filter(d -> !d.getId().equals(currentId));
            }
            stream.forEach(item -> {
                sb.append(LocaleMessageUtils.getMessage("AGENT")).append("：")
                        .append(item.getName())
                        .append("，").append(LocaleMessageUtils.getMessage("agent_company")).append("：")
                        .append(item.getCompanyName())
                        .append("，").append(LocaleMessageUtils.getMessage(contactTypeKey)).append("：")
                        .append(contactValueGetter.apply(item))
                        .append("，").append(LocaleMessageUtils.getMessage("STUDENT_EXIST")).append("！<br/>");
            });

            // 按名称和联系方式分组，合并公司名称
            // Map<String, Map<String, Set<String>>> groupedMap = stream
            // .collect(Collectors.groupingBy(
            // item -> item.getName() + "|" + contactValueGetter.apply(item), //
            // 主分组键：名称+联系方式
            // Collectors.groupingBy(ContactPersonVo::getCompanyName, // 嵌套分组：按公司去重
            // Collectors.mapping(ContactPersonVo::getCompanyName, Collectors.toSet()))
            // ));
            //
            // groupedMap.forEach((key, companiesMap) -> {
            // String[] parts = key.split("\\|");
            // String agentName = parts[0];
            // String contactValue = parts[1];
            //
            // // 合并不同公司名称
            // String mergedCompanies = String.join("，", companiesMap.keySet());
            //
            // sb.append(LocaleMessageUtils.getMessage("AGENT")).append("：")
            // .append(agentName)
            // .append("，").append(LocaleMessageUtils.getMessage("agent_company")).append("：").append(mergedCompanies)
            // .append("，").append(LocaleMessageUtils.getMessage(contactTypeKey)).append("：").append(contactValue)
            // .append("，").append(LocaleMessageUtils.getMessage("STUDENT_EXIST")).append("！<br/>");
            // });
        }
    }

    /**
     * 根据联系人ids获取 联系人emali Map
     *
     * @Date 14:58 2022/3/12
     * <AUTHOR>
     */
    @Override
    public Map<Long, String> getContactPersonEmailById(Set<Long> ids) {
        List<SaleContactPerson> contactPeople = personMapper.selectBatchIds(ids);
        Map<Long, String> map = new HashMap();
        for (SaleContactPerson contactPerson : contactPeople) {
            map.put(contactPerson.getId(), contactPerson.getEmail());
        }
        return map;
    }

    @Override
    public String getExistContactPersonEmailPM(String email) {
        StringBuilder sb = new StringBuilder();
        // List<ContactPersonDto> emailList =
        // personMapper.getExistContactPersonPM(email, null, null, companyId);
        List<SaleContactPerson> emailList = personMapper
                .selectList(Wrappers.<SaleContactPerson>lambdaQuery().eq(SaleContactPerson::getEmail, email));
        if (GeneralTool.isNotEmpty(emailList)) {
            sb.append(LocaleMessageUtils.getMessage("CONTACT_EMAIL"))
                    .append(LocaleMessageUtils.getMessage("STUDENT_EXIST"));
        }

        return sb.toString();
    }

    @Override
    public List<SaleContactPerson> getContactPersonByCondition(
            LambdaQueryWrapper<SaleContactPerson> lambdaQueryWrapper) {
        return personMapper.selectList(lambdaQueryWrapper);
    }

    @Override
    public List<ContactPersonVo> getContactPersonInfo(Set<Long> agentIds) {
        List<ContactPersonVo> contactPersonInfos = personMapper.getContactPersonInfo(agentIds);

        if (GeneralTool.isNotEmpty(contactPersonInfos)) {
            List<Long> contactPersonIds = contactPersonInfos.stream().map(ContactPersonVo::getId)
                    .collect(Collectors.toList());
            if (GeneralTool.isNotEmpty(contactPersonIds)) {
                List<UserContactPerson> userContactPeoples = userContactPersonMapper
                        .selectList(Wrappers.<UserContactPerson>lambdaQuery()
                                .in(UserContactPerson::getFkContactPersonId, contactPersonIds));
                if (GeneralTool.isNotEmpty(userContactPeoples)) {
                    contactPersonInfos.stream().forEach(contactPersonDto -> {
                        UserContactPerson userContactPerson = userContactPeoples.stream()
                                .filter(item -> item.getFkContactPersonId().equals(contactPersonDto.getId()))
                                .findFirst().orElse(null);
                        if (GeneralTool.isNotEmpty(userContactPerson)) {
                            contactPersonDto.setFkUserId(userContactPerson.getFkUserId());
                        }
                    });
                }
            }
            List<Long> userIds = contactPersonInfos.stream().map(ContactPersonVo::getFkUserId).filter(Objects::nonNull)
                    .collect(Collectors.toList());
            if (GeneralTool.isNotEmpty(userIds)) {
                Result<List<User>> usersResult = registrationCenterClient.getUsersByIds(userIds);
                if (usersResult.isSuccess() && GeneralTool.isNotEmpty(usersResult.getData())) {
                    contactPersonInfos.stream().forEach(contactPersonDto -> {
                        User user = usersResult.getData().stream()
                                .filter(item -> item.getId().equals(contactPersonDto.getFkUserId())).findFirst()
                                .orElse(null);
                        if (GeneralTool.isNotEmpty(user)) {
                            contactPersonDto.setLoginId(user.getLoginId());
                            contactPersonDto.setFkPlatformType(user.getFkPlatformType());
                        }
                    });
                }
            }
            for (ContactPersonVo contactPersonVo : contactPersonInfos) {
                if (GeneralTool.isNotEmpty(contactPersonVo.getFkUserId())) {
                    contactPersonVo.setMobile(contactPersonVo.getLoginId());
                }
                if (GeneralTool.isNotEmpty(contactPersonVo.getFkPlatformType())) {
                    if (contactPersonVo.getFkPlatformType().equals(ProjectKeyEnum.GET_BMS.key)) {
                        contactPersonVo.setFkPlatformType("AIS注册");
                    } else if (contactPersonVo.getFkPlatformType().equals(ProjectKeyEnum.GET_ISSUE.key)) {
                        contactPersonVo.setFkPlatformType("ISSUE注册");
                    } else if (contactPersonVo.getFkPlatformType().equals(ProjectKeyEnum.GET_MSO.key)) {
                        contactPersonVo.setFkPlatformType("MSO注册");
                    } else if (contactPersonVo.getFkPlatformType().equals(ProjectKeyEnum.GET_IB.key)) {
                        contactPersonVo.setFkPlatformType("IBS注册");
                    } else if (contactPersonVo.getFkPlatformType().equals(ProjectKeyEnum.GET_MP_EXAM.key)) {
                        contactPersonVo.setFkPlatformType("GET_MP_EXAM注册");
                    } else if (contactPersonVo.getFkPlatformType().equals(ProjectKeyEnum.GET_HKISO.key)) {
                        contactPersonVo.setFkPlatformType("GTE_HKISO_EXAM注册");
                    }
                }
            }
        }
        return contactPersonInfos;
        // return null;
    }

    @Override
    public List<BaseSelectEntity> getContactPersonMobileAreaCodeSelect(Long fkAgentId) {
        if (GeneralTool.isEmpty(fkAgentId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        return personMapper.getContactPersonMobileAreaCodeSelect(TableEnum.SALE_AGENT.key, fkAgentId);
    }

    @Override
    public List<ContactPersonMobileSelectVo> getContactPersonMobileSelect(Long fkAgentId) {
        if (GeneralTool.isEmpty(fkAgentId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        return personMapper.getContactPersonMobileSelect(TableEnum.SALE_AGENT.key, fkAgentId);
    }

    /**
     * 获取表关联已使用的新联系人类型
     *
     * @param fkTableId 表ID
     * @return 已使用的新联系人类型集合
     */
    @Override
    public Set<String> getUsedNewTypeKeysByTableId(Long fkTableId) {
        // 查询表关联的所有联系人
        List<SaleContactPerson> contactPersons = personMapper.selectList(
                Wrappers.<SaleContactPerson>lambdaQuery()
                        .eq(SaleContactPerson::getFkTableId, fkTableId));

        if (CollectionUtil.isEmpty(contactPersons)) {
            return Collections.emptySet();
        }

        // 判断是否选择过新的联系人类型
        return getAllNewType(contactPersons);
    }

    /**
     * 获取表关联已使用的新联系人类型（排除指定联系人）
     *
     * @param fkTableId 表ID
     * @param excludeContactPersonId 要排除的联系人ID
     * @return 已使用的新联系人类型集合
     */
    @Override
    public Set<String> getUsedNewTypeKeysByTableId(Long fkTableId, Long excludeContactPersonId) {
        // 查询表关联的所有联系人，排除指定联系人
        LambdaQueryWrapper<SaleContactPerson> queryWrapper = Wrappers.<SaleContactPerson>lambdaQuery()
                .eq(SaleContactPerson::getFkTableId, fkTableId);
        
        // 如果需要排除指定联系人，则添加排除条件
        if (excludeContactPersonId != null) {
            queryWrapper.ne(SaleContactPerson::getId, excludeContactPersonId);
        }
        
        List<SaleContactPerson> contactPersons = personMapper.selectList(queryWrapper);

        if (CollectionUtil.isEmpty(contactPersons)) {
            return Collections.emptySet();
        }

        // 判断是否选择过新的联系人类型
        return getAllNewType(contactPersons);
    }

    /**
     * 获取联系人存在什么新的联系人类型
     *
     * @param contactPersons
     * @return
     */
    private Set<String> getAllNewType(List<SaleContactPerson> contactPersons) {
        Set<String> usedTypeKeys = contactPersons.stream()
                .filter(person -> GeneralTool.isNotEmpty(person.getFkContactPersonTypeKey()))
                .flatMap(person -> Arrays.stream(person.getFkContactPersonTypeKey().split(",")))
                .map(String::trim)
                .filter(ContactPersonTypeEnum::isNewContactPersonType)
                .collect(Collectors.toSet());
        return usedTypeKeys;
    }

}

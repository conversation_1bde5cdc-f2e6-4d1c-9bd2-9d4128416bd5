package com.get.salecenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.core.mybatis.utils.ValidList;
import com.get.institutioncenter.vo.AreaCityVo;
import com.get.institutioncenter.vo.AreaCountryVo;
import com.get.institutioncenter.vo.AreaStateVo;
import com.get.permissioncenter.vo.StaffVo;
import com.get.salecenter.dto.CommentDto;
import com.get.salecenter.dto.EventCostDto;
import com.get.salecenter.dto.EventDto;
import com.get.salecenter.dto.EventRegistrationStatisticsDto;
import com.get.salecenter.dto.MediaAndAttachedDto;
import com.get.salecenter.dto.query.EventQueryDto;
import com.get.salecenter.entity.Event;
import com.get.salecenter.vo.CommentVo;
import com.get.salecenter.vo.EventDataVo;
import com.get.salecenter.vo.EventRegistrationStatisticsVo;
import com.get.salecenter.vo.EventTypeVo;
import com.get.salecenter.vo.EventVo;
import com.get.salecenter.vo.MediaAndAttachedVo;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
 * @author: Sea
 * @create: 2020/12/7 15:13
 * @verison: 1.0
 * @description:
 */
public interface IEventService extends IService<Event> {
    /**
     * @return com.get.salecenter.vo.EventVo
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    EventVo findEventById(Long id);

    /**
     * @return void
     * @Description :新增
     * @Param [eventDto]
     * <AUTHOR>
     */
    Long addEvent(EventDto eventDto);

    /**
     * @return void
     * @Description :删除
     * @Param [id]
     * <AUTHOR>
     */
    void delete(Long id);

    /**
     * @return com.get.salecenter.vo.EventVo
     * @Description :修改
     * @Param [eventDto]
     * <AUTHOR>
     */
    EventVo updateEvent(EventDto eventDto);

    /**
     * @return java.util.List<com.get.salecenter.vo.EventVo>
     * @Description :列表
     * @Param [eventVo, page]
     * <AUTHOR>
     */
    List<EventVo> getEvents(EventQueryDto eventQueryVo, Page page);

    /**
     * @return void
     * @Description: 导出活动汇总excel
     * @Param [response, eventVo]
     * <AUTHOR>
     */
    void exportEventExcel(HttpServletResponse response, EventQueryDto eventQueryVo);

    /**
     * @return java.util.List<java.lang.String>
     * @Description :获取院校/集团/项目 下拉框数据
     * @Param [companyId]
     * <AUTHOR>
     */
    List<String> getEventTargetList(Long companyId);

    /**
     * @return java.util.List<java.lang.String>
     * @Description :获取活动主题 下拉框数据
     * @Param [companyId]
     * <AUTHOR>
     */
    List<String> getEventThemeList(Long companyId);

    /**
     * @return java.lang.Long
     * @Description: 增加或者更新评论
     * @Param [commentDto]
     * <AUTHOR>
     **/
    Long editComment(CommentDto commentDto);

    /**
     * @return java.util.List<com.get.salecenter.vo.CommentVo>
     * @Description: 获取所有评论
     * @Param [commentDto, page]
     * <AUTHOR>
     */
    List<CommentVo> getComments(CommentDto commentDto, Page page);

    /**
     * @return java.util.List<com.get.salecenter.vo.MediaAndAttachedDto>
     * @Description: 附件列表
     * @Param [data, page]
     * <AUTHOR>
     */
    List<MediaAndAttachedVo> getItemMedia(MediaAndAttachedDto data, Page page);

    /**
     * @return java.lang.Long
     * @Description: 新增附件
     * @Param [mediaAttachedVo]
     * <AUTHOR>
     */
    List<MediaAndAttachedVo> addItemMedia(ValidList<MediaAndAttachedDto> mediaAttachedVo);

    /**
     * @return void
     * @Description :结束
     * @Param [eventId]
     * <AUTHOR>
     */
    void end(Long eventId);

    /**
     * @return void
     * @Description :取消
     * @Param [eventId]
     * <AUTHOR>
     */
    void cancel(Long eventId);

    /**
     * @return java.util.List<com.get.salecenter.vo.EventVo>
     * @Description : 活动数据总览
     * @Param [data, page]
     * <AUTHOR>
     */
    List<EventDataVo> getEventDatas(EventQueryDto eventQueryDto, Page page);

    /**
     * @return java.util.List<com.get.salecenter.vo.EventVo>
     * @Description : 活动数据总览列表
     * @Param [data, page]
     * <AUTHOR>
     */
    List<EventDataVo> getEventDatasList(EventQueryDto eventQueryDto, Page page);

    /**
     * 城市下拉框
     *
     * @param companyId
     * @param eventStateIds
     * @return
     */
    List<AreaCityVo> getCitySelect(Long companyId, List<Long> eventStateIds);

    /**
     * @return
     * @Description :活动举办区域下拉框
     * @Param [companyId]
     * <AUTHOR>
     */
    List<AreaStateVo> getEventStateList(Long companyId);

    /**
     * @return
     * @Description :活动国家下拉框
     * @Param [companyId]
     * <AUTHOR>
     */
    List<AreaCountryVo> getEventCountryList(Long companyId);

    /**
     * @return
     * @Description :活动负责人下拉框
     * @Param [companyId]
     * <AUTHOR>
     */
    List<StaffVo> getEventStaffList(Long companyId);


    /**
     * @return
     * @Description :活动第二负责人下拉框
     * @Param [companyId]
     * <AUTHOR>
     */
    List<StaffVo> getEventStaff2List(Long companyId);

    /**
     * @return
     * @Description :活动类型下拉框
     * @Param [companyId]
     * <AUTHOR>
     */
    List<EventTypeVo> getEventTypeSelect(Long companyId);

    /**
     * 活动举办城市下拉框
     *
     * @param companyId
     * @return
     */
    List<AreaCityVo> getEventCitySelect(Long companyId);

    /**
     * @return void
     * @Description :计划
     * @Param [eventId]
     * <AUTHOR>
     */
    void plan(Long eventId);

    /**
     * @return void
     * @Description :延期
     * @Param [eventId]
     * <AUTHOR>
     */
    void postpone(Long eventId);

    /**
     * 活动费用列表
     *
     * @param eventCostDto
     * @param page
     * @return
     */
    List<EventVo> getCostList(EventCostDto eventCostDto, Page page);

    /**
     * 活动名称模糊搜索
     *
     * @param companyId
     * @param eventName
     * @return
     */
    List<EventVo> getEventsByName(Long companyId, String eventName);


    Event getEventById(Long fkEventId);

    /**
     * 修改接口（免评价）
     * @param eventDto
     * @return
     */
    EventVo updateEventWithoutRemark(EventDto eventDto);

    /**
     * 结束免评价
     * @param eventId
     */
    void endWithoutRemark(Long eventId);

    /**
     * 活动下拉
     * @param companyId
     * @return
     */
    List<EventVo> getEventSelect(Long companyId);

    List<EventRegistrationStatisticsVo> getEventRegistrationStatistics(EventRegistrationStatisticsDto eventRegistrationStatisticsVo, Page page);
    /**
     * 当活动时间或者活动状态发生变化时，邮件提醒
     *
     * @param oldEvent 修改前活动对象
     * @param newEvent 修改后活动对象
     */
    void eventReminderEmail(Event oldEvent, Event newEvent);

    ResponseBo checkEventActivityFees(EventDto eventDto);

    /**
     * @param eventId
     * 上架下架伙伴
     */
    void updateHuatongPartner(Long eventId);
}

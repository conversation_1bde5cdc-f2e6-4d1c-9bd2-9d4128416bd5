package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.utils.CommonUtil;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.dto.MediaAndAttachedDto;
import com.get.salecenter.service.IGoproNucleicAcidService;
import com.get.salecenter.service.IMediaAndAttachedService;
import com.get.salecenter.dto.GoproNucleicAcidUpdateDto;
import com.get.salecenter.vo.MediaAndAttachedVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * @author: Hardy
 * @create: 2022/5/25 12:23
 * @verison: 1.0
 * @description:
 */
@Api(tags = "GoPro核酸资料")
@RestController
@RequestMapping("sale/goproNucleicAcid")
public class GoproNucleicAcidController {

    @Resource
    private IGoproNucleicAcidService goproNucleicAcidService;
    @Resource
    private IMediaAndAttachedService attachedService;


    /**
     * @return com.get.common.result.ResponseBo
     * @Description :保存核酸信息
     * @Param []
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "保存核酸信息", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/GoPro核酸资料/提交GoPro核酸资料")
    @PostMapping("add")
    public ResponseBo add(@RequestBody GoproNucleicAcidUpdateDto goproNucleicAcidUpdateDto) {
        return SaveResponseBo.ok(goproNucleicAcidService.addGoproNucleicAcid(goproNucleicAcidUpdateDto));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :区域下拉框
     * @Param []
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "区域下拉框", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/GoPro核酸资料/区域下拉框")
    @GetMapping("getRegionSelect")
    public ResponseBo<Map<String, Object>> getRegionSelect() {
        return new ListResponseBo<>(goproNucleicAcidService.getRegionSelect());
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :区域经理下拉框
     * @Param []
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "区域经理下拉框", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/GoPro核酸资料/区域经理下拉框")
    @PostMapping("getStaffSelect")
    public ResponseBo<Map<String, Object>> getStaffSelect(@RequestParam("id")Integer id) {
        return new ListResponseBo<>(goproNucleicAcidService.getStaffSelect(id));
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.MediaAndAttachedDto>
     * @Description : 保存GoPro核酸资料附件
     * @Param [mediaAttachedVo]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "保存GoPro核酸资料附件",notes = "typekey为convention_gopro_nucleic_acid_file")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/GoPro核酸资料/保存GoPro核酸资料附件")
    @PostMapping("upload")
    public ResponseBo<MediaAndAttachedVo> addGoproNucleicAcidMedia(@RequestBody  @Validated(MediaAndAttachedDto.Add.class) ValidList<MediaAndAttachedDto> mediaAttachedVo) {
        return new ListResponseBo<>(goproNucleicAcidService.addItemMedia(mediaAttachedVo));
    }


    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "上传GoPro核酸资料")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/GoPro核酸资料/上传资料")
    @PostMapping("uploadAttached")
    public ResponseBo uploadAttached(@RequestParam("files") @NotNull(message = "文件不能为空") MultipartFile[] files) {
        ResponseBo responseBo = new ResponseBo();
        responseBo.put("data", attachedService.uploadAppendix(files));
        return responseBo;
    }

    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "导出文件")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/GoPro核酸资料/导出文件")
    @PostMapping("exportInfo")
    public void exportInfo(HttpServletResponse response) throws Exception {
        goproNucleicAcidService.exportInfo(response);
    }

    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "全部导出文件")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/GoPro核酸资料/全部导出文件")
    @PostMapping("exportAllInfo")
    public void exportAllInfo(HttpServletResponse response,@RequestBody GoproNucleicAcidUpdateDto goproNucleicAcidUpdateDto) throws Exception {
        goproNucleicAcidService.exportAllInfo(response, goproNucleicAcidUpdateDto);
    }


    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "导出gopro核酸信息的excel文件")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/GoPro核酸资料/导出gopro核酸信息的excel文件")
    @PostMapping("exportAllExcelInfo")
    public void exportAllExcelInfo(HttpServletResponse response,@RequestBody GoproNucleicAcidUpdateDto goproNucleicAcidUpdateDto) throws Exception {
        goproNucleicAcidService.exportAllExcelInfo(response, goproNucleicAcidUpdateDto);
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "导出gopro核酸信息的图片文件")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/GoPro核酸资料/导出gopro核酸信息的图片文件")
    @PostMapping("exportAllImageInfo")
    public void exportAllImageInfo(HttpServletResponse response,@RequestBody GoproNucleicAcidUpdateDto goproNucleicAcidUpdateDto) throws Exception {
        CommonUtil.ok(response);
        goproNucleicAcidService.exportAllImageInfo(response, goproNucleicAcidUpdateDto);
    }

}

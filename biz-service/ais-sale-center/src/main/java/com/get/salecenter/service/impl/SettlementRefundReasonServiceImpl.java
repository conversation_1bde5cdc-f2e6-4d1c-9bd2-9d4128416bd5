package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.SettlementRefundReasonMapper;
import com.get.salecenter.entity.SettlementRefundReason;
import com.get.salecenter.service.SettlementRefundReasonService;
import com.get.salecenter.dto.SettlementRefundReasonDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
public class SettlementRefundReasonServiceImpl extends ServiceImpl< SettlementRefundReasonMapper,SettlementRefundReason> implements  SettlementRefundReasonService {

    @Resource
    private SettlementRefundReasonMapper settlementRefundReasonMapper;

    @Resource
    private UtilService utilService;


    @Override
    public List<SettlementRefundReason> datas(SettlementRefundReasonDto data, SearchBean<SettlementRefundReasonDto> page) {

        List<SettlementRefundReason> ct = new ArrayList<>();
        LambdaQueryWrapper<SettlementRefundReason> wrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(data.getReasonName())){
            wrapper.like(SettlementRefundReason::getReasonName,data.getReasonName());
        }
        wrapper.orderByDesc(SettlementRefundReason::getViewOrder);
        if (GeneralTool.isNotEmpty(page)){
            IPage<SettlementRefundReason> pages = settlementRefundReasonMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
            page.setAll((int) pages.getTotal());
            ct = pages.getRecords();
        }else {
            ct = settlementRefundReasonMapper.selectList(wrapper);
        }
        return ct;
    }

    @Override
    public SaveResponseBo save(SettlementRefundReasonDto settlementRefundReasonDto) {
        if (Objects.isNull(settlementRefundReasonDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        SettlementRefundReason settlementRefundReason = BeanCopyUtils.objClone(settlementRefundReasonDto, SettlementRefundReason::new);
        utilService.setCreateInfo(settlementRefundReason);
        Integer orderView = settlementRefundReasonMapper.getMaxOrder() + 1;
        settlementRefundReason.setViewOrder(orderView);
        settlementRefundReasonMapper.insert(settlementRefundReason);
        return SaveResponseBo.ok(settlementRefundReason.getId());
    }

    @Override
    public ResponseBo<SettlementRefundReason> findInfoById(Long id) {
        if (Objects.isNull(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        SettlementRefundReason settlementRefundReason = settlementRefundReasonMapper.selectById(id);
        return new ResponseBo<>(settlementRefundReason);
    }

    @Override
    public ResponseBo update(SettlementRefundReasonDto settlementRefundReasonDto) {
        if (Objects.isNull(settlementRefundReasonDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        SettlementRefundReason settlementRefundReason = BeanCopyUtils.objClone(settlementRefundReasonDto, SettlementRefundReason::new);
        utilService.setUpdateInfo(settlementRefundReason);
        settlementRefundReasonMapper.updateById(settlementRefundReason);
        return new ResponseBo<>();
    }

    @Override
    public ResponseBo movingOrder(List<SettlementRefundReasonDto> feeTypeVos) {
        if (Objects.isNull(feeTypeVos) || feeTypeVos.size()!=2) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        SettlementRefundReasonDto first = feeTypeVos.get(0);
        SettlementRefundReasonDto last = feeTypeVos.get(1);
        SettlementRefundReason fis = settlementRefundReasonMapper.selectById(first.getId());
        fis.setViewOrder(last.getViewOrder());
        SettlementRefundReason las = settlementRefundReasonMapper.selectById(last.getId());
        las.setViewOrder(first.getViewOrder());
        List<SettlementRefundReason> upList = new ArrayList<>(2);
        utilService.setUpdateInfo(fis);
        utilService.setUpdateInfo(las);
        upList.add(fis);
        upList.add(las);
        updateBatchById(upList);
        return new ResponseBo<>();
    }

    @Override
    public ResponseBo delete(Long id) {
        if (Objects.isNull(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        settlementRefundReasonMapper.deleteById(id);
        return new ResponseBo<>();
    }

    @Override
    public List<BaseSelectEntity> getServiceTypeList() {
        return settlementRefundReasonMapper.getServiceTypeList();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchAdd(ValidList<SettlementRefundReasonDto> settlementRefundReasonDtos) {
        if (GeneralTool.isEmpty(settlementRefundReasonDtos)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        for (SettlementRefundReasonDto settlementRefundReasonDto : settlementRefundReasonDtos) {
            if(GeneralTool.isEmpty(settlementRefundReasonDto.getId())){
                save(settlementRefundReasonDto);
            }else {
                SettlementRefundReason settlementRefundReason = BeanCopyUtils.objClone(settlementRefundReasonDto, SettlementRefundReason::new);
                utilService.updateUserInfoToEntity(settlementRefundReason);
                settlementRefundReasonMapper.updateById(settlementRefundReason);
            }

        }

    }
}

package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseService;
import com.get.salecenter.vo.ConventionSponsorFeeVo;
import com.get.salecenter.entity.ConventionSponsorFee;
import com.get.salecenter.dto.ConventionSponsorFeeDto;

import java.util.List;
import java.util.Map;

/**
 * @author: Sea
 * @create: 2021/5/8 11:07
 * @verison: 1.0
 * @description:
 */
public interface IConventionSponsorFeeService extends BaseService<ConventionSponsorFee> {
    /**
     * @return com.get.salecenter.vo.ConventionSponsorFeeVo
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    ConventionSponsorFeeVo findConventionSponsorFeeById(Long id);

    /**
     * @return java.lang.Long
     * @Description :新增
     * @Param [conventionSponsorFeeDto]
     * <AUTHOR>
     */
    Long addConventionSponsorFee(ConventionSponsorFeeDto conventionSponsorFeeDto);

    /**
     * @return void
     * @Description :删除
     * @Param [id]
     * <AUTHOR>
     */
    void delete(Long id);

    /**
     * @return com.get.salecenter.vo.ConventionSponsorFeeVo
     * @Description :修改
     * @Param [conventionSponsorFeeDto]
     * <AUTHOR>
     */
    ConventionSponsorFeeVo updateConventionSponsorFee(ConventionSponsorFeeDto conventionSponsorFeeDto);

    /**
     * @return java.util.List<com.get.salecenter.vo.ConventionSponsorFeeVo>
     * @Description :列表
     * @Param [conventionSponsorFeeDto, page]
     * <AUTHOR>
     */
    List<ConventionSponsorFeeVo> getConventionSponsorFees(ConventionSponsorFeeDto conventionSponsorFeeDto, Page page);

    /**
     * @return void
     * @Description :上移下移
     * @Param [conventionSponsorFeeDtos]
     * <AUTHOR>
     */
    void movingOrder(List<ConventionSponsorFeeDto> conventionSponsorFeeDtos);

    /**
     * @Description :获取赞助列表信息以及每个赞助是否售空
     * @Param [conventionId]
     * <AUTHOR>
     */
    List<Map<String, List<ConventionSponsorFeeVo>>> getSponsorshipFee(Long conventionId);

    /**
     * @return java.lang.Integer
     * @Description :赞助类型总数
     * @Param [fkConventionId]
     * <AUTHOR>
     */
    Integer getTotal(Long fkConventionId);
}

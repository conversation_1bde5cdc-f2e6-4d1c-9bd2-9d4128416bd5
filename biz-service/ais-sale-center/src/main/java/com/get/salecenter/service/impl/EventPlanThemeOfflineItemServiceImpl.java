package com.get.salecenter.service.impl;

import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.TableEnum;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.EventPlanThemeOfflineItemMapper;
import com.get.salecenter.vo.EventPlanThemeOfflineItemVo;
import com.get.salecenter.entity.EventPlanRegistration;
import com.get.salecenter.entity.EventPlanRegistrationEvent;
import com.get.salecenter.entity.EventPlanThemeOffline;
import com.get.salecenter.entity.EventPlanThemeOfflineItem;
import com.get.salecenter.service.EventPlanRegistrationEventService;
import com.get.salecenter.service.EventPlanRegistrationService;
import com.get.salecenter.service.EventPlanThemeOfflineItemService;
import com.get.salecenter.service.EventPlanThemeOfflineService;
import com.get.salecenter.dto.EventPlanThemeOfflineItemDto;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12
 */
@Service
public class EventPlanThemeOfflineItemServiceImpl extends BaseServiceImpl<EventPlanThemeOfflineItemMapper, EventPlanThemeOfflineItem> implements EventPlanThemeOfflineItemService {


    @Resource
    private EventPlanThemeOfflineItemMapper itemMapper;

    @Lazy
    @Resource
    private EventPlanThemeOfflineService offlineService;

    @Resource
    private UtilService utilService;

    @Resource
    private EventPlanRegistrationEventService eventService;

    @Resource
    private EventPlanRegistrationService eventPlanRegistrationService;

    @Override
    public Integer getMaxViewOrder(Long fkEventPlanThemeOfflineId){
        if (GeneralTool.isEmpty(fkEventPlanThemeOfflineId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        return itemMapper.getMaxViewOrder(fkEventPlanThemeOfflineId);
    }

    @Override
    public List<EventPlanThemeOfflineItemVo> getEventPlanThemeOfflineItems(Long fkEventPlanId , Long fkEventPlanThemeOfflineId) {
        if (GeneralTool.isEmpty(fkEventPlanThemeOfflineId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        List<EventPlanThemeOfflineItem> eventPlanThemeOfflineItems = itemMapper.selectList(Wrappers.<EventPlanThemeOfflineItem>lambdaQuery()
                .eq(EventPlanThemeOfflineItem::getFkEventPlanThemeOfflineId, fkEventPlanThemeOfflineId)
                .orderByDesc(EventPlanThemeOfflineItem::getViewOrder));
        if(GeneralTool.isEmpty(eventPlanThemeOfflineItems)){
            return Collections.emptyList();
        }
        List<EventPlanThemeOfflineItemVo> eventPlanThemeOfflineItemVos = BeanCopyUtils.copyListProperties(eventPlanThemeOfflineItems, EventPlanThemeOfflineItemVo::new);

        //活动计划主题id
        Set<Long> offlineIds = eventPlanThemeOfflineItems.stream().map(EventPlanThemeOfflineItem::getFkEventPlanThemeOfflineId).collect(Collectors.toSet());
        List<EventPlanThemeOffline> offlineList = offlineService.list(Wrappers.<EventPlanThemeOffline>lambdaQuery().in(EventPlanThemeOffline::getId, offlineIds));
        Map<Long, Long> themeMap = new HashMap<>();
        offlineList.forEach(o->themeMap.put(o.getId(),o.getFkEventPlanThemeId()));
        //报名名册
        List<EventPlanRegistration> registrations = eventPlanRegistrationService.list(Wrappers.<EventPlanRegistration>lambdaQuery()
                .eq(EventPlanRegistration::getFkEventPlanId, fkEventPlanId));
        //查询报名名册数
        Set<Long> fkLineItemIds = eventPlanThemeOfflineItemVos.stream().map(EventPlanThemeOfflineItemVo::getId).collect(Collectors.toSet());
        List<EventPlanRegistrationEvent> registrationlist = new ArrayList<>();
        if(GeneralTool.isNotEmpty(registrations)){
            Set<Long> fkRegistrationIds = registrations.stream().map(EventPlanRegistration::getId).collect(Collectors.toSet());
            registrationlist = eventService.list(Wrappers.<EventPlanRegistrationEvent>lambdaQuery()
                    .in(EventPlanRegistrationEvent::getFkEventPlanRegistrationId,fkRegistrationIds)
                    .eq(EventPlanRegistrationEvent::getFkTableName, TableEnum.EVENT_PLAN_THEME_OFFLINE_ITEM.key)
                    .in(EventPlanRegistrationEvent::getFkTableId,fkLineItemIds));
        }
        for (EventPlanThemeOfflineItemVo dto : eventPlanThemeOfflineItemVos) {
            //设置主题ID
            dto.setFkEventPlanThemeId(themeMap.get(dto.getFkEventPlanThemeOfflineId()));
            //报名名册合计
            if(GeneralTool.isNotEmpty(registrationlist)){
                List<EventPlanRegistrationEvent> regList = registrationlist.stream()
                        .filter(r -> r.getFkTableId().equals(dto.getId())).collect(Collectors.toList());
                dto.setRegistrationCount(regList.size());
            }
        }
        return eventPlanThemeOfflineItemVos;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void activate(EventPlanThemeOfflineItemDto offlineItemVo){
        if (GeneralTool.isEmpty(offlineItemVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        EventPlanThemeOfflineItem offlineItem = BeanCopyUtils.objClone(offlineItemVo, EventPlanThemeOfflineItem::new);
        utilService.updateUserInfoToEntity(offlineItem);
        itemMapper.updateById(offlineItem);
    }

    @Override
    public void movingOrder(Long fkEventPlanThemeOfflineId,Integer start,Integer end) {
        LambdaQueryWrapper<EventPlanThemeOfflineItem> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (end > start){
            lambdaQueryWrapper.between(EventPlanThemeOfflineItem::getViewOrder,start,end).orderByDesc(EventPlanThemeOfflineItem::getViewOrder);
        }else {
            lambdaQueryWrapper.between(EventPlanThemeOfflineItem::getViewOrder,end,start).orderByDesc(EventPlanThemeOfflineItem::getViewOrder);

        }
        lambdaQueryWrapper.eq(EventPlanThemeOfflineItem::getFkEventPlanThemeOfflineId,fkEventPlanThemeOfflineId);
        List<EventPlanThemeOfflineItem> offlineItems = list(lambdaQueryWrapper);

        //从下上移 列表倒序排序
        List<EventPlanThemeOfflineItem> updateList = Lists.newArrayList();
        if (end>start){
            int finalEnd = end;
            List<EventPlanThemeOfflineItem> sortedList = Lists.newArrayList();
            EventPlanThemeOfflineItem offlineItem = offlineItems.get(offlineItems.size() - 1);
            sortedList.add(offlineItem);
            offlineItems.remove(offlineItems.size() - 1);
            sortedList.addAll(offlineItems);
            for (EventPlanThemeOfflineItem themeOnline : sortedList) {
                themeOnline.setViewOrder(finalEnd);
                finalEnd--;
            }
            updateList.addAll(sortedList);
        }else {
            int finalStart = start;
            List<EventPlanThemeOfflineItem> sortedList = Lists.newArrayList();
            EventPlanThemeOfflineItem offlineItem = offlineItems.get(0);
            offlineItems.remove(0);
            sortedList.addAll(offlineItems);
            sortedList.add(offlineItem);
            for (EventPlanThemeOfflineItem themeOfflineItem : sortedList) {
                themeOfflineItem.setViewOrder(finalStart);
                finalStart--;
            }
            updateList.addAll(sortedList);
        }

        if (GeneralTool.isNotEmpty(updateList)){
            boolean batch = updateBatchById(updateList);
            if (!batch){
                throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
            }
        }
    }
}

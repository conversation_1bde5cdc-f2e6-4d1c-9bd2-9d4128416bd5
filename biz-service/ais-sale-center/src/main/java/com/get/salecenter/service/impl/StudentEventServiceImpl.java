package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.StudentEventMapper;
import com.get.salecenter.vo.StudentEventVo;
import com.get.salecenter.vo.StudentEventTypeVo;
import com.get.salecenter.entity.StudentEvent;
import com.get.salecenter.service.IStudentEventService;
import com.get.salecenter.service.IStudentEventTypeService;
import com.get.salecenter.dto.StudentEventDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/11/10
 * @TIME: 16:04
 * @Description:
 **/
@Service
public class StudentEventServiceImpl extends ServiceImpl<StudentEventMapper, StudentEvent> implements IStudentEventService {
    @Resource
    private UtilService utilService;
    @Resource
    private StudentEventMapper studentEventMapper;
    @Resource
    private IStudentEventTypeService typeService;

    @Override
    public StudentEventVo findStudentEventById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        StudentEvent studentEvent = studentEventMapper.selectById(id);
        return BeanCopyUtils.objClone(studentEvent, StudentEventVo::new);
    }

    @Override
    public List<StudentEventVo> getStudentEvents(StudentEventDto studentEventDto, Page page) {
//        Example example = new Example(StudentEvent.class);
//        Example.Criteria criteria = example.createCriteria();
        LambdaQueryWrapper<StudentEvent> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(studentEventDto)) {
            if (GeneralTool.isNotEmpty(studentEventDto.getFkStudentId())) {
//                criteria.andEqualTo("fkStudentId", studentEventDto.getFkStudentId());
                lambdaQueryWrapper.eq(StudentEvent::getFkStudentId, studentEventDto.getFkStudentId());
            }
            if (GeneralTool.isNotEmpty(studentEventDto.getFkStudentEventTypeId())) {
//                criteria.andEqualTo("fkStudentEventTypeId", studentEventDto.getFkStudentEventTypeId());
                lambdaQueryWrapper.eq(StudentEvent::getFkStudentEventTypeId, studentEventDto.getFkStudentEventTypeId());
            }
            if (GeneralTool.isNotEmpty(studentEventDto.getKeyWord())) {
//                criteria.andLike("description", "%" + studentEventDto.getKeyWord() + "%");
                lambdaQueryWrapper.like(StudentEvent::getDescription, studentEventDto.getKeyWord());
            }
        }
        IPage<StudentEvent> iPage = studentEventMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
        List<StudentEvent> studentEvents = iPage.getRecords();
        page.setAll((int) iPage.getTotal());

        List<StudentEventVo> collect =
                studentEvents.stream().map(studentEvent -> BeanCopyUtils.objClone(studentEvent, StudentEventVo::new)).collect(Collectors.toList());

        //设置类型名称
        if (GeneralTool.isNotEmpty(collect)) {
            for (StudentEventVo studentEventVo : collect) {
                StudentEventTypeVo studentEventType = typeService.findStudentEventTypeById(studentEventVo.getFkStudentEventTypeId());
                if (GeneralTool.isNotEmpty(studentEventType)) {
                    studentEventVo.setFkStudentEventTypeName(studentEventType.getTypeName());
                }
            }
        }
        return collect;
    }

    @Override
    public StudentEventVo updateStudentEvent(StudentEventDto studentEventDto) {
        if (GeneralTool.isEmpty(studentEventDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        StudentEvent studentEvent = BeanCopyUtils.objClone(studentEventDto, StudentEvent::new);
        utilService.updateUserInfoToEntity(studentEvent);
        studentEventMapper.updateById(studentEvent);
        return findStudentEventById(studentEvent.getId());
    }

    @Override
    public Long addStudentEvent(StudentEventDto studentEventDto) {
        if (GeneralTool.isEmpty(studentEventDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        StudentEvent studentEvent = BeanCopyUtils.objClone(studentEventDto, StudentEvent::new);
        utilService.updateUserInfoToEntity(studentEvent);
        studentEventMapper.insertSelective(studentEvent);
        return studentEvent.getId();
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        studentEventMapper.deleteById(id);
    }

    @Override
    public void deleteByStudentId(Long mergedStudentId) {
        if (GeneralTool.isNotEmpty(mergedStudentId)) {
            if (studentEventMapper.selectCount(Wrappers.<StudentEvent>lambdaQuery().eq(StudentEvent::getFkStudentId,mergedStudentId))>0) {
                studentEventMapper.delete(Wrappers.<StudentEvent>lambdaQuery().eq(StudentEvent::getFkStudentId,mergedStudentId));
            }
        }
    }

    /**
     * 合并学生事件
     * @param mergedStudentId
     * @param targetStudentId
     */
    @Override
    public void mergeData(Long mergedStudentId, Long targetStudentId) {
        List<StudentEvent> studentEvents = studentEventMapper.selectList(Wrappers.<StudentEvent>lambdaQuery().eq(StudentEvent::getFkStudentId, mergedStudentId));
        if (GeneralTool.isNotEmpty(studentEvents)) {
            studentEvents.forEach(s->s.setFkStudentId(targetStudentId));
            updateBatchById(studentEvents);
        }
    }
}

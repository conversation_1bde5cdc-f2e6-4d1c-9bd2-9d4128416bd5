package com.get.salecenter.service.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.vo.AreaRegionVo;
import com.get.institutioncenter.vo.AreaStateVo;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.dao.sale.StudentMapper;
import com.get.salecenter.dao.sale.StudentOfferItemMapper;
import com.get.salecenter.vo.*;
import com.get.salecenter.service.IStudentCountService;
import com.get.salecenter.service.IStudentOfferService;
import com.get.salecenter.utils.ConvertUtils;
import com.get.salecenter.utils.MyStringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.StringJoiner;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2021/2/2
 * @TIME: 10:15
 * @Description:
 **/
@Service
public class StudentCountServiceImpl implements IStudentCountService {
    @Resource
    private StudentOfferItemMapper studentOfferItemMapper;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private StudentMapper studentMapper;
    @Resource
    private IStudentOfferService studentOfferService;

    @Override
    public List<StudentCountVo> getStudentCountRecord(List<Long> companyIds, List<Long> areaCountryIds, String year) {
        Long staffId = SecureUtil.getStaffId();

        List<Long> staffFollowerIds = new ArrayList<>();
        //员工id + 业务下属员工ids
        if (GeneralTool.isNotEmpty(permissionCenterClient.getStaffFollowerIds(staffId).getData())) {
            staffFollowerIds = permissionCenterClient.getStaffFollowerIds(staffId).getData().stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        }
        staffFollowerIds.add(staffId);
        Boolean isBd = studentOfferService.getIsBd(staffId);
        List<StudentCountVo> studentCount = studentOfferItemMapper.getStudentCount(companyIds, areaCountryIds, year, SecureUtil.getStaffInfo().getIsStudentOfferItemFinancialHiding(),staffFollowerIds,SecureUtil.getStaffInfo().getIsStudentAdmin(),isBd, SecureUtil.getPermissionGroupInstitutionIds(), SecureUtil.getStaffBoundBdIds());
        //获取学生集合的所有国家ids
        Set<Long> countryIds = studentCount.stream().map(StudentCountVo::getFkAreaCountryId).collect(Collectors.toSet());
        //根据国家ids获取国家名称
        Map<Long, String> countryNamesByIds = new HashMap<>();
        Result<Map<Long, String>> result1 = institutionCenterClient.getCountryNamesByIds(countryIds);
        if (result1.isSuccess() && GeneralTool.isNotEmpty(result1.getData())) {
            countryNamesByIds = result1.getData();
        }
        //学校ids
        Set<Long> institutionIds = studentCount.stream().map(StudentCountVo::getFkInstitutionId).collect(Collectors.toSet());
        //学校ids查找对应名称
        Map<Long, String> institutionNamesByIds = new HashMap<>();
        Result<Map<Long, String>> result2 = institutionCenterClient.getInstitutionNamesByIds(institutionIds);
        if (result2.isSuccess() && GeneralTool.isNotEmpty(result2.getData())) {
            institutionNamesByIds = result2.getData();
        }

        for (StudentCountVo studentCountVo : studentCount) {
            StringJoiner stringJoiner = new StringJoiner(" ");
            if (GeneralTool.isNotEmpty(studentCountVo.getFkAreaCountryId())) {
                String countryName = countryNamesByIds.get(studentCountVo.getFkAreaCountryId());
                stringJoiner.add("【" + countryName + "】");
            }
            stringJoiner.add(studentCountVo.getStudentName());
            if (GeneralTool.isNotEmpty(studentCountVo.getFkInstitutionId())) {
                String institutionName = institutionNamesByIds.get(studentCountVo.getFkInstitutionId());
                stringJoiner.add(institutionName);
            }
            studentCountVo.setStudentItemRecord(stringJoiner.toString());
        }
        return studentCount;
    }

    /**
     *
     * @param companyIds
     * @param areaCountryIds
     * @param year
     * @param statisticsFlag true:统计申请计划数  false:统计学生数
     * @return
     */
    @Override
    public List<WorldHistogramVo> getWorldHistogram(List<Long> companyIds, List<Long> areaCountryIds, String year, Boolean statisticsFlag) {
        List<WorldHistogramVo> dtos = new ArrayList<>();
        if(GeneralTool.isEmpty(areaCountryIds))
        {
            areaCountryIds = new ArrayList<>();
            Result<Set<Long>> countryIdsResult = institutionCenterClient.getAllCountryId();
            if (countryIdsResult.isSuccess() && GeneralTool.isNotEmpty(countryIdsResult.getData())) {
                areaCountryIds.addAll(countryIdsResult.getData());
            }
        }
        if (GeneralTool.isNotEmpty(areaCountryIds)) {
            Set<Long> countryIds = new HashSet<>(areaCountryIds);
            Map<Long, String> countryChnNameByIds = new HashMap<>();
            Result<Map<Long, String>> result = institutionCenterClient.getCountryChnNameByIds(countryIds);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                countryChnNameByIds = result.getData();
            }
            Map<Long, String> countryNameByIds = new HashMap<>();
            Result<Map<Long, String>> result_ = institutionCenterClient.getCountryNamesByIds(countryIds);
            if (result_.isSuccess() && GeneralTool.isNotEmpty(result_.getData())) {
                countryNameByIds = result_.getData();
            }
            Long staffId = SecureUtil.getStaffId();

            List<Long> staffFollowerIds = new ArrayList<>();
            //员工id + 业务下属员工ids
            if (GeneralTool.isNotEmpty(permissionCenterClient.getStaffFollowerIds(staffId).getData())) {
                staffFollowerIds = permissionCenterClient.getStaffFollowerIds(staffId).getData().stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
            }
            staffFollowerIds.add(staffId);
            List<SelItem> list;
            Boolean isBd = studentOfferService.getIsBd(staffId);
            if (statisticsFlag) {

                list = studentMapper.getCountByCountryTwo(companyIds, areaCountryIds,staffFollowerIds, year, SecureUtil.getStaffInfo().getIsStudentAdmin(), SecureUtil.getStaffInfo().getIsStudentOfferItemFinancialHiding(),isBd, SecureUtil.getPermissionGroupInstitutionIds(), SecureUtil.getStaffBoundBdIds());
            } else {
                list = studentMapper.getStudentCountByCountry(companyIds, areaCountryIds,staffFollowerIds, year, SecureUtil.getStaffInfo().getIsStudentAdmin(), SecureUtil.getStaffInfo().getIsStudentOfferItemFinancialHiding(),isBd, SecureUtil.getPermissionGroupInstitutionIds(), SecureUtil.getStaffBoundBdIds());
            }
            Map<Long, Object> convert = ConvertUtils.convert(list);
            if(GeneralTool.isNotEmpty(countryNameByIds))
            {
                for (Long areaCountryId : areaCountryIds) {
                    if (GeneralTool.isNotEmpty(areaCountryId)) {
                        WorldHistogramVo worldHistogramVo = new WorldHistogramVo();
                        String countryName = countryNameByIds.get(areaCountryId);
                        if (GeneralTool.isNotEmpty(countryName) && countryName.contains("（")) {
                            String[] split = countryNameByIds.get(areaCountryId).split("（");
                            worldHistogramVo.setCountryName(split[0]);
                        } else {
                            worldHistogramVo.setCountryName(countryNameByIds.get(areaCountryId));
                        }
                        worldHistogramVo.setCountryNameChn(countryChnNameByIds.get(areaCountryId));
                        if (convert.containsKey(areaCountryId)) {
                            worldHistogramVo.setNum(Integer.parseInt(convert.get(areaCountryId).toString()));
                        }else {
                            worldHistogramVo.setNum(0);
                        }
                        dtos.add(worldHistogramVo);
                    }
                }
            }

        }

        List<WorldHistogramVo> newList = dtos.stream().sorted(Comparator.comparing(WorldHistogramVo::getNum).reversed()).collect(Collectors.toList());
        if (newList.size() < 11) {
            return newList;
        } else {
            return newList.subList(0, 9);
        }
    }

    @Override
    public List<WorldHistogramVo> getAllWorldHistogram(List<Long> companyIds, String year) {
        List<WorldHistogramVo> dtos = new ArrayList<>();
        Set<Long> countryIds = new HashSet<>();
        Result<Set<Long>> countryIdsResult = institutionCenterClient.getAllCountryId();
        if (countryIdsResult.isSuccess() && GeneralTool.isNotEmpty(countryIdsResult.getData())) {
            countryIds = countryIdsResult.getData();
        }
        Map<Long, String> countryChnNameByIds = new HashMap<>();
        Result<Map<Long, String>> countryChnNameByIdsResult = institutionCenterClient.getCountryChnNameByIds(countryIds);
        if (countryChnNameByIdsResult.isSuccess() && GeneralTool.isNotEmpty(countryChnNameByIdsResult.getData())) {
            countryChnNameByIds = countryChnNameByIdsResult.getData();
        }
        Map<Long, String> countryNameByIds = new HashMap<>();
        Result<Map<Long, String>> countryNameByIdsResult = institutionCenterClient.getCountryNamesByIds(countryIds);
        if (countryNameByIdsResult.isSuccess() && GeneralTool.isNotEmpty(countryNameByIdsResult.getData())) {
            countryNameByIds = countryNameByIdsResult.getData();
        }
        for (Long areaCountryId : countryIds) {
            if (GeneralTool.isNotEmpty(areaCountryId)) {
                WorldHistogramVo worldHistogramVo = new WorldHistogramVo();
                if (countryNameByIds.get(areaCountryId).contains("（")) {
                    String[] split = countryNameByIds.get(areaCountryId).split("（");
                    worldHistogramVo.setCountryName(split[0]);
                } else {
                    worldHistogramVo.setCountryName(countryNameByIds.get(areaCountryId));
                }
                worldHistogramVo.setCountryNameChn(countryChnNameByIds.get(areaCountryId));
                worldHistogramVo.setNum(studentMapper.getCountByCountry(companyIds, areaCountryId, year, SecureUtil.getStaffInfo().getIsStudentOfferItemFinancialHiding()));
                dtos.add(worldHistogramVo);
            }
        }


        List<WorldHistogramVo> newList = dtos.stream().sorted(Comparator.comparing(WorldHistogramVo::getNum).reversed()).collect(Collectors.toList());
        if (newList.size() < 11) {
            return newList;
        } else {
            return newList.subList(0, 9);
        }
    }

    @Override
    public List<WorldHistogramVo> getStateStudentNum(List<Long> companyIds, List<Long> areaCountryIds, List<Long> stateIds, String year, Boolean statisticsFlag, Boolean isStudentOfferItemFinancialHiding, List<Long> staffFollowerIds) {
        List<WorldHistogramVo> dtos = new ArrayList<>();
        if (GeneralTool.isNotEmpty(stateIds)) {
            Set<Long> stateSetIds = new HashSet<>(stateIds);
            Map<Long, String> stateNamesByIds = new HashMap<>();
            Result<Map<Long, String>> stateNamesByIdsResult = institutionCenterClient.getStateNamesByIds(stateSetIds);
            if (stateNamesByIdsResult.isSuccess() && GeneralTool.isNotEmpty(stateNamesByIdsResult.getData())) {
                stateNamesByIds = stateNamesByIdsResult.getData();
                Map<Long, Integer> itemMap = new HashMap<>();
                Map<Long, Integer> studentMap = new HashMap<>();
                Boolean isBd = studentOfferService.getIsBd(SecureUtil.getStaffId());
                if (statisticsFlag){
                    List<SelCountVo> countByState = studentOfferItemMapper.getCountByState(companyIds, areaCountryIds, stateIds, year, isStudentOfferItemFinancialHiding, staffFollowerIds, SecureUtil.getStaffInfo().getIsStudentAdmin(),isBd, SecureUtil.getPermissionGroupInstitutionIds(), SecureUtil.getStaffBoundBdIds());
                    itemMap = countByState.stream().collect(Collectors.toMap(SelCountVo::getId, SelCountVo::getNum));
                }else {
                    List<SelCountVo> countByState = studentMapper.getCountByState(companyIds, areaCountryIds, stateIds, year, isStudentOfferItemFinancialHiding, staffFollowerIds, SecureUtil.getStaffInfo().getIsStudentAdmin(),isBd, SecureUtil.getPermissionGroupInstitutionIds(), SecureUtil.getStaffBoundBdIds());
                    studentMap = countByState.stream().collect(Collectors.toMap(SelCountVo::getId, SelCountVo::getNum));
                }

                Map<Long, AreaRegionVo> areaRegionDtoMap = institutionCenterClient.getRegionMapByStateIds(stateIds);
                for (Long id : stateIds) {
                    WorldHistogramVo worldHistogramVo = new WorldHistogramVo();
                    if (stateNamesByIds.get(id).contains("（")) {
                        worldHistogramVo.setCountryNameChn(MyStringUtils.extractMessageByChar(stateNamesByIds.get(id), '（', '）').get(0));
                        worldHistogramVo.setCountryName(stateNamesByIds.get(id).split("（")[0]);
                        AreaRegionVo areaRegionVo = areaRegionDtoMap.get(id);
                        if (GeneralTool.isNotEmpty(areaRegionVo)) {
                            worldHistogramVo.setRegionName(areaRegionVo.getName());
                            worldHistogramVo.setRegionNameChn(areaRegionVo.getNameChn());
                        }
                    }
                    if (statisticsFlag) {
                        worldHistogramVo.setNum(itemMap.get(id) != null ?itemMap.get(id):new Integer(0));
                    } else {
                        worldHistogramVo.setNum(studentMap.get(id) != null ? studentMap.get(id):new Integer(0));
                    }
                    dtos.add(worldHistogramVo);
                }
            }
        } else {
//            ListResponseBo responseBo = institutionCenterClient.getByFkAreaCountryId(3L);
            Result<List<AreaStateVo>> result = institutionCenterClient.getByFkAreaCountryId(3L);
            if (!result.isSuccess()) {
                return null;
            }
            JSONArray objects = JSONUtil.parseArray(result.getData());
            List<AreaStateVo> datas = JSONUtil.toList(objects, AreaStateVo.class);
            List<Long> ids = datas.stream().map(AreaStateVo::getId).collect(Collectors.toList());
            Map<Long, Integer> itemMap = new HashMap<>();
            Map<Long, Integer> studentMap = new HashMap<>();
            Boolean isBd = studentOfferService.getIsBd(SecureUtil.getStaffId());
            if (statisticsFlag){
                List<SelCountVo> countByState = studentOfferItemMapper.getCountByState(companyIds, areaCountryIds, ids, year, isStudentOfferItemFinancialHiding, staffFollowerIds, SecureUtil.getStaffInfo().getIsStudentAdmin(),isBd, SecureUtil.getPermissionGroupInstitutionIds(), SecureUtil.getStaffBoundBdIds());
                itemMap = countByState.stream().collect(Collectors.toMap(SelCountVo::getId, SelCountVo::getNum));
            }else {
                List<SelCountVo> countByState = studentMapper.getCountByState(companyIds, areaCountryIds, ids, year, isStudentOfferItemFinancialHiding, staffFollowerIds, SecureUtil.getStaffInfo().getIsStudentAdmin(),isBd, SecureUtil.getPermissionGroupInstitutionIds(), SecureUtil.getStaffBoundBdIds());
                studentMap = countByState.stream().collect(Collectors.toMap(SelCountVo::getId, SelCountVo::getNum));
            }
            for (AreaStateVo areaStateVo : datas) {
                WorldHistogramVo worldHistogramVo = new WorldHistogramVo();
                worldHistogramVo.setCountryName(areaStateVo.getNameChn());
                if (statisticsFlag) {
                    worldHistogramVo.setNum(itemMap.get(areaStateVo.getId()) != null?itemMap.get(areaStateVo.getId()):new Integer(0));
                } else {
                    worldHistogramVo.setNum(studentMap.get(areaStateVo.getId()) != null?studentMap.get(areaStateVo.getId()):new Integer(0));
                }
                dtos.add(worldHistogramVo);
            }
        }
//        List<WorldHistogramVo> newList = dtos.stream().sorted(Comparator.comparing(WorldHistogramVo::getStudentNum).reversed().thenComparing(WorldHistogramVo::getCountryName)).collect(Collectors.toList());
        List<WorldHistogramVo> newList = dtos.stream().sorted(Comparator.comparing(WorldHistogramVo::getNum).reversed().thenComparing(WorldHistogramVo::getCountryName)).collect(Collectors.toList());
        return newList;
    }

    @Override
    public List<WorldMapVo> getWorldMapDtos(List<Long> companyId, List<Long> areaCountryIds, String year, Boolean statisticsFlag) {
        Long staffId = SecureUtil.getStaffId();

        List<Long> staffFollowerIds = new ArrayList<>();
        //员工id + 业务下属员工ids
        if (GeneralTool.isNotEmpty(permissionCenterClient.getStaffFollowerIds(staffId).getData())) {
            staffFollowerIds = permissionCenterClient.getStaffFollowerIds(staffId).getData().stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        }
        staffFollowerIds.add(staffId);
        Boolean isBd = studentOfferService.getIsBd(staffId);
        List<WorldMapVo> worldMapVos = studentOfferItemMapper.getWorldMapDtos(companyId, areaCountryIds, year, statisticsFlag, SecureUtil.getStaffInfo().getIsStudentOfferItemFinancialHiding(),staffFollowerIds,SecureUtil.getStaffInfo().getIsStudentAdmin(),isBd, SecureUtil.getPermissionGroupInstitutionIds(), SecureUtil.getStaffBoundBdIds());
        Set<Long> startCountryIds = worldMapVos.stream().map(WorldMapVo::getStartCountry).collect(Collectors.toSet());
        Set<Long> endCountryIds = worldMapVos.stream().map(WorldMapVo::getEndCountry).collect(Collectors.toSet());
        startCountryIds.removeIf(Objects::isNull);
        endCountryIds.removeIf(Objects::isNull);
        Map<Long, String> startCountryChnNameByIds = new HashMap<>();
        Map<Long, String> startCountryNameByIds = new HashMap<>();
        Map<Long, String> endCountryChnNameByIds = new HashMap<>();
        Map<Long, String> endCountryNameByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(startCountryIds)) {
//            startCountryChnNameByIds = institutionCenterClient.getCountryChnNameByIds(startCountryIds);
            Result<Map<Long, String>> countryChnNameByIdsResult = institutionCenterClient.getCountryChnNameByIds(startCountryIds);
            if (countryChnNameByIdsResult.isSuccess() && GeneralTool.isNotEmpty(countryChnNameByIdsResult.getData())) {
                startCountryChnNameByIds = countryChnNameByIdsResult.getData();
            }
        }
        if (GeneralTool.isNotEmpty(endCountryIds)) {
//            endCountryChnNameByIds = institutionCenterClient.getCountryChnNameByIds(endCountryIds);
            Result<Map<Long, String>> countryChnNameByIdsResult = institutionCenterClient.getCountryChnNameByIds(endCountryIds);
            if (countryChnNameByIdsResult.isSuccess() && GeneralTool.isNotEmpty(countryChnNameByIdsResult.getData())) {
                endCountryChnNameByIds = countryChnNameByIdsResult.getData();
            }
        }
        if (GeneralTool.isNotEmpty(startCountryIds)) {
//            startCountryNameByIds = institutionCenterClient.getCountryNamesByIds(startCountryIds);
            Result<Map<Long, String>> countryNameByIdsResult = institutionCenterClient.getCountryNamesByIds(startCountryIds);
            if (countryNameByIdsResult.isSuccess() && GeneralTool.isNotEmpty(countryNameByIdsResult.getData())) {
                startCountryNameByIds = countryNameByIdsResult.getData();
            }
        }
        if (GeneralTool.isNotEmpty(endCountryIds)) {
//            endCountryNameByIds = institutionCenterClient.getCountryNamesByIds(endCountryIds);
            Result<Map<Long, String>> countryNameByIdsResult = institutionCenterClient.getCountryNamesByIds(endCountryIds);
            if (countryNameByIdsResult.isSuccess() && GeneralTool.isNotEmpty(countryNameByIdsResult.getData())) {
                endCountryNameByIds = countryNameByIdsResult.getData();
            }
        }

        for (WorldMapVo worldMapVo : worldMapVos) {
            if (GeneralTool.isNotEmpty(worldMapVo.getStartCountry())) {
                worldMapVo.setStartName(startCountryChnNameByIds.get(worldMapVo.getStartCountry()));
                if (GeneralTool.isNotEmpty(startCountryNameByIds.get(worldMapVo.getStartCountry())) && startCountryNameByIds.get(worldMapVo.getStartCountry()).contains("（")) {
                    String[] split = startCountryNameByIds.get(worldMapVo.getStartCountry()).split("（");
                    worldMapVo.setStartNameEn(split[0]);
                } else {
                    worldMapVo.setStartNameEn(startCountryNameByIds.get(worldMapVo.getStartCountry()));
                }
            }
            if (GeneralTool.isNotEmpty(worldMapVo.getEndCountry())) {
                worldMapVo.setEndName(endCountryChnNameByIds.get(worldMapVo.getEndCountry()));
                String contryName = endCountryNameByIds.get(worldMapVo.getEndCountry());
                if(GeneralTool.isNotEmpty(contryName) && contryName.contains("（"))
                {
                    String[] split = endCountryNameByIds.get(worldMapVo.getEndCountry()).split("（");
                    worldMapVo.setEndNameEn(split[0]);
                } else {
                    worldMapVo.setEndNameEn(endCountryNameByIds.get(worldMapVo.getEndCountry()));
                }
            }
        }
        worldMapVos = setOffSet(worldMapVos);
        return worldMapVos;
    }

    /**
     * 学生分布城市数量
     *
     * @param cityIds
     * @param statisticsFlag                    true:统计申请计划数  false:统计学生数
     * @param isStudentOfferItemFinancialHiding
     * @return
     */
    @Override
    public List<WorldHistogramVo> getCityStudentNum(List<Long> companyIds, List<Long> areaCountryIds, List<Long> cityIds, String year, Boolean statisticsFlag, Boolean isStudentOfferItemFinancialHiding, List<Long> staffFollowerIds) {
        List<WorldHistogramVo> dtos = new ArrayList<>();
        if (GeneralTool.isNotEmpty(cityIds)) {
            Set<Long> citySetIds = new HashSet<>(cityIds);
            Map<Long, String> cityNamesByIds = new HashMap<>();
            Result<Map<Long, String>> result = institutionCenterClient.getCityNamesByIds(citySetIds);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                cityNamesByIds = result.getData();
            }
            for (Long id : cityIds) {
                WorldHistogramVo worldHistogramVo = new WorldHistogramVo();
                if (cityNamesByIds.get(id).contains("（")) {
                    worldHistogramVo.setCountryNameChn(MyStringUtils.extractMessageByChar(cityNamesByIds.get(id), '（', '）').get(0));
                    worldHistogramVo.setCountryName(cityNamesByIds.get(id).split("（")[0]);
                }
                Boolean isBd = studentOfferService.getIsBd(SecureUtil.getStaffId());
                if (statisticsFlag) {
                    worldHistogramVo.setNum(studentOfferItemMapper.getCountByCity(companyIds, id, year, isStudentOfferItemFinancialHiding,staffFollowerIds,SecureUtil.getStaffInfo().getIsStudentAdmin(),isBd, SecureUtil.getPermissionGroupInstitutionIds(), SecureUtil.getStaffBoundBdIds()));
                } else {
                    worldHistogramVo.setNum(studentMapper.getCountByCity(companyIds, id, year, isStudentOfferItemFinancialHiding,staffFollowerIds,SecureUtil.getStaffInfo().getIsStudentAdmin(),isBd, SecureUtil.getPermissionGroupInstitutionIds(), SecureUtil.getStaffBoundBdIds()));
                }
                dtos.add(worldHistogramVo);
            }
        } else {
            Result<List<AreaStateVo>> result = institutionCenterClient.getByFkAreaCountryId(3L);
            if (!result.isSuccess()) {
                return null;
            }
            JSONArray objects = JSONUtil.parseArray(result.getData());
            List<AreaStateVo> datas = JSONUtil.toList(objects, AreaStateVo.class);
            List<Long> ids = datas.stream().map(AreaStateVo::getId).collect(Collectors.toList());
            Map<Long, Integer> studentMap = new HashMap<>();
            Boolean isBd = studentOfferService.getIsBd(SecureUtil.getStaffId());
            List<SelCountVo> countByState = studentMapper.getCountByState(companyIds, areaCountryIds, ids, year, isStudentOfferItemFinancialHiding, staffFollowerIds, SecureUtil.getStaffInfo().getIsStudentAdmin(),isBd, SecureUtil.getPermissionGroupInstitutionIds(), SecureUtil.getStaffBoundBdIds());
            studentMap = countByState.stream().collect(Collectors.toMap(SelCountVo::getId, SelCountVo::getNum));

            for (AreaStateVo areaStateVo : datas) {
                WorldHistogramVo worldHistogramVo = new WorldHistogramVo();
                worldHistogramVo.setCountryName(areaStateVo.getNameChn());
                worldHistogramVo.setNum(studentMap.get(areaStateVo.getId()) != null?studentMap.get(areaStateVo.getId()):new Integer(0));
                dtos.add(worldHistogramVo);
            }
        }
        List<WorldHistogramVo> newList = dtos.stream().sorted(Comparator.comparing(WorldHistogramVo::getNum).reversed().thenComparing(WorldHistogramVo::getCountryName)).collect(Collectors.toList());
        return newList;
    }

    /**
     * 系统首页数据-获取总学生申请数
     *
     * @Date 11:34 2022/8/2
     * <AUTHOR>
     */
    @Override
    public Long getStudentItemTotalSum(List<Long> companyIds, List<Long> countryIds, String year, Boolean isStudentOfferItemFinancialHiding,List<Long> staffFollowerIds,Boolean isStudentAdmin) {
        Boolean isBd = studentOfferService.getIsBd(SecureUtil.getStaffId());
        return studentOfferItemMapper.getStudentItemTotalSum(companyIds, countryIds, year, isStudentOfferItemFinancialHiding,staffFollowerIds,isStudentAdmin,isBd, SecureUtil.getPermissionGroupInstitutionIds(), SecureUtil.getStaffBoundBdIds());
    }


    /**
     * 把终点国家一样的线合并 然后把学生数加起来 set到同国家最后一条线上
     *
     * @Date 15:05 2021/11/24
     * <AUTHOR>
     */
    private List<WorldMapVo> setOffSet(List<WorldMapVo> worldMapVos) {
        List<WorldMapVo> dtos = new ArrayList<>();
        if (GeneralTool.isNotEmpty(worldMapVos)) {
            //初始化一个map
            Map<String, List<WorldMapVo>> map = new HashMap<>();
            for (WorldMapVo wm : worldMapVos) {
                String key = wm.getEndName();
                if (map.containsKey(key)) {
                    //map中存在以此id作为的key，将数据存放当前key的map中
                    map.get(key).add(wm);
                } else {
                    //map中不存在以此id作为的key，新建key用来存放数据
                    List<WorldMapVo> list = new ArrayList<>();
                    list.add(wm);
                    map.put(key, list);
                }
            }
            //分组结束，map中的数据就是分组后的数据
            map.forEach((key, value) ->
            {
                List<WorldMapVo> wmdtos = value;
                /*if(wmdtos.size()==1){
                    wmdtos.get(0).setOffset(0);
                    dtos.add(wmdtos.get(0));
                }else{
                    int count = 0;
                    for (WorldMapVo wmdto:wmdtos){
                        count ++;
                        wmdto.setOffset(count);
                        dtos.add(wmdto);
                    }
                }*/

                //同一个目的国家 最多人报读所在国家 MaxFlag=true
                Integer maxStudentSize = null;
                int maxValue = 0;

                int count = 0;
                for (int i = 0; i < wmdtos.size(); i++) {
                    count = count + wmdtos.get(i).getValue();
                    WorldMapVo worldMapVo = wmdtos.get(i);
                    if (maxValue < worldMapVo.getValue()) {
                        maxValue = worldMapVo.getValue();
                        maxStudentSize = i;
                    }
                    if (i == wmdtos.size() - 1) {
                        worldMapVo.setTotal(count);
                    }
                    dtos.add(worldMapVo);
                }

                //前端要求：同一个终点国家 如果报读学生人数最大的起点国家为中国（包括台湾、香港、澳门） ，则该起点为中国（包括台湾、香港、澳门）的国家线 MaxFlag=true
                if (maxStudentSize != null) {
                    WorldMapVo worldMapVo = wmdtos.get(maxStudentSize);
                    worldMapVo.setMaxFlag(true);
                    if (GeneralTool.isNotEmpty(worldMapVo.getStartCountry())) {
                        if (3L == worldMapVo.getStartCountry() || 9L == worldMapVo.getStartCountry() || 10L == worldMapVo.getStartCountry() ||
                                33L == worldMapVo.getStartCountry()) {
                            for (WorldMapVo wmdto : wmdtos) {
                                if (GeneralTool.isNotEmpty(wmdto.getStartCountry())) {
                                    if (3L == wmdto.getStartCountry() || 9L == wmdto.getStartCountry() || 10L == wmdto.getStartCountry() ||
                                            33L == wmdto.getStartCountry()) {
                                        wmdto.setMaxFlag(true);
                                    }
                                }
                            }
                        }
                    }
                }

            });
        }
        List<WorldMapVo> newList = dtos.stream().sorted(Comparator.comparing(WorldMapVo::getTotal).reversed().
                        thenComparing(WorldMapVo::getEndName, Comparator.nullsLast(String::compareTo)))
                .filter(worldMapDto -> GeneralTool.isNotEmpty(worldMapDto.getEndName()) && GeneralTool.isNotEmpty(worldMapDto.getStartName())).collect(Collectors.toList());

        return newList;
    }

    @Override
    public Long getStudentTotalSum(List<Long> companyIds, List<Long> areaCountryIds, String year, Boolean isStudentOfferItemFinancialHiding, List<Long> staffFollowerIds, Boolean isStudentAdmin) {
        Boolean isBd = studentOfferService.getIsBd(SecureUtil.getStaffId());
        return studentOfferItemMapper.getStudentTotalSum(companyIds, areaCountryIds, year, isStudentOfferItemFinancialHiding,staffFollowerIds,isStudentAdmin,isBd, SecureUtil.getPermissionGroupInstitutionIds(), SecureUtil.getStaffBoundBdIds());
    }



    /**
     * 获取stateIds
     *
     * @return
     */
    private List<Long> getStateIds(String num) {
        List<String> numList = new ArrayList<>();
        numList.add(num);
//        ResponseBo<List<Long>> responseBo = institutionCenterClient.getCountryIdByKey(numList);
        Result<List<Long>> result = institutionCenterClient.getCountryIdByKey(numList);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            List<Long> data = result.getData();
//            ListResponseBo listResponseBo =
            Result<List<AreaStateVo>> result_ = institutionCenterClient.getByFkAreaCountryId(data.get(0));
            if (result_.isSuccess() && GeneralTool.isNotEmpty(result_.getData())) {
                cn.hutool.json.JSONArray objects = JSONUtil.parseArray(result_.getData());
                List<AreaStateVo> datas = JSONUtil.toList(objects, AreaStateVo.class);
                return datas.stream().map(AreaStateVo::getId).collect(Collectors.toList());
            }
        }
        return null;
    }
}

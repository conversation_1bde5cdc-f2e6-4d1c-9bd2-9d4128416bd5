package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.EventDeferTimeMapper;
import com.get.salecenter.dao.sale.EventMapper;
import com.get.salecenter.dto.EventDeferTimeDto;
import com.get.salecenter.entity.Event;
import com.get.salecenter.entity.EventDeferTime;
import com.get.salecenter.entity.EventRegistration;
import com.get.salecenter.service.IEventDeferTimeService;
import com.get.salecenter.service.IEventRegistrationService;
import com.get.salecenter.service.IEventService;
import com.get.salecenter.vo.EventDeferTimeVo;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class EventDeferTimeServiceImpl extends ServiceImpl<EventDeferTimeMapper, EventDeferTime> implements IEventDeferTimeService {

    @Resource
    private EventDeferTimeMapper eventDeferTimeMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private EventMapper eventMapper;
    @Resource
    private IEventService eventService;
    @Lazy
    @Resource
    private IEventRegistrationService eventRegistrationService;

    private SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    private static boolean sameDate(Date d1, Date d2) {
        SimpleDateFormat fmt = new SimpleDateFormat("yyyyMMdd");
        //fmt.setTimeZone(new TimeZone()); // 如果需要设置时间区域，可以在这里设置
        return fmt.format(d1).equals(fmt.format(d2));
    }

    @Override
    public List<EventDeferTimeVo> datas(Long fkEventId) {
        if (GeneralTool.isEmpty(fkEventId)) {
            return Collections.emptyList();
        }
        List<EventDeferTime> eventDeferTimeList = eventDeferTimeMapper.selectList(Wrappers.<EventDeferTime>lambdaQuery()
                .eq(EventDeferTime::getFkEventId, fkEventId)
                .orderByDesc(EventDeferTime::getGmtCreate));

        List<EventDeferTimeVo> eventDeferTimeVoList = new ArrayList<>();
        for (EventDeferTime eventDeferTime : eventDeferTimeList) {
            EventDeferTimeVo eventDeferTimeVo = BeanCopyUtils.objClone(eventDeferTime, EventDeferTimeVo::new);
            if (GeneralTool.isNotEmpty(eventDeferTimeVo.getEventTime()) && GeneralTool.isNotEmpty(eventDeferTimeVo.getEventTimeEnd())) {
                if (sameDate(eventDeferTimeVo.getEventTime(), eventDeferTimeVo.getEventTimeEnd())) {
                    String eventTimeName = formatter.format(eventDeferTimeVo.getEventTime()) + "至" + new SimpleDateFormat("HH:mm:ss").format(eventDeferTimeVo.getEventTimeEnd());
                    eventDeferTimeVo.setEventTimeName(eventTimeName);
                } else {
                    String eventTimeName = formatter.format(eventDeferTimeVo.getEventTime()) + "至" + formatter.format(eventDeferTimeVo.getEventTimeEnd());
                    eventDeferTimeVo.setEventTimeName(eventTimeName);
                }
            }
            eventDeferTimeVoList.add(eventDeferTimeVo);
        }
        return eventDeferTimeVoList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long addEventDeferTime(EventDeferTimeDto eventDeferTimeDto) {
        if (GeneralTool.isEmpty(eventDeferTimeDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        EventDeferTime eventDeferTime = BeanCopyUtils.objClone(eventDeferTimeDto, EventDeferTime::new);
        Event oldEvent = eventMapper.selectById(eventDeferTime.getFkEventId());
        if (GeneralTool.isEmpty(oldEvent)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }


        Event event = new Event();
        event.setId(eventDeferTimeDto.getFkEventId());
        event.setStatus(ProjectExtraEnum.EVENT_POSTPONE.key);
        int i = eventMapper.updateById(event);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
        //设置待定状态
        doSetUndeterminedStatus(eventDeferTimeDto.getFkEventId());

        Event newEvent = eventMapper.selectById(eventDeferTime.getFkEventId());

        // 更新该活动的活动时间字段
        newEvent.setEventTime(eventDeferTime.getEventTime());
        newEvent.setEventTimeEnd(eventDeferTime.getEventTimeEnd());
        utilService.setUpdateInfo(newEvent);
        eventMapper.updateById(newEvent);
        // 发邮件通知
        //TODO 发邮件通知 需要立即发送邮件，需要拆分
        eventService.eventReminderEmail(oldEvent, newEvent);

        // 插入活动时间日志表
        utilService.setCreateInfo(eventDeferTime);
        eventDeferTimeMapper.insert(eventDeferTime);
        return eventDeferTime.getId();
    }

    /**
     * 设置待定状态
     * @param eventId
     */
    private void doSetUndeterminedStatus(Long eventId) {
        List<EventRegistration> eventRegistrations = eventRegistrationService.list(Wrappers.<EventRegistration>lambdaUpdate()
                .eq(EventRegistration::getFkEventId, eventId));
        if (GeneralTool.isEmpty(eventRegistrations)){
            return;
        }
        eventRegistrations = eventRegistrations.stream().filter(eventRegistration -> eventRegistration.getStatus()!=2).collect(Collectors.toList());
        if (GeneralTool.isEmpty(eventRegistrations)){
            return;
        }
        for (EventRegistration eventRegistration : eventRegistrations) {
            eventRegistration.setStatus(2);
            utilService.setUpdateInfo(eventRegistration);
        }
        boolean b = eventRegistrationService.updateBatchById(eventRegistrations);
        if (!b){
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
//        EventRegistration eventRegistration = new EventRegistration();
//        eventRegistration.setStatus(2);
//        utilService.setUpdateInfo(eventRegistration);
//        boolean update = eventRegistrationService.update(eventRegistration, Wrappers.<EventRegistration>lambdaUpdate()
//                .eq(EventRegistration::getFkEventId, eventId));
//        if (!update){
//            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
//        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        EventDeferTime eventDeferTime = eventDeferTimeMapper.selectById(id);
        if (GeneralTool.isEmpty(eventDeferTime)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        int delete = eventDeferTimeMapper.deleteById(id);
        if (delete < 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
        // 用最新的时间固化到该活动的活动时间字段
        EventDeferTime lastEventDeferTime = eventDeferTimeMapper.selectOne(Wrappers.<EventDeferTime>lambdaQuery()
                .eq(EventDeferTime::getFkEventId, eventDeferTime.getFkEventId())
                .orderByDesc(EventDeferTime::getGmtCreate)
                .last("limit 1"));
        Event oldEvent = eventMapper.selectById(eventDeferTime.getFkEventId());
        Event newEvent = BeanCopyUtils.objClone(oldEvent, Event::new);
        if (GeneralTool.isNotEmpty(lastEventDeferTime)) {
            newEvent.setEventTime(lastEventDeferTime.getEventTime());
            newEvent.setEventTimeEnd(lastEventDeferTime.getEventTimeEnd());
        } else { // 表示删除了最后一个活动时间，那就置空该活动的活动时间
            newEvent.setEventTime(null);
            newEvent.setEventTimeEnd(null);
        }
        utilService.setUpdateInfo(newEvent);
        eventMapper.updateByIdWithNull(newEvent);
        // 发邮件通知
        eventService.eventReminderEmail(oldEvent, newEvent);
    }
}

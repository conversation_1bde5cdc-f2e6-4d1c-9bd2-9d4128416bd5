<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.convention.GoproRegistrationMapper">

    <update id="updateByPrimaryKey" parameterType="com.get.salecenter.entity.GoproRegistration">
        update ais_app_convention_center.m_gopro_registration
        set fk_company_id = #{fkCompanyId,jdbcType=INTEGER},
        retreat_type_name = #{retreatTypeName,jdbcType=VARCHAR},
        name = #{name,jdbcType=VARCHAR},
        gender = #{gender,jdbcType=VARCHAR},
        company = #{company,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
        mobile = #{mobile,jdbcType=VARCHAR},
        email = #{email,jdbcType=VARCHAR},
        area_country_name = #{areaCountryName,jdbcType=VARCHAR},
        area_state_name = #{areaStateName,jdbcType=VARCHAR},
        area_city_name = #{areaCityName,jdbcType=VARCHAR},
        fk_area_country_id = #{fkAreaCountryId,jdbcType=INTEGER},
        fk_area_state_id = #{fkAreaStateId,jdbcType=INTEGER},
        fk_area_city_id = #{fkAreaCityId,jdbcType=INTEGER},
        bd_region = #{bdRegion,jdbcType=VARCHAR},
        bd_name = #{bdName,jdbcType=VARCHAR},
        v_status = #{vStatus,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>
  <delete id="deleteById">
    delete from ais_app_convention_center.m_gopro_registration where id=#{id}
  </delete>
  <select id="getCountSeatUsed" resultType="java.lang.Integer">
      select count(*) count from ais_app_convention_center.m_gopro_registration where retreat_type_name = #{type} AND year = #{year}
    </select>
  <select id="getIsExistMobile" resultType="com.get.salecenter.dto.GoproRegistrationDto">
    select name from ais_app_convention_center.m_gopro_registration where mobile = #{mobile} and year =#{year}
  </select>
  <select id="getGoProList" resultType="com.get.salecenter.dto.GoproRegistrationDto">
      SELECT
          gm.id,
          gc.short_name fk_company_name,
          retreat_type_name,
          gm.`name`,
          gm.company,
          gm.gender,
          CASE gm.gender
          WHEN 0 THEN
          '女'
          WHEN 1 THEN
          '男'
          ELSE
          '其他'
          END gender_name,
          gm.fk_area_country_id,
          gm.fk_area_state_id,
          gm.fk_area_city_id,
          gm.mobile,
          gm.email,
          gm.area_country_name,
          gm.area_city_name,
          gm.area_state_name,
          gm.area_city_name,
          gm.bd_region,
          gm.bd_name,
          gm.v_status,
          gm.remark,
          gm.gmt_create
          FROM
          ais_app_convention_center.m_gopro_registration gm
          LEFT JOIN ais_permission_center.m_company gc ON gm.fk_company_id = gc.id
          <if test="!isExclude">
              INNER JOIN(
              SELECT r.name_chn FROM ais_institution_center.u_area_region r INNER JOIN ais_sale_center.r_staff_bd_code s ON s.fk_area_region_id = r.id
              WHERE s.fk_staff_id = #{fkStaffId}
              ) rg ON gm.bd_region LIKE concat('',rg.name_chn,'%')
          </if>
        WHERE
          1 = 1
        <if test="year!=null">
          AND gm.year = #{year}
        </if>
        <if test="company != null and company != ''">
          AND gm.company LIKE CONCAT('%', #{company}, '%')
        </if>
        <if test="name != null and name != ''">
          AND gm.name LIKE CONCAT('%', #{name}, '%')
        </if>
        <if test="bdName!=null and bdName!=''">
          AND gm.bd_name LIKE concat("%", #{bdName}, "%")
        </if>
        <if test="bdRegion!=null and bdRegion!=''">
          AND gm.bd_region LIKE concat("%", #{bdRegion}, "%")
        </if>
        <if test="mobile != null and mobile != ''">
          AND gm.mobile LIKE CONCAT('%', #{mobile}, '%')
        </if>
        <if test="str != null and str != ''">
          AND (gm.retreat_type_name =#{str1}
          OR gm.retreat_type_name =#{str2}
          OR gm.retreat_type_name =#{str3})
        </if>
        ORDER BY gm.id desc
  </select>
  <select id="getSeatCountList" resultType="java.util.Map">
     select value1,value2,value3,value4 from ais_permission_center.m_config where config_key='GOPROSEAT_COUNT'
  </select>
    <select id="getUsedByTypeAndId" resultType="java.lang.Integer">
        select count(*) count from ais_app_convention_center.m_gopro_registration where retreat_type_name = #{type} and id !=#{id} and `year` = #{year}
    </select>
    <select id="getYears" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT `year` as name from m_gopro_registration GROUP BY `year`
    </select>

</mapper>
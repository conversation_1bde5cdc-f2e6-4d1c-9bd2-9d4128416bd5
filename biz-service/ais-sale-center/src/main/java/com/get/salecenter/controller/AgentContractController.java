package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.dto.AgentContractCompanyDto;
import com.get.salecenter.dto.AgentContractDto;
import com.get.salecenter.dto.CommentDto;
import com.get.salecenter.dto.ContactPersonDto;
import com.get.salecenter.dto.CreateAgentContractDto;
import com.get.salecenter.dto.MediaAndAttachedDto;
import com.get.salecenter.dto.query.AgentContractQueryDto;
import com.get.salecenter.entity.AgentContract;
import com.get.salecenter.service.IAgentContractService;
import com.get.salecenter.vo.AgentContractSelect;
import com.get.salecenter.vo.AgentContractTypeVo;
import com.get.salecenter.vo.AgentContractVo;
import com.get.salecenter.vo.CommentVo;
import com.get.salecenter.vo.CompanyTreeVo;
import com.get.salecenter.vo.ContactPersonVo;
import com.get.salecenter.vo.MediaAndAttachedVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * <AUTHOR>
 * @DATE: 2020/9/14
 * @TIME: 16:27
 * @Description: 代理合同管理
 **/

@Api(tags = "代理合同管理")
@RestController
@RequestMapping("sale/agentContract")
public class AgentContractController {
    @Resource
    private IAgentContractService agentContractService;

    /**
     * 列表数据
     *
     * @param searchBean
     * @return
     */
    @ApiOperation(value = "列表数据")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生代理合同管理/查询")
    @PostMapping("datas")
    public ResponseBo<AgentContractVo> datas(@RequestBody SearchBean<AgentContractQueryDto> searchBean) {
        List<AgentContractVo> datas = agentContractService.getAgentContractDtos(searchBean.getData(), searchBean);
        Page p = BeanCopyUtils.objClone(searchBean, Page::new);
        return new ListResponseBo<>(datas, p);
    }
    @ApiOperation(value = "续约")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生代理合同管理/续约")
    @PostMapping("renewal")
    public ResponseBo renewal(@RequestBody List<Long> contractIds){
        agentContractService.renewal(contractIds);
        return new ResponseBo();
    }

    /**
     * 新增信息
     *
     * @param contractVo
     * @return
     * @
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生代理合同管理/新增")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(AgentContractDto.Add.class)  AgentContractDto contractVo) {
        return SaveResponseBo.ok(agentContractService.addContract(contractVo));
    }

    /**
     * 修改信息
     *
     * @param contractVo
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生代理合同管理/更新合同")
    @PostMapping("update")
    public ResponseBo<AgentContractVo> update(@RequestBody @Validated(AgentContractDto.Update.class) AgentContractDto contractVo) {
        return UpdateResponseBo.ok(agentContractService.updateAgentContract(contractVo));
    }

    @ApiOperation(value = "生成代理合同", notes = "")
    @VerifyLogin(IsVerify = false)
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生代理合同管理/生成代理合同")
    @GetMapping("createAgentContractDocx")
    /**
     * 字段说明
     * contractVsion
     * 合同版本
     * 0 中文 1 英文 2 越南
     * */
    public void createAgentContractDocx(@RequestParam("contractId") Long contractId, @RequestParam("agentId") Long agentId, @RequestParam("contractVsion") Long contractVsion,@RequestParam("contractTemplateMode") Integer contractTemplateMode, HttpServletResponse response){
     if (contractTemplateMode == null){
         contractTemplateMode = 0;
     }else if (contractTemplateMode == 1 && contractVsion == 0){
         agentContractService.createAgentContractPdf(contractId,agentId, contractVsion, contractTemplateMode,response);
     }
        agentContractService.createAgentContractDocx(contractId, agentId, contractVsion, contractTemplateMode, response);

    }


    @ApiOperation(value = "生成新协议代理合同", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生代理合同管理/生成代理合同")
    @PostMapping("createAgentContractPdf")
    /**
     * 字段说明
     * contractVsion
     * 合同版本
     * 0 中文 1 英文 2 越南 3新协议
     * */
    public void createAgentContractPdf(@RequestParam("contractId") Long contractId, @RequestParam("agentId") Long agentId, @RequestParam("contractVsion") Long contractVsion, @RequestParam("contractTemplateMode") Integer contractTemplateMode, HttpServletResponse response){
        agentContractService.createAgentContractPdf(contractId,agentId, contractVsion, contractTemplateMode,response);
    }


    @ApiOperation(value = "生成代理合同(免权限，小程序或其他地方调用)", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生代理合同管理/生成代理合同(免权限，小程序或其他地方调用)")
    @PostMapping("createAgentContractPdfForOthers")
    @VerifyLogin(IsVerify = false)
    @VerifyPermission(IsVerify = false)
    /**
     * 字段说明
     * contractVsion
     * 合同版本
     * 0 中文 1 英文 2 越南 3新协议
     * */
    public void createAgentContractPdfForOthers(@RequestBody CreateAgentContractDto createAgentContractDto, HttpServletResponse response) {
        agentContractService.createAgentContractPdfForOthers(createAgentContractDto,response);
    }

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/学生代理合同管理/合同详情")
    @GetMapping("/{id}")
    public ResponseBo<AgentContractVo> detail(@PathVariable("id") Long id) {
        AgentContractVo agentContractVo = agentContractService.findAgentContractById(id);
        return new ResponseBo<>(agentContractVo);
    }


    /**
     * 删除信息
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/学生代理合同管理/删除合同")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        agentContractService.deleteAgentContract(id);
        return DeleteResponseBo.ok();
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.AgentContractTypeVo>
     * @Description: 合同类型下拉框
     * @Param []
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "合同类型下拉框", notes = "keyWord为关键词")
    @GetMapping("getContractType")
    public ResponseBo<AgentContractTypeVo> getContractType() {
        List<AgentContractTypeVo> agentContractTypeVos = agentContractService.getContractType();
        return new ListResponseBo<>(agentContractTypeVos);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 合同下拉
     * @Param []
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "所属合同下拉框", notes = "")
    @GetMapping("getAllContract")
    public ResponseBo<AgentContractSelect> getAllAgentContract() {
        List<AgentContractSelect> agentContract = agentContractService.getAllAgentContract();
        return new ListResponseBo<>(agentContract);
    }


    /**
     * 查询代理附件
     *
     * @param voSearchBean
     * @return
     * @
     */
    @ApiOperation(value = "查询代理合同文件", notes = "keyWord为关键词")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生代理合同管理/查询代理附件")
    @PostMapping("getAgentMedia")
    public ResponseBo<MediaAndAttachedVo> getAgentMedia(@RequestBody SearchBean<MediaAndAttachedDto> voSearchBean) {
        List<MediaAndAttachedVo> staffMedia = agentContractService.getAgentContractMedia(voSearchBean.getData(), voSearchBean);
        Page page = BeanCopyUtils.objClone(voSearchBean, Page::new);
        return new ListResponseBo<>(staffMedia, page);
    }


    /**
     * @param mediaAttachedVo
     * @return
     * @
     */
    @ApiOperation(value = "保存代理合同文件")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生代理合同管理/保存附件")
    @PostMapping("addMediaAndAttached")
    public ResponseBo<MediaAndAttachedVo> addMediaAndAttached(@RequestBody  @Validated(MediaAndAttachedDto.Add.class) ValidList<MediaAndAttachedDto> mediaAttachedVo) {
        return new ListResponseBo<>(agentContractService.addAgentContractMedia(mediaAttachedVo));
    }


    /**
     * 代理合同联系人列表
     *
     * @param searchBean
     * @return
     * @
     */
    @ApiOperation(value = "代理合同联系人列表")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/联系人管理/代理联系人列表")
    @PostMapping("getContactPerson")
    public ResponseBo<ContactPersonVo> getAgentContactPerson(@RequestBody SearchBean<ContactPersonDto> searchBean) {
        List<ContactPersonVo> contactPersonVos = agentContractService.getContactPerson(searchBean.getData(), searchBean);
        Page p = BeanCopyUtils.objClone(searchBean, Page::new);
        return new ListResponseBo<>(contactPersonVos, p);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 新增代理合同联系人
     * @Param [contactPersonVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增代理合同联系人", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/代理管理/新增代理联系人")
    @PostMapping("addContactPerson")
    public ResponseBo addContactPerson(@RequestBody @Validated(ContactPersonDto.Add.class)  ContactPersonDto contactPersonVo) {
        return SaveResponseBo.ok(agentContractService.addContactPerson(contactPersonVo));
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 代理合同-公司安全配置
     * @Param [validList]
     * <AUTHOR>
     */
    @ApiOperation(value = "代理合同-公司安全配置")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生代理合同管理/安全配置")
    @PostMapping("editProviderCompanyRelation")
    public ResponseBo editProviderCompanyRelation(@RequestBody @Validated(AgentContractCompanyDto.Add.class)   ValidList<AgentContractCompanyDto> validList) {
        agentContractService.editAgentContractCompany(validList);
        return UpdateResponseBo.ok();
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.CompanyTreeVo>
     * @Description: 回显代理合同和公司的关系
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "回显代理合同和公司的关系", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/学生代理合同管理/代理合同和公司的关系（数据回显）")
    @GetMapping("getRelation/{contractId}")
    public ResponseBo<CompanyTreeVo> getContractCompanyRelation(@PathVariable("contractId") Long id) {
        List<CompanyTreeVo> contractCompanyRelation = agentContractService.getContractCompanyRelation(id);
        return new ListResponseBo<>(contractCompanyRelation);
    }

    /**
     * @param commentDto
     * @return
     * @
     */
    @ApiOperation(value = "编辑评论")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生代理合同管理/编辑评论")
    @PostMapping("editComment")
    public ResponseBo editComment(@RequestBody  @Validated(CommentDto.Add.class)  CommentDto commentDto) {
        return SaveResponseBo.ok(agentContractService.editComment(commentDto));
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.AgentContractVo>
     * @Description: 评论列表数据
     * @Param [searchBean]
     * <AUTHOR>
     */
    @ApiOperation(value = "评论列表数据")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生代理合同管理/查询")
    @PostMapping("getComments")
    public ResponseBo<CommentVo> getComment(@RequestBody SearchBean<CommentDto> searchBean) {
        List<CommentVo> datas = agentContractService.getComments(searchBean.getData(), searchBean);
        Page p = BeanCopyUtils.objClone(searchBean, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @ Description :fegin调用，根据id查询某条记录
     * @ Param [id]
     * @ return com.get.salecenter.vo.AgentContractVo
     * @ author LEO
     */
    @ApiIgnore
    @ApiOperation(value = "fegin调用，根据id查询某条记录")
    @GetMapping("getAgentContractById")
    @VerifyPermission(IsVerify = false)
    public AgentContractVo getAgentContractById(@RequestParam("id") Long id) {
        AgentContractVo agentContractById = agentContractService.getAgentContractById(id);
        return agentContractById;
    }

    @ApiIgnore
    @ApiOperation(value = "fegin调用，更新表的status状态")
    @PostMapping("updateChangeStatus")
    public void updateChangeStatus(@RequestBody AgentContract agentContract) {
        agentContractService.updateChangeStatus(agentContract);
    }

    @ApiOperation(value = "fegin调用，开始流程")
    @GetMapping("startContractFlow")
    public ResponseBo startContractFlow(@RequestParam("businessKey") String businessKey,
                                        @RequestParam("procdefKey") String procdefKey,
                                        @RequestParam("companyId") String companyId) throws GetServiceException {
        if (businessKey == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        agentContractService.startContractFlow(businessKey, procdefKey, companyId);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "表单作废")
    @PostMapping("updateCancellationBusiness")
    public ResponseBo updateCancellationBusiness(@RequestParam("id") Long id) {
        agentContractService.updateCancellationBusiness(id);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "重新提交/发起")
    @GetMapping("/getContractUserSubmit")
    public ResponseBo getContractUserSubmit(@RequestParam("taskId") String taskId, @RequestParam("status") String status) {
        agentContractService.getUserSubmit(taskId, status);
        return ResponseBo.ok();
    }

    @ApiIgnore
    @ApiOperation("查找代理的跟进人StaffId,一个代理跟进人只有一个 判断是否激活")
    @GetMapping("getStaffByAgentId")
    public Long getStaffByAgentId(@RequestParam("fkAgentId") Long fkAgentId) {
        Long staffByAgentId = agentContractService.getStaffByAgentId(fkAgentId);
        return staffByAgentId;
    }

    @ApiIgnore
    @ApiOperation("fegin调用根据表名更新状态")
    @PostMapping("/changeStatus")
    public ResponseBo changeStatus(@RequestParam("status") Integer status, @RequestParam("tableName") String tableName, @RequestParam("businessKey") Long businessKey) {
        agentContractService.changeStatus(status, tableName, businessKey);
        return ResponseBo.ok();
    }

    @ApiOperation("代理合同表单撤回")
    @GetMapping("getRevokeAgentContract")
    @VerifyPermission(IsVerify = false)
    public ResponseBo getRevokeAgentContract(@RequestParam("id") Long id,
                                             @RequestParam("summary") String summary) {
        agentContractService.getRevokeAgentContract(id, summary);
        return ResponseBo.ok();
    }
    
    /** 
    * @Description: 同步cpp代理附件接口(已同步完注释)
    * @Param: 
    * @return: 
    * @Author: Walker
    * @Date: 2022/4/26
    */
    /*@ApiOperation("同步cpp代理附件接口")
    @GetMapping("synchronizeCppAttactment")
    public ResponseBo synchronizeCppAttactment(){
        agentContractService.synchronizeCppAttactment();
        return ResponseBo.ok();
    }*/

    /** 
    * @Description: 修改代理合同的编号和同步合同创建时间
    * @Param: 
    * @return: 
    * @Author: Walker
    * @Date: 2022/8/2
    */
    @ApiOperation("修改代理合同的编号和同步合同创建时间")
    @GetMapping("synchronizeCppAttactmentInfo")
    public ResponseBo synchronizeCppAttactmentInfo(){
        agentContractService.synchronizeCppAttactmentInfo();
        return ResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "续期合同", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生代理合同管理/续期合同")
    @PostMapping("renewalAgentContract")
    public ResponseBo renewalAgentContract(@RequestParam("id")Long id) {
        agentContractService.renewalAgentContract(id);
        return UpdateResponseBo.ok();
    }

    /**
     * 合同审批同意
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "合同审批同意", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生代理合同管理/合同审批同意")
    @GetMapping("agree/{id}")
    public ResponseBo agree(@PathVariable("id") Long id) {
        agentContractService.agree(id);
        return UpdateResponseBo.ok();
    }

    /**
     * 发送代理合同未签署提醒邮件 (Feign接口)
     *
     * @return
     */
    @ApiIgnore
    @PostMapping("/feign/send-agent-contract-unsigned-reminders")
    public ResponseBo sendAgentContractUnsignedReminders() {
        try {
            agentContractService.sendAgentContractUnsignedReminders();
            return ResponseBo.ok();
        } catch (Exception e) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("news_email_send_fail"));
        }
    }

}
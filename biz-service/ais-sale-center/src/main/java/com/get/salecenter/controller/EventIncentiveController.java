package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.vo.CommentVo;
import com.get.salecenter.vo.EventIncentiveListVo;
import com.get.salecenter.vo.EventIncentiveVo;
import com.get.salecenter.service.IEventIncentiveService;
import com.get.salecenter.dto.*;
import com.get.salecenter.vo.MediaAndAttachedVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;


/**
 * @author: Hardy
 * @create: 2022/7/19 14:53
 * @verison: 1.0
 * @description:
 */
@Api(tags = "奖励推广活动管理")
@RestController
@RequestMapping("sale/eventIncentive")
public class EventIncentiveController {

    @Resource
    private IEventIncentiveService eventIncentiveService;

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :新增奖励推广活动
     * @Param []
     * <AUTHOR>
     */
    @ApiOperation(value = "新增奖励推广活动", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/奖励推广活动管理/新增")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(EventIncentiveUpdateDto.Add.class) EventIncentiveUpdateDto eventIncentiveUpdateDto) {
        return SaveResponseBo.ok(eventIncentiveService.addEventIncentive(eventIncentiveUpdateDto));
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.EventVo>
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/奖励推广活动管理/奖励推广活动详情")
    @GetMapping("/{id}")
    public ResponseBo<EventIncentiveVo> detail(@PathVariable("id") Long id) {
        EventIncentiveVo data = eventIncentiveService.findEventIncentiveById(id);
        return new ResponseBo<>(data);
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.EventVo>
     * @Description :列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/奖励推广活动管理/奖励推广活动列表")
    @PostMapping("datas")
    public ResponseBo<EventIncentiveListVo> datas(@RequestBody SearchBean<EventIncentiveListDto> page) {
        List<EventIncentiveListVo> datas = eventIncentiveService.getEventIncentives(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :删除信息
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/奖励推广活动管理/删除活动")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        eventIncentiveService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentVo>
     * @Description: 修改信息
     * @Param [studentVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/奖励推广活动管理/编辑")
    @PostMapping("update")
    public ResponseBo<EventIncentiveVo> update(@RequestBody  @Validated(EventIncentiveUpdateDto.Update.class)  EventIncentiveUpdateDto eventBillUpdateVo) {
        return UpdateResponseBo.ok(eventIncentiveService.updateEventIncentive(eventBillUpdateVo));
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.MediaAndAttachedDto>
     * @Description : 查询活动汇总费用附件
     * @Param [voSearchBean]
     * <AUTHOR>
     */
    @ApiOperation(value = "查询活动汇总费用附件", notes = "keyWord为关键词")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/奖励推广活动管理/查询附件")
    @PostMapping("getItemMedia")
    public ResponseBo<MediaAndAttachedVo> getItemMedia(@RequestBody SearchBean<MediaAndAttachedDto> voSearchBean) {
        List<MediaAndAttachedVo> staffMedia = eventIncentiveService.getItemMedia(voSearchBean.getData(), voSearchBean);
        Page page = BeanCopyUtils.objClone(voSearchBean, Page::new);
        return new ListResponseBo<>(staffMedia, page);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.MediaAndAttachedDto>
     * @Description : 保存活动汇总费用附件
     * @Param [mediaAttachedVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "保存附件")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/奖励推广活动管理/保存附件")
    @PostMapping("upload")
    public ResponseBo<MediaAndAttachedVo> addAgentMedia(@RequestBody @Validated(MediaAndAttachedDto.Add.class) ValidList<MediaAndAttachedDto> mediaAttachedVo) {
        return new ListResponseBo<>(eventIncentiveService.addItemMedia(mediaAttachedVo));
    }

    /**
     * @param commentDto
     * @return
     * @
     */
    @ApiOperation(value = "编辑评论")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/奖励推广活动管理/编辑评论")
    @PostMapping("editComment")
    public ResponseBo editComment(@RequestBody @Validated(CommentDto.Add.class) CommentDto commentDto) {
        return SaveResponseBo.ok(eventIncentiveService.editComment(commentDto));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.AgentContractVo>
     * @Description: 评论列表数据
     * @Param [searchBean]
     * <AUTHOR>
     */
    @ApiOperation(value = "评论列表数据")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/奖励推广活动管理/查询评论")
    @PostMapping("getComments")
    public ResponseBo<CommentVo> getComment(@RequestBody SearchBean<CommentDto> searchBean) {
        List<CommentVo> datas = eventIncentiveService.getComments(searchBean.getData(), searchBean);
        Page p = BeanCopyUtils.objClone(searchBean, Page::new);
        return new ListResponseBo<>(datas, p);
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.EventCostVo>
     * @Description :列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "分配奖励活动列表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/奖励推广活动管理/分配奖励活动列表")
    @PostMapping("getEventIncentiveList")
    public ResponseBo<EventIncentiveListVo> getEventIncentiveList(@RequestBody SearchBean<EventIncentiveDistributeDto> page) {
        List<EventIncentiveListVo> datas = eventIncentiveService.getEventIncentiveList(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description :结束
     * @Param [eventId]
     * <AUTHOR>
     */
    @ApiOperation(value = "结束", notes = "eventId活动id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/奖励推广活动管理/结束")
    @PostMapping("end")
    public ResponseBo end(@RequestParam Long eventIncentiveId) {
        eventIncentiveService.end(eventIncentiveId);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :计划
     * @Param [eventId]
     * <AUTHOR>
     */
    @ApiOperation(value = "计划", notes = "eventId活动id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/奖励推广活动管理/计划")
    @PostMapping("plan")
    public ResponseBo plan(@RequestParam Long eventIncentiveId) {
        eventIncentiveService.plan(eventIncentiveId);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :延期
     * @Param [eventId]
     * <AUTHOR>
     */
    @ApiOperation(value = "延期", notes = "eventId活动id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/奖励推广活动管理/延期")
    @PostMapping("postpone")
    public ResponseBo postpone(@RequestParam Long eventIncentiveId) {
        eventIncentiveService.postpone(eventIncentiveId);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :取消
     * @Param [eventId]
     * <AUTHOR>
     */
    @ApiOperation(value = "取消", notes = "eventId活动id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/奖励推广活动管理/取消")
    @PostMapping("cancel")
    public ResponseBo cancel(@RequestParam Long eventIncentiveId) {
        eventIncentiveService.cancel(eventIncentiveId);
        return ResponseBo.ok();
    }

    /**
     * 活动状态下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "活动状态下拉框数据", notes = "")
    @GetMapping("getEventStatusSelect")
    public ResponseBo getEventStatusSelect() {
        return new ListResponseBo<>(ProjectExtraEnum.enumsTranslation2Arrays(ProjectExtraEnum.EVENT_STATUS));
    }

    /**
     * 公开对象枚举
     */
    @ApiOperation(value = "公开对象下拉框数据", notes = "")
    @GetMapping("getEventPublicLevel")
    public ResponseBo getEventPublicLevel() {
        return new ListResponseBo<>(ProjectExtraEnum.enumsTranslation2Arrays(ProjectExtraEnum.EVENT_PUBLIC_LEVEL));
    }

}

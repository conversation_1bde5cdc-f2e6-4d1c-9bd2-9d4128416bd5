package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.institutioncenter.vo.AreaCityVo;
import com.get.institutioncenter.vo.AreaCountryVo;
import com.get.institutioncenter.vo.AreaStateVo;
import com.get.permissioncenter.vo.StaffVo;
import com.get.salecenter.dto.CommentDto;
import com.get.salecenter.dto.EventCostDto;
import com.get.salecenter.dto.EventDto;
import com.get.salecenter.dto.EventRegistrationStatisticsDto;
import com.get.salecenter.dto.MediaAndAttachedDto;
import com.get.salecenter.dto.query.EventQueryDto;
import com.get.salecenter.service.IEventService;
import com.get.salecenter.vo.CommentVo;
import com.get.salecenter.vo.EventDataVo;
import com.get.salecenter.vo.EventRegistrationStatisticsVo;
import com.get.salecenter.vo.EventTypeVo;
import com.get.salecenter.vo.EventVo;
import com.get.salecenter.vo.MediaAndAttachedVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * @author: Sea
 * @create: 2020/12/7 15:12
 * @verison: 1.0
 * @description: 活动管理控制器
 */
@Api(tags = "活动管理")
@RestController
@RequestMapping("sale/event")
public class EventController {
    @Resource
    private IEventService eventService;

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.EventVo>
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/活动管理/活动详情")
    @GetMapping("/{id}")
    public ResponseBo<EventVo> detail(@PathVariable("id") Long id) {
        EventVo data = eventService.findEventById(id);
        return new ResponseBo<>(data);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :新增信息
     * @Param [eventVos]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增接口", notes = "eventTargetCountryList活动对象国家(多选)集合形式，eventTarget活动目标对象String形式")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/活动管理/新增活动")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(EventDto.Add.class) EventDto eventDto) {
        return SaveResponseBo.ok(eventService.addEvent(eventDto));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :删除信息
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/活动管理/删除活动")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        eventService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.EventVo>
     * @Description :修改信息
     * @Param [eventDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/活动管理/更新活动")
    @PostMapping("update")
    public ResponseBo<EventVo> update(@RequestBody @Validated(EventDto.Update.class) EventDto eventDto) {
        return UpdateResponseBo.ok(eventService.updateEvent(eventDto));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.EventVo>
     * @Description :修改信息
     * @Param [eventDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口（免评价）", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/活动管理/更新活动（免评价）")
    @PostMapping("updateWithoutRemark")
    public ResponseBo<EventVo> updateWithoutRemark(@RequestBody @Validated(EventDto.Update.class)  EventDto eventDto) {
        return UpdateResponseBo.ok(eventService.updateEventWithoutRemark(eventDto));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.EventVo>
     * @Description :列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/活动管理/查询活动")
    @PostMapping("datas")
    public ResponseBo<EventVo> datas(@RequestBody SearchBean<EventQueryDto> page) {
        List<EventVo> datas = eventService.getEvents(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    @ApiOperation(value = "导出活动汇总Excel")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/活动管理/导出活动汇总Excel")
    @PostMapping("/exportEventExcel")
    @ResponseBody
    public void exportEventExcel(HttpServletResponse response, @RequestBody EventQueryDto eventQueryVo) {
        eventService.exportEventExcel(response, eventQueryVo);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :获取院校/集团/项目 下拉框数据
     * @Param [companyId]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "获取院校/集团/项目下拉框数据", notes = "")
    @GetMapping("getEventTargetList")
    public ResponseBo getEventTargetList(@RequestParam("companyId") Long companyId) {
        List<String> datas = eventService.getEventTargetList(companyId);
        return new ListResponseBo<>(datas);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :获取活动主题 下拉框数据
     * @Param [companyId]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "获取活动主题 下拉框数据", notes = "")
    @GetMapping("getEventThemeList")
    public ResponseBo getEventThemeList(@RequestParam("companyId") Long companyId) {
        List<String> datas = eventService.getEventThemeList(companyId);
        return new ListResponseBo<>(datas);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description : 编辑评论
     * @Param [commentDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "编辑评论")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/活动管理/编辑评论")
    @PostMapping("editComment")
    public ResponseBo editComment(@RequestBody @Validated(CommentDto.Add.class)  CommentDto commentDto) {
        return SaveResponseBo.ok(eventService.editComment(commentDto));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.AgentContractVo>
     * @Description : 评论列表数据
     * @Param [searchBean]
     * <AUTHOR>
     */
    @ApiOperation(value = "评论列表数据")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/活动管理/查询评论")
    @PostMapping("getComments")
    public ResponseBo<CommentVo> getComment(@RequestBody SearchBean<CommentDto> searchBean) {
        List<CommentVo> datas = eventService.getComments(searchBean.getData(), searchBean);
        Page p = BeanCopyUtils.objClone(searchBean, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.MediaAndAttachedDto>
     * @Description : 查询活动附件
     * @Param [voSearchBean]
     * <AUTHOR>
     */
    @ApiOperation(value = "查询活动附件", notes = "keyWord为关键词")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/活动管理/查询附件")
    @PostMapping("getItemMedia")
    public ResponseBo<MediaAndAttachedVo> getItemMedia(@RequestBody SearchBean<MediaAndAttachedDto> voSearchBean) {
        List<MediaAndAttachedVo> staffMedia = eventService.getItemMedia(voSearchBean.getData(), voSearchBean);
        Page page = BeanCopyUtils.objClone(voSearchBean, Page::new);
        return new ListResponseBo<>(staffMedia, page);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.MediaAndAttachedDto>
     * @Description : 保存活动附件
     * @Param [mediaAttachedVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "保存活动附件")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/活动管理/保存附件")
    @PostMapping("upload")
    public ResponseBo<MediaAndAttachedVo> addAgentMedia(@RequestBody  @Validated(MediaAndAttachedDto.Add.class)  ValidList<MediaAndAttachedDto> mediaAttachedVo) {
        return new ListResponseBo<>(eventService.addItemMedia(mediaAttachedVo));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :结束
     * @Param [eventId]
     * <AUTHOR>
     */
    @ApiOperation(value = "结束", notes = "eventId活动id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/活动管理/结束")
    @PostMapping("end")
    public ResponseBo end(@RequestParam Long eventId) {
        eventService.end(eventId);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :结束
     * @Param [eventId]
     * <AUTHOR>
     */
    @ApiOperation(value = "结束（免评价）", notes = "eventId活动id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/活动管理/结束（免评价）")
    @PostMapping("endWithoutRemark")
    public ResponseBo endWithoutRemark(@RequestParam Long eventId) {
        eventService.endWithoutRemark(eventId);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :计划
     * @Param [eventId]
     * <AUTHOR>
     */
    @ApiOperation(value = "计划", notes = "eventId活动id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/活动管理/计划")
    @PostMapping("plan")
    public ResponseBo plan(@RequestParam Long eventId) {
        eventService.plan(eventId);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :延期
     * @Param [eventId]
     * <AUTHOR>
     */
    @ApiOperation(value = "延期", notes = "eventId活动id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/活动管理/延期")
    @PostMapping("postpone")
    public ResponseBo postpone(@RequestParam Long eventId) {
        eventService.postpone(eventId);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :取消
     * @Param [eventId]
     * <AUTHOR>
     */
    @ApiOperation(value = "取消", notes = "eventId活动id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/活动管理/取消")
    @PostMapping("cancel")
    public ResponseBo cancel(@RequestParam Long eventId) {
        eventService.cancel(eventId);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "修改接口(上下架华通伙伴)", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/活动管理/更新活动(上下架华通伙伴)")
    @PostMapping("updateHuatongPartner")
    public ResponseBo<EventVo> updateHuatongPartner(@RequestParam Long eventId) {
        eventService.updateHuatongPartner(eventId);
        return UpdateResponseBo.ok();
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.EventVo>
     * @Description :
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "活动数据总览", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/活动管理/活动数据总览")
    @PostMapping("eventDatas")
    public ResponseBo<EventDataVo> getEventDatas(@RequestBody SearchBean<EventQueryDto> page) {
        List<EventDataVo> datas = eventService.getEventDatas(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.EventVo>
     * @Description :
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "活动数据总览列表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/活动管理/活动数据总览列表")
    @PostMapping("eventDatasList")
    public ResponseBo<EventDataVo> getEventDatasList(@RequestBody SearchBean<EventQueryDto> page) {
        List<EventDataVo> datas = eventService.getEventDatasList(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :活动举办区域下拉框数据
     * @Param [companyId]
     * <AUTHOR>
     */
    @ApiIgnore
    @ApiOperation(value = "活动举办区域下拉框数据", notes = "")
    @GetMapping("getEventStateSelect")
    public ResponseBo<AreaStateVo> getEventStateSelect(@RequestParam("companyId") Long companyId) {
        List<AreaStateVo> datas = eventService.getEventStateList(companyId);
        return new ListResponseBo<>(datas);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :活动对象国家下拉框数据
     * @Param [companyId]
     * <AUTHOR>
     */
    @ApiIgnore
    @ApiOperation(value = "活动对象国家下拉框数据", notes = "")
    @GetMapping("getEventCountrySelect")
    public ResponseBo<AreaCountryVo> getEventCountrySelect(@RequestParam("companyId") Long companyId) {
        List<AreaCountryVo> datas = eventService.getEventCountryList(companyId);
        return new ListResponseBo<>(datas);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :活动负责人下拉框数据
     * @Param [companyId]
     * <AUTHOR>
     */
    @ApiIgnore
    @ApiOperation(value = "活动负责人下拉框数据", notes = "")
    @GetMapping("getEventStaffSelect")
    public ResponseBo<StaffVo> getEventStaffSelect(@RequestParam("companyId") Long companyId) {
        List<StaffVo> datas = eventService.getEventStaffList(companyId);
        return new ListResponseBo<>(datas);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :活动第二负责人下拉框数据
     * @Param [companyId]
     * <AUTHOR>
     */
    @ApiIgnore
    @ApiOperation(value = "活动第二负责人下拉框数据", notes = "")
    @GetMapping("getEventStaff2Select")
    public ResponseBo<StaffVo> getEventStaff2Select(@RequestParam("companyId") Long companyId) {
        List<StaffVo> datas = eventService.getEventStaff2List(companyId);
        return new ListResponseBo<>(datas);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :活动类型下拉框数据
     * @Param [companyId]
     * <AUTHOR>
     */
    @ApiIgnore
    @ApiOperation(value = "活动类型下拉框数据", notes = "")
    @GetMapping("getEventTypeSelect")
    public ResponseBo<EventTypeVo> getEventTypeSelect(@RequestParam("companyId") Long companyId) {
        List<EventTypeVo> datas = eventService.getEventTypeSelect(companyId);
        return new ListResponseBo<>(datas);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :活动举办城市下拉框数据
     * @Param [companyId]
     * <AUTHOR>
     */
    @ApiIgnore
    @ApiOperation(value = "活动举办城市下拉框数据", notes = "")
    @GetMapping("getEventCitySelect")
    public ResponseBo<AreaCityVo> getEventCitySelect(@RequestParam("companyId") Long companyId) {
        List<AreaCityVo> datas = eventService.getEventCitySelect(companyId);
        return new ListResponseBo<>(datas);
    }

    /**
     * 活动搜索维度下拉框
     *
     * @param
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "活动搜索维度下拉框", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, description = "销售中心/活动管理/活动搜索维度下拉框")
    @GetMapping("getEventSearchBySelect")
    public ResponseBo<Map<String, Object>> getEventSearchBySelect() {
        return new ListResponseBo<>(ProjectExtraEnum.enumsTranslation2Arrays(ProjectExtraEnum.EVENT_SEARCH_BY));
    }

    /**
     * 活动搜索维度关联下拉框
     *
     * @param
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "活动搜索维度关联下拉框", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, description = "销售中心/活动管理/活动搜索维度关联下拉框")
    @PostMapping("getLastEventSearchSelect")
    public ResponseBo<?> getLastEventSearchSelect(@RequestParam("companyId") Long companyId, @RequestParam("groupBy") Integer groupBy) {
        switch (groupBy) {
            case 0:
                return getEventCountrySelect(companyId);
            case 1:
                return getEventStateSelect(companyId);
            case 2:
                return getEventStaffSelect(companyId);
            case 3:
                return getEventStaffSelect(companyId);
            case 4:
                return getEventStaff2Select(companyId);
            case 5:
                return getEventTypeSelect(companyId);
            default:
                return new ListResponseBo<>();
        }
    }

    /**
     * 活动搜索城市下拉框
     *
     * @param
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "活动搜索城市下拉框", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, description = "销售中心/活动管理/活动搜索城市下拉框")
    @PostMapping("getCitySelect")
    public ResponseBo getCitySelect(@RequestParam("companyId") Long companyId, @RequestBody List<Long> eventStateIds) {
        List<AreaCityVo> datas = eventService.getCitySelect(companyId, eventStateIds);
        return new ListResponseBo<>(datas);
    }

    /**
     * 活动状态下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "活动状态下拉框数据", notes = "")
    @GetMapping("getEventStatusSelect")
    public ResponseBo getEventStatusSelect() {
        return new ListResponseBo<>(ProjectExtraEnum.enumsTranslation2Arrays(ProjectExtraEnum.EVENT_STATUS));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.EventCostVo>
     * @Description :列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "活动费用列表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/活动管理/活动费用列表")
    @PostMapping("getCostList")
    public ResponseBo<EventVo> getCostList(@RequestBody SearchBean<EventCostDto> page) {
        List<EventVo> datas = eventService.getCostList(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    @ApiOperation(value = "活动报名名册汇总列表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/活动管理/活动报名名册汇总列表")
    @PostMapping("getEventRegistrationStatistics")
    public ResponseBo<EventRegistrationStatisticsVo> getEventRegistrationStatistics(@RequestBody SearchBean<EventRegistrationStatisticsDto> page) {
        List<EventRegistrationStatisticsVo> datas = eventService.getEventRegistrationStatistics(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    //费用校验接口
    @ApiOperation(value = "活动费用校验", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/活动管理/活动费用校验")
    @PostMapping("checkEventCost")
    public ResponseBo checkEventActivityFees(@RequestBody EventDto eventDto) {
        return eventService.checkEventActivityFees(eventDto);
    }
}

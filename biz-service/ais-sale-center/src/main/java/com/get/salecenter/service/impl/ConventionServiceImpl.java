package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.vo.StaffVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.dao.sale.ConventionMapper;
import com.get.salecenter.dao.sale.EventMapper;
import com.get.salecenter.vo.AssignConventionOperatePersonListVo;
import com.get.salecenter.vo.ConventionVo;
import com.get.salecenter.vo.EventVo;
import com.get.salecenter.vo.MediaAndAttachedVo;
import com.get.salecenter.entity.Convention;
import com.get.salecenter.entity.ConventionStaff;
import com.get.salecenter.entity.Event;
import com.get.salecenter.entity.EventCost;
import com.get.salecenter.service.IConventionService;
import com.get.salecenter.service.IConventionStaffService;
import com.get.salecenter.service.IDeleteService;
import com.get.salecenter.service.IEventCostService;
import com.get.salecenter.service.IEventService;
import com.get.salecenter.service.IMediaAndAttachedService;
import com.get.salecenter.dto.*;
import com.get.salecenter.dto.ConventionDto;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: Sea
 * @create: 2020/7/1 10:01
 * @verison: 1.0
 * @description: 峰会管理业务实现类
 */
@Service
public class ConventionServiceImpl extends ServiceImpl<ConventionMapper,Convention> implements IConventionService {
    @Resource
    private ConventionMapper conventionMapper;
    @Resource
    private IMediaAndAttachedService attachedService;
    @Resource
    private UtilService utilService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private IDeleteService deleteService;
    @Lazy
    @Resource
    private IEventService eventService;

    @Resource
    private EventMapper eeventMapper;

    @Resource
    private IEventCostService eventCostService;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Lazy
    @Resource
    private IConventionStaffService conventionStaffService;

    @Override
    public ConventionVo findConventionById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        Convention convention = conventionMapper.selectById(id);
        if (GeneralTool.isEmpty(convention)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        if (!SecureUtil.validateCompany(convention.getFkCompanyId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
        }
        ConventionVo conventionVo = BeanCopyUtils.objClone(convention, ConventionVo::new);
        Result<String> result = permissionCenterClient.getCompanyNameById(convention.getFkCompanyId());
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            conventionVo.setCompanyName(result.getData());
        }


        if (GeneralTool.isNotEmpty(convention.getFkEventId())){
            Event event = eventService.getEventById(convention.getFkEventId());


            if (GeneralTool.isNotEmpty(event)&&GeneralTool.isNotEmpty(event.getEventTheme())) {
                String cityName;
                String eventTheme;
                if (GeneralTool.isEmpty(event.getFkAreaCityIdHold())){
                    cityName = "无举办城市";
                }else {
                    cityName = institutionCenterClient.getCityChnNameById(event.getFkAreaCityIdHold()).getData();
                    if (GeneralTool.isEmpty(cityName)){
                        cityName = "无举办城市";
                    }
                }
                if (GeneralTool.isEmpty(event.getEventTheme())){
                    eventTheme = "无活动主题";
                }else {
                    eventTheme = event.getEventTheme();
                }
                conventionVo.setEventThemeName("【"+ event.getNum()+"】"+eventTheme+"，"+cityName);
            }
        }

        MediaAndAttachedDto attachedVo = new MediaAndAttachedDto();
        attachedVo.setFkTableName(TableEnum.SALE_CONVENTION.key);
        attachedVo.setFkTableId(id);
        List<MediaAndAttachedVo> mediaAndAttachedVos = attachedService.getMediaAndAttachedDto(attachedVo);
        conventionVo.setMediaAndAttachedDtos(mediaAndAttachedVos);
        return conventionVo;
    }

    @Override
    public Convention getConventionById(Long id) {
        return conventionMapper.selectById(id);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long addConvention(ConventionDto conventionDto) {
        if (GeneralTool.isEmpty(conventionDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        if (GeneralTool.isNotEmpty(conventionDto.getFkEventId())){
            validateAddEventBinding(conventionDto.getFkEventId());
        }
        Convention convention = BeanCopyUtils.objClone(conventionDto, Convention::new);
        utilService.updateUserInfoToEntity(convention);
        conventionMapper.insert(convention);
        return convention.getId();
    }

    private void validateAddEventBinding(Long fkEventId) {
        LambdaQueryWrapper<Convention> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(Convention::getFkEventId, fkEventId);
        List<Convention> conventions = conventionMapper.selectList(lambdaQueryWrapper);
        if (GeneralTool.isNotEmpty(conventions)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("add_convention_and_event_exist_binding"));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (conventionMapper.selectById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        //判断该峰会能否删除
        deleteService.deleteValidateConvention(id);
        conventionMapper.deleteById(id);
        //同时删除该表id下的所有媒体附件
        attachedService.deleteMediaAndAttachedByTableId(id, TableEnum.SALE_CONVENTION.key);
    }

    @Override
    public ConventionVo updateConvention(ConventionDto conventionDto) {
        if (conventionDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        Convention result = conventionMapper.selectById(conventionDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        Convention convention = BeanCopyUtils.objClone(conventionDto, Convention::new);

        validateEventBinding(conventionDto, result);

        utilService.updateUserInfoToEntity(convention);
        conventionMapper.updateByIdWithNull(convention);
        return findConventionById(convention.getId());
    }

    private void validateEventBinding(ConventionDto conventionDto, Convention result) {
        if (!Objects.equals(result.getFkEventId(), conventionDto.getFkEventId())){
            if (GeneralTool.isNotEmpty(conventionDto.getFkEventId())){
                LambdaQueryWrapper<Convention> lambdaQueryWrapper = Wrappers.lambdaQuery();
                lambdaQueryWrapper.eq(Convention::getFkEventId, conventionDto.getFkEventId());
                List<Convention> conventions = conventionMapper.selectList(lambdaQueryWrapper);
                if (GeneralTool.isNotEmpty(conventions)){
                    throw new GetServiceException(LocaleMessageUtils.getMessage("add_convention_and_event_exist_binding"));
                }

            }

            LambdaQueryWrapper<EventCost> lambdaQueryWrapper = Wrappers.lambdaQuery();
            lambdaQueryWrapper.eq(EventCost::getFkEventId, result.getFkEventId());
            List<EventCost> eventCosts = eventCostService.getEventCostsByCondition(lambdaQueryWrapper);
            if (GeneralTool.isNotEmpty(eventCosts)){
                throw new GetServiceException(LocaleMessageUtils.getMessage("convention_and_event_exist_binding"));
            }
        }
    }

    @Override
    public List<ConventionVo> getConventions(ConventionDto conventionDto, Page page) {
        LambdaQueryWrapper<Convention> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        //不选所属公司时
        if (GeneralTool.isEmpty(conventionDto) || GeneralTool.isEmpty(conventionDto.getFkCompanyId())) {
            List<Long> companyIds = getCompanyIds();
            lambdaQueryWrapper.in(Convention::getFkCompanyId, companyIds);
        }
        if (GeneralTool.isNotEmpty(conventionDto)) {
            //查询条件-所属公司
            if (GeneralTool.isNotEmpty(conventionDto.getFkCompanyId())) {
                if (!SecureUtil.validateCompany(conventionDto.getFkCompanyId())) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
                }
                lambdaQueryWrapper.eq(Convention::getFkCompanyId, conventionDto.getFkCompanyId());
            }
            //查询条件-主题查询
            if (GeneralTool.isNotEmpty(conventionDto.getThemeName())) {
                lambdaQueryWrapper.like(Convention::getThemeName, conventionDto.getThemeName());
            }
            //查询条件-年度查询  从输入年度的1月1日 到第二年的1月1日
            if (GeneralTool.isNotEmpty(conventionDto.getYear())) {
                lambdaQueryWrapper.between(Convention::getStartTime, conventionDto.getYear() + "-01-01", new Long(conventionDto.getYear()) + 1L + "-01-01");
            }
        }
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        List<Convention> conventions = conventionMapper.selectByExample(example);
//        page.restPage(conventions);
        lambdaQueryWrapper.orderByDesc(Convention::getGmtCreate);
        IPage<Convention> pages = conventionMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
        List<Convention> conventions = pages.getRecords();
        page.setAll((int) pages.getTotal());
        if (GeneralTool.isEmpty(conventions)){
            return Collections.emptyList();
        }

        List<ConventionVo> convertDatas = new ArrayList<>();

        //公司ids
        Set<Long> companyIds = conventions.stream().map(Convention::getFkCompanyId).collect(Collectors.toSet());
        //根据公司ids获取名称map
        Map<Long, String> companyNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(companyIds)) {
            Result<Map<Long, String>> result = permissionCenterClient.getCompanyNamesByIds(companyIds);
            if (result.isSuccess() && result.getData() != null) {
                companyNamesByIds = result.getData();
            }
        }

        Set<Long> ids = conventions.stream().map(Convention::getId).collect(Collectors.toSet());
        List<ConventionStaff> conventionStaffs = conventionStaffService.list(Wrappers.lambdaQuery(conventionStaffService.getEntityClass())
                .in(ConventionStaff::getFkConventionId, ids));

        Map<Long, List<ConventionStaff>> conventionStaffsMap = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(conventionStaffs)){
            conventionStaffsMap = conventionStaffs.stream().collect(Collectors.groupingBy(ConventionStaff::getFkConventionId));
        }

        Set<String> createUsers = conventions.stream().map(Convention::getGmtCreateUser).collect(Collectors.toSet());
        List<StaffVo> staffVos = permissionCenterClient.getStaffByCreateUsers(createUsers).getData();
        Map<String, Long> createUserMap = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(staffVos)){
            createUserMap = staffVos.stream().collect(Collectors.toMap(StaffVo::getLoginId, StaffVo::getId));
        }

        for (Convention convention : conventions) {
            //历史代码 改成批量查询 暂时不想管。。
            //根据各自峰会id查找对应的流程数procedureCount、报名数registrationCount、参展人数personCount
            Long procedureCount = conventionMapper.getProcedureCount(convention.getId());
            Long registrationCount = conventionMapper.getRegistrationCount(convention.getId());
            Long personCount = conventionMapper.getPersonCount(convention.getId());
            String companyName = companyNamesByIds.get(convention.getFkCompanyId());
            Event event = null;
            if (GeneralTool.isNotEmpty(convention.getFkEventId())) {
                 event = eeventMapper.selectById(convention.getFkEventId());
            }


            //将结果统一到ConventionDto 返回给前端
            ConventionVo conventionVo = BeanCopyUtils.objClone(convention, ConventionVo::new);
            conventionVo.setProcedureCount(procedureCount);
            conventionVo.setRegistrationCount(registrationCount);
            conventionVo.setPersonCount(personCount);
            conventionVo.setCompanyName(companyName);
            List<ConventionStaff> conventionStaffList = conventionStaffsMap.get(conventionVo.getId());
            if(GeneralTool.isNotEmpty(event)){
                conventionVo.setNum(event.getNum());
            }
            if (GeneralTool.isEmpty(conventionStaffList)){
                //无配置
                conventionVo.setHasOperatePermission(true);
            } else {
                //有配置 判断是否创建人
                //是创建人
                if (SecureUtil.getStaffId().equals(createUserMap.get(convention.getGmtCreateUser()))){
                    conventionVo.setHasOperatePermission(true);
                }else {
                    //非创建人
                    List<ConventionStaff> staffConfigs = conventionStaffList.stream().filter(c -> c.getFkStaffId().equals(SecureUtil.getStaffId())).collect(Collectors.toList());
                    Map<Integer, List<ConventionStaff>> staffModeMap = conventionStaffList.stream().collect(Collectors.groupingBy(ConventionStaff::getMode));
                    //查看是否有配置登陆人权限
                    //没配置登录人
                    if (GeneralTool.isEmpty(staffConfigs)){
                        List<ConventionStaff> allows = staffModeMap.get(ProjectExtraEnum.CONVENTION_STAFF_ALLOW.key);
                        List<ConventionStaff> prohibits = staffModeMap.get(ProjectExtraEnum.CONVENTION_STAFF_PROHIBIT.key);
                        //只配置禁止进入
                        if (GeneralTool.isEmpty(allows)&&GeneralTool.isNotEmpty(prohibits)){
                            conventionVo.setHasOperatePermission(true);
                        }else {
                            conventionVo.setHasOperatePermission(false);
                        }
                    }else {
                        //配置了登陆人
                        Integer mode = staffConfigs.get(0).getMode();
                        if (ProjectExtraEnum.CONVENTION_STAFF_PROHIBIT.key.equals(mode)){
                            conventionVo.setHasOperatePermission(false);
                        }else {
                            conventionVo.setHasOperatePermission(true);
                        }
                    }

                }
            }
            convertDatas.add(conventionVo);
        }
        return convertDatas;
    }

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :获取登录人对应所有公司id集合
     * @Param []
     * <AUTHOR>
     */
    private List<Long> getCompanyIds() {
        List<Long> companyIds = SecureUtil.getCompanyIdsByStaffId(GetAuthInfo.getStaffId());
        if (GeneralTool.isEmpty(companyIds)) {
            companyIds.add(0L);
        }
        return companyIds;
    }

    @Override
    public MediaAndAttachedVo addConventionMedia(MediaAndAttachedDto mediaAttachedVo) {
        if (GeneralTool.isEmpty(mediaAttachedVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        //设置插入的表
        mediaAttachedVo.setFkTableName(TableEnum.SALE_CONVENTION.key);
        return attachedService.addMediaAndAttached(mediaAttachedVo);
    }

    @Override
    public List<Map<String, Object>> findMediaAndAttachedType() {
        return FileTypeEnum.enumsTranslation2Arrays(FileTypeEnum.CONVENTION);
    }

    @Override
    public String getConventionNameById(Long id) {
        return conventionMapper.getConventionNameById(id);
    }

    @Override
    public Map<Long,String> getConventionNameByIds(Set<Long> ids){
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(ids)) {
            return map;
        }
        List<Convention> conventions = this.conventionMapper.selectList(Wrappers.<Convention>query().lambda().in(Convention::getId, ids));
        for (Convention convention : conventions) {
            map.put(convention.getId(), convention.getThemeName());
        }
        return map;
    }

    @Override
    public List<BaseSelectEntity> getEventListByName(Long companyId, String eventName) {
        List<EventVo> events = eventService.getEventsByName(companyId, eventName);

        if (GeneralTool.isNotEmpty(events)){
            List<BaseSelectEntity> baseSelectEntities = events.stream().map(e -> {
                BaseSelectEntity baseSelectEntity = new BaseSelectEntity();
                baseSelectEntity.setId(e.getId());
                baseSelectEntity.setName(e.getFullName());
                return baseSelectEntity;
            }).collect(Collectors.toList());
            return baseSelectEntities;
        }else {
            return Collections.emptyList();
        }

    }

    @Override
    public List<Convention> getEventCopyList(ConventionCopyDto conventionCopyDto) {
        LambdaQueryWrapper<Convention> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Convention::getFkCompanyId, conventionCopyDto.getFkCompanyId());
        lambdaQueryWrapper.ne(Convention::getId, conventionCopyDto.getId());  //过滤本身峰会
        List<Convention> conventions = conventionMapper.selectList(lambdaQueryWrapper);
        return conventions;
    }

    /**
     * 分配峰会操作人员权限
     * @param assignConventionOperatePersonDto
     */
    @Override
    public void assignConventionOperatePerson(AssignConventionOperatePersonDto assignConventionOperatePersonDto) {

        LambdaQueryWrapper<ConventionStaff> wrapper = Wrappers.lambdaQuery(conventionStaffService.getEntityClass());
        wrapper.eq(ConventionStaff::getFkConventionId, assignConventionOperatePersonDto.getFkConventionId());
        wrapper.in(ConventionStaff::getFkStaffId, assignConventionOperatePersonDto.getFkStaffIds());
        List<ConventionStaff> conventionStaffs = conventionStaffService.list(wrapper);

        List<Long> saveStaffIds = Lists.newArrayList();
        if (GeneralTool.isEmpty(conventionStaffs)){
            saveStaffIds = assignConventionOperatePersonDto.getFkStaffIds();
        }else {
            List<Long> staffIds = conventionStaffs.stream().map(ConventionStaff::getFkStaffId).collect(Collectors.toList());
            saveStaffIds = assignConventionOperatePersonDto.getFkStaffIds().stream().filter(staffId->!staffIds.contains(staffId)).collect(Collectors.toList());
        }

        if (GeneralTool.isEmpty(saveStaffIds)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("all_operators_have_been_assigned_permissions"));
        }

        List<ConventionStaff> saveList = Lists.newArrayList();
        for (Long saveStaffId : saveStaffIds) {
            ConventionStaff conventionStaff = new ConventionStaff();
            conventionStaff.setFkConventionId(assignConventionOperatePersonDto.getFkConventionId());
            conventionStaff.setFkStaffId(saveStaffId);
            if (GeneralTool.isNotEmpty(assignConventionOperatePersonDto.getMode())){
                conventionStaff.setMode(assignConventionOperatePersonDto.getMode());
            }else {
                conventionStaff.setMode(ProjectExtraEnum.CONVENTION_STAFF_ALLOW.key);
            }
            utilService.setCreateInfo(conventionStaff);
            saveList.add(conventionStaff);
        }

        //saveBatch方法多加了个更新时间
        for (ConventionStaff conventionStaff : saveList) {
            int i = conventionStaffService.getBaseMapper().insert(conventionStaff);
            if (i<1){
                throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
            }
        }

//        boolean saveBatch = conventionStaffService.saveBatch(saveList);
//        if (!saveBatch){
//            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
//        }
    }

    /**
     * 分配峰会操作人员列表
     * @param assignConventionOperatePersonListDto
     * @return
     */
    @Override
    public List<AssignConventionOperatePersonListVo> getAssignConventionOperatePersonList(AssignConventionOperatePersonListDto assignConventionOperatePersonListDto) {

        List<ConventionStaff> conventionStaffs = conventionStaffService.list(Wrappers.lambdaQuery(conventionStaffService.getEntityClass())
                .eq(ConventionStaff::getFkConventionId, assignConventionOperatePersonListDto.getFkConventionId())
                .orderByAsc(ConventionStaff::getGmtCreate));

        if (GeneralTool.isEmpty(conventionStaffs)){
            return Collections.emptyList();
        }
        Set<Long> staffIds = conventionStaffs.stream().map(ConventionStaff::getFkStaffId).collect(Collectors.toSet());
        Map<Long, String> staffNamesByIds = permissionCenterClient.getStaffNamesByIds(staffIds);
        List<AssignConventionOperatePersonListVo> assignConventionOperatePersonListVos = BeanCopyUtils.copyListProperties(conventionStaffs, AssignConventionOperatePersonListVo::new);

        for (AssignConventionOperatePersonListVo assignConventionOperatePersonListVo : assignConventionOperatePersonListVos) {
            assignConventionOperatePersonListVo.setStaffFullName(staffNamesByIds.get(assignConventionOperatePersonListVo.getFkStaffId()));
            assignConventionOperatePersonListVo.setModeName(ProjectExtraEnum.getValueByKey(assignConventionOperatePersonListVo.getMode(),ProjectExtraEnum.CONVENTION_STAFF_MODE_TYPE));
        }
        return assignConventionOperatePersonListVos;
    }

    /**
     * 删除峰会操作人员
     * @param id
     */
    @Override
    public void deleteAssignConventionOperatePerson(Long id) {
        boolean b = conventionStaffService.removeById(id);
        if (!b){
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }

    /**
     * 登陆人是否有操作峰会权限
     * @param conventionId
     * @return
     */
    @Override
    public Boolean hasConventionOperatePermission(Long conventionId) {
        //若无配置 或者配置中全是禁止进入的配置  则全部用户可以进入
        List<ConventionStaff> conventionStaffList = conventionStaffService.list(Wrappers.lambdaQuery(conventionStaffService.getEntityClass())
                .eq(ConventionStaff::getFkConventionId, conventionId));
        if(GeneralTool.isEmpty(conventionStaffList)){
            return true;
        }

        Long staffId = SecureUtil.getStaffId();
        Convention convention = getById(conventionId);
        String gmtCreateUser = convention.getGmtCreateUser();
        StaffVo staffVo = permissionCenterClient.getStaffByCreateUser(gmtCreateUser).getData();
        if (GeneralTool.isNotEmpty(staffVo)&&staffId.equals(staffVo.getId())){
            return true;
        }

        List<ConventionStaff> conventionStaffs = conventionStaffList.stream().filter(c->c.getFkStaffId().equals(staffId)).collect(Collectors.toList());
        //没配置登陆人
        if (GeneralTool.isEmpty(conventionStaffs)){
            Map<Integer, List<ConventionStaff>> staffModeMap = conventionStaffList.stream().collect(Collectors.groupingBy(ConventionStaff::getMode));
            List<ConventionStaff> allows = staffModeMap.get(ProjectExtraEnum.CONVENTION_STAFF_ALLOW.key);
            List<ConventionStaff> prohibits = staffModeMap.get(ProjectExtraEnum.CONVENTION_STAFF_PROHIBIT.key);
            //只配置禁止进入
            if (GeneralTool.isEmpty(allows)&&GeneralTool.isNotEmpty(prohibits)){
                return true;
            }else {
                return false;
            }
        }else {
            Integer mode = conventionStaffs.get(0).getMode();
            if (ProjectExtraEnum.CONVENTION_STAFF_PROHIBIT.key.equals(mode)){
                return false;
            }else {
                return true;
            }
        }

    }

    /**
     * 有配置操作权限的峰会下拉
     * @param companyId
     * @return
     */
    @Override
    public List<BaseSelectEntity> getConventionWithPermissionConfigSelect(Long companyId) {
        List<Convention> conventions = list(Wrappers.lambdaQuery(Convention.class).eq(Convention::getFkCompanyId, companyId).orderByDesc(Convention::getGmtCreate));
        if (GeneralTool.isEmpty(conventions)){
            return Collections.emptyList();
        }
        List<Long> conventionIds = conventions.stream().map(Convention::getId).collect(Collectors.toList());

        List<ConventionStaff> conventionStaffList = conventionStaffService.list(Wrappers.lambdaQuery(ConventionStaff.class).in(ConventionStaff::getFkConventionId, conventionIds));
        if (GeneralTool.isEmpty(conventionStaffList)){
            return Collections.emptyList();
        }
        Set<Long> conventionWithPermissionIds = conventionStaffList.stream().collect(Collectors.groupingBy(ConventionStaff::getFkConventionId)).keySet();
        return conventions.stream().filter(convention -> conventionWithPermissionIds.contains(convention.getId())).map(convention -> {
            BaseSelectEntity baseSelectEntity = new BaseSelectEntity();
            baseSelectEntity.setId(convention.getId());
            baseSelectEntity.setName(convention.getThemeName());
            return baseSelectEntity;
        }).collect(Collectors.toList());
    }

    /**
     * 复制峰会操作人员配置
     * @param sourceConventionId
     * @param targetConventionId
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void copyConventionOperatePersonConfig(Long sourceConventionId, Long targetConventionId) {
        if (Objects.equals(sourceConventionId, targetConventionId)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("consistent_with_the_current_summit"));
        }

        List<ConventionStaff> conventionStaffs = conventionStaffService.list(Wrappers.lambdaQuery(ConventionStaff.class)
                .eq(ConventionStaff::getFkConventionId, sourceConventionId));

        if (GeneralTool.isEmpty(conventionStaffs)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_replicable_configuration"));
        }

        //删除原配置
        conventionStaffService.remove(Wrappers.lambdaQuery(ConventionStaff.class)
                .eq(ConventionStaff::getFkConventionId,targetConventionId));

        conventionStaffs.forEach(conventionStaff -> {
            conventionStaff.setId(null);
            conventionStaff.setFkConventionId(targetConventionId);
        });

        //saveBatch方法多了个更新时间
        for (ConventionStaff conventionStaff : conventionStaffs) {
            utilService.setCreateInfo(conventionStaff);
            int i = conventionStaffService.getBaseMapper().insert(conventionStaff);
            if (i<1){
                throw new GetServiceException(LocaleMessageUtils.getMessage("copy_permission_configuration_failed"));
            }
        }
//        boolean saveBatch = conventionStaffService.saveBatch(conventionStaffs);
//        if (!saveBatch){
//            throw new GetServiceException(LocaleMessageUtils.getMessage("copy_permission_configuration_failed"));
//        }

    }

    /**
     * 活动下拉
     * @param companyId
     * @return
     */
    @Override
    public List<BaseSelectEntity> getEventSelect(Long companyId) {
        List<EventVo> events = eventService.getEventSelect(companyId);

        if (GeneralTool.isNotEmpty(events)){
            List<BaseSelectEntity> baseSelectEntities = events.stream().map(e -> {
                BaseSelectEntity baseSelectEntity = new BaseSelectEntity();
                baseSelectEntity.setId(e.getId());
                baseSelectEntity.setName(e.getFullName());
                return baseSelectEntity;
            }).collect(Collectors.toList());
            return baseSelectEntities;
        }else {
            return Collections.emptyList();
        }
    }

    /**
     * 校代住房表单邮件发送
     */
    private void sendEmailTo(){

    }


}

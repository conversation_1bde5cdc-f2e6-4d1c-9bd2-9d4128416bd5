package com.get.salecenter.service.impl;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.AgentContractFormulaCommissionMapper;
import com.get.salecenter.vo.AgentContractFormulaCommissionVo;
import com.get.salecenter.entity.AgentContractFormulaCommission;
import com.get.salecenter.service.IAgentContractFormulaCommissionService;
import com.get.salecenter.dto.AgentContractFormulaCommissionDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: Sea
 * @create: 2021/4/23 18:35
 * @verison: 1.0
 * @description:
 */
@Service
public class AgentContractFormulaCommissionServiceImpl implements IAgentContractFormulaCommissionService {
    @Resource
    private AgentContractFormulaCommissionMapper agentContractFormulaCommissionMapper;
    @Resource
    private UtilService utilService;

    @Override
    public Long addContractFormulaCommission(AgentContractFormulaCommissionDto agentContractFormulaCommissionDto) {
        if (GeneralTool.isEmpty(agentContractFormulaCommissionDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        if (GeneralTool.isEmpty(agentContractFormulaCommissionDto.getCommissionRateAg())
                && GeneralTool.isEmpty(agentContractFormulaCommissionDto.getFixedAmountAg())
                && GeneralTool.isEmpty(agentContractFormulaCommissionDto.getReceivableRateAg())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("required_rate_or_amount"));
        }
        Integer maxStep = agentContractFormulaCommissionMapper.getMaxStep(agentContractFormulaCommissionDto.getFkAgentContractFormulaId());
        AgentContractFormulaCommission contractFormulaCommission = BeanCopyUtils.objClone(agentContractFormulaCommissionDto, AgentContractFormulaCommission::new);
        contractFormulaCommission.setStep(maxStep);
        utilService.updateUserInfoToEntity(contractFormulaCommission);
        agentContractFormulaCommissionMapper.insert(contractFormulaCommission);
        return contractFormulaCommission.getId();
    }

    @Override
    public void deleteByFkid(Long contractFormulaId) {
//        Example example = new Example(AgentContractFormulaCommission.class);
//        example.createCriteria().andEqualTo("fkAgentContractFormulaId", contractFormulaId);
//        agentContractFormulaCommissionMapper.deleteByExample(example);
        agentContractFormulaCommissionMapper.delete(Wrappers.<AgentContractFormulaCommission>lambdaQuery().eq(AgentContractFormulaCommission::getFkAgentContractFormulaId, contractFormulaId));
    }

    @Override
    public List<AgentContractFormulaCommissionVo> getAgentContractFormulaCommissionByFkid(Long id) {
//        Example example = new Example(AgentContractFormulaCommission.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkAgentContractFormulaId",id);
//        example.orderBy("step").asc();
//        List<AgentContractFormulaCommission> agentContractFormulaCommissions = agentContractFormulaCommissionMapper.selectByExample(example);
        List<AgentContractFormulaCommission> agentContractFormulaCommissions = agentContractFormulaCommissionMapper.selectList(Wrappers.<AgentContractFormulaCommission>lambdaQuery()
                .eq(AgentContractFormulaCommission::getFkAgentContractFormulaId, id)
                .orderByAsc(AgentContractFormulaCommission::getStep));

        return agentContractFormulaCommissions.stream().map(agentContractFormulaCommission -> BeanCopyUtils.objClone(agentContractFormulaCommission, AgentContractFormulaCommissionVo::new)).collect(Collectors.toList());
    }
}

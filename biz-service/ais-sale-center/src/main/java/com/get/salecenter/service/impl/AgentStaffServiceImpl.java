package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.GetStringUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.dao.sale.AgentCompanyMapper;
import com.get.salecenter.dao.sale.AgentStaffMapper;
import com.get.salecenter.dao.sale.StudentOfferMapper;
import com.get.salecenter.dto.AgentStaffDto;
import com.get.salecenter.entity.Agent;
import com.get.salecenter.entity.AgentCompany;
import com.get.salecenter.entity.AgentStaff;
import com.get.salecenter.service.*;
import com.get.salecenter.utils.sale.GetAgentLabelDataUtils;
import com.get.salecenter.vo.*;
import com.google.common.collect.Maps;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: Sea
 * @create: 2020/10/16 15:16
 * @verison: 1.0
 * @description:
 */
@Service
public class AgentStaffServiceImpl extends BaseServiceImpl<AgentStaffMapper,AgentStaff> implements IAgentStaffService {

    @Resource
    private AgentStaffMapper agentStaffMapper;
    @Resource
    private UtilService utilService;
    @Lazy
    @Resource
    private IStudentAgentService studentAgentService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Lazy
    @Resource
    private IStaffBdCodeService staffBdCodeService;
    @Resource
    private AgentCompanyMapper agentCompanyMapper;
    @Resource
    private StudentOfferMapper studentOfferMapper;
    @Lazy
    @Resource
    private IAgentService agentService;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    private GetAgentLabelDataUtils getAgentLabelDataUtils;
    @Override
    public Long addAgentStaff(AgentStaffVo agentStaffVo) {
        AgentStaff agentStaff = BeanCopyUtils.objClone(agentStaffVo, AgentStaff::new);
        utilService.updateUserInfoToEntity(agentStaff);
        agentStaffMapper.insertSelective(agentStaff);
        return agentStaff.getId();
    }

    @Override
    public List<Long> getAgentIdsByBdNum(String bdNum) {
        List<Long> list = agentStaffMapper.getAgentIdsByBdNum(bdNum);
        //若为空 防止 in（）报错，默认给值为0
        if (GeneralTool.isEmpty(list)) {
            list.add(0L);
        }
        return list;
    }

    @Override
    public List<Long> getAgentIdsByBdAreaRegionId(Long fkAreaRegionId) {
        List<Long> data = new ArrayList<>();
        if (GeneralTool.isNotEmpty(fkAreaRegionId)) {
            data = agentStaffMapper.getAgentIdsByBdAreaRegionId(fkAreaRegionId);
        } else {
            data.add(0L);
        }
        return data;
    }

    @Override
    public AgentStaff getAgentStaffByAgentId(Long id) {
//        Example example = new Example(AgentStaff.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("isActive", true);
//        criteria.andEqualTo("fkAgentId", id);
//        List<AgentStaff> agentStaffs = agentStaffMapper.selectByExample(example);
        List<AgentStaff> agentStaffs = agentStaffMapper.selectList(Wrappers.<AgentStaff>lambdaQuery().eq(AgentStaff::getIsActive, true).eq(AgentStaff::getFkAgentId, id));
        if (GeneralTool.isNotEmpty(agentStaffs)) {
            return agentStaffs.get(0);
        }
        return null;
    }

    @Override
    public Map<Long, AgentStaff> getAgentStaffByAgentIds(Set<Long> ids) {
        Map<Long, AgentStaff> map = new HashMap<>();
        if (GeneralTool.isEmpty(ids)) {
            return map;
        }
//        Example example = new Example(AgentStaff.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("isActive", true);
//        criteria.andIn("fkAgentId", ids);
//        List<AgentStaff> agentStaffs = agentStaffMapper.selectByExample(example);

        List<AgentStaff> agentStaffs = agentStaffMapper.selectList(Wrappers.<AgentStaff>lambdaQuery()
                .eq(AgentStaff::getIsActive, true)
                .in(AgentStaff::getFkAgentId, ids));

        if (GeneralTool.isEmpty(agentStaffs)) {
            return map;
        }
        for (AgentStaff agentStaff : agentStaffs) {
            //如果集合中存在这个代理id，则跳过本次循环(只存在一个激活的代理)
            if (map.containsKey(agentStaff.getFkAgentId())) {
                continue;
            }
            map.put(agentStaff.getFkAgentId(), agentStaff);
        }
        return map;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setBd(AgentStaffDto agentStaffDto) {
        //代理绑定BD，提交时，需要验证这个代理所属公司，是否包含了BD所属公司，只有一致才能绑定
//        Example example1 = new Example(AgentCompany.class);
//        example1.createCriteria().andEqualTo("fkAgentId", agentStaffDto.getFkAgentId());
//        List<AgentCompany> agentCompanyList = agentCompanyMapper.selectByExample(example1);

        List<AgentCompany> agentCompanyList = agentCompanyMapper.selectList(Wrappers.<AgentCompany>lambdaQuery()
                .eq(AgentCompany::getFkAgentId, agentStaffDto.getFkAgentId()));
        if (GeneralTool.isEmpty(agentCompanyList)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        Result<Map<Long, Long>> result = permissionCenterClient.getCompanyIdByStaffIds(Collections.singleton(agentStaffDto.getFkStaffId()));
        Map<Long, Long> staffCompanyIdMap = new HashMap<>();
        if (result.isSuccess() && result.getData() != null) {
            staffCompanyIdMap = result.getData();
        }
//        Map<Long, Long> staffCompanyIdMap = permissionCenterClient.getCompanyIdByStaffIds(Collections.singleton(agentStaffDto.getFkStaffId()));
        Collection<Long> values = staffCompanyIdMap.values();

        boolean companyFlag = false;
        for (AgentCompany agentCompany : agentCompanyList) {
            for (Long value : values) {
                if (agentCompany.getFkCompanyId().equals(value)) {
                    companyFlag = true;
                    break;
                }
            }
        }
        if (!companyFlag) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("bd_agent_company_inconsistency"));
        }

        AgentStaff agentStaff = BeanCopyUtils.objClone(agentStaffDto, AgentStaff::new);
//        Example example = new Example(AgentStaff.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkAgentId", agentStaffDto.getFkAgentId());
//        criteria.andEqualTo("isActive", true);
        //是否绑定过BD
        List<AgentStaff> agentStaffs = agentStaffMapper.selectList(Wrappers.<AgentStaff>lambdaQuery()
                .eq(AgentStaff::getFkAgentId, agentStaffDto.getFkAgentId())
                .eq(AgentStaff::getIsActive, true));
        //没有，直接新增
        if (GeneralTool.isEmpty(agentStaffs)) {
            agentStaff.setIsActive(true);
            utilService.updateUserInfoToEntity(agentStaff);
            agentStaff.setActiveDate(new Date());
            agentStaffMapper.insertSelective(agentStaff);
        } else {
            if (agentStaffs.get(0).getFkStaffId().equals(agentStaff.getFkStaffId())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("bd_agent_exit"));
            }
            //有，先修改上一个BD的信息，在新增一个
            agentStaffs.get(0).setIsActive(false);
            utilService.updateUserInfoToEntity(agentStaffs.get(0));
            agentStaffs.get(0).setUnactiveDate(new Date());
            agentStaffMapper.updateById(agentStaffs.get(0));

            agentStaff.setIsActive(true);
            utilService.updateUserInfoToEntity(agentStaff);
            agentStaff.setActiveDate(new Date());
            agentStaffMapper.insertSelective(agentStaff);
        }
    }

    @Override
    public List<AgentStaffVo> getAgentStaffs(AgentStaffDto agentStaffDto, Page page) {
//        Example example = new Example(AgentStaff.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkAgentId", agentStaffDto.getFkAgentId());
//        example.orderBy("activeDate").desc();
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        List<AgentStaff> agentStaffs = agentStaffMapper.selectByExample(example);
//        page.restPage(agentStaffs);
        IPage<AgentStaff> pages = agentStaffMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), Wrappers.<AgentStaff>lambdaQuery()
                .eq(AgentStaff::getFkAgentId, agentStaffDto.getFkAgentId())
                .orderByDesc(AgentStaff::getActiveDate));
        List<AgentStaff> agentStaffs = pages.getRecords();
        page.setAll((int) pages.getTotal());

        List<AgentStaffVo> convertDatas = new ArrayList<>();
        //员工id集合
        Set<Long> staffIds = new HashSet<>();
        //获取各自集合的值
        for (AgentStaff agentStaff : agentStaffs) {
            staffIds.add(agentStaff.getFkStaffId());
        }
        //feign调用获取对应map
        Map<Long, String> staffNameMap = getStaffNameMap(staffIds);
        //通过staffIds 查找员工id - 公司名称 map
        Map<Long, String> companyNameMap = staffBdCodeService.getCompanyNamesByStaffIds(staffIds);
        for (AgentStaff agentStaff : agentStaffs) {
            AgentStaffVo agentStaffVo = BeanCopyUtils.objClone(agentStaff, AgentStaffVo::new);
            agentStaffVo.setBdName(staffNameMap.get(agentStaffVo.getFkStaffId()));
            agentStaffVo.setCompanyName(companyNameMap.get(agentStaff.getFkStaffId()));
            agentStaffVo.setBdCode(staffBdCodeService.getBDbyStaffId(agentStaff.getFkStaffId()));
            convertDatas.add(agentStaffVo);
        }
        return convertDatas;
    }

    @Override
    public List<StaffBdCodeVo> getStaffBdCodeList(Long companyId, Boolean testBdFlag, Boolean subordinateFlag) {
        if (GeneralTool.isEmpty(companyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("公司id不能为空"));
        }
        if (!SecureUtil.validateCompany(companyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
        }
        List<StaffBdCodeVo> staffBdCodeList = new ArrayList<>();
        Result<List<Long>> result = permissionCenterClient.getStaffIdsByCompanyId(companyId);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            List<Long> staffIds = result.getData();
            if (!subordinateFlag) {
                List<Long> staffFollowerIds = permissionCenterClient.getStaffFollowerIds(SecureUtil.getStaffId()).getData();
                staffFollowerIds.add(SecureUtil.getStaffId());
                staffIds.retainAll(staffFollowerIds);
            }
            if (GeneralTool.isEmpty(staffIds)) {
                return null;
            }
            staffBdCodeList = agentStaffMapper.getStaffBdCodeList(staffIds, testBdFlag);
            Set<Long> staffIds_ = staffBdCodeList.stream().map(StaffBdCodeVo::getFkStaffId).collect(Collectors.toSet());
            Result<Map<Long, Boolean>> reDuty = permissionCenterClient.getStaffIsOnDuty(staffIds_);
            Map<Long, Boolean> onDutyMap = new HashMap<>();
            if (reDuty.isSuccess()) {
                if (GeneralTool.isNotEmpty(reDuty.getData())) {
                    onDutyMap = reDuty.getData();
                }
            }
            if (GeneralTool.isNotEmpty(staffIds_)) {
                Result<Map<Long, String>> staffNameResult = permissionCenterClient.getStaffNameMap(staffIds_);
                if (staffNameResult.isSuccess() && GeneralTool.isNotEmpty(staffNameResult)) {
                    Map<Long, String> staffNameMap = staffNameResult.getData();
                    for (StaffBdCodeVo staffBdCodeVo : staffBdCodeList) {
//                        String staffName = permissionCenterClient.getStaffName(staffBdCodeVo.getFkStaffId());
                        String staffName = staffNameMap.get(staffBdCodeVo.getFkStaffId());//从map中获取名字
                        staffBdCodeVo.setBdName(staffName);
                        staffBdCodeVo.setIsOnDuty(onDutyMap.get(staffBdCodeVo.getFkStaffId()));
                    }
                }
            }
        }
        return staffBdCodeList.stream().sorted(Comparator.comparing(StaffBdCodeVo::getIsOnDuty).reversed()).collect(Collectors.toList());
    }

    @Override
    public List<BaseSelectEntity> getAgentSelect() {
        List<Long> agentIds = studentAgentService.getAgentIds();
        if (GeneralTool.isEmpty(agentIds)) {
            agentIds = new ArrayList<>();
            agentIds.add(0L);
        }
        return agentStaffMapper.getAgentSelect(agentIds);
    }


    @Override
    public List<StaffBdCodeVo> getBdSelect(Long agentId) {
        if (GeneralTool.isEmpty(agentId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("agent_id_null"));
        }
        List<StaffBdCodeVo> staffBdCodeVoList = agentStaffMapper.getBDSelect(agentId);
        Set<Long> fkStaffIds = staffBdCodeVoList.stream().map(StaffBdCodeVo::getFkStaffId).collect(Collectors.toSet());
        Map<Long, String> staffNameMap = new HashMap<>();
        Result<Map<Long, String>> staffNameResult = permissionCenterClient.getStaffNameMap(fkStaffIds);
        if (staffNameResult.isSuccess() && GeneralTool.isNotEmpty(staffNameResult)) {
            staffNameMap = staffNameResult.getData();
            for (StaffBdCodeVo staffBdCodeVo : staffBdCodeVoList) {
                staffBdCodeVo.setBdName(staffNameMap.get(staffBdCodeVo.getFkStaffId()));
            }
        }
        return staffBdCodeVoList;
    }

    @Override
    public List<Long> getAgentIdByStaffIds(List<Long> staffIds) {
        if (GeneralTool.isEmpty(staffIds)) {
            return null;
        }
//        Example example = new Example(AgentStaff.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andIn("fkStaffId", staffIds);
//        List<AgentStaff> agentStaffs = agentStaffMapper.selectByExample(example);
        List<AgentStaff> agentStaffs = agentStaffMapper.selectList(Wrappers.<AgentStaff>lambdaQuery().in(AgentStaff::getFkStaffId, staffIds));
        if (GeneralTool.isEmpty(agentStaffs)) {
            return null;
        }
        return agentStaffs.stream().distinct().map(AgentStaff::getFkAgentId).collect(Collectors.toList());
    }

    @Override
    public List<Long> getAgentIdByStaffIdsIsActive(List<Long> staffIds) {
        if (GeneralTool.isEmpty(staffIds)) {
            return null;
        }
        List<AgentStaff> agentStaffs = agentStaffMapper.selectList(Wrappers.<AgentStaff>query().lambda().in(AgentStaff::getFkStaffId, staffIds).eq(AgentStaff::getIsActive, true));
        if (GeneralTool.isEmpty(agentStaffs)) {
            return null;
        }
        return agentStaffs.stream().distinct().map(AgentStaff::getFkAgentId).collect(Collectors.toList());
    }

    @Override
    public List<BaseSelectEntity> getAgentByStudentId(Long studentId) {
        if (GeneralTool.isEmpty(studentId)) {
            return null;
        }
        return agentStaffMapper.getAgentByStudentId(studentId);
    }

    @Override
    public List<AgentAndAgentLabelVo> getAgentList(Long studentId) {
        if (GeneralTool.isEmpty(studentId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        //获取业务下属
        List<Long> staffFollowerIds = permissionCenterClient.getStaffFollowerIds(SecureUtil.getStaffId()).getData();
        if (GeneralTool.isEmpty(staffFollowerIds)) {
            staffFollowerIds = new ArrayList<>();
        }
        staffFollowerIds.add(SecureUtil.getStaffId());
        //获取能看的国家线
        List<Long> visibleCountryIds = studentOfferMapper.getVisibleCountryIdByStaffIds(GetStringUtils.getSqlString(staffFollowerIds));
        List<AgentAndAgentLabelVo> datas = new ArrayList<>();
        if (GeneralTool.isNotEmpty(visibleCountryIds)) {
            List<BaseSelectEntity> agentList = agentStaffMapper.getAgentList(studentId, visibleCountryIds);
            Set<Long> agentIds = agentList.stream().map(BaseSelectEntity::getId).collect(Collectors.toSet());
            if (GeneralTool.isNotEmpty(agentIds)) {
                Map<Long, List<AgentLabelVo>> agentLabelMap = getAgentLabelDataUtils.getAgentLabelMap(agentIds).getAgentLabelMap();
                for (BaseSelectEntity baseSelectEntity : agentList) {
                    AgentAndAgentLabelVo agentAndAgentLabelVo = new AgentAndAgentLabelVo();
                    agentAndAgentLabelVo.setId(baseSelectEntity.getId());
                    agentAndAgentLabelVo.setName(baseSelectEntity.getName());
                    agentAndAgentLabelVo.setFullName(baseSelectEntity.getFullName());
                    agentAndAgentLabelVo.setStatus(baseSelectEntity.getStatus());
                    agentAndAgentLabelVo.setAgentLabelVos(agentLabelMap.get(agentAndAgentLabelVo.getId()));
                    datas.add(agentAndAgentLabelVo);
                }
            }
        }
        return datas;
    }

    @Override
    public List<Agent> getBdCount(Long staffId) {
        return agentStaffMapper.getBdCount(staffId);
    }

    @Override
    public AgentStaffVo updateAgentStaff(AgentStaffDto agentStaffDto) {
        if (GeneralTool.isEmpty(agentStaffDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }

        //至少需要绑定一个BD
        List<AgentStaff> agentStaffList = agentStaffMapper.selectList(new LambdaQueryWrapper<AgentStaff>()
                .ne(AgentStaff::getFkStaffId, agentStaffDto.getFkStaffId())
                .eq(AgentStaff::getIsActive,true));
        if (GeneralTool.isEmpty(agentStaffList)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("required_bd"));
        }
        AgentStaff agentStaff = BeanCopyUtils.objClone(agentStaffDto, AgentStaff::new);
        agentStaff.setUnactiveDate(new Date());
        utilService.updateUserInfoToEntity(agentStaff);
        agentStaffMapper.updateById(agentStaff);
        AgentStaff returnAgentStaff = agentStaffMapper.selectById(agentStaffDto.getId());
        return BeanCopyUtils.objClone(returnAgentStaff, AgentStaffVo::new);
    }

    /**
     * @Description :feign调用一次查出全部负责人对应名称
     * @Param [staffIds]
     * <AUTHOR>
     */
    private Map<Long, String> getStaffNameMap(Set<Long> staffIds) {
        staffIds.removeIf(Objects::isNull);
        //feign调用一次查出全部对应员工名称
        Result<Map<Long, String>> staffNameResult = permissionCenterClient.getStaffNameMap(staffIds);
        if (staffNameResult.isSuccess() && GeneralTool.isNotEmpty(staffNameResult)) {
            return staffNameResult.getData();
        }
        return new HashMap<>();//空的集合
    }

    /**
     * @ Description :员工跟进的全部代理
     * @ Param [fkStaffId]
     * @ return java.util.List<java.lang.Long>
     * @ author LEO
     */
    @Override
    public List<Long> getAgentsByStaffId(Long fkStaffId) {
//        Example example = new Example(AgentStaff.class);
//        example.createCriteria().andEqualTo("fkStaffId", fkStaffId).andEqualTo("isActive", 1);
//        List<AgentStaff> agentStaffs = agentStaffMapper.selectByExample(example);
        List<AgentStaff> agentStaffs = agentStaffMapper.selectList(Wrappers.<AgentStaff>lambdaQuery()
                .eq(AgentStaff::getFkStaffId, fkStaffId)
                .eq(AgentStaff::getIsActive, 1));
        return agentStaffs.stream().map(AgentStaff::getFkAgentId).collect(Collectors.toList());


    }

    @Override
    public void updateAgentStaffBinding(AgentStaffDto agentStaffDto) {
        if (GeneralTool.isEmpty(agentStaffDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        //先对数据进行解绑操作
        List<AgentVo> agents = agentService.getAgents(agentStaffDto.getAgentVo(), null);
        if (GeneralTool.isEmpty(agents)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        Set<Long> agentIds = agents.stream().map(AgentVo::getId).collect(Collectors.toSet());
        LambdaQueryWrapper<AgentStaff> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(AgentStaff::getFkAgentId,agentIds);
        queryWrapper.eq(AgentStaff::getIsActive,true);
        AgentStaff agentStaff = new AgentStaff();
        agentStaff.setIsActive(false);
        agentStaff.setUnactiveDate(new Date());
        agentStaffMapper.update(agentStaff,queryWrapper);
        //进行新增操作
        List<AgentStaff> agentStaffList = new ArrayList<>();
        agentIds.stream().forEach(item ->{
            AgentStaff agentStaffs = new AgentStaff();
            agentStaffs.setFkAgentId(item);
            utilService.updateUserInfoToEntity(agentStaffs);
            agentStaffs.setIsActive(true);
            agentStaffs.setActiveDate(new Date());
            agentStaffList.add(agentStaffs);
        });
        agentStaffList.stream().forEach(item ->{
            item.setFkStaffId(agentStaffDto.getFkStaffId());
        });
        if (GeneralTool.isNotEmpty(agentStaffList)) {
            saveBatch(agentStaffList);
        }
    }

    @Override
    public Map<Long, String> getBdNameByAgentIds(Set<Long> agentIds) {
        if (GeneralTool.isEmpty(agentIds)){
            return null;
        }
        Map<Long, String> agentMap = new HashMap<>();
        List<BaseSelectEntity> bdNameByAgentList = agentStaffMapper.getBdNameByAgentIds(agentIds);
        if (GeneralTool.isNotEmpty(bdNameByAgentList)){
            agentMap = bdNameByAgentList.stream().collect(Collectors.toMap(BaseSelectEntity::getId, BaseSelectEntity::getName));
        }
        return agentMap;
    }

    /**
     * 有绑定代理并且为业务下属的 BD下拉框数据
     *
     * @Date 12:14 2023/3/16
     * <AUTHOR>
     */
    @Override
    public List<StaffBdCodeVo> getExistAgentStaffBdCodeList(Long companyId) {
        if (GeneralTool.isEmpty(companyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("公司id不能为空"));
        }
        List<StaffBdCodeVo> staffBdCodeList = new ArrayList<>();
        Result<List<Long>> result = permissionCenterClient.getStaffIdsByCompanyId(companyId);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            List<Long> staffIds = result.getData();
            Long staffId = SecureUtil.getStaffId();
            //员工id + 业务下属员工ids
            List<Long> staffFollowerIds = permissionCenterClient.getStaffFollowerIds(staffId).getData().stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
            staffFollowerIds.add(staffId);

            List<Long> staffIdList = staffFollowerIds.stream().filter(staffIds::contains).collect(Collectors.toList());
            staffBdCodeList = agentStaffMapper.getStaffBdCodeList(staffIdList, false);
            Set<Long> staffIds_ = staffBdCodeList.stream().map(StaffBdCodeVo::getFkStaffId).collect(Collectors.toSet());
            Result<Map<Long, Boolean>> reDuty = permissionCenterClient.getStaffIsOnDuty(staffIds_);
            Map<Long, Boolean> onDutyMap = new HashMap<>();
            if (reDuty.isSuccess()) {
                if (GeneralTool.isNotEmpty(reDuty.getData())) {
                    onDutyMap = reDuty.getData();
                }
            }
            if (GeneralTool.isNotEmpty(staffIds_)) {
                Result<Map<Long, String>> staffNameResult = permissionCenterClient.getStaffNameMap(staffIds_);
                if (staffNameResult.isSuccess() && GeneralTool.isNotEmpty(staffNameResult)) {
                    Map<Long, String> staffNameMap = staffNameResult.getData();
                    for (StaffBdCodeVo staffBdCodeVo : staffBdCodeList) {
                        String staffName = staffNameMap.get(staffBdCodeVo.getFkStaffId());//从map中获取名字
                        staffBdCodeVo.setBdName(staffName);
                        staffBdCodeVo.setIsOnDuty(onDutyMap.get(staffBdCodeVo.getFkStaffId()));
                    }
                }
            }
        }
        return staffBdCodeList.stream().sorted(Comparator.comparing(StaffBdCodeVo::getIsOnDuty).reversed()).collect(Collectors.toList());

    }

    /**
     * 获取bd ids
     * @param agentIds
     * @return
     */
    @Override
    public Map<Long, List<Long>> getBdIdByAgentIds(Set<Long> agentIds) {
        Map<Long, List<Long>> map = Maps.newHashMap();
//        LambdaQueryChainWrapper<AgentStaff> wrapper = lambdaQuery();
//        List<AgentStaff> agentStaffs = list(wrapper.in(AgentStaff::getFkAgentId, agentIds).eq(AgentStaff::getIsActive, true));
        List<AgentStaff> agentStaffs = list(Wrappers.lambdaQuery(AgentStaff.class).in(AgentStaff::getFkAgentId, agentIds).eq(AgentStaff::getIsActive, true));
        if (GeneralTool.isNotEmpty(agentStaffs)){
            Map<Long, List<AgentStaff>> agentStaffMap = agentStaffs.stream().collect(Collectors.groupingBy(AgentStaff::getFkAgentId));
            if (GeneralTool.isNotEmpty(agentStaffMap)){
                for (Map.Entry<Long, List<AgentStaff>> entry : agentStaffMap.entrySet()) {
                    map.put(entry.getKey(),entry.getValue().stream().map(AgentStaff::getFkStaffId).collect(Collectors.toList()));
                }
            }
        }
        return map;
    }

    /**
     * 根据代理ids获取bd的区域dto集合
     * @param agentIds 代理ids
     * @return
     */
    @Override
    public Map<Long, List<AreaRegionVo>> getAreaRegionDtosByAgentIds(Set<Long> agentIds) {
        Map<Long, List<AreaRegionVo>> map = Maps.newHashMap();
        List<StaffBdCodeVo> areaRegionIdList = agentStaffMapper.getAreaRegionIdByAgentIds(agentIds);
        Set<Long> areaRegionIds = new HashSet<>();
        for (StaffBdCodeVo staffBdCodeVo : areaRegionIdList) {
            String fkAreaRegionId = staffBdCodeVo.getFkAreaRegionId();
            if (GeneralTool.isNotEmpty(fkAreaRegionId)) {
                String[] split = fkAreaRegionId.split(",");
                for (String sp : split) {
                    if (GeneralTool.isNotEmpty(sp)) {
                        Long areaRegionId = Long.valueOf(sp);
                        areaRegionIds.add(areaRegionId);
                    }
                }
            }
        }
        Map<Long, com.get.institutioncenter.vo.AreaRegionVo> areaRegionDtoByIdsMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(areaRegionIds)) {
            Result<Map<Long, com.get.institutioncenter.vo.AreaRegionVo>> areaRegionDtoByIdsMapResult = institutionCenterClient.getAreaRegionDtoByIds(areaRegionIds);
            if (areaRegionDtoByIdsMapResult.isSuccess() && GeneralTool.isNotEmpty(areaRegionDtoByIdsMapResult.getData())) {
                areaRegionDtoByIdsMap = areaRegionDtoByIdsMapResult.getData();
            }
        }
        for (StaffBdCodeVo staffBdCodeVo : areaRegionIdList) {
            List<AreaRegionVo> areaRegionVos = new ArrayList<>();
            String fkAreaRegionId = staffBdCodeVo.getFkAreaRegionId();
            if (GeneralTool.isNotEmpty(fkAreaRegionId)) {
                String[] split = fkAreaRegionId.split(",");
                for (String sp : split) {
                    if (GeneralTool.isNotEmpty(sp)) {
                        Long areaRegionId = Long.valueOf(sp);
                        com.get.institutioncenter.vo.AreaRegionVo areaRegionVoClone = areaRegionDtoByIdsMap.get(areaRegionId);
                        if (GeneralTool.isNotEmpty(areaRegionVoClone)) {
                            AreaRegionVo areaRegionVo = BeanCopyUtils.objClone(areaRegionVoClone, AreaRegionVo::new);
                            areaRegionVos.add(areaRegionVo);
                        }
                    }
                }
            }
            map.put(staffBdCodeVo.getFkAgentId(), areaRegionVos);
        }
        return map;
    }

    /**
     * BD下拉框数据 by公司ids and 有权限bd
     *
     * @Date 14:53 2024/7/18
     * <AUTHOR>
     */
    @Override
    public List<StaffBdCodeVo> getStaffBdCodeListByPermission(List<Long> companyIds) {
        List<StaffBdCodeVo> staffBdCodeList = new ArrayList<>();
        if (GeneralTool.isEmpty(companyIds)) {
            companyIds = SecureUtil.getCompanyIds();
        } else {
            if (!SecureUtil.validateCompanys(companyIds)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
            }
        }
        Result<List<Long>> result = permissionCenterClient.getStaffIdsByCompanyIds(companyIds);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            List<Long> staffIds = result.getData();
                List<Long> staffFollowerIds = permissionCenterClient.getStaffFollowerIds(SecureUtil.getStaffId()).getData();
                staffFollowerIds.add(SecureUtil.getStaffId());
                staffIds.retainAll(staffFollowerIds);
            if (GeneralTool.isEmpty(staffIds)) {
                return null;
            }
            staffBdCodeList = agentStaffMapper.getStaffBdCodeList(staffIds, false);
            Set<Long> staffIds_ = staffBdCodeList.stream().map(StaffBdCodeVo::getFkStaffId).collect(Collectors.toSet());
            Result<Map<Long, Boolean>> reDuty = permissionCenterClient.getStaffIsOnDuty(staffIds_);
            Map<Long, Boolean> onDutyMap = new HashMap<>();
            if (reDuty.isSuccess()) {
                if (GeneralTool.isNotEmpty(reDuty.getData())) {
                    onDutyMap = reDuty.getData();
                }
            }
            if (GeneralTool.isNotEmpty(staffIds_)) {
                Result<Map<Long, String>> staffNameResult = permissionCenterClient.getStaffNameMap(staffIds_);
                if (staffNameResult.isSuccess() && GeneralTool.isNotEmpty(staffNameResult)) {
                    Map<Long, String> staffNameMap = staffNameResult.getData();
                    for (StaffBdCodeVo staffBdCodeVo : staffBdCodeList) {
                        String staffName = staffNameMap.get(staffBdCodeVo.getFkStaffId());//从map中获取名字
                        staffBdCodeVo.setBdName(staffName);
                        staffBdCodeVo.setIsOnDuty(onDutyMap.get(staffBdCodeVo.getFkStaffId()));
                    }
                }
            }
        }
        return staffBdCodeList;
    }

    @Override
    public AgentStaff getActiveAgentStaffByAgentId(Long agentId) {
        LambdaQueryWrapper<AgentStaff> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AgentStaff::getFkAgentId, agentId);
        wrapper.eq(AgentStaff::getIsActive, true);
        return agentStaffMapper.selectOne(wrapper);
    }


}

package com.get.salecenter.service.impl;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.CollectionUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.dao.sale.AgentContractFormulaCompanyMapper;
import com.get.salecenter.vo.CompanyTreeVo;
import com.get.salecenter.entity.AgentContractFormulaCompany;
import com.get.salecenter.service.IAgentCompanyService;
import com.get.salecenter.service.IAgentContractFormulaCompanyService;
import com.get.salecenter.dto.AgentContractFormulaCompanyDto;
import net.sf.json.JSONArray;
import net.sf.json.JsonConfig;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: Sea
 * @create: 2021/1/6 15:10
 * @verison: 1.0
 * @description:
 */
@Service
public class AgentContractFormulaCompanyServiceImpl implements IAgentContractFormulaCompanyService {
    @Resource
    private AgentContractFormulaCompanyMapper agentContractFormulaCompanyMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private IAgentCompanyService agentCompanyService;

    @Override
    public Map<Long, String> getCompanyNameMapByFkids(List<Long> agentContractFormulaIds) {
        //关系map
        Map<Long, List<Long>> idMap = new HashMap<>();
        Map<Long, String> nameMap = new HashMap<>();
        //全部companyId集合
        Set<Long> companyIdSet = new HashSet<>();
        for (Long agentContractFormulaId : agentContractFormulaIds) {
            //通过agentContractFormulaId获取对应所有国家id
            List<Long> companyIds = getCompanyIdListByFkid(agentContractFormulaId);
            companyIdSet.addAll(companyIds);
            //agentContractFormulaId和companyIds一一对应关系map
            idMap.put(agentContractFormulaId, companyIds);
        }
        //feign调用一次查出 companyId和companyName对应关系map
        Map<Long, String> companyNameMap = new HashMap<>();
        Result<Map<Long, String>> result = permissionCenterClient.getCompanyNamesByIds(companyIdSet);
        if (result.isSuccess() && result.getData() != null) {
            companyNameMap = result.getData();
        }
        //map由agentContractFormulaId 对应 companyIds 转成 agentContractFormulaId 对应 companyNames
        for (Map.Entry<Long, List<Long>> agentContractFormula : idMap.entrySet()) {
            List<String> companyNames = new ArrayList<>();
            List<Long> companyIds = agentContractFormula.getValue();
            for (Long companyId : companyIds) {
                companyNames.add(companyNameMap.get(companyId));
            }
            nameMap.put(agentContractFormula.getKey(), StringUtils.join(companyNames, "，"));
        }
        return nameMap;
    }

    @Override
    public List<Long> getCompanyIdListByFkid(Long agentContractFormulaId) {
        return agentContractFormulaCompanyMapper.getCompanyIdsByFkid(agentContractFormulaId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editAgentContractFormulaCompany(List<AgentContractFormulaCompanyDto> agentContractFormulaCompanyDtos) {
        if (GeneralTool.isEmpty(agentContractFormulaCompanyDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
//        //通过代理id获取该代理安全配置公司
//        List<Long> agentCompanyIds = agentCompanyService.getRelationByAgentId(agentContractFormulaCompanyDtos.get(0).getAgentId());
//        for (AgentContractFormulaCompanyDto agentContractFormulaCompanyVo : agentContractFormulaCompanyDtos) {
//            if(!agentCompanyIds.contains(agentContractFormulaCompanyVo.getFkCompanyId())){
//                throw new GetServiceException("所选公司要在该代理安全配置的公司中");
//            }
//        }
        //先删后增
//        Example example = new Example(AgentContractFormulaCompany.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkAgentContractFormulaId", agentContractFormulaCompanyDtos.get(0).getFkAgentContractFormulaId());
//        agentContractFormulaCompanyMapper.deleteByExample(example);
        List<Long> companyIds = SecureUtil.getCompanyIds();
        agentContractFormulaCompanyMapper.delete(Wrappers.<AgentContractFormulaCompany>lambdaQuery().eq(AgentContractFormulaCompany::getFkAgentContractFormulaId, agentContractFormulaCompanyDtos.get(0).getFkAgentContractFormulaId()).in(AgentContractFormulaCompany::getFkCompanyId,companyIds));
        List<AgentContractFormulaCompany> collect = agentContractFormulaCompanyDtos.stream().map(agentContractFormulaCompanyVo -> BeanCopyUtils.objClone(agentContractFormulaCompanyVo, AgentContractFormulaCompany::new)).collect(Collectors.toList());
        for (AgentContractFormulaCompany agentContractFormulaCompany : collect) {
            utilService.updateUserInfoToEntity(agentContractFormulaCompany);
            agentContractFormulaCompanyMapper.insertSelective(agentContractFormulaCompany);
        }
    }

    @Override
    public List<CompanyTreeVo> getAgentContractFormulaCompany(Long agentContractFormulaId) {
        if (GeneralTool.isEmpty(agentContractFormulaId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        //获取公司
        List<CompanyTreeVo> companyTreeVo = getCompanyTreeDto();
        if (GeneralTool.isEmpty(companyTreeVo)) {
            return null;
        }
        List<AgentContractFormulaCompany> relation = getRelationByContractFormulaId(agentContractFormulaId);
        setContactFlag(companyTreeVo, relation);
        return getTreeList(companyTreeVo);
    }

    @Override
    public void deleteByFkid(Long agentContractFormulaId) {
//        Example example = new Example(AgentContractFormulaCompany.class);
//        example.createCriteria().andEqualTo("fkAgentContractFormulaId", agentContractFormulaId);
//        agentContractFormulaCompanyMapper.deleteByExample(example);
        agentContractFormulaCompanyMapper.delete(Wrappers.<AgentContractFormulaCompany>lambdaQuery().eq(AgentContractFormulaCompany::getFkAgentContractFormulaId, agentContractFormulaId));
    }

    @Override
    public List<Long> getFormulaIdByCompanyIds(List<Long> companyIds) {
        //防止in()报错
        if (GeneralTool.isEmpty(companyIds)) {
            companyIds = new ArrayList<>();
            companyIds.add(0L);
        }
        return agentContractFormulaCompanyMapper.getFormulaIdByCompanyIds(companyIds);
    }


    private List<CompanyTreeVo> getCompanyTreeDto() {
//        ListResponseBo responseBo = permissionCenterClient.getAllCompanyDto();
        List<CompanyTreeVo> companyTreeVos = new ArrayList<>();
        Result<List<com.get.permissioncenter.vo.tree.CompanyTreeVo>> result = permissionCenterClient.getAllCompanyDto();
        if (result.isSuccess() && CollectionUtil.isNotEmpty(result.getData())) {
            JsonConfig config = new JsonConfig();
            config.setExcludes(new String[]{"departmentTree", "totalNum"});
            JSONArray jsonArray = JSONArray.fromObject(result.getData(), config);
            return JSONArray.toList(jsonArray, new CompanyTreeVo(), new JsonConfig());
        }
        return companyTreeVos;//如没有则返回一个空的集合
    }

    private List<AgentContractFormulaCompany> getRelationByContractFormulaId(Long agentContractFormulaId) {
//        Example example = new Example(AgentContractFormulaCompany.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkAgentContractFormulaId", agentContractFormulaId);
//        return agentContractFormulaCompanyMapper.selectByExample(example);
        return agentContractFormulaCompanyMapper.selectList(Wrappers.<AgentContractFormulaCompany>lambdaQuery().eq(AgentContractFormulaCompany::getFkAgentContractFormulaId, agentContractFormulaId));
    }

    private void setContactFlag(List<CompanyTreeVo> companyTreeVo, List<AgentContractFormulaCompany> relation) {
        for (CompanyTreeVo treeDto : companyTreeVo) {
            for (AgentContractFormulaCompany agentContractFormulaCompany : relation) {
                if (treeDto.getId().equals(String.valueOf(agentContractFormulaCompany.getFkCompanyId()))) {
                    treeDto.setFlag(true);
                }
            }
        }
    }

    private List<CompanyTreeVo> getTreeList(List<CompanyTreeVo> entityList) {
        List<CompanyTreeVo> resultList = new ArrayList<>();
        // 获取顶层元素集合
        String parentId;
        for (CompanyTreeVo entity : entityList) {
            parentId = String.valueOf(entity.getFkParentCompanyId());
            if (parentId == null || "0".equals(parentId)) {
                //获取父节点的部门信息
                resultList.add(entity);
            }
        }
        //假如没有父节点
        if (GeneralTool.isEmpty(resultList)) {
            //获取最小节点
            CompanyTreeVo minTreeNode = entityList.stream().min(Comparator.comparing(CompanyTreeVo::getFkParentCompanyId)).get();
            resultList.add(minTreeNode);
            if (GeneralTool.isNotEmpty(minTreeNode)) {
                //获取相同的最小节点
                List<CompanyTreeVo> minTreeNodes = entityList.stream().filter(treeDto ->
                        treeDto.getFkParentCompanyId().equals(minTreeNode.getFkParentCompanyId()) &&
                                !treeDto.getId().equals(minTreeNode.getId())).distinct().collect(Collectors.toList());
                resultList.addAll(minTreeNodes);
            }
        }
        // 获取每个顶层元素的子数据集合
        for (CompanyTreeVo entity : resultList) {
            entity.setChildList(getSubList(entity.getId(), entityList));
        }
        return resultList;
    }

    private List<CompanyTreeVo> getSubList(String id, List<CompanyTreeVo> entityList) {
        List<CompanyTreeVo> childList = new ArrayList<>();
        String parentId;
        // 子集的直接子对象
        for (CompanyTreeVo entity : entityList) {
            parentId = String.valueOf(entity.getFkParentCompanyId());
            if (id.equals(parentId)) {
                //获取子节点的部门信息
                childList.add(entity);
            }
        }
        // 子集的间接子对象
        for (CompanyTreeVo entity : childList) {
            //递归调用
            entity.setChildList(getSubList(entity.getId(), entityList));
        }
        // 递归退出条件
        if (childList.size() == 0) {
            return null;
        }
        return childList;
    }
}

package com.get.salecenter.service;

import java.util.List;
import java.util.Map;

public interface IConventionExamPrizesService {

//    List<ConventionExamPrizesVo> getDatas(ConventionExamPrizesDto data, SearchBean<ConventionExamPrizesDto> page);

    void updateExamPrizesExchange(Long id, Boolean isExchange);

    List<Map<String, Object>> findPrizesType();

//    List<BaseSelectEntity> examinationSelectByConventionId(Long fkConventionId);

//    void exportExcel(HttpServletResponse response, Long fkConventionId);
}

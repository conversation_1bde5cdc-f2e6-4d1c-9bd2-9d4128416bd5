<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.newissue.newIssueUserSuperiorMapper">
    <resultMap id="BaseResultMap" type="com.get.salecenter.entity.NewIssueUserSuperior">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="fk_user_id" jdbcType="BIGINT" property="fkUserId"/>
        <result column="fk_user_superior_id" jdbcType="BIGINT" property="fkUserSuperiorId"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser"/>
    </resultMap>
    <select id="selectExistDatas" resultType="com.get.salecenter.entity.NewIssueUserSuperior">
      SELECT * FROM `r_user_superior` where fk_user_superior_id=#{fkUserId}
      UNION all
      SELECT * FROM `r_user_superior` where fk_user_id=#{fkUserId}
    </select>


</mapper>
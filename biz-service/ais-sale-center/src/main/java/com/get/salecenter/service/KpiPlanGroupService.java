package com.get.salecenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.salecenter.dto.KpiPlanGroupSearchDto;
import com.get.salecenter.vo.KpiPlanGroupResultVo;
import com.get.salecenter.entity.KpiPlanGroup;
import com.get.salecenter.dto.KpiPlanGroupDto;
import com.get.salecenter.vo.KpiPlanGroupVo;

import java.util.List;


/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
public interface KpiPlanGroupService extends IService<KpiPlanGroup> {
    /**
     * kpi方案组别列表
     * <AUTHOR>
     * @DateTime 2024/4/16 16:01
     */
    KpiPlanGroupResultVo datas(KpiPlanGroupSearchDto searchVo);

    /**
     * 新增kpi方案组别
     * <AUTHOR>
     * @DateTime 2024/4/18 14:45
     */
    Long addKpiPlanGroup(KpiPlanGroupDto kpiPlanGroupDto);

    /**
     * 删除kpi方案组别
     * <AUTHOR>
     * @DateTime 2024/4/18 14:50
     */
    void delete(Long id);

    /**
     * 修改kpi方案组别
     * <AUTHOR>
     * @DateTime 2024/4/18 14:52
     */
    void updateKpiPlanGroup(KpiPlanGroupDto kpiPlanGroupDto);


    /**
     * 拖拽
     * <AUTHOR>
     * @DateTime 2024/4/18 14:53
     */
    void movingOrder(Long fkKpiPlanId,Integer start,Integer end);

    /**
     * 获取组别下拉
     *
     * @param fkKpiPlanId KPI方案ID
     * @return
     */
    List<KpiPlanGroupVo> getGroupSelect(Long fkKpiPlanId);
}

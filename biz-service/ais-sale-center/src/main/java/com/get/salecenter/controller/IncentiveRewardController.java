package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.salecenter.vo.IncentiveRewardVo;
import com.get.salecenter.service.IIncentiveRewardService;
import com.get.salecenter.dto.IncentiveRewardDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * Incentive奖励政策奖品管理
 * <AUTHOR>
 * @since 2023-03-13
 */

@Api(tags = "Incentive奖励奖品管理")
@RestController
@RequestMapping("sale/incentiveReward")
public class IncentiveRewardController {

    @Resource
    private IIncentiveRewardService incentiveRewardService;

    /**
     * @Description :详情
     * @Param [id]
     */
    @ApiOperation(value = "详情", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/配置管理/奖励政策奖品/详情")
    @GetMapping("/{id}")
    public ResponseBo<IncentiveRewardVo> detail(@PathVariable(value = "id") Long id) {
        IncentiveRewardVo data = incentiveRewardService.selectById(id);
        return new ResponseBo<>(data);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :新增
     * @Param [UIncentiveRewardVo]
     */
    @ApiOperation(value = "新增", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/配置管理/奖励政策奖品/新增")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(IncentiveRewardDto.Add.class) IncentiveRewardDto uIncentiveRewardDto) {
        return SaveResponseBo.ok(incentiveRewardService.addUIncentiveReward(uIncentiveRewardDto));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :删除
     * @Param [id]
     */
    @ApiOperation(value = "删除", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/配置管理/奖励政策奖品/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        incentiveRewardService.deleteDataById(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @Description :修改
     * @Param [eventVo]
     */
    @ApiOperation(value = "修改", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/配置管理/奖励政策奖品/更新")
    @PostMapping("update")
    public ResponseBo<IncentiveRewardVo> update(@RequestBody  @Validated(IncentiveRewardDto.Update.class) IncentiveRewardDto uIncentiveRewardDto) {
        return UpdateResponseBo.ok(incentiveRewardService.updateUIncentiveReward(uIncentiveRewardDto));
    }

    /**
     * @Description :列表数据
     * @Param [page]
     */
    @ApiOperation(value = "列表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/配置管理/奖励政策奖品/列表")
    @PostMapping("datas")
    public ResponseBo<IncentiveRewardVo> datas(@RequestBody SearchBean<IncentiveRewardDto> page) {
        List<IncentiveRewardVo> datas = incentiveRewardService.getIncentiveRewards(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

//    /**
//     * @Description :下拉数据
//     * @Param [page]
//     */
//    @ApiOperation(value = "下拉数据（根据ids集合返回）", notes = "")
//    @PostMapping("getIncentiveRewardListByIds")
//    public ResponseBo<IncentiveRewardVo> getIncentiveRewardListByIds(@RequestBody IncentiveRewardSearchDto incentiveRewardSearchVo) {
//        List<IncentiveRewardVo> datas = incentiveRewardService.getIncentiveRewardListByIds(incentiveRewardSearchVo);
//        return new ListResponseBo<>(datas);
//    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 上移下移
     * @Param [voList]
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.HELPCENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/配置管理/奖励政策奖品/排序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<IncentiveRewardDto> voList) {
        incentiveRewardService.movingOrder(voList);
        return ResponseBo.ok();
    }
}

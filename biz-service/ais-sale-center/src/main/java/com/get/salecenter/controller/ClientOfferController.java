package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.vo.ClientOfferVo;
import com.get.salecenter.vo.StudentProjectRoleStaffVo;
import com.get.salecenter.entity.StudentProjectRoleStaff;
import com.get.salecenter.service.IClientOfferService;
import com.get.salecenter.dto.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * author:Neil
 * Time: 14:08
 * Date: 2022/8/17
 * Description:客户咨询方案管理
 */
@Api(tags = "客户咨询方案管理")
@RestController
@RequestMapping("sale/clientOffer")
public class ClientOfferController {

    @Resource
    private IClientOfferService clientOfferService;

    @ApiOperation(value = "客户咨询方案列表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/客户咨询方案管理/客户咨询方案列表")
    @PostMapping("datas")
    public ResponseBo<ClientOfferVo> datas(@RequestBody SearchBean<ClientOfferDto> page){
        List<ClientOfferVo> datas = clientOfferService.getClientOfferList(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 新增信息
     * @Param [studentVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "绑定项目成员新增", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/咨询方案管理/绑定项目成员新增")
    @PostMapping("addProjectRoleStaff")
    public ResponseBo addProjectRoleStaff( @RequestBody StudentProjectRoleStaff projectRoleStaffVo) {
        return SaveResponseBo.ok(clientOfferService.addProjectRoleStaff(projectRoleStaffVo));
    }


    @ApiOperation(value = "修改客户申请方案状态", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/咨询方案管理/修改客户申请方案状态")
    @PostMapping("updateStatus")
    public ResponseBo updateStatus(@RequestBody ClientOfferUpdateDto clientOfferVo){
        clientOfferService.updateStatus(clientOfferVo);
        return ResponseBo.ok();
    }


    @ApiOperation(value = "获取资源方案详情", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/咨询方案管理/获取资源方案详情")
    @GetMapping("findClientOfferById/{id}")
    public ResponseBo<ClientOfferInfoDto> findClientOfferById(@PathVariable("id")Long id){
        return clientOfferService.getClientInfoById(id);
    }

    @ApiOperation(value = "添加客户资源方案", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/咨询方案管理/添加客户资源方案")
    @PostMapping("addClientOffer")
    public ResponseBo<Long> addClientOffer(@RequestBody @Validated(ClientOfferAddDto.Add.class) ClientOfferAddDto clientOfferAddDto){
        return clientOfferService.addClientOffer(clientOfferAddDto);
    }

    @ApiOperation(value = "修改客户资源方案", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/咨询方案管理/修改客户资源方案")
    @PostMapping("updateClientOffer")
    public ResponseBo<ClientOfferInfoDto> updateClientOffer(@RequestBody   @Validated(ClientOfferAddDto.Update.class)  ClientOfferAddDto clientOfferAddDto){
        return clientOfferService.updateClientOffer(clientOfferAddDto);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 学生申请方案下拉
     * @Param [providerId]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "客户申请方案下拉", notes = "")
    @PostMapping("getClientOfferSelect")
    public ResponseBo<BaseSelectEntity> getClientOfferSelect(@RequestParam(value = "clientId") Long clientId) {
        return new ListResponseBo<>(clientOfferService.getClientOfferSelect(clientId));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentVo>
     * @Description: 解绑项目成员
     * @Param [studentVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "解绑项目成员", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/客户管理/解绑项目成员")
    @PostMapping("updateProjectRoleStaff")
    public ResponseBo<StudentProjectRoleStaffVo> update(@RequestBody  @Validated(StudentProjectRoleStaffUpdateDto.Update.class)
                                                        StudentProjectRoleStaffUpdateDto projectRoleStaffVo) {
        return UpdateResponseBo.ok(clientOfferService.updateProjectRoleStaff(projectRoleStaffVo));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentVo>
     * @Description: 列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "绑定项目成员列表数据")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生管理/查询")
    @PostMapping("getProjectRoleStaff")
    public ResponseBo<StudentProjectRoleStaffVo> getProjectRoleStaff(@RequestBody SearchBean<ClientProjectRoleStaffDto> page) {
        List<StudentProjectRoleStaffVo> datas = clientOfferService.getProjectRoleStaff(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    @ApiOperation(value = "获取客户代理下拉", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/咨询方案管理/获取客户代理下拉")
    @GetMapping("getClientAgentSelection/{fkClientId}")
    public ResponseBo<BaseSelectEntity> getClientAgentSelection(@PathVariable("fkClientId")Long fkClientId){
        return clientOfferService.getClientAgentSelection(fkClientId);
    }

    /**
     * 角色员工下拉(角色联动)
     *
     * @Date 15:27 2021/7/6
     * <AUTHOR>
     */
    @ApiOperation(value = "角色员工下拉(角色联动)")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/咨询方案管理/角色员工下拉")
    @VerifyPermission(IsVerify = false)
    @GetMapping("getRoleStaffByRoleSelect")
    public ResponseBo<BaseSelectEntity> getRoleStaffByRoleSelect() {
        List<BaseSelectEntity> datas = clientOfferService.getRoleStaffByRoleSelect();
        return new ListResponseBo<>(datas);
    }


    @ApiOperation(value = "修改状态接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/咨询方案管理/修改状态")
    @PostMapping("updateStep")
    public ResponseBo updateStep(@RequestBody ClientOfferUpdateDto clientOfferVo) {
        clientOfferService.updateStep(clientOfferVo);
        return DeleteResponseBo.ok();
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "代理下拉", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/咨询方案管理/代理下拉")
    @PostMapping("getAgentSelect")
    public ResponseBo<BaseSelectEntity> getAgentSelect(@RequestParam("clientId")Long clientId) {
        return new ListResponseBo(clientOfferService.getAgentSelect(clientId));
    }
}

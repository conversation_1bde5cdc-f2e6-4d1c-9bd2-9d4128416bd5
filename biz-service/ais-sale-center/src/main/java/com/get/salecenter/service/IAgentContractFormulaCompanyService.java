package com.get.salecenter.service;


import com.get.salecenter.vo.CompanyTreeVo;
import com.get.salecenter.dto.AgentContractFormulaCompanyDto;

import java.util.List;
import java.util.Map;

/**
 * @author: Sea
 * @create: 2021/1/6 15:07
 * @verison: 1.0
 * @description:
 */
public interface IAgentContractFormulaCompanyService {

    /**
     * @Description :通过学生代理合同公式id 查找对应公司名称
     * @Param [agentContractFormulaIds]
     * <AUTHOR>
     */
    Map<Long, String> getCompanyNameMapByFkids(List<Long> agentContractFormulaIds);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :通过学生代理合同公式id 查找对应公司ids
     * @Param [agentContractFormulaId]
     * <AUTHOR>
     */
    List<Long> getCompanyIdListByFkid(Long agentContractFormulaId);

    /**
     * @return void
     * @Description :学生代理合同公式-安全配置
     * @Param [agentContractFormulaCompanyDtos]
     * <AUTHOR>
     */
    void editAgentContractFormulaCompany(List<AgentContractFormulaCompanyDto> agentContractFormulaCompanyDtos);

    /**
     * @return java.util.List<com.get.salecenter.vo.CompanyTreeVo>
     * @Description :学生代理合同公式-安全配置详情
     * @Param [agentContractFormulaId]
     * <AUTHOR>
     */
    List<CompanyTreeVo> getAgentContractFormulaCompany(Long agentContractFormulaId);

    /**
     * @return void
     * @Description :根据agentContractFormulaId删除
     * @Param [agentContractFormulaId]
     * <AUTHOR>
     */
    void deleteByFkid(Long agentContractFormulaId);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :通过公司ids 查找对应代理合同公式ids
     * @Param [companyIds]
     * <AUTHOR>
     */
    List<Long> getFormulaIdByCompanyIds(List<Long> companyIds);
}

package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;

import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.file.utils.FileUtils;
import com.get.salecenter.dao.convention.GoproRegistrationMapper;
import com.get.salecenter.vo.GoproRegistrationVo;
import com.get.salecenter.entity.GoproRegistration;
import com.get.salecenter.service.IConventionGoproRegistrationService;
import com.get.salecenter.dto.GoproRegistrationDto;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 活动报名业务逻辑类
 *
 * <AUTHOR>
 * @date 2022/5/10 11:25
 */
@Service
public class ConventionGoproRegistrationServiceImpl implements IConventionGoproRegistrationService {

    @Resource
    private GoproRegistrationMapper goproRegistrationMapper;

    /**
     * 活动报名列表
     *
     * @Date 12:12 2022/5/10
     * <AUTHOR>
     */
    @Override
    public List<GoproRegistrationDto> datas(GoproRegistrationDto data, Page<GoproRegistrationDto> page) {
        String str = "";
        String str1 = "";
        String str2 = "";
        String str3 = "";
        if(GeneralTool.isNotEmpty(data.getRetreatTypeNameStr())){
            List<String> result = Arrays.asList(data.getRetreatTypeNameStr().split(","));
            if(result.size() == 1){
                str1 = result.get(0);
            }
            if(result.size() == 2){
                str1 = result.get(0);
                str2 = result.get(1);
            }
            if(result.size() == 3){
                str1 = result.get(0);
                str2 = result.get(1);
                str3 = result.get(2);
            }
            str = "1";
        }
        IPage<GoproRegistrationDto> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        Long staffId = SecureUtil.getStaffId();
        List<Long> excludePersonnel = Arrays.asList(1L,71L,72L,106L);
        //获取分页数据
        List<GoproRegistrationDto> datas = goproRegistrationMapper.getGoProList(iPage,data.getCompany(),data.getYear(),data.getName(),data.getMobile(),data.getBdName(),data.getBdRegion(),str,str1,str2,str3, staffId,excludePersonnel.contains(staffId));
        page.setAll((int) iPage.getTotal());
        return datas;
    }

    @Override
    public int getCountSeatUsed(String type){
        int year = LocalDate.now().getYear();
        return goproRegistrationMapper.getCountSeatUsed(type,year);
    }

    @Override
    public synchronized List<GoproRegistrationDto> getIsExistMobile(String Mobile){
        int year = LocalDate.now().getYear();
        return goproRegistrationMapper.getIsExistMobile(Mobile,year);
    }

    @Override
    public Long addGopro(GoproRegistrationDto goproRegistrationDto) {
        GoproRegistration goproRegistration = BeanCopyUtils.objClone(goproRegistrationDto, GoproRegistration::new);
        goproRegistration.setGmtCreate(new Date());
        goproRegistration.setYear(LocalDate.now().getYear());
        goproRegistrationMapper.insert(goproRegistration);
        return goproRegistration.getId();
    }

    /**
     * 获取年份下拉
     * @return
     */
    @Override
    public List<BaseSelectEntity> getYears() {
        List<BaseSelectEntity> years = goproRegistrationMapper.getYears();
        return years.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Override
    public int getUsed(String type){
//        int countUsed = goproRegistrationMapper.getCountSeatUsed(type);
        return 0;
    }

    @Override
    public int getUsedByTypeAndId(String type,Long id){
        int year = LocalDate.now().getYear();
        return goproRegistrationMapper.getUsedByTypeAndId(type,id,year);
    }


    @Override
    public List<Map<String,Object>> getSeatCountList(){
        List<Map<String,Object>> list= goproRegistrationMapper.getSeatCountList();
        return list;
    }

    @Override
    public void exportGoPro(HttpServletResponse response, GoproRegistrationDto goproRegistrationDto){
        String str = "";
        String str1 = "";
        String str2 = "";
        String str3 = "";
        if(GeneralTool.isNotEmpty(goproRegistrationDto.getRetreatTypeNameStr())){
            List<String> result = Arrays.asList(goproRegistrationDto.getRetreatTypeNameStr().split(","));
            if(result.size() == 1){
                str1 = result.get(0);
            }
            if(result.size() == 2){
                str1 = result.get(0);
                str2 = result.get(1);
            }
            if(result.size() == 3){
                str1 = result.get(0);
                str2 = result.get(1);
                str3 = result.get(2);
            }
            str = "1";
        }
        IPage<GoproRegistrationDto> iPage = null;
        Long staffId = SecureUtil.getStaffId();
        List<Long> excludePersonnel = Arrays.asList(1L,71L,72L,106L);
        List<GoproRegistrationDto> list =  goproRegistrationMapper.getGoProList(iPage, goproRegistrationDto.getCompany(), goproRegistrationDto.getYear(), goproRegistrationDto.getName(), goproRegistrationDto.getMobile(), goproRegistrationDto.getBdName(), goproRegistrationDto.getBdRegion(),str,str1,str2,str3,staffId,excludePersonnel.contains(staffId));
        List<GoproRegistrationVo> listDto = new ArrayList();
        if(GeneralTool.isNotEmpty(list)){
            for (GoproRegistrationDto vo : list){
                GoproRegistrationVo goDto = new GoproRegistrationVo();
                goDto.setFkCompanyName(vo.getFkCompanyName());
                goDto.setRetreatTypeName(vo.getRetreatTypeName());
                goDto.setName(vo.getName());
                goDto.setGenderName(vo.getGenderName());
                goDto.setCompany(vo.getCompany());
                goDto.setMobile(vo.getMobile());
                goDto.setEmail(vo.getEmail());
                goDto.setAreaCountryName(vo.getAreaCountryName());
                goDto.setAreaStateName(vo.getAreaStateName());
//                goDto.setAreaCityName(dto1.getAreaCityName());
                goDto.setBdRegion(vo.getBdRegion());
                goDto.setBdName(vo.getBdName());
//                goDto.setVStatus(dto1.getVStatus());
                goDto.setRemark(vo.getRemark());
                goDto.setGmtCreate(vo.getGmtCreate());
                listDto.add(goDto);
            }
        }
        FileUtils.exportExcelNotWrapText(response, listDto, "GoProActivity", GoproRegistrationVo.class);
    }

    @Override
    public void update(GoproRegistrationDto goproRegistrationDto){
        GoproRegistration goproRegistration = BeanCopyUtils.objClone(goproRegistrationDto, GoproRegistration::new);
        goproRegistration.setGmtModified(new Date());
        goproRegistrationMapper.updateByPrimaryKey(goproRegistration);
    }

    @Override
    public void delete(Long id){
        goproRegistrationMapper.deleteById(id);
    }
}

package com.get.salecenter.service;


import com.get.salecenter.vo.ConventionTableRegistrationVo;
import com.get.salecenter.dto.ConventionTableRegistrationDto;

/**
 * @author: Sea
 * @create: 2020/8/31 11:40
 * @verison: 1.0
 * @description:
 */
public interface IConventionTableRegistrationService {
    /**
     * 配置桌台-培训桌
     *
     * @param conventionTableRegistrationDto
     * @return
     */
    void configurationTable(ConventionTableRegistrationDto conventionTableRegistrationDto);

    /**
     * 通过桌台id 查询 培训桌-峰会报名 中间表对应报名信息
     *
     * @param id
     * @return
     */
    ConventionTableRegistrationVo getRegistrationByTableId(Long id);
}

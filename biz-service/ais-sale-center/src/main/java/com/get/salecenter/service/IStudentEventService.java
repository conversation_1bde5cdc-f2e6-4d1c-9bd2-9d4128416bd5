package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.salecenter.vo.StudentEventVo;
import com.get.salecenter.dto.StudentEventDto;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/11/10
 * @TIME: 16:03
 * @Description:
 **/
public interface IStudentEventService {
    /**
     * @return com.get.salecenter.vo.StudentEventVo
     * @Description: 详情
     * @Param [id]
     * <AUTHOR>
     */
    StudentEventVo findStudentEventById(Long id);

    /**
     * @return java.util.List<com.get.salecenter.vo.StudentEventVo>
     * @Description: 列表数据
     * @Param [studentEventDto, page]
     * <AUTHOR>
     */
    List<StudentEventVo> getStudentEvents(StudentEventDto studentEventDto, Page page);

    /**
     * @return com.get.salecenter.vo.StudentEventVo
     * @Description: 修改
     * @Param [studentEventDto]
     * <AUTHOR>
     */
    StudentEventVo updateStudentEvent(StudentEventDto studentEventDto);

    /**
     * @return java.lang.Long
     * @Description: 保存
     * @Param [studentEventDto]
     * <AUTHOR>
     */
    Long addStudentEvent(StudentEventDto studentEventDto);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 删除被合并学生事件
     * @param mergedStudentId
     */
    void deleteByStudentId(Long mergedStudentId);

    /**
     * 合并学生事件
     * @param mergedStudentId
     */
    void mergeData(Long mergedStudentId, Long targetStudentId);
}

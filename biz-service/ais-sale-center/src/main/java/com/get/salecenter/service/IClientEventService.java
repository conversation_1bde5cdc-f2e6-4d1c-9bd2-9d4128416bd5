package com.get.salecenter.service;

import com.get.common.result.SearchBean;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.salecenter.vo.ClientEventVo;
import com.get.salecenter.dto.ClientEventAddDto;
import com.get.salecenter.dto.ClientEventDto;
import com.get.salecenter.dto.ClientEventRemindDto;

import java.util.List;

/**
 * author:Neil
 * Time: 14:09
 * Date: 2022/8/17
 * Description:
 */
public interface IClientEventService {
    /**
     * 咨询客户列表事件数据
     * @param data
     * @param page
     * @return
     */
    List<ClientEventVo> getClientEvents(ClientEventDto data, SearchBean<ClientEventDto> page);

    /**
     * 添加咨询客户事件
     * @param clientEventVo
     * @return
     */
    Long addClientEvent(ClientEventAddDto clientEventVo);

    /**
     * 删除询客户事件
     * @param id
     */
    void delete(Long id);

    /**
     * 添加回访提醒
     * @param clientEventRemindDto
     */
    void addRemindTaskEven(ClientEventRemindDto clientEventRemindDto);

    /**
     * 客户事件类型下拉
     * @return
     */
    List<BaseSelectEntity> getClientEventTypeSelect();

}

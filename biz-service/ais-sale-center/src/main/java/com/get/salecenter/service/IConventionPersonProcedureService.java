package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.salecenter.vo.ConventionPersonProcedureVo;
import com.get.salecenter.vo.ConventionProcedureVo;
import com.get.salecenter.dto.ConventionPersonProcedureDto;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/7/15 15:39
 * @verison: 1.0
 * @description: 峰会参展人员-参会流程配置管理接口
 */
public interface IConventionPersonProcedureService {

    /**
     * 参会流程配置
     *
     * @param conventionPersonProcedureDtos
     */
    void conventionProcedureConfiguration(List<ConventionPersonProcedureDto> conventionPersonProcedureDtos);


    /**
     * 参会流程配置列表
     *
     * @param conventionId
     * @param personId
     * @return
     */
    List<ConventionProcedureVo> getConventionPersonProcedure(Long conventionId, Long personId);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 根据流程id 条件查找对应参加流程的人员信息
     *
     * @param conventionPersonProcedureDto
     * @param page
     * @return
     */
    List<ConventionPersonProcedureVo> getConventionPerson(ConventionPersonProcedureDto conventionPersonProcedureDto, Page page);


    /**
     * 导出流程的参会人列表
     *
     * @param response
     * @param conventionPersonProcedureDto
     */
    void exportConventionPersonProcedureDetail(HttpServletResponse response, ConventionPersonProcedureDto conventionPersonProcedureDto);
}

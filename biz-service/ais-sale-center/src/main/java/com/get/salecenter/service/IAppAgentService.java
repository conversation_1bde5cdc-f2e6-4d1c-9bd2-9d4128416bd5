package com.get.salecenter.service;

import com.alibaba.fastjson.JSONObject;
import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.service.GetService;
import com.get.filecenter.vo.FileVo;
import com.get.partnercenter.dto.RegisterPartnerUserDto;
import com.get.partnercenter.vo.RegisterPartnerUserVo;
import com.get.permissioncenter.vo.StaffVo;
import com.get.salecenter.dto.AgentContractRenewalDto;
import com.get.salecenter.dto.AppAgentAddDto;
import com.get.salecenter.dto.AppAgentApproveCommentDto;
import com.get.salecenter.dto.AppAgentChangeDataDto;
import com.get.salecenter.dto.AppAgentContactPersonListDto;
import com.get.salecenter.dto.AppAgentContractAccountListDto;
import com.get.salecenter.dto.AppAgentDto;
import com.get.salecenter.dto.AppAgentListDto;
import com.get.salecenter.dto.AppAgentRenewalUpdateDto;
import com.get.salecenter.dto.AppAgentUpdateDto;
import com.get.salecenter.dto.MediaAndAttachedDto;
import com.get.salecenter.entity.AppAgent;
import com.get.salecenter.entity.SaleContactPerson;
import com.get.salecenter.enums.ContactPersonTypeEnum;
import com.get.salecenter.vo.AppAgentChangeDataVo;
import com.get.salecenter.vo.AppAgentContactPersonListVo;
import com.get.salecenter.vo.AppAgentContractAccountListVo;
import com.get.salecenter.vo.AppAgentFormDetailVo;
import com.get.salecenter.vo.AppAgentListVo;
import com.get.salecenter.vo.AppAgentVo;
import com.get.salecenter.vo.MediaAndAttachedVo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: Hardy
 * @create: 2022/11/17 17:15
 * @verison: 1.0
 * @description:
 */
public interface IAppAgentService extends GetService<AppAgent> {

    /**
     * 代理申请表单新增
     *
     * @param appAgentAddDto
     * @return
     */
    String addAppAgent(AppAgentAddDto appAgentAddDto);

    /**
     * 列表
     * 
     * @param appAgentListDto
     * @param page
     * @return
     */
    List<AppAgentListVo> getAppAgents(AppAgentListDto appAgentListDto, Page page);

    /**
     * 详情
     * 
     * @param id
     * @return
     */
    AppAgentVo findAppAgentById(Long id);

    /**
     * 更新
     * 
     * @param appAgentUpdateDto
     * @return
     */
    AppAgentVo updateAppAgent(AppAgentUpdateDto appAgentUpdateDto);

    /**
     * 联系人列表
     * 
     * @param appAgentContactPersonListDto
     * @param page
     * @return
     */
    List<AppAgentContactPersonListVo> getAppAgentContactPersons(
            AppAgentContactPersonListDto appAgentContactPersonListDto, Page page);

    /**
     * 合同账户列表
     * 
     * @param appAgentContractAccountListDto
     * @param page
     * @return
     */
    List<AppAgentContractAccountListVo> getAppAgentContractAccounts(
            AppAgentContractAccountListDto appAgentContractAccountListDto, Page page);

    /**
     * 附件列表
     * 
     * @param mediaAndAttachedDto
     * @param voSearchBean
     * @return
     */
    List<MediaAndAttachedVo> getAppAgentMedia(MediaAndAttachedDto mediaAndAttachedDto, Page voSearchBean);

    /**
     * 添加附件
     * 
     * @param mediaAttachedVo
     * @return
     */
    List<MediaAndAttachedVo> addAgentMedia(List<MediaAndAttachedDto> mediaAttachedVo);

    /**
     * 更新状态
     * 
     * @param id
     * @param appStatus
     */
    void updateAppStatus(Long id, Integer appStatus);

    /**
     * 设置审核同意
     * 
     * @param appAgentVo
     */
    void doSetAgree(AppAgentVo appAgentVo);

    /**
     * 申请状态下拉
     * 
     * @return
     */
    List<Map<String, Object>> getAppStatusSelect();

    /**
     * 验证手机号
     * 
     * @param id
     * @return
     */
    String validateAgentContactPersonAndAccount(Long id);

    /**
     * 获取bd下拉
     * 
     * @param companyId
     * @return
     */
    List<BaseSelectEntity> getBdStaffSelect(Long companyId);

    /**
     * 表单配置项
     * 
     * @return
     */
    JSONObject getAppAgentFormConfig();

    /**
     * 代理申请表单回显
     * 
     * @param sign
     * @return
     */
    AppAgentFormDetailVo getAppAgentFormDetail(String sign);

    AppAgentFormDetailVo getAppAgentFormDetailById(Long id);

    /**
     * 常用国家下拉框
     * 
     * @return
     */
    List<BaseSelectEntity> getCommonCountrySelect();

    /**
     * 获取下载文件
     * 
     * @param response
     */
    void getDownloadFilePrivate(HttpServletResponse response, FileVo fileVo);

    /**
     * 奖学金用户是否已申请
     * 
     * @param userId
     * @return
     */
    Boolean validatedUser(Long userId);

    StaffVo getBdStaffInfoHasEmail(Long bdStaffId);

    /**
     * 获取或构建代理续约申请数据回显，不保存到数据库
     * 优先获取已存在的续约申请数据，如果不存在则从代理基础数据构建新的续约申请数据
     *
     * @param agentContractRenewalDto 代理合同续约DTO（包含代理ID和联系人ID）
     * @return 续约申请数据（现有数据或新构建的数据）
     */
    AppAgentFormDetailVo getOrBuildRenewalApplicationData(AgentContractRenewalDto agentContractRenewalDto);

    /**
     * 代理申请续签修改
     *
     * @param appAgentRenewalUpdateDto
     */
    void renewalUpdate(AppAgentRenewalUpdateDto appAgentRenewalUpdateDto);

    /**
     * 续约审核通过修改
     *
     * @param appAgentAddDto
     */
    void renewalAgreeUpdate(AppAgentAddDto appAgentAddDto);

    /**
     * 变更申请数据获取
     *
     * @param fkAppAgentId
     * @return
     */
    AppAgentChangeDataVo getChangeData(Long fkAppAgentId);

    /**
     * 变更申请数据修改
     *
     * @param appAgentChangeDataDto
     */
    void changeData(AppAgentChangeDataDto appAgentChangeDataDto);

    void getLetterForAgentInformationChangePdf(AppAgentDto appAgentDto, HttpServletResponse response);

    /**
     * 根据代理ID集合批量获取有效的BD员工信息（跳过邮箱为空的员工）
     *
     * @param agentIds 代理ID集合
     * @return Map<Long, StaffVo> key为代理ID，value为BD员工信息
     */
    Map<Long, StaffVo> getStaffInfoSkipEmptyEmailByAgentIds(Set<Long> agentIds);

    /**
     * 续约表单新增
     *
     * @param appAgentAddDto
     */
    void renewalAdd(AppAgentAddDto appAgentAddDto);

    /**
     * 判断联系人是否符合注册条件（SaleContactPerson版本）
     *
     * @param contactPerson 联系人
     * @param personTypeEnum 联系人类型枚举
     * @return 是否符合条件
     */
    boolean isEligibleContactPerson(SaleContactPerson contactPerson, ContactPersonTypeEnum personTypeEnum);

    /**
     * 调用合作伙伴中心注册用户的公共方法
     *
     * @param registerList 注册用户列表
     * @param agentId 代理ID（用于日志记录）
     * @return 注册结果列表
     */
    List<RegisterPartnerUserVo> callPartnerCenterRegister(List<RegisterPartnerUserDto> registerList, Long agentId);

    /**
     * 续约审核拒绝修改并发送邮件
     *
     * @param appAgentApproveCommentDto 审批意见DTO
     */
    void renewalRejectAndSendEmail(AppAgentApproveCommentDto appAgentApproveCommentDto);

    List<AppAgentVo> getAppAgentFormDetailByAgentId(Long agentId);

    List<BaseSelectEntity> getCommonCountrySelectBySummit();
}

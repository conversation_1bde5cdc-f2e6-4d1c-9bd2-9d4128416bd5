package com.get.salecenter.service.impl;


import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.vo.*;
import com.get.salecenter.vo.TableChartVo;
import com.get.salecenter.entity.ConventionHotel;
import com.get.salecenter.service.ChartService;
import com.get.salecenter.service.IConventionHotelRoomPersonService;
import com.get.salecenter.service.IConventionHotelRoomService;
import com.get.salecenter.service.IConventionHotelService;
import com.get.salecenter.service.IConventionTableService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: Sea
 * @create: 2020/10/27 15:04
 * @verison: 1.0
 * @description:
 */
@Service
public class ChartServiceImpl implements ChartService {
    @Resource
    private IConventionHotelRoomService hotelRoomService;
    @Resource
    private IConventionHotelService hotelService;
    @Resource
    private IConventionHotelRoomPersonService hotelRoomPersonService;
    @Resource
    private IConventionTableService tableService;

    @Override
    public List<DateRoomChartVo> getRoomChartList(boolean isArrange, Long conventionId, List<Integer> types) {
        //该峰会下所有酒店房型
        List<ConventionHotel> conventionHotelList = hotelService.getConventionHotelList(conventionId);
        //获取该峰会下所有酒店房型id
        List<Long> conventionHotelIds = conventionHotelList.stream().map(ConventionHotel::getId).collect(Collectors.toList());
        if (GeneralTool.isEmpty(conventionHotelIds)) {
            conventionHotelIds.add(0L);
        }
        //获取日期集合
        List<String> dates = hotelRoomService.getDates(conventionHotelIds);
        List<DateRoomChartVo> dateRoomChartVos = new ArrayList<>();
        for (String date : dates) {
            DateRoomChartVo dateRoomChartVo = new DateRoomChartVo();
            List<RoomChartDataVo> child = new ArrayList<>();
            dateRoomChartVo.setDate(date);
            for (ConventionHotel conventionHotel : conventionHotelList) {
                RoomChartDataVo roomChartDataVo = new RoomChartDataVo();
                Integer bedCount = conventionHotel.getBedCount();
                String roomType = conventionHotel.getRoomType();
                roomChartDataVo.setRoomTypeName(roomType);
                //通过酒店id和日期查找房间ids
                List<Long> roomIds = hotelRoomService.getRoomIds(conventionHotel.getId(), date);
                //床位总数 = 床位数 * 房间数
                int bedSum = bedCount * roomIds.size();
                //查找该日期和房型下，所有房间已经被安排得床位数
                //安排总人数
                Integer arrangedBedCount = hotelRoomPersonService.getArrangedBedCount(roomIds,null);
                //指定参会人安排数
                Integer arrangedBedCountByType = arrangedBedCount;
                if (GeneralTool.isNotEmpty(types)){
                    arrangedBedCountByType = hotelRoomPersonService.getArrangedBedCount(roomIds,types);
                }
                int roomSum = roomIds.size();
                //查找该日期下和房型下被使用的房间数
                Integer usedRoomCount = hotelRoomPersonService.getUsedRoomCount(roomIds,null);
                Integer usedRoomCountByType = usedRoomCount;
                if (GeneralTool.isNotEmpty(types)){
                    usedRoomCountByType = hotelRoomPersonService.getUsedRoomCount(roomIds,types);
                }
                //显示 已安排/未安排
                if (isArrange) {
                    //对应选择的类型
                    roomChartDataVo.setPersonCount(arrangedBedCountByType);
                    roomChartDataVo.setRoomCount(usedRoomCountByType);
                } else {
                    roomChartDataVo.setPersonCount(bedSum - arrangedBedCount);
                    roomChartDataVo.setRoomCount(roomSum - usedRoomCount);
                }
                child.add(roomChartDataVo);
            }
            dateRoomChartVo.setChild(child);
            dateRoomChartVos.add(dateRoomChartVo);
        }
        return dateRoomChartVos;
    }


    @Override
    public ChartVo getTableChartList(Long conventionId, String tableType) {
        List<TableChartVo> result = new ArrayList<>();
        //该峰会和类型下所有桌台信息
        List<TableChartVo> tableChartVoList = tableService.getTableChartList(conventionId, tableType);
        //已安排统计图对象
        TableChartVo arrangedTableChartVo = new TableChartVo();
        //已安排座位数集合
        List<Integer> arrangedCountList = new ArrayList<>();
        //未安排统计图对象
        TableChartVo notArrangedTableChartVo = new TableChartVo();
        //未安排座位数集合
        List<Integer> notArrangedCountList = new ArrayList<>();
        Integer personCount = 0;
        Integer arrangedPersonCount = 0;
        for (TableChartVo tableChartVo : tableChartVoList) {
            //已经安排了多少人
            arrangedPersonCount = arrangedPersonCount + tableChartVo.getCount();
            //可坐多少人
            personCount = personCount + tableChartVo.getSeatCount();
            arrangedCountList.add(tableChartVo.getCount());
            notArrangedCountList.add(tableChartVo.getSeatCount() - tableChartVo.getCount());
        }
        arrangedTableChartVo.setName("已安排座位数");
        arrangedTableChartVo.setCountList(arrangedCountList);

        notArrangedTableChartVo.setName("未安排座位数");
        notArrangedTableChartVo.setCountList(notArrangedCountList);

        result.add(arrangedTableChartVo);
        result.add(notArrangedTableChartVo);
        //统计结果对象
        ChartVo countResult = new ChartVo();
        //设置共多少桌
        Integer tableCount = tableChartVoList.size();
        countResult.setTableCount(tableCount);
        //设置可坐多少人
        countResult.setPersonCount(personCount);
        //设置已经安排多少人
        countResult.setArrangedPersonCount(arrangedPersonCount);
        //设置还剩多少人
        Integer notArrangedPersonCount = personCount - arrangedPersonCount;
        countResult.setNotArrangedPersonCount(notArrangedPersonCount);
        countResult.setTableChartDtos(result);
        return countResult;
    }

    @Override
    public List<String> getDates(Long conventionId) {
        List<Long> conventionHotelIds = hotelService.getConventionHotelIds(conventionId);
        return hotelRoomService.getDates(conventionHotelIds);
    }

    @Override
    public List<String> getTableNums(Long conventionId, String tableType) {
        return tableService.getTableNums(conventionId, tableType);
    }

    @Override
    public List<UsedRoomChartVo> getUsedRoomChartList(boolean isUsed, Long conventionId) {
        //该峰会下所有酒店房型
        List<ConventionHotel> conventionHotelList = hotelService.getConventionHotelList(conventionId);
        //获取该峰会下所有酒店房型id
        List<Long> conventionHotelIds = conventionHotelList.stream().map(ConventionHotel::getId).collect(Collectors.toList());
        if (GeneralTool.isEmpty(conventionHotelIds)) {
            conventionHotelIds.add(0L);
        }
        //获取日期集合
        List<String> dates = hotelRoomService.getDates(conventionHotelIds);
        List<UsedRoomChartVo> result = new ArrayList<>();

        //根据房型归类返回数据
        for (ConventionHotel conventionHotel : conventionHotelList) {
            UsedRoomChartVo usedRoomChartVo = new UsedRoomChartVo();
            List<Integer> countList = new ArrayList<>();
            String roomType = conventionHotel.getRoomType();
            //遍历日期 日期为x轴
            for (String date : dates) {
                //通过酒店id和日期查找房间ids
                List<Long> roomIds = hotelRoomService.getRoomIds(conventionHotel.getId(), date);
                //房间总数
                int roomSum = roomIds.size();
                //查找该日期下和房型下被使用的房间数
                Integer usedRoomCount = hotelRoomPersonService.getUsedRoomCount(roomIds,null);
                //显示 已使用/未使用 的房间数
                if (isUsed) {
                    countList.add(usedRoomCount);
                } else {
                    countList.add(roomSum - usedRoomCount);
                }

            }
            usedRoomChartVo.setName(roomType);
            usedRoomChartVo.setCountList(countList);
            result.add(usedRoomChartVo);
        }

        return result;
    }

    @Override
    public List<String> getRoomTypeNames(Long conventionId) {
        List<String> roomTypeNames = new ArrayList<>();
        //该峰会下所有酒店房型
        List<ConventionHotel> conventionHotelList = hotelService.getConventionHotelList(conventionId);
        for (ConventionHotel conventionHotel : conventionHotelList) {
            roomTypeNames.add(conventionHotel.getRoomType());
        }
        return roomTypeNames;
    }


}

package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseVoEntity;
import com.get.salecenter.vo.AppAgentApproveCommentVo;
import com.get.salecenter.service.IAppAgentApproveCommentService;
import com.get.salecenter.dto.AppAgentApproveCommentDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2023/3/31 10:39
 * @verison: 1.0
 * @description:
 */
@Api(tags = "代理申请审批意见管理")
@RestController
@RequestMapping("sale/appAgentApproveComment")
public class AppAgentApproveCommentController {

    @Resource
    private IAppAgentApproveCommentService appAgentApproveCommentService;


    @ApiOperation(value = "保存代理申请审批意见", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/代理申请审批意见管理/保存代理申请审批意见")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated({BaseVoEntity.Add.class}) AppAgentApproveCommentDto appAgentApproveCommentDto) {
        return SaveResponseBo.ok(appAgentApproveCommentService.addAppAgentApproveCommentVo(appAgentApproveCommentDto));
    }


    @ApiOperation(value = "发送邮件", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/代理申请审批意见管理/发送邮件")
    @PostMapping("sendEmail")
    public ResponseBo sendEmail(@RequestParam("id")Long id) {
        appAgentApproveCommentService.sendEmailUnified(id);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "保存并发送邮件", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/代理申请审批意见管理/保存并发送邮件")
    @PostMapping("saveAndSendEmail")
    public ResponseBo saveAndSendEmail(@RequestBody @Validated({BaseVoEntity.Add.class}) AppAgentApproveCommentDto appAgentApproveCommentDto) {
        return SaveResponseBo.ok(appAgentApproveCommentService.saveAndSendEmail(appAgentApproveCommentDto));
    }


    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/代理申请审批意见管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        appAgentApproveCommentService.delete(id);
        return DeleteResponseBo.ok();
    }


    @ApiOperation(value = "列表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/代理申请审批意见管理/列表")
    @PostMapping("datas")
    public ResponseBo<AppAgentApproveCommentVo> datas(@RequestBody SearchBean<AppAgentApproveCommentDto> page) {
        List<AppAgentApproveCommentVo> datas = appAgentApproveCommentService.getAppAgentApproveComments(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    @ApiOperation(value = "申请管理审批拒绝并保存审批意见", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/申请管理审批拒绝并保存审批意见")
    @PostMapping("saveCommentAndSendRejectEmail")
    public ResponseBo saveCommentAndSendRejectEmail(@RequestBody @Validated({BaseVoEntity.Add.class}) AppAgentApproveCommentDto appAgentApproveCommentDto) {
        Long id = appAgentApproveCommentService.saveCommentAndSendRejectEmail(appAgentApproveCommentDto);
        return SaveResponseBo.ok(id);
    }

}

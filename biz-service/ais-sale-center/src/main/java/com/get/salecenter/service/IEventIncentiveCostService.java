package com.get.salecenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.common.result.Page;
import com.get.salecenter.vo.EventBillVo;
import com.get.salecenter.vo.EventIncentiveCostVo;
import com.get.salecenter.entity.EventIncentiveCost;
import com.get.salecenter.dto.EventIncentiveCostListDto;
import com.get.salecenter.dto.EventIncentiveCostUpdateDto;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2022/7/19 14:45
 * @verison: 1.0
 * @description:
 */
public interface IEventIncentiveCostService extends IService<EventIncentiveCost> {

    /**
     * 新增奖励推广活动费用归口
     *
     * @param eventIncentiveCostUpdateDto
     * @return
     */
    Long addEventIncentiveCost(EventIncentiveCostUpdateDto eventIncentiveCostUpdateDto);

    /**
     * 删除接口
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 更新接口
     *
     * @param eventIncentiveCostUpdateDto
     * @return
     */
    EventIncentiveCostVo updateEventIncentiveCost(EventIncentiveCostUpdateDto eventIncentiveCostUpdateDto);

    /**
     * 详情接口
     *
     * @param id
     * @return
     */
    EventIncentiveCostVo findEventIncentiveCostById(Long id);

    /**
     * 列表
     *
     * @param eventIncentiveCostListDto
     * @return
     */
    List<EventIncentiveCostVo> getEventIncentiveCosts(EventIncentiveCostListDto eventIncentiveCostListDto, Page page);

    /**
     * 活动费用下拉框
     *
     * @param institutionProviderId
     * @param companyId
     * @param eventIncentiveCostId
     * @return
     */
    List<EventBillVo> getEventBillSelect(Long institutionProviderId, Long companyId, Long eventIncentiveCostId);

    BigDecimal getEventIncentiveCostSubtotal(EventIncentiveCostUpdateDto eventIncentiveCostVo);
}

package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.permissioncenter.vo.DepartmentAndStaffVo;
import com.get.salecenter.dto.*;
import com.get.salecenter.service.IEventBillService;
import com.get.salecenter.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * @author: Hardy
 * @create: 2022/5/9 11:40
 * @verison: 1.0
 * @description: 活动汇总费用管理
 */
@Api(tags = "活动汇总费用管理")
@RestController
@RequestMapping("sale/eventBill")
public class EventBillController {

    @Resource
    private IEventBillService eventBillService;

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :新增信息（包含应收计划和发票的生成和绑定）
     * @Param []
     * <AUTHOR>
     */
    @ApiOperation(value = "发起收款计划", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/活动汇总费用管理/发起收款计划")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(EventBillUpdateDto.Add.class)  EventBillUpdateDto eventBillVo) {
        return SaveResponseBo.ok(eventBillService.addEventBill(eventBillVo));
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.EventVo>
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/活动汇总费用管理/活动汇总费用详情")
    @GetMapping("/{id}")
    public ResponseBo<EventBillVo> detail(@PathVariable("id") Long id) {
        EventBillVo data = eventBillService.findEventBillById(id);
        return new ResponseBo<>(data);
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.EventVo>
     * @Description :列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/活动汇总费用管理/活动汇总费用列表")
    @PostMapping("datas")
    public ResponseBo<EventBillListVo> datas(@RequestBody SearchBean<EventBillListDto> page) {
        List<EventBillListVo> datas = eventBillService.getEventBills(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    @ApiOperation(value = "导出活动费用汇总列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/活动汇总费用管理/活动汇总费用列表导出")
    @PostMapping("exportEventBillList")
    public void exportEventBillList(@RequestBody SearchBean<EventBillListDto> page, HttpServletResponse response) {
        eventBillService.exportEventBillList(page.getData(),page,response);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentVo>
     * @Description: 修改信息
     * @Param [studentVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/活动汇总费用管理/编辑")
    @PostMapping("update")
    public ResponseBo<EventBillVo> update(@RequestBody @Validated(EventBillUpdateDto.Update.class) EventBillUpdateDto eventBillUpdateDto) {
        return UpdateResponseBo.ok(eventBillService.updateEventBill(eventBillUpdateDto));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.MediaAndAttachedDto>
     * @Description : 查询活动汇总费用附件
     * @Param [voSearchBean]
     * <AUTHOR>
     */
    @ApiOperation(value = "查询活动汇总费用附件", notes = "keyWord为关键词")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/活动汇总费用管理/查询附件")
    @PostMapping("getItemMedia")
    public ResponseBo<MediaAndAttachedVo> getItemMedia(@RequestBody SearchBean<MediaAndAttachedDto> voSearchBean) {
        List<MediaAndAttachedVo> staffMedia = eventBillService.getItemMedia(voSearchBean.getData(), voSearchBean);
        Page page = BeanCopyUtils.objClone(voSearchBean, Page::new);
        return new ListResponseBo<>(staffMedia, page);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.MediaAndAttachedDto>
     * @Description : 保存活动汇总费用附件
     * @Param [mediaAttachedVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "保存活动汇总费用附件")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/活动汇总费用管理/保存附件")
    @PostMapping("upload")
    public ResponseBo<MediaAndAttachedVo> addAgentMedia(@RequestBody @Validated(MediaAndAttachedDto.Add.class)  ValidList<MediaAndAttachedDto> mediaAttachedVo) {
        return new ListResponseBo<>(eventBillService.addItemMedia(mediaAttachedVo));
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description :作废接口
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "作废接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/活动汇总费用管理/作废")
    @GetMapping("updateInvalidStatus/{id}")
    public ResponseBo updateInvalidStatus(@PathVariable("id") Long id) {
        eventBillService.updateInvalidStatus(id);
        return DeleteResponseBo.ok();
    }


    /**
     * @param commentDto
     * @return
     * @
     */
    @ApiOperation(value = "编辑评论")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/活动汇总费用管理/编辑评论")
    @PostMapping("editComment")
    public ResponseBo editComment(@RequestBody @Validated(CommentDto.Add.class) CommentDto commentDto) {
        return SaveResponseBo.ok(eventBillService.editComment(commentDto));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.AgentContractVo>
     * @Description: 评论列表数据
     * @Param [searchBean]
     * <AUTHOR>
     */
    @ApiOperation(value = "评论列表数据")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/活动汇总费用管理/查询评论")
    @PostMapping("getComments")
    public ResponseBo<CommentVo> getComment(@RequestBody SearchBean<CommentDto> searchBean) {
        List<CommentVo> datas = eventBillService.getComments(searchBean.getData(), searchBean);
        Page p = BeanCopyUtils.objClone(searchBean, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 下拉
     * @Param []
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "活动摘要下拉框(搜索)", notes = "")
    @GetMapping("getSummarySelect")
    public ResponseBo<BaseSelectEntity> getSummarySelect(@RequestParam("fkCompanyId") Long fkCompanyId) {
        return new ListResponseBo<>(eventBillService.getSummarySelect(fkCompanyId));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 下拉
     * @Param []
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "活动摘要下拉框(搜索)", notes = "")
    @GetMapping("getSummarySelectList")
    public ResponseBo<BaseSelectEntity> getSummarySelect(@RequestParam("fkCompanyIdList") List<Long> fkCompanyIdList) {
        return new ListResponseBo<>(eventBillService.getSummarySelectList(fkCompanyIdList));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :创建财务单据
     * @Param []
     * <AUTHOR>
     */
    @ApiOperation(value = "创建财务单据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/活动汇总费用管理/创建财务单据")
    @PostMapping("generateBill")
    public ResponseBo generateBill(@RequestBody EventBillUpdateDto eventBillVo) {
        eventBillService.generateBill(eventBillVo);
        return ResponseBo.ok();
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.EventVo>
     * @Description :默认通知人列表
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "默认通知人列表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/活动汇总费用管理/默认通知人列表")
    @PostMapping("getDefaultStafffNotices")
    public ResponseBo<DepartmentAndStaffVo> getDefaultStafffNotices(@RequestParam("fkCompanyId")Long fkCompanyId) {
        List<DepartmentAndStaffVo> datas = eventBillService.getDefaultStafffNotices(fkCompanyId);
        return new ListResponseBo<>(datas);
    }



    /**
     * @return com.get.common.result.ResponseBo
     * @Description :作废接口
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "财务一键作废", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/活动汇总费用管理/财务一键作废")
    @GetMapping("updateInvalidStatusFinance/{id}")
    public ResponseBo updateInvalidStatusFinance(@PathVariable("id") Long id) {
        eventBillService.updateInvalidStatusFinance(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :分配状态下拉
     * @Param []
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "分配状态下拉", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/活动汇总费用管理/分配状态下拉")
    @GetMapping("getAllocationStatusSelect")
    public ResponseBo<Map<String, Object>> getAllocationStatusSelect() {
        return new ListResponseBo<>(eventBillService.getAllocationStatusSelect());
    }



    /**
     * @return com.get.common.result.ResponseBo
     * @Description :作废接口
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "作废财务单据", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/活动汇总费用管理/作废财务单据")
    @GetMapping("updateInvalidStatusFinancialDocuments/{id}")
    public ResponseBo updateInvalidStatusFinancialDocuments(@PathVariable("id") Long id) {
        eventBillService.updateInvalidStatusFinancialDocuments(id);
        return DeleteResponseBo.ok();
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description :重新发起
     * @Param [eventBillUpdateAmountDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "重新发起", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/活动汇总费用管理/重新发起")
    @PostMapping("updateAmount")
    public ResponseBo updateAmount(@RequestBody  @Validated(EventBillUpdateAmountDto.Update.class) EventBillUpdateAmountDto eventBillUpdateAmountDto) {
        return eventBillService.updateAmount(eventBillUpdateAmountDto);
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.EventVo>
     * @Description :列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "统计小计", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/活动汇总费用管理/统计小计")
    @PostMapping("getEventBillSubtotals")
    public ResponseBo<EventBillSubtotalVo> getEventBillSubtotals(@RequestBody EventBillListDto eventBillListDto) {
        List<EventBillSubtotalVo> datas = eventBillService.getEventBillSubtotals(eventBillListDto);
        return new ListResponseBo<>(datas);
    }

    @ApiOperation(value = "活动汇总费用应收列表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/活动汇总费用管理/活动汇总费用应收列表")
    @PostMapping("getEventBillReceivablePlanList")
    public ResponseBo<ReceivablePlanNewVo> getEventBillReceivablePlanList(@RequestParam("invoiceNum")String invoiceNum) {
        List<ReceivablePlanNewVo> datas = eventBillService.getEventBillReceivablePlanList(invoiceNum);
        return new ListResponseBo<>(datas);
    }

}

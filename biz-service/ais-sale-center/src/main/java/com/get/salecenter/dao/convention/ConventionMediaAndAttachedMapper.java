package com.get.salecenter.dao.convention;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.entity.SaleMediaAndAttached;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Author:cream
 * @Date: 2023/5/12  11:26
 */
@Mapper
@DS("conventiondb")
public interface ConventionMediaAndAttachedMapper extends GetMapper<SaleMediaAndAttached> {

    Integer getNextIndexKey(@Param("tableId") Long tableId, @Param("tableName") String tableName);
}

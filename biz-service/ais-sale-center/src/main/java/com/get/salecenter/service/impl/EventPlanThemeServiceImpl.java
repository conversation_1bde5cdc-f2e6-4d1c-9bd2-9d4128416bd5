package com.get.salecenter.service.impl;

import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.EventPlanThemeMapper;
import com.get.salecenter.vo.EventPlanThemeVo;
import com.get.salecenter.entity.EventPlanTheme;
import com.get.salecenter.entity.EventPlanThemeOffline;
import com.get.salecenter.entity.EventPlanThemeOnline;
import com.get.salecenter.entity.EventPlanThemeWorkshop;
import com.get.salecenter.service.EventPlanThemeOfflineService;
import com.get.salecenter.service.EventPlanThemeOnlineService;
import com.get.salecenter.service.EventPlanThemeService;
import com.get.salecenter.service.EventPlanThemeWorkshopService;
import com.get.salecenter.dto.EventPlanThemeDto;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12
 */
@Service
public class EventPlanThemeServiceImpl extends BaseServiceImpl<EventPlanThemeMapper, EventPlanTheme> implements EventPlanThemeService {

    @Resource
    private UtilService utilService;

    @Resource
    private EventPlanThemeMapper eventPlanThemeMapper;


    @Resource
    @Lazy
    private EventPlanThemeOnlineService eventPlanThemeOnlineService;


    @Resource
    @Lazy
    private EventPlanThemeOfflineService eventPlanThemeOfflineService;


    @Resource
    @Lazy
    private EventPlanThemeWorkshopService eventPlanThemeWorkshopService;


    @Override
    public List<EventPlanThemeVo> getEventPlanThemes(Long fkEventPlanId) {
        if(GeneralTool.isEmpty(fkEventPlanId)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }

        List<EventPlanThemeVo> themeDtoList = eventPlanThemeMapper.getEventPlanThemes(fkEventPlanId);
        if(GeneralTool.isEmpty(themeDtoList)){
            return Collections.emptyList();
        }

        for(EventPlanThemeVo dto : themeDtoList){
            switch (dto.getDisplayType()){
                case 1:
                    dto.setDisplayTypeName(ProjectExtraEnum.EVENT_PLAN_THEME_ONLINE.value);
                    break;
                case 2:
                    dto.setDisplayTypeName(ProjectExtraEnum.EVENT_PLAN_THEME_OFFLINE.value);
                    break;
                case 3:
                    dto.setDisplayTypeName(ProjectExtraEnum.EVENT_PLAN_THEME_WORKSHOP.value);
                    break;
                default:
                    break;
            }
        }
        return themeDtoList;
    }

    @Override
    public EventPlanThemeVo findEventPlanThemeById(Long id){
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        EventPlanTheme theme = eventPlanThemeMapper.selectById(id);
        EventPlanThemeVo eventPlanDto = BeanCopyUtils.objClone(theme, EventPlanThemeVo::new);
        return eventPlanDto;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long addEventPlanTheme(EventPlanThemeDto vo) {
        if (GeneralTool.isEmpty(vo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        EventPlanTheme eventPlanTheme = BeanCopyUtils.objClone(vo, EventPlanTheme::new);
        Integer maxViewOrder = eventPlanThemeMapper.getMaxViewOrder(vo.getFkEventPlanId());
        maxViewOrder = GeneralTool.isEmpty(maxViewOrder) ? 0 : maxViewOrder;
        eventPlanTheme.setViewOrder(maxViewOrder);
        utilService.setCreateInfo(eventPlanTheme);
        eventPlanThemeMapper.insert(eventPlanTheme);
        return eventPlanTheme.getId();
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        EventPlanTheme eventPlanTheme = eventPlanThemeMapper.selectById(id);
        if (GeneralTool.isEmpty(eventPlanTheme)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        //判断能否删除
        List<EventPlanThemeOnline> onlines = eventPlanThemeOnlineService.list(Wrappers.<EventPlanThemeOnline>lambdaQuery()
                .eq(EventPlanThemeOnline::getFkEventPlanThemeId,id)
                .eq(EventPlanThemeOnline::getIsActive,true));
        List<EventPlanThemeOffline> offlines = eventPlanThemeOfflineService.list(Wrappers.<EventPlanThemeOffline>lambdaQuery()
                .eq(EventPlanThemeOffline::getFkEventPlanThemeId, id)
                .eq(EventPlanThemeOffline::getIsActive, true));
        List<EventPlanThemeWorkshop> workshops = eventPlanThemeWorkshopService.list(Wrappers.<EventPlanThemeWorkshop>lambdaQuery()
                .eq(EventPlanThemeWorkshop::getFkEventPlanThemeId, id)
                .eq(EventPlanThemeWorkshop::getIsActive, true));

        if(GeneralTool.isNotEmpty(onlines) || GeneralTool.isNotEmpty(offlines) || GeneralTool.isNotEmpty(workshops)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("has_associated"));
        }
        int delete = eventPlanThemeMapper.deleteById(id);
        if (delete <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }

    @Override
    public EventPlanThemeVo updateEventPlanTheme(EventPlanThemeDto vo) {
        if (GeneralTool.isEmpty(vo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        EventPlanTheme theme = eventPlanThemeMapper.selectById(vo.getId());
        if (GeneralTool.isEmpty(theme)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        EventPlanTheme eventPlanTheme = BeanCopyUtils.objClone(vo, EventPlanTheme::new);
        utilService.setUpdateInfo(eventPlanTheme);
        int update = eventPlanThemeMapper.updateById(eventPlanTheme);

        if (update <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }

        return findEventPlanThemeById(eventPlanTheme.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void movingOrder( Long fkEventPlanId,Integer start,Integer end) {
        LambdaQueryWrapper<EventPlanTheme> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (end > start){
            lambdaQueryWrapper.between(EventPlanTheme::getViewOrder,start,end).orderByDesc(EventPlanTheme::getViewOrder);
        }else {
            lambdaQueryWrapper.between(EventPlanTheme::getViewOrder,end,start).orderByDesc(EventPlanTheme::getViewOrder);

        }
        lambdaQueryWrapper.eq(EventPlanTheme::getFkEventPlanId,fkEventPlanId);
        List<EventPlanTheme> eventPlanThemes = list(lambdaQueryWrapper);

        //从下上移 列表倒序排序
        List<EventPlanTheme> updateList = Lists.newArrayList();
        if (end > start){
            int finalEnd = end;
            List<EventPlanTheme> sortedList = Lists.newArrayList();
            EventPlanTheme policy = eventPlanThemes.get(eventPlanThemes.size() - 1);
            sortedList.add(policy);
            eventPlanThemes.remove(eventPlanThemes.size() - 1);
            sortedList.addAll(eventPlanThemes);
            for (EventPlanTheme eventPlanTheme : sortedList) {
                eventPlanTheme.setViewOrder(finalEnd);
                finalEnd--;
            }
            updateList.addAll(sortedList);
        }else {
            int finalStart = start;
            List<EventPlanTheme> sortedList = Lists.newArrayList();
            EventPlanTheme policy = eventPlanThemes.get(0);
            eventPlanThemes.remove(0);
            sortedList.addAll(eventPlanThemes);
            sortedList.add(policy);
            for (EventPlanTheme eventPlanTheme : sortedList) {
                eventPlanTheme.setViewOrder(finalStart);
                finalStart--;
            }
            updateList.addAll(sortedList);
        }

        if (GeneralTool.isNotEmpty(updateList)){
            boolean batch = updateBatchById(updateList);
            if (!batch){
                throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
            }
        }
    }
}

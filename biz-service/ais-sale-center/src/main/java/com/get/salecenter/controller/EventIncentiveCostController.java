package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.vo.EventBillVo;
import com.get.salecenter.vo.EventIncentiveCostVo;
import com.get.salecenter.service.IEventIncentiveCostService;
import com.get.salecenter.dto.EventIncentiveCostListDto;
import com.get.salecenter.dto.EventIncentiveCostUpdateDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2022/7/19 16:51
 * @verison: 1.0
 * @description:
 */
@Api(tags = "奖励推广活动费用归口管理")
@RestController
@RequestMapping("sale/eventIncentiveCost")
public class EventIncentiveCostController {


    @Resource
    private IEventIncentiveCostService eventIncentiveCostService;


    /**
     * @return com.get.common.result.ResponseBo
     * @Description :新增信息
     * @Param [eventCostVos]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/奖励推广活动费用归口/新增")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(EventIncentiveCostUpdateDto.Add.class) EventIncentiveCostUpdateDto eventIncentiveCostUpdateDto) {
        return SaveResponseBo.ok(eventIncentiveCostService.addEventIncentiveCost(eventIncentiveCostUpdateDto));
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description :删除信息
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/奖励推广活动费用归口/删除费用归口")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        eventIncentiveCostService.delete(id);
        return DeleteResponseBo.ok();
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.EventCostVo>
     * @Description :修改信息
     * @Param [eventCostVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/奖励推广活动费用归口/更新费用归口")
    @PostMapping("update")
    public ResponseBo<EventIncentiveCostVo> update(@RequestBody @Validated(EventIncentiveCostUpdateDto.Update.class)  EventIncentiveCostUpdateDto eventIncentiveCostUpdateDto) {
        return UpdateResponseBo.ok(eventIncentiveCostService.updateEventIncentiveCost(eventIncentiveCostUpdateDto));
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.EventCostVo>
     * @Description :列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/奖励推广活动费用归口/列表")
    @PostMapping("datas")
    public ResponseBo<EventIncentiveCostVo> datas(@RequestBody SearchBean<EventIncentiveCostListDto> page) {
        List<EventIncentiveCostVo> datas = eventIncentiveCostService.getEventIncentiveCosts(page.getData(),page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas,p);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.EventCostVo>
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/奖励推广活动费用归口/费用归口详情")
    @GetMapping("/{id}")
    public ResponseBo<EventIncentiveCostVo> detail(@PathVariable("id") Long id) {
        EventIncentiveCostVo data = eventIncentiveCostService.findEventIncentiveCostById(id);
        return new ResponseBo<>(data);
    }


    /**
     * 活动费用归口收款单下拉框
     *
     * @Date 12:01 2021/12/3
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "活动费用归口活动费用下拉框", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/奖励推广活动费用归口/活动费用归口活动费用下拉框")
    @GetMapping("/getEventBillSelect")
    public ResponseBo<EventBillVo> getEventBillSelect(@RequestParam("institutionProviderId") Long institutionProviderId,
                                                      @RequestParam("companyId") Long companyId,
                                                      @RequestParam(value = "eventIncentiveCostId", required = false) Long eventIncentiveCostId) {
        return new ListResponseBo<>(eventIncentiveCostService.getEventBillSelect(institutionProviderId, companyId, eventIncentiveCostId));
    }


    /**
     * 根据receiptFormId获取活动费用归口Dtos
     *
     * @param eventIncentiveCostVo
     * @return
     */
    @ApiOperation(value = "费用小计", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/奖励推广活动费用归口/费用小计")
    @PostMapping("getEventIncentiveCostSubtotal")
    public ResponseBo<BigDecimal> getEventIncentiveCostSubtotal(@RequestBody EventIncentiveCostUpdateDto eventIncentiveCostVo) {
        return new ResponseBo<>(eventIncentiveCostService.getEventIncentiveCostSubtotal(eventIncentiveCostVo));
    }
}

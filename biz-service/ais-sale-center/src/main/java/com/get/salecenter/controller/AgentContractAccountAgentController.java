package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.utils.ValidList;
import com.get.salecenter.vo.AgentContractAccountVo;
import com.get.salecenter.service.IAgentContractAgentAccountService;
import com.get.salecenter.dto.AgentContractAgentAccountDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 合同账户管理
 *
 * @Date 16:01 2021/6/28
 * <AUTHOR>
 */
@Api(tags = "合同账户管理")
@RestController
@RequestMapping("sale/agentContractAgentAccount")
public class AgentContractAccountAgentController {

    @Autowired
    private IAgentContractAgentAccountService accountService;

    /**
     * @return com.get.common.result.ResponseBo
     * @Description：列表数据
     * @Param [page]
     * <AUTHOR>
     **/
    @ApiOperation(value = "合同绑定账户列表数据", notes = "keyWord为关键词")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/代理合同账户管理/合同绑定账户列表数据")
    @PostMapping("datas")
    public ResponseBo datas(@RequestBody SearchBean<AgentContractAgentAccountDto> page) {
        List<AgentContractAccountVo> datas = accountService.getAgentContractAgentAccount(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }


    /**
     * 代理合同-账户绑定
     *
     * @Date 16:49 2021/6/28
     * <AUTHOR>
     */
    @ApiOperation(value = "代理合同-账户绑定接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/代理合同账户管理/代理合同-账户绑定")
    @PostMapping("binding/{contractId}")
    public ResponseBo binding(@RequestBody @Validated(AgentContractAgentAccountDto.Add.class)  ValidList<AgentContractAgentAccountDto> agentAccountVos, @PathVariable("contractId") Long contractId) {
        accountService.bindingContractAgentAccount(agentAccountVos, contractId);
        return UpdateResponseBo.ok();
    }

    @ApiOperation(value = "合同绑定账户详情列表数据", notes = "合同id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/代理合同账户管理/合同绑定账户详情数据")
    @GetMapping("/{contractId}")
    public ResponseBo<AgentContractAccountVo> detail(@RequestBody @PathVariable("contractId") Long contractId) {
        List<AgentContractAccountVo> datas = accountService.getAgentContractAgentAccountDetail(contractId);
        return new ListResponseBo<>(datas);
    }


}

package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseVoEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.utils.GeneralTool;
import com.get.filecenter.feign.IFileCenterClient;
import com.get.filecenter.vo.FileVo;
import com.get.salecenter.dto.*;
import com.get.salecenter.dto.AgentContractRenewalDto;
import com.get.salecenter.service.IAppAgentService;
import com.get.salecenter.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2022/11/17 16:10
 * @verison: 1.0
 * @description:
 */
@Api(tags = "代理申请管理")
@RestController
@RequestMapping("sale/appAgent")
public class AppAgentController {

    @Resource
    private IAppAgentService appAgentService;
    @Resource
    private IFileCenterClient fileCenterClient;

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 代理申请续签修改
     * @Param [agentEventVo]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "代理申请续签修改", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/代理申请管理/代理申请续签修改")
    @PostMapping("renewalUpdate")
    public ResponseBo renewalUpdate(
            @RequestBody @Validated(BaseVoEntity.Update.class) AppAgentRenewalUpdateDto appAgentRenewalUpdateDto) {
        appAgentService.renewalUpdate(appAgentRenewalUpdateDto);
        return UpdateResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 代理申请表单新增
     * @Param [agentEventVo]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "代理申请表单新增", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/代理申请管理/代理申请表单新增")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(AppAgentAddDto.Add.class) AppAgentAddDto appAgentAddDto) {
        return new ResponseBo(appAgentService.addAppAgent(appAgentAddDto));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 续约表单新增
     * @Param [agentEventVo]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "续约表单新增", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/代理申请管理/续约表单新增")
    @PostMapping("renewalAdd")
    public ResponseBo renewalAdd(@RequestBody @Validated(AppAgentAddDto.Add.class) AppAgentAddDto appAgentAddDto) {
        appAgentService.renewalAdd(appAgentAddDto);
        return ResponseBo.ok();
    }

    /**
     * 续约审核通过修改
     *
     *
     * @param appAgentAddDto
     * @return
     */
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "续约审核通过修改", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/代理申请管理/续约审核通过修改")
    @PostMapping("renewalAgreeUpdate")
    public ResponseBo renewalAgreeUpdate(
            @RequestBody @Validated(BaseVoEntity.Update.class) AppAgentAddDto appAgentAddDto) {
        this.appAgentService.renewalAgreeUpdate(appAgentAddDto);
        return UpdateResponseBo.ok();
    }

    /**
     * 续约审核拒绝修改并发送邮件
     *
     * @param appAgentApproveCommentDto
     * @return
     */
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "续约审核拒绝修改并发送邮件", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/代理申请管理/续约审核拒绝修改并发送邮件")
    @PostMapping("renewalRejectAndSendEmail")
    public ResponseBo renewalRejectAndSendEmail(
            @RequestBody @Validated(BaseVoEntity.Add.class) AppAgentApproveCommentDto appAgentApproveCommentDto) {
        this.appAgentService.renewalRejectAndSendEmail(appAgentApproveCommentDto);
        return UpdateResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 代理申请表单新增
     * @Param [agentEventVo]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "代理申请表单回显", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/代理申请管理/代理申请表单回显")
    @PostMapping("getAppAgentFormDetail")
    public ResponseBo<AppAgentFormDetailVo> getAppAgentFormDetail(@RequestParam("sign") String sign) {
        return new ResponseBo<>(appAgentService.getAppAgentFormDetail(sign));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 代理申请表单新增
     * @Param [agentEventVo]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "代理申请表单回显", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/代理申请管理/代理申请表单回显")
    @GetMapping("getAppAgentFormDetailById/{id}")
    public ResponseBo<AppAgentFormDetailVo> getAppAgentFormDetailById(@PathVariable Long id) {
        return new ResponseBo<>(appAgentService.getAppAgentFormDetailById(id));
    }

    /**
     * 列表
     *
     * @param page
     * @return
     */
    @ApiOperation(value = "列表", notes = "num代理编号 name代理名称")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/代理申请管理/列表")
    @PostMapping("datas")
    public ResponseBo<AppAgentListVo> datas(@RequestBody SearchBean<AppAgentListDto> page) {
        List<AppAgentListVo> datas = appAgentService.getAppAgents(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/代理申请管理/详情")
    @GetMapping("/{id}")
    public ResponseBo<AppAgentVo> detail(@PathVariable("id") Long id) {
        AppAgentVo data = appAgentService.findAppAgentById(id);
        return new ResponseBo<>(data);
    }

    /**
     * 修改信息
     *
     * @param appAgentUpdateDto
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/代理申请管理/更新代理申请")
    @PostMapping("update")
    public ResponseBo<AppAgentVo> update(
            @RequestBody @Validated(BaseVoEntity.Add.class) AppAgentUpdateDto appAgentUpdateDto) {
        return UpdateResponseBo.ok(appAgentService.updateAppAgent(appAgentUpdateDto));
    }

    /**
     * 联系人列表
     *
     * @param page
     * @return
     */
    @ApiOperation(value = "联系人列表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/代理申请管理/联系人列表")
    @PostMapping("getAppAgentContactPersons")
    public ResponseBo<AppAgentContactPersonListVo> getAppAgentContactPersons(
            @RequestBody SearchBean<AppAgentContactPersonListDto> page) {
        List<AppAgentContactPersonListVo> datas = appAgentService.getAppAgentContactPersons(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 合同账户列表
     *
     * @param page
     * @return
     */
    @ApiOperation(value = "合同账户列表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/代理申请管理/合同账户列表")
    @PostMapping("getAppAgentContractAccounts")
    public ResponseBo<AppAgentContractAccountListVo> getAppAgentContractAccounts(
            @RequestBody SearchBean<AppAgentContractAccountListDto> page) {
        List<AppAgentContractAccountListVo> datas = appAgentService.getAppAgentContractAccounts(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 查询代理附件
     *
     * @param voSearchBean
     * @return
     * @
     */
    @ApiOperation(value = "查询代理附件", notes = "keyWord为关键词")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/代理申请管理/查询代理申请附件")
    @PostMapping("getAgentMedia")
    public ResponseBo<MediaAndAttachedVo> getAppAgentMedia(@RequestBody SearchBean<MediaAndAttachedDto> voSearchBean) {
        List<MediaAndAttachedVo> staffMedia = appAgentService.getAppAgentMedia(voSearchBean.getData(), voSearchBean);
        Page page = BeanCopyUtils.objClone(voSearchBean, Page::new);
        return new ListResponseBo<>(staffMedia, page);
    }

    /**
     * 代理附件保存接口
     *
     * @param mediaAttachedVo
     * @return
     * @
     */
    @VerifyLogin(IsVerify = false)
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "代理附件保存接口")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/代理申请管理/代理申请附件保存接口")
    @PostMapping("upload")
    public ResponseBo<MediaAndAttachedVo> addAgentMedia(
            @RequestBody @Validated(MediaAndAttachedDto.Add.class) ValidList<MediaAndAttachedDto> mediaAttachedVo) {
        return new ListResponseBo<>(appAgentService.addAgentMedia(mediaAttachedVo));
    }

    /**
     * 审核
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "申请审核")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/代理申请管理/审核")
    @GetMapping("review/{id}")
    public ResponseBo review(@PathVariable("id") Long id) {
        appAgentService.updateAppStatus(id, ProjectExtraEnum.APP_STATUS_REVIEW.key);
        return UpdateResponseBo.ok();
    }

    /**
     * 审核拒绝
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "审核拒绝")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/代理申请管理/审核拒绝")
    @GetMapping("reject/{id}")
    public ResponseBo reject(@PathVariable("id") Long id) {
        appAgentService.updateAppStatus(id, ProjectExtraEnum.APP_STATUS_REJECT.key);
        return UpdateResponseBo.ok();
    }

    /**
     * 审核通过
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "审核通过")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/代理申请管理/审核通过")
    @GetMapping("agree/{id}")
    public ResponseBo agree(@PathVariable("id") Long id) {
        appAgentService.updateAppStatus(id, ProjectExtraEnum.APP_STATUS_AGREE.key);
        return UpdateResponseBo.ok();
    }

    /**
     * 审核通过
     *
     * @return
     * @
     */
    @ApiOperation(value = "申请状态下拉框")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/代理申请管理/申请状态下拉框")
    @PostMapping("getAppStatusSelect")
    public ResponseBo getAppStatusSelect() {
        return new ListResponseBo(appAgentService.getAppStatusSelect());
    }

    @ApiOperation("检测是否存在相同代理")
    @GetMapping("validateAgentContactPersonAndAccount")
    @VerifyPermission(IsVerify = false)
    public ResponseBo validateAgentContactPersonAndAccount(@RequestParam("id") Long id) {
        String message = appAgentService.validateAgentContactPersonAndAccount(id);
        ResponseBo responseBo = new ResponseBo();
        if (GeneralTool.isNotEmpty(message)) {
            responseBo.setMessage(message);
        }

        return responseBo;
    }

    /**
     * bd下拉框
     *
     * @return
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "bd下拉框", notes = "")
    @GetMapping("getBdStaffSelect")
    public ResponseBo<BaseSelectEntity> getBdStaffSelect(@RequestParam("companyId") Long companyId) {
        return new ListResponseBo<>(appAgentService.getBdStaffSelect(companyId));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 代理申请表单新增
     * @Param [agentEventVo]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "代理在线申请表单系统参数", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/代理申请管理/代理在线申请表单系统参数")
    @PostMapping("getAppAgentFormConfig")
    public ResponseBo getAppAgentFormConfig() {
        return new ResponseBo(appAgentService.getAppAgentFormConfig());
    }

    @VerifyLogin(IsVerify = false)
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "常用国家下拉", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/代理申请管理/常用国家下拉框")
    @GetMapping("getCommonCountrySelect")
    public ResponseBo<BaseSelectEntity> getCommonCountrySelect() {
        return new ListResponseBo<>(appAgentService.getCommonCountrySelect());
    }


    @VerifyLogin(IsVerify = false)
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "峰会表单-常用国家下拉", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/代理申请管理/峰会表单-常用国家下拉")
    @GetMapping("getCommonCountrySelectBySummit")
    public ResponseBo<BaseSelectEntity> getCommonCountrySelectBySummit() {
        return new ListResponseBo<>(appAgentService.getCommonCountrySelectBySummit());
    }




    @VerifyLogin(IsVerify = false)
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "下载文件接口-私密桶")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "文件中心/下载文件-私密桶")
    @PostMapping("download")
    public void download(HttpServletResponse response, @RequestBody FileVo fileVo) {
        // 设置文件路径
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        appAgentService.getDownloadFilePrivate(response, fileVo);
    }

    @VerifyLogin(IsVerify = false)
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "代理申请在线表单-奖学金用户是否已申请")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "代理申请在线表单-奖学金用户是否已申请")
    @PostMapping("validatedUser")
    public ResponseBo<Boolean> validatedUser(@RequestParam("userId") Long userId) {
        return new ResponseBo<>(appAgentService.validatedUser(userId));
    }

    /**
     * 变更申请数据获取
     */
    @VerifyLogin(IsVerify = false)
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "变更申请数据获取")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "变更申请数据获取")
    @GetMapping("getChangeData/{fkAppAgentId}")
    public ResponseBo<AppAgentChangeDataVo> getChangeData(@PathVariable("fkAppAgentId") Long fkAppAgentId) {
        return new ResponseBo<>(appAgentService.getChangeData(fkAppAgentId));
    }

    /**
     * 变更申请数据获取
     */
    @VerifyLogin(IsVerify = false)
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "变更申请数据修改")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "变更申请数据修改")
    @PostMapping("changeData")
    public ResponseBo<AppAgentChangeDataVo> changeData(
            @RequestBody @Validated(BaseVoEntity.Update.class) AppAgentChangeDataDto appAgentChangeDataDto) {
        appAgentService.changeData(appAgentChangeDataDto);
        return UpdateResponseBo.ok();
    }

    @ApiOperation(value = "生成代理信息变更申请函Pdf")
    @VerifyLogin(IsVerify = false)
    @VerifyPermission(IsVerify = false)
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/代理申请管理/生成代理信息变更申请函Pdf")
    @PostMapping("getLetterForAgentInformationChangePdf")
    public void getLetterForAgentInformationChangePdf(@RequestBody AppAgentDto appAgentDto,
            HttpServletResponse response) {
        appAgentService.getLetterForAgentInformationChangePdf(appAgentDto, response);
    }

    /**
     * 代理续约申请数据组装回显
     *
     * @param agentContractRenewalDto 代理合同续约DTO（包含代理ID和联系人ID）
     * @return 组装好的代理申请数据
     */
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "代理续约申请数据组装回显", notes = "根据AgentContractRenewalDto组装续约申请数据,不保存到数据库")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/代理申请管理/代理续约申请数据组装回显")
    @PostMapping("getOrBuildRenewalApplicationData")
    public ResponseBo<AppAgentFormDetailVo> getOrBuildRenewalApplicationData(
            @RequestBody @Validated AgentContractRenewalDto agentContractRenewalDto) {
        return new ResponseBo<>(appAgentService.getOrBuildRenewalApplicationData(agentContractRenewalDto));
    }


    @ApiOperation(value = "代理申请-根据代理id获取对应的代理申请数据")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/代理申请管理/代理申请-根据代理id获取对应的代理申请数据")
    @GetMapping("getAppAgentFormDetailByAgentId")
    public ResponseBo<List<AppAgentVo>> getAppAgentFormDetailByAgentId(@RequestParam("agentId") Long agentId) {
        return new ResponseBo<>(appAgentService.getAppAgentFormDetailByAgentId(agentId));
    }

}

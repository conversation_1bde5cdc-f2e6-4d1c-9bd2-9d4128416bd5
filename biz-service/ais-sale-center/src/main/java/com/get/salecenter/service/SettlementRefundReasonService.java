package com.get.salecenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.salecenter.entity.SettlementRefundReason;
import com.get.salecenter.dto.SettlementRefundReasonDto;

import java.util.List;

public interface SettlementRefundReasonService extends IService<SettlementRefundReason> {
    /**
     * 退款原因列表
     * @param data
     * @param page
     * @return
     */
    List<SettlementRefundReason> datas(SettlementRefundReasonDto data, SearchBean<SettlementRefundReasonDto> page);

    SaveResponseBo save(SettlementRefundReasonDto settlementRefundReasonDto);

    ResponseBo<SettlementRefundReason> findInfoById(Long id);

    ResponseBo update(SettlementRefundReasonDto settlementRefundReasonDto);

    ResponseBo movingOrder(List<SettlementRefundReasonDto> feeTypeVos);

    ResponseBo delete(Long id);

    List<BaseSelectEntity> getServiceTypeList();

    void batchAdd(ValidList<SettlementRefundReasonDto> settlementRefundReasonDtos);
}

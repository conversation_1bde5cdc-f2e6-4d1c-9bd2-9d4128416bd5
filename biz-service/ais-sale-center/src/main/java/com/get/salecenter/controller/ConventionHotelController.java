package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.vo.ConventionHotelVo;
import com.get.salecenter.entity.ConventionHotel;
import com.get.salecenter.service.IConventionHotelService;
import com.get.salecenter.dto.ConventionHotelDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/7/6 11:25
 * @verison: 1.0
 * @description: 酒店房型管理控制器
 */
@Api(tags = "酒店房型管理")
@RestController
@RequestMapping("sale/conventionHotel")
public class ConventionHotelController {

    @Resource
    private IConventionHotelService conventionHotelService;

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/酒店房型管理/酒店房型详情")
    @GetMapping("/{id}")
    public ResponseBo<ConventionHotelVo> detail(@PathVariable("id") Long id) {
        ConventionHotelVo data = conventionHotelService.findConventionHotelById(id);
        return new ResponseBo<>(data);
    }

    /**
     * 批量新增信息
     *
     * @param conventionHotelDtos
     * @return
     * @
     */
    @ApiOperation(value = "批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/酒店房型管理/新增酒店房型")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody @Validated(ConventionHotelDto.Add.class) ValidList<ConventionHotelDto> conventionHotelDtos) {
        conventionHotelService.batchAdd(conventionHotelDtos);
        return SaveResponseBo.ok();
    }

    /**
     * 删除信息
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/酒店房型管理/删除酒店房型")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        conventionHotelService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param conventionHotelDto
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/酒店房型管理/更新酒店房型")
    @PostMapping("update")
    public ResponseBo<ConventionHotelVo> update(@RequestBody @Validated(ConventionHotelDto.Update.class) ConventionHotelDto conventionHotelDto) {
        return UpdateResponseBo.ok(conventionHotelService.updateConventionHotel(conventionHotelDto));
    }

    /**
     * 列表数据
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "列表数据", notes = "keyWord为搜索关键字(酒店名称,房型),fkConventionId该峰会id(一定有)")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/酒店房型管理/查询酒店房型")
    @PostMapping("datas")
    public ResponseBo<ConventionHotelVo> datas(@RequestBody SearchBean<ConventionHotelDto> page) {
        List<ConventionHotelVo> datas = conventionHotelService.getConventionHotels(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 上移下移
     *
     * @param conventionHotelDtos
     * @return
     * @
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/酒店房型管理/移动顺序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<ConventionHotelDto> conventionHotelDtos) {
        conventionHotelService.movingOrder(conventionHotelDtos);
        return ResponseBo.ok();
    }

    /**
     * 酒店下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "酒店下拉框数据", notes = "id峰会id")
    @GetMapping("getHotelList/{id}")
    public ResponseBo getHotelList(@PathVariable("id") Long id) {
        List<String> datas = conventionHotelService.getHotelList(id);
        return new ListResponseBo<>(datas);
    }

    /**
     * 房型下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "房型下拉框数据", notes = "hotel酒店名称")
    @GetMapping("getRoomTypeList")
    public ResponseBo getRoomTypeList(@RequestParam String hotel) {
        List<String> datas = conventionHotelService.getRoomTypeList(hotel);
        return new ListResponseBo<>(datas);
    }

    /**
     * 酒店房型下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "酒店房型下拉框数据", notes = "id峰会id")
    @GetMapping("getConventionHotelList/{id}")
    public ResponseBo getConventionHotelList(@PathVariable Long id) {
        List<ConventionHotel> datas = conventionHotelService.getConventionHotelList(id);
        return new ListResponseBo<>(datas);
    }

}

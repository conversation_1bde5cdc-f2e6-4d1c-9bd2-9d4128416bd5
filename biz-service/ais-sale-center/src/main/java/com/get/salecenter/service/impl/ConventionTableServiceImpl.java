package com.get.salecenter.service.impl;

import cn.hutool.poi.excel.BigExcelWriter;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.file.utils.FileUtils;
import com.get.salecenter.dao.sale.ConventionTableMapper;
import com.get.salecenter.vo.*;
import com.get.salecenter.vo.ConventionTableRegistrationVo;
import com.get.salecenter.entity.ConventionPerson;
import com.get.salecenter.entity.ConventionTable;
import com.get.salecenter.entity.ConventionTablePerson;
import com.get.salecenter.service.IConventionPersonService;
import com.get.salecenter.service.IConventionProcedureService;
import com.get.salecenter.service.IConventionRegistrationService;
import com.get.salecenter.service.IConventionTablePersonService;
import com.get.salecenter.service.IConventionTableRegistrationService;
import com.get.salecenter.service.IConventionTableService;
import com.get.salecenter.service.IDeleteService;
import com.get.salecenter.utils.MyStringUtils;
import com.get.salecenter.dto.ConventionPersonDto;
import com.get.salecenter.dto.ConventionTableDto;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;


/**
 * @author: Sea
 * @create: 2020/8/27 11:33
 * @verison: 1.0
 * @description:
 */
@Service
public class ConventionTableServiceImpl implements IConventionTableService {
    @Resource
    private ConventionTableMapper conventionTableMapper;
    @Resource
    private IConventionTableRegistrationService conventionTableRegistrationService;
    @Resource
    private IConventionTablePersonService conventionTablePersonService;
    @Resource
    private IConventionProcedureService conventionProcedureService;
    @Resource
    private UtilService utilService;
    @Resource
    private IConventionPersonService personService;
    @Resource
    private IConventionRegistrationService registrationService;
    @Resource
    private IDeleteService deleteService;

    @Override
    public ConventionTableVo findConventionTableById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ConventionTable conventionTable = conventionTableMapper.selectById(id);
        return BeanCopyUtils.objClone(conventionTable, ConventionTableVo::new);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(ConventionTableDto conventionTableDto) {
        if (GeneralTool.isEmpty(conventionTableDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        //最大桌台编号
        String maxTableNum;
        //获取该桌台类型最大桌台编号(带前序号)和排序
        String maxTableNumString;
        Integer viewOrder;
        ConventionTable result = conventionTableMapper.getMaxTableNum(conventionTableDto.getFkConventionId(), conventionTableDto.getFkTableTypeKey(), conventionTableDto.getPreNum());
        if (GeneralTool.isEmpty(result)) {
            maxTableNumString = null;
            viewOrder = 0;
        } else {
            maxTableNumString = result.getTableNum();
            viewOrder = result.getViewOrder();
        }
        //批量添加桌台 桌台编号自动生成
        for (int i = 1; i <= conventionTableDto.getTableCount(); i++) {
            if (GeneralTool.isEmpty(maxTableNumString) && 0 == viewOrder) {
                maxTableNum = "0";
            } else {
                //根据前序号长度截取
                int length = conventionTableDto.getPreNum().length();
                maxTableNum = maxTableNumString.substring(length);
            }
            //获取桌台编号
            String tableNum = MyStringUtils.getmaxTableNum(conventionTableDto.getPreNum(), new Long(maxTableNum) + i);
            conventionTableDto.setTableNum(tableNum);
            conventionTableDto.setViewOrder(viewOrder + i);
            ConventionTable conventionTable = BeanCopyUtils.objClone(conventionTableDto, ConventionTable::new);
            utilService.updateUserInfoToEntity(conventionTable);
            conventionTableMapper.insertSelective(conventionTable);
        }
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (findConventionTableById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        //判断该桌台能否删除
        deleteService.deleteValidateTable(id);
        conventionTableMapper.deleteById(id);
    }

    @Override
    public ConventionTableVo updateConventionTable(ConventionTableDto conventionTableDto) {
        return null;
    }

    @Override
    public List<ConventionTableVo> getConventionTables(ConventionTableDto conventionTableDto) {
        LambdaQueryWrapper<ConventionTable> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ConventionTable::getFkConventionId, conventionTableDto.getFkConventionId());

        if (GeneralTool.isNotEmpty(conventionTableDto)) {
            //桌台类型条件
            if (GeneralTool.isNotEmpty(conventionTableDto.getFkTableTypeKey())) {
                lambdaQueryWrapper.eq(ConventionTable::getFkTableTypeKey, conventionTableDto.getFkTableTypeKey());
            }
            //桌台编号条件
            if (GeneralTool.isNotEmpty(conventionTableDto.getTableNum())) {
                lambdaQueryWrapper.like(ConventionTable::getTableNum, conventionTableDto.getTableNum());
            }
        }
        lambdaQueryWrapper.orderByAsc(ConventionTable::getPreNum, ConventionTable::getViewOrder);
        List<ConventionTable> conventionTables = conventionTableMapper.selectList(lambdaQueryWrapper);
        List<ConventionTableVo> convertDatas = new ArrayList<>();
        for (ConventionTable conventionTable : conventionTables) {
            ConventionTableVo conventionTableVo = BeanCopyUtils.objClone(conventionTable, ConventionTableVo::new);
            //根据桌台类型查询中间表
            if (ProjectKeyEnum.CONVENTION_TRAINING_TABLE.key.equals(conventionTable.getFkTableTypeKey())) {
                //通过桌台id 查询 培训桌-峰会报名 中间表对应报名信息
                ConventionTableRegistrationVo conventionTableRegistrationVoList = conventionTableRegistrationService.getRegistrationByTableId(conventionTable.getId());
                conventionTableVo.setConventionTableRegistrationDto(conventionTableRegistrationVoList);
                //通过桌台id 查询 培训桌-参会人员 中间表对应参会人员信息
                List<ConventionTablePersonVo> conventionTablePersonVoList = conventionTablePersonService.getPersonByTableId(conventionTable.getId());
                conventionTableVo.setConventionTablePersonDtoList(conventionTablePersonVoList);
                //剩余座位数
                int remainingSeatCount = conventionTable.getSeatCount() - conventionTablePersonVoList.size();
                conventionTableVo.setRemainingSeatCount(remainingSeatCount);

            } else if (ProjectKeyEnum.CONVENTION_DINNER_TABLE.key.equals(conventionTable.getFkTableTypeKey())) {
                //通过桌台id 查询 晚宴桌-参会人员 中间表对应参会人员信息
                List<ConventionTablePersonVo> conventionTablePersonVoList = conventionTablePersonService.getPersonByTableId(conventionTable.getId());
                conventionTableVo.setConventionTablePersonDtoList(conventionTablePersonVoList);
                //剩余座位数
                int remainingSeatCount = conventionTable.getSeatCount() - conventionTablePersonVoList.size();
                conventionTableVo.setRemainingSeatCount(remainingSeatCount);
            } else {
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
            }
            addConvertDatasByIsFull(conventionTableDto, convertDatas, conventionTableVo);
        }
        //根据姓名条件过滤
        if (GeneralTool.isNotEmpty(conventionTableDto.getPersonName())||GeneralTool.isNotEmpty(conventionTableDto.getSearchRepeatPerson())) {
            filterConvertDatasByPersonName(conventionTableDto, convertDatas);
        }
        //根据展位名称过滤
        if (GeneralTool.isNotEmpty(conventionTableDto.getBoothName())) {
            filterConvertDatasByBoothName(conventionTableDto, convertDatas);
        }
        return convertDatas;
    }

    @Override
    public void exportConventionTableExcel(HttpServletResponse response , ConventionTableDto conventionTableDto){
        List<ConventionTableVo> list = getConventionTables(conventionTableDto);
        List<String> tipsList = new ArrayList<>(Arrays.asList("角色对应匹配颜色",null,"校代",null,null,"代理",null,null,"嘉宾"));
        List<String> titleList = new ArrayList<>();
        if(ProjectKeyEnum.CONVENTION_DINNER_TABLE.key.equals(conventionTableDto.getFkTableTypeKey())){
            titleList = new ArrayList<>(Arrays.asList("桌号","座位数","剩余座位"));
        }else if(ProjectKeyEnum.CONVENTION_TRAINING_TABLE.key.equals(conventionTableDto.getFkTableTypeKey())){
            titleList = new ArrayList<>(Arrays.asList("桌号","座位数","剩余座位","展位","国家"));
        }
        //最大列数
        Integer size = list.stream().max(Comparator.comparing(ConventionTableVo::getSeatCount)).get().getSeatCount();
        Integer maxRow = size + titleList.size();
        BigExcelWriter writer = FileUtils.setExcelStyleWrapText("ConventionTableExcel", maxRow);

        List<List<String>> contentRowList = new ArrayList<>();
        //数据内容
        for(ConventionTableVo dto : list){
            List<String> contentList = new ArrayList<>();
            contentList.add(dto.getTableNum());
            contentList.add(String.valueOf(dto.getSeatCount()));
            contentList.add(String.valueOf(dto.getRemainingSeatCount()));
            if(ProjectKeyEnum.CONVENTION_TRAINING_TABLE.key.equals(conventionTableDto.getFkTableTypeKey())){
                //展会名称
                String boothName = "";
                if(GeneralTool.isNotEmpty(dto.getConventionTableRegistrationDto())){
                    if(GeneralTool.isNotEmpty(dto.getConventionTableRegistrationDto().getConventionRegistrationDto())){
                        boothName = dto.getConventionTableRegistrationDto().getConventionRegistrationDto().getBoothName();
                    }
                }
                contentList.add(boothName);
                //国家名称
                List<String> countryList = new ArrayList<>();
                if(GeneralTool.isNotEmpty(dto.getConventionTableRegistrationDto())){
                    if(GeneralTool.isNotEmpty(dto.getConventionTableRegistrationDto().getConventionRegistrationDto())){
                        countryList = dto.getConventionTableRegistrationDto().getConventionRegistrationDto().getCountry();
                    }
                }

                StringBuffer stringBuffer = new StringBuffer();
                String countryStr = "";
                if(GeneralTool.isNotEmpty(countryList)){
                    for(int i = 0 ; i < countryList.size() ; i++){
                        stringBuffer.append("[").append(countryList.get(i)).append("]");
                    }
                    countryStr = stringBuffer.toString();
                }
                contentList.add(countryStr);
            }

            for(ConventionTablePersonVo personDto: dto.getConventionTablePersonDtoList()){
                StringBuffer stringBuffer = new StringBuffer();
                if(GeneralTool.isNotEmpty(personDto.getConventionPersonDto().getName())){
                    stringBuffer.append(personDto.getConventionPersonDto().getName());
                }
                if(GeneralTool.isNotEmpty(personDto.getConventionPersonDto().getNameChn())){
                    stringBuffer.append("（").append(personDto.getConventionPersonDto().getNameChn()).append("）");
                }

                contentList.add(stringBuffer.toString());
            }
            for(int i = 0 ; i < dto.getRemainingSeatCount() ; i++){
                contentList.add("未分配");
            }
            contentRowList.add(contentList);
        }
        List<List<String>> rows = new ArrayList<>();
        rows.add(tipsList);
        rows.add(titleList);
        rows.addAll(contentRowList);
        writer.write(rows,true);
        //i为列
        for(int i = 0 ; i < maxRow ; i++ ){
            //列宽
            if(i >= titleList.size()){
                writer.setColumnWidth(i, 30);
            }

            //j为行
            for(int j = 0;j < 2;j++){
                //标题字体
                setTitleStyle(writer,i,j);
                setTitleStyle(writer,i,j);
                //填充白色背景色
                CellStyle cellStyle = writer.createCellStyle(i, j);
                cellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
                setBorder(cellStyle);
            }
        }

        setCellStyle(writer,ProjectExtraEnum.INSTITUTION_AGENT.key,3,0);
        setCellStyle(writer,ProjectExtraEnum.AGENT.key,6,0);
        setCellStyle(writer,ProjectExtraEnum.GUEST.key,9,0);
        //标题行高
        writer.setRowHeight(1,23);
        //内容行高
        for(int i = 2 ;i < rows.size();i++){
            writer.setRowHeight(i,30);
        }
        //对应角色匹配颜色
        for(int i = 0 ; i < list.size() ; i++){
            int x = titleList.size();
            int y = i+2;
            for(ConventionTablePersonVo person : list.get(i).getConventionTablePersonDtoList()){
                setCellStyle(writer,person.getConventionPersonDto().getType(),x,y);
                x++;
            }
        }
        FileUtils.doExportExcel(response, writer, "ConventionTableExcel");
    }

    /**
     * 标题样式
     * @param writer
     * @param x
     * @param y
     */
    public void setTitleStyle(BigExcelWriter writer, int x, int y){
        Workbook workbook = writer.getWorkbook();
        CellStyle cellStyle = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        cellStyle .setFont(font);
        // 字体垂直居中
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        //字体水平居中
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        //自动换行
        cellStyle.setWrapText(true);
        setBorder(cellStyle);
        writer.setStyle(cellStyle,x,y);
    }

    /**
     * 单元格背景色
     * @param writer
     * @param type
     * @param x
     * @param y
     */
    public void setCellStyle(BigExcelWriter writer, Integer type, int x, int y) {
        CellStyle cellStyle = writer.createCellStyle(x, y);
        if(ProjectExtraEnum.INSTITUTION_AGENT.key.equals(type)){
            cellStyle.setFillForegroundColor(IndexedColors.SKY_BLUE.getIndex());
        } else if(ProjectExtraEnum.AGENT.key.equals(type)){
            cellStyle.setFillForegroundColor(IndexedColors.ORANGE.getIndex());
        }else if(ProjectExtraEnum.GUEST.key.equals(type)){
            cellStyle.setFillForegroundColor(IndexedColors.PINK.getIndex());
        }else{
            cellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        }
        setBorder(cellStyle);
        // 字体垂直居中
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        //字体水平居中
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        //自动换行
        cellStyle.setWrapText(true);
    }

    /**
     * 设置单元格背景色，需重设边框，否则出现边框丢失情况
     * @param cellStyle
     */
    public void setBorder(CellStyle cellStyle){
        //边框设置
        // 顶边栏
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
        // 右边栏
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
        // 底边栏
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        // 左边栏
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        //设置纹理，设置背景色必须设置
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
    }

    @Override
    public Integer setVip(Long id, Integer isVip) {
        ConventionTable table = new ConventionTable();
        table.setId(id);
        table.setIsVip(isVip);
        int i = conventionTableMapper.updateById(table);
        if (i < 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
        return isVip;
    }

    @Override
    public void seatCountUp(Long id, Integer seatCount) {
        ConventionTable conventionTable = new ConventionTable();
        conventionTable.setId(id);
        conventionTable.setSeatCount(seatCount + 1);
        int i = conventionTableMapper.updateById(conventionTable);
        if (i < 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
    }

    @Override
    public void seatCountDown(Long id, Integer seatCount) {
        //通过桌台id 查询该桌台对应的数据list
        List<ConventionTablePerson> conventionTablePersonList = conventionTablePersonService.getTablePersonListById(id);
        if (seatCount <= conventionTablePersonList.size()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("table_countDown"));
        }
        ConventionTable conventionTable = new ConventionTable();
        conventionTable.setId(id);
        conventionTable.setSeatCount(seatCount - 1);
        int i = conventionTableMapper.updateById(conventionTable);
        if (i < 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
    }

    @Override
    public List<TableChartVo> getTableChartList(Long conventionId, String tableType) {
        return conventionTableMapper.getTableChartList(conventionId, tableType);
    }

    @Override
    public List<String> getTableNums(Long conventionId, String tableType) {
        List<String> tableNums = conventionTableMapper.getTableNums(conventionId, tableType);
        //培训桌需要查出桌台编号和安排的展位名称   后台拼接
        if (ProjectKeyEnum.CONVENTION_TRAINING_TABLE.key.equals(tableType)) {
            List<String> result = new ArrayList<>();
            for (String tableNum : tableNums) {
                //培训桌根据桌台编号查找该桌台安排的展位名称
                String bootName = conventionTableMapper.getBootName(tableNum, conventionId, tableType);
                if (GeneralTool.isNotEmpty(bootName)) {
                    tableNum = tableNum + "(" + bootName + ")";
                }
                result.add(tableNum);
            }
            return result;
        }
        return tableNums;
    }

    @Override
    public List<ConventionPersonVo> getNotArrangedPersonList(ConventionPersonDto conventionPersonDto, Page page, String type) {
        //获取已安排的人员
        List<ConventionPerson> personList = personService.getPersonForTable(conventionPersonDto.getFkConventionId(), type);
        List<Long> personIds = personList.stream().map(ConventionPerson::getId).collect(Collectors.toList());
        if (GeneralTool.isEmpty(personIds)) {
            personIds.add(0L);
        }
        //峰会流程id
        List<Long> procedureIds = conventionProcedureService.getProcedureIdsByTable(type, conventionPersonDto.getFkConventionId());
        conventionPersonDto.setFkConventionProcedureIds(procedureIds);
        //筛选出未被安排的人员信息
        IPage<ConventionPersonVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<ConventionPersonVo> conventionPersonList = personService.getNotArrangedPersonList(iPage, personIds, conventionPersonDto);
        page.setAll((int) iPage.getTotal());
        return conventionPersonList;

    }

    /**
     * 获取桌子角色小计
     *
     * @param
     * @param
     * @return
     */
    @Override
    public List<TableCharacterSubtotal> getTableCharacterSubtotal(ConventionTableDto conventionTableVo) {
        return conventionTableMapper.getTableCharacterSubtotal(conventionTableVo);
    }

    private void addConvertDatasByIsFull(ConventionTableDto conventionTableDto, List<ConventionTableVo> convertDatas, ConventionTableVo conventionTableVo) {
        int remainingSeatCount = conventionTableVo.getRemainingSeatCount();
        //已满未满都看
        if (GeneralTool.isEmpty(conventionTableDto.getIsFull())) {

            convertDatas.add(conventionTableVo);
            return;
        }
        //只看未满(剩余座位数不为0)
        if (conventionTableDto.getIsFull() == 0) {
            if (remainingSeatCount != 0) {
                convertDatas.add(conventionTableVo);
            }
        }
        //只看已满(剩余座位数为0)
        if (conventionTableDto.getIsFull() == 1) {
            if (remainingSeatCount == 0) {
                convertDatas.add(conventionTableVo);
            }
        }
    }

    /**
     * @return void
     * @Description :根据人员姓名条件过滤
     * @Param [conventionTableDto, convertDatas]
     * <AUTHOR>
     */
    private void filterConvertDatasByPersonName(ConventionTableDto conventionTableDto, List<ConventionTableVo> convertDatas) {
        if (GeneralTool.isNotEmpty(conventionTableDto.getPersonName())){
            /**
             * 旧代码只能在此基础上拓展 。。
             */
            //先查找符合条件的参会人员id
            String personName = "%" + conventionTableDto.getPersonName() + "%";
            List<Long> personIds = personService.getPersonIdsByName(personName);
            Iterator<ConventionTableVo> iterator = convertDatas.iterator();
            while (iterator.hasNext()) {
                ConventionTableVo conventionTableVo = iterator.next();
                List<ConventionTablePersonVo> conventionTablePersonVoList = conventionTableVo.getConventionTablePersonDtoList();
                //获取集合中所有的fkPersonId
                if (GeneralTool.isEmpty(conventionTablePersonVoList)) {
                    iterator.remove();
                    continue;
                }
                // TODO 改过
               // List<Long> ids = conventionTablePersonVoList.stream().map(ConventionTablePerson::getFkConventionPersonId).collect(Collectors.toList());
                List<Long> ids = conventionTablePersonVoList.stream().map(ConventionTablePersonVo::getFkConventionPersonId).collect(Collectors.toList());
                //当该桌台中的参加人员id不属于查询条件的id中时，移除这一条（判断两个集合中是否有相同元素，有返回false）
                if (Collections.disjoint(personIds, ids)) {
                    iterator.remove();
                }
            }
        }

        if (GeneralTool.isNotEmpty(conventionTableDto.getSearchRepeatPerson())&&ProjectKeyEnum.CONVENTION_TRAINING_TABLE.key.equals(conventionTableDto.getFkTableTypeKey())){
            List<Long> personIds = new ArrayList<>();
            personIds = personService.getRepeatPerson(conventionTableDto.getSearchRepeatPerson(), conventionTableDto.getFkConventionId(), conventionTableDto.getFkTableTypeKey());
            if (GeneralTool.isEmpty(personIds)){
                personIds.add(0L);
            }
            Iterator<ConventionTableVo> iterator = convertDatas.iterator();
            while (iterator.hasNext()) {
                ConventionTableVo conventionTableVo = iterator.next();
                List<ConventionTablePersonVo> conventionTablePersonVoList = conventionTableVo.getConventionTablePersonDtoList();
                //获取集合中所有的fkPersonId
                if (GeneralTool.isEmpty(conventionTablePersonVoList)) {
                    iterator.remove();
                    continue;
                }
                //TODO 改过
               // List<Long> ids = conventionTablePersonVoList.stream().map(ConventionTablePerson::getFkConventionPersonId).collect(Collectors.toList());
                List<Long> ids = conventionTablePersonVoList.stream().map(ConventionTablePersonVo::getFkConventionPersonId).collect(Collectors.toList());
                //当该桌台中的参加人员id不属于查询条件的id中时，移除这一条（判断两个集合中是否有相同元素，有返回false）
                if (Collections.disjoint(personIds, ids)) {
                    iterator.remove();
                }
            }
        }

    }

    /**
     * @return void
     * @Description :根据展位名称条件过滤
     * @Param [conventionTableDto, convertDatas]
     * <AUTHOR>
     */
    private void filterConvertDatasByBoothName(ConventionTableDto conventionTableDto, List<ConventionTableVo> convertDatas) {
        String boothName = "%" + conventionTableDto.getBoothName() + "%";
        //先查找符合条件的峰会报名ids
        List<Long> registrationIds = registrationService.getRegistrationIdsByName(boothName);
        Iterator<ConventionTableVo> iterator = convertDatas.iterator();
        while (iterator.hasNext()) {
            ConventionTableVo conventionTableVo = iterator.next();
            ConventionTableRegistrationVo conventionTableRegistrationVo = conventionTableVo.getConventionTableRegistrationDto();
            //获取集合中所有的fkPersonId
            if (GeneralTool.isEmpty(conventionTableRegistrationVo)) {
                iterator.remove();
                continue;
            }
            Long id = conventionTableRegistrationVo.getFkConventionRegistrationId();
            //当该桌台中的参加人员id不属于查询条件的id中时，移除这一条（判断两个集合中是否有相同元素，有返回false）
            if (!registrationIds.contains(id)) {
                iterator.remove();
            }
        }
    }
}

package com.get.salecenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.permissioncenter.vo.DepartmentAndStaffVo;
import com.get.salecenter.dto.*;
import com.get.salecenter.entity.EventBill;
import com.get.salecenter.vo.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * @author: Hardy
 * @create: 2022/5/9 11:42
 * @verison: 1.0
 * @description:
 */
public interface IEventBillService extends IService<EventBill> {

    /**
     * 发起收款计划（包含一些关系绑定）
     *
     * @param eventBillVo
     * @return
     */
    Long addEventBill(EventBillUpdateDto eventBillVo);

    /**
     * 详情接口
     *
     * @param id
     * @return
     */
    EventBillVo findEventBillById(Long id);


    /**
     * 查询活动汇总费用附件
     *
     * @param data
     * @param page
     * @return
     */
    List<MediaAndAttachedVo> getItemMedia(MediaAndAttachedDto data, Page page);

    /**
     * 保存活动汇总费用附件
     *
     * @param mediaAttachedVo
     * @return
     */
    List<MediaAndAttachedVo> addItemMedia(ValidList<MediaAndAttachedDto> mediaAttachedVo);

    /**
     *
     * @param eventBillListDto
     * @param page
     * @return
     */
    List<EventBillListVo> getEventBills(EventBillListDto eventBillListDto, Page page);

    /**
     *
     *
     * @param eventBillUpdateDto
     * @return
     */
    EventBillVo updateEventBill(EventBillUpdateDto eventBillUpdateDto);

    /**
     * 作废收款计划
     *
     * @param id
     */
    void updateInvalidStatus(Long id);

    /**
     * 编辑批量
     *
     * @param commentDto
     * @return
     */
    Long editComment(CommentDto commentDto);

    /**
     * 评论列表
     *
     * @param commentDto
     * @param page
     * @return
     */
    List<CommentVo> getComments(CommentDto commentDto, Page page);

    /**
     * 搜索的摘要下拉
     *
     * @param fkCompanyId
     * @return
     */
    List<BaseSelectEntity> getSummarySelect(Long fkCompanyId);
    /**
     * 搜索的摘要下拉
     *
     * @param fkCompanyIdList
     * @return
     */
    List<BaseSelectEntity> getSummarySelectList(List<Long> fkCompanyIdList);

    /**
     * 创建财务单据
     *
     * @param eventBillVo
     */
    void generateBill(EventBillUpdateDto eventBillVo);

    /**
     * 默认通知人列表
     *
     * @return
     */
    List<DepartmentAndStaffVo> getDefaultStafffNotices(Long fkCompanyId);

    /**
     * 财务一键作废
     *
     * @param id
     */
    void updateInvalidStatusFinance(Long id);

    /**
     * 分配状态下拉
     *
     * @return
     */
    List<Map<String, Object>> getAllocationStatusSelect();

    List<EventBillVo> getEventBillByProviderIdAndEventIncentiveCostId(Long institutionProviderId, Long companyId, Long eventIncentiveCostId);

    /**
     * 作废财务单据
     *
     * @param id
     */
    void updateInvalidStatusFinancialDocuments(Long id);

    /**
     * 发送到账提醒邮件
     *
     */
    ResponseBo sendAccountEmail(EventBillAccountNoticeDto eventBillAccountNoticeDto);

    /**
     * 重新发起（修改活动费用）
     * @param eventBillUpdateAmountDto
     */
    ResponseBo updateAmount(EventBillUpdateAmountDto eventBillUpdateAmountDto);

    /**
     * 统计小计
     * @param eventBillListDto
     * @return
     */
    List<EventBillSubtotalVo> getEventBillSubtotals(EventBillListDto eventBillListDto);

    List<ReceivablePlanNewVo> getEventBillReceivablePlanList(String invoiceNum);

    void exportEventBillList(EventBillListDto eventBillListDto, Page page, HttpServletResponse response);
}

package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.ConventionPersonRegistrationMapper;
import com.get.salecenter.vo.ConventionPersonVo;
import com.get.salecenter.entity.ConventionPersonRegistration;
import com.get.salecenter.service.IConventionPersonRegistrationService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: Hardy
 * @create: 2022/7/14 10:43
 * @verison: 1.0
 * @description:
 */
@Service
public class ConventionPersonRegistrationServiceImpl extends ServiceImpl<ConventionPersonRegistrationMapper, ConventionPersonRegistration> implements IConventionPersonRegistrationService {

    @Resource
    private ConventionPersonRegistrationMapper conventionPersonRegistrationMapper;

    @Override
    public List<ConventionPersonRegistration> getConventionPersonRegistrationByCondition(LambdaQueryWrapper<ConventionPersonRegistration> lambdaQueryWrapper) {
        return conventionPersonRegistrationMapper.selectList(lambdaQueryWrapper);
    }

    @Override
    public Map<Long, String> getConventionPersonProvideNameMapByIds(List<Long> institutionAgentIds) {
        Map<Long, String> map = new HashMap<>();
        List<ConventionPersonVo> conventionPersonVos = conventionPersonRegistrationMapper.getConventionPersonProvideNameMapByIds(institutionAgentIds);
        if (GeneralTool.isEmpty(conventionPersonVos)){
            return null;
        }
        for (ConventionPersonVo conventionPersonVo : conventionPersonVos) {
            map.put(conventionPersonVo.getId(), conventionPersonVo.getCompany());
        }
        return map;
    }
}

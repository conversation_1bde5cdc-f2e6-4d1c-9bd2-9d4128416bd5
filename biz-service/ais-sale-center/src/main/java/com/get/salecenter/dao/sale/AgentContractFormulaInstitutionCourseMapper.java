package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.entity.AgentContractFormulaInstitutionCourse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AgentContractFormulaInstitutionCourseMapper extends BaseMapper<AgentContractFormulaInstitutionCourse> {

    /**
     * @return int
     * @Description :新增
     * @Param [record]
     * <AUTHOR>
     */
    int insertSelective(AgentContractFormulaInstitutionCourse record);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :查找agentContractFormulaId对应课程ids
     * @Param [agentContractFormulaId]
     * <AUTHOR>
     */
    List<Long> getInstitutionCourseIdsByFkid(@Param("agentContractFormulaId") Long agentContractFormulaId);
}
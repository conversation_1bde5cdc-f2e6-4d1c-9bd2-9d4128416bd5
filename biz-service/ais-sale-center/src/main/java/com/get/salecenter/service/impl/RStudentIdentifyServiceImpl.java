package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.salecenter.dao.sale.RStudentIdentifyMapper;
import com.get.salecenter.entity.RStudentIdentify;
import com.get.salecenter.service.IRStudentIdentifyService;
import org.springframework.stereotype.Service;

/**
 学生识别(r_student_identify)
 */
@Service("saleCenterRStudentIdentifyService")
public class RStudentIdentifyServiceImpl extends ServiceImpl<RStudentIdentifyMapper, RStudentIdentify> implements IRStudentIdentifyService {


}


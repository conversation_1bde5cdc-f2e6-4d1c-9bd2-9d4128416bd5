package com.get.salecenter.service.impl;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.crypto.digest.MD5;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.aisplatformcenterap.feign.IPlatformCenterClient;
import com.get.common.consts.AESConstant;
import com.get.common.consts.CacheKeyConstants;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.utils.AESUtils;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.DataConverter;
import com.get.common.utils.GetDateUtil;
import com.get.common.utils.HttpUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.service.impl.GetServiceImpl;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.redis.cache.GetRedis;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.api.ResultCode;
import com.get.core.tool.utils.BeanUtil;
import com.get.core.tool.utils.CollectionUtil;
import com.get.core.tool.utils.DateUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.file.utils.FileUtils;
import com.get.financecenter.dto.AgentSettlementBatchExportDto;
import com.get.financecenter.dto.BatchDownloadAgentReconciliationDto;
import com.get.financecenter.dto.query.AgentSettlementQueryDto;
import com.get.financecenter.feign.IFinanceCenterClient;
import com.get.financecenter.vo.AgentSettlementGrossAmountVo;
import com.get.institutioncenter.dto.NewEmailGetAgentDto;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.dto.CompanyConfigInfoDto;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.ConfigVo;
import com.get.permissioncenter.vo.StaffVo;
import com.get.platformconfigcenter.vo.UserAgentVo;
import com.get.pmpcenter.dto.agent.AgentCommissionTypeAgentDto;
import com.get.registrationcenter.feign.IRegistrationCenterClient;
import com.get.remindercenter.enums.EmailTemplateEnum;
import com.get.remindercenter.feign.IReminderCenterClient;
import com.get.salecenter.dao.oldissue.AgencyCompanyHasAgencyUsersMapper;
import com.get.salecenter.dao.sale.AgentCompanyMapper;
import com.get.salecenter.dao.sale.AgentContractMapper;
import com.get.salecenter.dao.sale.AgentMapper;
import com.get.salecenter.dao.sale.AgentStaffMapper;
import com.get.salecenter.dao.sale.KpiInstitutionProviderMapper;
import com.get.salecenter.dao.sale.MediaAndAttachedMapper;
import com.get.salecenter.dao.sale.PayablePlanMapper;
import com.get.salecenter.dao.sale.RAgentUuidMapper;
import com.get.salecenter.dao.sale.StaffBdCodeMapper;
import com.get.salecenter.dao.sale.StudentOfferItemMapper;
import com.get.salecenter.dao.sale.StudentOfferItemSettlementBatchExchangeMapper;
import com.get.salecenter.dto.AgentAddDto;
import com.get.salecenter.dto.AgentCompanyDto;
import com.get.salecenter.dto.AgentContactPersonDto;
import com.get.salecenter.dto.AgentContractDto;
import com.get.salecenter.dto.AgentContractRenewalDto;
import com.get.salecenter.dto.AgentDto;
import com.get.salecenter.dto.AgentIdCardDto;
import com.get.salecenter.dto.AgentRoleStaffAddDto;
import com.get.salecenter.dto.AgentSourceDto;
import com.get.salecenter.dto.AgentUpdateDto;
import com.get.salecenter.dto.BusinessLicenseResultDto;
import com.get.salecenter.dto.CommissionSummaryDto;
import com.get.salecenter.dto.CommissionSummarySecondaryScreeningDto;
import com.get.salecenter.dto.ContactPersonDto;
import com.get.salecenter.dto.EmailSendContext;
import com.get.salecenter.dto.MediaAndAttachedDto;
import com.get.salecenter.dto.query.AgentContractQueryDto;
import com.get.salecenter.dto.query.AgentQueryDto;
import com.get.salecenter.entity.Agent;
import com.get.salecenter.entity.AgentCompany;
import com.get.salecenter.entity.AgentContract;
import com.get.salecenter.entity.AgentContractAccount;
import com.get.salecenter.entity.AgentRoleStaff;
import com.get.salecenter.entity.AgentStaff;
import com.get.salecenter.entity.AppAgent;
import com.get.salecenter.entity.AppAgentContactPerson;
import com.get.salecenter.entity.AppAgentContractAccount;
import com.get.salecenter.entity.KpiInstitutionProvider;
import com.get.salecenter.entity.RAgentUuid;
import com.get.salecenter.entity.SaleContactPerson;
import com.get.salecenter.entity.SaleMediaAndAttached;
import com.get.salecenter.entity.StaffBdCode;
import com.get.salecenter.entity.StudentOfferItem;
import com.get.salecenter.entity.StudentProjectRole;
import com.get.salecenter.enums.AgentAppFromEnum;
import com.get.salecenter.enums.MiniProgramPageEnum;
import com.get.salecenter.enums.AgentAppTypeEnum;
import com.get.salecenter.enums.AgentContractApprovalStatusEnum;
import com.get.salecenter.enums.ContactPersonTypeEnum;
import com.get.salecenter.service.AgentRoleStaffService;
import com.get.salecenter.service.IAgentCompanyService;
import com.get.salecenter.service.IAgentContractAccountService;
import com.get.salecenter.service.IAgentContractService;
import com.get.salecenter.service.IAgentService;
import com.get.salecenter.service.IAgentStaffService;
import com.get.salecenter.service.IAppAgentContactPersonService;
import com.get.salecenter.service.IAppAgentContractAccountService;
import com.get.salecenter.service.IAppAgentService;
import com.get.salecenter.service.ICompanyRelationService;
import com.get.salecenter.service.IContactPersonService;
import com.get.salecenter.service.IDeleteService;
import com.get.salecenter.service.IMediaAndAttachedService;
import com.get.salecenter.service.IStaffBdCodeService;
import com.get.salecenter.service.IStudentAgentService;
import com.get.salecenter.service.IStudentOfferService;
import com.get.salecenter.service.IStudentProjectRoleService;
import com.get.salecenter.utils.EmailSenderUtils;
import com.get.salecenter.utils.MyStringUtils;
import com.get.salecenter.utils.VerifyDataPermissionsUtils;
import com.get.salecenter.utils.sale.GetAgentLabelDataUtils;
import com.get.salecenter.vo.AgenCommissionAndAgentSearchVo;
import com.get.salecenter.vo.AgentBusinessInfoVo;
import com.get.salecenter.vo.AgentCompanyInfoVo;
import com.get.salecenter.vo.AgentContractAccountVo;
import com.get.salecenter.vo.AgentContractVo;
import com.get.salecenter.vo.AgentExportVo;
import com.get.salecenter.vo.AgentLabelVo;
import com.get.salecenter.vo.AgentListVo;
import com.get.salecenter.vo.AgentRoleStaffVo;
import com.get.salecenter.vo.AgentSettlementPageVo;
import com.get.salecenter.vo.AgentSettlementVo;
import com.get.salecenter.vo.AgentSourceExcelVo;
import com.get.salecenter.vo.AgentSourceVo;
import com.get.salecenter.vo.AgentSubVo;
import com.get.salecenter.vo.AgentVo;
import com.get.salecenter.vo.AreaRegionVo;
import com.get.salecenter.vo.CommissionSummaryPageVo;
import com.get.salecenter.vo.CommissionSummaryVo;
import com.get.salecenter.vo.CompanyTreeVo;
import com.get.salecenter.vo.ContactPersonVo;
import com.get.salecenter.vo.MediaAndAttachedVo;
import com.get.salecenter.vo.PayablePlanVo;
import com.get.salecenter.vo.SelItem;
import com.get.salecenter.vo.StudentProjectRoleVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JsonConfig;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.StringJoiner;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @author: Sea
 * @create: 2020/7/7 11:53
 * @verison: 1.0
 * @description: 代理管理实现类
 */
@Service
@Slf4j
public class AgentServiceImpl extends GetServiceImpl<AgentMapper, Agent> implements IAgentService {
    @Resource
    private AgentMapper agentMapper;
    @Resource
    private AgentStaffMapper agentStaffMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IContactPersonService personService;
    @Resource
    private IAgentCompanyService companyService;
    @Resource
    private IMediaAndAttachedService attachedService;
    @Resource
    private IAgentStaffService agentStaffService;
    @Resource
    private IStudentAgentService studentAgentService;
    @Resource
    private ICompanyRelationService companyRelationService;
    @Resource
    private IAgentContractService agentContractService;
    @Resource
    private IStaffBdCodeService staffBdCodeService;
    @Resource
    private IAppAgentService appAgentService;
    @Resource
    private IDeleteService deleteService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    private StudentOfferItemSettlementBatchExchangeMapper studentOfferItemSettlementBatchExchangeMapper;
    @Resource
    private StudentOfferItemMapper studentOfferItemMapper;
    @Resource
    private AgentCompanyMapper agentCompanyMapper;
    @Resource
    @Lazy
    private IAgentContractAccountService agentContractAccountService;
    @Resource
    private PayablePlanMapper payablePlanMapper;
    @Resource
    private StaffBdCodeMapper staffBdCodeMapper;
    @Resource
    private IPlatformCenterClient platformCenterClient;
    @Resource
    private IFinanceCenterClient financeCenterClient;
    @Resource
    private AgencyCompanyHasAgencyUsersMapper agencyCompanyHasAgencyUsersMapper;
    @Resource
    private VerifyDataPermissionsUtils verifyDataPermissionsUtils;
    @Resource
    private IContactPersonService contactPersonService;
    @Lazy
    @Resource
    private IStudentProjectRoleService studentProjectRoleService;
    @Lazy
    @Resource
    private AgentRoleStaffService agentRoleStaffService;
    @Resource
    private MediaAndAttachedMapper mediaAndAttachedMapper;
    @Resource
    private AgentContractMapper agentContractMapper;
    @Resource
    private IRegistrationCenterClient registrationCenterClient;
    @Resource
    @Lazy
    private IStudentOfferService studentOfferService;
    @Resource
    private KpiInstitutionProviderMapper kpiInstitutionProviderMapper;
    @Resource
    private RAgentUuidMapper rAgentUuidMapper;
    @Resource
    private GetAgentLabelDataUtils getAgentLabelDataUtils;

    @Resource
    private IReminderCenterClient reminderCenterClient;
    @Resource
    private EmailSenderUtils emailSenderUtils;
    @Resource
    private IAppAgentContactPersonService appAgentContactPersonService;
    @Resource
    private IAppAgentContractAccountService appAgentContractAccountService;

    @Value("${bl-url}")
    public String blUrl;
    @Value("${idCard-url}")
    public String idCardUrl;

    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private GetRedis getRedis;

    // @Qualifier("servletConfig")
    // @Autowired
    // private ServletConfig servletConfig;

    @Override
    public AgentVo findAgentById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        verifyDataPermissionsUtils.verifyByBusinessId(id, VerifyDataPermissionsUtils.AGENT_O);
        Agent agent = agentMapper.selectById(id);
        AgentVo agentVo = BeanCopyUtils.objClone(agent, AgentVo::new);
        Map<String, String> companyMap = getCompanyMap();
        if (GeneralTool.isNotEmpty(agentVo)) {
            findByIdSetDtoName(agentVo, companyMap);
        }
        // assert agentVo != null;
        // //获取代理项目成员配置
        // List<AgentRoleStaff> agentRoleStaffList = doGetAgentRoleStaffByAgentId(id);
        // //设置名称
        // List<AgentRoleStaffVo> agentRoleStaffDtos =
        // doSetAgentRoleStaffDtos(agentRoleStaffList);
        //
        // agentVo.setAgentRoleStaffDtoList(agentRoleStaffDtos);

        // 首次合同创建时间
        List<AgentContract> agentContracts = agentContractMapper.selectList(new LambdaQueryWrapper<AgentContract>()
                .eq(AgentContract::getFkAgentId, id).orderByAsc(AgentContract::getGmtCreate));
        if (GeneralTool.isNotEmpty(agentContracts)) {
            agentVo.setFirstContractTime(agentContracts.get(0).getGmtCreate());
        }
        // 获取最新的生效合同数据状态
        AgentContract latestActiveAgentContract = this.agentContractService.latestActiveAgentContract(agent.getId());
        if (latestActiveAgentContract != null) {
            AgentContractApprovalStatusEnum actualContractStatusEnum = AgentContractApprovalStatusEnum.getActualContractStatus(
                    agent.getContractApprovalStatus(),
                    latestActiveAgentContract.getContractApprovalStatus(),
                    latestActiveAgentContract.getStartTime(),
                    latestActiveAgentContract.getEndTime()
            );
            if (actualContractStatusEnum != null) {
                agentVo.setContractApprovalStatus(actualContractStatusEnum.getCode());
            }
        }

        List<StudentOfferItem> studentOfferItems = studentOfferItemMapper
                .selectList(new LambdaQueryWrapper<StudentOfferItem>().eq(StudentOfferItem::getFkAgentId, id)
                        .orderByAsc(StudentOfferItem::getGmtCreate));
        if (GeneralTool.isNotEmpty(studentOfferItems)) {
            agentVo.setFirstOfferItemTime(studentOfferItems.get(0).getGmtCreate());
        }
        return agentVo;
    }

    private List<AgentRoleStaffVo> doSetAgentRoleStaffDtos(List<AgentRoleStaff> agentRoleStaffList) {
        if (GeneralTool.isEmpty(agentRoleStaffList)) {
            return null;
        }
        Set<Long> countryIds = agentRoleStaffList.stream().map(AgentRoleStaff::getFkAreaCountryId)
                .filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Long, String> countryNameMap = Maps.newHashMap();
        Set<Long> staffIds = agentRoleStaffList.stream().map(AgentRoleStaff::getFkStaffId).collect(Collectors.toSet());
        if (GeneralTool.isNotEmpty(countryIds)) {
            countryNameMap = institutionCenterClient.getCountryNamesByIds(countryIds).getData();
        }
        Map<Long, String> staffNamesByIds = permissionCenterClient.getStaffNamesByIds(staffIds);
        Map<Long, String> allRoleName = studentProjectRoleService.getAllRoleName();

        List<AgentRoleStaffVo> agentRoleStaffVos = BeanCopyUtils.copyListProperties(agentRoleStaffList,
                AgentRoleStaffVo::new);
        for (AgentRoleStaffVo agentRoleStaffVo : agentRoleStaffVos) {
            agentRoleStaffVo.setFkStaffName(staffNamesByIds.get(agentRoleStaffVo.getFkStaffId()));
            agentRoleStaffVo.setFkTypeKeyName(
                    TableEnum.getValueByKey(agentRoleStaffVo.getFkTypeKey(), TableEnum.BUSINESS_TYPE));
            agentRoleStaffVo.setFkStudentProjectRoleName(allRoleName.get(agentRoleStaffVo.getFkStudentProjectRoleId()));
            if (GeneralTool.isNotEmpty(agentRoleStaffVo.getFkAreaCountryId())) {
                agentRoleStaffVo.setAreaCountryName(countryNameMap.get(agentRoleStaffVo.getFkAreaCountryId()));
            }
        }
        return agentRoleStaffVos;
    }

    private List<AgentRoleStaff> doGetAgentRoleStaffByAgentId(Long id) {
        return agentRoleStaffService.list(Wrappers.<AgentRoleStaff>lambdaQuery()
                .eq(AgentRoleStaff::getFkAgentId, id));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long addAgent(AgentAddDto agentAddDto) {
        this.validateAddAgent(agentAddDto);
        AgentDto agentDto = agentAddDto.getAgentVo();
        List<AgentContactPersonDto> contactPersonVos = agentAddDto.getAgentContactPersonVos();
        if (GeneralTool.isEmpty(agentDto) || GeneralTool.isEmpty(contactPersonVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        // if (GeneralTool.isEmpty(bdCode)) {
        // throw new
        // GetServiceException(LocaleMessageUtils.getMessage("staff_bd_null"));
        // }
        List<Agent> agents = new ArrayList<>();
        if (GeneralTool.isNotEmpty(agentDto.getNum())) {
            agents = validateAdd(agentDto);
        }
        if (GeneralTool.isNotEmpty(agents)) {
            StringBuilder sb = new StringBuilder();
            Agent agent = agents.get(0);
            String agentName = agent.getName();
            sb.append(LocaleMessageUtils.getMessage("duplicate_proxy"));
            sb.append(agentName);
            AgentStaff agentStaff = agentStaffService.getAgentStaffByAgentId(agent.getId());
            if (GeneralTool.isNotEmpty(agentStaff)) {
                String staffName = "";
                Result<String> staffNameResult = permissionCenterClient.getStaffName(agentStaff.getFkStaffId());
                if (staffNameResult.isSuccess() && staffNameResult.getData() != null) {
                    staffName = staffNameResult.getData();
                }
                if (GeneralTool.isNotEmpty(staffName)) {
                    sb.append(",");
                    sb.append(LocaleMessageUtils.getMessage("Bound_BD"));
                    sb.append(agentName);
                }
            }
            throw new GetServiceException(LocaleMessageUtils.getMessage("code_or_name_exists") + "," + sb.toString());
        }
        if (GeneralTool.isEmpty(agentDto.getFkParentAgentId()) && !agentDto.getIsSettlementPort()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("PRIMARY_AGENT_SETTLEMENT_PORT_NOT_EXISTS"));
        }
        Agent agent = BeanCopyUtils.objClone(agentDto, Agent::new);
        // 随机生成代理邀请码
        setInvitationCode(agent);
        agent.setIsCustomerChannel(false);
        utilService.updateUserInfoToEntity(agent);
        int flag = agentMapper.insert(agent);

        RAgentUuid rAgentUuid = new RAgentUuid();

        rAgentUuid.setFkAgentId(agent.getId());
        rAgentUuid.setFkAgentUuid(UUID.randomUUID().toString());
        utilService.updateUserInfoToEntity(rAgentUuid);
        rAgentUuidMapper.insert(rAgentUuid);

        // String bdCode = staffBdCodeService.getBDbyStaffId(GetAuthInfo.getStaffId());
        // if (StringUtils.isNotBlank(bdCode)) {
        // AgentStaffVo agentStaffDto = new AgentStaffVo();
        // agentStaffDto.setFkStaffId(GetAuthInfo.getStaffId());
        // agentStaffDto.setFkAgentId(agent.getId());
        // agentStaffDto.setIsActive(true);
        // agentStaffDto.setActiveDate(new Date());
        // agentStaffService.addAgentStaff(agentStaffDto);
        // }
        // 如果用户输入，按用户编号保存，如果用户留空，系统自动生成，AG+6位ID数字，例：AG000001
        if (GeneralTool.isEmpty(agentDto.getNum())) {
            agent.setNum(MyStringUtils.getAgentNum(agent.getId()));
        }
        agentMapper.updateById(agent);
        String nature = agentDto.getNature();
        List<MediaAndAttachedDto> mediaAttachedVos = new ArrayList<>();
        if ("1".equals(nature)) {
            MediaAndAttachedDto mediaAndAttachedDto = agentAddDto.getMediaAndAttachedVo();
            if (mediaAndAttachedDto != null) {
                mediaAndAttachedDto.setFkTableId(agent.getId());
                mediaAndAttachedDto.setFkTableName(TableEnum.SALE_AGENT.key);
                mediaAndAttachedDto.setTypeKey(TableEnum.BUSINESS_LICENSE.key);
                mediaAttachedVos.add(mediaAndAttachedDto);
            }
        }
        List<AgentIdCardDto> cardVo = agentAddDto.getAgentIdCardVos();
        if (GeneralTool.isNotEmpty(cardVo)) {
            for (AgentIdCardDto idCardVo : cardVo) {
                Integer type = idCardVo.getType();
                String value = ProjectExtraEnum.getInitialValueByKey(type, ProjectExtraEnum.idCartFB);
                if (StringUtils.isBlank(value)) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("illegal_type_file"));
                }
                MediaAndAttachedDto attachedVo = idCardVo.getMediaAndAttachedVo();
                attachedVo.setFkTableId(agent.getId());
                attachedVo.setFkTableName(TableEnum.SALE_AGENT.key);
                if (type == 0) {
                    attachedVo.setTypeKey(FileTypeEnum.SALE_AGENT_ID_CARD_FRONT.key);
                } else {
                    attachedVo.setTypeKey(FileTypeEnum.SALE_AGENT_ID_CARD_BACK.key);
                }
                mediaAttachedVos.add(attachedVo);
            }
        }
        if (GeneralTool.isNotEmpty(mediaAttachedVos)) {
            attachedService.saveBatchMediaAndAttached(mediaAttachedVos);
        }
        if (flag > 0) {
            Long companyId;
            if (GeneralTool.isNotEmpty(agentDto.getFkCompanyId())) {
                companyId = agentDto.getFkCompanyId();
            } else {
                companyId = SecureUtil.getFkCompanyId();
            }
            // 添加到公司中间表
            AgentCompanyDto agentCompanyDto = new AgentCompanyDto();
            agentCompanyDto.setFkAgentId(agent.getId());
            agentCompanyDto.setFkCompanyId(companyId);
            companyService.addRelation(agentCompanyDto);

            // Example example = new Example(AgentStaff.class);
            // example.createCriteria().andEqualTo("fkStaffId",
            // StaffContext.getStaff().getId()).andEqualTo("isActive", 1);
            // List<AgentStaff> agentStaffs = agentStaffMapper.selectByExample(example);

            // List<StaffBdCode> staffBdCodes =
            // staffBdCodeMapper.selectList(Wrappers.<StaffBdCode>lambdaQuery()
            // .eq(StaffBdCode::getFkStaffId, GetAuthInfo.getStaffId()));
            // if (GeneralTool.isNotEmpty(staffBdCodes)) {
            // //添加到员工代理中间表
            // AgentStaffVo agentStaff = new AgentStaffVo();
            // agentStaff.setFkAgentId(agent.getId());
            // agentStaff.setFkStaffId(GetAuthInfo.getStaffId());
            // agentStaff.setIsActive(true);
            // agentStaff.setActiveDate(new Date());
            // agentStaffService.addAgentStaff(agentStaff);
            // }
            List<ContactPersonDto> personVos = contactPersonVos.stream().map(c -> {
                ContactPersonDto personVo = new ContactPersonDto();
                BeanCopyUtils.copyProperties(c, personVo);
                personVo.setFkTableName(TableEnum.SALE_AGENT.key);
                personVo.setFkTableId(agent.getId());
                return personVo;
            }).collect(Collectors.toList());
            contactPersonService.addAgentContactPerson(personVos);

            // 添加get_app_registration_center.m_user
            Long fkUserId = registrationCenterClient.insertAgentUser(agent).getData();
            if (GeneralTool.isEmpty(agent.getId()) || GeneralTool.isEmpty(companyId) || GeneralTool.isEmpty(fkUserId)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
            }
            // platformCenterClient.insertIssueAgentUser(agent.getId(),companyId,fkUserId);

        }

        assert agent != null;
        if (GeneralTool.isEmpty(agentAddDto.getAgentRoleStaffAddVos())) {
            return agent.getId();
        }

        // 新增代理项目成员配置
        doSaveAgentRoleStaff(agentAddDto.getAgentRoleStaffAddVos(), agent.getId());
        return agent.getId();
    }

    /**
     * 新增联系人校验
     *
     * @param agentAddDto
     */
    private void validateAddAgent(AgentAddDto agentAddDto) {
        List<AgentContactPersonDto> contactPersonList = agentAddDto.getAgentContactPersonVos();
        if (CollectionUtil.isEmpty(contactPersonList)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("contact_person_empty"));
        }

        // 使用Map来收集我们关心的特殊类型联系人，key为类型code，value为联系人对象
        Map<String, AgentContactPersonDto> specialContactsMap = new HashMap<>();
        Set<String> specialTypeCodes = Sets.newHashSet(
                ContactPersonTypeEnum.EMERGENCY.getCode(),
                ContactPersonTypeEnum.ADMIN.getCode(),
                ContactPersonTypeEnum.COMMISSION.getCode());

        for (AgentContactPersonDto contactPerson : contactPersonList) {
            String typeKeyString = contactPerson.getFkContactPersonTypeKey();
            if (StringUtils.isBlank(typeKeyString)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("contact_person_type_empty"));
            }

            Set<String> typeSet = new HashSet<>(Arrays.asList(typeKeyString.split(",")));

            // 校验单个联系人内部的类型冲突
            if (ContactPersonTypeEnum.hasConflictingNewTypes(typeSet)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("contact_person_type_conflict"));
            }

            // 遍历当前联系人的所有类型，检查是否是我们关心的特殊类型
            for (String typeCode : typeSet) {
                if (specialTypeCodes.contains(typeCode)) {
                    // 如果Map中已存在该类型的联系人，说明重复了
                    if (specialContactsMap.containsKey(typeCode)) {
                        // 可以根据typeCode动态生成更精确的错误消息
                        throw new GetServiceException(
                                LocaleMessageUtils.getFormatMessage("contact_type_duplicate"));
                    }
                    specialContactsMap.put(typeCode, contactPerson);
                }
            }
        }

        // 从Map中获取联系人
        AgentContactPersonDto emergencyContact = specialContactsMap.get(ContactPersonTypeEnum.EMERGENCY.getCode());
        AgentContactPersonDto adminContact = specialContactsMap.get(ContactPersonTypeEnum.ADMIN.getCode());
        AgentContactPersonDto commissionContact = specialContactsMap.get(ContactPersonTypeEnum.COMMISSION.getCode());

        // 如果没有紧急联系人，或者另外两者都不存在，则无需进行后续的交叉校验
        if (emergencyContact == null || (adminContact == null && commissionContact == null)) {
            return;
        }

        // 执行跨联系人的校验
        if (adminContact != null) {
            checkDuplicatePerson(emergencyContact, adminContact);
        }
        if (commissionContact != null) {
            checkDuplicatePerson(emergencyContact, commissionContact);
        }
    }

    /**
     * 校验两个联系人是否为同一人（通过姓名和邮箱判断）
     *
     * @param contact1 联系人1
     * @param contact2 联系人2
     */
    private void checkDuplicatePerson(AgentContactPersonDto contact1, AgentContactPersonDto contact2) {
        // 快速返回，避免不必要的处理
        if (contact1 == null || contact2 == null) {
            return;
        }

        // 判断是否为同一人，是则抛出异常
        if (isSamePerson(contact1, contact2)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("agent_emergency_contact_duplicate"));
        }
    }

    /**
     * 判断两个联系人是否为同一人
     * 判断依据：姓名或邮箱任一相同且不为空（避免角色重复）
     *
     * @param contact1 联系人1
     * @param contact2 联系人2
     * @return 如果是同一人返回true，否则返回false
     */
    private boolean isSamePerson(AgentContactPersonDto contact1, AgentContactPersonDto contact2) {
        // 有效的联系人必须同时具有非空的姓名和邮箱
        if (StringUtils.isBlank(contact1.getName()) || StringUtils.isBlank(contact1.getEmail()) ||
                StringUtils.isBlank(contact2.getName()) || StringUtils.isBlank(contact2.getEmail())) {
            return false;
        }

        // 姓名或邮箱任一相同则认为是同一人（避免角色重复）
        return Objects.equals(contact1.getName(), contact2.getName()) ||
                Objects.equals(contact1.getEmail(), contact2.getEmail());
    }

    /**
     * 保存代理项目成员配置
     *
     * @param agentRoleStaffAddDtos
     * @param agentId
     */
    private void doSaveAgentRoleStaff(List<AgentRoleStaffAddDto> agentRoleStaffAddDtos, Long agentId) {

        List<AgentRoleStaff> agentRoleStaffList = Lists.newArrayList();

        for (AgentRoleStaffAddDto agentRoleStaffAddDto : agentRoleStaffAddDtos) {
            // 验证必选参数是否为空
            validateAgentRoleStaffAddVo(agentRoleStaffAddDto);
            AgentRoleStaff agentRoleStaff = BeanCopyUtils.objClone(agentRoleStaffAddDto, AgentRoleStaff::new);
            assert agentRoleStaff != null;
            agentRoleStaff.setFkAgentId(agentId);
            utilService.setCreateInfo(agentRoleStaff);
            if (GeneralTool.isEmpty(agentRoleStaffAddDto.getFkAreaCountryIds())) {
                agentRoleStaffList.add(agentRoleStaff);
            } else {
                for (Long fkAreaCountryId : agentRoleStaffAddDto.getFkAreaCountryIds()) {
                    AgentRoleStaff roleStaff = BeanCopyUtils.objClone(agentRoleStaff, AgentRoleStaff::new);
                    roleStaff.setFkAreaCountryId(fkAreaCountryId);
                    agentRoleStaffList.add(roleStaff);
                }
            }
        }
        if (GeneralTool.isNotEmpty(agentRoleStaffList)) {
            boolean b = agentRoleStaffService.saveBatch(agentRoleStaffList);
            if (!b) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
            }
        }

    }

    private void validateAgentRoleStaffAddVo(AgentRoleStaffAddDto agentRoleStaffAddDto) {
        if (GeneralTool.isEmpty(agentRoleStaffAddDto.getFkStaffId())
                || GeneralTool.isEmpty(agentRoleStaffAddDto.getFkStudentProjectRoleId())
                || GeneralTool.isEmpty(agentRoleStaffAddDto.getFkTypeKey())
                || GeneralTool.isEmpty(agentRoleStaffAddDto.getFkCompanyId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
    }

    @Override
    public Boolean isActive(Long id) {
        if (GeneralTool.isNull(id)) {
            return false;
        }
        return agentMapper.isActiveByAgentId(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addAgent(AgentDto agentDto) {
        if (GeneralTool.isEmpty(agentDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        // String bdCode = staffBdCodeService.getBDbyStaffId(GetAuthInfo.getStaffId());
        // if (GeneralTool.isEmpty(bdCode)) {
        // throw new
        // GetServiceException(LocaleMessageUtils.getMessage("staff_bd_null"));
        // }
        List<Agent> agents = new ArrayList<>();
        if (GeneralTool.isNotEmpty(agentDto.getNum())) {
            agents = validateAdd(agentDto);
        }
        if (GeneralTool.isNotEmpty(agents)) {
            StringBuffer sb = new StringBuffer();
            Agent agent = agents.get(0);
            String agentName = agent.getName();
            sb.append(LocaleMessageUtils.getMessage("duplicate_proxy"));
            sb.append(agentName);
            AgentStaff agentStaff = agentStaffService.getAgentStaffByAgentId(agent.getId());
            if (GeneralTool.isNotEmpty(agentStaff)) {
                String staffName = "";
                Result<String> staffNameResult = permissionCenterClient.getStaffName(agentStaff.getFkStaffId());
                if (staffNameResult.isSuccess() && staffNameResult.getData() != null) {
                    staffName = staffNameResult.getData();
                }
                if (GeneralTool.isNotEmpty(staffName)) {
                    sb.append(",");
                    sb.append(LocaleMessageUtils.getMessage("Bound_BD"));
                    sb.append(agentName);
                }
            }
            throw new GetServiceException(LocaleMessageUtils.getMessage("code_or_name_exists") + "," + sb.toString());
        }
        if (GeneralTool.isEmpty(agentDto.getFkParentAgentId()) && !agentDto.getIsSettlementPort()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("PRIMARY_AGENT_SETTLEMENT_PORT_NOT_EXISTS"));
        }
        Agent agent = BeanCopyUtils.objClone(agentDto, Agent::new);
        // 随机生成代理邀请码
        setInvitationCode(agent);
        utilService.updateUserInfoToEntity(agent);
        // 代理申请，代理创建时间为代理申请创建时间
        if (GeneralTool.isNotEmpty(agentDto.getGmtCreate())) {
            agent.setGmtCreate(agentDto.getGmtCreate());
        }
        agent.setIsCustomerChannel(false);
        int flag = agentMapper.insert(agent);

        RAgentUuid rAgentUuid = new RAgentUuid();
        rAgentUuid.setFkAgentId(agent.getId());
        rAgentUuid.setFkAgentUuid(UUID.randomUUID().toString());
        utilService.updateUserInfoToEntity(rAgentUuid);
        rAgentUuidMapper.insert(rAgentUuid);

        // 如果用户输入，按用户编号保存，如果用户留空，系统自动生成，AG+6位ID数字，例：AG000001
        if (GeneralTool.isEmpty(agentDto.getNum())) {
            agent.setNum(MyStringUtils.getAgentNum(agent.getId()));
        }
        agentMapper.updateById(agent);

        if (flag > 0) {
            Long companyId;
            if (GeneralTool.isNotEmpty(agentDto.getFkCompanyId())) {
                companyId = agentDto.getFkCompanyId();
            } else {
                companyId = SecureUtil.getFkCompanyId();
            }
            // 添加到公司中间表
            AgentCompanyDto agentCompanyDto = new AgentCompanyDto();
            agentCompanyDto.setFkAgentId(agent.getId());
            agentCompanyDto.setFkCompanyId(companyId);
            companyService.addRelation(agentCompanyDto);

            // 添加get_app_registration_center.m_user
            Long fkUserId = registrationCenterClient.insertAgentUser(agent).getData();
            if (GeneralTool.isEmpty(agent.getId()) || GeneralTool.isEmpty(companyId) || GeneralTool.isEmpty(fkUserId)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
            }
            // platformCenterClient.insertIssueAgentUser(agent.getId(),companyId,fkUserId);

            // Example example = new Example(AgentStaff.class);
            // example.createCriteria().andEqualTo("fkStaffId",
            // StaffContext.getStaff().getId()).andEqualTo("isActive", 1);
            // List<AgentStaff> agentStaffs = agentStaffMapper.selectByExample(example);
            // String bdCode = staffBdCodeService.getBDbyStaffId(GetAuthInfo.getStaffId());
            // if (StringUtils.isNotBlank(bdCode)) {
            // AgentStaffVo agentStaffDto = new AgentStaffVo();
            // agentStaffDto.setFkStaffId(GetAuthInfo.getStaffId());
            // agentStaffDto.setFkAgentId(agent.getId());
            // agentStaffDto.setIsActive(true);
            // agentStaffDto.setActiveDate(new Date());
            // agentStaffService.addAgentStaff(agentStaffDto);
            // }
            // List<StaffBdCode> staffBdCodes =
            // staffBdCodeMapper.selectList(Wrappers.<StaffBdCode>lambdaQuery()
            // .eq(StaffBdCode::getFkStaffId, GetAuthInfo.getStaffId()));
            // if (GeneralTool.isNotEmpty(staffBdCodes)) {
            // //添加到员工代理中间表
            // AgentStaffVo agentStaff = new AgentStaffVo();
            // agentStaff.setFkAgentId(agent.getId());
            // agentStaff.setFkStaffId(GetAuthInfo.getStaffId());
            // agentStaff.setIsActive(true);
            // agentStaff.setActiveDate(new Date());
            // agentStaffService.addAgentStaff(agentStaff);
            // }
        }
        return agent.getId();
    }

    /**
     * @Description：随机生成代理邀请码
     * @Param
     * @Date 16:59 2021/4/30
     * <AUTHOR>
     */
    private void setInvitationCode(Agent agent) {
        boolean flag = true;
        while (flag) {
            // 代理邀请码
            String agentInvitationCode = MyStringUtils.getAgentInvitationCode();
            // Example example = new Example(Agent.class);
            // example.createCriteria().andEqualTo("invitationCode", agentInvitationCode);
            // List<Agent> agents = agentMapper.selectByExample(example);
            List<Agent> agents = agentMapper
                    .selectList(Wrappers.<Agent>lambdaQuery().eq(Agent::getInvitationCode, agentInvitationCode));
            if (GeneralTool.isEmpty(agents)) {
                agent.setInvitationCode(agentInvitationCode);
                flag = false;
            }
        }
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (findAgentById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        deleteService.deleteValidateAgent(id);
        agentMapper.deleteById(id);
    }

    @Override
    public AgentVo updateAgent(AgentUpdateDto agentUpdateDto) {
        AgentDto agentDto = agentUpdateDto.getAgentVo();
        if (agentDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        Agent agent = agentMapper.selectById(agentDto.getId());
        if (agent == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        if (GeneralTool.isEmpty(agentDto.getFkParentAgentId()) && !agentDto.getIsSettlementPort()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("PRIMARY_AGENT_SETTLEMENT_PORT_NOT_EXISTS"));
        }
        // 获取所有子代理
        List<Long> followerIds = agentMapper.getAgentFollowerIds(agentDto.getId());
        // 子代理中与编辑父代理重复 则抛出异常
        if (followerIds.contains(agentDto.getFkParentAgentId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("agent_bind_parent_error"));
        }
        if (GeneralTool.isNotEmpty(agentDto.getFkParentAgentId())) {
            if (agentDto.getFkParentAgentId().equals(agentDto.getId())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("parent_agent_bind_error"));
            }
        }

        // Agent agent = BeanCopyUtils.objClone(agentDto, Agent::new);
        BeanCopyUtils.copyProperties(agentDto, agent);
        if (GeneralTool.isEmpty(agent.getNum())) {
            // 重新随机生成代理邀请码
            agent.setNum(MyStringUtils.getAgentNum(agent.getId()));
        }
        List<Agent> list = this.agentMapper.selectList(
                Wrappers.<Agent>query().lambda().eq(Agent::getNum, agent.getNum()).ne(Agent::getId, agentDto.getId()));

        // if (list.size() <= 0 || list.get(0).getId().equals(agent.getId())) {
        // List<Agent> list1 =
        // this.agentMapper.selectList(Wrappers.<Agent>query().lambda().eq(Agent::getName,agent.getName()));
        // if (list1.size() > 0 && !list1.get(0).getId().equals(agent.getId())){
        // StringBuffer sb = new StringBuffer();
        // Agent agent1 = list.get(0);
        // String agentName = agent1.getName();
        // sb.append(LocaleMessageUtils.getMessage("duplicate_proxy"));
        // sb.append(agentName);
        // AgentStaff agentStaff =
        // agentStaffService.getAgentStaffByAgentId(agent1.getId());
        // if(GeneralTool.isNotEmpty(agentStaff)){
        // Result<String> staffNameResult=
        // permissionCenterClient.getStaffName(agentStaff.getFkStaffId());
        // if(staffNameResult.isSuccess() && staffNameResult.getData()!=null)
        // {
        // sb.append(",");
        // sb.append(LocaleMessageUtils.getMessage("Bound_BD"));
        // sb.append(agentName);
        // }
        // }
        // throw new
        // GetServiceException(LocaleMessageUtils.getMessage("code_or_name_exists")+","+sb.toString());
        // }
        // } else {
        // StringBuffer sb = new StringBuffer();
        // Agent agent1 = list.get(0);
        // String agentName = agent1.getName();
        // sb.append(LocaleMessageUtils.getMessage("duplicate_proxy"));
        // sb.append(agentName);
        // AgentStaff agentStaff =
        // agentStaffService.getAgentStaffByAgentId(agent1.getId());
        // if(GeneralTool.isNotEmpty(agentStaff)){
        // Result<String> staffNameResult =
        // permissionCenterClient.getStaffName(agentStaff.getFkStaffId());
        // if(staffNameResult.isSuccess() && staffNameResult.getData()!=null)
        // {
        // sb.append(",");
        // sb.append(LocaleMessageUtils.getMessage("Bound_BD"));
        // sb.append(agentName);
        // }
        // }
        // throw new
        // GetServiceException(LocaleMessageUtils.getMessage("code_or_name_exists")+","+sb.toString());
        // }
        //
        // utilService.updateUserInfoToEntity(agent);
        // agentMapper.updateById(agent);

        if (list.size() >= 1) {
            StringBuffer sb = new StringBuffer();
            Agent agent1 = list.get(0);
            String agentName = agent1.getName();
            sb.append(LocaleMessageUtils.getMessage("duplicate_proxy"));
            sb.append(agentName);
            AgentStaff agentStaff = agentStaffService.getAgentStaffByAgentId(agent1.getId());
            if (GeneralTool.isNotEmpty(agentStaff)) {
                String staffName = "";
                Result<String> staffNameResult = permissionCenterClient.getStaffName(agentStaff.getFkStaffId());
                if (staffNameResult.isSuccess() && staffNameResult.getData() != null) {
                    staffName = staffNameResult.getData();
                }
                if (GeneralTool.isNotEmpty(staffName)) {
                    sb.append(",");
                    sb.append(LocaleMessageUtils.getMessage("Bound_BD"));
                    sb.append(staffName);
                }
            }
            throw new GetServiceException(LocaleMessageUtils.getMessage("code_exists") + "," + sb.toString());
        }

        utilService.updateUserInfoToEntity(agent);
        agentMapper.updateByIdWithNull(agent);
        String nature = agentDto.getNature();
        List<MediaAndAttachedDto> mediaAttachedVos = new ArrayList<>();
        if ("1".equals(nature)) {
            MediaAndAttachedDto mediaAndAttachedDto = agentUpdateDto.getMediaAndAttachedVo();
            if (mediaAndAttachedDto != null) {
                mediaAndAttachedDto.setFkTableId(agent.getId());
                mediaAndAttachedDto.setFkTableName(TableEnum.SALE_AGENT.key);
                mediaAndAttachedDto.setTypeKey(TableEnum.BUSINESS_LICENSE.key);
                mediaAttachedVos.add(mediaAndAttachedDto);
            }
        }
        List<AgentIdCardDto> cardVo = agentUpdateDto.getAgentIdCardVos();
        if (GeneralTool.isNotEmpty(cardVo)) {
            for (AgentIdCardDto idCardVo : cardVo) {
                Integer type = idCardVo.getType();
                String value = ProjectExtraEnum.getInitialValueByKey(type, ProjectExtraEnum.idCartFB);
                if (StringUtils.isBlank(value)) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("illegal_type_file"));
                }
                MediaAndAttachedDto attachedVo = idCardVo.getMediaAndAttachedVo();
                attachedVo.setFkTableId(agent.getId());
                attachedVo.setFkTableName(TableEnum.SALE_AGENT.key);
                if (type == 1) {
                    attachedVo.setTypeKey(FileTypeEnum.SALE_AGENT_ID_CARD_FRONT.key);
                } else {
                    attachedVo.setTypeKey(FileTypeEnum.SALE_AGENT_ID_CARD_BACK.key);
                }
                mediaAttachedVos.add(attachedVo);
            }
        }
        if (GeneralTool.isNotEmpty(mediaAttachedVos)) {
            attachedService.saveBatchMediaAndAttached(mediaAttachedVos);
        }

        AgentVo agentVo = findAgentById(agent.getId());
        // HTI代理同步到CRM 需要公司id=3 且 代理id>43966
        copyAgentToCRM(agentVo);

        return agentVo;
    }

    /**
     * HTI代理同步到CRM
     *
     * @param agentVo
     */
    private void copyAgentToCRM(AgentVo agentVo) {

        // List<String> configKeys =
        // Lists.newArrayList(ProjectKeyEnum.IAE_CRM_SECRETID.key,ProjectKeyEnum.HTI_AGENT_BMS_TO_CRM.key);
        // //获取配置
        // Map<String, ConfigVo> systemConfig = getSystemConfig(configKeys);
        //
        // 发送请求
        // createUrlAndSendRequest(systemConfig,agentVo);

        // 需代理id>43966
        if (agentVo.getId() <= 43966L) {
            return;
        }
        Map<Long, String> companyConfigMap = permissionCenterClient
                .getCompanyConfigMap(ProjectKeyEnum.HTI_AGENT_BMS_TO_CRM.key, 1).getData();
        // String configValue1 = companyConfigMap.get(SecureUtil.getFkCompanyId());
        String configValue1 = companyConfigMap.get(agentVo.getFkCompanyId());
        companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.HTI_AGENT_BMS_TO_CRM.key, 4)
                .getData();
        // String configValue4 = companyConfigMap.get(SecureUtil.getFkCompanyId());
        String configValue4 = companyConfigMap.get(agentVo.getFkCompanyId());

        // ConfigVo apiConfig =
        // systemConfig.get(ProjectKeyEnum.HTI_AGENT_BMS_TO_CRM.key);
        // String switchConfig = apiConfig.getValue1();
        // JSONObject switchConfigJsonObject = JSON.parseObject(switchConfig);
        // Integer iaeFlag = switchConfigJsonObject.getInteger("IAE");
        // Integer geaFlag = switchConfigJsonObject.getInteger("OTHER");
        if (configValue1.equals("1")) {
            if (GeneralTool.isEmpty(agentVo.getBdCode())) {
                // 没绑定bdCode时 bdCode为空远程调用接口时候报参数缺少
                return;
            }

            // ConfigVo secretConfigVo =
            // systemConfig.get(ProjectKeyEnum.IAE_CRM_SECRETID.key);
            ConfigVo secretConfigVo = permissionCenterClient.getConfigByKey(ProjectKeyEnum.IAE_CRM_SECRETID.key)
                    .getData();

            // 密钥
            String secretKey = secretConfigVo.getValue1();
            // 请求路径
            String apiUrl = configValue4;
            int index = apiUrl.indexOf("?");
            // 接口url
            String url = (index == -1) ? apiUrl : apiUrl.substring(0, index);

            // 获取十位时间戳
            long time = System.currentTimeMillis() / 1000;

            // 获取令牌sign
            if (GeneralTool.isEmpty(secretKey)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("missing_required_configuration"));
            }
            String painText = time + "&" + secretKey;
            MD5 md5 = MD5.create();
            String sign = md5.digestHex(painText);

            // 参数map
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("agentId", agentVo.getId());
            paramMap.put("companyId", agentVo.getCompanyIds().get(0));
            paramMap.put("num", agentVo.getNum());
            paramMap.put("name", agentVo.getName());
            paramMap.put("bdCode", agentVo.getBdCode());
            paramMap.put("countryName", MyStringUtils.getStringBetween(agentVo.getCountryName(), "（", "）"));
            paramMap.put("provinceName", MyStringUtils.getStringBetween(agentVo.getStateName(), "（", "）"));
            paramMap.put("cityName", MyStringUtils.getStringBetween(agentVo.getCityName(), "（", "）"));
            paramMap.put("nature", agentVo.getNature());
            paramMap.put("time", String.valueOf(time));
            paramMap.put("sign", sign);

            // 发送请求
            String resultStr = HttpUtil.post(url, paramMap);
            JSONObject result = JSONObject.parseObject(resultStr);
            if (!result.getBoolean("success")) {
                log.info("同步失败");
                // 同步失败抛出异常
                throw new GetServiceException(result.getString("message"));
            }
        }

    }

    /**
     * 发送请求
     *
     * @param systemConfig
     * @param agentVo
     */
    private void createUrlAndSendRequest(Map<String, ConfigVo> systemConfig, AgentVo agentVo) {
        // 需代理id>43966
        if (agentVo.getId() <= 43966L) {
            return;
        }
        ConfigVo apiConfig = systemConfig.get(ProjectKeyEnum.HTI_AGENT_BMS_TO_CRM.key);
        String switchConfig = apiConfig.getValue1();
        JSONObject switchConfigJsonObject = JSON.parseObject(switchConfig);
        Integer iaeFlag = switchConfigJsonObject.getInteger("IAE");
        Integer geaFlag = switchConfigJsonObject.getInteger("OTHER");
        if ((SecureUtil.getFkCompanyId().equals(3L) && iaeFlag == 1)
                || (!SecureUtil.getFkCompanyId().equals(3L) && geaFlag == 1)) {
            if (GeneralTool.isEmpty(agentVo.getBdCode())) {
                // 没绑定bdCode时 bdCode为空远程调用接口时候报参数缺少
                return;
            }
            ConfigVo secretConfigVo = systemConfig.get(ProjectKeyEnum.IAE_CRM_SECRETID.key);
            // 密钥
            String secretKey = secretConfigVo.getValue1();
            // 请求路径
            String apiUrl = apiConfig.getValue4();
            int index = apiUrl.indexOf("?");
            // 接口url
            String url = (index == -1) ? apiUrl : apiUrl.substring(0, index);

            // 获取十位时间戳
            long time = System.currentTimeMillis() / 1000;

            // 获取令牌sign
            if (GeneralTool.isEmpty(secretKey)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("missing_required_configuration"));
            }
            String painText = time + "&" + secretKey;
            MD5 md5 = MD5.create();
            String sign = md5.digestHex(painText);

            // 参数map
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("agentId", agentVo.getId());
            paramMap.put("companyId", agentVo.getCompanyIds().get(0));
            paramMap.put("num", agentVo.getNum());
            paramMap.put("name", agentVo.getName());
            paramMap.put("bdCode", agentVo.getBdCode());
            paramMap.put("countryName", MyStringUtils.getStringBetween(agentVo.getCountryName(), "（", "）"));
            paramMap.put("provinceName", MyStringUtils.getStringBetween(agentVo.getStateName(), "（", "）"));
            paramMap.put("cityName", MyStringUtils.getStringBetween(agentVo.getCityName(), "（", "）"));
            paramMap.put("nature", agentVo.getNature());
            paramMap.put("time", String.valueOf(time));
            paramMap.put("sign", sign);

            // 发送请求
            String resultStr = HttpUtil.post(url, paramMap);
            JSONObject result = JSONObject.parseObject(resultStr);
            if (!result.getBoolean("success")) {
                log.info("同步失败");
                // 同步失败抛出异常
                throw new GetServiceException(result.getString("message"));
            }
        }

    }

    /**
     * 获取配置
     *
     * @param configKeys
     * @return
     */
    private Map<String, ConfigVo> getSystemConfig(List<String> configKeys) {
        Map<String, ConfigVo> configDtoMap = Maps.newHashMap();
        if (GeneralTool.isEmpty(configKeys)) {
            return configDtoMap;
        }
        for (String configKey : configKeys) {
            ConfigVo configVo = permissionCenterClient.getConfigByKey(configKey).getData();
            if (GeneralTool.isEmpty(configVo)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("missing_required_configuration"));
            }
            configDtoMap.put(configKey, configVo);
        }
        return configDtoMap;
    }

    @Override
    public List<AgentVo> dataList(AgentQueryDto agentVo, Page page) {
        // LambdaQueryWrapper<Agent> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        // if (GeneralTool.isEmpty(agentVo.getFkCompanyId())) {
        // throw new
        // GetServiceException(LocaleMessageUtils.getMessage("company_id_null"));
        // }
        //
        // //代理关系查询条件
        // List<Long> queryAgentIds = new ArrayList<>();
        // List<Long> allAgentIds = new ArrayList<>();
        // //查询获取当前登录人 及下属员工所对应的所有代理/员工以及旗下员工所创建的代理
        // List<Long> agentIdList = getAgentList();
        // if (GeneralTool.isNotEmpty(agentIdList)) {
        // allAgentIds.addAll(agentIdList);
        // }
        // if (GeneralTool.isNotEmpty(agentVo.getFkCompanyId())) {
        // //该公司下的代理ids
        // List<Long> companyAgentIds = queryByCompanyId(agentVo);
        // if (GeneralTool.isNotEmpty(companyAgentIds)) {
        // allAgentIds.addAll(companyAgentIds);
        // }
        // }
        // if (GeneralTool.isNotEmpty(allAgentIds)) {
        // allAgentIds =
        // allAgentIds.stream().distinct().collect(Collectors.toList());//去重
        // } else {
        // return null;//直接返回
        // }
        // //查询条件-BD编号
        // if (GeneralTool.isNotEmpty(agentVo.getBdCode())) {
        // //获取查询条件代理ids
        // List<Long> agentIds_ =
        // agentStaffService.getAgentIdsByBdNum(agentVo.getBdCode());
        // if (GeneralTool.isNotEmpty(agentIds_) && GeneralTool.isNotEmpty(allAgentIds))
        // {
        // for (Long aLong : agentIds_) {
        // if (allAgentIds.contains(aLong)) {
        // queryAgentIds.add(aLong);
        // }
        // }
        // } else {
        // return null;//没有数据直接返回
        // }
        // }
        // //查询条件-BD绑定大区id
        // if (GeneralTool.isNotEmpty(agentVo.getFkAreaRegionId())) {
        // List<Long> agentIds_ =
        // agentStaffService.getAgentIdsByBdAreaRegionId(agentVo.getFkAreaRegionId());
        // if (GeneralTool.isNotEmpty(agentIds_) && GeneralTool.isNotEmpty(allAgentIds))
        // {
        // if (GeneralTool.isNotEmpty(queryAgentIds)) {
        // List<Long> tempList = new ArrayList<>();
        // for (Long aLong : agentIds_) {
        // //如存在则符合查询条件
        // if (queryAgentIds.contains(aLong)) {
        // tempList.add(aLong);
        // }
        // }
        // if (GeneralTool.isNotEmpty(tempList)) {
        // queryAgentIds = tempList;//加入查询条件
        // } else {
        // return null;//返回空
        // }
        // } else {
        // for (Long aLong : agentIds_) {
        // if (allAgentIds.contains(aLong)) {
        // queryAgentIds.add(aLong);
        // }
        // }
        // }
        // } else {
        // return null;//没有数据直接返回
        // }
        // }
        //
        // if (GeneralTool.isNotEmpty(queryAgentIds)) {
        // lambdaQueryWrapper.in(Agent::getId, queryAgentIds);
        // } else if (GeneralTool.isNotEmpty(allAgentIds)) {
        // lambdaQueryWrapper.in(Agent::getId, allAgentIds);
        // }

        // //代理ids
        // List<Long> agentIds = new ArrayList<>();
        // //查询条件-公司id
        // if (GeneralTool.isNotEmpty(agentVo.getFkCompanyId())) {
        // //该公司下的代理ids
        // agentIds.addAll(queryByCompanyId(agentVo));
        // }
        // 父级代理
        // if (GeneralTool.isNotEmpty(agentVo.getFkParentAgentId())) {
        // lambdaQueryWrapper.eq(Agent::getFkParentAgentId,
        // agentVo.getFkParentAgentId());
        // }
        // //查询条件-国家
        // if (GeneralTool.isNotEmpty(agentVo.getFkAreaCountryId())) {
        // lambdaQueryWrapper.eq(Agent::getFkAreaCountryId,
        // agentVo.getFkAreaCountryId());
        // }
        // //查询条件-州省
        // if (GeneralTool.isNotEmpty(agentVo.getFkAreaStateId())) {
        // lambdaQueryWrapper.eq(Agent::getFkAreaStateId, agentVo.getFkAreaStateId());
        // }
        // //查询条件-城市
        // if (GeneralTool.isNotEmpty(agentVo.getFkAreaCityId())) {
        // lambdaQueryWrapper.eq(Agent::getFkAreaCityId, agentVo.getFkAreaCityId());
        // }
        // //查询条件-BD编号
        // if (GeneralTool.isNotEmpty(agentVo.getBdCode())) {
        // //获取代理ids
        // agentIds.addAll(agentStaffService.getAgentIdsByBdNum(agentVo.getBdCode()));
        // }
        // 查询条件-是否生效
        // if (GeneralTool.isNotEmpty(agentVo.getIsActive())) {
        // lambdaQueryWrapper.eq(Agent::getIsActive, agentVo.getIsActive());
        // }
        // //查询条件-是否为关键代理
        // if (GeneralTool.isNotEmpty(agentVo.getIsKeyAgent())) {
        // lambdaQueryWrapper.eq(Agent::getIsKeyAgent, agentVo.getIsKeyAgent());
        // }
        // //查询条件-BD绑定大区id
        // if (GeneralTool.isNotEmpty(agentVo.getFkAreaRegionId())) {
        // agentIds.addAll(agentStaffService.getAgentIdsByBdAreaRegionId(agentVo.getFkAreaRegionId()));
        // }
        // if(GeneralTool.isNotEmpty(agentIds))
        // {
        //// lambdaQueryWrapper.in(Agent::getId,agentIds.stream().distinct());
        // lambdaQueryWrapper.in(Agent::getId,agentIds.stream().collect(Collectors.toSet()));
        // }
        // 查询条件-编号/名称关键字
        // if (GeneralTool.isNotEmpty(agentVo.getKeyWord())) {
        // lambdaQueryWrapper.and(wrapper_ ->
        // wrapper_.like(Agent::getNum, agentVo.getKeyWord()).or().like(Agent::getName,
        // agentVo.getKeyWord()));
        // }
        // lambdaQueryWrapper.orderByAsc(Agent::getNum);
        // lambdaQueryWrapper.orderByDesc(Agent::getIsActive);

        // 获取业务下属
        Long staffId = SecureUtil.getStaffId();
        // 员工id + 业务下属员工ids
        List<Long> staffFollowerIds = permissionCenterClient.getStaffFollowerIds(staffId).getData().stream()
                .filter(Objects::nonNull).distinct().collect(Collectors.toList());
        staffFollowerIds.add(staffId);

        IPage<AgentVo> iPage = GetCondition
                .getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        // 获取分页数据
        List<AgentVo> agents = agentMapper.getAgentList(iPage, agentVo);
        page.setAll((int) iPage.getTotal());

        // IPage<Agent> iPage =
        // agentMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(),
        // page.getShowCount())), lambdaQueryWrapper);
        // List<Agent> agents = iPage.getRecords();
        // page.setAll((int) iPage.getTotal());

        List<AgentVo> convertDatas = new ArrayList<>();
        Map<String, String> companyMap = getCompanyMap();
        // 国家ids
        // TODO 改过
        // Set<Long> countryIds =
        // agents.stream().map(Agent::getFkAreaCountryId).collect(Collectors.toSet());
        Set<Long> countryIds = agents.stream().map(AgentVo::getFkAreaCountryId).collect(Collectors.toSet());
        // 州省ids
        // TODO 改过
        // Set<Long> stateIds =
        // agents.stream().map(Agent::getFkAreaStateId).collect(Collectors.toSet());
        Set<Long> stateIds = agents.stream().map(AgentVo::getFkAreaStateId).collect(Collectors.toSet());
        // 城市ids
        // TODO 改过
        // Set<Long> cityIds =
        // agents.stream().map(Agent::getFkAreaCityId).collect(Collectors.toSet());
        Set<Long> cityIds = agents.stream().map(AgentVo::getFkAreaCityId).collect(Collectors.toSet());
        // 根据国家ids获取国家名称
        Map<Long, String> countryNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(countryIds)) {
            Result<Map<Long, String>> countryNameByIdsResult = institutionCenterClient.getCountryNamesByIds(countryIds);
            if (countryNameByIdsResult.isSuccess() && GeneralTool.isNotEmpty(countryNameByIdsResult.getData())) {
                countryNamesByIds = countryNameByIdsResult.getData();
            }
        }
        // 根据州省ids获取州省名称
        Map<Long, String> stateNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(stateIds)) {
            Result<Map<Long, String>> result = institutionCenterClient.getStateFullNamesByIds(stateIds);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                stateNamesByIds = result.getData();
            }
        }
        // 根据州省ids获取州省名称
        Map<Long, String> cityNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(cityIds)) {
            Result<Map<Long, String>> result = institutionCenterClient.getCityFullNamesByIds(cityIds);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                cityNamesByIds = result.getData();
            }
        }

        // 父代理对应的num
        Map<Long, String> parentAgentNumMap = new HashMap<>();
        // 父代理对应的name
        Map<Long, String> parentAgentNameMap = new HashMap<>();
        // 父代理
        // TODO 改过
        // Set<Long> parentAgentIds =
        // agents.stream().map(Agent::getFkParentAgentId).collect(Collectors.toSet());
        Set<Long> parentAgentIds = agents.stream().map(AgentVo::getFkParentAgentId).collect(Collectors.toSet());
        // 去除空元素
        parentAgentIds.removeIf(Objects::isNull);
        if (GeneralTool.isNotEmpty(parentAgentIds)) {
            List<Agent> parentAgents = agentMapper
                    .selectList(Wrappers.<Agent>query().lambda().in(Agent::getId, parentAgentIds));
            for (Agent parentAgent : parentAgents) {
                parentAgentNumMap.put(parentAgent.getId(), parentAgent.getNum());
                parentAgentNameMap.put(parentAgent.getId(), parentAgent.getName());
            }
        }

        // 代理ids TODO 改过Agent
        Set<Long> agentIds_ = agents.stream().map(AgentVo::getId).collect(Collectors.toSet());
        // 代理标签
        Map<Long, List<AgentLabelVo>> agentLabelMap = getAgentLabelDataUtils.getAgentLabelMap(agentIds_)
                .getAgentLabelMap();
        // 通过代理ids获取激活的代理员工BD编号对象
        Map<Long, AgentStaff> agentStaffByAgentIds = new HashMap<>();
        // 根据代理ids查询公司ids
        Map<Long, Set<Long>> relationByAgentIds = new HashMap<>();
        // 根据代理ids查询ISSUE用户信息
        Map<Long, List<UserAgentVo>> UserAgentDtolist = new HashMap<>();
        if (GeneralTool.isNotEmpty(agentIds_)) {
            agentStaffByAgentIds = agentStaffService.getAgentStaffByAgentIds(agentIds_);
            relationByAgentIds = companyService.getRelationByAgentIds(agentIds_);
            // UserAgentDtolist =
            // platformCenterClient.getUsersByAgentIds(agentIds_).getData();
        }

        // 获取所有代理员工
        Set<Long> fkStaffIds = new HashSet<>();
        Map<Long, StaffBdCode> bDbyStaffIds = new HashMap<>();
        Map<Long, String> staffNamesByIds = new HashMap<>();
        for (Map.Entry<Long, AgentStaff> longAgentStaffEntry : agentStaffByAgentIds.entrySet()) {
            fkStaffIds.add(longAgentStaffEntry.getValue().getFkStaffId());
        }
        if (GeneralTool.isNotEmpty(fkStaffIds)) {
            // 根据staffIds获取对应bdCode
            bDbyStaffIds = staffBdCodeService.getBDbyStaffIds(fkStaffIds);
            // 根据staffIds获取员工姓名
            Result<Map<Long, String>> staffNameResult = permissionCenterClient.getStaffNameMap(fkStaffIds);
            if (staffNameResult.isSuccess() && GeneralTool.isNotEmpty(staffNameResult)) {
                staffNamesByIds = staffNameResult.getData();
            }
        }

        if (GeneralTool.isEmpty(agentIds_)) {
            agentIds_.add(0L);
        }
        List<ContactPersonVo> contactPersons = contactPersonService.getContactPersonInfo(agentIds_);

        Map<Long, List<ContactPersonVo>> contactPersonMap = contactPersons.stream()
                .collect(Collectors.groupingBy(ContactPersonVo::getFkTableId));
        // TODO 改过
        // Agent agent
        for (AgentVo agent : agents) {
            AgentVo agentDto = BeanCopyUtils.objClone(agent, AgentVo::new);

            if (GeneralTool.isNotEmpty(contactPersonMap)
                    && GeneralTool.isNotEmpty(contactPersonMap.get(agent.getId()))) {
                agentDto.setContactPersonDtos(contactPersonMap.get(agent.getId()));
            }

            setDtoName(agentDto, companyMap, countryNamesByIds, stateNamesByIds,
                    cityNamesByIds, parentAgentNumMap, parentAgentNameMap, agentStaffByAgentIds, relationByAgentIds,
                    bDbyStaffIds, staffNamesByIds);
            // 获取issue用户信息
            if (GeneralTool.isNotEmpty(UserAgentDtolist)) {
                agentDto.setAgentInfoDto(UserAgentDtolist.get(agentDto.getId()));
            }
            // TODO 代理标签注释
            agentDto.setAgentLabelVos(agentLabelMap.getOrDefault(agentDto.getId(), Collections.emptyList()));
            convertDatas.add(agentDto);
        }
        // 判断代理是否拥有Issue账号
        // 初始化状态
        for (AgentVo agentDto : convertDatas) {
            agentDto.setIsJumpIssue(false);
        }
        Set<String> idGeas = convertDatas.stream().filter(i -> i.getIdGea() != null).map(AgentVo::getIdGea)
                .collect(Collectors.toSet());
        Set<Long> ids = convertDatas.stream().map(AgentVo::getId).collect(Collectors.toSet());
        List<Long> cppAgencyIdList = agencyCompanyHasAgencyUsersMapper.getAllCppAgencyId(idGeas);
        List<Long> BmsAgencyIdList = agencyCompanyHasAgencyUsersMapper.getAllBmsAgencyId(ids);
        if (GeneralTool.isNotEmpty(cppAgencyIdList)) {
            for (AgentVo agentDto : convertDatas) {
                if (GeneralTool.isNotEmpty(agentDto.getIdGea())) {
                    String[] array = agentDto.getIdGea().split(",");
                    List<String> resultList = Arrays.asList(array);
                    List<Long> results = resultList.stream().map(i -> Long.valueOf(i)).collect(Collectors.toList());
                    for (Long id : results) {
                        Boolean isJumpIssue = cppAgencyIdList.contains(id);
                        if (isJumpIssue) {
                            agentDto.setIsJumpIssue(isJumpIssue);
                        }
                    }
                }

            }
        }
        if (GeneralTool.isNotEmpty(BmsAgencyIdList)) {
            for (AgentVo agentDto : convertDatas) {
                if (GeneralTool.isNotEmpty(agentDto.getId())) {
                    Boolean isJumpIssue = BmsAgencyIdList.contains(agentDto.getId());
                    agentDto.setIsJumpIssue(isJumpIssue);
                }
            }
        }

        return convertDatas;
    }

    @Override
    public List<AgentVo> getAgents(AgentQueryDto agentVo, Page page) {
        List<AgentVo> convertDatas = new ArrayList<>();
        // 获取业务下属
        Long staffId = SecureUtil.getStaffId();
        // 员工id + 业务下属员工ids
        List<Long> staffFollowerIds = permissionCenterClient.getStaffFollowerIds(staffId).getData();
        staffFollowerIds.add(staffId);
        List<AgentVo> agents = new ArrayList<>();
        if (page == null) {
            agents = agentMapper.getAgents(null, agentVo, staffFollowerIds, SecureUtil.getCompanyIds(),
                    agentVo.getAgentAnnualSummaryVo(), SecureUtil.getCountryIds(), SecureUtil.getInstitutionIds());
        } else {
            IPage<Agent> iPage = GetCondition
                    .getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
            // 获取分页数据
            agents = agentMapper.getAgents(iPage, agentVo, staffFollowerIds, SecureUtil.getCompanyIds(),
                    agentVo.getAgentAnnualSummaryVo(), SecureUtil.getCountryIds(), SecureUtil.getInstitutionIds());
            page.setAll((int) iPage.getTotal());
        }
        if (GeneralTool.isEmpty(agents)) {
            return Collections.emptyList();
        }

        // 公司map
        Map<String, String> companyMap = getCompanyMap();
        // 国家ids
        // TODO 改过
        // Set<Long> countryIds =
        // agents.stream().map(Agent::getFkAreaCountryId).collect(Collectors.toSet());
        Set<Long> countryIds = agents.stream().map(AgentVo::getFkAreaCountryId).collect(Collectors.toSet());
        // 州省ids
        // TODO 改过
        // Set<Long> stateIds =
        // agents.stream().map(Agent::getFkAreaStateId).collect(Collectors.toSet());
        Set<Long> stateIds = agents.stream().map(AgentVo::getFkAreaStateId).collect(Collectors.toSet());
        // 城市ids
        // TODO 改过
        // Set<Long> cityIds =
        // agents.stream().map(Agent::getFkAreaCityId).collect(Collectors.toSet());
        Set<Long> cityIds = agents.stream().map(AgentVo::getFkAreaCityId).collect(Collectors.toSet());
        // 根据国家ids获取国家名称
        Map<Long, String> countryNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(countryIds)) {
            Result<Map<Long, String>> countryNameByIdsResult = institutionCenterClient.getCountryNamesByIds(countryIds);
            if (countryNameByIdsResult.isSuccess() && GeneralTool.isNotEmpty(countryNameByIdsResult.getData())) {
                countryNamesByIds = countryNameByIdsResult.getData();
            }
        }
        // 根据州省ids获取州省名称
        Map<Long, String> stateNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(stateIds)) {
            Result<Map<Long, String>> result = institutionCenterClient.getStateFullNamesByIds(stateIds);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                stateNamesByIds = result.getData();
            }
        }
        // 根据州省ids获取州省名称
        Map<Long, String> cityNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(cityIds)) {
            Result<Map<Long, String>> result = institutionCenterClient.getCityFullNamesByIds(cityIds);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                cityNamesByIds = result.getData();
            }
        }

        // 父代理对应的num
        Map<Long, String> parentAgentNumMap = new HashMap<>();
        // 父代理对应的name
        Map<Long, String> parentAgentNameMap = new HashMap<>();
        // 父代理
        // TODO 改过
        // Set<Long> parentAgentIds =
        // agents.stream().map(Agent::getFkParentAgentId).collect(Collectors.toSet());
        Set<Long> parentAgentIds = agents.stream().map(AgentVo::getFkParentAgentId).collect(Collectors.toSet());
        // 去除空元素
        parentAgentIds.removeIf(Objects::isNull);
        if (GeneralTool.isNotEmpty(parentAgentIds)) {
            List<Agent> parentAgents = agentMapper
                    .selectList(Wrappers.<Agent>query().lambda().in(Agent::getId, parentAgentIds));
            for (Agent parentAgent : parentAgents) {
                parentAgentNumMap.put(parentAgent.getId(), parentAgent.getNum());
                parentAgentNameMap.put(parentAgent.getId(), parentAgent.getName());
            }
        }

        // 代理ids TODO 改过Agent
        Set<Long> agentIds = agents.stream().map(AgentVo::getId).collect(Collectors.toSet());
        // 代理标签
        Map<Long, List<AgentLabelVo>> agentLabelMap = getAgentLabelDataUtils.getAgentLabelMap(agentIds)
                .getAgentLabelMap();
        List<Long> exist = attachedService.isExist(TableEnum.BUSINESS_LICENSE.key, agentIds);
        // 通过代理ids获取激活的代理员工BD编号对象
        Map<Long, AgentStaff> agentStaffByAgentIds = new HashMap<>();
        // 根据代理ids查询公司ids
        Map<Long, Set<Long>> relationByAgentIds = new HashMap<>();
        // 根据代理ids查询ISSUE用户信息
        Map<Long, List<UserAgentVo>> userAgentDtolist = new HashMap<>();
        // 合同id map
        Map<Long, List<Long>> agentContractIdListMap = Maps.newHashMap();
        // 合同和附件map
        Map<Long, List<SaleMediaAndAttached>> mediaAndAttachedsMap = Maps.newHashMap();
        // key:代理id value:最新创建的两个合同的返佣比例备注
        Map<Long, String> returnCommissionRateNoteMap = Maps.newHashMap();
        // key:代理id value:最新合同信息
        Map<Long, AgentContract> latestContractMap = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(agentIds)) {
            agentStaffByAgentIds = agentStaffService.getAgentStaffByAgentIds(agentIds);
            relationByAgentIds = companyService.getRelationByAgentIds(agentIds);
            if (GeneralTool.isEmpty(agentVo.getIsExport())) {
                // userAgentDtolist =
                // platformCenterClient.getUsersByAgentIds(agentIds).getData();
            }
            List<AgentContract> agentContracts = agentContractMapper
                    .selectList(Wrappers.lambdaQuery(AgentContract.class).in(AgentContract::getFkAgentId, agentIds)
                            .eq(AgentContract::getIsActive, true));

            if (GeneralTool.isNotEmpty(agentContracts)) {
                List<Long> agentContractIds = agentContracts.stream().map(AgentContract::getId)
                        .collect(Collectors.toList());
                agentContractIdListMap = agentContracts.stream().collect(Collectors.groupingBy(
                        AgentContract::getFkAgentId, Collectors.mapping(AgentContract::getId, Collectors.toList())));
                List<SaleMediaAndAttached> saleMediaAndAttacheds = mediaAndAttachedMapper
                        .selectList(Wrappers.lambdaQuery(SaleMediaAndAttached.class)
                                .in(SaleMediaAndAttached::getFkTableId, agentContractIds)
                                .eq(SaleMediaAndAttached::getFkTableName, TableEnum.AGENT_CONTRACT.key));
                if (GeneralTool.isNotEmpty(saleMediaAndAttacheds)) {
                    mediaAndAttachedsMap = saleMediaAndAttacheds.stream()
                            .collect(Collectors.groupingBy(SaleMediaAndAttached::getFkTableId));
                }

                // 最新创建的两个合同的返佣比例备注
                Map<Long, List<AgentContract>> lastTwoContracts = agentContracts.stream()
                        .collect(Collectors.groupingBy(
                                // 代理id分组
                                AgentContract::getFkAgentId,
                                Collectors.mapping(
                                        Function.identity(),
                                        Collectors.collectingAndThen(
                                                Collectors.toList(),
                                                // 同一个代理id的结果取前两条记录
                                                contracts -> contracts.stream()
                                                        .sorted(Comparator.comparing(AgentContract::getGmtCreate)
                                                                .reversed())
                                                        .limit(2)
                                                        .collect(Collectors.toList())))));
                // 将每个分组中的数据进行拼接
                returnCommissionRateNoteMap = lastTwoContracts.entrySet().stream()
                        .collect(Collectors.toMap(
                                Map.Entry::getKey,
                                entry -> entry.getValue().stream()
                                        .map(contract -> (GeneralTool.isNotEmpty(contract.getStartTime())
                                                ? DateUtil.formatDate(contract.getStartTime())
                                                : "-") +
                                                " 至 " +
                                                (GeneralTool.isNotEmpty(contract.getEndTime())
                                                        ? DateUtil.formatDate(contract.getEndTime())
                                                        : "-")
                                                +
                                                " " +
                                                (StringUtils.isNotBlank(contract.getReturnCommissionRateNote())
                                                        ? contract.getReturnCommissionRateNote()
                                                        : ""))
                                        .collect(Collectors.joining("\n"))));

                // 获取代理最新合同信息 - 按创建时间倒序，取最新的合同
                latestContractMap = agentContracts.stream()
                        .filter(agentContract -> {
                            if (ObjectUtils.isNull(agentContract)) {
                                return false;
                            }
                            return agentContract.getIsActive();
                        })
                        .collect(Collectors.toMap(
                                AgentContract::getFkAgentId,
                                Function.identity(),
                                (existing, replacement) -> {
                                    // 如果有多个合同，取最新创建时间的合同
                                    return existing.getGmtCreate().after(replacement.getGmtCreate()) ? existing
                                            : replacement;
                                }));
            }
        }

        // 获取所有代理员工
        Set<Long> fkStaffIds = new HashSet<>();
        Map<Long, StaffBdCode> bDbyStaffIds = new HashMap<>();
        Map<Long, String> staffNamesByIds = new HashMap<>();
        for (Map.Entry<Long, AgentStaff> longAgentStaffEntry : agentStaffByAgentIds.entrySet()) {
            fkStaffIds.add(longAgentStaffEntry.getValue().getFkStaffId());
        }
        if (GeneralTool.isNotEmpty(fkStaffIds)) {
            // 根据staffIds获取对应bdCode
            bDbyStaffIds = staffBdCodeService.getBDbyStaffIds(fkStaffIds);
            // 根据staffIds获取员工姓名
            Result<Map<Long, String>> staffNameResult = permissionCenterClient.getStaffNameMap(fkStaffIds);
            if (staffNameResult.isSuccess() && GeneralTool.isNotEmpty(staffNameResult)) {
                staffNamesByIds = staffNameResult.getData();
            }
        }

        // 查询代理联系人
        Map<Long, List<ContactPersonVo>> contactPersonMap = null;
        // 区分导出和列表 导出不用查联系人
        if (GeneralTool.isNotEmpty(page)) {
            if (GeneralTool.isEmpty(agentIds)) {
                agentIds.add(0L);
            }
            List<ContactPersonVo> contactPersons = contactPersonService.getContactPersonInfo(agentIds);
            contactPersonMap = contactPersons.stream().collect(Collectors.groupingBy(ContactPersonVo::getFkTableId));
        }

        // 获取营业执照配置
        Result<ConfigVo> config = permissionCenterClient.getConfigByKey("AGENT_BUSINESS_LICENSE");
        LocalDateTime configTime = null;
        if (config.isSuccess() && config.getData() != null) {
            configTime = LocalDateTime.parse(config.getData().getValue1(),
                    DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        // 代理管理列表，增加绑定学生数
        Map<Long, Integer> agentStudentNumMap = studentAgentService.getAgentStudentNum(agentIds);

        for (AgentVo agentDto : agents) {
            // AgentVo agentDto = BeanCopyUtils.objClone(agent, AgentVo::new);
            setDtoName(agentDto, companyMap, countryNamesByIds, stateNamesByIds,
                    cityNamesByIds, parentAgentNumMap, parentAgentNameMap, agentStaffByAgentIds, relationByAgentIds,
                    bDbyStaffIds, staffNamesByIds);

            StringBuffer stringBuffer = new StringBuffer();
            stringBuffer.append(agentDto.getNameNote()).append("/").append(agentDto.getCompanyName());
            agentDto.setNameNoteAndCompanyName(stringBuffer.toString());
            LocalDateTime createTime = agentDto.getGmtCreate().toInstant().atZone(ZoneId.systemDefault())
                    .toLocalDateTime();
            // 是否有营业执照
            if (configTime != null) {
                // 系统参数设置开始提醒时间，例如从：2022-10-15 00:00:00 开始创建的代理信息，若没有营业执照的，自动置前，并橙色显示
                agentDto.setIsABusinessLicense(!"1".equals(agentDto.getNature()) || exist.contains(agentDto.getId())
                        || createTime.isBefore(configTime));
            }
            // 获取issue用户信息
            if (GeneralTool.isNotEmpty(userAgentDtolist)) {
                agentDto.setAgentInfoDto(userAgentDtolist.get(agentDto.getId()));
            }

            if (GeneralTool.isNotEmpty(contactPersonMap)
                    && GeneralTool.isNotEmpty(contactPersonMap.get(agentDto.getId()))) {
                agentDto.setContactPersonDtos(contactPersonMap.get(agentDto.getId()));
            }
            // 代理学生数
            agentDto.setStudentNum(agentStudentNumMap.get(agentDto.getId()));
            // 设置返佣比例备注
            agentDto.setReturnCommissionRateNote(returnCommissionRateNoteMap.getOrDefault(agentDto.getId(), ""));

            // 设置合同相关信息
            AgentContract latestContract = latestContractMap.get(agentDto.getId());
            if (ObjectUtils.isNull(latestContract)) {
                // 无合同
                agentDto.setContractApprovalStatus(AgentContractApprovalStatusEnum.NO_CONTRACT.getCode());
            } else {
                agentDto.setContractEndTime(latestContract.getEndTime());
                // 计算实际合同状态
                AgentContractApprovalStatusEnum actualStatus = AgentContractApprovalStatusEnum
                        .getActualContractStatus(
                                agentDto.getContractApprovalStatus(),
                                latestContract.getContractApprovalStatus(),
                                latestContract.getStartTime(),
                                latestContract.getEndTime()
                        );
                // 根据实际状态设置合同信息
                if (actualStatus != null) {
                    agentDto.setContractApprovalStatus(actualStatus.getCode());
                }
            }

            List<Long> agentContractIds = agentContractIdListMap.get(agentDto.getId());
            if (GeneralTool.isEmpty(agentContractIds)) {
                agentDto.setIsHasContractAttachment(false);
            } else {
                agentDto.setIsHasContractAttachment(false);
                for (Long agentContractId : agentContractIds) {
                    List<SaleMediaAndAttached> saleMediaAndAttacheds = mediaAndAttachedsMap.get(agentContractId);
                    if (GeneralTool.isNotEmpty(saleMediaAndAttacheds)) {
                        agentDto.setIsHasContractAttachment(true);
                        break;
                    }
                }
            }
            agentDto.setAgentLabelVos(agentLabelMap.getOrDefault(agentDto.getId(), Collections.emptyList()));
            convertDatas.add(agentDto);
        }
        // 判断代理是否拥有Issue账号
        // 初始化状态
        for (AgentVo agentDto : convertDatas) {
            agentDto.setIsJumpIssue(false);
        }
        Set<String> idGeas = convertDatas.stream().filter(i -> i.getIdGea() != null).map(AgentVo::getIdGea)
                .collect(Collectors.toSet());
        Set<Long> ids = convertDatas.stream().map(AgentVo::getId).collect(Collectors.toSet());
        List<Long> cppAgencyIdList = agencyCompanyHasAgencyUsersMapper.getAllCppAgencyId(idGeas);
        List<Long> BmsAgencyIdList = agencyCompanyHasAgencyUsersMapper.getAllBmsAgencyId(ids);
        if (GeneralTool.isNotEmpty(cppAgencyIdList)) {
            for (AgentVo agentDto : convertDatas) {
                if (GeneralTool.isNotEmpty(agentDto.getIdGea())) {
                    String[] array = agentDto.getIdGea().split(",");
                    List<String> resultList = Arrays.asList(array);
                    List<Long> results = resultList.stream().map(i -> Long.valueOf(i)).collect(Collectors.toList());
                    for (Long id : results) {
                        Boolean isJumpIssue = cppAgencyIdList.contains(id);
                        if (isJumpIssue) {
                            agentDto.setIsJumpIssue(isJumpIssue);
                        }
                    }
                }
            }
        }
        if (GeneralTool.isNotEmpty(BmsAgencyIdList)) {
            for (AgentVo agentDto : convertDatas) {
                if (GeneralTool.isNotEmpty(agentDto.getId()) && !agentDto.getIsJumpIssue()) {
                    Boolean isJumpIssue = BmsAgencyIdList.contains(agentDto.getId());
                    agentDto.setIsJumpIssue(isJumpIssue);
                }
            }
        }

        // Service层过滤逻辑处理（处理6和7状态以及时间段过滤）
        convertDatas = filterByLogicalStatus(convertDatas, agentVo);

        // 若没有营业执照的，自动置前
        if (configTime != null) {
            List<AgentVo> first = convertDatas.stream().filter(c -> !c.getIsABusinessLicense())
                    .sorted(((o1, o2) -> o2.getGmtCreate().compareTo(o1.getGmtCreate()))).collect(Collectors.toList());
            List<AgentVo> end = convertDatas.stream().filter(AgentVo::getIsABusinessLicense)
                    .sorted(((o1, o2) -> o2.getGmtCreate().compareTo(o1.getGmtCreate()))).collect(Collectors.toList());
            first.addAll(end);
            return first;
        }
        return convertDatas;
    }

    /**
     * Service层过滤逻辑处理（处理6和7状态以及时间段过滤）
     *
     * @param agentVos      代理列表
     * @param agentQueryDto 查询条件
     * @return 过滤后的代理列表
     */
    private List<AgentVo> filterByLogicalStatus(List<AgentVo> agentVos, AgentQueryDto agentQueryDto) {
        if (agentVos == null || agentVos.isEmpty()) {
            return agentVos;
        }

        Stream<AgentVo> stream = agentVos.stream();

        // 过滤合同状态（所有状态：0、1、2、3、4、-4、5、6、7）
        Integer contractStatus = agentQueryDto.getContractApprovalStatus();
        if (contractStatus != null) {
            Integer statusFilter = contractStatus;

            // 对所有状态进行过滤
            stream = stream.filter(agentVo -> {
                Integer currentStatus = agentVo.getContractApprovalStatus();
                return currentStatus != null && currentStatus.equals(statusFilter);
            });
        }

        // 时间段过滤（生效合同时间段）
        if (agentQueryDto.getContractEffectiveTimeBegin() != null
                || agentQueryDto.getContractEffectiveTimeEnd() != null) {
            stream = stream.filter(agentVo -> {
                // 只对生效中的合同进行时间段过滤
                if (!AgentContractApprovalStatusEnum.EFFECTIVE.getCode().equals(agentVo.getContractApprovalStatus())) {
                    return false;
                }

                Date contractEndTime = agentVo.getContractEndTime();
                if (contractEndTime == null) {
                    return false;
                }

                // 检查开始时间
                if (agentQueryDto.getContractEffectiveTimeBegin() != null) {
                    if (contractEndTime.before(agentQueryDto.getContractEffectiveTimeBegin())) {
                        return false;
                    }
                }

                // 检查结束时间
                if (agentQueryDto.getContractEffectiveTimeEnd() != null) {
                    if (contractEndTime.after(agentQueryDto.getContractEffectiveTimeEnd())) {
                        return false;
                    }
                }

                return true;
            });
        }

        return stream.collect(Collectors.toList());
    }

    /**
     * Author Cream
     * Description : //获取代理激活数量信息和合同过期统计信息
     * Date 2023/8/23 12:41
     * Params:
     * Return
     */
    @Override
    public ResponseBo<AgentBusinessInfoVo> getAgentActiveInfo(AgentQueryDto agentVo) {
        // 获取业务下属
        Long staffId = SecureUtil.getStaffId();
        // 员工id + 业务下属员工ids
        List<Long> staffFollowerIds = permissionCenterClient.getStaffFollowerIds(staffId).getData();
        staffFollowerIds.add(staffId);
        // 查询所有代理
        List<AgentVo> agents = agentMapper.getAgents(null, agentVo, staffFollowerIds, SecureUtil.getCompanyIds(),
                agentVo.getAgentAnnualSummaryVo(), SecureUtil.getCountryIds(), SecureUtil.getInstitutionIds());

        // 统计激活和未激活代理数量
        long activeCount = agents.stream().filter(AgentVo::getIsActive).count();
        long unActiveCount = agents.size() - activeCount;

        // 获取代理IDs用于查询合同
        List<Long> agentIds = agents.stream().map(AgentVo::getId).collect(Collectors.toList());

        // 统计过期合同数量
        long contractExpiredCount = 0;
        if (GeneralTool.isNotEmpty(agentIds)) {
            // 查询所有代理的合同信息
            List<AgentContract> agentContracts = agentContractMapper
                    .selectList(Wrappers.lambdaQuery(AgentContract.class)
                            .in(AgentContract::getFkAgentId, agentIds)
                            .eq(AgentContract::getIsActive, true));

            if (GeneralTool.isNotEmpty(agentContracts)) {
                // 获取每个代理的最新合同
                Map<Long, AgentContract> latestContractMap = agentContracts.stream()
                        .collect(Collectors.toMap(
                                AgentContract::getFkAgentId,
                                Function.identity(),
                                (existing, replacement) -> existing.getGmtCreate().after(replacement.getGmtCreate())
                                        ? existing
                                        : replacement));

                // 统计过期合同数量
                contractExpiredCount = agents.stream()
                        .filter(agent -> {
                            AgentContract latestContract = latestContractMap.get(agent.getId());
                            if (latestContract != null) {
                                AgentContractApprovalStatusEnum actualStatus = AgentContractApprovalStatusEnum
                                        .getActualContractStatus(
                                                agent.getContractApprovalStatus(),
                                                latestContract.getContractApprovalStatus(),
                                                latestContract.getStartTime(),
                                                latestContract.getEndTime()
                                        );
                                return actualStatus == AgentContractApprovalStatusEnum.EXPIRED;
                            }
                            return false;
                        })
                        .count();
            }
        }

        AgentBusinessInfoVo agentBusinessInfoVo = new AgentBusinessInfoVo(
                activeCount, unActiveCount, contractExpiredCount);
        return new ResponseBo<>(agentBusinessInfoVo);
    }

    @Override
    public List<Long> getAgentIds(String agentNum, String agentName) {
        // Example example = new Example(Agent.class);
        // Example.Criteria criteria = example.createCriteria();
        // if (GeneralTool.isNotEmpty(agentNum)) {
        // criteria.andLike("num", "%" + agentNum + "%");
        // }
        // if (GeneralTool.isNotEmpty(agentName)) {
        // criteria.andLike("name", "%" + agentName + "%");
        // }
        // List<Agent> agents = agentMapper.selectByExample(example);

        LambdaQueryWrapper<Agent> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(agentNum)) {
            lambdaQueryWrapper.like(Agent::getNum, agentNum);
        }
        if (GeneralTool.isNotEmpty(agentName)) {
            lambdaQueryWrapper.like(Agent::getName, agentName);
        }
        List<Agent> agents = agentMapper.selectList(lambdaQueryWrapper);
        List<Long> agentIds = new ArrayList<>();
        // 若为空 防止 in（）报错，默认给值为0
        if (GeneralTool.isEmpty(agents)) {
            agentIds.add(0L);
        } else {
            for (Agent agent : agents) {
                agentIds.add(agent.getId());
            }
        }
        return agentIds;
    }

    @Override
    public List<BaseSelectEntity> getAgentList(Long companyId, Boolean isPersonalName) {
        if (GeneralTool.isEmpty(companyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("company_id_null"));
        }
        // Example example = new Example(Agent.class);
        // Example.Criteria criteria = example.createCriteria();

        // 有权限查看的国家
        // List<Long> countryIds = getCountryIds();
        // criteria.andIn("fkAreaCountryId", countryIds);

        // 有权限查看的公司下的代理
        // List<Long> agentIdList = getAgentIds(companyId);
        // criteria.andIn("id", agentIdList);
        // example.orderBy("name").asc();
        // List<Agent> agents = agentMapper.selectByExample(example);
        Integer value = 0;
        // List<CompanyConfigInfoDto> companySettlementConfigInfo =
        // permissionCenterClient.getCompanySettlementConfigInfo(ProjectKeyEnum.STUDENT_AGENT_BINDING_LIST_LIMIT.key);
        Map<Long, String> companyConfigMap = permissionCenterClient
                .getCompanyConfigMap(ProjectKeyEnum.STUDENT_AGENT_BINDING_LIST_LIMIT.key, 1).getData();
        value = Integer.valueOf(companyConfigMap.get(companyId));
        // List<CompanyConfigInfoDto> companyAgentLimit =
        // permissionCenterClient.getCompanySettlementConfigInfo(ProjectKeyEnum.STUDENT_AGENT_BINDING_LIST_LIMIT.key);
        // if (GeneralTool.isNotEmpty(companyAgentLimit)){
        // List<CompanyConfigInfoDto> vos = companyAgentLimit.stream().filter(e ->
        // e.getCompanyId().equals(companyId)).collect(Collectors.toList());
        // value = vos.get(0).getValue();
        // }

        List<Long> agentIdList = getAgentIds(companyId);
        // 获取业务下属
        Long staffId = SecureUtil.getStaffId();
        // 员工id + 业务下属员工ids
        List<Long> staffFollowerIds = permissionCenterClient.getStaffFollowerIds(staffId).getData();
        staffFollowerIds.add(staffId);
        List<Agent> agents = agentMapper.getAgentListNew(agentIdList, staffFollowerIds, value);
        // List<Agent> agents =
        // agentMapper.selectList(Wrappers.<Agent>lambdaQuery().in(Agent::getId,
        // agentIdList).eq(Agent::getIsActive, 1).orderByAsc(Agent::getName));
        Map<Long, String> nameNoteMap = agents.stream().collect(HashMap::new,
                (m, v) -> m.put(v.getId(), v.getNameNote()), HashMap::putAll);
        List<BaseSelectEntity> resoutList = agents.stream().map(agent -> {
            BaseSelectEntity baseSelectEntity = BeanCopyUtils.objClone(agent, BaseSelectEntity::new);
            if (GeneralTool.isNotEmpty(isPersonalName) && isPersonalName) {
                if (GeneralTool.isNotEmpty(agent.getNature())
                        && (String.valueOf(ProjectExtraEnum.AGENT_NATURE_PERSON.key).equals(agent.getNature())
                                || String.valueOf(ProjectExtraEnum.AGENT_NATURE_STUDIO.key).equals(agent.getNature())
                                || String.valueOf(ProjectExtraEnum.AGENT_NATURE_PERSONAL_ACCOUNT_COMPANY.key)
                                        .equals(agent.getNature()))) {
                    assert baseSelectEntity != null;
                    baseSelectEntity.setName(agent.getPersonalName());
                }
            }
            return baseSelectEntity;
        }).collect(Collectors.toList());
        for (BaseSelectEntity entity : resoutList) {
            if (GeneralTool.isNotEmpty(nameNoteMap) && GeneralTool.isNotEmpty(nameNoteMap.get(entity.getId()))) {
                entity.setName(
                        entity.getName() + "（" + nameNoteMap.get(entity.getId()) + "）" + "（" + entity.getNum() + "）");
            }
            entity.setFullName(nameNoteMap.get(entity.getId()));
        }
        return resoutList;
    }

    @Override
    public List<AgentListVo> getAgentListNew(Long companyId, Boolean isPersonalName) {
        List<Long> agentIdList = getAgentIds(companyId);
        List<Agent> agents = agentMapper.selectList(Wrappers.<Agent>lambdaQuery().in(Agent::getId, agentIdList)
                .eq(Agent::getIsActive, 1).orderByAsc(Agent::getName));
        Map<Long, String> nameNoteMap = agents.stream().collect(HashMap::new,
                (m, v) -> m.put(v.getId(), v.getNameNote()), HashMap::putAll);
        List<AgentListVo> resoutList = agents.stream().map(agent -> {
            AgentListVo baseSelectEntity = BeanCopyUtils.objClone(agent, AgentListVo::new);
            if (GeneralTool.isNotEmpty(isPersonalName) && isPersonalName) {
                if (GeneralTool.isNotEmpty(agent.getNature())
                        && (String.valueOf(ProjectExtraEnum.AGENT_NATURE_PERSON.key).equals(agent.getNature())
                                || String.valueOf(ProjectExtraEnum.AGENT_NATURE_STUDIO.key).equals(agent.getNature())
                                || String.valueOf(ProjectExtraEnum.AGENT_NATURE_PERSONAL_ACCOUNT_COMPANY.key)
                                        .equals(agent.getNature()))) {
                    assert baseSelectEntity != null;
                    baseSelectEntity.setName(agent.getPersonalName());
                }
            }
            return baseSelectEntity;
        }).collect(Collectors.toList());
        for (BaseSelectEntity entity : resoutList) {
            if (GeneralTool.isNotEmpty(nameNoteMap) && GeneralTool.isNotEmpty(nameNoteMap.get(entity.getId()))) {
                entity.setName(entity.getName() + "（" + nameNoteMap.get(entity.getId()) + "）");
            }
            entity.setFullName(nameNoteMap.get(entity.getId()));
        }
        return resoutList;
    }

    /**
     * 峰会参会人员编辑代理下拉框
     *
     * @param companyId
     * @return
     */
    @Override
    public List<BaseSelectEntity> getConventionAgentList(Long companyId) {
        List<Long> agentIdList = getAgentIds(companyId);
        List<Agent> agents = agentMapper.selectList(Wrappers.<Agent>lambdaQuery().in(Agent::getId, agentIdList)
                .eq(Agent::getIsActive, 1).orderByAsc(Agent::getName));
        Map<Long, String> nameNoteMap = agents.stream().collect(HashMap::new,
                (m, v) -> m.put(v.getId(), v.getNameNote()), HashMap::putAll);
        Map<Long, String> numMap = agents.stream().collect(HashMap::new, (m, v) -> m.put(v.getId(), v.getNum()),
                HashMap::putAll);
        List<BaseSelectEntity> resoutList = agents.stream()
                .map(agent -> BeanCopyUtils.objClone(agent, BaseSelectEntity::new)).collect(Collectors.toList());
        for (BaseSelectEntity entity : resoutList) {
            if (GeneralTool.isNotEmpty(nameNoteMap) && GeneralTool.isNotEmpty(nameNoteMap.get(entity.getId()))) {
                entity.setName(entity.getName() + "（" + nameNoteMap.get(entity.getId()) + "）");
            }
            if (GeneralTool.isNotEmpty(numMap) && GeneralTool.isNotEmpty(numMap.get(entity.getId()))) {
                entity.setName(entity.getName() + "（" + numMap.get(entity.getId()) + "）");
            }
            entity.setFullName(nameNoteMap.get(entity.getId()));
        }
        return resoutList;
    }

    @Override
    public Map<Long, Long> getAgentCompanyIdByIds(Set<Long> ids) {
        if (GeneralTool.isEmpty(ids)) {
            return null;
        }
        List<AgentCompany> agentCompanies = agentCompanyMapper
                .selectList(Wrappers.<AgentCompany>lambdaQuery().in(AgentCompany::getFkAgentId, ids));
        Map<Long, Long> map = agentCompanies.stream()
                .collect(Collectors.toMap(AgentCompany::getFkAgentId, AgentCompany::getFkCompanyId));
        return map;
    }

    // @Override
    // @Transactional(rollbackFor = Exception.class)
    // public void deleteAgent(Long id) {
    // //校验3个表是否有关联数据，否则不能删除代理
    // get_sale_center.r_student_agent、get_sale_center.m_student_offer、get_app_issue_center.m_student
    // List<Long> stuIds = studentAgentService.getRelationByAgentId(id);
    // if(GeneralTool.isNotEmpty(stuIds))
    // {
    // throw new GetServiceException("该代理下有关联学生数据，不能删除");
    // }
    // List<StudentOffer> studentOffers = studentOfferService.list(new
    // LambdaQueryWrapper<StudentOffer>().eq(StudentOffer::getFkAgentId,id));
    // if(GeneralTool.isNotEmpty(studentOffers))
    // {
    // throw new GetServiceException("该代理下有关联offer数据，不能删除");
    // }
    //// List<Long> ids = agentMapper.getIssueAgenInfo(id);
    //// if(GeneralTool.isNotEmpty(ids))
    //// {
    //// throw new GetServiceException("该代理下有绑定ISSUE用户数据，不能删除");
    //// }
    // //判断是否ISSUE有学生
    // List<Long> issueStulist = platformCenterClient.getIssueStuIdsByAgentId(id);
    // if(GeneralTool.isNotEmpty(issueStulist))
    // {
    // throw new GetServiceException("该代理下有绑定ISSUE学生数据，不能删除");
    // }
    // //删除ISSUE绑定关系
    // Result<Boolean> result =
    // platformCenterClient.removeIssueRelationByAgentId(id);
    // if(result.isSuccess() && result.getData())
    // {
    // //删除公司代理关系表
    // companyService.remove(Wrappers.<AgentCompany>query().lambda().eq(AgentCompany::getFkAgentId,
    // id));
    // //删除空代理
    // this.delete(id);
    // }
    // }

    @Override
    public Boolean validateCustomerChannelRequired() {
        Integer flag = 0;
        Long fkCompanyId = SecureUtil.getFkCompanyId();
        Map<Long, String> companyConfigMap = permissionCenterClient
                .getCompanyConfigMap(ProjectKeyEnum.AGENT_INFO_OPT_LIMIT.key, 3).getData();
        return companyConfigMap.get(fkCompanyId).equals("1");

        // ConfigVo configDto =
        // permissionCenterClient.getConfigByKey(ProjectKeyEnum.AGENT_INFO_OPT_LIMIT.key).getData();
        // if (GeneralTool.isNotEmpty(configDto) &&
        // GeneralTool.isNotEmpty(configDto.getValue3())){
        // JSONObject jsonObject = JSONObject.parseObject(configDto.getValue3());
        //
        // if (fkCompanyId.equals(3L)){
        // flag = jsonObject.getInteger("IAE");
        // }else {
        // flag = jsonObject.getInteger("OTHER");
        // }
        // return flag == 1;
        // }
        // return false;
    }

    @Override
    public List<BaseSelectEntity> getCustomerChannelAgentSelect(Long companyId) {
        List<Long> agentIdList = getAgentIds(companyId);
        if (GeneralTool.isEmpty(agentIdList)) {
            return Collections.emptyList();
        }
        List<Agent> agents = agentMapper.selectList(Wrappers.lambdaQuery(Agent.class)
                .in(Agent::getId, agentIdList)
                .eq(Agent::getIsActive, true)
        // .eq(Agent::getIsCustomerChannel,true)
        );

        // 代理对应的bd
        Map<Long, List<Long>> bdIdsMap = agentStaffService.getBdIdByAgentIds(Sets.newHashSet(agentIdList));
        Map<Long, String> staffNamesMap = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(bdIdsMap)) {
            Set<Long> bdIds = bdIdsMap.values().stream()
                    .flatMap(List::stream)
                    .collect(Collectors.toSet());

            if (GeneralTool.isNotEmpty(bdIds)) {
                List<StaffVo> staffVos = permissionCenterClient.getStaffDtoByIds(bdIds);
                if (GeneralTool.isNotEmpty(staffVos)) {
                    staffNamesMap = staffVos.stream()
                            .collect(Collectors.toMap(StaffVo::getId, s -> Stream.of(s.getName(), s.getNameEn())
                                    .filter(Objects::nonNull)
                                    .collect(Collectors.joining(" "))));
                }
            }
        }

        Map<Long, String> nameNoteMap = agents.stream().collect(HashMap::new,
                (m, v) -> m.put(v.getId(), v.getNameNote()), HashMap::putAll);
        Map<Long, String> finalStaffNamesMap = staffNamesMap;
        List<BaseSelectEntity> baseSelectEntities = BeanCopyUtils.copyListProperties(agents, BaseSelectEntity::new,
                (agent, entity) -> {
                    if (GeneralTool.isNotEmpty(nameNoteMap)
                            && GeneralTool.isNotEmpty(nameNoteMap.get(entity.getId()))) {
                        entity.setName(entity.getName() + "（" + nameNoteMap.get(entity.getId()) + "）" + "（"
                                + entity.getNum() + "）");
                    }
                    if (GeneralTool.isNotEmpty(bdIdsMap) && GeneralTool.isNotEmpty(bdIdsMap.get(entity.getId()))) {
                        List<Long> bdIds = bdIdsMap.get(entity.getId());
                        if (GeneralTool.isNotEmpty(bdIds)) {
                            Long bdId = bdIds.get(0);
                            if (GeneralTool.isNotEmpty(finalStaffNamesMap)
                                    && GeneralTool.isNotEmpty(finalStaffNamesMap.get(bdId))) {
                                String bdName = finalStaffNamesMap.get(bdId);
                                entity.setName(entity.getName() + "（" + bdName + "）");
                            }
                        }
                    }
                    entity.setFullName(nameNoteMap.get(entity.getId()));
                });
        baseSelectEntities = baseSelectEntities.stream().sorted(Comparator.comparing(BaseSelectEntity::getName))
                .collect(Collectors.toList());
        return baseSelectEntities;
    }

    @Override
    public List<AgentVo> getAgentListAll() {
        // 公司
        List<Long> companyIds = new ArrayList<>();
        companyIds.addAll(SecureUtil.getCompanyIds());

        // 获取业务下属
        Long staffId = SecureUtil.getStaffId();
        // 员工id + 业务下属员工ids
        List<Long> staffFollowerIds = permissionCenterClient.getStaffFollowerIds(staffId).getData();
        staffFollowerIds.add(staffId);
        List<AgentVo> agents = agentMapper.getAgentListAll(companyIds, staffFollowerIds);
        return agents;
    }

    @Override
    public List<BaseSelectEntity> getAgentSelect(String name) {
        if (GeneralTool.isEmpty(name)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        LambdaQueryWrapper<Agent> queryWrapper = new LambdaQueryWrapper<Agent>()
                .and(wrapper -> wrapper.like(Agent::getName, name).or()
                        .like(Agent::getNameNote, name))
                .last("limit 100");
        List<Agent> agents = agentMapper.selectList(queryWrapper);
        return Optional.ofNullable(agents.stream().map(e -> {
            BaseSelectEntity baseSelect = new BaseSelectEntity();
            baseSelect.setId(e.getId());
            if (GeneralTool.isNotEmpty(e.getNameNote())) {
                baseSelect.setFullName(e.getName() + "（" + e.getNameNote() + "）");
            } else {
                baseSelect.setFullName(e.getName());
            }
            return baseSelect;
        }).collect(Collectors.toList())).orElse(null);
    }

    /**
     * 当前登陆人可查看的所有的并且为生效状态的代理ids
     *
     * @Date 11:13 2022/1/10
     * <AUTHOR>
     */
    public List<Long> getAgentList() {
        List<Long> agentIdList = studentAgentService.getAgentIdsIsActive();
        if (GeneralTool.isEmpty(agentIdList)) {
            agentIdList = new ArrayList<>();
            agentIdList.add(0L);
        }
        // 获取员工以及旗下员工所创建的代理ids
        Long staffId = SecureUtil.getStaffId();
        List<Long> staffFollowerIds = new ArrayList<>();
        Result<List<Long>> result = permissionCenterClient.getStaffFollowerIds(staffId);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            staffFollowerIds.addAll(result.getData());
        }
        staffFollowerIds.add(staffId);
        Set<Long> ids = new HashSet<>(staffFollowerIds);
        Result<Map<Long, String>> staffLoginIdResult = permissionCenterClient.getStaffLoginIdByIds(ids);
        if (staffLoginIdResult.isSuccess() && GeneralTool.isNotEmpty(staffLoginIdResult.getData())) {
            Map<Long, String> longIdMap = staffLoginIdResult.getData();
            List<String> userNames = new ArrayList<>(longIdMap.values());
            List<Agent> agentList = agentMapper
                    .selectList(Wrappers.<Agent>query().lambda().in(Agent::getGmtCreateUser, userNames));
            agentIdList.addAll(agentList.stream().map(Agent::getId).distinct().collect(Collectors.toList()));
        }
        return agentIdList.stream().distinct().collect(Collectors.toList());
    }

    @Override
    public void editAgentCompany(List<AgentCompanyDto> validList) {
        companyService.editAgentCompany(validList);
    }

    @Override
    public List<CompanyTreeVo> getAgentCompanyRelation(Long agentId) {
        return companyRelationService.getAgentCompanyRelation(agentId);
    }

    @Override
    public List<MediaAndAttachedVo> getAgentMedia(MediaAndAttachedDto attachedVo, Page page) {
        if (GeneralTool.isEmpty(attachedVo.getFkTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        // verifyDataPermissionsUtils.verifyByBusinessId(attachedVo.getFkTableId(),VerifyDataPermissionsUtils.AGENT_O);
        attachedVo.setFkTableName(TableEnum.SALE_AGENT.key);
        return attachedService.getMediaAndAttachedDto(attachedVo, page, FileTypeEnum.SALE_AGENT_FILE.key,
                FileTypeEnum.BUSINESS_LICENSE.key, FileTypeEnum.SALE_AGENT_ID_CARD_FRONT.key,
                FileTypeEnum.SALE_AGENT_ID_CARD_BACK.key, FileTypeEnum.APP_AGENT_CONTRACT_ACCOUNT_STATEMENT_FILE.key);
    }

    @Override
    public ResponseBo<BusinessLicenseResultDto> analysisOfBusinessLicense(MultipartFile file) {
        if (Objects.isNull(file)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_file_null"));
        }
        JSONObject jsonObject = HttpUtils.sendFile(blUrl, file);
        if (Objects.isNull(jsonObject)) {
            int i = 0;
            while (jsonObject == null && i <= 3) {
                jsonObject = HttpUtils.sendFile(blUrl, file);
                i++;
            }
        }
        BusinessLicenseResultDto licenseResultVo = new BusinessLicenseResultDto();
        try {
            JSONObject wordsResult = jsonObject.getJSONObject("data").getJSONObject("words_result");
            JSONObject p1 = wordsResult.getJSONObject("法人");
            if (!Objects.isNull(p1)) {
                Object words = p1.get("words");
                if (words != null) {
                    licenseResultVo.setCorporateLegalPerson(String.valueOf(words));
                }
            }
            JSONObject p2 = wordsResult.getJSONObject("单位名称");
            if (!Objects.isNull(p2)) {
                Object words = p2.get("words");
                if (words != null) {
                    licenseResultVo.setCorporateName(String.valueOf(words));
                }
            }
            JSONObject p3 = wordsResult.getJSONObject("社会信用代码");
            if (!Objects.isNull(p3)) {
                Object words = p3.get("words");
                if (words != null) {
                    licenseResultVo.setTaxNumber(String.valueOf(words));
                }
            }
            JSONObject p4 = wordsResult.getJSONObject("地址");
            if (!Objects.isNull(p4)) {
                Object words = p4.get("words");
                if (words != null) {
                    licenseResultVo.setAddress(String.valueOf(words));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("解析失败返回的对象：" + GeneralTool.toJson(jsonObject));
            throw new GetServiceException(LocaleMessageUtils.getMessage("analysis_failed"));
        }
        return new ResponseBo<>(licenseResultVo);
    }

    /**
     * Author Cream
     * Description : //解析代理身份证
     * Date 2022/11/25 11:57
     * Params:
     * Return
     */
    @Override
    public ResponseBo<String> verifyIdCard(MultipartFile file, Integer type) {
        if (Objects.isNull(file)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_file_null"));
        }
        if (Objects.isNull(type)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("type_is_null"));
        }
        String cardType = ProjectExtraEnum.getInitialValueByKey(type, ProjectExtraEnum.idCartFB);
        if (StringUtils.isBlank(cardType)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("illegal_type"));
        }
        String url = idCardUrl + cardType;
        JSONObject jsonObject = HttpUtils.sendFile(url, file);
        if (Objects.isNull(jsonObject)) {
            int i = 0;
            while (jsonObject == null && i <= 3) {
                jsonObject = HttpUtils.sendFile(url, file);
                i++;
            }
        }
        try {
            if ("success".equals(String.valueOf(jsonObject.get("result")))) {
                JSONObject wordsResult = jsonObject.getJSONObject("data").getJSONObject("words_result");
                if (wordsResult.isEmpty()) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("invalid_id_card"));
                }
                if (0 == type) {
                    if (wordsResult.size() == 6 && GeneralTool.isNotEmpty(wordsResult.getJSONObject("姓名"))
                            && GeneralTool.isNotEmpty(wordsResult.getJSONObject("民族"))
                            && GeneralTool.isNotEmpty(wordsResult.getJSONObject("住址"))
                            && GeneralTool.isNotEmpty(wordsResult.getJSONObject("公民身份号码"))
                            && GeneralTool.isNotEmpty(wordsResult.getJSONObject("出生"))
                            && GeneralTool.isNotEmpty(wordsResult.getJSONObject("性别"))) {
                        JSONObject p1 = wordsResult.getJSONObject("公民身份号码");
                        if (!Objects.isNull(p1)) {
                            String words = String.valueOf(p1.get("words"));
                            if (StringUtils.isNotBlank(words)) {
                                return new ResponseBo<>(words);
                            }
                        }
                    }
                } else if (1 == type) {
                    if (wordsResult.size() == 3 && GeneralTool.isNotEmpty(wordsResult.getJSONObject("签发机关"))
                            && GeneralTool.isNotEmpty(wordsResult.getJSONObject("签发日期"))
                            && GeneralTool.isNotEmpty(wordsResult.getJSONObject("失效日期"))) {
                        return new ResponseBo<>();
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("解析失败返回的对象：" + GeneralTool.toJson(jsonObject));
            throw new GetServiceException(LocaleMessageUtils.getMessage("invalid_id_card"));
        }
        throw new GetServiceException(LocaleMessageUtils.getMessage("invalid_id_card"));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<MediaAndAttachedVo> addAgentMedia(List<MediaAndAttachedDto> mediaAttachedVos) {
        if (GeneralTool.isEmpty(mediaAttachedVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        List<MediaAndAttachedVo> mediaAndAttachedVos = new ArrayList<>();
        Long agentId = mediaAttachedVos.get(0).getFkTableId();
        Agent agent = agentMapper.selectById(agentId);
        for (MediaAndAttachedDto mediaAndAttachedDto : mediaAttachedVos) {
            if (TableEnum.BUSINESS_LICENSE.key.equals(mediaAndAttachedDto.getTypeKey()) &&
                    agent != null && !"1".equals(agent.getNature())) {
                throw new GetServiceException(
                        LocaleMessageUtils.getMessage("non_corporate_agents_cannot_upload_their_business_license"));
            }
            // 设置插入的表
            mediaAndAttachedDto.setFkTableName(TableEnum.SALE_AGENT.key);
            mediaAndAttachedVos.add(attachedService.addMediaAndAttached(mediaAndAttachedDto));
        }
        return mediaAndAttachedVos;
    }

    @Override
    public List<ContactPersonVo> getAgentContactPersonDtos(ContactPersonDto contactPersonVo, Page page) {
        if (GeneralTool.isEmpty(contactPersonVo.getFkTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        contactPersonVo.setFkTableName(TableEnum.SALE_AGENT.key);
        contactPersonVo.setIsContractContact(false);
        return personService.getContactPersonDtos(contactPersonVo, page);
    }

    @Override
    public Long addAgentContactPerson(ContactPersonDto contactPersonVo) {
        // 校验联系人添加
        this.validateAddContractPerson(contactPersonVo);
        // if (StringUtils.isBlank(contactPersonVo.getFkContactPersonTypeKey())){
        // contactPersonVo.setFkContactPersonTypeKey("CONTACT_AGENT_SALES");
        // }
        contactPersonVo.setFkTableName(TableEnum.SALE_AGENT.key);
        return personService.addAgentContactPerson(contactPersonVo);
    }

    /**
     * 校验联系人类型添加
     *
     * @param contactPersonVo
     */
    @Override
    public void validateAddContractPerson(ContactPersonDto contactPersonVo) {
        if (GeneralTool.isEmpty(contactPersonVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }

        // 1. 校验联系人类型是否已存在
        validateExistingContactPersonType(contactPersonVo);

        // 2. 校验紧急联系人与其他特殊类型联系人的冲突
        validateEmergencyContactConflicts(contactPersonVo);
    }

    /**
     * 校验联系人类型是否已存在
     *
     * @param contactPersonVo 联系人DTO
     */
    private void validateExistingContactPersonType(ContactPersonDto contactPersonVo) {
        // 获取表关联的ID和表名
        Long fkTableId = contactPersonVo.getFkTableId();
        String fkTableName = contactPersonVo.getFkTableName();

        if (fkTableId == null || StringUtils.isBlank(fkTableName)) {
            return;
        }

        // 获取当前联系人的新联系人类型
        Set<String> currentNewTypes = parseContactPersonTypes(contactPersonVo.getFkContactPersonTypeKey());
        if (CollectionUtil.isEmpty(currentNewTypes)) {
            return;
        }

        // 获取表关联已使用的新联系人类型
        // 如果是编辑操作（ID不为空），则排除当前联系人ID
        Set<String> usedNewTypeKeys;
        if (contactPersonVo.getId() != null) {
            // 编辑操作：排除当前联系人ID，避免和自己冲突
            usedNewTypeKeys = personService.getUsedNewTypeKeysByTableId(fkTableId, contactPersonVo.getId());
        } else {
            // 新增操作：查询所有已使用的类型
            usedNewTypeKeys = personService.getUsedNewTypeKeysByTableId(fkTableId);
        }

        // 检查是否有重复的新类型
        Set<String> duplicateTypes = new HashSet<>(currentNewTypes);
        duplicateTypes.retainAll(usedNewTypeKeys);

        if (CollectionUtil.isNotEmpty(duplicateTypes)) {
            // 获取第一个重复类型的名称，用于错误提示
            String duplicateType = duplicateTypes.iterator().next();
            ContactPersonTypeEnum typeEnum = ContactPersonTypeEnum.getContactPersonTypeByCode(duplicateType);
            String typeName = typeEnum != null ? typeEnum.getMsg() : duplicateType;

            throw new GetServiceException(LocaleMessageUtils.getFormatMessage("contact_person_type_exists", typeName));
        }
    }

    /**
     * 校验紧急联系人与其他特殊类型联系人的冲突
     *
     * @param contactPersonVo 联系人DTO
     */
    private void validateEmergencyContactConflicts(ContactPersonDto contactPersonVo) {
        // 获取联系人类型集合
        Set<String> typeSet = parseContactPersonTypes(contactPersonVo.getFkContactPersonTypeKey());

        // 校验单个联系人内部的类型冲突
        if (ContactPersonTypeEnum.hasConflictingNewTypes(typeSet)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("contact_person_type_conflict"));
        }

        // 检查是否包含需要校验的特殊类型
        boolean hasEmergency = typeSet.contains(ContactPersonTypeEnum.EMERGENCY.getCode());
        boolean hasAdmin = typeSet.contains(ContactPersonTypeEnum.ADMIN.getCode());
        boolean hasCommission = typeSet.contains(ContactPersonTypeEnum.COMMISSION.getCode());
        
        // 如果没有任何特殊类型，跳过校验
        if (!hasEmergency && !hasAdmin && !hasCommission) {
            return;
        }

        // 获取表关联的所有联系人
        List<SaleContactPerson> contactPersons = personService.getContactPersonByFkTableId(
                contactPersonVo.getFkTableName(), contactPersonVo.getFkTableId());

        if (CollectionUtil.isEmpty(contactPersons)) {
            return;
        }

        // 双向校验冲突
        validateCrossTypeConflicts(contactPersonVo, contactPersons, hasEmergency, hasAdmin, hasCommission);
    }

    /**
     * 交叉类型冲突校验（双向校验）
     *
     * @param currentContact 当前联系人
     * @param existingContacts 现有联系人列表
     * @param hasEmergency 当前联系人是否包含紧急联系人类型
     * @param hasAdmin 当前联系人是否包含企业管理员类型
     * @param hasCommission 当前联系人是否包含佣金结算负责人类型
     */
    private void validateCrossTypeConflicts(ContactPersonDto currentContact, 
                                           List<SaleContactPerson> existingContacts,
                                           boolean hasEmergency, boolean hasAdmin, boolean hasCommission) {
        for (SaleContactPerson existing : existingContacts) {
            // 跳过自己
            if (currentContact.getId() != null && currentContact.getId().equals(existing.getId())) {
                continue;
            }
            
            Set<String> existingTypes = parseContactPersonTypes(existing.getFkContactPersonTypeKey());
            boolean existingHasEmergency = existingTypes.contains(ContactPersonTypeEnum.EMERGENCY.getCode());
            boolean existingHasSpecial = existingTypes.contains(ContactPersonTypeEnum.ADMIN.getCode()) 
                                      || existingTypes.contains(ContactPersonTypeEnum.COMMISSION.getCode());
            
            // 双向冲突检查：只有是同一个人时才报错
            if (isSamePerson(currentContact, existing)) {
                // 情况1：当前是EMERGENCY，现有是ADMIN/COMMISSION
                if (hasEmergency && existingHasSpecial) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("agent_emergency_contact_duplicate"));
                }
                // 情况2：当前是ADMIN/COMMISSION，现有是EMERGENCY  
                if ((hasAdmin || hasCommission) && existingHasEmergency) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("agent_emergency_contact_duplicate"));
                }
            }
        }
    }

    /**
     * 判断两个联系人是否是同一个人（通过姓名和电子邮箱判断）
     *
     * @param person1 联系人1
     * @param person2 联系人2
     * @return 是否是同一个人
     */
    private boolean isSamePerson(ContactPersonDto person1, SaleContactPerson person2) {
        // 姓名或邮箱任一相同则认为是同一人（避免角色重复）
        
        // 检查姓名是否相同
        boolean nameMatches = StringUtils.isNotBlank(person1.getName()) && StringUtils.isNotBlank(person2.getName())
                && person1.getName().equals(person2.getName());
        
        // 检查邮箱是否相同
        boolean emailMatches = StringUtils.isNotBlank(person1.getEmail()) && StringUtils.isNotBlank(person2.getEmail())
                && person1.getEmail().equals(person2.getEmail());
        
        // 姓名或邮箱任一相同即认为是同一人
        return nameMatches || emailMatches;
    }

    /**
     * 解析联系人类型字符串为类型集合
     *
     * @param typeKeyString 类型字符串，格式为"类型1,类型2,类型3"
     * @return 类型集合
     */
    private Set<String> parseContactPersonTypes(String typeKeyString) {
        if (StringUtils.isBlank(typeKeyString)) {
            return Collections.emptySet();
        }
        return Arrays.stream(typeKeyString.split(","))
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
    }

    @Override
    public List<AgentContractVo> getAgentContractDtos(AgentContractQueryDto contractVo, Page page) {
        if (GeneralTool.isEmpty(contractVo.getFkAgentId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("agent_id_null"));
        }
        return agentContractService.getAgentContractDtos(contractVo, page);
    }

    @Override
    public Long addAgentContract(AgentContractDto contractVo) {
        return agentContractService.addAgentContract(contractVo);
    }

    @Override
    public Long addApprovedAgentContract(AgentContractDto contractVo) {
        return agentContractService.addApprovedAgentContract(contractVo);
    }

    @Override
    public List<Long> getAgentFollowerIds(Long agentId) {
        if (GeneralTool.isEmpty(agentId)) {
            return null;
        }
        return agentMapper.getAgentFollowerIds(agentId);
    }

    @Override
    public List<Long> getAgentFollowerIdsByIds(String agentIds) {
        if (GeneralTool.isEmpty(agentIds)) {
            return null;
        }
        return agentMapper.getAgentFollowerIdsByIds(agentIds);
    }

    /**
     * 查询下一级非结算口代理ids
     *
     * @Date 12:32 2021/8/12
     * <AUTHOR>
     */
    @Override
    public List<Long> getSubordinateAgentIds(List<Long> agentIds) {
        List<Long> subordinateAgentIds = agentMapper.getSubordinateNotPortAgentIds(agentIds);
        if (GeneralTool.isNotEmpty(subordinateAgentIds)) {
            subordinateAgentIds.addAll(getSubordinateAgentIds(subordinateAgentIds));
        }
        return subordinateAgentIds.stream().distinct().collect(Collectors.toList());
    }

    @Override
    public String getNameById(Long agentId) {
        if (GeneralTool.isEmpty(agentId)) {
            return null;
        }
        Agent agent = agentMapper.selectById(agentId);
        if (GeneralTool.isEmpty(agent)) {
            return null;
        }
        StringBuilder sb = new StringBuilder(agent.getName());
        if (GeneralTool.isNotEmpty(agent.getNameNote())) {
            sb.append("(").append(agent.getNameNote()).append(")");
        }
        return sb.toString();
    }

    @Override
    public List<Long> getAgentListIds(String name) {
        if (GeneralTool.isEmpty(name)) {
            return null;
        }
        // Example example = new Example(Agent.class);
        // Example.Criteria criteria = example.createCriteria();
        // criteria.andLike("name", "%" + name + "%");
        // List<Agent> agents = agentMapper.selectByExample(example);

        List<Agent> agents = agentMapper.selectList(Wrappers.<Agent>lambdaQuery().like(Agent::getName, name));

        return agents.stream().map(Agent::getId).collect(Collectors.toList());
    }

    /**
     * 获取代理
     *
     * @param agentIds
     * @return
     */
    @Override
    public Map<Long, Agent> getAgentsByIds(Set<Long> agentIds) {
        Map<Long, Agent> map = new HashMap<>();
        if (GeneralTool.isEmpty(agentIds)) {
            return map;
        }
        LambdaQueryWrapper<Agent> agentLambdaQueryWrapper = Wrappers.<Agent>lambdaQuery();
        if (GeneralTool.isNotEmpty(agentIds) && agentIds.size() < 10000) {
            agentLambdaQueryWrapper.in(Agent::getId, agentIds);
        }
        List<Agent> agents = agentMapper.selectList(agentLambdaQueryWrapper);
        if (GeneralTool.isNotEmpty(agents)) {
            return agents.stream().collect(Collectors.toMap(Agent::getId, agent -> agent));
        }
        return map;
    }

    @Override
    public Map<Long, String> getAgentNamesByIds(Set<Long> agentIds) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(agentIds)) {
            return map;
        }
        // Example example = new Example(Agent.class);
        // example.createCriteria().andIn("id", agentIds);
        // List<Agent> agents = agentMapper.selectByExample(example);

        List<Agent> agents = agentMapper.selectBatchIds(agentIds);
        if (GeneralTool.isEmpty(agents)) {
            return map;
        }
        for (Agent agent : agents) {
            if (GeneralTool.isNotEmpty(agent.getNameNote())) {
                map.put(agent.getId(), agent.getName() + "（" + agent.getNameNote() + "）");
            } else {
                map.put(agent.getId(), agent.getName());
            }

        }
        return map;
    }

    // private boolean validateAdd(AgentDto agentVo) {
    //// Example example = new Example(Agent.class);
    //// Example.Criteria criteria = example.createCriteria();
    //// criteria.andEqualTo("num", agentDto.getNum());
    //// List<Agent> list = this.agentMapper.selectByExample(example);
    //
    // List<Agent> agents =
    // agentMapper.selectList(Wrappers.<Agent>lambdaQuery().eq(Agent::getNum,agentDto.getNum()));
    // return GeneralTool.isEmpty(agents);
    // }

    private List<Agent> validateAdd(AgentDto agentDto) {
        LambdaQueryWrapper<Agent> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.or().eq(Agent::getNum, agentDto.getNum());
        List<Agent> list = this.agentMapper.selectList(lambdaQueryWrapper);
        return list;
    }

    private boolean validateUpdate(Agent agent) {
        // Example example = new Example(Agent.class);
        // Example.Criteria criteria = example.createCriteria();
        // criteria.andEqualTo("num", agent.getNum());
        // List<Agent> list = this.agentMapper.selectByExample(example);

        List<Agent> agents = agentMapper.selectList(Wrappers.<Agent>lambdaQuery().eq(Agent::getNum, agent.getNum()));
        if (agents.size() <= 0 || agents.get(0).getId().equals(agent.getId())) {
            List<Agent> list1 = this.agentMapper
                    .selectList(Wrappers.<Agent>lambdaQuery().eq(Agent::getName, agent.getName()));
            return list1.size() <= 0 || list1.get(0).getId().equals(agent.getId());
        } else {
            return false;
        }
    }

    /**
     * 当前登陆人可查看的所有的代理ids
     *
     * @Date 11:08 2022/1/10
     * <AUTHOR>
     */
    @Override
    public List<Long> getAgentIdList() {
        List<Long> agentIdList = studentAgentService.getAgentIds();
        if (GeneralTool.isEmpty(agentIdList)) {
            agentIdList = new ArrayList<>();
            agentIdList.add(0L);
        }
        // 获取员工以及旗下员工所创建的代理ids
        Long staffId = SecureUtil.getStaffId();
        List<Long> staffFollowerIds = new ArrayList<>();
        Result<List<Long>> result = permissionCenterClient.getStaffFollowerIds(staffId);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            staffFollowerIds.addAll(result.getData());
        }
        staffFollowerIds.add(staffId);
        Set<Long> ids = new HashSet<>(staffFollowerIds);
        Map<Long, String> longIdMap = new HashMap<>();
        Result<Map<Long, String>> staffLoginIdResult = permissionCenterClient.getStaffLoginIdByIds(ids);
        if (staffLoginIdResult.isSuccess() && GeneralTool.isNotEmpty(staffLoginIdResult.getData())) {
            longIdMap = staffLoginIdResult.getData();
            List<String> userNames = new ArrayList<>(longIdMap.values());
            List<Agent> agentList = agentMapper
                    .selectList(Wrappers.<Agent>lambdaQuery().in(Agent::getGmtCreateUser, userNames));
            agentIdList.addAll(agentList.stream().map(Agent::getId).distinct().collect(Collectors.toList()));
        }
        return agentIdList;
    }

    /**
     * feign财务佣金汇总列表
     *
     * @return
     * @Date 16:08 2021/12/24
     * <AUTHOR>
     */
    @Override
    public CommissionSummaryPageVo commissionSummary(CommissionSummaryDto commissionSummaryDto, Page page) {
        CommissionSummaryPageVo commissionSummaryPageVo = new CommissionSummaryPageVo();
        if (GeneralTool.isNotEmpty(commissionSummaryDto.getFkCompanyId())) {
            if (!SecureUtil.validateCompany(commissionSummaryDto.getFkCompanyId())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
            }
        }
        if (GeneralTool.isNotEmpty(commissionSummaryDto.getFkCompanyIds())) {
            if (!SecureUtil.validateCompanys(commissionSummaryDto.getFkCompanyIds())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
            }
        }

        // 先根据Vo条件筛选一遍数据，获取筛选后的代理对象
        List<CommissionSummarySecondaryScreeningDto> commissionSummarySecondaryScreeningDtoList = agentMapper
                .getAgentBycommissionSummaryVo(commissionSummaryDto);
        if (GeneralTool.isEmpty(commissionSummarySecondaryScreeningDtoList)) {
            page.setTotalResult(0);
            return null;
        }
        IPage<CommissionSummaryVo> ipage = GetCondition
                .getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<CommissionSummaryVo> commissionSummaryVos = agentMapper.commissionSummary(ipage, commissionSummaryDto,
                commissionSummarySecondaryScreeningDtoList);
        page.setAll((int) ipage.getTotal());

        Set<String> currencyNum = new HashSet<>();
        if (GeneralTool.isNotEmpty(commissionSummaryVos)) {
            Set<String> accountCurrencyNum = commissionSummaryVos.stream()
                    .map(CommissionSummaryVo::getAccountCurrencyNum).collect(Collectors.toSet());
            Set<String> planCurrencyNum = commissionSummaryVos.stream().map(CommissionSummaryVo::getPlanCurrencyNum)
                    .collect(Collectors.toSet());
            currencyNum.addAll(accountCurrencyNum);
            currencyNum.addAll(planCurrencyNum);
        }

        Map<String, String> currencyNamesByNums = new HashMap<>();
        Result<Map<String, String>> result = financeCenterClient.getCurrencyNamesByNums(currencyNum);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            currencyNamesByNums = result.getData();
        }

        for (CommissionSummaryVo commissionSummaryVo : commissionSummaryVos) {
            commissionSummaryVo.setFkTypeKeyName(
                    TableEnum.getValueByKey(commissionSummaryVo.getFkTypeKey(), TableEnum.COMMISSION_SETTLEMENT_TYPE));
            commissionSummaryVo
                    .setAccountCurrencyNumName(currencyNamesByNums.get(commissionSummaryVo.getAccountCurrencyNum()));
        }
        commissionSummaryPageVo.setCommissionSummaryDtoList(commissionSummaryVos);
        commissionSummaryPageVo.setPage(page);
        return commissionSummaryPageVo;
    }

    // /**
    // * 财务佣金汇总批次子项列表
    // *
    // * @Date 15:42 2021/12/24
    // * <AUTHOR>
    // */
    // @Override
    // public List<CommissionSummaryBatchItemVo>
    // commissionSummaryForIfile(CommissionSummaryBatchDto
    // commissionSummaryBatchDto) {
    // if (!SecureUtil.validateCompany(commissionSummaryBatchDto.getFkCompanyId()))
    // {
    // throw new
    // GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
    // }
    // List<CommissionSummaryBatchItemVo> commissionSummaryBatchItemList =
    // agentMapper.commissionSummaryBatchItemList(commissionSummaryBatchDto);
    // List<PayablePlanSettlementBatchExchange> payablePlanSettlementBatchExchanges
    // =
    // payablePlanSettlementBatchExchangeMapper.selectList(Wrappers.<PayablePlanSettlementBatchExchange>query().lambda().eq(PayablePlanSettlementBatchExchange::getNumSettlementBatch,
    // commissionSummaryBatchDto.getNumSettlementBatch()));
    //
    // //人民币结算方式专用：港币兑人民币汇率
    // BigDecimal hkdExchangeRmbRate = null;
    // BigDecimal hkdExchangeRmbServiceFee = null;
    // //人民币结算方式专用Map：各币种兑香港汇率
    // Map<String, BigDecimal> hkdRateMap = new HashMap<>();
    // for (PayablePlanSettlementBatchExchange payablePlanSettlementBatchExchange :
    // payablePlanSettlementBatchExchanges) {
    // if
    // ("HKD".equals(payablePlanSettlementBatchExchange.getFkCurrencyTypeNumExchange()))
    // {
    // hkdRateMap.put(payablePlanSettlementBatchExchange.getFkCurrencyTypeNum(),
    // payablePlanSettlementBatchExchange.getExchangeRate());
    // }
    // if
    // ("CNY".equals(payablePlanSettlementBatchExchange.getFkCurrencyTypeNumExchange())
    // && "HKD".equals(payablePlanSettlementBatchExchange.getFkCurrencyTypeNum())) {
    // hkdExchangeRmbRate = payablePlanSettlementBatchExchange.getExchangeRate();
    // hkdExchangeRmbServiceFee =
    // payablePlanSettlementBatchExchange.getServiceFee();
    // }
    // }
    // for (CommissionSummaryBatchItemVo commissionSummaryBatchItemVo :
    // commissionSummaryBatchItemList) {
    // //人民币结算
    // if ("CNY".equals(commissionSummaryBatchItemVo.getAccountCurrencyNum())) {
    // if (GeneralTool.isNotEmpty(hkdRateMap)) {
    // commissionSummaryBatchItemVo.setExchangeHkdRate(hkdRateMap.get(commissionSummaryBatchItemVo.getPlanCurrencyNum()));
    // commissionSummaryBatchItemVo.setExchangeHkdAmount(commissionSummaryBatchItemVo.getPayableAmount().multiply(hkdRateMap.get(commissionSummaryBatchItemVo.getPlanCurrencyNum())));
    // commissionSummaryBatchItemVo.setServiceFee(hkdExchangeRmbServiceFee);
    // }
    // if (GeneralTool.isNotEmpty(hkdExchangeRmbRate)) {
    // commissionSummaryBatchItemVo.setHkdExchangeRmbRate(hkdExchangeRmbRate);
    // commissionSummaryBatchItemVo.setHkdExchangeRmbAmount(commissionSummaryBatchItemVo.getExchangeHkdAmount().multiply(hkdExchangeRmbRate));
    // }
    // } else {
    // //外币结算
    // payablePlanSettlementBatchExchanges =
    // payablePlanSettlementBatchExchangeMapper.selectList(Wrappers.<PayablePlanSettlementBatchExchange>query().lambda()
    // .eq(PayablePlanSettlementBatchExchange::getNumSettlementBatch,
    // commissionSummaryBatchDto.getNumSettlementBatch())
    // .eq(PayablePlanSettlementBatchExchange::getFkAgentId,
    // commissionSummaryBatchItemVo.getId())
    // .eq(PayablePlanSettlementBatchExchange::getFkTypeKey,
    // commissionSummaryBatchItemVo.getFkTypeKey())
    // .eq(PayablePlanSettlementBatchExchange::getFkCurrencyTypeNum,
    // commissionSummaryBatchItemVo.getPlanCurrencyNum()));
    // if (GeneralTool.isNotEmpty(payablePlanSettlementBatchExchanges)) {
    // PayablePlanSettlementBatchExchange payablePlanSettlementBatchExchange =
    // payablePlanSettlementBatchExchanges.get(0);
    // commissionSummaryBatchItemVo.setExchangeCurrencyNum(payablePlanSettlementBatchExchange.getFkCurrencyTypeNumExchange());
    // commissionSummaryBatchItemVo.setExchangeRate(payablePlanSettlementBatchExchange.getExchangeRate());
    // commissionSummaryBatchItemVo.setExchangeAmount(commissionSummaryBatchItemVo.getPayableAmount().multiply(payablePlanSettlementBatchExchange.getExchangeRate()));
    // commissionSummaryBatchItemVo.setServiceFee(payablePlanSettlementBatchExchange.getServiceFee());
    // }
    // }
    // commissionSummaryBatchItemVo.setFkTypeKeyName(TableEnum.getValueByKey(commissionSummaryBatchItemVo.getFkTypeKey(),
    // TableEnum.COMMISSION_SETTLEMENT_TYPE));
    // }
    // return commissionSummaryBatchItemList;
    // }

    /**
     * feign 根据财务结算批次号获取代理应付计划
     *
     * @Date 14:30 2021/12/28
     * <AUTHOR>
     */
    @Override
    public List<PayablePlanVo> getAgentPayablePlanByNumSettlementBatch(String numSettlementBatch) {
        List<PayablePlanVo> agentPayablePlanByNumSettlementBatch = agentMapper
                .getAgentPayablePlanByNumSettlementBatch(numSettlementBatch);
        Set<Long> agentIds = agentPayablePlanByNumSettlementBatch.stream().map(PayablePlanVo::getFkAgentId)
                .collect(Collectors.toSet());
        List<AgentCompany> agentCompanies = agentCompanyMapper
                .selectList(Wrappers.<AgentCompany>query().lambda().in(AgentCompany::getFkAgentId, agentIds));
        Map<Long, Long> agentCompanyIdMap = new HashMap<>();
        for (AgentCompany agentCompany : agentCompanies) {
            agentCompanyIdMap.put(agentCompany.getFkAgentId(), agentCompany.getFkCompanyId());
        }
        for (PayablePlanVo payablePlanByNumSettlementBatch : agentPayablePlanByNumSettlementBatch) {
            payablePlanByNumSettlementBatch
                    .setAgentCompanyId(agentCompanyIdMap.get(payablePlanByNumSettlementBatch.getFkAgentId()));
        }
        return agentPayablePlanByNumSettlementBatch;
    }

    /**
     * 用于第三步佣金结算总额表导出的（第一步）
     *
     * @param agentSettlementVo
     * @return
     */
    @Override
    public List<AgentSettlementGrossAmountVo> getAgentListToExport(AgentSettlementQueryDto agentSettlementVo) {
        if (GeneralTool.isEmpty(agentSettlementVo)) {
            return null;
        }
        List<AgentSettlementGrossAmountVo> agentSettlementGrossAmountDTOList = new ArrayList<>();
        if (GeneralTool.isNotEmpty(agentSettlementVo.getFkCompanyIds())) {
            if (!SecureUtil.validateCompanys(agentSettlementVo.getFkCompanyIds())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
            }
        }
        if (GeneralTool.isNotEmpty(agentSettlementVo.getFkCompanyId())) {
            // 兼容单公司条件
            if (!SecureUtil.validateCompany(agentSettlementVo.getFkCompanyId())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
            }
        }

        List<Long> staffFollowerIds = permissionCenterClient.getStaffFollowerIds(SecureUtil.getStaffId()).getData();
        if (GeneralTool.isEmpty(staffFollowerIds)) {
            staffFollowerIds = new ArrayList<>();
        }
        staffFollowerIds.add(SecureUtil.getStaffId());

        String studentName = agentSettlementVo.getStudentName();
        if (StringUtils.isNotBlank(studentName)) {
            agentSettlementVo.setStudentName(studentName.replace(" ", "").trim());
        }
        if (GeneralTool.isNotEmpty(agentSettlementVo.getCommissionMark())) {
            agentSettlementVo.setCommissionMark(agentSettlementVo.getCommissionMark().toLowerCase());
        }

        Map<Long, String> companyConfigMap = permissionCenterClient
                .getCompanyConfigMap(ProjectKeyEnum.SETTLEMENT_COMMISSION_LIST_LIMIT.key, 1).getData();
        String configValue1 = companyConfigMap.get(SecureUtil.getFkCompanyId());
        boolean payInAdvanceFlag = configValue1.equals("1");

        List<Agent> agentList = agentMapper.agentSettlementList(null, staffFollowerIds, agentSettlementVo,
                ProjectKeyEnum.STEP_ENROLLED.key, ProjectKeyEnum.STEP_ENROLLED_TBC.key, payInAdvanceFlag, false);

        // 国家ids
        Set<Long> countryIds = agentList.stream().map(Agent::getFkAreaCountryId).collect(Collectors.toSet());
        // 州省ids
        Set<Long> stateIds = agentList.stream().map(Agent::getFkAreaStateId).collect(Collectors.toSet());
        // 城市ids
        Set<Long> cityIds = agentList.stream().map(Agent::getFkAreaCityId).collect(Collectors.toSet());

        // 根据国家ids获取国家名称
        Map<Long, String> countryNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(countryIds)) {
            Result<Map<Long, String>> countryNameResult = institutionCenterClient.getCountryNamesByIds(countryIds);
            if (countryNameResult.isSuccess() && GeneralTool.isNotEmpty(countryNameResult.getData())) {
                countryNamesByIds = countryNameResult.getData();
            }
        }
        // 根据州省ids获取州省名称
        Map<Long, String> stateNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(stateIds)) {
            Result<Map<Long, String>> result = institutionCenterClient.getStateFullNamesByIds(stateIds);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                stateNamesByIds = result.getData();
            }
        }
        // 根据州省ids获取州省名称
        Map<Long, String> cityNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(cityIds)) {
            Result<Map<Long, String>> result = institutionCenterClient.getCityFullNamesByIds(cityIds);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                cityNamesByIds = result.getData();
            }
        }
        Set<Long> agentIds = agentList.stream().map(Agent::getId).collect(Collectors.toSet());
        // 根据所有的代理id获取对应的bd名称
        Map<Long, String> bdNameMap = agentStaffService.getBdNameByAgentIds(agentIds);

        // 设置代理相关信息
        for (Agent agent : agentList) {
            AgentSettlementGrossAmountVo agentSettlementGrossAmountVo = new AgentSettlementGrossAmountVo();
            agentSettlementGrossAmountVo.setAgentId(agent.getId());
            if (GeneralTool.isNotEmpty(agent.getName())) {
                agentSettlementGrossAmountVo.setAgentName(agent.getName());
                if (StringUtils.isNotBlank(agent.getNameNote())) {
                    agentSettlementGrossAmountVo.setAgentName(agent.getName() + "(" + agent.getNameNote() + ")");
                }
            }
            if (GeneralTool.isNotEmpty(agent.getFkAreaCountryId())) {
                // 设置国家名称
                agentSettlementGrossAmountVo.setCountryName(countryNamesByIds.get(agent.getFkAreaCountryId()));
            }
            if (GeneralTool.isNotEmpty(agent.getFkAreaStateId())) {
                // 设置州省名称
                agentSettlementGrossAmountVo.setStateName(stateNamesByIds.get(agent.getFkAreaStateId()));
            }
            if (GeneralTool.isNotEmpty(agent.getFkAreaCityId())) {
                // 设置城市名称
                agentSettlementGrossAmountVo.setCityName(cityNamesByIds.get(agent.getFkAreaCityId()));
            }
            // bd名称
            agentSettlementGrossAmountVo.setBdName(bdNameMap.get(agent.getId()));
            agentSettlementGrossAmountDTOList.add(agentSettlementGrossAmountVo);
        }
        return agentSettlementGrossAmountDTOList;
    }

    @Override
    public Set<String> getNewAgentEmails(NewEmailGetAgentDto newEmailGetAgentDto) {
        Set<String> emails = new HashSet<>();

        if (GeneralTool.isEmpty(newEmailGetAgentDto.getFkCountryId())
                && GeneralTool.isEmpty(newEmailGetAgentDto.getFkInstitutionId())) {
            return null;
        }
        Set<String> emailSet = studentOfferItemMapper.getNewAgentEmails(newEmailGetAgentDto);
        for (String s : emailSet) {
            String emailString = s.replace(" ", "").trim();
            String[] emailsArray = emailString.split(";");
            List<String> emailList = Arrays.stream(emailsArray).distinct().collect(Collectors.toList());
            emails.addAll(emailList);
        }
        return emails;
        // return emailSet;
    }

    @Override
    public Set<String> getNewAgentAllEmails(Long newsId, Integer type) {
        Set<String> emails = new HashSet<>();
        Set<String> emailSet = studentOfferItemMapper.getNewAgentAllEmails();
        for (String s : emailSet) {
            String emailString = s.replace(" ", "").trim();
            String[] emailsArray = emailString.split(";");
            List<String> emailList = Arrays.stream(emailsArray).distinct().collect(Collectors.toList());
            emails.addAll(emailList);
        }
        return emails;
    }

    /**
     * feign 代理佣金结算列表
     *
     * @Date 11:15 2021/12/21
     * <AUTHOR>
     */
    @Override
    public AgentSettlementPageVo agentSettlementList(AgentSettlementQueryDto agentSettlementVo, Page page) {
        AgentSettlementPageVo agentSettlementPageVo = new AgentSettlementPageVo();

        if (GeneralTool.isEmpty(agentSettlementVo)) {
            return null;
        }
        List<AgentSettlementVo> agentSettlementVoList = new ArrayList<>();
        if (GeneralTool.isNotEmpty(agentSettlementVo.getFkCompanyIds())) {
            if (!SecureUtil.validateCompanys(agentSettlementVo.getFkCompanyIds())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
            }
        }
        if (GeneralTool.isNotEmpty(agentSettlementVo.getFkCompanyId())) {
            // 兼容单公司条件
            if (!SecureUtil.validateCompany(agentSettlementVo.getFkCompanyId())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
            }
        }

        List<Long> staffFollowerIds = permissionCenterClient.getStaffFollowerIds(SecureUtil.getStaffId()).getData();
        if (GeneralTool.isEmpty(staffFollowerIds)) {
            staffFollowerIds = new ArrayList<>();
        }
        staffFollowerIds.add(SecureUtil.getStaffId());

        String studentName = agentSettlementVo.getStudentName();
        if (StringUtils.isNotBlank(studentName)) {
            agentSettlementVo.setStudentName(studentName.replace(" ", "").trim());
        }
        if (GeneralTool.isNotEmpty(agentSettlementVo.getCommissionMark())) {
            agentSettlementVo.setCommissionMark(agentSettlementVo.getCommissionMark().toLowerCase());
        }

        Map<Long, String> companyConfigMap = permissionCenterClient
                .getCompanyConfigMap(ProjectKeyEnum.SETTLEMENT_COMMISSION_LIST_LIMIT.key, 1).getData();
        String configValue1 = companyConfigMap.get(SecureUtil.getFkCompanyId());
        boolean payInAdvanceFlag = configValue1.equals("1");

        IPage<Agent> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<Agent> agentList = agentMapper.agentSettlementList(iPage, staffFollowerIds, agentSettlementVo,
                ProjectKeyEnum.STEP_ENROLLED.key, ProjectKeyEnum.STEP_ENROLLED_TBC.key, payInAdvanceFlag, false);
        page.setAll((int) iPage.getTotal());
        if (GeneralTool.isEmpty(agentList)) {
            agentSettlementPageVo.setPage(page);
            return agentSettlementPageVo;
        }
        for (Agent agent : agentList) {
            AgentSettlementVo agentSettlementDto = BeanCopyUtils.objClone(agent, AgentSettlementVo::new);
            if (agent.getIsSettlementPort()) {
                agentSettlementDto.setSettlementPortAgentId(agentSettlementDto.getId());
            } else {
                // 这个代理不是结算口代理 找上级结算口代理作为结算代理
                Long parentAgentId = agent.getFkParentAgentId();
                while (true) {
                    Agent parentAgent = agentMapper.selectById(parentAgentId);
                    if (parentAgent.getIsSettlementPort()) {
                        agentSettlementDto.setSettlementPortAgentId(parentAgent.getId());
                        break;
                    } else {
                        parentAgentId = parentAgent.getFkParentAgentId();
                    }
                }
            }
            agentSettlementVoList.add(agentSettlementDto);
        }
        // 国家ids
        Set<Long> countryIds = agentSettlementVoList.stream().map(AgentSettlementVo::getFkAreaCountryId)
                .collect(Collectors.toSet());
        // 州省ids
        Set<Long> stateIds = agentSettlementVoList.stream().map(AgentSettlementVo::getFkAreaStateId)
                .collect(Collectors.toSet());
        // 城市ids
        Set<Long> cityIds = agentSettlementVoList.stream().map(AgentSettlementVo::getFkAreaCityId)
                .collect(Collectors.toSet());
        // 代理ids
        Set<Long> ids = agentSettlementVoList.stream().map(AgentSettlementVo::getId).collect(Collectors.toSet());

        List<SelItem> selItems = payablePlanMapper.getAgentPayInfo(ids);
        Map<Long, List<SelItem>> listMap = selItems.stream().filter(f -> !Objects.isNull(f.getKeyId()))
                .collect(Collectors.groupingBy(SelItem::getKeyId));
        Map<Long, Boolean> isFirst = new HashMap<>(agentSettlementVoList.size());
        for (Map.Entry<Long, List<SelItem>> entry : listMap.entrySet()) {
            isFirst.put(entry.getKey(),
                    entry.getValue().stream().anyMatch(f -> Integer.parseInt(f.getVal().toString()) == 0));
        }

        // 根据国家ids获取国家名称
        Map<Long, String> countryNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(countryIds)) {
            Result<Map<Long, String>> countryNameResult = institutionCenterClient.getCountryNamesByIds(countryIds);
            if (countryNameResult.isSuccess() && GeneralTool.isNotEmpty(countryNameResult.getData())) {
                countryNamesByIds = countryNameResult.getData();
            }
        }
        // 根据州省ids获取州省名称
        Map<Long, String> stateNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(stateIds)) {
            Result<Map<Long, String>> result = institutionCenterClient.getStateFullNamesByIds(stateIds);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                stateNamesByIds = result.getData();
            }
        }
        // 根据州省ids获取州省名称
        Map<Long, String> cityNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(cityIds)) {
            Result<Map<Long, String>> result = institutionCenterClient.getCityFullNamesByIds(cityIds);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                cityNamesByIds = result.getData();
            }
        }
        List<Long> settlementPortAgentIdIds = agentSettlementVoList.stream()
                .map(AgentSettlementVo::getSettlementPortAgentId).collect(Collectors.toList());
        Map<Long, List<AgentContractAccountVo>> agentContractAccountByAgentIds = agentContractAccountService
                .getAgentContractAccountByAgentIds(settlementPortAgentIdIds);

        // 结算口代理迭代
        Set<Long> collect = agentSettlementVoList.stream().map(AgentSettlementVo::getId).collect(Collectors.toSet());
        if (GeneralTool.isEmpty(collect)) {
            throw new GetServiceException(ResultCode.SERVER_EXCEPTION);
        }
        List<AgentCompanyInfoVo> agentCompanyName = agentCompanyMapper.getAgentCompanyName(collect);
        Map<Long, List<AgentCompanyInfoVo>> companyMap = agentCompanyName.stream()
                .collect(Collectors.groupingBy(AgentCompanyInfoVo::getAgentId));
        // 获取待结算标记代理ids
        Result<List<Long>> result = financeCenterClient.getSettlementFlagAgentIds();
        if (!result.isSuccess()) {
            throw new GetServiceException(result.getMessage());
        }
        List<Long> agentFlagIds = result.getData();
        for (AgentSettlementVo agentSettlementDto : agentSettlementVoList) {
            if (agentFlagIds.contains(agentSettlementDto.getSettlementPortAgentId())) {
                agentSettlementDto.setSettlementFlag(true);
            } else {
                agentSettlementDto.setSettlementFlag(false);
            }
            if (StringUtils.isNotBlank(agentSettlementDto.getNameNote())) {
                agentSettlementDto.setName(agentSettlementDto.getName() + "(" + agentSettlementDto.getNameNote() + ")");
            }
            if (isFirst.containsKey(agentSettlementDto.getId())) {
                agentSettlementDto.setFirstFlag(isFirst.get(agentSettlementDto.getId()));
            }
            agentSettlementDto.setFkCompanyName(companyMap.get(agentSettlementDto.getId()).get(0).getCompanyName());
            agentSettlementDto.setFkCompanyId(companyMap.get(agentSettlementDto.getId()).get(0).getCompanyId());
            setAreaName(agentSettlementDto, countryNamesByIds, stateNamesByIds, cityNamesByIds);
            agentSettlementDto.setAgentContractAccountList(
                    agentContractAccountByAgentIds.get(agentSettlementDto.getSettlementPortAgentId()));
            int planCount = agentMapper.getAgentPayablePlanCountByAgentIds(agentSettlementDto.getId(),
                    agentSettlementVo, ProjectKeyEnum.STEP_ENROLLED.key, ProjectKeyEnum.STEP_ENROLLED_TBC.key,
                    payInAdvanceFlag);
            agentSettlementDto.setPlanCount(planCount);
            // 佣金结算时检测提示
            List<SaleMediaAndAttached> saleMediaAndAttacheds = mediaAndAttachedMapper
                    .selectList(Wrappers.<SaleMediaAndAttached>lambdaQuery()
                            .eq(SaleMediaAndAttached::getFkTableName, TableEnum.SALE_AGENT.key)
                            .eq(SaleMediaAndAttached::getFkTableId, agentSettlementDto.getSettlementPortAgentId())
                            .eq(SaleMediaAndAttached::getTypeKey, FileTypeEnum.SALE_AGENT_ID_CARD_FRONT.key));
            List<SaleMediaAndAttached> saleMediaAndAttachedList = mediaAndAttachedMapper
                    .selectList(Wrappers.<SaleMediaAndAttached>lambdaQuery()
                            .eq(SaleMediaAndAttached::getFkTableName, TableEnum.SALE_AGENT.key)
                            .eq(SaleMediaAndAttached::getFkTableId, agentSettlementDto.getSettlementPortAgentId())
                            .eq(SaleMediaAndAttached::getTypeKey, FileTypeEnum.SALE_AGENT_ID_CARD_BACK.key));
            List<CompanyConfigInfoDto> companySettlementConfigInfo = permissionCenterClient
                    .getCompanySettlementConfigInfo(ProjectKeyEnum.AGENT_INFO_OPT_LIMIT.key);
            Map<Long, Integer> agentIdLimitMap = companySettlementConfigInfo.stream()
                    .collect(Collectors.toMap(CompanyConfigInfoDto::getCompanyId, CompanyConfigInfoDto::getValue));
            Integer agentIdLimit = agentIdLimitMap.get(Long.parseLong(agentSettlementDto.getFkCompanyId()));
            if ((GeneralTool.isEmpty(saleMediaAndAttacheds) || GeneralTool.isEmpty(saleMediaAndAttachedList))
                    && agentIdLimit == 1) {
                agentSettlementDto.setIdCardFlag(false);
            } else {
                agentSettlementDto.setIdCardFlag(true);
            }
            // 结算父代理
            Agent portAgent = agentMapper.selectById(agentSettlementDto.getSettlementPortAgentId());
            agentSettlementDto.setPortNature(portAgent.getNature());
            // 营业执照校验
            saleMediaAndAttacheds = mediaAndAttachedMapper.selectList(Wrappers.<SaleMediaAndAttached>lambdaQuery()
                    .eq(SaleMediaAndAttached::getFkTableName, TableEnum.SALE_AGENT.key)
                    .eq(SaleMediaAndAttached::getFkTableId, agentSettlementDto.getSettlementPortAgentId())
                    .eq(SaleMediaAndAttached::getTypeKey, FileTypeEnum.BUSINESS_LICENSE.key));
            if (GeneralTool.isNotEmpty(saleMediaAndAttacheds)) {
                agentSettlementDto.setBusinessLicenseFlag(true);
            } else {
                agentSettlementDto.setBusinessLicenseFlag(false);
            }

            // 合同过期校验 【合同有效】= 需要检查合同期限是否有效 + 是否已经上传合同附件。
            List<Long> contractIds = agentContractMapper
                    .checkAgentContractExpirationTime(agentSettlementDto.getSettlementPortAgentId());
            if (GeneralTool.isEmpty(contractIds)) {
                agentSettlementDto.setContractFlag(false);
            } else {
                saleMediaAndAttacheds = mediaAndAttachedMapper.selectList(Wrappers.<SaleMediaAndAttached>lambdaQuery()
                        .eq(SaleMediaAndAttached::getFkTableName, TableEnum.SALE_CONTRACT.key)
                        .in(SaleMediaAndAttached::getFkTableId, contractIds)
                        .eq(SaleMediaAndAttached::getTypeKey, FileTypeEnum.SALE_CONTRACT_FILE.key));
                if (GeneralTool.isNotEmpty(saleMediaAndAttacheds)) {
                    agentSettlementDto.setContractFlag(true);
                } else {
                    agentSettlementDto.setContractFlag(false);
                }
            }
            // 是否有首选
            agentSettlementDto.setPreferredAccountFlag(false);
            if (GeneralTool.isNotEmpty(agentSettlementDto.getAgentContractAccountList())) {
                for (AgentContractAccountVo agentContractAccountVo : agentSettlementDto.getAgentContractAccountList()) {
                    if (GeneralTool.isNotEmpty(agentContractAccountVo.getIsDefault())
                            && agentContractAccountVo.getIsDefault()) {
                        agentSettlementDto.setPreferredAccountFlag(true);
                    }
                }
            }

        }
        agentSettlementPageVo.setAgentSettlementDtoList(agentSettlementVoList);
        agentSettlementPageVo.setPage(page);
        return agentSettlementPageVo;
    }

    /**
     * Author Cream
     * Description : //获取结算的ids
     * Date 2023/2/24 16:24
     * Params:
     * Return
     */
    @Override
    public List<Long> getAgentSettlementIds(AgentSettlementQueryDto agentSettlementVo, String local, Long staffId,
            boolean payInAdvanceFlag, boolean exportFlag) {
        if (GeneralTool.isEmpty(agentSettlementVo)) {
            return null;
        }
        if (GeneralTool.isNotEmpty(agentSettlementVo.getFkCompanyIds())) {
            if (!SecureUtil.validateCompanys(agentSettlementVo.getFkCompanyIds())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission", local));
            }
        }
        if (GeneralTool.isNotEmpty(agentSettlementVo.getFkCompanyId())) {
            // 兼容单公司条件
            if (!SecureUtil.validateCompany(agentSettlementVo.getFkCompanyId())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission", local));
            }
        }
        if (GeneralTool.isNotEmpty(agentSettlementVo.getCommissionMark())) {
            agentSettlementVo.setCommissionMark(agentSettlementVo.getCommissionMark().toLowerCase());
        }
        List<Long> staffFollowerIds = permissionCenterClient.getStaffFollowerIds(staffId).getData();
        if (GeneralTool.isEmpty(staffFollowerIds)) {
            staffFollowerIds = new ArrayList<>();
        }
        staffFollowerIds.add(staffId);
        agentSettlementVo.setStudentName(DataConverter.stringManipulation(agentSettlementVo.getStudentName()));

        List<Agent> agentList = agentMapper.agentSettlementList(null, staffFollowerIds, agentSettlementVo,
                ProjectKeyEnum.STEP_ENROLLED.key, ProjectKeyEnum.STEP_ENROLLED_TBC.key, payInAdvanceFlag, exportFlag);
        return agentList.stream().map(Agent::getId).collect(Collectors.toList());
    }

    /**
     * 检查代理资料是否完善
     *
     * @Date 15:12 2022/11/28
     * <AUTHOR>
     */
    @Override
    public boolean checkAgentData(List<BatchDownloadAgentReconciliationDto> batchDownloadAgentReconciliationVoList) {
        boolean flag = true;
        Map<Long, String> companyConfigMap = permissionCenterClient
                .getCompanyConfigMap(ProjectKeyEnum.AGENT_INFO_OPT_LIMIT.key, 1).getData();
        for (BatchDownloadAgentReconciliationDto batchDownloadAgentReconciliationDto : batchDownloadAgentReconciliationVoList) {
            // 代理id
            Long agentId = batchDownloadAgentReconciliationDto.getSettlementPortAgentId();
            // 同代理的应收计划信息List
            List<AgentSettlementBatchExportDto> agentSettlementOfferItemDtoList = batchDownloadAgentReconciliationDto
                    .getAgentSettlementOfferItemDtoList();
            // 如果结算币种有人民币账户需要身份证校验
            boolean existRmbAccountFlag = agentSettlementOfferItemDtoList.stream()
                    .anyMatch(agentSettlementOfferItemDto -> "CNY"
                            .equals(agentSettlementOfferItemDto.getAgentAccountCurrencyTypeNum()));

            if (existRmbAccountFlag) {
                // 身份证校验(需要配置判断)
                Integer agentLimit = 1;
                long companyId = getAgentCompanyIdById(agentId).longValue();
                if (GeneralTool.isNotEmpty(companyConfigMap)) {
                    agentLimit = Integer.valueOf(companyConfigMap.get(companyId));
                }
                List<SaleMediaAndAttached> saleMediaAndAttacheds = mediaAndAttachedMapper
                        .selectList(Wrappers.<SaleMediaAndAttached>lambdaQuery()
                                .eq(SaleMediaAndAttached::getFkTableName, TableEnum.SALE_AGENT.key)
                                .eq(SaleMediaAndAttached::getFkTableId, agentId)
                                .eq(SaleMediaAndAttached::getTypeKey, FileTypeEnum.SALE_AGENT_ID_CARD_FRONT.key));
                List<SaleMediaAndAttached> saleMediaAndAttachedList = mediaAndAttachedMapper
                        .selectList(Wrappers.<SaleMediaAndAttached>lambdaQuery()
                                .eq(SaleMediaAndAttached::getFkTableName, TableEnum.SALE_AGENT.key)
                                .eq(SaleMediaAndAttached::getFkTableId, agentId)
                                .eq(SaleMediaAndAttached::getTypeKey, FileTypeEnum.SALE_AGENT_ID_CARD_BACK.key));
                if ((GeneralTool.isEmpty(saleMediaAndAttacheds) || GeneralTool.isEmpty(saleMediaAndAttachedList))
                        && agentLimit == 1) {
                    flag = false;
                }
                Agent agent = agentMapper.selectById(agentId);
                if (agent.getNature().equals(ProjectExtraEnum.AGENT_NATURE_COMPANY.key.toString())) {
                    // 营业执照校验
                    saleMediaAndAttacheds = mediaAndAttachedMapper
                            .selectList(Wrappers.<SaleMediaAndAttached>lambdaQuery()
                                    .eq(SaleMediaAndAttached::getFkTableName, TableEnum.SALE_AGENT.key)
                                    .eq(SaleMediaAndAttached::getFkTableId, agentId)
                                    .eq(SaleMediaAndAttached::getTypeKey, FileTypeEnum.BUSINESS_LICENSE.key));
                    if (GeneralTool.isEmpty(saleMediaAndAttacheds)) {
                        flag = false;
                    }
                }
            }
            // 合同过期校验 【合同有效】= 需要检查合同期限是否有效 + 是否已经上传合同附件。
            List<Long> contractIds = agentContractMapper.checkAgentContractExpirationTime(agentId);
            if (GeneralTool.isEmpty(contractIds)) {
                flag = false;
            } else {
                List<SaleMediaAndAttached> saleMediaAndAttacheds = mediaAndAttachedMapper
                        .selectList(Wrappers.<SaleMediaAndAttached>lambdaQuery()
                                .eq(SaleMediaAndAttached::getFkTableName, TableEnum.SALE_CONTRACT.key)
                                .in(SaleMediaAndAttached::getFkTableId, contractIds)
                                .eq(SaleMediaAndAttached::getTypeKey, FileTypeEnum.SALE_CONTRACT_FILE.key));
                if (GeneralTool.isEmpty(saleMediaAndAttacheds)) {
                    flag = false;
                }
            }
        }

        return flag;
    }

    @Override
    public List<BaseSelectEntity> getAgentByTargetName(String targetName) {
        return agentMapper.getAgentByTargetName(DataConverter.stringManipulation(targetName));
    }

    @Override
    public List<ContactPersonVo> getAgentContactPersonByAgentId(Long id) {
        Set<Long> ids = new HashSet<>();
        ids.add(id);
        return contactPersonService.getContactPersonInfo(ids);
    }

    /**
     * feign 根据代理id获取该代理下级非结算口的代理ids
     *
     * @return
     * @Date 11:15 2021/12/21
     * <AUTHOR>
     */
    @Override
    public List<Long> getSubordinateNotPortAgentIdsById(Long agentId) {
        return getSubordinateAgentIds(Collections.singletonList(agentId));
    }

    @Override
    public Boolean agentIsKeyExpired() {
        return agentMapper.agentIsKeyExpired();
    }

    // /**
    // * 代理对账单确认列表
    // *
    // * @Date 11:15 2021/12/21
    // * <AUTHOR>
    // */
    // @Override
    // public List<AgencyStatementDto> agencyStatementDatas(AgencyStatementVo
    // agencyStatementVo, Page page) {
    // List<Long> agentIdList = getAgentList();
    // PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
    // List<AgencyStatementDto> agencyStatementDtoList =
    // agentMapper.agencyStatementDatas(agentIdList, agencyStatementVo);
    // page.restPage(agencyStatementDtoList);
    //
    //
    //
    //
    //
    //
    //
    //
    //
    //
    //
    //
    // //国家ids
    // Set<Long> countryIds =
    // agencyStatementDtoList.stream().map(AgentSettlementVo::getFkAreaCountryId).collect(Collectors.toSet());
    // //州省ids
    // Set<Long> stateIds =
    // agencyStatementDtoList.stream().map(AgentSettlementVo::getFkAreaStateId).collect(Collectors.toSet());
    // //城市ids
    // Set<Long> cityIds =
    // agencyStatementDtoList.stream().map(AgentSettlementVo::getFkAreaCityId).collect(Collectors.toSet());
    // //根据国家ids获取国家名称
    // Map<Long, String> countryNamesByIds = new HashMap<>();
    // if (GeneralTool.isNotEmpty(countryIds)) {
    // countryNamesByIds = feignInstitutionService.getCountryNamesByIds(countryIds);
    // }
    // //根据州省ids获取州省名称
    // Map<Long, String> stateNamesByIds = new HashMap<>();
    // if (GeneralTool.isNotEmpty(stateIds)) {
    // stateNamesByIds = feignInstitutionService.getStateFullNamesByIds(stateIds);
    // }
    // //根据州省ids获取州省名称
    // Map<Long, String> cityNamesByIds = new HashMap<>();
    // if (GeneralTool.isNotEmpty(cityIds)) {
    // cityNamesByIds = feignInstitutionService.getCityFullNamesByIds(cityIds);
    // }
    // List<Long> agentIds =
    // agencyStatementDtoList.stream().map(AgentSettlementVo::getId).collect(Collectors.toList());
    // Map<Long, List<com.get.common.entity.fegin.AgentContractAccountVo>>
    // agentContractAccountByAgentIds =
    // agentContractAccountService.getAgentContractAccountByAgentIds(agentIds);
    //
    // //结算口代理迭代
    // for (AgentSettlementVo agentSettlementDto : agencyStatementDtoList) {
    // setAreaName(agentSettlementDto, countryNamesByIds, stateNamesByIds,
    // cityNamesByIds);
    // agentSettlementDto.setAgentContractAccountList(agentContractAccountByAgentIds.get(agentSettlementDto.getId()));
    // //查询结算口代理以及其下级非结算口代理的 代理id以及应付计划数
    // Long agentId = agentSettlementDto.getId();
    // List<Long> subordinateAgentIds =
    // getSubordinateAgentIds(Collections.singletonList(agentId));
    // subordinateAgentIds.add(agentId);
    // int planCount =
    // agentMapper.getAgentPayablePlanCountByAgentIds(subordinateAgentIds,
    // agentSettlementVo, ProjectExtraEnum.UNSETTLED.key);
    // agentSettlementDto.setPlanCount(planCount);
    // }
    // return agencyStatementDtoList;
    // }

    /**
     * 区域名字set
     *
     * @Date 10:48 2021/12/22
     * <AUTHOR>
     */
    private void setAreaName(AgentSettlementVo agentDto, Map<Long, String> countryNamesByIds,
            Map<Long, String> stateNamesByIds,
            Map<Long, String> cityNamesByIds) {
        if (GeneralTool.isNotEmpty(agentDto)) {
            if (GeneralTool.isNotEmpty(agentDto.getFkAreaCountryId())) {
                // 设置国家名称
                agentDto.setCountryName(countryNamesByIds.get(agentDto.getFkAreaCountryId()));
            }
            if (GeneralTool.isNotEmpty(agentDto.getFkAreaStateId())) {
                // 设置州省名称
                agentDto.setStateName(stateNamesByIds.get(agentDto.getFkAreaStateId()));
            }
            if (GeneralTool.isNotEmpty(agentDto.getFkAreaCityId())) {
                // 设置城市名称
                agentDto.setCityName(cityNamesByIds.get(agentDto.getFkAreaCityId()));
            }
        }
    }

    /**
     * 设置批量列表数据（优化列表查询，多次查询修改成一次查询）
     *
     * @param agentVo
     * @param companyMap
     * @param countryNamesByIds
     * @param stateNamesByIds
     * @param cityNamesByIds
     * @param parentAgentNumMap
     * @param parentAgentNameMap
     * @
     */
    private void setDtoName(AgentVo agentVo, Map<String, String> companyMap,
            Map<Long, String> countryNamesByIds, Map<Long, String> stateNamesByIds,
            Map<Long, String> cityNamesByIds, Map<Long, String> parentAgentNumMap,
            Map<Long, String> parentAgentNameMap, Map<Long, AgentStaff> agentStaffByAgentIds,
            Map<Long, Set<Long>> relationByAgentIds, Map<Long, StaffBdCode> bDbyStaffIds,
            Map<Long, String> staffNamesByIds) {
        setAreaName(agentVo, countryNamesByIds, stateNamesByIds, cityNamesByIds);
        // 有父id时，设置父编号和名称
        if (GeneralTool.isNotEmpty(agentVo.getFkParentAgentId())) {
            agentVo.setParentAgentNum(parentAgentNumMap.get(agentVo.getFkParentAgentId()));
            agentVo.setParentAgentName(parentAgentNameMap.get(agentVo.getFkParentAgentId()));
        }
        // 设置BD编号和名称
        AgentStaff agentStaff = agentStaffByAgentIds.get(agentVo.getId());
        if (GeneralTool.isNotEmpty(agentStaff)) {
            // 根据staffId 获取对应bdCode
            if (GeneralTool.isNotEmpty(bDbyStaffIds)) {
                StaffBdCode staffBdCode = bDbyStaffIds.get(agentStaff.getFkStaffId());
                if (GeneralTool.isNotEmpty(staffBdCode)) {
                    agentVo.setBdCode(staffBdCode.getBdCode());
                    // 查询所有大区列表
                    if (GeneralTool.isEmpty(staffBdCode.getFkAreaRegionId())) {
                        System.out.println("agentVo.getId() = " + agentVo.getId());
                    } else if (GeneralTool.isNotEmpty(staffBdCode.getFkAreaRegionId())) {
                        String[] split = staffBdCode.getFkAreaRegionId().split(",");
                        Set<Long> areaRegionIds = new HashSet<>();
                        for (String sp : split) {
                            if (GeneralTool.isNotEmpty(sp)) {
                                Long areaRegionId = Long.valueOf(sp);
                                areaRegionIds.add(areaRegionId);
                            }

                        }
                        if (GeneralTool.isNotEmpty(areaRegionIds)) {
                            Map<Long, com.get.institutioncenter.vo.AreaRegionVo> areaRegionDtoByIdsMap = new HashMap<>();
                            Result<Map<Long, com.get.institutioncenter.vo.AreaRegionVo>> areaRegionDtoByIdsMapResult = institutionCenterClient
                                    .getAreaRegionDtoByIds(areaRegionIds);
                            if (areaRegionDtoByIdsMapResult.isSuccess()
                                    && GeneralTool.isNotEmpty(areaRegionDtoByIdsMapResult.getData())) {
                                areaRegionDtoByIdsMap = areaRegionDtoByIdsMapResult.getData();
                            }
                            Collection<com.get.institutioncenter.vo.AreaRegionVo> valueCollection = areaRegionDtoByIdsMap
                                    .values();
                            List<com.get.institutioncenter.vo.AreaRegionVo> valueList = new ArrayList(valueCollection);
                            List<AreaRegionVo> areaRegionVos = BeanCopyUtils.copyListProperties(valueList,
                                    AreaRegionVo::new);
                            // BD绑定的大区
                            agentVo.setAreaRegionDtos(areaRegionVos);
                        }
                    }

                }
            }

            // feign调用 根据员工id 获取员工姓名
            String staffName = staffNamesByIds.get(agentStaff.getFkStaffId());
            agentVo.setBdName(staffName);
        }

        Set<Long> companyIds = relationByAgentIds.get(agentVo.getId());
        if (GeneralTool.isNotEmpty(companyIds)) {
            StringBuilder builder = new StringBuilder();
            for (Long companyId : companyIds) {
                String companyName = companyMap.get(String.valueOf(companyId));
                builder.append(companyName).append("，");
            }
            agentVo.setCompanyName(sub(builder));
        }
    }

    private void setNames(AgentVo agentVo, Map<String, String> companyMap,
            Map<Long, String> countryNamesByIds, Map<Long, String> stateNamesByIds,
            Map<Long, String> cityNamesByIds,
            Map<Long, AgentStaff> agentStaffByAgentIds,
            Map<Long, Set<Long>> relationByAgentIds, Map<Long, StaffBdCode> bDbyStaffIds,
            Map<Long, String> staffNamesByIds) {
        setAreaName(agentVo, countryNamesByIds, stateNamesByIds, cityNamesByIds);
        // 设置BD编号和名称
        AgentStaff agentStaff = agentStaffByAgentIds.get(agentVo.getFkAgentId());
        if (GeneralTool.isNotEmpty(agentStaff)) {
            // 根据staffId 获取对应bdCode
            if (GeneralTool.isNotEmpty(bDbyStaffIds)) {
                StaffBdCode staffBdCode = bDbyStaffIds.get(agentStaff.getFkStaffId());
                if (GeneralTool.isNotEmpty(staffBdCode)) {
                    agentVo.setBdCode(staffBdCode.getBdCode());
                    // 查询所有大区列表
                    if (GeneralTool.isEmpty(staffBdCode.getFkAreaRegionId())) {
                        System.out.println("agentVo.getFkAgentId() = " + agentVo.getFkAgentId());
                    } else if (GeneralTool.isNotEmpty(staffBdCode.getFkAreaRegionId())) {
                        String[] split = staffBdCode.getFkAreaRegionId().split(",");
                        Set<Long> areaRegionIds = new HashSet<>();
                        for (String sp : split) {
                            if (GeneralTool.isNotEmpty(sp)) {
                                Long areaRegionId = Long.valueOf(sp);
                                areaRegionIds.add(areaRegionId);
                            }

                        }
                        if (GeneralTool.isNotEmpty(areaRegionIds)) {
                            Map<Long, com.get.institutioncenter.vo.AreaRegionVo> areaRegionDtoByIdsMap = new HashMap<>();
                            Result<Map<Long, com.get.institutioncenter.vo.AreaRegionVo>> areaRegionDtoByIdsMapResult = institutionCenterClient
                                    .getAreaRegionDtoByIds(areaRegionIds);
                            if (areaRegionDtoByIdsMapResult.isSuccess()
                                    && GeneralTool.isNotEmpty(areaRegionDtoByIdsMapResult.getData())) {
                                areaRegionDtoByIdsMap = areaRegionDtoByIdsMapResult.getData();
                            }
                            Collection<com.get.institutioncenter.vo.AreaRegionVo> valueCollection = areaRegionDtoByIdsMap
                                    .values();
                            List<com.get.institutioncenter.vo.AreaRegionVo> valueList = new ArrayList(valueCollection);
                            List<AreaRegionVo> areaRegionVos = BeanCopyUtils.copyListProperties(valueList,
                                    AreaRegionVo::new);
                            // BD绑定的大区
                            agentVo.setAreaRegionDtos(areaRegionVos);
                        }
                    }

                }
            }

            // feign调用 根据员工id 获取员工姓名
            String staffName = staffNamesByIds.get(agentStaff.getFkStaffId());
            agentVo.setBdName(staffName);
        }

        Set<Long> companyIds = relationByAgentIds.get(agentVo.getFkAgentId());
        if (GeneralTool.isNotEmpty(companyIds)) {
            StringBuilder builder = new StringBuilder();
            for (Long companyId : companyIds) {
                String companyName = companyMap.get(String.valueOf(companyId));
                builder.append(companyName).append("，");
                companyIds.add(companyId);
            }
            agentVo.setCompanyName(sub(builder));
            if (companyIds.size() > 1) {
                agentVo.setCompanyIds(new ArrayList<>(companyIds));
            } else {
                agentVo.setFkCompanyId(companyIds.iterator().next());
            }

        }

    }

    private void setAreaName(AgentVo agentVo, Map<Long, String> countryNamesByIds, Map<Long, String> stateNamesByIds,
            Map<Long, String> cityNamesByIds) {
        if (GeneralTool.isNotEmpty(agentVo)) {
            if (GeneralTool.isNotEmpty(agentVo.getFkAreaCountryId())) {
                // 设置国家名称
                agentVo.setCountryName(countryNamesByIds.get(agentVo.getFkAreaCountryId()));
            }
            if (GeneralTool.isNotEmpty(agentVo.getFkAreaStateId())) {
                // 设置州省名称
                agentVo.setStateName(stateNamesByIds.get(agentVo.getFkAreaStateId()));
            }
            if (GeneralTool.isNotEmpty(agentVo.getFkAreaCityId())) {
                // 设置城市名称
                agentVo.setCityName(cityNamesByIds.get(agentVo.getFkAreaCityId()));
            }
        }
    }

    /**
     * 设置返回数据，单条查询
     *
     * @param agentVo
     * @param companyMap
     * @
     */
    private void findByIdSetDtoName(AgentVo agentVo, Map<String, String> companyMap) {
        findByIdSetAreaName(agentVo);
        // 有父id时，设置父编号和名称
        if (GeneralTool.isNotEmpty(agentVo.getFkParentAgentId())) {
            Agent parentAgent = agentMapper.selectById(agentVo.getFkParentAgentId());
            if (GeneralTool.isNotEmpty(parentAgent)) {
                agentVo.setParentAgentNum(parentAgent.getNum());
                agentVo.setParentAgentName(parentAgent.getName());
            }
        }
        // 设置BD编号和名称
        AgentStaff agentStaff = agentStaffService.getAgentStaffByAgentId(agentVo.getId());
        if (GeneralTool.isNotEmpty(agentStaff)) {
            // 根据staffId 获取对应bdCode
            String bdCode = staffBdCodeService.getBDbyStaffId(agentStaff.getFkStaffId());
            agentVo.setBdCode(bdCode);
            // feign调用 根据员工id 获取员工姓名
            Result<String> result = permissionCenterClient.getStaffName(agentStaff.getFkStaffId());
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                agentVo.setBdName(result.getData());
            }
        }

        List<Long> companyIds = companyService.getRelationByAgentId(agentVo.getId());
        if (GeneralTool.isNotEmpty(companyIds)) {
            StringBuilder builder = new StringBuilder();
            for (Long companyId : companyIds) {
                String companyName = companyMap.get(String.valueOf(companyId));
                builder.append(companyName).append("，");
            }
            agentVo.setCompanyName(sub(builder));
            agentVo.setFkCompanyId(companyIds.stream()
                    .filter(Objects::nonNull)
                    .min(Long::compare).get());
            agentVo.setCompanyIds(companyIds);
        }
    }

    private void findByIdSetAreaName(AgentVo agentVo) {
        if (GeneralTool.isNotEmpty(agentVo)) {
            if (GeneralTool.isNotEmpty(agentVo.getFkAreaCountryId())) {
                // 设置国家名称
                // agentVo.setCountryName(institutionCenterClient.getCountryNameById(agentVo.getFkAreaCountryId()));
                Result<String> result = institutionCenterClient.getCountryNameById(agentVo.getFkAreaCountryId());
                if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                    agentVo.setCountryName(result.getData());
                }
            }
            if (GeneralTool.isNotEmpty(agentVo.getFkAreaStateId())) {
                // 设置州省名称
                // agentVo.setStateName(institutionCenterClient.getStateNameById(agentVo.getFkAreaStateId()));
                Result<String> result = institutionCenterClient.getStateFullNameById(agentVo.getFkAreaStateId());
                if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                    agentVo.setStateName(result.getData());
                }
            }
            if (GeneralTool.isNotEmpty(agentVo.getFkAreaCityId())) {
                // 设置城市名称
                // agentVo.setCityName(institutionCenterClient.getCityNameById(agentVo.getFkAreaCityId()));
                Result<String> result = institutionCenterClient.getCityFullNameById(agentVo.getFkAreaCityId());
                if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                    agentVo.setCityName(result.getData());
                }
            }
        }
    }

    private List<Long> getCountryIds() {
        List<Long> emptyQueryList = getEmptyQueryList();
        // 获取当前登录用户所属国家
        List<Long> countryIds = SecureUtil.getCountryIdsByStaffId(GetAuthInfo.getStaffId());
        if (GeneralTool.isNotEmpty(countryIds)) {
            // 移除空元素
            countryIds.removeIf(Objects::isNull);
        }
        countryIds = GeneralTool.isEmpty(countryIds) ? emptyQueryList : countryIds;
        return countryIds;
    }

    private List<Long> getAgentIds(Long companyId) {
        if (!SecureUtil.validateCompany(companyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
        }
        // 获取当前登录用户所属公司
        List<Long> companyIds = new ArrayList<>();
        companyIds.add(companyId);
        // 查询对应的代理
        List<Long> agentIds = companyService.getRelationByCompanyId(companyIds);
        // 防止空数组查询条件可以查看全部
        List<Long> emptyQueryList = getEmptyQueryList();
        agentIds = GeneralTool.isEmpty(agentIds) ? emptyQueryList : agentIds;
        return agentIds;
    }

    private List<Long> queryByCompanyId(AgentDto agentDto) {
        if (!SecureUtil.validateCompany(agentDto.getFkCompanyId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
        }
        List<Long> queryCompanyIdList = new ArrayList<>();
        queryCompanyIdList.add(agentDto.getFkCompanyId());
        List<Long> agentIds = companyService.getRelationByCompanyId(queryCompanyIdList);
        List<Long> emptyQueryList = getEmptyQueryList();
        agentIds = GeneralTool.isEmpty(agentIds) ? emptyQueryList : agentIds;
        return agentIds;
    }

    private List<Long> getEmptyQueryList() {
        List<Long> empty = new ArrayList<>();
        empty.add(0L);
        return empty;
    }

    private Map<String, String> getCompanyMap() {
        // ListResponseBo responseBo = permissionCenterClient.getAllCompanyDto();
        // 初始为5的map
        Map<String, String> companyMap = new HashMap<>(5);
        Result<List<com.get.permissioncenter.vo.tree.CompanyTreeVo>> result = permissionCenterClient.getAllCompanyDto();
        if (result.isSuccess() && CollectionUtil.isNotEmpty(result.getData())) {
            JsonConfig config = new JsonConfig();
            config.setExcludes(new String[] { "departmentTree", "totalNum" });
            JSONArray jsonArray = JSONArray.fromObject(result.getData(), config);
            List<CompanyTreeVo> companyTreeVos = JSONArray.toList(jsonArray, new CompanyTreeVo(), new JsonConfig());
            if (GeneralTool.isNotEmpty(companyTreeVos)) {
                companyMap = companyTreeVos.stream()
                        .collect(Collectors.toMap(CompanyTreeVo::getId, CompanyTreeVo::getShortName));
            }
        }
        return companyMap;
    }

    /**
     * @return java.lang.String
     * @Description :截取字符串逗号
     * @Param [sb]
     * <AUTHOR>
     */
    private String sub(StringBuilder sb) {
        if (GeneralTool.isEmpty(sb)) {
            return null;
        }
        String substring = null;
        int i = sb.lastIndexOf("，");
        if (i != -1) {
            substring = sb.substring(0, i);
        }
        return substring;
    }

    /**
     * @Description：查询所有代理
     * @Param
     * @Date 12:32 2021/5/11
     * <AUTHOR>
     */
    @Override
    public List<AgentVo> getAllAgent() {
        // Example example = new Example(Agent.class);
        // example.setOrderByClause("is_active desc,num");
        // List<Agent> agents = agentMapper.selectByExample(example);
        List<Agent> agents = agentMapper.selectList(Wrappers.<Agent>lambdaQuery().last(" order by is_active desc,num"));
        return agents.stream().map(agent -> BeanCopyUtils.objClone(agent, AgentVo::new)).collect(Collectors.toList());
    }

    @Override
    public Map<Long, String> getIsExistAgent(Long companyId, Long id, String name, String taxCode, String nature,
            String legalPerson, String idCard) {
        Map<Long, String> resultMap = new HashMap<>();
        String type = "";
        AgentVo isExistAgent = null;
        // 个人加身份证
        if (GeneralTool.isNotEmpty(idCard) && GeneralTool.isNotEmpty(nature)
                && ProjectExtraEnum.AGENT_NATURE_PERSON.key.toString().equals(nature)) {
            isExistAgent = agentMapper.getIsExistAgent(null, companyId, null, null, nature, null, idCard);
            type = "idCardNum";
        } else if (GeneralTool.isNotEmpty(taxCode) && GeneralTool.isNotEmpty(nature)
                && ProjectExtraEnum.AGENT_NATURE_COMPANY.key.toString().equals(nature)) {
            isExistAgent = agentMapper.getIsExistAgent(null, companyId, null, taxCode, nature, null, null);
            if (ObjectUtils.isNull(isExistAgent) || "/".equals(isExistAgent.getTaxCode())) {
                isExistAgent = null;
            }
            type = "tax";
        } else if (GeneralTool.isNotEmpty(idCard) && GeneralTool.isNotEmpty(nature)
                && ProjectExtraEnum.AGENT_NATURE_STUDIO.key.toString().equals(nature)) {
            isExistAgent = agentMapper.getIsExistAgent(null, companyId, null, null, nature, null, idCard);
            type = "workRoom";
        } else if (GeneralTool.isNotEmpty(idCard) && GeneralTool.isNotEmpty(nature)
                && ProjectExtraEnum.AGENT_NATURE_INTERNATIONAL_SCHOOL.key.toString().equals(nature)) {
            isExistAgent = agentMapper.getIsExistAgent(null, companyId, null, null, nature, null, idCard);
            type = "school";
        } else if (GeneralTool.isNotEmpty(name) && GeneralTool.isNotEmpty(nature)
                && ProjectExtraEnum.AGENT_NATURE_OTHER.key.toString().equals(nature)) {
            isExistAgent = agentMapper.getIsExistAgent(null, companyId, name, null, nature, null, null);
            type = "other";
        }
        StringBuilder sb = new StringBuilder();
        if (GeneralTool.isNotEmpty(isExistAgent)) {
            Agent agent = null;
            if (GeneralTool.isNotEmpty(id)) {
                agent = agentMapper.selectById(id);
            }
            String staffName = "";
            if (GeneralTool.isNotEmpty(isExistAgent.getFkStaffId())) {
                Result<String> staffNameResult = permissionCenterClient.getStaffName(isExistAgent.getFkStaffId());
                if (staffNameResult.isSuccess() && GeneralTool.isNotEmpty(staffNameResult)) {
                    staffName = staffNameResult.getData();
                }
            }
            if (GeneralTool.isNotEmpty(agent) && !agent.getId().equals(isExistAgent.getId())) {
                switch (type) {
                    case "idCardNum":
                        sb.append(LocaleMessageUtils.getMessage("PUBLIC_AGENT")).append("：")
                                .append(isExistAgent.getName()).append("，")
                                .append(LocaleMessageUtils.getMessage("agent_company")).append("：")
                                .append(isExistAgent.getCompanyName()).append("，")
                                .append(LocaleMessageUtils.getMessage("PROPERTIES_FOR")).append("：")
                                .append(LocaleMessageUtils.getMessage("PERSON")).append("，")
                                .append(LocaleMessageUtils.getMessage("IDENTITY_CARD")).append("：")
                                .append(isExistAgent.getIdCardNum())
                                .append(LocaleMessageUtils.getMessage("STUDENT_EXIST")).append("!<br/>")
                                .append(LocaleMessageUtils.getMessage("AGENT_STAFF")).append("：").append(staffName)
                                .append("；<br/>");
                        resultMap.put(isExistAgent.getId(), sb.toString());
                        return resultMap;
                    case "tax":
                        sb.append(LocaleMessageUtils.getMessage("PUBLIC_AGENT")).append("：")
                                .append(isExistAgent.getName()).append("，")
                                .append(LocaleMessageUtils.getMessage("agent_company")).append("：")
                                .append(isExistAgent.getCompanyName()).append("，")
                                .append(LocaleMessageUtils.getMessage("PROPERTIES_FOR")).append("：")
                                .append(LocaleMessageUtils.getMessage("COMPANY")).append("，")
                                .append(LocaleMessageUtils.getMessage("TAX_NUMBER_OT_THE_COMPANY")).append("：")
                                .append(isExistAgent.getTaxCode())
                                .append(LocaleMessageUtils.getMessage("STUDENT_EXIST")).append("!");
                        resultMap.put(isExistAgent.getId(), sb.toString());
                        return resultMap;
                    case "workRoom":
                        sb.append(LocaleMessageUtils.getMessage("PUBLIC_AGENT")).append("：")
                                .append(isExistAgent.getName()).append("，")
                                .append(LocaleMessageUtils.getMessage("agent_company")).append("：")
                                .append(isExistAgent.getCompanyName()).append("，")
                                .append(LocaleMessageUtils.getMessage("PROPERTIES_FOR")).append("：")
                                .append(LocaleMessageUtils.getMessage("WORKROOM")).append("，")
                                .append(LocaleMessageUtils.getMessage("IDENTITY_CARD")).append("：")
                                .append(isExistAgent.getLegalPerson())
                                .append(LocaleMessageUtils.getMessage("STUDENT_EXIST")).append("!");
                        resultMap.put(isExistAgent.getId(), sb.toString());
                        return resultMap;
                    case "school":
                        sb.append(LocaleMessageUtils.getMessage("PUBLIC_AGENT")).append("：")
                                .append(isExistAgent.getName()).append("，")
                                .append(LocaleMessageUtils.getMessage("agent_company")).append("：")
                                .append(isExistAgent.getCompanyName()).append("，")
                                .append(LocaleMessageUtils.getMessage("PROPERTIES_FOR")).append("：")
                                .append(LocaleMessageUtils.getMessage("INTERNATIONAL_SCHOOL")).append("，")
                                .append(LocaleMessageUtils.getMessage("IDENTITY_CARD")).append("：")
                                .append(isExistAgent.getLegalPerson())
                                .append(LocaleMessageUtils.getMessage("STUDENT_EXIST")).append("!");
                        resultMap.put(isExistAgent.getId(), sb.toString());
                        return resultMap;
                    case "other":
                        sb.append(LocaleMessageUtils.getMessage("PUBLIC_AGENT")).append("：")
                                .append(isExistAgent.getName()).append("，")
                                .append(LocaleMessageUtils.getMessage("agent_company")).append("：")
                                .append(isExistAgent.getCompanyName()).append("，")
                                .append(LocaleMessageUtils.getMessage("PROPERTIES_FOR")).append("：")
                                .append(LocaleMessageUtils.getMessage("OTHER")).append("，")
                                .append(LocaleMessageUtils.getMessage("AGENT_NAME")).append("：")
                                .append(isExistAgent.getName()).append(LocaleMessageUtils.getMessage("STUDENT_EXIST"))
                                .append("!");
                        resultMap.put(isExistAgent.getId(), sb.toString());
                        return resultMap;
                }
            } else if (GeneralTool.isEmpty(agent)) {
                switch (type) {
                    case "idCardNum":
                        sb.append(LocaleMessageUtils.getMessage("PUBLIC_AGENT")).append("：")
                                .append(isExistAgent.getName()).append("，")
                                .append(LocaleMessageUtils.getMessage("agent_company")).append("：")
                                .append(isExistAgent.getCompanyName()).append("，")
                                .append(LocaleMessageUtils.getMessage("PROPERTIES_FOR")).append("：")
                                .append(LocaleMessageUtils.getMessage("PERSON")).append("，")
                                .append(LocaleMessageUtils.getMessage("IDENTITY_CARD")).append("：")
                                .append(isExistAgent.getIdCardNum())
                                .append(LocaleMessageUtils.getMessage("STUDENT_EXIST")).append("!<br/>")
                                .append(LocaleMessageUtils.getMessage("AGENT_STAFF")).append("：").append(staffName)
                                .append("；<br/>");
                        resultMap.put(isExistAgent.getId(), sb.toString());
                        return resultMap;
                    case "tax":
                        sb.append(LocaleMessageUtils.getMessage("PUBLIC_AGENT")).append("：")
                                .append(isExistAgent.getName()).append("，")
                                .append(LocaleMessageUtils.getMessage("agent_company")).append("：")
                                .append(isExistAgent.getCompanyName()).append("，")
                                .append(LocaleMessageUtils.getMessage("PROPERTIES_FOR")).append("：")
                                .append(LocaleMessageUtils.getMessage("COMPANY")).append("，")
                                .append(LocaleMessageUtils.getMessage("TAX_NUMBER_OT_THE_COMPANY")).append("：")
                                .append(isExistAgent.getTaxCode())
                                .append(LocaleMessageUtils.getMessage("STUDENT_EXIST")).append("!");
                        resultMap.put(isExistAgent.getId(), sb.toString());
                        return resultMap;
                    case "workRoom":
                        sb.append(LocaleMessageUtils.getMessage("PUBLIC_AGENT")).append("：")
                                .append(isExistAgent.getName()).append("，")
                                .append(LocaleMessageUtils.getMessage("agent_company")).append("：")
                                .append(isExistAgent.getCompanyName()).append("，")
                                .append(LocaleMessageUtils.getMessage("PROPERTIES_FOR")).append("：")
                                .append(LocaleMessageUtils.getMessage("WORKROOM")).append("，")
                                .append(LocaleMessageUtils.getMessage("IDENTITY_CARD")).append("：")
                                .append(isExistAgent.getLegalPerson())
                                .append(LocaleMessageUtils.getMessage("STUDENT_EXIST")).append("!");
                        resultMap.put(isExistAgent.getId(), sb.toString());
                        return resultMap;
                    case "school":
                        sb.append(LocaleMessageUtils.getMessage("PUBLIC_AGENT")).append("：")
                                .append(isExistAgent.getName()).append("，")
                                .append(LocaleMessageUtils.getMessage("agent_company")).append("：")
                                .append(isExistAgent.getCompanyName()).append("，")
                                .append(LocaleMessageUtils.getMessage("PROPERTIES_FOR")).append("：")
                                .append(LocaleMessageUtils.getMessage("INTERNATIONAL_SCHOOL")).append("，")
                                .append(LocaleMessageUtils.getMessage("IDENTITY_CARD")).append("：")
                                .append(isExistAgent.getLegalPerson())
                                .append(LocaleMessageUtils.getMessage("STUDENT_EXIST")).append("!");
                        resultMap.put(isExistAgent.getId(), sb.toString());
                        return resultMap;
                    case "other":
                        sb.append(LocaleMessageUtils.getMessage("PUBLIC_AGENT")).append("：")
                                .append(isExistAgent.getName()).append("，")
                                .append(LocaleMessageUtils.getMessage("agent_company")).append("：")
                                .append(isExistAgent.getCompanyName()).append("，")
                                .append(LocaleMessageUtils.getMessage("PROPERTIES_FOR")).append("：")
                                .append(LocaleMessageUtils.getMessage("OTHER")).append("，")
                                .append(LocaleMessageUtils.getMessage("AGENT_NAME")).append("：")
                                .append(isExistAgent.getName()).append(LocaleMessageUtils.getMessage("STUDENT_EXIST"))
                                .append("!");
                        resultMap.put(isExistAgent.getId(), sb.toString());
                        return resultMap;
                }
            }
        }
        return resultMap;
    }

    @Override
    public Integer getAgentCompanyIdById(Long id) {
        return agentCompanyMapper.getAgentCompanyIdById(id);
    }

    // @Override
    // public List<UserInfoDto> getIssueUserByAgentId(Long fkAgentId) {
    // if (GeneralTool.isEmpty(fkAgentId)) {
    // throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
    // }
    // return platformCenterClient.getUserByAgentId(fkAgentId, null).getData();
    // }

    @Override
    public List<AgentSourceVo> getSource(AgentSourceDto agentSourceDto, Page page) {
        Long staffId = SecureUtil.getStaffId();
        // 获取员工以及旗下员工所创建的代理ids
        List<Long> staffFollowerIds = new ArrayList<>();
        Result<List<Long>> result = permissionCenterClient.getStaffFollowerIds(staffId);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            staffFollowerIds.addAll(result.getData());
        }
        // 添加员工自己id
        staffFollowerIds.add(staffId);

        String beginTime = "";
        String endTime = "";
        String opening_start_time = "";
        String opening_end_time = "";
        // 时间配置
        ConfigVo data = permissionCenterClient.getConfigByKey("GEA_SCORE_ACTIVITY").getData();
        System.out.println("data" + data);
        // 时间1
        if (GeneralTool.isNotEmpty(data) && GeneralTool.isNotEmpty(data.getValue1())) {
            JSONObject jsonObject = JSONObject.parseObject(data.getValue1());
            beginTime = jsonObject.getString("start_time");
            endTime = jsonObject.getString("end_time");
        }
        // 时间2
        if (GeneralTool.isNotEmpty(data) && GeneralTool.isNotEmpty(data.getValue2())) {
            JSONObject jsonObject2 = JSONObject.parseObject(data.getValue2());
            opening_start_time = jsonObject2.getString("opening_start_time");
            opening_end_time = jsonObject2.getString("opening_end_time");
        }

        // kpi排除学校列表
        StringBuilder fkInstitutionIdsExcluding = new StringBuilder();
        List<KpiInstitutionProvider> kpiInstitutionProviders = kpiInstitutionProviderMapper
                .selectList(new LambdaQueryWrapper<KpiInstitutionProvider>()
                        .isNotNull(KpiInstitutionProvider::getFkInstitutionIdsExcluding));
        for (KpiInstitutionProvider kpiInstitutionProvider : kpiInstitutionProviders) {
            if (GeneralTool.isBlank(fkInstitutionIdsExcluding)) {
                fkInstitutionIdsExcluding.append(kpiInstitutionProvider.getFkInstitutionIdsExcluding());
            } else {
                fkInstitutionIdsExcluding.append(",").append(kpiInstitutionProvider.getFkInstitutionIdsExcluding());
            }
        }

        // Set<Long> ids = new HashSet<>(staffFollowerIds);
        // //转成idsString
        // String splitSetString = "";
        // Object[] array = ids.toArray();
        // splitSetString = StringUtils.join(array, ",");
        List<AgentSourceVo> listSoure = new ArrayList<>();
        if (page != null) {
            IPage<CommissionSummaryVo> ipage = GetCondition
                    .getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
            listSoure = agentMapper.getSource(ipage,
                    agentSourceDto.getAgentName(), staffFollowerIds,
                    SecureUtil.getStaffInfo().getIsStudentOfferItemFinancialHiding(),
                    beginTime, endTime, opening_start_time, opening_end_time, fkInstitutionIdsExcluding.toString(),
                    null, null, null);
            page.setAll((int) ipage.getTotal());
        } else {
            listSoure = agentMapper.getSource(null,
                    agentSourceDto.getAgentName(), staffFollowerIds,
                    SecureUtil.getStaffInfo().getIsStudentOfferItemFinancialHiding(),
                    beginTime, endTime, opening_start_time, opening_end_time, fkInstitutionIdsExcluding.toString(),
                    null, null, null);
        }
        for (AgentSourceVo agentSourceVo : listSoure) {
            agentSourceVo.setFkCompanyId(2L);
            agentSourceVo.setBeginTime(beginTime.substring(0, 10));
            agentSourceVo.setEndTime(endTime.substring(0, 10));
            agentSourceVo.setOpeningStartTime(opening_start_time.substring(0, 10));
            agentSourceVo.setOpeningEndTime(opening_end_time.substring(0, 10));
        }
        return listSoure;
    }

    /**
     * @Description: 导出积分excel
     * @Param: agentSourceDto
     * @return:
     * @Author: Walker
     * @Date: 2022/3/16
     */
    @Override
    public void exportAgentSourceExcel(HttpServletResponse response, AgentSourceDto agentSourceDto) {
        // Long staffId = SecureUtil.getStaffId();
        // //获取员工以及旗下员工所创建的代理ids
        // List<Long> staffFollowerIds = new ArrayList<>();
        // Result<List<Long>> result =
        // permissionCenterClient.getStaffFollowerIds(staffId);
        // if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
        // staffFollowerIds.addAll(result.getData());
        // }
        // //添加员工自己id
        // staffFollowerIds.add(staffId);
        // Set<Long> ids = new HashSet<>(staffFollowerIds);
        // //转成idsString
        // String splitSetString = "";
        // if (staffFollowerIds.size() != 1) {
        // Object[] array = ids.toArray();
        // splitSetString = StringUtils.join(array, ",");
        // }
        // String beginTime = "";
        // String endTime = "";
        // String opening_start_time = "";
        // String opening_end_time = "";
        // //时间配置
        // ConfigVo data =
        // permissionCenterClient.getConfigByKey("GEA_SCORE_ACTIVITY").getData();
        // System.out.println("data"+data);
        // //时间1
        // if (GeneralTool.isNotEmpty(data)&&GeneralTool.isNotEmpty(data.getValue1())) {
        // JSONObject jsonObject = JSONObject.parseObject(data.getValue1());
        // beginTime = jsonObject.getString("start_time");
        // endTime = jsonObject.getString("end_time");
        // }
        // //时间2
        // if (GeneralTool.isNotEmpty(data)&&GeneralTool.isNotEmpty(data.getValue2())) {
        // JSONObject jsonObject2 = JSONObject.parseObject(data.getValue2());
        // opening_start_time = jsonObject2.getString("opening_start_time");
        // opening_end_time = jsonObject2.getString("opening_end_time");
        // }
        // List<AgentSourceVo> list =
        // agentMapper.getSource(agentSourceDto.getAgentName(),staffFollowerIds,
        // splitSetString,SecureUtil.getStaffInfo().getIsStudentOfferItemFinancialHiding(),beginTime,endTime,opening_start_time,opening_end_time);
        // List<AgentSourceExcelVo> listSoure = new ArrayList<>();
        // for (AgentSourceVo agentSourceDto : list) {
        // AgentSourceExcelVo aseDto = new AgentSourceExcelVo();
        // aseDto.setAdmittedScore(agentSourceDto.getAdmittedScore());
        // aseDto.setAgentName(agentSourceDto.getAgentName());
        // //aseDto.setCrtScore(agentSourceDto.getCrtScore());
        // aseDto.setKpiScore(agentSourceDto.getKpiScore());
        // aseDto.setTtlScore(agentSourceDto.getTtlScore());
        // aseDto.setNoAdmittedCount(agentSourceDto.getNoAdmittedCount());
        // aseDto.setBdNames(agentSourceDto.getBdNames());
        // listSoure.add(aseDto);
        // }
        List<AgentSourceVo> listSoure = getSource(agentSourceDto, null);
        List<AgentSourceExcelVo> agentSourceExcelVos = BeanUtil.copyProperties(listSoure, AgentSourceExcelVo.class);
        FileUtils.exportExcelNotWrapText(response, agentSourceExcelVos, "AgentSource", AgentSourceExcelVo.class);
    }

    @Override
    public AgentSourceVo getSumSourceByCppIdOrBmsId(String cpp_id, String bms_id, String json) {
        if (GeneralTool.isNotEmpty(json)) {
            JSONObject jsonObject = JSONObject.parseObject(json);
            String cpp_id1 = jsonObject.getString("cpp_id");
            String bms_id1 = jsonObject.getString("bms_id");
            if (GeneralTool.isNotEmpty(cpp_id1)) {
                cpp_id = cpp_id1;
            }
            if (GeneralTool.isNotEmpty(bms_id1)) {
                bms_id = bms_id1;
            }
        }
        String beginTime = "";
        String endTime = "";
        String opening_start_time = "";
        String opening_end_time = "";
        // 时间配置
        ConfigVo data = permissionCenterClient.getConfigByKey("GEA_SCORE_ACTIVITY").getData();
        System.out.println("data" + data);
        // 时间1
        if (GeneralTool.isNotEmpty(data) && GeneralTool.isNotEmpty(data.getValue1())) {
            JSONObject jsonObject = JSONObject.parseObject(data.getValue1());
            beginTime = jsonObject.getString("start_time");
            endTime = jsonObject.getString("end_time");
        }
        // 时间2
        if (GeneralTool.isNotEmpty(data) && GeneralTool.isNotEmpty(data.getValue2())) {
            JSONObject jsonObject2 = JSONObject.parseObject(data.getValue2());
            opening_start_time = jsonObject2.getString("opening_start_time");
            opening_end_time = jsonObject2.getString("opening_end_time");
        }

        String state = "";
        if (GeneralTool.isNotEmpty(bms_id)) {// 优先判断bms_id
            state = "1";
        } else {
            state = "2";
        }
        // kpi排除学校列表
        StringBuilder fkInstitutionIdsExcluding = new StringBuilder();
        List<KpiInstitutionProvider> kpiInstitutionProviders = kpiInstitutionProviderMapper
                .selectList(new LambdaQueryWrapper<KpiInstitutionProvider>()
                        .isNotNull(KpiInstitutionProvider::getFkInstitutionIdsExcluding));
        for (KpiInstitutionProvider kpiInstitutionProvider : kpiInstitutionProviders) {
            if (GeneralTool.isBlank(fkInstitutionIdsExcluding)) {
                fkInstitutionIdsExcluding.append(kpiInstitutionProvider.getFkInstitutionIdsExcluding());
            } else {
                fkInstitutionIdsExcluding.append(",").append(kpiInstitutionProvider.getFkInstitutionIdsExcluding());
            }
        }
        List<AgentSourceVo> listSoure = agentMapper.getSource(null,
                null, null, true,
                beginTime, endTime, opening_start_time, opening_end_time, fkInstitutionIdsExcluding.toString(), cpp_id,
                bms_id, state);

        List<AgentSourceVo> listAll = agentMapper.getSourceListAll(true, beginTime, endTime, opening_start_time,
                opening_end_time);
        // AgentSourceVo agentSourceVo = agentMapper.getSumSourceByCppIdOrBmsId(cpp_id,
        // bms_id, state , true,beginTime,endTime,opening_start_time,opening_end_time);
        if (GeneralTool.isEmpty(listSoure)) {
            AgentSourceVo sourceDto = new AgentSourceVo();
            sourceDto.setRank(51);
            sourceDto.setRankName("继续加油 挺进50强");
            return sourceDto;
        }
        AgentSourceVo agentSourceVo = listSoure.get(0);
        for (int i = 0; i < listAll.size(); i++) {
            if (listAll.get(i).getTtlScore().equals(agentSourceVo.getTtlScore())) {
                if (i <= 9) {
                    agentSourceVo.setRank(i + 1);
                    agentSourceVo.setRankName("Top10");
                } else if (i <= 19) {
                    agentSourceVo.setRank(i + 1);
                    agentSourceVo.setRankName("Top20");
                } else if (i <= 29) {
                    agentSourceVo.setRank(i + 1);
                    agentSourceVo.setRankName("Top30");
                } else if (i <= 39) {
                    agentSourceVo.setRank(i + 1);
                    agentSourceVo.setRankName("Top40");
                } else if (i <= 49) {
                    agentSourceVo.setRank(i + 1);
                    agentSourceVo.setRankName("Top50");
                } else {
                    agentSourceVo.setRank(51);
                    agentSourceVo.setRankName("继续加油 挺进50强");
                }
                break;
            } else {
                agentSourceVo.setRank(51);
                agentSourceVo.setRankName("继续加油 挺进50强");
            }
        }
        return agentSourceVo;
    }

    @Override
    public List<BaseSelectEntity> getContactPersonAgentList(Long companyId) {
        Long staffId = SecureUtil.getStaffId();
        List<Long> staffFollowerIds = permissionCenterClient.getStaffFollowerIds(staffId).getData();
        staffFollowerIds.add(staffId);
        staffFollowerIds = staffFollowerIds.stream().distinct().collect(Collectors.toList());
        List<Agent> agents = agentMapper.getContactPersonAgentList(staffFollowerIds, SecureUtil.getFkCompanyId(),
                companyId);
        return agents.stream().map(agent -> BeanCopyUtils.objClone(agent, BaseSelectEntity::new))
                .collect(Collectors.toList());
    }

    /**
     * 导出代理列表Excel
     *
     * @param response
     * @param agentVo
     */
    @Override
    public void exportAgentDatasExcel(HttpServletResponse response, AgentQueryDto agentVo) {
        // 此方法为导出方法
        agentVo.setIsExport(true);
        List<AgentVo> agentVos = getAgents(agentVo, null);
        if (GeneralTool.isEmpty(agentVos)) {
            return;
        }
        List<AgentExportVo> agentExportVos = new ArrayList<>();
        for (AgentVo agentDto : agentVos) {
            AgentExportVo agentExportVo = BeanCopyUtils.objClone(agentDto, AgentExportVo::new);
            if (GeneralTool.isNotEmpty(agentExportVo)) {
                if (GeneralTool.isNotEmpty(agentDto.getAreaRegionDtos())) {
                    StringJoiner sj = new StringJoiner(";");
                    for (AreaRegionVo areaRegionVo : agentDto.getAreaRegionDtos()) {
                        String regionName = areaRegionVo.getFkAreaCountryName()
                                + (GeneralTool.isNotEmpty(areaRegionVo.getNameChn()) ? "-" + areaRegionVo.getNameChn()
                                        : "");
                        sj.add(regionName);
                    }
                    agentExportVo.setAreaRegionName(sj.toString());
                }

                if (GeneralTool.isNotEmpty(agentDto.getAgentLabelVos())) {
                    agentExportVo.setAgentLabelNames(agentDto.getAgentLabelVos().stream()
                            .map(vo -> "【" + vo.getLabelName() + "】").collect(Collectors.joining(" ")));
                }

                agentExportVo.setIsActiveName(
                        GeneralTool.isNotEmpty(agentDto.getIsActive()) ? agentDto.getIsActive() ? "是" : "否" : "否");

                agentExportVo.setIsCustomerChannelName(GeneralTool.isNotEmpty(agentDto.getIsCustomerChannel())
                        ? agentDto.getIsCustomerChannel() ? "是" : "否"
                        : "");

                agentExportVo.setBdCodeAndName(agentDto.getBdCode() + "/" + agentDto.getBdName());
                if (GeneralTool.isNotEmpty(agentDto.getParentAgentNum())
                        && GeneralTool.isNotEmpty(agentDto.getParentAgentName())) {
                    agentExportVo.setParentAgentNumAndName(
                            agentDto.getParentAgentNum() + "/" + agentDto.getParentAgentName());
                } else {
                    agentExportVo.setParentAgentNumAndName("");
                }

                agentExportVo.setCountryAndStateName(agentDto.getCountryName() + "/" + agentDto.getStateName());
                if (GeneralTool.isNotEmpty(agentDto.getGmtCreate())) {
                    String createDate = GetDateUtil.formatDate(agentDto.getGmtCreate(), "yyyy-MM-dd HH:mm:ss");
                    agentExportVo.setGmtCreateStr(createDate);
                }

                if (GeneralTool.isNotEmpty(agentDto.getFirstContractTime())) {
                    String firstContractTime = GetDateUtil.formatDate(agentDto.getFirstContractTime(),
                            "yyyy-MM-dd HH:mm:ss");
                    agentExportVo.setFirstContractTimeStr(firstContractTime);
                }

                agentExportVos.add(agentExportVo);
            }
        }

        Integer flag = 0;
        Set<String> ignoreFields = Sets.newHashSet();
        // ConfigVo configDto =
        // permissionCenterClient.getConfigByKey(ProjectKeyEnum.AGENT_INFO_OPT_LIMIT.key).getData();
        // JSONObject jsonObject = JSONObject.parseObject(configDto.getValue3());
        // if (SecureUtil.getFkCompanyId().equals(3L)){
        // flag = jsonObject.getInteger("IAE");
        // }else {
        // flag = jsonObject.getInteger("OTHER");
        // }
        Map<Long, String> companyConfigMap = permissionCenterClient
                .getCompanyConfigMap(ProjectKeyEnum.AGENT_INFO_OPT_LIMIT.key, 3).getData();
        flag = Integer.valueOf(companyConfigMap.get(SecureUtil.getFkCompanyId()));
        if (flag == 0) {
            ignoreFields.add("isCustomerChannelName");
        }
        if (GeneralTool.isNotEmpty(ignoreFields)) {
            Map<String, String> fileMap = FileUtils.getFileMapIgnoreSomeField(AgentExportVo.class, ignoreFields);
            FileUtils.exportExcelNotWrapText(response, agentExportVos, "Agent", fileMap);
        } else {
            FileUtils.exportExcelNotWrapText(response, agentExportVos, "Agent", AgentExportVo.class);
        }
    }

    @Override
    public Long doGetAgentIdByPayablePlanId(Long payablePlanId) {
        return agentMapper.getAgentIdByPayablePlanId(payablePlanId);
    }

    @Override
    public List<AgentSubVo> getAgentListByName(List<Long> companyIds, String agentName) {
        // 判断是否进行权限控制
        Map<Long, String> companyConfigMap = permissionCenterClient
                .getCompanyConfigMap(ProjectKeyEnum.STUDENT_AGENT_BINDING_LIST_LIMIT.key, 1).getData();
        Integer value = Integer.valueOf(companyConfigMap.get(SecureUtil.getFkCompanyId()));
        // Map<Long, Integer> companySettlementConfigInfoMap =
        // permissionCenterClient.getCompanySettlementConfigInfoMap(ProjectKeyEnum.STUDENT_AGENT_BINDING_LIST_LIMIT.key);
        // Integer value =
        // companySettlementConfigInfoMap.get(SecureUtil.getFkCompanyId());

        // 获取业务下属
        Long staffId = SecureUtil.getStaffId();
        // 员工id + 业务下属员工ids
        List<Long> staffFollowerIds = permissionCenterClient.getStaffFollowerIds(staffId).getData();
        staffFollowerIds.add(staffId);
        List<AgentSubVo> agents = agentMapper.getAgentListByName(companyIds, agentName, staffFollowerIds, value);
        for (AgentSubVo agent : agents) {
            List<Agent> agentList = agentMapper
                    .selectList(Wrappers.<Agent>lambdaQuery().eq(Agent::getFkParentAgentId, agent.getId()));
            Set<Long> ids = agentList.stream().map(Agent::getId).collect(Collectors.toSet());
            agent.setSonAgentIds(ids);
        }
        return agents;
    }

    @Override
    public List<ContactPersonVo> getContactPersonInfo(Long id) {
        LambdaQueryWrapper<SaleContactPerson> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(SaleContactPerson::getFkTableId, id);
        lambdaQueryWrapper.eq(SaleContactPerson::getFkTableName, TableEnum.SALE_AGENT.key);
        // lambdaQueryWrapper.ne(SaleContactPerson::getFkContactPersonTypeKey,
        // "CONTACT_AGENT_CONTRACT");
        // 修复多选的情况
        lambdaQueryWrapper.apply("FIND_IN_SET({0},fk_contact_person_type_key)=0", "CONTACT_AGENT_CONTRACT");
        List<SaleContactPerson> contactPersons = contactPersonService.getContactPersonByCondition(lambdaQueryWrapper);
        return BeanCopyUtils.copyListProperties(contactPersons, ContactPersonVo::new);
    }

    @Override
    public List<ContactPersonVo> getAgentContactPersonInfo(Long id) {
        LambdaQueryWrapper<SaleContactPerson> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(SaleContactPerson::getFkTableId, id);
        lambdaQueryWrapper.eq(SaleContactPerson::getFkTableName, TableEnum.SALE_AGENT.key);
        // lambdaQueryWrapper.eq(SaleContactPerson::getFkContactPersonTypeKey,
        // "CONTACT_AGENT_CONTRACT");
        // 修复多选的情况
        lambdaQueryWrapper.apply("FIND_IN_SET({0},fk_contact_person_type_key)>0", "CONTACT_AGENT_CONTRACT");
        List<SaleContactPerson> contactPersons = contactPersonService.getContactPersonByCondition(lambdaQueryWrapper);
        return BeanCopyUtils.copyListProperties(contactPersons, ContactPersonVo::new);
    }

    @Override
    public Agent getAgentById(Long fkAgentId) {
        return agentMapper.selectById(fkAgentId);
    }

    /**
     * 绑定默认项目成员列表
     *
     * @param companyId
     * @return
     */
    @Override
    public List<StudentProjectRoleVo> getAgentDefaultProjectRole(Long companyId) {

        // 获取角色key
        List<String> projectRoleKeys = doGetProjectRoleKeys(companyId);

        // 获取studentProjectRoles
        List<StudentProjectRole> studentProjectRoles = doGetStudentProjectRoles(projectRoleKeys);

        // 获取studentProjectRoleDtos 格式化角色名称
        return doGetStudentProjectRoleDtos(companyId, studentProjectRoles);
    }

    @Override
    public Agent getIaeAgentById(Long agentId) {
        if (agentId == null) {
            return null;
        }
        return agentMapper.getIaeAgentById(agentId);
    }

    private List<StudentProjectRoleVo> doGetStudentProjectRoleDtos(Long companyId,
            List<StudentProjectRole> studentProjectRoles) {
        if (GeneralTool.isEmpty(studentProjectRoles)) {
            return Collections.emptyList();
        }
        List<StudentProjectRoleVo> studentProjectRoleVos = BeanCopyUtils.copyListProperties(studentProjectRoles,
                StudentProjectRoleVo::new);
        String companyName = permissionCenterClient.getCompanyNameById(companyId).getData();
        for (StudentProjectRoleVo studentProjectRoleVo : studentProjectRoleVos) {
            studentProjectRoleVo.setCompanyName(companyName);
            // 格式化：【公司】角色名称（key）
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append('【')
                    .append(companyName)
                    .append('】')
                    .append(studentProjectRoleVo.getRoleName())
                    .append('（')
                    .append(studentProjectRoleVo.getRoleKey())
                    .append('）');
            studentProjectRoleVo.setFormatName(stringBuilder.toString());
        }
        return studentProjectRoleVos;
    }

    private List<StudentProjectRole> doGetStudentProjectRoles(List<String> projectRoleKeys) {
        if (GeneralTool.isNotEmpty(projectRoleKeys)) {
            return studentProjectRoleService.list(Wrappers.<StudentProjectRole>lambdaQuery()
                    .in(StudentProjectRole::getRoleKey, projectRoleKeys).orderByDesc(StudentProjectRole::getViewOrder));
        }
        return Collections.emptyList();
    }

    /**
     * 获取角色key
     *
     * @param companyId
     * @return
     */
    private List<String> doGetProjectRoleKeys(Long companyId) {
        ConfigVo configVo = permissionCenterClient.getConfigByKey(ProjectKeyEnum.AGENT_PROJECT_ROLE.key).getData();
        if (GeneralTool.isNotEmpty(configVo) && GeneralTool.isNotEmpty(configVo.getValue4())) {
            // {"2":
            // ["GEA_AD_UK","GEA_ARC_UK","GEA_LI_UK","GEA_RC_UK","GEA_AD_AU","GEA_ARC_AU","GEA_LI_AU","GEA_RC_AU","GEA_AD_US","GEA_ARC_US"],
            // "3": ["IAE_COORDINATOR","IAE_COUNSELLING_SUPPORT","IAE_ASSISTANT"],
            // "4": ["INDEX_CM","INDEX_ARC"],
            // "5": ["SEA_ARC","SEA_CM"]}
            String defaultProjectRoleJsonStr = configVo.getValue4();
            JSONObject parseObject = JSONObject.parseObject(defaultProjectRoleJsonStr);
            String companyKey = String.valueOf(companyId);
            com.alibaba.fastjson.JSONArray jsonArray = parseObject.getJSONArray(companyKey);
            if (GeneralTool.isEmpty(jsonArray)) {
                return Collections.emptyList();
            }
            return jsonArray.toJavaList(String.class);
        }
        return Collections.emptyList();
    }

    @Override
    public List<Long> getAgentIdByEmail(String email) {
        return agentMapper.getAgentIdByEmail(email);
    }

    @Override
    public AgenCommissionAndAgentSearchVo getAgentCommissionTypeAndAgentIsBind(
            AgentCommissionTypeAgentDto agentCommissionTypeAgentDto, Page page) {

        AgenCommissionAndAgentSearchVo agenCommissionAndAgentSearchVo = new AgenCommissionAndAgentSearchVo();
        List<AgentVo> convertDatas = new ArrayList<>();

        // 获取业务下属
        Long staffId = SecureUtil.getStaffId();
        // 员工id + 业务下属员工ids
        List<Long> staffFollowerIds = permissionCenterClient.getStaffFollowerIds(staffId).getData();
        staffFollowerIds.add(staffId);
        List<AgentVo> agents = new ArrayList<>();
        if (page == null) {
            agents = agentMapper.getAgentCommissionTypeAndAgentIsBind(null, agentCommissionTypeAgentDto,
                    staffFollowerIds, SecureUtil.getCompanyIds(), SecureUtil.getCountryIds());

        } else {
            IPage<Agent> iPage = GetCondition
                    .getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
            // 获取分页数据
            agents = agentMapper.getAgentCommissionTypeAndAgentIsBind(iPage, agentCommissionTypeAgentDto,
                    staffFollowerIds, SecureUtil.getCompanyIds(), SecureUtil.getCountryIds());
            page.setAll((int) iPage.getTotal());
        }
        // List<AgentVo> agents =
        // agentMapper.getAgentsAll(agenCommissionAndAgentSearchDto.getData(),
        // staffFollowerIds, SecureUtil.getCompanyIds(), SecureUtil.getCountryIds());
        if (GeneralTool.isEmpty(agents)) {
            return new AgenCommissionAndAgentSearchVo();
        }
        // 公司map
        Map<String, String> companyMap = getCompanyMap();
        // 国家ids
        Set<Long> countryIds = agents.stream().map(AgentVo::getFkAreaCountryId).collect(Collectors.toSet());
        // 州省ids
        Set<Long> stateIds = agents.stream().map(AgentVo::getFkAreaStateId).collect(Collectors.toSet());
        // 城市ids
        Set<Long> cityIds = agents.stream().map(AgentVo::getFkAreaCityId).collect(Collectors.toSet());
        // 根据国家ids获取国家名称
        Map<Long, String> countryNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(countryIds)) {
            Result<Map<Long, String>> countryNameByIdsResult = institutionCenterClient.getCountryNamesByIds(countryIds);
            if (countryNameByIdsResult.isSuccess() && GeneralTool.isNotEmpty(countryNameByIdsResult.getData())) {
                countryNamesByIds = countryNameByIdsResult.getData();
            }
        }
        // 根据州省ids获取州省名称
        Map<Long, String> stateNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(stateIds)) {
            Result<Map<Long, String>> result = institutionCenterClient.getStateFullNamesByIds(stateIds);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                stateNamesByIds = result.getData();
            }
        }
        // 根据州省ids获取州省名称
        Map<Long, String> cityNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(cityIds)) {
            Result<Map<Long, String>> result = institutionCenterClient.getCityFullNamesByIds(cityIds);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                cityNamesByIds = result.getData();
            }
        }

        // 代理ids
        Set<Long> agentIds = agents.stream().map(AgentVo::getFkAgentId).collect(Collectors.toSet());
        // 通过代理ids获取激活的代理员工BD编号对象
        Map<Long, AgentStaff> agentStaffByAgentIds = new HashMap<>();
        // 根据代理ids查询公司ids
        Map<Long, Set<Long>> relationByAgentIds = new HashMap<>();
        // 代理标签
        Map<Long, List<AgentLabelVo>> agentLabelMap = new HashMap<>();

        if (GeneralTool.isNotEmpty(agentIds)) {
            agentStaffByAgentIds = agentStaffService.getAgentStaffByAgentIds(agentIds);
            relationByAgentIds = companyService.getRelationByAgentIds(agentIds);
            agentLabelMap = getAgentLabelDataUtils.getAgentLabelMap(agentIds).getAgentLabelMap();

        }

        // 获取所有代理员工
        Set<Long> fkStaffIds = new HashSet<>();
        Map<Long, StaffBdCode> bDbyStaffIds = new HashMap<>();
        Map<Long, String> staffNamesByIds = new HashMap<>();
        for (Map.Entry<Long, AgentStaff> longAgentStaffEntry : agentStaffByAgentIds.entrySet()) {
            fkStaffIds.add(longAgentStaffEntry.getValue().getFkStaffId());
        }
        if (GeneralTool.isNotEmpty(fkStaffIds)) {
            // 根据staffIds获取对应bdCode
            bDbyStaffIds = staffBdCodeService.getBDbyStaffIds(fkStaffIds);
            // 根据staffIds获取员工姓名
            Result<Map<Long, String>> staffNameResult = permissionCenterClient.getStaffNameMap(fkStaffIds);
            if (staffNameResult.isSuccess() && GeneralTool.isNotEmpty(staffNameResult)) {
                staffNamesByIds = staffNameResult.getData();
            }
        }

        long startTime = System.currentTimeMillis(); // 记录开始时间
        for (AgentVo agentVo : agents) {
            setNames(agentVo, companyMap, countryNamesByIds, stateNamesByIds,
                    cityNamesByIds, agentStaffByAgentIds, relationByAgentIds,
                    bDbyStaffIds, staffNamesByIds);

            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append(agentVo.getNameNote())
                    .append("/")
                    .append(agentVo.getCompanyName());
            agentVo.setNameNoteAndCompanyName(stringBuilder.toString());
            if (GeneralTool.isNotEmpty(agentVo.getFkAgentId())) {
                getAgentLabelDataUtils.setAgentLabelVosByLabelMap(agentVo, agentLabelMap, agentVo.getFkAgentId(),
                        AgentVo::setAgentLabelVos);
            }

            convertDatas.add(agentVo);
        }
        long endTime = System.currentTimeMillis(); // 记录结束时间
        long duration = endTime - startTime; // 计算耗时（毫秒）
        System.out.println("执行耗时for外层: " + duration + " 毫秒");

        agenCommissionAndAgentSearchVo.setAgentCommissionAndAgentList(convertDatas);
        agenCommissionAndAgentSearchVo.setPage(page);
        return agenCommissionAndAgentSearchVo;

    }

    /**
     * 发送代理合同续约邮件
     *
     * @param agentContractRenewalDto 代理合同续约DTO，包含代理ID和联系人ID
     */
    @Override
    public void sendEmailRenewalEmail(AgentContractRenewalDto agentContractRenewalDto) {
        log.info("开始处理代理合同续签, 参数: {}", agentContractRenewalDto);

        // 代理id
        Long agentId = agentContractRenewalDto.getAgentId();
        // 联系人id
        Long contactPersonId = agentContractRenewalDto.getContactPersonId();

        // 获取代理信息
        Agent agent = this.getById(agentId);
        if (Objects.isNull(agent)) {
            log.error("代理信息不存在");
            throw new GetServiceException(LocaleMessageUtils.getMessage("agent_not_exist"));
        }

        // 校验合同状态是否允许续约
        AgentContract latestActiveAgentContract = agentContractService.latestActiveAgentContract(agentId);
        if (latestActiveAgentContract != null) {
            // 只有存在合同时才进行状态校验，状态不是"未签署、待审核、已驳回"才允许续约
            Integer contractApprovalStatus = latestActiveAgentContract.getContractApprovalStatus();
            if (!AgentContractApprovalStatusEnum.isRenewalApplicationAllowed(contractApprovalStatus)) {
                log.error("代理最新合同审批状态不允许续约，当前状态: {}({}), 代理ID: {}, 合同ID: {}",
                        contractApprovalStatus, contractApprovalStatus, agentId, latestActiveAgentContract.getId());
                throw new GetServiceException(LocaleMessageUtils.getMessage("agent_status_not_allow_renewal"));
            }
        }

        // 3. 获取联系人信息
        SaleContactPerson saleContactPerson = this.contactPersonService.getById(contactPersonId);
        if (Objects.isNull(saleContactPerson)) {
            log.error("联系人信息不存在，联系人ID: {}", contactPersonId);
            throw new GetServiceException(LocaleMessageUtils.getFormatMessage("contact_person_not_found", contactPersonId));
        }

        // 校验代理数据
        AgentStaff agentStaff = this.agentStaffService.getAgentStaffByAgentId(agentId);
        // 代理续约后创建申请数据
        try {
            // 发送代理合同续签邮件（给联系人和BD员工）
            sendRenewalEmailToContactAndStaff(saleContactPerson, agent, agentStaff);
            log.info("代理续约邮件发送成功，代理ID: {}, 联系人: {}", agentId, saleContactPerson.getEmail());
        } catch (Exception e) {
            log.error("代理续约邮件发送失败，代理ID: {}, 错误: {}", agentId, e.getMessage(), e);
            // 不影响主流程，记录错误即可
        }

    }

    /**
     * 发送续签邮件给联系人和BD员工
     *
     * @param saleContactPerson 联系人信息
     * @param agent 代理信息
     * @param agentStaff 代理员工信息
     */
    private void sendRenewalEmailToContactAndStaff(SaleContactPerson saleContactPerson, Agent agent, AgentStaff agentStaff) {
        try {
            log.info("开始发送续签邮件，代理ID: {}", agent.getId());
            List<EmailSendContext> emailContexts = new ArrayList<>();
            
            // 构建联系人邮件
            if (saleContactPerson != null && StringUtils.isNotBlank(saleContactPerson.getEmail())) {
                String[] emails = saleContactPerson.getEmail().split("; ");
                if (ArrayUtil.isEmpty(emails) || StringUtils.isBlank(emails[0])) {
                    return;
                }
                String email = emails[0];
                EmailSendContext contactContext = buildRenewalEmailContext(
                        email,
                        saleContactPerson.getName(),
                        agent.getId(),
                        agentStaff != null ? agentStaff.getFkStaffId() : null
                );
                emailContexts.add(contactContext);
                log.debug("构建联系人续签邮件上下文，收件人: {}, 代理ID: {}", email, agent.getId());
            }


            // 给BD员工发送相同内容的邮件副本
            sendBdEmailCopiesForRenewal(emailContexts, agent.getId(), agentStaff != null ? agentStaff.getFkStaffId() : null);

            // 批量发送邮件
            if (!emailContexts.isEmpty()) {
                emailSenderUtils.sendBatchEmails(emailContexts, agent.getId());
                log.info("成功发送续签邮件，代理ID: {}, 邮件数量: {}", agent.getId(), emailContexts.size());
            } else {
                log.warn("没有有效的收件人，跳过邮件发送，代理ID: {}", agent.getId());
            }

        } catch (Exception e) {
            log.error("发送续签邮件失败，代理ID: {}, 错误: {}", agent.getId(), e.getMessage(), e);
            throw e; // 重新抛出异常，让上层处理
        }
    }

    /**
     * 构建续签邮件上下文
     *
     * @param recipientEmail 收件人邮箱
     * @param recipientName 收件人姓名
     * @param agentId 代理ID
     * @param staffId 员工ID
     * @return 邮件发送上下文
     */
    private EmailSendContext buildRenewalEmailContext(String recipientEmail, String recipientName, Long agentId, Long staffId) {
        String tokenKey = CacheKeyConstants.SALE_AGENT_RENEWAL_PREFIX + agentId;
        String token = this.getRedis.get(tokenKey);
        if (StringUtils.isBlank(token)) {
            token = UUID.randomUUID().toString().replace("-", "");
            this.getRedis.set(tokenKey, token);
        }

        // 新申请拒绝邮件 - 使用加密的appAgentId（逻辑不变）
        String encryptedAgentId;
        try {
            encryptedAgentId = AESUtils.Encrypt(String.valueOf(agentId), AESConstant.AESKEY);
        } catch (Exception e) {
            log.error("代理申请ID加密失败", e);
            throw new GetServiceException(LocaleMessageUtils.getMessage("sign_encryption_failed"));
        }

        // 构建邮件参数
        Map<String, String> emailParams = new HashMap<>();
        emailParams.put("personalName", recipientName); // 收件人姓名
        emailParams.put("name", recipientName); // 收件人姓名（与personalName相同）
        emailParams.put("sign", encryptedAgentId); // 代理ID
        emailParams.put("renewalToken", token); // 代理ID

        // 构建二维码路径（使用续约申请页面，统一参数名为id）
        String qrcodePath = MiniProgramPageEnum.RENEWAL_APPLY.buildFullPath(URLUtil.encodeAll(encryptedAgentId));
        if (StringUtils.isNotBlank(token)) {
            qrcodePath = qrcodePath + "&renewalToken=" + token;
        }
        emailParams.put("qrcode", qrcodePath);
        
        // 添加staffId用于邮件国际化
        if (staffId != null) {
            emailParams.put("staffId", staffId.toString());
        }

        // 构建邮件上下文
        return EmailSendContext.builder()
                .projectKey(ProjectKeyEnum.SALE_CENTER)
                .tableName(TableEnum.SALE_AGENT)
                .tableId(agentId)
                .recipient(recipientEmail)
                .emailTemplate(EmailTemplateEnum.AGENT_CONTRACT_RENEWAL)
                .parameters(emailParams)
                .build();
    }


    /**
     * 代理续约后创建申请数据
     *
     * @param agent      代理信息
     * @param agentStaff BD员工信息
     * @return 创建的申请ID
     */
    private Long createRenewalAppAgent(Agent agent, AgentStaff agentStaff) {
        log.info("开始创建代理续约申请数据，代理ID: {}", agent.getId());

        // 1. 构造AppAgent主记录
        AppAgent appAgent = buildAppAgentFromAgent(agent, agentStaff.getFkStaffId());

        // 2. 保存申请记录
        utilService.setCreateInfo(appAgent);
        boolean saved = appAgentService.save(appAgent);
        if (!saved) {
            log.error("保存代理续约申请失败，代理ID: {}", agent.getId());
            throw new GetServiceException(LocaleMessageUtils.getMessage("save_agent_application_failed"));
        }
        Long appAgentId = appAgent.getId();

        // 3. 查询并批量创建联系人记录
        createAppAgentContactPersonBatch(agent.getId(), appAgentId);

        // 4. 复制合同账户记录
        copyAgentContractAccountToAppAgent(agent.getId(), appAgentId);

        // 5. 复制附件记录
        copyAgentAttachmentsToAppAgent(agent.getId(), appAgentId);

        log.info("代理续约申请数据创建完成，申请ID: {}", appAgentId);
        return appAgentId;
    }

    /**
     * 构造AppAgent主记录（从Agent反向构造）
     *
     * @param agent     原代理信息
     * @param fkStaffId BD员工ID
     * @return AppAgent对象
     */
    private AppAgent buildAppAgentFromAgent(Agent agent, Long fkStaffId) {
        AppAgent appAgent = new AppAgent();

        // 基本信息直接复制
        appAgent.setFkCompanyId(SecureUtil.getFkCompanyId());
        appAgent.setFkAreaCountryId(agent.getFkAreaCountryId());
        appAgent.setFkAreaStateId(agent.getFkAreaStateId());
        appAgent.setFkAreaCityId(agent.getFkAreaCityId());
        appAgent.setName(agent.getName());
        appAgent.setNameNote(agent.getNameNote());
        appAgent.setPersonalName(agent.getPersonalName());
        appAgent.setNickName(agent.getNickName());
        appAgent.setNature(agent.getNature());
        appAgent.setNatureNote(agent.getNatureNote());
        appAgent.setLegalPerson(agent.getLegalPerson());
        appAgent.setTaxCode(agent.getTaxCode());
        appAgent.setIdCardNum(agent.getIdCardNum());
        appAgent.setAddress(agent.getAddress());
        appAgent.setRemark(agent.getRemark());

        // BD员工信息
        appAgent.setFkStaffId(fkStaffId);

        // 续约申请特有字段
        appAgent.setAppType(AgentAppTypeEnum.RENEWAL_APPLICATION.getCode()); // 续约申请
        appAgent.setAppStatus(ProjectExtraEnum.APP_STATUS_NEW.key); // 待审核状态(0)
        appAgent.setAppFrom(AgentAppFromEnum.WEB_APPLY_2.getCode()); // 网页申请
        appAgent.setFkAgentId(agent.getId()); // 关联原代理ID

        // 审核时间相关（续约申请暂时为空）
        appAgent.setAppStatusModifyTime(null);
        appAgent.setAppStatusModifyUser(null);

        return appAgent;
    }

    /**
     * 批量创建申请联系人记录
     *
     * @param agentId    代理ID
     * @param appAgentId 申请代理ID
     */
    private void createAppAgentContactPersonBatch(Long agentId, Long appAgentId) {
        // 查询代理的所有联系人（排除合同联系人类型，保持与前端显示逻辑一致）
        List<SaleContactPerson> contactPersons = personService.list(
                Wrappers.<SaleContactPerson>lambdaQuery()
                        .eq(SaleContactPerson::getFkTableName, TableEnum.SALE_AGENT.key)
                        .eq(SaleContactPerson::getFkTableId, agentId)
                        .apply("FIND_IN_SET('CONTACT_AGENT_CONTRACT', fk_contact_person_type_key) = 0"));

        if (CollectionUtil.isEmpty(contactPersons)) {
            log.warn("代理ID: {} 没有找到非合同联系人信息", agentId);
            return;
        }

        log.info("代理ID: {} 查询到 {} 个非合同联系人", agentId, contactPersons.size());

        // 批量构造数据，避免循环中执行SQL
        List<AppAgentContactPerson> appContactPersons = new ArrayList<>();
        for (SaleContactPerson contactPerson : contactPersons) {
            AppAgentContactPerson appContactPerson = new AppAgentContactPerson();
            appContactPerson.setFkAppAgentId(appAgentId);
            appContactPerson.setFkContactPersonId(agentId);
            appContactPerson.setFkContactPersonTypeKey(contactPerson.getFkContactPersonTypeKey());
            appContactPerson.setName(contactPerson.getName());
            appContactPerson.setGender(contactPerson.getGender());
            appContactPerson.setDepartment(contactPerson.getDepartment());
            appContactPerson.setTitle(contactPerson.getTitle());
            appContactPerson.setMobileAreaCode(contactPerson.getMobileAreaCode());
            appContactPerson.setMobile(contactPerson.getMobile());
            appContactPerson.setEmail(contactPerson.getEmail());
            appContactPerson.setIsCommissionEmail(contactPerson.getIsCommissionEmail());
            appContactPerson.setIsNewsEmail(contactPerson.getIsNewsEmail());
            appContactPerson.setFkAreaCountryIdsNews(contactPerson.getFkAreaCountryIdsNews());
            appContactPerson.setRemark(contactPerson.getRemark());

            utilService.setCreateInfo(appContactPerson);
            appContactPersons.add(appContactPerson);
        }

        // 批量保存联系人记录
        if (CollectionUtil.isNotEmpty(appContactPersons)) {
            try {
                boolean batchSaved = appAgentContactPersonService.saveBatch(appContactPersons);
                if (!batchSaved) {
                    log.error("批量保存申请联系人失败，申请代理ID: {}", appAgentId);
                    throw new GetServiceException(LocaleMessageUtils.getMessage("save_contact_person_failed"));
                }
                log.info("批量创建申请联系人成功，申请代理ID: {}, 非合同联系人数量: {}", appAgentId, appContactPersons.size());
            } catch (Exception e) {
                log.error("批量保存申请联系人发生异常，申请代理ID: " + appAgentId, e);
                throw new GetServiceException(LocaleMessageUtils.getMessage("save_contact_person_failed"));
            }
        }
    }

    /**
     * 创建申请联系人记录（单个，保留兼容性）
     *
     * @param appAgentId    申请代理ID
     * @param contactPerson 联系人信息
     * @deprecated 建议使用 createAppAgentContactPersonBatch 批量处理
     */
    @Deprecated
    private void createAppAgentContactPerson(Long appAgentId, SaleContactPerson contactPerson) {
        // 转换为申请联系人
        AppAgentContactPerson appContactPerson = new AppAgentContactPerson();
        appContactPerson.setFkAppAgentId(appAgentId);
        appContactPerson.setFkContactPersonTypeKey(contactPerson.getFkContactPersonTypeKey());
        appContactPerson.setName(contactPerson.getName());
        appContactPerson.setGender(contactPerson.getGender());
        appContactPerson.setDepartment(contactPerson.getDepartment());
        appContactPerson.setTitle(contactPerson.getTitle());
        appContactPerson.setMobileAreaCode(contactPerson.getMobileAreaCode());
        appContactPerson.setMobile(contactPerson.getMobile());
        appContactPerson.setEmail(contactPerson.getEmail());
        appContactPerson.setIsCommissionEmail(contactPerson.getIsCommissionEmail());
        appContactPerson.setIsNewsEmail(contactPerson.getIsNewsEmail());
        appContactPerson.setFkAreaCountryIdsNews(contactPerson.getFkAreaCountryIdsNews());
        appContactPerson.setRemark(contactPerson.getRemark());

        utilService.setCreateInfo(appContactPerson);
        appAgentContactPersonService.save(appContactPerson);

        log.info("创建申请联系人成功，申请代理ID: {}, 联系人ID: {}", appAgentId, contactPerson.getId());
    }

    /**
     * 复制合同账户记录
     *
     * @param agentId    代理ID
     * @param appAgentId 申请代理ID
     */
    private void copyAgentContractAccountToAppAgent(Long agentId, Long appAgentId) {
        List<AgentContractAccount> contractAccounts = agentContractAccountService.list(
                Wrappers.<AgentContractAccount>lambdaQuery()
                        .eq(AgentContractAccount::getFkAgentId, agentId)
                        .eq(AgentContractAccount::getIsActive, true));

        // 批量构造数据，避免循环中执行SQL
        List<AppAgentContractAccount> appAccounts = new ArrayList<>();
        for (AgentContractAccount account : contractAccounts) {
            AppAgentContractAccount appAccount = new AppAgentContractAccount();

            // 基本信息复制
            appAccount.setFkAgentContractAccountId(agentId);
            appAccount.setFkAppAgentId(appAgentId);
            appAccount.setFkCurrencyTypeNum(account.getFkCurrencyTypeNum());
            appAccount.setBankAccount(account.getBankAccount());
            appAccount.setBankAccountNum(account.getBankAccountNum());
            appAccount.setBankName(account.getBankName());
            appAccount.setBankBranchName(account.getBankBranchName());
            appAccount.setFkAreaCountryId(account.getFkAreaCountryId());
            appAccount.setFkAreaStateId(account.getFkAreaStateId());
            appAccount.setFkAreaCityId(account.getFkAreaCityId());
            appAccount.setFkAreaCityDivisionId(account.getFkAreaCityDivisionId());
            appAccount.setBankAddress(account.getBankAddress());
            appAccount.setBankCodeType(account.getBankCodeType());
            appAccount.setBankCode(account.getBankCode());
            appAccount.setAreaCountryCode(account.getAreaCountryCode());
            appAccount.setIsDefault(account.getIsDefault());
            appAccount.setRemark(account.getRemark());

            utilService.setCreateInfo(appAccount);
            appAccounts.add(appAccount);
        }

        // 批量保存，避免循环中的数据库操作
        if (!appAccounts.isEmpty()) {
            appAgentContractAccountService.saveBatch(appAccounts);
        }

        log.info("复制合同账户记录完成，代理ID: {}, 申请代理ID: {}, 账户数量: {}",
                agentId, appAgentId, contractAccounts.size());
    }

    /**
     * 复制附件记录
     *
     * @param agentId    代理ID
     * @param appAgentId 申请代理ID
     */
    private void copyAgentAttachmentsToAppAgent(Long agentId, Long appAgentId) {
        List<SaleMediaAndAttached> attachments = attachedService.list(
                Wrappers.<SaleMediaAndAttached>lambdaQuery()
                        .eq(SaleMediaAndAttached::getFkTableName, TableEnum.SALE_AGENT.key)
                        .eq(SaleMediaAndAttached::getFkTableId, agentId));

        // 批量构造数据，避免循环中执行SQL
        List<SaleMediaAndAttached> newAttachments = new ArrayList<>();
        for (SaleMediaAndAttached attachment : attachments) {
            SaleMediaAndAttached newAttachment = new SaleMediaAndAttached();

            // 复制附件信息，更改关联关系
            BeanCopyUtils.copyProperties(attachment, newAttachment);
            newAttachment.setId(null); // 生成新ID
            newAttachment.setFkTableName(TableEnum.APP_AGENT.key); // 关联到申请表
            newAttachment.setFkTableId(appAgentId); // 关联到申请ID
            newAttachment.setGmtModified(null);
            newAttachment.setGmtModifiedUser(null);

            utilService.setCreateInfo(newAttachment);
            newAttachments.add(newAttachment);
        }

        // 批量保存，避免循环中的数据库操作
        if (!newAttachments.isEmpty()) {
            attachedService.saveBatch(newAttachments);
        }

        log.info("复制附件记录完成，代理ID: {}, 申请代理ID: {}, 附件数量: {}",
                agentId, appAgentId, attachments.size());
    }

    /**
     * 给BD员工发送续约邮件副本
     *
     * @param originalContexts 原始邮件上下文列表
     * @param agentId 代理ID
     * @param staffId BD员工ID
     */
    private void sendBdEmailCopiesForRenewal(List<EmailSendContext> originalContexts, Long agentId, Long staffId) {
        try {
            if (staffId == null) {
                log.warn("BD员工ID为空，跳过BD邮件副本发送，代理ID: {}", agentId);
                return;
            }

            // 获取BD员工信息
            Result<StaffVo> staffResult = permissionCenterClient.getStaffById(staffId);
            if (!staffResult.isSuccess() || staffResult.getData() == null) {
                log.warn("获取BD员工信息失败，跳过BD邮件副本发送，员工ID: {}, 代理ID: {}", staffId, agentId);
                return;
            }
            
            StaffVo bdStaff = staffResult.getData();
            if (StringUtils.isBlank(bdStaff.getEmail())) {
                log.warn("BD员工邮箱为空，跳过BD邮件副本发送，员工ID: {}, 代理ID: {}", staffId, agentId);
                return;
            }

            List<EmailSendContext> bdEmailContexts = new ArrayList<>();

            // 复制续约邮件给BD
            for (EmailSendContext originalContext : originalContexts) {
                EmailSendContext bdContext = createBdEmailCopyForAgent(originalContext, bdStaff);
                bdEmailContexts.add(bdContext);
            }

            // 发送BD邮件副本
            if (!bdEmailContexts.isEmpty()) {
                emailSenderUtils.sendBatchEmails(bdEmailContexts, agentId);
                log.info("成功发送BD续约邮件副本，代理ID: {}, 邮件数量: {}", agentId, bdEmailContexts.size());
            }

        } catch (Exception e) {
            log.error("发送BD续约邮件副本异常，代理ID: {}, 错误: {}", agentId, e.getMessage());
        }
    }

    /**
     * 创建BD员工的邮件副本（Agent场景）
     *
     * @param originalContext 原始邮件上下文
     * @param bdStaff BD员工信息
     * @return BD员工邮件上下文
     */
    private EmailSendContext createBdEmailCopyForAgent(EmailSendContext originalContext, StaffVo bdStaff) {
        // 完全复制原始邮件参数，不做任何修改
        Map<String, String> bdEmailParams = new HashMap<>(originalContext.getParameters());

        // 构建BD员工邮件上下文
        return EmailSendContext.builder()
                .projectKey(originalContext.getProjectKey())
                .tableName(originalContext.getTableName())
                .tableId(originalContext.getTableId())
                .recipient(bdStaff.getEmail()) // 只改变收件人邮箱
                .emailTemplate(originalContext.getEmailTemplate()) // 使用相同的邮件模板
                .parameters(bdEmailParams) // 参数完全不变
                .build();
    }

}

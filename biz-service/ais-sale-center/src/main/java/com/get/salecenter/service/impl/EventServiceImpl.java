package com.get.salecenter.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.consts.SaleCenterConstant;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.CollectionUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.file.utils.FileUtils;
import com.get.financecenter.feign.IFinanceCenterClient;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.institutioncenter.vo.AreaCityVo;
import com.get.institutioncenter.vo.AreaCountryVo;
import com.get.institutioncenter.vo.AreaStateVo;
import com.get.permissioncenter.entity.StaffAreaCountry;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.CompanyConfigAnalysisVo;
import com.get.permissioncenter.vo.StaffVo;
import com.get.remindercenter.dto.RemindTaskDto;
import com.get.remindercenter.entity.EmailSenderQueue;
import com.get.remindercenter.enums.EmailTemplateEnum;
import com.get.remindercenter.feign.IReminderCenterClient;
import com.get.salecenter.dao.sale.EventBillMapper;
import com.get.salecenter.dao.sale.EventCostMapper;
import com.get.salecenter.dao.sale.EventDeferTimeMapper;
import com.get.salecenter.dao.sale.EventMapper;
import com.get.salecenter.dao.sale.EventRegistrationMapper;
import com.get.salecenter.dao.sale.EventTypeMapper;
import com.get.salecenter.dto.CommentDto;
import com.get.salecenter.dto.EventCostDto;
import com.get.salecenter.dto.EventDto;
import com.get.salecenter.dto.EventRegistrationStatisticsDto;
import com.get.salecenter.dto.EventTargetAreaCountryDto;
import com.get.salecenter.dto.MediaAndAttachedDto;
import com.get.salecenter.dto.query.EventQueryDto;
import com.get.salecenter.entity.Event;
import com.get.salecenter.entity.EventBill;
import com.get.salecenter.entity.EventCost;
import com.get.salecenter.entity.EventDeferTime;
import com.get.salecenter.entity.EventRegistration;
import com.get.salecenter.entity.EventType;
import com.get.salecenter.entity.SaleComment;
import com.get.salecenter.service.ICommentService;
import com.get.salecenter.service.IDeleteService;
import com.get.salecenter.service.IEventRegistrationService;
import com.get.salecenter.service.IEventService;
import com.get.salecenter.service.IEventTargetAreaCountryService;
import com.get.salecenter.service.IEventTypeService;
import com.get.salecenter.service.IMediaAndAttachedService;
import com.get.salecenter.utils.MyStringUtils;
import com.get.salecenter.utils.sale.VerifyStudentOfferItemUtils;
import com.get.salecenter.vo.CommentVo;
import com.get.salecenter.vo.EventDataVo;
import com.get.salecenter.vo.EventExportVo;
import com.get.salecenter.vo.EventRegistrationStatisticsVo;
import com.get.salecenter.vo.EventTypeVo;
import com.get.salecenter.vo.EventVo;
import com.get.salecenter.vo.MediaAndAttachedVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.StringJoiner;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static com.get.core.tool.utils.DateUtil.now;

/**
 * @author: Sea
 * @create: 2020/12/7 15:13
 * @verison: 1.0
 * @description:
 */
@Service
public class EventServiceImpl extends ServiceImpl<EventMapper, Event> implements IEventService {
    @Resource
    private EventMapper eventMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IEventTargetAreaCountryService eventTargetAreaCountryService;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    private IFinanceCenterClient financeCenterClient;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private IEventTypeService eventTypeService;
    @Resource
    private ICommentService commentService;
    @Resource
    private IMediaAndAttachedService attachedService;
    @Resource
    private IDeleteService deleteService;
    @Resource
    private EventTypeMapper eventTypeMapper;
    @Resource
    private EventCostMapper eventCostMapper;
    @Resource
    private EventBillMapper eventBillMapper;

    @Resource
    private EventRegistrationMapper eventRegistrationMapper;
    @Lazy
    @Resource
    private IEventRegistrationService eventRegistrationService;

    @Resource
    private VerifyStudentOfferItemUtils verifyStudentOfferItemUtils;
    @Resource
    private EventDeferTimeMapper eventDeferTimeMapper;

    @Resource
    private IReminderCenterClient reminderCenterClient;

    private SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    private static boolean sameDate(Date d1, Date d2) {
        SimpleDateFormat fmt = new SimpleDateFormat("yyyyMMdd");
        //fmt.setTimeZone(new TimeZone()); // 如果需要设置时间区域，可以在这里设置
        return fmt.format(d1).equals(fmt.format(d2));
    }

    /**
     * 获取当前日期是星期几<br>
     *
     * @param dt
     * @return 当前日期是星期几
     */
    public static String getWeekOfDate(Date dt) {
        String[] weekDays = {"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"};
        Calendar cal = Calendar.getInstance();
        cal.setTime(dt);
        int w = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (w < 0)
            w = 0;
        return weekDays[w];
    }

    @Override
    public EventVo findEventById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        Event event = eventMapper.selectById(id);
        if (GeneralTool.isEmpty(event)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }

        if (!SecureUtil.validateCompany(event.getFkCompanyId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
        }
        EventVo eventVo = BeanCopyUtils.objClone(event, EventVo::new);
        //设置活动对象国家名称和id
        List<Long> countryIds = eventTargetAreaCountryService.getCountryIdsByEventIds(Collections.singletonList(id));
        Map<Long, String> countryNameMap = new HashMap<>();
        Result<Map<Long, String>> countryNameResult = institutionCenterClient.getCountryNamesByIds(new HashSet<>(countryIds));
        if (countryNameResult.isSuccess() && GeneralTool.isNotEmpty(countryNameResult.getData())) {
            countryNameMap = countryNameResult.getData();
        }
        //TODO 改过
        //String countryName = setEventTargetCountryName(countryNameMap, eventVo);
        String countryName = setEventTargetCountryName(countryNameMap, eventVo);
        eventVo.setEventTargetCountryName(countryName);
        eventVo.setEventTargetCountryList(countryIds);
        //活动状态枚举
        Map<Integer, String> eventStatusNameMap = new HashMap<>();
        for (ProjectExtraEnum eventStatus : ProjectExtraEnum.EVENT_STATUS) {
            eventStatusNameMap.put(eventStatus.key, eventStatus.value);
        }
        eventVo.setStatusName(eventStatusNameMap.get(event.getStatus()));
        //设置活动类型名称
        eventVo.setEventTypeName(eventTypeService.getEventTypeNameById(event.getFkEventTypeId()));

        //设置公开对象名称
        eventVo.setPublicLevelName(translateStatus(event.getPublicLevel()));

        //设置国家名称
//        eventVo.setAreaCountryNameHold(institutionCenterClient.getCountryNameById(eventVo.getFkAreaCountryIdHold()));
        Result<String> result00 = institutionCenterClient.getCountryNameById(eventVo.getFkAreaCountryIdHold());
        if (result00.isSuccess() && GeneralTool.isNotEmpty(result00.getData())) {
            eventVo.setAreaCountryNameHold(result00.getData());
        }
        //设置州省名称
//        eventVo.setAreaStateNameHold(institutionCenterClient.getStateNameById(eventVo.getFkAreaStateIdHold()));
        Result<String> result11 = institutionCenterClient.getStateNameById(eventVo.getFkAreaStateIdHold());
        if (result11.isSuccess() && GeneralTool.isNotEmpty(result11.getData())) {
            eventVo.setAreaStateNameHold(result11.getData());
        }
        //设置城市名称
//        eventVo.setAreaCityNameHold(institutionCenterClient.getCityNameById(eventVo.getFkAreaCityIdHold()));
        Result<String> result22 = institutionCenterClient.getCityNameById(eventVo.getFkAreaCityIdHold());
        if (result22.isSuccess() && GeneralTool.isNotEmpty(result22.getData())) {
            eventVo.setAreaCityNameHold(result22.getData());
        }
        //设置负责人名称
        Result<String> result1 = permissionCenterClient.getStaffName(eventVo.getFkStaffIdLeader1());
        if (result1.isSuccess() && GeneralTool.isNotEmpty(result1.getData())) {
            eventVo.setStaffNameLeader1(result1.getData());
        }
        Result<String> result2 = permissionCenterClient.getStaffName(eventVo.getFkStaffIdLeader2());
        if (result2.isSuccess() && GeneralTool.isNotEmpty(result2.getData())) {
            eventVo.setStaffNameLeader2(result2.getData());
        }
        //设置币种名称
        Result<String> typeNameresult = financeCenterClient.getCurrencyNameByNum(eventVo.getFkCurrencyTypeNum());
        if (typeNameresult.isSuccess() && GeneralTool.isNotEmpty(typeNameresult.getData())) {
            eventVo.setCurrencyTypeName(typeNameresult.getData());
        }
        //设置公司名称
        Result<String> companyNameResult = permissionCenterClient.getCompanyNameById(eventVo.getFkCompanyId());
        if (companyNameResult.isSuccess() && GeneralTool.isNotEmpty(companyNameResult.getData())) {
            eventVo.setCompanyName(companyNameResult.getData());
        }
        List<Long> list = new ArrayList<>();
        list.add(GetAuthInfo.getStaffId());
        Result<List<Long>> result = permissionCenterClient.getStaffFollowerIds(GetAuthInfo.getStaffId());
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            List<Long> staffFollowerIds = result.getData();
            if (GeneralTool.isNotEmpty(staffFollowerIds)) {
                list.addAll(staffFollowerIds);
                if (GeneralTool.isNotEmpty(eventVo.getGmtCreateUser())) {
                    Result<StaffVo> staffDtoResult = permissionCenterClient.getStaffByCreateUser(eventVo.getGmtCreateUser());
                    if (staffDtoResult.isSuccess() && staffDtoResult.getData() != null) {
                        StaffVo staffByCreateUser = staffDtoResult.getData();
                        if (GeneralTool.isNotEmpty(staffByCreateUser)) {
                            eventVo.setVisitStatus(list.contains(staffByCreateUser.getId()));
                        }
                    }

                }
            }

        }

        //当前登录人如果是负责人的话
        if(GeneralTool.isNotEmpty(eventVo.getFkStaffIdLeader1())){
            List<Long> staffFollowerIds = result.getData();
            list.addAll(staffFollowerIds);
            // 如果当前用户是第一负责人，或者当前用户是第一负责人的上司，则可以查看
            if (list.contains(eventVo.getFkStaffIdLeader1())) {
                eventVo.setVisitStatus(true);
            }
        }

        if (GeneralTool.isNotEmpty(eventVo.getEventTime()) && GeneralTool.isNotEmpty(eventVo.getEventTimeEnd())) {
            String eventTimeName;
            if (sameDate(eventVo.getEventTime(), eventVo.getEventTimeEnd())) {
                eventTimeName = formatter.format(eventVo.getEventTime()) + "至" + new SimpleDateFormat("HH:mm:ss").format(eventVo.getEventTimeEnd());
            } else {
                eventTimeName = formatter.format(eventVo.getEventTime()) + "至" + formatter.format(eventVo.getEventTimeEnd());
            }
            eventVo.setEventTimeName(eventTimeName + "（当前）");

            // 获取当前活动的活动时间日志记录
            List<EventDeferTime> eventDeferTimeList = eventDeferTimeMapper.selectList(Wrappers.<EventDeferTime>lambdaQuery()
                    .eq(EventDeferTime::getFkEventId, id)
                    .orderByDesc(EventDeferTime::getGmtCreate));
            List<String> eventDeferTimeNameList = new ArrayList<>();
            for (EventDeferTime eventDeferTime : eventDeferTimeList) {
                Date eventTime = eventDeferTime.getEventTime();
                Date eventTimeEnd = eventDeferTime.getEventTimeEnd();
                // 排除当前活动时间
                if (eventTime.equals(eventVo.getEventTime()) && eventTimeEnd.equals(eventVo.getEventTimeEnd())) {
                    continue;
                }
                String eventDeferTimeName;
                if (sameDate(eventTime, eventTimeEnd)) {
                    eventDeferTimeName = formatter.format(eventTime) + "至" + new SimpleDateFormat("HH:mm:ss").format(eventTimeEnd);
                } else {
                    eventDeferTimeName = formatter.format(eventTime) + "至" + formatter.format(eventTimeEnd);
                }
                eventDeferTimeNameList.add(eventDeferTimeName);
            }
            eventVo.setEventDeferTimeNameList(eventDeferTimeNameList);
        }

        BigDecimal bdSumAmount = BigDecimal.ZERO;
        bdSumAmount = bdSumAmount.add(event.getBdFoodAmount()==null?BigDecimal.ZERO:event.getBdFoodAmount())
                .add(event.getBdOtherAmount()==null?BigDecimal.ZERO:event.getBdOtherAmount())
                .add(event.getBdPrizeAmount()==null?BigDecimal.ZERO:event.getBdPrizeAmount())
                .add(event.getBdVenueAmount()==null?BigDecimal.ZERO:event.getBdVenueAmount());

        eventVo.setBdSumAmount(bdSumAmount);

        return eventVo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long addEvent(EventDto eventDto) {
        if (GeneralTool.isEmpty(eventDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        if (GeneralTool.isNotEmpty(eventDto.getEventTime())) {
            if (GeneralTool.isEmpty(eventDto.getEventTimeEnd())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("end_time_not_null"));
            }
        }
        validataRequired(eventDto);
        Event event = BeanCopyUtils.objClone(eventDto, Event::new);
        validataEventTarget(eventDto, event);
        utilService.updateUserInfoToEntity(event);
        int i = eventMapper.insert(event);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
        //获取自动生成的活动编号
        event.setNum(MyStringUtils.getEventNum(event.getId()));
        eventMapper.updateById(event);
        //同时中间表r_event_target_area_country新增
        insertTable(eventDto, event);
        // 当有设置活动时间，同时插入活动时间日志表
        if (GeneralTool.isNotEmpty(eventDto.getEventTime()) && GeneralTool.isNotEmpty(eventDto.getEventTimeEnd())) {
            EventDeferTime eventDeferTime = new EventDeferTime();
            eventDeferTime.setFkEventId(event.getId());
            eventDeferTime.setEventTime(event.getEventTime());
            eventDeferTime.setEventTimeEnd(event.getEventTimeEnd());
            utilService.setCreateInfo(eventDeferTime);
            eventDeferTimeMapper.insert(eventDeferTime);
        }
        return event.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (eventMapper.selectById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        //验证能否删除
        deleteService.deleteValidateEvent(id);
        //同时删除中间表r_event_target_area_country数据
        eventTargetAreaCountryService.deleteByEventId(id);
        int i = eventMapper.deleteById(id);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public EventVo updateEvent(EventDto eventDto) {
        if (eventDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        Event result = eventMapper.selectById(eventDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        if (GeneralTool.isNotEmpty(eventDto.getEventTime())) {
            if (GeneralTool.isEmpty(eventDto.getEventTimeEnd())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("end_time_not_null"));
            }
        }
        validataRequired(eventDto);
        Event event = BeanCopyUtils.objClone(eventDto, Event::new);
        validataEventTarget(eventDto, event);
        utilService.updateUserInfoToEntity(event);
        int i = eventMapper.updateByIdWithNull(event);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
        //同时更新掉中间表r_event_target_area_country数据
        eventTargetAreaCountryService.deleteByEventId(eventDto.getId());
        insertTable(eventDto, event);
        //更改报名名册状态
        if (eventDto.getStatus() == 2 || eventDto.getStatus() == 3){
            LambdaQueryWrapper<EventRegistration> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(EventRegistration::getFkEventId, eventDto.getId());
            EventRegistration eventRegistration = new EventRegistration();
            eventRegistration.setStatus(2);
            utilService.setUpdateInfo(eventRegistration);
            eventRegistrationMapper.update(eventRegistration,lambdaQueryWrapper);
        }

        return findEventById(eventDto.getId());

    }

    /**
     * @return void
     * @Description :判断是否是空格数据
     * @Param [eventDto, event]
     * <AUTHOR>
     */
    private void validataEventTarget(EventDto eventDto, Event event) {
        //判断是否是空格数据
        if (null != eventDto.getEventTarget()) {
            String eventTargetTrim = event.getEventTarget().trim();
            if ("".equals(event.getEventTarget().trim())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
            } else {
                StringJoiner stringJoiner = new StringJoiner(",");
                for (String eventTarget : eventTargetTrim.split(",")) {
                    //重组数据 防止前端拼接了空数据
                    if (!"".equals(eventTarget.trim())) {
                        stringJoiner.add(eventTarget.trim());
                    }
                }
                event.setEventTarget(stringJoiner.toString());
            }
        }
    }

    /**
     * @return void
     * @Description :判断必填是否有天
     * @Param [eventDto, event]
     * <AUTHOR>
     */
    private void validataRequired(EventDto eventDto) {
        if (eventDto.getStatus() == 1) {
            if (GeneralTool.isEmpty(eventDto.getActualAmount())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("actual_amount_null"));
            }
            if (GeneralTool.isEmpty(eventDto.getBudgetAmount())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("budget_amount_null"));
            }
            if (GeneralTool.isEmpty(eventDto.getAttendedCount())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("attended_count_null"));
            }
            if (GeneralTool.isEmpty(eventDto.getRemark())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("remark_null"));
            }
            if (GeneralTool.isEmpty(eventDto.getFkCurrencyTypeNum())){
                throw new GetServiceException(LocaleMessageUtils.getMessage("currency_type_num_not_null"));
            }
        }
    }

    /**
     * @return void
     * @Description :判断必填是否有天
     * @Param [eventVo, event]
     * <AUTHOR>
     */
    private void validataRequiredByEnd(Event event) {

        if (GeneralTool.isEmpty(event.getActualAmount())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("attended_count_actual_amount_budget_amount_remark_null"));
        }
        if (GeneralTool.isEmpty(event.getAttendedCount())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("attended_count_actual_amount_budget_amount_remark_null"));
        }
        if (GeneralTool.isEmpty(event.getRemark())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("attended_count_actual_amount_budget_amount_remark_null"));
        }
        if (GeneralTool.isEmpty(event.getBudgetAmount())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("attended_count_actual_amount_budget_amount_remark_null"));
        }
    }

    /**
     * @return void
     * @Description :判断必填是否有天
     * @Param [eventVo, event]
     * <AUTHOR>
     */
    private void validateRequiredByEndWithoutRemark(Event event) {
        if (GeneralTool.isEmpty(event.getActualAmount())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("attended_count_actual_amount_budget_amount_null"));
        }
        if (GeneralTool.isEmpty(event.getAttendedCount())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("attended_count_actual_amount_budget_amount_null"));
        }
        if (GeneralTool.isEmpty(event.getBudgetAmount())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("attended_count_actual_amount_budget_amount_null"));
        }
    }

    @Override
    public List<EventVo> getEvents(EventQueryDto eventQueryVo, Page page) {
        List<Event> events = new ArrayList<>();
        //封装列表操作
        if (page == null){
            events = getEventPageList(eventQueryVo, page);
        }else {
            events = getEventPageList(eventQueryVo, page);
        }
//        List<Event> events = getEventPageList(eventQueryVo, page);
        List<EventVo> convertDatas = new ArrayList<>();
        //媒体附件对象
        MediaAndAttachedDto mediaAndAttachedVo = new MediaAndAttachedDto();
        //活动id集合
        List<Long> eventIds = new ArrayList<>();
        //币种编号集合
        Set<String> currencyTypeNums = new HashSet<>();
        //城市id集合
        Set<Long> areaCityIds = new HashSet<>();
        //州省id集合
        Set<Long> areaStateIds = new HashSet<>();
        //国家id集合
        Set<Long> areaCountryIds = new HashSet<>();
        //负责人id集合
        Set<Long> staffIds = new HashSet<>();
        //所属公司id集合
        Set<Long> companyIds = new HashSet<>();
        //获取各自集合的值
        for (Event event : events) {
            areaCountryIds.add(event.getFkAreaCountryIdHold());
            areaStateIds.add(event.getFkAreaStateIdHold());
            areaCityIds.add(event.getFkAreaCityIdHold());
            currencyTypeNums.add(event.getFkCurrencyTypeNum());
            eventIds.add(event.getId());
            staffIds.add(event.getFkStaffIdLeader1());
            staffIds.add(event.getFkStaffIdLeader2());
            companyIds.add(event.getFkCompanyId());
        }
        //feign调用获取对应map
        Map<Long, String> countryNameMap = getCountryNameMap(areaCountryIds);
        Map<Long, String> stateNameMap = getStateNameMap(areaStateIds);
        Map<Long, String> cityNameMap = getCityNameChnsByIds(areaCityIds);
        Map<String, String> currencyNameMap = getCurrencyNameMap(currencyTypeNums);
        Map<Long, String> eventTargetCountryNameMap = getEventTargetCountryNameMap(eventIds);
        Map<Long, String> staffNameMap = getStaffNameMap(staffIds);
        Map<Long, String> companyNameMap = getCompanyNameMap(companyIds);
        Map<Integer, String> eventStatusNameMap = new HashMap<>();
        for (ProjectExtraEnum eventStatus : ProjectExtraEnum.EVENT_STATUS) {
            eventStatusNameMap.put(eventStatus.key, eventStatus.value);
        }

        List<Long> staffFollowerIds = new ArrayList<>();
        Result<List<Long>> result = permissionCenterClient.getStaffFollowerIds(GetAuthInfo.getStaffId());
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            staffFollowerIds = result.getData();
        }

        List<EventCost> eventCosts = eventCostMapper.selectList(Wrappers.<EventCost>lambdaQuery().in(EventCost::getFkEventId, eventIds));
        Map<Long, List<EventCost>> eventCostMap = eventCosts.stream().collect(Collectors.groupingBy(EventCost::getFkEventId));

        //获得报名名册状态
        Set<Long> eventRegistrationsIds = new HashSet<>();
        LambdaQueryWrapper<EventRegistration> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(EventRegistration::getFkEventId,eventIds);
        lambdaQueryWrapper.eq(EventRegistration::getStatus,2);
        List<EventRegistration> eventRegistrations = eventRegistrationMapper.selectList(lambdaQueryWrapper);
        if (GeneralTool.isNotEmpty(eventRegistrations)){
            eventRegistrationsIds = eventRegistrations.stream().map(EventRegistration::getFkEventId).collect(Collectors.toSet());
        }


        //学校报名数
        LambdaQueryWrapper<EventRegistration> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(EventRegistration::getFkEventId,eventIds);
        List<EventRegistration> eventRegistrationList = eventRegistrationMapper.selectList(wrapper);
        Map<Long, Long> registerCountMap = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(eventRegistrationList)){
            registerCountMap = eventRegistrationList.stream().collect(Collectors.groupingBy(EventRegistration::getFkEventId, Collectors.counting()));
        }



        for (Event event : events) {
            EventVo eventDto = BeanCopyUtils.objClone(event, EventVo::new);
            //feign调用返回的map 根据key-id获取对应value-名称 设置返回给前端
            eventDto.setAreaCountryNameHold(countryNameMap.get(eventDto.getFkAreaCountryIdHold()));
            eventDto.setAreaStateNameHold(stateNameMap.get(eventDto.getFkAreaStateIdHold()));
            eventDto.setAreaCityNameHold(cityNameMap.get(eventDto.getFkAreaCityIdHold()));
            eventDto.setCurrencyTypeName(currencyNameMap.get(eventDto.getFkCurrencyTypeNum()));
            eventDto.setStaffNameLeader1(staffNameMap.get(eventDto.getFkStaffIdLeader1()));
            eventDto.setStaffNameLeader2(staffNameMap.get(eventDto.getFkStaffIdLeader2()));
            eventDto.setCompanyName(companyNameMap.get(event.getFkCompanyId()));

            //设置公开对象名称
            eventDto.setPublicLevelName(translateStatus(eventDto.getPublicLevel()));


            //设置值返回前端
            eventDto.setEventTypeName(eventTypeService.getEventTypeNameById(event.getFkEventTypeId()));
            String countryName = setEventTargetCountryName(eventTargetCountryNameMap, eventDto);
            eventDto.setEventTargetCountryName(countryName);
            setMediaAndAttachedDtoList(eventDto, mediaAndAttachedVo);
            eventDto.setStatusName(eventStatusNameMap.get(eventDto.getStatus()));
            //学校报名数
            eventDto.setRegisterCount(GeneralTool.isNotEmpty(registerCountMap.get(event.getId()))?registerCountMap.get(event.getId()):0L);

            BigDecimal bdSumAmount = BigDecimal.ZERO;
            bdSumAmount = bdSumAmount.add(event.getBdFoodAmount()==null?BigDecimal.ZERO:event.getBdFoodAmount())
                    .add(event.getBdOtherAmount()==null?BigDecimal.ZERO:event.getBdOtherAmount())
                    .add(event.getBdPrizeAmount()==null?BigDecimal.ZERO:event.getBdPrizeAmount())
                    .add(event.getBdVenueAmount()==null?BigDecimal.ZERO:event.getBdVenueAmount());
            eventDto.setBdSumAmount(bdSumAmount);

            List<Long> list = new ArrayList<>();
            list.add(GetAuthInfo.getStaffId());
            if (GeneralTool.isNotEmpty(staffFollowerIds)) {
                list.addAll(staffFollowerIds);
                if (GeneralTool.isNotEmpty(eventDto.getGmtCreateUser())) {
                    Result<StaffVo> staffDtoResult = permissionCenterClient.getStaffByCreateUser(eventDto.getGmtCreateUser());
                    if (staffDtoResult.isSuccess() && staffDtoResult.getData() != null) {
                        StaffVo staffByCreateUser = staffDtoResult.getData();
                        if (GeneralTool.isNotEmpty(staffByCreateUser)) {
                            eventDto.setVisitStatus(list.contains(staffByCreateUser.getId()));
                        }
                    }

                }

            }
            //当前登录人如果是负责人的话
            if(GeneralTool.isNotEmpty(eventDto.getFkStaffIdLeader1())){
                list.addAll(staffFollowerIds);
                // 如果当前用户是第一负责人，或者当前用户是第一负责人的上司，则可以查看
                if (list.contains(eventDto.getFkStaffIdLeader1())) {
                    eventDto.setVisitStatus(true);
                }
            }



            if (GeneralTool.isNotEmpty(eventDto.getEventTime()) && GeneralTool.isNotEmpty(eventDto.getEventTimeEnd())) {
                if (sameDate(eventDto.getEventTime(), eventDto.getEventTimeEnd())) {
                    String eventTimeName = formatter.format(eventDto.getEventTime()) + "至" + new SimpleDateFormat("HH:mm:ss").format(eventDto.getEventTimeEnd());
                    eventDto.setEventTimeName(eventTimeName);
                } else {
                    String eventTimeName = formatter.format(eventDto.getEventTime()) + "至" + formatter.format(eventDto.getEventTimeEnd());
                    eventDto.setEventTimeName(eventTimeName);
                }
            }

            BigDecimal amountIncome = BigDecimal.ZERO;
            if (GeneralTool.isNotEmpty(eventCostMap)&&GeneralTool.isNotEmpty(eventCostMap.get(eventDto.getId()))){
                List<EventCost> eventCostList = eventCostMap.get(eventDto.getId());
                for (EventCost eventCost : eventCostList) {
                    amountIncome = amountIncome.add(GeneralTool.isNotEmpty(eventCost.getAmountRmb())?eventCost.getAmountRmb():BigDecimal.ZERO);
                }
            }
            eventDto.setAmountIncome(amountIncome);
            String currenyName = financeCenterClient.getCurrencyNameByNum("CNY").getData();
            eventDto.setAmountIncomeCurrency(amountIncome.toString() +" "+ currenyName.toString());
            //判断是否有报名名册有待定
            if (GeneralTool.isNotEmpty(eventRegistrationsIds) && eventRegistrationsIds.contains(eventDto.getId())){
                eventDto.setIsEventRegistrationStatus(true);
            }else{
                eventDto.setIsEventRegistrationStatus(false);
            }
            convertDatas.add(eventDto);
        }
        return convertDatas;
    }


    public  String translateStatus(String statusCodes) {
        if (statusCodes == null || statusCodes.isEmpty()) {
            return "";
        }

        StringBuilder translatedStatuses = new StringBuilder();

        StringJoiner stringJoiner = new StringJoiner(" ");
        if (GeneralTool.isNotEmpty(statusCodes)) {
            List<String> result = Arrays.asList(statusCodes.split(","));
            for (String name : result) {
                stringJoiner.add(ProjectExtraEnum.getValueByKey(Integer.valueOf(name), ProjectExtraEnum.PUBLIC_OBJECTS));
            }
            return stringJoiner.toString();
        }

        return translatedStatuses.toString();
    }


    @Override
    public void exportEventExcel(HttpServletResponse response, EventQueryDto eventQueryVo) {

        List<EventVo> events = getEvents(eventQueryVo, null);
        List<EventExportVo> eventExportDtos = new ArrayList<>();
        for (EventVo event : events) {
            EventExportVo eventExportVo = BeanCopyUtils.objClone(event, EventExportVo::new);
            eventExportDtos.add(eventExportVo);
        }

        FileUtils.exportExcelNotWrapText(response, eventExportDtos, "Event", EventExportVo.class);
    }

    @Override
    public List<String> getEventTargetList(Long companyId) {
        return eventMapper.getEventTargetList(companyId);
    }

    @Override
    public List<String> getEventThemeList(Long companyId) {
        return eventMapper.getEventThemeList(companyId);
    }

    @Override
    public Long editComment(CommentDto commentDto) {
        SaleComment comment = BeanCopyUtils.objClone(commentDto, SaleComment::new);
        if (GeneralTool.isNotEmpty(commentDto)) {
            if (GeneralTool.isNotEmpty(commentDto.getId())) {
                comment.setFkTableName(TableEnum.SALE_EVENT.key);
                commentService.updateComment(comment);
            } else {
                comment.setFkTableName(TableEnum.SALE_EVENT.key);
                commentService.addComment(comment);
            }
        }
        return comment.getId();
    }

    @Override
    public List<CommentVo> getComments(CommentDto commentDto, Page page) {
        commentDto.setFkTableName(TableEnum.SALE_EVENT.key);
        return commentService.datas(commentDto, page);
    }

    @Override
    public List<MediaAndAttachedVo> getItemMedia(MediaAndAttachedDto attachedVo, Page page) {
        if (GeneralTool.isEmpty(attachedVo.getFkTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        attachedVo.setFkTableName(TableEnum.SALE_EVENT.key);
        return attachedService.getMediaAndAttachedDto(attachedVo, page);

    }

    @Override
    public List<MediaAndAttachedVo> addItemMedia(ValidList<MediaAndAttachedDto> mediaAttachedVos) {
        if (GeneralTool.isEmpty(mediaAttachedVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        List<MediaAndAttachedVo> mediaAndAttachedVos = new ArrayList<>();
        for (MediaAndAttachedDto mediaAndAttachedDto : mediaAttachedVos) {
            //设置插入的表
            mediaAndAttachedDto.setFkTableName(TableEnum.SALE_EVENT.key);
            mediaAndAttachedVos.add(attachedService.addMediaAndAttached(mediaAndAttachedDto));
        }
        return mediaAndAttachedVos;
    }

    @Override
    public void end(Long eventId) {
        if (GeneralTool.isEmpty(eventId) || GeneralTool.isEmpty(eventMapper.selectById(eventId))) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        Event event = eventMapper.selectById(eventId);
        validataRequiredByEnd(event);
        event.setStatus(1);
        int i = eventMapper.updateById(event);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void cancel(Long eventId) {
        if (GeneralTool.isEmpty(eventId) || GeneralTool.isEmpty(eventMapper.selectById(eventId))) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        Event event = new Event();
        event.setId(eventId);
        event.setStatus(2);
        int i = eventMapper.updateById(event);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
        //设置待定状态
        doSetUndeterminedStatus(eventId);
    }

    @Override
    public void plan(Long eventId) {
        if (GeneralTool.isEmpty(eventId) || GeneralTool.isEmpty(eventMapper.selectById(eventId))) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        Event event = new Event();
        event.setId(eventId);
        event.setStatus(ProjectExtraEnum.EVENT_PLAN.key);
        int i = eventMapper.updateById(event);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void postpone(Long eventId) {
        if (GeneralTool.isEmpty(eventId) || GeneralTool.isEmpty(eventMapper.selectById(eventId))) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        Event event = new Event();
        event.setId(eventId);
        event.setStatus(ProjectExtraEnum.EVENT_POSTPONE.key);
        int i = eventMapper.updateById(event);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
        //设置待定状态
        doSetUndeterminedStatus(eventId);
    }

    /**
     * 设置待定状态
     * @param eventId
     */
    private void doSetUndeterminedStatus(Long eventId) {
        List<EventRegistration> eventRegistrations = eventRegistrationService.list(Wrappers.<EventRegistration>lambdaUpdate()
                .eq(EventRegistration::getFkEventId, eventId));
        if (GeneralTool.isEmpty(eventRegistrations)){
            return;
        }
        eventRegistrations = eventRegistrations.stream().filter(eventRegistration -> eventRegistration.getStatus()!=2).collect(Collectors.toList());
        if (GeneralTool.isEmpty(eventRegistrations)){
            return;
        }
        for (EventRegistration eventRegistration : eventRegistrations) {
            eventRegistration.setStatus(2);
            utilService.setUpdateInfo(eventRegistration);
        }
        boolean b = eventRegistrationService.updateBatchById(eventRegistrations);
        if (!b){
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
//        EventRegistration eventRegistration = new EventRegistration();
//        eventRegistration.setStatus(2);
//        utilService.setUpdateInfo(eventRegistration);
//        boolean update = eventRegistrationService.update(eventRegistration, Wrappers.<EventRegistration>lambdaUpdate()
//                .eq(EventRegistration::getFkEventId, eventId));
//        if (!update){
//            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
//        }
    }

    @Override
    public List<EventVo> getCostList(EventCostDto eventCostDto, Page page) {
        if (GeneralTool.isEmpty(eventCostDto.getFkEventBillId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
//        ReceiptFormVo receiptFormDto = financeCenterClient.getReceiptFormByFormId(eventCostDto.getFkReceiptFormId());
        EventBill eventBill = eventBillMapper.selectById(eventCostDto.getFkEventBillId());
        if (GeneralTool.isEmpty(eventBill)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        //封装列表操作
        IPage<EventVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<EventVo> eventVos = eventMapper.getCostList(iPage, eventCostDto);
        page.setAll((int) iPage.getTotal());

        List<EventVo> convertDatas = new ArrayList<>();
        //活动id集合
        List<Long> eventIds = new ArrayList<>();
        //币种编号集合
        Set<String> currencyTypeNums = new HashSet<>();
        //城市id集合
        Set<Long> areaCityIds = new HashSet<>();
        //州省id集合
        Set<Long> areaStateIds = new HashSet<>();
        //国家id集合
        Set<Long> areaCountryIds = new HashSet<>();
        //负责人id集合
        Set<Long> staffIds = new HashSet<>();
        //所属公司id集合
//        Set<Long> companyIds = new HashSet<>();
        //获取各自集合的值
        for (EventVo eventVo : eventVos) {
            areaCountryIds.add(eventVo.getFkAreaCountryIdHold());
            areaStateIds.add(eventVo.getFkAreaStateIdHold());
            areaCityIds.add(eventVo.getFkAreaCityIdHold());
            if (GeneralTool.isNotEmpty(eventVo.getFkCurrencyTypeNum())) {
                currencyTypeNums.add(eventVo.getFkCurrencyTypeNum());
            }
            eventIds.add(eventVo.getId());
            staffIds.add(eventVo.getFkStaffIdLeader1());
            staffIds.add(eventVo.getFkStaffIdLeader2());
            if (GeneralTool.isNotEmpty(eventBill.getFkCurrencyTypeNumEvent())) {
                currencyTypeNums.add(eventBill.getFkCurrencyTypeNumEvent());
            }
            if (GeneralTool.isNotEmpty(eventVo.getEventCostCurrencyTypeNum())) {
                currencyTypeNums.add(eventVo.getEventCostCurrencyTypeNum());
            }
//            companyIds.add(eventVo.getFkCompanyId());
        }
        //feign调用获取对应map
        Map<Long, String> countryNameMap = getCountryNameMap(areaCountryIds);
        Map<Long, String> stateNameMap = getStateNameMap(areaStateIds);
        Map<Long, String> cityNameMap = getCityNameChnsByIds(areaCityIds);
        Map<String, String> currencyNameMap = getCurrencyNameMap(currencyTypeNums);
        Map<Long, String> eventTargetCountryNameMap = getEventTargetCountryNameMap(eventIds);
        Map<Long, String> staffNameMap = getStaffNameMap(staffIds);
//        Map<Long, String> companyNameMap = getCompanyNameMap(companyIds);
        Map<Integer, String> eventStatusNameMap = new HashMap<>();
        for (ProjectExtraEnum eventStatus : ProjectExtraEnum.EVENT_STATUS) {
            eventStatusNameMap.put(eventStatus.key, eventStatus.value);
        }

//        List<Long> staffFollowerIds = feignPermissionService.getStaffFollowerIds(StaffContext.getStaff().getId());
        for (EventVo eventVo : eventVos) {
            //feign调用返回的map 根据key-id获取对应value-名称 设置返回给前端
            eventVo.setAreaCountryNameHold(countryNameMap.get(eventVo.getFkAreaCountryIdHold()));
            eventVo.setAreaStateNameHold(stateNameMap.get(eventVo.getFkAreaStateIdHold()));
            eventVo.setAreaCityNameHold(cityNameMap.get(eventVo.getFkAreaCityIdHold()));
//            eventVo.setCurrencyTypeName(currencyNameMap.get(eventVo.getFkCurrencyTypeNum()));
            eventVo.setStaffNameLeader1(staffNameMap.get(eventVo.getFkStaffIdLeader1()));
            eventVo.setStaffNameLeader2(staffNameMap.get(eventVo.getFkStaffIdLeader2()));

            eventVo.setReceiptCurrencyTypeName(currencyNameMap.get(eventBill.getFkCurrencyTypeNumEvent()));
            eventVo.setEventCostCurrencyTypeNumName(currencyNameMap.get(eventVo.getEventCostCurrencyTypeNum()));
//            eventVo.setCompanyName(companyNameMap.get(event.getFkCompanyId()));
            //设置值返回前端
            eventVo.setEventTypeName(eventTypeService.getEventTypeNameById(eventVo.getFkEventTypeId()));

            //新写的代码 无用
//            Event event = BeanCopyUtils.objClone(eventVo, Event::new);
            //TODO 改过
            //String countryName = setEventTargetCountryName(eventTargetCountryNameMap, eventVo);
            String countryName = setEventTargetCountryName(eventTargetCountryNameMap, eventVo);
            eventVo.setEventTargetCountryName(countryName);
            eventVo.setStatusName(eventStatusNameMap.get(eventVo.getStatus()));

            if (GeneralTool.isNotEmpty(eventVo.getEventCostAmount())) {
                eventVo.setEventCostAmount(eventVo.getEventCostAmount().setScale(4, BigDecimal.ROUND_HALF_UP));
                eventVo.setEventCostAmountCurrency(eventVo.getEventCostAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString()+ eventVo.getEventCostCurrencyTypeNumName());

            }
            if (GeneralTool.isNotEmpty(eventVo.getAmountReceivable())) {
                eventVo.setAmountReceivable(eventVo.getAmountReceivable().setScale(4, BigDecimal.ROUND_HALF_UP));
                eventVo.setAmountReceivableCurrency(eventVo.getAmountReceivable().setScale(2, BigDecimal.ROUND_HALF_UP).toString()+ eventVo.getReceiptCurrencyTypeName());
            }

            if (GeneralTool.isNotEmpty(eventVo.getEventTime()) && GeneralTool.isNotEmpty(eventVo.getEventTimeEnd())) {
                if (sameDate(eventVo.getEventTime(), eventVo.getEventTimeEnd())) {
                    String eventTimeName = formatter.format(eventVo.getEventTime()) + "至" + new SimpleDateFormat("HH:mm:ss").format(eventVo.getEventTimeEnd());
                    eventVo.setEventTimeName(eventTimeName);
                } else {
                    String eventTimeName = formatter.format(eventVo.getEventTime()) + "至" + formatter.format(eventVo.getEventTimeEnd());
                    eventVo.setEventTimeName(eventTimeName);
                }
            }
            convertDatas.add(eventVo);
        }
        return convertDatas;
    }

    @Override
    public List<EventVo> getEventsByName(Long companyId, String eventName) {
        List<EventVo> events = eventMapper.getEventsByName(companyId, eventName);
        return events;
    }

    @Override
    public Event getEventById(Long id) {
        Event event = getById(id);
        return event;
    }

    /**
     * 修改，免评价
     * @param eventDto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public EventVo updateEventWithoutRemark(EventDto eventDto) {
        if (eventDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        Event result = eventMapper.selectById(eventDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        if (GeneralTool.isNotEmpty(eventDto.getEventTime())) {
            if (GeneralTool.isEmpty(eventDto.getEventTimeEnd())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("end_time_not_null"));
            }
        }
        validateRequiredWithoutRemark(eventDto);
        Event event = BeanCopyUtils.objClone(eventDto, Event::new);
        validataEventTarget(eventDto, event);
        utilService.updateUserInfoToEntity(event);
        int i = eventMapper.updateById(event);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
        //同时更新掉中间表r_event_target_area_country数据
        eventTargetAreaCountryService.deleteByEventId(eventDto.getId());
        insertTable(eventDto, event);
        //更改报名名册状态
        if (eventDto.getStatus() == 2 || eventDto.getStatus() == 3){
            LambdaQueryWrapper<EventRegistration> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(EventRegistration::getFkEventId, eventDto.getId());
            EventRegistration eventRegistration = new EventRegistration();
            eventRegistration.setStatus(2);
            utilService.setUpdateInfo(eventRegistration);
            eventRegistrationMapper.update(eventRegistration,lambdaQueryWrapper);
        }

        return findEventById(eventDto.getId());
    }

    /**
     * 结束免评价
     * @param eventId
     */
    @Override
    public void endWithoutRemark(Long eventId) {
        if (GeneralTool.isEmpty(eventId) || GeneralTool.isEmpty(eventMapper.selectById(eventId))) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        Event event = eventMapper.selectById(eventId);
        validateRequiredByEndWithoutRemark(event);
        event.setStatus(1);
        int i = eventMapper.updateById(event);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
    }

    @Override
    public List<EventVo> getEventSelect(Long companyId) {
//        List<Long> staffFollowerIds = verifyStudentOfferItemUtils.getStaffFollowerIds(SecureUtil.getStaffId());
        List<EventVo> eventVos = eventMapper.getEventSelect(companyId,null);
        if (GeneralTool.isEmpty(eventVos)){
            return Collections.emptyList();
        }
        return eventVos;
    }



    @Override
    public List<EventRegistrationStatisticsVo> getEventRegistrationStatistics(EventRegistrationStatisticsDto eventRegistrationStatisticsVo, Page page) {
        IPage<EventRegistrationStatisticsVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(),page.getShowCount()));
        List<EventRegistrationStatisticsVo> eventRegistrationStatisticsDtos = eventMapper.getEventRegistrationStatistics(iPage,eventRegistrationStatisticsVo);
        if (GeneralTool.isEmpty(eventRegistrationStatisticsDtos)){
            return Collections.emptyList();
        }
        page.setAll((int) iPage.getTotal());
        Set<Long> fkInstitutionProviderIds = eventRegistrationStatisticsDtos.stream().map(EventRegistrationStatisticsVo::getFkInstitutionProviderId).collect(Collectors.toSet());
        Map<Long, String> institutionProviderNamesMap = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(fkInstitutionProviderIds)){
            institutionProviderNamesMap = institutionCenterClient.getInstitutionProviderNamesByIds(fkInstitutionProviderIds).getData();
        }

        Set<Long> cityIds = eventRegistrationStatisticsDtos.stream().map(EventRegistrationStatisticsVo::getFkAreaCityIdHold).collect(Collectors.toSet());
        Map<Long, String> cityNameMap = getCityNameChnsByIds(cityIds);
        for (EventRegistrationStatisticsVo eventRegistrationStatisticsDto : eventRegistrationStatisticsDtos) {
            if (GeneralTool.isNotEmpty(eventRegistrationStatisticsDto.getFkInstitutionProviderId())){
                eventRegistrationStatisticsDto.setFkInstitutionProviderName(institutionProviderNamesMap.get(eventRegistrationStatisticsDto.getFkInstitutionProviderId()));
            }
            if (GeneralTool.isNotEmpty(cityNameMap) && GeneralTool.isNotEmpty(cityNameMap.get(eventRegistrationStatisticsDto.getFkAreaCityIdHold()))){
                eventRegistrationStatisticsDto.setAreaCityNameHold(cityNameMap.get(eventRegistrationStatisticsDto.getFkAreaCityIdHold()));
            }
        }
        return eventRegistrationStatisticsDtos;
    }

    /**
     * 验证修改 免评价
     * @param eventDto
     */
    private void validateRequiredWithoutRemark(EventDto eventDto) {
        if (eventDto.getStatus() == 1) {
            if (GeneralTool.isEmpty(eventDto.getActualAmount())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("actual_amount_null"));
            }
            if (GeneralTool.isEmpty(eventDto.getBudgetAmount())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("budget_amount_null"));
            }
            if (GeneralTool.isEmpty(eventDto.getAttendedCount())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("attended_count_null"));
            }
        }
    }

    /**
     * 总统计
     *
     * @param eventQueryDto
     * @param page
     * @return
     * @
     */
    @Override
    public List<EventDataVo> getEventDatas(EventQueryDto eventQueryDto, Page page) {
        //根据搜索维度区分
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
        page.setShowCount(1000);
        eventQueryDto = getEventVo(eventQueryDto);
        List<EventDataVo> eventDataVos = new ArrayList<>();
        //按活动对象国家搜索
        if (GeneralTool.isNotEmpty(eventQueryDto.getEventTargetCountryList())) {
            //Long countryId = eventVo.getEventTargetCountryList().get(0);
            //String countryName = feignInstitutionService.getCountryNameById(countryId);
            List<EventVo> eventDatasByCountry = getEventDataStatisticsByTargetCountry(eventQueryDto, page);
            if (!validateEventDtos(eventDatasByCountry)) {
                EventDataVo eventDataVo = new EventDataVo();
                setNullEventDataDto(eventDataVo);
                eventDataVos.add(eventDataVo);
                return eventDataVos;
            }
            DecimalFormat decimalFormat = new DecimalFormat("0.00");
            for (EventVo eventVo : eventDatasByCountry) {
                EventDataVo eventDataVo = new EventDataVo();
                //eventDataVo.setAreaCountryNameHold(countryName);
                setData(decimalFormat, eventVo);
                setEventData(eventVo, eventDataVo);
                eventDataVos.add(eventDataVo);
            }
        }
//        //按州省搜索
//        else if (GeneralTool.isNotEmpty(eventVo.getAreaStateHoldIds())||GeneralTool.isNotEmpty(eventVo.getAreaCityHoldIds())) {
//            //String stateName = feignInstitutionService.getStateNameById(eventVo.getFkAreaStateIdHold());
//            List<EventVo> eventDatasByState = getEventDataStatistics(eventVo, page);
//            if (!validateEventDtos(eventDatasByState)) {
//                EventDataVo eventDataDto = new EventDataVo();
//                setNullEventDataDto(eventDataDto);
//                eventDataVos.add(eventDataDto);
//                return eventDataVos;
//            }
//            DecimalFormat decimalFormat = new DecimalFormat("0.00");
//            for (EventVo eventDto : eventDatasByState) {
//                EventDataVo eventDataDto = new EventDataVo();
//                //eventDataDto.setAreaStateNameHold(stateName);
//                setData(decimalFormat, eventDto);
//                setEventData(eventDto, eventDataDto);
//                eventDataVos.add(eventDataDto);
//            }
//        }
//        //负责人搜索
//        else if (GeneralTool.isNotEmpty(eventVo.getFkStaffIdLeader1Ids())) {
//            //String staffName = feignPermissionService.getStaffName(eventVo.getFkStaffIdLeader1());
//            List<EventVo> eventDatasByStaff = getEventDataStatistics(eventVo, page);
//            if (!validateEventDtos(eventDatasByStaff)) {
//                EventDataVo eventDataDto = new EventDataVo();
//                setNullEventDataDto(eventDataDto);
//                eventDataVos.add(eventDataDto);
//                return eventDataVos;
//            }
//            DecimalFormat decimalFormat = new DecimalFormat("0.00");
//            for (EventVo eventDto : eventDatasByStaff) {
//                EventDataVo eventDataDto = new EventDataVo();
//                //eventDataDto.setStaffNameLeader1(staffName);
//                setData(decimalFormat, eventDto);
//                setEventData(eventDto, eventDataDto);
//                eventDataVos.add(eventDataDto);
//            }
//        }
//        //活动类型搜索
//        else if (GeneralTool.isNotEmpty(eventVo.getEventTypeIds())) {
//            //String eventTypeName = eventTypeService.getEventTypeNameById(eventVo.getFkEventTypeId());
//            List<EventVo> eventDatasByEventType = getEventDataStatistics(eventVo, page);
//            if (!validateEventDtos(eventDatasByEventType)) {
//                EventDataVo eventDataDto = new EventDataVo();
//                setNullEventDataDto(eventDataDto);
//                eventDataVos.add(eventDataDto);
//                return eventDataVos;
//            }
//            DecimalFormat decimalFormat = new DecimalFormat("0.00");
//            for (EventVo eventDto : eventDatasByEventType) {
//                EventDataVo eventDataDto = new EventDataVo();
//                //eventDataDto.setEventTypeName(eventTypeName);
//                setData(decimalFormat, eventDto);
//                setEventData(eventDto, eventDataDto);
//                eventDataVos.add(eventDataDto);
//            }
//        } else {
//            //统计全部
//            //不传维度
//            List<EventVo> eventDatas = getEventDataStatistics(eventVo, page);
//            if (!validateEventDtos(eventDatas)) {
//                EventDataVo eventDataDto = new EventDataVo();
//                setNullEventDataDto(eventDataDto);
//                eventDataVos.add(eventDataDto);
//                return eventDataVos;
//            }
//            DecimalFormat decimalFormat = new DecimalFormat("0.00");
//            for (EventVo eventData : eventDatas) {
//                EventDataVo eventDataDto = new EventDataVo();
//                setData(decimalFormat, eventData);
//                setEventData(eventData, eventDataDto);
//                eventDataVos.add(eventDataDto);
//            }
//        }
        //        //按州省-城市搜索
//        else if (CheckUtils.isNotEmpty(eventVo.getAreaStateHoldIds())||CheckUtils.isNotEmpty(eventVo.getAreaCityHoldIds())) {
//            //String stateName = feignInstitutionService.getStateNameById(eventVo.getFkAreaStateIdHold());
//            if (searchEventData(eventVo, page, eventDataVos)) return eventDataVos;
//        }
//        //负责人搜索
//        else if (CheckUtils.isNotEmpty(eventVo.getFkStaffIdLeader1Ids())) {
//            //String staffName = feignPermissionService.getStaffName(eventVo.getFkStaffIdLeader1());
//            if (searchEventData(eventVo, page, eventDataVos)) return eventDataVos;
//        }
//        //活动类型搜索
//        else if (CheckUtils.isNotEmpty(eventVo.getEventTypeIds())) {
//            //String eventTypeName = eventTypeService.getEventTypeNameById(eventVo.getFkEventTypeId());
//            if (searchEventData(eventVo, page, eventDataVos)) return eventDataVos;
//        }
        else {
            //根据条件查询
            if (searchEventData(eventQueryDto, page, eventDataVos)) return eventDataVos;
        }
        return eventDataVos;
    }

    private boolean searchEventData(EventQueryDto eventQueryDto, Page page, List<EventDataVo> eventDataVos) {
        List<EventVo> eventDatasByEventType = getEventDataStatistics(eventQueryDto, page);
        if (!validateEventDtos(eventDatasByEventType)) {
            EventDataVo eventDataVo = new EventDataVo();
            setNullEventDataDto(eventDataVo);
            eventDataVos.add(eventDataVo);
            return true;
        }
        DecimalFormat decimalFormat = new DecimalFormat("0.00");
        for (EventVo eventVo : eventDatasByEventType) {
            EventDataVo eventDataVo = new EventDataVo();
            //eventDataVo.setEventTypeName(eventTypeName);
            setData(decimalFormat, eventVo);
            setEventData(eventVo, eventDataVo);
            eventDataVos.add(eventDataVo);
        }
        return false;
    }


    /**
     * 设置查询为空时各个数据为0
     *
     * @param eventDataVo
     */
    private void setNullEventDataDto(EventDataVo eventDataVo) {
        BigDecimal zeroBigDecimal = new BigDecimal(0);
        eventDataVo.setEventCount(0);
        eventDataVo.setSumAttendedCount(0);
        eventDataVo.setAverageAttendedCount(0d);
        eventDataVo.setSumBudgetAmount(zeroBigDecimal);
        eventDataVo.setAverageBudgetAmount(zeroBigDecimal);
        eventDataVo.setSumActualAmount(zeroBigDecimal);
        eventDataVo.setAverageActualAmount(zeroBigDecimal);
    }

    /**
     * 判断为空的情况，因为用了count（*），所以查出为空也会返回一条数据，且count（*）=0
     *
     * @param eventDataDtos
     * @return
     */
    private Boolean validateEventDtos(List<EventVo> eventDataDtos) {
        if (eventDataDtos.size() == 1) {
            return eventDataDtos.get(0).getEventCount() != 0;
        }
        return true;
    }

//    @Override
//    public List<EventDataVo> getEventDatasList(EventDto eventVo, Page page)  {
////        //以活动对象国家分组
////        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        eventVo = getEventVo(eventVo);
//        List<EventDataVo> eventDataDtos = new ArrayList<>();
//        if (GeneralTool.isEmpty(eventVo.getGroupBy())) {
//            getEventTargetCountries(eventVo, page, eventDataDtos);
//            setRank(eventDataDtos);
//            return eventDataDtos;
//        }
//        if (0 == eventVo.getGroupBy()) {
//            getEventTargetCountries(eventVo, page, eventDataDtos);
//        }
//        //以举办区域分组
//        if (1 == eventVo.getGroupBy()) {
//            if (GeneralTool.isNotEmpty(eventVo.getAreaCityHoldIds())) {
//                List<EventVo> eventDatasList = getEventDatasGroupByAreaCity(eventVo, page);
//                Map<Long, String> cityNameMap;
//                Set<Long> cityIds = eventDatasList.stream().map(EventVo::getFkAreaCityIdHold).collect(Collectors.toSet());
//                cityIds.removeIf(Objects::isNull);
//                if (GeneralTool.isEmpty(cityIds)) {
//                    cityIds.add(0L);
//                }
//                cityNameMap = getCityNameMap(cityIds);
//                DecimalFormat decimalFormat = new DecimalFormat("0.00");
//                for (EventVo eventDto : eventDatasList) {
//                    EventDataVo eventDataDto = new EventDataVo();
//                    if (GeneralTool.isEmpty(cityNameMap.get(eventDto.getFkAreaCityIdHold()))) {
//                        eventDataDto.setAreaCityNameHold("暂无地区");
//                        eventDataDto.setSearchResultName("暂无地区");
//                    } else {
//                        eventDataDto.setAreaCityNameHold(MyStringUtils.extractMessageByChar(cityNameMap.get(eventDto.getFkAreaCityIdHold()), '（', '）').get(0));
//                        eventDataDto.setSearchResultName(eventDataDto.getAreaCityNameHold());
//                    }
//                    eventDataDto.setId(eventDto.getFkAreaCityIdHold());
//                    setData(decimalFormat, eventDto);
//                    setEventData(eventDto, eventDataDto);
//                    eventDataDtos.add(eventDataDto);
//                }
//            } else {
//                List<EventVo> eventDatasList = getEventDatasGroupByAreaState(eventVo, page);
//                Map<Long, String> stateNameMap;
//                Set<Long> stateIds = eventDatasList.stream().map(EventVo::getFkAreaStateIdHold).collect(Collectors.toSet());
//                stateIds.removeIf(Objects::isNull);
//                if (GeneralTool.isEmpty(stateIds)) {
//                    stateIds.add(0L);
//                }
//                stateNameMap = getStateNameMap(stateIds);
//                DecimalFormat decimalFormat = new DecimalFormat("0.00");
//                for (EventVo eventDto : eventDatasList) {
//                    EventDataVo eventDataDto = new EventDataVo();
//                    if (GeneralTool.isEmpty(stateNameMap.get(eventDto.getFkAreaStateIdHold()))) {
//                        eventDataDto.setAreaStateNameHold("暂无地区");
//                        eventDataDto.setSearchResultName("暂无地区");
//                    } else {
//                        eventDataDto.setAreaStateNameHold(MyStringUtils.extractMessageByChar(stateNameMap.get(eventDto.getFkAreaStateIdHold()), '（', '）').get(0));
//                        eventDataDto.setSearchResultName(eventDataDto.getAreaStateNameHold());
//                    }
//                    eventDataDto.setId(eventDto.getFkAreaStateIdHold());
//                    setData(decimalFormat, eventDto);
//                    setEventData(eventDto, eventDataDto);
//                    eventDataDtos.add(eventDataDto);
//                }
//            }
////            }
//        }
//        //以负责人分组
//        if (2 == eventVo.getGroupBy()) {
//            List<EventVo> eventDatasList = getEventDatasGroupByStaffIdLeader(eventVo, page);
//            Map<Long, String> staffNameMap;
//            Set<Long> staffIds = new HashSet<>();
//            for (EventVo eventDto : eventDatasList) {
//                staffIds.add(eventDto.getFkStaffIdLeader1());
//            }
//            staffNameMap = getStaffNameMap(staffIds);
//            DecimalFormat decimalFormat = new DecimalFormat("0.00");
//            for (EventVo eventDto : eventDatasList) {
//                EventDataVo eventDataDto = new EventDataVo();
//                if (GeneralTool.isEmpty(staffNameMap.get(eventDto.getFkStaffIdLeader1()))) {
//                    eventDataDto.setStaffNameLeader1("暂无负责人");
//                    eventDataDto.setSearchResultName("暂无负责人");
//                } else {
//                    eventDataDto.setStaffNameLeader1(staffNameMap.get(eventDto.getFkStaffIdLeader1()));
//                    eventDataDto.setSearchResultName(staffNameMap.get(eventDto.getFkStaffIdLeader1()));
//                }
//                eventDataDto.setId(eventDto.getFkStaffIdLeader1());
//                setData(decimalFormat, eventDto);
//                setEventData(eventDto, eventDataDto);
//                eventDataDtos.add(eventDataDto);
//            }
//        }
//        //以活动类型分组
//        if (3 == eventVo.getGroupBy()) {
//            List<EventVo> eventDatasList = getEventDatasGroupByEventTypeId(eventVo, page);
//
//            DecimalFormat decimalFormat = new DecimalFormat("0.00");
//            for (EventVo eventDto : eventDatasList) {
//                EventDataVo eventDataDto = new EventDataVo();
//                String eventTypeName = eventTypeService.getEventTypeNameById(eventDto.getFkEventTypeId());
//                eventDataDto.setEventTypeName(eventTypeName);
//                eventDataDto.setSearchResultName(eventTypeName);
//                eventDataDto.setId(eventDto.getFkEventTypeId());
//                setData(decimalFormat, eventDto);
//                setEventData(eventDto, eventDataDto);
//                eventDataDtos.add(eventDataDto);
//            }
//        }
//        setRank(eventDataDtos);
//        return eventDataDtos;
//    }

    @Override
    public List<EventDataVo> getEventDatasList(EventQueryDto eventQueryDto, Page page) {
        //以活动对象国家分组
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
        page.setShowCount(1000);
        eventQueryDto = getEventVo(eventQueryDto);
        List<EventDataVo> eventDataVos = new ArrayList<>();
        if (GeneralTool.isEmpty(eventQueryDto.getGroupBy())) {
            getEventTargetCountries(eventQueryDto, page, eventDataVos);
            setRank(eventDataVos);
            return eventDataVos;
        }
        if (Objects.equals(eventQueryDto.getGroupBy(), ProjectExtraEnum.SEARCH_BY_TARGET_COUNTRY.key)) {
            getEventTargetCountries(eventQueryDto, page, eventDataVos);
        }
        //以举办省份分组
        if (Objects.equals(eventQueryDto.getGroupBy(), ProjectExtraEnum.SEARCH_BY_STATE.key)) {
//            if (CheckUtils.isNotEmpty(eventVo.getAreaCityHoldIds())) {
//                List<EventVo> eventDatasList = getEventDatasGroupByAreaCity(eventVo, page);
//                Map<Long, String> cityNameMap;
//                Set<Long> cityIds = eventDatasList.stream().map(EventVo::getFkAreaCityIdHold).collect(Collectors.toSet());
//                cityIds.removeIf(Objects::isNull);
//                if (CheckUtils.isEmpty(cityIds)) {
//                    cityIds.add(0L);
//                }
//                cityNameMap = getCityNameMap(cityIds);
//                DecimalFormat decimalFormat = new DecimalFormat("0.00");
//                for (EventVo eventDto : eventDatasList) {
//                    EventDataVo eventDataDto = new EventDataVo();
//                    if (CheckUtils.isEmpty(cityNameMap.get(eventDto.getFkAreaCityIdHold()))) {
//                        eventDataDto.setAreaStateNameHold("暂无地区");
//                        eventDataDto.setSearchResultName("暂无地区");
//                    } else {
//                        eventDataDto.setAreaStateNameHold(MyStringUtils.extractMessageByChar(cityNameMap.get(eventDto.getFkAreaCityIdHold()), '（', '）').get(0));
//                        eventDataDto.setSearchResultName(eventDataDto.getAreaStateNameHold());
//                    }
//                    eventDataDto.setId(eventDto.getFkAreaCityIdHold());
//                    setData(decimalFormat, eventDto);
//                    setEventData(eventDto, eventDataDto);
//                    eventDataVos.add(eventDataDto);
//                }
//            } else {
            List<EventVo> eventDatasList = getEventDatasGroupByAreaState(eventQueryDto, page);
            Map<Long, String> stateNameMap;
            Set<Long> stateIds = eventDatasList.stream().map(EventVo::getFkAreaStateIdHold).collect(Collectors.toSet());
            stateIds.removeIf(Objects::isNull);
            if (GeneralTool.isEmpty(stateIds)) {
                stateIds.add(0L);
            }
            stateNameMap = getStateNameMap(stateIds);
            DecimalFormat decimalFormat = new DecimalFormat("0.00");
            for (EventVo eventVo : eventDatasList) {
                EventDataVo eventDataVo = new EventDataVo();
                if (GeneralTool.isEmpty(stateNameMap.get(eventVo.getFkAreaStateIdHold()))) {
                    eventDataVo.setAreaStateNameHold("暂无地区");
                    eventDataVo.setSearchResultName("暂无地区");
                } else {
                    eventDataVo.setAreaStateNameHold(MyStringUtils.extractMessageByChar(stateNameMap.get(eventVo.getFkAreaStateIdHold()), '（', '）').get(0));
                    eventDataVo.setSearchResultName(eventDataVo.getAreaStateNameHold());
                }
                eventDataVo.setId(eventVo.getFkAreaStateIdHold());
                setData(decimalFormat, eventVo);
                setEventData(eventVo, eventDataVo);
                eventDataVos.add(eventDataVo);
            }
//            }
        }
        //以举办城市分组
        if (Objects.equals(eventQueryDto.getGroupBy(), ProjectExtraEnum.SEARCH_BY_CITY.key)) {
            List<EventVo> eventDatasList = getEventDatasGroupByAreaCity(eventQueryDto, page);
            Map<Long, String> cityNameMap;
            Set<Long> cityIds = eventDatasList.stream().map(EventVo::getFkAreaCityIdHold).collect(Collectors.toSet());
            cityIds.removeIf(Objects::isNull);
            if (GeneralTool.isEmpty(cityIds)) {
                cityIds.add(0L);
            }
            cityNameMap = getCityNameMap(cityIds);
            DecimalFormat decimalFormat = new DecimalFormat("0.00");
            for (EventVo eventVo : eventDatasList) {
                EventDataVo eventDataVo = new EventDataVo();
                if (GeneralTool.isEmpty(cityNameMap.get(eventVo.getFkAreaCityIdHold()))) {
                    eventDataVo.setAreaStateNameHold("暂无地区");
                    eventDataVo.setSearchResultName("暂无地区");
                } else {
                    eventDataVo.setAreaStateNameHold(MyStringUtils.extractMessageByChar(cityNameMap.get(eventVo.getFkAreaCityIdHold()), '（', '）').get(0));
                    eventDataVo.setSearchResultName(eventDataVo.getAreaStateNameHold());
                }
                eventDataVo.setId(eventVo.getFkAreaCityIdHold());
                setData(decimalFormat, eventVo);
                setEventData(eventVo, eventDataVo);
                eventDataVos.add(eventDataVo);
            }
        }
        //以负责人分组
        if (Objects.equals(eventQueryDto.getGroupBy(), ProjectExtraEnum.SEARCH_BY_STAFF.key)) {
            List<EventVo> eventDatasList = getEventDatasGroupByStaffIdLeader(eventQueryDto, page);
            Map<Long, String> staffNameMap;
            Set<Long> staffIds = new HashSet<>();
            for (EventVo eventVo : eventDatasList) {
                staffIds.add(eventVo.getFkStaffIdLeader1());
            }
            staffNameMap = getStaffNameMap(staffIds);
            DecimalFormat decimalFormat = new DecimalFormat("0.00");
            for (EventVo eventVo : eventDatasList) {
                EventDataVo eventDataVo = new EventDataVo();
                if (GeneralTool.isEmpty(staffNameMap.get(eventVo.getFkStaffIdLeader1()))) {
                    eventDataVo.setStaffNameLeader1("暂无负责人");
                    eventDataVo.setSearchResultName("暂无负责人");
                } else {
                    eventDataVo.setStaffNameLeader1(staffNameMap.get(eventVo.getFkStaffIdLeader1()));
                    eventDataVo.setSearchResultName(staffNameMap.get(eventVo.getFkStaffIdLeader1()));
                }
                eventDataVo.setId(eventVo.getFkStaffIdLeader1());
                setData(decimalFormat, eventVo);
                setEventData(eventVo, eventDataVo);
                eventDataVos.add(eventDataVo);
            }
        }
        //以第二负责人分组
        if (Objects.equals(eventQueryDto.getGroupBy(), ProjectExtraEnum.SEARCH_BY_STAFF2.key)) {
            List<EventVo> eventDatasList = getEventDatasGroupByStaffIdLeader2(eventQueryDto, page);
            Map<Long, String> staffNameMap;
            Set<Long> staffIds = new HashSet<>();
            for (EventVo eventVo : eventDatasList) {
                staffIds.add(eventVo.getFkStaffIdLeader2());
            }
            staffNameMap = getStaffNameMap(staffIds);
            DecimalFormat decimalFormat = new DecimalFormat("0.00");
            for (EventVo eventVo : eventDatasList) {
                EventDataVo eventDataVo = new EventDataVo();
                if (GeneralTool.isEmpty(staffNameMap.get(eventVo.getFkStaffIdLeader2()))) {
                    eventDataVo.setStaffNameLeader2("暂无第二负责人");
                    eventDataVo.setSearchResultName("暂无第二负责人");
                } else {
                    eventDataVo.setStaffNameLeader2(staffNameMap.get(eventVo.getFkStaffIdLeader2()));
                    eventDataVo.setSearchResultName(staffNameMap.get(eventVo.getFkStaffIdLeader2()));
                }
                eventDataVo.setId(eventVo.getFkStaffIdLeader2());
                setData(decimalFormat, eventVo);
                setEventData(eventVo, eventDataVo);
                eventDataVos.add(eventDataVo);
            }
        }
        //以活动类型分组
        if (Objects.equals(eventQueryDto.getGroupBy(), ProjectExtraEnum.SEARCH_BY_EVENT_TYPE.key)) {
            List<EventVo> eventDatasList = getEventDatasGroupByEventTypeId(eventQueryDto, page);

            DecimalFormat decimalFormat = new DecimalFormat("0.00");
            for (EventVo eventVo : eventDatasList) {
                EventDataVo eventDataVo = new EventDataVo();
                String eventTypeName = eventTypeService.getEventTypeNameById(eventVo.getFkEventTypeId());
                eventDataVo.setEventTypeName(eventTypeName);
                eventDataVo.setSearchResultName(eventTypeName);
                eventDataVo.setId(eventVo.getFkEventTypeId());
                setData(decimalFormat, eventVo);
                setEventData(eventVo, eventDataVo);
                eventDataVos.add(eventDataVo);
            }
        }
        setRank(eventDataVos);
        return eventDataVos;
    }

    private void getEventTargetCountries(EventQueryDto eventQueryDto, Page page, List<EventDataVo> eventDataVos) throws GetServiceException {
        if (GeneralTool.isNotEmpty(eventQueryDto.getEventTargetCountryList())) {
            List<EventVo> eventDatasList = getEventDatasGroupByTargetCountry(eventQueryDto, page);
            Map<Long, String> countryNameMap;
            Set<Long> countryIds = new HashSet<>();
            for (EventVo eventVo : eventDatasList) {
                countryIds.add(eventVo.getFkAreaCountryIdHold());
            }
            countryNameMap = getCountryNameMap(countryIds);
            DecimalFormat decimalFormat = new DecimalFormat("0.00");
            for (EventVo eventVo : eventDatasList) {
                EventDataVo eventDataVo = new EventDataVo();
                if (GeneralTool.isEmpty(countryNameMap.get(eventVo.getFkAreaCountryIdHold()))) {
                    eventDataVo.setEventTargetCountryName("暂无国家");
                    eventDataVo.setSearchResultName("暂无国家");
                } else {
//                    eventDataVo.setEventTargetCountryName(MyStringUtils.extractMessageByChar(countryNameMap.get(eventVo.getFkAreaCountryIdHold()), '（', '）').get(0));
//                    eventDataVo.setSearchResultName(MyStringUtils.extractMessageByChar(countryNameMap.get(eventVo.getFkAreaCountryIdHold()), '（', '）').get(0));
                    //活动举办国家Name取英文
                    eventDataVo.setEventTargetCountryName(countryNameMap.get(eventVo.getFkAreaCountryIdHold()));
                    eventDataVo.setSearchResultName(countryNameMap.get(eventVo.getFkAreaCountryIdHold()));
                }
                eventDataVo.setId(eventVo.getFkAreaCountryIdHold());
                setData(decimalFormat, eventVo);
                setEventData(eventVo, eventDataVo);
                eventDataVos.add(eventDataVo);
            }
        } else {
            List<EventVo> eventDatasList = getEventDatasGroupByTargetCountry(eventQueryDto, page);
            Map<Long, String> countryNameMap;
            Set<Long> countryIds = new HashSet<>();
            for (EventVo eventVo : eventDatasList) {
                countryIds.add(eventVo.getFkAreaCountryIdHold());
            }
            countryNameMap = getCountryNameMap(countryIds);
            DecimalFormat decimalFormat = new DecimalFormat("0.00");
            for (EventVo eventVo : eventDatasList) {
                EventDataVo eventDataVo = new EventDataVo();
                if (GeneralTool.isEmpty(countryNameMap.get(eventVo.getFkAreaCountryIdHold()))) {
                    eventDataVo.setEventTargetCountryName("暂无国家");
                    eventDataVo.setSearchResultName("暂无国家");
                } else {
//                    eventDataVo.setEventTargetCountryName(MyStringUtils.extractMessageByChar(countryNameMap.get(eventVo.getFkAreaCountryIdHold()), '（', '）').get(0));
//                    eventDataVo.setSearchResultName(MyStringUtils.extractMessageByChar(countryNameMap.get(eventVo.getFkAreaCountryIdHold()), '（', '）').get(0));
                    //活动举办国家改成英文显示
                    eventDataVo.setEventTargetCountryName(countryNameMap.get(eventVo.getFkAreaCountryIdHold()));
                    eventDataVo.setSearchResultName(countryNameMap.get(eventVo.getFkAreaCountryIdHold()));
                }
                eventDataVo.setId(eventVo.getFkAreaCountryIdHold());
                setData(decimalFormat, eventVo);
                setEventData(eventVo, eventDataVo);
                eventDataVos.add(eventDataVo);
            }
        }
    }

    /**
     * 设置排名
     *
     * @param eventDataVos
     */
    private void setRank(List<EventDataVo> eventDataVos) {
        int rank = 1;
        for (int i = 0; i < eventDataVos.size(); i++) {
            if (i > 0 && (eventDataVos.get(i).getEventCount().equals(eventDataVos.get(i - 1).getEventCount()))) {
                eventDataVos.get(i).setRank(eventDataVos.get(i - 1).getRank());
                rank++;
            } else {
                eventDataVos.get(i).setRank(rank);
                rank++;
            }

        }
    }


    @Override
    public List<AreaCityVo> getCitySelect(Long companyId, List<Long> eventStateIds) {
        List<AreaCityVo> areaCityVos = new ArrayList<>();
//        if (GeneralTool.isEmpty(eventStateIds)) {
//            eventStateIds.add(0L);
//        }
        List<Long> cityIds = eventMapper.getEventCityList(companyId, eventStateIds);
        Set<Long> cityIdSet = new HashSet<>(cityIds);
        Map<Long, String> cityNameMap = getCityNameMap(cityIdSet);
        for (Long cityId : cityIds) {
            AreaCityVo areaCityVo = new AreaCityVo();
            areaCityVo.setId(cityId);
            areaCityVo.setName(cityNameMap.get(cityId));
            areaCityVos.add(areaCityVo);
        }
        return areaCityVos;
    }

    /**
     * 区域下拉框
     *
     * @param companyId
     * @return
     */
    @Override
    public List<AreaStateVo> getEventStateList(Long companyId) {
        List<AreaStateVo> areaStateVos = new ArrayList<>();
        List<Long> stateIds = eventMapper.getEventStateList(companyId);
        Set<Long> stateIdsSet = new HashSet<>(stateIds);
        Map<Long, String> stateNameMap = getStateNameMap(stateIdsSet);
        for (Long stateId : stateIds) {
            AreaStateVo areaStateVo = new AreaStateVo();
            areaStateVo.setId(stateId);
            areaStateVo.setName(stateNameMap.get(stateId));
            areaStateVos.add(areaStateVo);
        }
        return areaStateVos;
    }

    /**
     * 国家下拉
     *
     * @param companyId
     * @return
     */
    @Override
    public List<AreaCountryVo> getEventCountryList(Long companyId) {
        List<AreaCountryVo> areaCountryVos = new ArrayList<>();
        List<Long> countryIds = eventMapper.getEventTargetCountryList(companyId);

        Set<Long> countryIdsSet = new HashSet<>(countryIds);
        Map<Long, String> countryNameMap = getCountryNameMap(countryIdsSet);
        for (Long countryId : countryIds) {
            AreaCountryVo areaCountryVo = new AreaCountryVo();
            areaCountryVo.setId(countryId);
            areaCountryVo.setName(countryNameMap.get(countryId));
            areaCountryVos.add(areaCountryVo);
        }
        return areaCountryVos;
    }

    /**
     * 负责人下拉框
     *
     * @param companyId
     * @return
     */
    @Override
    public List<StaffVo> getEventStaffList(Long companyId) {
        List<StaffVo> staffVos = new ArrayList<>();
        List<Long> staffIds = eventMapper.getEventStaffList(companyId);
        Set<Long> staffIdsSet = new HashSet<>(staffIds);
        Map<Long, String> staffNameMap = getStaffNameMap(staffIdsSet);
        for (Long staffId : staffIds) {
            StaffVo staffVo = new StaffVo();
            staffVo.setId(staffId);
            staffVo.setName(staffNameMap.get(staffId));
            staffVos.add(staffVo);
        }
        return staffVos;
    }

    /**
     * 第二负责人下拉框
     *
     * @param companyId
     * @return
     */
    @Override
    public List<StaffVo> getEventStaff2List(Long companyId) {
        List<StaffVo> staffVos = new ArrayList<>();
        List<Long> staffIds = eventMapper.getEventStaff2List(companyId);
        Set<Long> staffIdsSet = new HashSet<>(staffIds);
        Map<Long, String> staffNameMap = getStaffNameMap(staffIdsSet);
        for (Long staffId : staffIds) {
            StaffVo staffVo = new StaffVo();
            staffVo.setId(staffId);
            staffVo.setName(staffNameMap.get(staffId));
            staffVos.add(staffVo);
        }
        return staffVos;
    }


    /**
     * 活动类型下拉框
     *
     * @param companyId
     * @return
     */
    @Override
    public List<EventTypeVo> getEventTypeSelect(Long companyId) {
        List<EventTypeVo> eventTypeVos = new ArrayList<>();
        //获取下属员工所有部门
        Set<Long> fkDepartIds = permissionCenterClient.getStaffDepartmentsById(SecureUtil.getStaffId()).getData();
        List<Long> eventTypeIds = eventMapper.getEventTypeList(companyId,fkDepartIds);
        Set<Long> eventTypeIdsSet = new HashSet<>(eventTypeIds);
        Map<Long, String> eventTypeNameMap = getEventTypeNameMap(eventTypeIdsSet);
        for (Long eventTypeId : eventTypeIds) {
            EventTypeVo eventTypeVo = new EventTypeVo();
            eventTypeVo.setId(eventTypeId);
            eventTypeVo.setTypeName(eventTypeNameMap.get(eventTypeId));
            eventTypeVo.setName(eventTypeNameMap.get(eventTypeId));
            eventTypeVos.add(eventTypeVo);
        }
        return eventTypeVos;
    }

    @Override
    public List<AreaCityVo> getEventCitySelect(Long companyId) {
        List<AreaCityVo> areaCityVos = new ArrayList<>();
        List<Long> cityIds = eventMapper.getEventCitySelectList(companyId);
        Set<Long> cityIdsSet = new HashSet<>(cityIds);
        Map<Long, String> cityNameMap = getStateNameMap(cityIdsSet);
        for (Long cityId : cityIds) {
            AreaCityVo areaCityVo = new AreaCityVo();
            areaCityVo.setId(cityId);
            areaCityVo.setName(cityNameMap.get(cityId));
            areaCityVos.add(areaCityVo);
        }
        return areaCityVos;
    }

    /**
     * 查询eventType的名称
     *
     * @param eventTypeIds
     * @return
     */
    private Map<Long, String> getEventTypeNameMap(Set<Long> eventTypeIds) {
        eventTypeIds.removeIf(Objects::isNull);
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(eventTypeIds)) {
            return map;
        }
//        Example example = new Example(EventType.class);
//        example.createCriteria().andIn("id", eventTypeIds);
        List<EventType> eventTypes = eventTypeMapper.selectBatchIds(eventTypeIds);
        if (GeneralTool.isEmpty(eventTypes)) {
            return map;
        }
        for (EventType eventType : eventTypes) {
            map.put(eventType.getId(), eventType.getTypeName());
        }
        return map;
    }

    //统一结果
    private void setEventData(EventVo eventVo, EventDataVo eventDataVo) {
        eventDataVo.setEventCount(eventVo.getEventCount());
        eventDataVo.setSumAttendedCount(eventVo.getSumAttendedCount());
        eventDataVo.setAverageAttendedCount(eventVo.getAverageAttendedCount());
        eventDataVo.setSumBudgetAmount(eventVo.getSumBudgetAmount());
        eventDataVo.setAverageBudgetAmount(eventVo.getAverageBudgetAmount());
        eventDataVo.setSumActualAmount(eventVo.getSumActualAmount());
        eventDataVo.setAverageActualAmount(eventVo.getAverageActualAmount());
    }


    private void setData(DecimalFormat decimalFormat, EventVo event) throws GetServiceException {
        //设置平均参与人数 保留两位
        if (GeneralTool.isEmpty(event.getSumAttendedCount())) {
            event.setSumAttendedCount(0);
        }
        if (event.getEventCount() != 0 && event.getEventCount() != null) {
            Double averageAttendedCount = Double.valueOf(decimalFormat.format((double) event.getSumAttendedCount() / event.getEventCount()));
            event.setAverageAttendedCount(averageAttendedCount);
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("data_exception"));
        }
        //设置平均预算
        if (GeneralTool.isNotEmpty(event.getSumBudgetAmount()) && GeneralTool.isNotEmpty(event.getEventCount())) {
            BigDecimal averageBudgetAmount = event.getSumBudgetAmount().divide(new BigDecimal(event.getEventCount()), 2, BigDecimal.ROUND_HALF_UP);
            event.setAverageBudgetAmount(averageBudgetAmount);
        }
        //设置平均实际预算
        if (GeneralTool.isNotEmpty(event.getSumActualAmount()) && GeneralTool.isNotEmpty(event.getEventCount())) {
            BigDecimal averageActualAmount = event.getSumActualAmount().divide(new BigDecimal(event.getEventCount()), 2, BigDecimal.ROUND_HALF_UP);
            event.setAverageActualAmount(averageActualAmount);
        }
    }

    private List<EventVo> getEventDatasGroupByEventTypeId(EventQueryDto eventQueryDto, Page page) {
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        IPage<EventVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(),page.getShowCount()));
//        List<EventVo> events = eventMapper.getEventDatasGroupByEventTypeId(iPage,eventVo);
//        page.setAll((int)iPage.getTotal());
        List<EventVo> events = eventMapper.getEventDatasGroupByEventTypeId(eventQueryDto);
//        page.restPage(events);
        return events;
    }

    private List<EventVo> getEventDatasGroupByStaffIdLeader(EventQueryDto eventQueryDto, Page page) {
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        IPage<EventVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(),page.getShowCount()));
//        List<EventVo> events = eventMapper.getEventDatasGroupByStaffIdLeader(iPage,eventVo);
        List<EventVo> events = eventMapper.getEventDatasGroupByStaffIdLeader(null, eventQueryDto);
//        page.setAll((int)iPage.getTotal());
//        page.restPage(events);
        return events;
    }

    private List<EventVo> getEventDatasGroupByAreaCity(EventQueryDto eventQueryDto, Page page) {
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        IPage<EventVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<EventVo> events = eventMapper.getEventDatasGroupByAreaCity(null, eventQueryDto);
//        page.setAll((int)iPage.getTotal());
//        page.restPage(events);
        return events;
    }

    private List<EventVo> getEventDatasGroupByStaffIdLeader2(EventQueryDto eventQueryDto, Page page) {
        List<EventVo> events = eventMapper.getEventDatasGroupByStaffIdLeader2(eventQueryDto);
        return events;
    }

    private List<EventVo> getEventDatasGroupByAreaState(EventQueryDto eventQueryDto, Page page) {
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        IPage<EventVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(),page.getShowCount()));
        List<EventVo> events = eventMapper.getEventDatasGroupByAreaState(null, eventQueryDto);
//        page.setAll((int)iPage.getTotal());
//        page.restPage(events);
        return events;
    }

    private List<EventVo> getEventDatasGroupByAreaCountry(EventDto eventDto, Page page) {
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
        IPage<EventVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<EventVo> events = eventMapper.getEventDatasGroupByCountry(iPage, eventDto);
        page.setAll((int) iPage.getTotal());
//        page.restPage(events);
        return events;
    }

    /**
     * 按活动对象国家查询
     *
     * @param eventQueryDto
     * @param page
     * @return
     */
    private List<EventVo> getEventDatasGroupByTargetCountry(EventQueryDto eventQueryDto, Page page) {
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        IPage<EventVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(),page.getShowCount()));
        List<EventVo> events = eventMapper.getEventDatasGroupByTargetCountry(eventQueryDto);
//        page.setAll((int)iPage.getTotal());
//        page.restPage(events);
        return events;
    }

    /**
     * 按活动对象国家查询
     *
     * @param eventQueryDto
     * @param page
     * @return
     */
    private List<EventVo> getEventDataStatisticsByTargetCountry(EventQueryDto eventQueryDto, Page page) {
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
        IPage<EventVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<EventVo> events = eventMapper.getEventDataStatisticsByTargetCountry(iPage, eventQueryDto);
        page.setAll((int) iPage.getTotal());
//        page.restPage(events);
        return events;
    }

    private List<EventVo> getEventDataStatistics(EventQueryDto eventQueryDto, Page page) {
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
        IPage<EventVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<EventVo> events = eventMapper.getEventDataStatistics(iPage, eventQueryDto);
        page.setAll((int) iPage.getTotal());
//        page.restPage(events);
        return events;
    }


    /**
     * @return java.util.List<com.get.salecenter.entity.Event>
     * @Description :列表数据(分页)
     * @Param [eventVo, page]
     * <AUTHOR>
     */
    private List<Event> getEventPageList(EventQueryDto eventQueryVo, Page page) {
        eventQueryVo = getEventVo(eventQueryVo);
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
        Set<Long> fkDepartIds = permissionCenterClient.getStaffDepartmentsById(SecureUtil.getStaffId()).getData();
        List<Event> events = new ArrayList<>();
        if (page == null){
            events = eventMapper.getEventList(null, eventQueryVo, fkDepartIds);
        } else {
            IPage<EventVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
            //获取下属员工所有部门
            events = eventMapper.getEventList(iPage, eventQueryVo,fkDepartIds);
            page.setAll((int) iPage.getTotal());
        }
//        page.restPage(events);
        return events;
    }


    private EventQueryDto getEventVo(EventQueryDto eventQueryDto) {
        //不选所属公司时
        if (GeneralTool.isEmpty(eventQueryDto) || GeneralTool.isEmpty(eventQueryDto.getFkCompanyId())) {
            List<Long> companyIds = getCompanyIds();
            if (GeneralTool.isEmpty(eventQueryDto)) {
                eventQueryDto = new EventQueryDto();
            }
            eventQueryDto.setCompanyIds(companyIds);
        }
        if (GeneralTool.isNotEmpty(eventQueryDto)) {
            //查询条件-所属公司
            if (GeneralTool.isNotEmpty(eventQueryDto.getFkCompanyId())) {
                if (!SecureUtil.validateCompany(eventQueryDto.getFkCompanyId())) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
                }
            }
            //查询条件-活动对象国家
            if (GeneralTool.isNotEmpty(eventQueryDto.getEventTargetCountryList())) {
                //通过国家id 获取对应活动id
//                List<Long> eventIds = eventTargetAreaCountryService.getEventIdsByCountryId(eventVo.getEventTargetCountryList().get(0));
                List<Long> eventIds = eventTargetAreaCountryService.getEventIdsByCountryIds(eventQueryDto.getEventTargetCountryList());
                eventQueryDto.setEventIds(eventIds);
            }
            //查询条件-负责人
            if (GeneralTool.isNotEmpty(eventQueryDto.getStaffNameLeaderKey())) {
                //feign调用 根据员工姓名模糊查询员工ids
                Result<List<Long>> result = permissionCenterClient.getStaffIdsByNameKeyOrEnNameKey(eventQueryDto.getStaffNameLeaderKey());
                if (result.isSuccess() && CollectionUtil.isNotEmpty(result.getData())) {
                    List<Long> staffIds = result.getData();
                    eventQueryDto.setStaffIds(staffIds);
                }

            }
        }
        return eventQueryDto;
    }

    /**
     * @return void
     * @Description :设置dto的媒体附件
     * @Param [eventVo]
     * <AUTHOR>
     */
    private void setMediaAndAttachedDtoList(EventVo eventVo, MediaAndAttachedDto mediaAndAttachedDto) {
        mediaAndAttachedDto.setFkTableId(eventVo.getId());
        mediaAndAttachedDto.setFkTableName(TableEnum.SALE_EVENT.key);
        List<MediaAndAttachedVo> mediaAndAttachedVoList = attachedService.getMediaAndAttachedDto(mediaAndAttachedDto);
        eventVo.setMediaAndAttachedDtoList(mediaAndAttachedVoList);
    }

    /**
     * @return void
     * @Description :设置dto的活动对象国家名称
     * @Param [eventTargetCountryNameMap, eventDto]
     * <AUTHOR>
     */
    private String setEventTargetCountryName(Map<Long, String> eventTargetCountryNameMap, EventVo eventVo) {
        //该活动对应的国家
        List<Long> eventTargetCountryIdList = eventTargetAreaCountryService.getCountryIdsByEventIds(Collections.singletonList(eventVo.getId()));
        //拼接
        StringJoiner stringJoiner = new StringJoiner(",");
        for (Long eventTargetCountryId : eventTargetCountryIdList) {
            String eventTargetCountryName = eventTargetCountryNameMap.get(eventTargetCountryId);
            if (GeneralTool.isNotEmpty(eventTargetCountryName)) {
                stringJoiner.add(eventTargetCountryName);
            }
        }
        return stringJoiner.toString();
    }

    /**
     * @Description :feign调用一次 查出全部对应公司名称
     * @Param [companyIds]
     * <AUTHOR>
     */
    private Map<Long, String> getCompanyNameMap(Set<Long> companyIds) {
        companyIds.removeIf(Objects::isNull);
        //feign调用一次查出全部对应公司名称
//        return permissionCenterClient.getCompanyNamesByIds(companyIds);
        Result<Map<Long, String>> result = permissionCenterClient.getCompanyNamesByIds(companyIds);
        if (result.isSuccess()) {
            return result.getData();
        }
        return new HashMap<>();
    }

    /**
     * @Description :feign调用一次查出全部负责人对应名称
     * @Param [staffIds]
     * <AUTHOR>
     */
    private Map<Long, String> getStaffNameMap(Set<Long> staffIds) {
        staffIds.removeIf(Objects::isNull);
        //feign调用一次查出全部对应员工名称
        Result<Map<Long, String>> result = permissionCenterClient.getStaffNameMap(staffIds);
        if (result.isSuccess() && result.getData() != null) {
            return result.getData();
        }
        return new HashMap<>();
    }

    /**
     * @Description :feign调用一次查出全部活动对象国家名称
     * @Param [events]
     * <AUTHOR>
     */
    private Map<Long, String> getEventTargetCountryNameMap(List<Long> eventIds) {
        //查询中间表获取活动对象国家id
        Set<Long> eventTargetCountryIds = new HashSet<>(eventTargetAreaCountryService.getCountryIdsByEventIds(eventIds));
        eventTargetCountryIds.removeIf(Objects::isNull);
//        //feign调用一次查出全部活动对象国家名称
//        return institutionCenterClient.getCountryNamesByIds(eventTargetCountryIds);
        Result<Map<Long, String>> countryNameResult = institutionCenterClient.getCountryNamesByIds(eventTargetCountryIds);
        if (countryNameResult.isSuccess() && GeneralTool.isNotEmpty(countryNameResult.getData())) {
            return countryNameResult.getData();
        }
        return Maps.newHashMap();
    }

    /**
     * @Description :feign调用一次查出全部对应币种名称
     * @Param [events]
     * <AUTHOR>
     */
    private Map<String, String> getCurrencyNameMap(Set<String> currencyTypeNums) {
        currencyTypeNums.removeIf(Objects::isNull);
        //feign调用一次查出全部对应币种名称
//        return financeCenterClient.getCurrencyNamesByNums(currencyTypeNums);
        Result<Map<String, String>> result = financeCenterClient.getCurrencyNamesByNums(currencyTypeNums);
        if (result.isSuccess() && result.getData() != null) {
            return result.getData();
        }
        return new HashMap<>();
    }

    /**
     * @Description :feign调用一次查出全部对应城市名称
     * @Param [events]
     * <AUTHOR>
     */
    private Map<Long, String> getCityNameMap(Set<Long> areaCityIds) {
        areaCityIds.removeIf(Objects::isNull);
        //feign调用一次查出全部对应城市名称
//        return institutionCenterClient.getCityNamesByIds(areaCityIds);
        Result<Map<Long, String>> result = institutionCenterClient.getCityNamesByIds(areaCityIds);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            return result.getData();
        }
        return Maps.newHashMap();
    }

    /**
     * @Description :feign调用一次查出全部对应城市中文名称
     * @Param [events]
     * <AUTHOR>
     */
    private Map<Long, String> getCityNameChnsByIds(Set<Long> areaCityIds) {
        areaCityIds.removeIf(Objects::isNull);
        //feign调用一次查出全部对应城市名称
        Result<Map<Long, String>> result = institutionCenterClient.getCityNamesByIds(areaCityIds);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            return result.getData();
        }
        return Maps.newHashMap();
    }

    /**
     * @Description :feign调用一次查出全部对应州省名称
     * @Param [events]
     * <AUTHOR>
     */
    private Map<Long, String> getStateNameMap(Set<Long> areaStateIds) {
        areaStateIds.removeIf(Objects::isNull);
        //feign调用一次查出全部对应州省名称
        Result<Map<Long, String>> result = institutionCenterClient.getStateNamesByIds(areaStateIds);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            return result.getData();
        }
        return Maps.newHashMap();
    }


    /**
     * @Description :feign调用一次查出全部对应国家名称
     * @Param [events]
     * <AUTHOR>
     */
    private Map<Long, String> getCountryNameMap(Set<Long> areaCountryIds) {
        areaCountryIds.removeIf(Objects::isNull);
        //feign调用一次查出全部对应国家名称
        Result<Map<Long, String>> result = institutionCenterClient.getCountryNamesByIds(areaCountryIds);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            return result.getData();
        }
        return Maps.newHashMap();
    }

    /**
     * @return void
     * @Description :中间表r_event_target_area_country数据新增
     * @Param [event]
     * <AUTHOR>
     */
    private void insertTable(EventDto eventDto, Event event) {
        EventTargetAreaCountryDto eventTargetAreaCountryDto = new EventTargetAreaCountryDto();
        eventTargetAreaCountryDto.setFkEventId(event.getId());
        for (Long countryId : eventDto.getEventTargetCountryList()) {
            eventTargetAreaCountryDto.setFkAreaCountryId(countryId);
            eventTargetAreaCountryService.addEventTargetAreaCountry(eventTargetAreaCountryDto);
        }
    }

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :获取登录人对应所有公司id集合
     * @Param []
     * <AUTHOR>
     */
    private List<Long> getCompanyIds() {
        return SecureUtil.getCompanyIdsByStaffId(GetAuthInfo.getStaffId());
    }


    /**
     * 当活动时间或者活动状态发生变化时，邮件提醒
     *
     * @param oldEvent 修改前活动对象
     * @param newEvent 修改后活动对象
     */
    @Override
    public void  eventReminderEmail(Event oldEvent, Event newEvent) {
        if (GeneralTool.isEmpty(oldEvent) || GeneralTool.isEmpty(newEvent)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        // 如果活动时间或者活动状态没有发生改变，则不发邮件提醒
        String oldEventTime = GeneralTool.isNotEmpty(oldEvent.getEventTime()) ? formatter.format(oldEvent.getEventTime()) : "";
        String newEventTime = GeneralTool.isNotEmpty(newEvent.getEventTime()) ? formatter.format(newEvent.getEventTime()) : "";
        String oldEventTimeEnd = GeneralTool.isNotEmpty(oldEvent.getEventTimeEnd()) ? formatter.format(oldEvent.getEventTimeEnd()) : "";
        String newEventTimeEnd = GeneralTool.isNotEmpty(newEvent.getEventTimeEnd()) ? formatter.format(newEvent.getEventTimeEnd()) : "";
        if (oldEventTime.equals(newEventTime)
                && oldEventTimeEnd.equals(newEventTimeEnd)
                && oldEvent.getStatus().equals(newEvent.getStatus())
        ) {
            return;
        }
        //不等于
        Map<Long, CompanyConfigAnalysisVo> configMap = permissionCenterClient.getCompanyConfigAnalysis(ProjectKeyEnum.REMINDER_EMAIL_EVENT.key).getData();
        // 是否进行邮件提醒
        boolean flag = false;
        // 配置提醒人员ids
        List<Long> staffIds = Lists.newArrayList();
        if (GeneralTool.isNotEmpty(configMap) && configMap.containsKey(SecureUtil.getFkCompanyId())) {
            CompanyConfigAnalysisVo companyConfigAnalysisDto = configMap.get(SecureUtil.getFkCompanyId());
            String value1 = companyConfigAnalysisDto.getValue1();
            flag = "1".equals(value1);
            String value2 = companyConfigAnalysisDto.getValue2();
            staffIds = JSONArray.parseArray(value2, Long.class);
            staffIds = staffIds.stream().distinct().filter(Objects::nonNull).collect(Collectors.toList());
        }
        if (flag) {
            // 获取通知人员的业务国家权限
            Map<Long, List<Long>> staffIdCountryIdsMap = Maps.newHashMap();
            List<StaffAreaCountry> staffAreaCountryList = permissionCenterClient.getStaffAreaCountryByStaffIds(new ArrayList<>(staffIds)).getData();
            if (GeneralTool.isNotEmpty(staffAreaCountryList)) {
                Set<String> fkAreaCountryKeys = staffAreaCountryList.stream().map(StaffAreaCountry::getFkAreaCountryKey).collect(Collectors.toSet());
                Result<Map<String, Long>> result = institutionCenterClient.getCountryIdByKeys(fkAreaCountryKeys);
                Map<String, Long> keyIdMap = Maps.newHashMap();
                if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                    keyIdMap = result.getData();
                }
                Map<String, Long> finalKeyIdMap = keyIdMap;
                staffIdCountryIdsMap = staffAreaCountryList.stream()
                        .filter(sac -> finalKeyIdMap.containsKey(sac.getFkAreaCountryKey()))
                        .collect(Collectors.groupingBy(
                                StaffAreaCountry::getFkStaffId,
                                Collectors.mapping(
                                        sac -> finalKeyIdMap.get(sac.getFkAreaCountryKey()),
                                        Collectors.toList()
                                )
                        ));
            }
            Long eventId = newEvent.getId();
            // 获取活动的业务国家
            List<Long> eventTargetAreaCountryIds = eventTargetAreaCountryService.getCountryIdsByEventIds(Collections.singletonList(eventId));
            // 邮件提醒的人员ids
            Set<Long> reminderStaffIds = Sets.newHashSet();
            // 根据活动的业务国家，若通知人员包含其中任何一个业务国家的权限，都需要进行通知。
            for (Long staffId : staffIds) {
                //配置提醒人员id们对应的国家
                List<Long> staffCountryIds = staffIdCountryIdsMap.getOrDefault(staffId, Lists.newArrayList());
                if (staffCountryIds.stream().anyMatch(eventTargetAreaCountryIds::contains)) {
                    reminderStaffIds.add(staffId);
                }
            }

            // 邮件模板信息
            Map<String, String> map = Maps.newHashMap();
            // 活动时间
            StringBuilder eventTimeName = new StringBuilder();
            if (GeneralTool.isNotEmpty(newEvent.getEventTime()) && GeneralTool.isNotEmpty(newEvent.getEventTimeEnd())) {
                String newEventTimeStr;
//                if (sameDate(newEvent.getEventTime(), newEvent.getEventTimeEnd())) {
//                    newEventTimeStr = formatter.format(newEvent.getEventTime()) + "至" + new SimpleDateFormat("HH:mm:ss").format(newEvent.getEventTimeEnd());
//                } else {
//                    newEventTimeStr = formatter.format(newEvent.getEventTime()) + "至" + formatter.format(newEvent.getEventTimeEnd());
//                }
//                eventTimeName.append(newEventTimeStr);
//TODO HTI_EVENT_CHANGE_NOTICE
                if (sameDate(newEvent.getEventTime(), newEvent.getEventTimeEnd())) {
                    newEventTimeStr = "<span style='color:orange;'>"
                            + formatter.format(newEvent.getEventTime())
                            + "至"
                            + new SimpleDateFormat("HH:mm:ss").format(newEvent.getEventTimeEnd())
                            + "</span>";
                } else {
                    newEventTimeStr = "<span style='color:orange;'>"
                            + formatter.format(newEvent.getEventTime())
                            + "至"
                            + formatter.format(newEvent.getEventTimeEnd())
                            + "</span>";
                }
                eventTimeName.append(newEventTimeStr);
            }

            if (GeneralTool.isNotEmpty(oldEvent.getEventTime()) && GeneralTool.isNotEmpty(oldEvent.getEventTimeEnd())) {
                String oldEventTimeStr;
                if (sameDate(oldEvent.getEventTime(), oldEvent.getEventTimeEnd())) {
                    oldEventTimeStr = formatter.format(oldEvent.getEventTime()) + "至" + new SimpleDateFormat("HH:mm:ss").format(oldEvent.getEventTimeEnd());
                } else {
                    oldEventTimeStr = formatter.format(oldEvent.getEventTime()) + "至" + formatter.format(oldEvent.getEventTimeEnd());
                }
                eventTimeName.append("（原：").append(oldEventTimeStr).append("）");
            }
            map.put("eventTimeName", eventTimeName.toString());
            // 活动状态
            StringBuilder eventStatusName = new StringBuilder();
            if (GeneralTool.isNotEmpty(newEvent.getStatus())) {
                String newEventStatusName = ProjectExtraEnum.getInitialValueByKey(newEvent.getStatus(), ProjectExtraEnum.EVENT_STATUS);
                // 将状态名称用<span>标签包裹，并设置字体颜色为橙色
                String formattedStatusName = "<span style='color:orange;'>" + newEventStatusName + "</span>";
                eventStatusName.append(formattedStatusName);
            }


            if (GeneralTool.isNotEmpty(oldEvent.getStatus())) {
                String oldEventStatusName = ProjectExtraEnum.getInitialValueByKey(oldEvent.getStatus(), ProjectExtraEnum.EVENT_STATUS);
                eventStatusName.append("（原：").append(oldEventStatusName).append("）");
            }
            map.put("eventStatusName", eventStatusName.toString());
            map.put("gmtModifiedUser", newEvent.getGmtModifiedUser());
            if (GeneralTool.isNotEmpty(newEvent.getGmtModified())) {
                map.put("gmtModified", formatter.format(newEvent.getGmtModified()));
            }
            // 发送邮件
            String taskTitle = "【" + newEvent.getEventTheme() + "】活动变化通知";
            List<RemindTaskDto> remindTaskVos = Lists.newArrayList();
            if (GeneralTool.isNotEmpty(reminderStaffIds)) {
                for (Long staffId : reminderStaffIds) {
                    RemindTaskDto remindTaskVo = new RemindTaskDto();
                    remindTaskVo.setTaskTitle(taskTitle);
                    remindTaskVo.setTaskRemark(MyStringUtils.getReminderTemplate(map, SaleCenterConstant.EVENT_DATE_OR_STATUS_CHANGE_REMINDER));
                    //邮件方式发送
                    remindTaskVo.setRemindMethod("1");
                    //默认设置执行中
                    remindTaskVo.setStatus(1);
                    //默认背景颜色
                    remindTaskVo.setTaskBgColor("#3788d8");
                    remindTaskVo.setFkStaffId(staffId);
                    remindTaskVo.setStartTime(new Date());
                    remindTaskVo.setFkTableName(TableEnum.SALE_EVENT.key);
                    remindTaskVo.setFkTableId(eventId);
                    remindTaskVo.setFkRemindEventTypeKey(ProjectKeyEnum.HTI_EVENT_CHANGE_NOTICE.key);
                    remindTaskVos.add(remindTaskVo);
                }
            }

            // 批量添加到任务队列
            if (GeneralTool.isNotEmpty(remindTaskVos)) {
               // Result<Boolean> result = reminderCenterClient.batchAdd(remindTaskVos);
                 Result<Boolean> result = reminderCenterClient.batchAddTask(remindTaskVos);
                if (!result.isSuccess() || GeneralTool.isEmpty(result.getData())) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("news_emil_send_fail"));
                }
            }

            List<EmailSenderQueue> emailSenderQueueList = new ArrayList<>();
            EmailSenderQueue emailSenderQueue = new EmailSenderQueue();
            emailSenderQueue.setFkDbName(ProjectKeyEnum.SALE_CENTER.key);
            emailSenderQueue.setFkTableId(eventId);
            emailSenderQueue.setFkTableName(TableEnum.SALE_EVENT.key);
            emailSenderQueue.setFkEmailTypeKey(EmailTemplateEnum.HTI_EVENT_CHANGE_NOTICE.getEmailTemplateKey());
            emailSenderQueue.setOperationTime(now());
            emailSenderQueue.setEmailParameter(map.toString());
            emailSenderQueue.setEmailTo(reminderStaffIds.toString());
            emailSenderQueueList.add(emailSenderQueue);
            Result<Boolean> booleanResult = reminderCenterClient.batchAddEmailQueue(emailSenderQueueList);
            if (!booleanResult.isSuccess()) {
                throw new GetServiceException(LocaleMessageUtils.getMessage(booleanResult.getMessage()));
            }
        }
    }

    @Override
    public ResponseBo checkEventActivityFees(EventDto eventDto) {
        Long fkCompanyId = SecureUtil.getFkCompanyId();
//        List<Long> companyIds = SecureUtil.getCompanyIds();
        //读取配置文件 活动汇总批核预算费用警戒值（单位为百分比），value1=警戒值，0为不设置
        //value1={"GET":0,"HTI":90,"OTHER":0}
        Map<Long, CompanyConfigAnalysisVo> configMap = permissionCenterClient.getCompanyConfigAnalysis(ProjectKeyEnum.EVENT_TOTAL_BUDGET_LIMIT.key).getData();
        if (GeneralTool.isNotEmpty(configMap) && configMap.containsKey(fkCompanyId)){
            CompanyConfigAnalysisVo companyConfigAnalysisVo = configMap.get(fkCompanyId);
            //获取配置文件的值
            BigDecimal configNum = new BigDecimal(companyConfigAnalysisVo.getValue1().trim());
            if (eventDto.getAllocatedAmount() == null){
                System.out.println("已配活动费用为空");
            }
            if (GeneralTool.isNotEmpty(eventDto.getAllocatedAmount())){
                System.out.println("已配活动费用不为空");
            }

            if (eventDto.getAllocatedAmount() !=null && eventDto.getBudgetAmount() != null){
                // 根据配置文件的值，计算并对比活动费用是否超出预算
                if(GeneralTool.isEmpty(configNum)){
                    configNum = BigDecimal.valueOf(0);
                }
                BigDecimal configurationRatio = configNum.divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
                if (GeneralTool.isEmpty(configurationRatio)){
                    configurationRatio = BigDecimal.valueOf(0);
                }
                BigDecimal ActualAmount = eventDto.getAllocatedAmount().multiply(configurationRatio);
                if (eventDto.getBudgetAmount().compareTo(ActualAmount) == 1){
                    if (configNum.compareTo(BigDecimal.valueOf(0)) == 0){
                        return new ResponseBo<>(LocaleMessageUtils.getFormatMessage("event_activity_fees_exceed_budget",configNum));
                    }
                    else {
                        return new ResponseBo<>(LocaleMessageUtils.getFormatMessage("event_activity_fees_exceed_budget",configNum) + "%");
                    }
                }
            }
        }
        return ResponseBo.ok();
    }

    @Override
    public void updateHuatongPartner(Long eventId) {
        if (GeneralTool.isEmpty(eventId)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_is_null"));
        }
        Event event = eventMapper.selectById(eventId);
        if (GeneralTool.isEmpty(event)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        Integer key = ProjectExtraEnum.PUBLIC_PARTNER.key;
        boolean b = containsThirteen(event.getPublicLevel(),key);
        String newPublicLevel;
        if (b){
            //移除string中的13
            newPublicLevel = removeKey(event.getPublicLevel(), key);
        } else {
            //添加13到string中
            newPublicLevel = addKey(event.getPublicLevel(), key);

        }

        Event eventUpdate = new Event();
        eventUpdate.setId(eventId);
        eventUpdate.setPublicLevel(newPublicLevel);
        int i = eventMapper.updateById(eventUpdate);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
    }

    public static boolean containsThirteen(String input,Integer key) {
        if (input == null || input.isEmpty()) {
            return false;
        }
        String[] parts = input.split(",");
        Set<String> partSet = new HashSet<>(Arrays.asList(parts));

        return partSet.contains(key.toString());
    }

    private String removeKey(String input, Integer key) {
        if (GeneralTool.isEmpty(input)) {
            return null;
        }

        List<String> parts = Arrays.stream(input.split(","))
                .filter(part -> !part.isEmpty())
                .collect(Collectors.toList());

        // 移除所有匹配的key
        parts.removeIf(part -> key.toString().equals(part));

        // 重新拼接（空列表返回空字符串）
        return String.join(",", parts);
    }

    private String addKey(String input, Integer key) {
        String keyStr = key.toString();

        // 原始字符串为空时直接返回key
        if (GeneralTool.isEmpty(input)) {
            return keyStr;
        }
        // 非空时追加",key"（已通过containsThirteen确保不重复）
        return input + "," + keyStr;
    }



}

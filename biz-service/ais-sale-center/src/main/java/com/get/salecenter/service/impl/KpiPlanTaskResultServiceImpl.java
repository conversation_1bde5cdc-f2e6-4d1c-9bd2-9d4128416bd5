package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.salecenter.dao.sale.KpiPlanTaskResultMapper;
import com.get.salecenter.entity.KpiPlanTaskResult;
import com.get.salecenter.service.KpiPlanTaskResultService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 服务实现类
 */
@Service
public class KpiPlanTaskResultServiceImpl extends ServiceImpl<KpiPlanTaskResultMapper, KpiPlanTaskResult> implements KpiPlanTaskResultService {

    @Resource
    private KpiPlanTaskResultMapper kpiPlanTaskResultMapper;

    @Resource
    private UtilService utilService;

}

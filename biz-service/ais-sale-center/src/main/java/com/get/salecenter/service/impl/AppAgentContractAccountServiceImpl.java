package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.service.impl.GetServiceImpl;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.AppAgentContractAccountMapper;
import com.get.salecenter.vo.AppAgentContractAccountVo;
import com.get.salecenter.vo.AppAgentContractAccountSaveContext;
import com.get.salecenter.vo.MediaAndAttachedVo;
import com.get.salecenter.entity.AppAgent;
import com.get.salecenter.entity.AppAgentContractAccount;
import com.get.salecenter.entity.SaleMediaAndAttached;
import com.get.salecenter.service.IAppAgentContractAccountService;
import com.get.salecenter.service.IAppAgentService;
import com.get.salecenter.service.IMediaAndAttachedService;
import com.get.salecenter.dto.*;
import com.google.common.collect.Lists;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: Hardy
 * @create: 2022/11/18 11:01
 * @verison: 1.0
 * @description:
 */
@Service
public class AppAgentContractAccountServiceImpl extends GetServiceImpl<AppAgentContractAccountMapper, AppAgentContractAccount> implements IAppAgentContractAccountService {


    @Resource
    private UtilService utilService;
    @Lazy
    @Resource
    private IAppAgentService appAgentService;
    @Resource
    private IMediaAndAttachedService mediaAndAttachedService;

    /**
     * 查询列表
     * @param appAgentContractAccountListDto
     * @param page
     * @return
     */
    @Override
    public List<AppAgentContractAccount> getAppAgentContractAccounts(AppAgentContractAccountListDto appAgentContractAccountListDto, Page page) {
        LambdaQueryWrapper<AppAgentContractAccount> wrapper = Wrappers.lambdaQuery();
        if (GeneralTool.isNotEmpty(appAgentContractAccountListDto.getFkAppAgentId())){
            wrapper.eq(AppAgentContractAccount::getFkAppAgentId, appAgentContractAccountListDto.getFkAppAgentId());
        }

        IPage<AppAgentContractAccount> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<AppAgentContractAccount> appAgentContractAccountList = pages.getRecords();
        page.setAll((int) pages.getTotal());
        return appAgentContractAccountList;
    }

    /**
     * 新增
     * @param appAgentContractAccountAddDto
     * @return
     */
    @Override
    public Long addAppAgentContractAccount(AppAgentContractAccountAddDto appAgentContractAccountAddDto) {
        //TODO 改过，新泻的
        AppAgentContractAccountDto contractAccountDto =BeanCopyUtils.objClone(appAgentContractAccountAddDto,AppAgentContractAccountDto::new);
        //获取appAgent
        //TODO 改过
        //AppAgentContractAccountSaveContext appAgentContractAccountSaveContext = doGetAppAgentById(appAgentContractAccountAddDto);
        AppAgentContractAccountSaveContext appAgentContractAccountSaveContext = doGetAppAgentById(contractAccountDto);

        //查找已存在的合同账户
        doGetExistedAppAgentContractAccounts(appAgentContractAccountSaveContext);

        //保存合同账户
        doSaveAppAgentContractAccount(appAgentContractAccountSaveContext);

        return appAgentContractAccountSaveContext.getFkAppAgentContractAccountId();
    }

    /**
     * 查找已存在的合同账户
     * @param appAgentContractAccountSaveContext
     */
    private void doGetExistedAppAgentContractAccounts(AppAgentContractAccountSaveContext appAgentContractAccountSaveContext) {
        if (GeneralTool.isEmpty(appAgentContractAccountSaveContext)){
            return;
        }
        AppAgent appAgent = appAgentContractAccountSaveContext.getAppAgent();
        if (GeneralTool.isEmpty(appAgent)){
            return;
        }
        AppAgentContractAccountDto agentContractAccountVo = appAgentContractAccountSaveContext.getAgentContractAccountVo();
        LambdaQueryWrapper<AppAgentContractAccount> wrapper = Wrappers.<AppAgentContractAccount>lambdaQuery();
        wrapper.eq(AppAgentContractAccount::getFkAppAgentId, appAgent.getId());
        //如果是更新 排除自身
        if (GeneralTool.isNotEmpty(agentContractAccountVo.getId())){
            wrapper.ne(AppAgentContractAccount::getId,agentContractAccountVo.getId());
        }
        List<AppAgentContractAccount> appAgentContractAccounts = list(wrapper);
        appAgentContractAccountSaveContext.setAppAgentContractAccounts(appAgentContractAccounts);
    }

    /**
     * 根据id查询appAgent
     * @param appAgentContractAccountDto
     * @return
     */
    private AppAgentContractAccountSaveContext doGetAppAgentById(AppAgentContractAccountDto appAgentContractAccountDto) {
        AppAgent appAgent = appAgentService.getById(appAgentContractAccountDto.getFkAppAgentId());
        if (GeneralTool.isEmpty(appAgent)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        AppAgentContractAccountSaveContext appAgentContractAccountSaveContext = new AppAgentContractAccountSaveContext();
        appAgentContractAccountSaveContext.setAppAgent(appAgent);
        appAgentContractAccountSaveContext.setAgentContractAccountVo(appAgentContractAccountDto);
        return appAgentContractAccountSaveContext;
    }

    /**
     * 保存合同账户
     * @param appAgentContractAccountSaveContext
     */
    private void doSaveAppAgentContractAccount(AppAgentContractAccountSaveContext appAgentContractAccountSaveContext) {
        if (GeneralTool.isEmpty(appAgentContractAccountSaveContext)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
        AppAgent appAgent = appAgentContractAccountSaveContext.getAppAgent();
        AppAgentContractAccountDto appAgentContractAccountDto = appAgentContractAccountSaveContext.getAgentContractAccountVo();
        List<AppAgentContractAccount> appAgentContractAccounts = appAgentContractAccountSaveContext.getAppAgentContractAccounts();
        AppAgentContractAccount appAgentContractAccount = BeanCopyUtils.objClone(appAgentContractAccountDto, AppAgentContractAccount::new);
        assert appAgentContractAccount != null;

        long count = appAgentContractAccounts.stream().filter(AppAgentContractAccount::getIsDefault).count();
        if (count >=1L && GeneralTool.isNotEmpty(appAgentContractAccountDto.getIsDefault()) && appAgentContractAccountDto.getIsDefault()){
            throw new GetServiceException(LocaleMessageUtils.getMessage("only_has_one_default"));
        }

        if (GeneralTool.isNotEmpty(appAgentContractAccounts)){
            //一个币种只能创建一个账户
            Map<String, List<AppAgentContractAccount>> currencyNumMap = appAgentContractAccounts.stream().collect(Collectors.groupingBy(AppAgentContractAccount::getFkCurrencyTypeNum));
            if (GeneralTool.isNotEmpty(currencyNumMap.get(appAgentContractAccount.getFkCurrencyTypeNum()))){
                throw new GetServiceException(LocaleMessageUtils.getMessage("only_has_one_account"));
            }
        }

        utilService.updateUserInfoToEntity(appAgentContractAccount);
        boolean b = saveOrUpdate(appAgentContractAccount);
        if (!b){
            if (GeneralTool.isEmpty(appAgentContractAccountDto.getId())){
                throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
            }else {
                throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
            }
        }
        List<MediaAndAttachedDto> mediaAndAttachedDtos = appAgentContractAccountDto.getMediaAndAttachedVos();
        List<MediaAndAttachedDto> saveMediaAndAttachedDtos = Lists.newArrayList();

        if (GeneralTool.isNotEmpty(mediaAndAttachedDtos)){
            //删除图片 先删后增
            mediaAndAttachedService.remove(Wrappers.<SaleMediaAndAttached>lambdaQuery()
                    .eq(SaleMediaAndAttached::getFkTableName, TableEnum.APP_AGENT_CONTRACT_ACCOUNT.key)
                    .eq(SaleMediaAndAttached::getFkTableId, appAgentContractAccount.getId())
                    .eq(SaleMediaAndAttached::getTypeKey, FileTypeEnum.APP_AGENT_CONTRACT_ACCOUNT_STATEMENT_FILE.key));

            for (MediaAndAttachedDto mediaAndAttachedDto : mediaAndAttachedDtos) {
                String tableName = TableEnum.APP_AGENT_CONTRACT_ACCOUNT.key;
                mediaAndAttachedDto.setFkTableName(tableName);
                mediaAndAttachedDto.setFkTableId(appAgentContractAccount.getId());
                mediaAndAttachedDto.setTypeKey(FileTypeEnum.APP_AGENT_CONTRACT_ACCOUNT_STATEMENT_FILE.key);
                saveMediaAndAttachedDtos.add(mediaAndAttachedDto);
            }
        }
//        if (!Objects.equals(appAgentContractAccountDto.getBankAccount(),appAgent.getPersonalName())){
//            //需上传附件
//            if (GeneralTool.isEmpty(mediaAndAttachedDtos)){
//                throw new GetServiceException(LocaleMessageUtils.getMessage("app_agent_contract_need_attached"));
//            }
//
//            //删除图片 先删后增
//            mediaAndAttachedService.remove(Wrappers.<SaleMediaAndAttached>lambdaQuery()
//                    .eq(SaleMediaAndAttached::getFkTableName, TableEnum.APP_AGENT_CONTRACT_ACCOUNT.key)
//                    .eq(SaleMediaAndAttached::getFkTableId, appAgentContractAccount.getId())
//                    .eq(SaleMediaAndAttached::getTypeKey, FileTypeEnum.APP_AGENT_CONTRACT_ACCOUNT_STATEMENT_FILE.key));
//
//            for (MediaAndAttachedDto mediaAndAttachedVo : mediaAndAttachedDtos) {
//                String tableName = TableEnum.APP_AGENT_CONTRACT_ACCOUNT.key;
//                mediaAndAttachedVo.setFkTableName(tableName);
//                mediaAndAttachedVo.setFkTableId(appAgentContractAccount.getId());
//                mediaAndAttachedVo.setTypeKey(FileTypeEnum.APP_AGENT_CONTRACT_ACCOUNT_STATEMENT_FILE.key);
//                saveMediaAndAttachedDtos.add(mediaAndAttachedVo);
//            }
//        }
        if (GeneralTool.isNotEmpty(saveMediaAndAttachedDtos)){
            Boolean batchSaveResult = mediaAndAttachedService.saveBatchMediaAndAttached(saveMediaAndAttachedDtos);
            if (!batchSaveResult){
                throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
            }
        }

        appAgentContractAccountSaveContext.setFkAppAgentContractAccountId(appAgentContractAccount.getId());
    }



    /**
     * 详情
     * @param id
     * @return
     */
    @Override
    public AppAgentContractAccountVo findAppAgentContractAccountById(Long id) {
        //获取appAgentContractAccountDto
        AppAgentContractAccountVo appAgentContractAccountVo = doGetAppAgentContractAccountDto(id);

        //设置属性值
        doSetAppAgentContractAccountDto(appAgentContractAccountVo);

        return appAgentContractAccountVo;
    }

    /**
     * 设置appAgentContractAccountDto属性值
     * @param appAgentContractAccountVo
     */
    private void doSetAppAgentContractAccountDto(AppAgentContractAccountVo appAgentContractAccountVo) {
        if (GeneralTool.isEmpty(appAgentContractAccountVo)){
            return;
        }
        MediaAndAttachedDto attachedVo = new MediaAndAttachedDto();
        attachedVo.setFkTableName(TableEnum.APP_AGENT_CONTRACT_ACCOUNT.key);
        attachedVo.setFkTableId(appAgentContractAccountVo.getId());
        List<MediaAndAttachedVo> mediaAndAttachedVos = mediaAndAttachedService.getMediaAndAttachedDto(attachedVo);
        appAgentContractAccountVo.setMediaAndAttachedDtos(mediaAndAttachedVos);
    }


    /**
     * 根据id获取AppAgentContractAccountDto
     * @param id
     * @return
     */
    private AppAgentContractAccountVo doGetAppAgentContractAccountDto(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        return this.baseMapper.findAppAccountInfoById(id);
    }

    /**
     * 编辑
     * @param appAgentContractAccountUpdateDto1
     * @return
     */
    @Override
    public AppAgentContractAccountVo updateAppAgentContractAccount(AppAgentContractAccountUpdateDto appAgentContractAccountUpdateDto1) {

        //TODO 改过，新泻的
        AppAgentContractAccountDto contractAccountDto =BeanCopyUtils.objClone(appAgentContractAccountUpdateDto1,AppAgentContractAccountDto::new);

        //获取appAgent
        //TODO 改过
        //AppAgentContractAccountSaveContext appAgentContractAccountSaveContext = doGetAppAgentById(appAgentContractAccountUpdateDto1);
        AppAgentContractAccountSaveContext appAgentContractAccountSaveContext = doGetAppAgentById(contractAccountDto);



        //查找已存在的合同账户
        doGetExistedAppAgentContractAccounts(appAgentContractAccountSaveContext);

        //保存合同账户
        doSaveAppAgentContractAccount(appAgentContractAccountSaveContext);

        return findAppAgentContractAccountById(appAgentContractAccountSaveContext.getFkAppAgentContractAccountId());
    }

    /**
     * 附件上传
     * @param mediaAttachedVos
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<MediaAndAttachedVo> addMedia(List<MediaAndAttachedDto> mediaAttachedVos) {
        if (GeneralTool.isEmpty(mediaAttachedVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        List<MediaAndAttachedVo> mediaAndAttachedVos = new ArrayList<>();
        for (MediaAndAttachedDto mediaAndAttachedDto : mediaAttachedVos) {
            if (GeneralTool.isEmpty(mediaAndAttachedDto.getFkTableId())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("tableId_null"));
            }
            //设置插入的表
            mediaAndAttachedDto.setFkTableName(TableEnum.APP_AGENT_CONTRACT_ACCOUNT.key);
            mediaAndAttachedVos.add(mediaAndAttachedService.addMediaAndAttached(mediaAndAttachedDto));
        }
        return mediaAndAttachedVos;
    }
}

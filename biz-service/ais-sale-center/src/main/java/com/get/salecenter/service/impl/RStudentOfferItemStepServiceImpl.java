package com.get.salecenter.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.eunms.MediaTypeEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.tool.utils.RequestContextUtil;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.institutioncenter.vo.InstitutionCourseMatchVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.remindercenter.dto.RemindTaskDto;
import com.get.remindercenter.feign.IReminderCenterClient;
import com.get.salecenter.dao.sale.*;
import com.get.salecenter.dto.DeferEntranceTimeUpdateDto;
import com.get.salecenter.dto.MediaAndAttachedDto;
import com.get.salecenter.dto.RStudentOfferItemStepDto;
import com.get.salecenter.dto.RStudentOfferItemStepUpRemarkDto;
import com.get.salecenter.entity.*;
import com.get.salecenter.service.*;
import com.get.salecenter.utils.sale.VerifyStudentOfferItemUtils;
import com.get.salecenter.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/11/18
 * @TIME: 16:58
 * @Description:
 **/
@Slf4j
@Service
public class RStudentOfferItemStepServiceImpl extends ServiceImpl<RStudentOfferItemStepMapper, RStudentOfferItemStep> implements IRStudentOfferItemStepService {
    @Resource
    private RStudentOfferItemStepMapper rOfferItemStepMapper;
    @Resource
    private StudentOfferItemStepMapper studentOfferItemStepMapper;
    @Resource
    private IStudentOfferItemStepService offerItemStepService;
    @Resource
    private UtilService utilService;
    @Resource
    private IReceivablePlanService receivablePlanService;
    @Resource
    private StudentOfferItemMapper studentOfferItemMapper;
    @Resource
    private IStudentOfferItemService studentOfferItemService;
    @Resource
    private StudentOfferMapper studentOfferMapper;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private IReminderCenterClient reminderCenterClient;
    @Resource
    @Lazy
    private IStudentOfferItemService offerItemService;
    @Resource
    private StudentOfferItemDeferEntranceTimeMapper studentOfferItemDeferEntranceTimeMapper;
    @Resource
    private IStudentProjectRoleStaffService projectRoleStaffService;
    @Resource
    private IStudentService studentService;
    @Resource
    private AsyncReminderService asyncReminderService;
    @Resource
    private StudentMapper studentMapper;
    @Resource
    private AgentMapper agentMapper;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    private VerifyStudentOfferItemUtils verifyStudentOfferItemUtils;
    @Resource
    private MediaAndAttachedMapper mediaAndAttachedMapper;


    @Override
    public List<RStudentOfferItemStepVo> getReStudentStepLog(RStudentOfferItemStepDto studentOfferItemStepVo, Page page) {
        if (GeneralTool.isEmpty(studentOfferItemStepVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        if (GeneralTool.isEmpty(studentOfferItemStepVo.getFkStudentOfferItemId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("offerItem_id_null"));
        }
//        Example example = new Example(RStudentOfferItemStep.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkStudentOfferItemId", studentOfferItemStepVo.getFkStudentOfferItemId());

        IPage<RStudentOfferItemStep> iPage = rOfferItemStepMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())),
                Wrappers.<RStudentOfferItemStep>lambdaQuery().eq(RStudentOfferItemStep::getFkStudentOfferItemId, studentOfferItemStepVo.getFkStudentOfferItemId()));
        List<RStudentOfferItemStep> offerItemSteps = iPage.getRecords();
        page.setAll((int) iPage.getTotal());

        List<RStudentOfferItemStepVo> collect = offerItemSteps.stream().map(rStudentOfferItemStep ->
                BeanCopyUtils.objClone(rStudentOfferItemStep, RStudentOfferItemStepVo::new)).collect(Collectors.toList());

        //附件文件
        MediaAndAttachedDto mediaAndAttachedDto = new MediaAndAttachedDto();
        mediaAndAttachedDto.setFkTableId(studentOfferItemStepVo.getFkStudentOfferItemId());
        mediaAndAttachedDto.setFkTableName(TableEnum.SALE_STUDENT_OFFER_ITEM.key);
        List<MediaAndAttachedVo> mediaAndAttachedVo = offerItemService.getItemMedia(mediaAndAttachedDto, new Page());
        Map<String, List<MediaAndAttachedVo>> mediaAndAttachedDtoMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(mediaAndAttachedVo)){
            mediaAndAttachedDtoMap = mediaAndAttachedVo.stream().collect(Collectors.groupingBy(MediaAndAttachedVo::getTypeKey));
        }

        for (RStudentOfferItemStepVo rItemStepDto : collect) {
            StudentOfferItemStepVo offerItemStep = offerItemStepService.findStudentOfferItemStepById(rItemStepDto.getFkStudentOfferItemStepId());
            if (GeneralTool.isNotEmpty(offerItemStep)) {
                rItemStepDto.setStepName(offerItemStep.getStepName());
                rItemStepDto.setStepOrder(offerItemStep.getStepOrder());
            }
            String keyByStep = MediaTypeEnum.getKeyByStepId(rItemStepDto.getFkStudentOfferItemStepId());
            rItemStepDto.setKeyByStep(keyByStep);
            if (GeneralTool.isNotEmpty(mediaAndAttachedDtoMap)){
                rItemStepDto.setMediaAndAttachedDto(mediaAndAttachedDtoMap.get(keyByStep));
            }
        }
        if (GeneralTool.isNotEmpty(collect)) {
            collect = collect.stream().sorted(Comparator.comparing(RStudentOfferItemStepVo::getGmtCreate, Comparator.nullsFirst(Date::compareTo))
                    .thenComparing(RStudentOfferItemStepVo::getId)).collect(Collectors.toList());
        }
        return collect;
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public synchronized Long addReStudentOfferItemStep(RStudentOfferItemStepDto studentOfferItemStepVo) {
        if (GeneralTool.isEmpty(studentOfferItemStepVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));

        }
        RStudentOfferItemStep rStudentOfferItemStep = BeanCopyUtils.objClone(studentOfferItemStepVo, RStudentOfferItemStep::new);
        //        utilService.setCreateInfo(rStudentOfferItemStep);
//        StudentOfferItemStepVo step = offerItemStepService.findStudentOfferItemStepById(studentOfferItemStepVo.getFkStudentOfferItemStepId());
//
//        //获取要设置步骤
//        Integer stepOrder = step.getStepOrder();
//        //当前步骤
//        Integer nowStepOrder = rOfferItemStepMapper.getMaxStepOrder(studentOfferItemStepVo.getFkStudentOfferItemId());
//        if (CheckUtils.isNotEmpty(nowStepOrder)) {
//            if (stepOrder >= nowStepOrder) {
//                throw new YServiceException(LocaleMessageUtils.getMessage("step_no_reverse"));
//            }
//        }
//        StudentOfferItem studentOfferItem = studentOfferItemMapper.selectByPrimaryKey(studentOfferItemStepVo.getFkStudentOfferItemId());
        utilService.updateUserInfoToEntity(rStudentOfferItemStep);
        rOfferItemStepMapper.insertSelective(rStudentOfferItemStep);

//        StudentOfferItemStep studentOfferItemStep = studentOfferItemStepMapper.selectByPrimaryKey(rStudentOfferItemStep.getFkStudentOfferItemStepId());
//        if (ProjectKeyEnum.STEP_OFFER_SELECTION.key.equals(studentOfferItemStep.getStepKey())) {
//            receivablePlanService.generateReceivablePlan(studentOfferItem, true);
//        }
        //步骤为已录取，设置支付押金截止时间、接受Offer截止时间
        if (studentOfferItemStepVo.getFkStudentOfferItemStepId().equals(4L)) {
            StudentOfferItem studentOfferItem = new StudentOfferItem();
            studentOfferItem.setId(studentOfferItemStepVo.getId());
            studentOfferItem.setDepositDeadline(studentOfferItemStepVo.getDepositDeadline());
            studentOfferItem.setAcceptOfferDeadline(studentOfferItemStepVo.getAcceptOfferDeadline());
            offerItemService.setOfferItemDate(studentOfferItem);
        }

        return rStudentOfferItemStep.getId();
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public RStudentOfferItemStepSaveVo saveReStudentOfferItemStep(RStudentOfferItemStepDto studentOfferItemStepVo) {
        if (GeneralTool.isEmpty(studentOfferItemStepVo)
                ||GeneralTool.isEmpty(studentOfferItemStepVo.getFkStudentOfferItemId())
                || GeneralTool.isEmpty(studentOfferItemStepVo.getFkStudentOfferItemStepId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        RStudentOfferItemStepSaveVo rStudentOfferItemStepSaveVo = new RStudentOfferItemStepSaveVo();

        //获取当前学习计划
        StudentOfferItem studentOfferItem = studentOfferItemMapper.selectById(studentOfferItemStepVo.getFkStudentOfferItemId());

        if (GeneralTool.isNotEmpty(studentOfferItem.getFkParentStudentOfferItemId()) && GeneralTool.isNotEmpty(studentOfferItem.getIsStepFollow()) && studentOfferItem.getIsStepFollow()
            && studentOfferItemStepVo.getFkStudentOfferItemStepId()!=15L) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("seting_enrol_Failure_unsuccessful"));
        }


        //获取禁止回退步骤配置
        Long fkCompanyId = SecureUtil.getFkCompanyId();
        Result<Map<Long, String>> config = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.STEP_FALLBACK_OPT_LIMIT.key, 1);
//        Result<ConfigVo> config = permissionCenterClient.getConfigByKey(ProjectKeyEnum.STEP_FALLBACK_OPT_LIMIT.key);
        List<String> stepKeyList;
        if (config.isSuccess() && config.getData() != null) {
            String configValue1 = config.getData().get(fkCompanyId);
            stepKeyList = new ArrayList<>(JSON.parseArray(configValue1, String.class));
//            if (GeneralTool.isNotEmpty(value1)) {
//                JSONObject jsonObject = JSONObject.parseObject(value1);
//                if (fkCompanyId == 3) {
//                    stepKeyList = jsonObject.getJSONArray("IAE").toJavaList(String.class);
//                } else {
//                    stepKeyList = jsonObject.getJSONArray("OTHER").toJavaList(String.class);
//                }
//            }
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessageFormat("lack_of_business_configuration","STEP_FALLBACK_OPT_LIMIT"));
        }


        //判断当前学习计划绑定的“有效”的应收应付计划 有无实收实付
        Boolean hasPlan = studentOfferItemMapper.hasActualPlanAmount(studentOfferItemStepVo.getFkStudentOfferItemId());
        //判断新步骤是否为回退操作
        Boolean isBackStep = studentOfferItemMapper.isBackStep(studentOfferItemStepVo.getFkStudentOfferItemId(), studentOfferItemStepVo.getFkStudentOfferItemStepId());

        if(GeneralTool.isNotEmpty(stepKeyList)){
            //查询当前学习计划步骤信息
            StudentOfferItemStep studentOfferItemStep = studentOfferItemStepMapper.selectById(studentOfferItem.getFkStudentOfferItemStepId());
            String stepKey = studentOfferItemStep.getStepKey();

            //特殊步骤禁止回退操作
            if(isBackStep && stepKeyList.contains(stepKey)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_fallback"));
            }
        }

        //若学习计划以及子计划绑定了“有效”应收应付计划，则不能进行步骤回退操作,入学登记完成（Enrolled）可回退
        if (hasPlan && isBackStep) {
            StudentOfferItemStep step = studentOfferItemStepMapper.selectOne(Wrappers.<StudentOfferItemStep>lambdaQuery().eq(StudentOfferItemStep::getStepKey, ProjectKeyEnum.STEP_ENROLLED));
            if (!studentOfferItemStepVo.getFkStudentOfferItemStepId().equals(step.getId())
                    && !studentOfferItemStepVo.getIsFailurePage() && !studentOfferItemStepVo.getIsApplyRefundPage()) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("is_not_back"));
            }
        }

        //新增记录日志
        RStudentOfferItemStep rStudentOfferItemStep = BeanCopyUtils.objClone(studentOfferItemStepVo, RStudentOfferItemStep::new);
        utilService.updateUserInfoToEntity(rStudentOfferItemStep);
        rOfferItemStepMapper.insertSelective(rStudentOfferItemStep);

        //查询将要保存的步骤信息
        StudentOfferItemStep studentOfferItemStep = studentOfferItemStepMapper.selectById(studentOfferItemStepVo.getFkStudentOfferItemStepId());
        if (Objects.isNull(studentOfferItemStep)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("step_does_not_exist"));
        }
        String stepKey = studentOfferItemStep.getStepKey();
        //步骤跟随父级的子计划
        List<StudentOfferItem> studentOfferItems = new ArrayList<>();
        List<StudentOfferItem> studentOfferItemSonList = studentOfferItemMapper.selectList(Wrappers.<StudentOfferItem>lambdaQuery()
                .eq(StudentOfferItem::getFkParentStudentOfferItemId, studentOfferItem.getId())
                .eq(StudentOfferItem::getIsStepFollow, true));
        if (GeneralTool.isNotEmpty(studentOfferItemSonList)){
            studentOfferItems.addAll(studentOfferItemSonList);
        }
        //false无需判断课程
        boolean flag = false;
        //提交完成，院校已经收件，已通知缴费，入学失败
        if (ProjectKeyEnum.STEP_SUBMITTED.key.equals(stepKey) || ProjectKeyEnum.STEP_APP_RECEIVED.key.equals(stepKey)
                || ProjectKeyEnum.STEP_FAILURE.key.equals(stepKey) || ProjectKeyEnum.STEP_NOTIFIED.key.equals(stepKey)) {
            flag = true;
        }



        String studentName = "";
        String agentName = "";
        String institutionName = "";
        String courseName = "";
        Long companyId = 0L;
        Boolean isStepApply=true;
        if ("STEP_APPLICATION_EXTENSION".equals(studentOfferItemStep.getStepKey()) || studentOfferItemStepVo.getFkStudentOfferItemStepId()==4
                || studentOfferItemStepVo.getFkStudentOfferItemStepId()==6 || studentOfferItemStepVo.getFkStudentOfferItemStepId()==15 ){
            if (GeneralTool.isNotEmpty(studentOfferItem.getFkStudentId())){
                Student student = studentMapper.selectById(studentOfferItem.getFkStudentId());
                companyId = student.getFkCompanyId();
                studentName = student.getName();
                if (GeneralTool.isNotEmpty(student.getFirstName())&&GeneralTool.isNotEmpty(student.getLastName())){
                    studentName = studentName +"（"+student.getLastName()+" "+student.getFirstName()+"）";
                }
            }
            if (GeneralTool.isNotEmpty(studentOfferItem.getFkAgentId())){
                Agent agent = agentMapper.selectById(studentOfferItem.getFkAgentId());
                agentName = agent.getName();
            }

            if (GeneralTool.isNotEmpty(studentOfferItem.getFkInstitutionId())) {
                institutionName = institutionCenterClient.getInstitutionName(studentOfferItem.getFkInstitutionId()).getData();
            }else {
                institutionName = studentOfferItem.getOldInstitutionName();
            }


            if (GeneralTool.isNotEmpty(studentOfferItem.getFkInstitutionCourseId()) && studentOfferItem.getFkInstitutionCourseId() != -1 ) {
                courseName = institutionCenterClient.getCourseNameById(studentOfferItem.getFkInstitutionCourseId()).getData();
            }else {
                courseName = studentOfferItem.getOldCourseCustomName();
            }
            //已延期
            if (ProjectKeyEnum.STEP_APPLICATION_EXTENSION.key.equals(studentOfferItemStep.getStepKey())){
                studentOfferItem.setDepositDeadline(studentOfferItemStepVo.getDepositDeadline());
                studentOfferItem.setAcceptOfferDeadline(studentOfferItemStepVo.getAcceptOfferDeadline());
                if (GeneralTool.isNotEmpty(studentOfferItemStepVo.getDepositDeadline()) || GeneralTool.isNotEmpty(studentOfferItemStepVo.getAcceptOfferDeadline())){
                    //发送截止时间邮件
                       //studentOfferItemService.sendEmailOfferItemAdd(studentOfferItem);
                }
            }
        }
        if (!ProjectKeyEnum.STEP_NEW_APP.key.equals(studentOfferItemStep.getStepKey())){
            studentOfferItem.setNewAppStatus(null);
            if (GeneralTool.isNotEmpty(studentOfferItemStepVo.getFkInstitutionCourseId())){
                studentOfferItem.setFkInstitutionCourseId(studentOfferItemStepVo.getFkInstitutionCourseId());
            }
            if (GeneralTool.isNotEmpty(studentOfferItemStepVo.getOldCourseCustomName())){
                studentOfferItem.setOldCourseCustomName(studentOfferItemStepVo.getOldCourseCustomName());
            }
        }
        if (ProjectKeyEnum.STEP_NEW_APP.key.equals(studentOfferItemStep.getStepKey())){
            //如新申请状态不同，记录操作时间
            if ((GeneralTool.isEmpty(studentOfferItem.getNewAppStatus()) && GeneralTool.isNotEmpty(studentOfferItemStepVo.getNewAppStatus()))
                    || (GeneralTool.isNotEmpty(studentOfferItemStepVo.getNewAppStatus()) && GeneralTool.isNotEmpty(studentOfferItem.getNewAppStatus()) &&
                    !studentOfferItemStepVo.getNewAppStatus().equals(studentOfferItem.getNewAppStatus()))
            ) {
                studentOfferItem.setNewAppOptTime(new Date());
            }
            studentOfferItem.setNewAppStatus(studentOfferItemStepVo.getNewAppStatus());
        } else if (ProjectKeyEnum.STEP_SUBMITTED.key.equals(studentOfferItemStep.getStepKey())) {
            studentOfferItem.setId(studentOfferItemStepVo.getFkStudentOfferItemId());
            studentOfferItem.setFkInstitutionCourseId(studentOfferItemStepVo.getFkInstitutionCourseId());
            if (GeneralTool.isNotEmpty(studentOfferItemStepVo.getFkInstitutionCourseId()) && studentOfferItemStepVo.getFkInstitutionCourseId() != -1) {
                studentOfferItem.setFkInstitutionCourseCustomId(studentOfferItemStepVo.getFkInstitutionCourseId());
            }
            studentOfferItem.setAppMethod(studentOfferItemStepVo.getAppMethod());
            studentOfferItem.setAppRemark(studentOfferItemStepVo.getAppRemark());
            studentOfferItem.setLearningMode(studentOfferItemStepVo.getLearningMode());
            studentOfferItem.setDepositDeadline(studentOfferItemStepVo.getDepositDeadline());
            studentOfferItem.setAcceptOfferDeadline(studentOfferItemStepVo.getAcceptOfferDeadline());
            studentOfferItem.setFkStudentOfferItemStepId(studentOfferItemStepVo.getFkStudentOfferItemStepId());
            if (studentOfferItemStepVo.getAppMethod() != null && studentOfferItemStepVo.getAppMethod() == 0){
                if (GeneralTool.isNotEmpty(studentOfferItemStepVo.getSubmitAppTime())){
                    studentOfferItem.setSubmitAppTime(studentOfferItemStepVo.getSubmitAppTime());
                }else {
                    studentOfferItem.setSubmitAppTime(new Date());
                }
            }
        }else if (ProjectKeyEnum.STEP_ADMITTED.key.equals(studentOfferItemStep.getStepKey())) {
            studentOfferItem.setId(studentOfferItemStepVo.getFkStudentOfferItemId());
            studentOfferItem.setDepositDeadline(studentOfferItemStepVo.getDepositDeadline());
            studentOfferItem.setAcceptOfferDeadline(studentOfferItemStepVo.getAcceptOfferDeadline());
            if (GeneralTool.isNotEmpty(studentOfferItemStepVo.getDepositDeadline()) || GeneralTool.isNotEmpty(studentOfferItemStepVo.getAcceptOfferDeadline())){
                //发送截止时间邮件
                studentOfferItemService.sendEmailOfferItemAdd(studentOfferItem);
            }
        }else if (ProjectKeyEnum.STEP_APP_RECEIVED.key.equals(studentOfferItemStep.getStepKey())) {
            studentOfferItem.setFkInstitutionCourseId(studentOfferItemStepVo.getFkInstitutionCourseId());
            if (GeneralTool.isNotEmpty(studentOfferItemStepVo.getFkInstitutionCourseId()) && studentOfferItemStepVo.getFkInstitutionCourseId() != -1) {
                studentOfferItem.setFkInstitutionCourseCustomId(studentOfferItemStepVo.getFkInstitutionCourseId());
            }
        }else if (ProjectKeyEnum.STEP_NOTIFIED.key.equals(studentOfferItemStep.getStepKey())) {
            studentOfferItem.setFkInstitutionCourseId(studentOfferItemStepVo.getFkInstitutionCourseId());
            if (GeneralTool.isNotEmpty(studentOfferItemStepVo.getFkInstitutionCourseId()) && studentOfferItemStepVo.getFkInstitutionCourseId() != -1) {
                studentOfferItem.setFkInstitutionCourseCustomId(studentOfferItemStepVo.getFkInstitutionCourseId());
            }
        }else if (ProjectKeyEnum.STEP_DEPOSIT_PAID.key.equals(studentOfferItemStep.getStepKey())) {
            studentOfferItem.setId(studentOfferItemStepVo.getFkStudentOfferItemId());
            //若无值以操作步骤时间为依据
            if(GeneralTool.isNotEmpty(studentOfferItemStepVo.getDepositTime())){
                studentOfferItem.setDepositTime(studentOfferItemStepVo.getDepositTime());
            }else{
                studentOfferItem.setDepositTime(rStudentOfferItemStep.getGmtCreate());
            }
            Result<Boolean> result = reminderCenterClient.batchUpdate(new ArrayList<>(), TableEnum.SALE_STUDENT_OFFER_ITEM.key, studentOfferItemStepVo.getFkStudentOfferItemId());
            if (!result.isSuccess()) {
                throw new GetServiceException(result.getMessage());
            }
        }else if (ProjectKeyEnum.STEP_OFFER_SELECTION.key.equals(studentOfferItemStep.getStepKey())) {
            studentOfferItem.setId(studentOfferItemStepVo.getFkStudentOfferItemId());
            studentOfferItem.setStudentId(studentOfferItemStepVo.getStudentId());
            studentOfferItem.setOpeningTime(studentOfferItemStepVo.getOpeningTime());
//            if (studentOfferItem.getOpeningTime().compareTo(studentOfferItem.getDeferOpeningTime()) > 0) {
//                studentOfferItem.setDeferOpeningTime(studentOfferItem.getOpeningTime());//处理
//            }
            Date newerOpeningTime = offerItemService.compareAndReturnNewerOpeningTime(studentOfferItem,studentOfferItem.getOpeningTime(),true);
            studentOfferItem.setDeferOpeningTime(newerOpeningTime);

            studentOfferItem.setClosingTime(studentOfferItemStepVo.getClosingTime());
            studentOfferItem.setDurationType(studentOfferItemStepVo.getDurationType());
            studentOfferItem.setDuration(studentOfferItemStepVo.getDuration());
            studentOfferItem.setIsMain(studentOfferItemStepVo.getIsMain());
            studentOfferItem.setInsuranceBuyMethod(studentOfferItemStepVo.getInsuranceBuyMethod());
            studentOfferItem.setIsCreditExemption(studentOfferItemStepVo.getIsCreditExemption());
            studentOfferItem.setFkStudentOfferItemStepId(studentOfferItemStepVo.getFkStudentOfferItemStepId());
            Result<Boolean> result = reminderCenterClient.batchUpdate(new ArrayList<>(), TableEnum.SALE_STUDENT_OFFER_ITEM.key, studentOfferItemStepVo.getFkStudentOfferItemId());
            if (!result.isSuccess()) {
                throw new GetServiceException(LocaleMessageUtils.getMessage(result.getMessage()));
            }
        }
        if (ProjectKeyEnum.STEP_FAILURE.key.equals(studentOfferItemStep.getStepKey())) {
//            //能否设置入学失败判断
            offerItemService.validateInvalid(studentOfferItemStepVo.getFkStudentOfferItemId(),ProjectKeyEnum.APPLICATION_STEPS_ENROLL_FAILURE.key, false);
            studentOfferItem.setId(studentOfferItemStepVo.getFkStudentOfferItemId());
            studentOfferItem.setFkEnrolFailureReasonId(studentOfferItemStepVo.getFkEnrolFailureReasonId());
            studentOfferItem.setOtherFailureReason(studentOfferItemStepVo.getOtherFailureReason());
            studentOfferItem.setLearningMode(studentOfferItemStepVo.getLearningMode());
            studentOfferItem.setFkStudentOfferItemStepId(studentOfferItemStepVo.getFkStudentOfferItemStepId());
            studentOfferItem.setFkInstitutionCourseId(studentOfferItemStepVo.getFkInstitutionCourseId());

            //删除未执行的提醒任务
            List<String> fkRemindEventTypeKeys = new ArrayList<>();
            fkRemindEventTypeKeys.add("STUDENT_ACCEP_OFFER");
            fkRemindEventTypeKeys.add("STUDENT_PAY_DEPOSIT");
            fkRemindEventTypeKeys.add("AGENT_STUDENT_OFFER_ACCEP_DDL_NOTICE");
            fkRemindEventTypeKeys.add("AGENT_STUDENT_OFFER_DEPOSIT_DDL_NOTICE");
            fkRemindEventTypeKeys.add("STUDENT_ACCEP_OFFER_ENG");
            fkRemindEventTypeKeys.add("STUDENT_PAY_DEPOSIT_ENG");
            fkRemindEventTypeKeys.add("AGENT_STUDENT_OFFER_ACCEP_DDL_NOTICE_ENG");
            fkRemindEventTypeKeys.add("AGENT_STUDENT_OFFER_DEPOSIT_DDL_NOTICE_ENG");
            reminderCenterClient.batchDeleteByTableId(ProjectKeyEnum.SALE_STUDENT_OFFER_ITEM.key,studentOfferItem.getId(),fkRemindEventTypeKeys);
        }else if (ProjectKeyEnum.STEP_ENROLLED.key.equals(studentOfferItemStep.getStepKey())) {
            studentOfferItem.setId(studentOfferItemStepVo.getFkStudentOfferItemId());
            studentOfferItem.setLearningMode(studentOfferItemStepVo.getLearningMode());
            studentOfferItem.setFkStudentOfferItemStepId(studentOfferItemStepVo.getFkStudentOfferItemStepId());

            //子课同步主课逻辑
            if (studentOfferItemStepVo.getSynchmainItem()){
                MainCourseConditionVo ukMainCourse = isUkMainCourse(studentOfferItem.getId());
                if (ukMainCourse.getIsEligible()){
                    synchMainItemStep(studentOfferItem);
                }
            }
        }
        //申请延期 步骤中
//        else if (ProjectKeyEnum.STEP_APPLICATION_EXTENSION.key.equals(studentOfferItemStep.getStepKey()))
//        {
//            LambdaQueryWrapper<StudentOfferItemDeferEntranceTime> wrapper = new LambdaQueryWrapper<>();
//            wrapper.eq(StudentOfferItemDeferEntranceTime::getFkStudentOfferItemId,studentOfferItemStepVo.getFkStudentOfferItemId());
//            List<StudentOfferItemDeferEntranceTime> studentOfferItemDeferEntranceTimes = studentOfferItemDeferEntranceTimeMapper.selectList(wrapper);
//            if (GeneralTool.isEmpty(studentOfferItemDeferEntranceTimes)){
//                throw new GetServiceException(LocaleMessageUtils.getMessage("no_delayed_enrollment_time"));
//            }
//            //设置入学时间
//            //upataStepDeferEntranceTime(studentOfferItemStepVo);
//            studentOfferItem.setIsDeferEntrance(studentOfferItemStepVo.getIsDeferEntrance());
//            try {
//                if (studentOfferItemStepVo.getIsDeferEntrance()) {
//                    log.info("-------------延迟入学添加提醒任务------------------------");
//                    //学生 XXX 代理 YYYY 学校 ZZZ  课程AAA 开学时间从【X年X月X日】变更为【X年X月X日】。
//                    Date openingTime = studentOfferItem.getOpeningTime();
//                    Calendar calendar = Calendar.getInstance();
//                    calendar.setTime(openingTime);
//                    int openingYear = calendar.get(Calendar.YEAR);
//                    int openingMonth = calendar.get(Calendar.MONTH) + 1;
//                    int openingDate = calendar.get(Calendar.DATE);
//
//                    calendar.setTime(studentOfferItem.getDeferOpeningTime());
//                    Integer deferEntranceYear = calendar.get(Calendar.YEAR);
//                    Integer deferEntranceMonth = calendar.get(Calendar.MONTH) + 1;
//                    Integer deferEntranceDate = calendar.get(Calendar.DATE);
//
////                    StringBuilder sb = new StringBuilder();
////                    sb.append("学生 ").append(studentName);
////                    sb.append(";代理 ").append(agentName);
////                    sb.append(";学校 ").append(institutionName);
////                    sb.append(";课程 ").append(courseName);
////                    sb.append(";开学时间从" + "【").append(openingYear).append("年").append(openingMonth).append("月").append(openingDate).append("日】变更为");
////                    sb.append("【").append(deferEntranceYear).append("年").append(deferEntranceMonth).append("月").append(deferEntranceDate).append("日】");
//
//                    //加提醒任务
//                    Map<String, String> map = new HashMap<>();
//                    map.put("studentName",studentName);
//                    map.put("agentName",agentName);
//                    map.put("institutionName",institutionName);
//                    map.put("courseName",courseName);
//                    map.put("diffDateTime","从【"+openingYear+"年"+openingMonth+"月"+openingDate+"日】变更为【"+deferEntranceYear+"年"+deferEntranceMonth+"月"+deferEntranceDate+"日】");
//                    String remindText = com.get.salecenter.utils.MyStringUtils.getReminderTemplate(map, SaleCenterConstant.DEFERENTRANCE_REMINDER);
//
//                    List<RemindTaskDto> remindTaskDtos = new ArrayList<>();
//                    //发送提醒通知
//                    RemindTaskDto remindTaskVo = new RemindTaskDto();
//                    remindTaskVo.setTaskTitle("延迟入学通知");
//                    remindTaskVo.setTaskRemark(remindText);
//                    //邮件方式发送
//                    remindTaskVo.setRemindMethod("1");
//                    //默认设置执行中
//                    remindTaskVo.setStatus(1);
//                    //默认背景颜色
//                    remindTaskVo.setFkRemindEventTypeKey(ProjectKeyEnum.DEFER_ENTRANCE_EMAIL_REMIND_KEY.key);
//                    remindTaskVo.setTaskBgColor("#3788d8");
//                    remindTaskVo.setFkStaffId(1L);
//                    remindTaskVo.setStartTime(new Date());
//                    //当天发送
//                    remindTaskVo.setAdvanceDays("0");
//                    remindTaskVo.setFkTableName(TableEnum.SALE_STUDENT_OFFER_ITEM.key);
//                    remindTaskVo.setFkTableId(studentOfferItem.getId());
//                    remindTaskVo.setFkDbName(ProjectKeyEnum.REMINDER_CENTER.key);
//                    remindTaskDtos.add(remindTaskVo);
//                    reminderCenterClient.batchAdd(remindTaskDtos);
//                }
//            }catch (Exception e){
//                log.error("添加更新通知失败：",e);
//            }
//
//
//        }
        else if("STEP_TUITION_PAID".equals(studentOfferItemStep.getStepKey())){
            studentOfferItem.setTuitionTime(studentOfferItemStepVo.getTuitionTime());
        } else if (ProjectKeyEnum.STEP_POSTPONED.key.equals(studentOfferItemStep.getStepKey())) {
            //设置入学时间
//            upataStepDeferEntranceTime(studentOfferItemStepVo);
            LambdaQueryWrapper<StudentOfferItemDeferEntranceTime> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(StudentOfferItemDeferEntranceTime::getFkStudentOfferItemId,studentOfferItemStepVo.getFkStudentOfferItemId());
            List<StudentOfferItemDeferEntranceTime> studentOfferItemDeferEntranceTimes = studentOfferItemDeferEntranceTimeMapper.selectList(wrapper);
            if (GeneralTool.isEmpty(studentOfferItemDeferEntranceTimes)){
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_delayed_enrollment_time"));
            }
        }else if (ProjectKeyEnum.STEP_APPLY_REFUND.key.equals(studentOfferItemStep.getStepKey())){
            //1. 只有应收应付时，应收应付作废。
            //2. 有应收绑定发票时，提示不能操作（解绑后才能操作）。提示：对应创建的应收计划已经绑定发票，请解绑后再操作。
            //3. 有实收时，设置应收应付金额=0（分主课、子计划、后续课程的实收判断）
            //4. 发送邮件通知财务/佣金部
            //能否设置入学失败判断
            StepApplyRefundVo stepApplyRefundVo = offerItemService.validateInvalidApplyRefund(studentOfferItem, studentOfferItemStepVo.getIsApplyRefundPage());
            isStepApply = stepApplyRefundVo.getIsStepApply();
            rStudentOfferItemStepSaveVo.setReminderInformation(stepApplyRefundVo.getReminderInformation());
            rStudentOfferItemStepSaveVo.setUpateItemIds(stepApplyRefundVo.getUpateItemIds());
        }
        studentOfferItem.setStudentOfferItemStepTime(new Date());
        studentOfferItem.setFkStudentOfferItemStepId(studentOfferItemStepVo.getFkStudentOfferItemStepId());
        //学校
        if(GeneralTool.isNotEmpty(studentOfferItemStepVo.getFkInstitutionId())){
            studentOfferItem.setFkInstitutionId(studentOfferItemStepVo.getFkInstitutionId());
        }
        //提供商
        if(GeneralTool.isNotEmpty(studentOfferItemStepVo.getFkInstitutionProviderId())){
            studentOfferItem.setFkInstitutionProviderId(studentOfferItemStepVo.getFkInstitutionProviderId());
        }
        //渠道
        if(GeneralTool.isNotEmpty(studentOfferItemStepVo.getFkInstitutionChannelId())){
            studentOfferItem.setFkInstitutionChannelId(studentOfferItemStepVo.getFkInstitutionChannelId());
        }
        //支付方式
        if(GeneralTool.isNotEmpty(studentOfferItemStepVo.getDepositPaymentMethod())){
            studentOfferItem.setDepositPaymentMethod(studentOfferItemStepVo.getDepositPaymentMethod());
        }
        if(GeneralTool.isNotEmpty(studentOfferItemStepVo.getTuitionPaymentMethod())){
            studentOfferItem.setTuitionPaymentMethod(studentOfferItemStepVo.getTuitionPaymentMethod());
        }
        //学习计划备注
        if (GeneralTool.isNotEmpty(studentOfferItemStepVo.getAppRemark()) &&
                (ProjectKeyEnum.STEP_SUBMITTED.key.equals(studentOfferItemStep.getStepKey())
                        || ProjectKeyEnum.STEP_NOTIFIED.key.equals(studentOfferItemStep.getStepKey())
                        || ProjectKeyEnum.STEP_APP_RECEIVED.key.equals(studentOfferItemStep.getStepKey())
                        || ProjectKeyEnum.STEP_OFFER_SELECTION.key.equals(studentOfferItemStep.getStepKey())
                        || ProjectKeyEnum.STEP_ENROLLED.key.equals(studentOfferItemStep.getStepKey()))){
            if (GeneralTool.isNotEmpty(studentOfferItemStepVo.getAppRemark())){
                studentOfferItem.setAppRemark(studentOfferItemStepVo.getAppRemark());
            }
        }
        utilService.updateUserInfoToEntity(studentOfferItem);
        verifyStudentOfferItemUtils.verifyOfferItemUpdate(studentOfferItem);
        studentOfferItemMapper.updateById(studentOfferItem);


        //记录步骤新增提醒
        List<RemindTaskDto> remindTaskDtos = new ArrayList<>();
        //获取所有项目成员
        List<StudentProjectRoleStaffVo> projectRoleStaffs = projectRoleStaffService.getAllProjectRoleStaff(studentOfferItem.getFkStudentOfferId());
        if (GeneralTool.isNotEmpty(projectRoleStaffs)) {
            if ((studentOfferItemStepVo.getFkStudentOfferItemStepId()==4 || studentOfferItemStepVo.getFkStudentOfferItemStepId()==6
                    || (studentOfferItemStepVo.getFkStudentOfferItemStepId()==15 && isStepApply) )) {
                String typeKey;
                if (studentOfferItemStepVo.getFkStudentOfferItemStepId()==4) {
                    typeKey = ProjectKeyEnum.REMINDER_EMAIL_STEP_ADMITTED.key;
                }else if (studentOfferItemStepVo.getFkStudentOfferItemStepId()==15){
                    typeKey = ProjectKeyEnum.REMINDER_EMAIL_STEP_APPLY_REFUND.key;
                } else {
                    typeKey = ProjectKeyEnum.REMINDER_EMAIL_STEP_OFFER_SELECTION.key;
                }
                //判断是否需要发送邮件 1发送
                Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(typeKey, 1).getData();
                String configValue1 = companyConfigMap.get(fkCompanyId);
                Map<Long, String> companyConfigMap2 = permissionCenterClient.getCompanyConfigMap(typeKey, 2).getData();
                String configValue2 = companyConfigMap2.get(fkCompanyId);
                JSONArray jsonArray2 = JSON.parseArray(configValue2);


//                Result<ConfigVo> configByKey = permissionCenterClient.getConfigByKey(typeKey);
//                ConfigVo data = configByKey.getData();
//                JSONObject jsonObject = JSONObject.parseObject(data.getValue1());
//                String key = companyId == 3 ? "IAE" : "OTHER";
//                String gea = jsonObject.getString(key);
                String otherEmail = null;
                if (Integer.parseInt(configValue1) == 1) {
                    if (studentOfferItemStepVo.getFkStudentOfferItemStepId() == 6) {
//                        JSONObject object = JSONObject.parseObject(data.getValue2());
                        List<String> list = jsonArray2.toJavaList(String.class);
//                        if (companyId == 3) {
//                            list = JSONObject.parseObject(object.getString("IAE"), new TypeReference<List<String>>() {
//                            });
//                        }else {
//                            list = JSONObject.parseObject(object.getString("GEA"), new TypeReference<List<String>>() {
//                            });
//                        }
                        String c1= "commission";
                        String c2 = "commission.au";
                        String c3 = "commission.uk";
                        Long fkAreaCountryId = studentOfferItem.getFkAreaCountryId();
                        if (fkAreaCountryId == 6 || fkAreaCountryId == 8) {
                            otherEmail = c2;
                        }else if (fkAreaCountryId == 7){
                            otherEmail = c3;
                        }else {
                            otherEmail = c1;
                        }
                        if (!list.contains(otherEmail)) {
                            otherEmail = null;
                        }
                    }else if (studentOfferItemStepVo.getFkStudentOfferItemStepId() == 15 && !studentOfferItemStepVo.getIsApplyRefundPage()){
                        //通知财务部/佣金部
//                        JSONObject object = JSONObject.parseObject(data.getValue2());
                        List<String> list;
                        if (companyId == 3) {
                            list = jsonArray2.toJavaList(String.class);
                            String c1= "commission";
                            String c2 = "commission.au";
                            String c3 = "commission.uk";
                            Long fkAreaCountryId = studentOfferItem.getFkAreaCountryId();
                            if (fkAreaCountryId == 6 || fkAreaCountryId == 8) {
                                otherEmail = c2;
                            }else if (fkAreaCountryId == 7){
                                otherEmail = c3;
                            }else {
                                otherEmail = c1;
                            }
                            if (!list.contains(otherEmail)) {
                                otherEmail = null;
                            }
                        }else {
                            otherEmail = "specialist";
                        }

                    }
                    Map<String, String> headerMap = RequestContextUtil.getHeaderMap();
                    asyncReminderService.sendOfferItemStepUpdateEmail(studentOfferItemStepVo, projectRoleStaffs,
                            studentName, agentName, institutionName, courseName, studentOfferItem, otherEmail,headerMap,SecureUtil.getStaffId());

                    //【申请退押金】子计划/后续课程需要发邮箱
                    if (studentOfferItemStepVo.getFkStudentOfferItemStepId() == 15 && GeneralTool.isNotEmpty(studentOfferItems)){
                        String studentItemName = "";
                        String agentItemName = "";
                        String institutionItemName = "";
                        String courseItemName = "";
                        Set<Long> offerItemsIds = studentOfferItems.stream().map(StudentOfferItem::getId).collect(Collectors.toSet());
                        //子计划后续课程同样需要发邮件
                        LambdaQueryWrapper<StudentOfferItem> studentOfferItemLambdaQueryWrapper = new LambdaQueryWrapper<>();
                        studentOfferItemLambdaQueryWrapper.in(StudentOfferItem::getFkParentStudentOfferItemId, offerItemsIds).eq(StudentOfferItem::getIsStepFollow, true);
                        if (GeneralTool.isNotEmpty(rStudentOfferItemStepSaveVo.getUpateItemIds())){
                            studentOfferItemLambdaQueryWrapper.notIn(StudentOfferItem::getFkParentStudentOfferItemId, rStudentOfferItemStepSaveVo.getUpateItemIds());
                        }
                        List<StudentOfferItem> fllowItems =studentOfferItemMapper.selectList(studentOfferItemLambdaQueryWrapper);
                        if(GeneralTool.isNotEmpty(fllowItems)){
                            studentOfferItems.addAll(fllowItems);
                        }
                        for (StudentOfferItem offerItem : studentOfferItems) {
                            //解绑不需要发送邮件
                            if (GeneralTool.isNotEmpty(rStudentOfferItemStepSaveVo.getUpateItemIds())
                                    && rStudentOfferItemStepSaveVo.getUpateItemIds().contains(offerItem.getId())){
                                continue;
                            }
                            //获取所有项目成员
                            List<StudentProjectRoleStaffVo> projectRoleStaffItems = projectRoleStaffService.getAllProjectRoleStaff(offerItem.getFkStudentOfferId());

                            if (GeneralTool.isNotEmpty(offerItem.getFkStudentId())){
                                Student studentItem = studentMapper.selectById(offerItem.getFkStudentId());
                                companyId = studentItem.getFkCompanyId();
                                studentItemName = studentItem.getName();
                                if (GeneralTool.isNotEmpty(studentItem.getFirstName())&&GeneralTool.isNotEmpty(studentItem.getLastName())){
                                    studentItemName = studentItemName +"（"+studentItem.getLastName()+" "+studentItem.getFirstName()+"）";
                                }
                            }
                            if (GeneralTool.isNotEmpty(offerItem.getFkAgentId())){
                                Agent agentItem = agentMapper.selectById(offerItem.getFkAgentId());
                                agentItemName = agentItem.getName();
                            }

                            if (GeneralTool.isNotEmpty(offerItem.getFkInstitutionId())) {
                                institutionItemName = institutionCenterClient.getInstitutionName(offerItem.getFkInstitutionId()).getData();
                            }else {
                                institutionItemName = offerItem.getOldInstitutionName();
                            }


                            if (GeneralTool.isNotEmpty(offerItem.getFkInstitutionCourseId())&& offerItem.getFkInstitutionCourseId() != -1) {
                                courseItemName = institutionCenterClient.getCourseNameById(offerItem.getFkInstitutionCourseId()).getData();
                            }else {
                                courseItemName = offerItem.getOldCourseCustomName();
                            }

                            asyncReminderService.sendOfferItemStepUpdateEmail(studentOfferItemStepVo, projectRoleStaffItems,
                                    studentItemName, agentItemName, institutionItemName, courseItemName, offerItem, otherEmail,headerMap,SecureUtil.getStaffId());
                        }


                    }
                    //子计划取消跟随
                    if (GeneralTool.isNotEmpty(rStudentOfferItemStepSaveVo.getUpateItemIds())){
                        StudentOfferItem studentOfferItemSon = new StudentOfferItem();
                        studentOfferItemSon.setIsStepFollow(false);
                        utilService.setUpdateInfo(studentOfferItemSon);
                        LambdaQueryWrapper<StudentOfferItem> studentOfferItemLambdaQueryWrapper = new LambdaQueryWrapper<>();
                        studentOfferItemLambdaQueryWrapper.in(StudentOfferItem::getId, rStudentOfferItemStepSaveVo.getUpateItemIds());
                        studentOfferItemMapper.update(studentOfferItemSon,studentOfferItemLambdaQueryWrapper);
                    }
                }

            }
            //TODO 改过
           // List<Long> staffIds = projectRoleStaffs.stream().map(StudentProjectRoleStaff::getFkStaffId).collect(Collectors.toList());
            List<Long> staffIds = projectRoleStaffs.stream().map(StudentProjectRoleStaffVo::getFkStaffId).collect(Collectors.toList());
            Boolean isFollowHidden = studentOfferItem.getIsFollowHidden();
            Long staffId = SecureUtil.getStaffId();
            //排除记录人
            staffIds = staffIds.stream().filter(s -> !staffId.equals(s)).collect(Collectors.toList());
            //提醒标题
            StringBuilder stringBuffer = new StringBuilder();
            //学生名称
            String fkStudentName = studentService.getStudentNameById(studentOfferItem.getFkStudentId());
            stringBuffer.append("学生").append(fkStudentName).append("申请计划进入").append(studentOfferItemStep.getStepName()).append("步骤");
            //TODO 注释SAVE_NEW_STUDENT_OFFER_ITEM_STEP  lucky  2025/03/11
//            for (Long fkStaffId : staffIds) {
//                RemindTaskDto remindTaskDto = new RemindTaskDto();
//                remindTaskDto.setFkStaffId(fkStaffId);
//                remindTaskDto.setFkRemindEventTypeKey(ProjectKeyEnum.SAVE_NEW_STUDENT_OFFER_ITEM_STEP.key);
//                remindTaskDto.setStartTime(new Date());
//                remindTaskDto.setTaskBgColor("#3788d8");
//                remindTaskDto.setRemindMethod("3");
//                remindTaskDto.setStatus(1);
//                if (ProjectKeyEnum.STEP_APP_RECEIVED.key.equals(studentOfferItemStep.getStepKey())){
//                    //提前2天提醒，系统提醒+邮件提醒
//                    remindTaskDto.setAdvanceDays("-2");
//                    remindTaskDto.setRemindMethod("3,1");
//                }
//                remindTaskDto.setTaskTitle(stringBuffer.toString());
//                remindTaskDto.setFkTableName(TableEnum.SALE_STUDENT_OFFER_ITEM_STEP.key);
//                //去掉提醒红点
////                remindTaskDto.setFkDbName(ProjectKeyEnum.SALE_CENTER.key);
//                remindTaskDto.setFkTableId(rStudentOfferItemStep.getId());
//                remindTaskDtos.add(remindTaskDto);
//            }
//            if (!studentOfferItem.getIsFollowHidden()) {
//                reminderCenterClient.batchAdd(remindTaskDtos);
//            }
        }
        for (StudentOfferItem offerItem : studentOfferItems) {
            if (GeneralTool.isNotEmpty(rStudentOfferItemStepSaveVo.getUpateItemIds())
                    && rStudentOfferItemStepSaveVo.getUpateItemIds().contains(offerItem.getId())){
                continue;
            }
            //判断子课程是否为空
            if (flag) {
                Long fkInstitutionCourseId = offerItem.getFkInstitutionCourseId();
                if (Objects.isNull(fkInstitutionCourseId) || -1 == fkInstitutionCourseId) {
                    if (StringUtils.isBlank(offerItem.getOldCourseCustomName())) {
                        throw new GetServiceException(LocaleMessageUtils.getMessage("sub_application_not_selected_course"));
                    }
                }
            }
            //收到签证函更改子课程学生编号
            if(ProjectKeyEnum.STEP_OFFER_SELECTION.key.equals(studentOfferItemStep.getStepKey()) && GeneralTool.isNotEmpty(studentOfferItemStepVo.getSonStudentItemList())){
                List<RStudentOfferItemStepDto.SonStudentItem> sonStudentItemList = studentOfferItemStepVo.getSonStudentItemList();
                sonStudentItemList.stream().forEach(e->{
                    if (e.getItemId().equals(offerItem.getId())){
                        offerItem.setStudentId(e.getStudentId());
                    }
                });
            }
            offerItem.setStudentOfferItemStepTime(new Date());
            offerItem.setFkStudentOfferItemStepId(studentOfferItemStepVo.getFkStudentOfferItemStepId());
            if (GeneralTool.isNotEmpty(offerItem.getFkInstitutionCourseId())){
                setInstitutionCourseCustom(offerItem);
            }
            utilService.updateUserInfoToEntity(offerItem);
            verifyStudentOfferItemUtils.verifyOfferItemUpdate(offerItem);
            studentOfferItemMapper.updateById(offerItem);

            //子计划日志记录
            RStudentOfferItemStep rstep = new RStudentOfferItemStep();
            rstep.setFkStudentOfferItemStepId(studentOfferItemStepVo.getFkStudentOfferItemStepId());
            rstep.setFkStudentOfferItemId(offerItem.getId());
            utilService.updateUserInfoToEntity(rstep);
            rOfferItemStepMapper.insert(rstep);
            //后续课程
            List<StudentOfferItem> fllowItems = studentOfferItemMapper.selectList(Wrappers.<StudentOfferItem>lambdaQuery()
                    .eq(StudentOfferItem::getFkParentStudentOfferItemId, offerItem.getId()).eq(StudentOfferItem::getIsStepFollow, true));
            if(GeneralTool.isNotEmpty(fllowItems)){
                for(StudentOfferItem fllowItem : fllowItems){
                    fllowItem.setStudentOfferItemStepTime(new Date());
                    fllowItem.setFkStudentOfferItemStepId(studentOfferItemStepVo.getFkStudentOfferItemStepId());
                    utilService.updateUserInfoToEntity(fllowItem);
                    studentOfferItemMapper.updateById(fllowItem);
                    //后续课程日志记录
                    RStudentOfferItemStep rst = new RStudentOfferItemStep();
                    rst.setFkStudentOfferItemStepId(studentOfferItemStepVo.getFkStudentOfferItemStepId());
                    rst.setFkStudentOfferItemId(fllowItem.getId());
                    utilService.updateUserInfoToEntity(rst);
                    rOfferItemStepMapper.insertSelective(rst);
                }
            }
        }

        rStudentOfferItemStepSaveVo.setItemId(rStudentOfferItemStep.getId());
        return rStudentOfferItemStepSaveVo;
    }

    /**
     * 同步子课步骤到主课
     * 1.同步附件以及步骤记录（4，6，8）
     * 2.同步步骤 && studentId
     * @param studentOfferItem
     */
    private void synchMainItemStep(StudentOfferItem studentOfferItem) {
        StudentOfferItem studentOfferItemParent = this.studentOfferItemMapper.selectById(studentOfferItem.getFkParentStudentOfferItemId());

        //复制子计划附件
        List<SaleMediaAndAttached> saleMediaAndAttacheds = mediaAndAttachedMapper.selectList(new LambdaQueryWrapper<SaleMediaAndAttached>()
                .eq(SaleMediaAndAttached::getFkTableName, TableEnum.SALE_STUDENT_OFFER_ITEM.key)
                .in(SaleMediaAndAttached::getFkTableId, studentOfferItem.getId()).
                in(SaleMediaAndAttached::getTypeKey, MediaTypeEnum.enums2ArraysList(MediaTypeEnum.MEDIA_MAIN_TYPE)));

        for (SaleMediaAndAttached saleMediaAndAttached : saleMediaAndAttacheds) {
            saleMediaAndAttached.setFkTableId(studentOfferItemParent.getId());
            utilService.setCreateInfo(saleMediaAndAttached);
            mediaAndAttachedMapper.insert(saleMediaAndAttached);
        }
        //增加步骤日志
        StudentOfferItemStep studentOfferItemStepmParent = this.studentOfferItemStepMapper.selectById(studentOfferItemParent.getFkStudentOfferItemStepId());
        List<StudentOfferItemStep> studentOfferItemSteps = this.studentOfferItemStepMapper.selectList(new LambdaQueryWrapper<StudentOfferItemStep>()
                .in(StudentOfferItemStep::getStepKey, ProjectKeyEnum.getValuesByKeys(ProjectKeyEnum.SYN_MAIN_TYPE))
                .gt(StudentOfferItemStep::getStepOrder, studentOfferItemStepmParent.getStepOrder()));
        for (StudentOfferItemStep studentOfferItemStep : studentOfferItemSteps) {
            RStudentOfferItemStep rStudentOfferItemStep = new RStudentOfferItemStep();
            rStudentOfferItemStep.setFkStudentOfferItemStepId(studentOfferItemStep.getId());
            rStudentOfferItemStep.setFkStudentOfferItemId(studentOfferItemParent.getId());
            utilService.setCreateInfo(rStudentOfferItemStep);
            rOfferItemStepMapper.insert(rStudentOfferItemStep);
        }
        //修改主课记录
        studentOfferItemParent.setStudentOfferItemStepTime(new Date());
        studentOfferItemParent.setFkStudentOfferItemStepId(studentOfferItem.getFkStudentOfferItemStepId());
        if (GeneralTool.isEmpty(studentOfferItemParent.getStudentId()) && GeneralTool.isNotEmpty(studentOfferItem.getStudentId())) {
            studentOfferItemParent.setStudentId(studentOfferItem.getStudentId());
        }

        studentOfferItemMapper.updateById(studentOfferItemParent);
    }

    @Override
    public RStudentOfferItemStepVo updateReStudentOfferItemStep(RStudentOfferItemStepDto studentOfferItemStepVo) {
        if (GeneralTool.isEmpty(studentOfferItemStepVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        RStudentOfferItemStep rStudentOfferItemStep = BeanCopyUtils.objClone(studentOfferItemStepVo, RStudentOfferItemStep::new);
        utilService.updateUserInfoToEntity(rStudentOfferItemStep);
        rOfferItemStepMapper.updateById(rStudentOfferItemStep);
        RStudentOfferItemStep rStep = rOfferItemStepMapper.selectById(rStudentOfferItemStep.getId());
        return BeanCopyUtils.objClone(rStep, RStudentOfferItemStepVo::new);
    }

    @Override
    public RStudentOfferItemStepVo updateApplyStepLogRemark(RStudentOfferItemStepUpRemarkDto rStudentOfferItemStepUpRemarkDto) {
        if (GeneralTool.isEmpty(rStudentOfferItemStepUpRemarkDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        RStudentOfferItemStep rStudentOfferItemStep = BeanCopyUtils.objClone(rStudentOfferItemStepUpRemarkDto, RStudentOfferItemStep::new);
        utilService.updateUserInfoToEntity(rStudentOfferItemStep);
        rOfferItemStepMapper.updateById(rStudentOfferItemStep);
        RStudentOfferItemStep newRStudentOfferItemStep = rOfferItemStepMapper.selectById(rStudentOfferItemStep.getId());
        return BeanCopyUtils.objClone(newRStudentOfferItemStep, RStudentOfferItemStepVo::new);
    }

    @Override
    public List<Long> selectItemIdByStepIdAndGmtCreate(RStudentOfferItemStepDto RStudentOfferItemStepDto) {
        return rOfferItemStepMapper.selectItemIdByStepIdAndGmtCreate(RStudentOfferItemStepDto);
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        rOfferItemStepMapper.deleteById(id);
    }

    @Override
    public Boolean insertRSteps(List<RStudentOfferItemStep> childRStudentOfferItemSteps){
        log.info("子计划插入步骤......");
       return saveBatch(childRStudentOfferItemSteps);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchOneClickSubmission(Set<Long> itemIds) {
        if (GeneralTool.isEmpty(itemIds)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }

        List<StudentOfferItem> studentOfferItems = studentOfferItemMapper.selectBatchIds(itemIds);
        if (GeneralTool.isEmpty(studentOfferItems)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        checkBatchOneClickSubmission(studentOfferItems);
        for (StudentOfferItem offerItem : studentOfferItems) {
            RStudentOfferItemStepDto rStudentOfferItemStepDto = new RStudentOfferItemStepDto();
            rStudentOfferItemStepDto.setFkStudentOfferItemId(offerItem.getId());
            rStudentOfferItemStepDto.setFkStudentOfferItemStepId(2L);
            rStudentOfferItemStepDto.setAppMethod(0);  //默认网申
            saveReStudentOfferItemStep(rStudentOfferItemStepDto);
        }
    }

    @Override
    public Map<Long, Date> failureIds(Set<Long> itemIds) {
        if (GeneralTool.isEmpty(itemIds)){
            return null;
        }
        List<RStudentOfferItemStep> rStudentOfferItemSteps = rOfferItemStepMapper.failureIds(itemIds);
        Map<Long, Date> map = rStudentOfferItemSteps.stream().collect(HashMap::new, (m, v) -> m.put(v.getFkStudentOfferItemId(), v.getGmtCreate()), HashMap::putAll);
        return map;
    }

    /**
     * 获取最新备注
     * @param ids
     * @return
     */
    @Override
    public Map<Long, String> getLastRemarkByIds(Set<Long> ids) {
        if (GeneralTool.isEmpty(ids)) {
            return Collections.emptyMap();
        }
        List<SelItem> selItems = rOfferItemStepMapper.getLastRemarkByIds(ids);
        return selItems.stream().collect(HashMap<Long, String>::new, (k, v) -> k.put(v.getKeyId(), String.valueOf(v.getVal())), HashMap::putAll);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteStepLog(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        RStudentOfferItemStep rStudentOfferItemStep = rOfferItemStepMapper.selectById(id);
        if (GeneralTool.isEmpty(rStudentOfferItemStep)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }

        //当前步骤日志删除要回退步骤
        StudentOfferItem studentOfferItem = studentOfferItemMapper.selectById(rStudentOfferItemStep.getFkStudentOfferItemId());
        List<RStudentOfferItemStep> rStudentOfferItemSteps = rOfferItemStepMapper.selectList(new LambdaQueryWrapper<RStudentOfferItemStep>().eq(RStudentOfferItemStep::getFkStudentOfferItemId, studentOfferItem.getId())
                .orderByDesc(RStudentOfferItemStep::getGmtCreate).last("limit 2"));
        if (GeneralTool.isNotEmpty(rStudentOfferItemSteps) && rStudentOfferItemSteps.get(0).getId().equals(id)
                && GeneralTool.isNotEmpty(rStudentOfferItemSteps.get(1))
                && !rStudentOfferItemSteps.get(0).getGmtCreate().equals(rStudentOfferItemSteps.get(1).getGmtCreate())){
                studentOfferItem.setStudentOfferItemStepTime(new Date());
                studentOfferItem.setFkStudentOfferItemStepId(rStudentOfferItemSteps.get(1).getFkStudentOfferItemStepId());
                studentOfferItemMapper.updateById(studentOfferItem);
        }{
            List<RStudentOfferItemStep> itemSteps = rOfferItemStepMapper.selectList(new LambdaQueryWrapper<RStudentOfferItemStep>().eq(RStudentOfferItemStep::getFkStudentOfferItemId, studentOfferItem.getId())
                    .ne(RStudentOfferItemStep::getFkStudentOfferItemStepId, rStudentOfferItemStep.getFkStudentOfferItemStepId())
                    .orderByDesc(RStudentOfferItemStep::getFkStudentOfferItemStepId).last("limit 1"));
            if (GeneralTool.isNotEmpty(itemSteps)){
                studentOfferItem.setStudentOfferItemStepTime(new Date());
                studentOfferItem.setFkStudentOfferItemStepId(itemSteps.get(0).getFkStudentOfferItemStepId());
                studentOfferItemMapper.updateById(studentOfferItem);
            }
        }

        //后续当前步骤日志删除要回退步骤
//        List<StudentOfferItem> studentOfferItems = studentOfferItemMapper.selectList(Wrappers.<StudentOfferItem>query().lambda().eq(StudentOfferItem::getFkParentStudentOfferItemId, rStudentOfferItemStep.getFkStudentOfferItemId())
//                .eq(StudentOfferItem::getIsStepFollow, true));
//        if (GeneralTool.isNotEmpty(studentOfferItems)){
//            List<Long> itemIds = studentOfferItems.stream().map(StudentOfferItem::getId).collect(Collectors.toList());
//            itemIds.stream().forEach(i -> {
//                        //当前步骤日志删除要回退步骤
//                        StudentOfferItem studentOfferItemFollow = studentOfferItemMapper.selectById(i);
//                        List<RStudentOfferItemStep> rStudentOfferItemStepsFollow = rOfferItemStepMapper.selectList(new LambdaQueryWrapper<RStudentOfferItemStep>().eq(RStudentOfferItemStep::getFkStudentOfferItemId, i)
//                                .orderByDesc(RStudentOfferItemStep::getGmtCreate).last("limit 2"));
//                        if (GeneralTool.isNotEmpty(rStudentOfferItemStepsFollow) && rStudentOfferItemStepsFollow.get(0).getId().equals(id)){
//                            if (GeneralTool.isNotEmpty(rStudentOfferItemStepsFollow.get(1))){
//                                studentOfferItemFollow.setFkStudentOfferItemStepId(rStudentOfferItemStepsFollow.get(1).getFkStudentOfferItemStepId());
//                                studentOfferItemMapper.updateById(studentOfferItemFollow);
//                            }
//                        }
//                    }
//                    );
//            rOfferItemStepMapper.delete(Wrappers.<RStudentOfferItemStep>query().lambda().in(RStudentOfferItemStep::getFkStudentOfferItemId,itemIds)
//                    .eq(RStudentOfferItemStep::getFkStudentOfferItemStepId,rStudentOfferItemStep.getFkStudentOfferItemStepId()));
//        }
        //删除步骤日志，主要应用在后台点错步骤，需要将步骤删除，不能影响业绩统计时间点
        rOfferItemStepMapper.deleteById(id);
    }

    @Override
    public MainCourseConditionVo isUkMainCourse(Long id) {
        MainCourseConditionVo mainCourseConditionVo = new MainCourseConditionVo();
        Boolean isEligible = false;
        StudentOfferItem studentOfferItem = studentOfferItemMapper.selectById(id);
        if (GeneralTool.isEmpty(studentOfferItem.getFkParentStudentOfferItemId()) || studentOfferItem.getIsFollow()){
            mainCourseConditionVo.setIsEligible(isEligible);
            return mainCourseConditionVo;
        }

        StudentOfferItem studentOfferItemParent = studentOfferItemMapper.selectById(studentOfferItem.getFkParentStudentOfferItemId());
        //HTI暂不适用
//        Map<Long, Integer> companySettlementConfigInfoMap = permissionCenterClient.getCompanySettlementConfigInfoMap(ProjectKeyEnum.SYNC_STEP_UK_SUB_TO_MAIN.key);
//
//        if (companySettlementConfigInfoMap.get(SecureUtil.getFkCompanyId()) == 1
//            && (studentOfferItemParent.getFkStudentOfferItemStepId()!= 1L &&  studentOfferItemParent.getFkStudentOfferItemStepId()!= 9L
//            && studentOfferItemParent.getFkStudentOfferItemStepId()!= 8L)
//        ){
//            isEligible = true;
//            mainCourseConditionVo.setMainStepName(studentOfferItemStepMapper.getItemStepSelect()
//                    .stream().filter(e -> e.getId().equals(studentOfferItemParent.getFkStudentOfferItemStepId())).collect(Collectors.toList()).get(0).getName());
//        }
        Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.SYNC_STEP_UK_SUB_TO_MAIN.key, 1).getData();
        String configValue1 = companyConfigMap.get(SecureUtil.getFkCompanyId());
        if (configValue1.equals("1") && (studentOfferItemParent.getFkStudentOfferItemStepId()!= 1L &&  studentOfferItemParent.getFkStudentOfferItemStepId()!= 9L
                && studentOfferItemParent.getFkStudentOfferItemStepId()!= 8L)
        ){
            isEligible = true;
            mainCourseConditionVo.setMainStepName(studentOfferItemStepMapper.getItemStepSelect()
                    .stream().filter(e -> e.getId().equals(studentOfferItemParent.getFkStudentOfferItemStepId())).collect(Collectors.toList()).get(0).getName());
        }

        mainCourseConditionVo.setIsEligible(isEligible);
        return mainCourseConditionVo;
    }

    private void checkBatchOneClickSubmission(List<StudentOfferItem> offerItems){
        StringBuilder prompt = new StringBuilder();
        for (StudentOfferItem offerItem : offerItems) {
            //仅新申请可提交
            if (offerItem.getFkStudentOfferItemStepId() != 1){
                prompt.append(LocaleMessageUtils.getMessage("this")+offerItem.getNum()+LocaleMessageUtils.getMessage("is_not_new_app_step")+"<br>");
            }
            if (GeneralTool.isEmpty(offerItem.getFkInstitutionCourseId()) || (GeneralTool.isEmpty(offerItem.getOldCourseCustomName()) && offerItem.getFkInstitutionCourseId().equals(-1L))){
                prompt.append(LocaleMessageUtils.getMessage("this")+offerItem.getNum()+LocaleMessageUtils.getMessage("study_plan_not_exist")+"<br>");
            }
            if (GeneralTool.isEmpty(offerItem.getFkInstitutionProviderId()) || offerItem.getFkInstitutionProviderId() == -1){
                prompt.append(LocaleMessageUtils.getMessage("this")+offerItem.getNum()+LocaleMessageUtils.getMessage("study_plan_provider_not_exist")+"<br>");
            }
        }
        if (GeneralTool.isNotEmpty(prompt)){
            throw new GetServiceException(prompt.toString());
        }
    }

    public void setInstitutionCourseCustom(StudentOfferItem studentOfferItem) {
        try{
            if (studentOfferItem.getFkInstitutionCourseId() != -1){
                studentOfferItem.setFkInstitutionCourseCustomId(studentOfferItem.getFkInstitutionCourseId());
            }else{
                if (GeneralTool.isNotEmpty(studentOfferItem.getOldCourseCustomName())) {
                    List<InstitutionCourseMatchVo> institutionCourseByNameMatch =
                            institutionCenterClient.getInstitutionCourseByNameMatch(studentOfferItem.getOldCourseCustomName(),
                                    studentOfferItem.getFkInstitutionId()).getData();
                    if (GeneralTool.isNotEmpty(institutionCourseByNameMatch)) {
                        studentOfferItem.setFkInstitutionCourseCustomId(institutionCourseByNameMatch.get(0).getCourseId());
                    } else {
                        studentOfferItem.setFkInstitutionCourseCustomId(null);
                    }
                }
            }
        }catch(Exception e){
            //
        }
    }

    /**
     * 设置延迟入学时间
     * 1.需要同时设置入学标记和入学时间
     * 2.判断是否已经新增。未新增，则需要设置延迟入学时间
     * @param studentOfferItemStepVo
     */
    private void upataStepDeferEntranceTime(RStudentOfferItemStepDto studentOfferItemStepVo){
        if (GeneralTool.isNotEmpty(studentOfferItemStepVo.getIsDeferEntrance()) && studentOfferItemStepVo.getIsDeferEntrance()){
            //判断是否已经存在该时间
            if (GeneralTool.isEmpty(studentOfferItemStepVo.getDeferEntranceTime())){
                throw new GetServiceException(LocaleMessageUtils.getMessage("application_plan_delay_date_not_set"));
            }

            //判断是否已经新增，如已新增，则跳过一下步骤
            LambdaQueryWrapper<StudentOfferItemDeferEntranceTime> deferEntranceWrapper = new LambdaQueryWrapper<>();
            deferEntranceWrapper.eq(StudentOfferItemDeferEntranceTime::getFkStudentOfferItemId,studentOfferItemStepVo.getFkStudentOfferItemId());
            deferEntranceWrapper.eq(StudentOfferItemDeferEntranceTime::getDeferEntranceTime,studentOfferItemStepVo.getDeferEntranceTime());
            List<StudentOfferItemDeferEntranceTime> studentOfferItemDeferEntranceTimes = studentOfferItemDeferEntranceTimeMapper.selectList(deferEntranceWrapper);
            //新增延迟入学时间
            if (GeneralTool.isEmpty(studentOfferItemDeferEntranceTimes)){
                DeferEntranceTimeUpdateDto deferEntranceTimeUpdateDto = new DeferEntranceTimeUpdateDto();
                deferEntranceTimeUpdateDto.setIsDeferEntrance(true);
                deferEntranceTimeUpdateDto.setFkStudentOfferItemId(studentOfferItemStepVo.getFkStudentOfferItemId());
                deferEntranceTimeUpdateDto.setDeferEntranceTime(studentOfferItemStepVo.getDeferEntranceTime());
                studentOfferItemService.updateDeferEntranceTime(deferEntranceTimeUpdateDto);
            }
        }
    }

}

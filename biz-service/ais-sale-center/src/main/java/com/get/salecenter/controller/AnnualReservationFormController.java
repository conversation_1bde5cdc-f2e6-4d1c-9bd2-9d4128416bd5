package com.get.salecenter.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.redis.lock.RedisLock;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.entity.ConventionPerson;
import com.get.salecenter.vo.*;
import com.get.salecenter.vo.AnnualReservationFormVo;
import com.get.salecenter.service.IAnnualReservationFormService;
import com.get.salecenter.utils.MyStringUtils;
import com.get.salecenter.utils.WxUtils;
import com.get.salecenter.dto.*;
import com.get.salecenter.dto.AnnualReservationBoothDto;
import com.github.binarywang.wxpay.bean.notify.SignatureHeader;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2021/7/7 15:18
 * @verison: 1.0
 * @description:IAE峰会预订表单
 */
@Api(tags = "IAE峰会预订表单")
@RestController
@RequestMapping("sale/annualReservationForm")
@Slf4j
public class AnnualReservationFormController {

    @Resource
    private IAnnualReservationFormService annualReservationFormService;

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :新增信息
     * @Param [annualConferenceRegistrationVo]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "新增接口", notes = "")
    @PostMapping("add")
    public ResponseBo add(HttpServletRequest request) {
        String data = request.getParameter("data");
        JSONObject jsonData = JSONObject.parseObject(data);
        JSONArray voList = jsonData.getJSONArray("annualReservationFormDtos");
        List<AnnualReservationFormDto> annualReservationFormDtos = voList.toJavaList(AnnualReservationFormDto.class);
        String receiptCode = jsonData.getString("receiptCode");
        annualReservationFormService.addAnnualPersons(receiptCode, annualReservationFormDtos);
        return ResponseBo.ok();
    }


    /**
     * 机构名下拉框
     *
     * @param receiptCode
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "机构名下拉框", notes = "")
    @GetMapping("getInstitutionNameSelect/{receiptCode}")
    public ResponseBo getInstitutionNameSelect(@PathVariable("receiptCode") String receiptCode, @RequestParam(value = "fkConventionId", required = true) Long fkConventionId) {
        List<ConventionRegistrationVo> datas = annualReservationFormService.getInstitutionNameSelect(receiptCode, fkConventionId);
        return new ListResponseBo<>(datas);
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.AnnualHotelReservationVo>
     * @Description :详情
     * @Param [annualConferenceRegistrationVo]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "配置详情接口", notes = "")
    @GetMapping("getAnnualConferenceRegistrationDto")
    public ResponseBo<IaeAnnualReservationFormVo> getAnnualConferenceRegistrationDto(@RequestParam String receiptCode) {
        IaeAnnualReservationFormVo data = annualReservationFormService.getIaeAnnualReservationFormDto(receiptCode);
        return new ResponseBo<>(data);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.AnnualHotelReservationVo>
     * @Description :删除参会人接口
     * @Param [id]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "删除参会人接口", notes = "")
    @PostMapping("deletePerson/{id}")
    public ResponseBo deletePerson(@PathVariable("id") Long id, @RequestParam("receiptCode") String receiptCode) {
        annualReservationFormService.deleteConventionPerson(id, receiptCode);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.AnnualHotelReservationVo>
     * @Description :删除快递信息接口
     * @Param [id]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "删除快递信息接口", notes = "")
    @PostMapping("deleteExpressInfo")
    public ResponseBo deleteExpressInfo(HttpServletRequest request) {
        String requestPayload = MyStringUtils.getRequestPayload(request);
        JSONObject jsonObject = JSONObject.parseObject(requestPayload);
        JSONArray expressInfoVos = jsonObject.getJSONArray("expressInfoVos");
        Long id = jsonObject.getLong("id");
        Integer provideGifts = jsonObject.getInteger("provideGifts");
        List<ExpressInfoDto> expressInfoDtoList = expressInfoVos.toJavaList(ExpressInfoDto.class);
        annualReservationFormService.deleteExpressInfo(id, expressInfoDtoList, provideGifts);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.ConventionProcedureVo>
     * @Description :获取流程
     * @Param [receiptCode]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "查找流程", notes = "")
    @GetMapping("getConventionProcedures")
    public ResponseBo<ConventionProcedureVo> getConventionProcedures(@RequestParam("receiptCode") String receiptCode) {
        List<ConventionProcedureVo> conventionProcedureVos = annualReservationFormService.getConventionProcedures(receiptCode);
        return new ListResponseBo<>(conventionProcedureVos);
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.ConventionHotelVo>
     * @Description :获取流程
     * @Param [receiptCode]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "房间型号下拉框", notes = "")
    @GetMapping("getRoomTypeSelect")
    public ResponseBo<ConventionHotelVo> getRoomTypeSelect(@RequestParam("receiptCode") String receiptCode) {
        List<ConventionHotelVo> conventionHotelVos = annualReservationFormService.getRoomTypeSelect(receiptCode);
        return new ListResponseBo<>(conventionHotelVos);
    }


    /**
     * @return ResponseBo
     * @Description :验证手机号
     * @Param [conventionId, phone]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "验证手机号", notes = "")
    @GetMapping("validatedTel")
    public ResponseBo validatedTel(@RequestParam("conventionId") Long conventionId, @RequestParam("phone") String phone, @RequestParam(value = "personId", required = false) Long personId) {
        return new ResponseBo<>(annualReservationFormService.validatedTel(conventionId, phone, personId));
    }

    /**
     * @return ResponseBo
     * @Description :保存展位
     * @Param [request]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "保存展位接口", notes = "")
    @PostMapping("saveBoothVos")
    public ResponseBo saveBoothVos(HttpServletRequest request) {
        String data = request.getParameter("data");
        JSONObject jsonData = JSONObject.parseObject(data);
        JSONArray voList = jsonData.getJSONArray("annualReservationBoothVos");
        List<AnnualReservationBoothDto> annualReservationBoothDtoList = new ArrayList<>();
        for (int i = 0; i < voList.size(); i++) {
            JSONObject annualReservationBoothVo = voList.getJSONObject(i);
            JSONArray expressInfoVos = annualReservationBoothVo.getJSONArray("expressInfoDtoList");
            Long conventionRegistrationId = annualReservationBoothVo.getLong("conventionRegistrationId");
            String modifyName = annualReservationBoothVo.getString("modifyName");
            Integer provideGifts = annualReservationBoothVo.getInteger("provideGifts");
            AnnualReservationBoothDto boothVo = new AnnualReservationBoothDto();
            if (GeneralTool.isEmpty(expressInfoVos)) {
                boothVo.setExpressInfoVoList(new ArrayList<>());
            } else {
                List<ExpressInfoDto> expressInfoDtoList = expressInfoVos.toJavaList(ExpressInfoDto.class);
                boothVo.setExpressInfoVoList(expressInfoDtoList);
            }
            boothVo.setConventionRegistrationId(conventionRegistrationId);
            boothVo.setModifyName(modifyName);
            boothVo.setProvideGifts(provideGifts);
            annualReservationBoothDtoList.add(boothVo);
        }
        String receiptCode = jsonData.getString("receiptCode");
        annualReservationFormService.saveBoothVos(receiptCode, annualReservationBoothDtoList);
        return SaveResponseBo.ok();
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.AnnualBoothListVo>
     * @Description :详情
     * @Param [annualConferenceRegistrationVo]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "展位回显接口", notes = "")
    @GetMapping("getAnnualBoothListDto")
    public ResponseBo<AnnualBoothListVo> getAnnualBoothListDto(@RequestParam String receiptCode) {
        AnnualBoothListVo data = annualReservationFormService.getAnnualBoothListDto(receiptCode);
        return new ResponseBo<>(data);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :详情
     * @Param [annualConferenceRegistrationVo]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "参会人回显接口", notes = "")
    @GetMapping("getAnnualPerson")
    public ResponseBo<AnnualReservationFormVo> getAnnualPerson(@RequestParam String receiptCode) {
        List<AnnualReservationFormVo> data = annualReservationFormService.getAnnualPerson(receiptCode);
        return new ListResponseBo<>(data);
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description :参会人数量接口
     * @Param [receiptCode]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "参会人数量接口", notes = "")
    @GetMapping("getPersonCount")
    public ResponseBo<Integer> getPersonCount(@RequestParam String receiptCode) {
        Integer data = annualReservationFormService.getPersonCount(receiptCode);
        return new ResponseBo<>(data);
    }


    /**
     * 2022年度iae年会代理报名表单
     *
     * @Date 10:11 2022/8/9
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "2022年度iae年会代理报名表单", notes = "conventionId 峰会id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/峰会/iae年会代理报名表单")
    @PostMapping("addAgent/{conventionId}")
    public ResponseBo addAgent(@RequestBody List<AnnualReservationFormAgentDto> annualReservationFormAgentDtos, @PathVariable("conventionId") Long conventionId) {
        List<ConventionPerson> conventionPersonList = annualReservationFormService.addAgentAnnualPersons(annualReservationFormAgentDtos, conventionId);
        return new ListResponseBo(conventionPersonList);
    }

    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "峰会BD下拉框", notes = "conventionId：峰会id")
    @PostMapping("/getAreaRegionSelect/{conventionId}")
    public ResponseBo<BaseSelectEntity> getAreaRegionSelectByConventionId(@PathVariable("conventionId") Long conventionId) {
        return new ListResponseBo<>(annualReservationFormService.getAreaRegionSelectByConventionId(conventionId));
    }


    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "获取峰会名称", notes = "conventionId：峰会id")
    @GetMapping("/getConventionName/{conventionId}")
    public ResponseBo<String> getConventionName(@PathVariable("conventionId") Long conventionId) {
        return new ResponseBo<>(annualReservationFormService.getConventionName(conventionId));
    }

    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "加拿大冬季Retreat报名", notes = "conventionId 峰会id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/峰会/加拿大冬季Retreat报名")
    @PostMapping("canadaWinterRetreat/{conventionId}")
    @RedisLock(value = "get:canadaWinterRetreat:RedisLock", param = "#canadaWinterRetreatDto.itemName", waitTime = 10L)
    public ResponseBo canadaWinterRetreat(@RequestBody CanadaWinterRetreatDto canadaWinterRetreatDto, @PathVariable("conventionId") Long conventionId) {
        annualReservationFormService.canadaWinterRetreat(canadaWinterRetreatDto, conventionId);
        return ResponseBo.ok();
    }

    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "加拿大冬季Retreat报名下拉框", notes = "conventionId：峰会id")
    @GetMapping("getCanadaWinterRetreatSelect/{conventionId}")
    public ResponseBo<EventItemConfigVo> getCanadaWinterRetreatSelect(@PathVariable("conventionId") Long conventionId) {
        return new ListResponseBo<>(annualReservationFormService.getCanadaWinterRetreatSelect(conventionId));
    }


    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "年会微信支付", notes = "")
    @PostMapping("generateOrders")
    public ResponseBo generateOrders(@RequestBody @Validated ConventionHotelPayDto conventionHotelPayDto, HttpServletResponse response) throws IOException {
        annualReservationFormService.generateOrders(conventionHotelPayDto,response);
        return ResponseBo.ok();
    }


    @RequestMapping(value = "notifyWeiXinPay",produces = MediaType.APPLICATION_JSON_VALUE)
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "微信支付回调", notes = "")
    public void notifyWeiXinPay(@RequestBody String notifyData,@RequestHeader HttpHeaders httpHeaders) throws Exception{
        log.info("支付回调成功#notifyWeiXinPay---------------------------");
        SignatureHeader signatureHeaderByHttpHeaders = WxUtils.getSignatureHeaderByHttpHeaders(httpHeaders);
        annualReservationFormService.notifyWeiXinPay(signatureHeaderByHttpHeaders,notifyData);
    }

    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "支付提示", notes = "")
    @PostMapping("getAmountPayNotice")
    public ResponseBo<AmountPayNoticeVo> getAmountPayNotice(@RequestBody @Validated AmountPayNoticeDto amountPayNoticeDto) {
        AmountPayNoticeVo data = annualReservationFormService.getAmountPayNotice(amountPayNoticeDto);
        return new ResponseBo<>(data);
    }

    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "支付状态查询", notes = "")
    @PostMapping("getPayType")
    public ResponseBo getPayType(@RequestParam("fkConventionPersonId")Long fkConventionPersonId) {
        return new ResponseBo(annualReservationFormService.getPayType(fkConventionPersonId));
    }


    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "同住人下拉", notes = "")
    @PostMapping("getResidentSelect")
    public ResponseBo<BaseSelectEntity> getResidentSelect(@RequestParam("fkConventionId")Long fkConventionId,
                                                          @RequestParam(value = "fkConventionPersonId",required = false)Long fkConventionPersonId,
                                                          @RequestParam("fkConventionRegistrationId")Long fkConventionRegistrationId) {
        return new ListResponseBo<>(annualReservationFormService.getResidentSelect(fkConventionId,fkConventionPersonId,fkConventionRegistrationId));
    }


    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "常用币种下拉", notes = "")
    @PostMapping("getCommonCurrencySelect")
    public ResponseBo<BaseSelectEntity> getCommonCurrencySelect() {
        return new ListResponseBo<>(annualReservationFormService.getCommonCurrencySelect());
    }

    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "保存发票信息", notes = "")
    @PostMapping("saveInvoiceInfo")
    public ResponseBo saveInvoiceInfo(@RequestBody @Validated InvoiceInfoSaveDto invoiceInfoSaveDto) {
        annualReservationFormService.saveInvoiceInfo(invoiceInfoSaveDto);
        return SaveResponseBo.ok();
    }

    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "回显发票信息", notes = "")
    @PostMapping("getInvoiceInfo")
    public ResponseBo<InvoiceInfoVo> getInvoiceInfo(@RequestParam("fkConventionPersonId") Long fkConventionPersonId) {
        return new ResponseBo<>(annualReservationFormService.getInvoiceInfo(fkConventionPersonId));
    }

    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "代理年会微信支付", notes = "")
    @PostMapping("agentGenerateOrders")
    public ResponseBo agentGenerateOrders(@RequestBody @Validated ConventionHotelPayAgentDto conventionHotelPayAgentDto, HttpServletResponse response) throws IOException {
        annualReservationFormService.agentGenerateOrders(conventionHotelPayAgentDto,response);
        return ResponseBo.ok();
    }

    @RequestMapping(value = "notifyWeiXinAgentPay",produces = MediaType.APPLICATION_JSON_VALUE)
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "代理微信支付回调", notes = "")
    public void notifyWeiXinAgentPay(@RequestBody String notifyData,@RequestHeader HttpHeaders httpHeaders) throws Exception{
        log.info("支付回调成功#notifyWeiXinAgentPay---------------------------");
        SignatureHeader signatureHeaderByHttpHeaders = WxUtils.getSignatureHeaderByHttpHeaders(httpHeaders);
        annualReservationFormService.notifyWeiXinAgentPay(signatureHeaderByHttpHeaders,notifyData);
    }

    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "获取升级房费", notes = "")
    @PostMapping("getUpgradeRoomFee")
    public ResponseBo<BigDecimal> getUpgradeRoomFee() {
        BigDecimal amount = annualReservationFormService.getUpgradeRoomFee();
        return new ResponseBo<>(amount);
    }

}

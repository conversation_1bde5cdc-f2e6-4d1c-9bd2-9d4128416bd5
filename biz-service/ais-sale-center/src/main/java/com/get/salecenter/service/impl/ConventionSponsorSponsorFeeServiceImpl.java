package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.ConventionSponsorSponsorFeeMapper;
import com.get.salecenter.vo.ConventionSponsorFeeVo;
import com.get.salecenter.entity.ConventionSponsorSponsorFee;
import com.get.salecenter.service.IConventionSponsorFeeService;
import com.get.salecenter.service.IConventionSponsorSponsorFeeService;
import com.get.salecenter.dto.ConventionSponsorSponsorFeeDto;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.StringJoiner;

/**
 * @author: Sea
 * @create: 2021/5/8 14:16
 * @verison: 1.0
 * @description:
 */
@Service
public class ConventionSponsorSponsorFeeServiceImpl extends ServiceImpl<ConventionSponsorSponsorFeeMapper,ConventionSponsorSponsorFee> implements IConventionSponsorSponsorFeeService {
    @Resource
    private ConventionSponsorSponsorFeeMapper conventionSponsorSponsorFeeMapper;
    @Resource
    private UtilService utilService;
    @Lazy
    @Resource
    private IConventionSponsorFeeService conventionSponsorFeeService;

    @Override
    public List<ConventionSponsorFeeVo> getSponsorFeeDtoList(Long sponsorId) {
        return conventionSponsorSponsorFeeMapper.getSponsorFeeDtoList(sponsorId);
    }

    @Override
    public List<ConventionSponsorFeeVo> getSponsorFeeDtoListByReceiptCode(Long conventionId, String receiptCode) {
        return conventionSponsorSponsorFeeMapper.getSponsorFeeDtoListByReceiptCode(conventionId, receiptCode);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(List<ConventionSponsorSponsorFeeDto> conventionSponsorSponsorFeeDtos) {
        //没问题直接新增 先删后增
        deleteByFkid(conventionSponsorSponsorFeeDtos.get(0).getFkConventionSponsorId());
        for (ConventionSponsorSponsorFeeDto conventionSponsorSponsorFeeDto : conventionSponsorSponsorFeeDtos) {
            ConventionSponsorSponsorFee conventionSponsorSponsorFee = BeanCopyUtils.objClone(conventionSponsorSponsorFeeDto, ConventionSponsorSponsorFee::new);
            utilService.updateUserInfoToEntity(conventionSponsorSponsorFee);
            conventionSponsorSponsorFeeMapper.insert(conventionSponsorSponsorFee);
        }
    }

    @Override
    public void validateAdd(List<Long> newSponsorFeeIds, Long sponsorId) {
        StringJoiner stringJoiner = new StringJoiner("<br/>");
        for (Long newSponsorFeeId : newSponsorFeeIds) {
            ConventionSponsorFeeVo conventionSponsorFeeVo = conventionSponsorFeeService.findConventionSponsorFeeById(newSponsorFeeId);
            Integer countLimit = conventionSponsorFeeVo.getCountLimit();
            //如果是限量的，判断是否卖完了
            if (GeneralTool.isNotEmpty(countLimit) && countLimit > 0) {
                Boolean result = soldOut(newSponsorFeeId, conventionSponsorFeeVo.getCountLimit(), sponsorId);
                //卖完就添加异常信息
                if (result) {
                    if (GeneralTool.isEmpty(GetAuthInfo.getStaffId())) {
                        //表示年会那边的，英文
                        stringJoiner.add(conventionSponsorFeeVo.getTitle() + " is sold out");
                    } else {
                        //表示新系统这边，中文
                        stringJoiner.add(conventionSponsorFeeVo.getTitle() + " 已售完");
                    }
                }
            }

        }
        //有异常信息就提示用户哪些已经卖完
        if (GeneralTool.isNotEmpty(stringJoiner.toString())) {
            throw new GetServiceException(stringJoiner.toString());
        }
    }

    @Override
    public void deleteByFkid(Long sponsorId) {
//        Example example = new Example(ConventionSponsorSponsorFee.class);
//        example.createCriteria().andEqualTo("fkConventionSponsorId", sponsorId);
        conventionSponsorSponsorFeeMapper.delete(Wrappers.<ConventionSponsorSponsorFee>lambdaQuery().eq(ConventionSponsorSponsorFee::getFkConventionSponsorId, sponsorId));
    }

    @Override
    public Boolean soldOut(Long sponsorFeeId, Integer initNum, Long sponsorId) {
        LambdaQueryWrapper<ConventionSponsorSponsorFee> lambdaQueryWrapper = Wrappers.<ConventionSponsorSponsorFee>lambdaQuery().eq(ConventionSponsorSponsorFee::getFkConventionSponsorFeeId, sponsorFeeId);
        if (GeneralTool.isNotEmpty(sponsorId)) {
            lambdaQueryWrapper.ne(ConventionSponsorSponsorFee::getFkConventionSponsorId, sponsorId);
        }
        List<ConventionSponsorSponsorFee> sponsorSponsorFees = conventionSponsorSponsorFeeMapper.selectList(lambdaQueryWrapper);
        //如果是不限量的
        if (GeneralTool.isEmpty(initNum) || initNum < 0) {
            return false;
        }
        if (null != sponsorSponsorFees) {
            if (initNum > sponsorSponsorFees.size()) {
                return false;
            }
        }
        return true;
    }
}

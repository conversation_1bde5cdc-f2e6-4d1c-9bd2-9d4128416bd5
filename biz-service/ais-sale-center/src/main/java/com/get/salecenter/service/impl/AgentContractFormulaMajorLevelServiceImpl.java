package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.salecenter.dao.sale.AgentContractFormulaMajorLevelMapper;
import com.get.salecenter.entity.AgentContractFormulaMajorLevel;
import com.get.salecenter.service.IAgentContractFormulaMajorLevelService;
import com.get.salecenter.dto.AgentContractFormulaMajorLevelDto;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * @author: Sea
 * @create: 2021/1/6 15:13
 * @verison: 1.0
 * @description:
 */
@Service
public class AgentContractFormulaMajorLevelServiceImpl implements IAgentContractFormulaMajorLevelService {
    @Resource
    private AgentContractFormulaMajorLevelMapper agentContractFormulaMajorLevelMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;

    @Override
    public Long addAgentContractFormulaMajorLevel(AgentContractFormulaMajorLevelDto agentContractFormulaMajorLevelDto) {
        AgentContractFormulaMajorLevel agentContractFormulaMajorLevel = BeanCopyUtils.objClone(agentContractFormulaMajorLevelDto, AgentContractFormulaMajorLevel::new);
        utilService.updateUserInfoToEntity(agentContractFormulaMajorLevel);
        agentContractFormulaMajorLevelMapper.insertSelective(agentContractFormulaMajorLevel);
        return agentContractFormulaMajorLevel.getId();
    }

    @Override
    public void deleteByFkid(Long agentContractFormulaId) {
//        Example example = new Example(AgentContractFormulaMajorLevel.class);
//        example.createCriteria().andEqualTo("fkAgentContractFormulaId", agentContractFormulaId);
//        agentContractFormulaMajorLevelMapper.deleteByExample(example);

        agentContractFormulaMajorLevelMapper.delete(Wrappers.<AgentContractFormulaMajorLevel>lambdaQuery().eq(AgentContractFormulaMajorLevel::getFkAgentContractFormulaId, agentContractFormulaId));
    }

    @Override
    public Map<Long, String> getMajorLevelNameMapByFkids(List<Long> agentContractFormulaIds) {
        //关系map
        Map<Long, List<Long>> idMap = new HashMap<>();
        Map<Long, String> nameMap = new HashMap<>();
        //全部majorLevelId集合
        Set<Long> majorLevelIdSet = new HashSet<>();
        for (Long agentContractFormulaId : agentContractFormulaIds) {
            //通过agentContractFormulaId获取对应所有国家id
            List<Long> majorLevelIds = getMajorLevelIdListByFkid(agentContractFormulaId);
            majorLevelIdSet.addAll(majorLevelIds);
            //agentContractFormulaId和majorLevelIds一一对应关系map
            idMap.put(agentContractFormulaId, majorLevelIds);
        }
        majorLevelIdSet.removeIf(Objects::isNull);
        //feign调用一次查出 majorLevelId和majorLevelName对应关系map
        Map<Long, String> majorLevelNameMap = new HashMap<>();
        Result<Map<Long, String>> result = institutionCenterClient.getMajorLevelNamesByIds(majorLevelIdSet);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            //map由agentContractFormulaId 对应 majorLevelIds 转成 agentContractFormulaId 对应 majorLevelNames
            for (Map.Entry<Long, List<Long>> agentContractFormula : idMap.entrySet()) {
                List<String> majorLevelNames = new ArrayList<>();
                List<Long> majorLevelIds = agentContractFormula.getValue();
                for (Long majorLevelId : majorLevelIds) {
                    majorLevelNames.add(majorLevelNameMap.get(majorLevelId));
                }
                nameMap.put(agentContractFormula.getKey(), StringUtils.join(majorLevelNames, "，"));
            }
        }

        return nameMap;
    }

    @Override
    public List<Long> getMajorLevelIdListByFkid(Long agentContractFormulaId) {
        return agentContractFormulaMajorLevelMapper.getMajorLevelIdsByFkid(agentContractFormulaId);
    }
}

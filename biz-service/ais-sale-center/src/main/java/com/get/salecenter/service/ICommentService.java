package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.service.GetService;
import com.get.salecenter.entity.SaleComment;
import com.get.salecenter.dto.CommentDto;
import com.get.salecenter.vo.CommentVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE: 2020/11/5
 * @TIME: 15:01
 * @Description:
 **/
public interface ICommentService extends GetService<SaleComment> {

    /**
     * @return java.util.List<com.get.salecenter.vo.CommentVo>
     * @Description: 获取所有评论
     * @Param [commentDto, page]
     * <AUTHOR>
     */
    List<CommentVo> datas(CommentDto commentDto, Page page);

    /**
     * 保存
     *
     * @param comment
     * @return
     */
    void addComment(SaleComment comment);


    Map<Long,List<String>> getComment(List<Long> ids,String typeKey);

    /**
     * 更新
     *
     * @param comment
     * @return
     */
    void updateComment(SaleComment comment);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 删除学生备注
     * @param mergedStudentId
     */
    void deleteByStudentId(Long mergedStudentId);

    void mergeData(Long mergedStudentId, Long targetStudentId);
}

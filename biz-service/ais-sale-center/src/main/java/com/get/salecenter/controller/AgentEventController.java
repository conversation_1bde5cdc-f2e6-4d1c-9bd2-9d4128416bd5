package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.vo.AgentEventTypeVo;
import com.get.salecenter.vo.AgentEventVo;
import com.get.salecenter.service.IAgentEventService;
import com.get.salecenter.dto.AgentEventDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/9/14
 * @TIME: 11:58
 * @Description: 代理事件管理
 **/

@Api(tags = "代理事件管理")
@RestController
@RequestMapping("sale/agentEvent")
public class AgentEventController {
    @Autowired
    private IAgentEventService agentEventService;

    /**
     * 列表数据
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "列表数据", notes = "num代理编号 name代理名称")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生代理事件管理/查询代理事件")
    @PostMapping("datas")
    public ListResponseBo<AgentEventVo> datas(@RequestBody SearchBean<AgentEventDto> page) {
        List<AgentEventVo> datas = agentEventService.getAgentEventDtos(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 新增信息
     *
     * @param agentEventDto
     * @return
     * @
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生代理事件管理/新增事件")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(AgentEventDto.Add.class)  AgentEventDto agentEventDto) {
        return SaveResponseBo.ok(agentEventService.addAgentEvent(agentEventDto));
    }

    /**
     * 修改信息
     *
     * @param agentEventDto
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生代理事件管理/更新事件")
    @PostMapping("update")
    public ResponseBo<AgentEventVo> update(@RequestBody @Validated(AgentEventDto.Update.class) AgentEventDto agentEventDto) {
        return UpdateResponseBo.ok(agentEventService.updateAgentEvent(agentEventDto));
    }

    /**
     * 删除信息
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/学生代理事件管理/删除代理")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        agentEventService.deleteAgentEvent(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ListResponseBo<com.get.salecenter.vo.AgentEventTypeVo>
     * @Description: 代理事件类型
     * @Param []
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "代理事件类型下拉框", notes = "num代理编号 name代理名称")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/代理管理/查询代理事件类型")
    @GetMapping("getAllEventType")
    public ListResponseBo<AgentEventTypeVo> getAllEventType() {
        List<AgentEventTypeVo> datas = agentEventService.getAllEventType();
        return new ListResponseBo<>(datas);
    }


}

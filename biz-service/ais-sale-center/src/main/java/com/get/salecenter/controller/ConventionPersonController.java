package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.dto.*;
import com.get.salecenter.vo.*;
import com.get.salecenter.vo.AgentAttendanceBindingInfoVo;
import com.get.salecenter.entity.ConventionPerson;
import com.get.salecenter.service.IConventionPersonService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: Sea
 * @create: 2020/7/8 14:14
 * @verison: 1.0
 * @description: 峰会参展人员管理控制器
 */
@Api(tags = "峰会参展人员管理")
@RestController
@RequestMapping("sale/conventionPerson")
public class ConventionPersonController {

    @Resource
    private IConventionPersonService conventionPersonService;

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/峰会参展人员管理/峰会参展人员详情")
    @GetMapping("/{id}")
    public ResponseBo<ConventionPersonVo> detail(@PathVariable("id") Long id) {
        ConventionPersonVo data = conventionPersonService.findConventionPersonById(id);
        return new ResponseBo<>(data);
    }

    /**
     * 新增信息
     *
     * @param conventionPersonDto
     * @return
     * @
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/峰会参展人员管理/新增峰会参展人员")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(ConventionPersonDto.Add.class) ConventionPersonDto conventionPersonDto) {
        return SaveResponseBo.ok(conventionPersonService.addConventionPerson(conventionPersonDto));
    }

    /**
     * 删除信息
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/峰会参展人员管理/删除峰会参展人员")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        conventionPersonService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param conventionPersonDto
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/峰会参展人员管理/更新峰会参展人员")
    @PostMapping("update")
    public ResponseBo<ConventionPersonVo> update(@RequestBody @Validated(ConventionPersonDto.Update.class) ConventionPersonDto conventionPersonDto) {
        return UpdateResponseBo.ok(conventionPersonService.updateConventionPerson(conventionPersonDto));
    }

    /**
     * 列表数据
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "列表数据", notes = "type为参展人员类型，name为参展人员名称，tel为参展人员联系电话，institutionProviderName学校提供商名称，institutionName学校名称，agentNum代理编号，agentName代理名称，transportationCode交通编号,fkConventionId该峰会id(一定有)")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/峰会参展人员管理/查询峰会参展人员")
    @PostMapping("datas")
    public ResponseBo<ConventionPersonVo> datas(@RequestBody SearchBean<ConventionPersonDto> page) {
        List<ConventionPersonVo> datas = conventionPersonService.getConventionPersons(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 参会人员下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "参会人员下拉框数据", notes = "id为峰会id")
    @GetMapping("getConventionPersonList/{id}")
    public ResponseBo getConventionPersonList(@PathVariable("id") Long id) {
        List<ConventionPerson> datas = conventionPersonService.getConventionPersonList(id);
        return new ListResponseBo<>(datas);
    }

    /**
     * 参会人员下拉框数据-包含[系统] 锁定床位
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "参会人员下拉框数据-包含[系统] 锁定床位", notes = "id为峰会id")
    @GetMapping("getPersonAndLockBedList/{id}/{bdCodeKey}")
    public ResponseBo getPersonAndLockBedList(@PathVariable("id") Long id, @PathVariable("bdCodeKey") Boolean bdCodeKey) {
        List<ConventionPerson> datas = conventionPersonService.getPersonAndLockBedList(id, bdCodeKey);
        return new ListResponseBo<>(datas);
    }

    /**
     * 参会人员下拉框数据-桌台
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "参会人员下拉框数据-桌台", notes = "")
    @GetMapping("getPersonForTableList/{id}")
    public ResponseBo getPersonForTableList(@PathVariable("id") Long id, @RequestParam String type) {
        List<ConventionPerson> datas = conventionPersonService.getPersonForTableList(id, type);
        return new ListResponseBo<>(datas);
    }

    /**
     * 设置签到
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "签到", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/峰会参展人员管理/签到")
    @GetMapping("setSign/{id}")
    public ResponseBo setSign(@PathVariable("id") Long id, @RequestParam Boolean isAttend) {
        return UpdateResponseBo.ok(conventionPersonService.setSign(id, isAttend));
    }

    /**
     * 跟进BD下拉框
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "跟进BD下拉框", notes = "")
    @GetMapping("getBdList/{fkConventionId}")
    public ResponseBo getBdList(@PathVariable("fkConventionId") Long fkConventionId,@RequestParam(value = "fkAreaRegionId",required = false)Long fkAreaRegionId) {
        List<StaffBdCodeVo> datas = conventionPersonService.getBdList(fkConventionId,fkAreaRegionId);
        return new ListResponseBo<>(datas);
    }


    /**
     * @return void
     * @Description :导出参会人员名册Excel
     * @Param [response, conventionSponsorVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "导出参会人员名册Excel")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/峰会参展人员管理/导出参会人员名册Excel")
    @PostMapping("/exportConventionPersonExcel")
    @ResponseBody
    public void exportConventionPersonExcel(HttpServletResponse response, @RequestBody ConventionPersonDto conventionPersonDto) {
        conventionPersonService.exportConventionPersonExcel(response, conventionPersonDto);
    }


    /**
     * @Description: 根据手机号获取对应的峰会人员姓名
     * @Author: Jerry
     * @Date:10:45 2021/10/15
     */
    @ApiIgnore
    @GetMapping("/getNamesByMobiles")
    public Map<String, String> getNamesByMobiles(@RequestParam("mobiles") Set<String> mobiles) {
        return conventionPersonService.getNamesByMobiles(mobiles);
    }

//    /**
//     * @return void
//     * @Description :导出参会人员名册Excel
//     * @Param [response,conventionSponsorVo]
//     * <AUTHOR>
//     */
//    @ApiOperation(value = "导出报名名册Excel")
//    @GetMapping("/exportConventionPersonExcel")
//    @ResponseBody
//    public void exportConventionPersonExcel(HttpServletResponse response)  {
//        ConventionPersonDto conventionPersonVo = new ConventionPersonDto();
//        conventionPersonVo.setFkConventionId(8L);
//        conventionPersonService.exportConventionPersonExcel(response, conventionPersonVo);
//    }


//    /**
//     * 列表数据
//     *
//     * @param page
//     * @return
//     * @
//     */
//    @VerifyPermission(IsVerify = false)
//    @ApiOperation(value = "列表数据", notes = "type为参展人员类型，name为参展人员名称，tel为参展人员联系电话，institutionProviderName学校提供商名称，institutionName学校名称，agentNum代理编号，agentName代理名称，transportationCode交通编号,fkConventionId该峰会id(一定有)")
//    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/峰会参展人员管理/查询峰会参展人员")
//    @PostMapping("datas")
//    public ResponseBo<ConventionPersonVo> datas(@RequestBody SearchBean<ConventionPersonDto> page)  {
//        List<ConventionPersonVo> datas = conventionPersonService.getConventionPersons(page.getData(), page);
//        Page p = BeanCopyUtils.objClone(page, Page.class);
//        return new ListResponseBo<>(datas, p);
//    }

    /**
     * @Description: 峰会助力团排行榜
     * @Param:
     * @return:
     * @Author: Walker
     * @Date: 2022/8/30
     */
    @ApiOperation(value = "峰会助力团排行榜", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/活动投票/峰会助力团排行榜")
    @PostMapping("helpVotingRanking")
    public ResponseBo<ConventionPersonVotingRankingVo> heleVotingRanking(@RequestBody SearchBean<ConventionPersonVotingRankingDto> page) {
        List<ConventionPersonVotingRankingVo> datas = conventionPersonService.helpVotingRanking(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    @ApiOperation(value = "峰会集赞活动列表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/活动投票/峰会集赞活动列表")
    @PostMapping("likeCollectionActivity")
    public ResponseBo<LikeCollectionActivityVo> likeCollectionActivity(@RequestBody SearchBean<LikeCollectionActivitySearchDto> page) {
        List<LikeCollectionActivityVo> datas = conventionPersonService.likeCollectionActivity(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }
    @ApiOperation(value = "所属峰会下拉框", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/活动投票/所属峰会下拉框")
    @PostMapping("conventionSelect")
    public ResponseBo<ConventionSelectVo> conventionSelect() {
        Set<ConventionSelectVo> conventionSelectVos = conventionPersonService.conventionSelect();
        return new ResponseBo(conventionSelectVos);
    }

    @ApiOperation(value = "峰会集赞审批", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/活动投票/峰会集赞审批")
    @PostMapping("likeConventionApproval")
    public ResponseBo likeConventionApproval(@RequestParam("id") Long id,@RequestParam("status") Integer status ) {
        conventionPersonService.likeConventionApproval(id,status);
        return  ResponseBo.ok();
    }
    @ApiOperation(value = "峰会集赞编辑", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/活动投票/峰会集赞编辑")
    @PostMapping("likeConventionEdit")
    public ResponseBo likeConventionEdit(@RequestBody LikeConventionEditDto likeConventionEditDto) {
        conventionPersonService.likeConventionEdit(likeConventionEditDto);
        return  ResponseBo.ok();
    }


    /**
     * 曾参加过峰会的同名员工列表
     * @return
     * @
     */
    @ApiOperation(value = "曾参加过峰会的同名员工列表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/峰会参展人员管理/曾参加过峰会的同名员工列表")
    @PostMapping("getDuplicatedConventionPersonsInfo")
    public ResponseBo<DuplicatedConventionPersonVo> getDuplicatedConventionPersonsInfo(@RequestParam("type")Integer type, @RequestParam("fkCompanyId")Long fkCompanyId, @RequestParam("name")String name) {
        List<DuplicatedConventionPersonVo> datas = conventionPersonService.getDuplicatedConventionPersonsInfo(type,fkCompanyId,name);
        return new ListResponseBo<>(datas);
    }

    /**
     * 代理参会统计
     * @return
     * @
     */
    @ApiOperation(value = "代理参会统计", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/峰会参展人员管理/代理参会统计")
    @PostMapping("getAgentAttendanceStatistics")
    public ResponseBo<AgentAttendanceStatisticsVo> getAgentAttendanceStatistics(@RequestBody @Validated AgentAttendanceStatisticsDto agentAttendanceStatisticsDto) {
        List<AgentAttendanceStatisticsVo> datas = conventionPersonService.getAgentAttendanceStatistics(agentAttendanceStatisticsDto);
        return new ListResponseBo<>(datas);
    }


    @ApiOperation(value = "获取代理参会人绑定bd数量信息", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/峰会参展人员管理/获取代理参会人绑定bd数量信息")
    @PostMapping("getAgentAttendanceBindingInfo")
    public ResponseBo<AgentAttendanceBindingInfoVo> getAgentAttendanceBindingInfo(@RequestBody @Validated AgentAttendanceStatisticsDto agentAttendanceStatisticsDto){
        return new ResponseBo<>(conventionPersonService.getAgentAttendanceBindingInfo(agentAttendanceStatisticsDto));
    }

    /**
     * 跟进所有地区BD下拉框
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "跟进所有地区BD下拉框", notes = "")
    @GetMapping("getAllBdList/{fkConventionId}")
    public ResponseBo getAllBdList(@PathVariable("fkConventionId") Long fkConventionId) {
        List<StaffBdCodeVo> datas = conventionPersonService.getAllBdList(fkConventionId);
        return new ListResponseBo<>(datas);
    }
}

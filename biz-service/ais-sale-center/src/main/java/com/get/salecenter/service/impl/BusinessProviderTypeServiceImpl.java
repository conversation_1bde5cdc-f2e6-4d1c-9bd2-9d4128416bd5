package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.BusinessProviderTypeMapper;
import com.get.salecenter.vo.BusinessProviderTypeVo;
import com.get.salecenter.entity.BusinessProviderType;
import com.get.salecenter.service.BusinessProviderTypeService;
import com.get.salecenter.dto.BusinessProviderTypeListDto;
import com.get.salecenter.dto.BusinessProviderTypeDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class BusinessProviderTypeServiceImpl implements BusinessProviderTypeService {

    @Resource
    private BusinessProviderTypeMapper businessProviderTypeMapper;

    @Resource
    private UtilService utilService;

    @Override
    public List<BusinessProviderTypeVo> getBusinessProviderTypeDtos(BusinessProviderTypeListDto businessProviderTypeVo, SearchBean<BusinessProviderTypeListDto> page) {
        LambdaQueryWrapper<BusinessProviderType> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(businessProviderTypeVo)) {
            if (GeneralTool.isNotEmpty(businessProviderTypeVo.getKeyWord())) {
                lambdaQueryWrapper.like(BusinessProviderType::getTypeName, businessProviderTypeVo.getKeyWord());
            }
        }
        lambdaQueryWrapper.orderByDesc(BusinessProviderType::getViewOrder);
        IPage<BusinessProviderType> pages = businessProviderTypeMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
        List<BusinessProviderType> businessProviderTypes = pages.getRecords();
        page.setAll((int) pages.getTotal());
        return businessProviderTypes.stream().map(businessProviderType -> BeanCopyUtils.objClone(businessProviderType, BusinessProviderTypeVo::new)).collect(Collectors.toList());
    }

    @Override
    public void addBusinessProviderType(ValidList<BusinessProviderTypeDto> businessProviderTypeDtos) {
        if (GeneralTool.isEmpty(businessProviderTypeDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        for (BusinessProviderTypeDto businessProviderTypeDto : businessProviderTypeDtos) {
            if (GeneralTool.isEmpty(businessProviderTypeDto.getId())) {
                BusinessProviderType businessProviderType = BeanCopyUtils.objClone(businessProviderTypeDto, BusinessProviderType::new);
                utilService.updateUserInfoToEntity(businessProviderType);
                businessProviderType.setViewOrder(businessProviderTypeMapper.getMaxViewOrder());
                businessProviderTypeMapper.insert(businessProviderType);
            } else {
                BusinessProviderType businessProviderType = BeanCopyUtils.objClone(businessProviderTypeDto, BusinessProviderType::new);
                utilService.updateUserInfoToEntity(businessProviderType);
                businessProviderTypeMapper.updateById(businessProviderType);
            }

        }
    }

    @Override
    public BusinessProviderTypeVo updateBusinessProviderType(BusinessProviderTypeDto businessProviderTypeDto) {
        BusinessProviderType businessProviderType = BeanCopyUtils.objClone(businessProviderTypeDto, BusinessProviderType::new);
        utilService.updateUserInfoToEntity(businessProviderType);
        businessProviderTypeMapper.updateById(businessProviderType);
        return findBusinessProviderTypenById(businessProviderType.getId());
    }

    private BusinessProviderTypeVo findBusinessProviderTypenById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        BusinessProviderType businessProviderType = businessProviderTypeMapper.selectById(id);
        return BeanCopyUtils.objClone(businessProviderType, BusinessProviderTypeVo::new);
    }

    @Override
    public void deleteBusinessProviderType(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        businessProviderTypeMapper.deleteById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void movingOrder(List<BusinessProviderTypeDto> businessProviderTypeDtoList) {
        if (GeneralTool.isEmpty(businessProviderTypeDtoList)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        BusinessProviderType businessProviderType1 = BeanCopyUtils.objClone(businessProviderTypeDtoList.get(0), BusinessProviderType::new);
        BusinessProviderType businessProviderType2 = BeanCopyUtils.objClone(businessProviderTypeDtoList.get(1), BusinessProviderType::new);

        Integer viewOrder1 = businessProviderType1.getViewOrder();
        businessProviderType1.setViewOrder(businessProviderType2.getViewOrder());
        businessProviderType2.setViewOrder(viewOrder1);
        utilService.updateUserInfoToEntity(businessProviderType1);
        utilService.updateUserInfoToEntity(businessProviderType2);

        businessProviderTypeMapper.updateById(businessProviderType1);
        businessProviderTypeMapper.updateById(businessProviderType2);
    }

    @Override
    public List<BaseSelectEntity> selectBusinessProviderType() {
        return businessProviderTypeMapper.selectBusinessProviderType();
    }
}

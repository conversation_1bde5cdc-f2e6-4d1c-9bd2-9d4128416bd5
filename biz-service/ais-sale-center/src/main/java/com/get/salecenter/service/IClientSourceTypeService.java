package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.salecenter.vo.ClientSourceTypeVo;
import com.get.salecenter.dto.ClientSourceTypeListDto;
import com.get.salecenter.dto.ClientSourceTypeDto;

import java.util.List;

/**
 * 学生资源推荐来源类型Service
 */
public interface IClientSourceTypeService {

    /**
     * 获取列表数据，分页
     *
     * @param clientSourceTypeListDto 查询条件
     * @param page                   分页
     * @return
     */
    List<ClientSourceTypeVo> getClientSourceTypeList(ClientSourceTypeListDto clientSourceTypeListDto, Page page);

    /**
     * 新增学生资源推荐来源类型
     *
     * @param clientSourceTypeDto 新增参数
     * @return
     */
    Long addClientSourceType(ClientSourceTypeDto clientSourceTypeDto);

    /**
     * 修改学生资源推荐来源类型
     *
     * @param clientSourceTypeDto 修改参数
     * @return
     */
    ClientSourceTypeVo updateClientSourceType(ClientSourceTypeDto clientSourceTypeDto);

    /**
     * 删除学生资源推荐来源类型
     *
     * @param id 主键ID
     */
    void delete(Long id);

    /**
     * 根据id获取学生资源推荐来源类型
     *
     * @param id 主键ID
     * @return
     */
    ClientSourceTypeVo findClientSourceTypeById(Long id);

    /**
     * 上下移动
     *
     * @param clientSourceTypeDtos 移动参数列表
     */
    void movingOrder(List<ClientSourceTypeDto> clientSourceTypeDtos);

    /**
     * 获取下拉列表
     *
     * @return
     */
    List<ClientSourceTypeVo> getClientSourceTypeSelect();
}

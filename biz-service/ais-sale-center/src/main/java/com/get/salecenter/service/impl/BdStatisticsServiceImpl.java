package com.get.salecenter.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.shaded.com.google.gson.reflect.TypeToken;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.result.Page;
import com.get.common.result.SearchBean;
import com.get.common.utils.CommonUtil;
import com.get.common.utils.DataConverter;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.UserInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.BeanUtil;
import com.get.core.tool.utils.DateUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.tool.utils.RequestHeaderHandler;
import com.get.file.utils.FileUtils;
import com.get.filecenter.dto.FileDto;
import com.get.filecenter.feign.IFileCenterClient;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.institutioncenter.vo.AreaRegionVo;
import com.get.permissioncenter.entity.StaffDownload;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.ConfigVo;
import com.get.salecenter.dao.sale.AgentMapper;
import com.get.salecenter.dao.sale.ReceivablePlanBonusSettingMapper;
import com.get.salecenter.dao.sale.StaffCommissionActionMapper;
import com.get.salecenter.dao.sale.StaffCommissionPolicyMapper;
import com.get.salecenter.dao.sale.StudentOfferItemMapper;
import com.get.salecenter.dao.sale.StudentProjectRoleMapper;
import com.get.salecenter.dto.AgentAnnualSummaryDto;
import com.get.salecenter.dto.BdStudentBonusDto;
import com.get.salecenter.dto.BdStudentStatisticalComparisonDto;
import com.get.salecenter.dto.DelayConfigDto;
import com.get.salecenter.dto.StatisticalComparisonYearDto;
import com.get.salecenter.entity.StaffBdCode;
import com.get.salecenter.entity.StaffCommissionAction;
import com.get.salecenter.service.BdStatisticsService;
import com.get.salecenter.service.IStaffBdCodeService;
import com.get.salecenter.service.IStudentOfferService;
import com.get.salecenter.utils.sale.GetAgentLabelDataUtils;
import com.get.salecenter.vo.AgentAnnualStatisticsVo;
import com.get.salecenter.vo.AgentAnnualSummaryStatisticsVo;
import com.get.salecenter.vo.AgentLabelVo;
import com.get.salecenter.vo.AgentStatisticalConditionalEchoVo;
import com.get.salecenter.vo.BdStudentBonusVo;
import com.get.salecenter.vo.BdStudentStatisticalComparisonVo;
import com.get.salecenter.vo.BdStudentStatisticalConditionalEchoVo;
import com.get.salecenter.vo.CommissionPolicySettleAccountsVo;
import com.get.salecenter.vo.StaffCommissionPolicyVo;
import com.get.salecenter.vo.StudentStatistical;
import com.get.salecenter.vo.StudentStatisticalRatio;
import com.get.salecenter.vo.UnsignedVo;
import java.io.ByteArrayOutputStream;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

/**
 * bd统计业务逻辑处理类
 *
 * <AUTHOR>
 * @date 2023/3/16 10:03
 */
@Slf4j
@Service
public class BdStatisticsServiceImpl implements BdStatisticsService {

    @Resource
    private StudentOfferItemMapper studentOfferItemMapper;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private StaffCommissionPolicyMapper staffCommissionPolicyMapper;
    @Resource
    private StudentProjectRoleMapper studentProjectRoleMapper;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    private IStaffBdCodeService staffBdCodeService;
    @Resource
    private AgentMapper agentMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private StaffCommissionActionMapper staffCommissionActionMapper;
    @Resource
    private IFileCenterClient fileCenterClient;
    @Resource
    private IStudentOfferService studentOfferService;
    @Resource
    private ReceivablePlanBonusSettingMapper receivablePlanBonusSettingMapper;
    @Resource
    private GetAgentLabelDataUtils getAgentLabelDataUtils;

    /**
     * BD学生统计对比
     *
     * @Date 11:08 2022/12/28
     * <AUTHOR>
     */
    @Override
    public List<BdStudentStatisticalComparisonVo> bdStudentStatisticalComparison(BdStudentStatisticalComparisonDto bdStudentStatisticalComparisonVo, Page page, List<Long> countryIds, Long staffId) {
        List<BdStudentStatisticalComparisonVo> bdStudentStatisticalComparisonVoList = new ArrayList<>();
        if (GeneralTool.isEmpty(countryIds)) {
            return bdStudentStatisticalComparisonVoList;
        }
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");

        //同期比对年数
        Integer comparisonYears = bdStudentStatisticalComparisonVo.getComparisonYears();
        //查询时间
        Date studentBeginTime = bdStudentStatisticalComparisonVo.getStudentBeginTime();
        Date studentEndTime = bdStudentStatisticalComparisonVo.getStudentEndTime();
        //查询初始年份
        Calendar cal = Calendar.getInstance();
        cal.setTime(studentBeginTime);
        int year = cal.get(Calendar.YEAR);

        //比对年份List
        List<StatisticalComparisonYearDto> statisticalComparisonYearDtoList = new ArrayList<>();
        for (int i = 0; i <= comparisonYears; i++) {
            Date beginTime = DateUtil.minusYears(studentBeginTime, i);
            Date endTime = DateUtil.minusYears(studentEndTime, i);
            StatisticalComparisonYearDto statisticalComparisonYearDto = new StatisticalComparisonYearDto();
            statisticalComparisonYearDto.setBeginTime(beginTime);
            statisticalComparisonYearDto.setEndTime(endTime);
            statisticalComparisonYearDtoList.add(statisticalComparisonYearDto);
        }

        IPage<BdStudentStatisticalComparisonVo> iPage = null;
//        if (page != null && bdStudentStatisticalComparisonVo.getStatisticalType() != 4) {
        if (page != null) {
            iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        }
        if (bdStudentStatisticalComparisonVo.getStatisticalType() == 5) {
            //无申请代理列表
            bdStudentStatisticalComparisonVoList = studentOfferItemMapper.noApplicationAgencyList(iPage, bdStudentStatisticalComparisonVo, statisticalComparisonYearDtoList);
            if (page != null) {
                page.setAll((int) iPage.getTotal());
            }
            return bdStudentStatisticalComparisonVoList;
        } else {
            //其他比对统计
            IPage<Long> p2 = null;
            if (page != null) {
                p2 = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
            }

            ConfigVo configVo = permissionCenterClient.getConfigByKey("HTI_BMS_START_TIME").getData();
            Date htiStartTime;
            try {
                htiStartTime = sf.parse(configVo.getValue1());
            } catch (ParseException e) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("DATE_FORMAT_CONVERSION_ERROR"));
            }

            //分页 只要有数据的
            List<Long> ids = studentOfferItemMapper.selectBdStudentStatistical(p2, bdStudentStatisticalComparisonVo,
                    statisticalComparisonYearDtoList, countryIds, studentBeginTime, studentEndTime, htiStartTime);
            if (page != null) {
                page.setAll((int) p2.getTotal());
            }
            if (GeneralTool.isEmpty(ids)) {
                return bdStudentStatisticalComparisonVoList;
            }

            List<StaffCommissionPolicyVo> bdStaffCommissionPolicyList = staffCommissionPolicyMapper.getBdStaffCommissionPolicy(0L, bdStudentStatisticalComparisonVo.getProjectRoleKey());
            //员工提成业务步骤key
            Map<String, BigDecimal> commissionPolicMap = new HashMap<>();
            //计算未结算金额 匹配提成规则
            for (StaffCommissionPolicyVo staffCommissionPolicyVo : bdStaffCommissionPolicyList) {
                commissionPolicMap.put(staffCommissionPolicyVo.getFkStaffCommissionStepKey(), staffCommissionPolicyVo.getFixedAmount());
            }
            //获取基础数据，防止每年的国家数不一样
            bdStudentStatisticalComparisonVoList = studentOfferItemMapper.getBdStudentStatisticalBasicData(bdStudentStatisticalComparisonVo, ids,
                    statisticalComparisonYearDtoList, countryIds, studentBeginTime, studentEndTime, htiStartTime);

            Set<Long> agentIds = bdStudentStatisticalComparisonVoList.stream().map(BdStudentStatisticalComparisonVo::getTargetId).collect(Collectors.toSet());
            //代理标签
            Map<Long, List<AgentLabelVo>> agentLabelMap = getAgentLabelDataUtils.getAgentLabelMap(agentIds).getAgentLabelMap();
            //3213 BD学生统计对比，3个列表增加排序选择  特殊排序处理
            if (bdStudentStatisticalComparisonVo.getStatisticalType() == 1 || bdStudentStatisticalComparisonVo.getStatisticalType() == 2 || bdStudentStatisticalComparisonVo.getStatisticalType() == 3 || bdStudentStatisticalComparisonVo.getStatisticalType() == 4) {
                bdStudentStatisticalComparisonVoList = bdStudentStatisticalComparisonVoList.stream()
                        .sorted(Comparator.comparingInt(bdStudentStatisticalComparisonDto -> {
                            int index = ids.indexOf(bdStudentStatisticalComparisonDto.getTargetId());
                            return index != -1 ? index : Integer.MAX_VALUE;
                        }))
                        .collect(Collectors.toList());
            }
            if (bdStudentStatisticalComparisonVo.getStatisticalType() == 6 || bdStudentStatisticalComparisonVo.getStatisticalType() == 8) {
                for (BdStudentStatisticalComparisonVo bdStudentStatisticalComparisonDto : bdStudentStatisticalComparisonVoList) {
                    bdStudentStatisticalComparisonDto.setTargetName(bdStudentStatisticalComparisonVo.getProjectRoleName() + bdStudentStatisticalComparisonDto.getTargetName());
                }
            }

            DelayConfigDto delayConfig = getDelayConfig();
            //查询每年的数据

            for (int i = 1; i <= comparisonYears + 1; i++) {
                Date beginTime = DateUtil.minusYears(studentBeginTime, i - 1);
                Date endTime = DateUtil.minusYears(studentEndTime, i - 1);

                //查询本轮年份
                cal.setTime(beginTime);
                int nowYear = cal.get(Calendar.YEAR);
                Boolean isBd = studentOfferService.getIsBd(staffId);
                List<StudentStatistical> studentStatisticalList = studentOfferItemMapper.getBdStudentStatistical(bdStudentStatisticalComparisonVo,
                        ids, beginTime, endTime, null, null, null, null, countryIds, htiStartTime, false, delayConfig, isBd,
                        SecureUtil.getPermissionGroupInstitutionIds(),
                        SecureUtil.getStaffBoundBdIds());
                for (StudentStatistical studentStatistical : studentStatisticalList) {
                    if (bdStudentStatisticalComparisonVo.getStatisticalType() == 6 || bdStudentStatisticalComparisonVo.getStatisticalType() == 8) {
                        studentStatistical.setTargetName(bdStudentStatisticalComparisonVo.getProjectRoleName() + studentStatistical.getTargetName());
                    }
                    BdStudentStatisticalComparisonVo bdStudentStatisticalComparisonDto = null;
                    //迭代基础数据，按年份往里面塞值
                    for (BdStudentStatisticalComparisonVo bdStudentStatisticalDto : bdStudentStatisticalComparisonVoList) {
                        //如果对应了多个员工id,则要匹配上才塞值
                        if (GeneralTool.isEmpty(bdStudentStatisticalDto.getFkStaffId()) ||
                                (GeneralTool.isNotEmpty(bdStudentStatisticalDto.getFkStaffId()) && bdStudentStatisticalDto.getFkStaffId().equals(studentStatistical.getFkStaffId()))) {
                            //如果有区分业务国家
                            if (bdStudentStatisticalComparisonVo.getIsDistinguishCountryFlag() &&
                                    studentStatistical.getTargetId().equals(bdStudentStatisticalDto.getTargetId()) &&
                                    studentStatistical.getFkAreaCountryId().equals(bdStudentStatisticalDto.getFkAreaCountryId())) {
                                bdStudentStatisticalComparisonDto = bdStudentStatisticalDto;
                            } else if (!bdStudentStatisticalComparisonVo.getIsDistinguishCountryFlag() && studentStatistical.getTargetId().equals(bdStudentStatisticalDto.getTargetId())) {
                                if (bdStudentStatisticalComparisonVo.getStatisticalType() == 6) {
                                    //                                if (bdStudentStatisticalDto.getFkStudentProjectRoleId().equals(studentStatistical.getFkStudentProjectRoleId())) {
                                    bdStudentStatisticalComparisonDto = bdStudentStatisticalDto;
                                    //                                }
                                } else {
                                    bdStudentStatisticalComparisonDto = bdStudentStatisticalDto;
                                }
                            }
                        }
                    }

                    //前端要求返回格式转换
                    switch (bdStudentStatisticalComparisonVo.getStatisticalType()) {
                        case 1:
                            bdStudentStatisticalComparisonDto.setBdName(studentStatistical.getTargetName());
                            break;
                        case 2:
                            bdStudentStatisticalComparisonDto.setRegion(studentStatistical.getTargetName());
                            break;
                        case 3:
                            bdStudentStatisticalComparisonDto.setRegion(studentStatistical.getTargetName());
                            break;
                        case 6:
                        case 8:
                            bdStudentStatisticalComparisonDto.setRoleStaffName(studentStatistical.getTargetName());
                            break;
                    }


                    Map<String, StudentStatistical> studentStatisticalMap;
                    if (GeneralTool.isEmpty(bdStudentStatisticalComparisonDto.getStudentStatisticalMap())) {
                        studentStatisticalMap = new LinkedHashMap<>();
                        for (int j = comparisonYears; j >= 0; j--) {
                            //本轮年份
//                            int currentRound = year - j;
                            String timeInterval = sf.format(DateUtil.minusYears(studentBeginTime, j)) + " : " + sf.format(DateUtil.minusYears(studentEndTime, j));
                            studentStatisticalMap.put(timeInterval, null);
                        }
                    } else {
                        studentStatisticalMap = bdStudentStatisticalComparisonDto.getStudentStatisticalMap();
                    }

                    //如果为项目成员统计结算表 -> 计算已结算数
                    if (bdStudentStatisticalComparisonVo.getStatisticalType() == 8) {
                        if (GeneralTool.isEmpty(bdStudentStatisticalComparisonVo.getStatusSettlement())) {
                            studentStatistical.setCommissionActionItemNumStr(studentStatistical.getUnsettledCommissionActionItemNum() + " / " + studentStatistical.getCommissionActionItemNum());
                            studentStatistical.setCommissionActionDelayItemNumStr(studentStatistical.getUnsettledCommissionActionDelayItemNum() + " / " + studentStatistical.getCommissionActionDelayItemNum());
                            studentStatistical.setCommissionActionVisaNumStr(studentStatistical.getUnsettledCommissionActionVisaNum() + " / " + studentStatistical.getCommissionActionVisaNum());
                            studentStatistical.setCommissionActionEnrolledNumStr(studentStatistical.getUnsettledCommissionActionEnrolledNum() + " / " + studentStatistical.getCommissionActionEnrolledNum());
                            studentStatistical.setCommissionActionEnrolledVisaNumStr(studentStatistical.getUnsettledCommissionActionEnrolledVisaNum() + " / " + studentStatistical.getCommissionActionEnrolledVisaNum());
                            studentStatistical.setCommissionActionOsNumStr(studentStatistical.getUnsettledCommissionActionOsNum() + " / " + studentStatistical.getCommissionActionOsNum());
                            studentStatistical.setCommissionActionAdmittedNumStr(studentStatistical.getUnsettledCommissionActionAdmittedNum() + " / " + studentStatistical.getCommissionActionAdmittedNum());
                            studentStatistical.setCommissionActionAdmittedDelayNumStr(studentStatistical.getUnsettledCommissionActionAdmittedDelayNum() + " / " + studentStatistical.getCommissionActionAdmittedDelayNum());
                        } else if (bdStudentStatisticalComparisonVo.getStatusSettlement().equals(0)) {
                            studentStatistical.setCommissionActionItemNumStr(studentStatistical.getUnsettledCommissionActionItemNum().toString());
                            studentStatistical.setCommissionActionDelayItemNumStr(studentStatistical.getUnsettledCommissionActionDelayItemNum().toString());
                            studentStatistical.setCommissionActionVisaNumStr(studentStatistical.getUnsettledCommissionActionVisaNum().toString());
                            studentStatistical.setCommissionActionEnrolledNumStr(studentStatistical.getUnsettledCommissionActionEnrolledNum().toString());
                            studentStatistical.setCommissionActionEnrolledVisaNumStr(studentStatistical.getUnsettledCommissionActionEnrolledVisaNum().toString());
                            studentStatistical.setCommissionActionOsNumStr(studentStatistical.getUnsettledCommissionActionOsNum().toString());
                            studentStatistical.setCommissionActionAdmittedNumStr(studentStatistical.getUnsettledCommissionActionAdmittedNum().toString());
                            studentStatistical.setCommissionActionAdmittedDelayNumStr(studentStatistical.getUnsettledCommissionActionAdmittedDelayNum().toString());
                        } else {
                            studentStatistical.setCommissionActionItemNumStr(studentStatistical.getCommissionActionItemNum().toString());
                            studentStatistical.setCommissionActionDelayItemNumStr(studentStatistical.getCommissionActionDelayItemNum().toString());
                            studentStatistical.setCommissionActionVisaNumStr(studentStatistical.getCommissionActionVisaNum().toString());
                            studentStatistical.setCommissionActionEnrolledNumStr(studentStatistical.getCommissionActionEnrolledNum().toString());
                            studentStatistical.setCommissionActionEnrolledVisaNumStr(studentStatistical.getCommissionActionEnrolledVisaNum().toString());
                            studentStatistical.setCommissionActionOsNumStr(studentStatistical.getCommissionActionOsNum().toString());
                            studentStatistical.setCommissionActionAdmittedNumStr(studentStatistical.getCommissionActionAdmittedNum().toString());
                            studentStatistical.setCommissionActionAdmittedDelayNumStr(studentStatistical.getCommissionActionAdmittedDelayNum().toString());
                        }

                        //计算未结算金额
                        //新申请提成金额
                        BigDecimal newAmount = GeneralTool.isNotEmpty(commissionPolicMap.get(ProjectKeyEnum.STEP_NEW_APP.key)) ? commissionPolicMap.get(ProjectKeyEnum.STEP_NEW_APP.key) : BigDecimal.ZERO;
                        //OS提成金额
                        BigDecimal osAmount = GeneralTool.isNotEmpty(commissionPolicMap.get(ProjectKeyEnum.STEP_OFFER_SELECTION.key)) ? commissionPolicMap.get(ProjectKeyEnum.STEP_OFFER_SELECTION.key) : BigDecimal.ZERO;
                        //签证数提成金额
                        BigDecimal visaAmount = GeneralTool.isNotEmpty(commissionPolicMap.get(ProjectKeyEnum.STEP_VISA_GRANTED.key)) ? commissionPolicMap.get(ProjectKeyEnum.STEP_VISA_GRANTED.key) : BigDecimal.ZERO;
                        //入学提成金额
                        BigDecimal enrolledAmount = GeneralTool.isNotEmpty(commissionPolicMap.get(ProjectKeyEnum.STEP_ENROLLED.key)) ? commissionPolicMap.get(ProjectKeyEnum.STEP_ENROLLED.key) : BigDecimal.ZERO;
                        //后补签证金额
                        BigDecimal enrolledVisaAmount = GeneralTool.isNotEmpty(commissionPolicMap.get(ProjectKeyEnum.STEP_VISA_GRANTED_BACK.key)) ? commissionPolicMap.get(ProjectKeyEnum.STEP_VISA_GRANTED_BACK.key) : BigDecimal.ZERO;
                        //申请数提成金额
                        BigDecimal admittedAmount = GeneralTool.isNotEmpty(commissionPolicMap.get(ProjectKeyEnum.STEP_ADMITTED.key)) ? commissionPolicMap.get(ProjectKeyEnum.STEP_ADMITTED.key) : BigDecimal.ZERO;
                        //延迟提交
                        BigDecimal submittedDelayAmount = GeneralTool.isNotEmpty(commissionPolicMap.get(ProjectKeyEnum.STEP_SUBMITTED_DELAY.key)) ? commissionPolicMap.get(ProjectKeyEnum.STEP_SUBMITTED_DELAY.key) : BigDecimal.ZERO;
                        //延迟反馈
                        BigDecimal admittedDelayAmount = GeneralTool.isNotEmpty(commissionPolicMap.get(ProjectKeyEnum.STEP_ADMITTED_DELAY.key)) ? commissionPolicMap.get(ProjectKeyEnum.STEP_ADMITTED_DELAY.key) : BigDecimal.ZERO;

                        //未结算金额
                        BigDecimal commissionActionAmount = newAmount.multiply(new BigDecimal(studentStatistical.getUnsettledCommissionActionItemNum()))
                                .add(osAmount.multiply(new BigDecimal(studentStatistical.getUnsettledCommissionActionOsNum())))
                                .add(visaAmount.multiply(new BigDecimal(studentStatistical.getUnsettledCommissionActionVisaNum())))
                                .add(enrolledAmount.multiply(new BigDecimal(studentStatistical.getUnsettledCommissionActionEnrolledNum()))
                                        .add(enrolledVisaAmount.multiply(new BigDecimal(studentStatistical.getUnsettledCommissionActionEnrolledVisaNum())))
                                        .add(admittedAmount.multiply(new BigDecimal(studentStatistical.getUnsettledCommissionActionAdmittedNum())))
                                        .add(submittedDelayAmount.multiply(new BigDecimal(studentStatistical.getUnsettledCommissionActionDelayItemNum())))
                                        .add(admittedDelayAmount.multiply(new BigDecimal(studentStatistical.getUnsettledCommissionActionAdmittedDelayNum()))));

                        //已结算金额
                        BigDecimal settledCommissionActionAmount = newAmount.multiply(new BigDecimal(studentStatistical.getCommissionActionItemNum()))
                                .add(osAmount.multiply(new BigDecimal(studentStatistical.getCommissionActionOsNum())))
                                .add(visaAmount.multiply(new BigDecimal(studentStatistical.getCommissionActionVisaNum())))
                                .add(enrolledAmount.multiply(new BigDecimal(studentStatistical.getCommissionActionEnrolledNum())))
                                .add(enrolledVisaAmount.multiply(new BigDecimal(studentStatistical.getCommissionActionEnrolledVisaNum())))
                                .add(admittedAmount.multiply(new BigDecimal(studentStatistical.getCommissionActionAdmittedNum()))
                                        .add(submittedDelayAmount.multiply(new BigDecimal(studentStatistical.getCommissionActionDelayItemNum())))
                                        .add(admittedDelayAmount.multiply(new BigDecimal(studentStatistical.getCommissionActionAdmittedDelayNum()))));

                        if (GeneralTool.isEmpty(bdStudentStatisticalComparisonVo.getStatusSettlement())) {
                            studentStatistical.setCommissionActionAmount(commissionActionAmount + " / " + settledCommissionActionAmount);
                        } else if (bdStudentStatisticalComparisonVo.getStatusSettlement() == 0) {
                            studentStatistical.setCommissionActionAmount(commissionActionAmount.toString());
                        } else if (bdStudentStatisticalComparisonVo.getStatusSettlement() == 1) {
                            studentStatistical.setCommissionActionAmount(settledCommissionActionAmount.toString());
                        }
                    }

                    String timeInterval = sf.format(beginTime) + " : " + sf.format(endTime);
                    studentStatisticalMap.put(timeInterval, studentStatistical);
                    bdStudentStatisticalComparisonDto.setStudentStatisticalMap(studentStatisticalMap);
                }
            }


            //年份对比
            //下一年的数据 用于与本次年份数据对比
            for (BdStudentStatisticalComparisonVo bdStudentStatisticalComparisonDto : bdStudentStatisticalComparisonVoList) {
                //代理标签
                if (GeneralTool.isNotEmpty(bdStudentStatisticalComparisonDto.getTargetId())) {
                    getAgentLabelDataUtils.setAgentLabelVosByLabelMap(bdStudentStatisticalComparisonDto, agentLabelMap, bdStudentStatisticalComparisonDto.getTargetId(), BdStudentStatisticalComparisonVo::setAgentLabelVos);
                }
                //年份 - 学生数统计
                Map<String, StudentStatistical> studentStatisticalMap = bdStudentStatisticalComparisonDto.getStudentStatisticalMap();
                if (GeneralTool.isEmpty(studentStatisticalMap)) {
                    studentStatisticalMap = new LinkedHashMap<>();
                    for (int j = comparisonYears; j >= 0; j--) {
                        //本轮年份
//                        int currentRound = year - j;
                        String timeInterval = sf.format(DateUtil.minusYears(studentEndTime, j)) + " : " + sf.format(DateUtil.minusYears(studentEndTime, j));
                        studentStatisticalMap.put(timeInterval, null);
                    }
                    bdStudentStatisticalComparisonDto.setStudentStatisticalMap(studentStatisticalMap);
                }
                //年份 - 学生数统计比率
                Map<String, StudentStatisticalRatio> studentStatisticalRatioMap = new LinkedHashMap<>();
                for (int i = comparisonYears; i > 0; i--) {
                    //本轮年份
                    int currentRound = year - i;
                    String currentTimeInterval = sf.format(DateUtil.minusYears(studentBeginTime, i)) + " : " + sf.format(DateUtil.minusYears(studentEndTime, i));
                    //上一年年份
                    int lastYear = currentRound + 1;
                    String lastTimeInterval = sf.format(DateUtil.minusYears(studentBeginTime, i - 1)) + " : " + sf.format(DateUtil.minusYears(studentEndTime, i - 1));

                    studentStatisticalRatioMap.put(currentTimeInterval + " - " + lastTimeInterval, null);
                }

                for (int i = 0; i < comparisonYears; i++) {
                    //本轮年份
                    int currentRound = year - i;
                    String currentTimeInterval = sf.format(DateUtil.minusYears(studentBeginTime, i)) + " : " + sf.format(DateUtil.minusYears(studentEndTime, i));
                    //上一年年份
                    int lastYear = currentRound - 1;
                    String lastTimeInterval = sf.format(DateUtil.minusYears(studentBeginTime, i + 1)) + " : " + sf.format(DateUtil.minusYears(studentEndTime, i + 1));

                    //本轮年份统计数
                    StudentStatistical studentStatistical = studentStatisticalMap.get(currentTimeInterval);
                    if (GeneralTool.isEmpty(studentStatistical)) {
                        studentStatistical = new StudentStatistical();
                        studentStatistical.setNewFirstStudentNum(0);
                        studentStatistical.setOldFirstStudentNum(0);
                        studentStatistical.setAppOptStudentNum(0);
                        studentStatistical.setItemNum(0);
                        studentStatistical.setDelayItemNum(0);
                        studentStatistical.setOsNum(0);
                        studentStatistical.setRepeatOsStudentNum(0);
                        studentStatistical.setVisaNum(0);
                        studentStatistical.setEnrolledNum(0);
                        studentStatistical.setEnrolledVisaNum(0);
                        studentStatistical.setAdmittedNum(0);
                        studentStatistical.setDelayAdmittedNum(0);
                        studentStatistical.setTotalStudentNum(0);
                        studentStatistical.setNewTotalStudentNum(0);
                        studentStatistical.setOldTotalStudentNum(0);
                        studentStatisticalMap.put(currentTimeInterval, studentStatistical);
                    }
                    //上一年份统计数
                    StudentStatistical lastYearStudentStatistical = studentStatisticalMap.get(lastTimeInterval);
                    //最后一年份，不需要和后面年份比对了
                    if (currentRound == year - comparisonYears) {
                        continue;
                    }
                    if (GeneralTool.isEmpty(lastYearStudentStatistical)) {
                        lastYearStudentStatistical = new StudentStatistical();
                        lastYearStudentStatistical.setNewFirstStudentNum(0);
                        lastYearStudentStatistical.setAppOptStudentNum(0);
                        lastYearStudentStatistical.setOldFirstStudentNum(0);
                        lastYearStudentStatistical.setItemNum(0);
                        lastYearStudentStatistical.setDelayItemNum(0);
                        lastYearStudentStatistical.setOsNum(0);
                        lastYearStudentStatistical.setRepeatOsStudentNum(0);
                        lastYearStudentStatistical.setVisaNum(0);
                        lastYearStudentStatistical.setEnrolledNum(0);
                        lastYearStudentStatistical.setEnrolledVisaNum(0);
                        lastYearStudentStatistical.setAdmittedNum(0);
                        lastYearStudentStatistical.setDelayAdmittedNum(0);
                        lastYearStudentStatistical.setTotalStudentNum(0);
                        lastYearStudentStatistical.setNewTotalStudentNum(0);
                        lastYearStudentStatistical.setOldTotalStudentNum(0);
                        studentStatisticalMap.put(lastTimeInterval, lastYearStudentStatistical);
                    }
                    //学生数统计比率
                    StudentStatisticalRatio studentStatisticalRatio = new StudentStatisticalRatio();
                    //学生数增减比率
                    if (lastYearStudentStatistical.getOldFirstStudentNum().equals(0)) {
                        studentStatisticalRatio.setOldStudentNumRatioNum(new BigDecimal(studentStatistical.getOldFirstStudentNum()));
                    } else {
                        studentStatisticalRatio.setOldStudentNumRatio(new BigDecimal((studentStatistical.getOldFirstStudentNum() - lastYearStudentStatistical.getOldFirstStudentNum())).divide(new BigDecimal(lastYearStudentStatistical.getOldFirstStudentNum()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
                    }
                    if (lastYearStudentStatistical.getNewFirstStudentNum().equals(0)) {
                        studentStatisticalRatio.setNewStudentNumRatioNum(new BigDecimal(studentStatistical.getNewFirstStudentNum()));
                    } else {
                        studentStatisticalRatio.setNewStudentNumRatio(new BigDecimal((studentStatistical.getNewFirstStudentNum() - lastYearStudentStatistical.getNewFirstStudentNum())).divide(new BigDecimal(lastYearStudentStatistical.getNewFirstStudentNum()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
                    }
                    //总学生数增减比率
                    if (bdStudentStatisticalComparisonVo.getStatisticalType() == 2 || bdStudentStatisticalComparisonVo.getStatisticalType() == 3 || bdStudentStatisticalComparisonVo.getStatisticalType() == 4) {
                        if (lastYearStudentStatistical.getTotalStudentNum().equals(0)) {
                            studentStatisticalRatio.setTotalStudentNum(new BigDecimal(studentStatistical.getTotalStudentNum()));
                        } else {
                            studentStatisticalRatio.setTotalStudentNumRatio(new BigDecimal((studentStatistical.getTotalStudentNum() - lastYearStudentStatistical.getTotalStudentNum())).divide(new BigDecimal(lastYearStudentStatistical.getTotalStudentNum()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
                        }
                    }
                    if (bdStudentStatisticalComparisonVo.getStatisticalType() == 1 || bdStudentStatisticalComparisonVo.getStatisticalType() == 2 || bdStudentStatisticalComparisonVo.getStatisticalType() == 3 || bdStudentStatisticalComparisonVo.getStatisticalType() == 4 || bdStudentStatisticalComparisonVo.getStatisticalType() == 6 || bdStudentStatisticalComparisonVo.getStatisticalType() == 7 || bdStudentStatisticalComparisonVo.getStatisticalType() == 8) {
                        if (lastYearStudentStatistical.getNewTotalStudentNum().equals(0)) {
                            studentStatisticalRatio.setNewTotalStudentNum(new BigDecimal(studentStatistical.getNewTotalStudentNum()));
                        } else {
                            studentStatisticalRatio.setNewTotalStudentNumRatio(new BigDecimal((studentStatistical.getNewTotalStudentNum() - lastYearStudentStatistical.getNewTotalStudentNum())).divide(new BigDecimal(lastYearStudentStatistical.getNewTotalStudentNum()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
                        }
                        if (lastYearStudentStatistical.getOldTotalStudentNum().equals(0)) {
                            studentStatisticalRatio.setOldTotalStudentNum(new BigDecimal(studentStatistical.getOldTotalStudentNum()));
                        } else {
                            studentStatisticalRatio.setOldTotalStudentNumRatio(new BigDecimal((studentStatistical.getOldTotalStudentNum() - lastYearStudentStatistical.getOldTotalStudentNum())).divide(new BigDecimal(lastYearStudentStatistical.getOldTotalStudentNum()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
                        }
                        if (lastYearStudentStatistical.getAppOptStudentNum().equals(0)) {
                            studentStatisticalRatio.setAppOptStudentNum(new BigDecimal(studentStatistical.getAppOptStudentNum()));
                        } else {
                            studentStatisticalRatio.setAppOptStudentNumRatio(new BigDecimal((studentStatistical.getAppOptStudentNum() - lastYearStudentStatistical.getAppOptStudentNum())).divide(new BigDecimal(lastYearStudentStatistical.getAppOptStudentNum()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
                        }
                    }
                    //os数增减比率
                    if (lastYearStudentStatistical.getOsNum().equals(0)) {
                        studentStatisticalRatio.setOsNumRatioNum(new BigDecimal(studentStatistical.getOsNum()));
                    } else {
                        studentStatisticalRatio.setOsNumRatio(new BigDecimal((studentStatistical.getOsNum() - lastYearStudentStatistical.getOsNum())).divide(new BigDecimal(lastYearStudentStatistical.getOsNum()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
                    }
                    if (bdStudentStatisticalComparisonVo.getStatisticalType() == 6) {
                        //提交数减比率
                        if (lastYearStudentStatistical.getItemNum().equals(0)) {
                            studentStatisticalRatio.setItemNumRatioNum(new BigDecimal(studentStatistical.getItemNum()));
                        } else {
                            studentStatisticalRatio.setItemNumRatio(new BigDecimal((studentStatistical.getItemNum() - lastYearStudentStatistical.getItemNum())).divide(new BigDecimal(lastYearStudentStatistical.getItemNum()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
                        }
                        //延迟提交数减比率
                        if (lastYearStudentStatistical.getDelayItemNum().equals(0)) {
                            studentStatisticalRatio.setItemNumRatioDelayNum(new BigDecimal(studentStatistical.getDelayItemNum()));
                        } else {
                            studentStatisticalRatio.setItemNumDelayRatio(new BigDecimal((studentStatistical.getDelayItemNum() - lastYearStudentStatistical.getDelayItemNum())).divide(new BigDecimal(lastYearStudentStatistical.getDelayItemNum()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
                        }
                        //签证数增减比率
                        if (lastYearStudentStatistical.getVisaNum().equals(0)) {
                            studentStatisticalRatio.setVisaNumRatioNum(new BigDecimal(studentStatistical.getVisaNum()));
                        } else {
                            studentStatisticalRatio.setVisaNumRatio(new BigDecimal((studentStatistical.getVisaNum() - lastYearStudentStatistical.getVisaNum())).divide(new BigDecimal(lastYearStudentStatistical.getVisaNum()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
                        }
                        //入学数增减比率
                        if (lastYearStudentStatistical.getEnrolledNum().equals(0)) {
                            studentStatisticalRatio.setEnrolledNumRatioNum(new BigDecimal(studentStatistical.getEnrolledNum()));
                        } else {
                            studentStatisticalRatio.setEnrolledNumRatio(new BigDecimal((studentStatistical.getEnrolledNum() - lastYearStudentStatistical.getEnrolledNum())).divide(new BigDecimal(lastYearStudentStatistical.getEnrolledNum()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
                        }
                        //补签证增减比率
                        if (lastYearStudentStatistical.getEnrolledVisaNum().equals(0)) {
                            studentStatisticalRatio.setEnrolledVisaNumRatioNum(new BigDecimal(studentStatistical.getEnrolledVisaNum()));
                        } else {
                            studentStatisticalRatio.setEnrolledVisaNumRatio(new BigDecimal((studentStatistical.getEnrolledVisaNum() - lastYearStudentStatistical.getEnrolledVisaNum())).divide(new BigDecimal(lastYearStudentStatistical.getEnrolledVisaNum()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
                        }
                        //申请反馈数增减比率
                        if (lastYearStudentStatistical.getAdmittedNum().equals(0)) {
                            studentStatisticalRatio.setAdmittedRatioNum(new BigDecimal(studentStatistical.getAdmittedNum()));
                        } else {
                            studentStatisticalRatio.setAdmittedNumRatio(new BigDecimal((studentStatistical.getAdmittedNum() - lastYearStudentStatistical.getAdmittedNum())).divide(new BigDecimal(lastYearStudentStatistical.getAdmittedNum()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
                        }
                        //延迟申请反馈数增减比率
                        if (lastYearStudentStatistical.getDelayAdmittedNum().equals(0)) {
                            studentStatisticalRatio.setAdmittedRatioDelayNum(new BigDecimal(studentStatistical.getDelayAdmittedNum()));
                        } else {
                            studentStatisticalRatio.setAdmittedNumDelayRatio(new BigDecimal((studentStatistical.getDelayAdmittedNum() - lastYearStudentStatistical.getDelayAdmittedNum())).divide(new BigDecimal(lastYearStudentStatistical.getDelayAdmittedNum()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
                        }
                    }
                    studentStatisticalRatioMap.put(lastTimeInterval + " - " + currentTimeInterval, studentStatisticalRatio);
                    bdStudentStatisticalComparisonDto.setStudentStatisticalRatioMap(studentStatisticalRatioMap);
                }

            }

        }


        //年份 - 学生数统计 小计
        Map<String, StudentStatistical> studentStatisticalTotalMap = new LinkedHashMap<>();
        for (int j = comparisonYears; j >= 0; j--) {
            //本轮年份
//            int currentRound = year - j;
            String timeInterval = sf.format(DateUtil.minusYears(studentBeginTime, j)) + " : " + sf.format(DateUtil.minusYears(studentEndTime, j));
            studentStatisticalTotalMap.put(timeInterval, null);
        }
        //最终返回的排列好的 插入了小计的 统计列表List
        List<BdStudentStatisticalComparisonVo> bdStudentStatisticalComparisonVoListResult = new ArrayList<>();
        //小计对象
        BdStudentStatisticalComparisonVo bdStudentStatisticalComparisonSubtotalDto = new BdStudentStatisticalComparisonVo();
        bdStudentStatisticalComparisonSubtotalDto.setAreaCountryName("小计");
        //如果有按业务国家分并且要显示小计，计算下小计
        if (bdStudentStatisticalComparisonVo.getIsDistinguishCountryFlag() && !bdStudentStatisticalComparisonVo.getDisplayModeFlag()) {
            Long targetId = null;
            for (int j = 0; j < bdStudentStatisticalComparisonVoList.size(); j++) {
                BdStudentStatisticalComparisonVo bdStudentStatisticalComparisonDto = bdStudentStatisticalComparisonVoList.get(j);
                if (null == targetId) {
                    targetId = bdStudentStatisticalComparisonDto.getTargetId();
                    for (int i = 0; i <= comparisonYears; i++) {
                        Map<String, StudentStatistical> studentStatisticalMap = bdStudentStatisticalComparisonDto.getStudentStatisticalMap();
                        //本轮年份
//                        int currentRound = year - i;
                        String timeInterval = sf.format(DateUtil.minusYears(studentBeginTime, i)) + " : " + sf.format(DateUtil.minusYears(studentEndTime, i));
                        StudentStatistical studentStatistical = studentStatisticalMap.get(timeInterval);
                        StudentStatistical clone = BeanUtil.clone(studentStatistical);
                        studentStatisticalTotalMap.put(timeInterval, clone);
                    }

                    bdStudentStatisticalComparisonVoListResult.add(bdStudentStatisticalComparisonDto);
                    continue;
                }

                //targetId变化 准备进入下一个循环，先统计上一循环数据
                if (!targetId.equals(bdStudentStatisticalComparisonDto.getTargetId())) {
                    subtotalCalculation(comparisonYears, year, studentStatisticalTotalMap, bdStudentStatisticalComparisonVoListResult, bdStudentStatisticalComparisonSubtotalDto, bdStudentStatisticalComparisonVo.getStatisticalType(), studentBeginTime, studentEndTime);

                    //覆盖上一循环数据，重新开始新的循环
                    targetId = bdStudentStatisticalComparisonDto.getTargetId();
                    studentStatisticalTotalMap = new LinkedHashMap<>();
                    bdStudentStatisticalComparisonSubtotalDto = new BdStudentStatisticalComparisonVo();
                    bdStudentStatisticalComparisonSubtotalDto.setAreaCountryName("小计");

                    for (int k = comparisonYears; k >= 0; k--) {
                        Map<String, StudentStatistical> studentStatisticalMap = bdStudentStatisticalComparisonDto.getStudentStatisticalMap();
                        //本轮年份
                        int currentRound = year - k;
                        String timeInterval = sf.format(DateUtil.minusYears(studentBeginTime, k)) + " : " + sf.format(DateUtil.minusYears(studentEndTime, k));
                        StudentStatistical studentStatistical = studentStatisticalMap.get(timeInterval);
                        StudentStatistical clone = BeanUtil.clone(studentStatistical);
                        clone.setTargetId(targetId);
                        studentStatisticalTotalMap.put(timeInterval, clone);
                    }
                    bdStudentStatisticalComparisonVoListResult.add(bdStudentStatisticalComparisonDto);
                    //最后一条了 直接统计小计 没有下一循环了
                    if (bdStudentStatisticalComparisonVoList.size() == j + 1) {
                        //计算小计
                        subtotalCalculation(comparisonYears, year, studentStatisticalTotalMap, bdStudentStatisticalComparisonVoListResult, bdStudentStatisticalComparisonSubtotalDto, bdStudentStatisticalComparisonVo.getStatisticalType(), studentBeginTime, studentEndTime);
                    }
                    continue;
                }

                for (int i = 0; i <= comparisonYears; i++) {
                    //本轮年份
                    int currentRound = year - i;
                    String timeInterval = sf.format(DateUtil.minusYears(studentBeginTime, i)) + " : " + sf.format(DateUtil.minusYears(studentEndTime, i));

                    Map<String, StudentStatistical> studentStatisticalMap = bdStudentStatisticalComparisonDto.getStudentStatisticalMap();
                    StudentStatistical studentStatistical = studentStatisticalMap.get(timeInterval);
                    //学生数统计 小计       该TargetId的总计map 取出该年份的数 相加 最终得到该年份的总数
                    StudentStatistical studentStatisticalTotal = studentStatisticalTotalMap.get(timeInterval);
                    if (bdStudentStatisticalComparisonVo.getStatisticalType() == 2 || bdStudentStatisticalComparisonVo.getStatisticalType() == 3 || bdStudentStatisticalComparisonVo.getStatisticalType() == 4) {
                        studentStatisticalTotal.setTotalStudentNum(studentStatisticalTotal.getTotalStudentNum() + studentStatistical.getTotalStudentNum());
                    }
                    if (bdStudentStatisticalComparisonVo.getStatisticalType() == 1 || bdStudentStatisticalComparisonVo.getStatisticalType() == 2 || bdStudentStatisticalComparisonVo.getStatisticalType() == 4 || bdStudentStatisticalComparisonVo.getStatisticalType() == 6 || bdStudentStatisticalComparisonVo.getStatisticalType() == 7 || bdStudentStatisticalComparisonVo.getStatisticalType() == 8) {
                        studentStatisticalTotal.setNewTotalStudentNum(studentStatisticalTotal.getNewTotalStudentNum() + studentStatistical.getNewTotalStudentNum());
                        studentStatisticalTotal.setOldTotalStudentNum(studentStatisticalTotal.getOldTotalStudentNum() + studentStatistical.getOldTotalStudentNum());
                        studentStatisticalTotal.setAppOptStudentNum(studentStatisticalTotal.getAppOptStudentNum() + studentStatistical.getAppOptStudentNum());
                    }
                    if (bdStudentStatisticalComparisonVo.getStatisticalType() == 6 || bdStudentStatisticalComparisonVo.getStatisticalType() == 8) {
                        studentStatisticalTotal.setOldTotalStudentNum(studentStatisticalTotal.getOldTotalStudentNum() + studentStatistical.getOldTotalStudentNum());
                        studentStatisticalTotal.setAdmittedNum(studentStatisticalTotal.getAdmittedNum() + studentStatistical.getAdmittedNum());
                        studentStatisticalTotal.setDelayAdmittedNum(studentStatisticalTotal.getDelayAdmittedNum() + studentStatistical.getDelayAdmittedNum());
                    }
                    studentStatisticalTotal.setOldFirstStudentNum(studentStatisticalTotal.getOldFirstStudentNum() + studentStatistical.getOldFirstStudentNum());
                    studentStatisticalTotal.setNewFirstStudentNum(studentStatisticalTotal.getNewFirstStudentNum() + studentStatistical.getNewFirstStudentNum());
                    studentStatisticalTotal.setOsNum(studentStatisticalTotal.getOsNum() + studentStatistical.getOsNum());
                }

                bdStudentStatisticalComparisonVoListResult.add(bdStudentStatisticalComparisonDto);

                //最后一条了 直接统计小计 没有下一循环了
                if (bdStudentStatisticalComparisonVoList.size() == j + 1) {
                    //计算小计
                    subtotalCalculation(comparisonYears, year, studentStatisticalTotalMap, bdStudentStatisticalComparisonVoListResult, bdStudentStatisticalComparisonSubtotalDto, bdStudentStatisticalComparisonVo.getStatisticalType(), studentBeginTime, studentEndTime);
                }
            }

        } else {
            bdStudentStatisticalComparisonVoListResult = bdStudentStatisticalComparisonVoList;
        }

//        //代理比对特殊排序
//        if (bdStudentStatisticalComparisonVo.getStatisticalType() == 4) {
//            for (BdStudentStatisticalComparisonVo bdStudentStatisticalComparisonDto : bdStudentStatisticalComparisonVoListResult) {
//                Integer totalStudentNum = bdStudentStatisticalComparisonDto.getStudentStatisticalMap().get(year).getTotalStudentNum();
//                bdStudentStatisticalComparisonDto.setAgentSort(totalStudentNum);
//            }
//            bdStudentStatisticalComparisonVoListResult.sort(Collections.reverseOrder(Comparator.comparing(BdStudentStatisticalComparisonVo::getAgentSort)));
//            if (page != null) {
//                // 获取列表总记录数
//                int totalCount = bdStudentStatisticalComparisonVoListResult.size();
//                // 计算总页数
//                int totalPages = (totalCount + page.getShowCount() - 1) / page.getShowCount();
//                // 计算起始记录的下标
//                int startIndex = (page.getCurrentPage() - 1) * page.getShowCount();
//                // 计算结束记录的下标
//                int endIndex = Math.min(startIndex + page.getShowCount(), totalCount);
//                // 如果起始下标大于或等于总记录数，则返回空列表
//                if (startIndex >= totalCount) {
//                    return new ArrayList<>();
//                }
//                page.setAll(totalCount);
//                // 返回子列表
//                bdStudentStatisticalComparisonVoListResult = bdStudentStatisticalComparisonVoListResult.subList(startIndex, endIndex);
//            }
//       }

        return bdStudentStatisticalComparisonVoListResult;
    }

    /**
     * 获取延期统计数配置
     *
     * @Date 14:40 2024/1/4
     * <AUTHOR>
     */
    public DelayConfigDto getDelayConfig() {
        DelayConfigDto delayConfigDto = new DelayConfigDto();
        ConfigVo configVo = permissionCenterClient.getConfigByKey(ProjectKeyEnum.STAFF_COMMISSION_STEP_DELAY_CONFIG.key).getData();
        String configJson = configVo.getValue3();
        JSONObject configJsonObject = JSON.parseObject(configJson);
        //延期提交数配置
        JSONObject stepSubmittedDelay = configJsonObject.getJSONObject("STEP_SUBMITTED_DELAY");
        String submittedStartTime = stepSubmittedDelay.getString("start_time");
        Integer submittedDayInterval = stepSubmittedDelay.getInteger("day_interval");
        //延期反馈数配置
        JSONObject stepAdmittedDelay = configJsonObject.getJSONObject("STEP_ADMITTED_DELAY");
        String admittedStartTime = stepAdmittedDelay.getString("start_time");
        Integer admittedDayInterval = stepAdmittedDelay.getInteger("day_interval");
        List<Long> admittedExemptInstitutionIds = Arrays.stream(stepAdmittedDelay.getString("exempt_institution_ids").split(",")).map(Long::valueOf).collect(Collectors.toList());

        delayConfigDto.setSubmittedDayInterval(submittedDayInterval);
        delayConfigDto.setSubmittedStartTime(submittedStartTime);
        delayConfigDto.setAdmittedDayInterval(admittedDayInterval);
        delayConfigDto.setAdmittedStartTime(admittedStartTime);
        delayConfigDto.setAdmittedExemptInstitutionIds(admittedExemptInstitutionIds);
        return delayConfigDto;
    }


    /**
     * 小计计算
     *
     * @Date 17:16 2023/1/13
     * <AUTHOR>
     */
    private static void subtotalCalculation(Integer comparisonYears, int year, Map<String, StudentStatistical> studentStatisticalTotalMap,
                                            List<BdStudentStatisticalComparisonVo> bdStudentStatisticalComparisonVoListResult, BdStudentStatisticalComparisonVo bdStudentStatisticalComparisonSubtotalDto, Integer statisticalType, Date studentBeginTime, Date studentEndTime) {
        bdStudentStatisticalComparisonSubtotalDto.setStudentStatisticalMap(studentStatisticalTotalMap);
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");

        //年份 - 学生数统计比率
        Map<String, StudentStatisticalRatio> studentStatisticalRatioMap = new HashMap<>();
        for (int i = comparisonYears; i > 0; i--) {
            //本轮年份
            int currentRound = year - i;
            String currentTimeInterval = sf.format(DateUtil.minusYears(studentBeginTime, i)) + " : " + sf.format(DateUtil.minusYears(studentEndTime, i));
            //上一年年份
            int lastYear = currentRound - 1;
            String lastTimeInterval = sf.format(DateUtil.minusYears(studentBeginTime, i - 1)) + " : " + sf.format(DateUtil.minusYears(studentEndTime, i - 1));

            studentStatisticalRatioMap.put(currentTimeInterval + " - " + lastTimeInterval, null);
        }
        //计算上一循环的 年份对比增减比率
        for (int i = 0; i <= comparisonYears; i++) {
            //本轮年份
            int currentRound = year - i;
            String currentTimeInterval = sf.format(DateUtil.minusYears(studentBeginTime, i)) + " : " + sf.format(DateUtil.minusYears(studentEndTime, i));
            //上一年年份
            int lastYear = currentRound - 1;
            String lastTimeInterval = sf.format(DateUtil.minusYears(studentBeginTime, i + 1)) + " : " + sf.format(DateUtil.minusYears(studentEndTime, i + 1));


            //本轮年份统计数
            StudentStatistical studentStatistical = studentStatisticalTotalMap.get(currentTimeInterval);
            //上一年份统计数
            StudentStatistical lastYearStudentStatistical = studentStatisticalTotalMap.get(lastTimeInterval);
            //最后一年份，不需要和后面年份比对了
            if (currentRound == year - comparisonYears) {
                continue;
            }
            //学生数统计比率
            StudentStatisticalRatio studentStatisticalRatio = new StudentStatisticalRatio();
            //学生数增减比率
            if (lastYearStudentStatistical.getOldFirstStudentNum().equals(0)) {
                studentStatisticalRatio.setOldStudentNumRatioNum(new BigDecimal(studentStatistical.getOldFirstStudentNum()));
            } else {
                studentStatisticalRatio.setOldStudentNumRatio(new BigDecimal((studentStatistical.getOldFirstStudentNum() - lastYearStudentStatistical.getOldFirstStudentNum())).divide(new BigDecimal(lastYearStudentStatistical.getOldFirstStudentNum()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
            }
            if (lastYearStudentStatistical.getNewFirstStudentNum().equals(0)) {
                studentStatisticalRatio.setNewStudentNumRatioNum(new BigDecimal(studentStatistical.getNewFirstStudentNum()));
            } else {
                studentStatisticalRatio.setNewStudentNumRatio(new BigDecimal((studentStatistical.getNewFirstStudentNum() - lastYearStudentStatistical.getNewFirstStudentNum())).divide(new BigDecimal(lastYearStudentStatistical.getNewFirstStudentNum()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
            }
            //总学生数增减比率
            if (statisticalType == 2 || statisticalType == 3 || statisticalType == 4) {
                if (lastYearStudentStatistical.getTotalStudentNum().equals(0)) {
                    studentStatisticalRatio.setTotalStudentNum(new BigDecimal(studentStatistical.getTotalStudentNum()));
                } else {
                    studentStatisticalRatio.setTotalStudentNumRatio(new BigDecimal((studentStatistical.getTotalStudentNum() - lastYearStudentStatistical.getTotalStudentNum())).divide(new BigDecimal(lastYearStudentStatistical.getTotalStudentNum()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
                }
            }
            if (statisticalType == 1 || statisticalType == 6 || statisticalType == 7) {
                if (lastYearStudentStatistical.getNewTotalStudentNum().equals(0)) {
                    studentStatisticalRatio.setNewTotalStudentNum(new BigDecimal(studentStatistical.getNewTotalStudentNum()));
                } else {
                    studentStatisticalRatio.setNewTotalStudentNumRatio(new BigDecimal((studentStatistical.getNewTotalStudentNum() - lastYearStudentStatistical.getNewTotalStudentNum())).divide(new BigDecimal(lastYearStudentStatistical.getNewTotalStudentNum()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
                }
                if (lastYearStudentStatistical.getOldTotalStudentNum().equals(0)) {
                    studentStatisticalRatio.setOldTotalStudentNum(new BigDecimal(studentStatistical.getOldTotalStudentNum()));
                } else {
                    studentStatisticalRatio.setOldTotalStudentNumRatio(new BigDecimal((studentStatistical.getOldTotalStudentNum() - lastYearStudentStatistical.getOldTotalStudentNum())).divide(new BigDecimal(lastYearStudentStatistical.getOldTotalStudentNum()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
                }
                if (lastYearStudentStatistical.getAppOptStudentNum().equals(0)) {
                    studentStatisticalRatio.setAppOptStudentNum(new BigDecimal(studentStatistical.getAppOptStudentNum()));
                } else {
                    studentStatisticalRatio.setAppOptStudentNumRatio(new BigDecimal((studentStatistical.getAppOptStudentNum() - lastYearStudentStatistical.getAppOptStudentNum())).divide(new BigDecimal(lastYearStudentStatistical.getAppOptStudentNum()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
                }
            }
            if (statisticalType == 6 || statisticalType == 8) {
                if (lastYearStudentStatistical.getAdmittedNum().equals(0)) {
                    studentStatisticalRatio.setAdmittedRatioNum(new BigDecimal(studentStatistical.getAdmittedNum()));
                } else {
                    studentStatisticalRatio.setAdmittedNumRatio(new BigDecimal((studentStatistical.getAdmittedNum() - lastYearStudentStatistical.getAdmittedNum())).divide(new BigDecimal(lastYearStudentStatistical.getAdmittedNum()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
                }
                if (lastYearStudentStatistical.getDelayAdmittedNum().equals(0)) {
                    studentStatisticalRatio.setAdmittedRatioDelayNum(new BigDecimal(studentStatistical.getDelayAdmittedNum()));
                } else {
                    studentStatisticalRatio.setAdmittedNumDelayRatio(new BigDecimal((studentStatistical.getDelayAdmittedNum() - lastYearStudentStatistical.getDelayAdmittedNum())).divide(new BigDecimal(lastYearStudentStatistical.getDelayAdmittedNum()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
                }
            }
            //os数增减比率
            if (lastYearStudentStatistical.getOsNum().equals(0)) {
                studentStatisticalRatio.setOsNumRatioNum(new BigDecimal(studentStatistical.getOsNum()));
            } else {
                studentStatisticalRatio.setOsNumRatio(new BigDecimal((studentStatistical.getOsNum() - lastYearStudentStatistical.getOsNum())).divide(new BigDecimal(lastYearStudentStatistical.getOsNum()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
            }
            studentStatisticalRatioMap.put(lastTimeInterval + " - " + currentTimeInterval, studentStatisticalRatio);
            bdStudentStatisticalComparisonSubtotalDto.setStudentStatisticalRatioMap(studentStatisticalRatioMap);

        }
        //小计对象
        bdStudentStatisticalComparisonVoListResult.add(bdStudentStatisticalComparisonSubtotalDto);
    }

    @Async
    @Override
    public void downloadBdStudentStatisticalComparison(BdStudentStatisticalComparisonDto data, Map<String, String> headerMap
            , String locale, UserInfo user, List<Long> countryIds, Long staffId) {
        RequestHeaderHandler.setHeaderMap(headerMap);
        StaffDownload download = new StaffDownload();
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
             SXSSFWorkbook workbook = new SXSSFWorkbook()) {
            String description;
            switch (data.getStatisticalType()) {
                case 1:
                    description = "BD学生对比统计表";
                    break;
                case 2:
                    description = "区域学生对比统计表";
                    break;
                case 3:
                    description = "大区学生对比统计表";
                    break;
                case 4:
                    description = "代理学生对比统计表";
                    break;
                case 5:
                    description = "无申请代理列表";
                    break;
                case 6:
                    description = "项目成员学生统计对比列表";
                    break;
                case 8:
                    description = "项目成员学生结算表";
                    break;
                default:
                    throw new GetServiceException(LocaleMessageUtils.getMessage("illegal_type") + data.getStatisticalType());
            }
            download.setFkStaffId(user.getStaffId())
                    .setOptKey(FileTypeEnum.D_LIST_EXPORT.key)
                    .setOptDescription("《" + description + "》")
                    .setStatus(1)
                    .setGmtCreate(new Date());
            download.setGmtCreateUser(user.getLoginId());
            download.setId(permissionCenterClient.addDownloadRecord(download));

            List<BdStudentStatisticalComparisonVo> statisticalComparisonVos = bdStudentStatisticalComparison(data, null, countryIds, staffId);
            if (statisticalComparisonVos.isEmpty()) {
                return;
            }
            if (!data.getStatisticalType().equals(5) && !data.getTotalDisplayModeFlag()) {
                statisticalComparisonVos.addAll(bdStudentStatisticalComparisonTotal(data, countryIds, staffId));
            }
            Map<String, BigDecimal> commissionPolicMap = new HashMap<>();
            String tip = "";
            if (8 == data.getStatisticalType()) {
                List<StaffCommissionPolicyVo> bdStaffCommissionPolicyList = getBdStaffCommissionPolicy(0L, data.getProjectRoleKey());
                //员工提成业务步骤key

                //计算未结算金额 匹配提成规则
                for (StaffCommissionPolicyVo staffCommissionPolicyVo : bdStaffCommissionPolicyList) {
                    commissionPolicMap.put(staffCommissionPolicyVo.getFkStaffCommissionStepKey(), staffCommissionPolicyVo.getFixedAmount());
                }
                Integer settlement = data.getStatusSettlement();
                if (Objects.isNull(settlement)) {
                    tip = "（未结算/已结算）";
                } else {
                    if (0 == settlement) {
                        tip = "（未结算）";
                    }
                    if (1 == settlement) {
                        tip = "（已结算）";
                    }
                }
            }

            SXSSFSheet sheet = workbook.createSheet("学生统计表");
            //封装头数据
            BdStudentStatisticalConditionalEchoVo bdStudentStatisticalConditionalEchoVo = bdStudentStatisticalComparisonConditionalEcho(data);
            setHead(workbook, sheet, statisticalComparisonVos.get(0).getStudentStatisticalMap()
                    , statisticalComparisonVos.get(0).getStudentStatisticalRatioMap(), bdStudentStatisticalConditionalEchoVo,
                    data.getIsDistinguishCountryFlag(), data.getStatisticalType(), commissionPolicMap, tip, description);

            //封装数据
            setData(workbook, sheet, statisticalComparisonVos, data.getIsDistinguishCountryFlag(), data.getTotalDisplayModeFlag(), data.getStatisticalType());

            workbook.write(outputStream);
            MultipartFile multipartFile = FileUtils.getFile(outputStream, "test.xlsx", "xlsx");
            Result<List<FileDto>> upload = fileCenterClient.uploadAppendix(new MultipartFile[]{multipartFile}, LoggerModulesConsts.EXPORT);
            boolean flag = false;
            if (upload.isSuccess()) {
                List<FileDto> uploadData = upload.getData();
                if (uploadData != null && !uploadData.isEmpty()) {
                    FileDto fileDto = uploadData.get(0);
                    if (fileDto.getFileGuid() != null) {
                        download.setStatus(2)
                                .setFkFileGuid(fileDto.getFileGuid());
                        flag = true;
                    }
                }
            }
            if (!flag) {
                download.setStatus(0);
            }
        } catch (Exception e) {
            e.printStackTrace();
            download.setStatus(0);
            download.setRemark(LocaleMessageUtils.getMessage("no_data_search"));
        } finally {
            download.setGmtModifiedUser(user.getLoginId());
            download.setGmtModified(new Date());
            permissionCenterClient.updateDownload(download);
        }
    }

    /**
     * Author Cream
     * Description : //下载代理统计对比表
     * Date 2023/3/22 11:23
     * Params:
     * Return
     */
    @Override
    public void downloadAgentStatisticsComparison(AgentAnnualSummaryDto annualSummaryVo, Map<String, String> headerMap, String locale, UserInfo user, List<Long> countryIds, List<Long> institutionIds) {
        RequestHeaderHandler.setHeaderMap(headerMap);
        StaffDownload download = new StaffDownload();
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
             SXSSFWorkbook workbook = new SXSSFWorkbook()) {
            String description;
            Integer statisticalType = annualSummaryVo.getStatisticalFrom();
            switch (statisticalType) {
                case 1:
                    description = "代理年度总表对比统计";
                    break;
                case 2:
                    description = "代理时间区间对比统计";
                    break;
                default:
                    throw new GetServiceException(LocaleMessageUtils.getMessage("illegal_type") + statisticalType);
            }
            download.setFkStaffId(user.getStaffId())
                    .setOptKey(FileTypeEnum.D_LIST_EXPORT.key)
                    .setOptDescription("《" + description + "》")
                    .setStatus(1)
                    .setGmtCreate(new Date());
            download.setGmtCreateUser(user.getLoginId());
            download.setId(permissionCenterClient.addDownloadRecord(download));
            List<AgentAnnualSummaryStatisticsVo> summaryStatisticsDtos = Collections.emptyList();
            if (statisticalType == 1) {
                summaryStatisticsDtos = agentAnnualSummaryStatistics(annualSummaryVo, countryIds, user.getStaffId(), institutionIds);
            } else {
                summaryStatisticsDtos = agentAnnualIntervalComparisonStatistics(annualSummaryVo, countryIds, user.getStaffId(), institutionIds);
            }
            AgentStatisticalConditionalEchoVo agentStatisticalConditionalEchoVo = agentComparisonConditionalEcho(annualSummaryVo);
            SXSSFSheet sheet = workbook.createSheet(description);
            setHead(workbook, sheet, agentStatisticalConditionalEchoVo, annualSummaryVo, summaryStatisticsDtos.get(0), statisticalType, description);
            setData(workbook, sheet, annualSummaryVo, summaryStatisticsDtos);
            workbook.write(outputStream);
            MultipartFile multipartFile = FileUtils.getFile(outputStream, "test2.xlsx", "xlsx");
            Result<List<FileDto>> upload = fileCenterClient.uploadAppendix(new MultipartFile[]{multipartFile}, LoggerModulesConsts.EXPORT);
            boolean flag = false;
            if (upload.isSuccess()) {
                List<FileDto> uploadData = upload.getData();
                if (uploadData != null && !uploadData.isEmpty()) {
                    FileDto fileDto = uploadData.get(0);
                    if (fileDto.getFileGuid() != null) {
                        download.setStatus(2)
                                .setFkFileGuid(fileDto.getFileGuid());
                        flag = true;
                    }
                }
            }
            if (!flag) {
                download.setStatus(0);
            }
        } catch (Exception e) {
            e.printStackTrace();
            download.setStatus(0);
            download.setRemark(LocaleMessageUtils.getMessage("no_data_search"));
        } finally {
            download.setGmtModifiedUser(user.getLoginId());
            download.setGmtModified(new Date());
            permissionCenterClient.updateDownload(download);
        }
    }

    private void setHead(SXSSFWorkbook workbook, SXSSFSheet sheet, AgentStatisticalConditionalEchoVo agentStatisticalConditionalEchoVo, AgentAnnualSummaryDto annualSummaryVo,
                         AgentAnnualSummaryStatisticsVo agentAnnualSummaryStatisticsVo, Integer statisticalType, String description) {
        //设置表头请求信息数据
        int firstRow = 0;
        int lastRow = 1;
        int weight = 0;
        String typeString = "";
        //是否隐藏小计
        Boolean displayModeFlag = annualSummaryVo.getDisplayModeFlag();
        //是否隐藏总计
        Boolean totalDisplayModeFlag = annualSummaryVo.getTotalDisplayModeFlag();
        Boolean isDistinguishRegionFlag = annualSummaryVo.getIsDistinguishRegionFlag();
        Map<Integer, Map<String, AgentAnnualStatisticsVo>> agentAnnualStatisticsMap = agentAnnualSummaryStatisticsVo.getAgentAnnualStatisticsMap();
        Map<Integer, AgentAnnualStatisticsVo> agentAnnualStatisticsResultMap = agentAnnualSummaryStatisticsVo.getAgentAnnualStatisticsResultMap();
        Map<String, AgentAnnualStatisticsVo> agentAnnualStatisticsRatioMap = agentAnnualSummaryStatisticsVo.getAgentAnnualStatisticsRatioMap();
        switch (statisticalType) {
            case 1:

                weight = 3 + agentAnnualStatisticsMap.size() * 12 - 1;
                if (isDistinguishRegionFlag) {
                    weight += 3;
                }
                if (!displayModeFlag) {
                    weight += agentAnnualStatisticsMap.size();
                }
                if (!totalDisplayModeFlag) {
                    weight += 1;
                }
                break;
            case 2:
                weight = 5 + agentAnnualStatisticsResultMap.size() + agentAnnualStatisticsRatioMap.size() * 2 - 1;
                if (isDistinguishRegionFlag) {
                    weight += 5;
                }
                break;
            default:
        }
        Integer voStatisticalType = annualSummaryVo.getStatisticalType();
        switch (voStatisticalType) {
            case 1:
                typeString = "按代理创建时间统计";
                break;
            case 2:
                typeString = "按首次申请时间统计";
                break;
            default:
        }
        //设置头部信息及筛选条件
        sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, 0, 3));
        CellStyle bg = getLeft(workbook, (short) 14, true);
        SXSSFRow hrow = sheet.createRow(0);
        SXSSFCell hcell = hrow.createCell(0);
        hcell.setCellValue(description);
        hcell.setCellStyle(bg);
        sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, Math.max(weight - 6, 4), weight));
        hcell = hrow.createCell(Math.max(weight - 6, 4));
        hcell.setCellValue("报表生成日期 " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd HH:m:ss")));
        CellStyle right = getRight(workbook, (short) 14, true);
        hcell.setCellStyle(right);
        lastRow += 2;
        CellStyle c3 = getLeft(workbook, (short) 11, true);
        sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, 0, weight));
        hrow = sheet.createRow(lastRow);
        hcell = hrow.createCell(0);
        hcell.setCellValue("筛选条件：");
        hcell.setCellStyle(c3);
        lastRow++;
        sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, 0, weight));
        hrow = sheet.createRow(lastRow);
        hcell = hrow.createCell(0);
        if (StringUtils.isBlank(agentStatisticalConditionalEchoVo.getCompanyNameStr())) {
            hcell.setCellValue("  【所属分公司】 " + "/");
        } else {
            hcell.setCellValue("  【所属分公司】 " + agentStatisticalConditionalEchoVo.getCompanyNameStr());
        }
        hcell.setCellStyle(c3);
        lastRow++;
        sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, 0, weight));
        hrow = sheet.createRow(lastRow);
        hcell = hrow.createCell(0);
        hcell.setCellValue("  【统计方式】 " + typeString);
        hcell.setCellStyle(c3);
        lastRow++;
        sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, 0, weight));
        hrow = sheet.createRow(lastRow);
        hcell = hrow.createCell(0);
        if (StringUtils.isBlank(agentStatisticalConditionalEchoVo.getCreamTimeStr())) {
            hcell.setCellValue("  【代理创建时间】 " + "/");
        } else {
            hcell.setCellValue("  【代理创建时间】 " + agentStatisticalConditionalEchoVo.getCreamTimeStr());
        }
        hcell.setCellStyle(c3);
        lastRow++;
        sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, 0, weight));
        hrow = sheet.createRow(lastRow);
        hcell = hrow.createCell(0);
        if (StringUtils.isBlank(agentStatisticalConditionalEchoVo.getComparisonYearsStr())) {
            hcell.setCellValue("  【同期对比年数】 " + "/");
        } else {
            hcell.setCellValue("  【同期对比年数】 " + agentStatisticalConditionalEchoVo.getComparisonYearsStr());
        }
        hcell.setCellStyle(c3);
        lastRow++;
        sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, 0, weight));
        hrow = sheet.createRow(lastRow);
        hcell = hrow.createCell(0);
        if (StringUtils.isBlank(agentStatisticalConditionalEchoVo.getAreaRegionStr())) {
            hcell.setCellValue("  【BD大区】 " + "/");
        } else {
            hcell.setCellValue("  【BD大区】 " + agentStatisticalConditionalEchoVo.getAreaRegionStr());
        }
        hcell.setCellStyle(c3);
        lastRow++;
        sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, 0, weight));
        hrow = sheet.createRow(lastRow);
        hcell = hrow.createCell(0);
        if (StringUtils.isBlank(agentStatisticalConditionalEchoVo.getAgentAreaStr())) {
            hcell.setCellValue("  【代理所在区域】 " + "/");
        } else {
            hcell.setCellValue("  【代理所在区域】 " + agentStatisticalConditionalEchoVo.getAgentAreaStr());
        }
        hcell.setCellStyle(c3);
        lastRow++;
        sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, 0, weight));
        hrow = sheet.createRow(lastRow);
        hcell = hrow.createCell(0);
        if (StringUtils.isBlank(agentStatisticalConditionalEchoVo.getBdCodeStr())) {
            hcell.setCellValue("  【BD编号】 " + "/");
        } else {
            hcell.setCellValue("  【BD编号】 " + agentStatisticalConditionalEchoVo.getBdCodeStr());
        }
        hcell.setCellStyle(c3);

        //设置初始值 row col 索引从0开始 准备填充表格
        firstRow = lastRow + 2;
        lastRow = firstRow + 1;
        //起始列号
        int firstCol = 0;
        //终止列号
        int lastCol = 2;
        //获取样式
        CellStyle cellStyle = getCellStyle(workbook, (short) 12, true, (short) -1);
        //合并单元格
        sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, firstCol, lastCol));
        //设置背景颜色
        cellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        //颜色填充线条类型
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);


        CellStyle yellow = getCellStyle(workbook, (short) 12, true, (short) -1);
        sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, firstCol, lastCol));
        yellow.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
        yellow.setFillPattern(FillPatternType.SOLID_FOREGROUND);


        CellStyle darkYellow = getCellStyle(workbook, (short) 12, true, (short) -1);
        sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, firstCol, lastCol));
        darkYellow.setFillForegroundColor(IndexedColors.DARK_YELLOW.getIndex());
        darkYellow.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        //单元格，对应row中的一格
        Cell cell;
        SXSSFRow row;
        SXSSFRow row2;
        //创建行
        row = sheet.createRow(firstRow);
        row2 = sheet.createRow(lastRow + 1);
        cell = row.createCell(firstCol);
        cell.setCellValue("BD名称");
        cell.setCellStyle(yellow);
        if (1 == statisticalType) {
            sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow + 1, lastRow + 1, firstCol, lastCol));
            cell = row2.createCell(firstCol);
            cell.setCellValue("月份");
            cell.setCellStyle(cellStyle);
            if (isDistinguishRegionFlag) {
                sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow + 1, lastRow + 1, lastCol + 1, lastCol + 3));
                cell = row2.createCell(lastCol + 1);
                cell.setCellValue("");
                cell.setCellStyle(cellStyle);
            }
        }

        if (isDistinguishRegionFlag) {
            sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, lastCol + 1, lastCol + 3));
            cell = row.createCell(lastCol + 1);
            cell.setCellValue("大区");
            cell.setCellStyle(yellow);
            lastCol += 3;
        }

        int idx = lastCol + 1;
        if (1 == statisticalType) {
            for (Map.Entry<Integer, Map<String, AgentAnnualStatisticsVo>> entry : agentAnnualStatisticsMap.entrySet()) {
                Integer key = entry.getKey();
                Map<String, AgentAnnualStatisticsVo> value = entry.getValue();
                if (!displayModeFlag) {
                    sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, lastCol + 1, lastCol + value.size() + 1));
                } else {
                    sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, lastCol + 1, lastCol + value.size()));
                }
                cell = row.createCell(lastCol + 1);
                cell.setCellValue(String.valueOf(key));
                cell.setCellStyle(yellow);
                for (Map.Entry<String, AgentAnnualStatisticsVo> dtoEntry : value.entrySet()) {
                    if ("小计".equals(dtoEntry.getKey())) {
                        sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow + 1, lastRow + 1, idx, idx + 1));
                        cell = row2.createCell(idx);
                        cell.setCellValue(dtoEntry.getKey());
                        cell.setCellStyle(cellStyle);
                        idx += 2;
                    } else {
                        cell = row2.createCell(idx);
                        cell.setCellValue(dtoEntry.getKey());
                        cell.setCellStyle(cellStyle);
                        idx++;
                    }
                }
                lastCol = idx - 1;
            }
        } else if (2 == statisticalType) {
            SXSSFRow row3 = sheet.createRow(firstRow + 1);
            for (Map.Entry<Integer, AgentAnnualStatisticsVo> entry : agentAnnualStatisticsResultMap.entrySet()) {
                cell = row3.createCell(idx);
                cell.setCellValue("申请总数");
                cell.setCellStyle(yellow);
                cell = row3.createCell(idx + 1);
                cell.setCellValue("通过审核");
                cell.setCellStyle(yellow);
                cell = row3.createCell(idx + 2);
                cell.setCellValue("审核中");
                cell.setCellStyle(yellow);
                cell = row3.createCell(idx + 3);
                cell.setCellValue("拒绝");
                cell.setCellStyle(yellow);
                cell = row3.createCell(idx + 4);
                cell.setCellValue("合同未返回数");
                cell.setCellStyle(yellow);

                Integer key = entry.getKey();
                sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, firstRow, idx, idx + 4));
                cell = row.createCell(idx);
                cell.setCellValue(String.valueOf(key));
                cell.setCellStyle(yellow);
                idx += 5;
            }

            for (Map.Entry<String, AgentAnnualStatisticsVo> dtoEntry : agentAnnualStatisticsRatioMap.entrySet()) {
                sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, idx, idx + 1));
                cell = row.createCell(idx);
                cell.setCellValue(dtoEntry.getKey());
                cell.setCellStyle(yellow);
                idx += 2;
            }
        }
        if (1 == statisticalType && !totalDisplayModeFlag) {
            sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, lastCol + 1, lastCol + 2));
            cell = row.createCell(lastCol + 1);
            cell.setCellValue("");
            cell.setCellStyle(yellow);
            sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow + 1, lastRow + 1, lastCol + 1, lastCol + 2));
            cell = row2.createCell(lastCol + 1);
            cell.setCellValue("总计");
            cell.setCellStyle(darkYellow);
        }
    }

    private void setData(SXSSFWorkbook workbook, SXSSFSheet sheet, AgentAnnualSummaryDto annualSummaryVo, List<AgentAnnualSummaryStatisticsVo> summaryStatisticsDtos) {
        int firstRow = sheet.getLastRowNum();

        Boolean displayModeFlag = annualSummaryVo.getDisplayModeFlag();
        Boolean totalDisplayModeFlag = annualSummaryVo.getTotalDisplayModeFlag();
        Integer statisticalType = annualSummaryVo.getStatisticalFrom();
        if (statisticalType == 1) {
            firstRow += 1;
        }
        int firstCol;
        int lastCol;
        //默认颜色
        CellStyle ordinary = getCellStyle(workbook, (short) 10, false, (short) -1);
        //灰色
        CellStyle grey = getCellStyle(workbook, (short) 10, false, (short) -1);
        grey.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        grey.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        //红色字样式
        CellStyle redFont = getCellStyle(workbook, (short) 10, false, IndexedColors.RED.getIndex());

        //绿色字样式
        CellStyle greenFont = getCellStyle(workbook, (short) 10, false, IndexedColors.GREEN.getIndex());

        //蓝色字样式
        CellStyle blueFont = getCellStyle(workbook, (short) 10, false, IndexedColors.BLUE.getIndex());

        //灰色
        CellStyle darkYellow = getCellStyle(workbook, (short) 10, false, (short) -1);
        darkYellow.setFillForegroundColor(IndexedColors.DARK_YELLOW.getIndex());
        darkYellow.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        SXSSFCell cell;
        SXSSFRow row;
        Boolean isDistinguishRegionFlag = annualSummaryVo.getIsDistinguishRegionFlag();
        for (AgentAnnualSummaryStatisticsVo summaryStatisticsDto : summaryStatisticsDtos) {
            Map<Integer, Map<String, AgentAnnualStatisticsVo>> agentAnnualStatisticsMap = summaryStatisticsDto.getAgentAnnualStatisticsMap();
            if (statisticalType == 1) {
                if (Objects.isNull(agentAnnualStatisticsMap)) {
                    continue;
                }
            }
            firstCol = 0;
            lastCol = 2;
            row = sheet.createRow(firstRow);
            String bdName = summaryStatisticsDto.getBdName();
            String areaRegionName = summaryStatisticsDto.getAreaRegionName();
            if ("总计".equals(bdName)) {
                sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, firstRow, firstCol, lastCol));
                cell = row.createCell(firstCol);
                cell.setCellValue(bdName);
                cell.setCellStyle(darkYellow);
                if (isDistinguishRegionFlag) {
                    sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, firstRow, lastCol + 1, lastCol + 3));
                    cell = row.createCell(lastCol + 1);
                    cell.setCellValue(areaRegionName);
                    cell.setCellStyle(darkYellow);
                    lastCol = 5;
                }
            } else {
                if (isDistinguishRegionFlag) {
                    if ("小计".equals(areaRegionName)) {
                        sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, firstRow, firstCol, 5));
                        cell = row.createCell(firstCol);
                        cell.setCellValue(areaRegionName);
                        cell.setCellStyle(grey);
                    } else {
                        sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, firstRow, firstCol, lastCol));
                        cell = row.createCell(firstCol);
                        cell.setCellValue(bdName);
                        cell.setCellStyle(ordinary);
                        sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, firstRow, lastCol + 1, lastCol + 3));
                        cell = row.createCell(lastCol + 1);
                        cell.setCellValue(areaRegionName);
                        cell.setCellStyle(ordinary);
                    }
                    lastCol = 5;
                } else {
                    sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, firstRow, firstCol, lastCol));
                    cell = row.createCell(firstCol);
                    cell.setCellValue(bdName);
                    cell.setCellStyle(ordinary);
                }
            }
            int idx = lastCol + 1;
            if (1 == statisticalType) {
                for (Map.Entry<Integer, Map<String, AgentAnnualStatisticsVo>> entry : agentAnnualStatisticsMap.entrySet()) {
                    for (Map.Entry<String, AgentAnnualStatisticsVo> dtoEntry : entry.getValue().entrySet()) {
                        AgentAnnualStatisticsVo value = dtoEntry.getValue();
                        if (!displayModeFlag && "小计".equals(dtoEntry.getKey())) {
                            sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, firstRow, idx, idx + 1));
                            cell = row.createCell(idx);
                            cell.setCellValue(String.valueOf(value.getAgentNum()));
                            cell.setCellStyle(ordinary);
                            idx++;
                        } else {
                            cell = row.createCell(idx);
                            if ("小计".equals(areaRegionName) || "总计".equals(bdName)) {
                                setVal(cell, BigDecimal.valueOf(value.getAgentNum()), ordinary);
                            } else {
                                setCell(cell, BigDecimal.valueOf(value.getAgentNum()), redFont, greenFont, blueFont);
                            }
                        }
                        idx++;
                    }
                }
                if (!totalDisplayModeFlag) {
                    sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, firstRow, idx, idx + 1));
                    cell = row.createCell(idx);
                    setVal(cell, summaryStatisticsDto.getAgentTotalNumber(), grey);
                }
            } else if (2 == statisticalType) {
                Map<Integer, AgentAnnualStatisticsVo> statisticsResultMap = summaryStatisticsDto.getAgentAnnualStatisticsResultMap();
                if (Objects.isNull(statisticsResultMap)) {
                    statisticsResultMap = Collections.emptyMap();
                }
                for (Map.Entry<Integer, AgentAnnualStatisticsVo> dtoEntry : statisticsResultMap.entrySet()) {
                    AgentAnnualStatisticsVo value = dtoEntry.getValue();
                    if ("小计".equals(areaRegionName) || "总计".equals(bdName)) {
                        cell = row.createCell(idx);
                        setVal(cell, BigDecimal.valueOf(value.getApplicationsNum()), ordinary);
                        cell = row.createCell(idx + 1);
                        setVal(cell, BigDecimal.valueOf(value.getAgentNum()), ordinary);
                        cell = row.createCell(idx + 2);
                        setVal(cell, BigDecimal.valueOf(value.getInReviewNum()), ordinary);
                        cell = row.createCell(idx + 3);
                        setVal(cell, BigDecimal.valueOf(value.getRefuseNum()), ordinary);
                        cell = row.createCell(idx + 4);
                        setVal(cell, BigDecimal.valueOf(value.getUnsignedNum()), ordinary);
                    } else {
                        cell = row.createCell(idx);
                        setCell(cell, BigDecimal.valueOf(value.getApplicationsNum()), redFont, greenFont, blueFont);
                        cell = row.createCell(idx + 1);
                        setCell(cell, BigDecimal.valueOf(value.getAgentNum()), redFont, greenFont, blueFont);
                        cell = row.createCell(idx + 2);
                        setCell(cell, BigDecimal.valueOf(value.getInReviewNum()), redFont, greenFont, blueFont);
                        cell = row.createCell(idx + 3);
                        setCell(cell, BigDecimal.valueOf(value.getRefuseNum()), redFont, greenFont, blueFont);
                        cell = row.createCell(idx + 4);
                        setCell(cell, BigDecimal.valueOf(value.getUnsignedNum()), redFont, greenFont, blueFont);
                    }
                    idx += 5;
                }
                Map<String, AgentAnnualStatisticsVo> statisticsRatioMap = summaryStatisticsDto.getAgentAnnualStatisticsRatioMap();
                if (Objects.isNull(statisticsRatioMap)) {
                    statisticsRatioMap = Collections.emptyMap();
                }
                for (Map.Entry<String, AgentAnnualStatisticsVo> dtoEntry : statisticsRatioMap.entrySet()) {
                    AgentAnnualStatisticsVo value = dtoEntry.getValue();
                    sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, firstRow, idx, idx + 1));
                    if ("小计".equals(areaRegionName) || "总计".equals(bdName)) {
                        cell = row.createCell(idx);
                        setVal(cell, BigDecimal.valueOf(value.getAgentNum()), ordinary);
                    } else {
                        cell = row.createCell(idx);
                        setCell(cell, BigDecimal.valueOf(value.getAgentNum()), redFont, greenFont, blueFont);
                    }
                    idx += 2;
                }
            }
            firstRow++;
        }
    }

    //封装数据

    /**
     * @param workbook
     * @param sheet
     * @param statisticalComparisonDtos 数据
     * @param isHidden                  是否区分业务国家
     * @param statisticalType           图表类型
     * @description 设置头信息
     */
    private void setData(SXSSFWorkbook workbook, SXSSFSheet sheet, List<BdStudentStatisticalComparisonVo> statisticalComparisonDtos
            , boolean isHidden, boolean isShowTotal, Integer statisticalType) {
        int firstRow = sheet.getLastRowNum() + 1;
        int lastRow = firstRow;
        int firstCol;
        int lastCol;
        CellStyle cellStyle;
        //默认颜色
        CellStyle ordinary = getCellStyle(workbook, (short) 10, false, (short) -1);
        //灰色
        CellStyle grey = getCellStyle(workbook, (short) 10, false, (short) -1);
        grey.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        grey.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        //橙色样式
//        CellStyle orange = getCellStyle(workbook, (short) 10, false, (short) -1);
//        orange.setFillForegroundColor(IndexedColors.ORANGE.getIndex());
//        orange.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        //红色字样式
        CellStyle redFont = getCellStyle(workbook, (short) 10, false, IndexedColors.RED.getIndex());

        //绿色字样式
        CellStyle greenFont = getCellStyle(workbook, (short) 10, false, IndexedColors.GREEN.getIndex());

        //蓝色字样式
        CellStyle blueFont = getCellStyle(workbook, (short) 10, false, IndexedColors.BLUE.getIndex());

        //红字灰色背景样式
        CellStyle redGrey = getCellStyle(workbook, (short) 10, false, IndexedColors.RED.getIndex());
        redGrey.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        redGrey.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        //蓝字灰色背景样式
        CellStyle blueGrey = getCellStyle(workbook, (short) 10, false, IndexedColors.BLUE.getIndex());
        blueGrey.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        blueGrey.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        //蓝字灰色背景样式
        CellStyle greenGrey = getCellStyle(workbook, (short) 10, false, IndexedColors.GREEN.getIndex());
        greenGrey.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        greenGrey.setFillPattern(FillPatternType.SOLID_FOREGROUND);
//        greenGrey<Object> bdTotal = new HashSet<>();
        int weight = 0;
        SXSSFRow row;
        Cell cell;
        String lastName = "";
        for (BdStudentStatisticalComparisonVo comparisonDto : statisticalComparisonDtos) {
            if (Objects.isNull(comparisonDto.getStudentStatisticalRatioMap())) {
                comparisonDto.setStudentStatisticalRatioMap(Collections.emptyMap());
            }
            cellStyle = ordinary;
            //初始化第一列的宽度为0-2、3个长度
            firstCol = 0;
            lastCol = 2;
            sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, firstCol, lastCol));
            row = sheet.createRow(firstRow);
            cell = row.createCell(firstCol);
            if (statisticalType == 4 || statisticalType == 5) {
                cell.setCellValue(comparisonDto.getBdName());
                if ("总计".equals(comparisonDto.getBdName())) {
                    cell.setCellStyle(grey);
                } else {
                    cell.setCellStyle(cellStyle);
                }
                sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, lastCol + 1, lastCol + 3));
                cell = row.createCell(lastCol + 1);
                cell.setCellValue(comparisonDto.getAgentNum());
                cell.setCellStyle(cellStyle);
                lastCol += 3;
                sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, lastCol + 1, lastCol + 3));
                cell = row.createCell(lastCol + 1);
                cell.setCellValue(comparisonDto.getTargetName());
                cell.setCellStyle(cellStyle);
                lastCol += 3;
                sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, lastCol + 1, lastCol + 3));
                cell = row.createCell(lastCol + 1);
                if ("小计".equals(comparisonDto.getAreaCountryName())) {
                    cell.setCellValue(lastName);
                } else {
                    cell.setCellValue(comparisonDto.getRegion());
                }
                if ("总计".equals(comparisonDto.getBdName())) {
                    lastName = comparisonDto.getBdName();
                } else {
//                    if (comparisonDto.getBdName() != null) {
//                        bdTotal.add(comparisonDto.getBdName());
//                    }
                    lastName = comparisonDto.getRegion();
                    cell.setCellStyle(cellStyle);
                }
                if (statisticalType == 5) {
                    //行数自增
                    firstRow++;
                    lastRow++;
                    continue;
                }
                lastCol += 3;
                sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, lastCol + 1, lastCol + 3));
                cell = row.createCell(lastCol + 1);
                if (GeneralTool.isNotEmpty(comparisonDto.getAgentLabelVos())) {
                    comparisonDto.setAgentLabelNames(
                            comparisonDto.getAgentLabelVos().stream()
                                    // 给每个标签名称包裹【】符号
                                    .map(vo -> "【" + vo.getLabelName() + "】")
                                    // 用逗号拼接所有带符号的标签
                                    .collect(Collectors.joining(","))
                    );
                }
                cell.setCellValue(comparisonDto.getAgentLabelNames());
                cell.setCellStyle(cellStyle);
                lastCol += 3;
            } else if (statisticalType == 1) {
                if ("小计".equals(comparisonDto.getAreaCountryName())) {
                    cell.setCellValue(lastName);
                } else {
                    cell.setCellValue(comparisonDto.getBdName());
                }
//            sheet.getRow(sheet.getLastRowNum()).getCell(firstCol).getStringCellValue()
                if ("总计".equals(comparisonDto.getBdName())) {
                    lastName = comparisonDto.getBdName();
                    cell.setCellStyle(grey);
                } else {
//                    if (comparisonDto.getTargetId()!=null) {
//                        bdTotal.add(comparisonDto.getTargetId());
//                    }
                    lastName = comparisonDto.getBdName();
                    cell.setCellStyle(cellStyle);
                }
            } else if (statisticalType == 2 || statisticalType == 3) {
                if ("小计".equals(comparisonDto.getAreaCountryName())) {
                    cell.setCellValue(lastName);
                } else {
                    cell.setCellValue(comparisonDto.getRegion());
                }
                if ("总计".equals(comparisonDto.getRegion())) {
                    lastName = comparisonDto.getRegion();
                    cell.setCellStyle(grey);
                } else {
//                    if (comparisonDto.getRegion()!=null) {
//                        bdTotal.add(comparisonDto.getRegion());
//                    }
                    lastName = comparisonDto.getRegion();
                    cell.setCellStyle(cellStyle);
                }
            } else if (statisticalType == 6) {
                cell.setCellValue(comparisonDto.getRoleStaffName());
                if ("总计".equals(comparisonDto.getRoleStaffName())) {
                    lastName = comparisonDto.getRoleStaffName();
                    cell.setCellStyle(grey);
                } else {
                    lastName = comparisonDto.getRoleStaffName();
                    cell.setCellStyle(cellStyle);
                }
            } else if (statisticalType == 8) {
                cell.setCellValue(comparisonDto.getRoleStaffName());
                if ("总计".equals(comparisonDto.getRoleStaffName())) {
                    cell.setCellStyle(grey);
                } else {
                    cell.setCellStyle(cellStyle);
                }
            }

            //第二列位置3-（2+3）宽度3个长度（列）
            if (isHidden) {
                sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, lastCol + 1, lastCol + 3));
                cell = row.createCell(lastCol + 1);
                cell.setCellValue(comparisonDto.getAreaCountryName());
                if ("小计".equals(comparisonDto.getAreaCountryName())) {
                    cell.setCellStyle(grey);
                    cellStyle = grey;
                } else {
                    cell.setCellStyle(cellStyle);
                }
                //获取当前最后一列的位置
                lastCol += 3;
            }
            //步进初始值1
            int idx = 1;
            if (Objects.isNull(comparisonDto.getStudentStatisticalMap())) {
                comparisonDto.setStudentStatisticalMap(Collections.emptyMap());
            }
            //此处map数据只需要步进1个单元格
            for (Map.Entry<String, StudentStatistical> entry : comparisonDto.getStudentStatisticalMap().entrySet()) {
                StudentStatistical statistical = entry.getValue();
                if (GeneralTool.isEmpty(statistical)) {
                    continue;
                }
                //项目成员
                if (statisticalType == 6) {
//                    cell = row.createCell(lastCol + idx);
//                    setVal(cell, statistical.getOldFirstStudentNum(), cellStyle);
//                    idx++;
//                    cell = row.createCell(lastCol + idx);
//                    setVal(cell, statistical.getNewFirstStudentNum(), cellStyle);
//                    idx++;
                    cell = row.createCell(lastCol + idx);
                    setVal(cell, statistical.getOldTotalStudentNum(), cellStyle);
                    idx++;
                    cell = row.createCell(lastCol + idx);
                    setVal(cell, statistical.getNewTotalStudentNum(), cellStyle);
                    idx++;
                    cell = row.createCell(lastCol + idx);
                    setVal(cell, statistical.getItemNum(), cellStyle);
                    idx++;
                    cell = row.createCell(lastCol + idx);
                    setVal(cell, statistical.getDelayItemNum(), cellStyle);
                    idx++;
                    cell = row.createCell(lastCol + idx);
                    setVal(cell, statistical.getAdmittedNum(), cellStyle);
                    idx++;
                    cell = row.createCell(lastCol + idx);
                    setVal(cell, statistical.getDelayAdmittedNum(), cellStyle);
                    idx++;
                    cell = row.createCell(lastCol + idx);
                    setVal(cell, statistical.getOsNum(), cellStyle);
                    idx++;
                    cell = row.createCell(lastCol + idx);
                    setVal(cell, statistical.getVisaNum(), cellStyle);
                    idx++;
                    cell = row.createCell(lastCol + idx);
                    setVal(cell, statistical.getEnrolledNum(), cellStyle);
                    idx++;
                } else if (statisticalType == 1) {
                    cell = row.createCell(lastCol + idx);
                    setVal(cell, statistical.getOldFirstStudentNum(), cellStyle);
                    idx++;
                    cell = row.createCell(lastCol + idx);
                    setVal(cell, statistical.getNewFirstStudentNum(), cellStyle);
                    idx++;
                    cell = row.createCell(lastCol + idx);
                    setVal(cell, statistical.getOldTotalStudentNum(), cellStyle);
                    idx++;
                    cell = row.createCell(lastCol + idx);
                    setVal(cell, statistical.getNewTotalStudentNum(), cellStyle);
                    idx++;
                    cell = row.createCell(lastCol + idx);
                    setVal(cell, statistical.getOsNum(), cellStyle);
                    idx++;
                    cell = row.createCell(lastCol + idx);
                    setVal(cell, statistical.getAppOptStudentNum(), cellStyle);
                    idx++;
                } else if (statisticalType == 3) {
                    cell = row.createCell(lastCol + idx);
                    setVal(cell, statistical.getOldFirstStudentNum(), cellStyle);
                    idx++;
                    cell = row.createCell(lastCol + idx);
                    setVal(cell, statistical.getNewFirstStudentNum(), cellStyle);
                    idx++;
                    cell = row.createCell(lastCol + idx);
                    setVal(cell, statistical.getOldTotalStudentNum(), cellStyle);
                    idx++;
                    cell = row.createCell(lastCol + idx);
                    setVal(cell, statistical.getNewTotalStudentNum(), cellStyle);
                    idx++;
                    cell = row.createCell(lastCol + idx);
                    setVal(cell, statistical.getOsNum(), cellStyle);
                    idx++;
                } else if (statisticalType == 8) {
//                    sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, lastCol + 1, lastCol + 2));
//                    cell = row.createCell(lastCol + 1);
//                    setVal(cell, statistical.getOldFirstStudentNum(), cellStyle);
//                    lastCol += 2;
//                    sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, lastCol + 1, lastCol + 2));
//                    cell = row.createCell(lastCol + 1);
//                    setVal(cell, statistical.getNewFirstStudentNum(), cellStyle);
//                    lastCol += 2;
                    sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, lastCol + 1, lastCol + 2));
                    cell = row.createCell(lastCol + 1);
                    setVal(cell, statistical.getOldTotalStudentNum(), cellStyle);
                    lastCol += 2;
                    sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, lastCol + 1, lastCol + 2));
                    cell = row.createCell(lastCol + 1);
                    setVal(cell, statistical.getNewTotalStudentNum(), cellStyle);
                    lastCol += 2;
                    sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, lastCol + 1, lastCol + 2));
                    cell = row.createCell(lastCol + 1);
                    setVal(cell, statistical.getCommissionActionItemNumStr(), cellStyle);
                    lastCol += 2;
                    sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, lastCol + 1, lastCol + 2));
                    cell = row.createCell(lastCol + 1);
                    setVal(cell, statistical.getCommissionActionDelayItemNumStr(), cellStyle);
                    lastCol += 2;
                    sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, lastCol + 1, lastCol + 2));
                    cell = row.createCell(lastCol + 1);
                    setVal(cell, statistical.getCommissionActionAdmittedNumStr(), cellStyle);
                    lastCol += 2;
                    sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, lastCol + 1, lastCol + 2));
                    cell = row.createCell(lastCol + 1);
                    setVal(cell, statistical.getCommissionActionAdmittedDelayNumStr(), cellStyle);
                    lastCol += 2;
                    sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, lastCol + 1, lastCol + 2));
                    cell = row.createCell(lastCol + 1);
                    setVal(cell, statistical.getCommissionActionOsNumStr(), cellStyle);
                    lastCol += 2;
                    sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, lastCol + 1, lastCol + 2));
                    cell = row.createCell(lastCol + 1);
                    setVal(cell, statistical.getCommissionActionVisaNumStr(), cellStyle);
                    lastCol += 2;
                    sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, lastCol + 1, lastCol + 2));
                    cell = row.createCell(lastCol + 1);
                    setVal(cell, statistical.getCommissionActionEnrolledNumStr(), cellStyle);
                    lastCol += 2;
                    sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, lastCol + 1, lastCol + 2));
                    cell = row.createCell(lastCol + 1);
                    setVal(cell, statistical.getCommissionActionEnrolledVisaNumStr(), cellStyle);
                    lastCol += 2;
                    sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, lastCol + 1, lastCol + 2));
                    cell = row.createCell(lastCol + 1);
                    setVal(cell, statistical.getCommissionActionAmount(), cellStyle);
                    lastCol += 2;
                } else if (statisticalType == 2 || statisticalType == 4) {
                    cell = row.createCell(lastCol + idx);
                    setVal(cell, statistical.getOldFirstStudentNum(), cellStyle);
                    idx++;
                    cell = row.createCell(lastCol + idx);
                    setVal(cell, statistical.getNewFirstStudentNum(), cellStyle);
                    idx++;
                    cell = row.createCell(lastCol + idx);
                    setVal(cell, statistical.getOldTotalStudentNum(), cellStyle);
                    idx++;
                    cell = row.createCell(lastCol + idx);
                    setVal(cell, statistical.getNewTotalStudentNum(), cellStyle);
                    idx++;
                    cell = row.createCell(lastCol + idx);
                    setVal(cell, statistical.getOsNum(), cellStyle);
                    idx++;
                } else {
                    cell = row.createCell(lastCol + idx);
                    setVal(cell, statistical.getTotalStudentNum(), cellStyle);
                    idx++;
                    cell = row.createCell(lastCol + idx);
                    setVal(cell, statistical.getOsNum(), cellStyle);
                    idx++;
                }

            }
            //获取最后一列位置
            lastCol = lastCol + idx - 1;
            //此处map数据需要步进2个单元格
            if (Objects.isNull(comparisonDto.getStudentStatisticalRatioMap())) {
                comparisonDto.setStudentStatisticalRatioMap(Collections.emptyMap());
            }
            for (Map.Entry<String, StudentStatisticalRatio> ratioEntry : comparisonDto.getStudentStatisticalRatioMap().entrySet()) {
                StudentStatisticalRatio value = ratioEntry.getValue();
                if (statisticalType == 6) {
//                    sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, lastCol + 1, lastCol + 2));
//                    cell = row.createCell(lastCol + 1);
//                    setCell(cell, value.getOldStudentNumRatio(), value.getOldStudentNumRatioNum(), blueFont, redFont, greenFont);
//                    lastCol += 2;
//                    sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, lastCol + 1, lastCol + 2));
//                    cell = row.createCell(lastCol + 1);
//                    setCell(cell, value.getNewStudentNumRatio(), value.getNewStudentNumRatioNum(), blueFont, redFont, greenFont);
//                    lastCol += 2;

                    sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, lastCol + 1, lastCol + 2));
                    cell = row.createCell(lastCol + 1);
                    setCell(cell, value.getOldTotalStudentNumRatio(), value.getOldTotalStudentNum(), blueFont, redFont, greenFont);
                    lastCol += 2;

                    sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, lastCol + 1, lastCol + 2));
                    cell = row.createCell(lastCol + 1);
                    setCell(cell, value.getNewTotalStudentNumRatio(), value.getNewTotalStudentNum(), blueFont, redFont, greenFont);
                    lastCol += 2;

                    sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, lastCol + 1, lastCol + 2));
                    if (weight == 0) {
                        weight = lastCol + 2;
                    }
                    cell = row.createCell(lastCol + 1);
                    setCell(cell, value.getItemNumRatio(), value.getItemNumRatioNum(), blueFont, redFont, greenFont);
                    lastCol += 2;

                    sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, lastCol + 1, lastCol + 2));
                    cell = row.createCell(lastCol + 1);
                    setCell(cell, value.getItemNumDelayRatio(), value.getItemNumRatioDelayNum(), blueFont, redFont, greenFont);
                    lastCol += 2;

                    sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, lastCol + 1, lastCol + 2));
                    cell = row.createCell(lastCol + 1);
                    setCell(cell, value.getAdmittedNumRatio(), value.getAdmittedRatioNum(), blueFont, redFont, greenFont);
                    lastCol += 2;

                    sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, lastCol + 1, lastCol + 2));
                    cell = row.createCell(lastCol + 1);
                    setCell(cell, value.getAdmittedNumDelayRatio(), value.getAdmittedRatioDelayNum(), blueFont, redFont, greenFont);
                    lastCol += 2;

                    sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, lastCol + 1, lastCol + 2));
                    cell = row.createCell(lastCol + 1);
                    setCell(cell, value.getOsNumRatio(), value.getOsNumRatioNum(), blueFont, redFont, greenFont);

                    lastCol += 2;
                    sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, lastCol + 1, lastCol + 2));
                    cell = row.createCell(lastCol + 1);
                    setCell(cell, value.getVisaNumRatio(), value.getVisaNumRatioNum(), blueFont, redFont, greenFont);

                    lastCol += 2;
                    sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, lastCol + 1, lastCol + 2));
                    cell = row.createCell(lastCol + 1);
                    setCell(cell, value.getEnrolledNumRatio(), value.getEnrolledNumRatioNum(), blueFont, redFont, greenFont);

                    lastCol += 2;
                } else if (statisticalType == 1) {
                    sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, lastCol + 1, lastCol + 2));
                    cell = row.createCell(lastCol + 1);
                    setCell(cell, value.getOldStudentNumRatio(), value.getOldStudentNumRatioNum(), blueGrey, greenGrey, comparisonDto.getAreaCountryName(), redGrey, redFont, greenFont, blueFont);
                    lastCol += 2;
                    sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, lastCol + 1, lastCol + 2));
                    cell = row.createCell(lastCol + 1);
                    setCell(cell, value.getNewStudentNumRatio(), value.getNewStudentNumRatioNum(), blueGrey, greenGrey, comparisonDto.getAreaCountryName(), redGrey, redFont, greenFont, blueFont);
                    lastCol += 2;
                    sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, lastCol + 1, lastCol + 2));
                    cell = row.createCell(lastCol + 1);
                    setCell(cell, value.getOldTotalStudentNumRatio(), value.getOldTotalStudentNum(), blueGrey, greenGrey, comparisonDto.getAreaCountryName(), redGrey, redFont, greenFont, blueFont);
                    lastCol += 2;
                    sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, lastCol + 1, lastCol + 2));
                    cell = row.createCell(lastCol + 1);
                    setCell(cell, value.getNewTotalStudentNumRatio(), value.getNewTotalStudentNum(), blueGrey, greenGrey, comparisonDto.getAreaCountryName(), redGrey, redFont, greenFont, blueFont);
                    lastCol += 2;
                    sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, lastCol + 1, lastCol + 2));
                    cell = row.createCell(lastCol + 1);
                    setCell(cell, value.getOsNumRatio(), value.getOsNumRatioNum(), blueGrey, greenGrey, comparisonDto.getAreaCountryName(), redGrey, redFont, greenFont, blueFont);
                    lastCol += 2;
                    sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, lastCol + 1, lastCol + 2));
                    cell = row.createCell(lastCol + 1);
                    setCell(cell, value.getAppOptStudentNumRatio(), value.getAppOptStudentNum(), blueGrey, greenGrey, comparisonDto.getAreaCountryName(), redGrey, redFont, greenFont, blueFont);
                    lastCol += 2;
                } else if (statisticalType == 2 || statisticalType == 3 || statisticalType == 4) {
                    sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, lastCol + 1, lastCol + 2));
                    cell = row.createCell(lastCol + 1);
                    setCell(cell, value.getOldStudentNumRatio(), value.getOldStudentNumRatioNum(), blueGrey, greenGrey, comparisonDto.getAreaCountryName(), redGrey, redFont, greenFont, blueFont);
                    lastCol += 2;
                    sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, lastCol + 1, lastCol + 2));
                    cell = row.createCell(lastCol + 1);
                    setCell(cell, value.getNewStudentNumRatio(), value.getNewStudentNumRatioNum(), blueGrey, greenGrey, comparisonDto.getAreaCountryName(), redGrey, redFont, greenFont, blueFont);
                    lastCol += 2;
                    sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, lastCol + 1, lastCol + 2));
                    cell = row.createCell(lastCol + 1);
                    setCell(cell, value.getOldTotalStudentNumRatio(), value.getOldTotalStudentNum(), blueGrey, greenGrey, comparisonDto.getAreaCountryName(), redGrey, redFont, greenFont, blueFont);
                    lastCol += 2;
                    sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, lastCol + 1, lastCol + 2));
                    cell = row.createCell(lastCol + 1);
                    setCell(cell, value.getNewTotalStudentNumRatio(), value.getNewTotalStudentNum(), blueGrey, greenGrey, comparisonDto.getAreaCountryName(), redGrey, redFont, greenFont, blueFont);
                    lastCol += 2;
                    sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, lastCol + 1, lastCol + 2));
                    cell = row.createCell(lastCol + 1);
                    setCell(cell, value.getOsNumRatio(), value.getOsNumRatioNum(), blueGrey, greenGrey, comparisonDto.getAreaCountryName(), redGrey, redFont, greenFont, blueFont);
                    lastCol += 2;
                } else {
                    sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, lastCol + 1, lastCol + 2));
                    cell = row.createCell(lastCol + 1);
                    setCell(cell, value.getTotalStudentNumRatio(), value.getTotalStudentNum(), blueGrey, greenGrey, comparisonDto.getAreaCountryName(), redGrey, redFont, greenFont, blueFont);
                    lastCol += 2;
                    sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, lastCol + 1, lastCol + 2));
                    cell = row.createCell(lastCol + 1);
                    setCell(cell, value.getOsNumRatio(), value.getOsNumRatioNum(), blueGrey, greenGrey, comparisonDto.getAreaCountryName(), redGrey, redFont, greenFont, blueFont);
                    lastCol += 2;
                }
            }
            //行数自增
            firstRow++;
            lastRow++;
        }
//        if (!isShowTotal && statisticalType==1) {
//            sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, 0, 2));
//            SXSSFRow last = sheet.createRow(lastRow);
//            CellStyle lg = getCellStyle(workbook, (short) 10, false, (short) -1);
//            lg.setFillForegroundColor(IndexedColors.GREY_40_PERCENT.getIndex());
//            lg.setFillPattern(FillPatternType.SOLID_FOREGROUND);
//            cell = last.createCell(0);
//            cell.setCellValue("BD共" + bdTotal.size() + "个");
//            cell.setCellStyle(lg);
//            sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, 3, weight));
//            cell = last.createCell(3);
//            cell.setCellStyle(lg);
//        }
    }

    private void setCell(Cell cell, BigDecimal ratio, BigDecimal ratioNum, CellStyle blueFont, CellStyle redFont,
                         CellStyle greenFont) {
        if (Objects.isNull(ratio)) {
            ratioNum = DataConverter.bigDecimalNullConvert(ratioNum);
            cell.setCellValue(ratioNum.toString());
            if (BigDecimal.ZERO.compareTo(ratioNum) == 0) {
                cell.setCellStyle(blueFont);
            } else {
                cell.setCellStyle(redFont);
            }
        } else {
            cell.setCellValue(ratio + "%");
            if (ratio.compareTo(BigDecimal.ZERO) < 0) {
                cell.setCellStyle(greenFont);
            } else {
                cell.setCellStyle(redFont);
            }
        }
    }

    private void setVal(Cell cell, Object obj, CellStyle cellStyle) {
        if (Objects.isNull(obj)) {
            cell.setCellValue("");
        } else {
            cell.setCellValue(obj.toString());
        }
        cell.setCellStyle(cellStyle);
    }

    private void setCell(Cell cell, BigDecimal num, CellStyle redFont, CellStyle greenFont, CellStyle blueFont) {
        num = DataConverter.bigDecimalNullConvert(num);
        cell.setCellValue(num.toString());
        if (BigDecimal.ZERO.compareTo(num) == 0) {
            cell.setCellStyle(blueFont);
        } else if (BigDecimal.ZERO.compareTo(num) > 0) {
            cell.setCellStyle(greenFont);
        } else {
            cell.setCellStyle(redFont);
        }
    }

    private void setCell(Cell cell, BigDecimal ratio, BigDecimal ratioNum, CellStyle blueGrey,
                         CellStyle greenGrey, String keyName, CellStyle redGrey, CellStyle redFont, CellStyle greenFont, CellStyle blueFont) {
        if ("小计".equals(keyName)) {
            if (Objects.isNull(ratio)) {
                ratioNum = DataConverter.bigDecimalNullConvert(ratioNum);
                cell.setCellValue(ratioNum.toString());
                if (BigDecimal.ZERO.compareTo(ratioNum) == 0) {
                    cell.setCellStyle(blueGrey);
                } else {
                    cell.setCellStyle(redGrey);
                }
            } else {
                cell.setCellValue(ratio + "%");
                if (ratio.compareTo(BigDecimal.ZERO) < 0) {
                    cell.setCellStyle(greenGrey);
                } else {
                    cell.setCellStyle(redGrey);
                }
            }
        } else {
            if (Objects.isNull(ratio)) {
                ratioNum = DataConverter.bigDecimalNullConvert(ratioNum);
                cell.setCellValue(ratioNum.toString());
                if (BigDecimal.ZERO.compareTo(ratioNum) == 0) {
                    cell.setCellStyle(blueFont);
                } else {
                    cell.setCellStyle(redFont);
                }
            } else {
                cell.setCellValue(ratio + "%");
                if (ratio.compareTo(BigDecimal.ZERO) < 0) {
                    cell.setCellStyle(greenFont);
                } else {
                    cell.setCellStyle(redFont);
                }
            }
        }

    }


    /**
     * @param workbook
     * @param sheet
     * @param studentStatisticalMap                 统计个数
     * @param studentStatisticalRatioMap            统计比率
     * @param bdStudentStatisticalConditionalEchoVo 筛选条件数据
     * @param isHidden                              是否区分业务国家
     * @param statisticalType                       图表类型
     * @description 设置头信息
     */
    private void setHead(SXSSFWorkbook workbook, SXSSFSheet sheet, Map<String, StudentStatistical> studentStatisticalMap
            , Map<String, StudentStatisticalRatio> studentStatisticalRatioMap
            , BdStudentStatisticalConditionalEchoVo bdStudentStatisticalConditionalEchoVo, boolean isHidden,
                         Integer statisticalType, Map<String, BigDecimal> commissionPolicMap, String tip, String description) {
        if (Objects.isNull(studentStatisticalRatioMap)) {
            studentStatisticalRatioMap = Collections.emptyMap();
        }
        //设置表头请求信息数据
        int firstRow = 0;
        int lastRow = 1;
        int weight;
        if (statisticalType == 4) {
            //前四列固定长度 + 个数map*2 + 比率map*4 -1 因为索引从0开始
            weight = 15 + studentStatisticalMap.size() * 5 + studentStatisticalRatioMap.size() * 10 - 1;
        } else if (statisticalType == 5) {
            weight = 11;
        } else if (statisticalType == 6) {
            weight = 3 + studentStatisticalMap.size() * 9 + studentStatisticalRatioMap.size() * 18 - 1;
        } else if (statisticalType == 8) {
            weight = 3 + studentStatisticalMap.size() * 9 * 2 - 1;
        } else if (statisticalType == 1) {
            weight = 3 + studentStatisticalMap.size() * 6 + studentStatisticalRatioMap.size() * 12 - 1;
        } else if (statisticalType == 2 || statisticalType == 3) {
            weight = 3 + studentStatisticalMap.size() * 5 + studentStatisticalRatioMap.size() * 10 - 1;
        } else {
            weight = 3 + studentStatisticalMap.size() * 2 + studentStatisticalRatioMap.size() * 4 - 1;
        }
        //判断是否要增加业务国家
        if (statisticalType != 5 && isHidden) {
            weight += 3;
        }
        sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, 0, 3));
        CellStyle bg = getLeft(workbook, (short) 14, true);
        SXSSFRow hrow = sheet.createRow(0);
        SXSSFCell hcell = hrow.createCell(0);
        hcell.setCellValue(description);
        hcell.setCellStyle(bg);
        sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, Math.max(weight - 6, 4), weight));
        hcell = hrow.createCell(Math.max(weight - 6, 4));
        hcell.setCellValue("报表生成日期 " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd HH:m:ss")));
        CellStyle right;
        if (statisticalType == 6) {
            right = getRight(workbook, (short) 9, true);
        } else {
            right = getRight(workbook, (short) 14, true);
        }
        hcell.setCellStyle(right);
        lastRow += 2;
        CellStyle c3 = getLeft(workbook, (short) 11, true);
        sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, 0, weight));
        hrow = sheet.createRow(lastRow);
        hcell = hrow.createCell(0);
        hcell.setCellValue("筛选条件：");
        hcell.setCellStyle(c3);
        lastRow++;
        sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, 0, weight));
        hrow = sheet.createRow(lastRow);
        hcell = hrow.createCell(0);
        if (StringUtils.isBlank(bdStudentStatisticalConditionalEchoVo.getCompanyNameStr())) {
            hcell.setCellValue("  【所属分公司】 " + "/");
        } else {
            hcell.setCellValue("  【所属分公司】 " + bdStudentStatisticalConditionalEchoVo.getCompanyNameStr());
        }
        hcell.setCellStyle(c3);
        lastRow++;
        sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, 0, weight));
        hrow = sheet.createRow(lastRow);
        hcell = hrow.createCell(0);
        if (StringUtils.isBlank(bdStudentStatisticalConditionalEchoVo.getCreamTimeStr())) {
            hcell.setCellValue("  【申请计划创建时间】 " + "/");
        } else {
            hcell.setCellValue("  【申请计划创建时间】 " + bdStudentStatisticalConditionalEchoVo.getCreamTimeStr());
        }
        hcell.setCellStyle(c3);
        lastRow++;
        sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, 0, weight));
        hrow = sheet.createRow(lastRow);
        hcell = hrow.createCell(0);
        if (StringUtils.isBlank(bdStudentStatisticalConditionalEchoVo.getComparisonYearsStr())) {
            hcell.setCellValue("  【同期对比年数】 " + "/");
        } else {
            hcell.setCellValue("  【同期对比年数】 " + bdStudentStatisticalConditionalEchoVo.getComparisonYearsStr());
        }
        hcell.setCellStyle(c3);
        lastRow++;
        if (statisticalType == 6 || statisticalType == 8) {
            sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, 0, weight));
            hrow = sheet.createRow(lastRow);
            hcell = hrow.createCell(0);
            if (StringUtils.isBlank(bdStudentStatisticalConditionalEchoVo.getProjectRoleName())) {
                hcell.setCellValue("  【项目成员角色】 " + "/");
            } else {
                hcell.setCellValue("  【项目成员角色】 " + bdStudentStatisticalConditionalEchoVo.getProjectRoleName());
            }
            hcell.setCellStyle(c3);
            lastRow++;
            sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, 0, weight));
            hrow = sheet.createRow(lastRow);
            hcell = hrow.createCell(0);
            if (GeneralTool.isEmpty(bdStudentStatisticalConditionalEchoVo.getStaffName())) {
                hcell.setCellValue("  【角色名称】 " + "/");
            } else {
                hcell.setCellValue("  【角色名称】 " + bdStudentStatisticalConditionalEchoVo.getStaffName());
            }
        } else {
            sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow + 1, 0, weight));
            hrow = sheet.createRow(lastRow);
            hcell = hrow.createCell(0);
            if (StringUtils.isBlank(bdStudentStatisticalConditionalEchoVo.getCountryStr())) {
                hcell.setCellValue("  【业务国家】 " + "/");
            } else {
                hcell.setCellValue("  【业务国家】 " + bdStudentStatisticalConditionalEchoVo.getCountryStr());
            }
            hcell.setCellStyle(c3);
            lastRow += 2;
            sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, 0, weight));
            hrow = sheet.createRow(lastRow);
            hcell = hrow.createCell(0);
            if (StringUtils.isBlank(bdStudentStatisticalConditionalEchoVo.getAgentAreaStr())) {
                hcell.setCellValue("  【代理所在区域】 " + "/");
            } else {
                hcell.setCellValue("  【代理所在区域】 " + bdStudentStatisticalConditionalEchoVo.getAgentAreaStr());
            }
            hcell.setCellStyle(c3);
            lastRow++;
            sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, 0, weight));
            hrow = sheet.createRow(lastRow);
            hcell = hrow.createCell(0);
            if (StringUtils.isBlank(bdStudentStatisticalConditionalEchoVo.getAreaRegionStr())) {
                hcell.setCellValue("  【BD大区】 " + "/");
            } else {
                hcell.setCellValue("  【BD大区】 " + bdStudentStatisticalConditionalEchoVo.getAreaRegionStr());
            }
            hcell.setCellStyle(c3);
            lastRow++;
            sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, 0, weight));
            hrow = sheet.createRow(lastRow);
            hcell = hrow.createCell(0);
            if (StringUtils.isBlank(bdStudentStatisticalConditionalEchoVo.getBdCodeStr())) {
                hcell.setCellValue("  【BD编号】 " + "/");
            } else {
                hcell.setCellValue("  【BD编号】 " + bdStudentStatisticalConditionalEchoVo.getBdCodeStr());
            }
            hcell.setCellStyle(c3);
            lastRow++;
            sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, 0, weight));
            hrow = sheet.createRow(lastRow);
            hcell = hrow.createCell(0);
            if (StringUtils.isBlank(bdStudentStatisticalConditionalEchoVo.getAgentNumStr())) {
                hcell.setCellValue("  【搜索代理编号】 " + "/");
            } else {
                hcell.setCellValue("  【搜索代理编号】 " + bdStudentStatisticalConditionalEchoVo.getAgentNumStr());
            }
        }
        hcell.setCellStyle(c3);

        //设置初始值 row col 索引从0开始
        firstRow = lastRow + 2;
        lastRow = firstRow + 1;
        int firstCol = 0;
        int lastCol = 2;
        //获取样式
        CellStyle cellStyle = getCellStyle(workbook, (short) 12, true, (short) -1);
        //合并单元格
        sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, firstCol, lastCol));
        //设置背景颜色
        cellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        //颜色填充线条类型
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Cell cell;
        SXSSFRow row;
        SXSSFRow row2;
        //创建行
        row = sheet.createRow(firstRow);
        row2 = sheet.createRow(lastRow);
        cell = row.createCell(firstCol);
        //设置cell的值。每次设置都需要添加cell样式
        if (statisticalType == 4 || statisticalType == 5) {
            cell.setCellValue("BD编号");
            cell.setCellStyle(cellStyle);
            sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, lastCol + 1, lastCol + 3));
            cell = row.createCell(lastCol + 1);
            cell.setCellValue("Subagent编号");
            cell.setCellStyle(cellStyle);
            lastCol += 3;
            sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, lastCol + 1, lastCol + 3));
            cell = row.createCell(lastCol + 1);
            cell.setCellValue("Subagent名称");
            cell.setCellStyle(cellStyle);
            lastCol += 3;
            sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, lastCol + 1, lastCol + 3));
            cell = row.createCell(lastCol + 1);
            cell.setCellValue("所在区域");
            cell.setCellStyle(cellStyle);
            if (statisticalType == 5) {
                return;
            }
            lastCol += 3;
            sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, lastCol + 1, lastCol + 3));
            cell = row.createCell(lastCol + 1);
            cell.setCellValue("代理标签");
            cell.setCellStyle(cellStyle);
            if (statisticalType != 4) {
                return;
            }
            lastCol += 3;
        } else {
            String str = "";
            switch (statisticalType) {
                case 1:
                    str = "BD编号/名称";
                    break;
                case 2:
                case 3:
                    str = "所在区域";
                    break;
                case 6:
                case 8:
                    str = "岗位/姓名";
                    break;
            }
            cell.setCellValue(str);
            cell.setCellStyle(cellStyle);
        }
        if (isHidden) {
            sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, lastRow, lastCol + 1, lastCol + 3));
            cell = row.createCell(lastCol + 1);
            cell.setCellValue("业务国家");
            cellStyle = getCellStyle(workbook, (short) 12, true, (short) -1);
            cellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            cell.setCellStyle(cellStyle);
            //获取当前最后列的位置 因为前两个步进为2.索引lastCol=2+3=5
            lastCol += 3;
        }
        //初始化下一个格子索引位置步进为 1
        int idx = lastCol + 1;
        for (Map.Entry<String, StudentStatistical> entry : studentStatisticalMap.entrySet()) {
            if (statisticalType == 6) {
                sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, firstRow, lastCol + 1, lastCol + 9));
                cell = row.createCell(lastCol + 1);
                cell.setCellValue(entry.getKey());
                cell.setCellStyle(cellStyle);
//                cell = row2.createCell(idx);
//                cell.setCellValue("旧学生数（首次）");
//                cell.setCellStyle(cellStyle);
//                idx++;
//                cell = row2.createCell(idx);
//                cell.setCellValue("新学生数（首次）");
//                cell.setCellStyle(cellStyle);
//                idx++;
                cell = row2.createCell(idx);
                cell.setCellValue("旧学生数");
                cell.setCellStyle(cellStyle);
                idx++;
                cell = row2.createCell(idx);
                cell.setCellValue("新学生数");
                cell.setCellStyle(cellStyle);
                idx++;
                cell = row2.createCell(idx);
                cell.setCellValue("提交数");
                cell.setCellStyle(cellStyle);
                idx++;
                cell = row2.createCell(idx);
                cell.setCellValue("延迟提交数");
                cell.setCellStyle(cellStyle);
                idx++;
                cell = row2.createCell(idx);
                cell.setCellValue("申请反馈数");
                cell.setCellStyle(cellStyle);
                idx++;
                cell = row2.createCell(idx);
                cell.setCellValue("延迟申请反馈数");
                cell.setCellStyle(cellStyle);
                idx++;
                cell = row2.createCell(idx);
                cell.setCellValue("OS数");
                cell.setCellStyle(cellStyle);
                idx++;
                cell = row2.createCell(idx);
                cell.setCellValue("签证数");
                cell.setCellStyle(cellStyle);
                idx++;
                cell = row2.createCell(idx);
                cell.setCellValue("入学数");
                cell.setCellStyle(cellStyle);
                idx++;
                //头部合并为5列，填充两次所以此处 +5
                lastCol += 9;
            } else if (statisticalType == 1) {
                sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, firstRow, lastCol + 1, lastCol + 6));
                cell = row.createCell(lastCol + 1);
                cell.setCellValue(entry.getKey());
                cell.setCellStyle(cellStyle);
                cell = row2.createCell(idx);
                cell.setCellValue("旧学生数（首次）");
                cell.setCellStyle(cellStyle);
                idx++;
                cell = row2.createCell(idx);
                cell.setCellValue("新学生数（首次）");
                cell.setCellStyle(cellStyle);
                idx++;
                cell = row2.createCell(idx);
                cell.setCellValue("旧学生数");
                cell.setCellStyle(cellStyle);
                idx++;
                cell = row2.createCell(idx);
                cell.setCellValue("新学生数");
                cell.setCellStyle(cellStyle);
                idx++;
                cell = row2.createCell(idx);
                cell.setCellValue("OS数");
                cell.setCellStyle(cellStyle);
                idx++;
                cell = row2.createCell(idx);
                cell.setCellValue("缺资料数");
                cell.setCellStyle(cellStyle);
                idx++;
                //头部合并为2列，填充两次所以此处 +2
                lastCol += 6;
                //头部合并为2列，填充两次所以此处 +2
            } else if (statisticalType == 3) {
                sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, firstRow, lastCol + 1, lastCol + 5));
                cell = row.createCell(lastCol + 1);
                cell.setCellValue(entry.getKey());
                cell.setCellStyle(cellStyle);
                cell = row2.createCell(idx);
                cell.setCellValue("旧学生数（首次）");
                cell.setCellStyle(cellStyle);
                idx++;
                cell = row2.createCell(idx);
                cell.setCellValue("新学生数（首次）");
                cell.setCellStyle(cellStyle);
                idx++;
                cell = row2.createCell(idx);
                cell.setCellValue("旧学生数");
                cell.setCellStyle(cellStyle);
                idx++;
                cell = row2.createCell(idx);
                cell.setCellValue("新学生数");
                cell.setCellStyle(cellStyle);
                idx++;
                cell = row2.createCell(idx);
                cell.setCellValue("OS数");
                cell.setCellStyle(cellStyle);
                idx++;
                //头部合并为2列，填充两次所以此处 +2
                lastCol += 5;
                //头部合并为2列，填充两次所以此处 +2
            } else if (statisticalType == 8) {
                sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, firstRow, lastCol + 1, lastCol + 20));
                cell = row.createCell(lastCol + 1);
                cell.setCellValue(entry.getKey() + tip);
                cell.setCellStyle(cellStyle);
//                sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, lastCol + 1, lastCol + 2));
//                cell = row2.createCell(lastCol + 1);
//                cell.setCellValue("旧学生数（首次）");
//                cell.setCellStyle(cellStyle);
//                lastCol += 2;
//                sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, lastCol + 1, lastCol + 2));
//                cell = row2.createCell(lastCol + 1);
//                cell.setCellValue("新学生数（首次）");
//                cell.setCellStyle(cellStyle);
//                lastCol += 2;
                sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, lastCol + 1, lastCol + 2));
                cell = row2.createCell(lastCol + 1);
                cell.setCellValue("旧学生数");
                cell.setCellStyle(cellStyle);
                lastCol += 2;
                sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, lastCol + 1, lastCol + 2));
                cell = row2.createCell(lastCol + 1);
                cell.setCellValue("新学生数");
                cell.setCellStyle(cellStyle);
                lastCol += 2;
                sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, lastCol + 1, lastCol + 2));
                cell = row2.createCell(lastCol + 1);
                cell.setCellValue("提交数（" + DataConverter.bigDecimalNullConvert(commissionPolicMap.get(ProjectKeyEnum.STEP_NEW_APP.key)) + "元）");
                cell.setCellStyle(cellStyle);
                lastCol += 2;
                sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, lastCol + 1, lastCol + 2));
                cell = row2.createCell(lastCol + 1);
                cell.setCellValue("延迟提交数（" + DataConverter.bigDecimalNullConvert(commissionPolicMap.get(ProjectKeyEnum.STEP_SUBMITTED_DELAY.key)) + "元）");
                cell.setCellStyle(cellStyle);
                lastCol += 2;
                sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, lastCol + 1, lastCol + 2));
                cell = row2.createCell(lastCol + 1);
                cell.setCellValue("申请反馈数（" + DataConverter.bigDecimalNullConvert(commissionPolicMap.get(ProjectKeyEnum.STEP_ADMITTED.key)) + "元）");
                cell.setCellStyle(cellStyle);
                lastCol += 2;
                sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, lastCol + 1, lastCol + 2));
                cell = row2.createCell(lastCol + 1);
                cell.setCellValue("延迟申请反馈数（" + DataConverter.bigDecimalNullConvert(commissionPolicMap.get(ProjectKeyEnum.STEP_ADMITTED_DELAY.key)) + "元）");
                cell.setCellStyle(cellStyle);
                lastCol += 2;
                sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, lastCol + 1, lastCol + 2));
                cell = row2.createCell(lastCol + 1);
                cell.setCellValue("OS数（" + DataConverter.bigDecimalNullConvert(commissionPolicMap.get(ProjectKeyEnum.STEP_OFFER_SELECTION.key)) + "元）");
                cell.setCellStyle(cellStyle);
                lastCol += 2;
                sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, lastCol + 1, lastCol + 2));
                cell = row2.createCell(lastCol + 1);
                cell.setCellValue("签证数（" + DataConverter.bigDecimalNullConvert(commissionPolicMap.get(ProjectKeyEnum.STEP_VISA_GRANTED.key)) + "元）");
                cell.setCellStyle(cellStyle);
                lastCol += 2;
                sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, lastCol + 1, lastCol + 2));
                cell = row2.createCell(lastCol + 1);
                cell.setCellValue("入学数（" + DataConverter.bigDecimalNullConvert(commissionPolicMap.get(ProjectKeyEnum.STEP_ENROLLED.key)) + "元）");
                cell.setCellStyle(cellStyle);
                lastCol += 2;
                sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, lastCol + 1, lastCol + 2));
                cell = row2.createCell(lastCol + 1);
                cell.setCellValue("后补签证数（" + DataConverter.bigDecimalNullConvert(commissionPolicMap.get(ProjectKeyEnum.STEP_VISA_GRANTED_BACK.key)) + "元）");
                cell.setCellStyle(cellStyle);
                lastCol += 2;
                sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, lastCol + 1, lastCol + 2));
                cell = row2.createCell(lastCol + 1);
                cell.setCellValue("结算金额");
                cell.setCellStyle(cellStyle);
                lastCol += 2;
            } else if (statisticalType == 2 || statisticalType == 4) {
                sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, firstRow, lastCol + 1, lastCol + 5));
                cell = row.createCell(lastCol + 1);
                cell.setCellValue(entry.getKey());
                cell.setCellStyle(cellStyle);
                cell = row2.createCell(idx);
                cell.setCellValue("旧学生数（首次）");
                cell.setCellStyle(cellStyle);
                idx++;
                cell = row2.createCell(idx);
                cell.setCellValue("新学生数（首次）");
                cell.setCellStyle(cellStyle);
                idx++;
                cell = row2.createCell(idx);
                cell.setCellValue("旧学生数");
                cell.setCellStyle(cellStyle);
                idx++;
                cell = row2.createCell(idx);
                cell.setCellValue("新学生数");
                cell.setCellStyle(cellStyle);
                idx++;
                cell = row2.createCell(idx);
                cell.setCellValue("OS数");
                cell.setCellStyle(cellStyle);
                idx++;
                lastCol += 5;
            } else {
                sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, firstRow, lastCol + 1, lastCol + 2));
                cell = row.createCell(lastCol + 1);
                cell.setCellValue(entry.getKey());
                cell.setCellStyle(cellStyle);
                cell = row2.createCell(idx);
                cell.setCellValue("学生数（首次）");
                cell.setCellStyle(cellStyle);
                cell = row2.createCell(idx + 1);
                cell.setCellValue("OS数");
                cell.setCellStyle(cellStyle);
                //头部合并为2列，填充两次所以此处 +2
                lastCol += 2;
                //头部合并为2列，填充两次所以此处 +2
                idx += 2;
            }
        }
        //此处步进为2
        for (Map.Entry<String, StudentStatisticalRatio> entry : studentStatisticalRatioMap.entrySet()) {
            if (statisticalType == 6) {
                sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, firstRow, lastCol + 1, lastCol + 18));
            } else if (statisticalType == 1) {
                sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, firstRow, lastCol + 1, lastCol + 12));
            } else if (statisticalType == 2 || statisticalType == 3 || statisticalType == 4) {
                sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, firstRow, lastCol + 1, lastCol + 10));
            } else {
                sheet.addMergedRegionUnsafe(new CellRangeAddress(firstRow, firstRow, lastCol + 1, lastCol + 6));
            }
            cell = row.createCell(lastCol + 1);
            cell.setCellValue(entry.getKey() + "增减比率");
            cell.setCellStyle(cellStyle);

            if (statisticalType == 6) {
                //合并两格
//                sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, lastCol + 1, lastCol + 2));
//                cell = row2.createCell(lastCol + 1);
//                cell.setCellValue("旧学生数(首次)");
//                cell.setCellStyle(cellStyle);
//                lastCol += 2;
//                sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, lastCol + 1, lastCol + 2));
//                cell = row2.createCell(lastCol + 1);
//                cell.setCellValue("新学生数(首次)");
//                cell.setCellStyle(cellStyle);
//                lastCol += 2;
                sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, lastCol + 1, lastCol + 2));
                cell = row2.createCell(lastCol + 1);
                cell.setCellValue("旧学生数");
                cell.setCellStyle(cellStyle);
                lastCol += 2;
                sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, lastCol + 1, lastCol + 2));
                cell = row2.createCell(lastCol + 1);
                cell.setCellValue("新学生数");
                cell.setCellStyle(cellStyle);
                lastCol += 2;
                //合并两格
                sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, lastCol + 1, lastCol + 2));
                cell = row2.createCell(lastCol + 1);
                cell.setCellValue("提交数");
                cell.setCellStyle(cellStyle);
                lastCol += 2;
                sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, lastCol + 1, lastCol + 2));
                cell = row2.createCell(lastCol + 1);
                cell.setCellValue("延迟提交数");
                cell.setCellStyle(cellStyle);
                lastCol += 2;
                sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, lastCol + 1, lastCol + 2));
                cell = row2.createCell(lastCol + 1);
                cell.setCellValue("申请反馈数");
                cell.setCellStyle(cellStyle);
                lastCol += 2;
                sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, lastCol + 1, lastCol + 2));
                cell = row2.createCell(lastCol + 1);
                cell.setCellValue("延迟申请反馈数");
                cell.setCellStyle(cellStyle);
                lastCol += 2;
                //合并两格
                sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, lastCol + 1, lastCol + 2));
                cell = row2.createCell(lastCol + 1);
                cell.setCellValue("OS数");
                cell.setCellStyle(cellStyle);
                lastCol += 2;
                //合并两格
                sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, lastCol + 1, lastCol + 2));
                cell = row2.createCell(lastCol + 1);
                cell.setCellValue("签证数");
                cell.setCellStyle(cellStyle);
                lastCol += 2;
                //合并两格
                sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, lastCol + 1, lastCol + 2));
                cell = row2.createCell(lastCol + 1);
                cell.setCellValue("入学数");
                cell.setCellStyle(cellStyle);
                lastCol += 2;
                //头部合并为4列，填充两次所以此处 +10
            } else if (statisticalType == 1) {
                //合并两格
                sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, lastCol + 1, lastCol + 2));
                cell = row2.createCell(lastCol + 1);
                cell.setCellValue("旧学生数(首次)");
                cell.setCellStyle(cellStyle);
                lastCol += 2;
                sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, lastCol + 1, lastCol + 2));
                cell = row2.createCell(lastCol + 1);
                cell.setCellValue("新学生数(首次)");
                cell.setCellStyle(cellStyle);
                lastCol += 2;
                sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, lastCol + 1, lastCol + 2));
                cell = row2.createCell(lastCol + 1);
                cell.setCellValue("旧学生数");
                cell.setCellStyle(cellStyle);
                lastCol += 2;
                sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, lastCol + 1, lastCol + 2));
                cell = row2.createCell(lastCol + 1);
                cell.setCellValue("新学生数");
                cell.setCellStyle(cellStyle);
                lastCol += 2;
                //合并两格
                sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, lastCol + 1, lastCol + 2));
                cell = row2.createCell(lastCol + 1);
                cell.setCellValue("OS数");
                cell.setCellStyle(cellStyle);
                lastCol += 2;
                sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, lastCol + 1, lastCol + 2));
                cell = row2.createCell(lastCol + 1);
                cell.setCellValue("缺资料数");
                cell.setCellStyle(cellStyle);
                lastCol += 2;
            } else if (statisticalType == 3) {
                //合并两格
                sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, lastCol + 1, lastCol + 2));
                cell = row2.createCell(lastCol + 1);
                cell.setCellValue("旧学生数(首次)");
                cell.setCellStyle(cellStyle);
                lastCol += 2;
                sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, lastCol + 1, lastCol + 2));
                cell = row2.createCell(lastCol + 1);
                cell.setCellValue("新学生数(首次)");
                cell.setCellStyle(cellStyle);
                lastCol += 2;
                sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, lastCol + 1, lastCol + 2));
                cell = row2.createCell(lastCol + 1);
                cell.setCellValue("旧学生数");
                cell.setCellStyle(cellStyle);
                lastCol += 2;
                sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, lastCol + 1, lastCol + 2));
                cell = row2.createCell(lastCol + 1);
                cell.setCellValue("新学生数");
                cell.setCellStyle(cellStyle);
                lastCol += 2;
                //合并两格
                sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, lastCol + 1, lastCol + 2));
                cell = row2.createCell(lastCol + 1);
                cell.setCellValue("OS数");
                cell.setCellStyle(cellStyle);
                lastCol += 2;
            } else if (statisticalType == 2 || statisticalType == 4) {
                sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, lastCol + 1, lastCol + 2));
                cell = row2.createCell(lastCol + 1);
                cell.setCellValue("旧学生数（首次）");
                cell.setCellStyle(cellStyle);
                lastCol += 2;
                sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, lastCol + 1, lastCol + 2));
                cell = row2.createCell(lastCol + 1);
                cell.setCellValue("新学生数（首次）");
                cell.setCellStyle(cellStyle);
                lastCol += 2;
                sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, lastCol + 1, lastCol + 2));
                cell = row2.createCell(lastCol + 1);
                cell.setCellValue("旧学生数");
                cell.setCellStyle(cellStyle);
                lastCol += 2;
                sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, lastCol + 1, lastCol + 2));
                cell = row2.createCell(lastCol + 1);
                cell.setCellValue("新学生数");
                cell.setCellStyle(cellStyle);
                lastCol += 2;
                //合并两格
                sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, lastCol + 1, lastCol + 2));
                cell = row2.createCell(lastCol + 1);
                cell.setCellValue("OS数");
                cell.setCellStyle(cellStyle);
                lastCol += 2;
            } else {
                //合并两格
                sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, lastCol + 1, lastCol + 2));
                cell = row2.createCell(lastCol + 1);
                cell.setCellValue("学生数（首次）");
                cell.setCellStyle(cellStyle);
                //合并两格
                sheet.addMergedRegionUnsafe(new CellRangeAddress(lastRow, lastRow, lastCol + 3, lastCol + 4));
                cell = row2.createCell(lastCol + 3);
                cell.setCellValue("OS数");
                cell.setCellStyle(cellStyle);
                //头部合并为4列，填充两次所以此处 +4
                lastCol += 4;
            }

        }
    }


    private CellStyle getLeft(SXSSFWorkbook workbook, short fontSize, boolean isBold) {
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setAlignment(HorizontalAlignment.LEFT);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        Font font = workbook.createFont();
        font.setFontHeightInPoints(fontSize);
        font.setFontName("宋体");
        //字号
        font.setBold(isBold);
        //加粗
        cellStyle.setFont(font);
        return cellStyle;
    }

    private CellStyle getRight(SXSSFWorkbook workbook, short fontSize, boolean isBold) {
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setAlignment(HorizontalAlignment.RIGHT);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        Font font = workbook.createFont();
        font.setFontHeightInPoints(fontSize);
        font.setFontName("宋体");
        //字号
        font.setBold(isBold);
        //加粗
        cellStyle.setFont(font);
        return cellStyle;
    }

    //cell 代表一个格子。获取cell样式
    private CellStyle getCellStyle(SXSSFWorkbook workbook, short fontSize, boolean isBold, short color) {
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        Font font = workbook.createFont();
        font.setFontHeightInPoints(fontSize);
        font.setFontName("宋体");
        //字号
        font.setBold(isBold);
        if (color > -1) {
            font.setColor(color);
        }
        //加粗
        cellStyle.setFont(font);
        return cellStyle;
    }

    /**
     * BD学生统计对比表 bd比对统计总计
     *
     * @Date 11:19 2023/1/6
     * <AUTHOR>
     */
    @Override
    public List<BdStudentStatisticalComparisonVo> bdStudentStatisticalComparisonTotal(BdStudentStatisticalComparisonDto bdStudentStatisticalComparisonQueryDto, List<Long> countryIds, Long staffId) {
        List<BdStudentStatisticalComparisonVo> bdStudentStatisticalComparisonVoList = new ArrayList<>();
        if (GeneralTool.isEmpty(countryIds)) {
            return bdStudentStatisticalComparisonVoList;
        }
        Boolean isBd = studentOfferService.getIsBd(staffId);
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");

        //同期比对年数
        Integer comparisonYears = bdStudentStatisticalComparisonQueryDto.getComparisonYears();
        //查询时间
        Date studentBeginTime = bdStudentStatisticalComparisonQueryDto.getStudentBeginTime();
        Date studentEndTime = bdStudentStatisticalComparisonQueryDto.getStudentEndTime();
        //查询初始年份
        Calendar cal = Calendar.getInstance();
        cal.setTime(studentBeginTime);
        int year = cal.get(Calendar.YEAR);

        //比对年份List
        List<StatisticalComparisonYearDto> statisticalComparisonYearDtoList = new ArrayList<>();
        for (int i = 0; i <= comparisonYears; i++) {
            Date beginTime = DateUtil.minusYears(studentBeginTime, i);
            Date endTime = DateUtil.minusYears(studentEndTime, i);
            StatisticalComparisonYearDto statisticalComparisonYearDto = new StatisticalComparisonYearDto();
            statisticalComparisonYearDto.setBeginTime(beginTime);
            statisticalComparisonYearDto.setEndTime(endTime);
            statisticalComparisonYearDtoList.add(statisticalComparisonYearDto);
        }

        List<StaffCommissionPolicyVo> bdStaffCommissionPolicyList = staffCommissionPolicyMapper.getBdStaffCommissionPolicy(0L, bdStudentStatisticalComparisonQueryDto.getProjectRoleKey());
        //员工提成业务步骤key
        Map<String, BigDecimal> commissionPolicMap = new HashMap<>();
        //计算未结算金额 匹配提成规则
        for (StaffCommissionPolicyVo staffCommissionPolicyVo : bdStaffCommissionPolicyList) {
            commissionPolicMap.put(staffCommissionPolicyVo.getFkStaffCommissionStepKey(), staffCommissionPolicyVo.getFixedAmount());
        }

        //如果区分业务国家 需要先获取基础数据，防止每年的国家数不一样
        if (bdStudentStatisticalComparisonQueryDto.getIsDistinguishCountryFlag()) {
            bdStudentStatisticalComparisonVoList = studentOfferItemMapper.getBdStudentStatisticalTotalBasicData(bdStudentStatisticalComparisonQueryDto,
                    DateUtil.minusYears(studentBeginTime, comparisonYears), studentEndTime, statisticalComparisonYearDtoList, countryIds);
        } else {
            //不区分业务国家只有一条总计
            BdStudentStatisticalComparisonVo bdStudentStatisticalComparisonVo = new BdStudentStatisticalComparisonVo();
            bdStudentStatisticalComparisonVoList.add(bdStudentStatisticalComparisonVo);
        }
        ConfigVo configVo = permissionCenterClient.getConfigByKey("HTI_BMS_START_TIME").getData();
        Date htiStartTime;
        try {
            htiStartTime = sf.parse(configVo.getValue1());
        } catch (ParseException e) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("DATE_FORMAT_CONVERSION_ERROR"));
        }

        DelayConfigDto delayConfig = getDelayConfig();

        for (int i = 0; i <= comparisonYears; i++) {
            Date beginTime = DateUtil.minusYears(studentBeginTime, i);
            Date endTime = DateUtil.minusYears(studentEndTime, i);

            //查询本轮年份
            cal.setTime(beginTime);
            int nowYear = cal.get(Calendar.YEAR);
            List<StudentStatistical> studentStatisticalList = studentOfferItemMapper.getBdStudentStatistical(bdStudentStatisticalComparisonQueryDto,
                    null, beginTime, endTime, null, null, null, null, countryIds,
                    htiStartTime, true, delayConfig, isBd,
                    SecureUtil.getPermissionGroupInstitutionIds(),
                    SecureUtil.getStaffBoundBdIds());
            //迭代基础数据，按年份往里面塞值
            for (StudentStatistical studentStatistical : studentStatisticalList) {
                BdStudentStatisticalComparisonVo bdStudentStatisticalComparisonVo = new BdStudentStatisticalComparisonVo();
                for (BdStudentStatisticalComparisonVo bdStudentStatisticalDto : bdStudentStatisticalComparisonVoList) {
                    if (bdStudentStatisticalComparisonQueryDto.getIsDistinguishCountryFlag() &&
                            studentStatistical.getFkAreaCountryId().equals(bdStudentStatisticalDto.getFkAreaCountryId())) {
                        bdStudentStatisticalComparisonVo = bdStudentStatisticalDto;
                    } else if (!bdStudentStatisticalComparisonQueryDto.getIsDistinguishCountryFlag()) {
                        bdStudentStatisticalComparisonVo = bdStudentStatisticalDto;
                    }
                }

                //前端要求返回格式转换
                switch (bdStudentStatisticalComparisonQueryDto.getStatisticalType()) {
                    case 1:
                        bdStudentStatisticalComparisonVo.setBdName("总计");
                        break;
                    case 2:
                        bdStudentStatisticalComparisonVo.setRegion("总计");
                        break;
                    case 3:
                        bdStudentStatisticalComparisonVo.setRegion("总计");
                        break;
                    case 4:
                        bdStudentStatisticalComparisonVo.setBdName("总计");
                        break;
                    case 6:
                    case 8:
                        bdStudentStatisticalComparisonVo.setRoleStaffName("总计");
                        break;
                }
                bdStudentStatisticalComparisonVo.setAreaCountryName(studentStatistical.getAreaCountryName());
                Map<String, StudentStatistical> studentStatisticalMap;
                if (GeneralTool.isEmpty(bdStudentStatisticalComparisonVo.getStudentStatisticalMap())) {
                    studentStatisticalMap = new LinkedHashMap<>();
                    for (int j = comparisonYears; j >= 0; j--) {
                        //本轮年份
//                        int currentRound = year - j;
                        String timeInterval = sf.format(DateUtil.minusYears(studentBeginTime, j)) + " : " + sf.format(DateUtil.minusYears(studentEndTime, j));
                        studentStatisticalMap.put(timeInterval, null);
                    }
                } else {
                    studentStatisticalMap = bdStudentStatisticalComparisonVo.getStudentStatisticalMap();
                }


                //如果为项目成员统计结算表 -> 计算已结算数
                if (bdStudentStatisticalComparisonQueryDto.getStatisticalType() == 8) {
                    if (GeneralTool.isEmpty(bdStudentStatisticalComparisonQueryDto.getStatusSettlement())) {
                        studentStatistical.setCommissionActionItemNumStr(studentStatistical.getUnsettledCommissionActionItemNum() + " / " + studentStatistical.getCommissionActionItemNum());
                        studentStatistical.setCommissionActionDelayItemNumStr(studentStatistical.getUnsettledCommissionActionDelayItemNum() + " / " + studentStatistical.getCommissionActionDelayItemNum());
                        studentStatistical.setCommissionActionVisaNumStr(studentStatistical.getUnsettledCommissionActionVisaNum() + " / " + studentStatistical.getCommissionActionVisaNum());
                        studentStatistical.setCommissionActionEnrolledNumStr(studentStatistical.getUnsettledCommissionActionEnrolledNum() + " / " + studentStatistical.getCommissionActionEnrolledNum());
                        studentStatistical.setCommissionActionEnrolledVisaNumStr(studentStatistical.getUnsettledCommissionActionEnrolledVisaNum() + " / " + studentStatistical.getCommissionActionEnrolledVisaNum());
                        studentStatistical.setCommissionActionOsNumStr(studentStatistical.getUnsettledCommissionActionOsNum() + " / " + studentStatistical.getCommissionActionOsNum());
                        studentStatistical.setCommissionActionAdmittedNumStr(studentStatistical.getUnsettledCommissionActionAdmittedNum() + " / " + studentStatistical.getCommissionActionAdmittedNum());
                        studentStatistical.setCommissionActionAdmittedDelayNumStr(studentStatistical.getUnsettledCommissionActionAdmittedDelayNum() + " / " + studentStatistical.getCommissionActionAdmittedDelayNum());
                    } else if (bdStudentStatisticalComparisonQueryDto.getStatusSettlement().equals(0)) {
                        studentStatistical.setCommissionActionItemNumStr(studentStatistical.getUnsettledCommissionActionItemNum().toString());
                        studentStatistical.setCommissionActionDelayItemNumStr(studentStatistical.getUnsettledCommissionActionDelayItemNum().toString());
                        studentStatistical.setCommissionActionVisaNumStr(studentStatistical.getUnsettledCommissionActionVisaNum().toString());
                        studentStatistical.setCommissionActionEnrolledNumStr(studentStatistical.getUnsettledCommissionActionEnrolledNum().toString());
                        studentStatistical.setCommissionActionEnrolledVisaNumStr(studentStatistical.getUnsettledCommissionActionEnrolledVisaNum().toString());
                        studentStatistical.setCommissionActionOsNumStr(studentStatistical.getUnsettledCommissionActionOsNum().toString());
                        studentStatistical.setCommissionActionAdmittedNumStr(studentStatistical.getUnsettledCommissionActionAdmittedNum().toString());
                        studentStatistical.setCommissionActionAdmittedDelayNumStr(studentStatistical.getUnsettledCommissionActionAdmittedDelayNum().toString());
                    } else {
                        studentStatistical.setCommissionActionItemNumStr(studentStatistical.getCommissionActionItemNum().toString());
                        studentStatistical.setCommissionActionDelayItemNumStr(studentStatistical.getCommissionActionDelayItemNum().toString());
                        studentStatistical.setCommissionActionVisaNumStr(studentStatistical.getCommissionActionVisaNum().toString());
                        studentStatistical.setCommissionActionEnrolledNumStr(studentStatistical.getCommissionActionEnrolledNum().toString());
                        studentStatistical.setCommissionActionEnrolledVisaNumStr(studentStatistical.getCommissionActionEnrolledVisaNum().toString());
                        studentStatistical.setCommissionActionOsNumStr(studentStatistical.getCommissionActionOsNum().toString());
                        studentStatistical.setCommissionActionAdmittedNumStr(studentStatistical.getCommissionActionAdmittedNum().toString());
                        studentStatistical.setCommissionActionAdmittedDelayNumStr(studentStatistical.getCommissionActionAdmittedDelayNum().toString());
                    }
                    //计算未结算金额
                    //新申请提成金额
                    BigDecimal newAmount = GeneralTool.isNotEmpty(commissionPolicMap.get(ProjectKeyEnum.STEP_NEW_APP.key)) ? commissionPolicMap.get(ProjectKeyEnum.STEP_NEW_APP.key) : BigDecimal.ZERO;
                    //OS提成金额
                    BigDecimal osAmount = GeneralTool.isNotEmpty(commissionPolicMap.get(ProjectKeyEnum.STEP_OFFER_SELECTION.key)) ? commissionPolicMap.get(ProjectKeyEnum.STEP_OFFER_SELECTION.key) : BigDecimal.ZERO;
                    //签证数提成金额
                    BigDecimal visaAmount = GeneralTool.isNotEmpty(commissionPolicMap.get(ProjectKeyEnum.STEP_VISA_GRANTED.key)) ? commissionPolicMap.get(ProjectKeyEnum.STEP_VISA_GRANTED.key) : BigDecimal.ZERO;
                    //入学提成金额
                    BigDecimal enrolledAmount = GeneralTool.isNotEmpty(commissionPolicMap.get(ProjectKeyEnum.STEP_ENROLLED.key)) ? commissionPolicMap.get(ProjectKeyEnum.STEP_ENROLLED.key) : BigDecimal.ZERO;
                    //后补签证金额
                    BigDecimal enrolledVisaAmount = GeneralTool.isNotEmpty(commissionPolicMap.get(ProjectKeyEnum.STEP_VISA_GRANTED_BACK.key)) ? commissionPolicMap.get(ProjectKeyEnum.STEP_VISA_GRANTED_BACK.key) : BigDecimal.ZERO;
                    //申请数提成金额
                    BigDecimal admittedAmount = GeneralTool.isNotEmpty(commissionPolicMap.get(ProjectKeyEnum.STEP_ADMITTED.key)) ? commissionPolicMap.get(ProjectKeyEnum.STEP_ADMITTED.key) : BigDecimal.ZERO;
                    //延迟提交
                    BigDecimal submittedDelayAmount = GeneralTool.isNotEmpty(commissionPolicMap.get(ProjectKeyEnum.STEP_SUBMITTED_DELAY.key)) ? commissionPolicMap.get(ProjectKeyEnum.STEP_SUBMITTED_DELAY.key) : BigDecimal.ZERO;
                    //延迟反馈
                    BigDecimal admittedDelayAmount = GeneralTool.isNotEmpty(commissionPolicMap.get(ProjectKeyEnum.STEP_ADMITTED_DELAY.key)) ? commissionPolicMap.get(ProjectKeyEnum.STEP_ADMITTED_DELAY.key) : BigDecimal.ZERO;

                    //未结算金额
                    BigDecimal commissionActionAmount = newAmount.multiply(new BigDecimal(studentStatistical.getUnsettledCommissionActionItemNum()))
                            .add(osAmount.multiply(new BigDecimal(studentStatistical.getUnsettledCommissionActionOsNum())))
                            .add(visaAmount.multiply(new BigDecimal(studentStatistical.getUnsettledCommissionActionVisaNum())))
                            .add(enrolledAmount.multiply(new BigDecimal(studentStatistical.getUnsettledCommissionActionEnrolledNum())))
                            .add(enrolledVisaAmount.multiply(new BigDecimal(studentStatistical.getUnsettledCommissionActionEnrolledVisaNum())))
                            .add(admittedAmount.multiply(new BigDecimal(studentStatistical.getUnsettledCommissionActionAdmittedNum()))
                                    .add(submittedDelayAmount.multiply(new BigDecimal(studentStatistical.getUnsettledCommissionActionDelayItemNum())))
                                    .add(admittedDelayAmount.multiply(new BigDecimal(studentStatistical.getUnsettledCommissionActionAdmittedDelayNum()))));

                    //已结算金额
                    BigDecimal settledCommissionActionAmount = newAmount.multiply(new BigDecimal(studentStatistical.getCommissionActionItemNum()))
                            .add(osAmount.multiply(new BigDecimal(studentStatistical.getCommissionActionOsNum())))
                            .add(visaAmount.multiply(new BigDecimal(studentStatistical.getCommissionActionVisaNum())))
                            .add(enrolledAmount.multiply(new BigDecimal(studentStatistical.getCommissionActionEnrolledNum())))
                            .add(enrolledVisaAmount.multiply(new BigDecimal(studentStatistical.getCommissionActionEnrolledVisaNum())))
                            .add(admittedAmount.multiply(new BigDecimal(studentStatistical.getCommissionActionAdmittedNum())))
                            .add(submittedDelayAmount.multiply(new BigDecimal(studentStatistical.getCommissionActionDelayItemNum())))
                            .add(admittedDelayAmount.multiply(new BigDecimal(studentStatistical.getCommissionActionAdmittedDelayNum())));

                    if (GeneralTool.isEmpty(bdStudentStatisticalComparisonQueryDto.getStatusSettlement())) {
                        studentStatistical.setCommissionActionAmount(commissionActionAmount + " / " + settledCommissionActionAmount);
                    } else if (bdStudentStatisticalComparisonQueryDto.getStatusSettlement() == 0) {
                        studentStatistical.setCommissionActionAmount(commissionActionAmount.toString());
                    } else if (bdStudentStatisticalComparisonQueryDto.getStatusSettlement() == 1) {
                        studentStatistical.setCommissionActionAmount(settledCommissionActionAmount.toString());
                    }
                }

                String timeInterval = sf.format(beginTime) + " : " + sf.format(endTime);
                studentStatisticalMap.put(timeInterval, studentStatistical);
                bdStudentStatisticalComparisonVo.setStudentStatisticalMap(studentStatisticalMap);


                //如果为项目成员统计结算表 -> 计算每个步骤结算总金额
                // 因为统计结算表只会有一年的数据，不存在多年数据对比的情况，所以只考虑一年就行了，这个循环只会循环一次，直接取这个循环的数据去算每个步骤总金额即可
                if (bdStudentStatisticalComparisonQueryDto.getStatisticalType() == 8) {
                    StudentStatistical studentCommissionStatistical = new StudentStatistical();
                    BdStudentStatisticalComparisonVo bdStudentCommissionStatisticalComparisonDto = new BdStudentStatisticalComparisonVo();
                    bdStudentCommissionStatisticalComparisonDto.setRoleStaffName("结算总金额");

                    //计算未结算金额
                    //新申请提成金额
                    BigDecimal newAmount = GeneralTool.isNotEmpty(commissionPolicMap.get(ProjectKeyEnum.STEP_NEW_APP.key)) ? commissionPolicMap.get(ProjectKeyEnum.STEP_NEW_APP.key) : BigDecimal.ZERO;
                    //OS提成金额
                    BigDecimal osAmount = GeneralTool.isNotEmpty(commissionPolicMap.get(ProjectKeyEnum.STEP_OFFER_SELECTION.key)) ? commissionPolicMap.get(ProjectKeyEnum.STEP_OFFER_SELECTION.key) : BigDecimal.ZERO;
                    //签证数提成金额
                    BigDecimal visaAmount = GeneralTool.isNotEmpty(commissionPolicMap.get(ProjectKeyEnum.STEP_VISA_GRANTED.key)) ? commissionPolicMap.get(ProjectKeyEnum.STEP_VISA_GRANTED.key) : BigDecimal.ZERO;
                    //入学提成金额
                    BigDecimal enrolledAmount = GeneralTool.isNotEmpty(commissionPolicMap.get(ProjectKeyEnum.STEP_ENROLLED.key)) ? commissionPolicMap.get(ProjectKeyEnum.STEP_ENROLLED.key) : BigDecimal.ZERO;
                    //后补签证金额
                    BigDecimal enrolledVisaAmount = GeneralTool.isNotEmpty(commissionPolicMap.get(ProjectKeyEnum.STEP_VISA_GRANTED_BACK.key)) ? commissionPolicMap.get(ProjectKeyEnum.STEP_VISA_GRANTED_BACK.key) : BigDecimal.ZERO;
                    //申请数提成金额
                    BigDecimal admittedAmount = GeneralTool.isNotEmpty(commissionPolicMap.get(ProjectKeyEnum.STEP_ADMITTED.key)) ? commissionPolicMap.get(ProjectKeyEnum.STEP_ADMITTED.key) : BigDecimal.ZERO;
                    //延迟提交
                    BigDecimal submittedDelayAmount = GeneralTool.isNotEmpty(commissionPolicMap.get(ProjectKeyEnum.STEP_SUBMITTED_DELAY.key)) ? commissionPolicMap.get(ProjectKeyEnum.STEP_SUBMITTED_DELAY.key) : BigDecimal.ZERO;
                    //延迟反馈
                    BigDecimal admittedDelayAmount = GeneralTool.isNotEmpty(commissionPolicMap.get(ProjectKeyEnum.STEP_ADMITTED_DELAY.key)) ? commissionPolicMap.get(ProjectKeyEnum.STEP_ADMITTED_DELAY.key) : BigDecimal.ZERO;


                    //未结算申请金额
                    BigDecimal unsettledCommissionActionItemAmount = new BigDecimal(studentStatistical.getUnsettledCommissionActionItemNum()).multiply(newAmount);
                    //未结算签证金额
                    BigDecimal unsettledCommissionActionVisaAmount = new BigDecimal(studentStatistical.getUnsettledCommissionActionVisaNum()).multiply(visaAmount);
                    //未结算入学金额
                    BigDecimal unsettledCommissionActionEnrolledAmount = new BigDecimal(studentStatistical.getUnsettledCommissionActionEnrolledNum()).multiply(enrolledAmount);
                    //未结算后补签证金额
                    BigDecimal unsettledCommissionActionEnrolledVisaAmount = new BigDecimal(studentStatistical.getUnsettledCommissionActionEnrolledVisaNum()).multiply(enrolledVisaAmount);
                    //未结算os金额
                    BigDecimal unsettledCommissionActionOsAmount = new BigDecimal(studentStatistical.getUnsettledCommissionActionOsNum()).multiply(osAmount);
                    //未结算申请反馈金额
                    BigDecimal unsettledCommissionActionAdmittedAmount = new BigDecimal(studentStatistical.getUnsettledCommissionActionAdmittedNum()).multiply(admittedAmount);
                    //未结算延期申请金额
                    BigDecimal unsettledCommissionActionDelayItemAmount = new BigDecimal(studentStatistical.getUnsettledCommissionActionDelayItemNum()).multiply(submittedDelayAmount);
                    //未结算延期申请反馈金额
                    BigDecimal unsettledCommissionActionAdmittedDelayAmount = new BigDecimal(studentStatistical.getUnsettledCommissionActionAdmittedDelayNum()).multiply(admittedDelayAmount);


                    //结算申请金额
                    BigDecimal commissionActionItemAmount = new BigDecimal(studentStatistical.getCommissionActionItemNum()).multiply(newAmount);
                    //结算签证金额
                    BigDecimal commissionActionVisaAmount = new BigDecimal(studentStatistical.getCommissionActionVisaNum()).multiply(visaAmount);
                    //结算入学金额
                    BigDecimal commissionActionEnrolledAmount = new BigDecimal(studentStatistical.getCommissionActionEnrolledNum()).multiply(enrolledAmount);
                    //结算后补签证金额
                    BigDecimal commissionActionEnrolledVisaAmount = new BigDecimal(studentStatistical.getCommissionActionEnrolledVisaNum()).multiply(enrolledVisaAmount);
                    //结算os金额
                    BigDecimal commissionActionOsAmount = new BigDecimal(studentStatistical.getCommissionActionOsNum()).multiply(osAmount);
                    //结算os金额
                    BigDecimal commissionActionAdmittedAmount = new BigDecimal(studentStatistical.getCommissionActionAdmittedNum()).multiply(admittedAmount);
                    //结算申请反馈金额
                    BigDecimal commissionActionItemDelayAmount = new BigDecimal(studentStatistical.getCommissionActionDelayItemNum()).multiply(submittedDelayAmount);
                    //结算延期申请反馈金额
                    BigDecimal commissionActionAdmittedDelayAmount = new BigDecimal(studentStatistical.getCommissionActionAdmittedDelayNum()).multiply(admittedDelayAmount);


                    if (GeneralTool.isEmpty(bdStudentStatisticalComparisonQueryDto.getStatusSettlement())) {
                        studentCommissionStatistical.setCommissionActionItemNumStr(unsettledCommissionActionItemAmount + " / " + commissionActionItemAmount);
                        studentCommissionStatistical.setCommissionActionDelayItemNumStr(unsettledCommissionActionDelayItemAmount + " / " + commissionActionItemDelayAmount);
                        studentCommissionStatistical.setCommissionActionVisaNumStr(unsettledCommissionActionVisaAmount + "/" + commissionActionVisaAmount);
                        studentCommissionStatistical.setCommissionActionEnrolledNumStr(unsettledCommissionActionEnrolledAmount + "/" + commissionActionEnrolledAmount);
                        studentCommissionStatistical.setCommissionActionEnrolledVisaNumStr(unsettledCommissionActionEnrolledVisaAmount + "/" + commissionActionEnrolledVisaAmount);
                        studentCommissionStatistical.setCommissionActionOsNumStr(unsettledCommissionActionOsAmount + "/" + commissionActionOsAmount);
                        studentCommissionStatistical.setCommissionActionAdmittedNumStr(unsettledCommissionActionAdmittedAmount + "/" + commissionActionAdmittedAmount);
                        studentCommissionStatistical.setCommissionActionAdmittedDelayNumStr(unsettledCommissionActionAdmittedDelayAmount + "/" + commissionActionAdmittedDelayAmount);
                    } else if (bdStudentStatisticalComparisonQueryDto.getStatusSettlement() == 0) {
                        studentCommissionStatistical.setCommissionActionItemNumStr(unsettledCommissionActionItemAmount.toString());
                        studentCommissionStatistical.setCommissionActionDelayItemNumStr(unsettledCommissionActionDelayItemAmount.toString());
                        studentCommissionStatistical.setCommissionActionVisaNumStr(unsettledCommissionActionVisaAmount.toString());
                        studentCommissionStatistical.setCommissionActionEnrolledNumStr(unsettledCommissionActionEnrolledAmount.toString());
                        studentCommissionStatistical.setCommissionActionEnrolledVisaNumStr(unsettledCommissionActionEnrolledVisaAmount.toString());
                        studentCommissionStatistical.setCommissionActionOsNumStr(unsettledCommissionActionOsAmount.toString());
                        studentCommissionStatistical.setCommissionActionAdmittedNumStr(unsettledCommissionActionAdmittedAmount.toString());
                        studentCommissionStatistical.setCommissionActionAdmittedDelayNumStr(unsettledCommissionActionAdmittedDelayAmount.toString());
                    } else if (bdStudentStatisticalComparisonQueryDto.getStatusSettlement() == 1) {
                        studentCommissionStatistical.setCommissionActionItemNumStr(commissionActionItemAmount.toString());
                        studentCommissionStatistical.setCommissionActionDelayItemNumStr(commissionActionItemDelayAmount.toString());
                        studentCommissionStatistical.setCommissionActionVisaNumStr(commissionActionVisaAmount.toString());
                        studentCommissionStatistical.setCommissionActionEnrolledNumStr(commissionActionEnrolledAmount.toString());
                        studentCommissionStatistical.setCommissionActionEnrolledVisaNumStr(commissionActionEnrolledVisaAmount.toString());
                        studentCommissionStatistical.setCommissionActionOsNumStr(commissionActionOsAmount.toString());
                        studentCommissionStatistical.setCommissionActionAdmittedNumStr(commissionActionAdmittedAmount.toString());
                        studentCommissionStatistical.setCommissionActionAdmittedDelayNumStr(commissionActionAdmittedDelayAmount.toString());
                    }

                    //未结算金额
                    BigDecimal commissionActionAmount = unsettledCommissionActionItemAmount
                            .add(unsettledCommissionActionVisaAmount)
                            .add(unsettledCommissionActionEnrolledAmount)
                            .add(unsettledCommissionActionEnrolledVisaAmount)
                            .add(unsettledCommissionActionOsAmount)
                            .add(unsettledCommissionActionAdmittedAmount)
                            .add(unsettledCommissionActionDelayItemAmount)
                            .add(unsettledCommissionActionAdmittedDelayAmount);

                    //已结算金额
                    BigDecimal settledCommissionActionAmount = commissionActionItemAmount
                            .add(commissionActionVisaAmount)
                            .add(commissionActionEnrolledAmount)
                            .add(commissionActionEnrolledVisaAmount)
                            .add(commissionActionOsAmount)
                            .add(commissionActionAdmittedAmount)
                            .add(commissionActionItemDelayAmount)
                            .add(commissionActionAdmittedDelayAmount);

                    if (GeneralTool.isEmpty(bdStudentStatisticalComparisonQueryDto.getStatusSettlement())) {
                        studentCommissionStatistical.setCommissionActionAmount(commissionActionAmount + " / " + settledCommissionActionAmount);
                    } else if (bdStudentStatisticalComparisonQueryDto.getStatusSettlement() == 0) {
                        studentCommissionStatistical.setCommissionActionAmount(commissionActionAmount.toString());
                    } else if (bdStudentStatisticalComparisonQueryDto.getStatusSettlement() == 1) {
                        studentCommissionStatistical.setCommissionActionAmount(settledCommissionActionAmount.toString());
                    }
                    Map<String, StudentStatistical> studentCommissionStatisticalMap = new HashMap<>();
                    studentCommissionStatisticalMap.put(timeInterval, studentCommissionStatistical);
                    bdStudentCommissionStatisticalComparisonDto.setStudentStatisticalMap(studentCommissionStatisticalMap);
                    bdStudentStatisticalComparisonVoList.add(bdStudentCommissionStatisticalComparisonDto);
                }

            }
        }

        //年份对比
        //下一年的数据 用于与本次年份数据对比
        for (BdStudentStatisticalComparisonVo bdStudentStatisticalComparisonVo : bdStudentStatisticalComparisonVoList) {
            //年份 - 学生数统计
            Map<String, StudentStatistical> studentStatisticalMap = bdStudentStatisticalComparisonVo.getStudentStatisticalMap();
            if (GeneralTool.isEmpty(studentStatisticalMap)) {
                studentStatisticalMap = new LinkedHashMap<>();
                for (int j = comparisonYears; j >= 0; j--) {
                    //本轮年份
//                    int currentRound = year - j;
                    String timeInterval = sf.format(DateUtil.minusYears(studentBeginTime, j)) + " : " + sf.format(DateUtil.minusYears(studentEndTime, j));
                    studentStatisticalMap.put(timeInterval, null);
                }
                bdStudentStatisticalComparisonVo.setStudentStatisticalMap(studentStatisticalMap);
            }
            //年份 - 学生数统计比率
            Map<String, StudentStatisticalRatio> studentStatisticalRatioMap = new LinkedHashMap<>();
            for (int i = comparisonYears; i > 0; i--) {
                //本轮年份
                int currentRound = year - i;
                String currentTimeInterval = sf.format(DateUtil.minusYears(studentBeginTime, i)) + " : " + sf.format(DateUtil.minusYears(studentEndTime, i));
                //上一年年份
                int lastYear = currentRound + 1;
                String lastTimeInterval = sf.format(DateUtil.minusYears(studentBeginTime, i - 1)) + " : " + sf.format(DateUtil.minusYears(studentEndTime, i - 1));

                studentStatisticalRatioMap.put(currentTimeInterval + " - " + lastTimeInterval, null);
            }

            for (int i = 0; i <= comparisonYears; i++) {
                //本轮年份
                int currentRound = year - i;
                String currentTimeInterval = sf.format(DateUtil.minusYears(studentBeginTime, i)) + " : " + sf.format(DateUtil.minusYears(studentEndTime, i));
                //上一年年份
                int lastYear = currentRound + 1;
                String lastTimeInterval = sf.format(DateUtil.minusYears(studentBeginTime, i + 1)) + " : " + sf.format(DateUtil.minusYears(studentEndTime, i + 1));


                //本轮年份统计数
                StudentStatistical studentStatistical = studentStatisticalMap.get(currentTimeInterval);
                if (GeneralTool.isEmpty(studentStatistical)) {
                    studentStatistical = new StudentStatistical();
                    studentStatistical.setOldFirstStudentNum(0);
                    studentStatistical.setNewFirstStudentNum(0);
                    studentStatistical.setAppOptStudentNum(0);
                    studentStatistical.setItemNum(0);
                    studentStatistical.setDelayItemNum(0);
                    studentStatistical.setOsNum(0);
                    studentStatistical.setVisaNum(0);
                    studentStatistical.setEnrolledNum(0);
                    studentStatistical.setEnrolledVisaNum(0);
                    studentStatistical.setTotalStudentNum(0);
                    studentStatistical.setNewTotalStudentNum(0);
                    studentStatistical.setOldTotalStudentNum(0);
                    studentStatistical.setAdmittedNum(0);
                    studentStatistical.setDelayAdmittedNum(0);
                    studentStatisticalMap.put(currentTimeInterval, studentStatistical);
                }
                //上一年份统计数
                StudentStatistical lastYearStudentStatistical = studentStatisticalMap.get(lastTimeInterval);
                //最后一年份，不需要和后面年份比对了
                if (currentRound == year - comparisonYears) {
                    continue;
                }
                if (GeneralTool.isEmpty(lastYearStudentStatistical)) {
                    lastYearStudentStatistical = new StudentStatistical();
                    lastYearStudentStatistical.setOldFirstStudentNum(0);
                    lastYearStudentStatistical.setNewFirstStudentNum(0);
                    lastYearStudentStatistical.setAppOptStudentNum(0);
                    lastYearStudentStatistical.setItemNum(0);
                    lastYearStudentStatistical.setDelayItemNum(0);
                    lastYearStudentStatistical.setOsNum(0);
                    lastYearStudentStatistical.setVisaNum(0);
                    lastYearStudentStatistical.setEnrolledNum(0);
                    lastYearStudentStatistical.setEnrolledVisaNum(0);
                    lastYearStudentStatistical.setTotalStudentNum(0);
                    lastYearStudentStatistical.setNewTotalStudentNum(0);
                    lastYearStudentStatistical.setOldTotalStudentNum(0);
                    lastYearStudentStatistical.setAdmittedNum(0);
                    lastYearStudentStatistical.setDelayAdmittedNum(0);
                    studentStatisticalMap.put(lastTimeInterval, lastYearStudentStatistical);
                }
                //学生数统计比率
                StudentStatisticalRatio studentStatisticalRatio = new StudentStatisticalRatio();
                //学生数增减比率
                if (lastYearStudentStatistical.getOldFirstStudentNum().equals(0)) {
                    studentStatisticalRatio.setOldStudentNumRatioNum(new BigDecimal(studentStatistical.getOldFirstStudentNum()));
                } else {
                    studentStatisticalRatio.setOldStudentNumRatio(new BigDecimal(studentStatistical.getOldFirstStudentNum() - lastYearStudentStatistical.getOldFirstStudentNum()).divide(new BigDecimal(lastYearStudentStatistical.getOldFirstStudentNum()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
                }
                if (lastYearStudentStatistical.getNewFirstStudentNum().equals(0)) {
                    studentStatisticalRatio.setNewStudentNumRatioNum(new BigDecimal(studentStatistical.getNewFirstStudentNum()));
                } else {
                    studentStatisticalRatio.setNewStudentNumRatio(new BigDecimal(studentStatistical.getNewFirstStudentNum() - lastYearStudentStatistical.getNewFirstStudentNum()).divide(new BigDecimal(lastYearStudentStatistical.getNewFirstStudentNum()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
                }
                if (lastYearStudentStatistical.getAppOptStudentNum().equals(0)) {
                    studentStatisticalRatio.setAppOptStudentNum(new BigDecimal(studentStatistical.getAppOptStudentNum()));
                } else {
                    studentStatisticalRatio.setAppOptStudentNumRatio(new BigDecimal(studentStatistical.getAppOptStudentNum() - lastYearStudentStatistical.getAppOptStudentNum()).divide(new BigDecimal(lastYearStudentStatistical.getAppOptStudentNum()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
                }
                //总学生数增减比率
                if (bdStudentStatisticalComparisonQueryDto.getStatisticalType() == 2 || bdStudentStatisticalComparisonQueryDto.getStatisticalType() == 3 || bdStudentStatisticalComparisonQueryDto.getStatisticalType() == 4) {
                    if (lastYearStudentStatistical.getTotalStudentNum().equals(0)) {
                        studentStatisticalRatio.setTotalStudentNum(new BigDecimal(studentStatistical.getTotalStudentNum()));
                    } else {
                        studentStatisticalRatio.setTotalStudentNumRatio(new BigDecimal(studentStatistical.getTotalStudentNum() - lastYearStudentStatistical.getTotalStudentNum()).divide(new BigDecimal(lastYearStudentStatistical.getTotalStudentNum()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
                    }
                }
                if (bdStudentStatisticalComparisonQueryDto.getStatisticalType() == 1 || bdStudentStatisticalComparisonQueryDto.getStatisticalType() == 2 || bdStudentStatisticalComparisonQueryDto.getStatisticalType() == 3 || bdStudentStatisticalComparisonQueryDto.getStatisticalType() == 4 || bdStudentStatisticalComparisonQueryDto.getStatisticalType() == 6 || bdStudentStatisticalComparisonQueryDto.getStatisticalType() == 7 || bdStudentStatisticalComparisonQueryDto.getStatisticalType() == 8) {
                    if (lastYearStudentStatistical.getNewTotalStudentNum().equals(0)) {
                        studentStatisticalRatio.setNewTotalStudentNum(new BigDecimal(studentStatistical.getNewTotalStudentNum()));
                    } else {
                        studentStatisticalRatio.setNewTotalStudentNumRatio(new BigDecimal(studentStatistical.getNewTotalStudentNum() - lastYearStudentStatistical.getNewTotalStudentNum()).divide(new BigDecimal(lastYearStudentStatistical.getNewTotalStudentNum()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
                    }
                    if (lastYearStudentStatistical.getOldTotalStudentNum().equals(0)) {
                        studentStatisticalRatio.setOldTotalStudentNum(new BigDecimal(studentStatistical.getOldTotalStudentNum()));
                    } else {
                        studentStatisticalRatio.setOldTotalStudentNumRatio(new BigDecimal(studentStatistical.getOldTotalStudentNum() - lastYearStudentStatistical.getOldTotalStudentNum()).divide(new BigDecimal(lastYearStudentStatistical.getOldTotalStudentNum()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
                    }
                }
                if (bdStudentStatisticalComparisonQueryDto.getStatisticalType() == 6 || bdStudentStatisticalComparisonQueryDto.getStatisticalType() == 8) {
                    if (lastYearStudentStatistical.getAdmittedNum().equals(0)) {
                        studentStatisticalRatio.setAdmittedRatioNum(new BigDecimal(studentStatistical.getAdmittedNum()));
                    } else {
                        studentStatisticalRatio.setAdmittedNumRatio(new BigDecimal(studentStatistical.getAdmittedNum() - lastYearStudentStatistical.getAdmittedNum()).divide(new BigDecimal(lastYearStudentStatistical.getAdmittedNum()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
                    }
                    if (lastYearStudentStatistical.getDelayAdmittedNum().equals(0)) {
                        studentStatisticalRatio.setAdmittedRatioDelayNum(new BigDecimal(studentStatistical.getDelayAdmittedNum()));
                    } else {
                        studentStatisticalRatio.setAdmittedNumDelayRatio(new BigDecimal(studentStatistical.getDelayAdmittedNum() - lastYearStudentStatistical.getDelayAdmittedNum()).divide(new BigDecimal(lastYearStudentStatistical.getDelayAdmittedNum()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
                    }
                }
                //os数增减比率
                if (lastYearStudentStatistical.getOsNum().equals(0)) {
                    studentStatisticalRatio.setOsNumRatioNum(new BigDecimal(studentStatistical.getOsNum()));
                } else {
                    studentStatisticalRatio.setOsNumRatio(new BigDecimal((studentStatistical.getOsNum() - lastYearStudentStatistical.getOsNum())).divide(new BigDecimal(lastYearStudentStatistical.getOsNum()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
                }
                if (bdStudentStatisticalComparisonQueryDto.getStatisticalType() == 6) {
                    //提交数减比率
                    if (lastYearStudentStatistical.getItemNum().equals(0)) {
                        studentStatisticalRatio.setItemNumRatioNum(new BigDecimal(studentStatistical.getItemNum()));
                    } else {
                        studentStatisticalRatio.setItemNumRatio(new BigDecimal((studentStatistical.getItemNum() - lastYearStudentStatistical.getItemNum())).divide(new BigDecimal(lastYearStudentStatistical.getItemNum()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
                    }
                    //延期提交数减比率
                    if (lastYearStudentStatistical.getDelayItemNum().equals(0)) {
                        studentStatisticalRatio.setItemNumRatioDelayNum(new BigDecimal(studentStatistical.getDelayItemNum()));
                    } else {
                        studentStatisticalRatio.setItemNumDelayRatio(new BigDecimal((studentStatistical.getDelayItemNum() - lastYearStudentStatistical.getDelayItemNum())).divide(new BigDecimal(lastYearStudentStatistical.getDelayItemNum()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
                    }
                    //签证数增减比率
                    if (lastYearStudentStatistical.getVisaNum().equals(0)) {
                        studentStatisticalRatio.setVisaNumRatioNum(new BigDecimal(studentStatistical.getVisaNum()));
                    } else {
                        studentStatisticalRatio.setVisaNumRatio(new BigDecimal((studentStatistical.getVisaNum() - lastYearStudentStatistical.getVisaNum())).divide(new BigDecimal(lastYearStudentStatistical.getVisaNum()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
                    }
                    //入学数增减比率
                    if (lastYearStudentStatistical.getEnrolledNum().equals(0)) {
                        studentStatisticalRatio.setEnrolledNumRatioNum(new BigDecimal(studentStatistical.getEnrolledNum()));
                    } else {
                        studentStatisticalRatio.setEnrolledNumRatio(new BigDecimal((studentStatistical.getEnrolledNum() - lastYearStudentStatistical.getEnrolledNum())).divide(new BigDecimal(lastYearStudentStatistical.getEnrolledNum()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
                    }
                    //后补签证增减比率
                    if (lastYearStudentStatistical.getEnrolledVisaNum().equals(0)) {
                        studentStatisticalRatio.setEnrolledVisaNumRatioNum(new BigDecimal(studentStatistical.getEnrolledVisaNum()));
                    } else {
                        studentStatisticalRatio.setEnrolledVisaNumRatio(new BigDecimal((studentStatistical.getEnrolledVisaNum() - lastYearStudentStatistical.getEnrolledVisaNum())).divide(new BigDecimal(lastYearStudentStatistical.getEnrolledVisaNum()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
                    }
                }
                studentStatisticalRatioMap.put(lastTimeInterval + " - " + currentTimeInterval, studentStatisticalRatio);
                bdStudentStatisticalComparisonVo.setStudentStatisticalRatioMap(studentStatisticalRatioMap);
            }

        }

        if (!bdStudentStatisticalComparisonQueryDto.getDisplayModeFlag()) {
            //小计对象
            BdStudentStatisticalComparisonVo bdStudentStatisticalComparisonSubtotalDto = new BdStudentStatisticalComparisonVo();
            //年份 - 学生数统计 总计
            Map<String, StudentStatistical> studentStatisticalTotalMap = new LinkedHashMap<>();
            for (int i = comparisonYears; i >= 0; i--) {
                //本轮年份
//                int currentRound = year - i;
                String timeInterval = sf.format(DateUtil.minusYears(studentBeginTime, i)) + " : " + sf.format(DateUtil.minusYears(studentEndTime, i));
                //先给个里面数值为0的默认对象，以防后续这个年份没有数据 空指针
                StudentStatistical studentStatistical = new StudentStatistical();
                studentStatistical.setNewFirstStudentNum(0);
                studentStatistical.setOldFirstStudentNum(0);
                studentStatistical.setAppOptStudentNum(0);
                studentStatistical.setItemNum(0);
                studentStatistical.setDelayItemNum(0);
                studentStatistical.setOsNum(0);
                studentStatistical.setVisaNum(0);
                studentStatistical.setEnrolledNum(0);
                studentStatistical.setEnrolledVisaNum(0);
                studentStatistical.setTotalStudentNum(0);
                studentStatistical.setNewTotalStudentNum(0);
                studentStatistical.setOldTotalStudentNum(0);
                studentStatistical.setAdmittedNum(0);
                studentStatistical.setDelayAdmittedNum(0);
                studentStatisticalTotalMap.put(timeInterval, studentStatistical);
            }

            for (BdStudentStatisticalComparisonVo bdStudentStatisticalComparisonVo : bdStudentStatisticalComparisonVoList) {
                for (int i = 0; i <= comparisonYears; i++) {
                    //本轮年份
                    int currentRound = year - i;
                    String timeInterval = sf.format(DateUtil.minusYears(studentBeginTime, i)) + " : " + sf.format(DateUtil.minusYears(studentEndTime, i));
                    Map<String, StudentStatistical> studentStatisticalMap = bdStudentStatisticalComparisonVo.getStudentStatisticalMap();
                    StudentStatistical studentStatistical = studentStatisticalMap.get(timeInterval);
                    //学生数统计 总计       该TargetId的总计map 取出该年份的数 相加 最终得到该年份的总数
                    StudentStatistical studentStatisticalTotal = studentStatisticalTotalMap.get(timeInterval);
                    if (GeneralTool.isEmpty(studentStatisticalTotal)) {
                        studentStatistical = studentStatisticalMap.get(timeInterval);
                        StudentStatistical clone = BeanUtil.clone(studentStatistical);
                        studentStatisticalTotalMap.put(timeInterval, clone);
                        continue;
                    }
                    if (bdStudentStatisticalComparisonQueryDto.getStatisticalType() == 2 || bdStudentStatisticalComparisonQueryDto.getStatisticalType() == 3 || bdStudentStatisticalComparisonQueryDto.getStatisticalType() == 4) {
                        studentStatisticalTotal.setTotalStudentNum(studentStatisticalTotal.getTotalStudentNum() + studentStatistical.getTotalStudentNum());
                    }
                    if (bdStudentStatisticalComparisonQueryDto.getStatisticalType() == 1 || bdStudentStatisticalComparisonQueryDto.getStatisticalType() == 2 || bdStudentStatisticalComparisonQueryDto.getStatisticalType() == 3 || bdStudentStatisticalComparisonQueryDto.getStatisticalType() == 4 || bdStudentStatisticalComparisonQueryDto.getStatisticalType() == 6 || bdStudentStatisticalComparisonQueryDto.getStatisticalType() == 7 || bdStudentStatisticalComparisonQueryDto.getStatisticalType() == 8) {
                        studentStatisticalTotal.setNewTotalStudentNum(studentStatisticalTotal.getNewTotalStudentNum() + studentStatistical.getNewTotalStudentNum());
                        studentStatisticalTotal.setOldTotalStudentNum(studentStatisticalTotal.getOldTotalStudentNum() + studentStatistical.getOldTotalStudentNum());
                    }
                    if (bdStudentStatisticalComparisonQueryDto.getStatisticalType() == 6 || bdStudentStatisticalComparisonQueryDto.getStatisticalType() == 8) {
                        studentStatisticalTotal.setAdmittedNum(studentStatisticalTotal.getAdmittedNum() + studentStatistical.getAdmittedNum());
                        studentStatisticalTotal.setDelayAdmittedNum(studentStatisticalTotal.getDelayAdmittedNum() + studentStatistical.getDelayAdmittedNum());
                    }
                    studentStatisticalTotal.setNewFirstStudentNum(studentStatisticalTotal.getNewFirstStudentNum() + studentStatistical.getNewFirstStudentNum());
                    studentStatisticalTotal.setAppOptStudentNum(studentStatisticalTotal.getAppOptStudentNum() + studentStatistical.getAppOptStudentNum());
                    studentStatisticalTotal.setOldFirstStudentNum(studentStatisticalTotal.getOldFirstStudentNum() + studentStatistical.getOldFirstStudentNum());
                    studentStatisticalTotal.setOsNum(studentStatisticalTotal.getOsNum() + studentStatistical.getOsNum());
                }
            }
            bdStudentStatisticalComparisonSubtotalDto.setStudentStatisticalMap(studentStatisticalTotalMap);
            bdStudentStatisticalComparisonSubtotalDto.setAreaCountryName("小计");
            Map<String, StudentStatisticalRatio> studentStatisticalRatioMap = new LinkedHashMap<>();
            //这个算法是对的
            for (int i = comparisonYears; i > 0; i--) {
                //本轮年份
                int currentRound = year - i;
                String currentTimeInterval = sf.format(DateUtil.minusYears(studentBeginTime, i)) + " : " + sf.format(DateUtil.minusYears(studentEndTime, i));
                //上一年年份
                int lastYear = currentRound + 1;
                String lastTimeInterval = sf.format(DateUtil.minusYears(studentBeginTime, i - 1)) + " : " + sf.format(DateUtil.minusYears(studentEndTime, i - 1));

                studentStatisticalRatioMap.put(currentTimeInterval + " - " + lastTimeInterval, null);
            }
            //计算上一循环的 年份对比增减比率
            for (int i = 0; i <= comparisonYears; i++) {
                //年份 - 学生数统计比率
                //本轮年份
                int currentRound = year - i;
                String currentTimeInterval = sf.format(DateUtil.minusYears(studentBeginTime, i)) + " : " + sf.format(DateUtil.minusYears(studentEndTime, i));
                //上一年年份
                int lastYear = currentRound - 1;
                String lastTimeInterval = sf.format(DateUtil.minusYears(studentBeginTime, i + 1)) + " : " + sf.format(DateUtil.minusYears(studentEndTime, i + 1));


                //本轮年份统计数
                StudentStatistical studentStatistical = studentStatisticalTotalMap.get(currentTimeInterval);
                //上一年份统计数
                StudentStatistical lastYearStudentStatistical = studentStatisticalTotalMap.get(lastTimeInterval);
                //最后一年份，不需要和后面年份比对了
                if (currentRound == year - comparisonYears) {
                    continue;
                }
                //学生数统计比率
                StudentStatisticalRatio studentStatisticalRatio = new StudentStatisticalRatio();
                //学生数增减比率
                if (lastYearStudentStatistical.getOldFirstStudentNum().equals(0)) {
                    studentStatisticalRatio.setOldStudentNumRatioNum(new BigDecimal(studentStatistical.getOldFirstStudentNum()));
                } else {
                    studentStatisticalRatio.setOldStudentNumRatio(new BigDecimal((studentStatistical.getOldFirstStudentNum() - lastYearStudentStatistical.getOldFirstStudentNum())).divide(new BigDecimal(lastYearStudentStatistical.getOldFirstStudentNum()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
                }
                if (lastYearStudentStatistical.getNewFirstStudentNum().equals(0)) {
                    studentStatisticalRatio.setNewStudentNumRatioNum(new BigDecimal(studentStatistical.getNewFirstStudentNum()));
                } else {
                    studentStatisticalRatio.setNewStudentNumRatio(new BigDecimal((studentStatistical.getNewFirstStudentNum() - lastYearStudentStatistical.getNewFirstStudentNum())).divide(new BigDecimal(lastYearStudentStatistical.getNewFirstStudentNum()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
                }
                if (lastYearStudentStatistical.getAppOptStudentNum().equals(0)) {
                    studentStatisticalRatio.setAppOptStudentNum(new BigDecimal(studentStatistical.getAppOptStudentNum()));
                } else {
                    studentStatisticalRatio.setAppOptStudentNumRatio(new BigDecimal((studentStatistical.getAppOptStudentNum() - lastYearStudentStatistical.getAppOptStudentNum())).divide(new BigDecimal(lastYearStudentStatistical.getAppOptStudentNum()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
                }
                //总学生数增减比率
                if (bdStudentStatisticalComparisonQueryDto.getStatisticalType() == 2 || bdStudentStatisticalComparisonQueryDto.getStatisticalType() == 3 || bdStudentStatisticalComparisonQueryDto.getStatisticalType() == 4) {
                    if (lastYearStudentStatistical.getTotalStudentNum().equals(0)) {
                        studentStatisticalRatio.setTotalStudentNum(new BigDecimal(studentStatistical.getTotalStudentNum()));
                    } else {
                        studentStatisticalRatio.setTotalStudentNumRatio(new BigDecimal((studentStatistical.getTotalStudentNum() - lastYearStudentStatistical.getTotalStudentNum())).divide(new BigDecimal(lastYearStudentStatistical.getTotalStudentNum()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
                    }
                }
                if (bdStudentStatisticalComparisonQueryDto.getStatisticalType() == 1 || bdStudentStatisticalComparisonQueryDto.getStatisticalType() == 2 || bdStudentStatisticalComparisonQueryDto.getStatisticalType() == 3 || bdStudentStatisticalComparisonQueryDto.getStatisticalType() == 4 || bdStudentStatisticalComparisonQueryDto.getStatisticalType() == 6 || bdStudentStatisticalComparisonQueryDto.getStatisticalType() == 7 || bdStudentStatisticalComparisonQueryDto.getStatisticalType() == 8) {
                    if (lastYearStudentStatistical.getNewTotalStudentNum().equals(0)) {
                        studentStatisticalRatio.setNewTotalStudentNum(new BigDecimal(studentStatistical.getNewTotalStudentNum()));
                    } else {
                        studentStatisticalRatio.setNewTotalStudentNumRatio(new BigDecimal((studentStatistical.getNewTotalStudentNum() - lastYearStudentStatistical.getNewTotalStudentNum())).divide(new BigDecimal(lastYearStudentStatistical.getNewTotalStudentNum()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
                    }
                    if (lastYearStudentStatistical.getOldTotalStudentNum().equals(0)) {
                        studentStatisticalRatio.setOldTotalStudentNum(new BigDecimal(studentStatistical.getOldTotalStudentNum()));
                    } else {
                        studentStatisticalRatio.setOldTotalStudentNumRatio(new BigDecimal((studentStatistical.getOldTotalStudentNum() - lastYearStudentStatistical.getOldTotalStudentNum())).divide(new BigDecimal(lastYearStudentStatistical.getOldTotalStudentNum()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
                    }
                }
                if (bdStudentStatisticalComparisonQueryDto.getStatisticalType() == 6 || bdStudentStatisticalComparisonQueryDto.getStatisticalType() == 8) {
                    if (lastYearStudentStatistical.getAdmittedNum().equals(0)) {
                        studentStatisticalRatio.setAdmittedRatioNum(new BigDecimal(studentStatistical.getAdmittedNum()));
                    } else {
                        studentStatisticalRatio.setAdmittedNumRatio(new BigDecimal((studentStatistical.getAdmittedNum() - lastYearStudentStatistical.getAdmittedNum())).divide(new BigDecimal(lastYearStudentStatistical.getAdmittedNum()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
                    }
                    if (lastYearStudentStatistical.getAdmittedNum().equals(0)) {
                        studentStatisticalRatio.setAdmittedRatioDelayNum(new BigDecimal(studentStatistical.getDelayAdmittedNum()));
                    } else {
                        studentStatisticalRatio.setAdmittedNumDelayRatio(new BigDecimal((studentStatistical.getDelayAdmittedNum() - lastYearStudentStatistical.getDelayAdmittedNum())).divide(new BigDecimal(lastYearStudentStatistical.getDelayAdmittedNum()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
                    }
                }
                //os数增减比率
                if (lastYearStudentStatistical.getOsNum().equals(0)) {
                    studentStatisticalRatio.setOsNumRatioNum(new BigDecimal(studentStatistical.getOsNum()));
                } else {
                    studentStatisticalRatio.setOsNumRatio(new BigDecimal((studentStatistical.getOsNum() - lastYearStudentStatistical.getOsNum())).divide(new BigDecimal(lastYearStudentStatistical.getOsNum()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
                }
                if (bdStudentStatisticalComparisonQueryDto.getStatisticalType() == 6) {
                    //申请数减比率
                    if (lastYearStudentStatistical.getItemNum().equals(0)) {
                        studentStatisticalRatio.setItemNumRatioNum(new BigDecimal(studentStatistical.getItemNum()));
                    } else {
                        studentStatisticalRatio.setItemNumRatio(new BigDecimal((studentStatistical.getItemNum() - lastYearStudentStatistical.getItemNum())).divide(new BigDecimal(lastYearStudentStatistical.getItemNum()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
                    }
                    //延期提交数减比率
                    if (lastYearStudentStatistical.getDelayItemNum().equals(0)) {
                        studentStatisticalRatio.setItemNumRatioDelayNum(new BigDecimal(studentStatistical.getDelayItemNum()));
                    } else {
                        studentStatisticalRatio.setItemNumDelayRatio(new BigDecimal((studentStatistical.getDelayItemNum() - lastYearStudentStatistical.getDelayItemNum())).divide(new BigDecimal(lastYearStudentStatistical.getDelayItemNum()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
                    }
                    //签证数增减比率
                    if (lastYearStudentStatistical.getVisaNum().equals(0)) {
                        studentStatisticalRatio.setVisaNumRatioNum(new BigDecimal(studentStatistical.getVisaNum()));
                    } else {
                        studentStatisticalRatio.setVisaNumRatio(new BigDecimal((studentStatistical.getVisaNum() - lastYearStudentStatistical.getVisaNum())).divide(new BigDecimal(lastYearStudentStatistical.getVisaNum()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
                    }
                    //入学数增减比率
                    if (lastYearStudentStatistical.getEnrolledNum().equals(0)) {
                        studentStatisticalRatio.setEnrolledNumRatioNum(new BigDecimal(studentStatistical.getEnrolledNum()));
                    } else {
                        studentStatisticalRatio.setEnrolledNumRatio(new BigDecimal((studentStatistical.getEnrolledNum() - lastYearStudentStatistical.getEnrolledNum())).divide(new BigDecimal(lastYearStudentStatistical.getEnrolledNum()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
                    }
                    //后补签证增减比率
                    if (lastYearStudentStatistical.getEnrolledVisaNum().equals(0)) {
                        studentStatisticalRatio.setEnrolledVisaNumRatioNum(new BigDecimal(studentStatistical.getEnrolledVisaNum()));
                    } else {
                        studentStatisticalRatio.setEnrolledVisaNumRatio(new BigDecimal((studentStatistical.getEnrolledVisaNum() - lastYearStudentStatistical.getEnrolledVisaNum())).divide(new BigDecimal(lastYearStudentStatistical.getEnrolledVisaNum()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
                    }
                }
                studentStatisticalRatioMap.put(lastTimeInterval + " - " + currentTimeInterval, studentStatisticalRatio);
            }
            bdStudentStatisticalComparisonSubtotalDto.setStudentStatisticalRatioMap(studentStatisticalRatioMap);
            bdStudentStatisticalComparisonVoList.add(bdStudentStatisticalComparisonSubtotalDto);
        }

        if (bdStudentStatisticalComparisonQueryDto.getStatisticalType() != 6 && bdStudentStatisticalComparisonQueryDto.getStatisticalType() != 8) {
            //个数总计
            Long num = studentOfferItemMapper.getBdStudentStatisticalBdTotal(bdStudentStatisticalComparisonQueryDto, DateUtil.minusYears(studentBeginTime, comparisonYears), studentEndTime, statisticalComparisonYearDtoList, countryIds);
            BdStudentStatisticalComparisonVo bdStudentStatisticalComparisonVo = new BdStudentStatisticalComparisonVo();

            switch (bdStudentStatisticalComparisonQueryDto.getStatisticalType()) {
                case 1:
                    bdStudentStatisticalComparisonVo.setBdName("BD共" + num.toString() + "个");
                    break;
                case 2:
                    bdStudentStatisticalComparisonVo.setRegion("区域共" + num.toString() + "个");
                    break;
                case 3:
                    bdStudentStatisticalComparisonVo.setRegion("大区共" + num.toString() + "个");
                    break;
                case 4:
                    bdStudentStatisticalComparisonVo.setBdName("代理共" + num.toString() + "个");
                    break;
            }
            bdStudentStatisticalComparisonVoList.add(bdStudentStatisticalComparisonVo);
        }
        return bdStudentStatisticalComparisonVoList;
    }

    /**
     * BD学生统计对比表 条件回显
     *
     * @Date 15:42 2023/1/6
     * <AUTHOR>
     */
    @Override
    public BdStudentStatisticalConditionalEchoVo bdStudentStatisticalComparisonConditionalEcho(BdStudentStatisticalComparisonDto bdStudentStatisticalComparisonQueryDto) {
        BdStudentStatisticalConditionalEchoVo bdStudentStatisticalConditionalEchoVo = new BdStudentStatisticalConditionalEchoVo();
        List<String> companyNames = permissionCenterClient.getCompanyNamesByIdsDESC(new HashSet<>(bdStudentStatisticalComparisonQueryDto.getFkCompanyIds())).getData();
//        StringBuilder sb = new StringBuilder();
//        for (String companyName : companyNameMap.values()) {
//            sb.append(companyName).append(",");
//        }
//        String companyNameStr = sb.toString();
//        if (companyNameStr.endsWith(",")) {
//            companyNameStr = companyNameStr.substring(0, companyNameStr.length() - 1);
//        }
        String companyNameStr = companyNames.stream().collect(Collectors.joining(","));
        bdStudentStatisticalConditionalEchoVo.setCompanyNameStr(companyNameStr);

        if (GeneralTool.isNotEmpty(bdStudentStatisticalComparisonQueryDto.getStudentBeginTime()) || GeneralTool.isNotEmpty(bdStudentStatisticalComparisonQueryDto.getStudentEndTime())) {
            SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
            bdStudentStatisticalConditionalEchoVo.setCreamTimeStr(sf.format(bdStudentStatisticalComparisonQueryDto.getStudentBeginTime()) + "到" + sf.format(bdStudentStatisticalComparisonQueryDto.getStudentEndTime()));
        }
        if (GeneralTool.isNotEmpty(bdStudentStatisticalComparisonQueryDto.getComparisonYears())) {
            bdStudentStatisticalConditionalEchoVo.setComparisonYearsStr(bdStudentStatisticalComparisonQueryDto.getComparisonYears().toString());
        }
        if (GeneralTool.isNotEmpty(bdStudentStatisticalComparisonQueryDto.getAreaCountryIds())) {
            Map<Long, String> countryChnNameMap = institutionCenterClient.getCountryChnNameByIds(bdStudentStatisticalComparisonQueryDto.getAreaCountryIds()).getData();
            if (GeneralTool.isNotEmpty(countryChnNameMap)) {
                List<String> countryChnNameList = countryChnNameMap.values().stream().collect(Collectors.toList());
                String countryChnNames = StringUtils.join(countryChnNameList.toArray(), ",");
                bdStudentStatisticalConditionalEchoVo.setCountryStr(countryChnNames);
            }
        }

//        if (GeneralTool.isNotEmpty(bdStudentStatisticalComparisonVo.getProjectRoleId())) {
//            StudentProjectRole studentProjectRole = studentProjectRoleMapper.selectById(bdStudentStatisticalComparisonVo.getProjectRoleId());
//            if (GeneralTool.isNotEmpty(studentProjectRole)) {
//                bdStudentStatisticalConditionalEchoVo.setProjectRoleName(studentProjectRole.getRoleName());
//            }
//        }
        bdStudentStatisticalConditionalEchoVo.setProjectRoleName(bdStudentStatisticalComparisonQueryDto.getProjectRoleName());

        if (GeneralTool.isNotEmpty(bdStudentStatisticalComparisonQueryDto.getFkStaffIds())) {
            Map<Long, String> staffNameMap = permissionCenterClient.getStaffNameMap(bdStudentStatisticalComparisonQueryDto.getFkStaffIds()).getData();
            if (GeneralTool.isNotEmpty(staffNameMap)) {
                bdStudentStatisticalConditionalEchoVo.setStaffName(staffNameMap.values().stream().collect(Collectors.toSet()));
            }
        }

        if (GeneralTool.isNotEmpty(bdStudentStatisticalComparisonQueryDto.getFkAreaRegionIdLIst())) {
            Map<Long, AreaRegionVo> regionDtoMap = institutionCenterClient.getAreaRegionDtoByIds(bdStudentStatisticalComparisonQueryDto.getFkAreaRegionIdLIst()).getData();
            if (GeneralTool.isNotEmpty(regionDtoMap)) {
                List<String> regionNameList = regionDtoMap.values().stream().map(AreaRegionVo::getNameChn).collect(Collectors.toList());
                String regionNames = StringUtils.join(regionNameList.toArray(), ",");
                bdStudentStatisticalConditionalEchoVo.setAreaRegionStr(regionNames);
            }
        }

        //代理所在区域

        if (GeneralTool.isNotEmpty(bdStudentStatisticalComparisonQueryDto.getFkAreaCityIdList())) {
            bdStudentStatisticalConditionalEchoVo.setAgentAreaStr(institutionCenterClient.getAreaCityNameByIds(bdStudentStatisticalComparisonQueryDto.getFkAreaCityIdList()).getData());
        } else if (GeneralTool.isNotEmpty(bdStudentStatisticalComparisonQueryDto.getFkAreaStateIdList())) {
            bdStudentStatisticalConditionalEchoVo.setAgentAreaStr(institutionCenterClient.getAreaStateNameByIds(bdStudentStatisticalComparisonQueryDto.getFkAreaStateIdList()).getData());
        }

        Map<Long, StaffBdCode> bDbyStaffMap = staffBdCodeService.getBDbyStaffIds(bdStudentStatisticalComparisonQueryDto.getFkStaffIds());
        if (GeneralTool.isNotEmpty(bDbyStaffMap)) {
            List<String> bdCodeList = bDbyStaffMap.values().stream().map(StaffBdCode::getBdCode).collect(Collectors.toList());
            String bdCodes = StringUtils.join(bdCodeList.toArray(), ",");
            bdStudentStatisticalConditionalEchoVo.setBdCodeStr(bdCodes);
        }
        bdStudentStatisticalConditionalEchoVo.setAgentNumStr(bdStudentStatisticalComparisonQueryDto.getAgentNumStr());
        return bdStudentStatisticalConditionalEchoVo;
    }

    /**
     * hti首页学生统计柱状图
     *
     * @Date · 2023/2/16
     * <AUTHOR>
     */
    @Override
    public List<StudentStatistical> htiHomeBarChart(BdStudentStatisticalComparisonDto bdStudentStatisticalComparisonQueryDto) {
        //获取员工以及旗下员工
        Long staffId = SecureUtil.getStaffId();
        List<Long> staffFollowerIds = new ArrayList<>();
        Result<List<Long>> result = permissionCenterClient.getStaffFollowerIds(staffId);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            staffFollowerIds.addAll(result.getData());
        }
        staffFollowerIds.add(staffId);
        ConfigVo configVo = permissionCenterClient.getConfigByKey("HTI_BMS_START_TIME").getData();
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
        Date htiStartTime;
        try {
            htiStartTime = sf.parse(configVo.getValue1());
        } catch (ParseException e) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("DATE_FORMAT_CONVERSION_ERROR"));
        }
        DelayConfigDto delayConfig = getDelayConfig();
        Boolean isBd = studentOfferService.getIsBd(staffId);
        return studentOfferItemMapper.getBdStudentStatistical(bdStudentStatisticalComparisonQueryDto, null,
                bdStudentStatisticalComparisonQueryDto.getStudentBeginTime(), bdStudentStatisticalComparisonQueryDto.getStudentEndTime(),
                staffFollowerIds, SecureUtil.getCountryIds(), SecureUtil.getStaffInfo().getIsStudentOfferItemFinancialHiding(),
                SecureUtil.getStaffInfo().getIsStudentAdmin(), SecureUtil.getCountryIds(), htiStartTime, false, delayConfig, isBd,
                SecureUtil.getPermissionGroupInstitutionIds(),
                SecureUtil.getStaffBoundBdIds());
    }


    /**
     * 获取角色提成规则
     *
     * @Date 16:16 2023/3/8
     * <AUTHOR>
     */
    @Override
    public List<StaffCommissionPolicyVo> getBdStaffCommissionPolicy(Long fkCompanyId, String projectRoleKey) {
        return staffCommissionPolicyMapper.getBdStaffCommissionPolicy(fkCompanyId, projectRoleKey);
    }

    /**
     * bd角色提成结算
     *
     * @Date 17:18 2023/3/10
     * <AUTHOR>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bdStaffCommissionPolicySettleAccounts(BdStudentStatisticalComparisonDto bdStudentStatisticalComparisonDto) {
        // 查询
        List<StaffCommissionPolicyVo> bdStaffCommissionPolicyList = staffCommissionPolicyMapper.getBdStaffCommissionPolicy(0L, bdStudentStatisticalComparisonDto.getProjectRoleKey());
        //员工提成业务步骤key
        Map<String, BigDecimal> commissionPolicMap = new HashMap<>();
        //计算未结算金额 匹配提成规则
        for (StaffCommissionPolicyVo staffCommissionPolicyVo : bdStaffCommissionPolicyList) {
            commissionPolicMap.put(staffCommissionPolicyVo.getFkStaffCommissionStepKey(), staffCommissionPolicyVo.getFixedAmount());
        }
        //新申请提成金额
        BigDecimal newAmount = GeneralTool.isNotEmpty(commissionPolicMap.get(ProjectKeyEnum.STEP_NEW_APP.key)) ? commissionPolicMap.get(ProjectKeyEnum.STEP_NEW_APP.key) : BigDecimal.ZERO;
        //OS提成金额
        BigDecimal osAmount = GeneralTool.isNotEmpty(commissionPolicMap.get(ProjectKeyEnum.STEP_OFFER_SELECTION.key)) ? commissionPolicMap.get(ProjectKeyEnum.STEP_OFFER_SELECTION.key) : BigDecimal.ZERO;
        //签证数提成金额
        BigDecimal visaAmount = GeneralTool.isNotEmpty(commissionPolicMap.get(ProjectKeyEnum.STEP_VISA_GRANTED.key)) ? commissionPolicMap.get(ProjectKeyEnum.STEP_VISA_GRANTED.key) : BigDecimal.ZERO;
        //入学提成金额
        BigDecimal enrolledAmount = GeneralTool.isNotEmpty(commissionPolicMap.get(ProjectKeyEnum.STEP_ENROLLED.key)) ? commissionPolicMap.get(ProjectKeyEnum.STEP_ENROLLED.key) : BigDecimal.ZERO;
        //后补签证金额
        BigDecimal enrolledVisaAmount = GeneralTool.isNotEmpty(commissionPolicMap.get(ProjectKeyEnum.STEP_VISA_GRANTED_BACK.key)) ? commissionPolicMap.get(ProjectKeyEnum.STEP_VISA_GRANTED_BACK.key) : BigDecimal.ZERO;
        //入学提成金额
        BigDecimal admittedAmount = GeneralTool.isNotEmpty(commissionPolicMap.get(ProjectKeyEnum.STEP_ADMITTED.key)) ? commissionPolicMap.get(ProjectKeyEnum.STEP_ADMITTED.key) : BigDecimal.ZERO;
        //延迟提交数金额
        BigDecimal submittedDelayAmount = GeneralTool.isNotEmpty(commissionPolicMap.get(ProjectKeyEnum.STEP_SUBMITTED_DELAY.key)) ? commissionPolicMap.get(ProjectKeyEnum.STEP_SUBMITTED_DELAY.key) : BigDecimal.ZERO;
        //延迟反馈数金额
        BigDecimal admittedDelayAmount = GeneralTool.isNotEmpty(commissionPolicMap.get(ProjectKeyEnum.STEP_ADMITTED_DELAY.key)) ? commissionPolicMap.get(ProjectKeyEnum.STEP_ADMITTED_DELAY.key) : BigDecimal.ZERO;
        // 得到金额
        //角色key
//        StudentProjectRole studentProjectRole = studentProjectRoleMapper.selectById(bdStudentStatisticalComparisonDto.getProjectRoleId());
//        String roleKey = studentProjectRole.getRoleKey();
        String roleKey = bdStudentStatisticalComparisonDto.getProjectRoleKey();
        DelayConfigDto delayConfig = getDelayConfig();
        inserCommissionAction(bdStudentStatisticalComparisonDto, newAmount, roleKey, "STEP_NEW_APP", delayConfig);
        inserCommissionAction(bdStudentStatisticalComparisonDto, osAmount, roleKey, "STEP_OFFER_SELECTION", delayConfig);
        inserCommissionAction(bdStudentStatisticalComparisonDto, visaAmount, roleKey, "STEP_VISA_GRANTED", delayConfig);
        inserCommissionAction(bdStudentStatisticalComparisonDto, enrolledAmount, roleKey, "STEP_ENROLLED", delayConfig);
        inserCommissionAction(bdStudentStatisticalComparisonDto, enrolledVisaAmount, roleKey, ProjectKeyEnum.STEP_VISA_GRANTED_BACK.key, delayConfig);
        inserCommissionAction(bdStudentStatisticalComparisonDto, admittedAmount, roleKey, "STEP_ADMITTED", delayConfig);
        inserCommissionAction(bdStudentStatisticalComparisonDto, submittedDelayAmount, roleKey, ProjectKeyEnum.STEP_SUBMITTED_DELAY.key, delayConfig);
        inserCommissionAction(bdStudentStatisticalComparisonDto, admittedDelayAmount, roleKey, ProjectKeyEnum.STEP_ADMITTED_DELAY.key, delayConfig);

    }


    /**
     * 代理年度总表对比统计
     *
     * @Date 11:11 2023/3/16
     * <AUTHOR>
     */
    @Override
    public List<AgentAnnualSummaryStatisticsVo> agentAnnualSummaryStatistics(AgentAnnualSummaryDto agentAnnualSummaryDto, List<Long> countryIds, Long staffId, List<Long> institutionIds) {
        List<AgentAnnualSummaryStatisticsVo> agentAnnualSummaryStatisticsVoList = new ArrayList<>();
        if (GeneralTool.isEmpty(countryIds)) {
            return agentAnnualSummaryStatisticsVoList;
        }
        //同期比对年数
        Integer comparisonYears = agentAnnualSummaryDto.getComparisonYears();
        //查询时间
        Date studentBeginTime = agentAnnualSummaryDto.getBeginTime();
        Date studentEndTime = agentAnnualSummaryDto.getEndTime();
        //查询初始年份
        Calendar cal = Calendar.getInstance();
        cal.setTime(studentBeginTime);
        int year = cal.get(Calendar.YEAR);

        //比对年份List
        List<StatisticalComparisonYearDto> statisticalComparisonYearDtoList = new ArrayList<>();
        for (int i = 0; i <= comparisonYears; i++) {
            Date beginTime = DateUtil.minusYears(studentBeginTime, i);
            Date endTime = DateUtil.minusYears(studentEndTime, i);
            StatisticalComparisonYearDto statisticalComparisonYearDto = new StatisticalComparisonYearDto();
            statisticalComparisonYearDto.setBeginTime(beginTime);
            statisticalComparisonYearDto.setEndTime(endTime);
            statisticalComparisonYearDtoList.add(statisticalComparisonYearDto);
        }

        //员工id + 业务下属员工ids
        List<Long> staffFollowerIds = permissionCenterClient.getStaffFollowerIds(staffId).getData().stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        staffFollowerIds.add(staffId);


        //只要有数据的
        agentAnnualSummaryStatisticsVoList = studentOfferItemMapper.selectAgentAnnualSummaryStatistics(agentAnnualSummaryDto, statisticalComparisonYearDtoList, countryIds, staffFollowerIds, institutionIds);

        if (GeneralTool.isEmpty(agentAnnualSummaryStatisticsVoList)) {
            return agentAnnualSummaryStatisticsVoList;
        }

        //先刷一遍默认0以及排序
        for (AgentAnnualSummaryStatisticsVo agentAnnualSummaryStatisticsVo : agentAnnualSummaryStatisticsVoList) {
            //插入初始数据 0
            insertInitStatistics(comparisonYears, year, agentAnnualSummaryStatisticsVoList, agentAnnualSummaryStatisticsVo, agentAnnualSummaryDto.getDisplayModeFlag());
        }

        Set<Long> bdIds = agentAnnualSummaryStatisticsVoList.stream().map(AgentAnnualSummaryStatisticsVo::getBdId).collect(Collectors.toSet());

        //查询每年的数据
        for (int i = 1; i <= comparisonYears + 1; i++) {
            Date beginTime = DateUtil.minusYears(studentBeginTime, i - 1);
            Date endTime = DateUtil.minusYears(studentEndTime, i - 1);
            agentAnnualSummaryDto.setJumpBeginTime(beginTime);
            agentAnnualSummaryDto.setJumpEndTime(endTime);
            //查询本轮年份
            cal.setTime(beginTime);
            int nowYear = cal.get(Calendar.YEAR);

            //本循环年各BD数据
            List<AgentAnnualStatisticsVo> agentAnnualStatisticsVoList = studentOfferItemMapper.agentAnnualSummaryStatistics(agentAnnualSummaryDto,
                    countryIds, staffFollowerIds, institutionIds);
            for (AgentAnnualStatisticsVo agentAnnualStatisticsVo : agentAnnualStatisticsVoList) {
                //找到bd主结构，往里面塞数据
                AgentAnnualSummaryStatisticsVo agentAnnualSummaryStatistics = null;
                for (AgentAnnualSummaryStatisticsVo agentAnnualSummaryStatisticsVo : agentAnnualSummaryStatisticsVoList) {
                    if (agentAnnualSummaryStatisticsVo.getBdId().equals(agentAnnualStatisticsVo.getBdId())) {
                        agentAnnualSummaryStatistics = agentAnnualSummaryStatisticsVo;
                    }
                }
                //年份 - (月份-代理数统计）
                Map<Integer, Map<String, AgentAnnualStatisticsVo>> agentAnnualStatisticsMap = agentAnnualSummaryStatistics.getAgentAnnualStatisticsMap();
                //本轮年份的 月份-代理数统计
                Map<String, AgentAnnualStatisticsVo> agentAnnualStatisticsDtoMap = agentAnnualStatisticsMap.get(nowYear);
                agentAnnualStatisticsDtoMap.put(String.valueOf(agentAnnualStatisticsVo.getMonth()), agentAnnualStatisticsVo);
            }
        }

        //计算该代理这一整年 这一行的代理数量小计
        if (!agentAnnualSummaryDto.getDisplayModeFlag()) {
            for (AgentAnnualSummaryStatisticsVo agentAnnualSummaryStatisticsVo : agentAnnualSummaryStatisticsVoList) {
                for (int i = 0; i <= comparisonYears; i++) {
                    //本轮年份
                    int currentRound = year - comparisonYears + i;

                    Long agentNum = 0L;
                    Map<String, AgentAnnualStatisticsVo> stringAgentAnnualStatisticsDtoMap = agentAnnualSummaryStatisticsVo.getAgentAnnualStatisticsMap().get(currentRound);
                    for (int j = 1; j < 12 + 1; j++) {
                        AgentAnnualStatisticsVo agentAnnualStatisticsVo2 = stringAgentAnnualStatisticsDtoMap.get(String.valueOf(j));
                        agentNum = agentNum + agentAnnualStatisticsVo2.getAgentNum();
                    }
                    AgentAnnualStatisticsVo agentAnnualStatisticsSubtotalDto = new AgentAnnualStatisticsVo();
                    agentAnnualStatisticsSubtotalDto.setBdId(agentAnnualSummaryStatisticsVo.getBdId());
                    agentAnnualStatisticsSubtotalDto.setAgentNum(agentNum);
                    stringAgentAnnualStatisticsDtoMap.put("小计", agentAnnualStatisticsSubtotalDto);
                }
            }
        }


        //最终返回的排列好的 插入了小计的 统计列表List
        List<AgentAnnualSummaryStatisticsVo> agentAnnualSummaryStatisticsVoListResult = new ArrayList<>();

        //总计List
        List<AgentAnnualSummaryStatisticsVo> agentAnnualSummaryStatisticsVoTotalList = new ArrayList<>();


        Type type = new TypeToken<HashMap<Integer, Map<String, AgentAnnualStatisticsVo>>>() {
        }.getType();


        if (!agentAnnualSummaryDto.getDisplayModeFlag() || !agentAnnualSummaryDto.getTotalDisplayModeFlag()) {
            //一次循环的小计  年份 - (月份-代理数统计）
            Map<Integer, Map<String, AgentAnnualStatisticsVo>> agentAnnualStatisticsSubtotalMap = null;

            //大区id,小计循环专用
            Long regionId = null;
            String areaRegionName = null;


            //计算小计、总计
            for (int z = 0; z < agentAnnualSummaryStatisticsVoList.size(); z++) {
                AgentAnnualSummaryStatisticsVo agentAnnualSummaryStatisticsVo = agentAnnualSummaryStatisticsVoList.get(z);
                //计算该代理的列总计
                if (!agentAnnualSummaryDto.getTotalDisplayModeFlag()) {
                    Long agentNumber = 0L;
                    for (int i = 0; i <= comparisonYears; i++) {
                        //本轮年份
                        int currentRound = year - i;
                        Map<String, AgentAnnualStatisticsVo> stringAgentAnnualStatisticsDtoMap = agentAnnualSummaryStatisticsVo.getAgentAnnualStatisticsMap().get(currentRound);
                        for (int j = 1; j < 12 + 1; j++) {
                            AgentAnnualStatisticsVo agentAnnualStatisticsVo = stringAgentAnnualStatisticsDtoMap.get(String.valueOf(j));
                            agentNumber = agentNumber + agentAnnualStatisticsVo.getAgentNum();
                        }
                    }
                    agentAnnualSummaryStatisticsVo.setAgentTotalNumber(agentNumber);
                }

                //计算每个大区的小计  初始数据
                if (regionId == null) {
                    agentAnnualSummaryStatisticsVoListResult.add(agentAnnualSummaryStatisticsVo);
                    regionId = agentAnnualSummaryStatisticsVo.getRegionId();
                    areaRegionName = agentAnnualSummaryStatisticsVo.getAreaRegionName();

                    agentAnnualStatisticsSubtotalMap = CommonUtil.copy(type, agentAnnualSummaryStatisticsVo.getAgentAnnualStatisticsMap());

                    //不区分大区的总计初始map
//                    agentAnnualStatisticsTotalMap = CommonUtil.copy(type, agentAnnualSummaryStatisticsVo.getAgentAnnualStatisticsMap());
                    //最后一条了，叠加完直接算小计 总计
                    if (agentAnnualSummaryStatisticsVoList.size() == z + 1) {
                        finalDataStatistics(agentAnnualSummaryDto, agentAnnualSummaryStatisticsVoListResult, agentAnnualSummaryStatisticsVoTotalList,
                                agentAnnualStatisticsSubtotalMap, regionId, areaRegionName, agentAnnualSummaryStatisticsVo, comparisonYears, year);
                    }
                } else if (regionId.equals(agentAnnualSummaryStatisticsVo.getRegionId())) {
                    agentAnnualSummaryStatisticsVoListResult.add(agentAnnualSummaryStatisticsVo);
                    //同一循环里的基础数据 数据叠加
                    for (int i = 0; i <= comparisonYears; i++) {
                        //本轮年份
                        int currentRound = year - i;
                        //小计-本年份基础数据 月份-代理数统计
                        Map<String, AgentAnnualStatisticsVo> agentAnnualStatisticsDtoMap = agentAnnualSummaryStatisticsVo.getAgentAnnualStatisticsMap().get(currentRound);
                        //小计-本年份累加小计数据 月份-代理数统计
                        Map<String, AgentAnnualStatisticsVo> agentAnnualStatisticsDtoSubtotalMap = agentAnnualStatisticsSubtotalMap.get(currentRound);

//                        //不区分大区的总计-本年份基础数据 月份-代理数统计
//                        Map<String, AgentAnnualStatisticsVo> agentAnnualStatisticsDtoInitMap = agentAnnualSummaryStatisticsVo.getAgentAnnualStatisticsMap().get(currentRound);
//                        //不区分大区的总计-本年份累加总计数据 月份-代理数统计
//                        Map<String, AgentAnnualStatisticsVo> agentAnnualStatisticsDtoTotalMap = agentAnnualStatisticsTotalMap.get(currentRound);


                        for (int j = 1; j < 12 + 1; j++) {
                            if (agentAnnualSummaryDto.getIsDistinguishRegionFlag()) {
                                //计算小计
                                AgentAnnualStatisticsVo agentAnnualStatisticsVo = agentAnnualStatisticsDtoMap.get(String.valueOf(j));
                                AgentAnnualStatisticsVo agentAnnualStatisticsSubtotalDto = agentAnnualStatisticsDtoSubtotalMap.get(String.valueOf(j));
                                agentAnnualStatisticsSubtotalDto.setAgentNum(agentAnnualStatisticsVo.getAgentNum() + agentAnnualStatisticsSubtotalDto.getAgentNum());
                            }
                            //不区分大区-计算总计
//                            AgentAnnualStatisticsVo agentAnnualStatisticsInitDto = agentAnnualStatisticsDtoInitMap.get(String.valueOf(j));
//                            AgentAnnualStatisticsVo agentAnnualStatisticsTotalDto = agentAnnualStatisticsDtoTotalMap.get(String.valueOf(j));
//                            agentAnnualStatisticsTotalDto.setAgentNum(agentAnnualStatisticsInitDto.getAgentNum() + agentAnnualStatisticsTotalDto.getAgentNum());
                        }
                        if (!agentAnnualSummaryDto.getDisplayModeFlag()) {
                            AgentAnnualStatisticsVo agentAnnualStatisticsVo = agentAnnualStatisticsDtoMap.get("小计");
                            AgentAnnualStatisticsVo agentAnnualStatisticsSubtotalDto = agentAnnualStatisticsDtoSubtotalMap.get("小计");
                            agentAnnualStatisticsSubtotalDto.setAgentNum(agentAnnualStatisticsVo.getAgentNum() + agentAnnualStatisticsSubtotalDto.getAgentNum());
                        }
                    }

                    //最后一条了，叠加完直接算小计 总计
                    if (agentAnnualSummaryStatisticsVoList.size() == z + 1) {
                        finalDataStatistics(agentAnnualSummaryDto, agentAnnualSummaryStatisticsVoListResult, agentAnnualSummaryStatisticsVoTotalList, agentAnnualStatisticsSubtotalMap, regionId, areaRegionName, agentAnnualSummaryStatisticsVo, comparisonYears, year);
                    }

                } else {
                    //该大区最后一条数据，插入小计
                    finalDataStatistics(agentAnnualSummaryDto, agentAnnualSummaryStatisticsVoListResult, agentAnnualSummaryStatisticsVoTotalList, agentAnnualStatisticsSubtotalMap, regionId, areaRegionName, agentAnnualSummaryStatisticsVo, comparisonYears, year);

                    agentAnnualSummaryStatisticsVoListResult.add(agentAnnualSummaryStatisticsVo);
                    regionId = agentAnnualSummaryStatisticsVo.getRegionId();

                    agentAnnualStatisticsSubtotalMap = CommonUtil.copy(type, agentAnnualSummaryStatisticsVo.getAgentAnnualStatisticsMap());
                    areaRegionName = agentAnnualSummaryStatisticsVo.getAreaRegionName();
                    //最后一条了，叠加完直接算小计 总计
                    if (agentAnnualSummaryStatisticsVoList.size() == z + 1) {
                        finalDataStatistics(agentAnnualSummaryDto, agentAnnualSummaryStatisticsVoListResult, agentAnnualSummaryStatisticsVoTotalList, agentAnnualStatisticsSubtotalMap, regionId, areaRegionName, agentAnnualSummaryStatisticsVo, comparisonYears, year);
                    }
                }


            }
        } else {
            agentAnnualSummaryStatisticsVoListResult = agentAnnualSummaryStatisticsVoList;
        }

        //行总计
        if (!agentAnnualSummaryDto.getTotalDisplayModeFlag()) {
            //区分大区
            if (agentAnnualSummaryDto.getIsDistinguishRegionFlag()) {
                //行总计对象
                AgentAnnualSummaryStatisticsVo agentAnnualSummaryStatisticsTotalDto = new AgentAnnualSummaryStatisticsVo();
                //行总计年份 - (月份-代理数统计）
                Map<Integer, Map<String, AgentAnnualStatisticsVo>> agentAnnualStatisticsTotalMap = new LinkedHashMap<>();
                //行总计的列总计数
                Long agentNum = 0L;

                for (int i = 0; i < agentAnnualSummaryStatisticsVoList.size(); i++) {
                    AgentAnnualSummaryStatisticsVo agentAnnualSummaryStatisticsVo = agentAnnualSummaryStatisticsVoList.get(i);

                    if (agentAnnualSummaryStatisticsTotalDto.getRegionId() == null ||
                            !agentAnnualSummaryStatisticsTotalDto.getRegionId().equals(agentAnnualSummaryStatisticsVo.getRegionId())) {
                        if (agentAnnualSummaryStatisticsTotalDto.getRegionId() != null) {
                            agentAnnualSummaryStatisticsVoTotalList.add(agentAnnualSummaryStatisticsTotalDto);
                        }
                        agentAnnualSummaryStatisticsTotalDto = new AgentAnnualSummaryStatisticsVo();
                        agentAnnualStatisticsTotalMap = new LinkedHashMap<>();
                        agentNum = 0L;
                    }
                    if (i == (agentAnnualSummaryStatisticsVoList.size() - 1)) {
                        agentAnnualSummaryStatisticsVoTotalList.add(agentAnnualSummaryStatisticsTotalDto);
                    }
                    agentAnnualSummaryStatisticsTotalDto.setBdName("总计");
                    agentAnnualSummaryStatisticsTotalDto.setRegionId(agentAnnualSummaryStatisticsVo.getRegionId());
                    agentAnnualSummaryStatisticsTotalDto.setAreaRegionName(agentAnnualSummaryStatisticsVo.getAreaRegionName());
                    //计算总计
                    agentNum = totalCalculation(comparisonYears, year, agentAnnualSummaryStatisticsTotalDto, agentAnnualStatisticsTotalMap, agentAnnualSummaryStatisticsVo, agentNum);
                }
                agentAnnualSummaryStatisticsVoListResult.addAll(agentAnnualSummaryStatisticsVoTotalList);
            } else {
                //不区分大区
                AgentAnnualSummaryStatisticsVo agentAnnualSummaryStatisticsTotalDto = new AgentAnnualSummaryStatisticsVo();
                agentAnnualSummaryStatisticsTotalDto.setBdName("总计");
                //年份 - (月份-代理数统计）
                Map<Integer, Map<String, AgentAnnualStatisticsVo>> agentAnnualStatisticsTotalMap = new LinkedHashMap<>();
                //行总计的列总计数
                Long agentNum = 0L;
                for (AgentAnnualSummaryStatisticsVo agentAnnualSummaryStatisticsVo : agentAnnualSummaryStatisticsVoList) {
                    //计算总计
                    agentNum = totalCalculation(comparisonYears, year, agentAnnualSummaryStatisticsTotalDto, agentAnnualStatisticsTotalMap, agentAnnualSummaryStatisticsVo, agentNum);

                    //                    for (int i = 0; i <= comparisonYears; i++) {
//                        //本轮年份
//                        int currentRound = year - i;
//                        //本年份基础数据 月份-代理数统计
//                        Map<String, AgentAnnualStatisticsVo> agentAnnualStatisticsDtoInitMap = agentAnnualSummaryStatisticsVo.getAgentAnnualStatisticsMap().get(currentRound);
//                        //本年份总计数据 月份-代理数统计
//                        Map<String, AgentAnnualStatisticsVo> agentAnnualStatisticsDtoTotalMap = agentAnnualStatisticsTotalMap.computeIfAbsent(currentRound, k -> new LinkedHashMap<>());
//                        for (int j = 1; j < 12 + 1; j++) {
//                            AgentAnnualStatisticsVo agentAnnualStatisticsInitDto = agentAnnualStatisticsDtoInitMap.get(String.valueOf(j));
//                            AgentAnnualStatisticsVo agentAnnualStatisticsTotalDto = agentAnnualStatisticsDtoTotalMap.get(String.valueOf(j));
//                            if (agentAnnualStatisticsTotalDto == null) {
//                                agentAnnualStatisticsTotalDto = new AgentAnnualStatisticsVo();
//                                agentAnnualStatisticsTotalDto.setAgentNum(agentAnnualStatisticsInitDto.getAgentNum());
//                            } else {
//                                agentAnnualStatisticsTotalDto.setAgentNum(agentAnnualStatisticsInitDto.getAgentNum() + agentAnnualStatisticsTotalDto.getAgentNum());
//                            }
//                            agentAnnualStatisticsTotalDto.setYear(currentRound);
//                            agentAnnualStatisticsTotalDto.setMonth(j);
//                            agentAnnualStatisticsDtoTotalMap.put(String.valueOf(j), agentAnnualStatisticsTotalDto);
//                        }
//                    }

                }

                agentAnnualSummaryStatisticsTotalDto.setAgentAnnualStatisticsMap(agentAnnualStatisticsTotalMap);
//                //计算行总计的列总计
//                Long agentNumber = 0L;
//                for (int i = comparisonYears; i >= 0; i--) {
//                    //本轮年份
//                    int currentRound = year - i;
//                    //计算行总计的每年的列小计
//                    Long agentSubtotal = 0L;
//                    Map<String, AgentAnnualStatisticsVo> stringAgentAnnualStatisticsDtoMap = agentAnnualStatisticsTotalMap.get(currentRound);
//                    for (int j = 1; j < 12 + 1; j++) {
//                        AgentAnnualStatisticsVo agentAnnualStatisticsDto = stringAgentAnnualStatisticsDtoMap.get(String.valueOf(j));
//                        agentNumber = agentNumber + agentAnnualStatisticsDto.getAgentNum();
//                        agentSubtotal = agentSubtotal + agentAnnualStatisticsDto.getAgentNum();
//                    }
//                    AgentAnnualStatisticsVo agentAnnualStatisticsSubtotalDto = new AgentAnnualStatisticsVo();
//                    agentAnnualStatisticsSubtotalDto.setAgentNum(agentSubtotal);
//                    stringAgentAnnualStatisticsDtoMap.put("小计", agentAnnualStatisticsSubtotalDto);
//                }
//                agentAnnualSummaryStatisticsTotalDto.setAgentTotalNumber(agentNumber);
                agentAnnualSummaryStatisticsVoTotalList.add(agentAnnualSummaryStatisticsTotalDto);
                agentAnnualSummaryStatisticsVoListResult.addAll(agentAnnualSummaryStatisticsVoTotalList);
            }

            //计算总计的行小计
            if (!agentAnnualSummaryDto.getDisplayModeFlag() && agentAnnualSummaryDto.getIsDistinguishRegionFlag()) {
                //总计的 行小计累加Map
                Map<Integer, Map<String, AgentAnnualStatisticsVo>> agentAnnualStatisticsMap = new HashMap<>();
                AgentAnnualSummaryStatisticsVo agentAnnualSummaryStatisticsSubtotalDto = new AgentAnnualSummaryStatisticsVo();
                agentAnnualSummaryStatisticsSubtotalDto.setAreaRegionName("小计");
                //列总计数
                Long agentNum = 0L;
                for (AgentAnnualSummaryStatisticsVo agentAnnualSummaryStatisticsVo : agentAnnualSummaryStatisticsVoTotalList) {
//                        Long agentNum = 0L;
//                        for (int i = comparisonYears; i >= 0; i--) {
//                            //本轮年份
//                            int currentRound = year - i;
//                            //同年份基础数据map
//                            Map<String, AgentAnnualStatisticsVo> stringAgentAnnualStatisticsDtoMap = agentAnnualSummaryStatisticsVo.getAgentAnnualStatisticsMap().get(currentRound);
//                            //同年份累加Map
//                            Map<String, AgentAnnualStatisticsVo> stringAgentAnnualStatisticsDtoTotalMap = agentAnnualStatisticsMap.computeIfAbsent(currentRound, k -> new LinkedHashMap<>());
//                            for (int j = 1; j < 12 + 1; j++) {
//                                //同月份基础数据对象
//                                AgentAnnualStatisticsVo agentAnnualStatisticsInitDto = stringAgentAnnualStatisticsDtoMap.get(String.valueOf(j));
//                                //同月份累加对象
//                                AgentAnnualStatisticsVo agentAnnualStatisticsTotalDto = stringAgentAnnualStatisticsDtoTotalMap.get(String.valueOf(j));
//                                if (agentAnnualStatisticsTotalDto == null) {
//                                    agentAnnualStatisticsTotalDto = new AgentAnnualStatisticsVo();
//                                    agentAnnualStatisticsTotalDto.setAgentNum(agentAnnualStatisticsInitDto.getAgentNum());
//                                } else {
//                                    agentAnnualStatisticsTotalDto.setAgentNum(agentAnnualStatisticsTotalDto.getAgentNum() + agentAnnualStatisticsInitDto.getAgentNum());
//                                }
//                                agentAnnualStatisticsTotalDto.setYear(currentRound);
//                                agentAnnualStatisticsTotalDto.setMonth(j);
//                                agentNum = agentNum + agentAnnualStatisticsInitDto.getAgentNum();
//                                stringAgentAnnualStatisticsDtoTotalMap.put(String.valueOf(j), agentAnnualStatisticsTotalDto);
//                            }
//                            //列小计累加
//                            agentAnnualSummaryStatisticsVo.setAgentTotalNumber(agentNum);
//                     }
                    //计算总计
                    agentNum = totalCalculation(comparisonYears, year, agentAnnualSummaryStatisticsSubtotalDto, agentAnnualStatisticsMap, agentAnnualSummaryStatisticsVo, agentNum);
                }


                agentAnnualSummaryStatisticsSubtotalDto.setAgentAnnualStatisticsMap(agentAnnualStatisticsMap);
//                Long agentNum = 0L;
//                for (int i = comparisonYears; i >= 0; i--) {
//                    //本轮年份
//                    int currentRound = year - i;
//                    for (int j = 1; j < 12 + 1; j++) {
//                        AgentAnnualStatisticsVo agentAnnualStatisticsDto2 = agentAnnualStatisticsMap.get(currentRound).get(String.valueOf(j));
//                        agentNum = agentNum + agentAnnualStatisticsDto2.getAgentNum();
//                    }
//                }
//                agentAnnualSummaryStatisticsSubtotalDto.setAgentTotalNumber(agentNum);
                agentAnnualSummaryStatisticsVoListResult.add(agentAnnualSummaryStatisticsSubtotalDto);

            }


            AgentAnnualSummaryStatisticsVo agentAnnualSummaryStatisticsTotalDto = new AgentAnnualSummaryStatisticsVo();
            agentAnnualSummaryStatisticsTotalDto.setBdName("BD共" + bdIds.size() + "个");
            agentAnnualSummaryStatisticsVoListResult.add(agentAnnualSummaryStatisticsTotalDto);
        }


        return agentAnnualSummaryStatisticsVoListResult;
    }

    /**
     * 计算总计
     *
     * @Date 15:33 2023/5/17
     * <AUTHOR>
     */
    private Long totalCalculation(Integer comparisonYears, int year, AgentAnnualSummaryStatisticsVo agentAnnualSummaryStatisticsTotalDto, Map<Integer, Map<String, AgentAnnualStatisticsVo>> agentAnnualStatisticsTotalMap, AgentAnnualSummaryStatisticsVo agentAnnualSummaryStatisticsVo, Long agentNum) {
        for (int i = comparisonYears; i >= 0; i--) {
            //本轮年份
            int currentRound = year - i;
            //计算行总计
            Map<String, AgentAnnualStatisticsVo> stringAgentAnnualStatisticsDtoTotalMap = agentAnnualStatisticsTotalMap.get(currentRound);
            if (GeneralTool.isEmpty(stringAgentAnnualStatisticsDtoTotalMap)) {
                stringAgentAnnualStatisticsDtoTotalMap = new LinkedHashMap<>();
            }
            agentAnnualStatisticsTotalMap.put(currentRound, stringAgentAnnualStatisticsDtoTotalMap);
            agentAnnualSummaryStatisticsTotalDto.setAgentAnnualStatisticsMap(agentAnnualStatisticsTotalMap);
            //列小计
            Long subtotal = 0L;
            for (int j = 1; j < 12 + 1; j++) {
                //行总计
                AgentAnnualStatisticsVo agentAnnualStatisticsTotalDto = stringAgentAnnualStatisticsDtoTotalMap.get(String.valueOf(j));
                AgentAnnualStatisticsVo agentAnnualStatisticsInitDto = agentAnnualSummaryStatisticsVo.getAgentAnnualStatisticsMap().get(currentRound).get(String.valueOf(j));
                if (agentAnnualStatisticsTotalDto == null) {
                    agentAnnualStatisticsTotalDto = new AgentAnnualStatisticsVo();
                    agentAnnualStatisticsTotalDto.setAgentNum(agentAnnualStatisticsInitDto.getAgentNum());
                } else {
                    agentAnnualStatisticsTotalDto.setAgentNum(agentAnnualStatisticsTotalDto.getAgentNum() + agentAnnualStatisticsInitDto.getAgentNum());
                }
                agentAnnualStatisticsTotalDto.setYear(currentRound);
                agentAnnualStatisticsTotalDto.setMonth(j);

                stringAgentAnnualStatisticsDtoTotalMap.put(String.valueOf(j), agentAnnualStatisticsTotalDto);

                //计算行总计的列总计
                agentNum = agentNum + agentAnnualStatisticsInitDto.getAgentNum();
                //计算列小计
                subtotal = subtotal + agentAnnualStatisticsInitDto.getAgentNum();
            }
            //列小计
            AgentAnnualStatisticsVo agentAnnualStatisticsVo = agentAnnualStatisticsTotalMap.get(currentRound).get("小计");
            if (agentAnnualStatisticsVo == null) {
                agentAnnualStatisticsVo = new AgentAnnualStatisticsVo();
                agentAnnualStatisticsVo.setAgentNum(0L);
            }
            agentAnnualStatisticsVo.setAgentNum(agentAnnualStatisticsVo.getAgentNum() + subtotal);
            agentAnnualStatisticsVo.setYear(currentRound);
            agentAnnualStatisticsVo.setBdId(agentAnnualSummaryStatisticsTotalDto.getBdId());
            agentAnnualStatisticsTotalMap.get(currentRound).put("小计", agentAnnualStatisticsVo);
        }
        agentAnnualSummaryStatisticsTotalDto.setAgentTotalNumber(agentNum);
        return agentNum;
    }


    /**
     * 最后一条了，叠加完直接算小计 总计
     *
     * @Date 10:21 2023/3/22
     * <AUTHOR>
     */
    private void finalDataStatistics(AgentAnnualSummaryDto agentAnnualSummaryDto, List<AgentAnnualSummaryStatisticsVo> agentAnnualSummaryStatisticsVoListResult,
                                     List<AgentAnnualSummaryStatisticsVo> agentAnnualSummaryStatisticsVoTotalList,
                                     Map<Integer, Map<String, AgentAnnualStatisticsVo>> agentAnnualStatisticsSubtotalMap,
                                     Long regionId, String areaRegionName, AgentAnnualSummaryStatisticsVo agentAnnualSummaryStatisticsVo, Integer comparisonYears, int year) {
        if (!agentAnnualSummaryDto.getDisplayModeFlag()) {
            //该大区最后一条数据，插入小计
            AgentAnnualSummaryStatisticsVo agentAnnualSummaryStatisticsSubtotalDto = new AgentAnnualSummaryStatisticsVo();
            agentAnnualSummaryStatisticsSubtotalDto.setRegionId(regionId);
            agentAnnualSummaryStatisticsSubtotalDto.setAreaRegionName("小计");
            agentAnnualSummaryStatisticsSubtotalDto.setAgentAnnualStatisticsMap(agentAnnualStatisticsSubtotalMap);
            Long agentNum = 0L;
            for (int i = comparisonYears; i >= 0; i--) {
                //本轮年份
                int currentRound = year - i;
                for (int j = 1; j < 12 + 1; j++) {
                    AgentAnnualStatisticsVo agentAnnualStatisticsVo2 = agentAnnualStatisticsSubtotalMap.get(currentRound).get(String.valueOf(j));
                    agentNum = agentNum + agentAnnualStatisticsVo2.getAgentNum();
                }
            }
            agentAnnualSummaryStatisticsSubtotalDto.setAgentTotalNumber(agentNum);

            agentAnnualSummaryStatisticsVoListResult.add(agentAnnualSummaryStatisticsSubtotalDto);
        }


    }

    /**
     * 最后一条了，叠加完直接算小计 总计   --计算比率页面
     *
     * @Date 10:21 2023/3/22
     * <AUTHOR>
     */
    private void finalRatioDataStatistics(AgentAnnualSummaryDto agentAnnualSummaryDto, List<AgentAnnualSummaryStatisticsVo> agentAnnualSummaryStatisticsVoListResult,
                                          List<AgentAnnualSummaryStatisticsVo> agentAnnualSummaryStatisticsVoTotalList,
                                          Map<Integer, AgentAnnualStatisticsVo> agentAnnualStatisticsSubtotalMap,
                                          Long regionId, int year, String areaRegionName, Integer comparisonYears) {
        if (!agentAnnualSummaryDto.getDisplayModeFlag()) {
            //该大区最后一条数据，插入小计
            AgentAnnualSummaryStatisticsVo agentAnnualSummaryStatisticsSubtotalDto = new AgentAnnualSummaryStatisticsVo();
            agentAnnualSummaryStatisticsSubtotalDto.setRegionId(regionId);
            agentAnnualSummaryStatisticsSubtotalDto.setAreaRegionName("小计");
            agentAnnualSummaryStatisticsSubtotalDto.setAgentAnnualStatisticsResultMap(agentAnnualStatisticsSubtotalMap);
            //计算该小计的增减比率
            Map<String, AgentAnnualStatisticsVo> agentAnnualStatisticsRatioMap = new HashMap<>();
            for (int i = comparisonYears; i > 0; i--) {
                AgentAnnualStatisticsVo agentAnnualStatisticsRatioDto = new AgentAnnualStatisticsVo();
                //本轮年份
                int currentRound = year - i;
                //下一年年份
                int lastYear = currentRound + 1;
                AgentAnnualStatisticsVo agentAnnualStatisticsVo = agentAnnualStatisticsSubtotalMap.get(currentRound);
                AgentAnnualStatisticsVo agentAnnualStatisticsLastDto = agentAnnualStatisticsSubtotalMap.get(lastYear);
                agentAnnualStatisticsRatioDto.setAgentNum(agentAnnualStatisticsLastDto.getAgentNum() - agentAnnualStatisticsVo.getAgentNum());
                agentAnnualStatisticsRatioMap.put(currentRound + " : " + lastYear, agentAnnualStatisticsRatioDto);
            }
            agentAnnualSummaryStatisticsSubtotalDto.setAgentAnnualStatisticsRatioMap(agentAnnualStatisticsRatioMap);
            agentAnnualSummaryStatisticsVoListResult.add(agentAnnualSummaryStatisticsSubtotalDto);

            //区分大区 计算总计
            if (agentAnnualSummaryDto.getIsDistinguishRegionFlag()) {
                AgentAnnualSummaryStatisticsVo agentAnnualSummaryStatisticsTotalDto = new AgentAnnualSummaryStatisticsVo();
                agentAnnualSummaryStatisticsTotalDto.setRegionId(regionId);
                agentAnnualSummaryStatisticsTotalDto.setAreaRegionName(areaRegionName);
                agentAnnualSummaryStatisticsTotalDto.setBdName("总计");
                agentAnnualSummaryStatisticsTotalDto.setAgentAnnualStatisticsResultMap(agentAnnualStatisticsSubtotalMap);
                agentAnnualSummaryStatisticsTotalDto.setAgentAnnualStatisticsRatioMap(agentAnnualStatisticsRatioMap);
                agentAnnualSummaryStatisticsVoTotalList.add(agentAnnualSummaryStatisticsTotalDto);
            }

        }

    }

    /**
     * 插入初始数据 全部月份未0
     *
     * @Date 11:46 2023/3/21
     * <AUTHOR>
     */
    private void insertInitStatistics(Integer comparisonYears, int year, List<AgentAnnualSummaryStatisticsVo> agentAnnualSummaryStatisticsVoList, AgentAnnualSummaryStatisticsVo agentAnnualSummaryStatisticsVo, Boolean displayModeFlag) {
        //年份 - (月份-代理数统计）
        Map<Integer, Map<String, AgentAnnualStatisticsVo>> agentAnnualStatisticsMap = new LinkedHashMap<>();
        for (int i = 0; i <= comparisonYears; i++) {
            //本轮年份
            int currentRound = year - comparisonYears + i;
            //月份-代理数统计
            Map<String, AgentAnnualStatisticsVo> agentAnnualStatisticsDtoMap = new LinkedHashMap<>();
            for (int j = 1; j < 12 + 1; j++) {
                AgentAnnualStatisticsVo agentAnnualStatisticsVo = new AgentAnnualStatisticsVo();
                agentAnnualStatisticsVo.setBdId(agentAnnualSummaryStatisticsVo.getBdId());
                agentAnnualStatisticsVo.setMonth(j);
                agentAnnualStatisticsVo.setAgentNum(0L);
                agentAnnualStatisticsDtoMap.put(String.valueOf(j), agentAnnualStatisticsVo);
            }
            if (!displayModeFlag) {
                AgentAnnualStatisticsVo agentAnnualStatisticsVo = new AgentAnnualStatisticsVo();
                agentAnnualStatisticsVo.setBdId(agentAnnualSummaryStatisticsVo.getBdId());
                agentAnnualStatisticsVo.setAgentNum(0L);
                agentAnnualStatisticsDtoMap.put("小计", agentAnnualStatisticsVo);
            }
            agentAnnualStatisticsMap.put(currentRound, agentAnnualStatisticsDtoMap);
        }
        agentAnnualSummaryStatisticsVo.setAgentAnnualStatisticsMap(agentAnnualStatisticsMap);
    }

    /**
     * 插入初始数据 全部数据为0
     *
     * @Date 11:46 2023/3/21
     * <AUTHOR>
     */
    private void insertInitAgentAnnualStatistics(Integer comparisonYears, int year, AgentAnnualSummaryStatisticsVo agentAnnualSummaryStatisticsVo) {
        //年份 - 代理数统计
        Map<Integer, AgentAnnualStatisticsVo> agentAnnualStatisticsResultMap = new LinkedHashMap<>();
        for (int i = 0; i <= comparisonYears; i++) {
            //本轮年份
            int currentRound = year - comparisonYears + i;
            AgentAnnualStatisticsVo agentAnnualStatisticsVo = new AgentAnnualStatisticsVo();
            agentAnnualStatisticsVo.setBdId(agentAnnualSummaryStatisticsVo.getBdId());
            agentAnnualStatisticsVo.setAgentNum(0L);
            agentAnnualStatisticsVo.setApplicationsNum(0L);
            agentAnnualStatisticsVo.setInReviewNum(0L);
            agentAnnualStatisticsVo.setRefuseNum(0L);
            agentAnnualStatisticsVo.setUnsignedNum(0L);
            agentAnnualStatisticsResultMap.put(currentRound, agentAnnualStatisticsVo);
        }
        agentAnnualSummaryStatisticsVo.setAgentAnnualStatisticsResultMap(agentAnnualStatisticsResultMap);
    }


    /**
     * 代理年度时间区间对比统计
     *
     * @Date 15:07 2023/3/22
     * <AUTHOR>
     */
    @Override
    public List<AgentAnnualSummaryStatisticsVo> agentAnnualIntervalComparisonStatistics(AgentAnnualSummaryDto agentAnnualSummaryDto, List<Long> countryIds, Long staffId, List<Long> institutionIds) {
        List<AgentAnnualSummaryStatisticsVo> agentAnnualSummaryStatisticsVoList = new ArrayList<>();
        if (GeneralTool.isEmpty(countryIds)) {
            return agentAnnualSummaryStatisticsVoList;
        }
        //同期比对年数
        Integer comparisonYears = agentAnnualSummaryDto.getComparisonYears();
        //查询时间
        Date studentBeginTime = agentAnnualSummaryDto.getBeginTime();
        Date studentEndTime = agentAnnualSummaryDto.getEndTime();
        //查询初始年份
        Calendar cal = Calendar.getInstance();
        cal.setTime(studentBeginTime);
        int year = cal.get(Calendar.YEAR);

        //比对年份List
        List<StatisticalComparisonYearDto> statisticalComparisonYearDtoList = new ArrayList<>();
        for (int i = 0; i <= comparisonYears; i++) {
            Date beginTime = DateUtil.minusYears(studentBeginTime, i);
            Date endTime = DateUtil.minusYears(studentEndTime, i);
            StatisticalComparisonYearDto statisticalComparisonYearDto = new StatisticalComparisonYearDto();
            statisticalComparisonYearDto.setBeginTime(beginTime);
            statisticalComparisonYearDto.setEndTime(endTime);
            statisticalComparisonYearDtoList.add(statisticalComparisonYearDto);
        }

        //员工id + 业务下属员工ids
        List<Long> staffFollowerIds = permissionCenterClient.getStaffFollowerIds(staffId).getData().stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        staffFollowerIds.add(staffId);


        //只要有数据的
        agentAnnualSummaryStatisticsVoList = studentOfferItemMapper.selectAgentAnnualSummaryStatistics(agentAnnualSummaryDto, statisticalComparisonYearDtoList, countryIds, staffFollowerIds, institutionIds);

        if (GeneralTool.isEmpty(agentAnnualSummaryStatisticsVoList)) {
            return agentAnnualSummaryStatisticsVoList;
        }
        //bd数
        int bdNumber = agentAnnualSummaryStatisticsVoList.size();

        //先刷一遍默认0以及排序
        for (AgentAnnualSummaryStatisticsVo agentAnnualSummaryStatisticsVo : agentAnnualSummaryStatisticsVoList) {
            //插入初始数据 0
            insertInitAgentAnnualStatistics(comparisonYears, year, agentAnnualSummaryStatisticsVo);
        }

        //查询每年的数据
        for (int i = 1; i <= comparisonYears + 1; i++) {
            Date beginTime = DateUtil.minusYears(studentBeginTime, i - 1);
            Date endTime = DateUtil.minusYears(studentEndTime, i - 1);
            agentAnnualSummaryDto.setJumpBeginTime(beginTime);
            agentAnnualSummaryDto.setJumpEndTime(endTime);
            //查询本轮年份
            cal.setTime(beginTime);
            int nowYear = cal.get(Calendar.YEAR);

            //本循环年各BD数据
            List<AgentAnnualStatisticsVo> agentAnnualStatisticsVoList = studentOfferItemMapper.agentAnnualSummaryStatistics(agentAnnualSummaryDto, countryIds, staffFollowerIds, institutionIds);
            for (AgentAnnualStatisticsVo agentAnnualStatisticsVo : agentAnnualStatisticsVoList) {
                //找到bd主结构，往里面塞数据
                AgentAnnualSummaryStatisticsVo agentAnnualSummaryStatistics = null;
                for (AgentAnnualSummaryStatisticsVo agentAnnualSummaryStatisticsVo : agentAnnualSummaryStatisticsVoList) {
                    if (agentAnnualSummaryStatisticsVo.getBdId().equals(agentAnnualStatisticsVo.getBdId())) {
                        agentAnnualSummaryStatistics = agentAnnualSummaryStatisticsVo;
                    }
                }
                //年份 - 代理数统计
                Map<Integer, AgentAnnualStatisticsVo> agentAnnualStatisticsResultMap = agentAnnualSummaryStatistics.getAgentAnnualStatisticsResultMap();
                agentAnnualStatisticsVo.setInReviewNum(0L);
                agentAnnualStatisticsVo.setRefuseNum(0L);
                agentAnnualStatisticsVo.setApplicationsNum(0L);
                agentAnnualStatisticsVo.setUnsignedNum(0L);
                agentAnnualStatisticsResultMap.put(nowYear, agentAnnualStatisticsVo);
            }
            if (agentAnnualSummaryDto.getStatisticalType() == 1) {
                agentAnnualStatisticsVoList = studentOfferItemMapper.agentAnnualSummaryStatisticsByCreateTime(agentAnnualSummaryDto, countryIds, staffFollowerIds);

                //合同未返回数Map
                List<Long> bdIds = agentAnnualStatisticsVoList.stream().map(AgentAnnualStatisticsVo::getBdId).collect(Collectors.toList());
                Map<Long, Long> bdUnsignedNumMap = new HashMap<>();
                if (GeneralTool.isNotEmpty(bdIds)) {
                    List<UnsignedVo> unsignedVoList = agentMapper.geUnsignedNumByBdIds(bdIds, agentAnnualSummaryDto, countryIds, staffFollowerIds);
                    for (UnsignedVo unsignedVo : unsignedVoList) {
                        bdUnsignedNumMap.put(unsignedVo.getBdId(), unsignedVo.getUnsignedNum());
                    }
                }
                for (AgentAnnualStatisticsVo agentAnnualStatisticsVo : agentAnnualStatisticsVoList) {
                    //找到bd主结构，往里面塞数据
                    AgentAnnualSummaryStatisticsVo agentAnnualSummaryStatistics = null;
                    for (AgentAnnualSummaryStatisticsVo agentAnnualSummaryStatisticsVo : agentAnnualSummaryStatisticsVoList) {
                        if (agentAnnualSummaryStatisticsVo.getBdId().equals(agentAnnualStatisticsVo.getBdId())) {
                            agentAnnualSummaryStatistics = agentAnnualSummaryStatisticsVo;
                        }
                    }
                    //年份 - 代理数统计
                    Map<Integer, AgentAnnualStatisticsVo> agentAnnualStatisticsResultMap = agentAnnualSummaryStatistics.getAgentAnnualStatisticsResultMap();
                    AgentAnnualStatisticsVo agentAnnualStatisticsVoResult = agentAnnualStatisticsResultMap.get(nowYear);
                    agentAnnualStatisticsVoResult.setInReviewNum(agentAnnualStatisticsVoResult.getInReviewNum() + agentAnnualStatisticsVo.getInReviewNum());
                    agentAnnualStatisticsVoResult.setRefuseNum(agentAnnualStatisticsVoResult.getRefuseNum() + agentAnnualStatisticsVo.getRefuseNum());
                    agentAnnualStatisticsVoResult.setApplicationsNum(agentAnnualStatisticsVoResult.getAgentNum() + agentAnnualStatisticsVoResult.getInReviewNum() + agentAnnualStatisticsVoResult.getRefuseNum());
                    agentAnnualStatisticsResultMap.put(nowYear, agentAnnualStatisticsVoResult);
                }
                //计算申请总数（通过审核 + 审核中 + 拒绝） + 合同未返回数
                for (AgentAnnualSummaryStatisticsVo agentAnnualSummaryStatisticsVo : agentAnnualSummaryStatisticsVoList) {
                    //年份 - 代理数统计
                    Map<Integer, AgentAnnualStatisticsVo> agentAnnualStatisticsResultMap = agentAnnualSummaryStatisticsVo.getAgentAnnualStatisticsResultMap();
                    AgentAnnualStatisticsVo agentAnnualStatisticsVoResult = agentAnnualStatisticsResultMap.get(nowYear);
                    Long unsignedNum = bdUnsignedNumMap.get(agentAnnualStatisticsVoResult.getBdId());
                    if (GeneralTool.isNotEmpty(unsignedNum)) {
                        agentAnnualStatisticsVoResult.setUnsignedNum(unsignedNum);
                    }
                    agentAnnualStatisticsVoResult.setApplicationsNum(agentAnnualStatisticsVoResult.getAgentNum() + agentAnnualStatisticsVoResult.getInReviewNum() + agentAnnualStatisticsVoResult.getRefuseNum());
                    agentAnnualStatisticsResultMap.put(nowYear, agentAnnualStatisticsVoResult);
                }
            }

        }


        //年份对比
        //下一年的数据 用于与本次年份数据对比
        for (AgentAnnualSummaryStatisticsVo agentAnnualSummaryStatisticsVo : agentAnnualSummaryStatisticsVoList) {
            Map<String, AgentAnnualStatisticsVo> agentAnnualStatisticsRatioMap = new LinkedHashMap<>();

            for (int j = comparisonYears; j > 0; j--) {
                //本轮年份
                int currentRound = year - j;
                //下一年年份
                int lastYear = currentRound + 1;
                AgentAnnualStatisticsVo agentAnnualStatisticsRatioDto = new AgentAnnualStatisticsVo();

                //该BD本年份数据
                AgentAnnualStatisticsVo agentAnnualStatisticsVo = agentAnnualSummaryStatisticsVo.getAgentAnnualStatisticsResultMap().get(currentRound);
                //该BD下一年份数据
                AgentAnnualStatisticsVo agentAnnualStatisticsNextYearDto = agentAnnualSummaryStatisticsVo.getAgentAnnualStatisticsResultMap().get(lastYear);
                agentAnnualStatisticsRatioDto.setAgentNum(agentAnnualStatisticsNextYearDto.getAgentNum() - agentAnnualStatisticsVo.getAgentNum());
                agentAnnualStatisticsRatioMap.put(currentRound + " : " + lastYear, agentAnnualStatisticsRatioDto);
            }
            agentAnnualSummaryStatisticsVo.setAgentAnnualStatisticsRatioMap(agentAnnualStatisticsRatioMap);
        }


        //计算小计和总计
        //最终返回的排列好的 插入了小计的 统计列表List
        List<AgentAnnualSummaryStatisticsVo> agentAnnualSummaryStatisticsVoListResult = new ArrayList<>();
        //总计List
        List<AgentAnnualSummaryStatisticsVo> agentAnnualSummaryStatisticsVoTotalList = new ArrayList<>();


        if (!agentAnnualSummaryDto.getDisplayModeFlag() || !agentAnnualSummaryDto.getTotalDisplayModeFlag()) {
            //一次循环的总计  年份 - 代理数统计
            Map<Integer, AgentAnnualStatisticsVo> agentAnnualStatisticsTotalMap = null;
            //一次循环的小计  年份 -代理数统计
            Map<Integer, AgentAnnualStatisticsVo> agentAnnualStatisticsSubtotalMap = null;
            //大区id,小计循环专用
            Long regionId = null;
            String areaRegionName = null;

            Type type = new TypeToken<HashMap<Integer, AgentAnnualStatisticsVo>>() {
            }.getType();

            //计算小计
            for (int z = 0; z < agentAnnualSummaryStatisticsVoList.size(); z++) {
                AgentAnnualSummaryStatisticsVo agentAnnualSummaryStatisticsVo = agentAnnualSummaryStatisticsVoList.get(z);

                //计算每个大区的小计  初始数据
                if (regionId == null) {
                    regionId = agentAnnualSummaryStatisticsVo.getRegionId();
                    areaRegionName = agentAnnualSummaryStatisticsVo.getAreaRegionName();

                    agentAnnualStatisticsSubtotalMap = CommonUtil.copy(type, agentAnnualSummaryStatisticsVo.getAgentAnnualStatisticsResultMap());
                    agentAnnualSummaryStatisticsVoListResult.add(agentAnnualSummaryStatisticsVo);

                    //不区分大区的总计初始map
                    agentAnnualStatisticsTotalMap = CommonUtil.copy(type, agentAnnualSummaryStatisticsVo.getAgentAnnualStatisticsResultMap());
                    //最后一条了，叠加完直接算小计
                    if (agentAnnualSummaryStatisticsVoList.size() == z + 1) {
                        finalRatioDataStatistics(agentAnnualSummaryDto, agentAnnualSummaryStatisticsVoListResult, agentAnnualSummaryStatisticsVoTotalList, agentAnnualStatisticsSubtotalMap, regionId, year, areaRegionName, comparisonYears);
                    }
                } else if (regionId.equals(agentAnnualSummaryStatisticsVo.getRegionId())) {
                    //同一循环里的基础数据 数据叠加
                    for (int i = 0; i <= comparisonYears; i++) {
                        //本轮年份
                        int currentRound = year - i;
                        //基础数据：本条数据该年份数据
                        AgentAnnualStatisticsVo agentAnnualStatisticsVo = agentAnnualSummaryStatisticsVo.getAgentAnnualStatisticsResultMap().get(currentRound);
                        //该年份小计对象   与 基础数据累加
                        AgentAnnualStatisticsVo agentAnnualStatisticsSubtotalDto = agentAnnualStatisticsSubtotalMap.get(currentRound);
                        agentAnnualStatisticsSubtotalDto.setAgentNum(agentAnnualStatisticsSubtotalDto.getAgentNum() + agentAnnualStatisticsVo.getAgentNum());
                        agentAnnualStatisticsSubtotalDto.setApplicationsNum(agentAnnualStatisticsSubtotalDto.getApplicationsNum() + agentAnnualStatisticsVo.getApplicationsNum());
                        agentAnnualStatisticsSubtotalDto.setInReviewNum(agentAnnualStatisticsSubtotalDto.getInReviewNum() + agentAnnualStatisticsVo.getInReviewNum());
                        agentAnnualStatisticsSubtotalDto.setRefuseNum(agentAnnualStatisticsSubtotalDto.getRefuseNum() + agentAnnualStatisticsVo.getRefuseNum());
                        agentAnnualStatisticsSubtotalDto.setUnsignedNum(agentAnnualStatisticsSubtotalDto.getUnsignedNum() + agentAnnualStatisticsVo.getUnsignedNum());

                        //不区分大区-计算总计
                        AgentAnnualStatisticsVo agentAnnualStatisticsTotalDto = agentAnnualStatisticsTotalMap.get(currentRound);
                        agentAnnualStatisticsTotalDto.setAgentNum(agentAnnualStatisticsTotalDto.getAgentNum() + agentAnnualStatisticsVo.getAgentNum());
                        agentAnnualStatisticsTotalDto.setApplicationsNum(agentAnnualStatisticsTotalDto.getApplicationsNum() + agentAnnualStatisticsVo.getApplicationsNum());
                        agentAnnualStatisticsTotalDto.setInReviewNum(agentAnnualStatisticsTotalDto.getInReviewNum() + agentAnnualStatisticsVo.getInReviewNum());
                        agentAnnualStatisticsTotalDto.setRefuseNum(agentAnnualStatisticsTotalDto.getRefuseNum() + agentAnnualStatisticsVo.getRefuseNum());
                        agentAnnualStatisticsTotalDto.setUnsignedNum(agentAnnualStatisticsTotalDto.getUnsignedNum() + agentAnnualStatisticsVo.getUnsignedNum());
                    }
                    agentAnnualSummaryStatisticsVoListResult.add(agentAnnualSummaryStatisticsVo);

                    //最后一条了，叠加完直接算小计
                    if (agentAnnualSummaryStatisticsVoList.size() == z + 1) {
                        finalRatioDataStatistics(agentAnnualSummaryDto, agentAnnualSummaryStatisticsVoListResult, agentAnnualSummaryStatisticsVoTotalList, agentAnnualStatisticsSubtotalMap, regionId, year, areaRegionName, comparisonYears);
                    }

                } else {
                    if (!agentAnnualSummaryDto.getDisplayModeFlag()) {
                        //该大区最后一条数据，插入小计
                        AgentAnnualSummaryStatisticsVo agentAnnualSummaryStatisticsSubtotalDto = new AgentAnnualSummaryStatisticsVo();
                        agentAnnualSummaryStatisticsSubtotalDto.setRegionId(regionId);
                        agentAnnualSummaryStatisticsSubtotalDto.setAreaRegionName("小计");
                        agentAnnualSummaryStatisticsSubtotalDto.setAgentAnnualStatisticsResultMap(agentAnnualStatisticsSubtotalMap);
                        //计算该小计的增减比率
                        Map<String, AgentAnnualStatisticsVo> agentAnnualStatisticsRatioMap = new HashMap<>();
                        for (int i = comparisonYears; i > 0; i--) {
                            AgentAnnualStatisticsVo agentAnnualStatisticsRatioDto = new AgentAnnualStatisticsVo();
                            //本轮年份
                            int currentRound = year - i;
                            //下一年年份
                            int lastYear = currentRound + 1;
                            AgentAnnualStatisticsVo agentAnnualStatisticsVo = agentAnnualStatisticsSubtotalMap.get(currentRound);
                            AgentAnnualStatisticsVo agentAnnualStatisticsLastDto = agentAnnualStatisticsSubtotalMap.get(lastYear);
                            agentAnnualStatisticsRatioDto.setAgentNum(agentAnnualStatisticsLastDto.getAgentNum() - agentAnnualStatisticsVo.getAgentNum());
                            agentAnnualStatisticsRatioMap.put(currentRound + " : " + lastYear, agentAnnualStatisticsRatioDto);
                        }
                        agentAnnualSummaryStatisticsSubtotalDto.setAgentAnnualStatisticsRatioMap(agentAnnualStatisticsRatioMap);
                        agentAnnualSummaryStatisticsVoListResult.add(agentAnnualSummaryStatisticsSubtotalDto);
                    }
                    //区分大区 计算总计
                    if (agentAnnualSummaryDto.getIsDistinguishRegionFlag()) {
                        //计算总计 总计对象
                        AgentAnnualSummaryStatisticsVo agentAnnualSummaryStatisticsTotalDto = new AgentAnnualSummaryStatisticsVo();
                        agentAnnualSummaryStatisticsTotalDto.setAreaRegionName(areaRegionName);
                        agentAnnualSummaryStatisticsTotalDto.setBdName("总计");
                        agentAnnualSummaryStatisticsTotalDto.setAgentAnnualStatisticsResultMap(agentAnnualStatisticsSubtotalMap);
                        agentAnnualSummaryStatisticsVoTotalList.add(agentAnnualSummaryStatisticsTotalDto);
                    } else {
                        //不区分大区-计算总计
                        //同一循环里的基础数据 数据叠加
                        for (int i = 0; i <= comparisonYears; i++) {
                            //本轮年份
                            int currentRound = year - i;
                            //不区分大区的总计-本年份基础数据 月份-代理数统计
                            AgentAnnualStatisticsVo agentAnnualStatisticsVoInit = agentAnnualSummaryStatisticsVo.getAgentAnnualStatisticsResultMap().get(currentRound);
                            //不区分大区的总计-本年份总计数据 月份-代理数统计
                            AgentAnnualStatisticsVo agentAnnualStatisticsVoTotal = agentAnnualStatisticsTotalMap.get(currentRound);
                            agentAnnualStatisticsVoTotal.setAgentNum(agentAnnualStatisticsVoInit.getAgentNum() + agentAnnualStatisticsVoTotal.getAgentNum());
                            agentAnnualStatisticsVoTotal.setApplicationsNum(agentAnnualStatisticsVoInit.getApplicationsNum() + agentAnnualStatisticsVoTotal.getApplicationsNum());
                            agentAnnualStatisticsVoTotal.setInReviewNum(agentAnnualStatisticsVoInit.getInReviewNum() + agentAnnualStatisticsVoTotal.getInReviewNum());
                            agentAnnualStatisticsVoTotal.setRefuseNum(agentAnnualStatisticsVoInit.getRefuseNum() + agentAnnualStatisticsVoTotal.getRefuseNum());
                            agentAnnualStatisticsVoTotal.setUnsignedNum(agentAnnualStatisticsVoInit.getUnsignedNum() + agentAnnualStatisticsVoTotal.getUnsignedNum());
                        }
                    }

                    regionId = agentAnnualSummaryStatisticsVo.getRegionId();

                    agentAnnualStatisticsSubtotalMap = CommonUtil.copy(type, agentAnnualSummaryStatisticsVo.getAgentAnnualStatisticsResultMap());
                    areaRegionName = agentAnnualSummaryStatisticsVo.getAreaRegionName();
                    agentAnnualSummaryStatisticsVoListResult.add(agentAnnualSummaryStatisticsVo);
                    //最后一条了，叠加完直接算小计
                    if (agentAnnualSummaryStatisticsVoList.size() == z + 1) {
                        finalRatioDataStatistics(agentAnnualSummaryDto, agentAnnualSummaryStatisticsVoListResult, agentAnnualSummaryStatisticsVoTotalList, agentAnnualStatisticsSubtotalMap, regionId, year, areaRegionName, comparisonYears);
                    }
                }
            }

            //总计
            if (!agentAnnualSummaryDto.getTotalDisplayModeFlag()) {
                //区分大区
                if (agentAnnualSummaryDto.getIsDistinguishRegionFlag()) {
                    //计算该大区总计的增减比率 以及 总计的小计map
                    for (AgentAnnualSummaryStatisticsVo agentAnnualSummaryStatisticsVo : agentAnnualSummaryStatisticsVoTotalList) {
                        Map<Integer, AgentAnnualStatisticsVo> agentAnnualStatisticsResultMap = agentAnnualSummaryStatisticsVo.getAgentAnnualStatisticsResultMap();
                        //总计的增减比率
                        Map<String, AgentAnnualStatisticsVo> agentAnnualStatisticsRatioMap = new HashMap<>();
                        for (int i = comparisonYears; i > 0; i--) {
                            AgentAnnualStatisticsVo agentAnnualStatisticsRatioDto = new AgentAnnualStatisticsVo();
                            //本轮年份
                            int currentRound = year - i;
                            //下一年年份
                            int lastYear = currentRound + 1;
                            AgentAnnualStatisticsVo agentAnnualStatisticsVo = agentAnnualStatisticsResultMap.get(currentRound);
                            AgentAnnualStatisticsVo agentAnnualStatisticsLastDto = agentAnnualStatisticsResultMap.get(lastYear);
                            agentAnnualStatisticsRatioDto.setAgentNum(agentAnnualStatisticsLastDto.getAgentNum() - agentAnnualStatisticsVo.getAgentNum());
                            agentAnnualStatisticsRatioDto.setApplicationsNum(agentAnnualStatisticsLastDto.getApplicationsNum() - agentAnnualStatisticsVo.getApplicationsNum());
                            agentAnnualStatisticsRatioDto.setInReviewNum(agentAnnualStatisticsLastDto.getInReviewNum() - agentAnnualStatisticsVo.getInReviewNum());
                            agentAnnualStatisticsRatioDto.setRefuseNum(agentAnnualStatisticsLastDto.getRefuseNum() - agentAnnualStatisticsVo.getRefuseNum());
                            agentAnnualStatisticsRatioDto.setUnsignedNum(agentAnnualStatisticsLastDto.getUnsignedNum() - agentAnnualStatisticsVo.getUnsignedNum());
                            agentAnnualStatisticsRatioMap.put(currentRound + " : " + lastYear, agentAnnualStatisticsRatioDto);
                        }
                        agentAnnualSummaryStatisticsVo.setAgentAnnualStatisticsRatioMap(agentAnnualStatisticsRatioMap);
                    }
                    agentAnnualSummaryStatisticsVoListResult.addAll(agentAnnualSummaryStatisticsVoTotalList);
                } else {
                    //不区分大区
                    AgentAnnualSummaryStatisticsVo agentAnnualSummaryStatisticsTotalDto = new AgentAnnualSummaryStatisticsVo();
                    agentAnnualSummaryStatisticsTotalDto.setBdName("总计");
                    agentAnnualSummaryStatisticsTotalDto.setAgentAnnualStatisticsResultMap(agentAnnualStatisticsTotalMap);
                    //总计的增减比率
                    Map<String, AgentAnnualStatisticsVo> agentAnnualStatisticsRatioMap = new HashMap<>();
                    for (int i = comparisonYears; i > 0; i--) {
                        AgentAnnualStatisticsVo agentAnnualStatisticsRatioDto = new AgentAnnualStatisticsVo();
                        //本轮年份
                        int currentRound = year - i;
                        //下一年年份
                        int lastYear = currentRound + 1;
                        AgentAnnualStatisticsVo agentAnnualStatisticsVo = agentAnnualStatisticsTotalMap.get(currentRound);
                        AgentAnnualStatisticsVo agentAnnualStatisticsLastDto = agentAnnualStatisticsTotalMap.get(lastYear);
                        agentAnnualStatisticsRatioDto.setAgentNum(agentAnnualStatisticsLastDto.getAgentNum() - agentAnnualStatisticsVo.getAgentNum());
                        agentAnnualStatisticsRatioDto.setApplicationsNum(agentAnnualStatisticsLastDto.getApplicationsNum() + agentAnnualStatisticsVo.getApplicationsNum());
                        agentAnnualStatisticsRatioDto.setInReviewNum(agentAnnualStatisticsLastDto.getInReviewNum() + agentAnnualStatisticsVo.getInReviewNum());
                        agentAnnualStatisticsRatioDto.setRefuseNum(agentAnnualStatisticsLastDto.getRefuseNum() + agentAnnualStatisticsVo.getRefuseNum());
                        agentAnnualStatisticsRatioDto.setUnsignedNum(agentAnnualStatisticsLastDto.getUnsignedNum() + agentAnnualStatisticsVo.getUnsignedNum());
                        agentAnnualStatisticsRatioMap.put(currentRound + " : " + lastYear, agentAnnualStatisticsRatioDto);
                    }
                    agentAnnualSummaryStatisticsTotalDto.setAgentAnnualStatisticsRatioMap(agentAnnualStatisticsRatioMap);
                    agentAnnualSummaryStatisticsVoListResult.add(agentAnnualSummaryStatisticsTotalDto);
                }


                //计算总计的小计
                if (!agentAnnualSummaryDto.getDisplayModeFlag() && agentAnnualSummaryDto.getIsDistinguishRegionFlag()) {
                    AgentAnnualSummaryStatisticsVo agentAnnualSummaryStatisticsSubtotalDto = new AgentAnnualSummaryStatisticsVo();
                    agentAnnualSummaryStatisticsSubtotalDto.setAreaRegionName("小计");
                    //总计的小计累加Map
                    Map<Integer, AgentAnnualStatisticsVo> agentAnnualStatisticsTotalSubtotalMap = null;
                    for (AgentAnnualSummaryStatisticsVo agentAnnualSummaryStatisticsVo : agentAnnualSummaryStatisticsVoTotalList) {
                        if (GeneralTool.isEmpty(agentAnnualStatisticsTotalSubtotalMap)) {
                            agentAnnualStatisticsTotalSubtotalMap = CommonUtil.copy(type, agentAnnualSummaryStatisticsVo.getAgentAnnualStatisticsResultMap());
                        } else {
                            for (int i = 0; i <= comparisonYears; i++) {
                                //本轮年份
                                int currentRound = year - i;
                                //基础数据
                                AgentAnnualStatisticsVo agentAnnualStatisticsInitDto = agentAnnualSummaryStatisticsVo.getAgentAnnualStatisticsResultMap().get(currentRound);
                                AgentAnnualStatisticsVo agentAnnualStatisticsSubtotalDto = agentAnnualStatisticsTotalSubtotalMap.get(currentRound);
                                agentAnnualStatisticsSubtotalDto.setAgentNum(agentAnnualStatisticsSubtotalDto.getAgentNum() + agentAnnualStatisticsInitDto.getAgentNum());
                                agentAnnualStatisticsSubtotalDto.setApplicationsNum(agentAnnualStatisticsSubtotalDto.getApplicationsNum() + agentAnnualStatisticsInitDto.getApplicationsNum());
                                agentAnnualStatisticsSubtotalDto.setInReviewNum(agentAnnualStatisticsSubtotalDto.getInReviewNum() + agentAnnualStatisticsInitDto.getInReviewNum());
                                agentAnnualStatisticsSubtotalDto.setRefuseNum(agentAnnualStatisticsSubtotalDto.getRefuseNum() + agentAnnualStatisticsInitDto.getRefuseNum());
                                agentAnnualStatisticsSubtotalDto.setUnsignedNum(agentAnnualStatisticsSubtotalDto.getUnsignedNum() + agentAnnualStatisticsInitDto.getUnsignedNum());

                            }
                        }
                    }

                    agentAnnualSummaryStatisticsSubtotalDto.setAgentAnnualStatisticsResultMap(agentAnnualStatisticsTotalSubtotalMap);
                    //计算该小计的增减比率
                    Map<String, AgentAnnualStatisticsVo> agentAnnualStatisticsRatioSubtotalMap = new HashMap<>();
                    for (int i = comparisonYears; i > 0; i--) {
                        AgentAnnualStatisticsVo agentAnnualStatisticsRatioDto = new AgentAnnualStatisticsVo();
                        //本轮年份
                        int currentRound = year - i;
                        //下一年年份
                        int lastYear = currentRound + 1;
                        AgentAnnualStatisticsVo agentAnnualStatisticsVo = agentAnnualStatisticsTotalSubtotalMap.get(currentRound);
                        AgentAnnualStatisticsVo agentAnnualStatisticsLastDto = agentAnnualStatisticsTotalSubtotalMap.get(lastYear);
                        agentAnnualStatisticsRatioDto.setAgentNum(agentAnnualStatisticsLastDto.getAgentNum() - agentAnnualStatisticsVo.getAgentNum());
                        agentAnnualStatisticsRatioDto.setApplicationsNum(agentAnnualStatisticsLastDto.getApplicationsNum() - agentAnnualStatisticsVo.getApplicationsNum());
                        agentAnnualStatisticsRatioDto.setInReviewNum(agentAnnualStatisticsLastDto.getInReviewNum() - agentAnnualStatisticsVo.getInReviewNum());
                        agentAnnualStatisticsRatioDto.setRefuseNum(agentAnnualStatisticsLastDto.getRefuseNum() - agentAnnualStatisticsVo.getRefuseNum());
                        agentAnnualStatisticsRatioDto.setUnsignedNum(agentAnnualStatisticsLastDto.getUnsignedNum() - agentAnnualStatisticsVo.getUnsignedNum());
                        agentAnnualStatisticsRatioSubtotalMap.put(currentRound + " : " + lastYear, agentAnnualStatisticsRatioDto);
                    }
                    agentAnnualSummaryStatisticsSubtotalDto.setAgentAnnualStatisticsRatioMap(agentAnnualStatisticsRatioSubtotalMap);
                    agentAnnualSummaryStatisticsVoListResult.add(agentAnnualSummaryStatisticsSubtotalDto);
                }

                AgentAnnualSummaryStatisticsVo agentAnnualSummaryStatisticsTotalDto = new AgentAnnualSummaryStatisticsVo();
                agentAnnualSummaryStatisticsTotalDto.setBdName("BD共" + bdNumber + "个");
                agentAnnualSummaryStatisticsVoListResult.add(agentAnnualSummaryStatisticsTotalDto);
            }


        } else {
            agentAnnualSummaryStatisticsVoListResult = agentAnnualSummaryStatisticsVoList;
        }


        return agentAnnualSummaryStatisticsVoListResult;
    }

    /**
     * 代理统计对比表 条件回显
     *
     * @param agentAnnualSummaryDto
     * @return
     */
    @Override
    public AgentStatisticalConditionalEchoVo agentComparisonConditionalEcho(AgentAnnualSummaryDto agentAnnualSummaryDto) {
        AgentStatisticalConditionalEchoVo agentStatisticalConditionalEchoVo = new AgentStatisticalConditionalEchoVo();
        Long fkCompanyId = agentAnnualSummaryDto.getFkCompanyId();
        agentStatisticalConditionalEchoVo.setCompanyNameStr(permissionCenterClient.getCompanyNameById(fkCompanyId).getData());
        if (GeneralTool.isNotEmpty(agentAnnualSummaryDto.getBeginTime()) || GeneralTool.isNotEmpty(agentAnnualSummaryDto.getEndTime())) {
            SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
            agentStatisticalConditionalEchoVo.setCreamTimeStr(sf.format(agentAnnualSummaryDto.getBeginTime()) + "到" + sf.format(agentAnnualSummaryDto.getEndTime()));
        }
        if (GeneralTool.isNotEmpty(agentAnnualSummaryDto.getComparisonYears())) {
            agentStatisticalConditionalEchoVo.setComparisonYearsStr(agentAnnualSummaryDto.getComparisonYears().toString());
        }
//        if (GeneralTool.isNotEmpty(agentAnnualSummaryDto.getAreaCountryIds())) {
//            Map<Long, String> countryChnNameMap = institutionCenterClient.getCountryChnNameByIds(agentAnnualSummaryDto.getAreaCountryIds()).getData();
//            if (GeneralTool.isNotEmpty(countryChnNameMap)) {
//                List<String> countryChnNameList = countryChnNameMap.values().stream().collect(Collectors.toList());
//                String countryChnNames = StringUtils.join(countryChnNameList.toArray(), ",");
//                agentStatisticalConditionalEchoVo.setCountryStr(countryChnNames);
//            }
//        }
//
//        if (GeneralTool.isNotEmpty(agentAnnualSummaryDto.getProjectRoleId())) {
//            StudentProjectRole studentProjectRole = studentProjectRoleMapper.selectById(agentAnnualSummaryDto.getProjectRoleId());
//            if (GeneralTool.isNotEmpty(studentProjectRole)) {
//                agentStatisticalConditionalEchoVo.setProjectRoleName(studentProjectRole.getRoleName());
//            }
//        }
//
//        if (GeneralTool.isNotEmpty(agentAnnualSummaryDto.getFkStaffIds())) {
//            Map<Long, String> staffNameMap = permissionCenterClient.getStaffNameMap(agentAnnualSummaryDto.getFkStaffIds()).getData();
//            if (GeneralTool.isNotEmpty(staffNameMap)) {
//                agentStatisticalConditionalEchoVo.setStaffName(staffNameMap.values().stream().collect(Collectors.toSet()));
//            }
//        }
//
        if (GeneralTool.isNotEmpty(agentAnnualSummaryDto.getFkAreaRegionIdList())) {
            Map<Long, AreaRegionVo> regionDtoMap = institutionCenterClient.getAreaRegionDtoByIds(agentAnnualSummaryDto.getFkAreaRegionIdList()).getData();
            if (GeneralTool.isNotEmpty(regionDtoMap)) {
                List<String> regionNameList = regionDtoMap.values().stream().map(AreaRegionVo::getNameChn).collect(Collectors.toList());
                String regionNames = StringUtils.join(regionNameList.toArray(), ",");
                agentStatisticalConditionalEchoVo.setAreaRegionStr(regionNames);
            }
        }

        //代理所在区域

        if (GeneralTool.isNotEmpty(agentAnnualSummaryDto.getFkAreaCityIdList())) {
            agentStatisticalConditionalEchoVo.setAgentAreaStr(institutionCenterClient.getAreaCityNameByIds(agentAnnualSummaryDto.getFkAreaCityIdList()).getData());
        } else if (GeneralTool.isNotEmpty(agentAnnualSummaryDto.getFkAreaStateIdList())) {
            agentStatisticalConditionalEchoVo.setAgentAreaStr(institutionCenterClient.getAreaStateNameByIds(agentAnnualSummaryDto.getFkAreaStateIdList()).getData());
        }

        Map<Long, StaffBdCode> bDbyStaffMap = staffBdCodeService.getBDbyStaffIds(agentAnnualSummaryDto.getFkBdIds());
        if (GeneralTool.isNotEmpty(bDbyStaffMap)) {
            List<String> bdCodeList = bDbyStaffMap.values().stream().map(StaffBdCode::getBdCode).collect(Collectors.toList());
            String bdCodes = StringUtils.join(bdCodeList.toArray(), ",");
            agentStatisticalConditionalEchoVo.setBdCodeStr(bdCodes);
        }
//        agentStatisticalConditionalEchoVo.setAgentNumStr(agentAnnualSummaryDto.getAgentNumStr());
        return agentStatisticalConditionalEchoVo;
    }

    /**
     * 生成提成结算
     *
     * @Date 11:26 2023/3/14
     * <AUTHOR>
     */
    @Async
    public void inserCommissionAction(BdStudentStatisticalComparisonDto bdStudentStatisticalComparisonDto, BigDecimal newAmount, String roleKey, String fkStaffCommissionStepKey, DelayConfigDto delayConfig) {
        // DelayConfigDto delayConfig = getDelayConfig();
        List<StaffCommissionAction> list = new ArrayList<>();
        List<CommissionPolicySettleAccountsVo> commissionPolicySettleAccountsVos = studentOfferItemMapper.bdStaffCommissionPolicySettleAccounts(bdStudentStatisticalComparisonDto, fkStaffCommissionStepKey, SecureUtil.getCountryIds(), bdStudentStatisticalComparisonDto.getStudentBeginTime(), bdStudentStatisticalComparisonDto.getStudentEndTime(), delayConfig);
        if (GeneralTool.isNotEmpty(commissionPolicySettleAccountsVos)) {
            for (CommissionPolicySettleAccountsVo commissionPolicySettleAccountsVo : commissionPolicySettleAccountsVos) {
                StaffCommissionAction staffCommissionAction = new StaffCommissionAction();
                staffCommissionAction.setFkCompanyId(commissionPolicySettleAccountsVo.getFkCompanyId());
                staffCommissionAction.setFkStudentId(commissionPolicySettleAccountsVo.getFkStudentId());
                staffCommissionAction.setFkStudentOfferItemId(commissionPolicySettleAccountsVo.getOfferItemId());
                staffCommissionAction.setFkStudentProjectRoleKey(roleKey);
                staffCommissionAction.setFkStaffCommissionStepKey(fkStaffCommissionStepKey);
                staffCommissionAction.setFkStaffId(commissionPolicySettleAccountsVo.getFkStaffId());
                staffCommissionAction.setSettlementDate(bdStudentStatisticalComparisonDto.getSettlementDate());
                staffCommissionAction.setFkCurrencyTypeNum("CNY");
                staffCommissionAction.setCommissionAmount(newAmount);
                staffCommissionAction.setStatus(2);
                staffCommissionAction.setPerformanceTime(commissionPolicySettleAccountsVo.getMinGmtCreate());

                LambdaQueryWrapper<StaffCommissionAction> staffCommissionActionLambdaQueryWrapper = new LambdaQueryWrapper<>();
                staffCommissionActionLambdaQueryWrapper.eq(StaffCommissionAction::getFkCompanyId, staffCommissionAction.getFkCompanyId());
                staffCommissionActionLambdaQueryWrapper.eq(StaffCommissionAction::getFkStudentId, staffCommissionAction.getFkStudentId());
                staffCommissionActionLambdaQueryWrapper.eq(StaffCommissionAction::getFkStudentOfferItemId, staffCommissionAction.getFkStudentOfferItemId());
                staffCommissionActionLambdaQueryWrapper.eq(StaffCommissionAction::getFkStudentProjectRoleKey, staffCommissionAction.getFkStudentProjectRoleKey());
                staffCommissionActionLambdaQueryWrapper.eq(StaffCommissionAction::getFkStaffCommissionStepKey, staffCommissionAction.getFkStaffCommissionStepKey());
                staffCommissionActionLambdaQueryWrapper.eq(StaffCommissionAction::getFkStaffId, staffCommissionAction.getFkStaffId());
                staffCommissionActionLambdaQueryWrapper.eq(StaffCommissionAction::getSettlementDate, staffCommissionAction.getSettlementDate());
                staffCommissionActionLambdaQueryWrapper.eq(StaffCommissionAction::getFkCurrencyTypeNum, staffCommissionAction);
                staffCommissionActionLambdaQueryWrapper.eq(StaffCommissionAction::getStatus, staffCommissionAction.getStatus());
                staffCommissionActionLambdaQueryWrapper.eq(StaffCommissionAction::getPerformanceTime, staffCommissionAction.getPerformanceTime());
                StaffCommissionAction commissionAction = staffCommissionActionMapper.selectOne(staffCommissionActionLambdaQueryWrapper);
                if (GeneralTool.isEmpty(commissionAction)) {
                    utilService.setCreateInfo(staffCommissionAction);
                    list.add(staffCommissionAction);
                }

            }
            //staffCommissionActionMapper.insert(staffCommissionAction);
            if (GeneralTool.isNotEmpty(list)) {
                staffCommissionActionMapper.insertBatchSomeColumn(list);
            }

        }
    }


    /**
     * BD学生奖励预统计
     *
     * @param page
     * @return
     */

    @Override
    public List<BdStudentBonusVo> bdStudentBonusPreStatistics(SearchBean<BdStudentBonusDto> page) {
        BdStudentBonusDto data = page.getData();
//        List<BdStudentBonusVo> bdStudentBonusVos = receivablePlanBonusSettingMapper.bdStudentBonusPreStatistics(bdStudentBonusVo,page);
        if (GeneralTool.isNotEmpty(data.getCompanyIds())) {
            if (!SecureUtil.validateCompanys(data.getCompanyIds())) {
                return null;
            }
        }
        //根据当前员工id获取 员工id + 业务下属员工ids(一层)
        Long staffId = SecureUtil.getStaffId();
        List<Long> staffFollowerIds = new ArrayList<>();
        Boolean isStudentAdmin = SecureUtil.getStaffInfo().getIsStudentAdmin();
        Boolean isStudentOfferItemFinancialHiding = SecureUtil.getStaffInfo().getIsStudentOfferItemFinancialHiding();
        //员工id + 业务下属员工ids
        List<Long> staffFollowerIdsResult = permissionCenterClient.getObtainDirectSubordinatesIds(staffId).getData();
        if (GeneralTool.isNotEmpty(staffFollowerIdsResult)) {
            staffFollowerIds = staffFollowerIdsResult.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        }
        staffFollowerIds.add(staffId);

        IPage<BdStudentBonusDto> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<BdStudentBonusVo> bdStudentBonusVos = studentOfferItemMapper.getBdBonusPreStatistics(iPage, data, staffFollowerIds, isStudentAdmin, isStudentOfferItemFinancialHiding,
                SecureUtil.getPermissionGroupInstitutionIds(),
                SecureUtil.getStaffBoundBdIds());
        page.setAll((int) iPage.getTotal());
        if (GeneralTool.isEmpty(bdStudentBonusVos)) {
            return Collections.emptyList();
        }
        Set<Long> agentIds = bdStudentBonusVos.stream().map(BdStudentBonusVo::getAgentId).collect(Collectors.toSet());
        Map<Long, List<AgentLabelVo>> agentLabelMap = getAgentLabelDataUtils.getAgentLabelMap(agentIds).getAgentLabelMap();
        bdStudentBonusVos.forEach(bdStudentBonusVo -> {
            if (GeneralTool.isNotEmpty(bdStudentBonusVo.getBdBonus())) {
                bdStudentBonusVo.setBdBonus(bdStudentBonusVo.getBdBonus().setScale(3, BigDecimal.ROUND_HALF_UP));
            }

            if (GeneralTool.isNotEmpty(bdStudentBonusVo.getAgentId())) {
                getAgentLabelDataUtils.setAgentLabelVosByLabelMap(bdStudentBonusVo, agentLabelMap, bdStudentBonusVo.getAgentId(), BdStudentBonusVo::setAgentLabelVos);
            }
        });
        return bdStudentBonusVos;
    }

    @Override
    public List<BaseSelectEntity> bdSubordinate() {
        //根据当前员工id获取 员工id + 业务下属员工ids(一层)
        Long staffId = SecureUtil.getStaffId();
        List<Long> staffFollowerIds = new ArrayList<>();
        //员工id + 业务下属员工ids
        List<Long> staffFollowerIdsResult = permissionCenterClient.getObtainDirectSubordinatesIds(staffId).getData();
//        if (GeneralTool.isNotEmpty(staffFollowerIdsResult)) {
//            staffFollowerIds = staffFollowerIdsResult.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
//        }
//
//        staffFollowerIds.add(staffId);
//
//        Result<Map<Long, String>> staffNameMap = permissionCenterClient.getStaffNameMap(new HashSet<>(staffFollowerIds));
//        List<BaseSelectEntity> baseSelectEntities = new ArrayList<>();
//        staffNameMap.getData().forEach((id, name) -> {
//            BaseSelectEntity baseSelectEntity = new BaseSelectEntity();
//            baseSelectEntity.setId(id);
//            baseSelectEntity.setName(name);
//            baseSelectEntities.add(baseSelectEntity);
//        });
//        return baseSelectEntities;


        List<Long> processedSubordinateIds = new ArrayList<>();
        if (GeneralTool.isNotEmpty(staffFollowerIdsResult)) {
            // 使用LinkedHashSet去重（保持顺序）
            Set<Long> uniqueIds = new LinkedHashSet<>(staffFollowerIdsResult);
            processedSubordinateIds = new ArrayList<>(uniqueIds);
        }

        List<Long> finalStaffIds = new ArrayList<>();
        finalStaffIds.add(staffId); // 当前员工ID插入最前面
        finalStaffIds.addAll(processedSubordinateIds); // 追加处理后的下属ID（保持原顺序）

        // 4. 根据最终ID列表获取公司名称（或后续操作）
        Result<Map<Long, String>> staffNameMapResult = permissionCenterClient.getStaffNameMap(new HashSet<>(finalStaffIds));
        Map<Long, String> staffNameMap = staffNameMapResult.getData();

        // 5. 组装结果（保持ID顺序）
        List<BaseSelectEntity> baseSelectEntities = new ArrayList<>();
        for (Long id : finalStaffIds) {
            String name = staffNameMap.getOrDefault(id, "");
            BaseSelectEntity entity = new BaseSelectEntity();
            entity.setId(id);
            entity.setName(name);
            baseSelectEntities.add(entity);
        }

        return baseSelectEntities;
    }

}

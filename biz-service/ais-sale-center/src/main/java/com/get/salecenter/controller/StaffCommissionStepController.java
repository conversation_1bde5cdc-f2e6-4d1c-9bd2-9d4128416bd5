package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseVoEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.vo.StaffCommissionStepVo;
import com.get.salecenter.service.IStaffCommissionStepService;
import com.get.salecenter.dto.StaffCommissionStepDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2023/2/6 10:00
 * @verison: 1.0
 * @description:
 */
@Api(tags = "结算步骤管理")
@RestController
@RequestMapping("sale/staffCommissionStep")
public class StaffCommissionStepController {


    @Resource
    private IStaffCommissionStepService staffCommissionStepService;

    @ApiOperation(value = "新增结算步骤(批量新增)", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/结算步骤管理/批量新增结算步骤")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody @Validated({BaseVoEntity.Add.class}) ValidList<StaffCommissionStepDto> staffCommissionStepDtos) {
        staffCommissionStepService.batchAdd(staffCommissionStepDtos);
        return SaveResponseBo.ok();
    }

    @ApiOperation(value = "删除", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/结算步骤管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        staffCommissionStepService.delete(id);
        return DeleteResponseBo.ok();
    }

    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/结算步骤管理/修改接口")
    @PostMapping("update")
    public ResponseBo<StaffCommissionStepVo> update(@RequestBody @Validated({BaseVoEntity.Update.class}) StaffCommissionStepDto staffCommissionStepDto) {
        return UpdateResponseBo.ok(staffCommissionStepService.updateStaffCommissionStep(staffCommissionStepDto));
    }

    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/结算步骤管理/查询")
    @PostMapping("datas")
    public ListResponseBo<StaffCommissionStepVo> datas(@RequestBody SearchBean<StaffCommissionStepDto> page) {
        List<StaffCommissionStepVo> datas = staffCommissionStepService.getStaffCommissionStepDtos(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    @ApiOperation(value = "排序", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/结算步骤管理/排序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<StaffCommissionStepDto> staffCommissionStepDtos) {
        staffCommissionStepService.movingOrder(staffCommissionStepDtos);
        return ResponseBo.ok();
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "结算步骤下拉", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/结算步骤管理/结算步骤下拉")
    @GetMapping("getStaffCommissionStepSelect/{companyId}")
    public ResponseBo<BaseSelectEntity> getCancelOfferReasonSelect(@PathVariable("companyId")Long companyId) {
        return new ListResponseBo<>(staffCommissionStepService.getCancelOfferReasonSelect(companyId));
    }

}

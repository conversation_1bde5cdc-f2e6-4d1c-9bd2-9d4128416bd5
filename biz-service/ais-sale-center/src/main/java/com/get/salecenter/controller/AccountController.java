package com.get.salecenter.controller;

import com.get.core.tool.api.Result;
import com.get.salecenter.entity.AgentContractAccount;
import com.get.salecenter.service.AccountService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
public class AccountController {

    @Resource
    private AccountService accountService;

    @PostMapping("/getAccount")
    public Result<AgentContractAccount> getAccount(@RequestParam("agentId") Long agentId, @RequestParam("backNum") String backNum){
        return accountService.getAccount(agentId,backNum);
    }
}

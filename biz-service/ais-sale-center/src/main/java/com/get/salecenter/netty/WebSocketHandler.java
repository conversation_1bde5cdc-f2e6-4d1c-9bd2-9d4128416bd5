package com.get.salecenter.netty;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.get.common.eunms.MsgActionEnum;
import com.get.core.mybatis.base.SystemPageVo;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.config.NettyConfig;
import com.get.salecenter.netty.protocol.HeartResponse;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.handler.codec.http.websocketx.TextWebSocketFrame;
import io.netty.util.AttributeKey;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;


/**
 * TextWebSocketFrame类型， 表示一个文本帧
 *
 * <AUTHOR>
 * @date 2020-03-28-13:47
 */
@Component
@ChannelHandler.Sharable
public class WebSocketHandler extends SimpleChannelInboundHandler<TextWebSocketFrame> {
    private static final Logger log = LoggerFactory.getLogger(NettyServer.class);

    /**
     * 一旦连接，第一个被执行
     *
     * @param ctx
     * @throws Exception
     */
    @Override
    public void handlerAdded(ChannelHandlerContext ctx) throws Exception {
        log.info("handlerAdded 被调用" + ctx.channel().id().asLongText());
        // 添加到channelGroup 通道组
        Channel channel = ctx.channel();
        NettyConfig.getChannelGroup().add(channel);
    }

    /**
     * 读取数据
     */
    @Override
    protected void channelRead0(ChannelHandlerContext ctx, TextWebSocketFrame msg) throws Exception {
        //获取传入jsonString
        String jsonString = msg.text();
        SystemPageVo systemPageVo = JSONUtil.toBean(jsonString, SystemPageVo.class);
        log.info("websocket推送测试 systemPageVo:{}", JSONUtil.toJsonStr(systemPageVo));
        // 获取用户ID
        Long uid = systemPageVo.getId();
        //获取消息类型
        Integer action = systemPageVo.getAction();

        //第一次连接时
        if (MsgActionEnum.CONNECT.key.equals(action)) {
            // 将用户ID作为自定义属性加入到channel中，方便随时channel中获取用户ID
            NettyConfig.getUserChannelMap().remove(uid);
            NettyConfig.getUserSearchMap().remove(uid);
            AttributeKey<String> key = AttributeKey.valueOf("userId");
            ctx.channel().attr(key).setIfAbsent(String.valueOf(uid));
            // 关联channel
            NettyConfig.getUserChannelMap().put(uid, ctx.channel());
            // 记录用户查询条件
            NettyConfig.getUserSearchMap().put(uid, systemPageVo);
            // 回复消息
            HeartResponse heartResponse = new HeartResponse();
            heartResponse.setData("connection succeeded");
            ctx.channel().writeAndFlush(new TextWebSocketFrame(JSON.toJSONString(heartResponse)));
        } else if (MsgActionEnum.SEARCH.key.equals(action)) {
            // 记录用户查询条件
            NettyConfig.getUserSearchMap().put(uid, systemPageVo);
        } else if (MsgActionEnum.KEEPALIVE.key.equals(action)) {
            ctx.channel().writeAndFlush(new TextWebSocketFrame(JSON.toJSONString(new HeartResponse())));
            //客户端心跳
        }
    }

    @Override
    public void handlerRemoved(ChannelHandlerContext ctx) throws Exception {
        log.info("handlerRemoved 被调用,客户端移除" + ctx.channel().id().asLongText());
        // 删除通道
        closeResource(ctx);
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        log.info("异常：{}", cause.getMessage());
        closeResource(ctx);
    }

    /**
     * @return void
     * @Description: 关闭资源
     * @Param [ctx]
     * <AUTHOR>
     */
    private void closeResource(ChannelHandlerContext ctx) {
        // 删除通道
        NettyConfig.getChannelGroup().remove(ctx.channel());
        removeUserId(ctx);
        ctx.channel().close();
        ctx.close();
    }

    /**
     * 删除用户与channel的对应关系
     *
     * @param ctx
     */
    private void removeUserId(ChannelHandlerContext ctx) {
        //删除map中的channel
        AttributeKey<String> key = AttributeKey.valueOf("userId");
        String userId = ctx.channel().attr(key).get();
        if (GeneralTool.isNotEmpty(userId)) {
            NettyConfig.getUserChannelMap().remove(Long.valueOf(userId));
            NettyConfig.getUserSearchMap().remove(Long.valueOf(userId));
        }
    }
}

package com.get.salecenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.core.tool.api.Result;
import com.get.financecenter.dto.OneClickSettlementDto;
import com.get.institutioncenter.entity.ContractFormula;
import com.get.institutioncenter.entity.ContractFormulaCommission;
import com.get.rocketmqcenter.dto.InsurancePlanMessageDto;
import com.get.salecenter.vo.*;
import com.get.salecenter.entity.PayablePlan;
import com.get.salecenter.entity.StudentOfferItem;
import com.get.salecenter.dto.*;
import com.get.salecenter.dto.query.PayablePlanNewQueryDto;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/11/23
 * @TIME: 12:33
 * @Description:
 **/
public interface IPayablePlanService extends IService<PayablePlan> {

    /**
     * @return java.util.List<com.get.salecenter.vo.PayablePlanVo>
     * @Description: 列表
     * @Param [planVo, page]
     * <AUTHOR>
     */
    List<PayablePlanVo> datas(PayablePlanDto planVo, Page page, String[] times);


    /**
     * 获取付款单应付列表
     * @param planVo
     * @param page
     * @return
     */
    ResponseBo<PayablePlanVo> getPayableList(PayablePlanDto planVo, Page page);

    void saveBatch(List<PayablePlan> planList);
    /**
     * 检查应付是否能作废
     * @param receivablePlanId
     * @return
     */
    Boolean checkPayableInfo(Long receivablePlanId);

    /**
     * 作废应付计划
     */
    void cancelPayPlan(Long receivablePlanId);
    /**
     * @Description: 代理应付汇总统计明细
     * @Author: Jerry
     * @Date:12:00 2021/11/22
     */
    List<PayablePlanVo> agentPaySumDetail(AgentPaySumDetailDto agentPaySumDetailDto, Page page);

    /**
     * 学生应付汇总统计明细
     *
     * @param studentPaySumDetailDto
     * @param page
     * @return
     */
    List<PayablePlanVo> studentPaySumDetail(StudentPaySumDetailDto studentPaySumDetailDto, Page page);

    /**
     * @return java.lang.Long
     * @Description: 新增
     * @Param [planVo]
     * <AUTHOR>
     */
    Long addPayablePlan(PayablePlanDto planVo);

    /**
     * @return com.get.salecenter.vo.PayablePlanVo
     * @Description: 修改
     * @Param [planVo]
     * <AUTHOR>
     */
    PayablePlanVo updatePayablePlan(PayablePlanDto planVo);

    /**
     * @return com.get.salecenter.vo.PayablePlanVo
     * @Description: 详情
     * @Param [planVo]
     * <AUTHOR>
     */
    PayablePlanVo findPayablePlanById(Long id);

    /**
     * @return void
     * @Description: 删除
     * @Param [id]
     * <AUTHOR>
     */
    void delete(Long id);

    /**
     * 根据IDs获取应付计划列表
     *
     * @param ids
     * @return
     */
    List<PayablePlanVo> getPayablePlanByIds(Set<Long> ids);


    /**
     * 获取应付详情
     * @param ids
     * @return
     */
    List<PayablePlanVo> getPayablePlanDetailsByIds(Set<Long> ids);

    /**
     * @return java.util.List<com.get.salecenter.vo.MediaAndAttachedDto>
     * @Description: 添加附件
     * @Param [mediaAttachedVo]
     * <AUTHOR>
     */
    List<MediaAndAttachedVo> addMedia(List<MediaAndAttachedDto> mediaAttachedVo);

    /**
     * @return java.util.List<com.get.salecenter.vo.MediaAndAttachedDto>
     * @Description: 获取附件
     * @Param [andAttachedVo, page]
     * <AUTHOR>
     */
    List<MediaAndAttachedVo> getMedia(MediaAndAttachedDto andAttachedVo, Page page);

    /**
     * @return void
     * @Description: 关闭
     * @Param [id, status]
     * <AUTHOR>
     */
    void unablePayable(Long id, Long status);


    /**
     * @return com.get.salecenter.vo.PayablePlanVo
     * @Description: 根据应付类型关键字和应付类型对应记录Id 查询
     * @Param [typeKey, targetId]
     * <AUTHOR>
     */
    List<PayablePlanVo> getPayablePlan(String typeKey, Long targetId);


    List<PublicPayFormDetailVo> getPaidAmountInfo(List<Long> ids);


    /**
     * @return com.get.salecenter.vo.PayablePlanVo
     * @Description: 根据应付类型关键字和应付类型对应记录Id 查询
     * @Param [typeKey, targetId]
     * <AUTHOR>
     */
    List<Long> getPayablePlanId(String typeKey, Long targetId);


    Map<Long,List<Long>> getPayablePlanIds(String typeKey, Set<Long> targetIds);

    /**
     * @return
     * @Description：feign 根据应付计划id查询应付计划应付金额
     * @Param
     * @Date 17:00 2021/4/23
     * <AUTHOR>
     */
    BigDecimal getPayablePlanAmountById(Long id);

    /**
     * @return void
     * @Description: 生成应付计划
     * @Param [contractFormula]
     * <AUTHOR>
     **/
    void generatePayablePlan(ContractFormula contractFormula, StudentOfferItem studentOfferItem, BigDecimal fee, List<ContractFormulaCommission> contractFormulaCommissions);

    /**
     * feign 批量编辑应付计划
     *
     * @return
     * @Date 12:38 2021/12/23
     * <AUTHOR>
     */
    Boolean batchUpdatePayablePlan(List<PayablePlanDto> payablePlanDtoList);

    /**
     * 财务确认代理佣金结算
     *
     * @Date 12:39 2021/12/23
     * <AUTHOR>
     */
//    Boolean financeConfirmSettlement(List<Long> payablePlanIdList);

    /**
     * feign 财务-应付计划编辑详情回显
     *
     * @Date 12:38 2021/12/23
     * <AUTHOR>
     */
    StudentPlanVo financePlanDetails(Long planId);

//    /**
//     * 获取iFile Excel信息
//     *
//     * @Date 14:14 2022/3/5
//     * <AUTHOR>
//     */
//    List<IFileInfoVo> iFileExcelInfo(String numSettlementBatch);

    /**
     * 获取iFile Excel信息
     *
     * @Date 14:14 2022/3/8
     * <AUTHOR>
     */
    List<IFileInfoVo> iFileGroupByAgidAndCurrencyInfo(String numSettlementBatch);

    /**
     * 删除记录
     *
     * @return
     */
    void deletePayablePlanByItemId(Long id);

    /**
     * 导出应付计划Excel
     *
     * @param response
     */
    void exportPayablePlanExcel(HttpServletResponse response, PayablePlanNewQueryDto payablePlanNewVo);

//    /**
//     * 应付计划预付按钮
//     *
//     * @Date 11:04 2022/3/22
//     * <AUTHOR>
//     */
//    void prepaymentButton(PrepaymentButtonDto prepaymentButtonDto);

//    /**
//     * 插入应付计划结算分期表
//     *
//     * @Date 14:52 2022/3/30
//     * <AUTHOR>
//     */
//    void insertSettlementInstallment(PayablePlan payablePlan, SaleReceiptFormItemVo receiptFormItemDto);


    List<PayablePlanNewVo> payablePlanDatas(PayablePlanNewQueryDto data, SearchBean<PayablePlanNewQueryDto> page, String[] times);

//    /**
//     * 应付计划取消预付按钮
//     *
//     * @Date 18:16 2022/4/19
//     * <AUTHOR>
//     */
//    void cancelPrepaymentButton(PrepaymentButtonCancelDto prepaymentButtonCancelDto);


    /**
     * feign 根据应收计划id获取对应的应付计划信息
     *
     * @Date 23:55 2022/4/21
     * <AUTHOR>
     */
    PayablePlan getPayablePlanByReceivablePlanId(Long fkReceivablePlanId);

    /**
     * 批量更新
     * @param payablePlans
     * @return
     */
    Boolean batchUpdateByIds(List<PayablePlan> payablePlans);

    /**
     *  获取应付计划
     * @param payablePlanId
     * @return
     */
    Result<PayablePlan> doGetPayableInfoById(Long payablePlanId);
    /**
     * 应付计划一键结算按钮
     *
     * @Date 14:51 2022/5/12
     * <AUTHOR>
     */
    void oneClickSettlement(OneClickSettlementDto oneClickSettlementDto);

    /**
     * 通过学习计划id获取应付计划
     * @param offerItemId
     * @return
     */
    Result<List<SettlementPayablePlanVo>> getPayablePlanByOfferItemId(Long offerItemId);

    /**
     * 获取最新的三条学费
     * @param fkCompanyId
     * @return
     */
    List<String> getPayPlanTheLatestThreeTuitionFees(Long fkCompanyId);

    /**
     * 发送佣金通知邮件
     * @param emails
     * @param fkPayablePlanId
     * @param commissionNotice
     */
    void sendSettlementCommissionEmail(List<String> emails,Long fkPayablePlanId, String commissionNotice);

    void batchUpdatePayablePlanInfo(BatchUpdatePayablePlanDto batchUpdatePayablePlanDto);

    PayablePlanCheckOutVo checkoutPayablePlan(PayablePlanCheckOutDto payablePlanCheckOutDto);

    /**
     * 修改应付计划佣金状态
     * @param updatePayablePlanStatusSettlementDto
     * @return
     */
    Boolean updatePayablePlanStatusSettlement(UpdatePayablePlanStatusSettlementDto updatePayablePlanStatusSettlementDto);

    /**
     * 根据应收计划ids获取对应的应付计划信息
     *
     * @param fkReceivablePlanIdList
     * @return
     */
    List<PayablePlan> getPayablePlanByReceivablePlanIds(List<Long> fkReceivablePlanIdList);

    /**
     * 澳小保创建应收应付
     * @param insurancePlanMessageDto
     * @return
     */
    Boolean createInsurancePlan(InsurancePlanMessageDto insurancePlanMessageDto);
}

package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.core.mybatis.service.impl.GetServiceImpl;
import com.get.salecenter.dao.sale.EventBillEventSummaryMapper;
import com.get.salecenter.entity.EventBillEventSummary;
import com.get.salecenter.service.IEventBillEventSummaryService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2022/6/7 18:06
 * @verison: 1.0
 * @description:
 */
@Service
public class EventBillEventSummaryServiceImpl extends GetServiceImpl<EventBillEventSummaryMapper, EventBillEventSummary> implements IEventBillEventSummaryService {

    @Resource
    private EventBillEventSummaryMapper eventBillEventSummaryMapper;
    @Override
    public Boolean batchAddByIds(List<EventBillEventSummary> eventBillEventSummaries) {
        return saveBatch(eventBillEventSummaries);
    }


    @Override
    public List<EventBillEventSummary> getEventBillEventSummariesByCondition(LambdaQueryWrapper<EventBillEventSummary> lambdaQueryWrapper) {
        return eventBillEventSummaryMapper.selectList(lambdaQueryWrapper);
    }
}

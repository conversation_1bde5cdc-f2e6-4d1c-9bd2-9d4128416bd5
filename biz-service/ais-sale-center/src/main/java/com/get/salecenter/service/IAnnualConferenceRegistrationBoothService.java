package com.get.salecenter.service;


import com.get.salecenter.vo.AnnualConferenceRegistrationBoothVo;
import com.get.salecenter.dto.AnnualConferenceRegistrationBoothDto;

import java.util.List;

/**
 * @author: Sea
 * @create: 2021/4/29 11:11
 * @verison: 1.0
 * @description:
 */
public interface IAnnualConferenceRegistrationBoothService {
    /**
     * @return void
     * @Description :新增
     * @Param [annualConferenceRegistrationBoothDto]
     * <AUTHOR>
     */
    void addAnnualConferenceRegistrationBooth(AnnualConferenceRegistrationBoothDto annualConferenceRegistrationBoothDto);

    /**
     * @return void
     * @Description :通过fkid删除
     * @Param [annualConferenceRegistrationId]
     * <AUTHOR>
     */
    void deleteByFkid(Long annualConferenceRegistrationId);

    List<AnnualConferenceRegistrationBoothVo> getBoothDto(Long annualConferenceRegistrationId);

    /**
     * @return java.util.List<java.lang.Integer>
     * @Description :获取已选了的展位号
     * @Param []
     * <AUTHOR>
     */
    List<Integer> getBoothIndex();

    /**
     * @return java.lang.Boolean
     * @Description :该展位是否已坐
     * @Param [boothIndex]
     * <AUTHOR>
     */
    Boolean haveSit(Integer boothIndex);
}

package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.salecenter.vo.ConventionHotelRoomVo;
import com.get.salecenter.service.IConventionHotelRoomService;
import com.get.salecenter.dto.ConventionHotelRoomDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/8/18 11:35
 * @verison: 1.0
 * @description: 酒店房间管理控制器
 */
@Api(tags = "酒店房间管理")
@RestController
@RequestMapping("sale/conventionHotelRoom")
public class ConventionHotelRoomController {

    @Resource
    private IConventionHotelRoomService conventionHotelRoomService;

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/酒店房间管理/酒店房间详情")
    @GetMapping("/{id}")
    public ResponseBo<ConventionHotelRoomVo> detail(@PathVariable("id") Long id) {
        ConventionHotelRoomVo data = conventionHotelRoomService.findConventionHotelRoomById(id);
        return new ResponseBo<>(data);
    }

    /**
     * 新增信息
     *
     * @param conventionHotelRoomDto
     * @return
     * @
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/酒店房间管理/新增酒店房间")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(ConventionHotelRoomDto.Add.class) ConventionHotelRoomDto conventionHotelRoomDto) {
        return SaveResponseBo.ok(conventionHotelRoomService.addConventionHotelRoom(conventionHotelRoomDto));
    }

    /**
     * 删除信息
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/酒店房间管理/删除酒店房间")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        conventionHotelRoomService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * 批量新增信息
     *
     * @param conventionHotelRoomDto
     * @return
     * @
     */
    @ApiOperation(value = "批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/酒店房间管理/新增酒店房间")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody  @Validated(ConventionHotelRoomDto.Add.class) ConventionHotelRoomDto conventionHotelRoomDto) {
        conventionHotelRoomService.batchAdd(conventionHotelRoomDto);
        return SaveResponseBo.ok();
    }

    /**
     * 列表数据
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "列表数据", notes = "hotel酒店名称 roomType房型（输入酒店名称后调用房型下拉框才能选择） roomNum房号 conventionId峰会id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/酒店房间管理/查询酒店房间")
    @PostMapping("datas")
    public ResponseBo datas(@RequestBody SearchBean<ConventionHotelRoomDto> page) {
        List<ConventionHotelRoomVo> datas = conventionHotelRoomService.getConventionHotelRooms(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 修改酒店房号（管理员）
     *
     * @param conventionHotelRoomDto
     * @return
     * @
     */
    @ApiOperation(value = "修改酒店房号（管理员）", notes = "必传：fkConventionHotelId房型id systemRoomNum系统房间编号 hotelRoomNum酒店房间编号")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/酒店住房安排管理/修改床位（管理员）")
    @PostMapping("updateHotelRoomNum")
    public ResponseBo updateHotelRoomNum(@RequestBody ConventionHotelRoomDto conventionHotelRoomDto) {
        conventionHotelRoomService.updateHotelRoomNum(conventionHotelRoomDto);
        return UpdateResponseBo.ok();
    }

    /**
     * 修改酒店房号（非管理员）
     *
     * @param conventionHotelRoomDto
     * @return
     * @
     */
    @ApiOperation(value = "修改酒店房号（非管理员）", notes = "必传：fkConventionHotelId房型id systemRoomNum系统房间编号 hotelRoomNum酒店房间编号")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/酒店住房安排管理/修改床位（非管理员）")
    @PostMapping("updateHotelRoomNumLimited")
    public ResponseBo updateHotelRoomNumLimited(@RequestBody ConventionHotelRoomDto conventionHotelRoomDto) {
        conventionHotelRoomService.updateHotelRoomNumLimited(conventionHotelRoomDto);
        return UpdateResponseBo.ok();
    }


    /**
     * 新增单个房间
     *
     * @param conventionHotelRoomDto
     * @return
     * @
     */
    @ApiOperation(value = "新增单个房间", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/酒店房间管理/新增单个房间")
    @PostMapping("addConventionHotelRoom")
    public ResponseBo addConventionHotelRoom(@RequestBody @Validated(ConventionHotelRoomDto.Add.class) ConventionHotelRoomDto conventionHotelRoomDto) {
        return SaveResponseBo.ok(conventionHotelRoomService.addSingleConventionHotelRoom(conventionHotelRoomDto));
    }

}

package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.service.GetService;
import com.get.salecenter.vo.StaffCommissionStepVo;
import com.get.salecenter.entity.StaffCommissionStep;
import com.get.salecenter.dto.StaffCommissionStepDto;

import java.util.List;

/**
 * @author: Hardy
 * @create: 2023/2/6 10:03
 * @verison: 1.0
 * @description:
 */
public interface IStaffCommissionStepService extends GetService<StaffCommissionStep> {

    /**
     * 批量新增
     * @param staffCommissionStepDtos
     */
    void batchAdd(List<StaffCommissionStepDto> staffCommissionStepDtos);

    /**
     * 删除
     * @param id
     */
    void delete(Long id);

    /**
     * 更新
     * @param staffCommissionStepDto
     * @return
     */
    StaffCommissionStepVo updateStaffCommissionStep(StaffCommissionStepDto staffCommissionStepDto);

    /**
     * 列表
     * @param staffCommissionStepDto
     * @param page
     * @return
     */
    List<StaffCommissionStepVo> getStaffCommissionStepDtos(StaffCommissionStepDto staffCommissionStepDto, Page page);

    /**
     * 排序
     * @param staffCommissionStepDtos
     */
    void movingOrder(List<StaffCommissionStepDto> staffCommissionStepDtos);

    /**
     * 结算步骤下拉
     * @param companyId
     * @return
     */
    List<BaseSelectEntity> getCancelOfferReasonSelect(Long companyId);
}

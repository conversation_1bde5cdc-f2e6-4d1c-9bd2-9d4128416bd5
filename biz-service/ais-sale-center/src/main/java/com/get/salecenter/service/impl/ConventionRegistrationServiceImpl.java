package com.get.salecenter.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.file.utils.FileUtils;
import com.get.financecenter.feign.IFinanceCenterClient;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.remindercenter.entity.EmailSenderQueue;
import com.get.remindercenter.enums.EmailTemplateEnum;
import com.get.remindercenter.feign.IReminderCenterClient;
import com.get.salecenter.dao.sale.ConventionPersonRegistrationMapper;
import com.get.salecenter.dao.sale.ConventionRegistrationAreaCountryMapper;
import com.get.salecenter.dao.sale.ConventionRegistrationMapper;
import com.get.salecenter.dao.sale.ConventionSponsorMapper;
import com.get.salecenter.dao.sale.EventCostMapper;
import com.get.salecenter.dto.*;
import com.get.salecenter.entity.*;
import com.get.salecenter.service.*;
import com.get.salecenter.vo.*;
import com.get.salecenter.vo.BoothVo;
import com.get.salecenter.utils.GetExchangeRateUtils;
import com.get.salecenter.utils.MyStringUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.get.core.tool.utils.DateUtil.now;

/**
 * @author: Sea
 * @create: 2020/7/3 11:03
 * @verison: 1.0
 * @description: 峰会报名管理实现类
 */
@Service
public class ConventionRegistrationServiceImpl extends ServiceImpl<ConventionRegistrationMapper, ConventionRegistration> implements IConventionRegistrationService {
    @Resource
    private ConventionRegistrationMapper conventionRegistrationMapper;
    @Resource
    private ConventionRegistrationAreaCountryMapper conventionRegistrationAreaCountryMapper;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    private IFinanceCenterClient financeCenterClient;
    @Resource
    private UtilService utilService;
    @Resource
    private IDeleteService deleteService;
    @Resource
    private ConventionSponsorMapper conventionSponsorMapper;
    @Resource
    private IConventionSponsorSponsorFeeService conventionSponsorSponsorFeeService;
    @Resource
    private GetExchangeRateUtils getExchangeRateUtils;
    @Resource
    private ConventionPersonRegistrationMapper conventionPersonRegistrationMapper;
    @Resource
    private EventCostMapper eventCostMapper;
    @Resource
    private IEventBillService eventBillService;
    @Resource
    private IEventCostService eventCostService;
    @Resource
    private IEventBillAreaCountryService eventBillAreaCountryService;
    @Resource
    private IEventBillEventSummaryService eventBillEventSummaryService;
    @Resource
    private IConventionService conventionService;

    @Resource
    private IReminderCenterClient reminderCenterClient;

    @Override
    public ConventionRegistrationVo findConventionRegistrationById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ConventionRegistration conventionRegistration = conventionRegistrationMapper.selectById(id);
        ConventionRegistrationVo conventionRegistrationVo = BeanCopyUtils.objClone(conventionRegistration, ConventionRegistrationVo::new);
        if (GeneralTool.isNotEmpty(conventionRegistrationVo)) {
            setResult(conventionRegistrationVo);
        }
        return conventionRegistrationVo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(List<ConventionRegistrationDto> conventionRegistrationDtos) {
        if (GeneralTool.isEmpty(conventionRegistrationDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        for (ConventionRegistrationDto conventionRegistrationDto : conventionRegistrationDtos) {
            ConventionRegistration conventionRegistration = BeanCopyUtils.objClone(conventionRegistrationDto, ConventionRegistration::new);
            if (conventionRegistration!=null) {
                if (StringUtils.isNotBlank(conventionRegistration.getBoothNum())){
                    Integer flag = conventionRegistrationMapper.validateAdd(conventionRegistration.getFkConventionId(), conventionRegistration.getBoothNum());
                    if (flag > 0) {
                        throw new GetServiceException(LocaleMessageUtils.getMessage("num_exist"));
                    }
                }
                if (GeneralTool.isEmpty(conventionRegistrationDto.getReceiptCode())) {
                    //没有回执码得时候，自动获取，重复则继续获取，直到不重复
                    boolean result = true;
                    while (result) {
                        String randomCode = MyStringUtils.getRandomCode();
                        if (!validateReceiptCode(randomCode)) {
                            result = false;
                            conventionRegistration.setReceiptCode(randomCode);
                        }
                    }
                }
                conventionRegistration.setStatus(0);
                setFreeCny(conventionRegistration);
                utilService.updateUserInfoToEntity(conventionRegistration);
                conventionRegistrationMapper.insert(conventionRegistration);
                if (StringUtils.isBlank(conventionRegistration.getBoothNum())) {
                    String s = String.valueOf(conventionRegistration.getId());
                    StringBuilder boothNum = new StringBuilder("R");
                    if (s.length() < 6) {
                        for (int i = 0; i < 6 - s.length(); i++) {
                            boothNum.append("0");
                        }
                        boothNum.append(s);
                    }
                    conventionRegistration.setBoothNum(boothNum.toString());
                }
                conventionRegistrationMapper.updateById(conventionRegistration);
                //中间表对象
                ConventionRegistrationAreaCountry conventionRegistrationAreaCountry = new ConventionRegistrationAreaCountry();
                //fk报名id即刚刚插入成功的报名表返回的id
                conventionRegistrationAreaCountry.setFkConventionRegistrationId(conventionRegistration.getId());
                if (GeneralTool.isNotEmpty(conventionRegistrationDto.getCNums())) {
                    List<String> cNums = conventionRegistrationDto.getCNums();
                    for (String cNum : cNums) {
                        //保存到报名和国家的中间表中
                        conventionRegistrationAreaCountry.setFkAreaCountryKey(cNum);
                        utilService.updateUserInfoToEntity(conventionRegistrationAreaCountry);
                        conventionRegistrationAreaCountryMapper.insert(conventionRegistrationAreaCountry);
                    }
                }
            }
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(ConventionRegistrationRosterDto conventionRegistrationDto) {
        if (GeneralTool.isEmpty(conventionRegistrationDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }

        if(GeneralTool.isEmpty(conventionRegistrationDto.getBoothNameList())||conventionRegistrationDto.getBoothNameList().size()==0){
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }

        Long id = null;
        // 定义 StringBuilder 用于拼接 randomCode
        StringBuilder receiptCodeBuilder = new StringBuilder();

        for(ConventionRegistrationBoothDto booth:conventionRegistrationDto.getBoothNameList()){
            ConventionRegistration conventionRegistration = BeanCopyUtils.objClone(conventionRegistrationDto, ConventionRegistration::new);
            conventionRegistration.setBoothName(booth.getBoothName());
            conventionRegistration.setFkCurrencyTypeNum("USD");
            // 当前时间
//            Date now = new Date();

            LocalDate localDate = LocalDate.now(); // 获取当前日期（不带时间）
            Date nowDateOnly = Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
            // 设置 cutoffDate 为 2025-07-15 00:00:00
            Calendar cal = Calendar.getInstance();
            cal.set(2025, Calendar.JULY, 15);
            cal.set(Calendar.MILLISECOND, 0);
            Date cutoffDate = cal.getTime();
            if (!nowDateOnly.after(cutoffDate)) {
                conventionRegistration.setRegistrationFee(BigDecimal.valueOf(4000.00));
            }else {
                conventionRegistration.setRegistrationFee(BigDecimal.valueOf(4500.00));
            }
//            if (nowDateOnly.before(cutoffDate)) {
//                conventionRegistration.setRegistrationFee(BigDecimal.valueOf(4000.00));
//            } else if (nowDateOnly.after(cutoffDate)) {
//                conventionRegistration.setRegistrationFee(BigDecimal.valueOf(4500.00));
//            }else {
//                conventionRegistration.setRegistrationFee(BigDecimal.valueOf(4000.00));
//            }
            conventionRegistration.setIsVerified(0);
            if (conventionRegistration!=null) {
                if (StringUtils.isNotBlank(conventionRegistration.getBoothNum())){
                    Integer flag = conventionRegistrationMapper.validateAdd(conventionRegistration.getFkConventionId(), conventionRegistration.getBoothNum());
                    if (flag > 0) {
                        throw new GetServiceException(LocaleMessageUtils.getMessage("num_exist"));
                    }
                }
                if (GeneralTool.isEmpty(conventionRegistrationDto.getReceiptCode())) {
                    //没有回执码得时候，自动获取，重复则继续获取，直到不重复
                    boolean result = true;
                    String randomCode = null;
                    while (result) {
                         randomCode = MyStringUtils.getRandomCode();
                        if (!validateReceiptCode(randomCode)) {
                            result = false;
                            conventionRegistration.setReceiptCode(randomCode);

                            // 拼接 randomCode 到 receiptCodeBuilder 中
                            if (receiptCodeBuilder.length() > 0) {
                                receiptCodeBuilder.append(",");
                            }
                            receiptCodeBuilder.append(randomCode);
                        }
                    }
                }
                conventionRegistration.setStatus(0);
                setFreeCny(conventionRegistration);
                utilService.updateUserInfoToEntity(conventionRegistration);
                conventionRegistrationMapper.insert(conventionRegistration);
                if (StringUtils.isBlank(conventionRegistration.getBoothNum())) {
                    String s = String.valueOf(conventionRegistration.getId());
                    StringBuilder boothNum = new StringBuilder("R");
                    if (s.length() < 6) {
                        for (int i = 0; i < 6 - s.length(); i++) {
                            boothNum.append("0");
                        }
                        boothNum.append(s);
                    }
                    conventionRegistration.setBoothNum(boothNum.toString());
                }
                conventionRegistrationMapper.updateById(conventionRegistration);
                //中间表对象
                ConventionRegistrationAreaCountry conventionRegistrationAreaCountry = new ConventionRegistrationAreaCountry();
                //fk报名id即刚刚插入成功的报名表返回的id
                conventionRegistrationAreaCountry.setFkConventionRegistrationId(conventionRegistration.getId());
                if (GeneralTool.isNotEmpty(booth.getCNums())) {
                    List<String> cNums = booth.getCNums();
                    for (String cNum : cNums) {
                        //保存到报名和国家的中间表中
                        conventionRegistrationAreaCountry.setFkAreaCountryKey(cNum);
                        utilService.updateUserInfoToEntity(conventionRegistrationAreaCountry);
                        conventionRegistrationAreaCountryMapper.insert(conventionRegistrationAreaCountry);
                    }
                }
            }
            id = conventionRegistration.getId();
        }
        Map<String,String> map = new HashMap<>();
        map.put("language",conventionRegistrationDto.getLanguage());
        map.put("receiptCodeList",receiptCodeBuilder.toString());
        String mapStr = JSON.toJSONString(map);
        EmailSenderQueue emailSenderQueue = new EmailSenderQueue();
        emailSenderQueue.setOperationTime(now());
        emailSenderQueue.setFkTableName(TableEnum.SUMMIT_REGISTRATION.key);
        emailSenderQueue.setFkTableId(id);
        emailSenderQueue.setFkEmailTypeKey(EmailTemplateEnum.SUMMIT_REGISTRATION_REMINDER.getEmailTemplateKey());
        emailSenderQueue.setFkDbName(ProjectKeyEnum.SALE_CENTER.key);
        emailSenderQueue.setEmailParameter(mapStr);
        List<EmailSenderQueue> queueList = new ArrayList<>();
        queueList.add(emailSenderQueue);
        Result<Boolean> result=reminderCenterClient.batchAddEmailQueue(queueList);
        if (!result.isSuccess()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage(result.getMessage()));
        }

    }

    /**
     * 设置费用折合人民币
     *
     * @param conventionRegistration
     * @throws GetServiceException
     */
    private void setFreeCny(ConventionRegistration conventionRegistration) throws GetServiceException {
        //设置费用折合人民币
        if ("CNY".equals(conventionRegistration.getFkCurrencyTypeNum())) {
            conventionRegistration.setRegistrationFeeCny(conventionRegistration.getRegistrationFee());
        } else {
            //获取汇率
            //根据币种查汇率
//            BigDecimal exchangeRate = financeCenterClient.getLastExchangeRate(true, conventionRegistration.getFkCurrencyTypeNum(), "CNY");
            Result<BigDecimal> result = financeCenterClient.getLastExchangeRate(true, conventionRegistration.getFkCurrencyTypeNum(), "CNY");
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                BigDecimal exchangeRate = result.getData();
                if (GeneralTool.isEmpty(exchangeRate)) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("rate_null"));
                }
                BigDecimal registrationFeeCny = conventionRegistration.getRegistrationFee().multiply(exchangeRate);
                conventionRegistration.setRegistrationFeeCny(registrationFeeCny);
            }

        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd2(List<ConventionRegistrationDto> conventionRegistrationDtos) {
        if (GeneralTool.isEmpty(conventionRegistrationDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_is_not_empty"));
        }
        for (ConventionRegistrationDto conventionRegistrationDto : conventionRegistrationDtos) {
            ConventionRegistration conventionRegistration = BeanCopyUtils.objClone(conventionRegistrationDto, ConventionRegistration::new);
            //有id 表示修改 不执行新增 继续下一个
            if (GeneralTool.isNotEmpty(conventionRegistrationDto.getId())) {
                updateConventionRegistration(conventionRegistrationDto);
                continue;
            }
            Integer flag = conventionRegistrationMapper.validateAdd(conventionRegistration.getFkConventionId(), conventionRegistration.getBoothNum());
            if (flag > 0) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("booth_num_is_exist"));
            }
            conventionRegistration.setStatus(0);
            //设置费用折合人民币
            setFreeCny(conventionRegistration);
            utilService.updateUserInfoToEntity(conventionRegistration);
            conventionRegistrationMapper.insertSelective(conventionRegistration);
            //中间表对象
            ConventionRegistrationAreaCountry conventionRegistrationAreaCountry = new ConventionRegistrationAreaCountry();
            //fk报名id即刚刚插入成功的报名表返回的id
            conventionRegistrationAreaCountry.setFkConventionRegistrationId(conventionRegistration.getId());
            if (GeneralTool.isNotEmpty(conventionRegistrationDto.getCNums())) {
                List<String> cNums = conventionRegistrationDto.getCNums();
                for (String cNum : cNums) {
                    //保存到报名和国家的中间表中
                    conventionRegistrationAreaCountry.setFkAreaCountryKey(cNum);
                    utilService.updateUserInfoToEntity(conventionRegistrationAreaCountry);
                    conventionRegistrationAreaCountryMapper.insert(conventionRegistrationAreaCountry);
                }
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        if (conventionRegistrationMapper.selectById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        //判断该峰会报名能否删除
        deleteService.deleteValidateRegistration(id);
        int i = conventionRegistrationMapper.deleteById(id);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
        //同时删除中间表关联数据
//        Example example = new Example(ConventionRegistrationAreaCountry.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkConventionRegistrationId", id);
//        int i1 = conventionRegistrationAreaCountryMapper.deleteByExample(example);

        int i1 = conventionRegistrationAreaCountryMapper.delete(Wrappers.<ConventionRegistrationAreaCountry>lambdaQuery().eq(ConventionRegistrationAreaCountry::getFkConventionRegistrationId, id));
        if (i1 <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));

        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer confirm(String code) {
        if(GeneralTool.isEmpty(code)){
           return 0;
        }
        List<String> codes = Arrays.asList(code.split(","));
        if(GeneralTool.isNotEmpty(codes)){
            for (String receiptCode : codes) {
                LambdaQueryWrapper<ConventionRegistration> queryWrapper = new LambdaQueryWrapper();
                queryWrapper.eq(ConventionRegistration::getReceiptCode, receiptCode);
                ConventionRegistration result = conventionRegistrationMapper.selectOne(queryWrapper);
                if (result == null) {
                   return 0;
                }
                if(result.getIsVerified()==1){
                    return 1;
                }
                result.setIsVerified(1);
                utilService.updateUserInfoToEntity(result);
                conventionRegistrationMapper.updateById(result);
            }
        }
        return 2;
    }

    @Override
    public List<ConventionRegistrationVo> getConventionRegistrationsByReceiptCode(List<String> receiptCode) {
        List<ConventionRegistration> conventionRegistrations = conventionRegistrationMapper.selectList(Wrappers.<ConventionRegistration>lambdaQuery()
                .in(ConventionRegistration::getReceiptCode, receiptCode));
        List<ConventionRegistrationVo> convertDatas = new ArrayList<>();
        for (ConventionRegistration conventionRegistration : conventionRegistrations) {
            ConventionRegistrationVo conventionRegistrationVo = BeanCopyUtils.objClone(conventionRegistration, ConventionRegistrationVo::new);
            //通过关联表根据报名id查出国家keys
            List<String> cNums = conventionRegistrationAreaCountryMapper.getCountryKey(conventionRegistrationVo.getId());
            conventionRegistrationVo.setCNums(cNums);
            convertDatas.add(conventionRegistrationVo);
        }
        return convertDatas;
    }

    @Override
    public Integer isRegistered(Long fkInstitutionProviderId,String providerName) {
        LambdaQueryWrapper<ConventionRegistration> queryWrapper = new LambdaQueryWrapper();
        if(GeneralTool.isNotEmpty(fkInstitutionProviderId)||GeneralTool.isNotEmpty(providerName)){
            queryWrapper.eq(ConventionRegistration::getFkInstitutionProviderId, fkInstitutionProviderId)
                    .or().eq(ConventionRegistration::getProviderName, providerName);
            if (conventionRegistrationMapper.selectCount(queryWrapper) > 0) {
                return 1;
//                if(language.equals("zh")){
//                    throw new GetServiceException(LocaleMessageUtils.getMessage("zh","REGISTRATION_CONFIRMATION"));
//                }else {
//                    throw new GetServiceException(LocaleMessageUtils.getMessage("en","REGISTRATION_CONFIRMATION"));
//                }

            }
        }
        return 0;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ConventionRegistrationVo updateConventionRegistration(ConventionRegistrationDto conventionRegistrationDto) {
        if (conventionRegistrationDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        ConventionRegistration result = conventionRegistrationMapper.selectById(conventionRegistrationDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        //获取汇率
        //根据币种查汇率
        BigDecimal exchangeRate = null;
        Result<BigDecimal> exchangeRateResult = financeCenterClient.getLastExchangeRate(true, conventionRegistrationDto.getFkCurrencyTypeNum(), "CNY");
        if (exchangeRateResult.isSuccess() && GeneralTool.isNotEmpty(exchangeRateResult.getData())) {
            exchangeRate = exchangeRateResult.getData();
        }
        if (GeneralTool.isEmpty(exchangeRate)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("rate_null"));
        }
        BigDecimal registrationFeeCny = conventionRegistrationDto.getRegistrationFee().multiply(exchangeRate);
        conventionRegistrationDto.setRegistrationFeeCny(registrationFeeCny);
        ConventionRegistration conventionRegistration = BeanCopyUtils.objClone(conventionRegistrationDto, ConventionRegistration::new);
        utilService.updateUserInfoToEntity(conventionRegistration);
        conventionRegistrationMapper.updateById(conventionRegistration);

        //删除中间表内容
//        Example example = new Example(ConventionRegistrationAreaCountry.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkConventionRegistrationId", conventionRegistrationDto.getId());
//        conventionRegistrationAreaCountryMapper.deleteByExample(example);

        conventionRegistrationAreaCountryMapper.delete(Wrappers.<ConventionRegistrationAreaCountry>lambdaQuery().eq(ConventionRegistrationAreaCountry::getFkConventionRegistrationId, conventionRegistrationDto.getId()));

        //重新插入修改后的数据
        //中间表对象
        ConventionRegistrationAreaCountry conventionRegistrationAreaCountry = new ConventionRegistrationAreaCountry();
        //fk报名id即刚刚插入成功的报名表返回的id
        conventionRegistrationAreaCountry.setFkConventionRegistrationId(conventionRegistration.getId());

        if (GeneralTool.isNotEmpty(conventionRegistrationDto.getCNums())) {
            List<String> cNums = conventionRegistrationDto.getCNums();
            for (String cNum : cNums) {
                //保存到报名和国家的中间表中
                conventionRegistrationAreaCountry.setFkAreaCountryKey(cNum);
                utilService.updateUserInfoToEntity(conventionRegistrationAreaCountry);
                conventionRegistrationAreaCountryMapper.insert(conventionRegistrationAreaCountry);
            }
        }
        return findConventionRegistrationById(conventionRegistration.getId());
    }

    //重点测试，这个是查询到所有数据再根据数据重新组装分页过于复杂
    @Override
    public List<ConventionRegistrationVo> getConventionRegistrations(ConventionRegistrationDto conventionRegistrationDto, Page page) {
        LambdaQueryWrapper<ConventionRegistration> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ConventionRegistration::getFkConventionId, conventionRegistrationDto.getFkConventionId());
        List<Long> idList = new ArrayList<>();
        if (GeneralTool.isNotEmpty(conventionRegistrationDto)) {
            //校验国家id是否为空
            if (GeneralTool.isNotEmpty(conventionRegistrationDto.getFkAreaCountryId())) {
                //根据传入的国家id查找key
                String countryKey = "";
                Result<String> result = institutionCenterClient.getCountryKeyById(conventionRegistrationDto.getFkAreaCountryId());
                if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                    countryKey = result.getData();
                }
                if (GeneralTool.isEmpty(countryKey)) {
                    return Collections.emptyList();//国家找不到，返回空列表
                }
                //根据国家key，查找到该国家的峰会id集合
                LambdaQueryWrapper<ConventionRegistrationAreaCountry> conventionRegistrationAreaCountryLambdaQueryWrapper = new LambdaQueryWrapper<>();
                conventionRegistrationAreaCountryLambdaQueryWrapper.eq(ConventionRegistrationAreaCountry::getFkAreaCountryKey, countryKey);
                List<ConventionRegistrationAreaCountry> conventionRegistrationAreaCountries = conventionRegistrationAreaCountryMapper.selectList(conventionRegistrationAreaCountryLambdaQueryWrapper);
                if (GeneralTool.isEmpty(conventionRegistrationAreaCountries)) {
                    return Collections.emptyList();//找不到，返回空列表
                }
                idList.addAll(conventionRegistrationAreaCountries.stream().map(ConventionRegistrationAreaCountry::getFkConventionRegistrationId).distinct().collect(Collectors.toList()));
            }
            //查询条件-学校提供商名称
            if (GeneralTool.isNotEmpty(conventionRegistrationDto.getInstitutionProviderName())) {
                //根据学校提供商名称模糊查找所有对应的id
                List<Long> fkInstitutionProviderIds = new ArrayList<>();
                Result<List<Long>> result = institutionCenterClient.getInstitutionProviderIdsByName(conventionRegistrationDto.getInstitutionProviderName());
                if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                    fkInstitutionProviderIds = result.getData();
                }
                //设置条件查询出属于这些id的报名数据
                if (GeneralTool.isNotEmpty(fkInstitutionProviderIds)) {
                    lambdaQueryWrapper.in(ConventionRegistration::getFkInstitutionProviderId, fkInstitutionProviderIds);
                }
            }
            //查询条件-联系人名称
            if (GeneralTool.isNotEmpty(conventionRegistrationDto.getContactPersonName())) {
                lambdaQueryWrapper.and(wrapper -> wrapper.like(ConventionRegistration::getContactPersonName, conventionRegistrationDto.getContactPersonName())
                        .or().like(ConventionRegistration::getContactPersonNameChn, conventionRegistrationDto.getContactPersonName()));
//                criteria1.andLike("contactPersonName", "%" + conventionRegistrationDto.getContactPersonName() + "%");
//                criteria1.orLike("contactPersonNameChn", "%" + conventionRegistrationDto.getContactPersonName() + "%");

            }
            //查询条件-展会名称/展会编号
            if (GeneralTool.isNotEmpty(conventionRegistrationDto.getBoothName())) {
                lambdaQueryWrapper.and(conventionRegistrationLambdaQueryWrapper ->
                        conventionRegistrationLambdaQueryWrapper.like(ConventionRegistration::getBoothName, conventionRegistrationDto.getBoothName())
                                .or().like(ConventionRegistration::getBoothNum, conventionRegistrationDto.getBoothName())
                        );
            }
            //展会编号
            if (GeneralTool.isNotEmpty(conventionRegistrationDto.getBoothNum())) {
                lambdaQueryWrapper.like(ConventionRegistration::getBoothNum, conventionRegistrationDto.getBoothNum());
            }
            //回执码
            if (GeneralTool.isNotEmpty(conventionRegistrationDto.getReceiptCode())) {
                lambdaQueryWrapper.like(ConventionRegistration::getReceiptCode, conventionRegistrationDto.getReceiptCode());

            }
            //状态
            if (GeneralTool.isNotEmpty(conventionRegistrationDto.getStatus())) {
                lambdaQueryWrapper.eq(ConventionRegistration::getStatus, conventionRegistrationDto.getStatus());

            }
            //提供商名称
            if (GeneralTool.isNotEmpty(conventionRegistrationDto.getProviderName())) {
                lambdaQueryWrapper.like(ConventionRegistration::getProviderName, conventionRegistrationDto.getProviderName());
            }

            //邮箱验证
            if (GeneralTool.isNotEmpty(conventionRegistrationDto.getIsVerified())) {
                lambdaQueryWrapper.eq(ConventionRegistration::getIsVerified, conventionRegistrationDto.getIsVerified());
            }

            //是否有参会人
            if (GeneralTool.isNotEmpty(conventionRegistrationDto.getHasConventionPerson())) {
                List<Long> conventionRegistrationIds = conventionPersonRegistrationMapper.getConventionRegistrationIds(conventionRegistrationDto.getFkConventionId());
                if (GeneralTool.isEmpty(conventionRegistrationIds)) {
                    conventionRegistrationIds.add(0L);
                }
                if (conventionRegistrationDto.getHasConventionPerson()) {
                    //如有国家选项，这里只允许从筛选的国家列表中获取
                    if (GeneralTool.isNotEmpty(idList) && GeneralTool.isNotEmpty(conventionRegistrationIds)) {
                        for (Long id : conventionRegistrationIds) {
                            //如国家列表中不存在，则移除
                            if (!idList.contains(id)) {
                                conventionRegistrationIds.remove(id);
                            }
                        }
                    }
                    idList.clear();
                    idList.addAll(conventionRegistrationIds);//将花名册ids写入主键id条件。
                } else {
                    lambdaQueryWrapper.notIn(ConventionRegistration::getId, conventionRegistrationIds);
                }
            }
            if (GeneralTool.isNotEmpty(idList)) {
                lambdaQueryWrapper.in(ConventionRegistration::getId, idList.stream().distinct().collect(Collectors.toList()));

            }
        }

        if (GeneralTool.isEmpty(conventionRegistrationDto.getOrderCondition())){
            lambdaQueryWrapper.orderByDesc(ConventionRegistration::getGmtCreate);
        }else {
            lambdaQueryWrapper.last(conventionRegistrationDto.getOrderCondition());
        }

        IPage<ConventionRegistration> pages = conventionRegistrationMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
        //查询到所有列表
        List<ConventionRegistration> conventionRegistrations = pages.getRecords();
        page.setAll((int) pages.getTotal());

        List<ConventionRegistrationVo> convertDatas = new ArrayList<>();
        //学校提供商ids
        Set<Long> fkInstitutionProviderIds = conventionRegistrations.stream().map(ConventionRegistration::getFkInstitutionProviderId).collect(Collectors.toSet());
        //根据学校提供商ids获取名称map
        Map<Long, String> institutionProviderNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkInstitutionProviderIds)) {
            Result<Map<Long, String>> institutionProviderNameResult = institutionCenterClient.getInstitutionProviderNamesByIds(fkInstitutionProviderIds);
            if (institutionProviderNameResult.isSuccess() && GeneralTool.isNotEmpty(institutionProviderNameResult.getData())) {
                institutionProviderNamesByIds = institutionProviderNameResult.getData();
            }
        }

        //报名ids
        Set<Long> ids = conventionRegistrations.stream().map(ConventionRegistration::getId).collect(Collectors.toSet());
        //通过关联表根据报名ids查出国家keys
        Map<Long, List<String>> countryKeyByIds = getCountryKeyByIds(ids);

        //币种编号nums
        Set<String> fkCurrencyTypeNums = conventionRegistrations.stream().map(ConventionRegistration::getFkCurrencyTypeNum).collect(Collectors.toSet());
        //根据币种编号nums获取名称map
        Result<Map<String, String>> result = financeCenterClient.getCurrencyNamesByNums(fkCurrencyTypeNums);
        if (result.isSuccess() && result.getData() != null) {
            Map<String, String> currencyNamesByNums = result.getData();
            for (ConventionRegistration conventionRegistration : conventionRegistrations) {
                ConventionRegistrationVo conventionRegistrationVo = BeanCopyUtils.objClone(conventionRegistration, ConventionRegistrationVo::new);
                setResultList(conventionRegistrationVo, institutionProviderNamesByIds, countryKeyByIds, currencyNamesByNums);
                convertDatas.add(conventionRegistrationVo);
            }
        }

        if (GeneralTool.isEmpty(convertDatas)){
            return Collections.emptyList();
        }
        Map<Long, EventCost> eventCostMap = Maps.newHashMap();
        List<Long> eventCostIds = convertDatas.stream().map(ConventionRegistrationVo::getFkEventCostId).filter(Objects::nonNull).collect(Collectors.toList());
        Set<String> currencyTypeNums = Sets.newHashSet();
        if (GeneralTool.isNotEmpty(eventCostIds)){
            List<EventCost> eventCosts = eventCostMapper.selectBatchIds(eventCostIds);
            if (GeneralTool.isNotEmpty(eventCosts)) {
                eventCostMap = eventCosts.stream().collect(Collectors.toMap(EventCost::getId, Function.identity()));
                currencyTypeNums = eventCosts.stream().map(EventCost::getFkCurrencyTypeNum).filter(Objects::nonNull).collect(Collectors.toSet());
            }
        }
        Map<String, String> numNameMap = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(currencyTypeNums)){
            numNameMap = financeCenterClient.getCurrencyNamesByNums(currencyTypeNums).getData();
        }

        for (ConventionRegistrationVo convertData : convertDatas) {
            EventCost eventCost = eventCostMap.get(convertData.getFkEventCostId());
            if (GeneralTool.isNotEmpty(eventCost)){
                convertData.setEventCostAmount(eventCost.getAmount());
                convertData.setEventCostAmountCurrencyNum(eventCost.getFkCurrencyTypeNum());
                convertData.setEventCostAmountCurrencyNumName(numNameMap.get(eventCost.getFkCurrencyTypeNum()));
            }
        }
        return convertDatas;
    }

//    /**
//     * 备份原来的方法
//     * @param conventionRegistrationVo
//     * @param page
//     * @return
//     */
//    @Override
//    public List<ConventionRegistrationVo> getConventionRegistrations(ConventionRegistrationDto conventionRegistrationVo, Page page)  {
//        Example example = new Example(ConventionRegistration.class);
//        Example.Criteria criteria = example.createCriteria();
//        Example.Criteria criteria1 = example.createCriteria();
//        criteria.andEqualTo("fkConventionId", conventionRegistrationVo.getFkConventionId());
//        if (GeneralTool.isNotEmpty(conventionRegistrationVo)) {
//            //查询条件-学校提供商名称
//            if (GeneralTool.isNotEmpty(conventionRegistrationVo.getInstitutionProviderName())) {
//                //根据学校提供商名称模糊查找所有对应的id
//                List<Long> fkInstitutionProviderIds = feignInstitutionService.getInstitutionProviderIds(conventionRegistrationVo.getInstitutionProviderName());
//                //设置条件查询出属于这些id的报名数据
//                if (GeneralTool.isNotEmpty(fkInstitutionProviderIds)) {
//                    criteria.andIn("fkInstitutionProviderId", fkInstitutionProviderIds);
//                }
//            }
//            //查询条件-联系人名称
//            if (GeneralTool.isNotEmpty(conventionRegistrationVo.getContactPersonName())) {
//                criteria1.andLike("contactPersonName", "%" + conventionRegistrationVo.getContactPersonName() + "%");
//                criteria1.orLike("contactPersonNameChn", "%" + conventionRegistrationVo.getContactPersonName() + "%");
//
//            }
//            //查询条件-展会名称
//            if (GeneralTool.isNotEmpty(conventionRegistrationVo.getBoothName())) {
//                criteria.andLike("boothName", "%" + conventionRegistrationVo.getBoothName() + "%");
//            }
//            //展会编号
//            if (GeneralTool.isNotEmpty(conventionRegistrationVo.getBoothNum())) {
//                criteria.andLike("boothNum", "%" + conventionRegistrationVo.getBoothNum() + "%");
//            }
//            //回执码
//            if (GeneralTool.isNotEmpty(conventionRegistrationVo.getReceiptCode())) {
//                criteria.andLike("receiptCode", "%" + conventionRegistrationVo.getReceiptCode() + "%");
//            }
//            //状态
//            if (GeneralTool.isNotEmpty(conventionRegistrationVo.getStatus())) {
//                criteria.andEqualTo("status", conventionRegistrationVo.getStatus());
//            }
//            //提供商名称
//            if (GeneralTool.isNotEmpty(conventionRegistrationVo.getProviderName())) {
//                criteria.andLike("providerName", "%" + conventionRegistrationVo.getProviderName() + "%");
//            }
//            //是否有参会人
//            if(GeneralTool.isNotEmpty(conventionRegistrationVo.getHasConventionPerson())){
//                List<Long> conventionRegistrationIds = conventionPersonRegistrationMapper.getConventionRegistrationIds(conventionRegistrationVo.getFkConventionId());
//                if (conventionRegistrationVo.getHasConventionPerson()){
//                    criteria.andIn("id",conventionRegistrationIds);
//                }else {
//                    criteria.andNotIn("id",conventionRegistrationIds);
//                }
//            }
//            example.and(criteria1);
//        }
//        example.orderBy("gmtCreate").desc();
//        //选择国家过滤
//        if (GeneralTool.isEmpty(conventionRegistrationVo.getFkAreaCountryId())) {
//            PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        }
//        //查询到所有列表
//        List<ConventionRegistration> conventionRegistrations = conventionRegistrationMapper.selectByExample(example);
//        //按国家名称过滤
//        if (GeneralTool.isNotEmpty(conventionRegistrationVo.getFkAreaCountryId())) {
//            List<ConventionRegistration> conventionRegistrationList = new ArrayList<>();
//            //峰会id-》表的id-》关联表国家key
//            for (ConventionRegistration conventionRegistration : conventionRegistrations) {
//                List<String> cNums = conventionRegistrationAreaCountryMapper.getCountryKey(conventionRegistration.getId());
//                ResponseBo<List<Long>> countryIdByKey = feignInstitutionService.getCountryIdByKey(cNums);
//                List<Long> countryIds = countryIdByKey.getData();
//                //通过关联表根据报名id查出国家keys
//                //feign调用 根据国家key查找国家名称
//                for (Long countryId : countryIds) {
//                    if (conventionRegistrationVo.getFkAreaCountryId().equals(countryId)) {
//                        conventionRegistrationList.add(conventionRegistration);
//                    }
//                    break;
//                }
//            }
//            int first = (page.getCurrentPage() - 1) * page.getShowCount();
//            int last = page.getCurrentPage() * page.getShowCount();
//            conventionRegistrations = conventionRegistrationList.subList(Math.max(0, first), Math.min(conventionRegistrationList.size(), last));
//            Integer currentPage = page.getCurrentPage();
//            Integer totalPage = (int) Math.ceil((double) conventionRegistrationList.size() / page.getShowCount());
//            page.restPage(conventionRegistrationList);
//            page.setTotalPage(totalPage);
//            if (currentPage > totalPage) {
//                page.setCurrentPage(totalPage);
//            }
//            page.setCurrentPage(currentPage);
//            page.setCurrentResult((currentPage - 1) * page.getShowCount());
//        }
//        if (GeneralTool.isEmpty(conventionRegistrationVo.getFkAreaCountryId())) {
//            page.restPage(conventionRegistrations);
//        }
//
//        List<ConventionRegistrationVo> convertDatas = new ArrayList<>();
//
//        //学校提供商ids
//        Set<Long> fkInstitutionProviderIds = conventionRegistrations.stream().map(ConventionRegistration::getFkInstitutionProviderId).collect(Collectors.toSet());
//        //根据学校提供商ids获取名称map
//        Map<Long, String> institutionProviderNamesByIds = new HashMap<>();
//        if(GeneralTool.isNotEmpty(fkInstitutionProviderIds)){
//            institutionProviderNamesByIds = feignInstitutionService.getInstitutionProviderNamesByIds(fkInstitutionProviderIds);
//        }
//
//        //报名ids
//        Set<Long> ids = conventionRegistrations.stream().map(ConventionRegistration::getId).collect(Collectors.toSet());
//        //通过关联表根据报名ids查出国家keys
//        Map<Long, List<String>> countryKeyByIds = getCountryKeyByIds(ids);
//
//        //币种编号nums
//        Set<String> fkCurrencyTypeNums = conventionRegistrations.stream().map(ConventionRegistration::getFkCurrencyTypeNum).collect(Collectors.toSet());
//        //根据币种编号nums获取名称map
//        Map<String, String> currencyNamesByNums = financeCenterClient.getCurrencyNamesByNums(fkCurrencyTypeNums);
//
//        for (ConventionRegistration conventionRegistration : conventionRegistrations) {
//            ConventionRegistrationVo conventionRegistrationDto = BeanCopyUtils.objClone(conventionRegistration, ConventionRegistrationVo.class);
//            setResultList(conventionRegistrationDto,institutionProviderNamesByIds,countryKeyByIds,currencyNamesByNums);
//            convertDatas.add(conventionRegistrationDto);
//        }
//        return convertDatas;
//    }

    /**
     * 统一结果(批量处理,减少多次调用接口)
     *
     * @param conventionRegistrationVo
     */
    private void setResultList(ConventionRegistrationVo conventionRegistrationVo, Map<Long, String> institutionProviderNamesByIds,
                               Map<Long, List<String>> countryKeyByIds, Map<String, String> currencyNamesByNums) {
        String type = null;
        String institutionProviderName = null;
        //feign调用 根据学校提供商的id 查找对应学校提供商名称和类别
        if (GeneralTool.isNotEmpty(conventionRegistrationVo.getFkInstitutionProviderId())) {
            Result<String> result = institutionCenterClient.getInstitutionProviderType(conventionRegistrationVo.getFkInstitutionProviderId());
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                type = result.getData();
            }
            institutionProviderName = institutionProviderNamesByIds.get(conventionRegistrationVo.getFkInstitutionProviderId());
        }
        //通过关联表根据报名id查出国家keys
        List<String> cNums = countryKeyByIds.get(conventionRegistrationVo.getId());
        //feign调用 根据国家key查找国家名称
        List<String> countrys = new ArrayList<>();
        if (GeneralTool.isNotEmpty(cNums)) {
            for (String countryKey : cNums) {
//            String country = institutionCenterClient.getCountryName(countryKey);
                Result<String> result = institutionCenterClient.getCountryName(countryKey);
                if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                    countrys.add(result.getData());
                }

            }
        }

        if (GeneralTool.isNotEmpty(conventionRegistrationVo.getStatus())) {
            String statusName = ProjectExtraEnum.getInitialValueByKey(conventionRegistrationVo.getStatus(), ProjectExtraEnum.CONVENTION_STATUS);
            conventionRegistrationVo.setStatusName(statusName);
        }

        //feign调用 根据币种编号查找币种名称
        String currencyName = currencyNamesByNums.get(conventionRegistrationVo.getFkCurrencyTypeNum());

        //统一结果到ConventionRegistrationDto中 返回给前端
        conventionRegistrationVo.setCountry(countrys);
        conventionRegistrationVo.setType(type);
        conventionRegistrationVo.setInstitutionProviderName(institutionProviderName);
        conventionRegistrationVo.setCNums(cNums);
        conventionRegistrationVo.setCurrencyName(currencyName);

    }

    /**
     * 统一结果
     *
     * @param conventionRegistrationVo
     */
    private void setResult(ConventionRegistrationVo conventionRegistrationVo) {
        String type = null;
        String institutionProviderName = null;
        //feign调用 根据学校提供商的id 查找对应学校提供商名称和类别
        if (GeneralTool.isNotEmpty(conventionRegistrationVo.getFkInstitutionProviderId())) {
            Result<String> result = institutionCenterClient.getInstitutionProviderType(conventionRegistrationVo.getFkInstitutionProviderId());
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                type = result.getData();
            }
//            institutionProviderName = institutionCenterClient.getInstitutionProviderName(conventionRegistrationVo.getFkInstitutionProviderId());
            Result<String> institutionProviderNameResult = institutionCenterClient.getInstitutionProviderName(conventionRegistrationVo.getFkInstitutionProviderId());
            if (institutionProviderNameResult.isSuccess() && GeneralTool.isNotEmpty(institutionProviderNameResult.getData())) {
                institutionProviderName = institutionProviderNameResult.getData();
            }
        }
        //通过关联表根据报名id查出国家keys
        List<String> cNums = conventionRegistrationAreaCountryMapper.getCountryKey(conventionRegistrationVo.getId());
        //feign调用 根据国家key查找国家名称
        List<String> countrys = new ArrayList<>();
        for (String countryKey : cNums) {
//            String country = institutionCenterClient.getCountryName(countryKey);
//            countrys.add(country);
            Result<String> result = institutionCenterClient.getCountryName(countryKey);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                countrys.add(result.getData());
            }
        }
        //feign调用 根据币种编号查找币种名称
        Result<String> result = financeCenterClient.getCurrencyNameByNum(conventionRegistrationVo.getFkCurrencyTypeNum());
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            String currencyName = result.getData();
            conventionRegistrationVo.setCurrencyName(currencyName);
        }
        //统一结果到ConventionRegistrationDto中 返回给前端
        conventionRegistrationVo.setCountry(countrys);
        conventionRegistrationVo.setType(type);
        conventionRegistrationVo.setInstitutionProviderName(institutionProviderName);
        conventionRegistrationVo.setCNums(cNums);


    }


    @Override
    public List<ConventionRegistrationVo> getConventionRegistrationList(Long conventionId) {
        List<ConventionRegistrationVo> convertDatas = new ArrayList<>();
        List<ConventionRegistrationVo> conventionRegistrationVoList = conventionRegistrationMapper.getConventionRegistrationDtoList(conventionId);
        for (ConventionRegistrationVo conventionRegistrationVo : conventionRegistrationVoList) {
            //通过关联表根据报名id查出国家keys
            List<String> cNums = conventionRegistrationAreaCountryMapper.getCountryKey(conventionRegistrationVo.getId());
            //feign调用 根据国家key查找国家名称
            List<String> countrys = new ArrayList<>();
            for (String countryKey : cNums) {
//                String country = feignInstitutionService.getCountryName(countryKey);
//                countrys.add(country);
                Result<String> result = institutionCenterClient.getCountryName(countryKey);
                if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                    countrys.add(result.getData());
                }
            }
            countrys.removeIf(Objects::isNull);
            conventionRegistrationVo.setCountry(countrys);
            convertDatas.add(conventionRegistrationVo);
        }
        return convertDatas;
    }

    @Override
    public List<Long> getRegistrationIdsByName(String boothName) {
        List<Long> registrationIds = conventionRegistrationMapper.getRegistrationIdsByName(boothName);
        if (GeneralTool.isEmpty(registrationIds)) {
            registrationIds.add(0L);
        }
        return registrationIds;
    }

    @Override
    public void updateStatus(Long id, Integer status) {
        conventionRegistrationMapper.updateStatus(id, status);
    }

    @Override
    public List<ConventionRegistrationVo> getConventionRegistrationDto(Long conventionId, String receiptCode) {
//        Example example = new Example(ConventionRegistration.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkConventionId", conventionId);
//        criteria.andEqualTo("receiptCode", receiptCode);
//        List<ConventionRegistration> conventionRegistrations = conventionRegistrationMapper.selectByExample(example);
        List<ConventionRegistration> conventionRegistrations = conventionRegistrationMapper.selectList(Wrappers.<ConventionRegistration>lambdaQuery()
                .eq(ConventionRegistration::getFkConventionId, conventionId)
                .eq(ConventionRegistration::getReceiptCode, receiptCode));
        List<ConventionRegistrationVo> convertDatas = new ArrayList<>();
        for (ConventionRegistration conventionRegistration : conventionRegistrations) {
            ConventionRegistrationVo conventionRegistrationVo = BeanCopyUtils.objClone(conventionRegistration, ConventionRegistrationVo::new);
            //通过关联表根据报名id查出国家keys
            List<String> cNums = conventionRegistrationAreaCountryMapper.getCountryKey(conventionRegistrationVo.getId());
            conventionRegistrationVo.setCNums(cNums);
            convertDatas.add(conventionRegistrationVo);
        }
        return convertDatas;
    }

    @Override
    public void deleteByReceiptCode(Long conventionId, String receiptCode) {
//        Example example = new Example(ConventionRegistration.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkConventionId", conventionId);
//        criteria.andEqualTo("receiptCode", receiptCode);
//        List<ConventionRegistration> conventionRegistrations = conventionRegistrationMapper.selectByExample(example);

        List<ConventionRegistration> conventionRegistrations = conventionRegistrationMapper.selectList(Wrappers.<ConventionRegistration>lambdaQuery()
                .eq(ConventionRegistration::getFkConventionId, conventionId)
                .eq(ConventionRegistration::getReceiptCode, receiptCode));
        for (ConventionRegistration conventionRegistration : conventionRegistrations) {
            delete(conventionRegistration.getId());
        }
    }

    @Override
    public List<String> getBoothIndex(Long conventionId) {
//        Example example = new Example(ConventionRegistration.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkConventionId", conventionId);
//        List<ConventionRegistration> conventionRegistrations = conventionRegistrationMapper.selectByExample(example);

        List<ConventionRegistration> conventionRegistrations = conventionRegistrationMapper.selectList(Wrappers.<ConventionRegistration>lambdaQuery()
                .eq(ConventionRegistration::getFkConventionId, conventionId));
        List<String> list = conventionRegistrations.stream().map(ConventionRegistration::getBoothNum).collect(Collectors.toList());
        list.removeIf(Objects::isNull);
        return list;
    }

    @Override
    public Boolean haveSit(Long conventionId, String boothNum) {
//        Example example = new Example(ConventionRegistration.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkConventionId", conventionId);
//        criteria.andEqualTo("boothNum", boothNum);
//        List<ConventionRegistration> conventionRegistrations = conventionRegistrationMapper.selectByExample(example);

        List<ConventionRegistration> conventionRegistrations = conventionRegistrationMapper.selectList(Wrappers.<ConventionRegistration>lambdaQuery()
                .eq(ConventionRegistration::getFkConventionId, conventionId)
                .eq(ConventionRegistration::getBoothNum, boothNum));
        //如果这个展位号对象已经存在，说明已经被选过，返回true 表示已坐
        if (GeneralTool.isNotEmpty(conventionRegistrations)) {
            return true;
        }
        return false;
    }

    @Override
    public Boolean validateReceiptCode(String receiptCode) {
//        Example example = new Example(ConventionRegistration.class);
//        example.createCriteria().andEqualTo("receiptCode", receiptCode);
//        List<ConventionRegistration> conventionRegistrations = conventionRegistrationMapper.selectByExample(example);
        List<ConventionRegistration> conventionRegistrations = conventionRegistrationMapper.selectList(Wrappers.<ConventionRegistration>lambdaQuery().eq(ConventionRegistration::getReceiptCode, receiptCode));
        //能查到说明数据库已有这个回执码
        if (GeneralTool.isEmpty(conventionRegistrations)) {
            return false;
        }
        return true;
    }

    @Override
    public List<RegistrationSponsorVo> getConventionRegistrationGroup(ConventionRegistrationDto conventionRegistrationDto, Page page) {
        //查询条件学校提供商
        if (GeneralTool.isNotEmpty(conventionRegistrationDto.getInstitutionProviderName())) {
            List<Long> fkInstitutionProviderIds = new ArrayList<>();
            Result<List<Long>> result = institutionCenterClient.getInstitutionProviderIdsByName(conventionRegistrationDto.getInstitutionProviderName());
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                fkInstitutionProviderIds = result.getData();
            }
            conventionRegistrationDto.setInstitutionProviderIds(fkInstitutionProviderIds);
        }
        //获取回执码非null的报名信息列表
        List<ConventionRegistrationVo> conventionRegistrationVos = conventionRegistrationMapper.getReceiptCodes(conventionRegistrationDto);
        List<RegistrationSponsorVo> registrationSponsorVoList = new ArrayList<>();
        //获取展位数据
        for (ConventionRegistrationVo conventionRegistrationVo : conventionRegistrationVos) {
            RegistrationSponsorVo registrationSponsorVo = new RegistrationSponsorVo();
            registrationSponsorVo.setReceiptCode(conventionRegistrationVo.getReceiptCode());
            registrationSponsorVo.setProviderName(conventionRegistrationVo.getProviderName());
            List<BoothVo> boothVos = new ArrayList<>();
            List<SponsorVo> sponsorVos = new ArrayList<>();
            List<ConventionSponsorFeeVo> conventionSponsorFeeVos = new ArrayList<>();

            //获取全部数据
            List<ConventionRegistrationVo> boothStatus0 = new ArrayList<>();
            List<ConventionRegistrationVo> boothStatus1 = new ArrayList<>();
            List<ConventionRegistrationVo> boothStatus2 = new ArrayList<>();
            List<ConventionRegistrationVo> boothStatus3 = new ArrayList<>();
            List<ConventionRegistrationVo> boothStatus4 = new ArrayList<>();
            List<ConventionRegistrationVo> booths = conventionRegistrationMapper.getBoothListByReceiptCode(conventionRegistrationVo.getReceiptCode(), conventionRegistrationDto.getFkConventionId());
            for (ConventionRegistrationVo booth : booths) {
                if (GeneralTool.isNotEmpty(booth.getStatus())) {
                    if (booth.getStatus() == 0) {
                        boothStatus0.add(booth);
                    } else if (booth.getStatus() == 1) {
                        boothStatus1.add(booth);
                    } else if (booth.getStatus() == 2) {
                        boothStatus2.add(booth);
                    } else if (booth.getStatus() == 3) {
                        boothStatus3.add(booth);
                    } else if (booth.getStatus() == 4) {
                        boothStatus4.add(booth);
                    }
                }
            }
            //状态为待发invoice的数据的展位信息数据
            getBoothDtoByStatus(conventionRegistrationDto, conventionRegistrationVo, boothVos, 0, boothStatus0);
            //状态为已发invoice的展位信息数据
            getBoothDtoByStatus(conventionRegistrationDto, conventionRegistrationVo, boothVos, 1, boothStatus1);
            //状态为已收款的展位信息数据
            getBoothDtoByStatus(conventionRegistrationDto, conventionRegistrationVo, boothVos, 2, boothStatus2);
            //状态为已完成的展位信息数据
            getBoothDtoByStatus(conventionRegistrationDto, conventionRegistrationVo, boothVos, 3, boothStatus3);
            //状态为已完成的展位信息数据
            getBoothDtoByStatus(conventionRegistrationDto, conventionRegistrationVo, boothVos, 4, boothStatus4);
            //获取全部数据
            List<ConventionSponsorVo> sponsorsStatus0 = new ArrayList<>();
            List<ConventionSponsorVo> sponsorsStatus1 = new ArrayList<>();
            List<ConventionSponsorVo> sponsorsStatus2 = new ArrayList<>();
            List<ConventionSponsorVo> sponsorsStatus3 = new ArrayList<>();
            List<ConventionSponsorVo> sponsors = conventionSponsorMapper.getSponsorListByReceiptCode(conventionRegistrationVo.getReceiptCode(), conventionRegistrationDto.getFkConventionId());
            for (ConventionSponsorVo sponsor : sponsors) {
                if (GeneralTool.isNotEmpty(sponsor.getStatus())) {
                    if (sponsor.getStatus() == 0) {
                        sponsorsStatus0.add(sponsor);
                    } else if (sponsor.getStatus() == 1) {
                        sponsorsStatus1.add(sponsor);
                    } else if (sponsor.getStatus() == 2) {
                        sponsorsStatus2.add(sponsor);
                    } else if (sponsor.getStatus() == 3) {
                        sponsorsStatus3.add(sponsor);
                    }
                }
            }
            //赞助项目列表 根据回执码 供应商查
            getConventionSponsorFee(conventionRegistrationDto, conventionRegistrationVo, sponsorVos, 0, sponsorsStatus0);
            getConventionSponsorFee(conventionRegistrationDto, conventionRegistrationVo, sponsorVos, 1, sponsorsStatus1);
            getConventionSponsorFee(conventionRegistrationDto, conventionRegistrationVo, sponsorVos, 2, sponsorsStatus2);
            getConventionSponsorFee(conventionRegistrationDto, conventionRegistrationVo, sponsorVos, 3, sponsorsStatus3);
            //查询新建状态的赞助项目
            //getConventionSponsorFee(conventionRegistrationDto, conventionRegistrationVo, sponsorVos, 0);
            //查询已确认状态的赞助项目
            //getConventionSponsorFee(conventionRegistrationDto, conventionRegistrationVo, sponsorVos, 1);
            //查询已付款状态的赞助项目
            //getConventionSponsorFee(conventionRegistrationDto, conventionRegistrationVo, sponsorVos, 2);
            //放入展位数据列表
            registrationSponsorVo.setBoothDtos(boothVos);
            //放入赞助商数据列表
            registrationSponsorVo.setSponsorDtos(sponsorVos);
            //放入最终结构的列表
            registrationSponsorVoList.add(registrationSponsorVo);
        }

        return registrationSponsorVoList;
    }

    @Override
    public Boolean providerNameVerify(Long conventionId, String providerName) {
        if (GeneralTool.isNotEmpty(conventionRegistrationMapper.providerNameVerify(conventionId, providerName.trim()))) {
            return true;
        }
        return false;
    }

    @Override
    public List<BoothVo> getSumRegistrationFreeByReceiptCode(String receiptCode) {
        List<BoothVo> boothDtos = new ArrayList<>();
        List<ConventionRegistration> conventionRegistrations = conventionRegistrationMapper.getSumRegistrationFreeListByReceiptCode(receiptCode);
        if (GeneralTool.isNotEmpty(conventionRegistrations)) {
            Set<String> currencyTypeNums = conventionRegistrations.stream()
                    .filter(Objects::nonNull)
                    .map(ConventionRegistration::getFkCurrencyTypeNum)
                    .collect(Collectors.toSet());
            Map<String, String> currencyNameMap = Maps.newHashMap();
            if (GeneralTool.isNotEmpty(currencyTypeNums)) {
                Result<Map<String, String>> result = financeCenterClient.getCurrencyNamesByNums(currencyTypeNums);
                if (result.isSuccess() && result.getData() != null) {
                    currencyNameMap = result.getData();
                }
            }
            Map<Integer, List<ConventionRegistration>> map = conventionRegistrations.stream()
                    .filter(conventionRegistration -> conventionRegistration.getStatus() != null)
                    .collect(Collectors.groupingBy(ConventionRegistration::getStatus));
            List<Integer> statusList = Arrays.stream(ProjectExtraEnum.CONVENTION_STATUS).map(item -> item.key).collect(Collectors.toList());
            for (Integer status : statusList) {
                BoothVo boothDto = new BoothVo();
                List<ConventionRegistration> conventionRegistrationsByStatus = map.getOrDefault(status, Lists.newArrayList());
                Set<String> curStatusCurrencyTypeNums = conventionRegistrationsByStatus.stream()
                        .filter(Objects::nonNull)
                        .map(ConventionRegistration::getFkCurrencyTypeNum)
                        .collect(Collectors.toSet());
                if (curStatusCurrencyTypeNums.size() > 1) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("currency_is_not_unique"));
                }
                if (GeneralTool.isNotEmpty(curStatusCurrencyTypeNums)) {
                    boothDto.setFkCurrencyTypeNum(curStatusCurrencyTypeNums.iterator().next());
                    boothDto.setCurrencyName(currencyNameMap.get(curStatusCurrencyTypeNums.iterator().next()));
                }
                BigDecimal sumRegistrationFree = conventionRegistrationsByStatus.stream()
                        .map(c -> Optional.ofNullable(c.getRegistrationFee()).orElse(BigDecimal.ZERO))
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                        .setScale(2, RoundingMode.HALF_UP);
                boothDto.setStatus(status);
                boothDto.setStatusName(ProjectExtraEnum.getInitialValueByKey(status, ProjectExtraEnum.CONVENTION_STATUS));
                boothDto.setSumRegistrationFee(sumRegistrationFree);
                boothDtos.add(boothDto);
            }
        }
        return boothDtos;
    }

    @Override
    public void exportConventionRegistrationExcel(HttpServletResponse response, ConventionRegistrationDto conventionRegistrationDto) {
        LambdaQueryWrapper lambdaQueryWrapper = getLambdaQueryWrapper(conventionRegistrationDto);
        List<ConventionRegistration> conventionRegistrations = conventionRegistrationMapper.selectList(lambdaQueryWrapper);
        List<ConventionRegistrationExportVo> conventionRegistrationExportVos = new ArrayList<>();
        //设置国家过滤
        if (GeneralTool.isNotEmpty(conventionRegistrationDto.getFkAreaCountryId())) {
            List<ConventionRegistration> conventionRegistrationList = new ArrayList<>();
            //峰会id-》表的id-》关联表国家key
            for (ConventionRegistration conventionRegistration : conventionRegistrations) {
                List<String> cNums = conventionRegistrationAreaCountryMapper.getCountryKey(conventionRegistration.getId());
                Result<List<Long>> countryIdByKeyResult = institutionCenterClient.getCountryIdByKey(cNums);
                if (countryIdByKeyResult.isSuccess() && GeneralTool.isNotEmpty(countryIdByKeyResult.getData())) {
                    List<Long> countryIds = countryIdByKeyResult.getData();
                    //通过关联表根据报名id查出国家keys
                    //feign调用 根据国家key查找国家名称
                    if (countryIds.contains(conventionRegistrationDto.getFkAreaCountryId())) {
                        conventionRegistrationList.add(conventionRegistration);
                    }
                }
            }
            conventionRegistrations = conventionRegistrationList;
        }
        //币种编号
        Set<String> currencyTypeNums = new HashSet<>();
        //获取各自的值
        for (ConventionRegistration conventionRegistration : conventionRegistrations) {
            currencyTypeNums.add(conventionRegistration.getFkCurrencyTypeNum());
        }
        //币种编号map
        currencyTypeNums.removeIf(Objects::isNull);
        Map<String, String> currencyNameMap = new HashMap<>();
        Result<Map<String, String>> result = financeCenterClient.getCurrencyNamesByNums(currencyTypeNums);
        if (result.isSuccess() && result.getData() != null) {
            currencyNameMap = result.getData();
        }


        Set<Long> ids = conventionRegistrations.stream().map(ConventionRegistration::getId).collect(Collectors.toSet());

        LambdaQueryWrapper<ConventionRegistrationAreaCountry> wrapper = Wrappers.<ConventionRegistrationAreaCountry>lambdaQuery();
        if (GeneralTool.isEmpty(ids)){
            ids.add(0L);
        }
        //国家名称
        List<ConventionRegistrationAreaCountry> conventionRegistrationAreaCountries = conventionRegistrationAreaCountryMapper.selectList(wrapper.in(ConventionRegistrationAreaCountry::getFkConventionRegistrationId, ids));
        Map<Long, List<ConventionRegistrationAreaCountry>> countryKeyMap = conventionRegistrationAreaCountries.stream().collect(Collectors.groupingBy(ConventionRegistrationAreaCountry::getFkConventionRegistrationId));
        Set<String> cNums = conventionRegistrationAreaCountries.stream().map(ConventionRegistrationAreaCountry::getFkAreaCountryKey).collect(Collectors.toSet());
//        List<String> cNums = (List<String>) conventionRegistrationAreaCountries;
        Map<String,String> countryMap = institutionCenterClient.getCountryNameByNums(cNums).getData();

        //放币种名和国家名
        for (ConventionRegistration conventionRegistration : conventionRegistrations) {
            ConventionRegistrationExportVo conventionRegistrationExportVo = new ConventionRegistrationExportVo();
            conventionRegistrationExportVo.setReceiptCode(conventionRegistration.getReceiptCode());
            //待发Invoice、已发Invoice、已收款 峰会报名状态枚举
            if (GeneralTool.isNotEmpty(conventionRegistration.getStatus())) {
                String statusName = ProjectExtraEnum.getInitialValueByKey(conventionRegistration.getStatus(), ProjectExtraEnum.CONVENTION_STATUS);
                conventionRegistrationExportVo.setStatus(statusName);
            } else {
                conventionRegistrationExportVo.setStatus(" ");
            }
            conventionRegistrationExportVo.setProviderName(conventionRegistration.getProviderName());
            //国家名称
            List<String> countrys = new ArrayList<>();

            List<ConventionRegistrationAreaCountry> conventionRegistrationAreaCountryList = null;
            if (GeneralTool.isNotEmpty(countryKeyMap)&&GeneralTool.isNotEmpty(countryKeyMap.get(conventionRegistration.getId()))){
                conventionRegistrationAreaCountryList = countryKeyMap.get(conventionRegistration.getId());
            }
            if (GeneralTool.isNotEmpty(conventionRegistrationAreaCountryList)){
                List<String> nums = conventionRegistrationAreaCountryList.stream().map(ConventionRegistrationAreaCountry::getFkAreaCountryKey).collect(Collectors.toList());
                if (GeneralTool.isNotEmpty(nums)){
                    for (String num : nums) {
                        if (GeneralTool.isNotEmpty(countryMap)&&GeneralTool.isNotEmpty(countryMap.get(num))){
                            countrys.add(countryMap.get(num));
                        }
                    }
                }
            }


            if(GeneralTool.isNotEmpty(conventionRegistration.getIsVerified())&&conventionRegistration.getIsVerified()==0){
                conventionRegistrationExportVo.setIsVerified("未确认");
            }else {
                conventionRegistrationExportVo.setIsVerified("已确认");
            }
//            for (String countryKey : cNums) {
////                String country = institutionCenterClient.getCountryName(countryKey);
////                countrys.add(country);
//                Result<String> countryResult = institutionCenterClient.getCountryName(countryKey);
//                if (countryResult.isSuccess() && GeneralTool.isNotEmpty(countryResult.getData())) {
//                    countrys.add(countryResult.getData());
//                }
//            }
            conventionRegistrationExportVo.setCountry(countrys);
            conventionRegistrationExportVo.setRegistrationFee(conventionRegistration.getRegistrationFee());
            conventionRegistrationExportVo.setCurrencyName(currencyNameMap.get(conventionRegistration.getFkCurrencyTypeNum()));
            conventionRegistrationExportVo.setBoothNum(conventionRegistration.getBoothNum());
            conventionRegistrationExportVo.setBoothName(conventionRegistration.getBoothName());
            conventionRegistrationExportVo.setContactPersonName(conventionRegistration.getContactPersonName());
            conventionRegistrationExportVo.setSummaryFee(conventionRegistration.getSummaryFee());
            conventionRegistrationExportVo.setContactEmail(conventionRegistration.getContactEmail());
            conventionRegistrationExportVo.setContactTel(conventionRegistration.getContactTel());
            conventionRegistrationExportVo.setRegistrationFeeCny(conventionRegistration.getRegistrationFeeCny());
            conventionRegistrationExportVos.add(conventionRegistrationExportVo);
        }

        List<String> apiResourceKeys = SecureUtil.getApiResourceKeys();
        String settingFeePermission = "conventionRegistrationArrangement.ListExpenseStatus";
        //不是管理员且没权限
        if (!SecureUtil.getStaffInfo().getIsAdmin()&&!apiResourceKeys.contains(settingFeePermission)){
            Set<String> ignoreFields = new HashSet<>(1);
            ignoreFields.add("status");
            Map<String, String> fileMap = FileUtils.getFileMapIgnoreSomeField(ConventionRegistrationExportVo.class, ignoreFields);
//            List<ConventionRegistrationNotAllExportVo> conventionRegistrationNotAllExportDtos = BeanCopyUtils.copyListProperties(conventionRegistrationExportVos, fileMap);
            FileUtils.exportExcel(response, conventionRegistrationExportVos, "ConventionRegistration", fileMap);
        }else {
            FileUtils.exportExcel(response, conventionRegistrationExportVos, "ConventionRegistration", ConventionRegistrationExportVo.class);
        }
    }

    @Override
    public BigDecimal registrationFeeCny(ConventionRegistrationDto conventionRegistrationDto) {
        if (GeneralTool.isEmpty(conventionRegistrationDto.getFkCurrencyTypeNum()) || GeneralTool.isEmpty(conventionRegistrationDto.getRegistrationFee())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        BigDecimal exchangeRate = null;
        Result<BigDecimal> exchangeRateResult = financeCenterClient.getLastExchangeRate(true, conventionRegistrationDto.getFkCurrencyTypeNum(), "CNY");
        if (exchangeRateResult.isSuccess() && GeneralTool.isNotEmpty(exchangeRateResult.getData())) {
            exchangeRate = exchangeRateResult.getData();
        }
        if (GeneralTool.isEmpty(exchangeRate)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("rate_null"));
        }
        return exchangeRate.multiply(conventionRegistrationDto.getRegistrationFee());
    }

    @Override
    public List<ConventionRegistration> getConventionRegistrationsByVo(ConventionRegistrationDto conventionRegistrationDto) {
        LambdaQueryWrapper<ConventionRegistration> lambdaQueryWrapper = Wrappers.lambdaQuery();
        if (GeneralTool.isNotEmpty(conventionRegistrationDto.getFkConventionId())){
            lambdaQueryWrapper.eq(ConventionRegistration::getFkConventionId, conventionRegistrationDto.getFkConventionId());
        }
        if (GeneralTool.isNotEmpty(conventionRegistrationDto.getReceiptCodes())){
            lambdaQueryWrapper.in(ConventionRegistration::getReceiptCode, conventionRegistrationDto.getReceiptCodes());
        }
        if (GeneralTool.isNotEmpty(conventionRegistrationDto.getFkEventCostId())){
            lambdaQueryWrapper.eq(ConventionRegistration::getFkEventCostId, conventionRegistrationDto.getFkEventCostId());
        }
        List<ConventionRegistration> conventionRegistrations = conventionRegistrationMapper.selectList(lambdaQueryWrapper);
        return conventionRegistrations;
    }

    @Override
    public ConventionRegistration getConventionRegistrationById(Long conventionRegistrationId) {
        return conventionRegistrationMapper.selectById(conventionRegistrationId);
    }

    @Override
    public void updateConventionRegistrationById(ConventionRegistration conventionRegistration) {
        boolean b = updateById(conventionRegistration);
        if (!b){
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
    }

    @Override
    public void updateWithNullConventionRegistrationById(ConventionRegistration conventionRegistration) {
        conventionRegistrationMapper.updateByIdWithNull(conventionRegistration);
    }

    /**
     * 一键生成活动费用汇总、应收计划、发票
     *
     * @param conventionRegistrationFastGenerationVo 参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void fastGeneration(ConventionRegistrationFastGenerationDto conventionRegistrationFastGenerationVo) {
        if (GeneralTool.isEmpty(conventionRegistrationFastGenerationVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        if (GeneralTool.isEmpty(conventionRegistrationFastGenerationVo.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ConventionRegistration conventionRegistration = conventionRegistrationMapper.selectById(conventionRegistrationFastGenerationVo.getId());
        if (GeneralTool.isEmpty(conventionRegistration)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        // 峰会id
        Convention convention = conventionService.getById(conventionRegistration.getFkConventionId());
        if (GeneralTool.isEmpty(convention.getFkEventId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("associated_event_id"));
        }
        // 更新对应的学校提供商和状态
        conventionRegistration.setFkInstitutionProviderId(conventionRegistrationFastGenerationVo.getFkInstitutionProviderId());
        conventionRegistration.setStatus(ProjectExtraEnum.CONVENTION_STATUS_SENT_INVOICE.key);
        conventionRegistrationMapper.updateById(conventionRegistration);
        // 币种
        String fkCurrencyTypeNumToAll = conventionRegistrationFastGenerationVo.getFkCurrencyTypeNumToAll();
        // 金额
        BigDecimal amountToAll = conventionRegistrationFastGenerationVo.getAmountToAll();
        /**
         * 创建活动费用汇总（发起收款计划）
         */
        // 获取币种汇率
        BigDecimal hkdRate = BigDecimal.ONE;
        BigDecimal cnyRate = BigDecimal.ONE;
        Result<BigDecimal> hkdRateResult = financeCenterClient.getLastExchangeRate(true, fkCurrencyTypeNumToAll, "HKD");
        if (hkdRateResult.isSuccess() && GeneralTool.isNotEmpty(hkdRateResult.getData())) {
            hkdRate = hkdRateResult.getData();
        }
        Result<BigDecimal> cnyRateResult = financeCenterClient.getLastExchangeRate(true, fkCurrencyTypeNumToAll, "CNY");
        if (cnyRateResult.isSuccess() && GeneralTool.isNotEmpty(cnyRateResult.getData())) {
            cnyRate = cnyRateResult.getData();
        }

        // 组装EventCostVo
        EventCostDto eventCostVo = new EventCostDto();
        eventCostVo.setFkEventId(convention.getFkEventId());
        eventCostVo.setFkCurrencyTypeNum(fkCurrencyTypeNumToAll);
        eventCostVo.setAmount(amountToAll);
        eventCostVo.setExchangeRateReceivable(BigDecimal.ONE);
        eventCostVo.setAmountReceivable(amountToAll);
        eventCostVo.setFkInstitutionProviderId(conventionRegistrationFastGenerationVo.getFkInstitutionProviderId());
        eventCostVo.setAmountHkd(hkdRate.multiply(amountToAll));
        eventCostVo.setExchangeRateHkd(hkdRate);
        eventCostVo.setAmountRmb(cnyRate.multiply(amountToAll));
        eventCostVo.setExchangeRateRmb(cnyRate);

        // 组装参数，由于参数复杂，这里一个个赋值
        ConventionRegistrationEventBillDto conventionRegistrationEventBillVo = new ConventionRegistrationEventBillDto();
        conventionRegistrationEventBillVo.setFkCompanyId(conventionRegistrationFastGenerationVo.getFkCompanyId());
        conventionRegistrationEventBillVo.setFkInstitutionProviderId(conventionRegistrationFastGenerationVo.getFkInstitutionProviderId());
        conventionRegistrationEventBillVo.setFkCurrencyTypeNumEvent(fkCurrencyTypeNumToAll);
        conventionRegistrationEventBillVo.setEventAmount(amountToAll);
        conventionRegistrationEventBillVo.setFkCurrencyTypeNumInvoice(fkCurrencyTypeNumToAll);
        conventionRegistrationEventBillVo.setInvoiceAmount(amountToAll);
        conventionRegistrationEventBillVo.setInvoiceSummary(conventionRegistrationFastGenerationVo.getInvoiceSummary());
        conventionRegistrationEventBillVo.setInvoiceContactPerson(conventionRegistrationFastGenerationVo.getInvoiceContactPerson());
        conventionRegistrationEventBillVo.setEventYear(conventionRegistrationFastGenerationVo.getEventYear());
        conventionRegistrationEventBillVo.setInvoiceContactEmail(conventionRegistration.getContactEmail());
        conventionRegistrationEventBillVo.setStatus(1);
        conventionRegistrationEventBillVo.setEventCostVos(Collections.singletonList(eventCostVo));
        conventionRegistrationEventBillVo.setFkAreaCountryIdList(conventionRegistrationFastGenerationVo.getFkAreaCountryIdList());
        // Summer说写死id（活动报名费）
        conventionRegistrationEventBillVo.setFkEventSummaryIdList(Collections.singletonList(19L));
        // 添加活动费用汇总
        EventBill eventBill = addEventBill(conventionRegistrationEventBillVo);
        // 组装EventBillUpdateVo
        EventBillUpdateDto eventBillVo = new EventBillUpdateDto();
        eventBillVo.setId(eventBill.getId());
        eventBillVo.setFkInvoiceNum(conventionRegistrationFastGenerationVo.getFkInvoiceNum());
        eventBillVo.setFkCurrencyTypeNumInvoice(fkCurrencyTypeNumToAll);
        eventBillVo.setInvoiceAmount(amountToAll);
        // 创建应收计划及发票（调用generateBill接口（创建财务单据））
        eventBillService.generateBill(eventBillVo);
    }



    /**
     * 添加活动费用汇总
     *
     * @param conventionRegistrationEventBillVo
     */
    private EventBill addEventBill(ConventionRegistrationEventBillDto conventionRegistrationEventBillVo) {
        if (GeneralTool.isEmpty(conventionRegistrationEventBillVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        EventBill eventBill = BeanCopyUtils.objClone(conventionRegistrationEventBillVo, EventBill::new);
        utilService.setCreateInfo(eventBill);
        eventBillService.save(eventBill);

        List<EventCostDto> eventCostVos = conventionRegistrationEventBillVo.getEventCostVos();
        if (GeneralTool.isNotEmpty(eventCostVos)){
            for (EventCostDto eventCostVo : eventCostVos) {
                eventCostVo.setFkEventBillId(eventBill.getId());
            }
            eventCostService.batchAdd(eventCostVos);
        }

        // 绑定业务国家
        List<Long> fkAreaCountryIdList = conventionRegistrationEventBillVo.getFkAreaCountryIdList();
        if (GeneralTool.isNotEmpty(fkAreaCountryIdList)){
            List<EventBillAreaCountry> eventBillAreaCountryList = new ArrayList<>();
            for (Long fkAreaCountryId : fkAreaCountryIdList) {
                EventBillAreaCountry eventBillAreaCountry = new EventBillAreaCountry();
                eventBillAreaCountry.setFkEventBillId(eventBill.getId());
                eventBillAreaCountry.setFkAreaCountryId(fkAreaCountryId);
                utilService.setCreateInfo(eventBillAreaCountry);
                eventBillAreaCountryList.add(eventBillAreaCountry);
            }
            eventBillAreaCountryService.saveBatch(eventBillAreaCountryList);
        }

        // 绑定摘要关系表
        List<Long> fkEventSummaryIdList = conventionRegistrationEventBillVo.getFkEventSummaryIdList();
        if (GeneralTool.isNotEmpty(fkEventSummaryIdList)){
            List<EventBillEventSummary> eventBillEventSummaryList = new ArrayList<>();
            for (Long fkEventSummaryId : fkEventSummaryIdList) {
                EventBillEventSummary eventBillEventSummary = new EventBillEventSummary();
                eventBillEventSummary.setFkEventBillId(eventBill.getId());
                eventBillEventSummary.setFkEventSummaryId(fkEventSummaryId);
                utilService.setCreateInfo(eventBillEventSummary);
                eventBillEventSummaryList.add(eventBillEventSummary);
            }
            eventBillEventSummaryService.saveBatch(eventBillEventSummaryList);
        }
        return eventBill;
    }

    /**
     * @return tk.mybatis.mapper.entity.Example
     * @Description :设置好example查询条件
     * @Param [conventionRegistrationDto]
     * <AUTHOR>
     */
    private LambdaQueryWrapper<ConventionRegistration> getLambdaQueryWrapper(ConventionRegistrationDto conventionRegistrationDto) {
        LambdaQueryWrapper<ConventionRegistration> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ConventionRegistration::getFkConventionId, conventionRegistrationDto.getFkConventionId());
        if (GeneralTool.isNotEmpty(conventionRegistrationDto)) {
            //查询条件-学校提供商名称
            if (GeneralTool.isNotEmpty(conventionRegistrationDto.getInstitutionProviderName())) {
                //根据学校提供商名称模糊查找所有对应的id
                List<Long> fkInstitutionProviderIds = new ArrayList<>();
                Result<List<Long>> result = institutionCenterClient.getInstitutionProviderIdsByName(conventionRegistrationDto.getInstitutionProviderName());
                if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                    fkInstitutionProviderIds = result.getData();
                }
                //设置条件查询出属于这些id的报名数据
                if (GeneralTool.isNotEmpty(fkInstitutionProviderIds)) {
                    lambdaQueryWrapper.in(ConventionRegistration::getFkInstitutionProviderId, fkInstitutionProviderIds);
                }
            }
            //查询条件-联系人名称
            if (GeneralTool.isNotEmpty(conventionRegistrationDto.getContactPersonName())) {
                lambdaQueryWrapper.and(wrapper -> wrapper.like(ConventionRegistration::getContactPersonName, conventionRegistrationDto.getContactPersonName())
                        .or().like(ConventionRegistration::getContactPersonNameChn, conventionRegistrationDto.getContactPersonName()));
//                criteria1.andLike("contactPersonName", "%" + conventionRegistrationDto.getContactPersonName() + "%");
//                criteria1.orLike("contactPersonNameChn", "%" + conventionRegistrationDto.getContactPersonName() + "%");

            }
            //查询条件-展会名称
            if (GeneralTool.isNotEmpty(conventionRegistrationDto.getBoothName())) {
                lambdaQueryWrapper.like(ConventionRegistration::getBoothName, conventionRegistrationDto.getBoothName());
            }
            //展会编号
            if (GeneralTool.isNotEmpty(conventionRegistrationDto.getBoothNum())) {
                lambdaQueryWrapper.like(ConventionRegistration::getBoothNum, conventionRegistrationDto.getBoothNum());

            }
            //回执码
            if (GeneralTool.isNotEmpty(conventionRegistrationDto.getReceiptCode())) {
                lambdaQueryWrapper.like(ConventionRegistration::getReceiptCode, conventionRegistrationDto.getReceiptCode());
            }
            //状态
            if (GeneralTool.isNotEmpty(conventionRegistrationDto.getStatus())) {
                lambdaQueryWrapper.eq(ConventionRegistration::getStatus, conventionRegistrationDto.getStatus());

            }
            //提供商名称
            if (GeneralTool.isNotEmpty(conventionRegistrationDto.getProviderName())) {
                lambdaQueryWrapper.eq(ConventionRegistration::getProviderName, conventionRegistrationDto.getProviderName());

            }
        }
        lambdaQueryWrapper.orderByDesc(ConventionRegistration::getGmtCreate);
        return lambdaQueryWrapper;
    }

    private List<BoothVo> getBoothByReceiptCodeAndStatus(Integer status, List<BoothVo> boothVos, List<ConventionRegistration> conventionRegistrations) {
        BoothVo boothVo = new BoothVo();
        BigDecimal sumRegistrationFree = new BigDecimal(0);

        //List<ConventionRegistration> conventionRegistrations = conventionRegistrationMapper.getSumRegistrationFreeByReceiptCode(receiptCode, status);
        //币种编号
        Set<String> currencyTypeNums = new HashSet<>();
        //获取各自的值
        for (ConventionRegistration conventionRegistration : conventionRegistrations) {
            currencyTypeNums.add(conventionRegistration.getFkCurrencyTypeNum());
        }
        //币种编号map
        currencyTypeNums.removeIf(Objects::isNull);
        Set<String> currencyTypeNumSet = new HashSet<>();
        if (GeneralTool.isNotEmpty(conventionRegistrations)) {
            for (ConventionRegistration conventionRegistration : conventionRegistrations) {
                if (GeneralTool.isNotEmpty(conventionRegistration.getRegistrationFee())) {
                    sumRegistrationFree = sumRegistrationFree.add(conventionRegistration.getRegistrationFee()).setScale(2, BigDecimal.ROUND_HALF_UP);
                }
                currencyTypeNumSet.add(conventionRegistration.getFkCurrencyTypeNum());
            }
        }
        if (currencyTypeNumSet.size() > 1) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("currency_is_not_unique"));
        }
        if (GeneralTool.isNotEmpty(currencyTypeNumSet)) {
            //String currencyName = feignFinanceService.getCurrencyNameByNum(currencyTypeNumSet.iterator().next());
            boothVo.setFkCurrencyTypeNum(currencyTypeNumSet.iterator().next());
            Result<Map<String, String>> result = financeCenterClient.getCurrencyNamesByNums(currencyTypeNums);
            if (result.isSuccess() && result.getData() != null) {
                Map<String, String> currencyNameMap = result.getData();
                boothVo.setCurrencyName(currencyNameMap.get(currencyTypeNumSet.iterator().next()));
            }

        }
        boothVo.setStatus(status);
        boothVo.setSumRegistrationFee(sumRegistrationFree);
        boothVos.add(boothVo);
        return boothVos;
    }

    private void getConventionSponsorFee(ConventionRegistrationDto conventionRegistrationDto, ConventionRegistrationVo conventionRegistrationVo, List<SponsorVo> sponsorVos, Integer sponsorStatus, List<ConventionSponsorVo> sponsors) {

        // 根据回执码 状态查询赞助项目
        //List<ConventionSponsorVo> sponsors = conventionSponsorMapper.getSponsorByReceiptCodeAndStatus(conventionRegistrationVo.getReceiptCode(), conventionRegistrationDto.getFkConventionId(), sponsorStatus);
        if (GeneralTool.isEmpty(sponsors)) {
            return;
        }
        //根据赞助商id查找所选赞助类型
        List<ConventionSponsorFeeVo> sponsorFeeDtoList = conventionSponsorSponsorFeeService.getSponsorFeeDtoList(sponsors.get(0).getId());
        for (ConventionSponsorFeeVo conventionSponsorFeeVo : sponsorFeeDtoList) {
            setSponsorResult(conventionSponsorFeeVo);
        }
        SponsorVo sponsorVo = new SponsorVo();
        sponsorVo.setStatus(sponsorStatus);
        sponsorVo.setConventionSponsorFeeDtoList(sponsorFeeDtoList);
        sponsorVos.add(sponsorVo);


    }

    //统一sponsor结果
    private void setSponsorResult(ConventionSponsorFeeVo sponsor) {
        //feign调用 根据币种编号查找币种名称
        Result<String> result = financeCenterClient.getCurrencyNameByNum(sponsor.getFkCurrencyTypeNum());
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            String currencyName = result.getData();
            sponsor.setCurrencyName(currencyName);
        }

    }

    private void getBoothDtoByStatus(ConventionRegistrationDto conventionRegistrationDto, ConventionRegistrationVo conventionRegistrationVo, List<BoothVo> boothVos, Integer status, List<ConventionRegistrationVo> booths) {
        //查询指定状态数据的展位
        conventionRegistrationVo.setStatus(status);
        //符合条件的展位数据
        //List<ConventionRegistrationVo> booths = conventionRegistrationMapper.getBoothsByReceiptCode(conventionRegistrationVo.getReceiptCode(), conventionRegistrationDto.getFkConventionId(), conventionRegistrationVo.getStatus());
        BoothVo boothVo = new BoothVo();
        //BigDecimal sumRegistrationFee = new BigDecimal(0);
        for (ConventionRegistrationVo booth : booths) {
            //设置国家、币种、折合人民币
            setResult(booth);
        }
        //boothVo.setSumRegistrationFee(sumRegistrationFee);
        boothVo.setStatus(status);
        boothVo.setConventionRegistrationDtos(booths);
        boothVos.add(boothVo);


    }


    /**
     * 通过关联表根据报名ids查出国家keys
     *
     * @param ids
     * @return
     */
    private Map<Long, List<String>> getCountryKeyByIds(Set<Long> ids) {
        Map<Long, List<String>> map = new HashMap<>();
        if (GeneralTool.isEmpty(ids)) {
            return map;
        }
//        Example example = new Example(ConventionRegistrationAreaCountry.class);
//        example.createCriteria().andIn("fkConventionRegistrationId",ids);
//        List<ConventionRegistrationAreaCountry> conventionRegistrationAreaCountries = conventionRegistrationAreaCountryMapper.selectByExample(example);

        List<ConventionRegistrationAreaCountry> conventionRegistrationAreaCountries = conventionRegistrationAreaCountryMapper.selectList(Wrappers.<ConventionRegistrationAreaCountry>lambdaQuery().in(ConventionRegistrationAreaCountry::getFkConventionRegistrationId, ids));
        if (GeneralTool.isEmpty(conventionRegistrationAreaCountries)) {
            return map;
        }
        for (ConventionRegistrationAreaCountry conventionRegistrationAreaCountry : conventionRegistrationAreaCountries) {
            //如果集合里面包含这个报名id，则往原来的集合里面添加元素
            if (map.containsKey(conventionRegistrationAreaCountry.getFkConventionRegistrationId())) {
                List<String> beforeCountryKeys = map.get(conventionRegistrationAreaCountry.getFkConventionRegistrationId());
                beforeCountryKeys.add(conventionRegistrationAreaCountry.getFkAreaCountryKey());
                map.put(conventionRegistrationAreaCountry.getFkConventionRegistrationId(), beforeCountryKeys);
                continue;
            }
            //不存在，则直接往集合添加新元素
            List<String> countryKey = new ArrayList<>();
            countryKey.add(conventionRegistrationAreaCountry.getFkAreaCountryKey());
            map.put(conventionRegistrationAreaCountry.getFkConventionRegistrationId(), countryKey);
        }
        return map;
    }
}

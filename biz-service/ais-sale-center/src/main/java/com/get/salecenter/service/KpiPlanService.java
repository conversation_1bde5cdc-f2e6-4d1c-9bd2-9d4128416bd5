package com.get.salecenter.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.permissioncenter.vo.CompanyVo;
import com.get.salecenter.dto.KpiPlanStatisticsExportDto;
import com.get.salecenter.entity.KpiPlanStaff;
import com.get.salecenter.vo.KpiPlanVo;
import com.get.salecenter.vo.KpiPlanStatisticsVo;
import com.get.salecenter.entity.KpiPlan;
import com.get.salecenter.dto.KpiPlanSearchDto;
import com.get.salecenter.dto.KpiPlanStatisticsDto;
import com.get.salecenter.dto.KpiPlanDto;
import com.get.salecenter.vo.KpiStaffIdAddVo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Set;

public interface KpiPlanService extends IService<KpiPlan> {

    /**
     * KPI方案列表数据
     * <AUTHOR>
     * @DateTime 2024/4/16 14:23
     */
    List<KpiPlanVo> datas(KpiPlanSearchDto kpiPlanSearchDto, Page page);

    /**
     * 详情
     * <AUTHOR>
     * @DateTime 2024/4/19 16:52
     */
    KpiPlanVo findKpiPlanById(Long id);

    /**
     * 更新KPI方案统计（即时）
     *
     * @param kpiPlanStatisticsDto 参数
     * @return
     */
    void addKpiPlanTaskResult(KpiPlanStatisticsDto kpiPlanStatisticsDto);

    /**
     * 获取KPI方案统计结果
     *
     * @param kpiPlanStatisticsVo 参数
     * @return
     */
    KpiPlanStatisticsVo getKpiPlanTaskResult(KpiPlanStatisticsDto kpiPlanStatisticsVo);

    /**
     * KPI方案统计（异步）
     *
     * @param kpiPlanStatisticsDto 参数
     * @return
     */
    KpiPlanStatisticsVo getKpiPlanStatistics(KpiPlanStatisticsDto kpiPlanStatisticsDto);

    /**
     * 没有找到考核人员，返回考核人员树最低层级的考核人员列表
     *
     * @param fkKpiPlanId      KPI方案ID
     * @param staffFollowerIds 指定人员的业务下属Ids
     * @return
     */
    List<KpiPlanStaff> getMinLevelStaffs(Long fkKpiPlanId, Set<Long> staffFollowerIds);

    /**
     * 导出KPI方案统计
     * <AUTHOR>
     * @DateTime 2024/4/24 15:55
     */
    void exportKpiPlanStatisticsExcel(KpiPlanStatisticsExportDto kpiPlanStatisticsExportDto, HttpServletResponse response);

    /**
     * 新增KPI方案
     * <AUTHOR>
     * @DateTime 2024/4/16 14:24
     */
    Long addKpiPlan(KpiPlanDto kpiPlanDto);

    /**
     * 修改KPI方案
     * <AUTHOR>
     * @DateTime 2024/4/18 15:09
     */
    void updateKpiPlan(KpiPlanDto kpiPlanDto);

    /**
     * 删除KPI方案
     * <AUTHOR>
     * @DateTime 2024/4/19 17:33
     */
    void delete(Long id);


    /**
     * 复制KPI方案
     * <AUTHOR>
     * @DateTime 2024/4/18 18:18
     */
    Long copyKpiPlan(Long fkKpiPlanId);

    /**
     * KPI代理排名导出
     * @param kpiPlanStatisticsDto
     */
    void exportKpiAgentRankExcel(KpiPlanStatisticsDto kpiPlanStatisticsDto);
    /**
     * 获取KPI方案数据时间戳之和
     *
     * @param fkKpiPlanId kpi方案id
     * @return
     */
    JSONObject getSumTimeKpiData(Long fkKpiPlanId);

    /**
     * KPI模板文件下载
     *
     * @param response
     */
    void downloadTemplateFile(HttpServletResponse response);

    /**
     * 导入KPI数据
     *
     * @param file        文件
     * @param fkKpiPlanId kpi方案id
     * @return
     */
    ResponseBo importKpiData(MultipartFile file, Long fkKpiPlanId);

    /**
     * KPI方案下拉
     *
     * @param fkCompanyIds 公司id
     * @return
     */
    List<KpiPlanDto> getKpiPlanSelect(String fkCompanyIds);

    /**
     * 定时任务，执行KPI方案统计
     */
    void kpiPlanStatistics();

    CompanyVo getKpiPlanCompanySelect(Long kpiPlanId);

    /**
     * 获取kpi方案第一层的目标设置人
     * @param kpiPlanStatisticsVo
     * @return
     */
    KpiStaffIdAddVo findFkStaffIdAdd(KpiPlanStatisticsDto kpiPlanStatisticsVo);
}

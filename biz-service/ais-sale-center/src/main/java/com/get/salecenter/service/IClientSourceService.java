package com.get.salecenter.service;


import com.get.common.result.Page;
import com.get.salecenter.vo.ClientSourceListVo;
import com.get.salecenter.dto.ClientSourceAddDto;
import com.get.salecenter.dto.ClientSourceListDto;

import java.util.List;

/**
 * author:Neil
 * Time: 12:53
 * Date: 2022/8/17
 * Description:
 */
public interface IClientSourceService {

    /**
     * 列表
     * @param clientSourceListDto
     * @param page
     * @return
     */
    List<ClientSourceListVo> getClientSources(ClientSourceListDto clientSourceListDto, Page page);

    /**
     * 删除
     * @param id
     */
    void delete(Long id);

    /**
     * 新增
     * @param clientSourceAddDto
     * @return
     */
    void addClientSource(ClientSourceAddDto clientSourceAddDto);

    /**
     * 验证学生资源来源
     * @param studentNum
     * @return
     */
    Boolean validateClientSource(String studentNum);

    void addBmsClientSource(ClientSourceAddDto clientSourceAddDto);

    void addCrmClientSource(ClientSourceAddDto clientSourceAddDto);

    void addAgentClientSource(ClientSourceAddDto clientSourceAddDto);

    void addBmsNotOsClientSource(ClientSourceAddDto clientSourceAddDto);

    void addMarketingDpClientSource(ClientSourceAddDto clientSourceAddDto);

    void addClassmatesClientSource(ClientSourceAddDto clientSourceAddDto);

    void addOtherClientSource(ClientSourceAddDto clientSourceAddDto);

    void addBusinessProviderClientSource(ClientSourceAddDto clientSourceAddDto);
}

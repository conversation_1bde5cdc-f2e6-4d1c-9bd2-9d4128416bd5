package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.eunms.ErrorCodeEnum;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseVoEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.vo.*;
import com.get.salecenter.vo.CommissionStaffDatasVo;
import com.get.salecenter.service.IStaffCommissionActionService;
import com.get.salecenter.dto.*;
import com.get.salecenter.dto.CommissionStaffDatasDto;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: Hardy
 * @create: 2023/2/7 15:16
 * @verison: 1.0
 * @description:
 */
@Api(tags = "员工提成结算记录管理")
@RestController
@RequestMapping("sale/staffCommissionAction")
public class StaffCommissionActionController {

    @Resource
    private IStaffCommissionActionService staffCommissionActionService;


    @ApiOperation(value = "批量确认结算（已确认列表的结算）", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/员工提成结算记录管理/批量确认结算")
    @PostMapping("confirmSettlement")
    public ResponseBo confirmSettlement(@RequestBody @Validated({BaseVoEntity.Add.class}) ValidList<StaffCommissionActionDto> staffCommissionActionDtos) {
        staffCommissionActionService.confirmSettlement(staffCommissionActionDtos);
        return SaveResponseBo.ok();
    }


    @ApiOperation(value = "单条取消结算", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/员工提成结算记录管理/单条取消结算")
    @PostMapping("cancelSettlementSingle")
    public ResponseBo cancelSettlementSingle(@RequestParam("studentId")Long studentId,@RequestParam("studentOfferItemId")Long studentOfferItemId,@RequestParam("commissionStep")String commissionStep) {
        staffCommissionActionService.cancelSettlementSingle(studentId,studentOfferItemId,commissionStep);
        return DeleteResponseBo.ok();
    }


    @ApiOperation(value = "查看", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/员工提成结算记录管理/查看")
    @PostMapping("getStudentOfferItemCommissionInfo")
    public ListResponseBo<StaffCommissionConfirmVo> getStudentOfferItemCommissionInfo(@RequestBody StudentOfferItemCommissionInfoDto studentOfferItemCommissionInfoDto) {
        List<StaffCommissionConfirmVo> datas = staffCommissionActionService.getStudentOfferItemCommissionInfo(studentOfferItemCommissionInfoDto);
        return new ListResponseBo<>(datas);
    }


    @ApiOperation(value = "结算信息列表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/员工提成结算记录管理/结算信息列表")
    @PostMapping("getSettlementInfo")
    public ResponseBo getSettlementInfo(@RequestBody StudentOfferItemCommissionInfoDto studentOfferItemCommissionInfoDto) {
        SettlementInfoVo settlementInfo = staffCommissionActionService.getSettlementInfo(studentOfferItemCommissionInfoDto);
        ListResponseBo<Object> responseBo = new ListResponseBo<>();
        //表头
        responseBo.setData(settlementInfo.getTitleInfos());
        //列表数据
        responseBo.setDatas(Lists.newArrayList(settlementInfo.getStaffCommissionTotalDto()));
        return responseBo;
    }


    @ApiOperation(value = "已确认取消结算", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/员工提成结算记录管理/已确认取消结算")
    @PostMapping("cancelSettlement")
    public ResponseBo cancelSettlement(@RequestParam("studentId")Long studentId,@RequestParam("commissionStep")String commissionStep) {
        staffCommissionActionService.cancelSettlement(studentId,commissionStep);
        return DeleteResponseBo.ok();
    }


    @ApiOperation(value = "申请提成汇总", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/员工提成结算记录管理/申请提成汇总")
    @PostMapping("getStaffCommissionSummary")
    public ResponseBo getStaffCommissionSummary(@RequestBody StaffCommissionSummaryDto staffCommissionSummaryDto) {
        List<StaffCommissionDatasVo> staffCommissionDatasVos = staffCommissionActionService.getStaffCommissionStepTitles(staffCommissionSummaryDto.getFkCompanyId());
        List<StaffCommissionSummaryVo> staffCommissionSummaryVos = staffCommissionActionService.getStaffCommissionSummary(staffCommissionSummaryDto, staffCommissionDatasVos);
        ListResponseBo<Object> responseBo = new ListResponseBo<>();
        //表头
        responseBo.setData(staffCommissionDatasVos);
        //列表数据
        responseBo.setDatas(staffCommissionSummaryVos);
        return responseBo;
    }

    @ApiOperation(value = "汇总取消结算", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/员工提成结算记录管理/汇总取消结算")
    @PostMapping("cancelSettlementInfo")
    public ResponseBo cancelSettlementInfo(@RequestParam("studentId")Long studentId) {
        staffCommissionActionService.cancelSettlementInfo(studentId);
        return DeleteResponseBo.ok();
    }

    @ApiOperation(value = "汇总结案", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/员工提成结算记录管理/汇总结案")
    @PostMapping("completeSettlement")
    public ResponseBo completeSettlement(@RequestParam("studentId")Long studentId) {
        staffCommissionActionService.completeSettlement(studentId);
        return UpdateResponseBo.ok();
    }


    @ApiOperation(value = "员工提成结算列表", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/员工提成结算记录管理/员工提成结算列表")
    @PostMapping("getCommissionStaffDatas")
    public ResponseBo getCommissionStaffDatas(@RequestBody CommissionStaffDatasDto commissionStaffDatasDto) {
        List<StaffCommissionDatasVo> staffCommissionDatasVos = staffCommissionActionService.getStaffCommissionStepTitles(commissionStaffDatasDto.getFkCompanyId());
        List<CommissionStaffDatasVo> commissionStaffDatasVos = staffCommissionActionService.getCommissionStaffDatas(commissionStaffDatasDto, staffCommissionDatasVos);
        ListResponseBo<Object> responseBo = new ListResponseBo<>();
        //表头
        responseBo.setData(staffCommissionDatasVos);
        //列表数据
        responseBo.setDatas(commissionStaffDatasVos);
        return responseBo;
    }

    @ApiOperation(value = "批量结算提成申请", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/员工提成结算记录管理/批量结算提成申请")
    @PostMapping("batchSettlementCommission")
    public ResponseBo batchSettlementCommission(@RequestBody List<BatchSettlementCommissionDto> batchSettlementCommissionDtos) {
        Boolean result = staffCommissionActionService.batchSettlementCommission(batchSettlementCommissionDtos);
        if (!result){
            ResponseBo responseBo = new ResponseBo();
            responseBo.setMessage(LocaleMessageUtils.getMessage("has_not_commission_record"));
            responseBo.setSuccess(false);
            responseBo.setCode(ErrorCodeEnum.VERIFY_FAILED.getCode());
            return responseBo;
        }
        return UpdateResponseBo.ok();
    }



    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "代理下拉框")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/员工提成结算记录管理/提成汇总代理下拉框")
    @GetMapping("getStaffCommissionAgentList")
    public ResponseBo<BaseSelectEntity> getStaffCommissionAgentList(@RequestParam("companyId") Long companyId) {
        List<BaseSelectEntity> datas = staffCommissionActionService.getStaffCommissionAgentList(companyId);
        return new ListResponseBo<>(datas);
    }


    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "跟进项目成员下拉框")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/员工提成结算记录管理/提成汇总跟进项目成员下拉框")
    @GetMapping("getStaffCommissionStaffList")
    public ResponseBo<BaseSelectEntity> getStaffCommissionStaffList(@RequestParam("companyId") Long companyId) {
        List<BaseSelectEntity> datas = staffCommissionActionService.getStaffCommissionStaffList(companyId);
        return new ListResponseBo<>(datas);
    }


    @ApiOperation(value = "员工结算统计", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/员工提成结算记录管理/员工结算统计")
    @PostMapping("getStaffSettlementStatistics")
    public ResponseBo getStaffSettlementStatistics(@RequestBody StaffSettlementStatisticsDto staffSettlementStatisticsDto) {

        List<StaffCommissionDatasVo> staffCommissionDatasVos = staffCommissionActionService.getStaffCommissionStepTitles(staffSettlementStatisticsDto.getFkCompanyId());

        StaffSettlementStatisticsVo staffSettlementStatisticsVo = staffCommissionActionService.getStaffSettlementStatistics(staffSettlementStatisticsDto, staffCommissionDatasVos);
        ListResponseBo<Object> responseBo = new ListResponseBo<>();
        //表头
        responseBo.setData(staffCommissionDatasVos);
        //列表数据
        responseBo.setDatas(Lists.newArrayList(staffSettlementStatisticsVo));
        return responseBo;
    }


    @ApiOperation(value = "员工结算统计导出", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/员工提成结算记录管理/员工结算统计导出")
    @PostMapping("exportStaffSettlementStatistics")
    public void exportStaffSettlementStatistics(HttpServletResponse response, @RequestBody StaffSettlementStatisticsDto staffSettlementStatisticsDto) {
        //表头
        List<StaffCommissionDatasVo> staffCommissionDatasVos = staffCommissionActionService.getStaffCommissionStepTitles(staffSettlementStatisticsDto.getFkCompanyId());
        //导出
        staffCommissionActionService.exportStaffSettlementStatistics(response, staffSettlementStatisticsDto, staffCommissionDatasVos);

    }

    @ApiOperation(value = "项目成员业绩结算退款模块", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/员工提成结算记录管理/项目成员业绩结算退款模块")
    @PostMapping("getStaffSettlementRefundList")
    public ListResponseBo<StaffCommissionRefundVo> getStaffSettlementRefundList(@Validated @RequestBody SearchBean<StaffCommissionRefundDto> page) throws ParseException {
        List<StaffCommissionRefundVo> staffCommissionDatasDtos = staffCommissionActionService.getStaffSettlementRefundList(page.getData(),page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(staffCommissionDatasDtos,p);
    }

    @ApiOperation(value = "项目成员业绩结算退款模块所有ids", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/员工提成结算记录管理/项目成员业绩结算退款模块")
    @PostMapping("getStaffSettlementRefundIds")
    public ResponseBo getStaffSettlementRefundIds(@Validated @RequestBody StaffCommissionRefundDto data) throws ParseException {

        List<StaffCommissionRefundVo> staffCommissionDatasDtos = staffCommissionActionService.getStaffSettlementRefundList(data,null);
        String ids = staffCommissionDatasDtos.stream().map(StaffCommissionRefundVo::getId).map(Object::toString).collect(Collectors.joining(","));
        return new ResponseBo(ids);
    }

    @ApiOperation(value = "项目成员业绩结算退款模块(角色总计)", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/员工提成结算记录管理/项目成员业绩结算退款模块角色总计")
    @PostMapping("getStaffSettlementRefundRoleSumarry")
    public ResponseBo<StaffCommissionRefundRoleVo> getStaffSettlementRefundRoleSumarry(@Validated @RequestBody StaffCommissionRefundDto staffCommissionRefundDto) throws ParseException {
        return new ResponseBo<>(staffCommissionActionService.getStaffSettlementRefundRoleSumarry(staffCommissionRefundDto));
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "结算日期下拉框", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/员工提成结算记录管理/结算日期下拉框")
    @GetMapping("getSettlementDateSelect")
    public ListResponseBo<BaseSelectEntity> getSettlementDateSelect(@RequestParam("companyId") Long companyId) {
        List<BaseSelectEntity> settlementDateList = staffCommissionActionService.getSettlementDateSelect(companyId);
        return new ListResponseBo<>(settlementDateList);
    }

    @ApiOperation(value = "批量退款审核", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/员工提成结算记录管理/批量退款审核")
    @PostMapping("batchRefundReview")
    public ResponseBo batchRefundReview(@RequestBody StaffCommissionBatchRefundDto staffCommissionRefundVo) {
        staffCommissionActionService.batchRefundReview(staffCommissionRefundVo);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "批量退款结算", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/员工提成结算记录管理/批量退款结算")
    @PostMapping("batchRefundSettlement")
    public ResponseBo batchRefundSettlement(@RequestBody StaffCommissionBatchRefundDto staffCommissionRefundVo) {
        staffCommissionActionService.batchRefundSettlement(staffCommissionRefundVo);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "项目成员业绩结算列表Excel", notes = "")
    @PostMapping("/exportStaffSettlementRefundExcel")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/员工提成结算记录管理/项目成员业绩结算列表Excel")
    @ResponseBody
    public void exportStaffSettlementRefundExcel(HttpServletResponse response, @RequestBody StaffCommissionRefundDto staffCommissionRefundDto) throws ParseException {
        staffCommissionActionService.exportStaffSettlementRefundExcel(response, staffCommissionRefundDto);
    }

    @ApiOperation(value = "项目成员业绩结算退款统计表查询条件回显", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/员工提成结算记录管理/项目成员业绩结算退款统计表查询条件回显")
    @PostMapping("refundSettlementConditionalEcho")
    public ResponseBo<StaffCommissionRefundEchoVo> refundSettlementConditionalEcho(@RequestBody StaffCommissionRefundDto staffCommissionRefundDto) {
        StaffCommissionRefundEchoVo staffCommissionRefundEchoVo = staffCommissionActionService.refundSettlementConditionalEcho(staffCommissionRefundDto);
        return new ResponseBo<>(staffCommissionRefundEchoVo);
    }

    @ApiOperation(value = "项目成员业绩结算退款统计表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/员工提成结算记录管理/项目成员业绩结算退款统计表")
    @PostMapping("getStaffSettlementRefundSummaryList")
    public ResponseBo<StaffCommissionRefundSummaryVo> getStaffSettlementRefundSummaryList(@RequestBody StaffCommissionRefundDto staffCommissionRefundDto) {
        return new ResponseBo<>(staffCommissionActionService.getStaffSettlementRefundSummaryList(staffCommissionRefundDto));
    }

    @ApiOperation(value = "项目成员业绩结算退款统计表导出", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/员工提成结算记录管理/项目成员业绩结算退款统计表导出")
    @PostMapping("downStaffSettlementRefundSummaryList")
    public void downStaffSettlementRefundSummaryList(HttpServletResponse response ,@RequestBody StaffCommissionRefundDto staffCommissionRefundDto) {
        staffCommissionActionService.downStaffSettlementRefundSummaryList(staffCommissionRefundDto,response);
    }
}

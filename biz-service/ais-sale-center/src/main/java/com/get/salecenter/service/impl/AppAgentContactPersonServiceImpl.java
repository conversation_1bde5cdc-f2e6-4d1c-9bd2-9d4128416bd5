package com.get.salecenter.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.service.impl.GetServiceImpl;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.CollectionUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.tool.utils.ObjectUtil;
import com.get.salecenter.dao.sale.AppAgentContactPersonMapper;
import com.get.salecenter.dto.AppAgentContactPersonAddDto;
import com.get.salecenter.dto.AppAgentContactPersonListDto;
import com.get.salecenter.dto.AppAgentContactPersonUpdateDto;
import com.get.salecenter.entity.AppAgentContactPerson;
import com.get.salecenter.enums.ContactPersonTypeEnum;
import com.get.salecenter.service.IAppAgentContactPersonService;
import com.get.salecenter.vo.AppAgentContactPersonVo;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: Hardy
 * @create: 2022/11/17 18:14
 * @verison: 1.0
 * @description:
 */
@Service
public class AppAgentContactPersonServiceImpl extends GetServiceImpl<AppAgentContactPersonMapper, AppAgentContactPerson>
        implements IAppAgentContactPersonService {

    @Resource
    private UtilService utilService;

    @Resource
    private AppAgentContactPersonMapper appAgentContactPersonMapper;

    /**
     * 列表搜索
     *
     * @param appAgentContactPersonListDto
     * @param page
     * @return
     */
    @Override
    public List<AppAgentContactPerson> getAppAgentContactPersons(
            AppAgentContactPersonListDto appAgentContactPersonListDto, Page page) {
        LambdaQueryWrapper<AppAgentContactPerson> wrapper = Wrappers.lambdaQuery();
        if (GeneralTool.isNotEmpty(appAgentContactPersonListDto.getFkAppAgentId())) {
            wrapper.eq(AppAgentContactPerson::getFkAppAgentId, appAgentContactPersonListDto.getFkAppAgentId());
        }

        if (GeneralTool.isNotEmpty(appAgentContactPersonListDto.getFkContactPersonTypeKey())) {
            wrapper.apply("FIND_IN_SET(\"" + appAgentContactPersonListDto.getFkContactPersonTypeKey()
                    + "\",fk_contact_person_type_key)");
        }

        if (GeneralTool.isNotEmpty(appAgentContactPersonListDto.getKeyWord())) {
            wrapper.and(w -> w.like(AppAgentContactPerson::getName, appAgentContactPersonListDto.getKeyWord()).or()
                    .like(AppAgentContactPerson::getEmail, appAgentContactPersonListDto.getKeyWord()));
        }
        IPage<AppAgentContactPerson> pages = this.page(
                GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<AppAgentContactPerson> appAgentContactPersonList = pages.getRecords();
        page.setAll((int) pages.getTotal());
        return appAgentContactPersonList;
    }

    /**
     * 新增
     *
     * @param appAgentContactPersonAddDto
     * @return
     */
    @Override
    public Long addAppAgentContactPerson(AppAgentContactPersonAddDto appAgentContactPersonAddDto) {
        // 新增联系人校验
        this.validateAddAgentContactPerson(appAgentContactPersonAddDto);

        AppAgentContactPerson appAgentContactPerson = BeanCopyUtils.objClone(appAgentContactPersonAddDto,
                AppAgentContactPerson::new);
        utilService.setCreateInfo(appAgentContactPerson);
        int i = this.baseMapper.insert(appAgentContactPerson);
        if (i != 1) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
        return appAgentContactPerson.getId();
    }

    /**
     * 新增联系人校验
     *
     * @param appAgentContactPersonAddDto
     */
    private void validateAddAgentContactPerson(AppAgentContactPersonAddDto appAgentContactPersonAddDto) {
        // 联系人类型 : <类型>,<类型> 可以有多个
        String fkContactPersonTypeKeys = appAgentContactPersonAddDto.getFkContactPersonTypeKey();
        Set<String> fkContactPersonTypeKeySet = Arrays.stream(fkContactPersonTypeKeys.split(","))
                .map(String::trim)
                .collect(Collectors.toSet());
        if (CollectionUtil.isEmpty(fkContactPersonTypeKeySet)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("contact_person_type_empty"));
        }
        // 紧急联系人不能和另外两个新加的联系人类型同时提交
        if (ContactPersonTypeEnum.hasConflictingNewTypes(fkContactPersonTypeKeySet)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("contact_person_type_conflict"));
        }

        // 新增的新联系人类型
        Set<String> addNewTypeSet = fkContactPersonTypeKeySet.stream()
                .filter(ContactPersonTypeEnum::isNewContactPersonType).collect(Collectors.toSet());
        if (CollectionUtil.isEmpty(addNewTypeSet)) {
            return;
        }
        LambdaQueryWrapper<AppAgentContactPerson> appAgentContactPersonLambdaQueryWrapper = new LambdaQueryWrapper<AppAgentContactPerson>()
                .eq(AppAgentContactPerson::getFkAppAgentId, appAgentContactPersonAddDto.getFkAppAgentId());
        // 已添加的新联系人类型
        List<AppAgentContactPerson> usedAppAgentContactPersonList = this.list(appAgentContactPersonLambdaQueryWrapper);
        if (CollectionUtil.isEmpty(usedAppAgentContactPersonList)) {
            return;
        }

        // 判断新的联系人类型之前是否加过
        Set<String> usedNewType = this.getAllNewType(usedAppAgentContactPersonList);
        boolean hasOverlap = addNewTypeSet.stream()
                .anyMatch(usedNewType::contains);
        if (hasOverlap) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("contact_person_type_already_exists"));
        }
        // 是否存在紧急联系人
        boolean hasEmergency = addNewTypeSet.contains(ContactPersonTypeEnum.EMERGENCY.getCode());
        if (!hasEmergency) {
            return;
        }
        // 是否存在相同的联系人
        boolean hasSamePerson = usedAppAgentContactPersonList.stream()
                .filter(userPerson -> ObjectUtil.isNotEmpty(userPerson.getEmail())
                        && ObjectUtil.isNotEmpty(userPerson.getName()))
                .anyMatch(usedPerson -> Objects.equals(appAgentContactPersonAddDto.getEmail(), usedPerson.getEmail())
                        && Objects.equals(appAgentContactPersonAddDto.getName(), usedPerson.getName()));
        if (hasSamePerson) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("contact_person_already_exists"));
        }
    }

    /**
     * 详情
     *
     * @param id
     * @return
     */
    @Override
    public AppAgentContactPersonVo finAppAgentContactPersonById(Long id) {
        AppAgentContactPerson appAgentContactPerson = this.getById(id);
        return BeanCopyUtils.objClone(appAgentContactPerson, AppAgentContactPersonVo::new);
    }

    /**
     * 编辑
     *
     * @param appAgentContactPersonUpdateDto
     * @return
     */
    @Override
    public AppAgentContactPersonVo updateAppAgentContactPerson(
            AppAgentContactPersonUpdateDto appAgentContactPersonUpdateDto) {
        AppAgentContactPerson appAgentContactPerson = BeanCopyUtils.objClone(appAgentContactPersonUpdateDto,
                AppAgentContactPerson::new);
        utilService.setUpdateInfo(appAgentContactPerson);
        this.baseMapper.updateById(appAgentContactPerson);
        return finAppAgentContactPersonById(appAgentContactPersonUpdateDto.getId());
    }

    /**
     * 根据代理id查询当前代理使用过什么新的联系人类型
     *
     * @param fkAppAgentId
     * @return
     */
    @Override
    public Set<String> getUsedNewTypeKeys(Long fkAppAgentId) {
        // 查询代理的所有联系人
        List<AppAgentContactPerson> appAgentContactPeople = this.list(
                Wrappers.<AppAgentContactPerson>lambdaQuery()
                        .eq(AppAgentContactPerson::getFkAppAgentId, fkAppAgentId));

        if (CollUtil.isEmpty(appAgentContactPeople)) {
            return Collections.emptySet();
        }
        // 判断当前代理是否选择过新的联系人类型
        return getAllNewType(appAgentContactPeople);
    }

    /**
     * 根据代理商ID获取联系人列表
     *
     * @param fkAppAgentId 代理商ID
     * @return 联系人VO对象列表
     */
    @Override
    public List<AppAgentContactPersonVo> getByAppAgentId(Long fkAppAgentId) {
        if (ObjectUtils.isNull(fkAppAgentId)) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<AppAgentContactPerson> appAgentContactPersonLambdaQueryWrapper = new LambdaQueryWrapper<AppAgentContactPerson>()
                .eq(AppAgentContactPerson::getFkAppAgentId, fkAppAgentId);
        List<AppAgentContactPerson> appAgentContactPersonList = this.appAgentContactPersonMapper
                .selectList(appAgentContactPersonLambdaQueryWrapper);
        if (CollectionUtils.isEmpty(appAgentContactPersonList)) {
            return new ArrayList<>();
        }
        List<AppAgentContactPersonVo> appAgentContactPersonVos = appAgentContactPersonList.stream()
                .map(appAgentContactPerson -> BeanCopyUtils.objClone(appAgentContactPerson, AppAgentContactPersonVo::new))
                .collect(Collectors.toList());

        return appAgentContactPersonVos;
    }

    /**
     * 获取联系人存在什么新的联系人类型
     *
     * @param appAgentContactPeople
     * @return
     */
    private Set<String> getAllNewType(List<AppAgentContactPerson> appAgentContactPeople) {
        Set<String> usedTypeKeys = appAgentContactPeople.stream()
                .filter(person -> GeneralTool.isNotEmpty(person.getFkContactPersonTypeKey()))
                .flatMap(person -> Arrays.stream(person.getFkContactPersonTypeKey().split(",")))
                .map(String::trim)
                .filter(ContactPersonTypeEnum::isNewContactPersonType)
                .collect(Collectors.toSet());
        return usedTypeKeys;
    }

}

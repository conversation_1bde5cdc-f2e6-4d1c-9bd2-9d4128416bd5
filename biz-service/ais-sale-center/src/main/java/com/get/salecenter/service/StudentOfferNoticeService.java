package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.salecenter.vo.StudentOfferNoticeListVo;
import com.get.salecenter.vo.StudentOfferNoticeListItemVo;
import com.get.salecenter.entity.StudentOfferNotice;
import com.get.core.mybatis.base.BaseService;
import com.get.salecenter.dto.*;
import com.get.salecenter.dto.BatchStudentOfferNoticeDto;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-13
 */
public interface StudentOfferNoticeService extends BaseService<StudentOfferNotice> {

    /**
     * 邮件统计按钮
     * @param emailStatisticsDto
     * @return
     */
    ResponseBo doEmailStatistics(EmailStatisticsDto emailStatisticsDto);

    /**
     * 列表
     * @param studentOfferNoticeListDto
     * @param page
     * @return
     */
    List<StudentOfferNoticeListVo> getStudentOfferNotices(StudentOfferNoticeListDto studentOfferNoticeListDto, Page page);

    /**
     * 批量发送邮件
     * @param batchStudentOfferNoticeDto
     */
    void batchSendStudentOfferNotice(BatchStudentOfferNoticeDto batchStudentOfferNoticeDto);

    /**
     * 批量删除
     * @param batchStudentOfferNoticeDto
     */
    void batchDeleteStudentOfferNotice(BatchStudentOfferNoticeDto batchStudentOfferNoticeDto);

    /**
     * batchStudentOfferNoticeDto
     * @param batchStudentOfferNoticeDto
     */
    void batchMatchStudentOfferNotice(BatchStudentOfferNoticeDto batchStudentOfferNoticeDto);

    /**
     * 修改
     * @param studentOfferNoticeUpdateDto
     * @return
     */
    StudentOfferNoticeListVo updateStudentOfferNotice(StudentOfferNoticeUpdateDto studentOfferNoticeUpdateDto);

    /**
     * 二级列表
     * @param studentOfferNoticeListItemDto
     * @return
     */
    List<StudentOfferNoticeListItemVo> getDatasItem(StudentOfferNoticeListItemDto studentOfferNoticeListItemDto);

    /**
     * 进度查询
     * @return
     */
    Integer getEmailStatisticsStatus();

    /**
     * 一键全发送
     */
    void sendAllWithOneClick() throws InterruptedException;
}

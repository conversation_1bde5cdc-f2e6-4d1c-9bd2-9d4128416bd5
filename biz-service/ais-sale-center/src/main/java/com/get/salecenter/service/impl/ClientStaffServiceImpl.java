package com.get.salecenter.service.impl;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.dao.sale.ClientStaffMapper;
import com.get.salecenter.dto.ClientStaffDto;
import com.get.salecenter.service.ClientStaffService;
import com.get.salecenter.vo.ClientStaffVo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


@Service
public class ClientStaffServiceImpl implements ClientStaffService {

    private final ClientStaffMapper clientStaffMapper;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private UtilService<Object> utilService;

    public ClientStaffServiceImpl(ClientStaffMapper clientStaffMapper) {
        this.clientStaffMapper = clientStaffMapper;
    }

    @Override
    public List<ClientStaffVo> findAllByClientStaff(ClientStaffDto data, Page page) {
        if (GeneralTool.isEmpty(data.getFkClientId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("clientId_is_null"));
        }

        IPage<ClientStaffVo> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<ClientStaffVo> clientStaffVos = clientStaffMapper.findAllByClientStaff(pages, data.getFkClientId());
        page.setAll((int) pages.getTotal());
        if (GeneralTool.isEmpty(clientStaffVos)) {
            //查询不出结果
            return Collections.emptyList();
        }
        
        Set<Long> staffIds = clientStaffVos.stream().map(ClientStaffVo::getFkStaffId).collect(Collectors.toSet());
        Map<Long, String> staffNamesMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(staffIds)) {
            Result<Map<Long, String>> staffNameResult = permissionCenterClient.getStaffNameMap(staffIds);
            if (staffNameResult.isSuccess() && GeneralTool.isNotEmpty(staffNameResult)) {
                staffNamesMap = staffNameResult.getData();
            }
            for (ClientStaffVo clientStaff : clientStaffVos) {
                clientStaff.setFkStaffName(staffNamesMap.get(clientStaff.getFkStaffId()));
            }
        }
        return clientStaffVos;
    }


    @Override
    public List<BaseSelectEntity> getObtainSubordinateEmployeesSelect() {
        List<BaseSelectEntity> resultList = new ArrayList<>();

        // 1. 获取下属员工ID列表
        Result<List<Long>> followerResult = permissionCenterClient.getStaffFollowerIds(SecureUtil.getStaffId());
        if (!followerResult.isSuccess() || GeneralTool.isEmpty(followerResult.getData())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        List<Long> staffFollowerIds = Optional.ofNullable(followerResult.getData()).orElse(Collections.emptyList());
        if (GeneralTool.isEmpty(staffFollowerIds)) {
            return resultList;
        }
        // 2. 批量获取员工姓名
        Set<Long> ids = new HashSet<>(staffFollowerIds);
        Map<Long, String> nameResult = permissionCenterClient.getStaffNamesByIds(ids);

        // 3. 构造 BaseSelectEntity 列表
        for (Long id : staffFollowerIds) {
            BaseSelectEntity baseSelectEntity = new BaseSelectEntity();
            baseSelectEntity.setId(id);
            baseSelectEntity.setName(nameResult.get(id));

            resultList.add(baseSelectEntity);

        }

        return resultList;

    }

    @Override
    public void updateCLientStaff(ClientStaffDto clientStaffDto) {
        if (GeneralTool.isEmpty(clientStaffDto.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage(LocaleMessageUtils.getMessage("id_is_null")));
        }
        if (GeneralTool.isEmpty(clientStaffDto.getFkStaffId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("staffId_is_null"));
        }
        if (GeneralTool.isEmpty(clientStaffDto.getIsActive())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("isActive_is_null"));
        }
        ClientStaffVo clientStaffVo = new ClientStaffVo();
        BeanCopyUtils.copyProperties(clientStaffDto, clientStaffVo);
        utilService.updateUserInfoToEntity(clientStaffVo);
        if (clientStaffVo.getIsActive() == 1) {
            clientStaffVo.setActiveDate(new Date());
        } else {
            clientStaffVo.setUnactiveDate(new Date());
        }
        clientStaffMapper.updateById(clientStaffVo);
    }

    /**
     * @param clientStaffDto
     * 绑定负责人
     */
    @Override
    public void addClientStaff(ClientStaffDto clientStaffDto) {
        if (GeneralTool.isEmpty(clientStaffDto.getFkClientId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("clientId_is_null"));
        }
        if (GeneralTool.isEmpty(clientStaffDto.getFkStaffId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("staffId_is_null"));
        }
        ClientStaffVo clientStaff = BeanCopyUtils.objClone(clientStaffDto, ClientStaffVo::new);
        clientStaff.setIsActive(1);
        clientStaff.setActiveDate(new Date());
        utilService.setCreateInfo(clientStaff);
        //先通过员工id查询是否有绑定关系，如果有且状态不为0，则抛出异常
        ClientStaffVo clientStaffVo = clientStaffMapper.selectByStaffId(clientStaff.getFkStaffId(), clientStaff.getFkClientId());
        if (clientStaffVo == null){
            clientStaffMapper.insert(clientStaff);
        }else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("staff_is_exist"));
        }


    }


}

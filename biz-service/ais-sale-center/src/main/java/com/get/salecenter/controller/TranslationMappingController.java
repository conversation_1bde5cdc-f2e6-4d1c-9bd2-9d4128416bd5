package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.salecenter.vo.TranslationMappingVo;
import com.get.salecenter.dto.TranslationDto;
import com.get.salecenter.service.TranslationMappingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 *  前端接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-04
 */

@Api(tags = "语言翻译管理")
@RestController
@RequestMapping("sale/translationMapping")
public class TranslationMappingController {


    @Resource
    private TranslationMappingService translationMappingService;

    /**
     * @return com.get.common.result.ResponseBo<com.get.institutioncenter.vo.InstitutionProviderVo>
     * @Description: 列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/语言翻译管理/查询翻译")
    @PostMapping("getTranslationMappingDtos")
    public ResponseBo<TranslationMappingVo> getTranslationMappingDtos(@RequestBody TranslationDto translationDto) {
        List<TranslationMappingVo> datas = translationMappingService.getTranslationMappingDtos(translationDto);
        return new ListResponseBo<>(datas);
    }

    /**
     * 保存翻译配置
     *
     * @param translationDtos
     * @return
     * @
     */

    @ApiOperation(value = "保存翻译接口")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/翻译管理/保存翻译")
    @PostMapping("updateTranslations")
    public ResponseBo updateTranslations(@RequestBody @Validated(TranslationDto.Add.class) List<TranslationDto> translationDtos) {
        translationMappingService.updateTranslations(translationDtos);
        return ResponseBo.ok();
    }
}

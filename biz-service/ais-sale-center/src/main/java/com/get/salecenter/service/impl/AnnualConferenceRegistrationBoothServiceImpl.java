package com.get.salecenter.service.impl;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.AnnualConferenceRegistrationBoothMapper;
import com.get.salecenter.dto.AnnualConferenceRegistrationBoothDto;
import com.get.salecenter.entity.AnnualConferenceRegistrationBooth;
import com.get.salecenter.service.IAnnualConferenceRegistrationBoothService;
import com.get.salecenter.vo.AnnualConferenceRegistrationBoothVo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: Sea
 * @create: 2021/4/29 11:11
 * @verison: 1.0
 * @description:
 */
@Service
public class AnnualConferenceRegistrationBoothServiceImpl implements IAnnualConferenceRegistrationBoothService {
    @Resource
    private AnnualConferenceRegistrationBoothMapper annualConferenceRegistrationBoothMapper;
    @Resource
    private UtilService utilService;

    @Override
    public void addAnnualConferenceRegistrationBooth(AnnualConferenceRegistrationBoothDto annualConferenceRegistrationBoothDto) {
        if (GeneralTool.isEmpty(annualConferenceRegistrationBoothDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_is_not_empty"));
        }
        Integer boothIndex = annualConferenceRegistrationBoothDto.getBoothIndex();
        //展位对象有id，表示在修改操作，展位号不需要验证，没有id，表示新增的，需要验证数据库是否已经存在
        if (GeneralTool.isEmpty(annualConferenceRegistrationBoothDto.getId())) {
            if (haveSit(boothIndex)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("the_booth_has_been_ticked"));
            }
        }
        AnnualConferenceRegistrationBooth annualConferenceRegistrationBooth = BeanCopyUtils.objClone(annualConferenceRegistrationBoothDto, AnnualConferenceRegistrationBooth::new);
        utilService.updateUserInfoToEntity(annualConferenceRegistrationBooth);
        annualConferenceRegistrationBoothMapper.insertSelective(annualConferenceRegistrationBooth);
    }

    @Override
    public void deleteByFkid(Long annualConferenceRegistrationId) {
//        Example example = new Example(AnnualConferenceRegistrationBooth.class);
//        example.createCriteria().andEqualTo("fkAnnualConferenceRegistrationId", annualConferenceRegistrationId);
//        annualConferenceRegistrationBoothMapper.deleteByExample(example);
        annualConferenceRegistrationBoothMapper.delete(Wrappers.<AnnualConferenceRegistrationBooth>lambdaQuery().eq(AnnualConferenceRegistrationBooth::getFkAnnualConferenceRegistrationId, annualConferenceRegistrationId));
    }

    @Override
    public List<AnnualConferenceRegistrationBoothVo> getBoothDto(Long annualConferenceRegistrationId) {
//        Example example = new Example(AnnualConferenceRegistrationBooth.class);
//        example.createCriteria().andEqualTo("fkAnnualConferenceRegistrationId", annualConferenceRegistrationId);
//        List<AnnualConferenceRegistrationBooth> booths = annualConferenceRegistrationBoothMapper.selectByExample(example);
        List<AnnualConferenceRegistrationBooth> booths = annualConferenceRegistrationBoothMapper.selectList(Wrappers.<AnnualConferenceRegistrationBooth>lambdaQuery().eq(AnnualConferenceRegistrationBooth::getFkAnnualConferenceRegistrationId, annualConferenceRegistrationId));
        return booths.stream().map(booth -> BeanCopyUtils.objClone(booth, AnnualConferenceRegistrationBoothVo::new)).collect(Collectors.toList());
    }

    @Override
    public List<Integer> getBoothIndex() {
//        List<AnnualConferenceRegistrationBooth> booths = annualConferenceRegistrationBoothMapper.selectAll();
        List<AnnualConferenceRegistrationBooth> booths = annualConferenceRegistrationBoothMapper.selectList(Wrappers.<AnnualConferenceRegistrationBooth>lambdaQuery());
        List<Integer> list = new ArrayList<>();
        booths.forEach(booth -> list.add(booth.getBoothIndex()));
        return list;
    }

    @Override
    public Boolean haveSit(Integer boothIndex) {
//        Example example = new Example(AnnualConferenceRegistrationBooth.class);
//        example.createCriteria().andEqualTo("boothIndex",boothIndex);
//        List<AnnualConferenceRegistrationBooth> booths = annualConferenceRegistrationBoothMapper.selectByExample(example);
        List<AnnualConferenceRegistrationBooth> booths = annualConferenceRegistrationBoothMapper.selectList(Wrappers.<AnnualConferenceRegistrationBooth>lambdaQuery().eq(AnnualConferenceRegistrationBooth::getBoothIndex, boothIndex));

        //如果这个展位号对象已经存在，说明已经被选过，返回true 表示已坐
        if (GeneralTool.isNotEmpty(booths)) {
            return true;
        }
        return false;
    }
}

package com.get.salecenter.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.consts.CacheKeyConstants;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.GetStringUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.redis.cache.GetRedis;
import com.get.core.redis.lock.RedisLock;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.DateUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.tool.utils.RequestContextUtil;
import com.get.financecenter.feign.IFinanceCenterClient;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.institutioncenter.vo.InstitutionCourseVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.CompanyConfigAnalysisVo;
import com.get.permissioncenter.vo.ConfigVo;
import com.get.remindercenter.dto.RemindTaskDto;
import com.get.remindercenter.feign.IReminderCenterClient;
import com.get.salecenter.bo.StudentOfferListQueryBo;
import com.get.salecenter.config.NettyPushConfig;
import com.get.salecenter.dao.sale.*;
import com.get.salecenter.dto.*;
import com.get.salecenter.dto.query.StudentOfferListQueryDto;
import com.get.salecenter.entity.*;
import com.get.salecenter.service.*;
import com.get.salecenter.utils.MyStringUtils;
import com.get.salecenter.utils.VerifyDataPermissionsUtils;
import com.get.salecenter.utils.sale.GetAgentLabelDataUtils;
import com.get.salecenter.utils.sale.VerifyStudentOfferItemUtils;
import com.get.salecenter.vo.*;
import com.get.workflowcenter.feign.IWorkflowCenterClient;
import com.get.workflowcenter.vo.ActRuTaskVo;
import com.get.workflowcenter.vo.HiCommentFeignVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/11/13
 * @TIME: 15:49
 * @Description:
 **/
@Service
@Slf4j
public class StudentOfferServiceImpl extends ServiceImpl<StudentOfferMapper, StudentOffer> implements IStudentOfferService {
    @Resource
    private StudentOfferMapper offerMapper;
    @Resource
    private AsyncReminderService asyncReminderService;
    @Resource
    private VerifyDataPermissionsUtils verifyDataPermissionsUtils;
    @Resource
    private IAgentService agentService;
    @Resource
    private UtilService utilService;
    @Resource
    private IStudentProjectRoleStaffService projectRoleStaffService;
    @Resource
    @Lazy
    private IStudentOfferItemService studentOfferItemService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    @Lazy
    private IDeleteService deleteService;
    @Resource
    private StudentAgentMapper studentAgentMapper;
    @Resource
    private StudentProjectRoleMapper studentProjectRoleMapper;
    @Resource
    private StaffBdCodeMapper staffBdCodeMapper;
    @Resource
    private StudentOfferItemMapper studentOfferItemMapper;
    @Resource
    private StudentOfferItemStepMapper studentOfferItemStepMapper;
    @Resource
    private RStudentOfferItemStepMapper rStudentOfferItemStepMapper;
    @Resource
    private IWorkflowCenterClient workflowCenterClient;
    @Resource
    private StudentMapper studentMapper;
    @Resource
    @Lazy
    private IStudentAgentService studentAgentService;
    @Resource
    private StudentProjectRoleStaffMapper studentProjectRoleStaffMapper;
    @Resource
    private IReminderCenterClient reminderCenterClient;
    @Resource
    private StudentOfferMapper studentOfferMapper;
    @Resource
    private IContactPersonService contactPersonService;
    @Resource
    @Lazy
    private IStudentService studentService;
    @Resource
    private StudentAccommodationMapper studentAccommodationMapper;
    @Resource
    private StudentInsuranceMapper studentInsuranceMapper;
    @Resource
    private GetRedis redisClient;
    @Resource
    private StudentOfferItemPreInstitutionGroupMapper studentOfferItemPreInstitutionGroupMapper;
    @Resource
    private StudentOfferItemPreInstitutionMapper studentOfferItemPreInstitutionMapper;
    @Resource
    private StudentOfferItemPreMajorLevelMapper studentOfferItemPreMajorLevelMapper;
    @Resource
    private RStudentOfferItemStepMapper rOfferItemStepMapper;
    @Resource
    @Lazy
    private IStudentProjectRoleStaffService studentProjectRoleStaffService;
    @Resource
    private ClientMapper clientMapper;
    @Resource
    private ICancelOfferReasonService cancelOfferReasonService;
    @Resource
    private VerifyStudentOfferItemUtils verifyStudentOfferItemUtils;
    @Resource
    private IStudentOfferItemStepService studentOfferItemStepService;
    @Resource
    private IFinanceCenterClient financeCenterClient;

    @Resource
    private NameLabelService nameLabelService;

    @Resource
    private GetAgentLabelDataUtils getAgentLabelDataUtils;

    /**
     * 查询学生学习申请方案列表
     *
     * @param studentOfferDto
     * @param page
     * @return
     * @
     */
    @Override
    public List<StudentOfferVo> getStudentOffer(StudentOfferDto studentOfferDto, Page page) {
        if (GeneralTool.isEmpty(studentOfferDto)) {
            if (GeneralTool.isEmpty(studentOfferDto.getFkStudentId())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("student_id_null"));
            }
        }
        //获取业务下属
        List<Long> staffFollowerIds = new ArrayList<>();
        Result<List<Long>> longResult = permissionCenterClient.getStaffFollowerIds(SecureUtil.getStaffId());
        if (longResult.isSuccess() && GeneralTool.isNotEmpty(longResult.getData())) {
            staffFollowerIds.addAll(longResult.getData());
        }
        if (GeneralTool.isEmpty(staffFollowerIds)) {
            staffFollowerIds = new ArrayList<>();
        }
        staffFollowerIds.add(SecureUtil.getStaffId());
        //获取能看的国家线
        List<Long> visibleCountryIds = offerMapper.getVisibleCountryIdByStaffIds(GetStringUtils.getSqlString(staffFollowerIds));

        //项目成员
        List<StudentProjectRoleStaff> studentProjectRoleStaffs = studentProjectRoleStaffMapper.selectList(Wrappers.<StudentProjectRoleStaff>lambdaQuery()
                .eq(StudentProjectRoleStaff::getFkTableName, TableEnum.SALE_STUDENT_OFFER.key)
                .eq(StudentProjectRoleStaff::getFkStaffId, SecureUtil.getStaffId())
                .eq(StudentProjectRoleStaff::getIsActive, true));

        Set<Long> offerIdset = new HashSet<>();
        if (GeneralTool.isNotEmpty(studentProjectRoleStaffs)) {
            offerIdset = studentProjectRoleStaffs.stream().map(StudentProjectRoleStaff::getFkTableId).collect(Collectors.toSet());
        }

//        Example example = new Example(StudentOffer.class);
//        Example.Criteria criteria = example.createCriteria();
        LambdaQueryWrapper<StudentOffer> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(studentOfferDto.getFkStudentId())) {
//            criteria.andEqualTo("fkStudentId", studentOfferDto.getFkStudentId());
            lambdaQueryWrapper.eq(StudentOffer::getFkStudentId, studentOfferDto.getFkStudentId());
        }
        if (GeneralTool.isNotEmpty(studentOfferDto.getFkAgentId())) {
//            criteria.andEqualTo("fkAgentId", studentOfferDto.getFkAgentId());
            lambdaQueryWrapper.eq(StudentOffer::getFkAgentId, studentOfferDto.getFkAgentId());
        }
        if (GeneralTool.isNotEmpty(studentOfferDto.getFkStaffId())) {
//            criteria.andEqualTo("fkStaffId", studentOfferDto.getFkStaffId());
            lambdaQueryWrapper.eq(StudentOffer::getFkStaffId, studentOfferDto.getFkStaffId());
        }
        if (GeneralTool.isNotEmpty(studentOfferDto.getNum())) {
//            criteria.andLike("num", "%" + studentOfferDto.getNum() + "%");
            lambdaQueryWrapper.like(StudentOffer::getNum, studentOfferDto.getNum());
        }
        if (GeneralTool.isNotEmpty(visibleCountryIds)) {
            lambdaQueryWrapper.in(StudentOffer::getFkAreaCountryId, visibleCountryIds);
        }
        //
        if (GeneralTool.isNotEmpty(offerIdset)) {
            lambdaQueryWrapper.in(StudentOffer::getId, offerIdset);
        }
//        example.orderBy("gmtCreate").desc();
        lambdaQueryWrapper.orderByDesc(StudentOffer::getGmtCreate);
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
        IPage<StudentOffer> iPage = offerMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
        List<StudentOffer> studentOffers = iPage.getRecords();
        page.setAll((int) iPage.getTotal());

        Set<Long> countryIds = studentOffers.stream().map(StudentOffer::getFkAreaCountryId).collect(Collectors.toSet());
        Map<Long, String> countryNames = new HashMap<>();
        Result<Map<Long, String>> result = institutionCenterClient.getCountryNamesByIds(countryIds);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            countryNames = result.getData();
        }


        List<StudentOfferVo> collect = studentOffers.stream().map(studentOffer ->
                BeanCopyUtils.objClone(studentOffer, StudentOfferVo::new)).collect(Collectors.toList());
        if (GeneralTool.isEmpty(collect)) {
            return collect;
        }
        //获取绑定代理联系人邮箱
        //Set<Long> fkContactPersonIds = studentOffers.stream().map(StudentOffer::getFkContactPersonId).collect(Collectors.toSet());
        //Map<Long, String> emailMap = contactPersonService.getContactPersonEmail(fkContactPersonIds);
        Set<Long> ids = collect.stream().map(StudentOfferVo::getId).collect(Collectors.toSet());
        Map<Long, List<StudentRoleAndStaffVo>> staffByTableIds = projectRoleStaffService.getRoleAndStaffByTableIds(ids);
        for (StudentOfferVo studentOfferVo : collect) {
            //设置员工和角色名称
//            List<StudentRoleAndStaffVo> roleAndStaff = projectRoleStaffService.getRoleAndStaffByTableId(studentOfferVo.getId(),TableEnum.SALE_STUDENT_OFFER.key);
            studentOfferVo.setStudentRoleAndStaffList(staffByTableIds.get(studentOfferVo.getId()));
            //设置代理联系人邮箱
            //studentOfferVo.setEmail(emailMap.get(studentOfferVo.getFkContactPersonId()));
        }
        //设置名称
        setName(collect, countryNames);
        return collect;
    }


    @Override
    public List<StudentOfferVo> getStudentOfferNew(StudentOfferDto studentOfferDto, Page page) {
        if (GeneralTool.isEmpty(studentOfferDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        if (GeneralTool.isEmpty(studentOfferDto.getFkStudentId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("student_id_null"));
        }
        //资源客户
        Boolean isBusiness = permissionCenterClient.getIsStaffBusiness(SecureUtil.getStaffId()).getData();
        if (isBusiness) {
            studentOfferDto.setIsClientQuery(true);
        }

        //获取业务下属
        List<Long> staffFollowerIds = new ArrayList<>();
        Result<List<Long>> longResult = permissionCenterClient.getStaffFollowerIds(SecureUtil.getStaffId());
        if (longResult.isSuccess() && GeneralTool.isNotEmpty(longResult.getData())) {
            staffFollowerIds.addAll(longResult.getData());
        }
        if (GeneralTool.isEmpty(staffFollowerIds)) {
            staffFollowerIds = new ArrayList<>();
        }
        staffFollowerIds.add(SecureUtil.getStaffId());
        //是否bd
        Boolean isBd = getIsBd(SecureUtil.getStaffId());

        List<StudentOffer> studentOffers = new ArrayList<>();

        if (!studentOfferDto.getIsClientQuery()) {
            IPage<StudentOffer> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
            studentOffers = offerMapper.getStudentOfferNew(iPage, studentOfferDto, staffFollowerIds,
                    SecureUtil.getCountryIds(),
                    SecureUtil.getStaffInfo().getIsStudentOfferItemFinancialHiding(),
                    SecureUtil.getStaffInfo().getIsStudentAdmin(),
                    SecureUtil.getStaffId(),
                    SecureUtil.getInstitutionIds(),
                    isBd, SecureUtil.getPermissionGroupInstitutionIds(), SecureUtil.getStaffBoundBdIds());
            page.setAll((int) iPage.getTotal());

        } else {
            //不考虑权限
            LambdaQueryWrapper<StudentOffer> lambdaQueryWrapper = getStudentOfferLambdaQueryWrapper(studentOfferDto);
            IPage<StudentOffer> iPage = offerMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
            studentOffers = iPage.getRecords();
            page.setAll((int) iPage.getTotal());
        }


        Set<Long> countryIds = studentOffers.stream().map(StudentOffer::getFkAreaCountryId).collect(Collectors.toSet());
        Map<Long, String> countryNames = new HashMap<>();
        Result<Map<Long, String>> result = institutionCenterClient.getCountryNamesByIds(countryIds);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            countryNames = result.getData();
        }

        List<StudentOfferVo> collect = studentOffers.stream().map(studentOffer ->
                BeanCopyUtils.objClone(studentOffer, StudentOfferVo::new)).collect(Collectors.toList());
        if (GeneralTool.isEmpty(collect)) {
            return collect;
        }
        //获取绑定代理联系人邮箱
//        Set<Long> fkContactPersonIds = studentOffers.stream().map(StudentOffer::getFkContactPersonId).collect(Collectors.toSet());
//        Map<Long, String> emailMap = contactPersonService.getContactPersonEmail(fkContactPersonIds);
        Set<Long> ids = collect.stream().map(StudentOfferVo::getId).collect(Collectors.toSet());
        Map<Long, List<StudentRoleAndStaffVo>> staffByTableIds = projectRoleStaffService.getRoleAndStaffByTableIds(ids);


        for (StudentOfferVo studentOfferVo : collect) {
            //设置员工和角色名称
//            List<StudentRoleAndStaffVo> roleAndStaff = projectRoleStaffService.getRoleAndStaffByTableId(studentOfferVo.getId(),TableEnum.SALE_STUDENT_OFFER.key);
            studentOfferVo.setStudentRoleAndStaffList(staffByTableIds.get(studentOfferVo.getId()));
            //设置代理联系人邮箱
            studentOfferVo.setEmail(studentOfferVo.getAgentContactEmail());
        }
        //设置名称
        setName(collect, countryNames);

        ConfigVo configVo = permissionCenterClient.getConfigByKey(ProjectKeyEnum.FINANCE_OPENING_TIME_COUNT.key).getData();
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
        Date beginOpenTime;
        Date endOpenTime;
        try {
            beginOpenTime = sf.parse(configVo.getValue1());
            endOpenTime = sf.parse(configVo.getValue2());
        } catch (ParseException e) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("DATE_FORMAT_CONVERSION_ERROR"));
        }

        //显示2022年的学生
        if (SecureUtil.getStaffId() == 1247 || SecureUtil.getStaffId() == 1245) {
            for (StudentOfferVo studentOfferVo : collect) {
                if (GeneralTool.isNotEmpty(studentOfferVo.getStudentItemAndStepDtos())) {
                    //TODO 改过 StudentItemAndStepVo
                    for (StudentItemInfoVo studentItemAndStepVo : studentOfferVo.getStudentItemAndStepDtos()) {
                        studentItemAndStepVo.setExistsFollow(studentOfferItemMapper.existsFollow(studentItemAndStepVo.getId(), beginOpenTime, endOpenTime));
                        if (GeneralTool.isNotEmpty(studentItemAndStepVo.getChildItemAndStepDtos())) {
                            //TODO 改过 StudentItemAndStepVo
                            for (StudentItemInfoVo childStudentItemAndStepVo : studentItemAndStepVo.getChildItemAndStepDtos()) {
                                childStudentItemAndStepVo.setExistsFollow(studentOfferItemMapper.existsFollow(childStudentItemAndStepVo.getId(), beginOpenTime, endOpenTime));
                            }
                        }
                    }
                }

            }
        }


        return collect;

    }

    private LambdaQueryWrapper<StudentOffer> getStudentOfferLambdaQueryWrapper(StudentOfferDto studentOfferDto) {
        LambdaQueryWrapper<StudentOffer> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(studentOfferDto.getFkAgentId())) {
            lambdaQueryWrapper.eq(StudentOffer::getFkAgentId, studentOfferDto.getFkAgentId());
        }
        if (GeneralTool.isNotEmpty(studentOfferDto.getFkStudentId())) {
            lambdaQueryWrapper.eq(StudentOffer::getFkStudentId, studentOfferDto.getFkStudentId());
        }
        if (GeneralTool.isNotEmpty(SecureUtil.getCountryIds())) {
            lambdaQueryWrapper.in(StudentOffer::getFkAreaCountryId, SecureUtil.getCountryIds());
        }
        lambdaQueryWrapper.eq(StudentOffer::getStatus, 1);
        lambdaQueryWrapper.orderByDesc(StudentOffer::getGmtCreate);
        return lambdaQueryWrapper;
    }

    @Override
    public Boolean getIsBd(Long fkStaffId) {
        //如果登陆人是bd
        List<StaffBdCode> staffBdCodes = staffBdCodeMapper.selectList(new LambdaQueryWrapper<StaffBdCode>().eq(StaffBdCode::getFkStaffId, fkStaffId));
        Boolean isBd = GeneralTool.isNotEmpty(staffBdCodes);
        return isBd;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchMaterialStatus(Long studentId) {
        StudentOfferDto studentOfferDto = new StudentOfferDto();
        studentOfferDto.setFkStudentId(studentId);
        //获取业务下属
        List<Long> staffFollowerIds = new ArrayList<>();
        Result<List<Long>> longResult = permissionCenterClient.getStaffFollowerIds(SecureUtil.getStaffId());
        if (longResult.isSuccess() && GeneralTool.isNotEmpty(longResult.getData())) {
            staffFollowerIds.addAll(longResult.getData());
        }
        if (GeneralTool.isEmpty(staffFollowerIds)) {
            staffFollowerIds = new ArrayList<>();
        }
        staffFollowerIds.add(SecureUtil.getStaffId());
        //需要加权限
        Boolean isBd = getIsBd(SecureUtil.getStaffId());
        List<StudentOffer> studentOffers = studentOfferMapper.getStudentOfferNew(null, studentOfferDto, staffFollowerIds, SecureUtil.getCountryIds(), SecureUtil.getStaffInfo().getIsStudentOfferItemFinancialHiding(), SecureUtil.getStaffInfo().getIsStudentAdmin(), SecureUtil.getStaffId(), SecureUtil.getInstitutionIds(), isBd, SecureUtil.getPermissionGroupInstitutionIds(), SecureUtil.getStaffBoundBdIds());
        if (GeneralTool.isEmpty(studentOffers)) {
            return;
        }

        Set<Long> offerIds = studentOffers.stream().map(StudentOffer::getId).collect(Collectors.toSet());
        LambdaQueryWrapper<StudentOfferItem> lambdaQueryWrapper = Wrappers.<StudentOfferItem>lambdaQuery().in(StudentOfferItem::getFkStudentOfferId, offerIds)
                .eq(StudentOfferItem::getStatus, 1)
                .eq(StudentOfferItem::getFkStudentOfferItemStepId, 1);
        if (!SecureUtil.getStaffInfo().getIsStudentOfferItemFinancialHiding()) {
            lambdaQueryWrapper.ne(StudentOfferItem::getIsFollowHidden, 1);
        }
        List<StudentOfferItem> studentOfferItems = studentOfferItemMapper.selectList(lambdaQueryWrapper);
        if (GeneralTool.isEmpty(studentOfferItems)) {
            return;
        }

        for (StudentOfferItem studentOfferItem : studentOfferItems) {
            studentOfferItem.setNewAppStatus(0);
            studentOfferItem.setNewAppOptTime(new Date());
            utilService.setUpdateInfo(studentOfferItem);
            studentOfferItemMapper.updateById(studentOfferItem);
        }


    }

    @Override
    public List<BaseSelectEntity> getContactPersonMobileAreaCodeSelect(Long fkAgentId) {
        List<BaseSelectEntity> resoult = contactPersonService.getContactPersonMobileAreaCodeSelect(fkAgentId);
        return resoult;
    }

    @Override
    public List<ContactPersonMobileSelectVo> getContactPersonMobileSelect(Long fkAgentId) {
        List<ContactPersonMobileSelectVo> resoult = contactPersonService.getContactPersonMobileSelect(fkAgentId);
        return resoult;
    }

    @Override
    public List<String> getAgentEmailSelect(Long fkAgentId) {
        return studentOfferItemMapper.getAgentEmailSelect(fkAgentId);
    }

    @Override
    public List<StudentOfferBindingVo> getOfferBindingList(StudentInfoDto studentInfoDto, SearchBean<StudentInfoDto> page) {

        if (GeneralTool.isEmpty(studentInfoDto.getStudentName())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }

        IPage<StudentOfferBindingVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));

        List<StudentOfferBindingVo> studentAgentBindingNewList = studentOfferMapper.getOfferBindingList(iPage, studentInfoDto);

        if (GeneralTool.isEmpty(studentAgentBindingNewList)) {
            return Collections.emptyList();
        }
        page.setAll((int) iPage.getTotal());
        //赋值
        setOfferBindingName(studentAgentBindingNewList);
        return studentAgentBindingNewList;
    }

    @Override
    public Boolean verifyStudentOffer(StudentOfferDto offerVo) {
        StudentOffer studentOffer = studentOfferMapper.verifyStudentOffer(offerVo);
        if (GeneralTool.isEmpty(studentOffer)) {
            return false;
        }
        return true;
    }

    @Override
    public Boolean activationVerifyOffer(Long id) {
        StudentOffer studentOffer = offerMapper.selectById(id);
        StudentOfferDto offerVo = BeanCopyUtils.objClone(studentOffer, StudentOfferDto::new);
        return verifyStudentOffer(offerVo);
    }

    @Override
    public List<StudentStepHistoryVo> getStatusChangeDetails(Long studentId) {
        Long staffId = SecureUtil.getStaffId();
        List<Long> staffFollowerIds = new ArrayList<>();
        //员工id + 业务下属员工ids
        List<Long> staffFollowerIdsResult = permissionCenterClient.getStaffFollowerIds(staffId).getData();
        if (GeneralTool.isNotEmpty(staffFollowerIdsResult)) {
            staffFollowerIds = staffFollowerIdsResult.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        }
        staffFollowerIds.add(staffId);
        Boolean isStudentOfferItemFinancialHiding = SecureUtil.getStaffInfo().getIsStudentOfferItemFinancialHiding();
        Boolean isStudentAdmin = SecureUtil.getStaffInfoByStaffId(staffId).getIsStudentAdmin();
        return studentOfferMapper.getStatusChangeDetails(studentId, staffFollowerIds, isStudentOfferItemFinancialHiding, isStudentAdmin,
                SecureUtil.getPermissionGroupInstitutionIds(),
                SecureUtil.getStaffBoundBdIds());
    }

    private void setOfferBindingName(List<StudentOfferBindingVo> studentAgentBindingNewList) {
        Set<Long> ids = studentAgentBindingNewList.stream().map(StudentOfferBindingVo::getId).collect(Collectors.toSet());
        Set<Long> fkAgentIds = studentAgentBindingNewList.stream().map(StudentOfferBindingVo::getFkAgentId).collect(Collectors.toSet());
        Set<Long> areaCountryIds = studentAgentBindingNewList.stream().map(StudentOfferBindingVo::getFkAreaCountryId).collect(Collectors.toSet());
        Set<Long> studentIds = studentAgentBindingNewList.stream().map(StudentOfferBindingVo::getFkStudentId).collect(Collectors.toSet());
        //根据代理ids获取名称
        //根据员工ids获取姓名
        Set<Long> allStaffIds = permissionCenterClient.getAllStaffIds();
        Map<Long, Agent> agentsMap = agentService.getAgentsByIds(fkAgentIds);
        Map<Long, String> staffNamesByIds = permissionCenterClient.getStaffNamesByIds(allStaffIds);
        Map<Long, String> areaCountryMap = institutionCenterClient.getCountryNamesByIds(areaCountryIds).getData();

        //查询是否属于issue创建
        Set<Long> issueOfferIds = studentOfferMapper.selectIssueCreatItem(ids);

        // 学生是否存在实付佣金绑定数据，需要考虑m_student_offer_item（留学申请计划）
        List<String> keys = Arrays.asList(TableEnum.SALE_STUDENT_OFFER_ITEM.key);
        // key:学生id value:存在的实付佣金绑定数据条数
        Map<Long, Integer> countMap = financeCenterClient.getPaymentFormItemList(keys, studentIds).getData();
        ;

        studentAgentBindingNewList.forEach(e -> {
            if (GeneralTool.isNotEmpty(agentsMap) && agentsMap.containsKey(e.getFkAgentId())) {
                Agent agent = agentsMap.get(e.getFkAgentId());
                //获取国家名称
                String agentName = agent.getName();
                String nameNote = agent.getNameNote();
                if (StringUtils.isNotBlank(nameNote)) {
                    agentName += "（" + nameNote + "）";
                }
                e.setFkAgentName(agentName);
            }
            if (GeneralTool.isNotEmpty(staffNamesByIds) && staffNamesByIds.containsKey(e.getFkStaffId())) {
                e.setBdName(staffNamesByIds.get(e.getFkStaffId()));
            }
            if (GeneralTool.isNotEmpty(areaCountryMap) && areaCountryMap.containsKey(e.getFkAreaCountryId())) {
                e.setFkAreaCountryName(areaCountryMap.get(e.getFkAreaCountryId()));
            }
            if ((GeneralTool.isNotEmpty(issueOfferIds) && issueOfferIds.contains(e.getId()))
                    || e.getGmtCreateUser().contains("issue")) {
                e.setIsIssueCreat(true);
            }
            // 是否存在实付佣金绑定数据
            if (GeneralTool.isNotEmpty(countMap) && countMap.containsKey(e.getFkStudentId())) {
                Integer count = countMap.get(e.getFkStudentId());
                e.setExistPaymentForm(count != null && count > 0);
            }
        });
    }

    private void verifyProjectRole(StudentOfferDto offerVo) {

        Long fkCompanyId = studentMapper.selectById(offerVo.getFkStudentId()).getFkCompanyId();

        //验证默认角色必填
        Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.OFFER_PROJECT_MEMBERS_LIMIT.key, 1).getData();
        String configValue1 = companyConfigMap.get(fkCompanyId);
        if (configValue1.equals("1")) {
            companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.OFFER_PROJECT_MEMBERS_LIMIT.key, 2).getData();
            String configValue2 = companyConfigMap.get(fkCompanyId);
            List<String> iaeRoleKeys = new ArrayList<>(JSON.parseArray(configValue2, String.class));
            if (GeneralTool.isNotEmpty(iaeRoleKeys)) {
                List<ProjectRoleStaffDto> roleStaffVo = offerVo.getRoleStaffVo();
                if (roleStaffVo.isEmpty()) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("project_members_cannot_be_empty"));
                }
                List<Long> roleIds = roleStaffVo.stream().map(ProjectRoleStaffDto::getFkRoleId).collect(Collectors.toList());
                List<StudentProjectRole> studentProjectRoles = studentProjectRoleMapper.selectList(Wrappers.<StudentProjectRole>lambdaQuery().in(StudentProjectRole::getId, roleIds));
                List<String> roleKeys = studentProjectRoles.stream().map(StudentProjectRole::getRoleKey).collect(Collectors.toList());
                List<StudentProjectRole> projectRoles = studentProjectRoleMapper.selectList(Wrappers.<StudentProjectRole>lambdaQuery().in(StudentProjectRole::getRoleKey, iaeRoleKeys));
                String str = projectRoles.stream().map(s -> "【" + s.getRoleName() + "】").collect(Collectors.joining("，"));
                if (!roleKeys.containsAll(iaeRoleKeys)) {
                    throw new GetServiceException(str + LocaleMessageUtils.getMessage("must_bind"));
                }
            }
        }

//        ConfigVo data = permissionCenterClient.getConfigByKey(ProjectKeyEnum.OFFER_PROJECT_MEMBERS_LIMIT.key).getData();
//        SwitchConfigEntity switchConfigEntity = JSON.parseObject(JSON.toJSONString(JSON.parse(data.getValue1())), SwitchConfigEntity.class);
//        EmailConfigEntity emailConfigEntity = JSON.parseObject(JSON.toJSONString(JSON.parse(data.getValue2())), EmailConfigEntity.class);
//        List<String> iaeRoleKeys;
//        boolean isVerify;
//        if (3 == fkCompanyId) {
//            iaeRoleKeys = emailConfigEntity.getIae();
//            isVerify = switchConfigEntity.isIae();
//        } else {
//            iaeRoleKeys = emailConfigEntity.getOther();
//            isVerify = switchConfigEntity.isOther();
//        }
//        if (isVerify) {
//            if (GeneralTool.isNotEmpty(iaeRoleKeys)) {
//                List<ProjectRoleStaffDto> roleStaffVo = offerVo.getRoleStaffVo();
//                if (roleStaffVo.isEmpty()) {
//                    throw new GetServiceException(LocaleMessageUtils.getMessage("project_members_cannot_be_empty"));
//                }
//                List<Long> roleIds = roleStaffVo.stream().map(ProjectRoleStaffDto::getFkRoleId).collect(Collectors.toList());
//                List<StudentProjectRole> studentProjectRoles = studentProjectRoleMapper.selectList(Wrappers.<StudentProjectRole>lambdaQuery().in(StudentProjectRole::getId, roleIds));
//                List<String> roleKeys = studentProjectRoles.stream().map(StudentProjectRole::getRoleKey).collect(Collectors.toList());
//                List<StudentProjectRole> projectRoles = studentProjectRoleMapper.selectList(Wrappers.<StudentProjectRole>lambdaQuery().in(StudentProjectRole::getRoleKey, iaeRoleKeys));
//                String str = projectRoles.stream().map(s->"【"+s.getRoleName()+"】").collect(Collectors.joining("，"));
//                if (!roleKeys.containsAll(iaeRoleKeys)) {
//                    throw new GetServiceException(str + "为必选项目成员，请绑定后再提交");
//                }
//            }
//        }
    }

    private void verifyProjectRoleRemove(StudentOfferListQueryBo studentOfferVo) {
        Long fkCompanyId = studentOfferVo.getFkCompanyId();
        //验证默认角色必填
        Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.OFFER_PROJECT_MEMBERS_LIMIT.key, 1).getData();
        String configValue1 = companyConfigMap.get(fkCompanyId);
        if (configValue1.equals("1")) {
            companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.OFFER_PROJECT_MEMBERS_LIMIT.key, 2).getData();
            String configValue2 = companyConfigMap.get(fkCompanyId);
            List<String> iaeRoleKeys = new ArrayList<>(JSON.parseArray(configValue2, String.class));
            if (GeneralTool.isNotEmpty(iaeRoleKeys)) {

                Long fkStudentProjectRoleId = studentOfferVo.getStudentProjectRoleStaff().getFkStudentProjectRoleId();
                List<StudentProjectRole> studentProjectRoles = studentProjectRoleMapper.selectList(Wrappers.<StudentProjectRole>lambdaQuery().eq(StudentProjectRole::getId, fkStudentProjectRoleId));
                List<String> roleKeys = studentProjectRoles.stream().map(StudentProjectRole::getRoleKey).collect(Collectors.toList());
                List<StudentProjectRole> projectRoles = studentProjectRoleMapper.selectList(Wrappers.<StudentProjectRole>lambdaQuery().in(StudentProjectRole::getRoleKey, iaeRoleKeys));
                String str = projectRoles.stream().map(s -> "【" + s.getRoleName() + "】").collect(Collectors.joining("，"));
                if (iaeRoleKeys.containsAll(roleKeys)) {
                    throw new GetServiceException(str + LocaleMessageUtils.getMessage("as_a_mandatory_project_member"));
                }
            }
        }
        //验证默认角色必填
//        ConfigVo data = permissionCenterClient.getConfigByKey(ProjectKeyEnum.OFFER_PROJECT_MEMBERS_LIMIT.key).getData();
//        SwitchConfigEntity switchConfigEntity = JSON.parseObject(JSON.toJSONString(JSON.parse(data.getValue1())), SwitchConfigEntity.class);
//        EmailConfigEntity emailConfigEntity = JSON.parseObject(JSON.toJSONString(JSON.parse(data.getValue2())), EmailConfigEntity.class);
//        List<String> iaeRoleKeys;
//        boolean isVerify;
//        if (3 == fkCompanyId) {
//            iaeRoleKeys = emailConfigEntity.getIae();
//            isVerify = switchConfigEntity.isIae();
//        } else {
//            iaeRoleKeys = emailConfigEntity.getOther();
//            isVerify = switchConfigEntity.isOther();
//        }
//        if (isVerify) {
//            if (GeneralTool.isNotEmpty(iaeRoleKeys)) {
//
//                Long fkStudentProjectRoleId = studentOfferVo.getStudentProjectRoleStaff().getFkStudentProjectRoleId();
////                List<ProjectRoleStaffDto> roleStaffVo = studentOfferVo.getRoleStaffVo();
////                if (roleStaffVo.isEmpty()) {
////                    throw new GetServiceException(LocaleMessageUtils.getMessage("project_members_cannot_be_empty"));
////                }
////                List<Long> roleIds = roleStaffVo.stream().map(ProjectRoleStaffDto::getFkRoleId).collect(Collectors.toList());
//                List<StudentProjectRole> studentProjectRoles = studentProjectRoleMapper.selectList(Wrappers.<StudentProjectRole>lambdaQuery().eq(StudentProjectRole::getId, fkStudentProjectRoleId));
//                List<String> roleKeys = studentProjectRoles.stream().map(StudentProjectRole::getRoleKey).collect(Collectors.toList());
//                List<StudentProjectRole> projectRoles = studentProjectRoleMapper.selectList(Wrappers.<StudentProjectRole>lambdaQuery().in(StudentProjectRole::getRoleKey, iaeRoleKeys));
//                String str = projectRoles.stream().map(s->"【"+s.getRoleName()+"】").collect(Collectors.joining("，"));
//                if (iaeRoleKeys.containsAll(roleKeys)) {
//                    throw new GetServiceException(str + "为必选项目成员，无法移除该角色");
//                }
//            }
//        }
    }

    private void verifyAddOffer(StudentOfferDto offerVo) {
        List<StudentOffer> offers = studentOfferMapper.selectList(Wrappers.<StudentOffer>lambdaQuery().eq(StudentOffer::getFkAgentId, offerVo.getFkAgentId())
                .eq(StudentOffer::getFkAreaCountryId, offerVo.getFkAreaCountryId())
                .eq(StudentOffer::getFkStudentId, offerVo.getFkStudentId())
                .eq(StudentOffer::getStatus, 1)
                .orderByDesc(StudentOffer::getGmtCreate));
        if (offers.isEmpty()) {
            return;
        }
        Set<Long> offerIds = offers.stream().map(StudentOffer::getId).collect(Collectors.toSet());
        List<ProjectRoleStaffDto> projectRoleStaffDtos = offerVo.getRoleStaffVo();
        //校验项目成员
        if (projectRoleStaffService.verifyProjectRole(projectRoleStaffDtos, offerIds)) {
            return;
        }
        //校验新增信息
        LocalDateTime now = LocalDateTime.now();
        if (offers.stream().anyMatch(f -> f.getGmtCreate().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime().plusHours(1).isAfter(now))) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("the_same_application_plan_has_been_created"));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long addOffer(StudentOfferDto offerVo) {
        if (GeneralTool.isEmpty(offerVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        //校验方案新增信息
        verifyAddOffer(offerVo);
        verifyProjectRole(offerVo);
        StudentOffer offer = BeanCopyUtils.objClone(offerVo, StudentOffer::new);
        Long fkContactPersonId = null;
        if (GeneralTool.isNotEmpty(offerVo.getEmail())) {
            offer.setAgentContactEmail(offerVo.getEmail());
//            String email = offerVo.getEmail();
//            //获取学生所有联系人
//            List<SaleContactPerson> personList = contactPersonService.getContactPersonByFkTableId(TableEnum.SALE_AGENT.key, offerVo.getFkAgentId());
//            //判断修改后的联系人邮箱是否已经存在，不存在则新增数据
//            List<String> emails = personList.stream().map(SaleContactPerson::getEmail).collect(Collectors.toList());
//
//            boolean flag = validateEmail(email, emails);
//
//            if (!flag){
//                //无相同记录
//                String name = email.substring(0, email.indexOf("@"));
//                ContactPersonVo contactPersonVo = new ContactPersonVo();
//                contactPersonVo.setEmail(email);
//                contactPersonVo.setName(name);
//                contactPersonVo.setFkTableName(TableEnum.SALE_AGENT.key);
//                contactPersonVo.setFkTableId(offerVo.getFkAgentId());
//                contactPersonVo.setFkContactPersonTypeKey("CONTACT_AGENT_SALES");
//                fkContactPersonId = contactPersonService.addContactPerson(contactPersonVo);
//            }else {
//                List<SaleContactPerson> data = personList.stream()
//                        .filter(person ->{
//                            List<String> emailList = new ArrayList<>(1);
//                            emailList.add(person.getEmail());
//                            return validateEmail(email, emailList);
//                        }).collect(Collectors.toList());
//                fkContactPersonId = data.get(0).getId();
//            }

//            if (!emails.contains(email)) {
//
//                String name = email.substring(0, email.indexOf("@"));
//                ContactPersonVo contactPersonVo = new ContactPersonVo();
//                contactPersonVo.setEmail(email);
//                contactPersonVo.setName(name);
//                contactPersonVo.setFkTableName(TableEnum.SALE_AGENT.key);
//                contactPersonVo.setFkTableId(offerVo.getFkAgentId());
//                contactPersonVo.setFkContactPersonTypeKey("CONTACT_AGENT_SALES");
//                fkContactPersonId = contactPersonService.addContactPerson(contactPersonVo);
//
//            } else {
//                List<SaleContactPerson> data = personList.stream()
//                        .filter(person -> email.equals(person.getEmail()))
//                        .collect(Collectors.toList());
//                fkContactPersonId = data.get(0).getId();
//            }

        }
//        offer.setFkContactPersonId(fkContactPersonId);
        offer.setStatusWorkflow(0);
        utilService.updateUserInfoToEntity(offer);
        int i = offerMapper.insert(offer);

        //方案编号
        String offerNum = MyStringUtils.getStudentOfferNum(offer.getId());
        offer.setNum(offerNum);
        offerMapper.updateById(offer);
        if (i > 0) {
            //添加角色绑定
            setProjectRoleStaff(offerVo, offer);
        }
        Map<String, String> headerMap = RequestContextUtil.getHeaderMap();
        offerVo.setId(offer.getId());
        asyncReminderService.sendOfferEmail(offerVo, headerMap, SecureUtil.getStaffId());
        return offer.getId();
    }


    private boolean validateEmail(String email, List<String> emails) {
        boolean flag = false;
        String[] updateEmails = email.split("; ");
        List<String> updateEmailList = Arrays.stream(updateEmails).distinct().collect(Collectors.toList());
        if (updateEmails.length != updateEmailList.size()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("repeat_email"));
        }

        for (String _emails : emails) {
            Set<String> tmp = Sets.newHashSet(updateEmailList);
            Set<String> oldEmails = Arrays.stream(_emails.split("; ")).collect(Collectors.toSet());

            if (tmp.size() == oldEmails.size()) {
                tmp.addAll(oldEmails);
                if (tmp.size() > oldEmails.size()) {
                    continue;
                }
                flag = true;
                break;
            }
        }
        return flag;

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public StudentOfferVo updateOffer(StudentOfferDto offerVo) {
        if (GeneralTool.isEmpty(offerVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        verifyProjectRole(offerVo);
        StudentOffer offer = BeanCopyUtils.objClone(offerVo, StudentOffer::new);
        Long fkContactPersonId = null;
        if (GeneralTool.isNotEmpty(offerVo.getEmail())) {
            offer.setAgentContactEmail(offerVo.getEmail());
//            String email = offerVo.getEmail();
//            //获取学生所有联系人
//            List<SaleContactPerson> personList = contactPersonService.getContactPersonByFkTableId(TableEnum.SALE_AGENT.key, offerVo.getFkAgentId());
//            //判断修改后的联系人邮箱是否已经存在，不存在则新增数据
//            List<String> emails = personList.stream().map(SaleContactPerson::getEmail).collect(Collectors.toList());
//
//            boolean flag = validateEmail(email, emails);
//            if (!flag){
//                //无相同记录
//                String name = email.substring(0, email.indexOf("@"));
//                ContactPersonVo contactPersonVo = new ContactPersonVo();
//                contactPersonVo.setEmail(email);
//                contactPersonVo.setName(name);
//                contactPersonVo.setFkTableName(TableEnum.SALE_AGENT.key);
//                contactPersonVo.setFkTableId(offerVo.getFkAgentId());
//                contactPersonVo.setFkContactPersonTypeKey("CONTACT_AGENT_SALES");
//                fkContactPersonId = contactPersonService.addContactPerson(contactPersonVo);
//            }else {
//                    List<SaleContactPerson> data = personList.stream()
//                        .filter(person ->{
//                            List<String> emailList = new ArrayList<>(1);
//                            emailList.add(person.getEmail());
//                            return  validateEmail(email, emailList);
//                        }).collect(Collectors.toList());
//                fkContactPersonId = data.get(0).getId();
//            }
        }
        List<ProjectRoleStaffDto> roleStaffVo = offerVo.getRoleStaffVo();
        projectRoleStaffService.batchProcessorPrs(roleStaffVo, offer.getId(), TableEnum.SALE_STUDENT_OFFER.key);
//        offer.setFkContactPersonId(fkContactPersonId);
        utilService.updateUserInfoToEntity(offer);
        offerMapper.updateById(offer);
        return findOfferById(offer.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateStudentOffer(StudentOfferVo studentOfferVo) {
        StudentOffer studentOffer = BeanCopyUtils.objClone(studentOfferVo, StudentOffer::new);
        if (GeneralTool.isEmpty(studentOffer)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        utilService.updateUserInfoToEntity(studentOffer);
        offerMapper.updateById(studentOffer);
        if (GeneralTool.isNotEmpty(studentOfferVo.getInvalidateOfferItem()) && studentOfferVo.getInvalidateOfferItem()) {
            List<StudentOfferItem> studentOfferItems = studentOfferItemMapper.selectList(Wrappers.<StudentOfferItem>lambdaQuery()
                    .eq(StudentOfferItem::getFkStudentOfferId, studentOffer.getId())
                    .eq(StudentOfferItem::getStatus, 1));
            if (GeneralTool.isEmpty(studentOfferItems)) {
                return true;
            }
            for (StudentOfferItem studentOfferItem : studentOfferItems) {
                studentOfferItem.setStatus(0);
                studentOfferItem.setGmtCreate(new Date());
                studentOfferItem.setGmtCreateUser("admin");
            }
            //作废学习计划
            studentOfferItemService.updateBatchById(studentOfferItems);
        }
        if (studentOffer.getStatus() == 2 || studentOffer.getStatus() == 0) {
            //流程终止需要关闭提醒
//            Example example = new Example(StudentOfferItem.class);
//            example.createCriteria().andEqualTo("fkStudentOfferId",studentOffer.getId());
//            List<StudentOfferItem> studentOfferItems = studentOfferItemMapper.selectByExample(example);

            List<StudentOfferItem> studentOfferItems = studentOfferItemMapper.selectList(Wrappers.<StudentOfferItem>lambdaQuery().eq(StudentOfferItem::getFkStudentOfferId, studentOffer.getId()));
            if (GeneralTool.isEmpty(studentOfferItems)) {
                return true;
            }
            Set<Long> ids = studentOfferItems.stream().map(StudentOfferItem::getId).collect(Collectors.toSet());
            List<RemindTaskDto> remindTaskDtos = new ArrayList<>();
            for (Long id : ids) {
                reminderCenterClient.batchUpdate(remindTaskDtos, TableEnum.SALE_STUDENT_OFFER_ITEM.key, id);
            }
        }

        return true;
    }

    @Override
    public StudentOfferVo findOfferById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        verifyDataPermissionsUtils.verifyByBusinessId(id, VerifyDataPermissionsUtils.OFFER_O);
        StudentOffer offer = offerMapper.selectById(id);
        if (Objects.isNull(offer)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_offer_null"));
        }
        StudentOfferVo studentOfferVo = BeanCopyUtils.objClone(offer, StudentOfferVo::new);
        studentOfferVo.setEnrolledFlag(false);
        Set<Long> agentIds = Collections.singleton(studentOfferVo.getFkAgentId());
        Map<Long, List<AgentLabelVo>> agentLabelMap = getAgentLabelDataUtils.getAgentLabelMap(agentIds).getAgentLabelMap();

//        Example example = new Example(StudentOfferItem.class);
//        example.createCriteria().andEqualTo("fkStudentOfferId", id).andEqualTo("status", 1);
//        List<StudentOfferItem> studentOfferItems = studentOfferItemMapper.selectByExample(example);
        List<StudentOfferItem> studentOfferItems = studentOfferItemMapper.selectList(Wrappers.<StudentOfferItem>lambdaQuery()
                .eq(StudentOfferItem::getFkStudentOfferId, id)
                .eq(StudentOfferItem::getStatus, 1));
        boolean studyPlanFlag = GeneralTool.isNotEmpty(studentOfferItems);
        studentOfferVo.setStudyPlanFlag(studyPlanFlag);
        boolean cancelOfferFlag = false;
        if (GeneralTool.isEmpty(studentOfferItems)) {
            cancelOfferFlag = true;
        } else {
            for (StudentOfferItem studentOfferItem : studentOfferItems) {
                StudentOfferItemStep studentOfferItemStep = studentOfferItemStepMapper.selectById(studentOfferItem.getFkStudentOfferItemStepId());
                if (GeneralTool.isNotEmpty(studentOfferItemStep) && ProjectKeyEnum.STEP_NEW_APP.key.equals(studentOfferItemStep.getStepKey())) {
                    cancelOfferFlag = true;
                } else {
                    cancelOfferFlag = false;
                    break;
                }
            }
        }

        Student student = studentMapper.selectById(offer.getFkStudentId());
        studentOfferVo.setStudentName(student.getName());
        studentOfferVo.setFkCompanyId(student.getFkCompanyId());
        studentOfferVo.setCancelOfferFlag(cancelOfferFlag);
        studentOfferVo.setAgentLabelVos(agentLabelMap.get(studentOfferVo.getFkAgentId()));

        setAgentAndBdName(studentOfferVo);
        //设置员工和角色名称
        List<StudentRoleAndStaffVo> roleAndStaff = projectRoleStaffService.getRoleAndStaffByTableId(studentOfferVo.getId(), TableEnum.SALE_STUDENT_OFFER.key);
        studentOfferVo.setStudentRoleAndStaffList(roleAndStaff);
        //设置国家名称
//        String countryName = institutionCenterClient.getCountryNameById(studentOfferVo.getFkAreaCountryId());
        Result<String> result = institutionCenterClient.getCountryNameById(studentOfferVo.getFkAreaCountryId());
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            studentOfferVo.setFkAreaCountryName(result.getData());
        }


        List<Long> studentOfferIds = new ArrayList<>(1);
        studentOfferIds.add(studentOfferVo.getId());
        Map<Long, ActRuTaskVo> actRuTaskDtoMap = new HashMap<>();
        Map<String, List<Long>> businessIdsWithProcdefKey = new HashMap<>();
        businessIdsWithProcdefKey.put(TableEnum.SALE_STUDENT_OFFER.key, studentOfferIds);
        Result<Map<Long, ActRuTaskVo>> actRuTaskDtoMapResult = workflowCenterClient.getActRuTaskDtosByBusinessKey(businessIdsWithProcdefKey);
        if (!actRuTaskDtoMapResult.isSuccess()) {
            throw new GetServiceException(actRuTaskDtoMapResult.getMessage());
        } else {
            actRuTaskDtoMap = actRuTaskDtoMapResult.getData();
        }
        //流程对象
        ActRuTaskVo actRuTaskVo = actRuTaskDtoMap.get(studentOfferVo.getId());
        //正在进行的任务id
        if (GeneralTool.isNotEmpty(actRuTaskVo.getId())) {
            studentOfferVo.setTaskId(Long.valueOf(actRuTaskVo.getId()));
        }
        //流程实例id
        if (GeneralTool.isNotEmpty(actRuTaskVo.getProcInstId())) {
            studentOfferVo.setProcInstId(Long.valueOf(actRuTaskVo.getProcInstId()));
        }
        //任务版本
        if (GeneralTool.isNotEmpty(actRuTaskVo.getTaskVersion())) {
            studentOfferVo.setTaskVersion(actRuTaskVo.getTaskVersion());
        }
        if (GeneralTool.isNotEmpty(actRuTaskVo.getProcInstId())) {
            Result<Object> objectResult = workflowCenterClient.getVariableByHisInstanceAndName(actRuTaskVo.getProcInstId(), "buttonType");
            if (objectResult.isSuccess() && GeneralTool.isNotEmpty(objectResult.getData())) {
                try {
                    String buttonTypeString = (String) objectResult.getData();
                    String workflowName = "0".equals(buttonTypeString) ? "申请终止流程" : "申请结案流程";
                    studentOfferVo.setWorkflowName(workflowName);
                    if (GeneralTool.isNotEmpty(buttonTypeString)) {
                        studentOfferVo.setButtonType(Integer.valueOf(buttonTypeString));
                    }
                } catch (Exception e) {
                    studentOfferVo.setButtonType(null);
                }
            }

        }

        //获取是否有申请方案
//        Example example1 = new Example(StudentOffer.class);
//        example1.createCriteria().andEqualTo("fkStudentId", studentOfferVo.getFkStudentId()).andEqualTo("status", 1);
//        List<StudentOffer> studentOffers = offerMapper.selectByExample(example1);
        List<StudentOffer> studentOffers = offerMapper.selectList(Wrappers.<StudentOffer>lambdaQuery()
                .eq(StudentOffer::getFkStudentId, studentOfferVo.getFkStudentId())
                .eq(StudentOffer::getStatus, 1));
        if (GeneralTool.isNotEmpty(studentOffers)) {
            studentOfferVo.setStudentOfferFlag(true);
        } else {
            studentOfferVo.setStudentOfferFlag(false);
        }

        //获取绑定的项目成员
        List<BaseSelectEntity> studentOfferSelect = offerMapper.getStudentOfferSelect(studentOfferVo.getFkStudentId());
        if (GeneralTool.isNotEmpty(studentOfferSelect)) {
            List<Long> offerIds = studentOfferSelect.stream().map(BaseSelectEntity::getId).collect(Collectors.toList());
            List<StudentProjectRoleStaffVo> studentProjectRoleStaffVos = studentProjectRoleStaffMapper.selectProjectStaff(TableEnum.SALE_STUDENT_OFFER.key, offerIds);
            for (StudentProjectRoleStaffVo studentProjectRoleStaffVo : studentProjectRoleStaffVos) {
                String staffIdStr1 = studentProjectRoleStaffVo.getStaffIdStr();
                String[] staffIdStr = new String[100];
                ;
                if (staffIdStr1 != null) {
                    Pattern pattern = Pattern.compile(",");
                    Matcher matcher = pattern.matcher(staffIdStr1);
                    if (matcher.find()) {
                        staffIdStr = studentProjectRoleStaffVo.getStaffIdStr().split(",");
                    } else {
                        staffIdStr[0] = staffIdStr1;
                    }
                }
                Set<Long> staffIds = Arrays.stream(staffIdStr)
                        .map(str -> Optional.ofNullable(str).map(Long::valueOf).orElse(null))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());
//                Map<Long, String> staffNameMap = permissionCenterClient.getStaffNameMap(staffIds);
                Result<Map<Long, String>> staffResult = permissionCenterClient.getStaffNameMap(staffIds);
                if (staffResult.isSuccess() && staffResult.getData() != null) {
                    Map<Long, String> staffNameMap = staffResult.getData();
                    studentProjectRoleStaffVo.setStaffName(StringUtils.join(staffNameMap.values(), ","));
                }
            }
            studentOfferVo.setStudentProjectRoleStaffDtos(studentProjectRoleStaffVos);
        }

        //获取绑定代理联系人邮箱
        if (GeneralTool.isNotEmpty(studentOfferVo.getAgentContactEmail())) {
            Set<String> agentEmails = Collections.singleton(studentOfferVo.getAgentContactEmail());
            Map<String, List<AgentLabelVo>> agentEamailLabelMap = getAgentLabelDataUtils.getAgentEmailLabelMap(agentEmails).getAgentEmailLabelMap();
            studentOfferVo.setEmail(studentOfferVo.getAgentContactEmail());
            studentOfferVo.setAgentEmailLabelVos(agentEamailLabelMap.get(studentOfferVo.getAgentContactEmail()));
        }
        Result<HiCommentFeignVo> hiCommentFeignDtoResult = workflowCenterClient.getHiComment(id, TableEnum.SALE_STUDENT_OFFER.key);
        if (hiCommentFeignDtoResult.isSuccess() && GeneralTool.isNotEmpty(hiCommentFeignDtoResult.getData())) {
            studentOfferVo.setAgreeButtonType(hiCommentFeignDtoResult.getData().getAgreeButtonType());
            studentOfferVo.setRefuseButtonType(hiCommentFeignDtoResult.getData().getRefuseButtonType());
        }
        //判断是否限制项目成员编辑
        Set<Long> offerIds = new HashSet<>();
        offerIds.add(studentOfferVo.getId());
        Set<Long> limitOfferIds = getProjectLimitConfigKey(offerIds, studentOfferVo.getFkCompanyId());
        if (GeneralTool.isNotEmpty(limitOfferIds)) {
            studentOfferVo.setIsProjectMembersLimit(true);
        }

        if (GeneralTool.isNotEmpty(studentOfferVo.getFkCancelOfferReasonId())) {
            //设置终止作废原因
            CancelOfferReason cancelOfferReason = cancelOfferReasonService.getById(studentOfferVo.getFkCancelOfferReasonId());
            if (GeneralTool.isNotEmpty(cancelOfferReason)) {
                studentOfferVo.setFkCancelOfferReasonName(cancelOfferReason.getReasonName());
            }
        }
        return studentOfferVo;
    }


    @Override
    public List<BaseSelectEntity> getContactPersonEmailSelect(Long fkAgentId) {
        List<BaseSelectEntity> resoult = contactPersonService.getContactPersonEmailSelect(fkAgentId);
        return resoult;
    }


    /**
     * @Description: feign调用 详情
     * @Author: Jerry
     * @Date:9:58 2021/11/4
     */
    @Override
    public StudentOfferVo getStudentOfferDetail(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        StudentOffer offer = offerMapper.selectById(id);
        StudentOfferVo studentOfferVo = BeanCopyUtils.objClone(offer, StudentOfferVo::new);
        List<StudentProjectRoleStaffVo> projectRoleStaffDtos = studentProjectRoleStaffMapper.selectProjectStaffById(TableEnum.SALE_STUDENT_OFFER.key, id);
        studentOfferVo.setStudentProjectRoleStaffDtos(projectRoleStaffDtos);
        return studentOfferVo;
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        deleteService.deleteValidateOffer(id);
        int delete = offerMapper.deleteById(id);
        if (delete <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
    }

    @Override
    public List<Long> getOfferIdByStudentId(Long studentId) {
        if (GeneralTool.isEmpty(studentId)) {
            return null;
        }
//        Example example = new Example(StudentOffer.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkStudentId", studentId);
//        List<StudentOffer> studentOffers = offerMapper.selectByExample(example);
        //放开作废的出来（绑定项目成员）
        List<StudentOffer> studentOffers = offerMapper.selectList(Wrappers.<StudentOffer>lambdaQuery()
                .eq(StudentOffer::getFkStudentId, studentId));
        if (GeneralTool.isEmpty(studentOffers)) {
            return null;
        }
        return studentOffers.stream().map(StudentOffer::getId).collect(Collectors.toList());
    }

    @Override
    public List<BaseSelectEntity> getStudentOfferSelect(Long studentId, String typeKey, Long fkCompanyId) {
        if (TableEnum.SALE_STUDENT_OFFER.key.equals(typeKey)) {
            List<Long> staffFollowerIds = new ArrayList<>();
            //员工id + 业务下属员工ids
            if (GeneralTool.isNotEmpty(permissionCenterClient.getStaffFollowerIds(SecureUtil.getStaffId()).getData())) {
                staffFollowerIds = permissionCenterClient.getStaffFollowerIds(SecureUtil.getStaffId()).getData().stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
            }
            staffFollowerIds.add(SecureUtil.getStaffId());
            List<BaseSelectEntity> studentOfferSelectNew = offerMapper.getStudentOfferSelectNew(studentId, staffFollowerIds, SecureUtil.getStaffInfo().getIsStudentAdmin(),
                    SecureUtil.getPermissionGroupInstitutionIds(),
                    SecureUtil.getStaffBoundBdIds());
            //iae公司项目成员绑定需要限制
            if (GeneralTool.isNotEmpty(studentOfferSelectNew) && GeneralTool.isNotEmpty(fkCompanyId)) {
                Set<Long> offerIds = studentOfferSelectNew.stream().map(BaseSelectEntity::getId).collect(Collectors.toSet());
                Set<Long> limitOfferIds = getProjectLimitConfigKey(offerIds, fkCompanyId);
                for (BaseSelectEntity offer : studentOfferSelectNew) {
                    if (GeneralTool.isNotEmpty(limitOfferIds) && limitOfferIds.contains(offer.getId())) {
                        offer.setStatus(1L);
                    } else {
                        offer.setStatus(0L);
                    }
                }
            }
            return studentOfferSelectNew;
        } else if (TableEnum.SALE_STUDENT_ACCOMMODATION.key.equals(typeKey)) {
            return studentAccommodationMapper.getStudentAccommodationSelect(studentId);
        } else if (TableEnum.SALE_STUDENT_INSURANCE.key.equals(typeKey)) {
            return studentInsuranceMapper.getStudentInsuranceSelect(studentId);
        }
        return null;
    }

    @Override
    public String getOfferNumById(Long offerId) {
        if (GeneralTool.isEmpty(offerId)) {
            return null;
        }
        StudentOffer offer = offerMapper.selectById(offerId);
        if (GeneralTool.isEmpty(offer)) {
            return null;
        }
        return offer.getNum();
    }

    @Override
    public Map<Long, String> getOfferNumByIds(Set<Long> offerIds) {
        if (GeneralTool.isEmpty(offerIds)) {
            return new HashMap<>();
        }
        List<StudentOffer> studentOffers = offerMapper.selectBatchIds(offerIds);
        if (GeneralTool.isEmpty(studentOffers)) {
            return null;
        }
        Map<Long, String> map = new HashMap<>();
        for (StudentOffer studentOffer : studentOffers) {
            map.put(studentOffer.getId(), studentOffer.getNum());
        }
        return map;
    }

    /**
     * 申请方案工作流详情
     *
     * @param id
     * @return
     */
    @Override
    public StudentOfferVo getStudentOfferForWorkFlow(Long id) {
        StudentOffer studentOffer = getById(id);
        if (GeneralTool.isEmpty(studentOffer)) {
            return null;
        }
        StudentOfferVo studentOfferVo = BeanCopyUtils.objClone(studentOffer, StudentOfferVo::new);
        assert studentOfferVo != null;
        if (GeneralTool.isNotEmpty(studentOfferVo.getFkCancelOfferReasonId())) {
            CancelOfferReason cancelOfferReason = cancelOfferReasonService.getById(studentOfferVo.getFkCancelOfferReasonId());
            studentOfferVo.setFkCancelOfferReasonName(cancelOfferReason.getReasonName());
        }

        if (GeneralTool.isNotEmpty(studentOfferVo.getFkStudentId())) {
            Student student = studentMapper.selectById(studentOfferVo.getFkStudentId());
            if (GeneralTool.isNotEmpty(student)) {
                String nameZh = student.getName();
                String nameEn = "";
                if (GeneralTool.isNotEmpty(student.getLastName()) || GeneralTool.isNotEmpty(student.getFirstName())) {
                    nameEn = "(" + (GeneralTool.isNotEmpty(student.getLastName()) ? student.getLastName() : "")
                            + (GeneralTool.isNotEmpty(student.getFirstName()) ? student.getFirstName() : "") + ")";
                }
                studentOfferVo.setStudentName(nameZh + nameEn);
            }
        }

        if (GeneralTool.isNotEmpty(studentOfferVo.getFkAreaCountryId())) {
            String countryName = institutionCenterClient.getCountryChnNameById(studentOfferVo.getFkAreaCountryId()).getData();
            studentOfferVo.setFkAreaCountryName(countryName);
        }

        List<StudentOfferItem> studentOfferItems = studentOfferItemMapper.selectList(Wrappers.<StudentOfferItem>lambdaQuery()
                .eq(StudentOfferItem::getFkStudentOfferId, id)
                .eq(StudentOfferItem::getStatus, 1));
        if (GeneralTool.isNotEmpty(studentOfferItems)) {
            Set<Long> institutionIds = studentOfferItems.stream().map(StudentOfferItem::getFkInstitutionId).filter(Objects::nonNull).collect(Collectors.toSet());
            if (GeneralTool.isNotEmpty(institutionIds)) {
                Map<Long, String> institutionNameMap = institutionCenterClient.getInstitutionNamesByIds(institutionIds).getData();
                if (GeneralTool.isNotEmpty(institutionNameMap)) {
                    List<String> institutionNames = new ArrayList<>(institutionNameMap.values());
                    String institutionName = String.join(";", institutionNames);
                    studentOfferVo.setInstitutionFullName(institutionName);
                }
            }
        }
        return studentOfferVo;
    }

    /**
     * 撤销学习申请方案流程
     *
     * @param id
     */
    @Override
    public void stopStudentOfferWorkFlow(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }

        Long staffId = workflowCenterClient.getStartUserIdByIdAndProcdefKey(id, TableEnum.SALE_STUDENT_OFFER.key).getData();
        if (!SecureUtil.getStaffId().equals(staffId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("login_id_ne_start_id"));
        }

        Result<Boolean> result = workflowCenterClient.stopStudentOfferWorkFlow(id);
        if (!result.isSuccess() || GeneralTool.isEmpty(result.getData())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("process_revocation_failed"));
        }
        StudentOffer studentOffer = getById(id);
        //变回待发起
        studentOffer.setStatusWorkflow(0);
        utilService.setUpdateInfo(studentOffer);
        boolean b = updateById(studentOffer);
        if (!b) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
//        Map<Long, ActRuTaskVo> actRuTaskDtoMap = new HashMap<>();
//        Map<String, List<Long>> businessIdsWithProcdefKey = new HashMap<>();
//        List<Long> ids = Lists.newArrayList(id);
//        businessIdsWithProcdefKey.put(TableEnum.SALE_STUDENT_OFFER.key, ids);
//        Result<Map<Long, ActRuTaskVo>> actRuTaskDtoMapResult = workflowCenterClient.getActRuTaskDtosByBusinessKey(businessIdsWithProcdefKey);
//        if (!actRuTaskDtoMapResult.isSuccess()) {
//            throw new GetServiceException(actRuTaskDtoMapResult.getMessage());
//        } else {
//            actRuTaskDtoMap = actRuTaskDtoMapResult.getData();
//        }
//        //流程对象
//        ActRuTaskVo actRuTaskDto = actRuTaskDtoMap.get(id);
//        if (GeneralTool.isNotEmpty(actRuTaskDto.getProcInstId())) {
//            Result<Object> objectResult = workflowCenterClient.getVariableByHisInstanceAndName(actRuTaskDto.getProcInstId(), "buttonType");
//            if (objectResult.isSuccess() && GeneralTool.isNotEmpty(objectResult.getData())) {
//                String buttonTypeString = (String) objectResult.getData();
//                if ("0".equals(buttonTypeString)){
//                    //作废流程 需恢复学习计划状态
//                }
//            }
//        }

    }


    @Override
    public String getAgentNameByOfferId(Long offerId) {
        if (GeneralTool.isEmpty(offerId)) {
            return null;
        }
        return offerMapper.getAgentNameByOfferId(offerId);
    }

    //查询代理名称
    @Override
    public Map<Long, String> getAgentNameByOfferIds(Set<Long> offerIds) {
        if (GeneralTool.isEmpty(offerIds)) {
            return null;
        }
        List<StudentOfferVo> agentNameByOfferIds = offerMapper.getAgentNameByOfferIds(offerIds);
        Map<Long, String> hashMap = new HashMap<>();
        if (GeneralTool.isEmpty(agentNameByOfferIds)) {
            return hashMap;
        }
        for (StudentOfferVo agentNameByOfferId : agentNameByOfferIds) {
            hashMap.put(agentNameByOfferId.getId(), agentNameByOfferId.getFkAgentName());
        }
        return hashMap;
    }


    @Override
    public List<Long> getStudentIdByStaffId(Long staffId) {
        if (GeneralTool.isEmpty(staffId)) {
            return null;
        }
//        Example example = new Example(StudentOffer.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkStaffId", staffId);
//        List<StudentOffer> studentOffers = offerMapper.selectByExample(example);
        List<StudentOffer> studentOffers = offerMapper.selectList(Wrappers.<StudentOffer>lambdaQuery()
                .eq(StudentOffer::getFkStaffId, staffId));
        return studentOffers.stream().map(StudentOffer::getFkStudentId).collect(Collectors.toList());
    }

    @Override
    public List<Long> getStudentIdByStaffNameOrCode(String staffNameOrCode) {
        if (GeneralTool.isEmpty(staffNameOrCode)) {
            return null;
        }
        //根据人员姓名关键字模糊查询人员
        List<Long> staffIdsByNameKeyList = null;
        Result<List<Long>> result = permissionCenterClient.getStaffIdsByNameKeyOrEnNameKey(staffNameOrCode);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            staffIdsByNameKeyList = result.getData();
        }
        //根据BD编号模糊查询人员
//        Example example = new Example(StaffBdCode.class);
//        example.createCriteria().andLike("bdCode", "%" + staffNameOrCode + "%");
//        List<StaffBdCode> staffBdCodeList = staffBdCodeMapper.selectByExample(example);

        List<StaffBdCode> staffBdCodeList = staffBdCodeMapper.selectList(Wrappers.<StaffBdCode>lambdaQuery()
                .like(StaffBdCode::getBdCode, staffNameOrCode));
        if (GeneralTool.isEmpty(staffIdsByNameKeyList) && GeneralTool.isEmpty(staffBdCodeList)) {
            return null;
        }
        Set<Long> staffIds = new HashSet<>();
        //获取符合条件的所有人员
        if (GeneralTool.isNotEmpty(staffIdsByNameKeyList)) {
            staffIds.addAll(staffIdsByNameKeyList);
        }
        if (GeneralTool.isNotEmpty(staffBdCodeList)) {
            Set<Long> fkStaffIds = staffBdCodeList.stream().map(StaffBdCode::getFkStaffId).collect(Collectors.toSet());
            if (GeneralTool.isNotEmpty(fkStaffIds)) {
                staffIds.addAll(fkStaffIds);
            }
        }
//        example.clear();
//        example = new Example(StudentOffer.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andIn("fkStaffId", staffIds);
//        criteria.andEqualTo("status", 1);
//        List<StudentOffer> studentOffers = offerMapper.selectByExample(example);
        List<StudentOffer> studentOffers = offerMapper.selectList(Wrappers.<StudentOffer>lambdaQuery()
                .in(StudentOffer::getFkStaffId, staffIds).eq(StudentOffer::getStatus, 1));
        return studentOffers.stream().map(StudentOffer::getFkStudentId).collect(Collectors.toList());
    }


    @Override
    public Set<Long> getStaffIdByStaffNameOrEnName(String staffNameOrEnName) {
        if (GeneralTool.isEmpty(staffNameOrEnName)) {
            return null;
        }
        //根据人员姓名或者英文名关键字模糊查询人员
        List<Long> staffIdsByNameKeyList = new ArrayList<>();
        Result<List<Long>> result = permissionCenterClient.getStaffIdsByNameKeyOrEnNameKey(staffNameOrEnName);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            staffIdsByNameKeyList.addAll(result.getData());
        }
        if (GeneralTool.isEmpty(staffIdsByNameKeyList)) {
            return null;
        }
        Set<Long> staffIds = new HashSet<>(staffIdsByNameKeyList);
        return staffIds;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unableOffer(Long offerId, Long status) {
        Set<Long> studentOfferItemByFkStudentOfferIds = studentOfferItemService.getStudentOfferItemByFkStudentOfferId(offerId);
        //若申请方案的学习计划以及子计划绑定了“有效”应收应付计划，则不能作废
        Boolean hasPlan = false;
        if (GeneralTool.isNotEmpty(studentOfferItemByFkStudentOfferIds)) {
            hasPlan = studentOfferItemMapper.hasPlansByItemIds(studentOfferItemByFkStudentOfferIds);
        }

        if (hasPlan && status.equals(0L)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("student_offer_is_not_close"));
        }

        StudentOffer offer = new StudentOffer();
        offer.setId(offerId);
        offer.setStatus(Math.toIntExact(status));
        offerMapper.updateById(offer);
        projectRoleStaffService.unableProjectRoleStaff(offerId, status);
//        studentOfferItemService.unableOfferItemByOfferId(offerId, status);

        //关闭所有的学习计划
//        Set<Long> studentOfferItemByFkStudentOfferId = studentOfferItemService.getStudentOfferItemByFkStudentOfferId(offerId);
        for (Long aLong : studentOfferItemByFkStudentOfferIds) {
            studentOfferItemService.unableOfferItem(aLong, status);
        }
    }

    /**
     * @return
     * @Description：获取学生绑定的代理列表
     * @Param
     * @Date 12:30 2021/4/25
     * <AUTHOR>
     */
    @Override
    public List<AgentAndAgentLabelVo> getAgentListByStudentId(Long studentId) throws GetServiceException {
        if (GeneralTool.isEmpty(studentId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("student_id_null"));
        }

//        //是否进行权限控制
//        Map<Long, Integer> companySettlementConfigInfoMap = permissionCenterClient.getCompanySettlementConfigInfoMap(ProjectKeyEnum.STUDENT_AGENT_BINDING_LIST_LIMIT.key);
//        Integer value = companySettlementConfigInfoMap.get(SecureUtil.getFkCompanyId());
//
//        //获取业务下属
//        Long staffId = SecureUtil.getStaffId();
//        //员工id + 业务下属员工ids
//        List<Long> staffFollowerIds = permissionCenterClient.getStaffFollowerIds(staffId).getData();
//        staffFollowerIds.add(staffId);

        List<AgentAndAgentLabelVo> agentLabelVos = studentAgentMapper.getAgentListByStudentId(studentId);
        if (GeneralTool.isEmpty(agentLabelVos)) {
            return Collections.emptyList();
        }
        Set<Long> fkAgentIds = agentLabelVos.stream().map(AgentAndAgentLabelVo::getId).collect(Collectors.toSet());
        Map<Long, List<AgentLabelVo>> agentLabelMap = getAgentLabelDataUtils.getAgentLabelMap(fkAgentIds).getAgentLabelMap();
        agentLabelVos.forEach(agentLabelVo -> {
           getAgentLabelDataUtils.setAgentLabelVosByLabelMap(agentLabelVo,agentLabelMap, agentLabelVo.getId(), AgentAndAgentLabelVo::setAgentLabelVos);
        });

        return agentLabelVos;
    }

    /**
     * 角色员工下拉(角色联动)
     *
     * @Date 16:05 2021/7/6
     * <AUTHOR>
     */
    @Override
    public List<BaseSelectEntity> getRoleStaffByRoleSelect(Long roleId, Long fkAreaCountryId) {
        StudentProjectRole studentProjectRole = studentProjectRoleMapper.selectById(roleId);
        if (GeneralTool.isEmpty(studentProjectRole)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        String[] departmentIds = studentProjectRole.getDepartmentNum().split(",");
        List<Long> departmentIdsList = new ArrayList<>();
        for (String departmentIdStr : departmentIds) {
            Long departmentId = Long.valueOf(departmentIdStr);
            departmentIdsList.add(departmentId);
        }
//        return permissionCenterClient.getStaffByDepartmentIds(departmentIdsList);
        //根据国家id查询国家编号
        String countryNum = "";
        if (GeneralTool.isNotEmpty(fkAreaCountryId)) {
            HashSet<Long> fkAreaCountryIds = new HashSet<>();
            fkAreaCountryIds.add(fkAreaCountryId);
            Map<Long, String> countryNumMap = institutionCenterClient.getCountryNumByCountryIds(fkAreaCountryIds).getData();
            if (GeneralTool.isNotEmpty(countryNumMap)) {
                countryNum = countryNumMap.get(fkAreaCountryId);
            }
        }
        Result<List<BaseSelectEntity>> result = permissionCenterClient.getStaffByDepartmentIdsAndCountryNum(departmentIdsList, countryNum);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            return result.getData();
        }
        return Lists.newArrayList();
    }

    /**
     * 激活方案
     *
     * @Date 12:29 2021/8/13
     * <AUTHOR>
     */
    @Override
    public void activationOffer(Long id) {
//        Example example = new Example(StudentOffer.class);
//        example.createCriteria().andEqualTo("id", id);
//        List<StudentOffer> studentOffers = offerMapper.selectByExample(example);
        StudentOffer studentOffer = offerMapper.selectById(id);
        if (GeneralTool.isEmpty(studentOffer)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        studentOffer.setStatus(1);
        utilService.updateUserInfoToEntity(studentOffer);
        offerMapper.updateById(studentOffer);
    }


    /**
     * @Description: 发起学生申请方案流程
     * @Author: Jerry
     * @Date:18:00 2021/11/3
     */
    @Override
    @RedisLock(value = CacheKeyConstants.STUDENT_OFFER_LOCK_KEY, param = "#businessKey", waitTime = 15L)
    public void startStudentOfferFlow(String businessKey, String procdefKey, String companyId, String buttonType, String submitReason, Long fkCancelOfferReasonId) throws GetServiceException {
//        Boolean flag = workflowCenterClient.startStudentOfferFlow(businessKey, procdefKey, companyId, buttonType);
        Result<Boolean> flag = workflowCenterClient.startStudentOfferFlow(businessKey, procdefKey, companyId, buttonType, submitReason, fkCancelOfferReasonId);
        if (!flag.isSuccess()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("start_process_fail_status"));
        }
//        Boolean flag = feignWorkFlowService.startStudentOfferFlow(businessKey, procdefKey, companyId, buttonType);
//        if (!flag) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("start_error"));
//        }
    }


    /**
     * @Description: 重新提交或放弃流程
     * @Author: Jerry
     * @Date:9:36 2021/11/5
     */
    @Override
    public void getUserSubmit(String businessKey, String procdefKey, String status) throws GetServiceException {
        List<Long> studentOfferIds = new ArrayList<>();
        studentOfferIds.add(Long.valueOf(businessKey));
        Map<Long, ActRuTaskVo> actRuTaskDtosByBusinessKey = new HashMap<>();
        Map<String, List<Long>> businessIdsWithProcdefKey = new HashMap<>();
        businessIdsWithProcdefKey.put(procdefKey, studentOfferIds);
        Result<Map<Long, ActRuTaskVo>> result = workflowCenterClient.getActRuTaskDtosByBusinessKey(businessIdsWithProcdefKey);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            actRuTaskDtosByBusinessKey = result.getData();
        }
        if (GeneralTool.isEmpty(actRuTaskDtosByBusinessKey)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        String taskId = null;
        for (Map.Entry<Long, ActRuTaskVo> longActRuTaskDtoEntry : actRuTaskDtosByBusinessKey.entrySet()) {
            if (GeneralTool.isNotEmpty(longActRuTaskDtoEntry.getValue())) {
                taskId = longActRuTaskDtoEntry.getValue().getId();
                break;
            }
        }
        Result<Boolean> result_ = workflowCenterClient.getUserSubmit(taskId, status);
        if (!result_.isSuccess()) {
            throw new GetServiceException(result_.getMessage());
        }
    }

    @Override
    public ResponseBo doGetStudentOfferWorkFlowPaginationInfo(StudentOfferListQueryDto studentOfferVo, Page page) {
        StudentOfferListQueryBo studentOfferListQueryBo = BeanCopyUtils.objClone(studentOfferVo, StudentOfferListQueryBo::new);
        if (GeneralTool.isEmpty(studentOfferVo)) {
            if (GeneralTool.isEmpty(studentOfferVo.getFkStudentId())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("student_id_null"));
            }
        }
        log.info("===>开始");

        List<Long> ids = new ArrayList<>();

        Long staffId = SecureUtil.getStaffId();
        //员工id + 业务下属员工ids
        List<Long> staffFollowerIds = permissionCenterClient.getStaffFollowerIds(staffId).getData().stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        staffFollowerIds.add(staffId);
        //模糊搜索申请的课程名称
//        if (GeneralTool.isNotEmpty(studentOfferVo.getCourseName())) {
//            //如有，则拼接搜索，没有则直接返回空
//            List<Long> courseIds = institutionCenterClient.getCourseIds(studentOfferVo.getCourseName()).getData();
//            if (!CollectionUtils.isEmpty(courseIds)) {
//                studentOfferVo.setCourseIds(courseIds);//写入集合，拼接到SQL进行in搜索
//            } else {
//                return new ResponseBo<>(page,0);//返回空
//            }
//        }
//        //模糊搜索申请的学校名称
//        if (GeneralTool.isNotEmpty(studentOfferVo.getSchoolName())) {
//            //如有，则拼接搜索，没有则直接返回空
//            List<Long> institutionIds = institutionCenterClient.getInstitutionIds(studentOfferVo.getSchoolName()).getData();
//            if (!CollectionUtils.isEmpty(institutionIds)) {
//                studentOfferVo.setInstitutionIds(institutionIds);//写入集合，拼接到SQL进行in搜索
//            } else {
//                return new ResponseBo<>(page,0);//返回空
//            }
//        }

        if (GeneralTool.isNotEmpty(studentOfferListQueryBo.getStatusWorkflow())) {
            if (studentOfferListQueryBo.getStatusWorkflow() == 0) {
                //0表示要显示我的申请列表，即我创建的表单
                studentOfferListQueryBo.setFkStaffIdWorkflow(SecureUtil.getStaffId());
            } else if (studentOfferListQueryBo.getStatusWorkflow() == 2) {
                //2表示显示我的审批列表，即我操作过的表单都要显示
                Map<Long, Integer> map = workflowCenterClient.getFromIdsByStaffId(SecureUtil.getStaffId(), TableEnum.SALE_STUDENT_OFFER.key).getData();
                List<Long> fromIds = new ArrayList<>(map.keySet());
                if (GeneralTool.isEmpty(fromIds)) {
                    fromIds.add(0L);
                }
                ids.addAll(fromIds);
            }
        }
        if (GeneralTool.isNotEmpty(ids)) {
            String idsString = GetStringUtils.getSqlString(ids);
            studentOfferListQueryBo.setIds(idsString);
        }
        long var1 = System.currentTimeMillis();
        String studentName = studentOfferListQueryBo.getStudentName();
        if (StringUtils.isNotBlank(studentName)) {
            studentOfferListQueryBo.setStudentName(studentName.replace(" ", "").trim());
        }

        //针对like的字段转为小写
        if (GeneralTool.isNotEmpty(studentOfferListQueryBo.getGroupName())) {
            studentOfferListQueryBo.setGroupName(studentOfferListQueryBo.getGroupName().toLowerCase());
        }
        if (GeneralTool.isNotEmpty(studentOfferListQueryBo.getChannelName())) {
            studentOfferListQueryBo.setChannelName(studentOfferListQueryBo.getChannelName().toLowerCase());
        }
        if (GeneralTool.isNotEmpty(studentOfferListQueryBo.getChannelNames())) {
            studentOfferListQueryBo.setChannelNames(studentOfferListQueryBo.getChannelNames().stream().map(name -> name.toLowerCase()).collect(Collectors.toList()));
        }
        if (GeneralTool.isNotEmpty(studentOfferListQueryBo.getAgentName())) {
            studentOfferListQueryBo.setAgentName(studentOfferListQueryBo.getAgentName().toLowerCase());
        }
        if (GeneralTool.isNotEmpty(studentOfferListQueryBo.getBdName())) {
            studentOfferListQueryBo.setBdName(studentOfferListQueryBo.getBdName().toLowerCase());
        }
        if (GeneralTool.isNotEmpty(studentOfferListQueryBo.getStudentName())) {
            studentOfferListQueryBo.setStudentName(studentOfferListQueryBo.getStudentName().toLowerCase());
        }
        //email
        if (GeneralTool.isNotEmpty(studentOfferListQueryBo.getEmail())) {
            studentOfferListQueryBo.setEmail(studentOfferListQueryBo.getEmail().toLowerCase());
        }
        if (GeneralTool.isNotEmpty(studentOfferListQueryBo.getSchoolName())) {
            studentOfferListQueryBo.setSchoolName(studentOfferListQueryBo.getSchoolName().toLowerCase());
        }
        //courseName
        if (GeneralTool.isNotEmpty(studentOfferListQueryBo.getCourseName())) {
            studentOfferListQueryBo.setCourseName(studentOfferListQueryBo.getCourseName().toLowerCase());
        }
        //oldCourseMajorLevelName
        if (GeneralTool.isNotEmpty(studentOfferListQueryBo.getOldCourseMajorLevelName())) {
            studentOfferListQueryBo.setOldCourseMajorLevelName(studentOfferListQueryBo.getOldCourseMajorLevelName().toLowerCase());
        }
        //oldCourseMajorLevelNames
        if (GeneralTool.isNotEmpty(studentOfferListQueryBo.getOldCourseMajorLevelNames())) {
            studentOfferListQueryBo.setOldCourseMajorLevelNames(studentOfferListQueryBo.getOldCourseMajorLevelNames().stream().map(name -> name.toLowerCase()).collect(Collectors.toList()));
        }
        Boolean isBd = getIsBd(SecureUtil.getStaffId());
        List<StudentOfferVo> offerDtos = offerMapper.getStudentOfferListNew2(studentOfferListQueryBo, staffFollowerIds, SecureUtil.getCountryIds(), SecureUtil.getStaffInfo().getIsStudentOfferItemFinancialHiding(), SecureUtil.getStaffInfo().getIsStudentAdmin(), SecureUtil.getInstitutionIds(), isBd,
                SecureUtil.getPermissionGroupInstitutionIds(),
                SecureUtil.getStaffBoundBdIds());

        Integer totalCount = offerDtos.get(0).getTotalCount();
        Integer showCount = page.getShowCount();
        page.setTotalPage(page.getShowCount() != null && showCount > 0 ? totalCount % showCount == 0 ? totalCount / showCount : totalCount / showCount + 1 : 0);
        page.setTotalResult(totalCount);
        return new ResponseBo<>(BeanCopyUtils.objClone(page, Page::new), System.currentTimeMillis() - var1);
    }

    /**
     * 申请方案审批列表
     *
     * @param studentOfferListQueryDto
     * @return
     */
    @Override
    public List<StudentOfferVo> getStudentOfferWorkFolwDatas(StudentOfferListQueryDto studentOfferListQueryDto, String[] times) {
        StudentOfferListQueryBo offerListQueryBo = BeanCopyUtils.objClone(studentOfferListQueryDto, StudentOfferListQueryBo::new);
        if (GeneralTool.isEmpty(offerListQueryBo)) {
            if (GeneralTool.isEmpty(offerListQueryBo.getFkStudentId())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("student_id_null"));
            }
        }

        long startTime = System.currentTimeMillis();
        log.info("===>开始");

        List<Long> ids = new ArrayList<>();
        //状态变更时间
//        if (GeneralTool.isNotEmpty(studentOfferVo.getStepChangeBeginDate()) || GeneralTool.isNotEmpty(studentOfferVo.getStepChangeEndDate())) {
//            List<Long> stepChangeDateIds = offerMapper.getOfferIdByStepChangeDate(studentOfferVo);
//            stepChangeDateIds.removeIf(Objects::isNull);
//            if (GeneralTool.isEmpty(stepChangeDateIds)) {
//                stepChangeDateIds.add(0L);
//            }
//            ids.addAll(stepChangeDateIds);
//        }

        Long staffId = SecureUtil.getStaffId();
        //员工id + 业务下属员工ids
        List<Long> staffFollowerIds = permissionCenterClient.getStaffFollowerIds(staffId).getData().stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        staffFollowerIds.add(staffId);

        Map<Long, Integer> workflowStatusMap = new HashMap<>();
        Result<Map<Long, Integer>> fromIdsResult = workflowCenterClient.getFromIdsByStaffId(GetAuthInfo.getStaffId(), TableEnum.SALE_STUDENT_OFFER.key);
        if (fromIdsResult.isSuccess() && GeneralTool.isNotEmpty(fromIdsResult.getData())) {
            workflowStatusMap = fromIdsResult.getData();
        }

        //员工 + 业务下属员工 loginId
//        Set<Long> followerIds = new HashSet<>(staffFollowerIds);
//        Map<Long, String> longIdMap = feignPermissionService.getStaffLoginIdByIds(followerIds);
//        List<String> userNames = new ArrayList<>(longIdMap.values());


        //模糊搜索申请的课程名称
//        if (GeneralTool.isNotEmpty(studentOfferVo.getCourseName())) {
//            //如有，则拼接搜索，没有则直接返回空
//            List<Long> courseIds = institutionCenterClient.getCourseIds(studentOfferVo.getCourseName()).getData();
//            if (!CollectionUtils.isEmpty(courseIds)) {
//                studentOfferVo.setCourseIds(courseIds);//写入集合，拼接到SQL进行in搜索
//            } else {
//                return Lists.newArrayList();//返回空
//            }
//        }
//        //模糊搜索申请的学校名称
//        if (GeneralTool.isNotEmpty(studentOfferVo.getSchoolName())) {
//            //如有，则拼接搜索，没有则直接返回空
//            List<Long> institutionIds = institutionCenterClient.getInstitutionIds(studentOfferVo.getSchoolName()).getData();
//            if (!CollectionUtils.isEmpty(institutionIds)) {
//                studentOfferVo.setInstitutionIds(institutionIds);//写入集合，拼接到SQL进行in搜索
//            } else {
//                return Lists.newArrayList();//返回空
//            }
//        }
        //发起人暂时在SQL中没有使用
//        if (GeneralTool.isNotEmpty(studentOfferVo.getFkCompanyId())) {
//            List<Long> staffIds = feignPermissionService.getStaffIdsByCompanyId(studentOfferVo.getFkCompanyId());
//            if (GeneralTool.isNotEmpty(staffIds)) {
//                studentOfferVo.setFkStaffIdWorkflowIds(MyStringUtils.getSqlString(staffIds));
//                if (GeneralTool.isNotEmpty(studentOfferVo.getStatusWorkflow()) && studentOfferVo.getStatusWorkflow() == 1) {
//                    //查看所有表单
//                    //studentOfferVo.setAllStudentIds(MyStringUtils.getSqlString(allStudentIds));
//                }
//            }else
//            {
//                return new ListResponseBo();
//            }
//
////            else {
////                List<Long> fkStaffIdWorkflowIds = new ArrayList<>();
////                fkStaffIdWorkflowIds.add(0L);
////                studentOfferVo.setFkStaffIdWorkflowIds(MyStringUtils.getSqlString(fkStaffIdWorkflowIds));
////            }
//        }
        if (GeneralTool.isNotEmpty(offerListQueryBo.getStatusWorkflow())) {
            if (offerListQueryBo.getStatusWorkflow() == 0) {
                //0表示要显示我的申请列表，即我创建的表单
                offerListQueryBo.setFkStaffIdWorkflow(SecureUtil.getStaffId());
            } else if (offerListQueryBo.getStatusWorkflow() == 2) {
                //2表示显示我的审批列表，即我操作过的表单都要显示
                Map<Long, Integer> map = workflowCenterClient.getFromIdsByStaffId(SecureUtil.getStaffId(), TableEnum.SALE_STUDENT_OFFER.key).getData();
                List<Long> fromIds = new ArrayList<>(map.keySet());
                if (GeneralTool.isEmpty(fromIds)) {
                    fromIds.add(0L);
                }
                ids.addAll(fromIds);
            }
        }
        if (GeneralTool.isNotEmpty(ids)) {
            String idsString = GetStringUtils.getSqlString(ids);
            offerListQueryBo.setIds(idsString);
        }


//        if (GeneralTool.isNotEmpty(studentOfferVo.getStudentVo().getState())){
//            studentOfferVo.setState(studentOfferVo.getStudentVo().getState());
//        }
//        if (GeneralTool.isNotEmpty(studentOfferVo.getStudentVo().getIsDeferEntrance())){
//            studentOfferVo.setIsDeferEntrance(studentOfferVo.getStudentVo().getIsDeferEntrance());
//        }
//        if (GeneralTool.isNotEmpty(studentOfferVo.getStudentVo().getFailureReasonId())){
//            studentOfferVo.setFailureReasonId(studentOfferVo.getStudentVo().getFailureReasonId());
//        }
//        if (GeneralTool.isNotEmpty(studentOfferVo.getStudentVo().getTel())){
//            studentOfferVo.setTel(studentOfferVo.getStudentVo().getTel());
//        }
//        if (GeneralTool.isNotEmpty(studentOfferVo.getStudentVo().getEmail())){
//            studentOfferVo.setEmail(studentOfferVo.getStudentVo().getEmail());
//        }

        long fStartTime = System.currentTimeMillis();

//        List<StudentOfferVo> studentOffers = offerMapper.getStudentOfferWorkFolw(studentOfferVo);
        if (offerListQueryBo.getPageNumber() != null && offerListQueryBo.getPageNumber() > 0) {
            offerListQueryBo.setOffset((offerListQueryBo.getPageNumber() - 1) * offerListQueryBo.getPageSize());
        }
        String studentName = offerListQueryBo.getStudentName();
        if (StringUtils.isNotBlank(studentName)) {
            offerListQueryBo.setStudentName(studentName.replace(" ", "").trim());
        }

        //针对like的字段转为小写
        if (GeneralTool.isNotEmpty(offerListQueryBo.getGroupName())) {
            offerListQueryBo.setGroupName(offerListQueryBo.getGroupName().toLowerCase());
        }
        if (GeneralTool.isNotEmpty(offerListQueryBo.getChannelName())) {
            offerListQueryBo.setChannelName(offerListQueryBo.getChannelName().toLowerCase());
        }
        if (GeneralTool.isNotEmpty(offerListQueryBo.getChannelNames())) {
            offerListQueryBo.setChannelNames(offerListQueryBo.getChannelNames().stream().map(name -> name.toLowerCase()).collect(Collectors.toList()));
        }
        if (GeneralTool.isNotEmpty(offerListQueryBo.getAgentName())) {
            offerListQueryBo.setAgentName(offerListQueryBo.getAgentName().toLowerCase());
        }
        if (GeneralTool.isNotEmpty(offerListQueryBo.getBdName())) {
            offerListQueryBo.setBdName(offerListQueryBo.getBdName().toLowerCase());
        }
        if (GeneralTool.isNotEmpty(offerListQueryBo.getStudentName())) {
            offerListQueryBo.setStudentName(offerListQueryBo.getStudentName().toLowerCase());
        }
        //email
        if (GeneralTool.isNotEmpty(offerListQueryBo.getEmail())) {
            offerListQueryBo.setEmail(offerListQueryBo.getEmail().toLowerCase());
        }
        if (GeneralTool.isNotEmpty(offerListQueryBo.getSchoolName())) {
            offerListQueryBo.setSchoolName(offerListQueryBo.getSchoolName().toLowerCase());
        }
        //courseName
        if (GeneralTool.isNotEmpty(offerListQueryBo.getCourseName())) {
            offerListQueryBo.setCourseName(offerListQueryBo.getCourseName().toLowerCase());
        }
        //oldCourseMajorLevelName
        if (GeneralTool.isNotEmpty(offerListQueryBo.getOldCourseMajorLevelName())) {
            offerListQueryBo.setOldCourseMajorLevelName(offerListQueryBo.getOldCourseMajorLevelName().toLowerCase());
        }
        //oldCourseMajorLevelNames
        if (GeneralTool.isNotEmpty(offerListQueryBo.getOldCourseMajorLevelNames())) {
            offerListQueryBo.setOldCourseMajorLevelNames(offerListQueryBo.getOldCourseMajorLevelNames().stream().map(String::toLowerCase).collect(Collectors.toList()));
        }
        //oldCourseTypeGroupNames
        if (GeneralTool.isNotEmpty(offerListQueryBo.getOldCourseTypeGroupNames())) {
            offerListQueryBo.setOldCourseTypeGroupNames(offerListQueryBo.getOldCourseTypeGroupNames().stream().map(name -> name.toLowerCase()).collect(Collectors.toList()));
        }

        if (GeneralTool.isNotEmpty(offerListQueryBo.getCourseIds()) && offerListQueryBo.getCourseIds().size() >= 100) {
            offerListQueryBo.setIsCourseGlobalMatching(true);
        }
        Boolean isBd = getIsBd(SecureUtil.getStaffId());
        List<StudentOfferVo> studentOffers = offerMapper.getStudentOfferListNew2(offerListQueryBo, staffFollowerIds, SecureUtil.getCountryIds(), SecureUtil.getStaffInfo().getIsStudentOfferItemFinancialHiding(), SecureUtil.getStaffInfo().getIsStudentAdmin(), SecureUtil.getInstitutionIds(), isBd, SecureUtil.getPermissionGroupInstitutionIds(), SecureUtil.getStaffBoundBdIds());

        List<StudentOfferVo> collect = studentOffers.stream().map(studentOffer ->
                BeanCopyUtils.objClone(studentOffer, StudentOfferVo::new)).collect(Collectors.toList());
        //批量修改离职人员替换直接返回
        if (GeneralTool.isNotEmpty(offerListQueryBo.getIsProjectUpate()) && offerListQueryBo.getIsProjectUpate()) {
            return collect;
        }

        long fEndTime = System.currentTimeMillis();
        Set<Long> studentIds = studentOffers.stream().map(StudentOfferVo::getFkStudentId).collect(Collectors.toSet());
        Map<Long, String> studentNameByIds = studentService.getStudentNameByIds(studentIds);
//   TODO   改过
//        Set<Long> countryIds = studentOffers.stream().map(StudentOffer::getFkAreaCountryId).collect(Collectors.toSet());
//        Set<Long> staffIds = studentOffers.stream().map(StudentOffer::getFkStaffIdWorkflow).collect(Collectors.toSet());
        Set<Long> countryIds = studentOffers.stream().map(StudentOfferVo::getFkAreaCountryId).collect(Collectors.toSet());
        Set<Long> staffIds = studentOffers.stream().map(StudentOfferVo::getFkStaffIdWorkflow).collect(Collectors.toSet());
        Map<Long, String> countryNames = institutionCenterClient.getCountryNamesByIds(countryIds).getData();
        Map<Long, String> companyNames = permissionCenterClient.getCompanyNamesByStaffIds(staffIds).getData();
        String companyName = permissionCenterClient.getCompanyNameById(offerListQueryBo.getFkCompanyId()).getData();
        if (GeneralTool.isEmpty(collect)) {
            return Lists.newArrayList();
        }
        List<Long> studentOfferIds = collect.stream().map(StudentOfferVo::getId).collect(Collectors.toList());
        Map<String, List<Long>> businessIdsWithProcdefKey = new HashMap<>();
        businessIdsWithProcdefKey.put(TableEnum.SALE_STUDENT_OFFER.key, studentOfferIds);
        Map<Long, ActRuTaskVo> actRuTaskDtoMap = workflowCenterClient.getActRuTaskDtosByBusinessKey(businessIdsWithProcdefKey).getData();

        Set<Long> dtoIds = collect.stream().map(StudentOfferVo::getId).collect(Collectors.toSet());
        //改成批量
        Map<Long, List<StudentRoleAndStaffVo>> roleAndStaffsMap = projectRoleStaffService.getRoleAndStaffByTableIds(dtoIds);

//        List<StudentRoleAndStaffVo> roleAndStaff = projectRoleStaffService.getRoleAndStaffByTableId(studentOfferDto.getId());
        Set<Long> agentIds = collect.stream().map(StudentOfferVo::getFkAgentId).collect(Collectors.toSet());
        Map<Long, List<AgentLabelVo>> agentLabelMap = getAgentLabelDataUtils.getAgentLabelMap(agentIds).getAgentLabelMap();
        Map<Long, Agent> agentsByIds = agentService.getAgentsByIds(agentIds);
        for (StudentOfferVo studentOfferVo : collect) {
            studentOfferVo.setStudentName(studentNameByIds.get(studentOfferVo.getFkStudentId()));
//            代理标签
            getAgentLabelDataUtils.setAgentLabelVosByLabelMap(studentOfferVo, agentLabelMap, studentOfferVo.getFkAgentId(), StudentOfferVo::setAgentLabelVos);
//            studentOfferVo.setStudentName(studentNameByIds.get(studentOfferVo.getId()));
            //设置员工和角色名称
            studentOfferVo.setStudentRoleAndStaffList(roleAndStaffsMap.get(studentOfferVo.getId()));
            if (GeneralTool.isEmpty(companyNames.get(studentOfferVo.getFkStaffIdWorkflow()))) {
                studentOfferVo.setCompanyName(companyName);
            } else {
                studentOfferVo.setCompanyName(companyNames.get(studentOfferVo.getFkStaffIdWorkflow()));
            }
            //流程对象
            ActRuTaskVo actRuTaskVo = actRuTaskDtoMap.get(studentOfferVo.getId());
            //正在进行的任务id
            if (GeneralTool.isNotEmpty(actRuTaskVo.getId())) {
                studentOfferVo.setTaskId(Long.valueOf(actRuTaskVo.getId()));
            }
            //流程实例id
            if (GeneralTool.isNotEmpty(actRuTaskVo.getProcInstId())) {
                studentOfferVo.setProcInstId(Long.valueOf(actRuTaskVo.getProcInstId()));
            }
            //任务版本
            if (GeneralTool.isNotEmpty(actRuTaskVo.getTaskVersion())) {
                studentOfferVo.setTaskVersion(actRuTaskVo.getTaskVersion());
            }
            if (GeneralTool.isNotEmpty(actRuTaskVo.getProcInstId())) {
                Result<Object> result = workflowCenterClient.getVariableByHisInstanceAndName(actRuTaskVo.getProcInstId(), "buttonType");
                try {
                    String buttonTypeString = (String) result.getData();
                    String workflowName = "0".equals(buttonTypeString) ? "申请终止流程" : "申请结案流程";
                    studentOfferVo.setWorkflowName(workflowName);
                    if (GeneralTool.isNotEmpty(buttonTypeString)) {
                        studentOfferVo.setButtonType(Integer.valueOf(buttonTypeString));
                    }
                } catch (Exception e) {
                    studentOfferVo.setButtonType(null);
                }
            }
            studentOfferVo.setExpenseClaimFormStatus(workflowStatusMap.get(studentOfferVo.getId()));
        }
        //设置名称
        setOfferDtoName(collect, countryNames, agentsByIds);
        long endTime = System.currentTimeMillis();

        if (GeneralTool.isNotEmpty(times)) {
            times[0] = String.valueOf((fEndTime - fStartTime));
            times[1] = String.valueOf((endTime - startTime) - (fEndTime - fStartTime));
        }
        return collect;
    }

    @Override
    public List<StudentOffer> getStudentOffersByStudentIds(List<Long> studentIds) {
        LambdaQueryWrapper<StudentOffer> lambdaQueryWrapper = Wrappers.<StudentOffer>lambdaQuery().ne(StudentOffer::getStatus, 0);
        if (studentIds != null && studentIds.size() < 10000) {
            lambdaQueryWrapper.in(StudentOffer::getFkStudentId, studentIds);
        }
        return offerMapper.selectList(lambdaQueryWrapper);
    }


    /**
     * @Description: 绑定代理下拉框
     * @Author: Jerry
     * @Date:12:29 2021/11/11
     */
    @Override
    public List<BaseSelectEntity> getAgentSelect(Long fkCompanyId) {
        List<BaseSelectEntity> baseSelectEntities = new ArrayList<>();
        List<Long> staffIds = new ArrayList<>();
        Result<List<Long>> result = permissionCenterClient.getStaffIdsByCompanyId(fkCompanyId);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            staffIds.addAll(result.getData());
        }
        if (GeneralTool.isEmpty(staffIds)) {
            return baseSelectEntities;
        }
//        Example example = new Example(StudentOffer.class);
//        example.createCriteria().andIn("fkStaffIdWorkflow", staffIds);
//        List<StudentOffer> studentOffers = offerMapper.selectByExample(example);
        List<StudentOffer> studentOffers = offerMapper.selectList(Wrappers.<StudentOffer>lambdaQuery().in(StudentOffer::getFkStaffIdWorkflow, staffIds));
        if (GeneralTool.isEmpty(studentOffers)) {
            return baseSelectEntities;
        }
        //获取所有的代理ids
        Set<Long> fkAgentIds = studentOffers.stream().map(StudentOffer::getFkAgentId).collect(Collectors.toSet());
        fkAgentIds.removeIf(Objects::isNull);
        //根据代理ids获取所有名称
        Map<Long, String> agentNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkAgentIds)) {
            agentNamesByIds = agentService.getAgentNamesByIds(fkAgentIds);
        }
        if (GeneralTool.isEmpty(agentNamesByIds)) {
            return baseSelectEntities;
        }
        BaseSelectEntity baseSelectEntity = null;
        for (Map.Entry<Long, String> agent : agentNamesByIds.entrySet()) {
            baseSelectEntity = new BaseSelectEntity();
            baseSelectEntity.setId(agent.getKey());
            baseSelectEntity.setName(agent.getValue());
            baseSelectEntities.add(baseSelectEntity);
        }
        return baseSelectEntities;
    }

    @Override
    public List<AgentVo> getAgentByOfferIds(Set<Long> offerIds) {
        return offerMapper.getAgentByOfferIds(GetStringUtils.getSqlString(offerIds));
    }

    /**
     * 关闭无效的申请方案
     *
     * @param studentId
     */
    @Override
    public void closeOtherStudentOffers(Long studentId) {
        //查询出学生下所有方案
        HashSet<Integer> hashSet = new HashSet<>();
        hashSet.add(1);
        hashSet.add(2);
        List<StudentOffer> studentOfferList = studentOfferMapper.selectList(Wrappers.<StudentOffer>lambdaQuery()
                .eq(StudentOffer::getFkStudentId, studentId)
                .eq(StudentOffer::getStatus, 1)
                .notIn(StudentOffer::getStatusWorkflow, hashSet));
        if (GeneralTool.isEmpty(studentOfferList)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("student_offer_not_exists"));
            return;
        }
        //查出入学登记完成的步骤id
        List<StudentOfferItemStep> studentOfferItemSteps = studentOfferItemStepMapper.selectList(Wrappers.<StudentOfferItemStep>lambdaQuery()
                .eq(StudentOfferItemStep::getStepKey, ProjectKeyEnum.STEP_ENROLLED.key));
        if (GeneralTool.isEmpty(studentOfferItemSteps)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("student_offer_item_step_not_exists"));
        }

        //筛选需要关闭的方案
        Long stepId = studentOfferItemSteps.get(0).getId();
        for (StudentOffer studentOffer : studentOfferList) {
            if (validateClosable(studentOffer, stepId)) {
                //关闭申请方案
                studentOffer.setStatus(0);
                utilService.updateUserInfoToEntity(studentOffer);
                studentOfferMapper.updateById(studentOffer);
            }
        }

        //把之前关闭的申请方案下的学习计划都关闭
        List<Long> offerIds = studentOfferList.stream().map(StudentOffer::getId).collect(Collectors.toList());
//        example.createCriteria().andIn("fkStudentOfferId", offerIds)
//                .andEqualTo("status", 1)
//                .andNotEqualTo("fkStudentOfferItemStepId", stepId);
//        StudentOfferItem studentOfferItemUpdate = new StudentOfferItem();
//        studentOfferItemUpdate.setStatus(0);
//        utilService.updateUserInfoToEntity(studentOfferItemUpdate);
//        studentOfferItemMapper.updateByExampleSelective(studentOfferItemUpdate, example);

        List<StudentOfferItem> studentOfferItems = studentOfferItemMapper.selectList(Wrappers.<StudentOfferItem>lambdaQuery()
                .in(StudentOfferItem::getFkStudentOfferId, offerIds)
                .eq(StudentOfferItem::getStatus, 1).ne(StudentOfferItem::getFkStudentOfferItemStepId, stepId));
        if (GeneralTool.isNotEmpty(studentOfferItems)) {
            studentOfferItems.stream().forEach(item -> {
                item.setStatus(0);
            });
        }
        this.studentOfferItemService.updateBatchById(studentOfferItems);
    }

    /**
     * 国家线下拉框
     *
     * @param companyId
     * @return
     */
    @Override
    public List<BaseSelectEntity> getAreaCountryList(Long companyId) {
        List<BaseSelectEntity> baseSelectEntities = new ArrayList<>();
        List<Long> countryIds = studentOfferMapper.getAreaCountryList(companyId);
        countryIds.removeIf(Objects::isNull);
        if (GeneralTool.isEmpty(countryIds)) {
            countryIds.add(0L);
        }
        Set<Long> countryIdsSet = new HashSet<>(countryIds);
        Map<Long, String> countryNameMap = getCountryNameMap(countryIdsSet);
        for (Long countryId : countryIds) {
            BaseSelectEntity baseSelectEntity = new BaseSelectEntity();
            baseSelectEntity.setId(countryId);
            baseSelectEntity.setFullName(countryNameMap.get(countryId));
            baseSelectEntities.add(baseSelectEntity);
        }
        return baseSelectEntities;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Set<Long> batchDistributeProjectRole(StudentOfferListQueryDto studentOfferListQueryDto) {

        StudentOfferListQueryBo studentOfferListQueryBo = BeanCopyUtils.objClone(studentOfferListQueryDto, StudentOfferListQueryBo::new);
        if (GeneralTool.isNotEmpty(studentOfferListQueryBo.getStudentProjectRoleStaff()) && GeneralTool.isEmpty(studentOfferListQueryBo.getStudentProjectRoleStaff().getOfferIds())
                && GeneralTool.isEmpty(studentOfferListQueryBo.getStudentProjectRoleStaff().getFkStudentProjectRoleId())
                && GeneralTool.isEmpty(studentOfferListQueryBo.getStudentProjectRoleStaff().getFkStaffId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }

        Long staffId = SecureUtil.getStaffId();
        //员工id + 业务下属员工ids
        List<Long> staffFollowerIds = permissionCenterClient.getStaffFollowerIds(staffId).getData().stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        staffFollowerIds.add(staffId);

        //不分页 查出符合条件的方案
        studentOfferListQueryBo.setPageNumber(0);
        studentOfferListQueryBo.setPageSize(null);
        Boolean isBd = getIsBd(SecureUtil.getStaffId());
        List<StudentOfferVo> studentOffers = offerMapper.getStudentOfferListNew2(studentOfferListQueryBo, staffFollowerIds, SecureUtil.getCountryIds(), SecureUtil.getStaffInfo().getIsStudentOfferItemFinancialHiding(), SecureUtil.getStaffInfo().getIsStudentAdmin(), SecureUtil.getInstitutionIds(), isBd, SecureUtil.getPermissionGroupInstitutionIds(), SecureUtil.getStaffBoundBdIds());

        Set<Long> offerIdSet = studentOffers.stream().map(StudentOfferVo::getId).collect(Collectors.toSet());
        if (GeneralTool.isEmpty(offerIdSet)) {
            return null;
        }

        //iae需要限制项目成员变动
        Set<Long> limitOfferIds = getProjectLimitConfigKey(offerIdSet, studentOfferListQueryBo.getFkCompanyId());
        if (GeneralTool.isNotEmpty(limitOfferIds)) {
            offerIdSet.removeAll(limitOfferIds);
            if (GeneralTool.isEmpty(offerIdSet)) {
                return offerIdSet;
            }
        }
        //相同国家下的相同角色只能绑定一个员工
        List<StudentProjectRoleStaff> studentProjectRoleStaffs = studentProjectRoleStaffMapper.selectList(Wrappers.<StudentProjectRoleStaff>lambdaQuery().eq(StudentProjectRoleStaff::getFkTableName, TableEnum.SALE_STUDENT_OFFER.key)
                .in(StudentProjectRoleStaff::getFkTableId, 3).eq(StudentProjectRoleStaff::getFkStudentProjectRoleId, studentOfferListQueryBo.getStudentProjectRoleStaff().getFkStudentProjectRoleId())
                .eq(StudentProjectRoleStaff::getIsActive, true));
        if (GeneralTool.isNotEmpty(studentProjectRoleStaffs)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("only_one_employee_can_be_bound_to_the_same_role_in_the_same_country"));
        }

        LambdaQueryWrapper<StudentProjectRoleStaff> wrapper = Wrappers.<StudentProjectRoleStaff>lambdaQuery()
                .in(StudentProjectRoleStaff::getFkTableId, offerIdSet)
                .eq(StudentProjectRoleStaff::getFkTableName, TableEnum.SALE_STUDENT_OFFER.key)
                .eq(StudentProjectRoleStaff::getFkStudentProjectRoleId, studentOfferListQueryBo.getStudentProjectRoleStaff().getFkStudentProjectRoleId())
                .eq(StudentProjectRoleStaff::getFkStaffId, studentOfferListQueryBo.getStudentProjectRoleStaff().getFkStaffId())
                .eq(StudentProjectRoleStaff::getIsActive, true);

        studentProjectRoleStaffMapper.delete(wrapper);
//        List<StudentProjectRoleStaff> studentProjectRoleStaffs = studentProjectRoleStaffMapper.selectList(wrapper);
//        Map<Long, List<StudentProjectRoleStaff>> map = new HashMap<>();
//        if (GeneralTool.isNotEmpty(studentProjectRoleStaffs)) {
//            map = studentProjectRoleStaffs.stream().collect(Collectors.groupingBy(StudentProjectRoleStaff::getFkTableId));
//            //先删后增
//        }

        List<StudentProjectRoleStaff> batchAddEnites = new ArrayList<>();
        for (Long offerId : offerIdSet) {
//            //已经有相同记录
//            if (GeneralTool.isNotEmpty(map.get(offerId))) {
//                continue;
//            }
            StudentProjectRoleStaff studentProjectRoleStaff = new StudentProjectRoleStaff();
            studentProjectRoleStaff.setFkTableName(TableEnum.SALE_STUDENT_OFFER.key);
            studentProjectRoleStaff.setFkTableId(offerId);
            studentProjectRoleStaff.setFkStudentProjectRoleId(studentOfferListQueryBo.getStudentProjectRoleStaff().getFkStudentProjectRoleId());
            studentProjectRoleStaff.setFkStaffId(studentOfferListQueryBo.getStudentProjectRoleStaff().getFkStaffId());
            studentProjectRoleStaff.setIsActive(true);
            studentProjectRoleStaff.setActiveDate(new Date());
            utilService.updateUserInfoToEntity(studentProjectRoleStaff);
            batchAddEnites.add(studentProjectRoleStaff);
//            studentProjectRoleStaffMapper.insertSelective(studentProjectRoleStaff);
        }
        if (GeneralTool.isNotEmpty(batchAddEnites)) {
            studentProjectRoleStaffService.batchAddStudentProjectRoleStaff(batchAddEnites);
        }
        return offerIdSet;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Set<Long> deletebatchDistributeProjectRole(StudentOfferListQueryDto studentOfferListQueryDto) {

        StudentOfferListQueryBo studentOfferListQueryBo = BeanCopyUtils.objClone(studentOfferListQueryDto, StudentOfferListQueryBo::new);
        if (GeneralTool.isNotEmpty(studentOfferListQueryBo.getStudentProjectRoleStaff()) && GeneralTool.isEmpty(studentOfferListQueryBo.getStudentProjectRoleStaff().getOfferIds())
                && GeneralTool.isEmpty(studentOfferListQueryBo.getStudentProjectRoleStaff().getFkStudentProjectRoleId())
                && GeneralTool.isEmpty(studentOfferListQueryBo.getStudentProjectRoleStaff().getFkStaffId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }

        //校验角色是否不能移除
        verifyProjectRoleRemove(studentOfferListQueryBo);


        Long staffId = SecureUtil.getStaffId();
        //员工id + 业务下属员工ids
        List<Long> staffFollowerIds = permissionCenterClient.getStaffFollowerIds(staffId).getData().stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        staffFollowerIds.add(staffId);

        //不分页 查出符合条件的方案
        studentOfferListQueryBo.setPageNumber(0);
        studentOfferListQueryBo.setPageSize(null);
        Boolean isBd = getIsBd(SecureUtil.getStaffId());
        List<StudentOfferVo> studentOffers = offerMapper.getStudentOfferListNew2(studentOfferListQueryBo, staffFollowerIds, SecureUtil.getCountryIds(), SecureUtil.getStaffInfo().getIsStudentOfferItemFinancialHiding(), SecureUtil.getStaffInfo().getIsStudentAdmin(), SecureUtil.getInstitutionIds(), isBd, SecureUtil.getPermissionGroupInstitutionIds(), SecureUtil.getStaffBoundBdIds());

        Set<Long> offerIdSet = studentOffers.stream().map(StudentOfferVo::getId).collect(Collectors.toSet());
        if (GeneralTool.isEmpty(offerIdSet)) {
            return null;
        }

        //iae需要限制项目成员变动
        Set<Long> limitOfferIds = getProjectLimitConfigKey(offerIdSet, studentOfferListQueryBo.getFkCompanyId());
        if (GeneralTool.isNotEmpty(limitOfferIds)) {
            offerIdSet.removeAll(limitOfferIds);
            if (GeneralTool.isEmpty(offerIdSet)) {
                return offerIdSet;
            }
        }


//        StudentProjectRoleStaff studentProjectRoleStaff = new StudentProjectRoleStaff();
//        studentProjectRoleStaff.setIsActive(false);
//        studentProjectRoleStaff.setUnactiveDate(new Date());

        LambdaQueryWrapper<StudentProjectRoleStaff> wrapper = Wrappers.<StudentProjectRoleStaff>lambdaQuery()
                .in(StudentProjectRoleStaff::getFkTableId, offerIdSet).eq(StudentProjectRoleStaff::getIsActive, true)
                .eq(StudentProjectRoleStaff::getFkTableName, TableEnum.SALE_STUDENT_OFFER.key)
                .eq(StudentProjectRoleStaff::getFkStudentProjectRoleId, studentOfferListQueryBo.getStudentProjectRoleStaff().getFkStudentProjectRoleId())
                .eq(StudentProjectRoleStaff::getFkStaffId, studentOfferListQueryBo.getStudentProjectRoleStaff().getFkStaffId());

        List<StudentProjectRoleStaff> studentProjectRoleStaffs = studentProjectRoleStaffMapper.selectList(wrapper);
        for (StudentProjectRoleStaff projectRoleStaff : studentProjectRoleStaffs) {
            projectRoleStaff.setIsActive(false);
            projectRoleStaff.setUnactiveDate(new Date());
        }
        studentProjectRoleStaffService.updateBatchById(studentProjectRoleStaffs);
        Set<Long> offerIdSet2 = studentProjectRoleStaffs.stream().map(StudentProjectRoleStaff::getFkTableId).collect(Collectors.toSet());

//        studentProjectRoleStaffMapper.update(studentProjectRoleStaff,wrapper);
        return offerIdSet2;
    }


    /**
     * 复制申请方案
     *
     * @param id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long copyOffer(Long id) {
        //查询出申请方案
        StudentOffer studentOffer = studentOfferMapper.selectById(id);
        if (GeneralTool.isEmpty(studentOffer)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        //设置属性
        StudentOffer studentOfferCopy = new StudentOffer();
        BeanUtils.copyProperties(studentOffer, studentOfferCopy);
        studentOfferCopy.setId(null);
        //默认设置打开
        studentOfferCopy.setStatus(1);
        //设置为待发起
        studentOfferCopy.setStatusWorkflow(0);
        studentOfferCopy.setFkStaffIdWorkflow(null);
        //置空修改时间
        studentOfferCopy.setGmtModified(null);
        studentOfferCopy.setGmtModifiedUser(null);
        studentOfferCopy.setIdGea(null);
        studentOfferCopy.setIdIae(null);
        //新增学习计划，设置创建时间
        utilService.updateUserInfoToEntity(studentOfferCopy);
        studentOfferMapper.insert(studentOfferCopy);
        //更新编号
        studentOfferCopy.setNum(GetStringUtils.getStudentOfferItemNum(studentOfferCopy.getId()));
        studentOfferMapper.updateById(studentOfferCopy);

        //项目成员配置同时分配
        List<StudentProjectRoleStaff> studentProjectRoleStaffs = studentProjectRoleStaffMapper.selectList(Wrappers.<StudentProjectRoleStaff>lambdaQuery()
                .eq(StudentProjectRoleStaff::getFkTableName, TableEnum.SALE_STUDENT_OFFER.key)
                .eq(StudentProjectRoleStaff::getFkTableId, id));

        List<StudentProjectRoleStaff> saveEnities = studentProjectRoleStaffs.stream().map(s -> {
            s.setId(null);
            s.setFkTableId(studentOfferCopy.getId());
            s.setGmtModified(null);
            s.setGmtModifiedUser(null);
            utilService.updateUserInfoToEntity(s);
            return s;
        }).collect(Collectors.toList());
        studentProjectRoleStaffService.batchAddStudentProjectRoleStaff(saveEnities);

        LambdaQueryWrapper<StudentOfferItem> lambdaQueryWrapper = Wrappers.<StudentOfferItem>lambdaQuery()
                .eq(StudentOfferItem::getFkStudentOfferId, id)
                .isNull(StudentOfferItem::getFkParentStudentOfferItemId);
        if (!SecureUtil.getStaffInfo().getIsStudentOfferItemFinancialHiding()) {
            lambdaQueryWrapper.ne(StudentOfferItem::getIsFollowHidden, 1);
        }
        List<StudentOfferItem> studentOfferItems = studentOfferItemMapper.selectList(lambdaQueryWrapper);
        if (GeneralTool.isNotEmpty(studentOfferItems)) {
            //复制学习计划
            for (StudentOfferItem studentOfferItem : studentOfferItems) {
                //学习计划的复制方法
                doCopyOfferItem(studentOfferItem.getId(), studentOfferCopy.getId());
            }
        }
        return studentOfferCopy.getId();
    }


    /**
     * @param itemId
     * @param offerId
     */
    private Long doCopyOfferItem(Long itemId, Long offerId) {
        //查询出学习计划
        StudentOfferItem studentOfferItem = studentOfferItemMapper.selectById(itemId);
        if (GeneralTool.isEmpty(studentOfferItem)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        //设置属性
//        StudentOfferItem studentOfferItemCopy = Tools.objClone(studentOfferItem, StudentOfferItem.class);
        StudentOfferItem studentOfferItemCopy = new StudentOfferItem();
        BeanUtils.copyProperties(studentOfferItem, studentOfferItemCopy);

        //默认设置为新申请
        studentOfferItemCopy.setFkStudentOfferItemStepId(1L);
        if (GeneralTool.isNotEmpty(studentOfferItemCopy.getFkParentStudentOfferItemId()) && studentOfferItemCopy.getIsStepFollow()) {
            StudentOfferItem parentItem = studentOfferItemMapper.selectById(studentOfferItemCopy.getFkParentStudentOfferItemId());
            studentOfferItemCopy.setFkStudentOfferItemStepId(parentItem.getFkStudentOfferItemStepId());
        }

        studentOfferItemCopy.setId(null);

        if (GeneralTool.isNotEmpty(studentOfferItemCopy.getIsStepFollow()) && studentOfferItemCopy.getIsStepFollow()) {
            studentOfferItemCopy.setFkStudentOfferItemStepId(studentOfferItem.getFkParentStudentOfferItemId());
        }
        //默认设置打开
        studentOfferItemCopy.setStudentOfferItemStepTime(new Date());
        studentOfferItemCopy.setFkStudentOfferId(offerId);
        studentOfferItemCopy.setFkIssueRpaOrderId(null);
        studentOfferItemCopy.setIsAddApp(null);
        studentOfferItemCopy.setConditionType(null);
        studentOfferItemCopy.setFkEnrolFailureReasonId(null);
        studentOfferItemCopy.setIsDeferEntrance(false);
        studentOfferItemCopy.setOtherFailureReason(null);
        studentOfferItemCopy.setDepositDeadline(null);
        studentOfferItemCopy.setAcceptOfferDeadline(null);
        //默认设置打开
        studentOfferItemCopy.setStatus(1);
        studentOfferItemCopy.setRpaOptTime(null);
        studentOfferItemCopy.setRpaFinishTime(null);
        studentOfferItemCopy.setRpaRemark(null);
        studentOfferItemCopy.setStatusRpa(null);
        studentOfferItemCopy.setFkPlatformType(ProjectKeyEnum.GET_BMS.key);
        studentOfferItemCopy.setOldInstitutionName(null);
        studentOfferItemCopy.setOldInstitutionFullName(null);
        studentOfferItemCopy.setIssueCourseInputFlag(null);
        studentOfferItemCopy.setIdGeaFinance(null);
        studentOfferItemCopy.setIdIssue(null);
        studentOfferItemCopy.setIdGea(null);
        studentOfferItemCopy.setIdIae(null);
        //置空修改时间
        studentOfferItemCopy.setGmtModified(null);
        studentOfferItemCopy.setGmtModifiedUser(null);

        //新增学习计划
        utilService.updateUserInfoToEntity(studentOfferItemCopy);
        //获取编号
        studentOfferItemCopy.setIsAddApp(false);
        studentOfferItemCopy.setFkPlatformType(ProjectKeyEnum.GET_BMS.key);
        verifyStudentOfferItemUtils.verifyOfferItemInsert(studentOfferItemCopy);
        studentOfferItemMapper.insertSelective(studentOfferItemCopy);
        studentOfferItemCopy.setNum(GetStringUtils.getStudentOfferItemNum(studentOfferItemCopy.getId()));
        studentOfferItemMapper.updateById(studentOfferItemCopy);

        //如果存在学习计划的子计划,则也进行复制
        List<StudentOfferItem> childStudentOfferItems = studentOfferItemMapper.getChildStudentOfferItem(itemId);
        if (GeneralTool.isNotEmpty(childStudentOfferItems)) {
            for (StudentOfferItem item : childStudentOfferItems) {
                StudentOfferItem childStudentOfferItemCopy = new StudentOfferItem();
                BeanUtils.copyProperties(item, childStudentOfferItemCopy);

                childStudentOfferItemCopy.setId(null);
                //默认设置为新申请
                childStudentOfferItemCopy.setStudentOfferItemStepTime(new Date());
                childStudentOfferItemCopy.setFkStudentOfferItemStepId(1L);
                childStudentOfferItemCopy.setFkIssueRpaOrderId(null);
                childStudentOfferItemCopy.setIsAddApp(null);
                childStudentOfferItemCopy.setConditionType(null);
                childStudentOfferItemCopy.setFkEnrolFailureReasonId(null);
                childStudentOfferItemCopy.setIsDeferEntrance(false);
                childStudentOfferItemCopy.setOtherFailureReason(null);
                childStudentOfferItemCopy.setDepositDeadline(null);
                childStudentOfferItemCopy.setAcceptOfferDeadline(null);
                //默认设置打开
                childStudentOfferItemCopy.setStatus(1);
                childStudentOfferItemCopy.setRpaOptTime(null);
                childStudentOfferItemCopy.setRpaFinishTime(null);
                childStudentOfferItemCopy.setRpaRemark(null);
                childStudentOfferItemCopy.setStatusRpa(null);
                childStudentOfferItemCopy.setFkPlatformType(ProjectKeyEnum.GET_BMS.key);
                childStudentOfferItemCopy.setOldInstitutionName(null);
                childStudentOfferItemCopy.setOldInstitutionFullName(null);
                childStudentOfferItemCopy.setIssueCourseInputFlag(null);
                childStudentOfferItemCopy.setIdGeaFinance(null);
                childStudentOfferItemCopy.setIdIssue(null);
                childStudentOfferItemCopy.setIdGea(null);
                childStudentOfferItemCopy.setIdIae(null);
                //置空修改时间
                childStudentOfferItemCopy.setGmtModified(null);
                childStudentOfferItemCopy.setGmtModifiedUser(null);

                //重新赋值父学习计划
                childStudentOfferItemCopy.setFkParentStudentOfferItemId(studentOfferItemCopy.getId());
                childStudentOfferItemCopy.setFkStudentOfferId(offerId);
                //新增学习计划
                utilService.updateUserInfoToEntity(childStudentOfferItemCopy);
                childStudentOfferItemCopy.setFkPlatformType(ProjectKeyEnum.GET_BMS.key);
                //获取编号
                childStudentOfferItemCopy.setIsAddApp(false);
                childStudentOfferItemCopy.setFkPlatformType(ProjectKeyEnum.GET_BMS.key);
                verifyStudentOfferItemUtils.verifyOfferItemInsert(childStudentOfferItemCopy);
                studentOfferItemMapper.insertSelective(childStudentOfferItemCopy);
                studentOfferItemCopy.setNum(GetStringUtils.getStudentOfferItemNum(childStudentOfferItemCopy.getId()));
                studentOfferItemMapper.updateById(childStudentOfferItemCopy);

                //默认插入第一个申请步骤
                RStudentOfferItemStep rStudentOfferItemStep = new RStudentOfferItemStep();
                rStudentOfferItemStep.setFkStudentOfferItemId(childStudentOfferItemCopy.getId());
                rStudentOfferItemStep.setFkStudentOfferItemStepId(childStudentOfferItemCopy.getFkStudentOfferItemStepId());
                utilService.updateUserInfoToEntity(rStudentOfferItemStep);
                rOfferItemStepMapper.insertSelective(rStudentOfferItemStep);
            }
        }

//        //添加关系表-前置集团
//        List<StudentOfferItemPreInstitutionGroup> studentOfferItemPreInstitutionGroups = studentOfferItemPreInstitutionGroupMapper.selectList(Wrappers.<StudentOfferItemPreInstitutionGroup>lambdaQuery()
//                .eq(StudentOfferItemPreInstitutionGroup::getFkStudentOfferItemId, itemId));
//        if (GeneralTool.isNotEmpty(studentOfferItemPreInstitutionGroups)) {
//            StudentOfferItemPreInstitutionGroup studentOfferItemPreInstitutionGroup = new StudentOfferItemPreInstitutionGroup();
//            studentOfferItemPreInstitutionGroup.setFkStudentOfferItemId(studentOfferItemCopy.getId());
//            studentOfferItemPreInstitutionGroup.setFkInstitutionGroupId(studentOfferItemPreInstitutionGroups.get(0).getFkInstitutionGroupId());
//            utilService.updateUserInfoToEntity(studentOfferItemPreInstitutionGroup);
//            studentOfferItemPreInstitutionGroupMapper.insert(studentOfferItemPreInstitutionGroup);
//        }
//        //添加关系表-前置学校
//        List<StudentOfferItemPreInstitution> studentOfferItemPreInstitutions = studentOfferItemPreInstitutionMapper.selectList(Wrappers.<StudentOfferItemPreInstitution>lambdaQuery()
//                .eq(StudentOfferItemPreInstitution::getFkStudentOfferItemId, itemId));
//        if (GeneralTool.isNotEmpty(studentOfferItemPreInstitutions)) {
//            StudentOfferItemPreInstitution studentOfferItemPreInstitution = new StudentOfferItemPreInstitution();
//            studentOfferItemPreInstitution.setFkStudentOfferItemId(studentOfferItemCopy.getId());
//            studentOfferItemPreInstitution.setFkInstitutionId(studentOfferItemPreInstitutions.get(0).getFkInstitutionId());
//            utilService.updateUserInfoToEntity(studentOfferItemPreInstitution);
//            studentOfferItemPreInstitutionMapper.insert(studentOfferItemPreInstitution);
//        }
//        //添加关系表-前置课程等级
//        List<StudentOfferItemPreMajorLevel> studentOfferItemPreMajorLevels = studentOfferItemPreMajorLevelMapper.selectList(Wrappers.<StudentOfferItemPreMajorLevel>lambdaQuery()
//                .eq(StudentOfferItemPreMajorLevel::getFkStudentOfferItemId, itemId));
//        if (GeneralTool.isNotEmpty(studentOfferItemPreMajorLevels)) {
//            StudentOfferItemPreMajorLevel studentOfferItemPreMajorLevel = new StudentOfferItemPreMajorLevel();
//            studentOfferItemPreMajorLevel.setFkStudentOfferItemId(studentOfferItemCopy.getId());
//            studentOfferItemPreMajorLevel.setFkMajorLevelId(studentOfferItemPreMajorLevels.get(0).getFkMajorLevelId());
//            utilService.updateUserInfoToEntity(studentOfferItemPreMajorLevel);
//            studentOfferItemPreMajorLevelMapper.insert(studentOfferItemPreMajorLevel);
//        }


        //默认插入第一个申请步骤
        RStudentOfferItemStep rStudentOfferItemStep = new RStudentOfferItemStep();
        rStudentOfferItemStep.setFkStudentOfferItemId(studentOfferItemCopy.getId());
        rStudentOfferItemStep.setFkStudentOfferItemStepId(studentOfferItemCopy.getFkStudentOfferItemStepId());
        utilService.updateUserInfoToEntity(rStudentOfferItemStep);
        rOfferItemStepMapper.insertSelective(rStudentOfferItemStep);

        //主动推送的数据
        ConcurrentHashMap<String, List<StudentCountVo>> pushFlagMap = NettyPushConfig.getPushFlagMap();
        List<StudentCountVo> studentCountVos = pushFlagMap.get(ProjectKeyEnum.PUSH_FLAG.key);
        if (GeneralTool.isNotEmpty(studentCountVos)) {
            studentCountVos.add(studentOfferItemService.getStudentCountRecord(studentOfferItemCopy.getId()));
        } else {
            studentCountVos = new ArrayList<>();
            studentCountVos.add(studentOfferItemService.getStudentCountRecord(studentOfferItemCopy.getId()));
        }
        pushFlagMap.put(ProjectKeyEnum.PUSH_FLAG.key, studentCountVos);
        return studentOfferItemCopy.getId();
    }

    /**
     * @Description :feign调用一次查出全部对应国家名称
     * @Param [events]
     * <AUTHOR>
     */
    private Map<Long, String> getCountryNameMap(Set<Long> areaCountryIds) {
        areaCountryIds.removeIf(Objects::isNull);
        //feign调用一次查出全部对应国家名称
        Result<Map<Long, String>> countryNameByIdsResult = institutionCenterClient.getCountryNamesByIds(areaCountryIds);
        if (countryNameByIdsResult.isSuccess() && GeneralTool.isNotEmpty(countryNameByIdsResult.getData())) {
            return countryNameByIdsResult.getData();
        }
        return Maps.newHashMap();
    }

    /**
     * 验证方案下是否有指定步骤id的学习计划
     *
     * @param studentOffer
     * @param stepId
     * @return
     */
    private Boolean validateClosable(StudentOffer studentOffer, Long stepId) {
        //查询申请方案下 对应步骤id的状态为打开的学习计划
        List<StudentOfferItem> studentOfferItems = studentOfferItemMapper.selectList(Wrappers.<StudentOfferItem>lambdaQuery()
                .eq(StudentOfferItem::getFkStudentOfferId, studentOffer.getId())
                .eq(StudentOfferItem::getFkStudentOfferItemStepId, stepId)
                .eq(StudentOfferItem::getStatus, 1));
        if (GeneralTool.isEmpty(studentOfferItems)) {
            //可以关闭
            return true;
        }
        //不可关闭
        return false;
    }

    private void setName(List<StudentOfferVo> collect, Map<Long, String> countryNames) {

        //代理ids
        Set<Long> agentIds = collect.stream().map(StudentOfferVo::getFkAgentId).collect(Collectors.toSet());
        //代理电邮标签
        Set<String> agentLabelEmailList = collect.stream().map(StudentOfferVo::getAgentContactEmail).collect(Collectors.toSet());

        Map<Long, List<AgentLabelVo>> agentLabelMap = getAgentLabelDataUtils.getAgentLabelMap(agentIds).getAgentLabelMap();
        Map<String, List<AgentLabelVo>> agentEamailLabelMap = getAgentLabelDataUtils.getAgentEmailLabelMap(agentLabelEmailList).getAgentEmailLabelMap();


        //根据代理ids获取名称
        Map<Long, String> agentNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(agentIds)) {
            agentNamesByIds = agentService.getAgentNamesByIds(agentIds);
        }

        //员工ids
        Set<Long> staffIds = collect.stream().map(StudentOfferVo::getFkStaffId).collect(Collectors.toSet());
        //根据员工ids获取名称
        Map<Long, String> staffNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(staffIds)) {
            Result<Map<Long, String>> staffResult = permissionCenterClient.getStaffNameMap(staffIds);
            if (staffResult.isSuccess() && staffResult.getData() != null) {
                staffNamesByIds = staffResult.getData();
            }
        }

        //申请方案ids
        Set<Long> ids = collect.stream().map(StudentOfferVo::getId).collect(Collectors.toSet());
        //根据申请方案ids获取学习计划学校tab
        //获取每个offer对应的学校
        //1. 没有获取到对应的学校tab
        Map<Long, List<InstitutionTabVo>> offerItemInstitutionTabMap = studentOfferItemService.getOfferItemInstitutionTab(ids);

        List<StudentItemInfoVo> itemAndStepDtoList = offerMapper.getItemAndStepsByIds(ids, SecureUtil.getStaffInfo().getIsStudentOfferItemFinancialHiding());
        //2. 没有获取到itemAndStepDtos
        Map<Long, List<StudentItemInfoVo>> itemAndStepDtosMap = itemAndStepDtoList.stream().collect(Collectors.groupingBy(StudentItemInfoVo::getFkStudentOfferId));

        List<StudentOfferItem> studentOfferItems = studentOfferItemMapper.selectList(Wrappers.<StudentOfferItem>lambdaQuery().in(StudentOfferItem::getFkStudentOfferId, ids));
        Map<Long, Date> maxDeferEntranceTimeMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(studentOfferItems)) {

            Set<Long> itemIds = studentOfferItems.stream().map(StudentOfferItem::getId).collect(Collectors.toSet());

            List<StudentOfferItemVo> maxDeferEntranceTimeDtos = studentOfferItemMapper.getMaxDeferEntranceTime(itemIds);
            maxDeferEntranceTimeMap = maxDeferEntranceTimeDtos.stream().collect(Collectors.toMap(StudentOfferItemVo::getId, StudentOfferItemVo::getMaxDeferEntranceTimes));
        }
        //TODO 改过
        //Set<Long> courseIds = itemAndStepDtoList.stream().map(StudentItemAndStepVo::getFkInstitutionCourseId).collect(Collectors.toSet());

        Set<Long> courseIds = itemAndStepDtoList.stream().map(StudentItemInfoVo::getFkInstitutionCourseId).collect(Collectors.toSet());
        /**
         * 如果 itemAndStepDtoList 中没有有效的 fkInstitutionId，则无法获取学校名称。
         *
         * 如果 itemAndStepDtoList 中没有有效的 fkStudentId，则无法获取学生名称。
         *
         * 如果 itemAndStepDtoList 中没有有效的 fkInstitutionCourseId，则无法获取课程名称。
         * */

        //课程id
        Result<Map<Long, String>> courseNameByIds = institutionCenterClient.getCourseNameByIds(courseIds);
        Map<Long, String> courseMap = new HashMap<>();
        if (courseNameByIds.isSuccess() && GeneralTool.isNotEmpty(courseNameByIds.getData())) {
            courseMap = courseNameByIds.getData();
        }
        //TODO 改过
        //Set<Long> institutionIds = itemAndStepDtoList.stream().map(StudentItemAndStepVo::getFkInstitutionId).collect(Collectors.toSet());
        Set<Long> institutionIds = itemAndStepDtoList.stream().map(StudentItemInfoVo::getFkInstitutionId).collect(Collectors.toSet());

        Map<Long, String> institutionNameMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(institutionIds)) {
            Result<Map<Long, String>> institutionNameByIds = institutionCenterClient.getInstitutionNamesByIds(institutionIds);
            if (institutionNameByIds.isSuccess() && GeneralTool.isNotEmpty(institutionNameByIds.getData())) {
                institutionNameMap = institutionNameByIds.getData();
            }
        }
        //TODO  改过
        //Set<Long> studentIds = itemAndStepDtoList.stream().map(StudentItemAndStepVo::getFkStudentId).collect(Collectors.toSet());
        Set<Long> studentIds = itemAndStepDtoList.stream().map(StudentItemInfoVo::getFkStudentId).collect(Collectors.toSet());
        Map<Long, String> studentNameMap = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(studentIds)) {
            List<StudentVo> studentZhEnNameByIds = studentMapper.getStudentZhEnNameByIds(studentIds);
            studentNameMap = studentZhEnNameByIds.stream().collect(Collectors.toMap(StudentVo::getId, StudentVo::getFullName));
        }

        for (StudentOfferVo offerDto : collect) {
            offerDto.setAgentEmailLabelVos(agentEamailLabelMap.get(offerDto.getEmail()));
            setAgentAndAgentLabelAndBdNameList(offerDto, agentNamesByIds, staffNamesByIds, agentLabelMap);
            //学习计划 先找出这个学习申请方案下的学习计划
            List<StudentItemInfoVo> itemAndStepDtos = itemAndStepDtosMap.get(offerDto.getId());
            List<InstitutionTabVo> baseSelectEntities = offerItemInstitutionTabMap.get(offerDto.getId());
            Map<Long, InstitutionTabVo> insMap = new HashMap<>();
            if (GeneralTool.isNotEmpty(baseSelectEntities)) {
                //学校名称
                insMap = baseSelectEntities.stream().collect(Collectors.toMap(InstitutionTabVo::getOfferItemId, Function.identity()));
            }
            if (GeneralTool.isNotEmpty(itemAndStepDtos)) {
                //移除空步骤元素
                itemAndStepDtos.removeIf(studentItemAndStepDto -> GeneralTool.isEmpty(studentItemAndStepDto.getStepName()));
                //子学习计划(次级课程) 父学习计划id - 子学习计划
                //TODO 改过
//                Map<Long, List<StudentItemAndStepVo>> childStepDtos = itemAndStepDtos.stream().filter(studentItemAndStepDto -> GeneralTool.isNotEmpty(studentItemAndStepDto.getFkParentStudentOfferItemId()))
//                        .collect(Collectors.groupingBy(StudentItemAndStepVo::getFkParentStudentOfferItemId));
                Map<Long, List<StudentItemInfoVo>> childStepDtos = itemAndStepDtos.stream().filter(studentItemAndStepDto -> GeneralTool.isNotEmpty(studentItemAndStepDto.getFkParentStudentOfferItemId()))
                        .collect(Collectors.groupingBy(StudentItemInfoVo::getFkParentStudentOfferItemId));
                //遍历学习计划 设置课程名字 如有子计划则插入子计划
                for (StudentItemInfoVo itemAndStepDto : itemAndStepDtos) {
                    //--设置最大延迟入学时间
                    if (GeneralTool.isNotEmpty(itemAndStepDto.getIsDeferEntrance())) {
                        if (itemAndStepDto.getIsDeferEntrance()) {
                            //设置最大延迟入学时间
                            itemAndStepDto.setMaxDeferEntranceTimes(maxDeferEntranceTimeMap.get(itemAndStepDto.getId()));
                        }
                    }

                    if (GeneralTool.isNotEmpty(itemAndStepDto.getFkInstitutionId())) {
                        itemAndStepDto.setFkInstitutionName(institutionNameMap.get(itemAndStepDto.getFkInstitutionId()));
                    }
                    if (GeneralTool.isNotEmpty(itemAndStepDto.getFkStudentId())) {
                        //设置学生名称
                        itemAndStepDto.setFkStudentName(studentNameMap.get(itemAndStepDto.getFkStudentId()));
                    }

                    //设置课程名称
                    String courseName = null;
                    if (GeneralTool.isNotEmpty(itemAndStepDto.getFkInstitutionCourseId())) {
                        courseName = courseMap.get(itemAndStepDto.getFkInstitutionCourseId());
                    }
                    if (GeneralTool.isNotEmpty(itemAndStepDto.getDurationType()) && GeneralTool.isNotEmpty(itemAndStepDto.getDuration())) {
                        courseName = courseName + " (" + itemAndStepDto.getDuration().doubleValue() + ProjectExtraEnum.getValueByKey(itemAndStepDto.getDurationType(), ProjectExtraEnum.DURATION_TYPE) + ")";
                    }
                    itemAndStepDto.setInstitutionInfo(insMap.get(itemAndStepDto.getId()));
                    itemAndStepDto.setInstitutionCourseName(courseName);
                    itemAndStepDto.setFkCourseName(courseName);
                    if (GeneralTool.isNotEmpty(itemAndStepDto.getOldCourseCustomName()) && GeneralTool.isNotEmpty(itemAndStepDto.getDurationType()) && GeneralTool.isNotEmpty(itemAndStepDto.getDuration())) {
                        itemAndStepDto.setOldCourseCustomName(itemAndStepDto.getOldCourseCustomName() + " (" + itemAndStepDto.getDuration().doubleValue() + ProjectExtraEnum.getValueByKey(itemAndStepDto.getDurationType(), ProjectExtraEnum.DURATION_TYPE) + ")");
                    }
                    if (GeneralTool.isNotEmpty(childStepDtos)) {
                        //TODO 改过
                        //List<StudentItemAndStepVo> childItemAndStepDtos = childStepDtos.get(itemAndStepDto.getId());
                        List<StudentItemInfoVo> childItemAndStepDtos = childStepDtos.get(itemAndStepDto.getId());
                        if (GeneralTool.isNotEmpty(childItemAndStepDtos)) {
                            //TODO 改过
                            //childItemAndStepDtos = childItemAndStepDtos.stream().sorted(Comparator.comparing(StudentItemAndStepVo::getGmtCreate)).collect(Collectors.toList());
                            childItemAndStepDtos = childItemAndStepDtos.stream().sorted(Comparator.comparing(StudentItemInfoVo::getGmtCreate)).collect(Collectors.toList());
                        }
                        //插入子学习计划
                        itemAndStepDto.setChildItemAndStepDtos(childItemAndStepDtos);
                    }
                }
                //TODO 改过
                //List<StudentItemAndStepVo> parentItemDtos = itemAndStepDtos.stream().filter(studentItemAndStepDto -> GeneralTool.isEmpty(studentItemAndStepDto.getFkParentStudentOfferItemId())).collect(Collectors.toList());
                List<StudentItemInfoVo> parentItemDtos = itemAndStepDtos.stream().filter(studentItemAndStepDto -> GeneralTool.isEmpty(studentItemAndStepDto.getFkParentStudentOfferItemId())).collect(Collectors.toList());
                //TODO 改过StudentItemAndStepVo
                for (StudentItemInfoVo parentItemDto : parentItemDtos) {
                    List<StudentOfferItem> studentOfferItems1 = studentOfferItemMapper.selectList(Wrappers.<StudentOfferItem>lambdaQuery().eq(StudentOfferItem::getFkParentStudentOfferItemId, parentItemDto.getId()));
                    if (GeneralTool.isNotEmpty(studentOfferItems1)) {
                        for (StudentOfferItem studentOfferItem : studentOfferItems1) {
                            if (!studentOfferItem.getIsFollowHidden() && studentOfferItem.getIsFollow()) {
                                Result<InstitutionCourseVo> courseById = institutionCenterClient.getCourseById(studentOfferItem.getFkInstitutionCourseId());
                                if (courseById.getData().getName() != null) {
                                    parentItemDto.setInstitutionCourseName(parentItemDto.getInstitutionCourseName() + " + " + courseById.getData().getName());
                                    parentItemDto.setFkCourseName(parentItemDto.getFkCourseName() + " + " + courseById.getData().getName());
                                }
                            }
                        }
                    }


                    if (GeneralTool.isNotEmpty(parentItemDto.getFkInstitutionId())) {
                        //设置学校名
                        String institutionName = parentItemDto.getFkInstitutionName();
                        parentItemDto.setFkInstitutionName(institutionName);

                        //设置学校业务标签
                        Map<Long, NameLabel> nameLabelMap = nameLabelService.getNameLabelListByFkTableName(TableEnum.INSTITUTION.key);
                        if (GeneralTool.isNotEmpty(nameLabelMap)) {
                            if (GeneralTool.isNotEmpty(nameLabelMap.get(parentItemDto.getFkInstitutionId()))) {
                                NameLabel nameLabel = nameLabelMap.get(parentItemDto.getFkInstitutionId());
                                parentItemDto.setLabel(nameLabel.getLabel());
                                parentItemDto.setPositionType(nameLabel.getPositionType());
                                if (nameLabel.getPositionType() == 1) {
                                    parentItemDto.setFkInstitutionName(parentItemDto.getFkInstitutionName() + " " + parentItemDto.getLabel());
                                    parentItemDto.getInstitutionInfo().setName(parentItemDto.getFkInstitutionName());
                                } else {
                                    parentItemDto.setFkInstitutionName(parentItemDto.getLabel() + " " + parentItemDto.getFkInstitutionName());
                                    parentItemDto.getInstitutionInfo().setName(parentItemDto.getFkInstitutionName());
                                }

                            }
                        }

                    }

                }
                //TODO 改过
//                parentItemDtos = parentItemDtos.stream().sorted(Comparator.comparing(StudentItemAndStepVo::getGmtCreate,Comparator.nullsLast(Date::compareTo))).collect(Collectors.toList());
//                offerDto.setStudentItemAndStepDtos(parentItemDtos);
                parentItemDtos = parentItemDtos.stream().sorted(Comparator.comparing(StudentItemInfoVo::getGmtCreate, Comparator.nullsLast(Date::compareTo))).collect(Collectors.toList());
                offerDto.setStudentItemAndStepDtos(parentItemDtos);
            }
            //只显示父学习计划
            //学习计划学校
            offerDto.setInstitutionTabs(offerItemInstitutionTabMap.get(offerDto.getId()));
            offerDto.setFkAreaCountryName(countryNames.get(offerDto.getFkAreaCountryId()));


//            if (GeneralTool.isNotEmpty(baseSelectEntities)) {
//                //父学习计划id - 子学习计划   先用着，之前代码写的乱七八糟 着急上线重构不了 垃圾
//                Map<Long, List<InstitutionTabVo>> childInstitutionBaseSelectEntityMap = new HashMap<>();
//                for (InstitutionTabVo baseSelectEntity : baseSelectEntities) {
//                    if (GeneralTool.isNotEmpty(baseSelectEntity.getIsFollow())) {
//                        if (!baseSelectEntity.getIsFollow()) {
//                            childInstitutionBaseSelectEntityMap.computeIfAbsent(baseSelectEntity.getParentId(),k -> new ArrayList<>()).add(baseSelectEntity);
//                        }
//                    } else {
//                        childInstitutionBaseSelectEntityMap.computeIfAbsent(baseSelectEntity.getParentId(),k -> new ArrayList<>()).add(baseSelectEntity);
//                    }
//                }
//                List<InstitutionAndItemVo> institutionAndItemDtos = new ArrayList<>();
//                //只保留父学习计划进行for循环
//                baseSelectEntities = baseSelectEntities.stream().filter(baseSelectEntity -> GeneralTool.isEmpty(baseSelectEntity.getParentId())).collect(Collectors.toList());
//
//                for (InstitutionTabVo outerTabs : baseSelectEntities) {
//                    Long offerItemId = outerTabs.getOfferItemId();
//                    InstitutionAndItemVo institutionAndItemDto = new InstitutionAndItemVo();
//                    List<StudentItemAndStepVo> studentItemAndStepDtos = new ArrayList<>();
//                    //遍历父学习计划
//                    if (GeneralTool.isNotEmpty(parentItemDtos)) {
//                        for (StudentItemAndStepVo studentItemAndStepDto : parentItemDtos) {
//                            if (offerItemId.equals(studentItemAndStepDto.getId())) {
//                                studentItemAndStepDtos.add(studentItemAndStepDto);
//                            }
//                            //开学时间倒序
//                            studentItemAndStepDtos = studentItemAndStepDtos.stream().sorted(Comparator.comparing(StudentItemAndStepVo::getOpeningTime)).collect(Collectors.toList());
//                            institutionAndItemDto.setStudentItemAndStepDtos(studentItemAndStepDtos);
//                        }
//                    }
//                    List<InstitutionTabVo> innerTabs = childInstitutionBaseSelectEntityMap.get(outerTabs.getOfferItemId());
//
//                    //修改baseSelectEntityList的排序使得与item的排序对应  当前学校对应的子学习计划
//                    List<StudentItemAndStepVo> vo = new ArrayList<>();
//                    if (GeneralTool.isNotEmpty(institutionAndItemDto.getStudentItemAndStepDtos())) {
//                        vo = institutionAndItemDto.getStudentItemAndStepDtos().stream()
//                                .filter(studentItemAndStepDto -> studentItemAndStepDto.getId().equals(offerItemId))
//                                .collect(Collectors.toList());
//                    }
//                    if (GeneralTool.isNotEmpty(vo)) {
//                        if (GeneralTool.isNotEmpty(vo.get(0).getStudentItemAndStepDtos())) {
//                            List<InstitutionTabVo> sortedInnerTabs = new ArrayList<>();
//                            List<Long> sortedIds = vo.get(0).getStudentItemAndStepDtos().stream().map(StudentItemAndStepVo::getId).collect(Collectors.toList());
//                            for (Long sortedId : sortedIds) {
//                                if (GeneralTool.isNotEmpty(innerTabs)) {
//                                    for (InstitutionTabVo innerTab : innerTabs) {
//                                        if (sortedId.equals(innerTab.getOfferItemId())) {
//                                            sortedInnerTabs.add(innerTab);
//                                        }
//                                    }
//                                }
//                            }
//                            //排序后的赋值回去
//                            innerTabs = sortedInnerTabs;
//                        }
//                    }
//                    outerTabs.setInstitutionTabs(innerTabs);
//                    institutionAndItemDto.setInstitutionTabs(outerTabs);
//                    institutionAndItemDtos.add(institutionAndItemDto);
//                }
//                offerDto.setInstitutionAndItemDtos(institutionAndItemDtos);
//            }
        }
    }

    private void setAgentAndBdNameList(StudentOfferVo offerDto, Map<Long, String> agentNamesByIds,
                                       Map<Long, String> staffNamesByIds) {
        if (GeneralTool.isNotEmpty(offerDto.getFkAgentId())) {
            offerDto.setFkAgentName(agentNamesByIds.get(offerDto.getFkAgentId()));
        }
        if (GeneralTool.isNotEmpty(offerDto.getFkStaffId())) {
            //bd名称
            String staffName = staffNamesByIds.get(offerDto.getFkStaffId());
            offerDto.setBdName(staffName);
        }
    }

    private void setAgentAndAgentLabelAndBdNameList(StudentOfferVo offerDto, Map<Long, String> agentNamesByIds,
                                                    Map<Long, String> staffNamesByIds, Map<Long, List<AgentLabelVo>> agentLabelMap) {
        if (GeneralTool.isNotEmpty(offerDto.getFkAgentId())) {
            offerDto.setFkAgentName(agentNamesByIds.get(offerDto.getFkAgentId()));
            offerDto.setAgentLabelVos(agentLabelMap.get(offerDto.getFkAgentId()));
        }
        if (GeneralTool.isNotEmpty(offerDto.getFkStaffId())) {
            //bd名称
            String staffName = staffNamesByIds.get(offerDto.getFkStaffId());
            offerDto.setBdName(staffName);
        }
    }

    private void setAgentAndBdName(StudentOfferVo offerDto) {
        if (GeneralTool.isNotEmpty(offerDto.getFkAgentId())) {
            Set<Long> ids = new HashSet<>();
            ids.add(offerDto.getFkAgentId());
            Map<Long, Agent> agentMap = agentService.getAgentsByIds(ids);
            if (GeneralTool.isNotEmpty(agentMap)) {
                Agent agent = agentMap.get(offerDto.getFkAgentId());
                if (GeneralTool.isNotEmpty(agent.getNameNote())) {
                    offerDto.setFkAgentName(agent.getName() + "（" + agent.getNameNote() + "）");
                } else {
                    offerDto.setFkAgentName(agent.getName());
                }
            }
        }
        if (GeneralTool.isNotEmpty(offerDto.getFkStaffId())) {
            //bd名称
            Result<String> result = permissionCenterClient.getStaffName(offerDto.getFkStaffId());
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                offerDto.setBdName(result.getData());
            }

        }
    }

    private void setProjectRoleStaff(StudentOfferDto offerVo, StudentOffer offer) {
        if (GeneralTool.isNotEmpty(offerVo.getRoleStaffVo())) {
            List<ProjectRoleStaffDto> roleStaffVos = offerVo.getRoleStaffVo();
            for (ProjectRoleStaffDto roleStaffVo : roleStaffVos) {
                StudentProjectRoleStaffDto projectRoleStaffVo = new StudentProjectRoleStaffDto();

                projectRoleStaffVo.setFkTableId(offer.getId());
                projectRoleStaffVo.setFkTableName(TableEnum.SALE_STUDENT_OFFER.key);
                projectRoleStaffVo.setFkStaffId(roleStaffVo.getFkStaffId());
                projectRoleStaffVo.setFkStudentProjectRoleId(roleStaffVo.getFkRoleId());
                projectRoleStaffVo.setIsActive(true);

                projectRoleStaffService.addProjectRoleStaff(projectRoleStaffVo);
            }
        }
    }

    /**
     * @Date 11:32 2021/6/24
     * <AUTHOR>
     */
    private List<Long> getStudentIdList() {
        //获取代理以及旗下代理所能看到的学生ids
        List<Long> studentIdList = studentAgentService.getStudentIds();
        if (GeneralTool.isEmpty(studentIdList)) {
            studentIdList = new ArrayList<>();
            studentIdList.add(0L);
        }
        //获取员工以及旗下员工所创建的学生ids
        Long staffId = GetAuthInfo.getStaffId();
        List<Long> staffFollowerIds = new ArrayList<>();
        Result<List<Long>> result = permissionCenterClient.getStaffFollowerIds(staffId);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            staffFollowerIds.addAll(result.getData());
        }
        staffFollowerIds.add(staffId);
        Set<Long> ids = new HashSet<>(staffFollowerIds);
        Map<Long, String> longIdMap = new HashMap<>();
        Result<Map<Long, String>> staffLoginIdResult = permissionCenterClient.getStaffLoginIdByIds(ids);
        if (staffLoginIdResult.isSuccess() && GeneralTool.isNotEmpty(staffLoginIdResult.getData())) {
            longIdMap = staffLoginIdResult.getData();
            List<String> userNames = new ArrayList<>(longIdMap.values());
            List<Student> studentList = studentMapper.selectList(Wrappers.<Student>lambdaQuery().in(Student::getGmtCreateUser, userNames));
            studentIdList.addAll(studentList.stream().map(Student::getId).collect(Collectors.toList()));
        }
//        Example example = new Example(Student.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andIn("gmtCreateUser", userNames);
//        List<Student> studentList = studentMapper.selectByExample(example);

        //获取当前登录人以及旗下员工所在项目组的学生
        studentIdList.addAll(studentProjectRoleStaffMapper.getStudetnIdsByStaffId(staffFollowerIds));
        return studentIdList;
    }

    private void setOfferDtoName(List<StudentOfferVo> collect, Map<Long, String> countryNames, Map<Long, Agent> agentsByIds) {

        //代理ids
        Set<Long> agentIds = collect.stream().map(StudentOfferVo::getFkAgentId).collect(Collectors.toSet());
        //根据代理ids获取名称
        Map<Long, String> agentNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(agentIds)) {
            agentNamesByIds = agentService.getAgentNamesByIds(agentIds);
        }

        //员工ids
        Set<Long> staffIds = collect.stream().map(StudentOfferVo::getFkStaffId).collect(Collectors.toSet());
        //根据员工ids获取名称
        Map<Long, String> staffNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(staffIds)) {
//            staffNamesByIds = permissionCenterClient.getStaffNameMap(staffIds);
            Result<Map<Long, String>> staffResult = permissionCenterClient.getStaffNameMap(staffIds);
            if (staffResult.isSuccess() && staffResult.getData() != null) {
                staffNamesByIds = staffResult.getData();
            }
        }

        //申请方案ids
        Set<Long> ids = collect.stream().map(StudentOfferVo::getId).collect(Collectors.toSet());
        //根据申请方案ids获取学习计划学校tab
        //获取每个offer对应的学校
        Map<Long, List<InstitutionTabVo>> offerItemInstitutionTabMap = studentOfferItemService.getOfferItemInstitutionTab(ids);

        List<StudentItemInfoVo> itemAndStepDtoList = offerMapper.getItemAndStepsByIds(ids, SecureUtil.getStaffInfo().getIsStudentOfferItemFinancialHiding());
//        if (GeneralTool.isNotEmpty(SecureUtil.getInstitutionIds())&&GeneralTool.isNotEmpty(itemAndStepDtoList)) {
//            itemAndStepDtoList = itemAndStepDtoList.stream().filter(i->SecureUtil.getInstitutionIds().contains(i.getFkInstitutionId())).collect(Collectors.toList());
//        }
        Map<Long, List<StudentItemInfoVo>> itemAndStepDtosMap = itemAndStepDtoList.stream().collect(Collectors.groupingBy(StudentItemInfoVo::getFkStudentOfferId));

        List<StudentOfferItem> studentOfferItems = studentOfferItemMapper.selectList(Wrappers.<StudentOfferItem>lambdaQuery().in(StudentOfferItem::getFkStudentOfferId, ids));
//        if (GeneralTool.isNotEmpty(SecureUtil.getInstitutionIds())&&GeneralTool.isNotEmpty(studentOfferItems)) {
//            studentOfferItems = studentOfferItems.stream().filter(i->SecureUtil.getInstitutionIds().contains(i.getFkInstitutionId())).collect(Collectors.toList());
//        }
        Map<Long, Date> maxDeferEntranceTimeMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(studentOfferItems)) {
            Set<Long> itemIds = studentOfferItems.stream().map(StudentOfferItem::getId).collect(Collectors.toSet());
            List<StudentOfferItemVo> maxDeferEntranceTimeDtos = studentOfferItemMapper.getMaxDeferEntranceTime(itemIds);
            maxDeferEntranceTimeMap = maxDeferEntranceTimeDtos.stream().collect(Collectors.toMap(StudentOfferItemVo::getId, StudentOfferItemVo::getMaxDeferEntranceTimes));
        }
        Long fkAgentId;
        String nameNote;
        for (StudentOfferVo offerDto : collect) {
            setAgentAndBdNameList(offerDto, agentNamesByIds, staffNamesByIds);
            fkAgentId = offerDto.getFkAgentId();
            if (agentsByIds.containsKey(fkAgentId)) {
                nameNote = agentsByIds.get(fkAgentId).getNameNote();
                if (StringUtils.isNotBlank(nameNote)) {
                    offerDto.setFkAgentName(offerDto.getFkAgentName() + "(" + nameNote + ")");
                }
            }
            //学习计划 先找出这个学习申请方案下的学习计划
            List<StudentItemInfoVo> itemAndStepDtos = itemAndStepDtosMap.get(offerDto.getId());
            if (GeneralTool.isNotEmpty(itemAndStepDtos)) {
                //移除空步骤元素
                itemAndStepDtos.removeIf(studentItemAndStepDto -> GeneralTool.isEmpty(studentItemAndStepDto.getStepName()));
                //子学习计划(次级课程) 父学习计划id - 子学习计划
                //TODO 改过
//                Map<Long, List<StudentItemAndStepVo>> childStepDtos = itemAndStepDtos.stream().filter(studentItemAndStepDto -> GeneralTool.isNotEmpty(studentItemAndStepDto.getFkParentStudentOfferItemId()))
//                        .collect(Collectors.groupingBy(StudentItemAndStepVo::getFkParentStudentOfferItemId));

                Map<Long, List<StudentItemInfoVo>> childStepDtos = itemAndStepDtos.stream().filter(studentItemAndStepDto -> GeneralTool.isNotEmpty(studentItemAndStepDto.getFkParentStudentOfferItemId()))
                        .collect(Collectors.groupingBy(StudentItemInfoVo::getFkParentStudentOfferItemId));
                List<InstitutionTabVo> baseSelectEntities = offerItemInstitutionTabMap.get(offerDto.getId());
                Map<Long, InstitutionTabVo> insMap = new HashMap<>();
                if (GeneralTool.isNotEmpty(baseSelectEntities)) {
                    insMap = baseSelectEntities.stream().collect(Collectors.toMap(InstitutionTabVo::getOfferItemId, Function.identity()));
                }
                //--设置最大延迟入学时间
                //TODO 改过 StudentItemAndStepVo
                for (StudentItemInfoVo itemAndStepDto : itemAndStepDtos) {
                    if (GeneralTool.isNotEmpty(itemAndStepDto.getIsDeferEntrance())) {
                        if (itemAndStepDto.getIsDeferEntrance()) {
                            //设置最大延迟入学时间
                            itemAndStepDto.setMaxDeferEntranceTimes(maxDeferEntranceTimeMap.get(itemAndStepDto.getId()));
                        }
                    }
                }
                //---
//                Set<Long> customCourseIds = new HashSet<>();
//                itemAndStepDtos.forEach(studentItemAndStepDto -> {
//                    if (GeneralTool.isNotEmpty(studentItemAndStepDto.getFkInstitutionCourseCustomId())) {
//                        customCourseIds.add(studentItemAndStepDto.getFkInstitutionCourseCustomId());
//                    }
//                });
                //遍历学习计划 设置课程名字 如有子计划则插入子计划
                for (StudentItemInfoVo itemAndStepDto : itemAndStepDtos) {
                    //设置课程名称
                    String courseName = null;
                    if (GeneralTool.isNotEmpty(itemAndStepDto.getFkInstitutionCourseId())) {
                        Result<String> result2 = institutionCenterClient.getCourseNameById(itemAndStepDto.getFkInstitutionCourseId());
                        if (result2.isSuccess() && GeneralTool.isNotEmpty(result2.getData())) {
                            courseName = result2.getData();
                        }
                    }
                    if (GeneralTool.isNotEmpty(itemAndStepDto.getDurationType()) && GeneralTool.isNotEmpty(itemAndStepDto.getDuration())) {
                        courseName = courseName + " (" + itemAndStepDto.getDuration().doubleValue() + ProjectExtraEnum.getValueByKey(itemAndStepDto.getDurationType(), ProjectExtraEnum.DURATION_TYPE) + ")";
                    }
                    itemAndStepDto.setInstitutionInfo(insMap.get(itemAndStepDto.getId()));
                    itemAndStepDto.setInstitutionCourseName(courseName);
                    if (GeneralTool.isNotEmpty(itemAndStepDto.getOldCourseCustomName()) && GeneralTool.isNotEmpty(itemAndStepDto.getDurationType()) && GeneralTool.isNotEmpty(itemAndStepDto.getDuration())) {
                        itemAndStepDto.setOldCourseCustomName(itemAndStepDto.getOldCourseCustomName() + " (" + itemAndStepDto.getDuration().doubleValue() + ProjectExtraEnum.getValueByKey(itemAndStepDto.getDurationType(), ProjectExtraEnum.DURATION_TYPE) + ")");
                    }
                    if (GeneralTool.isNotEmpty(childStepDtos)) {
                        //TODO 改过
                        //List<StudentItemAndStepVo> childItemAndStepDtos = childStepDtos.get(itemAndStepDto.getId());
                        List<StudentItemInfoVo> childItemAndStepDtos = childStepDtos.get(itemAndStepDto.getId());
                        if (GeneralTool.isNotEmpty(childItemAndStepDtos)) {
                            //TODO 改过
                            //childItemAndStepDtos = childItemAndStepDtos.stream().sorted(Comparator.comparing(StudentItemAndStepVo::getGmtCreate)).collect(Collectors.toList());
                            childItemAndStepDtos = childItemAndStepDtos.stream().sorted(Comparator.comparing(StudentItemInfoVo::getGmtCreate)).collect(Collectors.toList());

                        }
                        //插入子学习计划
                        itemAndStepDto.setChildItemAndStepDtos(childItemAndStepDtos);
                    }
                }
                //TODO 改过
                //List<StudentItemAndStepVo> parentItemDtos = itemAndStepDtos.stream().filter(studentItemAndStepDto -> GeneralTool.isEmpty(studentItemAndStepDto.getFkParentStudentOfferItemId())).collect(Collectors.toList());
                List<StudentItemInfoVo> parentItemDtos = itemAndStepDtos.stream().filter(studentItemAndStepDto -> GeneralTool.isEmpty(studentItemAndStepDto.getFkParentStudentOfferItemId())).collect(Collectors.toList());
                //TODO 改过 StudentItemAndStepVo
                for (StudentItemInfoVo parentItemDto : parentItemDtos) {
                    List<StudentOfferItem> studentOfferItems1 = studentOfferItemMapper.selectList(Wrappers.<StudentOfferItem>lambdaQuery().eq(StudentOfferItem::getFkParentStudentOfferItemId, parentItemDto.getId()));
                    if (GeneralTool.isNotEmpty(studentOfferItems1)) {
                        for (StudentOfferItem studentOfferItem : studentOfferItems1) {
                            if (!studentOfferItem.getIsFollowHidden() && studentOfferItem.getIsFollow()) {
                                Result<InstitutionCourseVo> courseById = institutionCenterClient.getCourseById(studentOfferItem.getFkInstitutionCourseId());
                                if (courseById.getData().getName() != null) {
                                    parentItemDto.setInstitutionCourseName(parentItemDto.getInstitutionCourseName() + " + " + courseById.getData().getName());
                                    parentItemDto.setFkCourseName(parentItemDto.getFkCourseName() + " + " + courseById.getData().getName());
                                }
                            }
                        }
                    }
                }
                //TODO 改过
//                parentItemDtos = parentItemDtos.stream().sorted(Comparator.comparing(StudentItemAndStepVo::getGmtCreate,Comparator.nullsLast(Date::compareTo))).collect(Collectors.toList());
//                offerDto.setStudentItemAndStepDtos(parentItemDtos);
                parentItemDtos = parentItemDtos.stream().sorted(Comparator.comparing(StudentItemInfoVo::getGmtCreate, Comparator.nullsLast(Date::compareTo))).collect(Collectors.toList());
                offerDto.setStudentItemAndStepDtos(parentItemDtos);

            }
//            //只显示父学习计划
//            List<StudentItemAndStepVo> parentItemDtos;
//            if (GeneralTool.isNotEmpty(itemAndStepDtos)) {
//                parentItemDtos = itemAndStepDtos.stream().filter(studentItemAndStepDto -> GeneralTool.isEmpty(studentItemAndStepDto.getFkParentStudentOfferItemId())).collect(Collectors.toList());
//                if (GeneralTool.isNotEmpty(parentItemDtos)){
////                    parentItemDtos = parentItemDtos.stream().sorted(Comparator.comparing(StudentItemAndStepVo::getOpeningTime)).collect(Collectors.toList());
//                    //开学时间倒序
//                    List<StudentItemAndStepVo> opTimeNotNull;
//                    List<StudentItemAndStepVo> opTimeIsNull;
//                    List<StudentItemAndStepVo> sortedList = new ArrayList<>();
//                    opTimeNotNull = parentItemDtos.stream().filter(s->GeneralTool.isNotEmpty(s.getGmtCreate())).sorted(Comparator.comparing(StudentItemAndStepVo::getGmtCreate)).collect(Collectors.toList());
//                    opTimeIsNull = parentItemDtos.stream().filter(s->GeneralTool.isEmpty(s.getGmtCreate())).sorted(Comparator.comparing(StudentItemAndStepVo::getId).reversed()).collect(Collectors.toList());
//                    sortedList.addAll(opTimeNotNull;
//                    sortedList.addAll(opTimeIsNull);
//                    parentItemDtos = sortedList;
//                }
//                offerDto.setStudentItemAndStepDtos(parentItemDtos);
//            }
            //学习计划学校
            offerDto.setInstitutionTabs(offerItemInstitutionTabMap.get(offerDto.getId()));
            offerDto.setFkAreaCountryName(countryNames.get(offerDto.getFkAreaCountryId()));

//            List<InstitutionTabVo> baseSelectEntities = offerItemInstitutionTabMap.get(offerDto.getId());
//
//            if (GeneralTool.isNotEmpty(baseSelectEntities)) {
//                //父学习计划id - 子学习计划   先用着，之前代码写的乱七八糟 着急上线重构不了 垃圾
//                Map<Long, List<InstitutionTabVo>> childInstitutionBaseSelectEntityMap = new HashMap<>();
//                for (InstitutionTabVo baseSelectEntity : baseSelectEntities) {
//                    if (GeneralTool.isNotEmpty(baseSelectEntity.getIsFollow())) {
//                        if (!baseSelectEntity.getIsFollow()) {
//                            List<InstitutionTabVo> childInstitutionBaseSelectEntityList = childInstitutionBaseSelectEntityMap.get(baseSelectEntity.getParentId());
//                            if (GeneralTool.isEmpty(childInstitutionBaseSelectEntityList)) {
//                                childInstitutionBaseSelectEntityList = new ArrayList<>();
//                            }
//                            childInstitutionBaseSelectEntityList.add(baseSelectEntity);
//                            childInstitutionBaseSelectEntityMap.put(baseSelectEntity.getParentId(), childInstitutionBaseSelectEntityList);
//                        }
//                    } else {
//                        List<InstitutionTabVo> childInstitutionBaseSelectEntityList = childInstitutionBaseSelectEntityMap.get(baseSelectEntity.getParentId());
//                        if (GeneralTool.isEmpty(childInstitutionBaseSelectEntityList)) {
//                            childInstitutionBaseSelectEntityList = new ArrayList<>();
//                        }
//                        childInstitutionBaseSelectEntityList.add(baseSelectEntity);
//                        childInstitutionBaseSelectEntityMap.put(baseSelectEntity.getParentId(), childInstitutionBaseSelectEntityList);
//                    }
//                }
////                baseSelectEntities.stream().filter(baseSelectEntity -> GeneralTool.isNotEmpty(baseSelectEntity.getParentId())).forEach(baseSelectEntity ->
////                        childInstitutionBaseSelectEntityMap.put(baseSelectEntity.getParentId(), baseSelectEntity));
//                List<InstitutionAndItemVo> institutionAndItemDtos = new ArrayList<>();
//                //只保留父学习计划进行for循环
//                baseSelectEntities = baseSelectEntities.stream().filter(baseSelectEntity -> GeneralTool.isEmpty(baseSelectEntity.getParentId())).collect(Collectors.toList());
//
//                for (InstitutionTabVo outerTabs : baseSelectEntities) {
//                    Long institutionTabId = outerTabs.getId();
//                    Long offerItemId = outerTabs.getOfferItemId();
//                    InstitutionAndItemVo institutionAndItemDto = new InstitutionAndItemVo();
//                    List<StudentItemAndStepVo> studentItemAndStepDtos = new ArrayList<>();
//                    //遍历父学习计划
//                    if (GeneralTool.isNotEmpty(parentItemDtos)) {
//                        for (StudentItemAndStepVo studentItemAndStepDto : parentItemDtos) {
//                            //                        if (institutionTabId.equals(studentItemAndStepDto.getFkInstitutionId())) {
//                            //                            studentItemAndStepDtos.add(studentItemAndStepDto);
//                            //                        }
//                            if (offerItemId.equals(studentItemAndStepDto.getId())) {
//                                studentItemAndStepDtos.add(studentItemAndStepDto);
//                            }
//                            //开学时间倒序
//                            studentItemAndStepDtos = studentItemAndStepDtos.stream().sorted(Comparator.comparing(StudentItemAndStepVo::getOpeningTime)).collect(Collectors.toList());
//                            institutionAndItemDto.setStudentItemAndStepDtos(studentItemAndStepDtos);
//                        }
//                    }
//                    List<InstitutionTabVo> innerTabs = childInstitutionBaseSelectEntityMap.get(outerTabs.getOfferItemId());
//
//                    //修改baseSelectEntityList的排序使得与item的排序对应  当前学校对应的子学习计划
//                    List<StudentItemAndStepVo> vo = new ArrayList<>();
//                    if (GeneralTool.isNotEmpty(institutionAndItemDto.getStudentItemAndStepDtos())) {
//                        vo = institutionAndItemDto.getStudentItemAndStepDtos().stream()
//                                .filter(studentItemAndStepDto -> studentItemAndStepDto.getId().equals(offerItemId))
//                                .collect(Collectors.toList());
//                    }
//                    if (GeneralTool.isNotEmpty(vo)) {
//                        if (GeneralTool.isNotEmpty(vo.get(0).getChildItemAndStepDtos())) {
//                            List<InstitutionTabVo> sortedInnerTabs = new ArrayList<>();
//                            List<Long> sortedIds = vo.get(0).getChildItemAndStepDtos().stream().map(StudentItemAndStepVo::getId).collect(Collectors.toList());
//                            for (Long sortedId : sortedIds) {
//                                if (GeneralTool.isNotEmpty(innerTabs)) {
//                                    for (InstitutionTabVo innerTab : innerTabs) {
//                                        if (sortedId.equals(innerTab.getOfferItemId())) {
//                                            sortedInnerTabs.add(innerTab);
//                                        }
//                                    }
//                                }
//                            }
//                            //排序后的赋值回去
//                            innerTabs = sortedInnerTabs;
//                        }
//                    }
//                    outerTabs.setInstitutionTabs(innerTabs);
//                    institutionAndItemDto.setInstitutionTabs(outerTabs);
//                    institutionAndItemDtos.add(institutionAndItemDto);
//                }
//                offerDto.setInstitutionAndItemDtos(institutionAndItemDtos);
//            }
        }
    }

    @Override
    public List<AgentsBindingVo> getAgentByStudentIdTypeKey(String fkStudentNum, String typeKey) throws GetServiceException {
        if (TableEnum.SALE_STUDENT_OFFER.key.equals(typeKey)) {
            return offerMapper.getStudentOfferAgentSelect(fkStudentNum);
        } else if (TableEnum.SALE_STUDENT_ACCOMMODATION.key.equals(typeKey)) {
            return studentAccommodationMapper.getStudentAccommodationAgent(fkStudentNum);
        } else if (TableEnum.SALE_STUDENT_INSURANCE.key.equals(typeKey)) {
            return studentInsuranceMapper.getStudentInsuranceAgent(fkStudentNum);
        }
        return null;
    }

    public Set<Long> getProjectLimitConfigKey(Set<Long> offerIds, Long fkCompanyId) {
        // CompanyConfigValueVo companyConfigValueDto = permissionCenterClient.getProjectLimitConfigKey(ProjectKeyEnum.ASSIGN_PROJECT_MEMBERS_LIMIT.key).getData();
//        List<CompanyConfigInfoDto> value1 = companyConfigValueDto.getValue1();
//        Map<Long, Integer> map = value1.stream().collect(Collectors.toMap(CompanyConfigInfoDto::getCompanyId, CompanyConfigInfoDto::getValue));
//        Integer isLimit = map.get(fkCompanyId);
        Map<Long, CompanyConfigAnalysisVo> isLimitMap = permissionCenterClient.getCompanyConfigAnalysis(ProjectKeyEnum.ASSIGN_PROJECT_MEMBERS_LIMIT.key).getData();
        CompanyConfigAnalysisVo companyConfigAnalysisVo = isLimitMap.get(fkCompanyId);
        if (GeneralTool.isNotEmpty(companyConfigAnalysisVo) && companyConfigAnalysisVo.getValue1().equals("1")) {
            List<BaseSelectEntity> itemStepSelect = studentOfferItemStepService.getItemStepSelect();
            List<String> stepString = JSONArray.parseArray(companyConfigAnalysisVo.getValue2(), String.class);
            return Optional.ofNullable(studentOfferItemService.getAssignProjectMembersLimit(offerIds,
                    itemStepSelect.stream().filter(i -> stepString.contains(i.getNum())).map(BaseSelectEntity::getId).collect(Collectors.toSet()))).orElse(null);
        }
        return null;
    }

    @Override
    public List<StudentOfferItemVerifyInfoVo> verifyStudentOfferItemCourse(Long studentId) {
        Student student = studentMapper.selectById(studentId);
        if (Objects.isNull(student)) {
            return Collections.emptyList();
        }
        //登录人业务国家
//        List<Long> countryIds = SecureUtil.getCountryIds();
//        if (GeneralTool.isEmpty(countryIds)) {
//            countryIds.add(0L);
//        }
        //获取业务下属
        List<Long> staffFollowerIds = new ArrayList<>();
        Result<List<Long>> longResult = permissionCenterClient.getStaffFollowerIds(SecureUtil.getStaffId());
        if (longResult.isSuccess() && GeneralTool.isNotEmpty(longResult.getData())) {
            staffFollowerIds.addAll(longResult.getData());
        }
        if (GeneralTool.isEmpty(staffFollowerIds)) {
            staffFollowerIds = new ArrayList<>();
        }
        staffFollowerIds.add(SecureUtil.getStaffId());
        StudentOfferDto studentOfferDto = new StudentOfferDto();
        studentOfferDto.setFkStudentId(studentId);
        Boolean isBd = getIsBd(SecureUtil.getStaffId());
        List<StudentOffer> studentOffer = offerMapper.getStudentOfferNew(null, studentOfferDto, staffFollowerIds, SecureUtil.getCountryIds(), SecureUtil.getStaffInfo().getIsStudentOfferItemFinancialHiding(), SecureUtil.getStaffInfo().getIsStudentAdmin(), SecureUtil.getStaffId(), SecureUtil.getInstitutionIds(), isBd, SecureUtil.getPermissionGroupInstitutionIds(), SecureUtil.getStaffBoundBdIds());
        if (GeneralTool.isEmpty(studentOffer)) {
            return Collections.emptyList();
        }

        Set<Long> offerIds = studentOffer.stream().map(StudentOffer::getId).collect(Collectors.toSet());
        LambdaQueryWrapper<StudentOfferItem> lambdaQueryWrapper = Wrappers.<StudentOfferItem>lambdaQuery().in(StudentOfferItem::getFkStudentOfferId, offerIds)
                .eq(StudentOfferItem::getStatus, 1);
        if (!SecureUtil.getStaffInfo().getIsStudentOfferItemFinancialHiding()) {
            lambdaQueryWrapper.ne(StudentOfferItem::getIsFollowHidden, 1);
        }
        List<StudentOfferItem> studentOfferItems = studentOfferItemMapper.selectList(lambdaQueryWrapper);
        if (GeneralTool.isEmpty(studentOfferItems)) {
            return Collections.emptyList();
        }
        List<StudentOfferItem> offerItemList = studentOfferItems.stream().filter(e -> e.getIsFollow() == false).collect(Collectors.toList());
        List<StudentOfferItem> offerItemChirens = studentOfferItems.stream().filter(e -> e.getIsFollow() == true).collect(Collectors.toList());
        StudentOfferItemStep studentOfferItemStep = studentOfferItemStepMapper.selectOne(Wrappers.<StudentOfferItemStep>lambdaQuery()
                .eq(StudentOfferItemStep::getStepKey, ProjectKeyEnum.STEP_FAILURE.key));
        Long stepId = studentOfferItemStep.getId();
        //入学失败更新负数应收计划ids
        ArrayList<StudentOfferItemVerifyInfoVo> baseSelectEntities = new ArrayList<>();
        Set<Long> institutionIds = studentOfferItems.stream().map(StudentOfferItem::getFkInstitutionId).collect(Collectors.toSet());
        Map<Long, String> institutionMap = institutionCenterClient.getInstitutionNamesByIds(institutionIds).getData();

        for (StudentOfferItem offerItem : offerItemList) {
            if (GeneralTool.isEmpty(offerItem.getFkInstitutionCourseId()) || (GeneralTool.isEmpty(offerItem.getOldCourseCustomName()) && offerItem.getFkInstitutionCourseId().equals(-1L))) {
                StudentOfferItemVerifyInfoVo baseSelectEntity = new StudentOfferItemVerifyInfoVo();
                baseSelectEntity.setId(offerItem.getId());
                baseSelectEntity.setNum(offerItem.getNum());
                if (GeneralTool.isNotEmpty(institutionMap)) {
                    baseSelectEntity.setName(institutionMap.get(offerItem.getFkInstitutionId()));
                }
                List<StudentOfferItem> offerItems = offerItemChirens.stream().filter(e -> e.getFkParentStudentOfferItemId().equals(offerItem.getId())).collect(Collectors.toList());
                baseSelectEntity.setItemchiren(offerItems);
                baseSelectEntities.add(baseSelectEntity);
            }
        }
        return baseSelectEntities;
    }

    /**
     * 合并学习方案数据
     *
     * @param mergedStudentId
     * @param targetStudentId
     */
    @Override
    public void mergeData(Long mergedStudentId, Long targetStudentId) {
        List<StudentOffer> studentOffers = studentOfferMapper.selectList(Wrappers.<StudentOffer>lambdaQuery().eq(StudentOffer::getFkStudentId, mergedStudentId));
        if (GeneralTool.isNotEmpty(studentOffers)) {
            studentOffers.forEach(s -> s.setFkStudentId(targetStudentId));
            updateBatchById(studentOffers);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateStudentOfferProjectRole(StudentOfferProjectUpdateDto studentOfferProjectUpdateDto) {
        String[] times = {"0", "0"};
        StudentOfferListQueryDto studentOfferListQueryDto = studentOfferProjectUpdateDto.getStudentOfferListVo();
        studentOfferListQueryDto.setPageNumber(null);
        studentOfferListQueryDto.setIsProjectUpate(true);
        //角色
        Set<Long> fkRoleIds = new HashSet<>();
        Set<Long> roleIds = studentOfferListQueryDto.getFkRoleIds();
        if (GeneralTool.isNotEmpty(roleIds)) {
            fkRoleIds.addAll(roleIds);
        }
        fkRoleIds.add(studentOfferProjectUpdateDto.getFkStudentProjectRoleIdOld());
        //项目成员
        List<Long> fkProjectRoleIds = new ArrayList<>();
        List<Long> projectRoleIds = studentOfferListQueryDto.getFkProjectRoleIds();
        if (GeneralTool.isNotEmpty(projectRoleIds)) {
            fkProjectRoleIds.addAll(projectRoleIds);
        }
        fkProjectRoleIds.add(studentOfferProjectUpdateDto.getFkStaffIdOld());
        studentOfferListQueryDto.setFkRoleIds(fkRoleIds);
        studentOfferListQueryDto.setFkProjectRoleIds(fkProjectRoleIds);
        //添加项目成员和角色，增加sql查询速度
        List<StudentOfferVo> studentOfferWorkFolwDatas = getStudentOfferWorkFolwDatas(studentOfferListQueryDto, times);
        Set<Long> ids = studentOfferWorkFolwDatas.stream().map(StudentOfferVo::getId).collect(Collectors.toSet());
        if (GeneralTool.isEmpty(ids)) {
            return;
        }
        StudentProjectRole studentProjectRole = studentProjectRoleMapper.selectById(studentOfferProjectUpdateDto.getFkStudentProjectRoleIdNew());
        if (GeneralTool.isNotEmpty(studentProjectRole) && (studentProjectRole.getRoleKey().equals("HTI_COORDINATOR") || studentProjectRole.getRoleKey().equals("HTI_COUNSELLING_SUPPORT"))) {
            List<StudentOfferItem> studentOfferItems = studentOfferItemMapper.selectList(Wrappers.<StudentOfferItem>lambdaQuery().in(StudentOfferItem::getFkStudentOfferId, ids));
            for (StudentOfferItem studentOfferItem : studentOfferItems) {
                ArrayList<RStudentOfferItemStep> rStudentOfferItemSteps = new ArrayList<>();
                if (studentOfferItem.getFkStudentOfferItemStepId().equals(1L)) {
                    RStudentOfferItemStep rStudentOfferItemStep = rStudentOfferItemStepMapper.selectOne(Wrappers.<RStudentOfferItemStep>lambdaQuery().eq(RStudentOfferItemStep::getFkStudentOfferItemId, studentOfferItem.getId()).orderByDesc(RStudentOfferItemStep::getGmtCreate).last("limit 1"));
                    rStudentOfferItemSteps.add(rStudentOfferItemStep);
                }
                if (studentOfferItem.getFkStudentOfferItemStepId().equals(2L)) {
                    RStudentOfferItemStep rStudentOfferItemStep = rStudentOfferItemStepMapper.selectOne(Wrappers.<RStudentOfferItemStep>lambdaQuery()
                            .eq(RStudentOfferItemStep::getFkStudentOfferItemId, studentOfferItem.getId())
                            .eq(RStudentOfferItemStep::getFkStudentOfferItemStepId, studentOfferItem.getFkStudentOfferItemStepId())
                            .orderByDesc(RStudentOfferItemStep::getGmtCreate).last("limit 1"));
                    rStudentOfferItemSteps.add(rStudentOfferItemStep);
                    List<RStudentOfferItemStep> offerItemSteps = rStudentOfferItemStepMapper.selectList(Wrappers.<RStudentOfferItemStep>lambdaQuery().eq(RStudentOfferItemStep::getFkStudentOfferItemId, studentOfferItem.getId()).eq(RStudentOfferItemStep::getFkStudentOfferItemStepId, 1L));
                    rStudentOfferItemSteps.addAll(offerItemSteps);
                }
                for (RStudentOfferItemStep rStudentOfferItemStep : rStudentOfferItemSteps) {
                    if (GeneralTool.isEmpty(rStudentOfferItemStep.getRemark())) {
                        rStudentOfferItemStep.setRemark("【重新指派，原时间：" + DateUtil.formatDateTime(rStudentOfferItemStep.getGmtCreate()) + "】");
                    } else {
                        rStudentOfferItemStep.setRemark(rStudentOfferItemStep.getRemark() + "【重新指派，原时间：" + DateUtil.formatDateTime(rStudentOfferItemStep.getGmtCreate()) + "】");
                    }
                    rStudentOfferItemStep.setReassignTime(new Date());
//                            utilService.setUpdateInfo(rStudentOfferItemStep);
                    rStudentOfferItemStepMapper.updateById(rStudentOfferItemStep);
                }
            }
        }


        LambdaQueryWrapper<StudentProjectRoleStaff> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StudentProjectRoleStaff::getFkTableName, TableEnum.SALE_STUDENT_OFFER.key)
                .in(StudentProjectRoleStaff::getFkTableId, ids)
                .eq(StudentProjectRoleStaff::getFkStaffId, studentOfferProjectUpdateDto.getFkStaffIdOld())
                .eq(StudentProjectRoleStaff::getIsActive, true)
                .eq(StudentProjectRoleStaff::getFkStudentProjectRoleId, studentOfferProjectUpdateDto.getFkStudentProjectRoleIdOld());
        StudentProjectRoleStaff studentProjectRoleStaff = new StudentProjectRoleStaff();
        studentProjectRoleStaff.setIsActive(false);
        studentProjectRoleStaff.setUnactiveDate(new Date());
        studentProjectRoleStaffMapper.update(studentProjectRoleStaff, lambdaQueryWrapper);

        //已存在则不用新增
        LambdaQueryWrapper<StudentProjectRoleStaff> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StudentProjectRoleStaff::getFkTableName, TableEnum.SALE_STUDENT_OFFER.key)
                .in(StudentProjectRoleStaff::getFkTableId, ids)
                .eq(StudentProjectRoleStaff::getIsActive, true)
                .eq(StudentProjectRoleStaff::getFkStaffId, studentOfferProjectUpdateDto.getFkStaffIdOldNew())
                .eq(StudentProjectRoleStaff::getFkStudentProjectRoleId, studentOfferProjectUpdateDto.getFkStudentProjectRoleIdNew());
        List<StudentProjectRoleStaff> studentProjectRoleStaffs = studentProjectRoleStaffMapper.selectList(queryWrapper);
        Set<Long> itemIds = studentProjectRoleStaffs.stream().map(StudentProjectRoleStaff::getFkTableId).collect(Collectors.toSet());

        List<StudentProjectRoleStaff> list = new ArrayList<>();
        for (Long id : ids) {
            //已存在则不用新增
            if (GeneralTool.isNotEmpty(itemIds)) {
                if (itemIds.contains(id)) {
                    continue;
                }
            }
            StudentProjectRoleStaff studentProjectRoleStaffNew = new StudentProjectRoleStaff();
            studentProjectRoleStaffNew.setFkTableName(TableEnum.SALE_STUDENT_OFFER.key);
            studentProjectRoleStaffNew.setFkTableId(id);
            studentProjectRoleStaffNew.setFkStaffId(studentOfferProjectUpdateDto.getFkStaffIdOldNew());
            studentProjectRoleStaffNew.setFkStudentProjectRoleId(studentOfferProjectUpdateDto.getFkStudentProjectRoleIdNew());
            studentProjectRoleStaffNew.setIsActive(true);
            studentProjectRoleStaffNew.setActiveDate(new Date());
            utilService.setCreateInfo(studentProjectRoleStaffNew);
            list.add(studentProjectRoleStaffNew);
        }

        studentProjectRoleStaffService.batchAddStudentProjectRoleStaff(list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean syncContacts() {
//        List<OfferContactsVo> offerContactsDtos = studentOfferItemMapper.syncContacts();
//
//        for (OfferContactsVo offerContactsDto : offerContactsDtos) {
//            StudentOffer studentOffer = studentOfferMapper.selectById(offerContactsDto.getId());
//            Long fkContactPersonId = null;
//            if (GeneralTool.isNotEmpty(offerContactsDto.getTargetEmail())) {
//                String email = offerContactsDto.getTargetEmail();
//                //获取学生所有联系人
//                List<SaleContactPerson> personList = contactPersonService.getContactPersonByFkTableId(TableEnum.SALE_AGENT.key, studentOffer.getFkAgentId());
//                //判断修改后的联系人邮箱是否已经存在，不存在则新增数据
//                List<String> emails = personList.stream().map(SaleContactPerson::getEmail).collect(Collectors.toList());
//
//                boolean flag = validateEmail(email, emails);
//                if (!flag){
//                    //无相同记录
//                    String name = email.substring(0, email.indexOf("@"));
//                    ContactPersonVo contactPersonVo = new ContactPersonVo();
//                    contactPersonVo.setEmail(email);
//                    contactPersonVo.setName(name);
//                    contactPersonVo.setFkTableName(TableEnum.SALE_AGENT.key);
//                    contactPersonVo.setFkTableId(studentOffer.getFkAgentId());
//                    contactPersonVo.setFkContactPersonTypeKey("CONTACT_AGENT_SALES");
//                    fkContactPersonId = contactPersonService.addContactPerson(contactPersonVo);
//                }else {
//                    List<SaleContactPerson> data = personList.stream()
//                            .filter(person ->{
//                                List<String> emailList = new ArrayList<>(1);
//                                emailList.add(person.getEmail());
//                                return  validateEmail(email, emailList);
//                            }).collect(Collectors.toList());
//                    fkContactPersonId = data.get(0).getId();
//                }
//            }
//
//            studentOffer.setFkContactPersonId(fkContactPersonId);
//            studentOfferMapper.updateById(studentOffer);
//        }
        return true;
    }

}

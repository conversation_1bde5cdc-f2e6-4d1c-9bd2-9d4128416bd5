package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.entity.ConventionPersonInstitutionProvider;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @author: Sea
 * @create: 2020/7/27 12:21
 * @verison: 1.0
 * @description: 参展人员和学校提供商关联中间表mapper
 */
@Mapper
public interface ConventionPersonInstitutionProviderMapper extends BaseMapper<ConventionPersonInstitutionProvider> {
    /**
     * 添加
     *
     * @param record
     * @return
     */
    int insert(ConventionPersonInstitutionProvider record);

    /**
     * 添加
     *
     * @param record
     * @return
     */
    int insertSelective(ConventionPersonInstitutionProvider record);

    /**
     * 通过学校提供商的ids 查找对应的参展人员ids
     *
     * @param institutionProviderIds
     * @return
     */
    List<Long> getConventionPersonIds(List<Long> institutionProviderIds);

    /**
     * 通过参展人员id 查找对应的学校提供商id
     *
     * @param id
     * @return
     */
    Long getInstitutionProviderId(Long id);
}
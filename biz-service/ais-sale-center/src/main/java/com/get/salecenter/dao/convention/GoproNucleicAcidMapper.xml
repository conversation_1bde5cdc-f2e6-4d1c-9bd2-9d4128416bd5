<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.convention.GoproNucleicAcidMapper">
  <insert id="insert" parameterType="com.get.salecenter.entity.GoproNucleicAcid" keyProperty="id" useGeneratedKeys="true">
    insert into m_gopro_nucleic_acid (id, fk_company_id, retreat_type_name,
                                      name, gender, institution,
                                      mobile, identity_type, identity_card,
                                      area_country_name, area_state_name, area_city_name,
                                      arrival_date, transportation_code, scheduling,
                                      bd_region, bd_name, order_id,
                                      gmt_create, gmt_create_user, gmt_modified,
                                      gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkCompanyId,jdbcType=BIGINT}, #{retreatTypeName,jdbcType=VARCHAR},
            #{name,jdbcType=VARCHAR}, #{gender,jdbcType=INTEGER}, #{institution,jdbcType=VARCHAR},
            #{mobile,jdbcType=VARCHAR}, #{identityType,jdbcType=VARCHAR}, #{identityCard,jdbcType=VARCHAR},
            #{areaCountryName,jdbcType=VARCHAR}, #{areaStateName,jdbcType=VARCHAR}, #{areaCityName,jdbcType=VARCHAR},
            #{arrivalDate,jdbcType=VARCHAR}, #{transportationCode,jdbcType=VARCHAR}, #{scheduling,jdbcType=VARCHAR},
            #{bdRegion,jdbcType=VARCHAR}, #{bdName,jdbcType=VARCHAR}, #{orderId,jdbcType=INTEGER},
            #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP},
            #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.salecenter.entity.GoproNucleicAcid" keyProperty="id" useGeneratedKeys="true">
    insert into m_gopro_nucleic_acid
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkCompanyId != null">
        fk_company_id,
      </if>
      <if test="retreatTypeName != null">
        retreat_type_name,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="gender != null">
        gender,
      </if>
      <if test="institution != null">
        institution,
      </if>
      <if test="mobile != null">
        mobile,
      </if>
      <if test="identityType != null">
        identity_type,
      </if>
      <if test="identityCard != null">
        identity_card,
      </if>
      <if test="areaCountryName != null">
        area_country_name,
      </if>
      <if test="areaStateName != null">
        area_state_name,
      </if>
      <if test="areaCityName != null">
        area_city_name,
      </if>
      <if test="arrivalDate != null">
        arrival_date,
      </if>
      <if test="transportationCode != null">
        transportation_code,
      </if>
      <if test="scheduling != null">
        scheduling,
      </if>
      <if test="bdRegion != null">
        bd_region,
      </if>
      <if test="bdName != null">
        bd_name,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkCompanyId != null">
        #{fkCompanyId,jdbcType=BIGINT},
      </if>
      <if test="retreatTypeName != null">
        #{retreatTypeName,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        #{gender,jdbcType=INTEGER},
      </if>
      <if test="institution != null">
        #{institution,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="identityType != null">
        #{identityType,jdbcType=VARCHAR},
      </if>
      <if test="identityCard != null">
        #{identityCard,jdbcType=VARCHAR},
      </if>
      <if test="areaCountryName != null">
        #{areaCountryName,jdbcType=VARCHAR},
      </if>
      <if test="areaStateName != null">
        #{areaStateName,jdbcType=VARCHAR},
      </if>
      <if test="areaCityName != null">
        #{areaCityName,jdbcType=VARCHAR},
      </if>
      <if test="arrivalDate != null">
        #{arrivalDate,jdbcType=VARCHAR},
      </if>
      <if test="transportationCode != null">
        #{transportationCode,jdbcType=VARCHAR},
      </if>
      <if test="scheduling != null">
        #{scheduling,jdbcType=VARCHAR},
      </if>
      <if test="bdRegion != null">
        #{bdRegion,jdbcType=VARCHAR},
      </if>
      <if test="bdName != null">
        #{bdName,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="getMaxOrderId" resultType="java.lang.Integer">
    select
      IFNULL(max(order_id)+1,1) order_id
    from
      m_gopro_nucleic_acid
    where 1=1
    <if test="type != null and type != '' and type == 'agent'">
      and bd_name is not null
    </if>
    <if test="type != null and type != '' and type == 'institution'">
      and bd_name is null
    </if>
  </select>
</mapper>
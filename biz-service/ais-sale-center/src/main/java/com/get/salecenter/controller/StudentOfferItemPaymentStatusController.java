package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
//import com.get.salecenter.dto.StudentOfferItemPaymentStatusDto;
import com.get.salecenter.dto.StudentOfferItemPaymentStatusDto;
import com.get.salecenter.dto.StudentOfferItemPaymentStatusRemarkDto;
import com.get.salecenter.dto.StudentOfferItemPaymentStatusToAppDto;
import com.get.salecenter.service.IStudentOfferItemPaymentStatusService;
//import com.get.salecenter.vo.StudentOfferItemPaymentStatusRemarkDto;
//import com.get.salecenter.vo.StudentOfferItemPaymentStatusRemarkDto;
//import com.get.salecenter.vo.StudentOfferItemPaymentStatusDto;
import com.get.salecenter.vo.StudentOfferItemPaymentStatusVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "代付费用日志管理")
@RestController
@RequestMapping("sale/studentOfferItemPaymentStatus")
public class StudentOfferItemPaymentStatusController {

    @Resource
    private IStudentOfferItemPaymentStatusService studentOfferItemPaymentStatusService;

    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/申请计划汇总/申请计划详情/代付费用日志列表")
    @PostMapping("datas")
    public ResponseBo<StudentOfferItemPaymentStatusVo> datas(@RequestBody SearchBean<StudentOfferItemPaymentStatusDto> page) {
        List<StudentOfferItemPaymentStatusVo> datas = studentOfferItemPaymentStatusService.datas(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/申请计划汇总/申请计划详情/新增代付费用日志")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(StudentOfferItemPaymentStatusDto.Add.class) StudentOfferItemPaymentStatusDto studentOfferItemPaymentStatusVo) {
        studentOfferItemPaymentStatusService.add(studentOfferItemPaymentStatusVo);
        return SaveResponseBo.ok();
    }

    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/申请计划汇总/申请计划详情/代付费用日志详情")
    @GetMapping("/{id}")
    public ResponseBo<StudentOfferItemPaymentStatusVo> detail(@PathVariable("id") Long id) {
        StudentOfferItemPaymentStatusVo data = studentOfferItemPaymentStatusService.detail(id);
        return new ResponseBo<>(data);
    }

    @ApiOperation(value = "修改代付费用日志备注接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/申请计划汇总/申请计划详情/修改代付费用日志备注接口")
    @PostMapping("updateRemark")
    public UpdateResponseBo updateRemark(@RequestBody @Validated StudentOfferItemPaymentStatusRemarkDto studentOfferItemPaymentStatusRemarkVo) {
        return UpdateResponseBo.ok(studentOfferItemPaymentStatusService.updateRemark(studentOfferItemPaymentStatusRemarkVo));
    }

    @ApiOperation(value = "更新付费状态", notes = "提供给app端接口，更新申请计划的支付状态，并插入代付日志, 并且发送邮件通知给项目成员")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/申请计划汇总/申请计划详情/更新付费状态")
    @PostMapping("updatePaymentStatusToApp")
    @VerifyLogin(IsVerify = false)
    public ResponseBo updatePaymentStatusToApp(@RequestBody @Validated StudentOfferItemPaymentStatusToAppDto studentOfferItemPaymentStatusToAppDto) {
        studentOfferItemPaymentStatusService.updatePaymentStatusToApp(studentOfferItemPaymentStatusToAppDto);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "代付币种下拉", notes = "易思汇支持的币种")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/申请计划汇总/申请计划详情/代付币种下拉")
    @PostMapping("getCurrencyTypeNumSelect")
    @VerifyPermission(IsVerify = false)
    public ResponseBo getCurrencyTypeNumSelect() {
        return new ListResponseBo<>(ProjectKeyEnum.enums2Arrays(ProjectKeyEnum.EASY_TRANSFER_USE_CURRENCY));
    }

}

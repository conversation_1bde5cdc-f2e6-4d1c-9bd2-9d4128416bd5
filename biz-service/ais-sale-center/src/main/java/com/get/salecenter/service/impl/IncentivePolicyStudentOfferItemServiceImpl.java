package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.redis.lock.RedisLockClient;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.DateUtil;
import com.get.core.tool.utils.Exceptions;
import com.get.core.tool.utils.GeneralTool;
import com.get.file.utils.FileUtils;
import com.get.financecenter.feign.IFinanceCenterClient;
import com.get.financecenter.vo.PaymentFormVo;
import com.get.financecenter.vo.ReceiptFormItemVo;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.dao.sale.MIncentivePolicyMapper;
import com.get.salecenter.dao.sale.RIncentivePolicyStudentOfferItemMapper;
import com.get.salecenter.dto.*;
import com.get.salecenter.entity.IncentivePolicy;
import com.get.salecenter.entity.IncentivePolicyStudentOfferItem;
import com.get.salecenter.service.IIncentivePolicyStudentOfferItemService;
import com.get.salecenter.service.IReceivableAndPayablePlanService;
import com.get.salecenter.vo.IncentivePolicyConditionVo;
import com.get.salecenter.vo.IncentivePolicyRewardVo;
import com.get.salecenter.vo.IncentivePolicyStudentOfferItemExcelVo;
import com.get.salecenter.vo.IncentivePolicyStudentOfferItemVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.get.common.cache.CacheNames.INCENTIVE_POLICY_KEY;

/**
 * <AUTHOR>
 * @since 2023-03-13
 */
@Service
@Slf4j
public class IncentivePolicyStudentOfferItemServiceImpl extends BaseServiceImpl<RIncentivePolicyStudentOfferItemMapper, IncentivePolicyStudentOfferItem> implements IIncentivePolicyStudentOfferItemService {

    @Resource
    private RIncentivePolicyStudentOfferItemMapper incentivePolicyStudentOfferItemMapper;
    @Resource
    private MIncentivePolicyMapper incentivePolicyMapper;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private UtilService utilService;
    @Resource
    private IFinanceCenterClient financeCenterClient;
    @Resource
    private IReceivableAndPayablePlanService receivableAndPayablePlanService;
    @Resource
    private RedisLockClient redisLockClient;


    @Override
    public List<IncentivePolicyStudentOfferItemVo> getPolicyStudentDatas(IncentivePolicyStudentOfferItemDto incentivePolicyStudentOfferItemDto, SearchBean<IncentivePolicyStudentOfferItemDto> page) {
        //查询条件-公司ids
        if (GeneralTool.isNotEmpty(incentivePolicyStudentOfferItemDto.getFkCompanyIds())) {
            if (!SecureUtil.validateCompanys(incentivePolicyStudentOfferItemDto.getFkCompanyIds())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
            }
        }

        //如需要执行统计符合策略的学生列表，则需要先更新学生列表
        if (incentivePolicyStudentOfferItemDto.getIsCountStudent() == 1) {
            //此处添加学生列表
            Boolean result = this.updatePolicyStudentOfferItem(incentivePolicyStudentOfferItemDto.getFkIncentivePolicyId());
            if (!result) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("student_statistics_failed"));
            }
        }

        if (GeneralTool.isNotEmpty(incentivePolicyStudentOfferItemDto.getStudentName())) {
            incentivePolicyStudentOfferItemDto.setStudentName(incentivePolicyStudentOfferItemDto.getStudentName().toLowerCase());
        }
        if (GeneralTool.isNotEmpty(incentivePolicyStudentOfferItemDto.getAgentNameOrNum())) {
            incentivePolicyStudentOfferItemDto.setAgentNameOrNum(incentivePolicyStudentOfferItemDto.getAgentNameOrNum().toLowerCase());
        }
        if (GeneralTool.isNotEmpty(incentivePolicyStudentOfferItemDto.getInstitutionName())) {
            incentivePolicyStudentOfferItemDto.setInstitutionName(incentivePolicyStudentOfferItemDto.getInstitutionName().toLowerCase());
        }
        //获取分页数据
        LambdaQueryWrapper<IncentivePolicyStudentOfferItem> wrapper = new LambdaQueryWrapper();
        List<IncentivePolicyStudentOfferItemVo> list = null;
        //为null表示未分页，一般用于导出
        if (page != null) {
            IPage<IncentivePolicyStudentOfferItem> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
            list = incentivePolicyStudentOfferItemMapper.datas(pages, incentivePolicyStudentOfferItemDto);
            page.setAll((int) pages.getTotal());
        } else {
            list = incentivePolicyStudentOfferItemMapper.datas(null, incentivePolicyStudentOfferItemDto);
        }
        //学校提供商关联
        Map<Long, String> providerNameMap = new HashMap<>();
        Set<Long> institutionProviderIds = list.stream().map(IncentivePolicyStudentOfferItemVo::getInstitutionProviderId).collect(Collectors.toSet());
        if (GeneralTool.isNotEmpty(institutionProviderIds)) {
            Result<Map<Long, String>> result = institutionCenterClient.getInstitutionProviderNamesByIds(institutionProviderIds);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                providerNameMap = result.getData();
            }
        }
        //公司名
        Set<Long> fkComanyIds = list.stream().map(IncentivePolicyStudentOfferItemVo::getFkCompanyId).collect(Collectors.toSet());
        //根据公司ids获取名称
        Map<Long, String> companyNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkComanyIds)) {
            Result<Map<Long, String>> result = permissionCenterClient.getCompanyNamesByIds(fkComanyIds);
            if (result.isSuccess() && result.getData() != null) {
                companyNamesByIds = result.getData();
            }
        }
        //根据国家ids获取国家名称
        Set<Long> countryIds = list.stream().map(IncentivePolicyStudentOfferItemVo::getFkAreaCountryId).collect(Collectors.toSet());
        Map<Long, String> countryNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(countryIds)) {
            Result<Map<Long, String>> result1 = institutionCenterClient.getCountryNamesByIds(countryIds);
            if (result1.isSuccess() && GeneralTool.isNotEmpty(result1.getData())) {
                countryNamesByIds = result1.getData();
            }
        }

        if (GeneralTool.isNotEmpty(list)) {
            for (IncentivePolicyStudentOfferItemVo incentivePolicyStudentOfferItemVo : list) {
                incentivePolicyStudentOfferItemVo.setFkIncentivePolicyId(incentivePolicyStudentOfferItemDto.getFkIncentivePolicyId());
                //公司名字
                incentivePolicyStudentOfferItemVo.setCompanyName(companyNamesByIds.get(incentivePolicyStudentOfferItemVo.getFkCompanyId()));
                //国家
                incentivePolicyStudentOfferItemVo.setCountryName(countryNamesByIds.get(incentivePolicyStudentOfferItemVo.getFkAreaCountryId()));
                incentivePolicyStudentOfferItemVo.setInstitutionProviderName(providerNameMap.get(incentivePolicyStudentOfferItemVo.getInstitutionProviderId()));
            }
        }
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updatePolicyStudentOfferItem(Long fkIncentivePolicyId) {
        log.info("=======策略ID【{}】开始统计符合条件的offer列表：date{}===========", fkIncentivePolicyId, DateUtil.formatDateTime(new Date()));
        //添加一个自定义分布式锁，以策略ID为key
        boolean lockRs = false;
        String key = INCENTIVE_POLICY_KEY + fkIncentivePolicyId.toString();
        try {
            lockRs = redisLockClient.trySingleLock(key);
            if (lockRs) {
                IncentivePolicy incentivePolicy = incentivePolicyMapper.selectById(fkIncentivePolicyId);
                if (GeneralTool.isEmpty(incentivePolicy)) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("the_strategy_does_not_exist"));
                }
                //已作废策略返回提示
                if (incentivePolicy.getStatus() == 0) {
//                    throw new GetServiceException("该策略已作废");
                    return true;
                }
                //已结算策略不再统计，返回历史列表
                if (incentivePolicy.getStatus() == 2) {
                    return true;
                }
                IncentivePolicyConditionVo conditionJson = incentivePolicy.getConditionJson();
                if (conditionJson.getMainCourseIds().size() > 0 || conditionJson.getMainCourseTypeIds().size() > 0 || conditionJson.getMainMajorLevelIds().size() > 0) {
                    conditionJson.setMainCourseMark(true);
                }
                if (conditionJson.getSubCourseIds().size() > 0 || conditionJson.getSubCourseTypeIds().size() > 0 || conditionJson.getSubMajorLevelIds().size() > 0) {
                    conditionJson.setSubCourseMark(true);
                }
                if (conditionJson.getLaterInstitutionProviderId() != null || conditionJson.getLaterUpCourseIds().size() > 0 || conditionJson.getLaterUpCourseTypeIds().size() > 0 || conditionJson.getLaterUpMajorLevelIds().size() > 0) {
                    conditionJson.setLaterCourseMark(true);
                }

                List<Long> stepOrderIds = conditionJson.getStepOrderIds();

                //置为空
                if (GeneralTool.isNotEmpty(conditionJson.getSubmitAppTimes()) && conditionJson.getSubmitAppTimes().size() == 1) {
                    if (GeneralTool.isEmpty(conditionJson.getSubmitAppTimes().get(0).getStartTime()) || GeneralTool.isEmpty(conditionJson.getSubmitAppTimes().get(0).getEndTime())) {
                        conditionJson.setSubmitAppTimes(null);
                    }
                    if (GeneralTool.isNotEmpty(conditionJson.getSubmitAppTimes())) {
                        //如有提交申请时间，则添加1和2两个步骤
                        if (GeneralTool.isNotEmpty(stepOrderIds)) {
                            stepOrderIds.add(1L);//新申请
                            stepOrderIds.add(2L);//提交完成
                        }
                    }

                }
                //押金支付时间
                if (GeneralTool.isNotEmpty(conditionJson.getDepositTimes()) && conditionJson.getDepositTimes().size() == 1) {
                    if (GeneralTool.isEmpty(conditionJson.getDepositTimes().get(0).getStartTime()) || GeneralTool.isEmpty(conditionJson.getDepositTimes().get(0).getEndTime())) {
                        conditionJson.setDepositTimes(null);
                    }
                    if (GeneralTool.isNotEmpty(conditionJson.getDepositTimes())) {
                        //如有押金支付时间，则添加1和2两个步骤
                        if (GeneralTool.isNotEmpty(stepOrderIds)) {
                            stepOrderIds.add(5L);//已付押金
                        }
                    }
                }
                //学费支付时间
                if (GeneralTool.isNotEmpty(conditionJson.getTuitionTimes()) && conditionJson.getTuitionTimes().size() == 1) {
                    if (GeneralTool.isEmpty(conditionJson.getTuitionTimes().get(0).getStartTime()) || GeneralTool.isEmpty(conditionJson.getTuitionTimes().get(0).getEndTime())) {
                        conditionJson.setTuitionTimes(null);
                    }
                    if (GeneralTool.isNotEmpty(conditionJson.getTuitionTimes())) {
                        //如有学费支付时间，则添加17这个步骤
                        if (GeneralTool.isNotEmpty(stepOrderIds)) {
                            stepOrderIds.add(17L);//已付学费
                        }
                    }
                }
                if (GeneralTool.isNotEmpty(conditionJson.getDeferOpeningTimes()) && conditionJson.getDeferOpeningTimes().size() == 1) {
                    if (GeneralTool.isEmpty(conditionJson.getDeferOpeningTimes().get(0).getStartTime()) || GeneralTool.isEmpty(conditionJson.getDeferOpeningTimes().get(0).getEndTime())) {
                        conditionJson.setDeferOpeningTimes(null);
                    }
                }

                //获取所有课程等级的名称
                Set<Long> courseLevelIds = new HashSet<>();
                List<String> mainMajorLevelNames = new ArrayList<>();//主课
                List<String> subMajorLevelNames = new ArrayList<>();//子课
                List<String> laterUpMajorLevelNames = new ArrayList<>();//后续课
                if (GeneralTool.isNotEmpty(conditionJson.getMainMajorLevelIds())) {
                    courseLevelIds.addAll(conditionJson.getMainMajorLevelIds());
                }
                if (GeneralTool.isNotEmpty(conditionJson.getSubMajorLevelIds())) {
                    courseLevelIds.addAll(conditionJson.getSubMajorLevelIds());
                }
                if (GeneralTool.isNotEmpty(conditionJson.getLaterUpMajorLevelIds())) {
                    courseLevelIds.addAll(conditionJson.getLaterUpMajorLevelIds());
                }
                if (courseLevelIds.size() > 0) {
                    Result<Map<Long, String>> result = institutionCenterClient.getMajorLevelNamesByIds(courseLevelIds);
                    if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                        Map<Long, String> nameMap = result.getData();
                        if (GeneralTool.isNotEmpty(nameMap)) {
                            nameMap.forEach((key_, value_) -> {
                                if (GeneralTool.isNotEmpty(conditionJson.getMainMajorLevelIds()) && conditionJson.getMainMajorLevelIds().contains(key_)) {
                                    mainMajorLevelNames.add(value_);
                                }
                                if (GeneralTool.isNotEmpty(conditionJson.getSubMajorLevelIds()) && conditionJson.getSubMajorLevelIds().contains(key_)) {
                                    subMajorLevelNames.add(value_);
                                }
                                if (GeneralTool.isNotEmpty(conditionJson.getLaterUpMajorLevelIds()) && conditionJson.getLaterUpMajorLevelIds().contains(key_)) {
                                    laterUpMajorLevelNames.add(value_);
                                }
                            });
                        }
                    }
                }
                if (mainMajorLevelNames.size() > 0) {
                    conditionJson.setMainMajorLevelNames(mainMajorLevelNames);
                }
                if (subMajorLevelNames.size() > 0) {
                    conditionJson.setSubMajorLevelNames(subMajorLevelNames);
                }
                if (laterUpMajorLevelNames.size() > 0) {
                    conditionJson.setLaterUpMajorLevelNames(laterUpMajorLevelNames);
                }

                //获取所有课程类型的名称：根据传入的课程类型组获取下面的子类型（id+名字集合）
//                Set<Long> courseTypeIds = new HashSet<>();
//                List<String> mainCourseTypeNames = new ArrayList<>();//主课
//                List<String> subCourseTypeNames = new ArrayList<>();//子课
//                List<String> laterUpCourseTypeNames = new ArrayList<>();//后续课
                if (GeneralTool.isNotEmpty(conditionJson.getMainCourseTypeIds())) {
                    //根据主课课程类型组获取所有的子类型id集合及名字
                    Result<Map<Long, String>> result = institutionCenterClient.getCourseTypeNamesByCourseGroupTypeIds(conditionJson.getMainCourseTypeIds().stream().collect(Collectors.toSet()));
                    Map<Long, String> nameMap = result.getData();
                    if (GeneralTool.isNotEmpty(nameMap)) {
                        conditionJson.setMainCourseTypeIds(nameMap.entrySet().stream().map(Map.Entry::getKey).collect(Collectors.toList()));
                        conditionJson.setMainCourseTypeNames(nameMap.entrySet().stream().map(Map.Entry::getValue).collect(Collectors.toList()));
                    }
                }
                if (GeneralTool.isNotEmpty(conditionJson.getSubCourseTypeIds())) {
                    //根据子课课程类型组获取所有的子类型id集合及名字
                    Result<Map<Long, String>> result = institutionCenterClient.getCourseTypeNamesByCourseGroupTypeIds(conditionJson.getSubCourseTypeIds().stream().collect(Collectors.toSet()));
                    Map<Long, String> nameMap = result.getData();
                    if (GeneralTool.isNotEmpty(nameMap)) {
                        conditionJson.setSubCourseTypeIds(nameMap.entrySet().stream().map(Map.Entry::getKey).collect(Collectors.toList()));
                        conditionJson.setSubCourseTypeNames(nameMap.entrySet().stream().map(Map.Entry::getValue).collect(Collectors.toList()));
                    }
                }
                if (GeneralTool.isNotEmpty(conditionJson.getLaterUpCourseTypeIds())) {
                    //根据后续课课程类型组获取所有的子类型id集合及名字
                    Result<Map<Long, String>> result = institutionCenterClient.getCourseTypeNamesByCourseGroupTypeIds(conditionJson.getLaterUpCourseTypeIds().stream().collect(Collectors.toSet()));
                    Map<Long, String> nameMap = result.getData();
                    if (GeneralTool.isNotEmpty(nameMap)) {
                        conditionJson.setLaterUpCourseTypeIds(nameMap.entrySet().stream().map(Map.Entry::getKey).collect(Collectors.toList()));
                        conditionJson.setLaterUpCourseTypeNames(nameMap.entrySet().stream().map(Map.Entry::getValue).collect(Collectors.toList()));
                    }
                }
//                if(courseTypeIds.size()>0)
//                {
//                    Result<Map<Long, String>> result = institutionCenterClient.getCourseGroupTypeNameByIds(courseTypeIds);
//                    if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
//                        Map<Long, String> nameMap = result.getData();
//                        if(GeneralTool.isNotEmpty(nameMap))
//                        {
//                            nameMap.forEach((key_, value_) -> {
//                                if(GeneralTool.isNotEmpty(conditionJson.getMainCourseTypeIds()) && conditionJson.getMainCourseTypeIds().contains(key_))
//                                {
//                                    mainCourseTypeNames.add(value_);
//                                }
//                                if(GeneralTool.isNotEmpty(conditionJson.getSubCourseTypeIds()) && conditionJson.getSubCourseTypeIds().contains(key_))
//                                {
//                                    subCourseTypeNames.add(value_);
//                                }
//                                if(GeneralTool.isNotEmpty(conditionJson.getLaterUpCourseTypeIds()) && conditionJson.getLaterUpCourseTypeIds().contains(key_))
//                                {
//                                    laterUpCourseTypeNames.add(value_);
//                                }
//                            });
//                        }
//                    }
//                }
//                if(mainCourseTypeNames.size()>0)
//                {
//                    conditionJson.setMainCourseTypeNames(mainCourseTypeNames);
//                }
//                if(subCourseTypeNames.size()>0)
//                {
//                    conditionJson.setSubCourseTypeNames(subCourseTypeNames);
//                }
//                if(laterUpCourseTypeNames.size()>0)
//                {
//                    conditionJson.setLaterUpCourseTypeNames(laterUpCourseTypeNames);
//                }

                //获取所有课程的名称
                Set<Long> courseIds = new HashSet<>();
                List<String> mainCourseNames = new ArrayList<>();//主课
                List<String> subCourseNames = new ArrayList<>();//子课
                List<String> laterUpCourseNames = new ArrayList<>();//后续课
                if (GeneralTool.isNotEmpty(conditionJson.getMainCourseIds())) {
                    courseIds.addAll(conditionJson.getMainCourseIds());
                }
                if (GeneralTool.isNotEmpty(conditionJson.getSubCourseIds())) {
                    courseIds.addAll(conditionJson.getSubCourseIds());
                }
                if (GeneralTool.isNotEmpty(conditionJson.getLaterUpCourseIds())) {
                    courseIds.addAll(conditionJson.getLaterUpCourseIds());
                }
                if (courseIds.size() > 0) {
                    Result<Map<Long, String>> result = institutionCenterClient.getCourseNameByIds(courseIds);
                    if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                        Map<Long, String> nameMap = result.getData();
                        if (GeneralTool.isNotEmpty(nameMap)) {
                            nameMap.forEach((key_, value_) -> {
                                if (GeneralTool.isNotEmpty(conditionJson.getMainCourseIds()) && conditionJson.getMainCourseIds().contains(key_)) {
                                    mainCourseNames.add(value_);
                                }
                                if (GeneralTool.isNotEmpty(conditionJson.getSubCourseIds()) && conditionJson.getSubCourseIds().contains(key_)) {
                                    subCourseNames.add(value_);
                                }
                                if (GeneralTool.isNotEmpty(conditionJson.getLaterUpCourseIds()) && conditionJson.getLaterUpCourseIds().contains(key_)) {
                                    laterUpCourseNames.add(value_);
                                }
                            });
                        }
                    }
                }
                if (mainCourseNames.size() > 0) {
                    conditionJson.setMainCourseNames(mainCourseNames);
                }
                if (subCourseNames.size() > 0) {
                    conditionJson.setSubCourseNames(subCourseNames);
                }
                if (laterUpCourseNames.size() > 0) {
                    conditionJson.setLaterUpCourseNames(laterUpCourseNames);
                }

                if (GeneralTool.isEmpty(conditionJson.getMainAndSubCourseFlag())) {
                    conditionJson.setMainAndSubCourseFlag(0);//默认为0-and
                }
                if (GeneralTool.isEmpty(conditionJson.getSubAndLaterCourseFlag())) {
                    conditionJson.setSubAndLaterCourseFlag(0);//默认为0-and
                }
                if (GeneralTool.isNotEmpty(stepOrderIds) && stepOrderIds.size() > 0) {
                    conditionJson.setStepOrderIds(stepOrderIds);
                }
                //根据策略返回符合条件的offerItem
                List<Long> studentOfferItemIds = incentivePolicyStudentOfferItemMapper.getStudentOfferItemList(conditionJson, incentivePolicy);
                log.info("=======策略ID【{}】符合条件的offer列表数量{}，date{}===========", fkIncentivePolicyId, studentOfferItemIds.size(), DateUtil.formatDateTime(new Date()));

                LambdaQueryWrapper<IncentivePolicyStudentOfferItem> wrapper = new LambdaQueryWrapper();
                wrapper.eq(IncentivePolicyStudentOfferItem::getFkIncentivePolicyId, fkIncentivePolicyId);
                //非系统录入的或者已结算的
                wrapper.and(
                        wrapper_ ->
                                wrapper_.notIn(IncentivePolicyStudentOfferItem::getCountType, 2).or().eq(IncentivePolicyStudentOfferItem::getFinanceStatus, 1));
                List<IncentivePolicyStudentOfferItem> incentivePolicyStudentOfferItems = incentivePolicyStudentOfferItemMapper.selectList(wrapper);
                List<Long> hisItemIds = incentivePolicyStudentOfferItems.stream().map(IncentivePolicyStudentOfferItem::getFkStudentOfferItemId).collect(Collectors.toList());

                //删除原策略统计（非结算，非手工计入和手工剔除）的学生计划
                LambdaQueryWrapper<IncentivePolicyStudentOfferItem> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                lambdaQueryWrapper.eq(IncentivePolicyStudentOfferItem::getFkIncentivePolicyId, fkIncentivePolicyId);
                if (GeneralTool.isNotEmpty(hisItemIds)) {
                    studentOfferItemIds.removeAll(hisItemIds);//移除非系统录入的或者已结算的id集合
                    lambdaQueryWrapper.notIn(IncentivePolicyStudentOfferItem::getFkStudentOfferItemId, hisItemIds);
                }
                incentivePolicyStudentOfferItemMapper.delete(lambdaQueryWrapper);

                List<IncentivePolicyStudentOfferItem> incentivePolicyStudentOfferItemList = new ArrayList<>();
                if (GeneralTool.isNotEmpty(studentOfferItemIds)) {
                    for (Long itemId : studentOfferItemIds) {
                        IncentivePolicyStudentOfferItem incentivePolicyStudentOfferItem = new IncentivePolicyStudentOfferItem();
                        incentivePolicyStudentOfferItem.setFkIncentivePolicyId(fkIncentivePolicyId);
                        incentivePolicyStudentOfferItem.setFkStudentOfferItemId(itemId);
                        incentivePolicyStudentOfferItem.setCountType(2);//系统计入
                        incentivePolicyStudentOfferItem.setFinanceStatus(0);//未结算
                        utilService.setCreateInfo(incentivePolicyStudentOfferItem);
                        incentivePolicyStudentOfferItemList.add(incentivePolicyStudentOfferItem);
                    }
                }
                if (incentivePolicyStudentOfferItemList.size() > 0) {
                    log.info("=======策略ID【{}】删除完成,date{}===========", fkIncentivePolicyId, DateUtil.formatDateTime(new Date()));
                    this.saveBatch(incentivePolicyStudentOfferItemList);
                }
            } else {
                throw new GetServiceException(LocaleMessageUtils.getMessage("data_is_being_compiled"));
            }
        } catch (Throwable e) {
            //处理抛出自定义异常
            if (e instanceof GetServiceException) {
                throw new GetServiceException(e.getMessage());
            } else {
                throw Exceptions.unchecked(e);
            }
        } finally {
            if (lockRs) {
                redisLockClient.unSingleLock(key);
            }
        }
        log.info("=======策略ID【{}】统计完成,date{}===========", fkIncentivePolicyId, DateUtil.formatDateTime(new Date()));
        return true;
    }

    @Override
    public Boolean updatePolicyStudentOfferItem(Long id, Long fkReceivablePlanId, Long fkPayablePlanId) {
        IncentivePolicyStudentOfferItem incentivePolicyStudentOfferItem = this.getById(id);
        incentivePolicyStudentOfferItem.setFkReceivablePlanId(fkReceivablePlanId);
        incentivePolicyStudentOfferItem.setFkPayablePlanId(fkPayablePlanId);
        return this.updateById(incentivePolicyStudentOfferItem);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String addDiyPolicyStudent(IncentivePolicyAddOfferItemDto incentivePolicyAddOfferItemDto) {
        if (GeneralTool.isEmpty(incentivePolicyAddOfferItemDto.getIncentivePolicyId()) || GeneralTool.isEmpty(incentivePolicyAddOfferItemDto.getStudentOfferItemIds())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        //根据策略计算应收应付金额
        IncentivePolicy incentivePolicy = incentivePolicyMapper.selectById(incentivePolicyAddOfferItemDto.getIncentivePolicyId());
        if (GeneralTool.isEmpty(incentivePolicy)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("the_current_reward_policy_does_not_exist"));
        }
        //非进行中不能进行结算
        if (incentivePolicy.getStatus() != 1) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("the_current_reward_policy_has_been_settled"));
        }

        List<IncentivePolicyStudentOfferItem> newIncentivePolicyStudentOfferItems = new ArrayList<>();

        //查询手工计入列表的集合
        List<IncentivePolicyStudentOfferItem> incentivePolicyStudentOfferItems = this.list(Wrappers.<IncentivePolicyStudentOfferItem>lambdaQuery()
                .in(IncentivePolicyStudentOfferItem::getFkStudentOfferItemId, incentivePolicyAddOfferItemDto.getStudentOfferItemIds())
                .eq(IncentivePolicyStudentOfferItem::getFkIncentivePolicyId, incentivePolicyAddOfferItemDto.getIncentivePolicyId())
                .eq(IncentivePolicyStudentOfferItem::getCountType, 1));//统计手工计入数

        List<Long> diyNum = incentivePolicyStudentOfferItems.stream().map(IncentivePolicyStudentOfferItem::getFkStudentOfferItemId).collect(Collectors.toList());
        //判断选择offerItemId是否已存在手动录入的item，如已存在则返回提示“xxxx”
        if (diyNum.contains(incentivePolicyAddOfferItemDto.getStudentOfferItemIds())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("repeated_selection_of_application_plan"));
        }
        Integer sysCount = 0;
        for (Long offerItemId : incentivePolicyAddOfferItemDto.getStudentOfferItemIds()) {
            //判断是否存在，存在则更新统计类型，否则插入
            IncentivePolicyStudentOfferItem item = this.getOne(Wrappers.<IncentivePolicyStudentOfferItem>lambdaQuery()
                    .in(IncentivePolicyStudentOfferItem::getFkStudentOfferItemId, offerItemId)
                    .eq(IncentivePolicyStudentOfferItem::getFkIncentivePolicyId, incentivePolicyAddOfferItemDto.getIncentivePolicyId())
                    .last(" limit 1"));
            if (GeneralTool.isNotEmpty(item)) {
                item.setCountType(1);//统计方式更改为：手工计入
                utilService.setUpdateInfo(item);
                this.updateById(item);
                sysCount = sysCount + 1;
            } else {
                //新增
                IncentivePolicyStudentOfferItem incentivePolicyStudentOfferItem = new IncentivePolicyStudentOfferItem();
                incentivePolicyStudentOfferItem.setFkIncentivePolicyId(incentivePolicyAddOfferItemDto.getIncentivePolicyId());
                incentivePolicyStudentOfferItem.setFkStudentOfferItemId(offerItemId);
                incentivePolicyStudentOfferItem.setCountType(1);//手工计入
                incentivePolicyStudentOfferItem.setFinanceStatus(0);//未结算
                utilService.setCreateInfo(incentivePolicyStudentOfferItem);
                newIncentivePolicyStudentOfferItems.add(incentivePolicyStudentOfferItem);
            }
        }
        if (GeneralTool.isNotEmpty(newIncentivePolicyStudentOfferItems)) {
            this.saveBatch(newIncentivePolicyStudentOfferItems);
        }

        Integer sucCount = incentivePolicyAddOfferItemDto.getStudentOfferItemIds().size() - sysCount;
        String result = "用户共选择" + incentivePolicyAddOfferItemDto.getStudentOfferItemIds().size() + "条进行手动计入设置，成功新增" + sucCount + "条。";
        if (sysCount > 0) {
            result += "其中有" + sysCount + "条系统计入申请计划，已设置类型为手工计入。";
        }
        return result;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteDiyPolicyStudent(List<Long> ids) {
        Wrapper<IncentivePolicyStudentOfferItem> wrapper = Wrappers.<IncentivePolicyStudentOfferItem>lambdaQuery()
                .in(IncentivePolicyStudentOfferItem::getId, ids);
        List<IncentivePolicyStudentOfferItem> list = this.list(wrapper);
        //是否为空
        if (GeneralTool.isEmpty(list)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_application_plans_that_can_be_manually_deleted"));
        }
        //是否已结算
        Long financeStatus_1_num = list.stream().filter(item -> item.getFinanceStatus() == 1).count();
        if (financeStatus_1_num.intValue() > 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("the_application_plan_has_been_settled"));
        }
        //是否已手工剔除
        Long countType_0_num = list.stream().filter(item -> item.getCountType() == 0).count();
        if (countType_0_num.intValue() > 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("duplicate_deletion_application_plan"));
        }
        list.stream().forEach(incentivePolicyStudentOfferItem -> {
            incentivePolicyStudentOfferItem.setCountType(0);//手工剔除
        });
        //批量更新
        this.updateBatchById(list);
//        Long fkIncentivePolicyId = list.get(0).getFkIncentivePolicyId();
        //重新统计申请计划列表
//        this.updatePolicyStudentOfferItem(fkIncentivePolicyId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelDiyPolicyStudent(Long id) {
        IncentivePolicyStudentOfferItem item = this.getById(id);
        //是否为空
        if (GeneralTool.isEmpty(item)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_cancellable_application_plans"));
        }
        //是否已结算
        if (item.getFinanceStatus() == 1) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("only_unsettled_application_plans_can_be_cancelled_manually"));
        }
        //是否系统计入
        if (item.getCountType() == 2) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("only_application_plans_that_are_not_included_in_the_system_can_cancel_manual_settings"));
        }
        //取消手动逻辑：先删除该计划，重新统计符合策略的学生计划
        this.removeById(id);
        //重新统计申请计划列表
        this.updatePolicyStudentOfferItem(item.getFkIncentivePolicyId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelSettlePolicyStudent(Long fkIncentivePolicyId) {
        boolean lockRs = false;
        String key = INCENTIVE_POLICY_KEY + fkIncentivePolicyId.toString();
        try {
            lockRs = redisLockClient.trySingleLock(key);
            if (lockRs) {
                Wrapper<IncentivePolicyStudentOfferItem> wrapper = Wrappers.<IncentivePolicyStudentOfferItem>lambdaQuery()
                        .eq(IncentivePolicyStudentOfferItem::getFkIncentivePolicyId, fkIncentivePolicyId)
                        .eq(IncentivePolicyStudentOfferItem::getFinanceStatus, 1);//已结算
                List<IncentivePolicyStudentOfferItem> items = this.list(wrapper);
                //是否为空
                if (GeneralTool.isEmpty(items)) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("no_cancellable_application_plans"));
                }
                Set<Long> receivablePlanIds = items.stream().map(IncentivePolicyStudentOfferItem::getFkReceivablePlanId).collect(Collectors.toSet());
                Set<Long> payablePlanIds = items.stream().map(IncentivePolicyStudentOfferItem::getFkPayablePlanId).collect(Collectors.toSet());

                //删除或者作废应收应付
                receivableAndPayablePlanService.delete(receivablePlanIds, payablePlanIds);

                IncentivePolicy incentivePolicy = incentivePolicyMapper.selectById(fkIncentivePolicyId);
                incentivePolicy.setStatus(1);//重新置为进行中
                incentivePolicyMapper.updateById(incentivePolicy);

                //更新统计数据为未结算
                for (IncentivePolicyStudentOfferItem incentivePolicyStudentOfferItem : items) {
                    incentivePolicyStudentOfferItem.setFinanceStatus(0);//未结算
                }
                this.updateBatchById(items);

                //判断有没有发生实收，如有实收，则需要删除实收实付
                List<ReceiptFormItemVo> receiptFormItemVos = financeCenterClient.getReceiptFormItemListFeignByPlanIds(receivablePlanIds);
                List<PaymentFormVo> paymentFormVos = financeCenterClient.getPayFormListFeignByPlanIds(payablePlanIds);
                Set<Long> paymentFormIds = paymentFormVos.stream().map(PaymentFormVo::getId).collect(Collectors.toSet());
                Set<Long> receiptFormIds = receiptFormItemVos.stream().map(ReceiptFormItemVo::getFkReceiptFormId).collect(Collectors.toSet());
                Boolean result = financeCenterClient.updateReceiptAndPaymentFormStatus(paymentFormIds, receiptFormIds);
                if (!result) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("invalid_cancellation_of_actual_payment_received"));
                }
            } else {
                throw new GetServiceException(LocaleMessageUtils.getMessage("there_is_data_being_settled"));
            }
        } catch (Throwable e) {
            //处理抛出自定义异常
            if (e instanceof GetServiceException) {
                throw new GetServiceException(e.getMessage());
            } else {
                throw Exceptions.unchecked(e);
            }
        } finally {
            if (lockRs) {
                redisLockClient.unSingleLock(key);
            }
        }
        //重新统计符合政策的学生计划列表
        this.updatePolicyStudentOfferItem(fkIncentivePolicyId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void settleSelectPolicyStudent(Long fkIncentivePolicyId) {
        //针对策略ID添加分布式锁，一次只能结算一批
        boolean lockRs = false;
        String key = INCENTIVE_POLICY_KEY + fkIncentivePolicyId.toString();
        try {
            lockRs = redisLockClient.trySingleLock(key);
            if (lockRs) {
                Wrapper<IncentivePolicyStudentOfferItem> wrapper = Wrappers.<IncentivePolicyStudentOfferItem>lambdaQuery()
//                        .in(IncentivePolicyStudentOfferItem::getFkStudentOfferItemId,incentivePolicyStudentOfferItemSettleVo.getFkStudentOfferItemIds())
                        .eq(IncentivePolicyStudentOfferItem::getFkIncentivePolicyId, fkIncentivePolicyId)
                        .eq(IncentivePolicyStudentOfferItem::getFinanceStatus, 0)//未結算
                        .ne(IncentivePolicyStudentOfferItem::getCountType, 0);//非人工剔除
                List<IncentivePolicyStudentOfferItem> list = this.list(wrapper);
                //是否为空
                if (GeneralTool.isEmpty(list)) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("no_unsettled_application_plans_available"));
                }
                //根据策略计算应收应付金额
                IncentivePolicy incentivePolicy = incentivePolicyMapper.selectById(fkIncentivePolicyId);

                if (GeneralTool.isEmpty(incentivePolicy)) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("reward_policy_does_not_exist"));
                }
                //非进行中不能进行结算
                if (incentivePolicy.getStatus() != 1) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("the_reward_policy_has_been_settled"));
                }

                IncentivePolicyRewardVo incentivePolicyRewardVo = incentivePolicy.getRewardJson();

                if (GeneralTool.isEmpty(incentivePolicyRewardVo.getRewardDetails()) && GeneralTool.isEmpty(incentivePolicyRewardVo.getRewardIds())) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("reward_rules_have_not_been_configured_yet"));
                }


                //进行应收应付结算
                if (GeneralTool.isNotEmpty(incentivePolicyRewardVo.getRewardDetails())) {
                    IncentivePolicyRewardVo.RewardDetail rewardDetail = null;
                    Integer offerItemNum = list.size();//统计计划数
                    //按照规则起始人数进行倒序排序，统计列表从符合人数最少的进行奖励规则匹配
                    List<IncentivePolicyRewardVo.RewardDetail> sortedRewardDetails = incentivePolicyRewardVo.getRewardDetails().stream()
                            .sorted(Comparator.comparing(IncentivePolicyRewardVo.RewardDetail::getRewardStartNum).reversed())
                            .collect(Collectors.toList());

                    for (IncentivePolicyRewardVo.RewardDetail rewardDetail_ : sortedRewardDetails) {
                        if (rewardDetail_ != null && rewardDetail_.getRewardStartNum() != null) {
                            //如学生offerItems数量大于起始数则符合条件
                            if (offerItemNum >= rewardDetail_.getRewardStartNum()) {
                                //匹配到的规则
                                rewardDetail = rewardDetail_;
                                break;
                            }
                        } else {
                            throw new GetServiceException(LocaleMessageUtils.getMessage("no_reward_rule_matching"));
                        }
                    }

                    if (GeneralTool.isEmpty(rewardDetail)) {
                        throw new GetServiceException(LocaleMessageUtils.getMessage("not_meeting_the_requirements_of_the_reward_rules"));
                    }

                    BigDecimal payableAmount = new BigDecimal(0);//应付金额
                    BigDecimal receivableAmount = new BigDecimal(0);//应收金额

                    //每人奖励：大于等于起点人数，则每个offerItem的应收应付应为规则设置的金额
                    if (rewardDetail.getRewardType() == 1) {
                        payableAmount = rewardDetail.getPayableAmount();
                        receivableAmount = rewardDetail.getReceivableAmount();
                    }
                    //一次性奖励：大于等于起点人数，则一次性奖励，每个offerItem均摊，应收应付为：规则金额/数量
                    else if (rewardDetail.getRewardType() == 2) {
                        payableAmount = rewardDetail.getPayableAmount().divide(BigDecimal.valueOf(offerItemNum), 2, RoundingMode.HALF_UP);//保留2位小数
                        receivableAmount = rewardDetail.getReceivableAmount().divide(BigDecimal.valueOf(offerItemNum), 2, RoundingMode.HALF_UP);//保留2位小数
                    }

                    List<IncentiveReceivableAndPayablePlanDto> incentiveReceivableAndPayablePlanDtoList = new ArrayList<>();
                    for (IncentivePolicyStudentOfferItem incentivePolicyStudentOfferItem : list) {
                        //应付
                        PayablePlanDto payablePlanDto = new PayablePlanDto();
                        payablePlanDto.setFkCompanyId(incentivePolicy.getFkCompanyId());//所属策略公司
                        payablePlanDto.setFkTypeKey(TableEnum.SALE_STUDENT_OFFER_ITEM.key);
                        payablePlanDto.setFkTypeTargetId(incentivePolicyStudentOfferItem.getFkStudentOfferItemId());
                        payablePlanDto.setFkCurrencyTypeNum(rewardDetail.getPayableCurrencyTypeNum());//币种
                        payablePlanDto.setSplitRate(new BigDecimal(100));//代理佣金费率
                        payablePlanDto.setBonusAmount(payableAmount);//奖励金额
                        payablePlanDto.setPayableAmount(payableAmount);//应付金额

                        //应收
                        ReceivablePlanDto receivablePlanDto = new ReceivablePlanDto();
                        receivablePlanDto.setFkCompanyId(incentivePolicy.getFkCompanyId());//所属策略公司
                        receivablePlanDto.setFkTypeKey(TableEnum.SALE_STUDENT_OFFER_ITEM.key);
                        receivablePlanDto.setFkTypeTargetId(incentivePolicyStudentOfferItem.getFkStudentOfferItemId());
                        receivablePlanDto.setFkCurrencyTypeNum(rewardDetail.getReceivableCurrencyTypeNum());//币种
                        receivablePlanDto.setNetRate(new BigDecimal(100));//渠道费率
                        receivablePlanDto.setBonusAmount(receivableAmount);//其它金额
                        receivablePlanDto.setReceivableAmount(receivableAmount);//应付
                        receivablePlanDto.setSummary(rewardDetail.getSummaryDes());//摘要
                        receivablePlanDto.setBonusType(ProjectExtraEnum.INCENTIVE_REWARD.key);//其它奖励类型

                        IncentiveReceivableAndPayablePlanDto incentiveReceivableAndPayablePlanDto = new IncentiveReceivableAndPayablePlanDto();
                        incentiveReceivableAndPayablePlanDto.setPayablePlanVo(payablePlanDto);
                        incentiveReceivableAndPayablePlanDto.setReceivablePlanVo(receivablePlanDto);
                        incentiveReceivableAndPayablePlanDto.setFkIncentivePolicyStudentOfferItemId(incentivePolicyStudentOfferItem.getId());

                        incentiveReceivableAndPayablePlanDtoList.add(incentiveReceivableAndPayablePlanDto);

                        incentivePolicyStudentOfferItem.setFinanceStatus(1);//标记为已结算
                        incentivePolicyStudentOfferItem.setSettlementTime(LocalDateTime.now());//结算时间
                        utilService.setUpdateInfo(incentivePolicyStudentOfferItem);
                    }
                    if (GeneralTool.isNotEmpty(incentiveReceivableAndPayablePlanDtoList)) {
                        receivableAndPayablePlanService.addReceivableAndPayablePlans(incentiveReceivableAndPayablePlanDtoList);
                    }
                }
                //货币奖励和非货币奖励都标记为结算状态
                incentivePolicy.setStatus(2);//已结算
                utilService.setUpdateInfo(incentivePolicy);
                incentivePolicyMapper.updateById(incentivePolicy);

                //批量标记计划列表为已结算
                this.saveOrUpdateBatch(list);
            } else {
                throw new GetServiceException(LocaleMessageUtils.getMessage("there_is_data_being_settled"));
            }
            System.out.println("成功！！！！");
        } catch (Throwable e) {

            //处理抛出自定义异常
            if (e instanceof GetServiceException) {
                throw new GetServiceException(e.getMessage());
            } else {
                throw Exceptions.unchecked(e);
            }
        } finally {
            if (lockRs) {
                redisLockClient.unSingleLock(key);
            }
        }
    }

    @Override
    public Integer settleSelectPolicyStudentCount(Long fkIncentivePolicyId) {
        //根据策略计算应收应付金额
        IncentivePolicy incentivePolicy = incentivePolicyMapper.selectById(fkIncentivePolicyId);
        if (GeneralTool.isEmpty(incentivePolicy)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("reward_policy_does_not_exist"));
        }
        //非进行中不能进行结算
        if (incentivePolicy.getStatus() != 1) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("the_reward_policy_has_been_settled"));
        }

        Wrapper<IncentivePolicyStudentOfferItem> wrapper = Wrappers.<IncentivePolicyStudentOfferItem>lambdaQuery()
                .eq(IncentivePolicyStudentOfferItem::getFkIncentivePolicyId, fkIncentivePolicyId)
                .eq(IncentivePolicyStudentOfferItem::getFinanceStatus, 0)//未結算
                .ne(IncentivePolicyStudentOfferItem::getCountType, 0);//非人工剔除
        List<IncentivePolicyStudentOfferItem> list = this.list(wrapper);

        return list.size();
    }


    /**
     * 导出列表Excel
     *
     * @param response
     */
    @Override
    public void exportDataExcel(HttpServletResponse response, IncentivePolicyStudentOfferItemDto incentivePolicyStudentOfferItemDto) {
        List<IncentivePolicyStudentOfferItemVo> incentivePolicyStudentOfferItemVos = getPolicyStudentDatas(incentivePolicyStudentOfferItemDto, null);
        if (GeneralTool.isEmpty(incentivePolicyStudentOfferItemVos)) {
            return;
        }
        //查询策略mum
        IncentivePolicy incentivePolicy = incentivePolicyMapper.selectById(incentivePolicyStudentOfferItemDto.getFkIncentivePolicyId());
        List<IncentivePolicyStudentOfferItemExcelVo> incentivePolicyStudentOfferItemExcelVos = BeanCopyUtils.copyListProperties(incentivePolicyStudentOfferItemVos, IncentivePolicyStudentOfferItemExcelVo::new);
        if (GeneralTool.isNotEmpty(incentivePolicy)) {
            incentivePolicyStudentOfferItemExcelVos.stream().forEach(item -> {
                item.setIncentivePolicyNum(incentivePolicy.getNum());
            });
        }
        FileUtils.exportExcelNotWrapText(response, incentivePolicyStudentOfferItemExcelVos, "IncentivePolicyOfferItem", IncentivePolicyStudentOfferItemExcelVo.class);
    }
}

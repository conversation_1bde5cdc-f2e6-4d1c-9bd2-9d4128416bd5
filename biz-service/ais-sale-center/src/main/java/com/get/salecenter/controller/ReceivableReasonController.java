package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.permissioncenter.vo.ResourceVo;
import com.get.salecenter.vo.ReceivableReasonVo;
import com.get.salecenter.service.IReceivableReasonService;
import com.get.salecenter.dto.ReceivableReasonDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/11/3
 * @TIME: 10:00
 * @Description:
 **/
@Api(tags = "应收计划额外原因类型管理")
@RestController
@RequestMapping("sale/receivablereason")
public class ReceivableReasonController {
    @Resource
    private IReceivableReasonService receivableReasonService;

    /**
     * 列表数据
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "列表数据", notes = "keyWord为关键词")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/应收计划额外原因类型管理/查询应收计划额外原因类型")
    @PostMapping("datas")
    public ResponseBo<ReceivableReasonVo> datas(@RequestBody SearchBean<ReceivableReasonDto> page) {
        List<ReceivableReasonVo> datas = receivableReasonService.getReceivableReasons(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/应收计划额外原因类型管理/应收计划额外原因类型详情")
    @GetMapping("/{id}")
    public ResponseBo<ReceivableReasonVo> detail(@PathVariable("id") Long id) {
        //TODO 改过
        //ReceivableReason data = receivableReasonService.findReceivableReasonById(id);
        ReceivableReasonVo data = receivableReasonService.findReceivableReasonById(id);
        ReceivableReasonVo receivableReasonVo = BeanCopyUtils.objClone(data, ReceivableReasonVo::new);
        return new ResponseBo<>(receivableReasonVo);
    }


    /**
     * 新增信息
     *
     * @param receivableReasonDto
     * @return
     * @
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/应收计划额外原因类型管理/新增应收计划额外原因类型")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(ReceivableReasonDto.Add.class) ReceivableReasonDto receivableReasonDto) {
        return SaveResponseBo.ok(this.receivableReasonService.addReceivableReason(receivableReasonDto));
    }

    /**
     * 删除
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为删除数据的ID")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/应收计划额外原因类型管理/删除应收计划额外原因类型")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        this.receivableReasonService.delete(id);
        return ResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param receivableReasonDto
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/应收计划额外原因类型管理/更新应收计划额外原因类型")
    @PostMapping("update")
    public ResponseBo<ResourceVo> update(@RequestBody @Validated(ReceivableReasonDto.Update.class) ReceivableReasonDto receivableReasonDto) {
        return UpdateResponseBo.ok(receivableReasonService.updateReceivableReason(receivableReasonDto));
    }


    /**
     * 批量新增信息
     *
     * @param receivableReasonDtos
     * @return
     * @
     */
    @ApiOperation(value = "批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/应收计划额外原因类型管理/批量保存")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody @Validated(ReceivableReasonDto.Add.class) ValidList<ReceivableReasonDto> receivableReasonDtos) {
        receivableReasonService.batchAdd(receivableReasonDtos);
        return ResponseBo.ok();
    }

    /**
     * 上移下移
     *
     * @param receivableReasonDtos
     * @return
     * @
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/应收计划额外原因类型管理/移动顺序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<ReceivableReasonDto> receivableReasonDtos) {
        receivableReasonService.movingOrder(receivableReasonDtos);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 应收计划额外原因下拉
     * @Param [companyId]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "应收计划额外原因下拉", notes = "")
    @GetMapping("getReasonSelect")
    public ResponseBo<BaseSelectEntity> getReasonSelect() {
        List<BaseSelectEntity> datas = receivableReasonService.getReasonSelect();
        return new ListResponseBo<>(datas);
    }

}

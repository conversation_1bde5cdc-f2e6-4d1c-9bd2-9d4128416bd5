package com.get.salecenter.service;


import com.get.core.mybatis.service.GetService;
import com.get.salecenter.entity.AgentCompany;
import com.get.salecenter.dto.AgentCompanyDto;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/9/15
 * @TIME: 14:28
 * @Description: 安全配置
 **/
public interface IAgentCompanyService extends GetService<AgentCompany> {

    void editAgentCompany(List<AgentCompanyDto> agentCompanyDtos);


    /**
     * @return java.lang.Long
     * @Description: 新增关系
     * @Param [agentCompanyDto]
     * <AUTHOR>
     */
    Long addRelation(AgentCompanyDto agentCompanyDto);


    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 根据公司id查询对应代理id
     * @Param [companyIds]
     * <AUTHOR>
     */
    List<Long> getRelationByCompanyId(List<Long> companyIds);


    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 根据代理id查询公司ids
     * @Param [agentId]
     * <AUTHOR>
     */
    List<Long> getRelationByAgentId(Long agentId);


    /**
     * 根据代理ids查询公司ids
     *
     * @param ids
     * @return
     */
    Map<Long, Set<Long>> getRelationByAgentIds(Set<Long> ids);


    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 查询合同对应代理所关联的公司
     * @Param []
     * <AUTHOR>
     */
    List<Long> getAgentCompanyIdByContractId(Long contractId);
}

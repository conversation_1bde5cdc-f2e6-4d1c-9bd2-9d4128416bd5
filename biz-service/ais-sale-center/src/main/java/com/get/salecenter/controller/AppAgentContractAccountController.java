package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.UpdateResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.vo.AppAgentContractAccountVo;
import com.get.salecenter.vo.MediaAndAttachedVo;
import com.get.salecenter.service.IAppAgentContractAccountService;
import com.get.salecenter.dto.*;
import com.get.salecenter.dto.AppAgentContractAccountUpdateDto;
import com.get.salecenter.dto.AppAgentContractAccountDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: Hardy
 * @create: 2022/11/22 18:13
 * @verison: 1.0
 * @description:
 */
@Api(tags = "代理申请合同账户管理")
@RestController
@RequestMapping("sale/appAgentContractAccount")
public class AppAgentContractAccountController {

    @Resource
    private IAppAgentContractAccountService appAgentContractAccountService;


    /**
     * 新增代理申请联系人
     *
     * @param appAgentContractAccountAddDto
     * @return
     * @
     */
    @ApiOperation(value = "新增合同账户", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/代理申请合同账户管理/新增合同账户")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated({AppAgentContractAccountAddDto.Add.class, AppAgentContractAccountDto.Save.class}) AppAgentContractAccountAddDto appAgentContractAccountAddDto) {
        return SaveResponseBo.ok(appAgentContractAccountService.addAppAgentContractAccount(appAgentContractAccountAddDto));
    }


    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/代理申请合同账户管理/合同详情")
    @GetMapping("/{id}")
    public ResponseBo<AppAgentContractAccountVo> detail(@PathVariable("id") Long id) {
        AppAgentContractAccountVo appAgentContractAccountVo = appAgentContractAccountService.findAppAgentContractAccountById(id);
        return new ResponseBo<>(appAgentContractAccountVo);
    }


    /**
     * 修改信息
     *
     * @param appAgentContractAccountUpdateDto1
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/代理申请合同账户管理/更新事件")
    @PostMapping("update")
    public ResponseBo<AppAgentContractAccountVo> update(@RequestBody @Validated({AppAgentContractAccountUpdateDto.Update.class, AppAgentContractAccountDto.Save.class}) AppAgentContractAccountUpdateDto appAgentContractAccountUpdateDto1) {
        return UpdateResponseBo.ok(appAgentContractAccountService.updateAppAgentContractAccount(appAgentContractAccountUpdateDto1));
    }




    /**
     * 代理附件保存接口
     *
     * @param mediaAttachedVo
     * @return
     * @
     */
    @VerifyLogin(IsVerify = false)
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "代理申请账户附件保存接口")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/代理申请账户管理/代理申请账户附件保存接口")
    @PostMapping("upload")
    public ResponseBo<MediaAndAttachedVo> addMedia(@RequestBody  @Validated(MediaAndAttachedDto.Add.class) ValidList<MediaAndAttachedDto> mediaAttachedVo) {
        return new ListResponseBo<>(appAgentContractAccountService.addMedia(mediaAttachedVo));
    }


}

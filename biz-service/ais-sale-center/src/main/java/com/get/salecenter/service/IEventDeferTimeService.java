package com.get.salecenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.salecenter.vo.EventDeferTimeVo;
import com.get.salecenter.entity.EventDeferTime;
import com.get.salecenter.dto.EventDeferTimeDto;

import java.util.List;

public interface IEventDeferTimeService extends IService<EventDeferTime> {
    /**
     * 获取该活动的活动时间日志
     *
     * @param fkEventId 活动ID
     * @return
     */
    List<EventDeferTimeVo> datas(Long fkEventId);

    /**
     * 新增活动时间日志
     *
     * @param eventDeferTimeDto 活动时间日志VO
     * @return
     */
    Long addEventDeferTime(EventDeferTimeDto eventDeferTimeDto);

    /**
     * 删除活动时间日志
     *
     * @param id 活动时间日志ID
     */
    void delete(Long id);

}

package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.salecenter.vo.SettlementCommissionNoticeVo;
import com.get.salecenter.service.PayableSettlementMessageService;
import com.get.salecenter.dto.SettlementCommissionNoticeDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Api(tags = "结算通知")
@RestController
@RequestMapping("/sale/setMsg")
public class PayableSettlementMessageController {

    @Resource
    private PayableSettlementMessageService payableSettlementMessageService;

//    @ApiOperation(value = "添加结算通知信息")
//    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DELETE, description = "财务中心/佣金结算/添加结算通知信息")
//    @PostMapping("add")
//    public SaveResponseBo add(@RequestBody @Validated({BaseVoEntity.Add.class}) SettlementCommissionNoticeDto settlementCommissionNoticeVo) {
//        return payableSettlementMessageService.add(settlementCommissionNoticeVo);
//    }



    @ApiOperation(value = "发送邮件")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DELETE, description = "财务中心/佣金结算/发送邮件")
    @PostMapping("sendEmail")
    public ResponseBo senEmail(@RequestBody @Validated SettlementCommissionNoticeDto settlementCommissionNoticeDto) {
        payableSettlementMessageService.sendEmail(settlementCommissionNoticeDto);
        return new ResponseBo();
    }
}

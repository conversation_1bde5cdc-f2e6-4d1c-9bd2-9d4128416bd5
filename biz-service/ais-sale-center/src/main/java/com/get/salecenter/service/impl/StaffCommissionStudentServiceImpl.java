package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.service.impl.GetServiceImpl;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.StaffCommissionStudentMapper;
import com.get.salecenter.entity.StaffCommissionAction;
import com.get.salecenter.entity.StaffCommissionStudent;
import com.get.salecenter.service.IStaffCommissionActionService;
import com.get.salecenter.service.IStaffCommissionStudentService;
import com.get.salecenter.dto.StaffCommissionStudentDto;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2023/2/7 15:54
 * @verison: 1.0
 * @description:
 */
@Service
public class StaffCommissionStudentServiceImpl extends GetServiceImpl<StaffCommissionStudentMapper, StaffCommissionStudent> implements IStaffCommissionStudentService {

   @Resource
   private UtilService utilService;
   @Lazy
   @Resource
   private IStaffCommissionActionService staffCommissionActionService;

   @Resource
   private StaffCommissionStudentMapper staffCommissionStudentMapper;
    /**
     * 新增
     * @param staffCommissionStudentDto
     * @return
     */
    @Override
    public Long addStaffCommissionStudent(StaffCommissionStudentDto staffCommissionStudentDto) {
        if (GeneralTool.isEmpty(staffCommissionStudentDto)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        StaffCommissionStudent staffCommissionStudent = BeanCopyUtils.objClone(staffCommissionStudentDto, StaffCommissionStudent::new);
        assert staffCommissionStudent != null;
        utilService.setCreateInfo(staffCommissionStudent);
        boolean b = save(staffCommissionStudent);
        if (!b){
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
        return staffCommissionStudent.getId();
    }

    /**
     * 批量新增
     * @param staffCommissionStudentDtos
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(List<StaffCommissionStudentDto> staffCommissionStudentDtos) {
        List<StaffCommissionStudent> staffCommissionStudents = BeanCopyUtils.copyListProperties(staffCommissionStudentDtos, StaffCommissionStudent::new);
        staffCommissionStudents.forEach(staffCommissionStudent -> utilService.setCreateInfo(staffCommissionStudent));
        boolean b = saveBatch(staffCommissionStudents);
        if (!b){
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
    }

    /**
     * 删除
     * @param id
     */
    @Override
    public void delete(Long id) {
        //检验删除
        validateDelete(id);

        LambdaQueryWrapper<StaffCommissionStudent> lambdaQuery = Wrappers.lambdaQuery(StaffCommissionStudent.class);
        lambdaQuery.eq(StaffCommissionStudent::getFkStudentId,id);
        boolean b = remove(lambdaQuery);
        if (!b){
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }

    /**
     * Author Cream
     * Description : //合并是否计算业绩关系数据
     * Date 2023/5/11 12:10
     * Params:
     * Return
     */
    @Override
    public void mergeData(Long mergedStudentId, Long targetStudentId) {
        StaffCommissionStudent mergedCommissionStudent = staffCommissionStudentMapper.selectOne(Wrappers.<StaffCommissionStudent>lambdaQuery().eq(StaffCommissionStudent::getFkStudentId, mergedStudentId));
        StaffCommissionStudent targetCommissionStudent = staffCommissionStudentMapper.selectOne(Wrappers.<StaffCommissionStudent>lambdaQuery().eq(StaffCommissionStudent::getFkStudentId, targetStudentId));
        if (GeneralTool.isNotEmpty(mergedCommissionStudent) && GeneralTool.isNotEmpty(targetCommissionStudent)) {
            Integer s1 = mergedCommissionStudent.getStatus();
            Integer s2 = targetCommissionStudent.getStatus();
            int minStatus = Math.min(s1,s2);
            targetCommissionStudent.setStatus(minStatus);
            utilService.setUpdateInfo(targetCommissionStudent);
            staffCommissionStudentMapper.deleteById(mergedCommissionStudent.getId());
            staffCommissionStudentMapper.updateById(targetCommissionStudent);
        }
        if (GeneralTool.isNotEmpty(mergedCommissionStudent) && GeneralTool.isNull(targetCommissionStudent)) {
            mergedCommissionStudent.setFkStudentId(targetStudentId);
            utilService.setUpdateInfo(mergedCommissionStudent);
            staffCommissionStudentMapper.updateById(mergedCommissionStudent);
        }
    }

    /**
     * 检验删除
     * @param id
     */
    private void validateDelete(Long id) {
        LambdaQueryWrapper<StaffCommissionStudent> lambdaQuery = Wrappers.lambdaQuery(StaffCommissionStudent.class);
        lambdaQuery.eq(StaffCommissionStudent::getFkStudentId,id);
        List<StaffCommissionStudent> staffCommissionStudents = list(lambdaQuery);
        if (GeneralTool.isEmpty(staffCommissionStudents)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
        List<StaffCommissionAction> staffCommissionActions = staffCommissionActionService.list(Wrappers.<StaffCommissionAction>lambdaQuery()
                .eq(StaffCommissionAction::getFkStudentId, id));
        if (GeneralTool.isNotEmpty(staffCommissionActions)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("has_associated"));
        }
    }
}

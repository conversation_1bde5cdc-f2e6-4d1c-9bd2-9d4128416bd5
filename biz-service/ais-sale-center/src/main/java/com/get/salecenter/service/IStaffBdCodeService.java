package com.get.salecenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.salecenter.vo.BdSelectVo;
import com.get.salecenter.vo.StaffBdCodeVo;
import com.get.salecenter.entity.StaffBdCode;
import com.get.salecenter.dto.StaffBdCodeDto;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/11/17
 * @TIME: 11:15
 * @Description:
 **/
public interface IStaffBdCodeService extends IService<StaffBdCode> {

    /**
     * 详情
     *
     * @param id
     * @return
     */
    StaffBdCodeVo findStaffBdCodeById(Long id);

    /**
     * 批量新增
     *
     * @param staffBdCodeDtos
     */
    void batchAdd(List<StaffBdCodeDto> staffBdCodeDtos);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 修改
     *
     * @param staffBdCodeDto
     * @return
     */
    StaffBdCodeVo updateStaffBdCode(StaffBdCodeDto staffBdCodeDto);

    /**
     * 列表
     *
     * @param staffBdCodeDto
     * @param page
     * @return
     */
    List<StaffBdCodeVo> getStaffBdCodes(StaffBdCodeDto staffBdCodeDto, Page page);


    /**
     * @return java.lang.String
     * @Description: 根据id获取bd
     * @Param [staffId]
     * <AUTHOR>
     */
    String getBDbyStaffId(Long staffId);

    /**
     * @Description: 根据ids获取bd
     * @Author: Jerry
     * @Date:10:05 2021/8/12
     */
    Map<Long, StaffBdCode> getBDbyStaffIds(Set<Long> staffIds);

    /**
     * 根据代理ids获取bd
     * <AUTHOR>
     * @DateTime 2023/1/11 14:38
     */
    List<StaffBdCodeVo> getBDbyAgentIds(Set<Long> fkAgentIds);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :
     * @Param [staffIds]
     * <AUTHOR>
     */
    Map<Long, String> getCompanyNamesByStaffIds(Set<Long> staffIds);

    /**
     * 根据公司id获取bd员工下拉框数据
     *
     * @Date 12:55 2021/8/20
     * <AUTHOR>
     */
    List<BaseSelectEntity> getStaffByBdCompanyIds(Long companyId);

    /**
     * @Description: 获取BD团队配置IAE下的所有大区ids
     * @Author: Jerry
     * @Date:12:15 2021/9/1
     */
    Set<Long> getAreaRegionIdsByCompanyId();

    /**
     * 根据BdCode获取员工ID
     *
     * @param bdCode
     * @return
     * @
     */
    Long getStaffIdByBdCode(String bdCode);

    Long getAreaRegionIdIdByBdCode(String bdCode);
    /**
     * 查询BD下拉框（百度式搜索）
     * @param companyIds
     * @param bdName
     * @return
     */
    List<BaseSelectEntity> getStaffByBdName(List<Long> companyIds, String bdName);

    /**
     * 根据大区获取BD对应员工IDs
     * @param fkAreaRegionId
     * @return
     */
    List<Long> getStaffIdsByFkAreaRegionId(Long fkAreaRegionId);

    /**
     * 代理申请表单获取bd
     * @param companyId
     * @return
     */
    List<BdSelectVo> getBdSelectByCompanyId(Long companyId);

    /**
     * 代理申请表单获取bd
     * @param companyId
     * @return
     */
    List<BdSelectVo> getBdSelectAgentOnlineForm(Long companyId);

    /**
     * 根据bdCode获取大区名称
     */
    Map<String,String> getAreaRegionBybdCode(Set<String> bdCodes);

    List<BaseSelectEntity> getStaff(List<Long> companyIds);
}

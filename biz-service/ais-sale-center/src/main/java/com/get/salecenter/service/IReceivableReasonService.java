package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.salecenter.vo.ReceivableReasonVo;
import com.get.salecenter.dto.ReceivableReasonDto;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/11/2
 * @TIME: 12:55
 * @Description:
 **/
public interface IReceivableReasonService {
    /**
     * 详情
     *
     * @param id
     * @return
     */
    ReceivableReasonVo findReceivableReasonById(Long id);

    /**
     * 列表数据
     *
     * @param receivableReasonDto
     * @param page
     * @return
     */
    List<ReceivableReasonVo> getReceivableReasons(ReceivableReasonDto receivableReasonDto, Page page);

    /**
     * 修改
     *
     * @param receivableReasonDto
     * @return
     */
    ReceivableReasonVo updateReceivableReason(ReceivableReasonDto receivableReasonDto);

    /**
     * 保存
     *
     * @param receivableReasonDto
     * @return
     */
    Long addReceivableReason(ReceivableReasonDto receivableReasonDto);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 批量保存
     *
     * @param receivableReasonDto
     * @return
     */
    void batchAdd(List<ReceivableReasonDto> receivableReasonDto);

    /**
     * 上移下移
     *
     * @param receivableReasonDto
     * @return
     */
    void movingOrder(List<ReceivableReasonDto> receivableReasonDto);

    /**
     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 下拉
     * @Param []
     * <AUTHOR>
     */
    List<BaseSelectEntity> getReasonSelect();

    /**
     * @param id
     * @return java.lang.String
     * @Description: 根据id查询名称
     * @Param [id]
     * <AUTHOR>
     */
    String getReasonNameById(Integer id);


    /**
     * 根据ids查询名称
     *
     * @param ids
     * @return
     */
    Map<Long, String> getReasonNameByIds(Set<Integer> ids);
}

package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.salecenter.dto.AgentContractApprovalDto;
import com.get.salecenter.service.IAgentContractApprovalService;
import com.get.salecenter.vo.AgentContractApprovalVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 合同审批 控制器
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/sale/agentContractApproval")
@Api(tags = "合同审批接口")
public class AgentContractApprovalController {

    private final IAgentContractApprovalService agentContractApprovalService;

    /**
     * 保存合同审批意见
     *
     * @param agentContractApprovalDto 合同审批DTO
     * @return 审批记录ID
     */
    @ApiOperation(value = "保存合同审批意见", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生代理合同管理/保存合同审批意见")
    @PostMapping("add")
    public ResponseBo addAgentContractApproval(
            @RequestBody @Validated(AgentContractApprovalDto.Add.class) AgentContractApprovalDto agentContractApprovalDto) {
        return SaveResponseBo.ok(agentContractApprovalService.addAgentContractApproval(agentContractApprovalDto));
    }

    /**
     * 保存合同审批意见并发送邮件
     *
     * @param agentContractApprovalDto 合同审批DTO
     * @return 审批记录ID
     */
    @ApiOperation(value = "保存合同审批意见并发送邮件", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生代理合同管理/保存合同审批意见并发送邮件")
    @PostMapping("saveAndSendEmail")
    public ResponseBo saveAndSendEmail(
            @RequestBody @Validated(AgentContractApprovalDto.Add.class) AgentContractApprovalDto agentContractApprovalDto) {
        Long approvalId = agentContractApprovalService.saveAndSendEmail(agentContractApprovalDto);
        return SaveResponseBo.ok(approvalId);
    }

    /**
     * 发送合同审批邮件
     *
     * @param id 审批记录ID
     * @return 响应结果
     */
    @ApiOperation(value = "发送合同审批邮件", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生代理合同管理/发送合同审批邮件")
    @PostMapping("sendEmail")
    public ResponseBo sendEmail(@RequestParam("id") Long id) {
        agentContractApprovalService.sendEmail(id);
        return ResponseBo.ok();
    }

    /**
     * 合同审批列表查询
     *
     * @param page 分页查询参数
     * @return 审批列表
     */
    @ApiOperation(value = "合同审批列表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生代理合同管理/合同审批列表")
    @PostMapping("datas")
    public ResponseBo<AgentContractApprovalVo> datas(@RequestBody SearchBean<AgentContractApprovalDto> page) {
        List<AgentContractApprovalVo> datas = agentContractApprovalService.getAgentContractApprovals(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

}
package com.get.salecenter.utils.sale;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.SecureRandom;
import java.util.Arrays;
import java.util.Base64;
import java.util.UUID;
import javax.crypto.Cipher;
import javax.crypto.Mac;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import org.springframework.stereotype.Component;

/**
 * AES加解密工具类
 */
@Component
public class SecureEncryptUtil {

    private static final String AES_ALGORITHM = "AES/CBC/PKCS5Padding";
    private static final String HMAC_ALGO = "HmacSHA256";

    /**
     * 加密
     *
     * @param originalData 原始数据
     * @param secretKey    任意长度密钥（将做 SHA-256 截断）
     */
    public static String encrypt(String originalData, String secretKey) throws Exception {
        byte[] keyBytes = normalizeAesKey(secretKey);

        // 生成随机 IV
        byte[] iv = new byte[16];
        new SecureRandom().nextBytes(iv);

        // 构造加密内容
        String randomStr = UUID.randomUUID().toString().replace("-", "").substring(0, 8);
        long timestamp = System.currentTimeMillis();
        String content = originalData + "|" + timestamp + "|" + randomStr;

        // AES 加密
        byte[] encrypted = aesEncrypt(content.getBytes(StandardCharsets.UTF_8), keyBytes, iv);

        // HMAC 签名
        byte[] hmac = generateHmac(encrypted, keyBytes);

        // 拼接 iv + hmac + encrypted 并 Base64 编码
        ByteBuffer buffer = ByteBuffer.allocate(iv.length + hmac.length + encrypted.length);
        buffer.put(iv);
        buffer.put(hmac);
        buffer.put(encrypted);

        return Base64.getEncoder().encodeToString(buffer.array());
    }

    /**
     * 解密
     *
     * @param cipherText 密文字符串
     * @param secretKey  原始密钥（任意长度）
     */
    public static String decrypt(String cipherText, String secretKey) throws Exception {
        byte[] fullData = Base64.getDecoder().decode(cipherText);
        byte[] iv = Arrays.copyOfRange(fullData, 0, 16);
        byte[] hmac = Arrays.copyOfRange(fullData, 16, 48);
        byte[] encrypted = Arrays.copyOfRange(fullData, 48, fullData.length);

        byte[] keyBytes = normalizeAesKey(secretKey);

        // 校验 HMAC 签名
        byte[] calculatedHmac = generateHmac(encrypted, keyBytes);
        if (!MessageDigest.isEqual(hmac, calculatedHmac)) {
            throw new SecurityException("数据完整性校验失败：HMAC 不一致");
        }

        byte[] decrypted = aesDecrypt(encrypted, keyBytes, iv);
        String result = new String(decrypted, StandardCharsets.UTF_8);

        // 取加密前的原始数据
        return result.split("\\|")[0];
    }

    // AES 加密
    private static byte[] aesEncrypt(byte[] data, byte[] key, byte[] iv) throws Exception {
        Cipher cipher = Cipher.getInstance(AES_ALGORITHM);
        SecretKeySpec secretKey = new SecretKeySpec(key, "AES");
        cipher.init(Cipher.ENCRYPT_MODE, secretKey, new IvParameterSpec(iv));
        return cipher.doFinal(data);
    }

    // AES 解密
    private static byte[] aesDecrypt(byte[] data, byte[] key, byte[] iv) throws Exception {
        Cipher cipher = Cipher.getInstance(AES_ALGORITHM);
        SecretKeySpec secretKey = new SecretKeySpec(key, "AES");
        cipher.init(Cipher.DECRYPT_MODE, secretKey, new IvParameterSpec(iv));
        return cipher.doFinal(data);
    }

    // HMAC-SHA256
    private static byte[] generateHmac(byte[] data, byte[] key) throws Exception {
        SecretKeySpec hmacKey = new SecretKeySpec(key, HMAC_ALGO);
        Mac mac = Mac.getInstance(HMAC_ALGO);
        mac.init(hmacKey);
        return mac.doFinal(data);
    }

    // 将任意长度的密钥标准化为 16 字节 AES 密钥（通过 SHA-256 截取）
    private static byte[] normalizeAesKey(String secretKey) throws Exception {
        MessageDigest sha256 = MessageDigest.getInstance("SHA-256");
        byte[] hash = sha256.digest(secretKey.getBytes(StandardCharsets.UTF_8));
        // 使用前 16 字节作为 AES-128 密钥
        return Arrays.copyOf(hash, 16);
    }

    /**
     * 随机生成16位信用卡加密密钥
     * @return
     */
    public static String getCardSecretKey() {
        return UUID.randomUUID()
                .toString()
                .replaceAll("-", "")
                .toUpperCase()
                .substring(0, 16);
    }
}
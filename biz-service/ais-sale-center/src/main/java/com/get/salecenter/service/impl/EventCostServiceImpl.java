package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.feign.IFinanceCenterClient;
import com.get.financecenter.vo.ReceiptFormVo;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.salecenter.dao.sale.EventBillMapper;
import com.get.salecenter.dao.sale.EventBillReceivablePlanMapper;
import com.get.salecenter.dao.sale.EventCostMapper;
import com.get.salecenter.dao.sale.ReceivablePlanMapper;
import com.get.salecenter.dto.ConventionRegistrationDto;
import com.get.salecenter.dto.ConventionSponsorDto;
import com.get.salecenter.dto.EventCostDto;
import com.get.salecenter.entity.*;
import com.get.salecenter.service.IConventionRegistrationService;
import com.get.salecenter.service.IConventionSponsorService;
import com.get.salecenter.service.IEventCostService;
import com.get.salecenter.service.IEventIncentiveCostService;
import com.get.salecenter.vo.EventBillVo;
import com.get.salecenter.vo.EventCostVo;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: Sea
 * @create: 2020/12/9 17:59
 * @verison: 1.0
 * @description:
 */
@Service
public class EventCostServiceImpl extends ServiceImpl<EventCostMapper, EventCost> implements IEventCostService {
    @Resource
    private EventCostMapper eventCostMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IFinanceCenterClient financeCenterClient;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    private ReceivablePlanMapper receivablePlanMapper;
    @Resource
    private EventBillMapper eventBillMapper;
    @Resource
    private IConventionRegistrationService conventionRegistrationService;
    @Resource
    private IConventionSponsorService conventionSponsorService;
    @Resource
    private IEventIncentiveCostService eventIncentiveCostService;
    @Resource
    private EventBillReceivablePlanMapper eventBillReceivablePlanMapper;


    @Override
    public EventCostVo findEventCostById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        EventCost eventCost = eventCostMapper.selectById(id);
        if (GeneralTool.isEmpty(eventCost)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
//        return BeanCopyUtils.objClone(eventCost, EventCostVo::new);
        EventCostVo eventCostVo = BeanCopyUtils.objClone(eventCost, EventCostVo::new);
//        if (GeneralTool.isNotEmpty(eventCostVo.getFkReceiptFormId())){
//            ReceiptFormVo receiptFormDto = feignFinanceService.getReceiptFormByFormId(eventCostVo.getFkReceiptFormId());
//            BigDecimal rate = feignFinanceService.getLastExchangeRate(false, eventCostVo.getFkCurrencyTypeNum(), receiptFormDto.getFkCurrencyTypeNum());
//
//            eventCostVo.setEquivalentRate(rate);
//            eventCostVo.setEquivalentAmount();
//            receiptFormDto.getFkCurrencyTypeNum()
//        }
        if (GeneralTool.isNotEmpty(eventCostVo.getFkReceiptFormId())) {
            ReceiptFormVo receiptFormVo = financeCenterClient.getReceiptFormByFormId(eventCostVo.getFkReceiptFormId());
            List<EventCost> eventCosts = eventCostMapper.selectList(Wrappers.<EventCost>lambdaQuery().eq(EventCost::getFkReceiptFormId, eventCostVo.getFkReceiptFormId()));
            BigDecimal subtractResult = receiptFormVo.getAmount();
            if (GeneralTool.isNotEmpty(eventCosts)) {
                for (EventCost cost : eventCosts) {
                    if (GeneralTool.isEmpty(cost.getAmountReceivable())) {
                        cost.setAmountReceivable(BigDecimal.ZERO);
                    }
                    subtractResult = subtractResult.subtract(cost.getAmountReceivable());
                }
            }
            eventCostVo.setReceiptFormBalance(subtractResult.setScale(2, BigDecimal.ROUND_HALF_UP));
        }

        ConventionRegistrationDto conventionRegistrationDto = new ConventionRegistrationDto();
        conventionRegistrationDto.setFkEventCostId(id);
        List<ConventionRegistration> conventionRegistrations = conventionRegistrationService.getConventionRegistrationsByVo(conventionRegistrationDto);
        if (GeneralTool.isNotEmpty(conventionRegistrations)) {
            eventCostVo.setFkConventionRegistrationId(conventionRegistrations.get(0).getId());
        }

        ConventionSponsorDto conventionSponsorDto = new ConventionSponsorDto();
        conventionSponsorDto.setFkEventCostId(id);
        List<ConventionSponsor> conventionSponsors = conventionSponsorService.getConventionSponsorsByVo(conventionSponsorDto);
        if (GeneralTool.isNotEmpty(conventionSponsors)) {
            eventCostVo.setFkConventionSponsorId(conventionSponsors.get(0).getId());
        }

        return eventCostVo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long addEventCost(EventCostDto eventCostDto) {
        if (GeneralTool.isEmpty(eventCostDto.getFkEventId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("event_not_bind"));
        }
        if (GeneralTool.isEmpty(eventCostDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        if (GeneralTool.isNotEmpty(eventCostDto.getFkReceiptFormId())) {
            validateReceiptFormBalance(eventCostDto);
        }
        EventCost eventCost = BeanCopyUtils.objClone(eventCostDto, EventCost::new);
        utilService.updateUserInfoToEntity(eventCost);
        int i = eventCostMapper.insert(eventCost);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
        if (GeneralTool.isNotEmpty(eventCostDto.getFkConventionRegistrationId())) {
            ConventionRegistration conventionRegistration = conventionRegistrationService.getConventionRegistrationById(eventCostDto.getFkConventionRegistrationId());
            if (GeneralTool.isNotEmpty(conventionRegistration)) {
                conventionRegistration.setFkEventCostId(eventCost.getId());
                utilService.setUpdateInfo(conventionRegistration);
                conventionRegistrationService.updateConventionRegistrationById(conventionRegistration);
            }
        }
        if (GeneralTool.isNotEmpty(eventCostDto.getFkConventionSponsorId())) {
            ConventionSponsor conventionSponsor = conventionSponsorService.getConventionSponsorById(eventCostDto.getFkConventionSponsorId());
            if (GeneralTool.isNotEmpty(conventionSponsor)) {
                conventionSponsor.setFkEventCostId(eventCost.getId());
                utilService.setUpdateInfo(conventionSponsor);
                conventionSponsorService.updateConventionSponsorById(conventionSponsor);
            }
        }
        return eventCost.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (eventCostMapper.selectById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        int i = eventCostMapper.deleteById(id);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }

        ConventionRegistrationDto conventionRegistrationDto = new ConventionRegistrationDto();
        conventionRegistrationDto.setFkEventCostId(id);
        List<ConventionRegistration> conventionRegistrations = conventionRegistrationService.getConventionRegistrationsByVo(conventionRegistrationDto);
        if (GeneralTool.isNotEmpty(conventionRegistrations)) {
            for (ConventionRegistration conventionRegistration : conventionRegistrations) {
                conventionRegistration.setFkEventCostId(null);
                utilService.setUpdateInfo(conventionRegistration);
                conventionRegistrationService.updateWithNullConventionRegistrationById(conventionRegistration);
            }
        }

        ConventionSponsorDto conventionSponsorDto = new ConventionSponsorDto();
        conventionSponsorDto.setFkEventCostId(id);
        List<ConventionSponsor> conventionSponsors = conventionSponsorService.getConventionSponsorsByVo(conventionSponsorDto);
        if (GeneralTool.isNotEmpty(conventionSponsors)) {
            for (ConventionSponsor conventionSponsor : conventionSponsors) {
                conventionSponsor.setFkEventCostId(null);
                utilService.setUpdateInfo(conventionSponsor);
                conventionSponsorService.updateWithNullConventionSponsorById(conventionSponsor);
            }
        }

    }

    @Override
    public EventCostVo updateEventCost(EventCostDto eventCostDto) {
        if (eventCostDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        EventCost result = eventCostMapper.selectById(eventCostDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        if (GeneralTool.isNotEmpty(eventCostDto.getFkReceiptFormId())) {
            validateReceiptFormBalance(eventCostDto);
        }
        //TODO 改过
        //EventCostVo eventCostVo = BeanCopyUtils.objClone(eventCostDto, EventCostVo::new);
        EventCost eventCostVo = BeanCopyUtils.objClone(eventCostDto, EventCost::new);
        utilService.updateUserInfoToEntity(eventCostVo);
        int i = eventCostMapper.updateById(eventCostVo);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
        return findEventCostById(eventCostDto.getId());
    }

    private void validateReceiptFormBalance(EventCostDto eventCostDto) {
        //获取总金额
        ReceiptFormVo receiptFormVo = financeCenterClient.getReceiptFormByFormId(eventCostDto.getFkReceiptFormId());
        BigDecimal receiptAmount = receiptFormVo.getAmount();

        LambdaQueryWrapper<EventCost> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(EventCost::getFkReceiptFormId, eventCostDto.getFkReceiptFormId());
        if (GeneralTool.isNotEmpty(eventCostDto.getId())) {
            lambdaQueryWrapper.ne(EventCost::getId, eventCostDto.getId());
        }
        List<EventCost> eventCosts = eventCostMapper.selectList(lambdaQueryWrapper);

        BigDecimal subtractResult = receiptAmount;
        if (GeneralTool.isNotEmpty(eventCosts)) {
            for (EventCost eventCost : eventCosts) {
                if (GeneralTool.isEmpty(eventCost.getAmountReceivable())) {
                    eventCost.setAmountReceivable(BigDecimal.ZERO);
                }
                subtractResult = subtractResult.subtract(eventCost.getAmountReceivable());
            }
        }
        subtractResult = subtractResult.subtract(eventCostDto.getAmountReceivable());
        //小于0余额不足
        if (subtractResult.compareTo(BigDecimal.ZERO) < 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("receipt_form_balance_not_enough"));
        }
    }

    @Override
    public List<EventCostVo> getEventCosts(EventCostDto eventCostDto, Page page) {
        LambdaQueryWrapper<EventCost> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(eventCostDto)) {
            if (GeneralTool.isNotEmpty(eventCostDto.getFkEventId())) {
//                criteria.andEqualTo("fkEventId",eventCostDto.getFkEventId());
                lambdaQueryWrapper.eq(EventCost::getFkEventId, eventCostDto.getFkEventId());
            } else {
                throw new GetServiceException(LocaleMessageUtils.getMessage("event_id_null"));
            }
        }
        IPage<EventCost> pages = eventCostMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
        List<EventCost> eventCosts = pages.getRecords();
        page.setAll((int) pages.getTotal());

        List<EventCostVo> convertDatas = new ArrayList<>();
        //学校提供商id集合
        Set<Long> institutionProviderIds = new HashSet<>();
        //币种编号集合
        Set<String> currencyTypeNums = new HashSet<>();
        //获取各自集合的值
        for (EventCost eventCost : eventCosts) {
            institutionProviderIds.add(eventCost.getFkInstitutionProviderId());
            currencyTypeNums.add(eventCost.getFkCurrencyTypeNum());
            currencyTypeNums.add("CNY");
        }

        Map<Long, String> institutionProviderNameMap = getInstitutionProviderNameMap(institutionProviderIds);
        Map<String, String> currencyNameMap = getCurrencyNameMap(currencyTypeNums);

        //发票对应收款单的收款情况  eventcost-》eventBill-》invoice-》receiptForm-》item
        Set<Long> eventCostIds = eventCosts.stream().map(EventCost::getId).collect(Collectors.toSet());
        List<EventCostVo> eventCostVoList = eventCostMapper.getReceiptFormInfo(eventCostIds);
        Map<Long, String> infoMap = eventCostVoList.stream().filter(e -> GeneralTool.isNotEmpty(e.getReceiptFormInfo())).collect(Collectors.toMap(EventCostVo::getId, EventCostVo::getReceiptFormInfo));


        for (EventCost eventCost : eventCosts) {
            EventCostVo eventCostVo = BeanCopyUtils.objClone(eventCost, EventCostVo::new);
            //feign调用返回的map 根据key-id获取对应value-名称 设置返回给前端
            eventCostVo.setInstitutionProviderName(institutionProviderNameMap.get(eventCostVo.getFkInstitutionProviderId()));
            eventCostVo.setCurrencyTypeName(currencyNameMap.get(eventCostVo.getFkCurrencyTypeNum()));
            eventCostVo.setAmountRmbCurrencyTypeName(currencyNameMap.get("CNY"));
            if (GeneralTool.isNotEmpty(infoMap) && GeneralTool.isNotEmpty(infoMap.get(eventCost.getId()))) {
                eventCostVo.setReceiptFormInfo(infoMap.get(eventCost.getId()));
            }

            convertDatas.add(eventCostVo);
        }
        return convertDatas;
    }

    /**
     * 活动费用归口收款单下拉框
     *
     * @Date 12:45 2021/12/3
     * <AUTHOR>
     */
    @Override
    public List<EventBillVo> getReceiptSelect(Long institutionProviderId, Long companyId, Long eventCostId, Long conventionRegistrationId, Long conventionSponsorId, String eventYear) {
//        List<com.get.financecenter.vo.ReceiptFormVo> receiptFormDtoListResult = financeCenterClient.getReceiptFormByInstitutionProviderId(institutionProviderId, companyId, eventCostId);

        if (GeneralTool.isNotEmpty(conventionRegistrationId)) {
            ConventionRegistration conventionRegistration = conventionRegistrationService.getConventionRegistrationById(conventionRegistrationId);
            if (GeneralTool.isNotEmpty(conventionRegistration.getFkEventCostId())) {
                eventCostId = conventionRegistration.getFkEventCostId();
            }
        }
        if (GeneralTool.isNotEmpty(conventionSponsorId)) {
            ConventionSponsor conventionSponsor = conventionSponsorService.getConventionSponsorById(conventionSponsorId);
            if (GeneralTool.isNotEmpty(conventionSponsor.getFkEventCostId())) {
                eventCostId = conventionSponsor.getFkEventCostId();
            }
        }

        List<EventBillVo> eventBillVos = eventBillMapper.getEventBillByInstitutionProviderId(institutionProviderId, companyId, eventCostId, eventYear);
        if (GeneralTool.isEmpty(eventBillVos)) {
            return Collections.emptyList();
        }

        Set<Long> eventBillIds = eventBillVos.stream().map(EventBillVo::getId).collect(Collectors.toSet());
        Map<Long, List<EventBillReceivablePlan>> eventBillReceivablePlansMap = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(eventBillIds)) {
            List<EventBillReceivablePlan> eventBillReceivablePlans = eventBillReceivablePlanMapper.selectList(Wrappers.lambdaQuery(EventBillReceivablePlan.class)
                    .in(EventBillReceivablePlan::getFkEventBillId, eventBillIds));
            if (GeneralTool.isNotEmpty(eventBillReceivablePlans)) {
                eventBillReceivablePlansMap = eventBillReceivablePlans.stream().collect(Collectors.groupingBy(EventBillReceivablePlan::getFkEventBillId));
            }
        }
        for (EventBillVo eventBillVo : eventBillVos) {
            List<EventBillReceivablePlan> eventBillReceivablePlans = eventBillReceivablePlansMap.get(eventBillVo.getId());
            if (GeneralTool.isEmpty(eventBillReceivablePlans)) {
                //应收计划id为空就是没创建财务单据的
                eventBillVo.setSelectName("【未创建财务单据】" + eventBillVo.getSelectName());
            }
        }

//        return eventBillVos;

//        List<ReceiptFormVo> receiptFormDtoList = BeanCopyUtils.copyListProperties(receiptFormDtoListResult, ReceiptFormVo::new);
////        Example example = new Example(EventCost.class);
////        Example.Criteria criteria = example.createCriteria().andEqualTo("fkReceiptFormId");
////        if (GeneralTool.isNotEmpty(eventCostId)) {
////            criteria.andNotEqualTo("id", eventCostId);
////        }
////        List<EventCost> eventCosts = eventCostMapper.selectByExample(example);
//
//        Set<String> currencyTypeNumSet = receiptFormDtoList.stream().map(ReceiptFormVo::getFkCurrencyTypeNum).collect(Collectors.toSet());
//        currencyTypeNumSet.removeIf(Objects::isNull);
//        Map<String, String> currencyNamesByNums = new HashMap<>();
//        Result<Map<String, String>> result = financeCenterClient.getCurrencyNamesByNums(currencyTypeNumSet);
//        if (result.isSuccess() && result.getData() != null) {
//            currencyNamesByNums = result.getData();
//        }
//
        Set<Long> ids = eventBillVos.stream().map(EventBillVo::getId).collect(Collectors.toSet());
        ids.removeIf(Objects::isNull);
        if (GeneralTool.isEmpty(ids)) {
            ids.add(0L);
        }
        LambdaQueryWrapper<EventCost> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(EventCost::getFkEventBillId, ids);
        if (GeneralTool.isNotEmpty(eventCostId)) {
            lambdaQueryWrapper.ne(EventCost::getId, eventCostId);
        }
        List<EventCost> eventCosts = eventCostMapper.selectList(lambdaQueryWrapper);
        Map<Long, List<EventCost>> eventCostsMap = eventCosts.stream().collect(Collectors.groupingBy(EventCost::getFkEventBillId));

        List<EventIncentiveCost> eventIncentiveCosts = eventIncentiveCostService.list(Wrappers.<EventIncentiveCost>lambdaQuery().in(EventIncentiveCost::getFkEventBillId, ids));
        Map<Long, List<EventIncentiveCost>> eventIncentiveCostsMap = eventIncentiveCosts.stream().collect(Collectors.groupingBy(EventIncentiveCost::getFkEventBillId));


//        Map<Long, List<ReceiptFormItemDto>> receiptFormItemMap = new HashMap<>();
//        List<ReceiptFormItemDto> formItemDtos = financeCenterClient.getReceiptFormItemsByFormIds(ids).getData();
////        List<ReceivablePlan> receivablePlans = receivablePlanMapper.selectList(Wrappers.<ReceivablePlan>lambdaQuery()
////                .eq(ReceivablePlan::getFkTypeTargetId, institutionProviderId)
////                .eq(ReceivablePlan::getFkTypeKey, TableEnum.INSTITUTION_PROVIDER.key));
//
//        if (GeneralTool.isNotEmpty(formItemDtos)){
//            receiptFormItemMap = formItemDtos.stream().collect(Collectors.groupingBy(ReceiptFormItemDto::getFkReceiptFormId));
//        }
//
        List<EventBillVo> resultList = new ArrayList<>();
        for (EventBillVo eventBillVo : eventBillVos) {

//        for (ReceiptFormVo receiptFormDto : receiptFormDtoList) {
////            Example example = new Example(EventCost.class);
////            Example.Criteria criteria = example.createCriteria().andEqualTo("fkReceiptFormId",receiptFormDto.getId());
////            if (GeneralTool.isNotEmpty(eventCostId)) {
////                criteria.andNotEqualTo("id", eventCostId);
////            }
////            List<EventCost> eventCosts = eventCostMapper.selectByExample(example);
//            StringBuilder selectName = new StringBuilder();
////            if (GeneralTool.isEmpty(receivablePlans)){
////                selectName.append("【未绑定应收】");
////            }
//            if (GeneralTool.isEmpty(receiptFormItemMap)||GeneralTool.isEmpty(receiptFormItemMap.get(receiptFormDto.getId()))){
//                selectName.append("【未绑定应收】");
//            }
//
//            selectName.append(receiptFormDto.getAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString());
//            if (GeneralTool.isNotEmpty(currencyNamesByNums)) {
//                if (GeneralTool.isNotEmpty(currencyNamesByNums.get(receiptFormDto.getFkCurrencyTypeNum()))) {
//                    selectName.append(currencyNamesByNums.get(receiptFormDto.getFkCurrencyTypeNum()));
//                }
//            }
//            if (GeneralTool.isNotEmpty(receiptFormDto.getSummary())) {
//                selectName.append("，").append(receiptFormDto.getSummary());
//            }
//            receiptFormDto.setSelectName(selectName.toString());
            List<EventCost> eventCostList = eventCostsMap.get(eventBillVo.getId());
            List<EventIncentiveCost> eventIncentiveCostList = eventIncentiveCostsMap.get(eventBillVo.getId());
            BigDecimal subtractResult = eventBillVo.getEventAmount();

            if (GeneralTool.isNotEmpty(eventCostList)) {
                for (EventCost eventCost : eventCostList) {
                    if (GeneralTool.isEmpty(eventCost.getAmountReceivable())) {
                        eventCost.setAmountReceivable(BigDecimal.ZERO);
                    }
                    subtractResult = subtractResult.subtract(eventCost.getAmountReceivable());
                }
            }

            if (GeneralTool.isNotEmpty(eventIncentiveCostList)) {
                for (EventIncentiveCost eventIncentiveCost : eventIncentiveCostList) {
                    if (GeneralTool.isEmpty(eventIncentiveCost.getAmountReceivable())) {
                        eventIncentiveCost.setAmountReceivable(BigDecimal.ZERO);
                    }
                    subtractResult = subtractResult.subtract(eventIncentiveCost.getAmountReceivable());
                }
            }

            eventBillVo.setEventBillBalance(subtractResult);
            //余额大于等于0
            if (subtractResult.compareTo(BigDecimal.ZERO) > 0) {
                resultList.add(eventBillVo);
            }
        }
//            receiptFormDto.setFkCurrencyTypeName(currencyNamesByNums.get(receiptFormDto.getFkCurrencyTypeNum()));
//            //余额大于等于0
//            if (subtractResult.compareTo(BigDecimal.ZERO) > 0) {
//                resultList.add(receiptFormDto);
//            }
//        }
        //编辑的全部显示
        if (GeneralTool.isNotEmpty(eventCostId)) {
            return eventBillVos;
        }
//
////        Set<Long> ids = receiptFormDtoList.stream().map(ReceiptFormVo::getId).collect(Collectors.toSet());
////        ids.removeIf(Objects::isNull);
////        if (GeneralTool.isEmpty(ids)) {
////            ids.add(0L);
////        }
////        eventCostId = null;
////        List<ReceiptFormVo> receiptFormDtos = eventCostMapper.getReceiptFormBalances(ids, eventCostId);
////        Map<Long, BigDecimal> receiptFormBalanceMap = receiptFormDtos.stream().collect(Collectors.toMap(ReceiptFormVo::getId, ReceiptFormVo::getReceiptFormBalance));
////        Set<String> currencyTypeNumSet = receiptFormDtoList.stream().map(ReceiptFormVo::getFkCurrencyTypeNum).collect(Collectors.toSet());
////        currencyTypeNumSet.removeIf(Objects::isNull);
////        Map<String, String> currencyNamesByNums = feignFinanceService.getCurrencyNamesByNums(currencyTypeNumSet);
////        for (ReceiptFormVo receiptFormDto : receiptFormDtoList) {
////            receiptFormDto.setReceiptFormBalance(receiptFormBalanceMap.get(receiptFormDto.getId()));
////            receiptFormDto.setFkCurrencyTypeName(currencyNamesByNums.get(receiptFormDto.getFkCurrencyTypeNum()));
////        }
        return resultList;
    }

    /**
     * 根据receiptFormIds获取活动费用归口
     *
     * @param receiptFormIds
     * @return
     */
    @Override
    public Map<Long, List<EventCostVo>> getEventCostDtoByReceiptFormIds(Set<Long> receiptFormIds) {
        if (GeneralTool.isEmpty(receiptFormIds)) {
            receiptFormIds.add(0L);
        }
        List<EventCost> eventCosts = eventCostMapper.selectList(Wrappers.<EventCost>lambdaQuery().in(EventCost::getFkReceiptFormId, receiptFormIds));
        if (GeneralTool.isEmpty(eventCosts)) {
            return null;
        }
        List<EventCostVo> eventCostVos = BeanCopyUtils.copyListProperties(eventCosts, EventCostVo::new);
        return eventCostVos.stream().collect(Collectors.groupingBy(EventCostVo::getFkReceiptFormId));
    }

    /**
     * 根据receiptFormIds获取活动费用归口Dtos
     *
     * @param receiptFormId
     * @return
     */
    @Override
    public List<EventCostVo> getEventCostDtoByReceiptFormId(Long receiptFormId) {
        List<EventCost> eventCosts = eventCostMapper.selectList(Wrappers.<EventCost>lambdaQuery().eq(EventCost::getFkReceiptFormId, receiptFormId));
        if (GeneralTool.isEmpty(eventCosts)) {
            return null;
        }
        return BeanCopyUtils.copyListProperties(eventCosts, EventCostVo::new);
    }

    @Override
    public BigDecimal getEventCostSubtotal(EventCostDto eventCostDto) {
        if (GeneralTool.isEmpty(eventCostDto.getFkEventId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("event_id_null"));
        }
        List<EventCost> eventCosts = eventCostMapper.selectList(Wrappers.<EventCost>lambdaQuery().eq(EventCost::getFkEventId, eventCostDto.getFkEventId()));
        if (GeneralTool.isEmpty(eventCosts)) {
            return BigDecimal.ZERO;
        }
        BigDecimal subtotal = BigDecimal.ZERO;
        for (EventCost eventCost : eventCosts) {
            subtotal = subtotal.add(GeneralTool.isNotEmpty(eventCost.getAmountRmb()) ? eventCost.getAmountRmb() : BigDecimal.ZERO);
        }
        return subtotal;
    }

    @Override
    public List<EventCost> getEventCostsByCondition(LambdaQueryWrapper<EventCost> lambdaQueryWrapper) {
        List<EventCost> eventCosts = eventCostMapper.selectList(lambdaQueryWrapper);
        return eventCosts;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(List<EventCostDto> eventCostDtos) {
        if (GeneralTool.isEmpty(eventCostDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        for (EventCostDto eventCostDto : eventCostDtos) {
            addEventCost(eventCostDto);
        }
    }

    @Override
    public EventCostVo getAllocatedActivityFees(EventCostDto eventCostDto) {
        if (GeneralTool.isEmpty(eventCostDto.getFkEventId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing") + "-eventId");
        }
        if (GeneralTool.isEmpty(eventCostDto.getFkCurrencyTypeNum())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing") + "-currencyTypeNum");
        }
        //返回参数
        EventCostVo eventCostVo = new EventCostVo();
        //获取所有已分配的活动费用
        LambdaQueryWrapper<EventCost> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(EventCost::getFkEventId, eventCostDto.getFkEventId());
        List<EventCost> eventCosts = eventCostMapper.selectList(lambdaQueryWrapper);
        if (GeneralTool.isEmpty(eventCosts)) {
            eventCostVo.setAmount(BigDecimal.ZERO);
            eventCostVo.setAmountRmbCurrencyTypeName(null);
            return eventCostVo;
        }
        //根据币种和汇率转换金额
        //币种编号集合
//        Map<String, BigDecimal> currencyTpeAndAmount = new HashMap<>();
        BigDecimal amountOfMoney = BigDecimal.ZERO;
        for (EventCost eventCost : eventCosts) {
//            currencyTpeAndAmount.put(eventCost.getFkCurrencyTypeNum(),eventCost.getAmount());
            if (eventCost.getFkCurrencyTypeNum() != null && eventCost.getAmount() != null) {
                BigDecimal money = ConvertAmount(eventCost.getFkCurrencyTypeNum(), eventCostDto.getFkCurrencyTypeNum(), eventCost.getAmount());
                amountOfMoney = amountOfMoney.add(money).setScale(4, RoundingMode.HALF_UP);
            }
        }
        //币种编号集合
        Set<String> currencyTypeNums = new HashSet<>();
        currencyTypeNums.add(eventCostDto.getFkCurrencyTypeNum());
        Map<String, String> currencyNameMap = getCurrencyNameMap(currencyTypeNums);
        //汇总金额和币种
        eventCostVo.setAmount(amountOfMoney.setScale(2, RoundingMode.HALF_UP));
        eventCostVo.setAmountRmbCurrencyTypeName(currencyNameMap.get(eventCostDto.getFkCurrencyTypeNum()));
        return eventCostVo;
    }

    @Override
    public List<String> getReceiptSelectYear(Long institutionProviderId, Long companyId, Long eventCostId, Long conventionRegistrationId, Long conventionSponsorId) {
        List<String> objects = eventBillMapper.getEventBillYearsByInstitutionProviderId(institutionProviderId, companyId, eventCostId);
        if (GeneralTool.isEmpty(objects)) {
            return Collections.emptyList();
        }
        return objects;

    }

    private final Map<String, BigDecimal> exchangeRateCache = new HashMap<>();

    private BigDecimal ConvertAmount(String currencyTypeNum, String currencyTypeNumTo, BigDecimal amount) {
        // 尝试从缓存中获取汇率
        Optional<BigDecimal> optionalExchangeRate = Optional.ofNullable(exchangeRateCache.get(currencyTypeNum + "_" + currencyTypeNumTo));
        BigDecimal money = BigDecimal.ZERO;
        String finalCurrencyTypeNumTo = currencyTypeNumTo;
        BigDecimal exchangeRate = optionalExchangeRate.orElseGet(() -> {
            // 如果缓存中没有找到汇率，则通过API获取并缓存
            Result<BigDecimal> result = financeCenterClient.getLastExchangeRate(false, currencyTypeNum, finalCurrencyTypeNumTo);
            BigDecimal rate = null;
            if (result.isSuccess() && result.getData() != null) {
                rate = result.getData();
                exchangeRateCache.put(currencyTypeNum + "_" + finalCurrencyTypeNumTo, rate); // 缓存汇率
            }
            return rate;
        });
        if (GeneralTool.isEmpty(exchangeRate)) {
            money = amount;
        } else {
            money = amount.multiply(exchangeRate);
        }

        //获取汇率后进行转换计算
        return money;

    }

    /**
     * @return java.util.Map<java.lang.Long, java.lang.String>
     * @Description :feign调用一次查出全部学校提供商id对应的名称
     * @Param [institutionProviderIds]
     * <AUTHOR>
     */
    private Map<Long, String> getInstitutionProviderNameMap(Set<Long> institutionProviderIds) {
        institutionProviderIds.removeIf(Objects::isNull);
        //feign调用一次查出全部学校提供商id对应的名称
//        return institutionCenterClient.getInstitutionProviderNamesByIds(institutionProviderIds);
        Result<Map<Long, String>> institutionProviderNameResult = institutionCenterClient.getInstitutionProviderNamesByIds(institutionProviderIds);
        if (institutionProviderNameResult.isSuccess() && GeneralTool.isNotEmpty(institutionProviderNameResult.getData())) {
            return institutionProviderNameResult.getData();
        }
        return Maps.newHashMap();
    }

    /**
     * @return java.util.Map<java.lang.String, java.lang.String>
     * @Description :feign调用一次查出全部对应币种名称
     * @Param [events]
     * <AUTHOR>
     */
    private Map<String, String> getCurrencyNameMap(Set<String> currencyTypeNums) {
        currencyTypeNums.removeIf(Objects::isNull);
        //feign调用一次查出全部对应币种名称
        Result<Map<String, String>> result = financeCenterClient.getCurrencyNamesByNums(currencyTypeNums);
        if (result.isSuccess() && result.getData() != null) {
            return result.getData();
        }
        return new HashMap<>();
    }
}

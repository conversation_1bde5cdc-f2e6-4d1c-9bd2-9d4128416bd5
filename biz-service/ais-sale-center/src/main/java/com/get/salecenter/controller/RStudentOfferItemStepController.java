package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.vo.MainCourseConditionVo;
import com.get.salecenter.vo.RStudentOfferItemStepVo;
import com.get.salecenter.vo.RStudentOfferItemStepSaveVo;
import com.get.salecenter.service.IRStudentOfferItemStepService;
import com.get.salecenter.dto.RStudentOfferItemStepUpRemarkDto;
import com.get.salecenter.dto.RStudentOfferItemStepDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.context.annotation.Lazy;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/11/18
 * @TIME: 17:25
 * @Description:
 **/

@Api(tags = "学生申请方案项目步骤记录管理")
@RestController
@RequestMapping("sale/studentOfferItemStepRelation")
public class RStudentOfferItemStepController {

    @Resource
    @Lazy
    private IRStudentOfferItemStepService rItemStepService;


    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.StudentOfferItemStepVo>
     * @Description :列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "stepName步骤名称")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生申请方案项目步骤记录管理/查询学生申请方案项目状态步骤")
    @PostMapping("datas")
    public ResponseBo<RStudentOfferItemStepVo> datas(@RequestBody SearchBean<RStudentOfferItemStepDto> page) {
        List<RStudentOfferItemStepVo> datas = rItemStepService.getReStudentStepLog(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 新增信息
     * @Param [StudentOfferVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生申请方案项目步骤记录管理/新增学生申请方案项目步骤记录")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(RStudentOfferItemStepDto.Add.class) RStudentOfferItemStepDto offerItemStepVo) {
        return SaveResponseBo.ok(rItemStepService.addReStudentOfferItemStep(offerItemStepVo));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description:保存记录申请步骤
     * @Param [offerItemStepVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "保存记录申请步骤", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生申请方案项目步骤记录管理/保存记录申请步骤")
    @PostMapping("save")
    public ResponseBo<RStudentOfferItemStepSaveVo> save(@RequestBody RStudentOfferItemStepDto offerItemStepVo) {
        return new ResponseBo(rItemStepService.saveReStudentOfferItemStep(offerItemStepVo));
    }

    @ApiOperation(value = "申请计划批量一键提交", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生申请方案项目步骤记录管理/申请计划批量一键提交")
    @PostMapping("batchOneClickSubmission")
    public ResponseBo batchOneClickSubmission( @RequestBody Set<Long> itemIds ) {
        rItemStepService.batchOneClickSubmission(itemIds);
        return ResponseBo.ok();
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.RStudentOfferItemStepVo>
     * @Description: 修改信息
     * @Param [itemStepVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生申请方案项目步骤记录管理/更新学生申请方案项目状态步骤")
    @PostMapping("update")
    public ResponseBo<RStudentOfferItemStepVo> update(@RequestBody @Validated(RStudentOfferItemStepDto.Update.class) RStudentOfferItemStepDto itemStepVo) {
        return UpdateResponseBo.ok(rItemStepService.updateReStudentOfferItemStep(itemStepVo));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :删除信息
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/学生申请方案项目步骤记录管理/删除学生申请方案项目状态步骤")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        rItemStepService.delete(id);
        return DeleteResponseBo.ok();
    }


    /**
     * @Description: 修改申请步骤日志备注
     * @Author: Jerry
     * @Date:11:37 2021/8/17
     */
    @ApiOperation(value = "修改申请步骤日志备注", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/学生申请方案项目步骤记录管理/修改申请步骤日志备注")
    @PostMapping("updateApplyStepLogRemark")
    public ResponseBo<RStudentOfferItemStepVo> updateApplyStepLogRemark(@RequestBody @Validated(RStudentOfferItemStepUpRemarkDto.Update.class)  RStudentOfferItemStepUpRemarkDto rStudentOfferItemStepUpRemarkDto) {
        return UpdateResponseBo.ok(rItemStepService.updateApplyStepLogRemark(rStudentOfferItemStepUpRemarkDto));
    }

    /**
     * 根据级别名称模糊查询课程ids
     *
     * @param
     * @return
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "根据时间状态糊查询申请计划ids", notes = "")
    @GetMapping("selectItemIdByStepIdAndGmtCreate")
    public List<Long> selectItemIdByStepIdAndGmtCreate(@RequestBody RStudentOfferItemStepDto rStudentOfferItemStepDto) {
        List<Long> datas = rItemStepService.selectItemIdByStepIdAndGmtCreate(rStudentOfferItemStepDto);
        return datas;
    }

    @ApiOperation(value = "删除申请步骤日志", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/学生申请方案项目步骤记录管理/删除申请步骤日志")
    @PostMapping("deleteStepLog/{id}")
    public ResponseBo deleteStepLog(@PathVariable("id") Long id) {
        rItemStepService.deleteStepLog(id);
        return DeleteResponseBo.ok();
    }

    @ApiOperation(value = "英国申请子课是否符合同步主课条件", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/学生申请方案项目步骤记录管理/英国申请子课是否符合同步主课条件")
    @PostMapping("isUkMainCourse/{id}")
    public ResponseBo<MainCourseConditionVo> isUkMainCourse(@PathVariable("id") Long id) {
        return new ResponseBo(rItemStepService.isUkMainCourse(id));
    }
}

package com.get.salecenter.service;

import com.get.salecenter.entity.AgentContractApprovalEntity;
import com.get.salecenter.dto.AgentContractApprovalDto;
import com.get.salecenter.vo.AgentContractApprovalVo;
import com.get.common.result.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 合同审批 服务类
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
public interface IAgentContractApprovalService extends IService<AgentContractApprovalEntity> {

    /**
     * 保存合同审批意见
     *
     * @param agentContractApprovalDto 合同审批DTO
     * @return 审批记录ID
     */
    Long addAgentContractApproval(AgentContractApprovalDto agentContractApprovalDto);

    /**
     * 发送邮件
     *
     * @param id 审批记录ID
     */
    void sendEmail(Long id);

    /**
     * 保存并发送邮件
     *
     * @param agentContractApprovalDto 合同审批DTO
     * @return 审批记录ID
     */
    Long saveAndSendEmail(AgentContractApprovalDto agentContractApprovalDto);

    /**
     * 列表查询
     *
     * @param agentContractApprovalDto 查询条件
     * @param page                     分页参数
     * @return 审批列表
     */
    List<AgentContractApprovalVo> getAgentContractApprovals(AgentContractApprovalDto agentContractApprovalDto,
            Page page);

}
package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.salecenter.dto.GoproRegistrationDto;


import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 活动报名业务逻辑类
 *
 * <AUTHOR>
 * @date 2022/5/10 11:25
 */
public interface IConventionGoproRegistrationService {

    /**
     * 活动报名列表
     *
     * @Date 12:12 2022/5/10
     * <AUTHOR>
     */

    List<GoproRegistrationDto> datas(GoproRegistrationDto data, Page<GoproRegistrationDto> page);

    int getCountSeatUsed(String type);

    List<GoproRegistrationDto> getIsExistMobile(String Mobile);

    Long addGopro(GoproRegistrationDto goproRegistrationDto);


    List<BaseSelectEntity> getYears();

    int getUsed(String type);

    List<Map<String,Object>> getSeatCountList();

    void exportGoPro(HttpServletResponse response, GoproRegistrationDto goproRegistrationDto);

    void update(GoproRegistrationDto goproRegistrationDto);

    int getUsedByTypeAndId(String type , Long id);

    void delete(Long id);
}

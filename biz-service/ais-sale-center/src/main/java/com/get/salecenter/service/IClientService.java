package com.get.salecenter.service;

import com.get.common.result.FocExportVo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.salecenter.dto.*;
import com.get.salecenter.vo.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * author:Neil
 * Time: 12:53
 * Date: 2022/8/17
 * Description:
 */
public interface IClientService {

    /**
     * 获取售前资源列表
     * @param clientDto
     * @param page
     * @return
     */
    ResponseBo<ClientListInfoDto> getListOfPreSalesResources(ClientDto clientDto, Page page);

    /**
     * 导出售前资源列表
     * @param clientDto
     * @param response
     * @return
     */
    void clientResourcesExport(ClientDto clientDto, List<FocExportVo> focExportVos, HttpServletResponse response);
    /**
     * 获取跟进状态下拉
     * @return
     */
    ResponseBo<Map<String,Object>> getFollowUpStatusList();


    /**
     * 添加售前资源客户
     * @param clientAddDto
     * @return
     */
    Long addClient(ClientAddDto clientAddDto);

    /**
     * 修改售前资源客户
     * @param clientUpdateDto
     * @return
     */
    ClientVo updateClient(ClientUpdateDto clientUpdateDto);

    /**
     * 客户详情接口
     * @param id
     * @return
     */
    ClientVo findClientById(Long id);

    /**
     * 设置签约时间
     * @param clientExpectSigningDto
     */
    void updateExpectSigningTime(ClientExpectSigningDto clientExpectSigningDto);

    /**
     * 设置客户星级
     * @param clientStarLevelDto
     */
    void updateStarLevel(ClientStarLevelDto clientStarLevelDto);

    /**
     * 删除客户信息
     * @param id
     */
    void delete(Long id);

    /**
     * 检测是否存在相同咨询客户
     * @param clientName
     * @param email
     * @param mobile
     * @param passpost
     * @param birthday
     * @param id
     * @param companyId
     * @return
     */
    Map<String, String> getIsExistStudent(String clientName, String email, String mobile, String passpost, String birthday, Long id, Long companyId);

    /**
     * 查询咨询客户附件
     * @param andAttachedVo
     * @param page
     * @return
     */
    List<MediaAndAttachedVo> getMedia(MediaAndAttachedDto andAttachedVo, Page page);

    /**
     * 保存咨询客户附件
     * @param mediaAttachedVo
     * @return
     */
    List<MediaAndAttachedVo> addMedia(ValidList<MediaAndAttachedDto> mediaAttachedVo);

    /**
     * 编辑评论
     * @param commentDto
     * @return
     */
    Long editComment(CommentDto commentDto);

    /**
     * 评论列表数据
     * @param data
     * @param page
     * @return
     */
    List<CommentVo> getComments(CommentDto data, Page page);

    /**
     * 学生客户审批列表
     * @param data
     * @param page
     * @return
     */
    List<ClientApprovalVo> getClientApprovalList(ClientApprovalDto data, Page page);

    /**
     * 申请学生审批
     * @param approvalStatusDto
     */
    List<String> upateApprovalStatus(ApprovalStatusDto approvalStatusDto);

    /**
     * 获取相同学生
     * @param sameStudentClientDto
     * @return
     */
    List<SameStudentClientVo> getSameStudentClients(SameStudentClientDto sameStudentClientDto);

    /**
     * 申请绑定
     * @param bindingStudentClientDto
     * @return
     */
    ClientVo bindingStudentClient(BindingStudentClientDto bindingStudentClientDto);

    /**
     * 表头信息
     * @return
     */
    List<FocExportVo> getClientOptions();

    /**
     * 创建业务学生
     * @param createBusinessStudentDto
     * @return
     */
    Long createBusinessStudent(CreateBusinessStudentDto createBusinessStudentDto);
    /**
     * 绑定业务学生
     *
     * @param boundBusinessStudentDto 参数
     * @return
     */
    void boundBusinessStudent(BoundBusinessStudentDto boundBusinessStudentDto);

    /**
     * 取消绑定业务学生
     *
     * @param
     * @return
     */
    void unbindBusinessStudent(Set<Long> studentIds);

    void updateActive( ClientUpdateDto clientUpdateVo);

    /**
     * 获取业务学生
     * @param fkClientId
     * @return
     */
    List<BusinessStudentVo> getBusinessStudent(Long fkClientId);

    List<BaseSelectEntity>  getSubordinatesOfTheCurrentUserSelect(List<Long> companyIds);
}

package com.get.salecenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.salecenter.dto.StudentInfoDto;
import com.get.salecenter.dto.StudentOfferDto;
import com.get.salecenter.dto.StudentOfferProjectUpdateDto;
import com.get.salecenter.dto.query.StudentOfferListQueryDto;
import com.get.salecenter.entity.StudentOffer;
import com.get.salecenter.vo.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/11/13
 * @TIME: 15:43
 * @Description:
 **/
public interface IStudentOfferService extends IService<StudentOffer> {

    /**
     * @return java.util.List<com.get.salecenter.vo.StudentOfferVo>
     * @Description: 列表数据
     * @Param [studentOfferDto, page]
     * <AUTHOR>
     */
    List<StudentOfferVo> getStudentOffer(StudentOfferDto studentOfferDto, Page page);

    /**
     * @return java.lang.Long
     * @Description: 新增
     * @Param [offerVo]
     * <AUTHOR>
     */
    Long addOffer(StudentOfferDto offerVo);

    /**
     * @return com.get.salecenter.vo.StudentOfferVo
     * @Description: 修改
     * @Param [offerVo]
     * <AUTHOR>
     */
    StudentOfferVo updateOffer(StudentOfferDto offerVo);


    Boolean updateStudentOffer(StudentOfferVo studentOffer);

    /**
     * @return com.get.salecenter.vo.StudentOfferVo
     * @Description: 详情
     * @Param [id]
     * <AUTHOR>
     */
    StudentOfferVo findOfferById(Long id);

    /**
     * 代理联系人邮箱下拉
     *
     * @param fkAgentId
     * @return
     */
    List<BaseSelectEntity> getContactPersonEmailSelect(Long fkAgentId);


    /**
     * @Description: feign调用 详情
     * @Author: Jerry
     * @Date:9:58 2021/11/4
     */
    StudentOfferVo getStudentOfferDetail(Long id);

    /**
     * @return void
     * @Description: 删除
     * @Param [id]
     * <AUTHOR>
     */
    void delete(Long id);


    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 根据学生id查找ids
     * @Param [studentId]
     * <AUTHOR>
     */
    List<Long> getOfferIdByStudentId(Long studentId);

    /**
     * @return java.lang.String
     * @Description: 查询代理名称
     * @Param [offerId]
     * <AUTHOR>
     */
    String getAgentNameByOfferId(Long offerId);

    /**
     * @return java.lang.String
     * @Description: 查询代理名称
     * @Param [offerId]
     * <AUTHOR>
     */
    Map<Long,String> getAgentNameByOfferIds(Set<Long> offerIds);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 根据员工id查询学生id
     * @Param [staffId]
     * <AUTHOR>
     */
    List<Long> getStudentIdByStaffId(Long staffId);

    /**
     * 根据员工姓名模糊查询学生id
     *
     * @param staffNameOrCode
     * @return
     */
    List<Long> getStudentIdByStaffNameOrCode(String staffNameOrCode);

    /**
     * 根据员工姓名或英文名模糊查询用户id
     *
     * @param staffNameOrEnName
     * @return
     */
    Set<Long> getStaffIdByStaffNameOrEnName(String staffNameOrEnName);

    /**
     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 学生申请方案下拉
     * @Param [projectType]
     * <AUTHOR>
     */
    List<BaseSelectEntity> getStudentOfferSelect(Long studentId, String typeKey,Long fkCompanyId);

    /**
     * @return java.lang.String
     * @Description: 根据id查询编号
     * @Param [offerId]
     * <AUTHOR>
     */
    String getOfferNumById(Long offerId);

    /**
     * @return void
     * @Description: 关闭
     * @Param [offerId]
     * <AUTHOR>
     */
    void unableOffer(Long offerId, Long status);

    /**
     * @return
     * @Description：获取学生绑定的代理列表
     * @Param
     * @Date 12:30 2021/4/25
     * <AUTHOR>
     */
    List<AgentAndAgentLabelVo> getAgentListByStudentId(Long studentId) throws GetServiceException;

    /**
     * @return
     * @Description：获取学生绑定的代理列表
     * @Param
     * @Date 12:30 2021/4/25
     * <AUTHOR>
     */
    List<AgentsBindingVo> getAgentByStudentIdTypeKey(String fkStudentNum, String typeKey) throws GetServiceException;

    /**
     * 角色员工下拉(角色联动)
     *
     * @Date 15:27 2021/7/6
     * <AUTHOR>
     */
    List<BaseSelectEntity> getRoleStaffByRoleSelect(Long roleId,Long fkAreaCountryId);

    /**
     * 激活方案
     *
     * @Date 12:29 2021/8/13
     * <AUTHOR>
     */
    void activationOffer(Long id);


    /**
     * @Description: 发起学生申请方案流程
     * @Author: Jerry
     * @Date:17:59 2021/11/3
     */
    void startStudentOfferFlow(String businessKey, String procdefKey, String companyId, String buttonType, String submitReason, Long fkCancelOfferReasonId) throws GetServiceException;


    /**
     * @Description: 重新提交或放弃流程
     * @Author: Jerry
     * @Date:9:36 2021/11/5
     */
    void getUserSubmit(String businessKey, String procdefKey, String status) throws GetServiceException;

    /**
     * 申请方案审批列表
     *
     * @param data
     * @return
     */
    List<StudentOfferVo> getStudentOfferWorkFolwDatas(StudentOfferListQueryDto data, String[] times);

    /**
     * 根据学生ids返回offer集合
     *
     * @param studentIds
     * @return
     */
    //@DS("saledb-doris")
    List<StudentOffer> getStudentOffersByStudentIds(List<Long> studentIds);


    ResponseBo doGetStudentOfferWorkFlowPaginationInfo(StudentOfferListQueryDto data, Page page);
    /**
     * @Description: 绑定代理下拉框
     * @Author: Jerry
     * @Date:12:28 2021/11/11
     */
    List<BaseSelectEntity> getAgentSelect(Long fkCompanyId);

    /**
     * 根据offerIds获取代理
     *
     * @param offerIds
     * @return
     */
    List<AgentVo> getAgentByOfferIds(@Param("offerIds") Set<Long> offerIds);

    /**
     * @return void
     * @Description : 关闭其他申请方案
     * @Param [id]
     * <AUTHOR>
     */
    void closeOtherStudentOffers(Long id);

    /**
     * 国家线下拉框
     *
     * @return
     */
    List<BaseSelectEntity> getAreaCountryList(Long companyId);

    /**
     * 申请方案批量分配项目成员
     *
     * @param studentOfferVo
     */
    Set<Long> batchDistributeProjectRole(StudentOfferListQueryDto studentOfferVo);

    /**
     * 复制申请方案
     *
     * @param id
     * @return
     */
    Long copyOffer(Long id);


    List<StudentOfferVo> getStudentOfferNew(StudentOfferDto studentOfferDto, Page page);

    Set<Long> deletebatchDistributeProjectRole(StudentOfferListQueryDto studentOfferVo);

    /**
     *根据id获取学习方案编号
     * @param offerIds
     * @return
     */
    Map<Long, String> getOfferNumByIds(Set<Long> offerIds);

    /**
     *  feign 申请方案工作流详情
     * @param id
     * @return
     */
    StudentOfferVo getStudentOfferForWorkFlow(Long id);

    /**
     * 撤销学生申请方案流程
     * @param id
     */
    void stopStudentOfferWorkFlow(Long id);

    Set<Long> getProjectLimitConfigKey(Set<Long> offerIds,Long fkCompanyId);

    List<StudentOfferItemVerifyInfoVo> verifyStudentOfferItemCourse(Long id);

    /**
     * 合并学习方案数据
     * @param mergedStudentId
     * @param targetStudentId
     */
    void mergeData(Long mergedStudentId, Long targetStudentId);

    void batchUpdateStudentOfferProjectRole(StudentOfferProjectUpdateDto studentOfferProjectUpdateDto);

    /**
     * 补充申请方案联系人邮箱
     */
    Boolean syncContacts();

    Boolean getIsBd(Long fkStaffId);

    void batchMaterialStatus(Long studentId);

    List<BaseSelectEntity> getContactPersonMobileAreaCodeSelect(Long fkAgentId);

    List<ContactPersonMobileSelectVo> getContactPersonMobileSelect(Long fkAgentId);

    List<String> getAgentEmailSelect(Long fkAgentId);

    /**
     * 获取申请方案列表(修改方案代理专用)
     * @param data
     * @param page
     * @return
     */
    List<StudentOfferBindingVo> getOfferBindingList(StudentInfoDto data, SearchBean<StudentInfoDto> page);

    Boolean verifyStudentOffer(StudentOfferDto offerVo);

    Boolean activationVerifyOffer(Long id);

    List<StudentStepHistoryVo> getStatusChangeDetails(Long studentId);

}

package com.get.salecenter.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseService;
import com.get.salecenter.dto.*;
import com.get.salecenter.vo.*;
import com.get.salecenter.vo.AgentAttendanceBindingInfoVo;
import com.get.salecenter.entity.ConventionPerson;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: Sea
 * @create: 2020/7/8 14:14
 * @verison: 1.0
 * @description: 峰会参展人员管理接口
 */
public interface IConventionPersonService extends BaseService<ConventionPerson> {

    /**
     * 详情
     *
     * @param id
     * @return
     */
    ConventionPersonVo findConventionPersonById(Long id);

    /**
     * 新增
     *
     * @param conventionPersonDto
     * @return
     */
    Long addConventionPerson(ConventionPersonDto conventionPersonDto);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 删除参会人关联表
     *
     * @param id               参会人员Id
     * @param conventionPerson 参会人员实体对象
     */
    void deleteAssociationTable(Long id, ConventionPerson conventionPerson);

    /**
     * 修改
     *
     * @param conventionPersonDto
     * @return
     */
    ConventionPersonVo updateConventionPerson(ConventionPersonDto conventionPersonDto);

    /**
     * 列表
     *
     * @param conventionPersonDto
     * @param page
     * @return
     */
    List<ConventionPersonVo> getConventionPersons(ConventionPersonDto conventionPersonDto, Page page);

    /**
     * 参会人员下拉框数据
     *
     * @param conventionId
     * @return
     */
    List<ConventionPerson> getConventionPersonList(Long conventionId);

    /**
     * 参会人员下拉框数据-包含系统锁定
     *
     * @param conventionId
     * @return
     * @
     */
    List<ConventionPerson> getPersonAndLockBedList(Long conventionId, Boolean bdCodeKey);

    /**
     * 查找参会人员姓名和性别
     *
     * @param id
     * @return
     */
    ConventionPersonVo findConventionPersonNameAndSex(Long id);

    /**
     * 设置签到
     *
     * @param id
     * @param isAttend
     */
    Boolean setSign(Long id, Boolean isAttend);

    /**
     * 跟进BD下拉框
     *
     * @return
     */
    List<StaffBdCodeVo> getBdList(Long fkConventionId, Long fkAreaRegionId);


    /**
     * @Description: 根据手机号获取对应的峰会人员姓名
     * @Author: Jerry
     * @Date:10:46 2021/10/15
     */
    Map<String, String> getNamesByMobiles(Set<String> mobiles);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :根据姓名模糊查询对应ids
     * @Param [personName]
     * <AUTHOR>
     */
    List<Long> getPersonIdsByName(String personName);

    /**
     * @return com.get.salecenter.vo.ConventionPersonVo
     * @Description :根据id查找姓名
     * @Param [id]
     * <AUTHOR>
     */
    ConventionPersonVo findPersonById(Long id);

    /**
     * @return java.util.List<com.get.salecenter.entity.ConventionPerson>
     * @Description :参会人员下拉框数据-桌台
     * @Param [id]
     * <AUTHOR>
     */
    List<ConventionPerson> getPersonForTableList(Long id, String type);

    /**
     * 根据峰会id和桌子类型 获取已安排桌子人员
     *
     * @Date 15:21 2021/6/29
     * <AUTHOR>
     */
    List<ConventionPerson> getPersonForTable(Long id, String type);

    /**
     * @return java.util.List<com.get.salecenter.vo.ConventionPersonVo>
     * @Description :筛选出桌台未被安排的人员信息
     * @Param [personIds, conventionPersonDto]
     * <AUTHOR>
     */
    List<ConventionPersonVo> getNotArrangedPersonList(IPage<ConventionPersonVo> iPage, List<Long> personIds, ConventionPersonDto conventionPersonDto);

    /**
     * @return java.util.List<com.get.salecenter.vo.ConventionPersonVo>
     * @Description :筛选出房间未被安排的人员信息
     * @Param [allRoomIds, conventionPersonDto]
     * <AUTHOR>
     */
    List<ConventionPersonVo> getRoomNotArrangedPersonList(IPage<ConventionPersonVo> conventionPersonDtoIPage, List<Long> roomIds, ConventionPersonDto conventionPersonDto);

    /**
     * @return java.util.List<java.lang.String>
     * @Description :根据bdName模糊查询bdNumList
     * @Param [bdNameKey]
     * <AUTHOR>
     */
    List<String> getBdCode(String bdNameKey);

    /**
     * @return java.lang.String
     * @Description :获取BD名称
     * @Param [bdCode]
     * <AUTHOR>
     */
    String getBdName(String bdCode);

    /**
     * 根据bdCode列表获取BD名称
     */
    Map<String,String> getBdNameMap(List<String> bdCodeList);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :根据姓名模糊查询对应ids
     * @Param [personName]
     * <AUTHOR>
     */
    List<Long> getPersonIdsByType(Integer personType);

    /**
     * @return void
     * @Description :导出参会人员Excel
     * @Param [response, conventionPersonDto]
     * <AUTHOR>
     */
    void exportConventionPersonExcel(HttpServletResponse response, ConventionPersonDto conventionPersonDto);


    /**
     * @Description: 根据名称模糊搜索参展人员ids
     * @Author: Jerry
     * @Date:16:24 2021/9/15
     */
    Set<Long> getConventionPersonIdsByName(String name);


    /**
     * @Description: 根据人员ids获取对象
     * @Author: Jerry
     * @Date:17:03 2021/9/15
     */
    Map<Long, ConventionPerson> getConventionPersonByConventionPersonIds(Set<Long> conventionPersonIds);

    /**
     * 获取ids by bdcode
     *
     * @param bdCode
     * @return
     */
    List<Long> getPersonIdsByBdCode(String bdCode);

    /**
     * 姓名和性别
     *
     * @return
     */
    List<ConventionPersonVo> findConventionPersonNameAndSexByConventionId(Long conventionId, List<String> bdCodes);

    /**
     *
     * @param conventionPersonId
     * @return
     */
    ConventionPerson getConventionPersonById(Long conventionPersonId);

    List<Long> getRepeatPerson(Integer searchRepeatPerson,Long conventionId,String tableTypeKey);

    List<ConventionPersonVotingRankingVo> helpVotingRanking(ConventionPersonVotingRankingDto conventionPersonVotingRankingDto, Page page);

    List<LikeCollectionActivityVo> likeCollectionActivity(LikeCollectionActivitySearchDto likeCollectionActivitySearchVo, Page page);

    Set<ConventionSelectVo> conventionSelect();

     void likeConventionApproval(Long id,Integer status );

    void likeConventionEdit(LikeConventionEditDto likeConventionEditDto);



        /**
         *曾参加过峰会的同名员工列表
         * @param type
         * @param fkCompanyId
         * @param name
         * @return
         */
    List<DuplicatedConventionPersonVo> getDuplicatedConventionPersonsInfo(Integer type, Long fkCompanyId, String name);

    /**
     * 代理参会统计
     * @param agentAttendanceStatisticsDto
     * @return
     */
    List<AgentAttendanceStatisticsVo> getAgentAttendanceStatistics(AgentAttendanceStatisticsDto agentAttendanceStatisticsDto);

    /**
     * 获取代理参会人绑定bd数量信息
     * @param agentAttendanceStatisticsDto
     * @return
     */
    AgentAttendanceBindingInfoVo getAgentAttendanceBindingInfo(AgentAttendanceStatisticsDto agentAttendanceStatisticsDto);
    /**
     * 获取该峰会的所有跟进bd
     * @param fkConventionId
     * @return
     */
    List<StaffBdCodeVo> getAllBdList(Long fkConventionId);
}

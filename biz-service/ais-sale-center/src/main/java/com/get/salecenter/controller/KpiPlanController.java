package com.get.salecenter.controller;


import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.CommonUtil;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.dto.KpiPlanStatisticsExportDto;
import com.get.salecenter.vo.KpiPlanVo;
import com.get.salecenter.vo.KpiPlanStatisticsVo;
import com.get.salecenter.service.KpiPlanService;
import com.get.salecenter.dto.KpiPlanSearchDto;
import com.get.salecenter.dto.KpiPlanStatisticsDto;
import com.get.salecenter.dto.KpiPlanDto;
import com.get.salecenter.vo.KpiStaffIdAddVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Set;

@Api(tags = "KPI方案管理")
@RestController
@RequestMapping("sale/kpiPlan")
public class KpiPlanController {

    @Resource
    private KpiPlanService kpiPlanService;

    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/KPI方案管理/列表数据")
    @PostMapping("datas")
    public ResponseBo<KpiPlanVo> datas(@RequestBody SearchBean<KpiPlanSearchDto> page) {
        List<KpiPlanVo> datas = kpiPlanService.datas(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    @ApiOperation(value = "详情", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/KPI方案管理/详情")
    @GetMapping("/{id}")
    public ResponseBo<KpiPlanVo> detail(@PathVariable("id") Long id) {
        KpiPlanVo data = kpiPlanService.findKpiPlanById(id);
        return new ResponseBo<>(data);
    }

    @ApiOperation(value = "更新KPI方案统计（即时）", notes = "将即时生成的统计结果放到结果表中")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/KPI方案管理/更新KPI方案统计（即时）")
    @PostMapping("addKpiPlanTaskResult")
    public ResponseBo addKpiPlanTaskResult(@RequestBody @Validated KpiPlanStatisticsDto kpiPlanStatisticsVo) {
        kpiPlanService.addKpiPlanTaskResult(kpiPlanStatisticsVo);
        return SaveResponseBo.ok();
    }

    @ApiOperation(value = "KPI方案统计", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/KPI方案管理/KPI方案统计")
    @PostMapping("getKpiPlanStatistics")
    public ResponseBo<KpiPlanStatisticsVo> getKpiPlanStatistics(@RequestBody @Validated  KpiPlanStatisticsDto kpiPlanStatisticsDto) {
        return new ResponseBo<>(kpiPlanService.getKpiPlanStatistics(kpiPlanStatisticsDto));
    }

    @ApiOperation(value = "导出KPI方案统计", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/KPI方案管理/导出KPI方案统计")
    @PostMapping("exportKpiPlanStatisticsExcel")
    public void exportKpiPlanStatisticsExcel(@RequestBody @Validated KpiPlanStatisticsExportDto exportDto, HttpServletResponse response) {
        kpiPlanService.exportKpiPlanStatisticsExcel(exportDto, response);
    }

    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/KPI方案管理/新增")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(KpiPlanDto.Add.class)  KpiPlanDto vo) {
        return SaveResponseBo.ok(kpiPlanService.addKpiPlan(vo));
    }

    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/KPI方案管理/更新")
    @PostMapping("update")
    public ResponseBo update(@RequestBody @Validated(KpiPlanDto.Update.class) KpiPlanDto vo) {
        kpiPlanService.updateKpiPlan(vo);
        return UpdateResponseBo.ok();
    }

    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/KPI方案管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        kpiPlanService.delete(id);
        return SaveResponseBo.ok();
    }

    @ApiOperation(value = "复制KPI方案", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/KPI方案管理/复制KPI方案")
    @PostMapping("copyKpiPlan")
    public ResponseBo copyKpiPlan(@RequestParam("fkKpiPlanId") Long fkKpiPlanId) {
        return new ResponseBo(kpiPlanService.copyKpiPlan(fkKpiPlanId));
    }

    @ApiOperation(value = "KPI代理排名导出", notes = "代理排名是根据KPI方案统计结果得到的")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/KPI方案管理/KPI方案统计/KPI代理排名导出")
    @PostMapping("exportKpiAgentRankExcel")
    public void exportKpiAgentRankExcel(HttpServletResponse response, @RequestBody @Validated KpiPlanStatisticsDto kpiPlanStatisticsDto) {
        CommonUtil.ok(response);
        kpiPlanService.exportKpiAgentRankExcel(kpiPlanStatisticsDto);
    }

    @ApiOperation(value = "获取KPI方案数据时间戳之和 / 获取KPI方案异步统计是否结束", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/KPI方案管理/KPI方案统计/获取KPI方案数据时间戳之和")
    @PostMapping("getSumTimeKpiData")
    public ResponseBo getSumTimeKpiData(@RequestParam("fkKpiPlanId") Long fkKpiPlanId) {
        return new ResponseBo<>(kpiPlanService.getSumTimeKpiData(fkKpiPlanId));
    }

    @ApiOperation(value = "KPI模板文件下载")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/KPI方案管理/KPI模板文件下载")
    @PostMapping("downloadTemplateFile")
    public void downloadTemplateFile(HttpServletResponse response) {
        kpiPlanService.downloadTemplateFile(response);
    }

    @ApiOperation(value = "KPI数据导入", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/KPI方案管理/KPI数据导入")
    @PostMapping("importKpiData")
    public ResponseBo importKpiData(@RequestParam("file") MultipartFile file, @RequestParam("fkKpiPlanId") Long fkKpiPlanId){
        return kpiPlanService.importKpiData(file, fkKpiPlanId);
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "KPI方案统计角色下拉", notes = "")
    @GetMapping("getCountRoleSelect")
    public ResponseBo getCountRoleSelect() {
        return new ListResponseBo<>(ProjectExtraEnum.enumsTranslation2Arrays(ProjectExtraEnum.COUNT_ROLE));
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "KPI方案下拉", notes = "")
    @GetMapping("getKpiPlanSelect")
    public ResponseBo getKpiPlanSelect(@RequestParam("fkCompanyIds") String fkCompanyIds) {
        return new ListResponseBo<>(kpiPlanService.getKpiPlanSelect(fkCompanyIds));
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "KPI方案所属公司下拉", notes = "")
    @GetMapping("getKpiPlanCompanySelect")
    public ResponseBo getKpiPlanCompanySelect(@RequestParam("kpiPlanId")Long kpiPlanId) {
        return new ResponseBo<>(kpiPlanService.getKpiPlanCompanySelect(kpiPlanId));
    }

    @ApiOperation(value = "获取kpi方案第一层目标设置人", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/KPI方案管理/获取kpi方案第一层目标设置人")
    @PostMapping("/findFkStaffIdAdd")
    public ResponseBo<KpiStaffIdAddVo> findFkStaffIdAdd(@RequestBody @Validated KpiPlanStatisticsDto kpiPlanStatisticsVo) {
        KpiStaffIdAddVo data = kpiPlanService.findFkStaffIdAdd(kpiPlanStatisticsVo);
        return new ResponseBo<>(data);
    }


}

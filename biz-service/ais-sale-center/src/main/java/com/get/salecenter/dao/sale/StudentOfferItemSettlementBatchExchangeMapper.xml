<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.StudentOfferItemSettlementBatchExchangeMapper">
  <insert id="insert" parameterType="com.get.salecenter.entity.StudentOfferItemSettlementBatchExchange">
    insert into r_student_offer_item_settlement_batch_exchange (id, num_settlement_batch, fk_currency_type_num, 
      fk_currency_type_num_exchange, exchange_rate, 
      service_fee, fk_agent_id, gmt_create, 
      gmt_create_user, gmt_modified, gmt_modified_user
      )
    values (#{id,jdbcType=BIGINT}, #{numSettlementBatch,jdbcType=VARCHAR}, #{fkCurrencyTypeNum,jdbcType=VARCHAR}, 
      #{fkCurrencyTypeNumExchange,jdbcType=VARCHAR}, #{exchangeRate,jdbcType=DECIMAL}, 
      #{serviceFee,jdbcType=DECIMAL}, #{fkAgentId,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.get.salecenter.entity.StudentOfferItemSettlementBatchExchange">
    insert into r_student_offer_item_settlement_batch_exchange
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="numSettlementBatch != null">
        num_settlement_batch,
      </if>
      <if test="fkCurrencyTypeNum != null">
        fk_currency_type_num,
      </if>
      <if test="fkCurrencyTypeNumExchange != null">
        fk_currency_type_num_exchange,
      </if>
      <if test="exchangeRate != null">
        exchange_rate,
      </if>
      <if test="serviceFee != null">
        service_fee,
      </if>
      <if test="fkAgentId != null">
        fk_agent_id,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="numSettlementBatch != null">
        #{numSettlementBatch,jdbcType=VARCHAR},
      </if>
      <if test="fkCurrencyTypeNum != null">
        #{fkCurrencyTypeNum,jdbcType=VARCHAR},
      </if>
      <if test="fkCurrencyTypeNumExchange != null">
        #{fkCurrencyTypeNumExchange,jdbcType=VARCHAR},
      </if>
      <if test="exchangeRate != null">
        #{exchangeRate,jdbcType=DECIMAL},
      </if>
      <if test="serviceFee != null">
        #{serviceFee,jdbcType=DECIMAL},
      </if>
      <if test="fkAgentId != null">
        #{fkAgentId,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>
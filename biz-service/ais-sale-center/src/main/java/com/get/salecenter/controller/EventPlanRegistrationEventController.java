package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.result.UpdateResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.salecenter.service.EventPlanRegistrationEventService;
import com.get.salecenter.dto.EventPlanRegistrationEventDeleteDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 *  前端接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12
 */

@Api(tags = "活动年度计划报名名册参加活动")
@RestController
@RequestMapping("sale/eventPlanRegistrationEvent")
public class EventPlanRegistrationEventController {
    @Resource
    private EventPlanRegistrationEventService eventPlanRegistrationEventService;

    @ApiOperation(value = "取消活动", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/活动年度计划报名名册参加活动/取消活动")
    @PostMapping("cancel")
    public ResponseBo cancel(@RequestParam("id") Long id,@RequestParam("isCancel") Boolean isCancel) {
        eventPlanRegistrationEventService.cancel(id,isCancel);
        return UpdateResponseBo.ok();
    }

    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/活动年度计划报名名册/删除报名名册项目")
    @PostMapping("delete")
    public ResponseBo delete(@RequestBody @Validated EventPlanRegistrationEventDeleteDto vo) {
        eventPlanRegistrationEventService.delete(vo);
        return DeleteResponseBo.ok();
    }
}

package com.get.salecenter.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.get.common.result.Page;
import com.get.salecenter.dto.EventCostDto;
import com.get.salecenter.entity.EventCost;
import com.get.salecenter.vo.EventBillVo;
import com.get.salecenter.vo.EventCostVo;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: Sea
 * @create: 2020/12/9 17:59
 * @verison: 1.0
 * @description:
 */
public interface IEventCostService extends IService<EventCost> {

    /**
     * @return com.get.salecenter.vo.EventCostVo
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    EventCostVo findEventCostById(Long id);

    /**
     * @return void
     * @Description :新增
     * @Param [eventCostVos]
     * <AUTHOR>
     */
    Long addEventCost(EventCostDto eventCostDto);

    /**
     * @return void
     * @Description :删除
     * @Param [id]
     * <AUTHOR>
     */
    void delete(Long id);

    /**
     * @return com.get.salecenter.vo.EventCostVo
     * @Description :修改
     * @Param [eventCostDto]
     * <AUTHOR>
     */
    EventCostVo updateEventCost(EventCostDto eventCostDto);

    /**
     * @return java.util.List<com.get.salecenter.vo.EventCostVo>
     * @Description :列表
     * @Param [eventCostDto, page]
     * <AUTHOR>
     */
    List<EventCostVo> getEventCosts(EventCostDto eventCostDto, Page page);

    /**
     * 活动费用归口收款单下拉框
     *
     * @Date 12:03 2021/12/3
     * <AUTHOR>
     */
    List<EventBillVo> getReceiptSelect(Long institutionProviderId, Long companyId, Long eventCostId, Long conventionRegistrationId, Long conventionSponsorId, String eventYear);

    /**
     * 根据receiptFormIds获取活动费用归口
     *
     * @param receiptFormIds
     * @return
     */
    Map<Long, List<EventCostVo>> getEventCostDtoByReceiptFormIds(Set<Long> receiptFormIds);

    /**
     * 根据receiptFormId获取活动费用归口Dtos
     *
     * @param receiptFormId
     * @return
     */
    List<EventCostVo> getEventCostDtoByReceiptFormId(Long receiptFormId);

    /**
     * 费用小计
     *
     * @param eventCostDto
     * @return
     */
    BigDecimal getEventCostSubtotal(EventCostDto eventCostDto);


    List<EventCost> getEventCostsByCondition(LambdaQueryWrapper<EventCost> lambdaQueryWrapper);

    void batchAdd(List<EventCostDto> eventCostDtos);

    EventCostVo getAllocatedActivityFees(EventCostDto eventCostDto);

    List<String> getReceiptSelectYear(Long institutionProviderId, Long companyId, Long eventCostId, Long conventionRegistrationId, Long conventionSponsorId);
}

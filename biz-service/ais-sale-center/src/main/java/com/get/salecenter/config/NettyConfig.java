package com.get.salecenter.config;

import com.get.core.mybatis.base.SystemPageVo;
import io.netty.channel.Channel;
import io.netty.channel.group.ChannelGroup;
import io.netty.channel.group.DefaultChannelGroup;
import io.netty.util.concurrent.GlobalEventExecutor;

import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2020-03-28-15:07
 */
public class NettyConfig {
    /**
     * 定义一个channel组，管理所有的channel
     * GlobalEventExecutor.INSTANCE 是全局的事件执行器，是一个单例
     */
    private static ChannelGroup channelGroup = new DefaultChannelGroup(GlobalEventExecutor.INSTANCE);

    /**
     * 存放用户与Chanel的对应信息，用于给指定用户发送消息
     */
    private static ConcurrentHashMap<Long, Channel> userChannelMap = new ConcurrentHashMap<>();


    private static ConcurrentHashMap<Long, SystemPageVo> userSearchMap = new ConcurrentHashMap<>();

    private NettyConfig() {
    }

    /**
     * 获取channel组
     *
     * @return
     */
    public static ChannelGroup getChannelGroup() {
        return channelGroup;
    }

    /**
     * 获取用户channel map
     *
     * @return
     */
    public static ConcurrentHashMap<Long, Channel> getUserChannelMap() {
        return userChannelMap;
    }


    public static ConcurrentHashMap<Long, SystemPageVo> getUserSearchMap() {
        return userSearchMap;
    }
}

package com.get.salecenter.service.impl;


import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.salecenter.dao.sale.AgentContractFormulaAreaCountryMapper;
import com.get.salecenter.entity.AgentContractFormulaAreaCountry;
import com.get.salecenter.service.IAgentContractFormulaAreaCountryService;
import com.get.salecenter.dto.AgentContractFormulaAreaCountryDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * @author: Sea
 * @create: 2021/1/6 15:09
 * @verison: 1.0
 * @description:
 */
@Service
public class AgentContractFormulaAreaCountryServiceImpl implements IAgentContractFormulaAreaCountryService {
    @Resource
    private AgentContractFormulaAreaCountryMapper agentContractFormulaAreaCountryMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;

    @Override
    public Long addAgentContractFormulaAreaCountry(AgentContractFormulaAreaCountryDto agentContractFormulaAreaCountryDto) {
        AgentContractFormulaAreaCountry agentContractFormulaAreaCountry = BeanCopyUtils.objClone(agentContractFormulaAreaCountryDto, AgentContractFormulaAreaCountry::new);
        utilService.updateUserInfoToEntity(agentContractFormulaAreaCountry);
        agentContractFormulaAreaCountryMapper.insertSelective(agentContractFormulaAreaCountry);
        return agentContractFormulaAreaCountry.getId();
    }

    @Override
    public void deleteByFkid(Long agentContractFormulaId) {
//        Example example = new Example(AgentContractFormulaAreaCountry.class);
//        example.createCriteria().andEqualTo("fkAgentContractFormulaId", agentContractFormulaId);
//        agentContractFormulaAreaCountryMapper.deleteByExample(example);
        agentContractFormulaAreaCountryMapper.delete(Wrappers.<AgentContractFormulaAreaCountry>lambdaQuery().eq(AgentContractFormulaAreaCountry::getFkAgentContractFormulaId, agentContractFormulaId));

    }

    @Override
    public Map<Long, String> getCountryNameMapByFkids(List<Long> agentContractFormulaIds) {
        //关系map
        Map<Long, List<Long>> idMap = new HashMap<>();
        Map<Long, String> nameMap = new HashMap<>();
        //全部countryId集合
        Set<Long> countryIdSet = new HashSet<>();
        for (Long agentContractFormulaId : agentContractFormulaIds) {
            //通过agentContractFormulaId获取对应所有国家id
            List<Long> countryIds = getCountryIdListByFkid(agentContractFormulaId);
            countryIdSet.addAll(countryIds);
            //agentContractFormulaId和countryIds一一对应关系map
            idMap.put(agentContractFormulaId, countryIds);
        }
        countryIdSet.removeIf(Objects::isNull);
        //feign调用一次查出 countryId和countryName对应关系map
        Map<Long, String> countryNameMap = new HashMap<>();
        Result<Map<Long, String>> result = institutionCenterClient.getCountryNamesByIds(countryIdSet);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            countryNameMap = result.getData();
            //map由agentContractFormulaId 对应 countryIds 转成 agentContractFormulaId 对应 countryNames
            for (Map.Entry<Long, List<Long>> agentContractFormula : idMap.entrySet()) {
                List<String> countryNames = new ArrayList<>();
                List<Long> countryIds = agentContractFormula.getValue();
                for (Long countryId : countryIds) {
                    countryNames.add(countryNameMap.get(countryId));
                }
                nameMap.put(agentContractFormula.getKey(), StringUtils.join(countryNames, "，"));
            }
        }
        return nameMap;
    }

    @Override
    public List<Long> getCountryIdListByFkid(Long agentContractFormulaId) {
        return agentContractFormulaAreaCountryMapper.getCountryIdsByFkid(agentContractFormulaId);
    }


}

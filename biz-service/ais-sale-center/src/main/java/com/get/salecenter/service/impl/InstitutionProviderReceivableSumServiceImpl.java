package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.file.utils.FileUtils;
import com.get.salecenter.dao.sale.ReceivablePlanMapper;
import com.get.salecenter.vo.InstitutionProviderReceivableSumExportVo;
import com.get.salecenter.vo.InstitutionProviderReceivableSumVo;
import com.get.salecenter.service.InstitutionProviderReceivableSumService;
import com.get.salecenter.dto.InstitutionProviderReceivableSumDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 　　　　　　　　　　◆◆
 * 　　　　　　　　　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　　　　◆　　　　　　　　　　　　◆　　　　　　　　　　　　　　◆　　　　　　　　　　　　　◆◆　　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆◆◆◆　　　　　　　　　◆◆◆　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　◆◆◆　◆◆◆　　　　　　　　　◆◆　◆◆　　　　　　　　　　◆◆　◆◆　　　　　　　　　　◆◆　◆◆
 * 　　　◆◆　　　　　◆◆　　　　　　　◆◆◆◆◆◆◆　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆◆◆◆
 * 　　　◆◆◆　　　◆◆◆　　　　　　　◆◆　　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　◆◆◆◆
 * 　　　　◆◆◆　◆◆◆　　　　　　　　◆◆◆　◆◆◆　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　◆◆◆
 * 　　　　◆◆◆◆◆◆◆　　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　　◆◆
 * 　　　　　　◆◆◆　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　◆◆◆
 * 　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　◆◆◆◆
 * <p>
 * Time: 10:33
 * Date: 2021/11/20
 * Description:提供商应收汇总统计业务实现类
 */
@Service
public class InstitutionProviderReceivableSumServiceImpl implements InstitutionProviderReceivableSumService {

    //    @Resource
//    private IInstitutionCenterClient institutionCenterClient;
//    @Resource
//    private IFinanceCenterClient financeCenterClient;
//    @Resource
//    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private ReceivablePlanMapper receivablePlanMapper;

    /**
     * @Description: 提供商应收汇总统计列表
     * @Author: Jerry
     * @Date:10:34 2021/11/20
     */
    @Override
    public List<InstitutionProviderReceivableSumVo> datas(InstitutionProviderReceivableSumDto institutionProviderReceivableSumDto, Page page) {
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
        if (GeneralTool.isNotEmpty(institutionProviderReceivableSumDto.getFkCompanyIds())) {
            //查看的公司权限
            if (!SecureUtil.validateCompanys(institutionProviderReceivableSumDto.getFkCompanyIds())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
            }
        }
        List<InstitutionProviderReceivableSumVo> institutionProviderReceivableSumVos = new ArrayList<>();
        if (page != null) {
            IPage<InstitutionProviderReceivableSumVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
            institutionProviderReceivableSumVos = receivablePlanMapper.getInstitutionProviderReceivableSumDatas(iPage, institutionProviderReceivableSumDto);
            page.setAll((int) iPage.getTotal());
        } else {
            institutionProviderReceivableSumVos = receivablePlanMapper.getInstitutionProviderReceivableSumDatas(null, institutionProviderReceivableSumDto);
        }

        if (GeneralTool.isEmpty(institutionProviderReceivableSumVos)) {
            return Collections.emptyList();
        }
//        page.restPage(institutionProviderReceivableSumVos);
        for (InstitutionProviderReceivableSumVo institutionProviderReceivableSumVo : institutionProviderReceivableSumVos) {
            String currencyTypeName = institutionProviderReceivableSumVo.getFkCurrencyTypeName();
            StringBuilder sb = new StringBuilder();
            sb.append("(").append(currencyTypeName).append(")");
            institutionProviderReceivableSumVo.setPayableAmountStr(institutionProviderReceivableSumVo.getPayableAmount() + sb.toString());
            institutionProviderReceivableSumVo.setAmountPayableStr(institutionProviderReceivableSumVo.getAmountPayable() + sb.toString());
            institutionProviderReceivableSumVo.setDifferPayStr(institutionProviderReceivableSumVo.getDifferPay() + sb.toString());

            String institutionProviderNameChn = institutionProviderReceivableSumVo.getInstitutionProviderNameChn();
            if (GeneralTool.isEmpty(institutionProviderReceivableSumVo.getInstitutionProviderName())) {
                institutionProviderReceivableSumVo.setInstitutionProviderFullName(institutionProviderNameChn);
                continue;
            }
            StringBuilder fullName = new StringBuilder(institutionProviderReceivableSumVo.getInstitutionProviderName());
            if (GeneralTool.isNotEmpty(institutionProviderNameChn)) {
                sb.append("(").append(institutionProviderNameChn).append(")");
            }
            institutionProviderReceivableSumVo.setInstitutionProviderFullName(fullName.toString());
        }
        return institutionProviderReceivableSumVos;
    }


    /**
     * @Description: 导出提供商应收汇总统计列表Excel
     * @Author: Jerry
     * @Date:15:32 2021/11/20
     */
    @Override
    public void exportAgentPaySumExcel(HttpServletResponse response, InstitutionProviderReceivableSumDto institutionProviderReceivableSumDto) {
        List<InstitutionProviderReceivableSumVo> datas = datas(institutionProviderReceivableSumDto, null);
        if (GeneralTool.isEmpty(datas)) {
            return;
        }
        List<InstitutionProviderReceivableSumExportVo> institutionProviderReceivableSumExportVos = new ArrayList<>();
        for (InstitutionProviderReceivableSumVo institutionProviderReceivableSumVo : datas) {
            InstitutionProviderReceivableSumExportVo institutionProviderReceivableSumExportVo = BeanCopyUtils.objClone(institutionProviderReceivableSumVo, InstitutionProviderReceivableSumExportVo::new);
//            if (GeneralTool.isNotEmpty(institutionProviderReceivableSumVo.getFkCurrencyTypeNum())) {
//                StringBuilder sb = new StringBuilder();
//                sb.append(institutionProviderReceivableSumVo.getFkCurrencyTypeName()).append("（").append(institutionProviderReceivableSumVo.getFkCurrencyTypeNum()).append("）");
//                institutionProviderReceivableSumExportVo.setFkCurrencyTypeName(sb.toString());
//            }
            institutionProviderReceivableSumExportVos.add(institutionProviderReceivableSumExportVo);
        }
        FileUtils.exportExcel(response, institutionProviderReceivableSumExportVos, "InstitutionProviderReceivableSumInfo", InstitutionProviderReceivableSumExportVo.class);
    }
}

package com.get.salecenter.controller;

import com.get.common.consts.AESConstant;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.utils.AESUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.log.exception.GetServiceException;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.service.IConventionRegistrationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;

@Controller
@RequestMapping("sale/registrationView")
@Api(tags = "峰会报名跳转确认")
@VerifyPermission(IsVerify = false)
public class RegistrationViewController {

    @Resource
    private IConventionRegistrationService conventionRegistrationService;

    @ApiOperation(value = "确认接口", notes = "")
    @VerifyLogin(IsVerify = false)
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/峰会报名管理/确认收到")
    @GetMapping("confirm")
    public String confirm(@RequestParam("code") String code, Model model) {
        String test = null;
        String receiptCode = null;
        try {
            receiptCode = AESUtils.DecryptURL(code, AESConstant.AESKEY);
        } catch (Exception e) {
            throw new GetServiceException(e.getMessage());
        }
        int num =  conventionRegistrationService.confirm(receiptCode);
        if (num==0) {
            model.addAttribute("message", "无效的验证码，请检查您的链接是否正确。");
            model.addAttribute("messageEn", "Invalid verification code, please check if your link is correct.");
        } else if (num==1) {
            model.addAttribute("message", "已成功报名，请勿重复操作！");
            model.addAttribute("messageEn", "Successfully registered, please do not repeat the process!");
        } else {
            model.addAttribute("message", "报名成功！感谢您的确认！");
            model.addAttribute("messageEn", "Registration successful! Thank you for your confirmation!");
        }
        return "result";
    }



}

package com.get.salecenter.service;

import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.service.GetService;
import com.get.salecenter.dto.RoleStaffDepartmentDto;
import com.get.salecenter.vo.StudentProjectRoleVo;
import com.get.salecenter.entity.StudentProjectRole;
import com.get.salecenter.dto.StudentProjectRoleDto;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: Sea
 * @create: 2020/11/2 16:16
 * @verison: 1.0
 * @description:
 */
public interface IStudentProjectRoleService extends GetService<StudentProjectRole> {
    /**
     * @return com.get.salecenter.vo.StudentProjectRoleVo
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    StudentProjectRoleVo findStudentProjectRoleById(Long id);

    /**
     * @return java.lang.Long
     * @Description :新增
     * @Param [studentProjectRoleDto]
     * <AUTHOR>
     */
    Long addStudentProjectRole(StudentProjectRoleDto studentProjectRoleDto);

    /**
     * @return void
     * @Description :删除
     * @Param [id]
     * <AUTHOR>
     */
    void delete(Long id);

    /**
     * @return com.get.salecenter.vo.StudentProjectRoleVo
     * @Description :修改
     * @Param [studentProjectRoleDto]
     * <AUTHOR>
     */
    StudentProjectRoleVo updateStudentProjectRole(StudentProjectRoleDto studentProjectRoleDto);


    /**
     * 获取存在学习方案角色下的所属部门
     *
     * @Date 10:55 2023/4/6
     * <AUTHOR>
     */
    ListResponseBo<BaseSelectEntity> getRoleStaffDepartmentByRoleId(RoleStaffDepartmentDto roleStaffDepartmentVo);


    /**
     * 获取角色下的员工
     *
     * @param
     * @param 
     * @param subordinateFlag
     * @return
     */
//    ListResponseBo<BaseSelectEntity> getRoleStaff(String projectRoleKey, String departmentIdStr, Boolean subordinateFlag, String companyIdStr);
    ListResponseBo<BaseSelectEntity> getRoleStaff(String projectRoleKey, String departmentIdStr, Boolean subordinateFlag, String companyIdStr);
    /**
     * @return java.util.List<com.get.salecenter.vo.StudentProjectRoleVo>
     * @Description :列表
     * @Param [studentProjectRoleDto, page]
     * <AUTHOR>
     */
    List<StudentProjectRoleVo> getStudentProjectRoles(StudentProjectRoleDto studentProjectRoleDto, Page page);

    /**
     * @return void
     * @Description :上移下移
     * @Param [studentProjectRoleDtos]
     * <AUTHOR>
     */
    void movingOrder(List<StudentProjectRoleDto> studentProjectRoleDtos);


    /**
     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 获取角色下拉
     * @Param [companyId]
     * <AUTHOR>
     */
    List<BaseSelectEntity> getRoleSelect(Long companyId);

    /**
     * 角色下拉
     *
     * @return
     */
    List<BaseSelectEntity> getRoleCollection();

    /**
     * 角色下拉,根据登录人部门与角色绑定部门过滤数据
     *
     * @return
     */
    List<BaseSelectEntity> getDepartmentRole(Long companyId);

    /**
     * @return java.lang.String
     * @Description: 根据id去角色名称
     * @Param [roleId]
     * <AUTHOR>
     */
    String getRoleNameById(Long roleId);

    /**
     * 获取所有项目角色名名称
     *
     * @Date 14:59 2021/8/2
     * <AUTHOR>
     */
    Map<Long, String> getAllRoleName();

    /**
     * 根据roleKey获取角色
     *
     * @param roleKey
     * @return
     */
    StudentProjectRoleVo getRoleByKey(String roleKey);

    /**
     * 根据roleKey获取角色
     * @param roleKeys
     * @return
     */
    List<StudentProjectRoleVo> getRoleByKeys(Set<String> roleKeys);

    /**
     * @param roleIds
     * @return
     */
    Map<Long, String> getRoleNameByIds(Set<Long> roleIds);

    /**
     * 获取对应公司下有申请计划的 角色下拉
     *
     * @Date 17:39 2023/1/11
     * <AUTHOR>
     */
    List<BaseSelectEntity> getExistsOfferItemAgentList(Long companyId);


    List<StudentProjectRole> getStudentProjectRoleListByRoleIds(Set<Long> roleIds);
}

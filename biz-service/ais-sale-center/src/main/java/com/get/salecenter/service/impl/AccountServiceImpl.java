package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.core.tool.api.Result;
import com.get.salecenter.dao.sale.AgentContractAccountMapper;
import com.get.salecenter.entity.AgentContractAccount;
import com.get.salecenter.service.AccountService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class AccountServiceImpl implements AccountService {

    @Resource
    private AgentContractAccountMapper accountMapper;

    @Override
    public Result<AgentContractAccount> getAccount(Long agentId, String backNum) {
        List<AgentContractAccount> account = accountMapper.selectList(Wrappers.<AgentContractAccount>lambdaQuery()
                .eq(AgentContractAccount::getFkAgentId, agentId).eq(AgentContractAccount::getBankAccountNum, backNum));
        if (account.isEmpty()) {
            return Result.data(null);
        }
        return Result.data(account.get(0));
    }
}

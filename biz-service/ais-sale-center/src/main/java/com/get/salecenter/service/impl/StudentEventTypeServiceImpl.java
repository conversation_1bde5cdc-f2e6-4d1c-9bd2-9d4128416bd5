package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.StudentEventTypeMapper;
import com.get.salecenter.vo.StudentEventTypeVo;
import com.get.salecenter.entity.StudentEventType;
import com.get.salecenter.service.IStudentEventTypeService;
import com.get.salecenter.dto.StudentEventTypeDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/11/2
 * @TIME: 14:38
 * @Description:
 **/
@Service
public class StudentEventTypeServiceImpl implements IStudentEventTypeService {
    @Resource
    private StudentEventTypeMapper studentEventTypeMapper;
    @Resource
    private UtilService utilService;

    @Override
    public StudentEventTypeVo findStudentEventTypeById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        StudentEventType studentEventType = studentEventTypeMapper.selectById(id);
        StudentEventTypeVo studentEventTypeVo = BeanCopyUtils.objClone(studentEventType, StudentEventTypeVo::new);
        return studentEventTypeVo;
    }

    @Override
    public List<StudentEventTypeVo> getStudentEventTypes(StudentEventTypeDto studentEventTypeDto, Page page) {
//        Example example = new Example(StudentEventType.class);
//        Example.Criteria criteria = example.createCriteria();
        LambdaQueryWrapper<StudentEventType> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(studentEventTypeDto)) {
            if (GeneralTool.isNotEmpty(studentEventTypeDto.getTypeName())) {
//                criteria.andLike("typeName", studentEventTypeDto.getTypeName());
                lambdaQueryWrapper.like(StudentEventType::getTypeName, studentEventTypeDto.getTypeName());
            }
        }
        lambdaQueryWrapper.orderByDesc(StudentEventType::getViewOrder);
        //获取分页数据
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
        IPage<StudentEventType> pages = studentEventTypeMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
        List<StudentEventType> studentEventTypes = pages.getRecords();
        page.setAll((int) pages.getTotal());
//        PageInfo<StudentEventType> pageInfo = new PageInfo<StudentEventType>(studentEventTypes);
//        page.setTotalResult(new Long(pageInfo.getTotal()).intValue());
        List<StudentEventTypeVo> convertDatas = new ArrayList<>();
        for (StudentEventType studentEventType : studentEventTypes) {
            StudentEventTypeVo studentEventTypeVo = BeanCopyUtils.objClone(studentEventType, StudentEventTypeVo::new);
            convertDatas.add(studentEventTypeVo);
        }
        return convertDatas;
    }

    @Override
    public StudentEventTypeVo updateStudentEventType(StudentEventTypeDto studentEventTypeDto) {
        if (studentEventTypeDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        StudentEventType rs = studentEventTypeMapper.selectById(studentEventTypeDto.getId());
        if (rs == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        StudentEventType studentEventType = BeanCopyUtils.objClone(studentEventTypeDto, StudentEventType::new);
        if (validateUpdate(studentEventTypeDto)) {
            utilService.updateUserInfoToEntity(studentEventType);
            studentEventTypeMapper.updateById(studentEventType);
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
        }
        return findStudentEventTypeById(studentEventType.getId());
    }

    @Override
    public Long addStudentEventType(StudentEventTypeDto studentEventTypeDto) {
        StudentEventType studentEventType = BeanCopyUtils.objClone(studentEventTypeDto, StudentEventType::new);
        if (validateAdd(studentEventTypeDto)) {
            studentEventType.setViewOrder(studentEventTypeMapper.getMaxViewOrder());
            utilService.updateUserInfoToEntity(studentEventType);
            studentEventTypeMapper.insert(studentEventType);
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));

        }
        return studentEventType.getId();
    }

    @Override
    public void delete(Long id) {
       // StudentEventType studentEventType = findStudentEventTypeById(id);
        StudentEventTypeVo studentEventType = findStudentEventTypeById(id);
        if (studentEventType == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        int i = studentEventTypeMapper.deleteById(studentEventType);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(List<StudentEventTypeDto> studentEventTypeDtos) {
        for (StudentEventTypeDto studentEventTypeDto : studentEventTypeDtos) {
            if (GeneralTool.isEmpty(studentEventTypeDto.getId())) {
                if (validateAdd(studentEventTypeDto)) {
                    StudentEventType studentEventType = BeanCopyUtils.objClone(studentEventTypeDto, StudentEventType::new);
                    studentEventType.setViewOrder(studentEventTypeMapper.getMaxViewOrder());
                    utilService.updateUserInfoToEntity(studentEventType);
                    studentEventTypeMapper.insert(studentEventType);
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
                }
            } else {
                if (validateUpdate(studentEventTypeDto)) {
                    StudentEventType studentEventType = BeanCopyUtils.objClone(studentEventTypeDto, StudentEventType::new);
                    utilService.updateUserInfoToEntity(studentEventType);
                    studentEventTypeMapper.updateById(studentEventType);
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
                }
            }
        }

    }

    @Override
    public void movingOrder(List<StudentEventTypeDto> studentEventTypeDtos) {
        if (GeneralTool.isEmpty(studentEventTypeDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        StudentEventType ro = BeanCopyUtils.objClone(studentEventTypeDtos.get(0), StudentEventType::new);
        Integer oneorder = ro.getViewOrder();
        StudentEventType rt = BeanCopyUtils.objClone(studentEventTypeDtos.get(1), StudentEventType::new);
        Integer twoorder = rt.getViewOrder();
        ro.setViewOrder(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setViewOrder(oneorder);
        utilService.updateUserInfoToEntity(rt);
        studentEventTypeMapper.updateById(ro);
        studentEventTypeMapper.updateById(rt);
    }

    @Override
    public List<BaseSelectEntity> getStudentEventTypeSelect() {
        return studentEventTypeMapper.getStudentEventTypeSelect();
    }

    private boolean validateUpdate(StudentEventTypeDto studentEventTypeDto) {
//        Example example = new Example(StudentEventType.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("typeName", studentEventTypeDto.getTypeName());
//        List<StudentEventType> list = this.studentEventTypeMapper.selectByExample(example);
        List<StudentEventType> list = this.studentEventTypeMapper.selectList(Wrappers.<StudentEventType>lambdaQuery().eq(StudentEventType::getTypeName, studentEventTypeDto.getTypeName()));
        return list.size() <= 0 || list.get(0).getId().equals(studentEventTypeDto.getId());
    }

    private boolean validateAdd(StudentEventTypeDto studentEventTypeDto) {
//        Example example = new Example(StudentEventType.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("typeName", studentEventTypeDto.getTypeName());
//        List<StudentEventType> list = this.studentEventTypeMapper.selectByExample(example);

        List<StudentEventType> list = this.studentEventTypeMapper.selectList(Wrappers.<StudentEventType>lambdaQuery().eq(StudentEventType::getTypeName, studentEventTypeDto.getTypeName()));

        return GeneralTool.isEmpty(list);
    }

    private boolean validateAdd(List<StudentEventTypeDto> studentEventTypeDtos) {
        boolean success = true;
        for (StudentEventTypeDto studentEventTypeDto : studentEventTypeDtos) {
//            Example example = new Example(StudentEventType.class);
//            Example.Criteria criteria = example.createCriteria();
//            criteria.andEqualTo("typeName", studentEventTypeDto.getTypeName());
//            List<StudentEventType> list = this.studentEventTypeMapper.selectByExample(example);
            List<StudentEventType> list = this.studentEventTypeMapper.selectList(Wrappers.<StudentEventType>lambdaQuery().eq(StudentEventType::getTypeName, studentEventTypeDto.getTypeName()));
            if (!GeneralTool.isEmpty(list)) {
                success = false;
            }
        }
        return success;
    }
}

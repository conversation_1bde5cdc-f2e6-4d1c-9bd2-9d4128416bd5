package com.get.salecenter.dao.newissue;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.entity.NewIssueStudentInstitutionCourse;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS("newissuedb")
public interface StudentInstitutionCourseMapper extends BaseMapper<NewIssueStudentInstitutionCourse>, GetMapper<NewIssueStudentInstitutionCourse> {
}
package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.salecenter.dao.sale.StudentServiceFeeTypeCompanyMapper;
import com.get.salecenter.entity.Student;
import com.get.salecenter.entity.StudentServiceFeeTypeCompany;
import com.get.salecenter.service.StudentServiceFeeTypeCompanyService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
public class StudentServiceFeeTypeCompanyServiceImpl extends ServiceImpl<StudentServiceFeeTypeCompanyMapper,StudentServiceFeeTypeCompany> implements StudentServiceFeeTypeCompanyService {

    @Resource
    private StudentServiceFeeTypeCompanyMapper studentServiceFeeTypeCompanyMapper;

    @Resource
    private UtilService<Object> utilService;

    /**
     * Author Cream
     * Description : //批量新增
     * Date 2023/2/3 15:29
     * Params:
     * Return
     */
    @Override
    public void batchInsert(List<StudentServiceFeeTypeCompany> studentServiceFeeTypeCompanies) {
        if (studentServiceFeeTypeCompanies.isEmpty()) {
            return;
        }
        saveBatch(studentServiceFeeTypeCompanies);
    }

    /**
     * Author Cream
     * Description : //单条新增
     * Date 2023/2/3 15:29
     * Params:
     * Return
     */
    @Override
    public void add(StudentServiceFeeTypeCompany studentServiceFeeTypeCompany) {
        if (Objects.isNull(studentServiceFeeTypeCompany)) {
            return;
        }
        utilService.setCreateInfo(studentServiceFeeTypeCompany);
        studentServiceFeeTypeCompanyMapper.insert(studentServiceFeeTypeCompany);
    }
}

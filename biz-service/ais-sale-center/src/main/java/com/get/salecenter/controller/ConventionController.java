package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.vo.AssignConventionOperatePersonListVo;
import com.get.salecenter.vo.ConventionVo;
import com.get.salecenter.entity.Convention;
import com.get.salecenter.service.IConventionService;
import com.get.salecenter.dto.*;
import com.get.salecenter.dto.ConventionDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @author: Sea
 * @create: 2020/6/30 17:37
 * @verison: 1.0
 * @description: 峰会管理控制器
 */
@Api(tags = "峰会管理")
@RestController
@RequestMapping("sale/convention")
public class ConventionController {

    @Resource
    private IConventionService conventionService;

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/峰会管理/峰会详情")
    @GetMapping("/{id}")
    public ResponseBo<ConventionVo> detail(@PathVariable("id") Long id) {
        ConventionVo data = conventionService.findConventionById(id);
        return new ResponseBo<>(data);
    }

    /**
     * 新增信息
     *
     * @param conventionDto
     * @return
     * @
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/峰会管理/新增峰会")
    @PostMapping("add")
    public ResponseBo add(@RequestBody  @Validated(ConventionDto.Add.class) ConventionDto conventionDto) {
        return SaveResponseBo.ok(this.conventionService.addConvention(conventionDto));
    }

    /**
     * 删除信息
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/峰会管理/删除峰会")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        this.conventionService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param conventionDto
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/峰会管理/更新峰会")
    @PostMapping("update")
    public ResponseBo<ConventionVo> update(@RequestBody @Validated(ConventionDto.Update.class) ConventionDto conventionDto) {
        return UpdateResponseBo.ok(conventionService.updateConvention(conventionDto));
    }

    /**
     * 列表数据
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "列表数据", notes = "themeName为主题名称 , year为年度")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/峰会管理/查询峰会")
    @PostMapping("datas")
    public ResponseBo<ConventionVo> datas(@RequestBody SearchBean<ConventionDto> page) {
        List<ConventionVo> datas = conventionService.getConventions(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);

    }

    /**
     * 保存文件
     *
     * @param mediaAttachedVo
     * @return
     * @
     */

    @ApiOperation(value = "保存文件")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/峰会管理/保存附件资料")
    @PostMapping("addMediaAndAttached")
    public ResponseBo addMediaAndAttached(@RequestBody @Validated(MediaAndAttachedDto.Add.class) MediaAndAttachedDto mediaAttachedVo) {
        return UpdateResponseBo.ok(conventionService.addConventionMedia(mediaAttachedVo));
    }

    /**
     * 峰会附件类型下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "峰会附件类型下拉框数据", notes = "")
    @PostMapping("findMediaAndAttachedType")
    public ResponseBo findMediaAndAttachedType() {
        List<Map<String, Object>> datas = conventionService.findMediaAndAttachedType();
        return new ListResponseBo<>(datas);
    }

    /**
     * 获取峰会主题名称
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "获取峰会主题名称", notes = "id为此条数据id")
    @GetMapping("getConventionNameById/{id}")
    public ResponseBo getConventionNameById(@PathVariable("id") Long id) {
        return new ResponseBo<>(conventionService.getConventionNameById(id));
    }


    /**
     * 关联活动下拉框(百度式搜索)
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "关联活动下拉框(百度式搜索)", notes = "")
    @GetMapping("getEventListByName")
    public ResponseBo<BaseSelectEntity> getEventListByName(@RequestParam("companyId") Long companyId, @RequestParam("eventName") String eventName) {
        List<BaseSelectEntity> datas = conventionService.getEventListByName(companyId,eventName);
        return new ListResponseBo<>(datas);
    }

    /**
     * 活动下拉框
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "活动下拉框", notes = "")
    @GetMapping("getEventSelect")
    public ResponseBo<BaseSelectEntity> getEventSelect(@RequestParam("companyId") Long companyId) {
        List<BaseSelectEntity> datas = conventionService.getEventSelect(companyId);
        return new ListResponseBo<>(datas);
    }

    /**
     * 列表数据
     *
     * @param conventionCopyDto
     * @return
     * @
     */
    @ApiOperation(value = "查询复制峰会活动峰会列表")
    @PostMapping("getEventCopyList")
    public ResponseBo<Convention> getEventCopyList(@RequestBody ConventionCopyDto conventionCopyDto) {
        List<Convention> datas = conventionService.getEventCopyList(conventionCopyDto);
        return new ListResponseBo<>(datas);

    }

    /**
     * 分配峰会操作人员权限
     * @param assignConventionOperatePersonDto
     * @return
     */
    @ApiOperation(value = "分配峰会操作人员权限")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/峰会管理/分配峰会操作人员权限")
    @PostMapping("assignConventionOperatePerson")
    public ResponseBo assignConventionOperatePerson(@RequestBody @Validated AssignConventionOperatePersonDto assignConventionOperatePersonDto) {
        conventionService.assignConventionOperatePerson(assignConventionOperatePersonDto);
        return ResponseBo.ok();

    }

    /**
     * 分配峰会操作人员列表
     * @param assignConventionOperatePersonListDto
     * @return
     */
    @ApiOperation(value = "分配峰会操作人员列表")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/峰会管理/分配峰会操作人员列表")
    @PostMapping("getAssignConventionOperatePersonList")
    public ResponseBo<AssignConventionOperatePersonListVo> getAssignConventionOperatePersonList(@RequestBody @Validated AssignConventionOperatePersonListDto assignConventionOperatePersonListDto) {
        List<AssignConventionOperatePersonListVo> assignConventionOperatePersonListVos = conventionService.getAssignConventionOperatePersonList(assignConventionOperatePersonListDto);
        return new ListResponseBo<>(assignConventionOperatePersonListVos);
    }


    /**
     * 删除峰会操作人员
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除峰会操作人员", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/峰会管理/删除峰会操作人员")
    @PostMapping("deleteAssignConventionOperatePerson/{id}")
    public ResponseBo deleteAssignConventionOperatePerson(@PathVariable("id") Long id) {
        conventionService.deleteAssignConventionOperatePerson(id);
        return DeleteResponseBo.ok();
    }

    /**
     * 登陆人是否有操作峰会权限
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "登陆人是否有操作峰会权限", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/峰会管理/登陆人是否有操作峰会权限")
    @PostMapping("hasConventionOperatePermission")
    public ResponseBo<Boolean> hasConventionOperatePermission(@RequestParam("conventionId")Long conventionId) {
        return new ResponseBo(conventionService.hasConventionOperatePermission(conventionId));
    }



    /**
     * 峰会下拉-复制峰会权限
     * @param companyId
     * @return
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "峰会下拉-复制峰会权限", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/峰会管理/峰会下拉-复制峰会权限")
    @GetMapping("getConventionWithPermissionConfigSelect")
    public ResponseBo<BaseSelectEntity> getConventionWithPermissionConfigSelect(@RequestParam("companyId") Long companyId) {
        return new ListResponseBo<>(conventionService.getConventionWithPermissionConfigSelect(companyId));
    }



    /**
     * 复制峰会操作人员权限
     * @return
     */
    @ApiOperation(value = "复制峰会操作人员权限",notes = "sourceConventionId下拉框的峰会id/被复制的峰会id，targetConventionId复制的峰会id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/峰会管理/复制峰会操作人员权限")
    @PostMapping("copyConventionOperatePersonConfig")
    public ResponseBo copyConventionOperatePersonConfig(@RequestParam("sourceConventionId") Long sourceConventionId,@RequestParam("targetConventionId") Long targetConventionId) {
        conventionService.copyConventionOperatePersonConfig(sourceConventionId,targetConventionId);
        return ResponseBo.ok();

    }


    /**
     * 权限模式下拉
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "权限模式下拉", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/峰会管理/权限模式下拉")
    @PostMapping("getConventionStaffModeSelect")
    public ResponseBo getConventionStaffModeSelect() {
        return new ListResponseBo<>(ProjectExtraEnum.enumsTranslation2Arrays(ProjectExtraEnum.CONVENTION_STAFF_MODE_TYPE));
    }
}

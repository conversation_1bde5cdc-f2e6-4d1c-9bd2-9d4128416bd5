package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.vo.AgentContractAccountVo;
import com.get.salecenter.vo.MediaAndAttachedVo;
import com.get.salecenter.vo.PayablePlanSettlementAgentAccountVo;
import com.get.salecenter.service.IAgentContractAccountService;
import com.get.salecenter.dto.AgentContractAccountDto;
import com.get.salecenter.dto.MediaAndAttachedDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE: 2020/9/15
 * @TIME: 10:02
 * @Description: 代理账户管理
 **/

@Api(tags = "代理账户管理")
@RestController
@RequestMapping("sale/agentContractAccount")
public class AgentContractAccountController {

    @Resource
    private IAgentContractAccountService agentContractAccountService;

    /**
     * @return com.get.common.result.ResponseBo
     * @Description：列表数据
     * @Param [page]
     * <AUTHOR>
     **/
    @ApiOperation(value = "账户列表数据", notes = "keyWord为关键词")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/代理账户管理/账户列表")
    @PostMapping("datas")
    public ResponseBo datas(@RequestBody SearchBean<AgentContractAccountDto> page) {
        List<AgentContractAccountVo> datas = agentContractAccountService.getAgentContractAccount(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/代理账户管理/详情")
    @GetMapping("/{id}")
    public ResponseBo<AgentContractAccountVo> detail(@PathVariable("id") Long id) {
        AgentContractAccountVo accountDto = agentContractAccountService.findAgentContractAccountById(id);
        return new ResponseBo<>(accountDto);
    }

    /**
     * 新增信息
     *
     * @param accountVo
     * @return
     * @
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/代理账户管理/新增事件")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(AgentContractAccountDto.Add.class) AgentContractAccountDto accountVo) {
        return SaveResponseBo.ok(agentContractAccountService.addContractAccount(accountVo));
    }
    @ApiOperation(value = "获取合同账户卡类型", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/代理账户管理/获取合同账户卡类型")
    @VerifyLogin(IsVerify = false)
    @GetMapping("getCardType")
    public ListResponseBo<Map<String, Object>> getCardType() {
        return new ListResponseBo<>(ProjectExtraEnum.enumsTranslation2Arrays(ProjectExtraEnum.cardType));
    }

    /**
     * 修改信息
     *
     * @param accountVo
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/代理账户管理/更新事件")
    @PostMapping("update")
    public ResponseBo<AgentContractAccountVo> update(@RequestBody  @Validated(AgentContractAccountDto.Update.class) AgentContractAccountDto accountVo) {
        return UpdateResponseBo.ok(agentContractAccountService.updateContractAccount(accountVo));
    }

    /**
     * 删除信息
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/代理账户管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        agentContractAccountService.delete(id);
        return DeleteResponseBo.ok();
    }

    //    /**
//     * 根据代理ids 获取银行账户
//     *
//     * @Date 16:55 2021/12/22
//     * <AUTHOR>
//     * @return
//     */
//    @ApiIgnore
//    @PostMapping("getAgentContractAccountByAgentIds")
//    public Map<Long, List<AgentContractAccount>> getAgentContractAccountByAgentIds(@RequestBody List<Long> agentIds) {
//        return agentContractAccountService.getAgentContractAccountByAgentIds(agentIds);
//    }

//    /**
//     * 根据应付计划ids 获取结算标记
//     *
//     * @Date 12:07 2022/1/11
//     * <AUTHOR>
//     */
//    @ApiIgnore
//    @PostMapping("getSettlementMarkByPayablePlanIds")
//    public Map<Long, List<PayablePlanSettlementAgentAccountVo>> getSettlementMarkByPayablePlanIds(@RequestBody List<Long> payablePlanIds) {
//        return agentContractAccountService.getSettlementMarkByPayablePlanIds(payablePlanIds);
//    }

    /**
     * feign 根据代理银行账号ids获取银行账号信息
     *
     * @Date 16:42 2022/1/6
     * <AUTHOR>
     */
    @ApiIgnore
    @PostMapping("getAgentContractAccountByAccountIds")
    public Map<Long, AgentContractAccountVo> getAgentContractAccountByAccountIds(@RequestBody List<Long> accountIds) {
        return agentContractAccountService.getAgentContractAccountByAccountIds(accountIds);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 代理银行账户下拉框
     * @Param [bankAccountId, isActive]
     * <AUTHOR>
     **/
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "银行账户下拉框", notes = "")
    @PostMapping("getContractAccountSelect")
    public ResponseBo<BaseSelectEntity> getContractAccountSelect(@RequestParam("fkTypeKey") String fkTypeKey,@RequestParam("fkTargetId") Long fkTargetId) {
        return new ListResponseBo<>(agentContractAccountService.getContractAccountSelect(fkTypeKey,fkTargetId));
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "代理银行账户下拉框2", notes = "")
    @GetMapping("getAgentContractAccountSelectById/{fkAgentId}")
    public ResponseBo<AgentContractAccountVo> getAgentContractAccountSelectById(@PathVariable("fkAgentId")Long fkAgentId) {
        return new ListResponseBo<>(agentContractAccountService.getAgentContractAccountSelectById(fkAgentId));
    }

    /**
     * feign调用，获取代理账户名称
     *
     * @param id
     * @return
     */
    @ApiIgnore
    @GetMapping("getAgentContractBankAccountNameById/{id}")
    public String getAgentContractBankAccountNameById(@PathVariable("id") Long id) {
        return agentContractAccountService.getAgentContractBankAccountNameById(id);
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "代理合同账户列表账户重复提示", notes = "")
    @GetMapping("getAgentContractAccountExist")
    public ResponseBo<String> getAgentContractAccountExist(@RequestParam(value = "id", required = false) Long id,
                                                           @RequestParam("companyId") Long companyId, @RequestParam(value = "bankAccount", required = false) String bankAccount,
                                                           @RequestParam(value = "bankAccountNum", required = false) String bankAccountNum) {
        return new ResponseBo<>(agentContractAccountService.getAgentContractAccountExist(id, companyId, bankAccount, bankAccountNum));
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "快捷首选合同", notes = "")
    @GetMapping("quickFirstContractAccount")
    public ResponseBo<Void> quickFirstContractAccount(@RequestParam("agentId") Long agentId,@RequestParam("accountId")Long accountId) {
        return agentContractAccountService.quickFirstContractAccount(agentId,accountId);
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "快速激活或屏蔽合同账户", notes = "")
    @GetMapping("quickActivationOrMask")
    public ResponseBo<Void> quickActivationOrMask(@RequestParam("agentId") Long agentId,@RequestParam("accountId")Long accountId,@RequestParam("status") Boolean status) {
        return agentContractAccountService.quickActivationOrMask(agentId,accountId,status);
    }



    /**
     * 代理附件保存接口
     *
     * @param mediaAttachedVos
     * @return
     * @
     */
    @VerifyLogin(IsVerify = false)
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "代理账户附件保存接口")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/代理账户管理/代理账户附件保存接口")
    @PostMapping("upload")
    public ResponseBo<MediaAndAttachedVo> addMedia(@RequestBody  @Validated(MediaAndAttachedDto.Add.class)  ValidList<MediaAndAttachedDto> mediaAttachedVos) {
        return new ListResponseBo<>(agentContractAccountService.addMedia(mediaAttachedVos));
    }

}

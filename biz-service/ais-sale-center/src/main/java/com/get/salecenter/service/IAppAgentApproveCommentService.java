package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.service.GetService;
import com.get.salecenter.vo.AppAgentApproveCommentVo;
import com.get.salecenter.entity.AppAgentApproveComment;
import com.get.salecenter.dto.AppAgentApproveCommentDto;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @author: Hardy
 * @create: 2023/3/31 10:42
 * @verison: 1.0
 * @description:
 */
public interface IAppAgentApproveCommentService extends GetService<AppAgentApproveComment> {

    /**
     * 保存代理申请审批意见
     * @param appAgentApproveCommentDto
     * @return
     */
    Long addAppAgentApproveCommentVo(AppAgentApproveCommentDto appAgentApproveCommentDto);

    /**
     * 发送邮件
     * @param id
     */
    void sendEmail(Long id);

    /**
     * 发送邮件（加锁控制重复发送）
     * @param id
     */
    void sendEmailByLock(Long id);

    /**
     * 保存并发送邮件
     * @param appAgentApproveCommentDto
     * @return
     */
    Long saveAndSendEmail(AppAgentApproveCommentDto appAgentApproveCommentDto);

    Long saveCommentAndSendRejectEmail(AppAgentApproveCommentDto appAgentApproveCommentDto);

    /**
     * 删除
     * @param id
     */
    void delete(Long id);

    /**
     * 列表
     * @param appAgentApproveCommentDto
     * @param page
     * @return
     */
    List<AppAgentApproveCommentVo> getAppAgentApproveComments(AppAgentApproveCommentDto appAgentApproveCommentDto, Page page);

    void sendEmailUnified(Long appAgentApproveCommentId);

    /**
     * 更新审批意见的邮件发送时间和人
     *
     * @param appAgentApproveCommentId 审批意见ID
     */
    void updateEmailTime(Long appAgentApproveCommentId);
}

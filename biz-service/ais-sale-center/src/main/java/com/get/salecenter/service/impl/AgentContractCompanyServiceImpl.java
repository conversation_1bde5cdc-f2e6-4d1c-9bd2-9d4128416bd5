package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.AgentContractCompanyMapper;
import com.get.salecenter.entity.AgentContractCompany;
import com.get.salecenter.service.IAgentCompanyService;
import com.get.salecenter.service.IAgentContractCompanyService;
import com.get.salecenter.dto.AgentContractCompanyDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/9/15
 * @TIME: 15:24
 * @Description:
 **/
@Service
public class AgentContractCompanyServiceImpl implements IAgentContractCompanyService {
    @Resource
    private AgentContractCompanyMapper companyMapper;
    @Resource
    private IAgentCompanyService agentCompanyService;
    @Resource
    private UtilService utilService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editAgentContractCompany(List<AgentContractCompanyDto> contractCompanyVo) {
        if (GeneralTool.isEmpty(contractCompanyVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        //合同id
        Long contractId = contractCompanyVo.get(0).getFkAgentContractId();
        Long companyId = contractCompanyVo.get(0).getFkCompanyId();
        if (GeneralTool.isEmpty(companyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("edit_companyId"));
        }
        List<AgentContractCompany> collect = contractCompanyVo.stream().map(contractCompanyVo1 ->
                BeanCopyUtils.objClone(contractCompanyVo1, AgentContractCompany::new)).collect(Collectors.toList());
        //合同对应代理的关联公司ids
        List<Long> companyIds = agentCompanyService.getAgentCompanyIdByContractId(contractId);
        //合同关联公司id
        List<Long> contractCompanyIds = collect.stream().map(AgentContractCompany::getFkCompanyId).collect(Collectors.toList());
        contractCompanyIds.removeIf(Objects::isNull);
        if (GeneralTool.isNotEmpty(contractCompanyIds)) {
            if (!companyIds.containsAll(contractCompanyIds)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("agent_not_configure_company"));
            }
        }
        //删除之前的记录
//        Example example = new Example(AgentContractCompany.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkAgentContractId", contractId);
//        companyMapper.deleteByExample(example);
        List<Long> companyIds1 = SecureUtil.getCompanyIds();
        companyMapper.delete(Wrappers.<AgentContractCompany>lambdaQuery().eq(AgentContractCompany::getFkAgentContractId, contractId).in(AgentContractCompany::getFkCompanyId,companyIds1));

        collect.forEach(agentContractCompany -> companyMapper.insertSelective(agentContractCompany));
    }

    @Override
    public Long addRelation(AgentContractCompanyDto relation) {
        AgentContractCompany contractCompany = BeanCopyUtils.objClone(relation, AgentContractCompany::new);
        utilService.updateUserInfoToEntity(contractCompany);
        companyMapper.insert(contractCompany);
        return contractCompany.getId();
    }

    @Override
    public List<Long> getRelationByCompanyId(List<Long> companyIds) {
        if (GeneralTool.isEmpty(companyIds)) {
            return null;
        }
//        Example example = new Example(AgentContractCompany.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andIn("fkCompanyId", companyIds);
//        List<AgentContractCompany> agentContractCompanies = companyMapper.selectByExample(example);

        List<AgentContractCompany> agentContractCompanies = companyMapper.selectList(Wrappers.<AgentContractCompany>lambdaQuery().in(AgentContractCompany::getFkCompanyId, companyIds));

        if (GeneralTool.isEmpty(agentContractCompanies)) {
            return null;
        }
        return agentContractCompanies.stream().map(AgentContractCompany::getFkAgentContractId).collect(Collectors.toList());
    }

    @Override
    public List<Long> getRelationByContractId(Long contractId) {
        if (GeneralTool.isEmpty(contractId)) {
            return null;
        }
//        Example example = new Example(AgentContractCompany.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkAgentContractId", contractId);
//        List<AgentContractCompany> agentContractCompanies = companyMapper.selectByExample(example);
        List<AgentContractCompany> agentContractCompanies = companyMapper.selectList(Wrappers.<AgentContractCompany>lambdaQuery().eq(AgentContractCompany::getFkAgentContractId, contractId));
        if (GeneralTool.isEmpty(agentContractCompanies)) {
            return null;
        }

        return agentContractCompanies.stream().map(AgentContractCompany::getFkCompanyId).collect(Collectors.toList());
    }


    @Override
    public Map<Long, Set<Long>> getRelationByContractIds(Set<Long> contractIds) {
        Map<Long, Set<Long>> map = new HashMap<>();
        if (GeneralTool.isEmpty(contractIds)) {
            return map;
        }
//        Example example = new Example(AgentContractCompany.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andIn("fkAgentContractId", contractIds);
//        List<AgentContractCompany> agentContractCompanies = companyMapper.selectByExample(example);

        List<AgentContractCompany> agentContractCompanies = companyMapper.selectList(Wrappers.<AgentContractCompany>lambdaQuery().in(AgentContractCompany::getFkAgentContractId, contractIds));

        if (GeneralTool.isEmpty(agentContractCompanies)) {
            return map;
        }
        for (AgentContractCompany agentContractCompany : agentContractCompanies) {
            //如果当前代理合同id已经在集合中存在，则往原来的集合里面添加元素
            if (map.containsKey(agentContractCompany.getFkAgentContractId())) {
                Set<Long> beforeCompanyIds = map.get(agentContractCompany.getFkAgentContractId());
                beforeCompanyIds.add(agentContractCompany.getFkCompanyId());
                map.put(agentContractCompany.getFkAgentContractId(), beforeCompanyIds);
                continue;
            }
            //如果不存在，则重新添加新元素
            Set<Long> companyIds = new HashSet<>();
            companyIds.add(agentContractCompany.getFkCompanyId());
            map.put(agentContractCompany.getFkAgentContractId(), companyIds);
        }
        return map;
    }

    @Override
    public Map<Long, Long> getAgentContractCompanyId(List<Long> contractIds) {
        if (GeneralTool.isEmpty(contractIds)) {
            return Collections.emptyMap();
        }
        List<AgentContractCompany> agentContractCompanies = companyMapper.selectList(Wrappers.<AgentContractCompany>lambdaQuery().in(AgentContractCompany::getFkAgentContractId, contractIds));
        return agentContractCompanies.stream().collect(HashMap<Long, Long>::new, (m, v) -> m.put(v.getFkAgentContractId(), v.getFkCompanyId()), HashMap::putAll);
    }
}

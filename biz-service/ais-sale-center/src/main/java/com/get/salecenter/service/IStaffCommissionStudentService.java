package com.get.salecenter.service;

import com.get.core.mybatis.service.GetService;
import com.get.salecenter.entity.StaffCommissionStudent;
import com.get.salecenter.dto.StaffCommissionStudentDto;

import java.util.List;

/**
 * @author: Hardy
 * @create: 2023/2/7 15:50
 * @verison: 1.0
 * @description:
 */
public interface IStaffCommissionStudentService extends GetService<StaffCommissionStudent> {
    /**
     * 新增
     * @param staffCommissionStudentDto
     * @return
     */
    Long addStaffCommissionStudent(StaffCommissionStudentDto staffCommissionStudentDto);

    /**
     * 批量新增
     * @param staffCommissionStudentDtos
     */
    void batchAdd(List<StaffCommissionStudentDto> staffCommissionStudentDtos);

    /**
     * 删除
     * @param id
     */
    void delete(Long id);

    /**
     * 合并是否计算业绩关系数据
     * @param mergedStudentId
     * @param targetStudentId
     */
    void mergeData(Long mergedStudentId, Long targetStudentId);
}

package com.get.salecenter.dao.oldissue;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.entity.AgencyCompanyHasAgencyUsers;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2022/3/17
 * @TIME: 12:42
 * @Description:
 **/
@Mapper
@DS("oldissuedb")
public interface AgencyCompanyHasAgencyUsersMapper extends BaseMapper<AgencyCompanyHasAgencyUsers> {

    List<Long> getAllCppAgencyId(@Param("idGeas") Set<String> idGeas);

    List<Long> getAllBmsAgencyId(@Param("ids") Set<Long> ids);
}

package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.UpdateResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.vo.ConventionAwardVo;
import com.get.salecenter.vo.RecordAwardVo;
import com.get.salecenter.service.IConventionAwardService;
import com.get.salecenter.service.IMediaAndAttachedService;
import com.get.salecenter.dto.ConventionAwardDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE: 2021/9/13
 * @TIME: 11:52
 * @Description:
 **/
@Api(tags = "奖品管理")
@RestController
@RequestMapping("sale/conventionAward")
public class ConventionAwardController {
    @Resource
    private IConventionAwardService conventionAwardService;

    @Resource
    private IMediaAndAttachedService mediaAndAttachedService;

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.EventTypeVo>
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/奖品管理/奖品详情")
    @GetMapping("/{id}")
    public ResponseBo<ConventionAwardVo> detail(@PathVariable("id") Long id) {
        ConventionAwardVo data = conventionAwardService.findConventionAwardById(id);
        return new ResponseBo<>(data);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :批量新增信息
     * @Param [eventTypeVos]
     * <AUTHOR>
     */
    @ApiOperation(value = "批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/奖品管理/批量新增奖品")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody @Validated(ConventionAwardDto.Add.class)  ValidList<ConventionAwardDto> conventionAwardDtos) {
        conventionAwardService.batchAdd(conventionAwardDtos);
        return SaveResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :删除信息
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/奖品管理/删除奖品")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        conventionAwardService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.ConventionAwardVo>
     * @Description :修改信息
     * @Param [ConventionAwardDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/奖品管理/更新奖品")
    @PostMapping("update")
    public ResponseBo<ConventionAwardVo> update(@RequestBody  @Validated(ConventionAwardDto.Update.class) ConventionAwardDto conventionAwardDto) {
        return UpdateResponseBo.ok(conventionAwardService.updateConventionAward(conventionAwardDto));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.ConventionAwardVo>
     * @Description :列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/奖品管理/查询奖品")
    @PostMapping("datas")
    public ResponseBo<ConventionAwardVo> datas(@RequestBody ConventionAwardDto page) {
        List<ConventionAwardVo> datas = conventionAwardService.getConventionAwards(page);
        return new ListResponseBo<>(datas);
    }

    @ApiOperation(value = "排序（拖拽）", notes = "传入拖拽后的列表")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/奖品管理/排序（拖拽）")
    @PostMapping("dragMovingOrder")
    public ResponseBo dragMovingOrder(@RequestParam("end")Integer end,@RequestParam("start")Integer start) {
        conventionAwardService.dragMovingOrder(end,start);
        return ResponseBo.ok();

    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :上移下移
     * @Param [conventionAwardDtos]
     * <AUTHOR>
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/奖品管理/移动顺序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<ConventionAwardDto> conventionAwardDtos) {
        conventionAwardService.movingOrder(conventionAwardDtos);
        return ResponseBo.ok();
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "上传奖品图片")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/奖品管理/上传图片")
    @PostMapping("/upload")
    public ResponseBo upload(@RequestParam("files") @NotNull(message = "文件不能为空") MultipartFile[] files) {
        ResponseBo responseBo = new ResponseBo();
        responseBo.put("data", mediaAndAttachedService.upload(files, LoggerModulesConsts.SALECENTER));
        return responseBo;
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 代理附件类型
     * @Param []
     * <AUTHOR>
     */
    @ApiOperation(value = "获取角色类型", notes = "")
    @GetMapping("findRolesType")
    @VerifyPermission(IsVerify = false)
    public ResponseBo findRolesType() {
        List<Map<String, Object>> datas = conventionAwardService.findRolesType();
        return new ListResponseBo<>(datas);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 获取奖品，大屏幕显示
     * @Param []
     * <AUTHOR>
     */
    @ApiOperation(value = "获取奖品，大屏幕显示", notes = "")
    @GetMapping("listAwardsAndTickets/{id}")
    @VerifyLogin(IsVerify = false)
    @VerifyPermission(IsVerify = false)
    public ResponseBo listAwardsAndTickets(@PathVariable("id") Long id) {
        List<RecordAwardVo> recordAwardVos = conventionAwardService.listAwardsAndTickets(id);
        return new ListResponseBo<>(recordAwardVos);
    }
}

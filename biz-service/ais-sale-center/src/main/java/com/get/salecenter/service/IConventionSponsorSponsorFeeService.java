package com.get.salecenter.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.get.salecenter.vo.ConventionSponsorFeeVo;
import com.get.salecenter.entity.ConventionSponsorSponsorFee;
import com.get.salecenter.dto.ConventionSponsorSponsorFeeDto;

import java.util.List;

/**
 * @author: Sea
 * @create: 2021/5/8 14:13
 * @verison: 1.0
 * @description:
 */
public interface IConventionSponsorSponsorFeeService extends IService<ConventionSponsorSponsorFee> {

    /**
     * @return java.util.List<com.get.salecenter.vo.ConventionSponsorFeeVo>
     * @Description :根据赞助商id查找所选赞助类型
     * @Param [sponsorId]
     * <AUTHOR>
     */
    List<ConventionSponsorFeeVo> getSponsorFeeDtoList(Long sponsorId);

    /**
     * @return java.util.List<com.get.salecenter.vo.ConventionSponsorFeeVo>
     * @Description :根据回执码查找所选赞助类型
     * @Param [conventionId, receiptCode]
     * <AUTHOR>
     */
    List<ConventionSponsorFeeVo> getSponsorFeeDtoListByReceiptCode(Long conventionId, String receiptCode);

    /**
     * @return void
     * @Description :批量新增
     * @Param [conventionSponsorSponsorFeeDtos]
     * <AUTHOR>
     */
    void batchAdd(List<ConventionSponsorSponsorFeeDto> conventionSponsorSponsorFeeDtos);

    /**
     * @return void
     * @Description :验证要新增得赞助类型是否售完
     * @Param [newSponsorFeeIds, sponsorId]
     * <AUTHOR>
     */
    void validateAdd(List<Long> newSponsorFeeIds, Long sponsorId);

    /**
     * @return void
     * @Description :根据赞助商id删除关联的赞助类型数据
     * @Param [sponsorId]
     * <AUTHOR>
     */
    void deleteByFkid(Long sponsorId);

    /**
     * @return java.lang.Boolean
     * @Description :赞助次数是否剩余
     * @Param [sponsorFeeId, initNum]
     * <AUTHOR>
     */
    Boolean soldOut(Long sponsorFeeId, Integer initNum, Long sponsorId);
}

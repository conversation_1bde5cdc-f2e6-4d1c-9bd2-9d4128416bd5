package com.get.salecenter.dao.convention;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.entity.GoproRegistration;
import com.get.salecenter.dto.GoproRegistrationDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
@DS("conventiondb")
public interface GoproRegistrationMapper extends GetMapper<GoproRegistration> {

    int getCountSeatUsed(@Param("type") String type,@Param("year") int year);

    int getUsedByTypeAndId(@Param("type") String type,@Param("id") Long id,@Param("year") int year);

    List<GoproRegistrationDto> getIsExistMobile(@Param("mobile") String mobile, @Param("year") int year);

    List<GoproRegistrationDto> getGoProList(IPage<GoproRegistrationDto> iPage, @Param("company") String company, @Param("year") Integer year, @Param("name") String name,
                                            @Param("mobile") String mobile, @Param("bdName") String bdName, @Param("bdRegion") String bdRegion, @Param("str") String str
            , @Param("str1") String str1, @Param("str2") String str2, @Param("str3") String str3, @Param("fkStaffId") Long fkStaffId, @Param("isExclude")boolean isExclude);

    List<Map<String,Object>> getSeatCountList();

    void updateByPrimaryKey(GoproRegistration goproRegistration);

    void deleteById(@Param("id") Long id);

    List<BaseSelectEntity> getYears();
}
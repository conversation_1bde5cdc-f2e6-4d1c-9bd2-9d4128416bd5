package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.salecenter.dao.sale.AgentContractFormulaCourseTypeMapper;
import com.get.salecenter.entity.AgentContractFormulaCourseType;
import com.get.salecenter.service.IAgentContractFormulaCourseTypeService;
import com.get.salecenter.dto.AgentContractFormulaCourseTypeDto;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * @author: Sea
 * @create: 2021/1/6 15:11
 * @verison: 1.0
 * @description:
 */
@Service
public class AgentContractFormulaCourseTypeServiceImpl implements IAgentContractFormulaCourseTypeService {
    @Resource
    private AgentContractFormulaCourseTypeMapper agentContractFormulaCourseTypeMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;

    @Override
    public Long addAgentContractFormulaCourseType(AgentContractFormulaCourseTypeDto agentContractFormulaCourseTypeDto) {
        AgentContractFormulaCourseType agentContractFormulaCourseType = BeanCopyUtils.objClone(agentContractFormulaCourseTypeDto, AgentContractFormulaCourseType::new);
        utilService.updateUserInfoToEntity(agentContractFormulaCourseType);
        agentContractFormulaCourseTypeMapper.insertSelective(agentContractFormulaCourseType);
        return agentContractFormulaCourseType.getId();
    }

    @Override
    public void deleteByFkid(Long agentContractFormulaId) {
//        Example example = new Example(AgentContractFormulaCourseType.class);
//        example.createCriteria().andEqualTo("fkAgentContractFormulaId", agentContractFormulaId);
//        agentContractFormulaCourseTypeMapper.deleteByExample(example);
        agentContractFormulaCourseTypeMapper.delete(Wrappers.<AgentContractFormulaCourseType>lambdaQuery().eq(AgentContractFormulaCourseType::getFkAgentContractFormulaId, agentContractFormulaId));
    }

    @Override
    public Map<Long, String> getCourseTypeNameMapByFkids(List<Long> agentContractFormulaIds) {
        //关系map
        Map<Long, List<Long>> idMap = new HashMap<>();
        Map<Long, String> nameMap = new HashMap<>();
        //全部courseTypeId集合
        Set<Long> courseTypeIdSet = new HashSet<>();
        for (Long agentContractFormulaId : agentContractFormulaIds) {
            //通过agentContractFormulaId获取对应所有国家id
            List<Long> courseTypeIds = getCourseTypeIdListByFkid(agentContractFormulaId);
            courseTypeIdSet.addAll(courseTypeIds);
            //agentContractFormulaId和courseTypeIds一一对应关系map
            idMap.put(agentContractFormulaId, courseTypeIds);
        }
        courseTypeIdSet.removeIf(Objects::isNull);
        //feign调用一次查出 courseTypeId和courseTypeName对应关系map
        Map<Long, String> courseTypeNameMap = new HashMap<>();
        Result<Map<Long, String>> result = institutionCenterClient.getCourseTypeNamesByIds(courseTypeIdSet);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            courseTypeNameMap = result.getData();
            //map由agentContractFormulaId 对应 courseTypeIds 转成 agentContractFormulaId 对应 courseTypeNames
            for (Map.Entry<Long, List<Long>> agentContractFormula : idMap.entrySet()) {
                List<String> courseTypeNames = new ArrayList<>();
                List<Long> courseTypeIds = agentContractFormula.getValue();
                for (Long courseTypeId : courseTypeIds) {
                    courseTypeNames.add(courseTypeNameMap.get(courseTypeId));
                }
                nameMap.put(agentContractFormula.getKey(), StringUtils.join(courseTypeNames, "，"));
            }
        }
        return nameMap;
    }

    @Override
    public List<Long> getCourseTypeIdListByFkid(Long agentContractFormulaId) {
        return agentContractFormulaCourseTypeMapper.getCourseTypeIdsByFkid(agentContractFormulaId);
    }
}

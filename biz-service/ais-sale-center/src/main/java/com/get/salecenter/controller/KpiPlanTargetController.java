package com.get.salecenter.controller;


import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.salecenter.service.KpiPlanTargetService;
import com.get.salecenter.dto.KpiPlanTargetDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "KPI目标设置管理")
@RestController
@RequestMapping("sale/kpiPlanTarget")
public class KpiPlanTargetController {

    @Resource
    private KpiPlanTargetService kpiPlanTargetService;

    @ApiOperation(value = "新增", notes = "")
    @PostMapping("add")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/KPI目标设置管理/新增")
    public ResponseBo add(@RequestBody @Validated(KpiPlanTargetDto.Add.class)  KpiPlanTargetDto kpiPlanTargetDto) {
        kpiPlanTargetService.addKpiPlanTarget(kpiPlanTargetDto);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "批量设置KPI", notes = "")
    @PostMapping("batchAdd")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/KPI目标设置管理/批量设置KPI")
    public ResponseBo batchAdd(@RequestBody  @Validated(KpiPlanTargetDto.Add.class)  List<KpiPlanTargetDto> kpiPlanTargetDtos) {
        kpiPlanTargetService.batchAdd(kpiPlanTargetDtos);
        return ResponseBo.ok();
    }

}

package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.common.eunms.TableEnum;
import com.get.common.result.ResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.PayablePlanMapper;
import com.get.salecenter.dao.sale.ReceivablePlanMapper;
import com.get.salecenter.dao.sale.StudentOfferItemDeferEntranceTimeMapper;
import com.get.salecenter.dao.sale.StudentOfferItemMapper;
import com.get.salecenter.vo.ReceivableAndPayablePlanVo;
import com.get.salecenter.vo.StudentOfferItemVo;
import com.get.salecenter.entity.PayablePlan;
import com.get.salecenter.entity.ReceivablePlan;
import com.get.salecenter.entity.StudentOfferItem;
import com.get.salecenter.entity.StudentOfferItemDeferEntranceTime;
import com.get.salecenter.service.IIncentivePolicyStudentOfferItemService;
import com.get.salecenter.service.IPayablePlanService;
import com.get.salecenter.service.IReceivableAndPayablePlanService;
import com.get.salecenter.service.IReceivablePlanService;
import com.get.salecenter.service.IStudentOfferItemService;
import com.get.salecenter.dto.*;
import com.get.salecenter.dto.EventOfferPlanDto;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * @author: Hardy
 * @create: 2021/12/8 12:02
 * @verison: 1.0
 * @description:
 */
@Service
public class ReceivableAndPayablePlanServiceImpl implements IReceivableAndPayablePlanService {

    @Resource
    @Lazy
    private IReceivablePlanService receivablePlanService;

    @Resource
    private StudentOfferItemDeferEntranceTimeMapper studentOfferItemDeferEntranceTimeMapper;

    @Resource
    @Lazy
    private IPayablePlanService payablePlanService;

    @Resource
    @Lazy
    private IIncentivePolicyStudentOfferItemService incentivePolicyStudentOfferItemService;

    @Resource
    private UtilService<Object> utilService;

    @Resource
    private StudentOfferItemMapper studentOfferItemMapper;
    @Resource
    private IStudentOfferItemService studentOfferItemService;
    @Resource
    private ReceivablePlanMapper receivablePlanMapper;
    @Resource
    private PayablePlanMapper payablePlanMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addReceivableAndPayablePlan(ReceivableAndPayablePlanDto receivableAndPayablePlanDto) {
        ReceivablePlanDto receivablePlanDto = receivableAndPayablePlanDto.getReceivablePlanVo();
        if (GeneralTool.isNotEmpty(receivablePlanDto)) {
            Long receivablePlanId = receivablePlanService.addReceivablePlan(receivablePlanDto);
            if (receivableAndPayablePlanDto.getFlag()){
                StudentOfferItem studentOfferItem = studentOfferItemMapper.selectById(receivablePlanDto.getFkTypeTargetId());
                studentOfferItem.setTuitionAmount(receivablePlanDto.getTuitionAmount());
                utilService.updateUserInfoToEntity(studentOfferItem);
                studentOfferItemMapper.updateById(studentOfferItem);
            }
            PayablePlanDto payablePlanDto = receivableAndPayablePlanDto.getPayablePlanVo();
            payablePlanDto.setFkReceivablePlanId(receivablePlanId);
            payablePlanService.addPayablePlan(payablePlanDto);
            updateOfferItemOpenTime(receivablePlanDto);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addReceivableAndPayablePlans(List<IncentiveReceivableAndPayablePlanDto> incentiveReceivableAndPayablePlanDtoList) {
        for(IncentiveReceivableAndPayablePlanDto incentiveReceivableAndPayablePlanDto : incentiveReceivableAndPayablePlanDtoList)
        {
            //应收
            ReceivablePlanDto receivablePlanDto = incentiveReceivableAndPayablePlanDto.getReceivablePlanVo();
            if (GeneralTool.isNotEmpty(receivablePlanDto)) {
                //应收id
                Long receivablePlanId = receivablePlanService.addReceivablePlan(receivablePlanDto);
                PayablePlanDto payablePlanDto = incentiveReceivableAndPayablePlanDto.getPayablePlanVo();
                payablePlanDto.setFkReceivablePlanId(receivablePlanId);
                //应付id
                Long payablePlanId = payablePlanService.addPayablePlan(payablePlanDto);
                //更新奖励应收应付
                incentivePolicyStudentOfferItemService.updatePolicyStudentOfferItem(incentiveReceivableAndPayablePlanDto.getFkIncentivePolicyStudentOfferItemId(),receivablePlanId,payablePlanId);
            }
        }
    }

    private void updateOfferItemOpenTime(ReceivablePlanDto receivablePlanDto){
        Date openTime = receivablePlanDto.getOpeningTime();
        Integer fkReceivableReasonId = receivablePlanDto.getFkReceivableReasonId();
        if (GeneralTool.isNotEmpty(fkReceivableReasonId) && GeneralTool.isNotEmpty(openTime)
                && TableEnum.SALE_STUDENT_OFFER_ITEM.key.equals(receivablePlanDto.getFkTypeKey())
                && 1 == fkReceivableReasonId) {
            Long fkTypeTargetId = receivablePlanDto.getFkTypeTargetId();
            StudentOfferItem studentOfferItem = studentOfferItemMapper.selectById(fkTypeTargetId);
            if (GeneralTool.isNotEmpty(studentOfferItem)) {
                StudentOfferItemVo oldStudentOfferItemVo = BeanCopyUtils.objClone(studentOfferItem, StudentOfferItemVo::new);
                EventOfferPlanDto offerPlanVo = new EventOfferPlanDto();
                BeanUtils.copyProperties(studentOfferItem,offerPlanVo);
                offerPlanVo.setOpeningTime(openTime);
                offerPlanVo.setDeferOpeningTime(openTime);
                studentOfferItemService.sendEmailNotice(offerPlanVo, oldStudentOfferItemVo,true);
                studentOfferItem.setOpeningTime(openTime);
                studentOfferItem.setIsDeferEntrance(true);
                studentOfferItem.setDeferOpeningTime(openTime);//最终入学时间以财务修改的为准，同时写入到延迟列表，无需处理
                utilService.setUpdateInfo(studentOfferItem);
                studentOfferItemMapper.updateById(studentOfferItem);
                StudentOfferItemDeferEntranceTime entranceTime = new StudentOfferItemDeferEntranceTime();
                entranceTime.setFkStudentOfferItemId(studentOfferItem.getId());
                entranceTime.setDeferEntranceTime(openTime);
                utilService.setCreateInfo(entranceTime);
                studentOfferItemDeferEntranceTimeMapper.insert(entranceTime);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseBo<ReceivableAndPayablePlanVo> update(ReceivableAndPayablePlanDto receivableAndPayablePlanDto) {
        ReceivablePlanDto receivablePlanDto = receivableAndPayablePlanDto.getReceivablePlanVo();
        if (GeneralTool.isNotEmpty(receivablePlanDto)) {
            receivablePlanService.updateReceivablePlan(receivablePlanDto);
            updateOfferItemOpenTime(receivablePlanDto);
        }
        if (GeneralTool.isNotEmpty(receivableAndPayablePlanDto.getPayablePlanVo())) {
            payablePlanService.updatePayablePlan(receivableAndPayablePlanDto.getPayablePlanVo());
        }
        Long payablePlanId = null;
        if (!Objects.isNull(receivableAndPayablePlanDto.getPayablePlanVo())) {
            payablePlanId = receivableAndPayablePlanDto.getPayablePlanVo().getId();
        }
        return details(receivableAndPayablePlanDto.getReceivablePlanVo().getId(), payablePlanId);
    }

    /**
     * Author Cream
     * Description : //详情
     * Date 2023/2/22 15:34
     * Params:
     * Return
     */
    @Override
    public ResponseBo<ReceivableAndPayablePlanVo> details(Long receivablePlanId, Long payablePlanId) {
        ReceivableAndPayablePlanVo receivableAndPayablePlanVo = new ReceivableAndPayablePlanVo();
        receivableAndPayablePlanVo.setReceivablePlanDto(receivablePlanService.findReceivablePlan(receivablePlanId));
        if (GeneralTool.isNotEmpty(payablePlanId)) {
            receivableAndPayablePlanVo.setPayablePlanDto(payablePlanService.findPayablePlanById(payablePlanId));
        }
        return new ResponseBo<>(receivableAndPayablePlanVo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean delete(Set<Long> receivablePlanIds, Set<Long> payablePlanIds) {
        //删除应收应付计划
        LambdaQueryWrapper<ReceivablePlan> receivablePlanLambdaQueryWrapper = new LambdaQueryWrapper<>();
        receivablePlanLambdaQueryWrapper.in(ReceivablePlan::getId,receivablePlanIds);
        receivablePlanMapper.delete(receivablePlanLambdaQueryWrapper);

        LambdaQueryWrapper<PayablePlan> payablePlanLambdaQueryWrapper = new LambdaQueryWrapper<>();
        payablePlanLambdaQueryWrapper.in(PayablePlan::getId,payablePlanIds);
        payablePlanMapper.delete(payablePlanLambdaQueryWrapper);
        return true;
    }
}

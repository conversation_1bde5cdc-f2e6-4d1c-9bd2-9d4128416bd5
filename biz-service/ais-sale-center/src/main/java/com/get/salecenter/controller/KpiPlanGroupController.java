package com.get.salecenter.controller;


import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.UpdateResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.dto.KpiPlanGroupSearchDto;
import com.get.salecenter.vo.KpiPlanGroupResultVo;
import com.get.salecenter.service.KpiPlanGroupService;
import com.get.salecenter.dto.KpiPlanGroupDto;
import com.get.salecenter.vo.KpiPlanGroupVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import java.util.List;

@Api(tags = "KPI方案组别管理")
@RestController
@RequestMapping("sale/kpiPlanGroup")
public class KpiPlanGroupController {

    @Resource
    private KpiPlanGroupService kpiPlanGroupService;

    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/KPI方案组别管理/列表数据")
    @PostMapping("datas")
    public ResponseBo<KpiPlanGroupResultVo> datas(@RequestBody KpiPlanGroupSearchDto searchVo) {
        KpiPlanGroupResultVo data = kpiPlanGroupService.datas(searchVo);
        return new ResponseBo<>(data);
    }

    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/KPI方案组别管理/新增")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(KpiPlanGroupDto.Add.class)  KpiPlanGroupDto vo) {
        return SaveResponseBo.ok(kpiPlanGroupService.addKpiPlanGroup(vo));
    }

    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/KPI方案组别管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        kpiPlanGroupService.delete(id);
        return SaveResponseBo.ok();
    }

    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/KPI方案组别管理/更新")
    @PostMapping("update")
    public ResponseBo update(@RequestBody @Validated(KpiPlanGroupDto.Update.class) KpiPlanGroupDto vo) {
        kpiPlanGroupService.updateKpiPlanGroup(vo);
        return UpdateResponseBo.ok();
    }


    @ApiOperation(value = "拖拽", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/KPI方案组别管理/拖拽")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestParam("fkKpiPlanId") Long fkKpiPlanId,
                                  @RequestParam("start")Integer start,
                                  @RequestParam("end")Integer end) {
        kpiPlanGroupService.movingOrder(fkKpiPlanId,start,end);
        return ResponseBo.ok();
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "KPI方案组别下拉", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/KPI方案组别管理/组别下拉")
    @PostMapping("getGroupSelect")
    public ResponseBo<KpiPlanGroupVo> getGroupSelect(@RequestParam("fkKpiPlanId") Long fkKpiPlanId) {
        List<KpiPlanGroupVo> data = kpiPlanGroupService.getGroupSelect(fkKpiPlanId);
        return new ListResponseBo<>(data);
    }

}

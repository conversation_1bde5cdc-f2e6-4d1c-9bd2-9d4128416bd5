package com.get.salecenter.service.impl;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.ErrorCodeEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.result.ResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.ConventionTableMapper;
import com.get.salecenter.dao.sale.ConventionTablePersonMapper;
import com.get.salecenter.vo.ConventionPersonVo;
import com.get.salecenter.vo.ConventionTableVo;
import com.get.salecenter.vo.ConventionTablePersonVo;
import com.get.salecenter.entity.ConventionPerson;
import com.get.salecenter.entity.ConventionTable;
import com.get.salecenter.entity.ConventionTablePerson;
import com.get.salecenter.service.IConventionPersonService;
import com.get.salecenter.service.IConventionTablePersonService;
import com.get.salecenter.service.IConventionTableService;
import com.get.salecenter.dto.ConventionTablePersonDto;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: Sea
 * @create: 2020/8/28 16:16
 * @verison: 1.0
 * @description:
 */
@Service
public class ConventionTablePersonServiceImpl implements IConventionTablePersonService {
    @Resource
    private ConventionTablePersonMapper conventionTablePersonMapper;
    @Resource
    private IConventionPersonService conventionPersonService;
    @Resource
    private IConventionTableService conventionTableService;
    @Resource
    private UtilService utilService;
    @Resource
    private ConventionTableMapper conventionTableMapper;


    @Override
    public Long configurationTable(ConventionTablePersonDto conventionTablePersonDto) {
        if (GeneralTool.isEmpty(conventionTablePersonDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        ConventionTableVo tableDto = conventionTableService.findConventionTableById(conventionTablePersonDto.getFkConventionTableId());
//        Example example = new Example(ConventionTablePerson.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkConventionTableId", conventionTablePersonDto.getFkConventionTableId());
        List<ConventionTablePerson> conventionTablePersons = conventionTablePersonMapper.selectList(Wrappers.<ConventionTablePerson>lambdaQuery().eq(ConventionTablePerson::getFkConventionTableId, conventionTablePersonDto.getFkConventionTableId()));
//        ConventionTable conventionTable = conventionTableMapper.selectById(conventionTablePersonDto.getFkConventionTableId());

        List<ConventionTable> tables = conventionTableMapper.selectList(Wrappers.lambdaQuery(ConventionTable.class)
                .eq(ConventionTable::getFkConventionId, tableDto.getFkConventionId())
                .eq(ConventionTable::getFkTableTypeKey, "CONVENTION_DINNER_TABLE"));

        List<Long> tableIds = Lists.newArrayList(conventionTablePersonDto.getFkConventionTableId());
        if (GeneralTool.isNotEmpty(tables)){
            List<Long> tableIdList = tables.stream().map(ConventionTable::getId).collect(Collectors.toList());
            tableIds.addAll(tableIdList);
        }
        //根据桌台ids判断要安排得人员是否已经被安排
        List<Long> personIds = conventionTablePersonMapper.getPersonIds(tableIds);
        if ("CONVENTION_DINNER_TABLE".equals(tableDto.getFkTableTypeKey())) {
            if (personIds.contains(conventionTablePersonDto.getFkConventionPersonId())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("person_arrange_table"));
            }
        }
        ConventionTablePerson conventionTablePerson = BeanCopyUtils.objClone(conventionTablePersonDto, ConventionTablePerson::new);
        //已安排的数量 <= 该桌台的座位数 则添加
        if (conventionTablePersons.size() < tableDto.getSeatCount()) {
            utilService.updateUserInfoToEntity(conventionTablePerson);
            conventionTablePersonMapper.insertSelective(conventionTablePerson);
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("table_full"));
        }
        return conventionTablePerson.getId();
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        conventionTablePersonMapper.deleteById(id);
    }

    @Override
    public List<ConventionTablePersonVo> getPersonByTableId(Long id) {
//        Example example = new Example(ConventionTablePerson.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkConventionTableId", id);
        List<ConventionTablePerson> conventionTablePersons = conventionTablePersonMapper.selectList(Wrappers.<ConventionTablePerson>lambdaQuery().eq(ConventionTablePerson::getFkConventionTableId, id));

        List<ConventionTablePersonVo> convertDatas = new ArrayList<>();
        for (ConventionTablePerson conventionTablePerson : conventionTablePersons) {
            ConventionTablePersonVo conventionTablePersonVo = BeanCopyUtils.objClone(conventionTablePerson, ConventionTablePersonVo::new);
            //通过参会人员id 查找参会人员
            ConventionPersonVo personDto = conventionPersonService.findPersonById(conventionTablePerson.getFkConventionPersonId());
            conventionTablePersonVo.setConventionPersonDto(personDto);
            convertDatas.add(conventionTablePersonVo);
        }
        return convertDatas;
    }

    @Override
    public List<ConventionTablePerson> getTablePersonListById(Long id) {
//        Example example = new Example(ConventionTablePerson.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkConventionTableId", id);
//        return conventionTablePersonMapper.selectByExample(example);
        List<ConventionTablePerson> conventionTablePersons = conventionTablePersonMapper.selectList(Wrappers.<ConventionTablePerson>lambdaQuery().eq(ConventionTablePerson::getFkConventionTableId, id));
        return conventionTablePersons;
    }

    @Override
    public List<ConventionTable> getTableByPersonId(Long id) {
        return conventionTablePersonMapper.getTableByPersonId(id);
    }

    @Override
    public ResponseBo validateConventionPerson(Long conventionPersonId,Long conventionId) {

        List<ConventionTablePerson> conventionTablePeoples = conventionTablePersonMapper.getConventionTablePersonByPersonId(conventionPersonId,conventionId,ProjectKeyEnum.CONVENTION_TRAINING_TABLE.key);
        ResponseBo responseBo = new ResponseBo();

        if (conventionTablePeoples.size()>=1){
            ConventionPerson conventionPerson = conventionPersonService.getConventionPersonById(conventionPersonId);
            //false
            responseBo.setSuccess(true);
            responseBo.setCode(ErrorCodeEnum.REQUEST_OK.getCode());
            responseBo.setData(false);
            responseBo.setMessage("【"+conventionPerson.getName()+"】"+"已经被分配了，请问是否继续分配呢？");
        }else {
            //不重复 true
            responseBo.setSuccess(true);
            responseBo.setCode(ErrorCodeEnum.REQUEST_OK.getCode());
            responseBo.setData(true);
        }
        return responseBo;
    }
}

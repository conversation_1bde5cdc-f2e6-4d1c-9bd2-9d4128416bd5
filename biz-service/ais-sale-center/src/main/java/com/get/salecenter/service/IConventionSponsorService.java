package com.get.salecenter.service;

import com.get.common.result.Page;
import com.get.salecenter.vo.ConventionSponsorFeeVo;
import com.get.salecenter.vo.ConventionSponsorVo;
import com.get.salecenter.entity.ConventionSponsor;
import com.get.salecenter.dto.ConventionSponsorDto;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * @author: Sea
 * @create: 2021/5/8 11:22
 * @verison: 1.0
 * @description:
 */
public interface IConventionSponsorService {
    /**
     * @return com.get.salecenter.vo.ConventionSponsorVo
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    ConventionSponsorVo findConventionSponsorById(Long id);

    /**
     * @return void
     * @Description :新增
     * @Param [conventionSponsorVos]
     * <AUTHOR>
     */
    Long addConventionSponsor(ConventionSponsorDto conventionSponsorDto);

    /**
     * @return void
     * @Description :删除
     * @Param [id]
     * <AUTHOR>
     */
    void delete(Long id);

    /**
     * @return com.get.salecenter.vo.ConventionSponsorVo
     * @Description :修改
     * @Param [conventionSponsorDto]
     * <AUTHOR>
     */
    ConventionSponsorVo updateConventionSponsor(ConventionSponsorDto conventionSponsorDto);

    /**
     * @return com.get.salecenter.vo.ConventionSponsorVo
     * @Description :年会注册那边用
     * @Param [conventionSponsorDto]
     * <AUTHOR>
     */
    void updateConventionSponsor2(ConventionSponsorDto conventionSponsorDto);

    /**
     * @return java.util.List<com.get.salecenter.vo.ConventionSponsorVo>
     * @Description :列表
     * @Param [conventionSponsorDto, page]
     * <AUTHOR>
     */
    List<ConventionSponsorVo> getConventionSponsors(ConventionSponsorDto conventionSponsorDto, Page page);

    /**
     * @return com.get.salecenter.vo.ConventionSponsorVo
     * @Description :根据回执码峰会id查找赞助商和赞助类型
     * @Param [conventionId, receiptCode]
     * <AUTHOR>
     */
    List<ConventionSponsorVo> getConventionSponsorFeeDtos(Long conventionId, String receiptCode);

    /**
     * @return java.util.List<java.util.Map < java.lang.String, java.util.List < com.get.salecenter.vo.ConventionSponsorFeeVo>>>
     * @Description :获取赞助列表信息以及每个赞助是否售空
     * @Param [conventionId]
     * <AUTHOR>
     */
    List<Map<String, List<ConventionSponsorFeeVo>>> getSponsorshipConfig(Long conventionId);

    /**
     * @return java.lang.Boolean
     * @Description :单个赞助对象验证是否售空
     * @Param [sponsorshipConfigId, initNum]
     * <AUTHOR>
     */
    Boolean soldOut(Long sponsorshipConfigId, Integer initNum);

    /**
     * @return void
     * @Description :修改状态
     * @Param [id, status]
     * <AUTHOR>
     */
    void updateStatus(Long id, Integer status);

    /**
     * @return java.lang.Boolean
     * @Description :验证回执码是否重复
     * @Param [receiptCode]
     * <AUTHOR>
     */
    Boolean validateReceiptCode(String receiptCode);

    /**
     * @return void
     * @Description :导出赞助商名册Excel
     * @Param [response, conventionSponsorDto]
     * <AUTHOR>
     */
    void exportSponsorExcel(HttpServletResponse response, ConventionSponsorDto conventionSponsorDto);

    /**
     * 根据id获取赞助商名册
     *
     * @param conventionRegistrationId
     * @return
     */
    ConventionSponsor getConventionSponsorById(Long conventionRegistrationId);

    /**
     * 根据id更新
     *
     * @param conventionSponsor
     */
    void updateConventionSponsorById(ConventionSponsor conventionSponsor);

    /**
     * 根据条件查询
     *
     * @param conventionSponsorDto
     * @return
     */
    List<ConventionSponsor> getConventionSponsorsByVo(ConventionSponsorDto conventionSponsorDto);

    /**
     * 置空的更新方法
     *
     * @param conventionSponsor
     */
    void updateWithNullConventionSponsorById(ConventionSponsor conventionSponsor);
}

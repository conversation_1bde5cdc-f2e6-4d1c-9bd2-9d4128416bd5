package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.redis.lock.RedisLock;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.service.IConventionTablePersonService;
import com.get.salecenter.dto.ConventionTablePersonDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: Sea
 * @create: 2020/8/28 12:00
 * @verison: 1.0
 * @description: 配置桌台-桌台人员管理
 */
@Api(tags = "配置桌台-桌台人员管理")
@RestController
@RequestMapping("sale/conventionTablePerson")
public class ConventionTablePersonController {

    @Resource
    private IConventionTablePersonService conventionTablePersonService;


    /**
     * 配置桌台-桌台人员
     *
     * @param conventionTablePersonDto
     * @return
     * @
     */
    @RedisLock(value = "get:addConventionPersonTable:RedisLock", param = "#conventionTablePersonDto.fkConventionPersonId", waitTime = 5L)
    @ApiOperation(value = "配置桌台", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/配置桌台-桌台人员管理/配置桌台")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(ConventionTablePersonDto.Add.class) ConventionTablePersonDto conventionTablePersonDto) {
        return SaveResponseBo.ok(conventionTablePersonService.configurationTable(conventionTablePersonDto));
    }

    /**
     * 配置桌台-桌台人员
     *
     * @param conventionPersonId
     * @param conventionId
     * @return
     * @
     */
    @ApiOperation(value = "校验参会人员是否重复配置", notes = "")
    @VerifyPermission(IsVerify = false)
    @GetMapping("validateConventionPerson")
    public ResponseBo validateConventionPerson(@RequestParam("conventionPersonId") Long conventionPersonId,@RequestParam("conventionId") Long conventionId) {
        return conventionTablePersonService.validateConventionPerson(conventionPersonId,conventionId);
    }

    /**
     * 删除信息
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/配置桌台-桌台人员管理/删除桌台人员配置")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        conventionTablePersonService.delete(id);
        return DeleteResponseBo.ok();
    }
}

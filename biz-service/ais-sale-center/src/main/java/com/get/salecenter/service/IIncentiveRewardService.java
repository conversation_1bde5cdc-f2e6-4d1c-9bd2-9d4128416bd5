package com.get.salecenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.common.result.SearchBean;
import com.get.salecenter.vo.IncentiveRewardVo;
import com.get.salecenter.entity.IncentiveReward;
import com.get.salecenter.dto.IncentiveRewardSearchDto;
import com.get.salecenter.dto.IncentiveRewardDto;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-03-13
 */
public interface IIncentiveRewardService extends IService<IncentiveReward> {

    IncentiveRewardVo selectById(Long id);

    Long addUIncentiveReward(IncentiveRewardDto uIncentiveRewardDto);

    void deleteDataById(Long id);

    IncentiveRewardVo updateUIncentiveReward(IncentiveRewardDto uIncentiveRewardDto);

    List<IncentiveRewardVo> getIncentiveRewards(IncentiveRewardDto data, SearchBean<IncentiveRewardDto> page);

    void movingOrder(List<IncentiveRewardDto> voList);

    List<IncentiveRewardVo> getIncentiveRewardListByIds(IncentiveRewardSearchDto incentiveRewardSearchDto);
}

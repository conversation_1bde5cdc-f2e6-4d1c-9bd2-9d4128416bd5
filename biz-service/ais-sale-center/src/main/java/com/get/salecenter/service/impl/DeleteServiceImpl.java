package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.TableEnum;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.feign.IFinanceCenterClient;
import com.get.salecenter.dao.sale.*;
import com.get.salecenter.entity.*;
import com.get.salecenter.service.IDeleteService;
import com.get.salecenter.service.IPayablePlanService;
import com.get.salecenter.service.IReceivablePlanService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/11/26
 * @TIME: 17:03
 * @Description:
 **/
@Service
public class DeleteServiceImpl implements IDeleteService {
    @Resource
    private AgentMapper agentMapper;
    @Resource
    private AgentContractMapper agentContractMapper;
    @Resource
    private AgentEventMapper agentEventMapper;
    @Resource
    private AgentCompanyMapper agentCompanyMapper;
    @Resource
    private EventRegistrationMapper eventRegistrationMapper;
    @Resource
    private AgentStaffMapper agentStaffMapper;
    @Resource
    private StudentAgentMapper studentAgentMapper;
    @Resource
    private ConventionPersonAgentMapper conventionPersonAgentMapper;
    @Resource
    private AgentContractAccountMapper contractAccountMapper;
    @Resource
    private AgentContractCompanyMapper contractCompanyMapper;
    @Resource
    private StudentContactPersonMapper studentContactPersonMapper;
    @Resource
    private StudentEventMapper studentEventMapper;
    @Resource
    private StudentOfferMapper studentOfferMapper;
    @Resource
    private StudentOfferItemMapper studentOfferItemMapper;
    @Resource
    private StudentProjectRoleStaffMapper projectRoleStaffMapper;
    @Resource
    private RStudentOfferItemStepMapper rStudentOfferItemStepMapper;
    @Resource
    private MediaAndAttachedMapper mediaAndAttachedMapper;
    @Resource
    private ConventionHotelRoomMapper conventionHotelRoomMapper;
    @Resource
    private IFinanceCenterClient financeCenterClient;
    @Resource
    private ConventionHotelRoomPersonMapper conventionHotelRoomPersonMapper;
    @Resource
    private ConventionTableRegistrationMapper conventionTableRegistrationMapper;
    @Resource
    private ConventionPersonProcedureMapper conventionPersonProcedureMapper;
    @Resource
    private ConventionTablePersonMapper conventionTablePersonMapper;
    @Resource
    private ConventionPersonMapper conventionPersonMapper;
    @Resource
    private ConventionTableMapper conventionTableMapper;
    @Resource
    private ConventionHotelMapper conventionHotelMapper;
    @Resource
    private ConventionProcedureMapper conventionProcedureMapper;
    @Resource
    private ConventionRegistrationMapper conventionRegistrationMapper;
    @Resource
    private EventCostMapper eventCostMapper;
    @Resource
    private EventMapper eventMapper;
    @Resource
    private AgentContractFormulaMapper agentContractFormulaMapper;
    @Resource
    private AgentContractAgentAccountMapper agentContractAgentAccountMapper;
    @Resource
    private StudentProjectRoleStaffMapper studentProjectRoleStaffMapper;
    @Resource
    private AgentRoleStaffMapper agentRoleStaffMapper;
    @Resource
    private PayablePlanMapper payablePlanMapper;
    @Resource
    private ReceivablePlanMapper receivablePlanMapper;
    @Resource
    private IPayablePlanService payablePlanService;
    @Resource
    private IReceivablePlanService receivablePlanService;
    @Resource
    private ClientEventMapper clientEventMapper;
    @Resource
    private ClientOfferMapper clientOfferMapper;
    @Resource
    private ClientAgentMapper clientAgentMapper;
    @Resource
    private RStudentToClientApprovalMapper studentToClientApprovalMapper;
    @Resource
    private ClientSourceMapper clientSourceMapper;
    @Resource
    private ClientStaffMapper clientStaffMapper;

    /**
     * 删除代理校验
     *
     * @param agentId
     * @return
     * @
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteValidateAgent(Long agentId) {
        if (agentMapper.isExistByAgentId(agentId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("agent_followAgent_data_association"));
        }
        if (agentContractMapper.isExistByAgentId(agentId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("agent_contract_data_association"));
        }
        if (agentEventMapper.isExistByAgentId(agentId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("agent_event_data_association"));
        }
        if (studentAgentMapper.isExistByAgentId(agentId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("agent_student_data_association"));
        }
        if (conventionPersonAgentMapper.isExistByAgentId(agentId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("agent_convention_data_association"));
        }
        if (agentContractFormulaMapper.agentContractFormulaIsEmpty(agentId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("agent_contractFormula_data_association"));
        }

        deleteAgentStaff(agentId);
        deleteAgentCompany(agentId);
        deleteMedia(agentId, TableEnum.CLIENT_OFFER.key);
        return true;
    }

    @Override
    public Boolean deleteValidateContact(Long contractId) {
        if (contractAccountMapper.isExistByContractId(contractId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("contract_contractAccount_data_association"));
        }

        deleteContractRelation(contractId);
        deleteMedia(contractId, TableEnum.SALE_CONTRACT.key);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteValidateStudent(Long studentId) {
        if (studentContactPersonMapper.isExistByStudentId(studentId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("student_contactPerson_data_association"));
        }
        if (studentEventMapper.isExistByStudentId(studentId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("student_event_data_association"));
        }
        if (studentOfferMapper.isExistByStudentId(studentId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("student_offer_data_association"));
        }

        deleteStudentAgent(studentId);
        deleteMedia(studentId, TableEnum.SALE_STUDENT.key);
        return true;
    }

    private void deleteStudentAgent(Long studentId) {
//        Example example = new Example(StudentAgent.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkStudentId", studentId);
//        studentAgentMapper.deleteByExample(example);
        studentAgentMapper.delete(Wrappers.<StudentAgent>lambdaQuery().eq(StudentAgent::getFkStudentId, studentId));
    }

    @Override
    @Transactional
    public Boolean deleteValidateClient(Long clientId) {
        if (studentContactPersonMapper.isExistByStudentId(clientId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("client_contactPerson_data_association"));
        }
        if (clientEventMapper.isExistByClientId(clientId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("client_event_data_association"));
        }
        if (clientOfferMapper.isExistByClientId(clientId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("client_offer_data_association"));
        }

        deleteClientAgent(clientId);
        deleteMedia(clientId, TableEnum.SALE_STUDENT.key);
        deleteRStudentToClientApproval(clientId);
        deleteClientSource(clientId);
        deletClientStaff(clientId);
        return true;
    }

    //删除学生负责人
    private void deletClientStaff(Long clientId) {
        clientStaffMapper.delete(Wrappers.<ClientStaff>lambdaQuery().eq(ClientStaff::getFkClientId, clientId));
    }

    private void deleteRStudentToClientApproval(Long clientId) {
        studentToClientApprovalMapper.delete(Wrappers.<RStudentToClientApproval>lambdaQuery().eq(RStudentToClientApproval::getFkClientId, clientId));
    }

    private void deleteClientSource(Long clientId) {
        clientSourceMapper.delete(Wrappers.<ClientSource>lambdaQuery().eq(ClientSource::getFkClientId, clientId));
    }

    private void deleteClientAgent(Long clientId) {
        clientAgentMapper.delete(Wrappers.<ClientAgent>lambdaQuery().eq(ClientAgent::getFkClientId, clientId));
    }

    @Override
    public Boolean deleteValidateOffer(Long offerId) {
        if (studentOfferItemMapper.isExistByOfferId(offerId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("offer_offerItem_data_association"));
        }
        deleteRoleStaff(offerId);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteRelationOfferItem(Long itemId) {
//        Example example = new Example(RStudentOfferItemStep.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkStudentOfferItemId", itemId);
//        int delete = rStudentOfferItemStepMapper.deleteByExample(example);
        if (payablePlanMapper.isExistByItemId(itemId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("item_payable_data_association"));
        }
        if (receivablePlanMapper.isExistByItemId(itemId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("item_receivable_data_association"));
        }
        payablePlanService.deletePayablePlanByItemId(itemId);
        receivablePlanService.deleteReceivablePlanByItemId(itemId);
        int delete = rStudentOfferItemStepMapper.delete(Wrappers.<RStudentOfferItemStep>lambdaQuery().eq(RStudentOfferItemStep::getFkStudentOfferItemId, itemId));
        return delete > 0;
    }

    @Override
    public Boolean deleteRelationOfferItemStep(Long itemStepId) {
        if (rStudentOfferItemStepMapper.isExistByItemStepId(itemStepId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("step_offerItem_data_association"));
        }
        return true;
    }

    @Override
    public Boolean deleteValidateHotel(Long hotelId) {
        if (conventionHotelRoomMapper.conventionHoteRoomIsEmpty(hotelId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("hotel_hotelRoom_data_association"));
        }
        return true;
    }

    @Override
    public Boolean deleteValidateHotelRoom(Long roomId) {
        String fieldName = "fk_convention_hotel_room_id";
        if (conventionHotelRoomPersonMapper.conventionHotelRoomPersonIsEmpty(fieldName, roomId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("hotelRoom_person_data_association"));
        }
        return true;
    }

    @Override
    public Boolean deleteValidateRegistration(Long registrationId) {
        String fieldName = "fk_convention_registration_id";
        if (conventionTableRegistrationMapper.conventionTableRegistrationIsEmpty(fieldName, registrationId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("registration_table_data_association"));
        }
        return true;
    }

    @Override
    public Boolean deleteValidatePerson(Long personId) {
        String fieldName = "fk_convention_person_id";
        if (conventionHotelRoomPersonMapper.conventionHotelRoomPersonIsEmpty(fieldName, personId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("person_hotelRoom_data_association"));
        }
        if (conventionTablePersonMapper.conventionTablePersonIsEmpty(fieldName, personId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("person_table_data_association"));
        }
        if (conventionPersonProcedureMapper.conventionPersonProcedureIsEmpty(fieldName, personId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("person_procedure_data_association"));
        }
        return true;
    }

    @Override
    public Boolean deleteValidateProcedure(Long procedureId) {
        String fieldName = "fk_convention_procedure_id";
        if (conventionPersonProcedureMapper.conventionPersonProcedureIsEmpty(fieldName, procedureId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("procedure_person_data_association"));
        }
        return true;
    }

    @Override
    public Boolean deleteValidateConvention(Long conventionId) {
        if (conventionPersonMapper.conventionPersonIsEmpty(conventionId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("convention_person_data_association"));
        }
        if (conventionTableMapper.conventionTableIsEmpty(conventionId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("convention_table_data_association"));
        }
        if (conventionHotelMapper.conventionHotelIsEmpty(conventionId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("convention_hotel_data_association"));
        }
        if (conventionProcedureMapper.conventionProcedureIsEmpty(conventionId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("convention_procedure_data_association"));
        }
        if (conventionRegistrationMapper.conventionRegistrationIsEmpty(conventionId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("convention_registration_data_association"));
        }
        return true;
    }

    @Override
    public Boolean deleteValidateTable(Long tableId) {
        String fieldName = "fk_convention_table_id";
        if (conventionTableRegistrationMapper.conventionTableRegistrationIsEmpty(fieldName, tableId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("table_registration_data_association"));
        }
        if (conventionTablePersonMapper.conventionTablePersonIsEmpty(fieldName, tableId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("table_person_data_association"));
        }
        return true;
    }

    @Override
    public Boolean deleteValidateEvent(Long eventId) {
//        Example example = new Example(EventCost.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkEventId", eventId);
//        List<EventCost> eventCosts = eventCostMapper.selectByExample(example);
        List<EventCost> eventCosts = eventCostMapper.selectList(Wrappers.<EventCost>lambdaQuery().eq(EventCost::getFkEventId, eventId));
        if (GeneralTool.isNotEmpty(eventCosts)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("event_cost_data_association"));
        }
        List<EventRegistration> eventRegistrations = eventRegistrationMapper.selectList(Wrappers.<EventRegistration>lambdaQuery().eq(EventRegistration::getFkEventId, eventId));
        if (GeneralTool.isNotEmpty(eventRegistrations)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("event_registration_data_association"));
        }
        //删除关联附件
        deleteMedia(eventId, TableEnum.SALE_EVENT.key);
        return true;
    }

    @Override
    public Boolean deleteValidateEventType(Long eventTypeId) {
//        Example example = new Example(Event.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkEventTypeId", eventTypeId);
//        List<Event> event = eventMapper.selectByExample(example);
        List<Event> event = eventMapper.selectList(Wrappers.<Event>lambdaQuery().eq(Event::getFkEventTypeId, eventTypeId));
        if (GeneralTool.isNotEmpty(event)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("eventType_event_data_association"));
        }
        return true;
    }

    @Override
    public Boolean deleteValidateStaffBdCode(Long staffId) {
//        Example example = new Example(AgentStaff.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkStaffId", staffId);
//        List<AgentStaff> agentStaffs = agentStaffMapper.selectByExample(example);
        List<AgentStaff> agentStaffs = agentStaffMapper.selectList(Wrappers.<AgentStaff>lambdaQuery()
                .eq(AgentStaff::getFkStaffId, staffId).eq(AgentStaff::getIsActive,true));

        if (GeneralTool.isNotEmpty(agentStaffs)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("staff_agent_data_association"));
        }
        return true;
    }

    @Override
    public Boolean deleteValidateSponsorFee(Long sponsorFeeId) {
        return null;
    }

    /**
     * 删除代理账户校验
     *
     * @Date 15:30 2021/6/28
     * <AUTHOR>
     */
    @Override
    public Boolean deleteValidateAgentAccount(Long agentAccountId) {
//        Example example = new Example(AgentContractAgentAccount.class);
//        example.createCriteria().andEqualTo("fkAgentContractAccountId", agentAccountId);
//        List<AgentContractAgentAccount> agentContractAgentAccounts = agentContractAgentAccountMapper.selectByExample(example);

        List<AgentContractAgentAccount> agentContractAgentAccounts = agentContractAgentAccountMapper.selectList(Wrappers.<AgentContractAgentAccount>lambdaQuery().eq(AgentContractAgentAccount::getFkAgentContractAccountId, agentAccountId));
        if (GeneralTool.isNotEmpty(agentContractAgentAccounts)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("agent_account_contract_data_association"));
        }
        Result<Boolean> result = financeCenterClient.checkExistAccountCommissionSettlement(agentAccountId);
        if (!result.isSuccess()) {
            throw new GetServiceException(result.getMessage());
        }
        if (result.getData()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("agent_account_settlement_data_association"));
        }
        return true;
    }

    /**
     * 删除项目成员角色校验
     *
     * @Date 11:25 2021/7/29
     * <AUTHOR>
     */
    @Override
    public void deleteValidateRole(Long id) {
//        Example example = new Example(StudentProjectRoleStaff.class);
//        example.createCriteria().andEqualTo("fkStudentProjectRoleId", id).andEqualTo("isActive", 1);
//        List<StudentProjectRoleStaff> studentProjectRoleStaffs = studentProjectRoleStaffMapper.selectByExample(example);
        List<StudentProjectRoleStaff> studentProjectRoleStaffs = studentProjectRoleStaffMapper.selectList(Wrappers.<StudentProjectRoleStaff>lambdaQuery()
                .eq(StudentProjectRoleStaff::getFkStudentProjectRoleId, id).eq(StudentProjectRoleStaff::getIsActive, 1));

        if (GeneralTool.isNotEmpty(studentProjectRoleStaffs)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("student_offer_project_role_data_association"));
        }


//        Example agentRoleStaffExample = new Example(AgentRoleStaff.class);
//        agentRoleStaffExample.createCriteria().andEqualTo("fkStudentProjectRoleId", id);
//        List<AgentRoleStaff> agentRoleStaffs = agentRoleStaffMapper.selectByExample(agentRoleStaffExample);

        List<AgentRoleStaff> agentRoleStaffs = agentRoleStaffMapper.selectList(Wrappers.<AgentRoleStaff>lambdaQuery()
                .eq(AgentRoleStaff::getFkStudentProjectRoleId, id));
        if (GeneralTool.isNotEmpty(agentRoleStaffs)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("staff_agent_project_role_data_association"));
        }
    }

    private void deleteRoleStaff(Long offerId) {
//        Example example = new Example(StudentProjectRoleStaff.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkTableName", TableEnum.SALE_STUDENT_OFFER.key);
//        criteria.andEqualTo("fkTableId", offerId);
//        projectRoleStaffMapper.deleteByExample(example);
        projectRoleStaffMapper.delete(Wrappers.<StudentProjectRoleStaff>lambdaQuery()
                .eq(StudentProjectRoleStaff::getFkTableName, TableEnum.SALE_STUDENT_OFFER.key).eq(StudentProjectRoleStaff::getFkTableId, offerId));
    }

    private void deleteMedia(Long agentId, String tableName) {
//        Example example = new Example(MediaAndAttached.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkTableName", tableName);
//        criteria.andEqualTo("fkTableId", agentId);
//        mediaAndAttachedMapper.deleteByExample(example);

        mediaAndAttachedMapper.delete(Wrappers.<SaleMediaAndAttached>lambdaQuery()
                .eq(SaleMediaAndAttached::getFkTableName, tableName).eq(SaleMediaAndAttached::getFkTableId, agentId));
    }

    private void deleteContractRelation(Long contractId) {
//        Example example = new Example(AgentContractCompany.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkAgentContractId", contractId);
//        contractCompanyMapper.deleteByExample(example);

        contractCompanyMapper.delete(Wrappers.<AgentContractCompany>lambdaQuery()
                .eq(AgentContractCompany::getFkAgentContractId, contractId));
    }

    private void deleteAgentCompany(Long agentId) {
//        Example example = new Example(AgentCompany.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkAgentId", agentId);
//        agentCompanyMapper.deleteByExample(example);
        agentCompanyMapper.delete(Wrappers.<AgentCompany>lambdaQuery()
                .eq(AgentCompany::getFkAgentId, agentId));
    }

    private void deleteAgentStaff(Long agentId) {
//        Example example = new Example(AgentStaff.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkAgentId", agentId);
//        agentStaffMapper.deleteByExample(example);

        agentStaffMapper.delete(Wrappers.<AgentStaff>lambdaQuery()
                .eq(AgentStaff::getFkAgentId, agentId));
    }

}

package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.secure.utils.SecureUtil;
import com.get.salecenter.vo.CommentVo;
import com.get.salecenter.vo.IncentivePolicyVo;
import com.get.salecenter.vo.MediaAndAttachedVo;
import com.get.salecenter.service.ICommentService;
import com.get.salecenter.service.IIncentivePolicyService;
import com.get.salecenter.dto.CommentDto;
import com.get.salecenter.dto.IncentivePolicyDto;
import com.get.salecenter.dto.MediaAndAttachedDto;
import com.get.salecenter.dto.query.IncentivePolicyQueryDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * Incentive奖励政策管理
 * <AUTHOR>
 * @since 2023-03-13
 */

@Api(tags = "Incentive奖励政策管理")
@RestController
@RequestMapping("sale/incentivePolicy")
public class IncentivePolicyController {
    @Resource
    private IIncentivePolicyService incentivePolicyService;
    @Resource
    private ICommentService commentService;

    /**
     * @Description :详情
     * @Param [id]
     */
    @ApiOperation(value = "详情", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/学生/奖励政策/详情")
    @GetMapping("/{id}")
    public ResponseBo<IncentivePolicyVo> detail(@PathVariable(value = "id") Long id) {
        IncentivePolicyVo data = incentivePolicyService.selectById(id);
        return new ResponseBo<>(data);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :新增
     * @Param [incentivePolicyDto]
     */
    @ApiOperation(value = "新增", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生/奖励政策/新增")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(IncentivePolicyDto.Add.class) IncentivePolicyDto incentivePolicyDto) {
        return SaveResponseBo.ok(incentivePolicyService.addIncentivePolicy(incentivePolicyDto));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :作废
     * @Param [id]
     */
    @ApiOperation(value = "作废", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/学生/奖励政策/作废")
    @PostMapping("updateStatus/{id}")
    public ResponseBo updateStatus(@PathVariable("id") Long id) {
        incentivePolicyService.updateStatus(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @Description :修改
     * @Param [incentivePolicyDto]
     */
    @ApiOperation(value = "修改", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生/奖励政策/更新")
    @PostMapping("update")
    public ResponseBo<IncentivePolicyVo> update(@RequestBody @Validated(IncentivePolicyDto.Update.class) IncentivePolicyDto incentivePolicyDto) {
        return UpdateResponseBo.ok(incentivePolicyService.updateIncentivePolicy(incentivePolicyDto));
    }

    /**
     * @Description :列表数据
     * @Param [page]
     */
    @ApiOperation(value = "列表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生/奖励政策/列表")
    @PostMapping("datas")
    public ResponseBo<IncentivePolicyVo> datas(@RequestBody SearchBean<IncentivePolicyQueryDto> page) {
        List<IncentivePolicyVo> datas = incentivePolicyService.getIncentivePolicys(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @Description :学校提供商下拉数据
     * @Param [page]
     */
    @ApiOperation(value = "学校提供商下拉数据", notes = "")
    @GetMapping("getInstitutionProviderSelect/{companyId}")
    public ResponseBo getInstitutionProviderSelect(@PathVariable("companyId") Long companyId) {
        if (!SecureUtil.validateCompany(companyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
        }
        List<BaseSelectEntity> datas = incentivePolicyService.getInstitutionProviderSelect(companyId);
        return new ListResponseBo<>(datas);
    }

    /**
     * 根据提供商id查询学校下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "根据提供商id查询学校下拉框数据", notes = "")
    @GetMapping("getInstitutionListByProviderId")
    public ResponseBo getInstitutionListByProviderId(@RequestParam(value = "providerId") Long providerId) {
        List<BaseSelectEntity> datas = incentivePolicyService.getInstitutionListByProviderId(providerId);
        return new ListResponseBo<>(datas);
    }


//    /**
//     * @return com.get.common.result.ResponseBo
//     * @Description: 拖动排序
//     * @Param [voList]
//     */
//    @ApiOperation(value = "列表排序", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生/奖励政策列表/排序")
//    @PostMapping("movingOrder")
//    public ResponseBo movingOrder(@RequestBody List<IncentivePolicyDto> voList) {
//        incentivePolicyService.movingOrder(voList);
//        return ResponseBo.ok();
//    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 拖动排序
     * @Param [voList]
     */
    @ApiOperation(value = "列表排序", notes = "")
    @PostMapping("movingOrderSelect")
    public ResponseBo movingOrderSelect(@RequestParam("end")int end,@RequestParam("start")int start) {
        incentivePolicyService.movingOrderSelect(end,start);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 复制奖励政策
     * @Param [StudentOfferVo]
     */
    @ApiOperation(value = "复制奖励政策", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生/奖励政策详情/复制")
    @PostMapping("copyPolicy")
    public ResponseBo<IncentivePolicyVo> copyPolicy(@RequestParam("id") Long id) {
        return new ResponseBo<>(incentivePolicyService.copyPolicy(id));
    }

    /**
     * 代理附件保存
     *
     * @param mediaAttachedVo
     * @return
     */

    @ApiOperation(value = "附件保存",notes = "注意文件的typeKey：incentive_policy_file")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生/奖励政策详情/附件保存")
    @PostMapping("upload")
    public ResponseBo<MediaAndAttachedVo> addAgentMedia(@RequestBody @Validated(MediaAndAttachedDto.Add.class) ValidList<MediaAndAttachedDto> mediaAttachedVo) {
        return new ListResponseBo<>(incentivePolicyService.addAgentMedia(mediaAttachedVo));
    }


    /**
     * 查询附件
     *
     * @param voSearchBean
     * @return
     */
    @ApiOperation(value = "查询附件", notes = "keyWord为关键词")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生/奖励政策详情/附件查询")
    @PostMapping("getPolicyMedia")
    public ResponseBo<MediaAndAttachedVo> getPolicyMedia(@RequestBody SearchBean<MediaAndAttachedDto> voSearchBean) {
        List<MediaAndAttachedVo> staffMedia = incentivePolicyService.getPolicyMedia(voSearchBean.getData(), voSearchBean);
        Page page = BeanCopyUtils.objClone(voSearchBean, Page::new);
        return new ListResponseBo<>(staffMedia, page);
    }


    /**
     * @param commentDto
     * @return
     */
    @ApiOperation(value = "编辑评论")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生/奖励政策详情/编辑评论")
    @PostMapping("editComment")
    public ResponseBo editComment(@RequestBody @Validated(CommentDto.Add.class)  CommentDto commentDto) {
        return SaveResponseBo.ok(incentivePolicyService.editComment(commentDto));
    }


    /**
     * @Description: 评论列表数据
     * @Param [searchBean]
     */
    @ApiOperation(value = "评论列表数据")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生/奖励政策详情/查询评论列表")
    @PostMapping("getComments")
    public ResponseBo<CommentVo> getComment(@RequestBody SearchBean<CommentDto> searchBean) {
        List<CommentVo> datas = incentivePolicyService.getComments(searchBean.getData(), searchBean);
        Page p = BeanCopyUtils.objClone(searchBean, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 删除信息
     * @Param [id]
     **/
    @ApiOperation(value = "删除评论接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/学生/奖励政策详情/删除评论")
    @GetMapping("deleteComment/{id}")
    public ResponseBo deleteComment(@PathVariable("id") Long id) {
        commentService.delete(id);
        return DeleteResponseBo.ok();
    }
}

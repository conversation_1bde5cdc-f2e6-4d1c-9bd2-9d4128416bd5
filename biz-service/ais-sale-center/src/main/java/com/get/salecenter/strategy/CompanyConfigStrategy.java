package com.get.salecenter.strategy;

import com.get.salecenter.utils.sale.SaleCenterConfigUtils;

import java.util.Optional;

/**
 * @author: Hardy
 * @create: 2024/3/19 18:19
 * @verison: 1.0
 * @description:
 */
public interface CompanyConfigStrategy {

    /**
     * 获取公司id
     * @return 公司id
     */
    Long getCompanyId();

    /**
     * 获取公司名称
     * @return 公司名称
     */
    String getCompany();

    /**
     * 获取value1配置
     * @param key 配置key
     * @param saleCenterConfigUtils 具体实现逻辑类
     * @return 配置值
     */
    Optional<Object> getValue1Config(String key, SaleCenterConfigUtils saleCenterConfigUtils);

    /**
     * 获取value2配置
     * @param key 配置key
     * @param saleCenterConfigUtils 具体实现逻辑类
     * @return 配置值
     */
    Optional<Object> getValue2Config(String key, SaleCenterConfigUtils saleCenterConfigUtils);

    /**
     * 获取value3配置
     * @param key 配置key
     * @param saleCenterConfigUtils 具体实现逻辑类
     * @return 配置值
     */
    Optional<Object> getValue3Config(String key, SaleCenterConfigUtils saleCenterConfigUtils);

    /**
     * 获取value4配置
     * @param key 配置key
     * @param saleCenterConfigUtils 具体实现逻辑类
     * @return 配置值
     */
    Optional<Object> getValue4Config(String key, SaleCenterConfigUtils saleCenterConfigUtils);
}

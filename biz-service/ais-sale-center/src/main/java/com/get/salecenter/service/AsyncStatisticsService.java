package com.get.salecenter.service;

import com.get.core.secure.StaffInfo;
import com.get.salecenter.bo.AgentApplicationRankingQueryBo;
import com.get.salecenter.entity.KpiPlan;
import com.get.salecenter.vo.AgentApplicationRankingVo;
import com.get.salecenter.vo.AgentApplicationStatisticsVo;
import com.get.salecenter.vo.StudentApplicationStatisticsVo;
import com.get.salecenter.vo.VipStatisticsVo;
import com.get.salecenter.dto.AgentPerformanceStatisticsDto;
import com.get.salecenter.dto.EmailStatisticsDto;
import com.get.salecenter.dto.StatisticsDto;
import com.get.salecenter.dto.VipStatisticsDto;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @DATE: 2022/3/30
 * @TIME: 17:35
 * @Description:
 **/
public interface AsyncStatisticsService {
//    /**
//     * 业绩统计
//     *
//     * @param statisticsVo
//     * @param isStudentOfferItemFinancialHiding
//     * @param isStudentAdmin
//     * @return
//     */
//    CompletableFuture<List<StudentApplicationStatisticsVo>> getStatisticsDtos(StatisticsDto statisticsVo, Boolean isStudentOfferItemFinancialHiding, Boolean isStudentAdmin);


    /**
     * 新建学生统计
     * <AUTHOR>
     * @DateTime 2022/11/22 14:06
     */
//    CompletableFuture<List<StudentApplicationStatisticsVo>> getCreateStatisticsDtos(StatisticsDto statisticsVo,String type, Boolean isStudentOfferItemFinancialHiding, Boolean isStudentAdmin);
    /**
     * 新建学生统计
     * <AUTHOR>
     * @DateTime 2022/11/22 14:06
     */
    List<StudentApplicationStatisticsVo> getCreateStatisticsDtos(StatisticsDto statisticsDto, String type, Boolean isStudentOfferItemFinancialHiding, Boolean isStudentAdmin);


//    /**
//     *  处理申请（含加申计划）
//     * <AUTHOR>
//     * @DateTime 2022/11/22 14:03
//     */
//    CompletableFuture<List<StudentApplicationStatisticsVo>> getApplicationStatisticsDtos(StatisticsDto statisticsVo,String type, Boolean isStudentOfferItemFinancialHiding, Boolean isStudentAdmin);

    /**
     *  处理申请（含加申计划）
     * <AUTHOR>
     * @DateTime 2022/11/22 14:03
     */
    List<StudentApplicationStatisticsVo> getApplicationStatisticsDtos(StatisticsDto statisticsDto, String type, Boolean isStudentOfferItemFinancialHiding, Boolean isStudentAdmin);

    /**
     * 定校量（按申请学校）
     * <AUTHOR>
     * @DateTime 2022/11/22 14:04
     */
//    CompletableFuture<List<StudentApplicationStatisticsVo>> getConfirmationStatisticsDtos(StatisticsDto statisticsDto,String type, Boolean isStudentOfferItemFinancialHiding, Boolean isStudentAdmin);
    List<StudentApplicationStatisticsVo> getConfirmationStatisticsDtos(StatisticsDto statisticsDto, String type, Boolean isStudentOfferItemFinancialHiding, Boolean isStudentAdmin);

    /**
     * 成功入学量（按申请学校）
     * <AUTHOR>
     * @DateTime 2022/11/22 14:14
     */
//    CompletableFuture<List<StudentApplicationStatisticsVo>> getSuccessStatisticsDtos(StatisticsDto statisticsDto,String type, Boolean isStudentOfferItemFinancialHiding, Boolean isStudentAdmin);
    List<StudentApplicationStatisticsVo> getSuccessStatisticsDtos(StatisticsDto statisticsDto, String type, Boolean isStudentOfferItemFinancialHiding, Boolean isStudentAdmin);
    /**
     * 代理省份送生确认量（按学生）、成功量（按学生）
     * <AUTHOR>
     * @DateTime 2022/11/30 13:03
     */
    List<StudentApplicationStatisticsVo> getAgentStateApplicationStatisticsByStudent(StatisticsDto statisticsDto, String type, Boolean isStudentOfferItemFinancialHiding, Boolean isStudentAdmin);
    /**
     * 定校量（按学生）
     * <AUTHOR>
     * @DateTime 2022/11/22 14:14
     */
    List<StudentApplicationStatisticsVo> getConfirmationByStudentStatisticsDtos(StatisticsDto statisticsDto, String type, Boolean isStudentOfferItemFinancialHiding, Boolean isStudentAdmin);

    /**
     * 成功入学量（按学生）
     * <AUTHOR>
     * @DateTime 2022/11/22 14:14
     */
    List<StudentApplicationStatisticsVo> getSuccessByStudentStatisticsDtos(StatisticsDto statisticsDto, String type, Boolean isStudentOfferItemFinancialHiding, Boolean isStudentAdmin);

    /**
     * 代理申请排行统计
     * <AUTHOR>
     * @DateTime 2022/11/22 14:18
     */
    CompletableFuture<List<AgentApplicationRankingVo>> getAgentApplicationRanking(AgentApplicationRankingQueryBo rankingQueryBo, String type, Boolean isStudentOfferItemFinancialHiding, Boolean isStudentAdmin);

    /**
     * 代理申请排行按学生统计
     * <AUTHOR>
     * @DateTime 2022/11/22 14:18
     */
    CompletableFuture<List<AgentApplicationRankingVo>> getAgentApplicationRankingByStudent(AgentApplicationRankingQueryBo rankingQueryBo, String type, Boolean isStudentOfferItemFinancialHiding, Boolean isStudentAdmin);


    /**
     * VIP统计
     * <AUTHOR>
     * @DateTime 2022/11/22 14:18
     */
    CompletableFuture<List<VipStatisticsVo>> getVipStatistics(VipStatisticsDto vipStatisticsDto, String type, Boolean isStudentOfferItemFinancialHiding, Boolean isStudentAdmin);

    /**
     * VIP统计按学生
     * <AUTHOR>
     * @DateTime 2022/11/22 14:18
     */
    CompletableFuture<List<VipStatisticsVo>> getVipStatisticsByStudent(VipStatisticsDto vipStatisticsDto, String type, Boolean isStudentOfferItemFinancialHiding, Boolean isStudentAdmin);

    /**
     * 代理业绩统计-申请分布图统计数据
     * <AUTHOR>
     * @DateTime 2022/12/2 11:20
     */
    CompletableFuture<AgentApplicationStatisticsVo> getAgentCountryApplicationStatistics(AgentPerformanceStatisticsDto agentPerformanceStatisticsDto, StaffInfo staffInfo);

    /**
     * 代理业绩统计-申请分布图统计（按学生）数据
     * <AUTHOR>
     * @DateTime 2022/12/2 11:20
     */
    CompletableFuture<AgentApplicationStatisticsVo> getAgentCountryApplicationStatisticsByStudent(AgentPerformanceStatisticsDto agentPerformanceStatisticsDto, String type, StaffInfo staffInfo);

    /**
     * 异步邮件生成
     * @param headerMap
     * @param staffInfo
     * @param locale
     * @param emailStatisticsDto
     */
    void asyncEmailStatistics(Map<String, String> headerMap, StaffInfo staffInfo, String locale,List<Long> countryIds, EmailStatisticsDto emailStatisticsDto,Long reportSaleId,String cacheKey);

    /**
     * 异步执行KPI方案统计
     *
     * @param kpiPlanList KPI方案集合
     */
    void batchAddKpiPlanTaskResult(List<KpiPlan> kpiPlanList);
}

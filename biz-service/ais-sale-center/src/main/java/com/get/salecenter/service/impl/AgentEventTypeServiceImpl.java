package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.AgentEventTypeMapper;
import com.get.salecenter.vo.AgentEventTypeVo;
import com.get.salecenter.entity.AgentEventType;
import com.get.salecenter.service.IAgentEventTypeService;
import com.get.salecenter.dto.AgentEventTypeDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/10/19
 * @TIME: 15:26
 * @Description:
 **/
@Service
public class AgentEventTypeServiceImpl implements IAgentEventTypeService {
    @Resource
    private AgentEventTypeMapper typeMapper;
    @Resource
    private UtilService utilService;

    @Override
    public List<AgentEventTypeVo> getAllEventType() {
//        List<AgentEventType> agentEventTypes = typeMapper.selectAll();
        List<AgentEventType> agentEventTypes = typeMapper.selectList(Wrappers.<AgentEventType>lambdaQuery());
        return agentEventTypes.stream().map(agentEventType ->
                BeanCopyUtils.objClone(agentEventType, AgentEventTypeVo::new)).collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addAgentEventType(List<AgentEventTypeDto> agentEventTypeDtoList) {
        if (GeneralTool.isEmpty(agentEventTypeDtoList)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        for (AgentEventTypeDto eventTypeVo : agentEventTypeDtoList) {
            if (GeneralTool.isEmpty(eventTypeVo.getId())) {
                AgentEventType agentEventType = BeanCopyUtils.objClone(eventTypeVo, AgentEventType::new);
                utilService.updateUserInfoToEntity(agentEventType);
                agentEventType.setViewOrder(typeMapper.getMaxViewOrder());
                typeMapper.insertSelective(agentEventType);
            } else {
                AgentEventType agentEventType = BeanCopyUtils.objClone(eventTypeVo, AgentEventType::new);
                utilService.updateUserInfoToEntity(agentEventType);
                typeMapper.updateById(agentEventType);
            }

        }
    }

    @Override
    public List<AgentEventTypeVo> getAgentEventTypeDtos(AgentEventTypeDto agentEventTypeDto, Page page) {
//        Example example = new Example(AgentEventType.class);
//        Example.Criteria criteria = example.createCriteria();
//        if (GeneralTool.isNotEmpty(agentEventTypeDto)) {
//            if (GeneralTool.isNotEmpty(agentEventTypeDto.getKeyWord())) {
//                criteria.andLike("typeName", "%" + agentEventTypeDto.getKeyWord() + "%");
//            }
//        }
//        example.orderBy("viewOrder").desc();
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        List<AgentEventType> agentEventTypes = typeMapper.selectByExample(example);
//        page.restPage(agentEventTypes);

        LambdaQueryWrapper<AgentEventType> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(agentEventTypeDto)) {
            if (GeneralTool.isNotEmpty(agentEventTypeDto.getKeyWord())) {
                lambdaQueryWrapper.like(AgentEventType::getTypeName, agentEventTypeDto.getKeyWord());
            }
        }
        lambdaQueryWrapper.orderByDesc(AgentEventType::getViewOrder);
        IPage<AgentEventType> iPage = typeMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
        List<AgentEventType> agentEventTypes = iPage.getRecords();
        page.setAll((int) iPage.getTotal());
        return agentEventTypes.stream().map(agentEventType -> BeanCopyUtils.objClone(agentEventType, AgentEventTypeVo::new)).collect(Collectors.toList());
    }

    @Override
    public AgentEventTypeVo updateAgentEventType(AgentEventTypeDto agentEventTypeDto) {
        AgentEventType agentEventType = BeanCopyUtils.objClone(agentEventTypeDto, AgentEventType::new);
        utilService.updateUserInfoToEntity(agentEventType);
        typeMapper.updateById(agentEventType);
        return findAgentEventTypeById(agentEventType.getId());
    }

    @Override
    public void deleteAgentEventType(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        typeMapper.deleteById(id);
    }

    @Override
    public AgentEventTypeVo findAgentEventTypeById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        AgentEventType agentEventType = typeMapper.selectById(id);
        return BeanCopyUtils.objClone(agentEventType, AgentEventTypeVo::new);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void movingOrder(List<AgentEventTypeDto> agentEventTypeDtoList) {
        if (GeneralTool.isEmpty(agentEventTypeDtoList)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        AgentEventType agentEventType1 = BeanCopyUtils.objClone(agentEventTypeDtoList.get(0), AgentEventType::new);
        AgentEventType agentEventType2 = BeanCopyUtils.objClone(agentEventTypeDtoList.get(1), AgentEventType::new);

        Integer viewOrder1 = agentEventType1.getViewOrder();
        agentEventType1.setViewOrder(agentEventType2.getViewOrder());
        agentEventType2.setViewOrder(viewOrder1);
        utilService.updateUserInfoToEntity(agentEventType1);
        utilService.updateUserInfoToEntity(agentEventType2);

        utilService.updateUserInfoToEntity(agentEventType1);
        utilService.updateUserInfoToEntity(agentEventType2);

        typeMapper.updateById(agentEventType1);
        typeMapper.updateById(agentEventType2);

    }


}

package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.salecenter.vo.IncentivePolicyStudentOfferItemVo;
import com.get.salecenter.vo.ReceivableAndPayablePlanVo;
import com.get.salecenter.entity.IncentivePolicyStudentOfferItem;
import com.get.salecenter.service.IIncentivePolicyStudentOfferItemService;
import com.get.salecenter.service.IReceivableAndPayablePlanService;
import com.get.salecenter.dto.IncentivePolicyAddOfferItemDto;
import com.get.salecenter.dto.IncentivePolicyStudentOfferItemDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * Incentive奖励政策学生统计
 *
 * <AUTHOR>
 * @since 2023-03-13
 */

@Api(tags = "Incentive奖励政策学生统计")
@RestController
@RequestMapping("sale/incentivePolicyStudentOfferItem")
public class IncentivePolicyStudentOfferItemController {

    @Resource
    private IIncentivePolicyStudentOfferItemService incentivePolicyStudentOfferItemService;
    @Resource
    private IReceivableAndPayablePlanService receivableAndPayablePlanService;

    /**
     * @Description :符合策略学生统计列表数据
     * @Param [page]
     */
    @ApiOperation(value = "学生统计列表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生/奖励政策/学生统计/列表")
    @PostMapping("policyStudentData")
    public ResponseBo<IncentivePolicyStudentOfferItemVo> policyStudentData(@RequestBody SearchBean<IncentivePolicyStudentOfferItemDto> page) {
        List<IncentivePolicyStudentOfferItemVo> datas = incentivePolicyStudentOfferItemService.getPolicyStudentDatas(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 导出Excel
     *
     * @param response
     * @param incentivePolicyStudentOfferItemDto
     */
    @ApiOperation(value = "导出Excel", notes = "")
    @PostMapping("/exportDataExcel")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生/奖励政策/学生统计/导出Excel")
    @ResponseBody
    public void exportDataExcel(HttpServletResponse response, @RequestBody IncentivePolicyStudentOfferItemDto incentivePolicyStudentOfferItemDto) {
        incentivePolicyStudentOfferItemService.exportDataExcel(response, incentivePolicyStudentOfferItemDto);
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description :新增手动计入
     * @Param [incentivePolicyVo]
     */
    @ApiOperation(value = "增加手动计入", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生/奖励政策/学生统计/增加手动计入")
    @PostMapping("addDiyPolicyStudent")
    public ResponseBo<String> addDiyPolicyStudent(@RequestBody @Validated(IncentivePolicyAddOfferItemDto.Add.class) IncentivePolicyAddOfferItemDto incentivePolicyAddOfferItemDto) {
        return new ResponseBo<>(incentivePolicyStudentOfferItemService.addDiyPolicyStudent(incentivePolicyAddOfferItemDto));
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description :手动剔除
     * @Param [ids]
     */
    @ApiOperation(value = "设置手动剔除", notes = "id集合")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/学生/奖励政策/学生统计/设置手动剔除")
    @PostMapping("deleteDiyPolicyStudent")
    public ResponseBo deleteDiyPolicyStudent(@RequestBody List<Long> ids) {
        incentivePolicyStudentOfferItemService.deleteDiyPolicyStudent(ids);
        return ResponseBo.ok();
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description :取消手动设置列表
     * @Param [ids]
     */
    @ApiOperation(value = "取消手动设置", notes = "id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生/奖励政策/学生统计/取消手动设置")
    @PostMapping("cancelDiyPolicyStudent/{id}")
    public ResponseBo cancelDiyPolicyStudent(@PathVariable("id") Long id) {
        incentivePolicyStudentOfferItemService.cancelDiyPolicyStudent(id);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :财务批量结算学生计划
     * @Param [ids]
     */
    @ApiOperation(value = "财务批量结算", notes = "fkIncentivePolicyId奖励策略ID")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/学生/奖励政策/学生统计/财务批量结算")
    @PostMapping("settleSelectPolicyStudent/{fkIncentivePolicyId}")
    public ResponseBo settleSelectPolicyStudent(@PathVariable("fkIncentivePolicyId") Long fkIncentivePolicyId) {
        incentivePolicyStudentOfferItemService.settleSelectPolicyStudent(fkIncentivePolicyId);
        return  ResponseBo.ok();
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description :财务批量取消结算
     * @Param [ids]
     */
    @ApiOperation(value = "财务批量取消结算", notes = "fkIncentivePolicyId")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/学生/奖励政策/学生统计/财务批量取消结算")
    @PostMapping("cancelSettlePolicyStudent/{fkIncentivePolicyId}")
    public ResponseBo cancelSettlePolicyStudent(@PathVariable("fkIncentivePolicyId") Long fkIncentivePolicyId) {
        incentivePolicyStudentOfferItemService.cancelSettlePolicyStudent(fkIncentivePolicyId);
        return ResponseBo.ok();
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description :财务批量结算统计数量
     * @Param [ids]
     */
    @ApiOperation(value = "获取财务批量结算统计数量", notes = "fkIncentivePolicyId奖励策略ID")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/学生/奖励政策/学生统计/获取财务批量结算统计数量")
    @PostMapping("settleSelectPolicyStudentCount/{fkIncentivePolicyId}")
    public  ResponseBo<Integer> settleSelectPolicyStudentCount(@PathVariable("fkIncentivePolicyId") Long fkIncentivePolicyId) {
        return new ResponseBo<>(incentivePolicyStudentOfferItemService.settleSelectPolicyStudentCount(fkIncentivePolicyId));
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description :查看已结算应收应付详情
     * @Param [ids]
     */
    @ApiOperation(value = "查看已结算应收应付详情", notes = "计划ID")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/学生/奖励政策/学生统计/查看已结算应收应付详情")
    @PostMapping("settleInfoById/{id}")
    public ResponseBo<ReceivableAndPayablePlanVo> settleInfoById(@PathVariable("id") Long id) {
        IncentivePolicyStudentOfferItem incentivePolicyStudentOfferItem = incentivePolicyStudentOfferItemService.getById(id);
        return receivableAndPayablePlanService.details(incentivePolicyStudentOfferItem.getFkReceivablePlanId(),incentivePolicyStudentOfferItem.getFkPayablePlanId());
    }

}

package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.entity.AnnualConferenceRegistrationSponsorship;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface AnnualConferenceRegistrationSponsorshipMapper extends BaseMapper<AnnualConferenceRegistrationSponsorship> {
    /**
     * @return int
     * @Description :新增
     * @Param [record]
     * <AUTHOR>
     */
    int insertSelective(AnnualConferenceRegistrationSponsorship record);

    /**
     * @return java.lang.Integer
     * @Description :查找该赞助内容已经有多少次数了
     * @Param [benefits]
     * <AUTHOR>
     */
    Integer getCount(String benefits);
}
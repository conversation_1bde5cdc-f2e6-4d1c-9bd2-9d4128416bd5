package com.get.salecenter.service.impl;

import cn.hutool.extra.mail.MailUtil;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.ConventionRegistrationMapper;
import com.get.salecenter.dto.SendMailDto;
import com.get.salecenter.entity.ConventionRegistration;
import com.get.salecenter.service.ISendMailService;
import com.get.salecenter.utils.MailTemplateUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2021/7/1 16:37
 * @verison: 1.0
 * @description:
 */
@Service
public class SendMailServiceImpl implements ISendMailService {
    //    @Resource
//    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private ConventionRegistrationMapper conventionRegistrationMapper;

    @Override
    public void send(SendMailDto sendMailDto) {
        //标题
        String title = "Hotel Reservation |  2022 GET-GEA Annual Conference";
        //要抄送人的邮箱
        //String ccEmail = feignSystemCenter.getConfigValueByConfigKey(ProjectKeyEnum.CONVENTION_REGISTRATION_CCEMAIL.key);
        String ccEmail = "<EMAIL>";
        //获取邮箱模板
        String template = MailTemplateUtils.getMailTemplate(sendMailDto.getName(), sendMailDto.getReceiptCode(), "email2.html");
        //发送并抄送邮件
        MailUtil.send(sendMailDto.getEmail(), ccEmail, null, title, template, true);
        System.out.println("发送邮件成功");
    }

    @Override
    public List<ConventionRegistration> getConventionRegistrationByReceiptCode(String receiptCode) {
        List<ConventionRegistration> conventionRegistrations = conventionRegistrationMapper.getConventionRegistrationByReceiptCode(receiptCode);
        if (GeneralTool.isEmpty(conventionRegistrations)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_is_empty"));
        }
        return conventionRegistrations;
    }

    @Override
    public List<String> getReceiptCodesByConventionId(Long conventionId) {
        List<String> receiptCodes = conventionRegistrationMapper.getReceiptCodesByConventionId(conventionId);
        if (GeneralTool.isEmpty(receiptCodes)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("receipt_code_null"));
        }
        return receiptCodes;
    }
}

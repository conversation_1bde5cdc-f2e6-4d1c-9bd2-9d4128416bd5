package com.get.salecenter.service.impl;

import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.entity.Institution;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.salecenter.dao.sale.NameLabelMapper;
import com.get.salecenter.vo.NameLabelVo;
import com.get.salecenter.entity.NameLabel;
import com.get.salecenter.service.NameLabelService;
import com.get.salecenter.dto.NameLabelSearchDto;
import com.get.salecenter.dto.NameLabelDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2024/3/25
 * @TIME: 10:59
 * @Description:
 **/
@Service
public class NameLabelServiceImpl extends ServiceImpl<NameLabelMapper, NameLabel> implements NameLabelService {

    @Resource
    private NameLabelMapper nameLabelMapper;

    @Resource
    private IInstitutionCenterClient institutionCenterClient;

    @Resource
    private UtilService utilService;
    @Override
    public List<NameLabelVo> datas(NameLabelSearchDto nameLabelSearchDto, Page page) {
        LambdaQueryWrapper<NameLabel> wrapper = Wrappers.lambdaQuery();
        if(GeneralTool.isNotEmpty(nameLabelSearchDto.getFkTableName())){
            wrapper.eq(NameLabel::getFkTableName, nameLabelSearchDto.getFkTableName());
        }

        //模糊搜索目标名称
        if(GeneralTool.isNotEmpty(nameLabelSearchDto.getKeyword())){
            List<Long> institutionIds = new ArrayList<>();
            //后面可能有多个类型，使用switch
            switch (nameLabelSearchDto.getFkTableName()){
                case "m_institution":
                    Result<List<Long>> resultData = institutionCenterClient.getInstitutionIdsByKeyword(nameLabelSearchDto.getKeyword());
                    if (resultData.isSuccess() && resultData.getData() != null) {
                        institutionIds = resultData.getData();
                    }
                    break;
                default:
                    Result<List<Long>> result1 = institutionCenterClient.getInstitutionIdsByKeyword(nameLabelSearchDto.getKeyword());
                    if (result1.isSuccess() && result1.getData() != null) {
                        institutionIds = result1.getData();
                    }
                    break;
            }
            if( GeneralTool.isNotEmpty(institutionIds)){
                if(GeneralTool.isNotEmpty(nameLabelSearchDto.getFkTableName())){
                    wrapper.in(NameLabel::getFkTableId,institutionIds);
                }
                if(GeneralTool.isEmpty(nameLabelSearchDto.getFkTableName())){
                    List<Long> finalInstitutionIds = institutionIds;
                    wrapper.and(w->
                               w.eq(NameLabel::getFkTableName,"m_institution")
                              .in(NameLabel::getFkTableId, finalInstitutionIds));
                }
            }


        }

        //模糊搜索标签
        if(GeneralTool.isNotEmpty(nameLabelSearchDto.getLabelKeyword())){
            wrapper.like(NameLabel::getLabel, nameLabelSearchDto.getLabelKeyword());
        }

        wrapper.orderByDesc(NameLabel::getViewOrder);

        IPage<NameLabel> iPage = nameLabelMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<NameLabel> nameLabels = iPage.getRecords();
        if (GeneralTool.isEmpty(nameLabels)){
            return Collections.emptyList();
        }
        page.setAll((int) iPage.getTotal());
        List<NameLabelVo> nameLabelVos = BeanCopyUtils.copyListProperties(nameLabels, NameLabelVo::new);
        //获取对应学校名
        Set<Long> fkInstitutionIds = nameLabelVos.stream().filter(i -> "m_institution".equals(i.getFkTableName())).map(NameLabelVo::getFkTableId).collect(Collectors.toSet());
        Result<Map<Long, String>> institutionNamesResult = institutionCenterClient.getInstitutionNamesByIds(fkInstitutionIds);
        if (institutionNamesResult.isSuccess() && institutionNamesResult.getData() != null) {
            Map<Long, String> institutionNamesMap = institutionNamesResult.getData();
            nameLabelVos.forEach(i -> {
                if ("m_institution".equals(i.getFkTableName())) {
                    i.setType("学校");
                    i.setName(institutionNamesMap.get(i.getFkTableId()));
                }
            });
        }
        return nameLabelVos;
    }

    @Override
    public NameLabelVo findNameLabelById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        NameLabel nameLabel = nameLabelMapper.selectById(id);
        NameLabelVo nameLabelVo =BeanCopyUtils.objClone(nameLabel, NameLabelVo::new);
        Result<Institution> result = institutionCenterClient.getInstitutionById(nameLabel.getFkTableId());
        if (result.isSuccess() && result.getData() != null) {
            Institution institution = result.getData();
            nameLabelVo.setName(institution.getName() + "（"+institution.getNameChn()+"）");
        }
        if("m_institution".equals(nameLabelVo.getFkTableName())){
            nameLabelVo.setType(TableEnum.INSTITUTION.value);
        }
        return nameLabelVo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long addNameLabel(NameLabelDto nameLabelDto) {
        if (GeneralTool.isEmpty(nameLabelDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        List<NameLabel> nameLabels = nameLabelMapper.selectList(Wrappers.<NameLabel>lambdaQuery().eq(NameLabel::getFkTableName, nameLabelDto.getFkTableName()).eq(NameLabel::getFkTableId, nameLabelDto.getFkTableId()));
        if (GeneralTool.isNotEmpty(nameLabels)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("EXISTENCE_LABEL"));
        }
        NameLabel nameLabel = BeanCopyUtils.objClone(nameLabelDto, NameLabel::new);
        utilService.setCreateInfo(nameLabel);
        //默认标签设置在名字前面
        if(GeneralTool.isEmpty(nameLabelDto.getPositionType())){
            nameLabel.setPositionType(0);
        }
        //获取最大排序
        NameLabel maxViewNameLabel = nameLabelMapper.selectOne(Wrappers.<NameLabel>lambdaQuery()
                .select(NameLabel::getViewOrder).orderByDesc(NameLabel::getViewOrder).last("limit 1"));
        Integer viewOrder = GeneralTool.isEmpty(maxViewNameLabel) ? 0 : maxViewNameLabel.getViewOrder() + 1;
        nameLabel.setViewOrder(viewOrder);

        nameLabelMapper.insert(nameLabel);
        return nameLabel.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        NameLabel nameLabel = nameLabelMapper.selectById(id);
        if (GeneralTool.isEmpty(nameLabel)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }

        int delete = nameLabelMapper.deleteById(id);
        if (delete <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public NameLabelVo updateNameLabel(NameLabelDto vo) {
        if (GeneralTool.isEmpty(vo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        NameLabel nameLabel = nameLabelMapper.selectById(vo.getId());
        if (GeneralTool.isEmpty(nameLabel)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        List<NameLabel> nameLabels = nameLabelMapper.selectList(Wrappers.<NameLabel>lambdaQuery()
                .eq(NameLabel::getFkTableName, vo.getFkTableName())
                .eq(NameLabel::getFkTableId, vo.getFkTableId())
                .ne(NameLabel::getId, vo.getId()));
        if (GeneralTool.isNotEmpty(nameLabels)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("EXISTENCE_LABEL"));
        }
        NameLabel nameLabel1 = BeanCopyUtils.objClone(vo, NameLabel::new);
        utilService.setUpdateInfo(nameLabel1);

        //默认标签设置在名字前面
        if(GeneralTool.isEmpty(nameLabel1.getPositionType())){
            nameLabel.setPositionType(0);
        }
        int update = nameLabelMapper.updateById(nameLabel1);

        if (update <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }

        return findNameLabelById(nameLabel1.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void movingOrder(Integer start,Integer end){
        LambdaQueryWrapper<NameLabel> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (end > start){
            lambdaQueryWrapper.between(NameLabel::getViewOrder,start,end).orderByDesc(NameLabel::getViewOrder);
        }else {
            lambdaQueryWrapper.between(NameLabel::getViewOrder,end,start).orderByDesc(NameLabel::getViewOrder);

        }
        List<NameLabel> nameLabels = list(lambdaQueryWrapper);

        //从下上移 列表倒序排序
        List<NameLabel> updateList = Lists.newArrayList();
        if (end > start){
            int finalEnd = end;
            List<NameLabel> sortedList = Lists.newArrayList();
            NameLabel policy = nameLabels.get(nameLabels.size() - 1);
            sortedList.add(policy);
            nameLabels.remove(nameLabels.size() - 1);
            sortedList.addAll(nameLabels);
            for (NameLabel nameLabel : sortedList) {
                nameLabel.setViewOrder(finalEnd);
                finalEnd--;
            }
            updateList.addAll(sortedList);
        }else {
            int finalStart = start;
            List<NameLabel> sortedList = Lists.newArrayList();
            NameLabel policy = nameLabels.get(0);
            nameLabels.remove(0);
            sortedList.addAll(nameLabels);
            sortedList.add(policy);
            for (NameLabel nameLabel : sortedList) {
                nameLabel.setViewOrder(finalStart);
                finalStart--;
            }
            updateList.addAll(sortedList);
        }

        if (GeneralTool.isNotEmpty(updateList)){
            boolean batch = updateBatchById(updateList);
            if (!batch){
                throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
            }
        }
    }

    @Override
    public Map<Long,NameLabel> getNameLabelListByFkTableName(String fkTableName){
        if(GeneralTool.isEmpty(fkTableName)){
            return null;
        }
        List<NameLabel> nameLabels = nameLabelMapper.selectList(Wrappers.<NameLabel>lambdaQuery()
                .eq(NameLabel::getFkTableName, fkTableName)
                .groupBy(NameLabel::getFkTableName,NameLabel::getFkTableId));
        if(GeneralTool.isEmpty(nameLabels)){
            return null;
        }
        return nameLabels.stream().collect(Collectors.toMap(NameLabel::getFkTableId, Function.identity()));

    }
}

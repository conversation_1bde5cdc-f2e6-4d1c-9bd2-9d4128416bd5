package com.get.salecenter.service;

import com.get.common.result.SearchBean;
import com.get.salecenter.vo.ClientContactPersonVo;
import com.get.salecenter.dto.ClientContactPersonDto;

import java.util.List;

/**
 * author:Neil
 * Time: 11:26
 * Date: 2022/8/19
 * Description:
 */
public interface IClientContactPersonService {
    /**
     * 新增客户联系人信息
     * @param contactPersonVo
     * @return
     */
    Long addContactPerson(ClientContactPersonDto contactPersonVo);

    /**
     * 新增客户联系人信息
     * @param data
     * @param page
     * @return
     */
    List<ClientContactPersonVo> getContactPersons(ClientContactPersonDto data, SearchBean<ClientContactPersonDto> page);

    /**
     * 删除客户联系人信息
     * @param id
     */
    void delete(Long id);

    /**
     * 修改接口
     * @param clientContactPersonDto
     * @return
     */
    ClientContactPersonVo updateContactPerson(ClientContactPersonDto clientContactPersonDto);

    /**
     * @return com.get.salecenter.vo.ClientContactPersonVo
     * @Description: 详情
     * @Param [id]
     * <AUTHOR>
     */
    ClientContactPersonVo findContactPersonById(Long id);
}

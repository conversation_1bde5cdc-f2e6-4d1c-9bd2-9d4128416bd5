package com.get.salecenter.service;


import com.get.salecenter.dto.AgentContractFormulaAreaCountryDto;

import java.util.List;
import java.util.Map;

/**
 * @author: Sea
 * @create: 2021/1/6 15:07
 * @verison: 1.0
 * @description:
 */
public interface IAgentContractFormulaAreaCountryService {
    /**
     * @return void
     * @Description :新增
     * @Param [agentContractFormulaAreaCountryDto]
     * <AUTHOR>
     */
    Long addAgentContractFormulaAreaCountry(AgentContractFormulaAreaCountryDto agentContractFormulaAreaCountryDto);

    /**
     * @return void
     * @Description :删除
     * @Param [agentContractFormulaId]
     * <AUTHOR>
     */
    void deleteByFkid(Long agentContractFormulaId);

    /**
     * @Description :通过学生代理合同公式id 查找对应国家名称
     * @Param [agentContractFormulaIds]
     * <AUTHOR>
     */
    Map<Long, String> getCountryNameMapByFkids(List<Long> agentContractFormulaIds);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :通过学生代理合同公式id 查找对应国家id
     * @Param [agentContractFormulaId]
     * <AUTHOR>
     */
    List<Long> getCountryIdListByFkid(Long agentContractFormulaId);
}

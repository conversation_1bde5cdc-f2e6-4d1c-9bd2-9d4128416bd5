package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.log.exception.GetServiceException;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.vo.ConventionProcedureVo;
import com.get.salecenter.service.IConventionProcedureService;
import com.get.salecenter.dto.ConventionProcedureCopyDto;
import com.get.salecenter.dto.ConventionProcedureDto;
import com.get.salecenter.dto.MediaAndAttachedDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @author: Sea
 * @create: 2020/7/2 11:01
 * @verison: 1.0
 * @description: 峰会流程管理控制器
 */
@Api(tags = "峰会流程管理")
@RestController
@RequestMapping("sale/conventionProcedure")
public class ConventionProcedureController {

    @Resource
    private IConventionProcedureService conventionProcedureService;

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/峰会流程管理/峰会流程详情")
    @GetMapping("/{id}")
    public ResponseBo<ConventionProcedureVo> detail(@PathVariable("id") Long id) {
        ConventionProcedureVo data = conventionProcedureService.findConventionProcedureById(id);
        return new ResponseBo<>(data);
    }

    /**
     * 新增信息
     *
     * @param conventionProcedureDto
     * @return
     * @
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/峰会流程管理/新增峰会流程")
    @PostMapping("add")
    public ResponseBo add(@RequestBody  @Validated(ConventionProcedureDto.Add.class) ConventionProcedureDto conventionProcedureDto) {
        return SaveResponseBo.ok(conventionProcedureService.addConventionProcedure(conventionProcedureDto));
    }

    /**
     * 删除信息
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/峰会流程管理/删除峰会流程")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        conventionProcedureService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param conventionProcedureDto
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/峰会流程管理/更新峰会流程")
    @PostMapping("update")
    public ResponseBo<ConventionProcedureVo> update(@RequestBody  @Validated(ConventionProcedureDto.Update.class) ConventionProcedureDto conventionProcedureDto) {
        return UpdateResponseBo.ok(conventionProcedureService.updateConventionProcedure(conventionProcedureDto));
    }

    /**
     * 列表数据
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "列表数据", notes = "fkConventionId该峰会id(一定有)")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/峰会流程管理/查询峰会流程")
    @PostMapping("datas")
    public ResponseBo<ConventionProcedureVo> datas(@RequestBody SearchBean<ConventionProcedureDto> page) {
        List<ConventionProcedureVo> datas = conventionProcedureService.getConventionProcedures(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 上移下移
     *
     * @param conventionProcedureDtos
     * @return
     * @
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/峰会流程管理/移动顺序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<ConventionProcedureDto> conventionProcedureDtos) {
        conventionProcedureService.movingOrder(conventionProcedureDtos);
        return ResponseBo.ok();
    }

    /**
     * @param mediaAttachedVo
     * @return
     * @
     */
    @ApiOperation(value = "保存文件")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/峰会管理/保存附件资料")
    @PostMapping("addMediaAndAttached")
    public ResponseBo addMediaAndAttached(@RequestBody @Validated(MediaAndAttachedDto.Add.class) MediaAndAttachedDto mediaAttachedVo) {
        return UpdateResponseBo.ok(conventionProcedureService.addConventionMedia(mediaAttachedVo));
    }

    /**
     * 峰会流程附件类型下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "峰会流程附件类型下拉框数据", notes = "")
    @PostMapping("findMediaAndAttachedType")
    public ResponseBo findMediaAndAttachedType() {
        List<Map<String, Object>> datas = conventionProcedureService.findMediaAndAttachedType();
        return new ListResponseBo<>(datas);
    }

    @PostMapping("copyConventionProcedure")
    @ApiOperation(value = "复制峰会活动流程", notes = "")
    public ResponseBo copyConventionProcedure(@RequestBody ConventionProcedureCopyDto conventionProcedureCopyDto){
        if (GeneralTool.isEmpty(conventionProcedureCopyDto.getId()) || GeneralTool.isEmpty(conventionProcedureCopyDto.getCopyConventionId())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        conventionProcedureService.copyConventionProcedure(conventionProcedureCopyDto);
        return ResponseBo.ok();
    }

    @GetMapping("summitPersonnelAllocation")
    @ApiOperation(value = "峰会人员分配", notes = "")
    public ResponseBo summitPersonnelAllocation() {
        conventionProcedureService.summitPersonnelAllocation();
        return ResponseBo.ok();
    }


}

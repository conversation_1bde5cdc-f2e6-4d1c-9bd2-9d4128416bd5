package com.get.salecenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.common.result.Page;
import com.get.salecenter.vo.StudentOfferRoleAndStaffVo;
import com.get.salecenter.vo.StudentProjectRoleStaffVo;
import com.get.salecenter.vo.StudentRoleAndStaffVo;
import com.get.salecenter.entity.StudentProjectRoleStaff;
import com.get.salecenter.dto.ProjectRoleStaffDto;
import com.get.salecenter.dto.StudentProjectRoleStaffDto;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/11/16
 * @TIME: 10:32
 * @Description:
 **/
public interface IStudentProjectRoleStaffService extends IService<StudentProjectRoleStaff> {
    /**
     * @return java.lang.Long
     * @Description: 新增
     * @Param [projectRoleStaffVo]
     * <AUTHOR>
     */
    Long addProjectRoleStaff(StudentProjectRoleStaffDto projectRoleStaffVo);

    Long batchAddProjectRoleStaff(List<StudentProjectRoleStaffDto> projectRoleStaffVos);

    void batchUpdate(List<StudentProjectRoleStaff> studentProjectRoleStaffs);

    void batchAdd(List<StudentProjectRoleStaff> studentProjectRoleStaffs);

    /**
     * 批量处理角色
     * @param projectRoleStaffDtos
     * @param id
     * @param typeKey
     */
    void batchProcessorPrs(List<ProjectRoleStaffDto> projectRoleStaffDtos, Long id, String typeKey);
    /**
     * @return java.util.List<com.get.salecenter.vo.StudentProjectRoleStaffVo>
     * @Description: 列表
     * @Param [projectRoleStaffVo, page]
     * <AUTHOR>
     */
    List<StudentProjectRoleStaffVo> getProjectRoleStaff(StudentProjectRoleStaffDto projectRoleStaffVo, Page page);

    /**
     * @return java.util.List<com.get.salecenter.vo.StudentProjectRoleStaffVo>
     * @Description: 咨询客户项目成员列表
     * @Param [projectRoleStaffVo, page]
     * <AUTHOR>
     */
    List<StudentProjectRoleStaffVo> getClientProjectRoleStaff(StudentProjectRoleStaffDto projectRoleStaffVo, Page page);


    /**
     * 列表，不进行分页
     *
     * @param studentIds
     * @return
     * @
     */
    List<Map<Long, String>> getProjectRoleStaffNoPage(String tableName, List<Long> studentIds);


    Map<Long, Object> getAFollowUpConsultant(String tableName, Set<Long> clientIds,String key);

    /**
     * @return com.get.salecenter.vo.StudentProjectRoleStaffVo
     * @Description: 详情
     * @Param [id]
     * <AUTHOR>
     */
    StudentProjectRoleStaffVo findProjectRoleStaffById(Long id);


    /**
     * @return java.lang.Long
     * @Description: 修改
     * @Param [projectRoleStaffVo]
     * <AUTHOR>
     */
    StudentProjectRoleStaffVo updateProjectRoleStaff(StudentProjectRoleStaffDto projectRoleStaffVo);


    /**
     * @return void
     * @Description: 删除
     * @Param [id]
     * <AUTHOR>
     */
    void delete(Long id);


    /**
     * @return void
     * @Description: 关闭（根据id关闭方案相关角色）
     * @Param [tableId]
     * <AUTHOR>
     */
    void unableProjectRoleStaff(Long tableId, Long status);


    /**
     * @return java.util.List<com.get.salecenter.vo.StudentRoleAndStaffVo>
     * @Description: 根据table查询name(学习方案)
     * @Param []
     * <AUTHOR>
     */
    List<StudentRoleAndStaffVo> getRoleAndStaffByTableId(Long tableId, String typeKey);

    /**
     * @param dtoIds
     * @return
     */
    Map<Long, List<StudentRoleAndStaffVo>> getRoleAndStaffByTableIds(Set<Long> dtoIds);

    /**
     * @param offerIds
     * @return
     */
    Map<Long, List<StudentOfferRoleAndStaffVo>> getRoleAndStaffByOfferIds(Set<Long> offerIds);

    /**
     * @param offerIds
     * @return
     */
    Map<Long, List<StudentOfferRoleAndStaffVo>> getRoleAndStaffByClientOfferIds(Set<Long> offerIds);

    /**
     * 根据学生申请方案Id和用户ID获取对应角色列表
     *
     * @param fkStudentOfferId
     * @param staffId
     * @return
     */
    List<StudentProjectRoleStaffVo> getProjectRole(Long fkStudentOfferId, List<Long> staffId);


    /**
     * 校验项目成员是否存在
     * @param projectRoleStaffDtos
     * @param tableIds
     * @return
     */
    Boolean verifyProjectRole(List<ProjectRoleStaffDto> projectRoleStaffDtos, Set<Long> tableIds);

    /**
     * 根据申请方案Id获取所有项目成员
     *
     * @return
     */
    List<StudentProjectRoleStaffVo> getAllProjectRoleStaff(Long fkStudentOfferId);

    /**
     * 批量添加
     *
     * @param studentProjectRoleStaffs
     * @return
     */
    Boolean batchAddStudentProjectRoleStaff(List<StudentProjectRoleStaff> studentProjectRoleStaffs);

    Set<Long> getRoleAndStaffByTableIdAndRoles(Long fkStudentOfferId, String key, List<String> roleList);

    List<StudentProjectRoleStaffVo> getStudentProjectRoleStaffList(Long fkStudentOfferId);

    /**
     * 通过表名、id集合、项目成员key，获取任务关联项与项目成员id关系集合
     *
     * @param tableName 表名
     * @param tableIds  id集合
     * @param roleKey   项目成员key
     * @param likeType  模糊查询类型（like、likeLeft、likeRight）
     * @return
     */
    List<StudentProjectRoleStaff> getStaffByTableIdsAndLikeRoleKey(String tableName, Set<Long> tableIds, String roleKey, String likeType);
}

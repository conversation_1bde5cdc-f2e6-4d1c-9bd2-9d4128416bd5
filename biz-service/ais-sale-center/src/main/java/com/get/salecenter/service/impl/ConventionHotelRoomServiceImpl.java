package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.ConventionHotelRoomMapper;
import com.get.salecenter.vo.ConventionHotelRoomVo;
import com.get.salecenter.vo.ConventionHotelVo;
import com.get.salecenter.entity.ConventionHotelRoom;
import com.get.salecenter.service.IConventionHotelRoomService;
import com.get.salecenter.service.IConventionHotelService;
import com.get.salecenter.service.IDeleteService;
import com.get.salecenter.utils.GetDayUtils;
import com.get.salecenter.utils.MyStringUtils;
import com.get.salecenter.dto.ConventionHotelRoomDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: Sea
 * @create: 2020/8/18 11:40
 * @verison: 1.0
 * @description: 酒店房间管理实现类
 */
@Service
public class ConventionHotelRoomServiceImpl implements IConventionHotelRoomService {
    @Resource
    private ConventionHotelRoomMapper conventionHotelRoomMapper;
    @Resource
    private IConventionHotelService conventionHotelService;
    @Resource
    private UtilService utilService;
    @Resource
    private IDeleteService deleteService;

    @Override
    public ConventionHotelRoomVo findConventionHotelRoomById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ConventionHotelRoom conventionHotelRoom = conventionHotelRoomMapper.selectById(id);
        return BeanCopyUtils.objClone(conventionHotelRoom, ConventionHotelRoomVo::new);
    }

    @Override
    public Long addConventionHotelRoom(ConventionHotelRoomDto conventionHotelRoomDto) {
        if (GeneralTool.isEmpty(conventionHotelRoomDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        //通过房型fkid获取已存在的最大系统房号(带前序号的字符串)
        String maxSystemRoomNumString = conventionHotelRoomMapper.getMaxSystemRoomNum(conventionHotelRoomDto.getFkConventionHotelId());
        //最大系统房号(不带前序号的字符串)
        String maxSystemRoomNum;
        //通过房型id获取该酒店房型前序号(房号)
        ConventionHotelVo conventionHotelVo = conventionHotelService.findConventionHotelById(conventionHotelRoomDto.getFkConventionHotelId());
        String preNum = conventionHotelVo.getPreNum();

        if (GeneralTool.isEmpty(maxSystemRoomNumString)) {
            maxSystemRoomNum = "0";
        } else {
            //根据前序号的长度，开始截取
            int length = preNum.length();
            maxSystemRoomNum = maxSystemRoomNumString.substring(length);
        }
        //得到自动生成的系统房号 = 前序号 + 最大系统房号 + 1
        String systemRoomNum = MyStringUtils.getSystemRoomNum(preNum, new Long(maxSystemRoomNum) + 1);
        conventionHotelRoomDto.setSystemRoomNum(systemRoomNum);
        ConventionHotelRoom conventionHotelRoom = BeanCopyUtils.objClone(conventionHotelRoomDto, ConventionHotelRoom::new);
        utilService.updateUserInfoToEntity(conventionHotelRoom);
        conventionHotelRoomMapper.insert(conventionHotelRoom);
        return conventionHotelRoom.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(ConventionHotelRoomDto conventionHotelRoomDto) {
        if (GeneralTool.isEmpty(conventionHotelRoomDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        if (0 == conventionHotelRoomDto.getRooms() || 0 == conventionHotelRoomDto.getDays()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("room_num_stay_day"));
        }
        //住店日期备份记录下
        Date stayDate = conventionHotelRoomDto.getStayDate();
        //通过房型fkid获取已存在的最大系统房号(带前序号的字符串)
        String maxSystemRoomNumString = conventionHotelRoomMapper.getMaxSystemRoomNum(conventionHotelRoomDto.getFkConventionHotelId());
        List<String> systemRoomNumList = getAllSystemRoomNum(conventionHotelRoomDto.getFkConventionHotelId());
        //最大系统房号(不带前序号的字符串)
        String maxSystemRoomNum;
        //添加的总记录数=房间数*住店天数
        for (long rooms = 1; rooms <= conventionHotelRoomDto.getRooms(); rooms++) {
            //通过房型id获取该酒店房型前序号(房号)
            ConventionHotelVo conventionHotelVo = conventionHotelService.findConventionHotelById(conventionHotelRoomDto.getFkConventionHotelId());
            String preNum = conventionHotelVo.getPreNum();

            if (GeneralTool.isEmpty(maxSystemRoomNumString)) {
                maxSystemRoomNum = "0";
            } else {
                //根据前序号的长度，开始截取
                int length = preNum.length();
                maxSystemRoomNum = maxSystemRoomNumString.substring(length);
            }
            //得到自动生成的系统房号 = 前序号 + 最大系统房号 + rooms(要看创建了几间房)
            String systemRoomNum = MyStringUtils.getSystemRoomNum(preNum, new Long(maxSystemRoomNum) + rooms);
            //用于计数
            Long tmpRooms = rooms;
            while (systemRoomNumList.contains(systemRoomNum)) {
                systemRoomNum = MyStringUtils.getSystemRoomNum(preNum, new Long(maxSystemRoomNum) + tmpRooms + 1);
            }
            conventionHotelRoomDto.setSystemRoomNum(systemRoomNum);
            //将住店日期通过备份还原
            conventionHotelRoomDto.setStayDate(stayDate);

            for (long days = 1; days <= conventionHotelRoomDto.getDays(); days++) {
                //日期加1
                Date date = GetDayUtils.addDay(conventionHotelRoomDto.getStayDate(), days);
                conventionHotelRoomDto.setStayDate(date);

                ConventionHotelRoom conventionHotelRoom = BeanCopyUtils.objClone(conventionHotelRoomDto, ConventionHotelRoom::new);
                utilService.updateUserInfoToEntity(conventionHotelRoom);
                conventionHotelRoomMapper.insert(conventionHotelRoom);
            }
        }
    }

    /**
     * 获取所有系统房号list
     *
     * @param fkConventionHotelId
     * @return
     */
    private List<String> getAllSystemRoomNum(Long fkConventionHotelId) {
//        Example example = new Example(ConventionHotelRoom.class);
//        example.setDistinct(true);
//        example.selectProperties("systemRoomNum");
//        example.createCriteria().andEqualTo("fkConventionHotelId",fkConventionHotelId);
//        example.orderBy("systemRoomNum").desc();
        //更改了去重方式，待测试
        List<ConventionHotelRoom> conventionHotelRooms = conventionHotelRoomMapper.selectList(Wrappers.<ConventionHotelRoom>lambdaQuery().eq(ConventionHotelRoom::getFkConventionHotelId, fkConventionHotelId).orderByDesc(ConventionHotelRoom::getSystemRoomNum));
        return conventionHotelRooms.stream().map(ConventionHotelRoom::getSystemRoomNum).distinct().collect(Collectors.toList());
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        //通过房间id查找是否已经被安排入住 用于判断该房间能否删除
        deleteService.deleteValidateHotelRoom(id);
        int i = conventionHotelRoomMapper.deleteById(id);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
        //同时删除该房间中间表数据
        //conventionHotelRoomPersonService.deleteByRoomId(id);
    }

    @Override
    public List<ConventionHotelRoomVo> getConventionHotelRooms(ConventionHotelRoomDto conventionHotelRoomDto, Page page) {
//        Example example = new Example(ConventionHotelRoom.class);
//        Example.Criteria criteria = example.createCriteria();
//        Example.Criteria criteria1 = example.createCriteria();
//        //根据峰会id 查出该峰会下的所有房型id
//        List<Long> conventionHotelIds = conventionHotelService.getConventionHotelIds(conventionHotelRoomDto.getConventionId());
//
//        //设置固定条件-列表显示的都是该峰会下的所有酒店房型开出的房间
//        criteria.andIn("fkConventionHotelId", conventionHotelIds);
//        if (GeneralTool.isNotEmpty(conventionHotelRoomDto)) {
//            //查询条件-酒店名称hotel  房型roomType
//            if (GeneralTool.isNotEmpty(conventionHotelRoomDto.getHotel()) && GeneralTool.isNotEmpty(conventionHotelRoomDto.getRoomType())) {
//                //根据峰会id,酒店名称和房型查询对应id
//                Long id = conventionHotelService.getConventionHotelId(conventionHotelRoomDto.getRoomType(), conventionHotelRoomDto.getHotel(), conventionHotelRoomDto.getConventionId());
//                criteria.andEqualTo("fkConventionHotelId", id);
//            }
//            if (GeneralTool.isNotEmpty(conventionHotelRoomDto.getHotel()) && GeneralTool.isEmpty(conventionHotelRoomDto.getRoomType())) {
//                //根据酒店名称查询对应的ids
//                List<Long> conventionHotelIds1 = conventionHotelService.getConventionHotelIdsByHotel(conventionHotelRoomDto.getHotel(), conventionHotelRoomDto.getConventionId());
//                criteria.andIn("fkConventionHotelId", conventionHotelIds1);
//            }
//            //查询条件是系统房号 or 酒店房号
//            if (GeneralTool.isNotEmpty(conventionHotelRoomDto.getRoomNum())) {
//                criteria1.andLike("systemRoomNum", "%" + conventionHotelRoomDto.getRoomNum() + "%");
//                criteria1.orLike("hotelRoomNum", "%" + conventionHotelRoomDto.getHotelRoomNum() + "%");
//            }
//        }
//        example.and(criteria1);
//        example.orderBy("systemRoomNum").asc();
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        List<ConventionHotelRoom> conventionHotelRooms = conventionHotelRoomMapper.selectByExample(example);
//        page.restPage(conventionHotelRooms);
        LambdaQueryWrapper<ConventionHotelRoom> conventionHotelRoomLambdaQueryWrapper = new LambdaQueryWrapper<>();
        //根据峰会id 查出该峰会下的所有房型id
        List<Long> conventionHotelIds = conventionHotelService.getConventionHotelIds(conventionHotelRoomDto.getConventionId());

        //设置固定条件-列表显示的都是该峰会下的所有酒店房型开出的房间
        conventionHotelRoomLambdaQueryWrapper.in(ConventionHotelRoom::getFkConventionHotelId, conventionHotelIds);
        if (GeneralTool.isNotEmpty(conventionHotelRoomDto)) {
            //查询条件-酒店名称hotel  房型roomType
            if (GeneralTool.isNotEmpty(conventionHotelRoomDto.getHotel()) && GeneralTool.isNotEmpty(conventionHotelRoomDto.getRoomType())) {
                //根据峰会id,酒店名称和房型查询对应id
                Long id = conventionHotelService.getConventionHotelId(conventionHotelRoomDto.getRoomType(), conventionHotelRoomDto.getHotel(), conventionHotelRoomDto.getConventionId());
                conventionHotelRoomLambdaQueryWrapper.eq(ConventionHotelRoom::getFkConventionHotelId, id);
            }
            if (GeneralTool.isNotEmpty(conventionHotelRoomDto.getHotel()) && GeneralTool.isEmpty(conventionHotelRoomDto.getRoomType())) {
                //根据酒店名称查询对应的ids
                List<Long> conventionHotelIds1 = conventionHotelService.getConventionHotelIdsByHotel(conventionHotelRoomDto.getHotel(), conventionHotelRoomDto.getConventionId());
                conventionHotelRoomLambdaQueryWrapper.in(ConventionHotelRoom::getFkConventionHotelId, conventionHotelIds1);
            }
            //查询条件是系统房号 or 酒店房号
            if (GeneralTool.isNotEmpty(conventionHotelRoomDto.getRoomNum())) {
                conventionHotelRoomLambdaQueryWrapper.and(wrapper -> wrapper
                        .like(ConventionHotelRoom::getSystemRoomNum, conventionHotelRoomDto.getRoomNum())
                        .or().like(ConventionHotelRoom::getHotelRoomNum, conventionHotelRoomDto.getHotelRoomNum()));
            }
        }
        conventionHotelRoomLambdaQueryWrapper.orderByAsc(ConventionHotelRoom::getSystemRoomNum);
        IPage<ConventionHotelRoom> iPage = conventionHotelRoomMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), conventionHotelRoomLambdaQueryWrapper);
        List<ConventionHotelRoom> conventionHotelRooms = iPage.getRecords();
        page.setAll((int) iPage.getTotal());

        List<ConventionHotelRoomVo> convertDatas = new ArrayList<>();
        for (ConventionHotelRoom conventionHotelRoom : conventionHotelRooms) {
            //根据fkConventionHotelId 查找对应酒店的名称和房型
            ConventionHotelVo conventionHotelVo = conventionHotelService.findConventionHotelById(conventionHotelRoom.getFkConventionHotelId());

            ConventionHotelRoomVo conventionHotelRoomVo = BeanCopyUtils.objClone(conventionHotelRoom, ConventionHotelRoomVo::new);
            //将要显示的参数set进去
            conventionHotelRoomVo.setHotel(conventionHotelVo.getHotel());
            conventionHotelRoomVo.setRoomType(conventionHotelVo.getRoomType());
            convertDatas.add(conventionHotelRoomVo);
        }
        return convertDatas;
    }

    @Override
    public List<Long> getRoomIdsByHotelId(Long conventionHotelId, String systemRoomNum) {
        return conventionHotelRoomMapper.getRoomIdsByHotelId(conventionHotelId, systemRoomNum);
    }

    @Override
    public List<String> getDates(List<Long> conventionHotelIds) {
        //根据酒店房型id 查出所开房间的入住日期集合
        List<String> list = new ArrayList<>();
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        List<Date> dates = conventionHotelRoomMapper.getDates(conventionHotelIds);
        for (Date date : dates) {
            list.add(dateFormat.format(date));
        }
        return list;
    }

    @Override
    public List<ConventionHotelRoomVo> getConventionHotelRoomDtoList(List<Long> conventionHotelIds, ConventionHotelRoomDto conventionHotelRoomDto, List<String> bdCodes, Long companyId) {
        return conventionHotelRoomMapper.getConventionHotelRoomDtoList(conventionHotelIds, conventionHotelRoomDto,bdCodes,companyId);
    }

    @Override
    public void updateHotelRoomNum(ConventionHotelRoomDto conventionHotelRoomDto) {
        ConventionHotelRoom conventionHotelRoom = BeanCopyUtils.objClone(conventionHotelRoomDto, ConventionHotelRoom::new);
        utilService.updateUserInfoToEntity(conventionHotelRoom);

//        Example example = new Example(ConventionHotelRoom.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("systemRoomNum", conventionHotelRoomDto.getSystemRoomNum());
//        criteria.andEqualTo("fkConventionHotelId", conventionHotelRoomDto.getFkConventionHotelId());
//        conventionHotelRoomMapper.updateByExampleSelective(conventionHotelRoom, example);
        LambdaQueryWrapper<ConventionHotelRoom> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ConventionHotelRoom::getSystemRoomNum, conventionHotelRoomDto.getSystemRoomNum());
        lambdaQueryWrapper.eq(ConventionHotelRoom::getFkConventionHotelId, conventionHotelRoomDto.getFkConventionHotelId());
        conventionHotelRoomMapper.update(conventionHotelRoom, lambdaQueryWrapper);
    }

    /**
     * 非管理员修改酒店床位
     *
     * @param conventionHotelRoomDto
     */
    @Override
    public void updateHotelRoomNumLimited(ConventionHotelRoomDto conventionHotelRoomDto) {
        ConventionHotelRoom conventionHotelRoom = BeanCopyUtils.objClone(conventionHotelRoomDto, ConventionHotelRoom::new);
//        Example example = new Example(ConventionHotelRoom.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("systemRoomNum", conventionHotelRoomDto.getSystemRoomNum());
//        criteria.andEqualTo("fkConventionHotelId", conventionHotelRoomDto.getFkConventionHotelId());
//        List<ConventionHotelRoom> conventionHotelRooms = conventionHotelRoomMapper.selectByExample(example);

        LambdaQueryWrapper<ConventionHotelRoom> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ConventionHotelRoom::getSystemRoomNum, conventionHotelRoomDto.getSystemRoomNum());
        lambdaQueryWrapper.eq(ConventionHotelRoom::getFkConventionHotelId, conventionHotelRoomDto.getFkConventionHotelId());
        List<ConventionHotelRoom> conventionHotelRooms = conventionHotelRoomMapper.selectList(lambdaQueryWrapper);
        for (ConventionHotelRoom hotelRoom : conventionHotelRooms) {
            if (GeneralTool.isNotEmpty(hotelRoom.getHotelRoomNum())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("hotelRoomNum_exists"));
            }
        }
        utilService.updateUserInfoToEntity(conventionHotelRoom);
        conventionHotelRoomMapper.update(conventionHotelRoom, lambdaQueryWrapper);
    }

    @Override
    public List<Long> getRoomIds(Long id, String date) {
        return conventionHotelRoomMapper.getRoomIds(id, date);
    }

    @Override
    public List<Long> getAllRoomIds(List<Long> conventionHotelIds, String roomDate) {
        List<Long> roomIds = conventionHotelRoomMapper.getAllRoomIds(conventionHotelIds, roomDate);
        //防止in()报错
        if (GeneralTool.isEmpty(roomIds)) {
            roomIds.add(0L);
        }
        return roomIds;
    }

    @Override
    public Long addSingleConventionHotelRoom(ConventionHotelRoomDto conventionHotelRoomDto) {
        if (GeneralTool.isEmpty(conventionHotelRoomDto.getSystemRoomNum()) && GeneralTool.isEmpty(conventionHotelRoomDto.getStayDate())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
//        Example example = new Example(ConventionHotelRoom.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkConventionHotelId", conventionHotelRoomDto.getFkConventionHotelId());
//        criteria.andEqualTo("systemRoomNum", conventionHotelRoomDto.getSystemRoomNum());
//        criteria.andEqualTo("stayDate", conventionHotelRoomDto.getStayDate());
//        if (GeneralTool.isNotEmpty(conventionHotelRoomDto.getHotelRoomNum())) {
//            criteria.andEqualTo("hotelRoomNum", conventionHotelRoomDto.getHotelRoomNum());
//        }
//        List<ConventionHotelRoom> conventionHotelRooms = conventionHotelRoomMapper.selectByExample(example);

        LambdaQueryWrapper<ConventionHotelRoom> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ConventionHotelRoom::getFkConventionHotelId, conventionHotelRoomDto.getFkConventionHotelId());
        lambdaQueryWrapper.eq(ConventionHotelRoom::getSystemRoomNum, conventionHotelRoomDto.getSystemRoomNum());
        lambdaQueryWrapper.eq(ConventionHotelRoom::getStayDate, conventionHotelRoomDto.getStayDate());
        if (GeneralTool.isNotEmpty(conventionHotelRoomDto.getHotelRoomNum())) {
            lambdaQueryWrapper.eq(ConventionHotelRoom::getHotelRoomNum, conventionHotelRoomDto.getHotelRoomNum());
        }
        List<ConventionHotelRoom> conventionHotelRooms = conventionHotelRoomMapper.selectList(lambdaQueryWrapper);

        if (GeneralTool.isNotEmpty(conventionHotelRooms)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
        }
//        Example example1 = new Example(ConventionHotelRoom.class);
//        Example.Criteria criteria1 = example1.createCriteria();
//        criteria1.andEqualTo("fkConventionHotelId", conventionHotelRoomDto.getFkConventionHotelId());
//        criteria1.andEqualTo("systemRoomNum", conventionHotelRoomDto.getSystemRoomNum());
//        List<ConventionHotelRoom> conventionHotelRoomList = conventionHotelRoomMapper.selectByExample(example1);

        LambdaQueryWrapper<ConventionHotelRoom> lambdaQueryWrapper_ = new LambdaQueryWrapper<>();
        lambdaQueryWrapper_.eq(ConventionHotelRoom::getFkConventionHotelId, conventionHotelRoomDto.getFkConventionHotelId());
        lambdaQueryWrapper_.eq(ConventionHotelRoom::getSystemRoomNum, conventionHotelRoomDto.getSystemRoomNum());
        if (GeneralTool.isNotEmpty(conventionHotelRoomDto.getHotelRoomNum())) {
            lambdaQueryWrapper_.eq(ConventionHotelRoom::getHotelRoomNum, conventionHotelRoomDto.getHotelRoomNum());
        }
        List<ConventionHotelRoom> conventionHotelRoomList = conventionHotelRoomMapper.selectList(lambdaQueryWrapper_);

        if (GeneralTool.isNotEmpty(conventionHotelRoomList)) {
            if (GeneralTool.isNotEmpty(conventionHotelRoomList.get(0).getHotelRoomNum())) {
                String hotelRoomNum = conventionHotelRoomList.get(0).getHotelRoomNum();
                conventionHotelRoomDto.setHotelRoomNum(hotelRoomNum);
            } else {
                conventionHotelRoomDto.setHotelRoomNum(null);
            }
        }
        ConventionHotelRoom conventionHotelRoom = BeanCopyUtils.objClone(conventionHotelRoomDto, ConventionHotelRoom::new);
        utilService.updateUserInfoToEntity(conventionHotelRoom);
        conventionHotelRoomMapper.insert(conventionHotelRoom);
        return conventionHotelRoom.getId();
    }
}

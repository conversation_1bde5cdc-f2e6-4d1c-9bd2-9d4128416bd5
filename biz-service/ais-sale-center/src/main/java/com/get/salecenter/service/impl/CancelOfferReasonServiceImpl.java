package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.service.impl.GetServiceImpl;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.CancelOfferReasonMapper;
import com.get.salecenter.vo.CancelOfferReasonVo;
import com.get.salecenter.entity.CancelOfferReason;
import com.get.salecenter.service.ICancelOfferReasonService;
import com.get.salecenter.dto.CancelOfferReasonDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: Hardy
 * @create: 2023/1/11 10:50
 * @verison: 1.0
 * @description:
 */
@Service
public class CancelOfferReasonServiceImpl extends GetServiceImpl<CancelOfferReasonMapper, CancelOfferReason> implements ICancelOfferReasonService {


    @Resource
    private UtilService utilService;

    /**
     * 新增
     * @param cancelOfferReasonDtos
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addCancelOfferReason(List<CancelOfferReasonDto> cancelOfferReasonDtos) {
        if (GeneralTool.isEmpty(cancelOfferReasonDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        List<CancelOfferReason> cancelOfferReasons = BeanCopyUtils.copyListProperties(cancelOfferReasonDtos, CancelOfferReason::new);
        Integer maxViewOrder = this.baseMapper.getMaxViewOrder();
        for (CancelOfferReason cancelOfferReason : cancelOfferReasons) {
            utilService.setCreateInfo(cancelOfferReason);
            cancelOfferReason.setViewOrder(maxViewOrder);
            maxViewOrder++;
        }
        int insertCount = cancelOfferReasons.size();
        while (insertCount>0){
            //批量插入 每次插入DEFAULT_BATCH_SIZE = 1000
            boolean b = saveBatch(cancelOfferReasons, DEFAULT_BATCH_SIZE);
            if (!b){
                throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
            }
            insertCount -= DEFAULT_BATCH_SIZE;
        }
    }

    /**
     * 删除
     * @param id
     */
    @Override
    public void deletecancelOfferReason(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        boolean b = removeById(id);
        if (!b){
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }

    /**
     * 更新
     * @param cancelOfferReasonDto
     * @return
     */
    @Override
    public CancelOfferReasonVo updateCancelOfferReason(CancelOfferReasonDto cancelOfferReasonDto) {
        CancelOfferReason cancelOfferReason = BeanCopyUtils.objClone(cancelOfferReasonDto, CancelOfferReason::new);
        utilService.setUpdateInfo(cancelOfferReason);
        boolean b = updateById(cancelOfferReason);
        if (!b){
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
        return findCancelOfferReasonById(cancelOfferReason.getId());
    }


    /**
     * 详情
     * @param id
     * @return
     */
    @Override
    public CancelOfferReasonVo findCancelOfferReasonById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        CancelOfferReason cancelOfferReason = getById(id);
        return BeanCopyUtils.objClone(cancelOfferReason, CancelOfferReasonVo::new);
    }

    /**
     * 列表
     * @param cancelOfferReasonDto
     * @param page
     * @return
     */
    @Override
    public List<CancelOfferReasonVo> getCancelOfferReasonDtos(CancelOfferReasonDto cancelOfferReasonDto, Page page) {
        LambdaQueryWrapper<CancelOfferReason> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(cancelOfferReasonDto)) {
            if (GeneralTool.isNotEmpty(cancelOfferReasonDto.getKeyWord())) {
                lambdaQueryWrapper.like(CancelOfferReason::getReasonName, cancelOfferReasonDto.getKeyWord());
            }
        }
        lambdaQueryWrapper.orderByDesc(CancelOfferReason::getViewOrder);
        IPage<CancelOfferReason> pages = page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
        List<CancelOfferReason> cancelOfferReasons = pages.getRecords();
        page.setAll((int) pages.getTotal());
        if (GeneralTool.isEmpty(cancelOfferReasons)){
            return Collections.emptyList();
        }
        return cancelOfferReasons.stream().map(cancelOfferReason -> BeanCopyUtils.objClone(cancelOfferReason, CancelOfferReasonVo::new)).collect(Collectors.toList());
    }

    /**
     * 排序
     * @param cancelOfferReasonDtos
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void movingOrder(List<CancelOfferReasonDto> cancelOfferReasonDtos) {
        if (GeneralTool.isEmpty(cancelOfferReasonDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        CancelOfferReason ro = BeanCopyUtils.objClone(cancelOfferReasonDtos.get(0), CancelOfferReason::new);
        Integer oneorder = ro.getViewOrder();
        CancelOfferReason rt = BeanCopyUtils.objClone(cancelOfferReasonDtos.get(1), CancelOfferReason::new);
        Integer twoorder = rt.getViewOrder();
        ro.setViewOrder(twoorder);
        utilService.setUpdateInfo(ro);
        rt.setViewOrder(oneorder);
        utilService.setUpdateInfo(rt);
        boolean b1 = updateById(ro);
        if (!b1) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
        boolean b2 = updateById(rt);
        if (!b2) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
    }

    /**
     * 申请方案终止作废原因下拉数据
     * @return
     */
    @Override
    public List<BaseSelectEntity> getCancelOfferReasonSelect() {
        List<CancelOfferReason> cancelOfferReasons = list(Wrappers.<CancelOfferReason>lambdaQuery().orderByDesc(CancelOfferReason::getViewOrder));
        if (GeneralTool.isEmpty(cancelOfferReasons)){
            return Collections.emptyList();
        }
        return cancelOfferReasons.stream().map(c->{
            BaseSelectEntity baseSelectEntity = new BaseSelectEntity();
            baseSelectEntity.setId(c.getId());
            baseSelectEntity.setName(c.getReasonName());
            baseSelectEntity.setOrder(c.getViewOrder());
            return baseSelectEntity;
        }).collect(Collectors.toList());
    }
}

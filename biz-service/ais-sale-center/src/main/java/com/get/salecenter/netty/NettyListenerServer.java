package com.get.salecenter.netty;

import com.get.salecenter.service.NettyTaskScheduler;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Description: 开启netty推送定时查询
 * @Param
 * @return
 * <AUTHOR>
 */
@Component
public class NettyListenerServer implements ApplicationRunner {
    @Resource
    private NettyTaskScheduler nettyTaskScheduler;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        System.out.println("加载启动定时器sale");
        nettyTaskScheduler.watchDog();
    }

}

package com.get.salecenter.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.consts.SaleCenterConstant;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.common.utils.MD5Utils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.feign.IFinanceCenterClient;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.remindercenter.dto.RemindTaskDto;
import com.get.remindercenter.feign.IReminderCenterClient;
import com.get.salecenter.dao.sale.StudentOfferItemMapper;
import com.get.salecenter.dao.sale.StudentOfferItemPaymentStatusMapper;
import com.get.salecenter.dto.StudentOfferItemPaymentStatusDto;
import com.get.salecenter.dto.StudentOfferItemPaymentStatusRemarkDto;
import com.get.salecenter.dto.StudentOfferItemPaymentStatusToAppDto;
import com.get.salecenter.entity.Agent;
import com.get.salecenter.entity.StudentOfferItem;
import com.get.salecenter.entity.StudentOfferItemPaymentStatus;
import com.get.salecenter.entity.StudentProjectRoleStaff;
import com.get.salecenter.service.IAgentService;
import com.get.salecenter.service.IStudentOfferItemPaymentStatusService;
import com.get.salecenter.service.IStudentOfferItemService;
import com.get.salecenter.service.IStudentProjectRoleStaffService;
import com.get.salecenter.utils.MyStringUtils;
import com.get.salecenter.vo.StudentOfferItemPaymentStatusVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 服务实现类
 */
@Slf4j
@Service
public class StudentOfferItemPaymentStatusServiceImpl extends ServiceImpl<StudentOfferItemPaymentStatusMapper, StudentOfferItemPaymentStatus> implements IStudentOfferItemPaymentStatusService {

    @Resource
    private StudentOfferItemPaymentStatusMapper studentOfferItemPaymentStatusMapper;
    @Resource
    @Lazy
    private IStudentOfferItemService studentOfferItemService;
    @Resource
    private IFinanceCenterClient financeCenterClient;
    @Resource
    private IReminderCenterClient reminderCenterClient;
    @Resource
    private IStudentProjectRoleStaffService studentProjectRoleStaffService;
    @Resource
    private StudentOfferItemMapper offerItemMapper;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    private IAgentService agentService;
    @Resource
    private UtilService utilService;

    @Value("${domainName}")
    private String domainName;
    @Value("${avatarKey.fkPlatformTypes}")
    private String fkPlatformTypes;
    @Value("${avatarKey.appId}")
    private String appId;
    @Value("${avatarKey.appSecret}")
    private String appSecret;

    /**
     * 代付费用日志列表
     *
     * @param vo   查询参数
     * @param page 分页参数
     * @return
     */
    @Override
    public List<StudentOfferItemPaymentStatusVo> datas(StudentOfferItemPaymentStatusDto vo, Page page) {
        if (GeneralTool.isEmpty(vo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        if (GeneralTool.isEmpty(vo.getFkStudentOfferItemId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("offerItem_id_null"));
        }
        LambdaQueryWrapper<StudentOfferItemPaymentStatus> lambdaQueryWrapper = Wrappers.<StudentOfferItemPaymentStatus>lambdaQuery()
                .eq(StudentOfferItemPaymentStatus::getFkStudentOfferItemId, vo.getFkStudentOfferItemId());
        IPage<StudentOfferItemPaymentStatus> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        IPage<StudentOfferItemPaymentStatus> pageList = studentOfferItemPaymentStatusMapper.selectPage(iPage, lambdaQueryWrapper);
        List<StudentOfferItemPaymentStatus> studentOfferItemPaymentStatusList = pageList.getRecords();
        page.setAll((int) pageList.getTotal());
        if (GeneralTool.isEmpty(studentOfferItemPaymentStatusList)) {
            return Collections.emptyList();
        }
        List<StudentOfferItemPaymentStatusVo> studentOfferItemPaymentStatusDtoList = BeanCopyUtils.copyListProperties(studentOfferItemPaymentStatusList, StudentOfferItemPaymentStatusVo::new);
        // 获取币种名称
        Map<String, String> currencyName = Maps.newHashMap();
        Set<String> fkCurrencyTypeNums = studentOfferItemPaymentStatusDtoList.stream().map(StudentOfferItemPaymentStatusVo::getFkCurrencyTypeNum).collect(Collectors.toSet());
        Result<Map<String, String>> currencyNameResult = financeCenterClient.getCurrencyNamesByNums(fkCurrencyTypeNums);
        if (currencyNameResult.isSuccess() && GeneralTool.isNotEmpty(currencyNameResult.getData())) {
            currencyName = currencyNameResult.getData();
        }
        // 设置属性内容
        for (StudentOfferItemPaymentStatusVo studentOfferItemPaymentStatusDto : studentOfferItemPaymentStatusDtoList) {
            studentOfferItemPaymentStatusDto.setFkCurrencyTypeNumName(currencyName.get(studentOfferItemPaymentStatusDto.getFkCurrencyTypeNum()));
            studentOfferItemPaymentStatusDto.setPaymentTypeName(ProjectExtraEnum.getInitialValueByKey(studentOfferItemPaymentStatusDto.getPaymentType(), ProjectExtraEnum.PAYMENT_TYPE));
            studentOfferItemPaymentStatusDto.setPaymentStatusName(ProjectExtraEnum.getInitialValueByKey(studentOfferItemPaymentStatusDto.getPaymentStatus(), ProjectExtraEnum.PAYMENT_STATUS));
        }
        return studentOfferItemPaymentStatusDtoList;
    }

    /**
     * 新增代付费用日志
     *
     * @param studentOfferItemPaymentStatusDto 新增代付费用日志参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(StudentOfferItemPaymentStatusDto studentOfferItemPaymentStatusDto) {
        if (GeneralTool.isEmpty(studentOfferItemPaymentStatusDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        StudentOfferItemPaymentStatus studentOfferItemPaymentStatus = BeanCopyUtils.objClone(studentOfferItemPaymentStatusDto, StudentOfferItemPaymentStatus::new);
        utilService.setCreateInfo(studentOfferItemPaymentStatus);
        studentOfferItemPaymentStatusMapper.insert(studentOfferItemPaymentStatus);

        // 更新申请计划支付状态
        StudentOfferItem studentOfferItem = studentOfferItemService.getById(studentOfferItemPaymentStatusDto.getFkStudentOfferItemId());
        if (GeneralTool.isEmpty(studentOfferItem)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        // 申请费代付类型
        List<Integer> applicationFeePaymentTypes = Arrays.stream(ProjectExtraEnum.APPLICATION_FEE_PAYMENT_TYPE).map(type -> type.key).collect(Collectors.toList());
        // 学费代付类型
        List<Integer> tuitionPaymentTypes = Arrays.stream(ProjectExtraEnum.TUITION_PAYMENT_TYPE).map(type -> type.key).collect(Collectors.toList());
        // 支付类型
        Integer paymentType = studentOfferItemPaymentStatusDto.getPaymentType();
        // 支付状态
        Integer paymentStatus = studentOfferItemPaymentStatusDto.getPaymentStatus();
        if (applicationFeePaymentTypes.contains(paymentType)) {
            studentOfferItem.setAppFeeStatus(paymentStatus);
        } else if (tuitionPaymentTypes.contains(paymentType)) {
            studentOfferItem.setTuitionStatus(paymentStatus);
        }
        utilService.setUpdateInfo(studentOfferItem);
        studentOfferItemService.updateById(studentOfferItem);
    }

    /**
     * 代付费用日志详情
     *
     * @param id 代付费用日志id
     * @return
     */
    @Override
    public StudentOfferItemPaymentStatusVo detail(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        StudentOfferItemPaymentStatus studentOfferItemPaymentStatus = studentOfferItemPaymentStatusMapper.selectById(id);
        if (GeneralTool.isEmpty(studentOfferItemPaymentStatus)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        return BeanCopyUtils.objClone(studentOfferItemPaymentStatus, StudentOfferItemPaymentStatusVo::new);
    }

    /**
     * 修改代付费用日志备注
     *
     * @param studentOfferItemPaymentStatusRemarkVo 修改代付费用日志备注参数
     * @return
     */
    @Override
    public StudentOfferItemPaymentStatusVo updateRemark(StudentOfferItemPaymentStatusRemarkDto studentOfferItemPaymentStatusRemarkVo) {
        if (GeneralTool.isEmpty(studentOfferItemPaymentStatusRemarkVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if (GeneralTool.isEmpty(studentOfferItemPaymentStatusRemarkVo.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        StudentOfferItemPaymentStatus studentOfferItemPaymentStatus = studentOfferItemPaymentStatusMapper.selectById(studentOfferItemPaymentStatusRemarkVo.getId());
        if (GeneralTool.isEmpty(studentOfferItemPaymentStatus)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        studentOfferItemPaymentStatus.setRemark(studentOfferItemPaymentStatusRemarkVo.getRemark());
        utilService.setUpdateInfo(studentOfferItemPaymentStatus);
        studentOfferItemPaymentStatusMapper.updateById(studentOfferItemPaymentStatus);
        return detail(studentOfferItemPaymentStatusRemarkVo.getId());
    }

    /**
     * 更新申请计划的支付状态，并插入代付日志, 并且发送邮件通知给项目成员
     *
     * @param studentOfferItemPaymentStatusToAppVo 参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePaymentStatusToApp(StudentOfferItemPaymentStatusToAppDto studentOfferItemPaymentStatusToAppVo) {
        log.info("【sale/studentOfferItemPaymentStatus/updatePaymentStatusToApp】请求参数：{}", JSONObject.toJSONString(studentOfferItemPaymentStatusToAppVo));
        if (GeneralTool.isEmpty(studentOfferItemPaymentStatusToAppVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        // 签名验证
        String appSign = studentOfferItemPaymentStatusToAppVo.getSign();
        String fkTableName = studentOfferItemPaymentStatusToAppVo.getFkTableName();
        Long fkTableId = studentOfferItemPaymentStatusToAppVo.getFkTableId();
        Long appTimeMillis = studentOfferItemPaymentStatusToAppVo.getAppTimeMillis();
        // 固定顺序加密
        String password = fkTableName + fkTableId + appTimeMillis + appId + appSecret;
        String sign = MD5Utils.encrypt(password);
        if (!appSign.equals(sign)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("signature_verification_failed"));
        }
        long currentTimeMillis = System.currentTimeMillis();
        // 检查时间戳是否在有效范围内 10s
        if (currentTimeMillis - appTimeMillis > 10000) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("request_has_expired"));
        }

        // 邮件接收人列表
        Set<Long> emailToStaffIds = Sets.newHashSet();
        // 邮件模板信息
        Map<String, String> map = Maps.newHashMap();
        // 学生姓名
        String studentName = "";
        // 代理姓名
        String agentName = "";
        // 申请国家
        String fkAreaCountryName = "";
        // 申请学校
        String fkInstitutionName = "";
        // 申请课程
        String fkCourseName = "";
        // 支付类型
        String paymentTypeName = ProjectExtraEnum.getInitialValueByKey(studentOfferItemPaymentStatusToAppVo.getPaymentType(), ProjectExtraEnum.PAYMENT_TYPE);
        map.put("paymentTypeName", paymentTypeName);
        // 支付币种
        Map<String, String> currencyName = Maps.newHashMap();
        Result<Map<String, String>> currencyNameResult = financeCenterClient.getCurrencyNamesByNums(Collections.singleton(studentOfferItemPaymentStatusToAppVo.getFkCurrencyTypeNum()));
        if (currencyNameResult.isSuccess() && GeneralTool.isNotEmpty(currencyNameResult.getData())) {
            currencyName = currencyNameResult.getData();
        }
        String fkCurrencyTypeNumName = currencyName.get(studentOfferItemPaymentStatusToAppVo.getFkCurrencyTypeNum());
        map.put("fkCurrencyTypeNumName", fkCurrencyTypeNumName);
        // 支付金额
        String paidAmount = String.valueOf(studentOfferItemPaymentStatusToAppVo.getPaidAmount());
        map.put("paidAmount", paidAmount);
        // 支付状态
        String paymentStatusName = ProjectExtraEnum.getInitialValueByKey(studentOfferItemPaymentStatusToAppVo.getPaymentStatus(), ProjectExtraEnum.PAYMENT_STATUS);
        map.put("paymentStatusName", paymentStatusName);
        // 备注
        String remark = studentOfferItemPaymentStatusToAppVo.getRemark();
        map.put("remark", remark);
        // 申请计划详情页url
        String studentOfferItemDetail = "";
        // 留学申请计划
        if (TableEnum.SALE_STUDENT_OFFER_ITEM.key.equals(studentOfferItemPaymentStatusToAppVo.getFkTableName())) {
            Long fkStudentOfferItemId = studentOfferItemPaymentStatusToAppVo.getFkTableId();
            StudentOfferItem studentOfferItem = studentOfferItemService.getById(fkStudentOfferItemId);
            if (GeneralTool.isEmpty(studentOfferItem)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
            }
            StudentOfferItemPaymentStatus studentOfferItemPaymentStatus = BeanCopyUtils.objClone(studentOfferItemPaymentStatusToAppVo, StudentOfferItemPaymentStatus::new);
            studentOfferItemPaymentStatus.setFkStudentOfferItemId(fkStudentOfferItemId);
            utilService.setCreateInfo(studentOfferItemPaymentStatus);
            studentOfferItemPaymentStatusMapper.insert(studentOfferItemPaymentStatus);

            // 申请费代付类型
            List<Integer> applicationFeePaymentTypes = Arrays.stream(ProjectExtraEnum.APPLICATION_FEE_PAYMENT_TYPE).map(type -> type.key).collect(Collectors.toList());
            // 学费代付类型
            List<Integer> tuitionPaymentTypes = Arrays.stream(ProjectExtraEnum.TUITION_PAYMENT_TYPE).map(type -> type.key).collect(Collectors.toList());
            // 支付类型
            Integer paymentType = studentOfferItemPaymentStatusToAppVo.getPaymentType();
            // 支付状态
            Integer paymentStatus = studentOfferItemPaymentStatusToAppVo.getPaymentStatus();
            if (applicationFeePaymentTypes.contains(paymentType)) {
                studentOfferItem.setAppFeeStatus(paymentStatus);
            } else if (tuitionPaymentTypes.contains(paymentType)) {
                studentOfferItem.setTuitionStatus(paymentStatus);
            }
            utilService.setUpdateInfo(studentOfferItem);
            studentOfferItemService.updateById(studentOfferItem);

            // 获取ARC的项目成员列表
            List<StudentProjectRoleStaff> studentProjectRoleStaffs = studentProjectRoleStaffService.getStaffByTableIdsAndLikeRoleKey(
                    TableEnum.SALE_STUDENT_OFFER_ITEM.key, Collections.singleton(fkStudentOfferItemId), "ARC", "like");
            if (GeneralTool.isNotEmpty(studentProjectRoleStaffs)) {
                Set<Long> staffIds = studentProjectRoleStaffs.stream().map(StudentProjectRoleStaff::getFkStaffId).collect(Collectors.toSet());
                emailToStaffIds.addAll(staffIds);
            }

            /**
             * 组装邮件信息
             */
            // 学生姓名
            studentName = offerItemMapper.getStudentNameByItemId(fkStudentOfferItemId);
            map.put("studentName", studentName);
            // 代理名称
            Agent agent = agentService.getById(studentOfferItem.getFkAgentId());
            agentName = Optional.ofNullable(agent.getName()).orElse("");
            map.put("agentName", agentName);
            // 申请国家
            Map<Long, String> countryNamesByIds = new HashMap<>();
            Result<Map<Long, String>> countryNameResult = institutionCenterClient.getCountryNamesByIds(Collections.singleton(studentOfferItem.getFkAreaCountryId()));
            if (countryNameResult.isSuccess() && GeneralTool.isNotEmpty(countryNameResult.getData())) {
                countryNamesByIds = countryNameResult.getData();
            }
            fkAreaCountryName = countryNamesByIds.get(studentOfferItem.getFkAreaCountryId());
            map.put("fkAreaCountryName", fkAreaCountryName);
            // 学校
            Map<Long, String> institutionNamesByIds = new HashMap<>();
            Result<Map<Long, String>> institutionNamesByIdsResult = institutionCenterClient.getInstitutionNamesByIds(Collections.singleton(studentOfferItem.getFkInstitutionId()));
            if (institutionNamesByIdsResult.isSuccess() && GeneralTool.isNotEmpty(institutionNamesByIdsResult.getData())) {
                institutionNamesByIds = institutionNamesByIdsResult.getData();
            }
            fkInstitutionName = institutionNamesByIds.get(studentOfferItem.getFkInstitutionId());
            map.put("fkInstitutionName", fkInstitutionName);
            // 课程
            Map<Long, String> courseNameByIds = new HashMap<>();
            Result<Map<Long, String>> courseNameByIdsResult = institutionCenterClient.getCourseNameByIds(Collections.singleton(studentOfferItem.getFkInstitutionCourseId()));
            if (courseNameByIdsResult.isSuccess() && GeneralTool.isNotEmpty(courseNameByIdsResult.getData())) {
                courseNameByIds = courseNameByIdsResult.getData();
            }
            if (GeneralTool.isNotEmpty(studentOfferItem.getFkInstitutionCourseId()) && studentOfferItem.getFkInstitutionCourseId() != -1 && GeneralTool.isNotEmpty(courseNameByIds)) {
                fkCourseName = courseNameByIds.get(studentOfferItem.getFkInstitutionCourseId());
            } else if (GeneralTool.isNotEmpty(studentOfferItem.getOldCourseCustomName())) {
                fkCourseName = "【自定义】" + studentOfferItem.getOldCourseCustomName();
            }
            map.put("fkCourseName", fkCourseName);
            // 申请计划详情链接
            // http://192.168.1.6:8081/sales-center/student-management/student-offer-item-detail/729998?offerid=454026&studentid=391508
            studentOfferItemDetail = domainName + "sales-center/student-management/student-offer-item-detail/"
                    + studentOfferItem.getId()
                    + "?offerid=" + studentOfferItem.getFkStudentOfferId()
                    + "&studentid=" + studentOfferItem.getStudentId();
            map.put("studentOfferItemDetail", studentOfferItemDetail);
        }

        String taskTitle = "学生" + paymentTypeName + "支付通知，学生：" + studentName + "，支付状态：" + paymentStatusName;
        // 发送邮件
        List<RemindTaskDto> remindTaskVos = Lists.newArrayList();
        if (GeneralTool.isNotEmpty(emailToStaffIds)) {
            for (Long staffId : emailToStaffIds) {
                RemindTaskDto remindTaskVo = new RemindTaskDto();
                remindTaskVo.setTaskTitle(taskTitle);
                remindTaskVo.setTaskRemark(MyStringUtils.getReminderTemplate(map, SaleCenterConstant.STUDENT_OFFER_ITEM_CHANGE_PAYMENT_STATUS_ARC_REMINDER));
                //邮件方式发送
                remindTaskVo.setRemindMethod("1");
                //默认设置执行中
                remindTaskVo.setStatus(1);
                //默认背景颜色
                remindTaskVo.setTaskBgColor("#3788d8");
                remindTaskVo.setFkStaffId(staffId);
                remindTaskVo.setStartTime(new Date());
                remindTaskVo.setFkTableName(TableEnum.SALE_STUDENT_OFFER_ITEM.key);
                remindTaskVo.setFkTableId(studentOfferItemPaymentStatusToAppVo.getFkTableId());
                remindTaskVo.setFkRemindEventTypeKey(ProjectKeyEnum.STUDENT_OFFER_ITEM_PAYMENT_ARC_NOTICE.key);
                remindTaskVos.add(remindTaskVo);
            }
        }

        // 批量添加到任务队列
        if (GeneralTool.isNotEmpty(remindTaskVos)) {
            Result<Boolean> result = reminderCenterClient.batchAdd(remindTaskVos);
            if (!result.isSuccess() || GeneralTool.isEmpty(result.getData())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("news_emil_send_fail"));
            }
        }
    }
}

package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.feign.IFinanceCenterClient;
import com.get.salecenter.dao.sale.ConventionSponsorFeeMapper;
import com.get.salecenter.dao.sale.ConventionSponsorSponsorFeeMapper;
import com.get.salecenter.vo.ConventionSponsorFeeVo;
import com.get.salecenter.entity.ConventionSponsorFee;
import com.get.salecenter.entity.ConventionSponsorSponsorFee;
import com.get.salecenter.service.IConventionSponsorFeeService;
import com.get.salecenter.service.IConventionSponsorSponsorFeeService;
import com.get.salecenter.service.IDeleteService;
import com.get.salecenter.dto.ConventionSponsorFeeDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: Sea
 * @create: 2021/5/8 11:07
 * @verison: 1.0
 * @description:
 */
@Service
public class ConventionSponsorFeeServiceImpl extends BaseServiceImpl<ConventionSponsorFeeMapper, ConventionSponsorFee> implements IConventionSponsorFeeService {
    @Resource
    private ConventionSponsorFeeMapper conventionSponsorFeeMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IDeleteService deleteService;
    @Resource
    private IFinanceCenterClient financeCenterClient;
    @Resource
    private IConventionSponsorSponsorFeeService conventionSponsorSponsorFeeService;
    @Resource
    private ConventionSponsorSponsorFeeMapper conventionSponsorSponsorFeeMapper;

    @Override
    public ConventionSponsorFeeVo findConventionSponsorFeeById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ConventionSponsorFee conventionSponsorFee = conventionSponsorFeeMapper.selectById(id);
        if (GeneralTool.isEmpty(conventionSponsorFee)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        ConventionSponsorFeeVo conventionSponsorFeeVo = BeanCopyUtils.objClone(conventionSponsorFee, ConventionSponsorFeeVo::new);
        //返回是否与赞助商存在绑定关系
        LambdaQueryWrapper<ConventionSponsorSponsorFee> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ConventionSponsorSponsorFee::getFkConventionSponsorFeeId,id);
        List<ConventionSponsorSponsorFee> conventionSponsorSponsorFees = conventionSponsorSponsorFeeMapper.selectList(lambdaQueryWrapper);
        if (GeneralTool.isNotEmpty(conventionSponsorSponsorFees)){
            conventionSponsorFeeVo.setIsBinding(true);
        }
        return conventionSponsorFeeVo;
    }

    @Override
    public Long addConventionSponsorFee(ConventionSponsorFeeDto conventionSponsorFeeDto) {
        if (GeneralTool.isEmpty(conventionSponsorFeeDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        ConventionSponsorFee conventionSponsorFee = BeanCopyUtils.objClone(conventionSponsorFeeDto, ConventionSponsorFee::new);
        Integer maxViewOrder = conventionSponsorFeeMapper.getMaxViewOrder(conventionSponsorFeeDto.getFkConventionId());
        conventionSponsorFee.setViewOrder(maxViewOrder);

        //设置费用折合人民币
        if ("CNY".equals(conventionSponsorFee.getFkCurrencyTypeNum())) {
            conventionSponsorFee.setFeeCny(conventionSponsorFee.getFee());
        } else {
            //获取汇率
            //根据币种查汇率
            Result<BigDecimal> result = financeCenterClient.getExchangeRate(conventionSponsorFee.getFkCurrencyTypeNum(), "CNY");
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                BigDecimal feeCny = conventionSponsorFee.getFee().multiply(result.getData());
                conventionSponsorFee.setFeeCny(feeCny);
            }
        }

        utilService.updateUserInfoToEntity(conventionSponsorFee);
        conventionSponsorFeeMapper.insertSelective(conventionSponsorFee);
        return conventionSponsorFee.getId();
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (conventionSponsorFeeMapper.selectById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        //删除验证
        deleteService.deleteValidateSponsorFee(id);
        conventionSponsorFeeMapper.deleteById(id);
    }

    @Override
    public ConventionSponsorFeeVo updateConventionSponsorFee(ConventionSponsorFeeDto conventionSponsorFeeDto) {
        if (conventionSponsorFeeDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        ConventionSponsorFee result = conventionSponsorFeeMapper.selectById(conventionSponsorFeeDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        if (GeneralTool.isNotEmpty(conventionSponsorFeeDto.getFee())) {
            // BigDecimal要与此BigDecimal进行比较。
            // 返回：-1，0或1; -1小于,0等于,1大于
            if (conventionSponsorFeeDto.getFee().compareTo(result.getFee()) != 0 || !conventionSponsorFeeDto.getFkCurrencyTypeNum().equals(result.getFkCurrencyTypeNum())) {
                //币种和费用数随便一个有变化就修改折合人民币的字段
                //设置费用折合人民币
                if ("CNY".equals(conventionSponsorFeeDto.getFkCurrencyTypeNum())) {
                    conventionSponsorFeeDto.setFeeCny(conventionSponsorFeeDto.getFee());
                } else {
                    //获取汇率
                    //根据币种查汇率
//                    BigDecimal exchangeRate = financeCenterClient.getExchangeRate(conventionSponsorFeeDto.getFkCurrencyTypeNum(), "CNY");
//                    BigDecimal feeCny = conventionSponsorFeeDto.getFee().multiply(exchangeRate);
//                    conventionSponsorFeeDto.setFeeCny(feeCny);
                    Result<BigDecimal> result_ = financeCenterClient.getExchangeRate(conventionSponsorFeeDto.getFkCurrencyTypeNum(), "CNY");
                    if (result_.isSuccess() && GeneralTool.isNotEmpty(result_.getData())) {
                        BigDecimal feeCny = conventionSponsorFeeDto.getFee().multiply(result_.getData());
                        conventionSponsorFeeDto.setFeeCny(feeCny);
                    }
                }
            }
        }
        //是否与赞助商存在绑定关系，若存在则不允许修改币种
        if (!conventionSponsorFeeDto.getFkCurrencyTypeNum().equals(result.getFkCurrencyTypeNum())){
            LambdaQueryWrapper<ConventionSponsorSponsorFee> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(ConventionSponsorSponsorFee::getFkConventionSponsorFeeId, conventionSponsorFeeDto.getId());
            List<ConventionSponsorSponsorFee> conventionSponsorSponsorFees = conventionSponsorSponsorFeeMapper.selectList(lambdaQueryWrapper);
            if (GeneralTool.isNotEmpty(conventionSponsorSponsorFees)){
                throw new GetServiceException(LocaleMessageUtils.getMessage("convention_sponsor_sponsor_fee_association"));
            }
        }

        ConventionSponsorFee conventionSponsorFee = BeanCopyUtils.objClone(conventionSponsorFeeDto, ConventionSponsorFee::new);
        utilService.updateUserInfoToEntity(conventionSponsorFee);
        conventionSponsorFeeMapper.updateById(conventionSponsorFee);
        return findConventionSponsorFeeById(conventionSponsorFee.getId());
    }

    @Override
    public List<ConventionSponsorFeeVo> getConventionSponsorFees(ConventionSponsorFeeDto conventionSponsorFeeDto, Page page) {
        LambdaQueryWrapper<ConventionSponsorFee> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ConventionSponsorFee::getFkConventionId, conventionSponsorFeeDto.getFkConventionId());
        if (GeneralTool.isNotEmpty(conventionSponsorFeeDto)) {
            //查询条件-费用名称
            if (GeneralTool.isNotEmpty(conventionSponsorFeeDto.getTitle())) {
                lambdaQueryWrapper.like(ConventionSponsorFee::getTitle, conventionSponsorFeeDto.getTitle());
            }
            //查询条件-类型名称
            if (GeneralTool.isNotEmpty(conventionSponsorFeeDto.getTypeName())) {
                lambdaQueryWrapper.like(ConventionSponsorFee::getTypeName, conventionSponsorFeeDto.getTypeName());
            }
        }
        lambdaQueryWrapper.orderByDesc(ConventionSponsorFee::getViewOrder);
        IPage<ConventionSponsorFee> pages = conventionSponsorFeeMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
        List<ConventionSponsorFee> conventionSponsorFees = pages.getRecords();
        page.setAll((int) pages.getTotal());

        List<ConventionSponsorFeeVo> convertDatas = new ArrayList<>();
        //币种编号集合
        Set<String> currencyTypeNums = conventionSponsorFees.stream().map(ConventionSponsorFee::getFkCurrencyTypeNum).collect(Collectors.toSet());
        currencyTypeNums.removeIf(Objects::isNull);
        Result<Map<String, String>> result = financeCenterClient.getCurrencyNamesByNums(currencyTypeNums);
        if (result.isSuccess() && result.getData() != null) {
            Map<String, String> currencyNameMap = result.getData();
            for (ConventionSponsorFee conventionSponsorFee : conventionSponsorFees) {
                ConventionSponsorFeeVo conventionSponsorFeeVo = BeanCopyUtils.objClone(conventionSponsorFee, ConventionSponsorFeeVo::new);
                conventionSponsorFeeVo.setCurrencyName(currencyNameMap.get(conventionSponsorFee.getFkCurrencyTypeNum()));
                convertDatas.add(conventionSponsorFeeVo);
            }
        }
        return convertDatas;
    }

    @Override
    public void movingOrder(List<ConventionSponsorFeeDto> conventionSponsorFeeDtos) {
        if (GeneralTool.isEmpty(conventionSponsorFeeDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        ConventionSponsorFee ro = BeanCopyUtils.objClone(conventionSponsorFeeDtos.get(0), ConventionSponsorFee::new);
        Integer oneorder = ro.getViewOrder();
        ConventionSponsorFee rt = BeanCopyUtils.objClone(conventionSponsorFeeDtos.get(1), ConventionSponsorFee::new);
        Integer twoorder = rt.getViewOrder();
        ro.setViewOrder(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setViewOrder(oneorder);
        utilService.updateUserInfoToEntity(rt);
        conventionSponsorFeeMapper.updateById(ro);
        conventionSponsorFeeMapper.updateById(rt);
    }

    @Override
    public List<Map<String, List<ConventionSponsorFeeVo>>> getSponsorshipFee(Long conventionId) {
        //查出全部赞助信息
//        Example example = new Example(ConventionSponsorFee.class);
//        example.createCriteria().andEqualTo("fkConventionId",conventionId);
//        List<ConventionSponsorFee> conventionSponsorFees = conventionSponsorFeeMapper.selectByExample(example);

        List<ConventionSponsorFee> conventionSponsorFees = conventionSponsorFeeMapper.selectList(Wrappers.<ConventionSponsorFee>lambdaQuery().eq(ConventionSponsorFee::getFkConventionId, conventionId).orderByAsc(ConventionSponsorFee::getViewOrder));
        //定义返回结果对象  key：类型  value：赞助对象集合
        List<Map<String, List<ConventionSponsorFeeVo>>> list = new ArrayList<>();
        Set<String> set = new HashSet<>();
        //收集去重后的类型集合
        conventionSponsorFees.forEach(conventionSponsorFee -> set.add(conventionSponsorFee.getTypeName()));
        //每个类型作为key创建一个map放入list
        for (String type : set) {
            Map<String, List<ConventionSponsorFeeVo>> map = new HashMap<>();
            //定义存在map中的赞助类型集合
            List<ConventionSponsorFeeVo> conventionSponsorFeeVoList = new ArrayList<>();
            map.put(type, conventionSponsorFeeVoList);
            list.add(map);
        }
        //获取每一个赞助信息，看他是哪个类型的
        for (ConventionSponsorFee conventionSponsorFee : conventionSponsorFees) {
            ConventionSponsorFeeVo conventionSponsorFeeVo = BeanCopyUtils.objClone(conventionSponsorFee, ConventionSponsorFeeVo::new);
            //是否售空
            Boolean result = conventionSponsorSponsorFeeService.soldOut(conventionSponsorFee.getId(), conventionSponsorFee.getCountLimit(), null);
            conventionSponsorFeeVo.setSoldOut(result);
            for (Map<String, List<ConventionSponsorFeeVo>> listMap : list) {
                //取出map，如果这个赞助对象的类型能从这个map中获取[] 表示这个赞助对象属于这个map，加入value
                List<ConventionSponsorFeeVo> conventionSponsorFeeVos = listMap.get(conventionSponsorFeeVo.getTypeName());
                if (conventionSponsorFeeVos != null) {
                    conventionSponsorFeeVos.add(conventionSponsorFeeVo);

                }

            }
        }
        return list;
    }

    @Override
    public Integer getTotal(Long fkConventionId) {
//        Example example = new Example(ConventionSponsorFee.class);
//        example.createCriteria().andEqualTo("fkConventionId",fkConventionId);
//        List<ConventionSponsorFee> conventionSponsorFees = conventionSponsorFeeMapper.selectByExample(example);

        List<ConventionSponsorFee> conventionSponsorFees = conventionSponsorFeeMapper.selectList(Wrappers.<ConventionSponsorFee>lambdaQuery().eq(ConventionSponsorFee::getFkConventionId, fkConventionId));
        if (GeneralTool.isNotEmpty(conventionSponsorFees)) {
            return conventionSponsorFees.size();
        }
        return null;
    }
}

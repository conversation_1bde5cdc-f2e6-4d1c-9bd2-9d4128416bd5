package com.get.salecenter.service;


import com.get.salecenter.vo.ConventionAwardWinnerVo;
import com.get.salecenter.entity.ConventionAwardCode;
import com.get.salecenter.dto.ConventionAwardWinnerDto;
import com.get.salecenter.dto.LuckDrawDto;

import java.util.List;

/**
 * @author: <PERSON>
 * @create: 2021/9/13 11:07
 * @verison: 1.0
 * @description:中奖名单管理业务层
 */
public interface IConventionAwardWinnerService {

    /**
     * @Description: 列表数据
     * @Author: Jerry
     * @Date:16:04 2021/9/15
     */
    List<ConventionAwardWinnerVo> datas(ConventionAwardWinnerDto conventionAwardWinnerDto);

    /**
     * @Description: 保存中奖纪录
     * @Author: jack
     * @Date:16:04 2021/9/23
     */
    void saveLottery(ConventionAwardWinnerDto conventionAwardWinnerDto);

    /**
     * 2022年峰会抽奖
     *
     * @Date 16:59 2023/2/2
     * <AUTHOR>
     */
    List<ConventionAwardCode> luckDraw(LuckDrawDto luckDrawDto);

    /**
     * 2022年峰会重新抽奖
     *
     * @Date 11:23 2023/2/6
     * <AUTHOR>
     */
    List<ConventionAwardCode> luckDrawAgain(LuckDrawDto luckDrawDto);

}

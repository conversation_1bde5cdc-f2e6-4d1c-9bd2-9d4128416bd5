package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.dto.EventCostDto;
import com.get.salecenter.service.IEventCostService;
import com.get.salecenter.vo.EventBillVo;
import com.get.salecenter.vo.EventCostVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * @author: Sea
 * @create: 2020/12/9 18:02
 * @verison: 1.0
 * @description: 费用归口管理控制器
 */
@Api(tags = "费用归口管理")
@RestController
@RequestMapping("sale/eventCost")
public class EventCostController {
    @Resource
    private IEventCostService eventCostService;

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.EventCostVo>
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/费用归口管理/费用归口详情")
    @GetMapping("/{id}")
    public ResponseBo<EventCostVo> detail(@PathVariable("id") Long id) {
        EventCostVo data = eventCostService.findEventCostById(id);
        return new ResponseBo<>(data);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :新增信息
     * @Param [eventCostVos]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/费用归口管理/新增费用归口")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(EventCostDto.Add.class)  EventCostDto eventCostDto) {
        return SaveResponseBo.ok(eventCostService.addEventCost(eventCostDto));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :删除信息
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/费用归口管理/删除费用归口")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        eventCostService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.EventCostVo>
     * @Description :修改信息
     * @Param [eventCostDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/费用归口管理/更新费用归口")
    @PostMapping("update")
    public ResponseBo<EventCostVo> update(@RequestBody  @Validated(EventCostDto.Update.class) EventCostDto eventCostDto) {
        return UpdateResponseBo.ok(eventCostService.updateEventCost(eventCostDto));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.EventCostVo>
     * @Description :列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/费用归口管理/查询费用归口")
    @PostMapping("datas")
    public ResponseBo<EventCostVo> datas(@RequestBody SearchBean<EventCostDto> page) {
        List<EventCostVo> datas = eventCostService.getEventCosts(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 活动费用归口收款单下拉框
     *
     * @Date 12:01 2021/12/3
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "活动费用归口收款单下拉框", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/费用归口管理/活动费用归口收款单下拉框")
    @GetMapping("/getReceiptSelect")
    public ResponseBo<EventBillVo> getReceiptSelect(@RequestParam("institutionProviderId") Long institutionProviderId,
                                                    @RequestParam("companyId") Long companyId,
                                                    @RequestParam(value = "eventCostId", required = false) Long eventCostId,
                                                    @RequestParam(value = "conventionRegistrationId",required = false)Long conventionRegistrationId,
                                                    @RequestParam(value = "conventionSponsorId",required = false)Long conventionSponsorId,
                                                    @RequestParam(value = "eventYear",required = false) String eventYear) {
        return new ListResponseBo<>(eventCostService.getReceiptSelect(institutionProviderId, companyId, eventCostId,conventionRegistrationId,conventionSponsorId,eventYear));
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "活动费用归口收款单年份统计", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/费用归口管理/活动费用归口收款单下拉框")
    @GetMapping("/getReceiptSelectYear")
    public ResponseBo<List<String>> getReceiptSelect(@RequestParam("institutionProviderId") Long institutionProviderId,
                                                @RequestParam("companyId") Long companyId,
                                                @RequestParam(value = "eventCostId", required = false) Long eventCostId,
                                                @RequestParam(value = "conventionRegistrationId",required = false)Long conventionRegistrationId,
                                                @RequestParam(value = "conventionSponsorId",required = false)Long conventionSponsorId) {
       return new ResponseBo<>(eventCostService.getReceiptSelectYear(institutionProviderId, companyId, eventCostId,conventionRegistrationId,conventionSponsorId));
    }


    /**
     * 根据receiptFormIds获取活动费用归口Dtos
     *
     * @param receiptFormIds
     * @return
     */
    @ApiIgnore
    @PostMapping("getEventCostDtoByReceiptFormIds")
    public Map<Long, List<EventCostVo>> getEventCostDtoByReceiptFormIds(@RequestBody Set<Long> receiptFormIds) {
        return eventCostService.getEventCostDtoByReceiptFormIds(receiptFormIds);
    }

    /**
     * 根据receiptFormId获取活动费用归口Dtos
     *
     * @param receiptFormId
     * @return
     */
    @ApiIgnore
    @PostMapping("getEventCostDtoByReceiptFormId")
    public List<EventCostVo> getEventCostDtoByReceiptFormId(@RequestParam("receiptFormId") Long receiptFormId) {
        return eventCostService.getEventCostDtoByReceiptFormId(receiptFormId);
    }

    /**
     * 根据receiptFormId获取活动费用归口Dtos
     *
     * @param eventCostDto
     * @return
     */
    @ApiOperation(value = "费用小计", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/费用归口管理/费用小计")
    @PostMapping("getEventCostSubtotal")
    public ResponseBo<BigDecimal> getEventCostSubtotal(@RequestBody EventCostDto eventCostDto) {
        return new ResponseBo<>(eventCostService.getEventCostSubtotal(eventCostDto));
    }
    @ApiOperation(value = "已分配活动费用汇总", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "销售中心/活动管理/活动汇总")
    @PostMapping("getAllocatedActivityFees")
    public ResponseBo<EventCostVo> getAllocatedActivityFees(@RequestBody EventCostDto eventCostDto) {
        return new ResponseBo<>(eventCostService.getAllocatedActivityFees(eventCostDto));
    }


    @ApiOperation(value = "批量分配活动", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/费用归口管理/批量分配活动")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody  @Validated(EventCostDto.Add.class) ValidList<EventCostDto> eventCostDtos) {
        eventCostService.batchAdd(eventCostDtos);
        return SaveResponseBo.ok();
    }
}

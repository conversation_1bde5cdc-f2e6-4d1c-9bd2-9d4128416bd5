<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.get.salecenter.dao.convention.ConventionMediaAndAttachedMapper">

    <select id="getNextIndexKey" resultType="java.lang.Integer">
        select max(index_key)+1 from s_media_and_attached where fk_table_id=#{tableId} and fk_table_name=#{tableName}
    </select>
</mapper>

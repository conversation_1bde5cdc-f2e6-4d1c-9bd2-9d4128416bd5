package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.salecenter.vo.ClientSourceListVo;
import com.get.salecenter.service.IAgentService;
import com.get.salecenter.service.IClientSourceService;
import com.get.salecenter.dto.ClientSourceAddDto;
import com.get.salecenter.dto.ClientSourceListDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * author:Neil
 * Time: 12:50
 * Date: 2022/8/17
 * Description: 售前客户管理
 */
@Api(tags = "学生资源来源管理")
@RestController
@RequestMapping("sale/clientSource")
public class ClientSourceController {

    @Resource
    private IClientSourceService clientSourceService;

    @Resource
    private IAgentService agentService;

    @ApiOperation(value = "推荐来源列表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生资源来源管理/推荐来源列表")
    @PostMapping("datas")
    public ResponseBo<ClientSourceListVo> datas(@RequestBody SearchBean<ClientSourceListDto> page){
        List<ClientSourceListVo> datas = clientSourceService.getClientSources(page.getData(),page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas,p);
    }


    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "销售中心/学生资源来源管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        clientSourceService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 新增信息
     * @Param [clientAddVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生资源来源管理/新增学生资源来源")
    @PostMapping("add")
    public ResponseBo add(@RequestBody ClientSourceAddDto clientSourceAddDto) {
        clientSourceService.addClientSource(clientSourceAddDto);
        return SaveResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 新增信息
     * @Param [clientAddVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "验证接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/学生资源来源管理/验证学生资源来源")
    @PostMapping("validateClientSource")
    public ResponseBo validateClientSource(@RequestParam("studentNum")String studentNum) {
        return new ResponseBo(clientSourceService.validateClientSource(studentNum));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 新增信息
     * @Param [clientAddVo]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "来源类型下拉", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生资源来源管理/来源类型下拉")
    @PostMapping("clientSourceTypeSelect")
    public ResponseBo clientSourceTypeSelect() {
        return new ListResponseBo(ProjectKeyEnum.enums2Arrays(ProjectKeyEnum.CLIENT_SOURCE_TYPE));
    }


    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "渠道代理下拉")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/学生资源来源管理/渠道代理下拉")
    @GetMapping("getCustomerChannelAgentSelect")
    public ResponseBo<BaseSelectEntity> getCustomerChannelAgentSelect(@RequestParam("companyId") Long companyId) {
        List<BaseSelectEntity> datas = agentService.getCustomerChannelAgentSelect(companyId);
        return new ListResponseBo<>(datas);
    }
}

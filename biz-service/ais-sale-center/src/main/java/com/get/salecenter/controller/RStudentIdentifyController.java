package com.get.salecenter.controller;


import com.baomidou.mybatisplus.extension.api.ApiController;
import com.get.salecenter.service.IRStudentIdentifyService;
import io.swagger.annotations.Api;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 学生识别(r_student_identify)
 */
@Api(tags = "学生识别")
@RestController
@RequestMapping("sale/studentIdentify")
public class RStudentIdentifyController extends ApiController {
    @Resource
    private IRStudentIdentifyService rStudentIdentifyService;

}


package com.get.salecenter.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.MediaTypeEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.service.impl.GetServiceImpl;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.vo.ConfigVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.dao.sale.StudentOfferItemMapper;
import com.get.salecenter.dao.sale.StudentOfferItemStepMapper;
import com.get.salecenter.vo.*;
import com.get.salecenter.vo.OfferItemLimitConfigVo;
import com.get.salecenter.entity.StudentOfferItem;
import com.get.salecenter.entity.StudentOfferItemStep;
import com.get.salecenter.service.IDeleteService;
import com.get.salecenter.service.IStudentOfferItemStepService;
import com.get.salecenter.service.IStudentProjectRoleService;
import com.get.salecenter.service.IStudentProjectRoleStaffService;
import com.get.salecenter.dto.StudentOfferItemStepDto;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.StringJoiner;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: Sea
 * @create: 2020/11/3 11:33
 * @verison: 1.0
 * @description:
 */
@Service
public class StudentOfferItemStepServiceImpl extends GetServiceImpl<StudentOfferItemStepMapper,StudentOfferItemStep> implements IStudentOfferItemStepService {
    @Resource
    private StudentOfferItemStepMapper studentOfferItemStepMapper;
    @Resource
    private StudentOfferItemMapper studentOfferItemMapper;
    @Resource
    private IDeleteService deleteService;
    @Resource
    private UtilService utilService;
    @Resource
    private IStudentProjectRoleService studentProjectRoleService;
    @Resource
    private IPermissionCenterClient feignPermissionService;
    @Resource
    private IStudentProjectRoleStaffService roleStaffService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Override
    public StudentOfferItemStepVo findStudentOfferItemStepById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        StudentOfferItemStep studentOfferItemStep = studentOfferItemStepMapper.selectById(id);
        StudentOfferItemStepVo studentOfferItemStepVo = BeanCopyUtils.objClone(studentOfferItemStep, StudentOfferItemStepVo::new);
        //前置步骤名称
        if (studentOfferItemStepVo !=null) {
            String[] split = studentOfferItemStepVo.getFkStudentOfferItemStepIdPrecondition().split(",");
            Set<Long> preconditions = Arrays.stream(split).filter(StringUtils::isNotBlank).map(Long::valueOf).collect(Collectors.toSet());
            if (GeneralTool.isNotEmpty(preconditions)) {
                StringJoiner stringJoiner = new StringJoiner(" ");
                List<StudentOfferItemStep> itemSteps = studentOfferItemStepMapper.selectBatchIds(preconditions);
                Map<Long, StudentOfferItemStep> itemStepMap = itemSteps.stream().collect(Collectors.toMap(StudentOfferItemStep::getId, Function.identity()));
                for (String precondition : split) {
                    if (StringUtils.isBlank(precondition)){
                        continue;
                    }
                    StudentOfferItemStep itemStep = itemStepMap.get(Long.parseLong(precondition));
                    if (GeneralTool.isNotEmpty(itemStep)) {
                        stringJoiner.add(itemStep.getStepName());
                    }
                }
                studentOfferItemStepVo.setPreconditionName(stringJoiner.toString());
            }
            //角色
            if (GeneralTool.isNotEmpty(studentOfferItemStepVo.getRoleKey())) {
                String[] key = studentOfferItemStep.getRoleKey().split(",");
                Set<String>  keys= Arrays.stream(key).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
                if (GeneralTool.isNotEmpty(keys)) {
                    List<StudentProjectRoleVo> roles = studentProjectRoleService.getRoleByKeys(keys);
                    Map<String, StudentProjectRoleVo> roleMap = roles.stream().collect(Collectors.toMap(StudentProjectRoleVo::getRoleKey, Function.identity()));
                    List<StudentProjectRoleVo> data = new ArrayList<>();
                    for (String roleKey : key) {
                        if (StringUtils.isBlank(roleKey)) {
                            continue;
                        }
                        StudentProjectRoleVo studentProjectRoleVo = roleMap.get(roleKey);
                        data.add(studentProjectRoleVo);

                    }
                    data = data.stream().filter(Objects::nonNull).collect(Collectors.toList());
                    //角色所属公司
                    //TODO 改过
                    //Set<Long> companyIds = data.stream().map(StudentProjectRole::getFkCompanyId).collect(Collectors.toSet());
                    Set<Long> companyIds = data.stream().map(StudentProjectRoleVo::getFkCompanyId).collect(Collectors.toSet());
                    Map<Long, String> companys = new HashMap<>();
                    Result<Map<Long, String>> result = feignPermissionService.getCompanyNamesByIds(companyIds);
                    if (result.isSuccess() && result.getData() != null) {
                        companys = result.getData();
                    }
                    for (StudentProjectRoleVo studentProjectRoleVo : data) {
                        studentProjectRoleVo.setCompanyName(companys.get(studentProjectRoleVo.getFkCompanyId()));
                        //格式化：【公司】角色名称（key）
                        String stringBuffer = '【' +
                                companys.get(studentProjectRoleVo.getFkCompanyId()) +
                                '】' +
                                studentProjectRoleVo.getRoleName() +
                                '（' +
                                studentProjectRoleVo.getRoleKey() +
                                '）';
                        studentProjectRoleVo.setFormatName(stringBuffer);
                    }
                    studentOfferItemStepVo.setStudentProjectRoleDtos(data);
                }
            }
        }
        return studentOfferItemStepVo;
    }

    /**
     * Author Cream
     * Description : // 获取步骤文件类型配置
     * Date 2023/5/10 10:32
     * Params:
     * Return
     * @param fkStudentOfferItemStepId
     */
    @Override
    public OfferItemFileConfigVo getFileConfig(Long fkStudentOfferItemStepId) {
        if (Objects.isNull(fkStudentOfferItemStepId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        StudentOfferItemStep studentOfferItemStep = studentOfferItemStepMapper.selectOne(Wrappers.<StudentOfferItemStep>lambdaQuery().eq(StudentOfferItemStep::getId, fkStudentOfferItemStepId));
        if (Objects.isNull(studentOfferItemStep)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        String configJson = studentOfferItemStep.getConfigJson();
        if (JSONUtil.isJson(configJson)) {
            return JSON.parseObject(JSON.toJSONString(JSON.parse(configJson)), OfferItemFileConfigVo.class);
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(List<StudentOfferItemStepDto> studentOfferItemStepDtos) {
        if (GeneralTool.isEmpty(studentOfferItemStepDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        //todo 这个查询是不是有问题？
        Integer maxStepOrder = studentOfferItemStepMapper.getMaxStepOrder();
        for (StudentOfferItemStepDto studentOfferItemStepDto : studentOfferItemStepDtos) {
            if (GeneralTool.isEmpty(studentOfferItemStepDto.getId())) {
                StudentOfferItemStep studentOfferItemStep = BeanCopyUtils.objClone(studentOfferItemStepDto, StudentOfferItemStep::new);
                if (validateAdd(studentOfferItemStepDto)) {
                    studentOfferItemStep.setFkStudentOfferItemStepIdPrecondition(studentOfferItemStepDto.getStudentOfferItemStepIdPrecondition());
                    studentOfferItemStep.setStepOrder(maxStepOrder);
                    utilService.updateUserInfoToEntity(studentOfferItemStep);
                    studentOfferItemStepMapper.insert(studentOfferItemStep);
                    maxStepOrder++;
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
                }
            } else {
                if (validateUpdate(studentOfferItemStepDto)) {
                    StudentOfferItemStep studentOfferItemStep = BeanCopyUtils.objClone(studentOfferItemStepDto, StudentOfferItemStep::new);
                    studentOfferItemStep.setFkStudentOfferItemStepIdPrecondition(studentOfferItemStepDto.getStudentOfferItemStepIdPrecondition());
                    utilService.updateUserInfoToEntity(studentOfferItemStep);
                    studentOfferItemStepMapper.updateById(studentOfferItemStep);
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
                }
            }

        }
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (findStudentOfferItemStepById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        deleteService.deleteRelationOfferItemStep(id);
        studentOfferItemStepMapper.deleteById(id);
    }

    @Override
    public StudentOfferItemStepVo updateStudentOfferItemStep(StudentOfferItemStepDto studentOfferItemStepDto) {
        if (studentOfferItemStepDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        StudentOfferItemStep result = studentOfferItemStepMapper.selectById(studentOfferItemStepDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        StudentOfferItemStep studentOfferItemStep = BeanCopyUtils.objClone(studentOfferItemStepDto, StudentOfferItemStep::new);
        if (validateUpdate(studentOfferItemStepDto)) {
            studentOfferItemStep.setFkStudentOfferItemStepIdPrecondition(studentOfferItemStepDto.getStudentOfferItemStepIdPrecondition());
            utilService.updateUserInfoToEntity(studentOfferItemStep);
            studentOfferItemStepMapper.updateById(studentOfferItemStep);
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
        }
        return findStudentOfferItemStepById(studentOfferItemStep.getId());
    }

    @Override
    public List<StudentOfferItemStepVo> getStudentOfferItemSteps(StudentOfferItemStepDto studentOfferItemStepDto, Page page) {
//        Example example = new Example(StudentOfferItemStep.class);
//        Example.Criteria criteria = example.createCriteria();
        LambdaQueryWrapper<StudentOfferItemStep> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(studentOfferItemStepDto)) {
            //查询条件-步骤名称
            if (GeneralTool.isNotEmpty(studentOfferItemStepDto.getStepName())) {
//                criteria.andLike("stepName", "%" + studentOfferItemStepDto.getStepName() + "%");
                lambdaQueryWrapper.like(StudentOfferItemStep::getStepName, studentOfferItemStepDto.getStepName());
            }
        }
        lambdaQueryWrapper.orderByAsc(StudentOfferItemStep::getStepOrder);

//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
        IPage<StudentOfferItemStep> pages = studentOfferItemStepMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
        List<StudentOfferItemStep> studentOfferItemSteps = pages.getRecords();
        page.setAll((int) pages.getTotal());
//        page.restPage(studentOfferItemSteps);
        List<StudentOfferItemStepVo> studentOfferItemStepVos = studentOfferItemSteps.stream().map(studentOfferItemStep -> BeanCopyUtils.objClone(studentOfferItemStep, StudentOfferItemStepVo::new)).collect(Collectors.toList());
        //前置步骤名称
        if (GeneralTool.isNotEmpty(studentOfferItemStepVos)) {
            for (StudentOfferItemStepVo studentOfferItemStepVo : studentOfferItemStepVos) {
                String[] split = studentOfferItemStepVo.getFkStudentOfferItemStepIdPrecondition().split(",");
                StringJoiner stringJoiner = new StringJoiner(" ");

                for (String precondition : split) {
                    StudentOfferItemStep studentOfferItemStep = studentOfferItemStepMapper.selectById(Long.valueOf(GeneralTool.isNotEmpty(precondition) ? precondition : "0"));
                    if (GeneralTool.isNotEmpty(studentOfferItemStep)) {
                        stringJoiner.add(studentOfferItemStep.getStepName());
                    }
                }
                studentOfferItemStepVo.setPreconditionName(stringJoiner.toString());

                //角色
                if (GeneralTool.isNotEmpty(studentOfferItemStepVo.getRoleKey())) {
                    String[] key = studentOfferItemStepVo.getRoleKey().split(",");
                    List<StudentProjectRoleVo> data = new ArrayList<>();
                    for (String roleKey : key) {
                        if (GeneralTool.isNotEmpty(roleKey)) {
                            StudentProjectRoleVo studentProjectRoleVo = studentProjectRoleService.getRoleByKey(roleKey);
                            if (GeneralTool.isNotEmpty(studentProjectRoleVo)) {
                                data.add(studentProjectRoleVo);
                            }
                        }
                    }
                    //角色所属公司
                    if (GeneralTool.isNotEmpty(data)) {
                        //TODO 改过
                       // Set<Long> companyIds = data.stream().map(StudentProjectRole::getFkCompanyId).collect(Collectors.toSet());
                        Set<Long> companyIds = data.stream().map(StudentProjectRoleVo::getFkCompanyId).collect(Collectors.toSet());
                        if (GeneralTool.isNotEmpty(companyIds)) {
                            Map<Long, String> companys = new HashMap<>();
                            Result<Map<Long, String>> result = feignPermissionService.getCompanyNamesByIds(companyIds);
                            if (result.isSuccess() && result.getData() != null) {
                                companys = result.getData();
                            }
                            for (StudentProjectRoleVo studentProjectRoleVo : data) {
                                studentProjectRoleVo.setCompanyName(companys.get(studentProjectRoleVo.getFkCompanyId()));
                            }
                        }
                    }

                    studentOfferItemStepVo.setStudentProjectRoleDtos(data.stream().sorted(Comparator.comparing(StudentProjectRoleVo::getFkCompanyId).thenComparing(StudentProjectRoleVo::getViewOrder, Comparator.reverseOrder())).collect(Collectors.toList()));
                }
            }
        }
        return studentOfferItemStepVos;
    }

    @Override
    public List<BaseSelectEntity> getItemStepPrecondition(Long itemStepId) {
        if (GeneralTool.isEmpty(itemStepId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        return studentOfferItemStepMapper.getItemStepPrecondition(itemStepId);
    }

    @Override
    public void movingOrder(List<StudentOfferItemStepDto> studentOfferItemStepDtos) {
        if (GeneralTool.isEmpty(studentOfferItemStepDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        StudentOfferItemStep ro = BeanCopyUtils.objClone(studentOfferItemStepDtos.get(0), StudentOfferItemStep::new);
        Integer oneorder = ro.getStepOrder();
        StudentOfferItemStep rt = BeanCopyUtils.objClone(studentOfferItemStepDtos.get(1), StudentOfferItemStep::new);
        Integer twoorder = rt.getStepOrder();
        ro.setStepOrder(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setStepOrder(oneorder);
        utilService.updateUserInfoToEntity(rt);
        studentOfferItemStepMapper.updateById(ro);
        studentOfferItemStepMapper.updateById(rt);
    }

    @Override
    public List<BaseSelectEntity> getItemStepSelect() {
        return studentOfferItemStepMapper.getItemStepSelect();
    }

    //    @Override
//    public List<BaseSelectEntity> getUnexecutedItemStep(Long fkStudentOfferItemId) {
//        StudentOfferItemStep studentOfferItemStep=new StudentOfferItemStep();
//        //获取指定学生申请方案项目中状态步骤step_Order最大的数据
//        if(GeneralTool.isNotEmpty(fkStudentOfferItemId)){
//            studentOfferItemStep= studentOfferItemStepMapper.getStudentOfferItemStepByMaxOrder(fkStudentOfferItemId);
//        }
//        Long itemStepId=null;
//        if(GeneralTool.isNotEmpty(studentOfferItemStep)){
//            itemStepId=studentOfferItemStep.getId();
//        }
//        return studentOfferItemStepMapper.getUnexecutedItemStep(itemStepId);
//    }




    @Override
    public List<BaseSelectEntity> getUnexecutedItemStep(Long fkStudentOfferItemId, Long fkStudentOfferId) {
        if (GeneralTool.isEmpty(fkStudentOfferItemId) || GeneralTool.isEmpty(fkStudentOfferId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_is_null"));
        }
        Long staffId = SecureUtil.getStaffId();
        Long fkDepartmentId = SecureUtil.getFkDepartmentId();
        Long fkCompanyId = SecureUtil.getFkCompanyId();
        //获取当前登陆人的所有资源key
        List<String> apiKeys = SecureUtil.getApiKeysByStaffId(staffId);
        //获取当前登录人的所有业务下属
        List<Long> staffIds = new ArrayList<>();
        Result<List<Long>> result = feignPermissionService.getStaffFollowerIds(staffId);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            staffIds.addAll(result.getData());
        }
        staffIds.add(staffId);
        //获取当前登录用户所在项目成员角色
        List<StudentProjectRoleStaffVo> studentProjectRoleStaffs = roleStaffService.getProjectRole(fkStudentOfferId, staffIds);
        List<Long> roleIds = studentProjectRoleStaffs.stream().map(StudentProjectRoleStaffVo::getFkStudentProjectRoleId).collect(Collectors.toList());

        //获取部门配置，对应部门人员可操作步骤任意一步
        List<Long> departmentIds = new ArrayList<>();
        String departmentNums = "";
        Result<ConfigVo> configByKey = permissionCenterClient.getConfigByKey(ProjectKeyEnum.WHITE_LIST_STUDENT_OFFER_ITEM_STEP.value);
        if (configByKey.isSuccess()&&GeneralTool.isNotEmpty(configByKey.getData())){
            departmentNums = configByKey.getData().getValue1();
        }

        String[] split = departmentNums.split(",");
        for (String s : split) {
            if (GeneralTool.isNotEmpty(s)){
                Long departmentId = permissionCenterClient.getDepartmentIdByNum(s).getData();
                if (GeneralTool.isNotEmpty(departmentId)){
                    departmentIds.add(departmentId);
                }
            }
        }
        if (GeneralTool.isEmpty(departmentIds)){
            departmentIds.add(0L);
        }

        //获取禁止回退步骤配置
        Result<Map<Long, String>> config = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.STEP_FALLBACK_OPT_LIMIT.key, 1);
//        Result<ConfigVo> config = permissionCenterClient.getConfigByKey(ProjectKeyEnum.STEP_FALLBACK_OPT_LIMIT.key);
        List<String> stepKeyList;
        if (config.isSuccess() && config.getData() != null) {
            String configValue1 = config.getData().get(fkCompanyId);
            stepKeyList = new ArrayList<>(JSON.parseArray(configValue1, String.class));
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage(LocaleMessageUtils.getMessageFormat("lack_of_business_configuration","STEP_FALLBACK_OPT_LIMIT")));
        }

        //获取【OS】后的步骤编辑，需要进行限制的步骤
        Result<Map<Long, String>> companyConfigMapResult = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.STEP_OS_EDIT_LIMIT.key, 1);

        //        Result<ConfigVo> configDtoResult = permissionCenterClient.getConfigByKey(ProjectKeyEnum.STEP_OS_EDIT_LIMIT.key);
        List<String> stepKeys = new ArrayList<>();
        if (companyConfigMapResult.isSuccess() && companyConfigMapResult.getData() != null) {
            String configValue1 = companyConfigMapResult.getData().get(fkCompanyId);
            if (GeneralTool.isNotEmpty(configValue1)) {
                JSONArray jsonArray = JSON.parseArray(configValue1);
                stepKeys = jsonArray.toJavaList(String.class);
//                JSONObject jsonObject = JSONObject.parseObject(value1);
//                if (fkCompanyId == 3) {
//                    stepKeys = jsonObject.getJSONArray("IAE").toJavaList(String.class);
//                } else {
//                    stepKeys = jsonObject.getJSONArray("OTHER").toJavaList(String.class);
//                }

            }
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage(LocaleMessageUtils.getMessageFormat("lack_of_business_configuration","STEP_FALLBACK_OPT_LIMIT")));
        }

        //获取当前申请方案当前步骤
        StudentOfferItemStep step = studentOfferItemMapper.getStudentOfferItemStep(fkStudentOfferItemId);
        Long itemStepId = step.getId();
        String stepKey = step.getStepKey();
        List<BaseSelectEntity> executedStep = new ArrayList<>();
        List<BaseSelectEntity> unexecutedStep = new ArrayList<>();
        List<BaseSelectEntity> resoult = new ArrayList<>();
        //已经执行的步骤
        List<StudentOfferItemStep> executedData = studentOfferItemStepMapper.getExecutedItemStep(itemStepId);
        if (GeneralTool.isNotEmpty(executedData)) {
            for (StudentOfferItemStep studentOfferItemStep : executedData) {
                BaseSelectEntity baseSelectEntity = new BaseSelectEntity();
                baseSelectEntity.setId(studentOfferItemStep.getId());
                String stepName = studentOfferItemStep.getStepName();
                StringBuffer stringBuffer = new StringBuffer();
                if(GeneralTool.isNotEmpty(stepKeyList) && stepKeyList.contains(stepKey)){
                    stringBuffer.append("（").append("申请已确定，不可回退").append("）").append(stepName);
                }else{
                    stringBuffer.append("（").append("回退").append("）").append(stepName);
                }

                baseSelectEntity.setName(stringBuffer.toString());
                baseSelectEntity.setNum(MediaTypeEnum.getKeyByStepId(studentOfferItemStep.getId()));
                if (studentOfferItemStep.getId() != 1) {
                    baseSelectEntity.setFullName(LocaleMessageUtils.getMessage(MediaTypeEnum.getKeyByStepId(studentOfferItemStep.getId())));
                }

                //判断步骤当前用户是否拥有权限操作,是：1L/否：0L
                //已执行的步骤添加权限
                if (GeneralTool.isNotEmpty(studentOfferItemStep.getRoleKey())) {
                    //获取步骤对应的角色
                    String[] key = studentOfferItemStep.getRoleKey().split(",");
                    List<StudentProjectRoleVo> studentProjectRoleVoList = new ArrayList<>();
                    for (String roleKey : key) {
                        if (GeneralTool.isNotEmpty(roleKey)) {
                            StudentProjectRoleVo studentProjectRoleVo = studentProjectRoleService.getRoleByKey(roleKey);
                            studentProjectRoleVoList.add(studentProjectRoleVo);
                        }
                    }
                    List<Long> roleIdList = studentProjectRoleVoList.stream().filter(Objects::nonNull).map(StudentProjectRoleVo::getId).collect(Collectors.toList());
                    //判断步骤当前用户是否拥有权限操作,是：1L/否：0L
                    Long status = Collections.disjoint(roleIds, roleIdList) ? 0L : 1L;


                    //如果该部门在白名单内，就有权限编辑步骤
                    if (departmentIds.contains(fkDepartmentId)){
                        status = 1L;
                    }

                    //如果步骤为Enrolled，需要判断用户资源key是否含有禁止key
                    if(studentOfferItemStep.getId().equals(8L) && apiKeys.contains(ProjectKeyEnum.STEP_ENROLLED_PROHIBITED.key)){
                            status = 0L;
                    }

                    //当前步骤为os后限制的步骤
                    if(GeneralTool.isNotEmpty(stepKeys) && stepKeys.contains(stepKey)){
                        //如果步骤为Failure，需要判断用户资源key是否含有允许key
                        if(studentOfferItemStep.getId().equals(9L) && !apiKeys.contains(ProjectKeyEnum.STEP_FAILURE_PERMIT.key)){
                            status = 0L;
                        }
                    }

                    baseSelectEntity.setStatus(status);
                } else {
                    baseSelectEntity.setStatus(0L);
                }

                //前端置灰，不可点击状态
                if(GeneralTool.isNotEmpty(stepKeyList) && stepKeyList.contains(stepKey)){
                    if(baseSelectEntity.getStatus().equals(1L)){
                        baseSelectEntity.setStatus(2L);
                    }

                }

                baseSelectEntity.setOrder(studentOfferItemStep.getStepOrder());
                executedStep.add(baseSelectEntity);
            }
        }
        //未执行步骤
//        List<StudentOfferItemStep> unexecutedData = studentOfferItemStepMapper.getUnexecutedItemStep(itemStepId);
        List<StudentOfferItemStep> unexecutedData = studentOfferItemStepMapper.getUnexecutedItemStepWithCountryConfig(itemStepId,fkStudentOfferItemId);
        if (GeneralTool.isNotEmpty(unexecutedData)) {
            for (StudentOfferItemStep studentOfferItemStep : unexecutedData) {
                BaseSelectEntity baseSelectEntity = new BaseSelectEntity();
                baseSelectEntity.setId(studentOfferItemStep.getId());
                baseSelectEntity.setName(studentOfferItemStep.getStepName());
                baseSelectEntity.setNum(MediaTypeEnum.getKeyByStepId(studentOfferItemStep.getId()));
                if (studentOfferItemStep.getId() != 1 && GeneralTool.isNotEmpty(MediaTypeEnum.getKeyByStepId(studentOfferItemStep.getId()))) {
                    baseSelectEntity.setFullName(LocaleMessageUtils.getMessage(MediaTypeEnum.getKeyByStepId(studentOfferItemStep.getId())));
                }
                baseSelectEntity.setOrder(studentOfferItemStep.getStepOrder());
                if (GeneralTool.isNotEmpty(studentOfferItemStep.getRoleKey())) {
                    //获取步骤对应的角色
                    String[] key = studentOfferItemStep.getRoleKey().split(",");
                    List<StudentProjectRoleVo> studentProjectRoleVoList = new ArrayList<>();
                    for (String roleKey : key) {
                        if (GeneralTool.isNotEmpty(roleKey)) {
                            StudentProjectRoleVo studentProjectRoleVo = studentProjectRoleService.getRoleByKey(roleKey);
                            studentProjectRoleVoList.add(studentProjectRoleVo);
                        }
                    }
                    List<Long> roleIdList = studentProjectRoleVoList.stream().filter(Objects::nonNull).map(StudentProjectRoleVo::getId).collect(Collectors.toList());
                    //判断步骤当前用户是否拥有权限操作,是：1L/否：0L
                    Long status = Collections.disjoint(roleIds, roleIdList) ? 0L : 1L;
//                    //如果业绩绑定的bd包含在登录人及其下属的ids中，就有权限编辑步骤
//                    if (staffIds.contains(bdId)){
//                        status = 1L;
//                    }

                    //如果该部门在白名单内，就有权限编辑步骤
                    if (departmentIds.contains(fkDepartmentId)){
                        status = 1L;
                    }

                    //如果步骤为Enrolled，需要判断用户资源key是否含有禁止key
                    if(studentOfferItemStep.getId().equals(8L) && apiKeys.contains(ProjectKeyEnum.STEP_ENROLLED_PROHIBITED.key)){
                        status = 0L;
                    }

                    //当前步骤为os后限制的步骤
                    if(GeneralTool.isNotEmpty(stepKeys) && stepKeys.contains(stepKey)){
                        //如果步骤为Failure，需要判断用户资源key是否含有允许key
                        if(studentOfferItemStep.getId().equals(9L) && !apiKeys.contains(ProjectKeyEnum.STEP_FAILURE_PERMIT.key)){
                            status = 0L;
                        }
                    }


                    baseSelectEntity.setStatus(status);
                } else {
                    baseSelectEntity.setStatus(0L);
                }

                unexecutedStep.add(baseSelectEntity);
            }
        }
        resoult.addAll(executedStep);
        resoult.addAll(unexecutedStep);
        resoult = resoult.stream().filter(f->f.getStatus()==1 || f.getStatus()==2).collect(Collectors.toList());
        resoult.sort(Comparator.comparing(BaseSelectEntity::getOrder));
        return resoult;
    }


    @Override
    public List<BaseSelectEntity> getUnexecutedItemStepWhitoutPermission(Long fkStudentOfferItemId) {
        if (GeneralTool.isEmpty(fkStudentOfferItemId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_is_null"));
        }

        //获取当前申请方案当前步骤
        StudentOfferItemStep step = studentOfferItemMapper.getStudentOfferItemStep(fkStudentOfferItemId);
        Long itemStepId = step.getId();
        List<BaseSelectEntity> executedStep = new ArrayList<>();
        List<BaseSelectEntity> unexecutedStep = new ArrayList<>();
        List<BaseSelectEntity> resoult = new ArrayList<>();
        //已经执行的步骤
        List<StudentOfferItemStep> executedData = studentOfferItemStepMapper.getExecutedItemStep(itemStepId);
        if (GeneralTool.isNotEmpty(executedData)) {
            for (StudentOfferItemStep studentOfferItemStep : executedData) {
                BaseSelectEntity baseSelectEntity = new BaseSelectEntity();
                baseSelectEntity.setId(studentOfferItemStep.getId());
                String stepName = studentOfferItemStep.getStepName();
                StringBuffer stringBuffer = new StringBuffer();
                stringBuffer.append("（").append("回退").append("）").append(stepName);
                baseSelectEntity.setName(stringBuffer.toString());
                baseSelectEntity.setNum(MediaTypeEnum.getKeyByStepId(studentOfferItemStep.getId()));
                if (studentOfferItemStep.getId() != 1) {
                    baseSelectEntity.setFullName(LocaleMessageUtils.getMessage(MediaTypeEnum.getKeyByStepId(studentOfferItemStep.getId())));
                }
                baseSelectEntity.setOrder(studentOfferItemStep.getStepOrder());
                executedStep.add(baseSelectEntity);
            }
        }
        //未执行步骤
        List<StudentOfferItemStep> unexecutedData = studentOfferItemStepMapper.getUnexecutedItemStep(itemStepId);
        if (GeneralTool.isNotEmpty(unexecutedData)) {
            for (StudentOfferItemStep studentOfferItemStep : unexecutedData) {
                BaseSelectEntity baseSelectEntity = new BaseSelectEntity();
                baseSelectEntity.setId(studentOfferItemStep.getId());
                baseSelectEntity.setName(studentOfferItemStep.getStepName());
                baseSelectEntity.setNum(MediaTypeEnum.getKeyByStepId(studentOfferItemStep.getId()));
                if (studentOfferItemStep.getId() != 1 && GeneralTool.isNotEmpty(MediaTypeEnum.getKeyByStepId(studentOfferItemStep.getId()))) {
                    baseSelectEntity.setFullName(LocaleMessageUtils.getMessage(MediaTypeEnum.getKeyByStepId(studentOfferItemStep.getId())));
                }
                baseSelectEntity.setOrder(studentOfferItemStep.getStepOrder());
                unexecutedStep.add(baseSelectEntity);
            }
        }
        resoult.addAll(executedStep);
        resoult.addAll(unexecutedStep);
        resoult.stream().sorted(Comparator.comparing(BaseSelectEntity::getOrder));
        return resoult;
    }

    @Override
    public Boolean hasChannelOrProvider(Long fkStudentOfferItemId){
        if(GeneralTool.isEmpty(fkStudentOfferItemId)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_is_null"));
        }
        //判断申请计划的提供商或渠道为空
        if(GeneralTool.isNotEmpty(fkStudentOfferItemId)){
            StudentOfferItem studentOfferItem = studentOfferItemMapper.selectById(fkStudentOfferItemId);
            //提供商为-1需要再次填写
            if (GeneralTool.isEmpty(studentOfferItem.getFkInstitutionProviderId()) || GeneralTool.isEmpty(studentOfferItem.getFkInstitutionChannelId())){
                return true;
            }
            if (studentOfferItem.getFkInstitutionProviderId() == -1L || studentOfferItem.getFkInstitutionChannelId() == -1){
                return true;
            }
            if(GeneralTool.isNotEmpty(studentOfferItem.getFkInstitutionProviderId()) && GeneralTool.isNotEmpty(studentOfferItem.getFkInstitutionChannelId())){
                return false;
            }
        }
        return true;
    }

//    @Override
//    public String getStepNameByItemId(Long formId) {
//        if (GeneralTool.isEmpty(formId)) {
//            return null;
//        }
//        return studentOfferItemStepMapper.getStepNameByItemId(formId);
//    }

    /**
     * 根据学习计划申请状态查询学生id
     *
     * @param state 1,2,3,4
     * @return
     */
    @Override
    public List<Long> getStudentIdByState(String state) {
        String[] split = state.split(",");
        List<Long> stateList = Arrays.stream(split).map(Long::parseLong).collect(Collectors.toList());
        List<Map<String, Object>> studentMinStepList = studentOfferItemMapper.getMinStepOrderByStudentId(null, stateList);
        List<Long> studentIds = new ArrayList<>();
        //着急上线，临时加需求 先用着
        for (Map<String, Object> map : studentMinStepList) {
            Object studentId = map.get("studentId");
            studentIds.add(Long.valueOf(studentId.toString()));
        }
        return studentIds;
    }

    @Override
    public ResponseBo<Map> stepEnrolledLimit() {
        ConfigVo configVo = permissionCenterClient.getConfigByKey(ProjectKeyEnum.STEP_ENROLLED_OPT_LIMIT.key).getData();
        if (configVo ==null || StringUtils.isBlank(configVo.getValue1())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("缺少佣金结算业务配置"));
        }
        Map map = JSON.parseObject(configVo.getValue1(), Map.class);
        return new ResponseBo<>(map);
    }

    @Override
    public Set<String> getItemStepSelectByStepKey(List<String> stepString) {
        LambdaQueryWrapper<StudentOfferItemStep> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(StudentOfferItemStep::getStepKey,stepString);
        lambdaQueryWrapper.orderByAsc(StudentOfferItemStep::getStepOrder);
        List<StudentOfferItemStep> studentOfferItemSteps = this.studentOfferItemStepMapper.selectList(lambdaQueryWrapper);
        Set<String> stepNameString = studentOfferItemSteps.stream().map(StudentOfferItemStep::getStepName).collect(Collectors.toSet());
        return stepNameString;
    }

    @Override
    public List<BaseSelectEntity> getSuccessfulCustomerStepSelect(Long fkCompanyId) {
        Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.SUCCESSFUL_LIST_DEFAULT_STEP.key, 1).getData();
//        ConfigVo configDto = permissionCenterClient.getConfigByKey(ProjectKeyEnum.SUCCESSFUL_LIST_DEFAULT_STEP.key).getData();
        if (companyConfigMap==null || StringUtils.isBlank(companyConfigMap.get(fkCompanyId))){
            throw new GetServiceException(LocaleMessageUtils.getMessage("成功客户列表缺少默认步骤配置"));
        }
        String configValue1 = companyConfigMap.get(fkCompanyId);
        List<String> list = new ArrayList<>(JSON.parseArray(configValue1, String.class));
//        JSONObject jsonObject = JSONObject.parseObject(configDto.getValue1());
//        if (fkCompanyId==3) {
//            list = JSONObject.parseObject(jsonObject.getString("IAE"), new TypeReference<List<String>>() {
//            });
//        }else {
//            list = JSONObject.parseObject(jsonObject.getString("OTHER"), new TypeReference<List<String>>() {
//            });
//        }
        return studentOfferItemStepMapper.getSuccessfulCustomerStepSelect(list);
    }

    @Override
    public List<BaseSelectEntity> getFailureCustomerStepSelect(Long fkCompanyId) {
        List<String> list = Lists.newArrayList(ProjectKeyEnum.STEP_FAILURE.key, ProjectKeyEnum.STEP_APPLY_REFUND.key);
        return studentOfferItemStepMapper.getSuccessfulCustomerStepSelect(list);
    }

    @Override
    public List<StudentOfferItemStep> getItemStepPostpositionByStepId(Long itemStepId) {
        if (GeneralTool.isEmpty(itemStepId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_is_null"));
        }
        return studentOfferItemStepMapper.getItemStepPostpositionByStepId(itemStepId);
    }

    @Override
    public List<StudentOfferItemStep> getItemStepPostpositionCOE() {
        List<String> valuesByKeys = ProjectKeyEnum.getValuesByKeys(ProjectKeyEnum.COE_POST_STEP);
        LambdaQueryWrapper<StudentOfferItemStep> lambdaQueryWrapper = new LambdaQueryWrapper<StudentOfferItemStep>().in(StudentOfferItemStep::getStepKey, valuesByKeys);
        return studentOfferItemStepMapper.selectList(lambdaQueryWrapper);
    }

    @Override
    public OfferItemLimitConfigVo getItemStepLimtit(Long fkStudentOfferItemStepId, Long fkStudentOfferItemId) {
        OfferItemLimitConfigVo offerItemLimitConfigVo = new OfferItemLimitConfigVo();

        Map<Long, String> payMentMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.STEP_OS_OPT_LIMIT.key, 3).getData();
        //押金支付方式配置
        if (GeneralTool.isNotEmpty(fkStudentOfferItemStepId) && fkStudentOfferItemStepId == 5L && payMentMap.get(SecureUtil.getFkCompanyId()).equals("1")){
            offerItemLimitConfigVo.setDepositPaymentLimit(true);
        }
        //学费支付方式配置
        if (GeneralTool.isNotEmpty(fkStudentOfferItemStepId) && fkStudentOfferItemStepId == 17L && payMentMap.get(SecureUtil.getFkCompanyId()).equals("1")){
            offerItemLimitConfigVo.setTuitionPaymentLimit(true);
        }
        //COE步骤
        if (GeneralTool.isNotEmpty(fkStudentOfferItemStepId) && fkStudentOfferItemStepId == 6L && payMentMap.get(SecureUtil.getFkCompanyId()).equals("1")){
            offerItemLimitConfigVo.setTuitionPaymentLimit(true);
            offerItemLimitConfigVo.setDepositPaymentLimit(true);
        }

        return offerItemLimitConfigVo;
    }




    private boolean validateAdd(StudentOfferItemStepDto studentOfferItemStepDto) {
//        Example example = new Example(StudentOfferItemStep.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("stepKey", studentOfferItemStepDto.getStepKey());
//        List<StudentOfferItemStep> list = this.studentOfferItemStepMapper.selectByExample(example);
        List<StudentOfferItemStep> list = this.studentOfferItemStepMapper.selectList(Wrappers.<StudentOfferItemStep>lambdaQuery().eq(StudentOfferItemStep::getStepKey, studentOfferItemStepDto.getStepKey()));
        return GeneralTool.isEmpty(list);
    }

    private boolean validateUpdate(StudentOfferItemStepDto studentOfferItemStepDto) {
//        Example example = new Example(StudentOfferItemStep.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("stepKey", studentOfferItemStepDto.getStepKey());
//        List<StudentOfferItemStep> list = this.studentOfferItemStepMapper.selectByExample(example);

        List<StudentOfferItemStep> list = this.studentOfferItemStepMapper.selectList(Wrappers.<StudentOfferItemStep>lambdaQuery().eq(StudentOfferItemStep::getStepKey, studentOfferItemStepDto.getStepKey()));
        return list.size() <= 0 || list.get(0).getId().equals(studentOfferItemStepDto.getId());
    }
}

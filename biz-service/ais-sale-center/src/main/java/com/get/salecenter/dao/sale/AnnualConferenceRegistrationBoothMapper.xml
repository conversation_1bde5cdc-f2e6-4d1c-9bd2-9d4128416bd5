<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.AnnualConferenceRegistrationBoothMapper">

  <insert id="insertSelective" parameterType="com.get.salecenter.entity.AnnualConferenceRegistrationBooth" keyProperty="id" useGeneratedKeys="true">
    insert into m_annual_conference_registration_booth
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkAnnualConferenceRegistrationId != null">
        fk_annual_conference_registration_id,
      </if>
      <if test="boothName != null">
        booth_name,
      </if>
      <if test="countryRegion != null">
        country_region,
      </if>
      <if test="boothIndex != null">
        booth_index,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkAnnualConferenceRegistrationId != null">
        #{fkAnnualConferenceRegistrationId,jdbcType=BIGINT},
      </if>
      <if test="boothName != null">
        #{boothName,jdbcType=VARCHAR},
      </if>
      <if test="countryRegion != null">
        #{countryRegion,jdbcType=VARCHAR},
      </if>
      <if test="boothIndex != null">
        #{boothIndex,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>
package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.api.ResultCode;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.entity.Company;
import com.get.salecenter.dao.sale.ConventionTableTypeMapper;
import com.get.salecenter.vo.ConventionTableTypeVo;
import com.get.salecenter.entity.ConventionTableType;
import com.get.salecenter.service.IConventionTableTypeService;
import com.get.salecenter.dto.ConventionTableTypeDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/8/25 15:02
 * @verison: 1.0
 * @description:
 */
@Service
public class ConventionTableTypeServiceImpl extends ServiceImpl<ConventionTableTypeMapper,ConventionTableType> implements IConventionTableTypeService {

    @Resource
    private ConventionTableTypeMapper conventionTableTypeMapper;

    @Resource
    private UtilService utilService;

    @Override
    public ConventionTableTypeVo findTableTypeById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ConventionTableType conventionTableType = conventionTableTypeMapper.selectById(id);
        return BeanCopyUtils.objClone(conventionTableType, ConventionTableTypeVo::new);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(List<ConventionTableTypeDto> tableTypeVos) {
        if (GeneralTool.isEmpty(tableTypeVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        //获取maxViewOrder最大排序((防止每次都要去查最大值，所以在外面查出一次，循环几次 就自增几次）)
        Integer maxViewOrder = conventionTableTypeMapper.getMaxViewOrder();
        for (ConventionTableTypeDto tableTypeVo : tableTypeVos) {
            if (GeneralTool.isEmpty(tableTypeVo.getId())) {
                ConventionTableType conventionTableType = BeanCopyUtils.objClone(tableTypeVo, ConventionTableType::new);
                String validateResult = validateAdd(tableTypeVo);
                if (GeneralTool.isEmpty(validateResult)) {
                    conventionTableType.setViewOrder(maxViewOrder);
                    utilService.updateUserInfoToEntity(conventionTableType);
                    conventionTableTypeMapper.insertSelective(conventionTableType);
                    maxViewOrder++;
                } else {
                    throw new GetServiceException(validateResult);
                }
            } else {
                ConventionTableType conventionTableType = BeanCopyUtils.objClone(tableTypeVo, ConventionTableType::new);
                String validateResult = validateUpdate(tableTypeVo);
                if (GeneralTool.isEmpty(validateResult)) {
                    utilService.updateUserInfoToEntity(conventionTableType);
                    conventionTableTypeMapper.updateById(conventionTableType);
                } else {
                    throw new GetServiceException(validateResult);
                }
            }

        }
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (findTableTypeById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        conventionTableTypeMapper.deleteById(id);
    }

    @Override
    public ConventionTableTypeVo updateTableType(ConventionTableTypeDto tableTypeVo) {
        if (tableTypeVo == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        ConventionTableType result = conventionTableTypeMapper.selectById(tableTypeVo.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }

        ConventionTableType conventionTableType = BeanCopyUtils.objClone(tableTypeVo, ConventionTableType::new);
        String validateResult = validateUpdate(tableTypeVo);
        if (GeneralTool.isEmpty(validateResult)) {
            utilService.updateUserInfoToEntity(conventionTableType);
            conventionTableTypeMapper.updateById(conventionTableType);
        } else {
            throw new GetServiceException(validateResult);
        }
        return findTableTypeById(tableTypeVo.getId());
    }

    @Override
    public List<ConventionTableTypeVo> getTableTypes(ConventionTableTypeDto tableTypeVo, Page page) {
        LambdaQueryWrapper<ConventionTableType> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(tableTypeVo)) {
            if (GeneralTool.isNotEmpty(tableTypeVo.getKeyWord())) {
                lambdaQueryWrapper.like(ConventionTableType::getTypeName, tableTypeVo.getKeyWord());
                lambdaQueryWrapper.or().like(ConventionTableType::getTypeKey, tableTypeVo.getKeyWord());
            }
        }
        lambdaQueryWrapper.orderByDesc(ConventionTableType::getViewOrder);
        IPage<ConventionTableType> iPage = conventionTableTypeMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
        List<ConventionTableType> conventionTableTypes = iPage.getRecords();
        page.setAll((int) iPage.getTotal());

        List<ConventionTableTypeVo> converDatas = new ArrayList<>();
        for (ConventionTableType conventionTableType : conventionTableTypes) {
            ConventionTableTypeVo conventionTableTypeVo = BeanCopyUtils.objClone(conventionTableType, ConventionTableTypeVo::new);
            converDatas.add(conventionTableTypeVo);
        }
        return converDatas;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void movingOrder(List<ConventionTableTypeDto> tableTypeVos) {
        if (GeneralTool.isEmpty(tableTypeVos)) {
            throw new GetServiceException(ResultCode.INVALID_PARAM, "传入值为空");
        }
        ConventionTableType ro = BeanCopyUtils.objClone(tableTypeVos.get(0), ConventionTableType::new);
        Integer oneorder = ro.getViewOrder();
        ConventionTableType rt = BeanCopyUtils.objClone(tableTypeVos.get(1), ConventionTableType::new);
        Integer twoorder = rt.getViewOrder();
        ro.setViewOrder(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setViewOrder(oneorder);
        utilService.updateUserInfoToEntity(rt);
        conventionTableTypeMapper.updateById(ro);
        conventionTableTypeMapper.updateById(rt);
    }

    @Override
    public List<ConventionTableTypeVo> getTableTypeList() {
        List<ConventionTableType> conventionTableTypes = conventionTableTypeMapper.selectList(Wrappers.<ConventionTableType>lambdaQuery());
        return BeanCopyUtils.copyListProperties(conventionTableTypes, ConventionTableTypeVo::new);
    }

    private String validateAdd(ConventionTableTypeDto tableTypeVo) {
        List<ConventionTableType> list = getConventionTableTypeList(tableTypeVo);
        if (GeneralTool.isNotEmpty(list)) {
            StringBuilder sb = new StringBuilder();
            String resultMsg = null;
            for (ConventionTableType tableType : list) {
                resultMsg = setValidMsg(tableTypeVo, sb, tableType);
            }
            return resultMsg;
        }
        return null;
    }

    private String validateUpdate(ConventionTableTypeDto tableTypeVo) {
        List<ConventionTableType> list = getConventionTableTypeList(tableTypeVo);
        if (GeneralTool.isNotEmpty(list)) {
            StringBuilder sb = new StringBuilder();
            String resultMsg = null;
            for (ConventionTableType tableType : list) {
                if (!tableTypeVo.getId().equals(tableType.getId())) {
                    resultMsg = setValidMsg(tableTypeVo, sb, tableType);
                }
            }
            return resultMsg;
        }
        return null;
    }

    /**
     * @return java.util.List<com.get.salecenter.entity.ConventionHotel>
     * @Description :根据验证条件获取list
     * @Param [tableTypeVo]
     * <AUTHOR>
     */
    private List<ConventionTableType> getConventionTableTypeList(ConventionTableTypeDto tableTypeVo) {
//        Example example = new Example(ConventionTableType.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("typeName", tableTypeVo.getTypeName());
//        criteria.orEqualTo("typeKey", tableTypeVo.getTypeKey());
        return this.conventionTableTypeMapper.selectList(Wrappers.<ConventionTableType>lambdaQuery().eq(ConventionTableType::getTypeName, tableTypeVo.getTypeName())
                .or().eq(ConventionTableType::getTypeKey, tableTypeVo.getTypeKey()));
    }

    /**
     * @return java.lang.String
     * @Description :设置返回验证信息
     * @Param [tableTypeVo, sb, tableType]
     * <AUTHOR>
     */
    private String setValidMsg(ConventionTableTypeDto tableTypeVo, StringBuilder sb, ConventionTableType tableType) {
        if (tableType.getTypeName().equals(tableTypeVo.getTypeName())) {
            sb.append("类型名称已存在，");
        }
        if (tableType.getTypeKey().equals(tableTypeVo.getTypeKey())) {
            sb.append("类型关键字已存在，");
        }
        return sub(sb);
    }

    /**
     * @return java.lang.String
     * @Description :截取字符串逗号
     * @Param [sb]
     * <AUTHOR>
     */
    private String sub(StringBuilder sb) {
        if (GeneralTool.isEmpty(sb)) {
            return null;
        }
        String substring = null;
        int i = sb.lastIndexOf("，");
        if (i != -1) {
            substring = sb.substring(0, i);
        }
        return substring;
    }
}

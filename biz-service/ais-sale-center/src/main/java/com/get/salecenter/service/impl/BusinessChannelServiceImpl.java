package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.CommonUtil;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.vo.tree.CompanyTreeVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.dao.sale.BusinessChannelCompanyMapper;
import com.get.salecenter.dao.sale.BusinessChannelMapper;
import com.get.salecenter.vo.BusinessChannelVo;
import com.get.salecenter.vo.StudentAccommodationVo;
import com.get.salecenter.vo.StudentInsuranceVo;
import com.get.salecenter.entity.BusinessChannel;
import com.get.salecenter.entity.BusinessChannelCompany;
import com.get.salecenter.service.IBusinessChannelService;
import com.get.salecenter.service.IStudentAccommodationService;
import com.get.salecenter.service.IStudentInsuranceService;
import com.get.salecenter.utils.MyStringUtils;
import com.get.salecenter.dto.BusinessChannelDto;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.StringJoiner;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2022/1/7
 * @TIME: 16:24
 * @Description:
 **/
@Service
public class BusinessChannelServiceImpl implements IBusinessChannelService {
    @Resource
    private UtilService utilService;
    @Resource
    private BusinessChannelMapper businessChannelMapper;
    @Resource
    private IPermissionCenterClient feignPermissionService;
    @Lazy
    @Resource
    private IStudentInsuranceService studentInsuranceService;
    @Lazy
    @Resource
    private IStudentAccommodationService studentAccommodationService;
    @Resource
    private IInstitutionCenterClient iInstitutionCenterClient;

    @Resource
    private BusinessChannelCompanyMapper bomChannelCompanyMapper;

    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Resource
    private BusinessChannelCompanyMapper businessChannelCompanyMapper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addBusinessChannel(List<BusinessChannelDto> businessChannelDtos) {
        if (GeneralTool.isEmpty(businessChannelDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        for (BusinessChannelDto businessChannelDto : businessChannelDtos) {
            if (GeneralTool.isEmpty(businessChannelDto.getId())) {
                BusinessChannel businessChannel = BeanCopyUtils.objClone(businessChannelDto, BusinessChannel::new);
                utilService.updateUserInfoToEntity(businessChannel);
                businessChannelMapper.insert(businessChannel);
                businessChannel.setNum(MyStringUtils.getBusinessChannelNum(businessChannel.getFkTypeKey(), businessChannel.getId()));
                businessChannelMapper.updateById(businessChannel);
                if (GeneralTool.isNotEmpty(businessChannel.getId())) {
                    BusinessChannelCompany businessChannelCompany = new BusinessChannelCompany();
                    businessChannelCompany.setFkCompanyId(businessChannelDto.getFkCompanyId());
                    businessChannelCompany.setFkBusinessChannelId(businessChannel.getId());
                    utilService.setCreateInfo(businessChannelCompany);
                    businessChannelCompanyMapper.insert(businessChannelCompany);
                }
            } else {
                BusinessChannel businessChannel = BeanCopyUtils.objClone(businessChannelDto, BusinessChannel::new);
                utilService.updateUserInfoToEntity(businessChannel);
                businessChannelMapper.updateById(businessChannel);
            }

        }
    }

    @Override
    public List<BusinessChannelVo> getBusinessChannelDtos(BusinessChannelDto businessChannelDto, Page page) {
        Map<Long, String> companyMap = getCompanyMap();
        IPage<BusinessChannelVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<BusinessChannelVo> dtos = businessChannelMapper.getDatas(iPage, businessChannelDto);
        page.setAll((int) iPage.getTotal());
        for (BusinessChannelVo businessChannelVo : dtos) {
            businessChannelVo.setFkTypeKeyName(TableEnum.getValue(businessChannelVo.getFkTypeKey()));

            List<BusinessChannelCompany> businessChannelCompanies = bomChannelCompanyMapper.selectList(
                    Wrappers.<BusinessChannelCompany>lambdaQuery().eq(BusinessChannelCompany::getFkBusinessChannelId, businessChannelVo.getId()));
            StringBuilder builder = new StringBuilder();
            for (BusinessChannelCompany businessChannelCompany : businessChannelCompanies) {
                String name = companyMap.get(businessChannelCompany.getFkCompanyId());
                builder.append(name).append("，");
                businessChannelVo.setCompanyName(sub(builder));
            }
//            Result<String> result = feignPermissionService.getCompanyNameById(businessChannelVo.getFkCompanyId());
//            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
//                businessChannelVo.setCompanyName(result.getData());
//            }
        }
        return dtos;
    }

    private String sub(StringBuilder sb) {
        if (GeneralTool.isEmpty(sb)) {
            return null;
        }
        String substring = null;
        int i = sb.lastIndexOf("，");
        if (i != -1) {
            substring = sb.substring(0, i);
        }
        return substring;
    }

    private Map<Long, String> getCompanyMap() {
        Result<List<CompanyTreeVo>> result = permissionCenterClient.getAllCompanyDto();
        if (!result.isSuccess()) {
            throw new GetServiceException(result.getMessage());
        }
        List<CompanyTreeVo> companyTreeVos = result.getData();
        //初始为5的map
        Map<Long, String> companyMap = new HashMap<>(5);
        if (GeneralTool.isNotEmpty(companyTreeVos)) {
            companyMap = companyTreeVos.stream().collect(Collectors.toMap(CompanyTreeVo::getId, CompanyTreeVo::getShortName));
        }
        return companyMap;
    }

    @Override
    public Map<Long,String> getNamesByIds(Set<Long> ids){
        if (GeneralTool.isEmpty(ids)) {
            return Collections.emptyMap();
        }
        List<BusinessChannelVo> namesByIds = businessChannelMapper.getNamesByIds(ids);
        if (GeneralTool.isNotEmpty(namesByIds)) {
            Map<Long,String> map=new HashMap<>(namesByIds.size());
            for (BusinessChannelVo namesById : namesByIds) {
                map.put(namesById.getId(),namesById.getName());
            }
            return map;
        }
        return Collections.emptyMap();
    }

    @Override
    public BusinessChannelVo updateBusinessChannel(BusinessChannelDto businessChannelDto) {
        BusinessChannel businessChannel = BeanCopyUtils.objClone(businessChannelDto, BusinessChannel::new);
        utilService.updateUserInfoToEntity(businessChannel);
        businessChannelMapper.updateById(businessChannel);
        return findBusinessChannelById(businessChannel.getId());
    }

    @Override
    public void deleteBusinessChannel(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        businessChannelMapper.deleteById(id);
    }

    @Override
    public BusinessChannelVo findBusinessChannelById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        BusinessChannel businessChannel = businessChannelMapper.selectById(id);
        return BeanCopyUtils.objClone(businessChannel, BusinessChannelVo::new);
    }

    /**
     * @Description:类型下拉框数据
     * @Param
     * @Date 15:49 2021/5/12
     * <AUTHOR>
     */
    @Override
    public List<Map<String, Object>> getModuleKey() {
        return ProjectKeyEnum.enumsTranslation2Arrays(ProjectKeyEnum.BUSINESS_TYPE);
    }

    @Override
    public List<BaseSelectEntity> channelSelect(String tableName,Long companyId) {
        return businessChannelMapper.channelSelect(tableName,companyId);
    }

    @Override
    public List<BaseSelectEntity> getTargetName(String tableName, Long companyId) {
        return businessChannelMapper.getTargetName(tableName, companyId);
    }

    @Override
    public List<Long> getChannelIds(String tableName, String channelName) {
        return businessChannelMapper.getChannelIds(tableName, channelName);
    }

    @Override
    public Set<Long> getBusinessId(Map<String, Set<Long>> params) {
        if (GeneralTool.isEmpty(params)) {
            return null;
        }
        String typeKey;
        Set<Long> result = new HashSet<>();
        for (Map.Entry<String, Set<Long>> entry : params.entrySet()) {
            typeKey = entry.getKey();
            if (ProjectKeyEnum.M_STUDENT_INSURANCE.key.equals(typeKey)) {
                result.addAll(businessChannelMapper.getBusinessId(ProjectKeyEnum.M_STUDENT_INSURANCE.key, entry.getValue()));
            } else if (ProjectKeyEnum.M_STUDENT_ACCOMMODATION.key.equals(typeKey)) {
                result.addAll(businessChannelMapper.getBusinessId(ProjectKeyEnum.M_STUDENT_ACCOMMODATION.key, entry.getValue()));
            }
        }
        return result;
    }

    /**
     * 获取留学住宿提供商id
     * @param ids
     * @return
     */
    @Override
    public Set<Long> getBusinessProviderId(Set<Long> ids) {
        if (GeneralTool.isEmpty(ids)) {
            return Collections.emptySet();
        }
        return businessChannelMapper.getBusinessProviderIdByAccIds(ids);
    }

    @Override
    public Map<Long, String> getChannelNamesByIds(Set<Long> ids) {
        if (GeneralTool.isEmpty(ids)) {
            return null;
        }
        LambdaQueryWrapper<BusinessChannel> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(BusinessChannel::getId, ids);
        List<BusinessChannel> businessChannelS = businessChannelMapper.selectList(lambdaQueryWrapper);
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(businessChannelS)) {
            return map;
        }
        for (BusinessChannel businessChannel : businessChannelS) {
            String name = GeneralTool.isEmpty(businessChannel.getName()) ? "" : businessChannel.getName();
            StringBuilder builder = new StringBuilder(name);
            if (GeneralTool.isNotEmpty(businessChannel.getNameChn())) {
                builder.append("（");
                builder.append(businessChannel.getNameChn());
                builder.append("）");
            }
            map.put(businessChannel.getId(), builder.toString());
        }
        return map;
    }

    @Override
    public List<BaseSelectEntity> getPlanIdsByTableNameAndChannelId(String tableName, Long channelId, Long receiptFormId, String fkTypeKey, Integer pageNumber, Integer pageSize) {
        Integer offset = CommonUtil.getOffset(pageNumber, pageSize);
        List<BusinessChannelVo> businessChannelVos = new ArrayList<>();
        if (TableEnum.BUSINESS_CHANNEL_ACC.key.equals(fkTypeKey) || TableEnum.BUSINESS_CHANNEL_INS.key.equals(fkTypeKey)){
            businessChannelVos = businessChannelMapper.getPlanIdsByChannel(channelId, receiptFormId, fkTypeKey, offset, pageSize);
        }
        businessChannelVos.addAll(businessChannelMapper.getPlanIdsByTableNameAndChannelId(tableName, channelId, receiptFormId, fkTypeKey, offset, pageSize));
        List<BaseSelectEntity> selectEntities = new ArrayList<>();
        if (TableEnum.SALE_STUDENT_INSURANCE.key.equals(tableName)) {
            for (BusinessChannelVo businessChannelVo : businessChannelVos) {
                BaseSelectEntity baseSelectEntity = new BaseSelectEntity();
                if (TableEnum.BUSINESS_CHANNEL_INS.key.equals(businessChannelVo.getFkTypeKey())) {
                    baseSelectEntity.setName("【"+ businessChannelVo.getFullName()+"】");
                    baseSelectEntity.setId(businessChannelVo.getPlanId());
                    selectEntities.add(baseSelectEntity);
                    continue;
                }
                StudentInsuranceVo studentInsuranceById = studentInsuranceService.findStudentInsuranceById(businessChannelVo.getId());
                String targetName = studentInsuranceById.getStudentName();
                String agentName = studentInsuranceById.getFkAgentName();
                String selectName = getInsurseSelectName(studentInsuranceById, targetName, agentName);
                baseSelectEntity.setName(selectName);
                baseSelectEntity.setId(businessChannelVo.getPlanId());
                selectEntities.add(baseSelectEntity);
            }
        } else if (TableEnum.SALE_STUDENT_ACCOMMODATION.key.equals(tableName)) {
            for (BusinessChannelVo businessChannelVo : businessChannelVos) {
                BaseSelectEntity baseSelectEntity = new BaseSelectEntity();
                if (TableEnum.BUSINESS_CHANNEL_ACC.key.equals(businessChannelVo.getFkTypeKey())) {
                    baseSelectEntity.setName("【"+ businessChannelVo.getFullName()+"】");
                    baseSelectEntity.setId(businessChannelVo.getPlanId());
                    selectEntities.add(baseSelectEntity);
                    continue;
                }
                StudentAccommodationVo studentAccommodationById = studentAccommodationService.findStudentAccommodationById(businessChannelVo.getId());
                String targetName = studentAccommodationById.getStudentName();
                String agentName = studentAccommodationById.getFkAgentName();
                String selectName = getAccommodationSelectName(studentAccommodationById, targetName, agentName);
                baseSelectEntity.setName(selectName);
                baseSelectEntity.setId(businessChannelVo.getPlanId());
                selectEntities.add(baseSelectEntity);
            }

        }

        return selectEntities;
    }

    @Override
    public List<CompanyTreeVo> allCompany(Long companyId) {
        return permissionCenterClient.getCompanyTree(companyId).getData();
    }

    @Override
    public List<CompanyTreeVo> getChannelCompanyRelation(Long channelId) {
        List<BusinessChannelCompany> businessChannelCompanies = businessChannelCompanyMapper.selectList(
                Wrappers.<BusinessChannelCompany>lambdaQuery().eq(BusinessChannelCompany::getFkBusinessChannelId, channelId));
        if (GeneralTool.isEmpty(channelId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        //获取公司
        List<CompanyTreeVo> companyTreeVo = permissionCenterClient.getAllCompanyDto().getData();
        if (GeneralTool.isEmpty(companyTreeVo)) {
            return null;
        }
        for (CompanyTreeVo treeDto : companyTreeVo) {
            for (BusinessChannelCompany businessChannelCompany : businessChannelCompanies) {
                if (treeDto.getId().longValue() == (businessChannelCompany.getFkCompanyId().longValue())) {
                    treeDto.setFlag(true);
                }
            }

        }
        return getTreeList(companyTreeVo);
    }

    @Override
    public void editChannelCompanyRelation(List<BusinessChannelDto> businessChannelDtos) {
        if (GeneralTool.isEmpty(businessChannelDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }

        List<Long> collect = businessChannelDtos.stream()
                .filter(Objects::nonNull)
                .map(BusinessChannelDto::getFkCompanyId).collect(Collectors.toList());

        //移除空元素
        collect.removeIf(Objects::isNull);
        List<BusinessChannelCompany>  companyList  = businessChannelCompanyMapper.selectList(
                    Wrappers.<BusinessChannelCompany>lambdaQuery()
                            .eq(BusinessChannelCompany::getFkBusinessChannelId, businessChannelDtos.get(0).getFkBusinessChannelId())

            );
        List<Long> companyIds = companyList.stream().map(BusinessChannelCompany::getFkCompanyId).collect(Collectors.toList());

        LambdaQueryWrapper<BusinessChannelCompany> wrapper = new LambdaQueryWrapper();
        wrapper.eq(BusinessChannelCompany::getFkBusinessChannelId, businessChannelDtos.get(0).getFkBusinessChannelId()).in(BusinessChannelCompany::getFkCompanyId,companyIds);
        businessChannelCompanyMapper.delete(wrapper);

        if (GeneralTool.isNotEmpty(collect)) {
            List<BusinessChannelCompany> channelCompanies = businessChannelDtos.stream()
                    .map(businessChannelDto ->
                            BeanCopyUtils.objClone(businessChannelDto, BusinessChannelCompany::new)).collect(Collectors.toList());
            //循环插入
            channelCompanies.forEach(businessChannelCompany -> {
                utilService.setCreateInfo(businessChannelCompany);
                businessChannelCompanyMapper.insert(businessChannelCompany);
            });
        }
    }

    private List<CompanyTreeVo> getTreeList(List<CompanyTreeVo> entityList) {
        List<CompanyTreeVo> resultList = new ArrayList<>();
        // 获取顶层元素集合
        String parentId;
        for (CompanyTreeVo entity : entityList) {
            parentId = String.valueOf(entity.getFkParentCompanyId());
            if (parentId == null || "0".equals(parentId)) {
                //获取父节点的部门信息
                resultList.add(entity);
            }
        }
        //假如没有父节点
        if (GeneralTool.isEmpty(resultList)) {
            //获取最小节点
            CompanyTreeVo minTreeNode = entityList.stream().min(Comparator.comparing(CompanyTreeVo::getFkParentCompanyId)).get();
            resultList.add(minTreeNode);
            if (GeneralTool.isNotEmpty(minTreeNode)) {
                //获取相同的最小节点
                List<CompanyTreeVo> minTreeNodes = entityList.stream().filter(treeDto ->
                        treeDto.getFkParentCompanyId().equals(minTreeNode.getFkParentCompanyId()) &&
                                treeDto.getId().longValue() != (minTreeNode.getId().longValue())).distinct().collect(Collectors.toList());
                resultList.addAll(minTreeNodes);
            }
        }
        // 获取每个顶层元素的子数据集合
        for (CompanyTreeVo entity : resultList) {
            entity.setChildList(getSubList(entity.getId(), entityList));
        }
        return resultList;
    }

    private List<CompanyTreeVo> getSubList(Long id, List<CompanyTreeVo> entityList) {
        List<CompanyTreeVo> childList = new ArrayList<>();
        // 子集的直接子对象
        for (CompanyTreeVo entity : entityList) {
            if (id.longValue() == (entity.getFkParentCompanyId())) {
                //获取子节点的部门信息
                childList.add(entity);
            }
        }
        // 子集的间接子对象
        for (CompanyTreeVo entity : childList) {
            //递归调用
            entity.setChildList(getSubList(entity.getId(), entityList));
        }
        // 递归退出条件
        if (childList.size() == 0) {
            return null;
        }
        return childList;
    }

    private String getAccommodationSelectName(StudentAccommodationVo studentAccommodationById, String targetName, String agentName) {
        StringJoiner stringJoiner = new StringJoiner("/");
        if (GeneralTool.isNotEmpty(studentAccommodationById.getNum())) {
            stringJoiner.add("【" + studentAccommodationById.getNum() + "】");
        }
        if (GeneralTool.isNotEmpty(targetName)) {
            stringJoiner.add(targetName);
        }
        if (GeneralTool.isNotEmpty(studentAccommodationById.getFkAreaCountryName())) {
            stringJoiner.add(studentAccommodationById.getFkAreaCountryName());
        }
        if (GeneralTool.isNotEmpty(studentAccommodationById.getChannelName())) {
            stringJoiner.add(studentAccommodationById.getChannelName());
        }
        if (GeneralTool.isNotEmpty(agentName)) {
            stringJoiner.add(agentName);
        }
        return stringJoiner.toString();
    }

    private String getInsurseSelectName(StudentInsuranceVo studentInsuranceById, String targetName, String agentName) {
        StringJoiner stringJoiner = new StringJoiner("/");
        if (GeneralTool.isNotEmpty(studentInsuranceById.getNum())) {
            stringJoiner.add("【" + studentInsuranceById.getNum() + "】");
        }
        if (GeneralTool.isNotEmpty(targetName)) {
            stringJoiner.add(targetName);
        }
        if (GeneralTool.isNotEmpty(studentInsuranceById.getFkAreaCountryName())) {
            stringJoiner.add(studentInsuranceById.getFkAreaCountryName());
        }
        if (GeneralTool.isNotEmpty(studentInsuranceById.getChannelName())) {
            stringJoiner.add(studentInsuranceById.getChannelName());
        }
        if (GeneralTool.isNotEmpty(agentName)) {
            stringJoiner.add(agentName);
        }
        return stringJoiner.toString();
    }
}

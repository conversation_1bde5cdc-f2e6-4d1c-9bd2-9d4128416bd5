package com.get.salecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.salecenter.vo.EventPlanThemeOfflineItemVo;
import com.get.salecenter.service.EventPlanThemeOfflineItemService;
import com.get.salecenter.dto.EventPlanThemeOfflineItemDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 *  前端接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12
 */

@Api(tags = "活动年度计划线下活动类型子项目管理")
@RestController
@RequestMapping("sale/eventPlanThemeOfflineItem")
public class EventPlanThemeOfflineItemController {
    @Resource
    private EventPlanThemeOfflineItemService eventPlanThemeOfflineItemService;

    @ApiOperation(value = "列表数据")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "销售中心/活动年度计划线下活动类型项目子项/列表数据")
    @PostMapping("datas")
    public ResponseBo<EventPlanThemeOfflineItemVo> datas(@RequestParam("fkEventPlanId") Long fkEventPlanId, @RequestParam("fkEventPlanThemeOfflineId") Long fkEventPlanThemeOfflineId){
        return new ListResponseBo(eventPlanThemeOfflineItemService.getEventPlanThemeOfflineItems(fkEventPlanId,fkEventPlanThemeOfflineId));
    }

    @ApiOperation(value = "激活", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "销售中心/活动年度计划线下活动类型项目管理/激活")
    @PostMapping("activate")
    public ResponseBo activate(@RequestBody  @Validated(EventPlanThemeOfflineItemDto.Update.class)  EventPlanThemeOfflineItemDto offlineitemVo) {
        eventPlanThemeOfflineItemService.activate(offlineitemVo);
        return SaveResponseBo.ok();
    }

    @ApiOperation(value = "拖拽", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "销售中心/活动年度计划线下活动类型项目子项/排序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestParam("fkEventPlanThemeOfflineId") Long fkEventPlanThemeOfflineId,@RequestParam("start")Integer start,@RequestParam("end")Integer end) {
        eventPlanThemeOfflineItemService.movingOrder(fkEventPlanThemeOfflineId,start,end);
        return ResponseBo.ok();
    }


}

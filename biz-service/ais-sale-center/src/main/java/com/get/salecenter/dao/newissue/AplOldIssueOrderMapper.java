package com.get.salecenter.dao.newissue;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.entity.AplOldIssueOrder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
@DS("newissuedb")
public interface AplOldIssueOrderMapper extends BaseMapper<AplOldIssueOrder> {

    int insertSelective(AplOldIssueOrder record);

    List<AplOldIssueOrder> getListByOrderIds(@Param("orderIds") List<Integer> orderIds);


}
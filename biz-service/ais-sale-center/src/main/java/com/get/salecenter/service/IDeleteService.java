package com.get.salecenter.service;


/**
 * <AUTHOR>
 * @DATE: 2020/11/26
 * @TIME: 17:02
 * @Description:
 **/
public interface IDeleteService {

    /**
     * @return java.lang.Boolean
     * @Description: 删除代理数据校验
     * @Param [agentId]
     * <AUTHOR>
     */
    Bo<PERSON>an deleteValidateAgent(Long agentId);

    /**
     * @return java.lang.Boolean
     * @Description: 删除合同数据校验
     * @Param [agentId]
     * <AUTHOR>
     */
    Boolean deleteValidateContact(Long contractId);

    /**
     * @return java.lang.Boolean
     * @Description: 删除学生数据校验
     * @Param [agentId]
     * <AUTHOR>
     */
    Boolean deleteValidateStudent(Long studentId);

    /**
     * @return java.lang.Boolean
     * @Description: 删除客户数据校验
     * @Param [clientId]
     * <AUTHOR>
     */
    Boolean deleteValidateClient(Long clientId);

    /**
     * @return java.lang.Boolean
     * @Description: 删除申请方案数据校验
     * @Param [agentId]
     * <AUTHOR>
     */
    Boolean deleteValidateOffer(Long offerId);


    /**
     * @return java.lang.Boolean
     * @Description: 删除申请方案项目数据
     * @Param [itemId]
     * <AUTHOR>
     */
    Boolean deleteRelationOfferItem(Long itemId);


    /**
     * @return java.lang.Boolean
     * @Description: 删除申请步骤
     * @Param [itemId]
     * <AUTHOR>
     */
    Boolean deleteRelationOfferItemStep(Long itemStepId);


    /**
     * @return java.lang.Boolean
     * @Description :删除酒店房型数据校验
     * @Param [hotelId]
     * <AUTHOR>
     */
    Boolean deleteValidateHotel(Long hotelId);

    /**
     * @return java.lang.Boolean
     * @Description :删除房间数据校验
     * @Param [roomId]
     * <AUTHOR>
     */
    Boolean deleteValidateHotelRoom(Long roomId);

    /**
     * @return java.lang.Boolean
     * @Description :刪除峰会报名数据校验
     * @Param [registrationId]
     * <AUTHOR>
     */
    Boolean deleteValidateRegistration(Long registrationId);

    /**
     * @return java.lang.Boolean
     * @Description :删除峰会参会人员数据校验
     * @Param [personId]
     * <AUTHOR>
     */
    Boolean deleteValidatePerson(Long personId);

    /**
     * @return java.lang.Boolean
     * @Description :删除峰会流程数据校验
     * @Param [procedure]
     * <AUTHOR>
     */
    Boolean deleteValidateProcedure(Long procedureId);

    /**
     * @return java.lang.Boolean
     * @Description :删除峰会数据校验
     * @Param [conventionId]
     * <AUTHOR>
     */
    Boolean deleteValidateConvention(Long conventionId);

    /**
     * @return java.lang.Boolean
     * @Description :删除桌台数据校验
     * @Param [tableId]
     * <AUTHOR>
     */
    Boolean deleteValidateTable(Long tableId);

    /**
     * @return java.lang.Boolean
     * @Description :删除活动数据校验
     * @Param [eventId]
     * <AUTHOR>
     */
    Boolean deleteValidateEvent(Long eventId);

    /**
     * @return java.lang.Boolean
     * @Description :删除活动类型校验
     * @Param [eventTypeId]
     * <AUTHOR>
     */
    Boolean deleteValidateEventType(Long eventTypeId);

    /**
     * @return java.lang.Boolean
     * @Description :删除bd配置校验
     * @Param [staffId]
     * <AUTHOR>
     */
    Boolean deleteValidateStaffBdCode(Long staffId);

    /**
     * @return java.lang.Boolean
     * @Description :删除峰会赞助类型校验
     * @Param [sponsorFeeId]
     * <AUTHOR>
     */
    Boolean deleteValidateSponsorFee(Long sponsorFeeId);

    /**
     * 删除代理账户校验
     *
     * @Date 15:30 2021/6/28
     * <AUTHOR>
     */
    Boolean deleteValidateAgentAccount(Long agentAccountId);

    /**
     * 删除项目成员角色校验
     *
     * @Date 11:25 2021/7/29
     * <AUTHOR>
     */
    void deleteValidateRole(Long id);
}

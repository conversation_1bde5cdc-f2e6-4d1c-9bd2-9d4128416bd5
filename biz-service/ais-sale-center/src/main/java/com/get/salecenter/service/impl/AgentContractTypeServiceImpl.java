package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.dao.sale.AgentContractTypeMapper;
import com.get.salecenter.vo.AgentContractTypeVo;
import com.get.salecenter.entity.AgentContractType;
import com.get.salecenter.service.IAgentContractTypeService;
import com.get.salecenter.dto.AgentContractTypeDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/9/14
 * @TIME: 14:53
 * @Description:
 **/
@Service
public class AgentContractTypeServiceImpl implements IAgentContractTypeService {
    @Resource
    private AgentContractTypeMapper typeMapper;
    @Resource
    private UtilService utilService;


    @Override
    public List<AgentContractTypeVo> getAllAgentContractTypes() {
//        Example example = new Example(AgentContractType.class);
//        List<AgentContractType> agentContractTypes = typeMapper.selectByExample(example);
        List<AgentContractType> agentContractTypes = typeMapper.selectList(Wrappers.<AgentContractType>lambdaQuery().orderByDesc(AgentContractType::getViewOrder));
        return agentContractTypes.stream().map(agentContractType ->
                BeanCopyUtils.objClone(agentContractType, AgentContractTypeVo::new)).collect(Collectors.toList());
    }

    //    @Transactional(propagation = Propagation.SUPPORTS, readOnly = true, rollbackFor = YException.class)
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addAgentContractType(List<AgentContractTypeDto> contractTypeDtos) {
        if (GeneralTool.isEmpty(contractTypeDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        for (AgentContractTypeDto contractTypeDto : contractTypeDtos) {
            if (GeneralTool.isEmpty(contractTypeDto.getId())) {
                AgentContractType agentContractType = BeanCopyUtils.objClone(contractTypeDto, AgentContractType::new);
                utilService.updateUserInfoToEntity(agentContractType);
                Integer maxViewOrder = typeMapper.getMaxViewOrder();
                agentContractType.setViewOrder(maxViewOrder);
                typeMapper.insertSelective(agentContractType);
            } else {
                AgentContractType contractType = BeanCopyUtils.objClone(contractTypeDto, AgentContractType::new);
                typeMapper.updateById(contractType);
            }
        }
    }

    @Override
    public List<AgentContractTypeVo> getAgentContractTypeDtos(AgentContractTypeDto contractTypeVo, Page page) {
//        Example example = new Example(AgentContractType.class);
//        Example.Criteria criteria = example.createCriteria();
//        if (GeneralTool.isNotEmpty(contractTypeVo)) {
//            if (GeneralTool.isNotEmpty(contractTypeVo.getTypeName())) {
//                criteria.andLike("typeName", "%" + contractTypeVo.getTypeName() + "%");
//            }
//        }
//        example.orderBy("viewOrder").desc();
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        List<AgentContractType> contractTypeList = typeMapper.selectByExample(example);
//        page.restPage(contractTypeList);
        LambdaQueryWrapper<AgentContractType> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(contractTypeVo)) {
            if (GeneralTool.isNotEmpty(contractTypeVo.getTypeName())) {
                lambdaQueryWrapper.like(AgentContractType::getTypeName, contractTypeVo.getTypeName());
            }
        }
        lambdaQueryWrapper.orderByDesc(AgentContractType::getViewOrder);
        IPage<AgentContractType> iPage = typeMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
        List<AgentContractType> contractTypeList = iPage.getRecords();
        page.setAll((int) iPage.getTotal());
        return contractTypeList.stream().map(contractType -> BeanCopyUtils.objClone(contractType, AgentContractTypeVo::new)).collect(Collectors.toList());
    }

    @Override
    public AgentContractTypeVo updateAgentContractType(AgentContractTypeDto agentEventTypeVo) {
        if (GeneralTool.isEmpty(agentEventTypeVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        AgentContractType contractType = BeanCopyUtils.objClone(agentEventTypeVo, AgentContractType::new);
        typeMapper.updateById(contractType);
        return findAgentContractTypeById(contractType.getId());
    }

    @Override
    public void deleteAgentContractType(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        typeMapper.deleteById(id);
    }

    @Override
    public AgentContractTypeVo findAgentContractTypeById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        AgentContractType agentContractType = typeMapper.selectById(id);
        return BeanCopyUtils.objClone(agentContractType, AgentContractTypeVo::new);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void movingOrder(List<AgentContractTypeDto> contractTypeDtos) {
        if (GeneralTool.isEmpty(contractTypeDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        AgentContractType agentContractType1 = BeanCopyUtils.objClone(contractTypeDtos.get(0), AgentContractType::new);
        AgentContractType agentContractType2 = BeanCopyUtils.objClone(contractTypeDtos.get(1), AgentContractType::new);
        //交换排序id
        Integer viewOrder = agentContractType1.getViewOrder();
        agentContractType1.setViewOrder(agentContractType2.getViewOrder());
        agentContractType2.setViewOrder(viewOrder);

        utilService.updateUserInfoToEntity(agentContractType1);
        utilService.updateUserInfoToEntity(agentContractType2);

        typeMapper.updateById(agentContractType1);
        typeMapper.updateById(agentContractType2);

    }
}

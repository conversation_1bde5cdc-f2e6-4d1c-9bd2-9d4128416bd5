<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.StudentOfferItemDeferEntranceTimeMapper">
    <insert id="insert" parameterType="com.get.salecenter.entity.StudentOfferItemDeferEntranceTime">
        insert into m_student_offer_item_defer_entrance_time
        (fk_student_offer_item_id,defer_entrance_time,gmt_create,gmt_create_user,gmt_modified,gmt_modified_user)
        values (#{fkStudentOfferItemId,jdbcType=BIGINT},#{deferEntranceTime,jdbcType=DATE},
               #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP},
                #{gmtModifiedUser,jdbcType=VARCHAR}
                )
    </insert>

    <select id="getStudentsFailureState" resultType="com.get.salecenter.vo.StudentOfferItemDeferEntranceTimeVo">
        SELECT COUNT(DISTINCT soi.id) AS itemNum
        FROM  m_student_offer_item AS soi
        INNER JOIN m_student_offer AS so ON so.id = soi.fk_student_offer_id AND so.status = 1
        INNER JOIN m_student AS s ON s.id = so.fk_student_id
        WHERE soi.status = 1 AND soi.is_defer_entrance = 1
        AND soi.fk_area_country_id  IN
        <foreach collection="countryIds" item="countryId" open="(" separator="," close=")">
            #{countryId}
        </foreach>
        <if test="companyId != null">
            AND s.fk_company_id = #{companyId}
        </if>
        <if test="staffId != null">
            AND (so.fk_staff_id = #{staffId}
            OR so.fk_staff_id IS NULL)
        </if>
        <if test="areaCountryIds != null and areaCountryIds.size()>0">
            AND so.fk_area_country_id IN
            <foreach collection="areaCountryIds" item="areaCountryId" open="(" separator="," close=")">
                #{areaCountryId}
            </foreach>
        </if>
        AND (
        1=1
        <if test="studentIds != null">
            AND s.id IN
            <foreach collection="studentIds" item="studentId" index="index" open="(" separator=","
                     close=")">
                #{studentId}
            </foreach>
            OR s.id IS NULL
        </if>
        <if test="userNames != null and userNames.size() > 0">
            OR s.gmt_create_user IN
            <foreach collection="userNames" item="userName" open="(" separator="," close=")">
                #{userName}
            </foreach>
        </if>
        )
        <if test="beginTime != null">
            AND DATE_FORMAT( soi.gmt_create, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{beginTime}, '%Y-%m-%d' )
        </if>
        <if test="endTime != null">
            AND DATE_FORMAT( soi.gmt_create, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{endTime}, '%Y-%m-%d' )
        </if>
    </select>

    <select id="getDeferEntranceByItemId" resultType="java.util.Date">
        SELECT defer_entrance_time FROM m_student_offer_item_defer_entrance_time WHERE fk_student_offer_item_id=#{fkStudentOfferItemId}
    </select>
    <select id="getLastDeferEntranceByItemId" resultType="java.util.Date">
        SELECT
            defer_entrance_time
        FROM
            m_student_offer_item_defer_entrance_time
        WHERE
            fk_student_offer_item_id = #{fkStudentOfferItemId}
        ORDER BY
            defer_entrance_time DESC
        LIMIT 1
    </select>
    <select id="getLastDeferEntranceByItemIds" resultType="com.get.salecenter.vo.SelItem">
        SELECT
            fk_student_offer_item_id as key_id,
            defer_entrance_time as val
        FROM
            m_student_offer_item_defer_entrance_time
            <if test="fkStudentOfferItemIds!=null and fkStudentOfferItemIds.size>0">
                WHERE
                fk_student_offer_item_id in
                <foreach collection="fkStudentOfferItemIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        ORDER BY
            defer_entrance_time ASC
    </select>
    <select id="getAllDeferEntranceByItemIds"
            resultType="com.get.salecenter.entity.StudentOfferItemDeferEntranceTime">
        SELECT
        fk_student_offer_item_id,
        defer_entrance_time
        FROM
        m_student_offer_item_defer_entrance_time
        <if test="fkStudentOfferItemIds!=null and fkStudentOfferItemIds.size>0">
            WHERE
            fk_student_offer_item_id in
            <foreach collection="fkStudentOfferItemIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        ORDER BY
        defer_entrance_time ASC
    </select>
</mapper>
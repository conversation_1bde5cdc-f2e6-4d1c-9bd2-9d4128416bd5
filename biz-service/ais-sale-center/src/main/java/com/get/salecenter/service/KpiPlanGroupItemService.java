package com.get.salecenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.salecenter.vo.KpiPlanGroupItemVo;
import com.get.salecenter.entity.KpiPlanGroupItem;
import com.get.salecenter.dto.KpiPlanGroupItemDto;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
public interface KpiPlanGroupItemService extends IService<KpiPlanGroupItem> {
    /**
     * 新增kpi方案组别
     * <AUTHOR>
     * @DateTime 2024/4/18 14:45
     */
    Long addKpiPlanGroupItem(KpiPlanGroupItemDto kpiPlanGroupItemDto);

    /**
     * 详情
     * <AUTHOR>
     * @DateTime 2024/4/19 16:52
     */
    KpiPlanGroupItemVo findKpiPlanGroupItemById(Long id);

    /**
     * 删除kpi方案组别
     * <AUTHOR>
     * @DateTime 2024/4/18 14:50
     */
    void delete(Long id);

    /**
     * 修改kpi方案组别
     * <AUTHOR>
     * @DateTime 2024/4/18 14:52
     */
    void updateKpiPlanGroupItem(KpiPlanGroupItemDto kpiPlanGroupItemDto);


    /**
     * 拖拽
     * <AUTHOR>
     * @DateTime 2024/4/18 14:53
     */
    void movingOrder(Long fkKpiPlanGroupId,Integer start,Integer end);

}

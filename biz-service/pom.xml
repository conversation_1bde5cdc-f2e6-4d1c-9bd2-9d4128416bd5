<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>hti-java-ais</artifactId>
        <groupId>com.get</groupId>
        <version>1.0.RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>biz-service</artifactId>
    <name>${project.artifactId}</name>
    <version>1.0.RELEASE</version>
    <packaging>pom</packaging>
    <description>业务微服务集合</description>

    <modules>
<!--        <module>finance-center</module>-->
<!--        <module>institution-center</module>-->
<!--        <module>sale-center</module>-->
<!--        <module>office-center</module>-->
<!--        <module>voting-center</module>-->
<!--        <module>resume-center</module>-->
<!--        <module>registration-center</module>-->
<!--        <module>report-center</module>-->
<!--        <module>reminder-center</module>-->
<!--        <module>exam-center</module>-->
<!--        <module>mps-center</module>-->

        <module>ais-finance-center</module>
        <module>ais-middle-center</module>
        <module>ais-institution-center</module>
        <module>ais-sale-center</module>
        <module>ais-office-center</module>
        <module>ais-voting-center</module>
        <module>ais-resume-center</module>
        <module>ais-registration-center</module>
        <module>ais-report-center</module>
        <module>ais-reminder-center</module>
        <module>ais-exam-center</module>
        <module>ais-mps-center</module>
        <module>ais-partner-center</module>
        <module>ais-mail-center</module>
        <module>ais-platform-center</module>
        <module>ais-pmp-center</module>
        <module>ais-mail-fetch-rocketmq</module>
        <module>ais-rocketmq-center</module>
        <module>ais-insurance-center</module>

    </modules>

    <dependencies>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>common</artifactId>
            <version>${get.project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>core-metrics</artifactId>
            <version>${get.project.version}</version>
        </dependency>
    </dependencies>
</project>
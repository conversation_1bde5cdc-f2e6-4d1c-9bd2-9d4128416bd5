package com.get.aismail.service;

import com.get.aismail.entity.MFileMail;
import com.get.aismail.entity.MMailSignature;
import com.get.aismail.vo.AddNewSignature;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface IMailSignatureService {
    void addNewSignature(AddNewSignature addNewSignature) throws Exception;

    MFileMail uploadFileForSignature(MultipartFile file) throws Exception;

    void deleteSignature(MMailSignature mSignature) throws Exception;

    void updateSignature(List<MMailSignature> mSignatures) throws Exception;

    List<MMailSignature> selectAllSignatures() throws Exception;
}

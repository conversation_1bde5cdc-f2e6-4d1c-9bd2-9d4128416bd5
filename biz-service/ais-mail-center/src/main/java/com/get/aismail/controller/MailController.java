package com.get.aismail.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.api.R;
import com.get.aismail.dto.MailDto;
import com.get.aismail.entity.MFileMail;
import com.get.aismail.service.IMailService;
import com.get.aismail.vo.*;
import com.get.common.result.ResponseBo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

@Api(tags = "邮件相关操作")
@RestController
@RequestMapping("/mail")
public class MailController {
    @Resource
    private IMailService mailService;

    @ApiOperation(value = "获取所有邮件")
    @PostMapping("/getAllMail")
    public ResponseBo<IPage<MailDto>> getAllMail(@RequestBody SearchMailVo searchMailVo) throws Exception {
        return new ResponseBo<>(mailService.getAllMail(searchMailVo));
    }

    @ApiOperation(value = "统计邮件未读数量")
    @PostMapping("/countNotReadNum")
    public ResponseBo<CountNotReadNumVo> countNotReadNum(@RequestBody SearchMailVo searchMailVo) throws Exception {
        return new ResponseBo<>(mailService.countNotReadNum(searchMailVo));
    }

    @ApiOperation(value = "更改邮件状态")
    @PostMapping("/changeMailStatus")
    public ResponseBo<Long> changeMailStatus(@RequestBody ChangeMailStatusVo changeMailStatusVo) throws Exception {
        return new ResponseBo<>(mailService.changeMailStatus(changeMailStatusVo));
    }

    @ApiOperation(value = "下载附件")
    @PostMapping("/downloadAttached")
    public ResponseEntity<org.springframework.core.io.Resource> downloadAttached(HttpServletResponse response, @RequestBody DownloadAttachedVo downloadAttachedVo) throws Exception {
        // 设置文件路径
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        return mailService.downloadAttached(response, downloadAttachedVo);
    }

    @ApiOperation(value = "上传附件")
    @PostMapping("/uploadAttached")
    public ResponseBo<List<String>> uploadAttached(@RequestParam("files") MultipartFile[] files) throws Exception {
        return new ResponseBo<>(mailService.uploadFile(files));
    }

    @ApiOperation(value = "上传附件用于解析")
    @PostMapping("/uploadFileToParse")
    public ResponseBo<MFileMail> uploadFileToParse(@RequestParam("file") MultipartFile file, @RequestParam("isBody") boolean isBody) throws Exception {
        return new ResponseBo<>(mailService.uploadFileToParse(file, isBody));
    }

    @ApiOperation(value = "发送邮件")
    @PostMapping("/sendMail")
    public ResponseBo<String> sendMail(@RequestBody SendMailVo sendMailVo) throws Exception {
        mailService.sendMail(sendMailVo);
        return new ResponseBo<>("send successful");
    }

    @ApiOperation(value = "手动获取所有邮件")
    @PostMapping("/getAllMailManually")
    public ResponseBo<IPage<MailDto>> getAllMailManually(@RequestBody SearchMailVo searchMailVo) throws Exception {
        return new ResponseBo<>(mailService.getAllMailManually(searchMailVo));
    }

    @ApiOperation(value = "保存邮件到草稿")
    @PostMapping("/saveMail")
    public ResponseBo<String> saveMail(@RequestBody SendMailVo sendMailVo) throws Exception {
        mailService.saveMail(sendMailVo);
        return new ResponseBo<>("send successful");
    }
}

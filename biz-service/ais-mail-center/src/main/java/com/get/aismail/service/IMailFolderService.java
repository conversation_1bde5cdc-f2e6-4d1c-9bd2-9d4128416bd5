package com.get.aismail.service;

import com.get.aismail.dto.AddNewFolder;
import com.get.aismail.entity.MMailFolder;
import com.get.aismail.vo.DeleteFolder;

import java.util.List;

public interface IMailFolderService {
    void addNewFolder(AddNewFolder addNewFolder);
    List<MMailFolder> selectAllFolder(String emailAccount);
    void deleteFolder(DeleteFolder deleteFolder);
    void updateFolder(List<MMailFolder> mailFolders);
}

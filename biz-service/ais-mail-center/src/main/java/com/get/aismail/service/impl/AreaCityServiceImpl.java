package com.get.aismail.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.aismail.dao.AreaCityMapper;
import com.get.aismail.dto.AreaCityDto;
import com.get.aismail.entity.AreaCity;
import com.get.aismail.service.IAreaCityService;
import com.get.common.utils.BeanCopyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class AreaCityServiceImpl implements IAreaCityService {

    @Autowired
    private AreaCityMapper areaCityMapper;

    @Override
    public List<AreaCityDto> getByFkAreaStateId(Long id) {
        LambdaQueryWrapper<AreaCity> wrapper = new LambdaQueryWrapper();
        wrapper.eq(AreaCity::getFkAreaStateId, id);
        //获取分页数据
        List<AreaCity> areaCitys = areaCityMapper.selectList(wrapper);
        List<AreaCityDto> areaCityDtoList = new ArrayList<>();
        for (AreaCity areaCity : areaCitys) {
            AreaCityDto areaCityDto = BeanCopyUtils.objClone(areaCity, AreaCityDto::new);
            areaCityDto.setFullName(areaCityMapper.getCityFullNameById(areaCity.getId()));
            areaCityDtoList.add(areaCityDto);
        }
        return areaCityDtoList;
    }
}

package com.get.aismail.controller;

import com.get.aismail.dto.AddNewFolder;
import com.get.aismail.entity.MMailFolder;
import com.get.aismail.service.impl.MailFolderServiceImpl;
import com.get.aismail.vo.DeleteFolder;
import com.get.common.result.ResponseBo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "邮件文件夹相关操作")
@RestController
@RequestMapping("/mail")
public class MailFolderController {
    @Resource
    private MailFolderServiceImpl mailFolderService;

    @ApiOperation(value = "添加邮箱文件夹")
    @PostMapping("/addNewMailFolder")
    public ResponseBo addNewMailFolder(@RequestBody AddNewFolder addNewFolder) throws Exception {
        mailFolderService.addNewFolder(addNewFolder);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "删除邮件文件夹")
    @PostMapping("/deleteFolder")
    public ResponseBo deleteFolder(@RequestBody DeleteFolder deleteFolder) throws Exception {
        mailFolderService.deleteFolder(deleteFolder);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "修改邮件文件夹")
    @PostMapping("/updateFolder")
    public ResponseBo updateFolder(@RequestBody List<MMailFolder> mailFolders) throws Exception {
        mailFolderService.updateFolder(mailFolders);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "获取所有邮箱文件夹")
    @PostMapping("/selectAllFolder")
    public ResponseBo<List<MMailFolder>> selectAllFolder(@RequestParam String emailAccount) throws Exception {
        return new ResponseBo<>(mailFolderService.selectAllFolder(emailAccount));
    }
}

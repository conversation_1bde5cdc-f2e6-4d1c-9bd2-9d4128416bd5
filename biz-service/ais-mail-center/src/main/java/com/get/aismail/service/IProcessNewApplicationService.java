package com.get.aismail.service;

import com.get.aismail.dto.*;
import com.get.aismail.vo.AddNewStudentVo;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.jsoup.select.Elements;

public interface IProcessNewApplicationService {
    NewApplicationInfoDto getStudentInfo(XWPFTable table) throws Exception;

    ContactInformationDto getContactInfo(XWPFTable table) throws Exception;

    HighestDegreeInfoDto getHighestDegreeInfo(XWPFTable table) throws Exception;

    DegreeInfoRemarkDto getDegreeInfoRemark(XWPFTable table) throws Exception;

    GradesInfoDto getGradesInfo(XWPFTable table) throws Exception;

    ApplySchoolInfoDto getApplySchoolInfo(XWPFTable table) throws Exception;

    AddNewStudentVo addNewStudentIssue(Elements table) throws Exception;
}

package com.get.aismail;

import com.get.common.constant.AppCenterConstant;
import com.get.core.cloud.feign.EnableGetFeign;
import com.get.core.start.GetApplication;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.cloud.client.SpringCloudApplication;

@EnableGetFeign
@SpringCloudApplication
@MapperScan("com.get.aismail.dao")
public class AisMailApplication {
    public static void main(String[] args) {
        GetApplication.run(AppCenterConstant.APPLICATION_AIS_MAIL, AisMailApplication.class, args);
    }
}
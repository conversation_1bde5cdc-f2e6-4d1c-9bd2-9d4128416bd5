package com.get.examcenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.examcenter.vo.ExaminationAnswerVo;
import com.get.examcenter.vo.ExaminationQuestionVo;
import com.get.examcenter.entity.ExaminationAnswer;
import com.get.examcenter.entity.ExaminationQuestion;
import com.get.examcenter.entity.ExaminationQuestionAssign;
import com.get.examcenter.entity.UserExaminationQuestionScore;
import com.get.examcenter.mapper.appexam.UserExaminationQuestionScoreMapper;
import com.get.examcenter.mapper.exam.ExaminationAnswerMapper;
import com.get.examcenter.mapper.exam.ExaminationPaperMapper;
import com.get.examcenter.mapper.exam.ExaminationQuestionAssignMapper;
import com.get.examcenter.mapper.exam.ExaminationQuestionMapper;
import com.get.examcenter.service.IExaminationQuestionService;
import com.get.examcenter.service.IQuestionTypeService;
import com.get.examcenter.utils.MyStringUtils;
import com.get.examcenter.dto.ExaminationAnswerDto;
import com.get.examcenter.dto.ExaminationQuestionAssignOrderDto;
import com.get.examcenter.dto.ExaminationQuestionDto;
import com.get.examcenter.dto.query.ExaminationQuestionQueryDto;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2021/8/23 16:11
 */
@Service
public class ExaminationQuestionServiceImpl implements IExaminationQuestionService {
    @Resource
    private ExaminationQuestionMapper examinationQuestionMapper;
    @Resource
    private ExaminationAnswerMapper examinationAnswerMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private ExaminationQuestionAssignMapper examinationQuestionAssignMapper;
    @Resource
    private UserExaminationQuestionScoreMapper userExaminationQuestionScoreMapper;
    @Resource
    private IQuestionTypeService questionTypeService;
    @Resource
    private ExaminationPaperMapper examinationPaperMapper;
    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Override
    public List<ExaminationQuestionVo> getExaminationQuestionData(ExaminationQuestionQueryDto data, SearchBean<ExaminationQuestionQueryDto> page) {
//        Example example = new Example(ExaminationQuestion.class);
//        Example.Criteria criteria = example.createCriteria();
//        LambdaQueryWrapper<ExaminationQuestion> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        QueryWrapper<ExaminationQuestion> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("1", 1);
        if (GeneralTool.isNotEmpty(data.getFkQuestionTypeId())) {
//            criteria.andEqualTo("fkQuestionTypeId", data.getFkQuestionTypeId());
            queryWrapper.lambda().eq(ExaminationQuestion::getFkQuestionTypeId, data.getFkQuestionTypeId());
        }
        if (GeneralTool.isNotEmpty(data.getQuestion())) {
//            criteria.andLike("question", "%" + data.getQuestion() + "%");
            queryWrapper.lambda().like(ExaminationQuestion::getQuestion, data.getQuestion());
        }
        if (GeneralTool.isNotEmpty(data.getIsRetest())) {
//            criteria.andEqualTo("isRetest", data.getIsRetest());
            queryWrapper.lambda().eq(ExaminationQuestion::getIsRetest, data.getIsRetest());
        }
        if (GeneralTool.isNotEmpty(data.getFkCompanyId())) {
            queryWrapper.lambda().eq(ExaminationQuestion::getFkCompanyId, data.getFkCompanyId());
        }
        if (GeneralTool.isNotEmpty(data.getFkExaminationPaperId())) {
            //根据考场Id进行查询过滤
//            Example examplePaper = new Example(ExaminationQuestionAssign.class);
//            examplePaper.createCriteria().andEqualTo("fkExaminationPaperId",data.getFkExaminationPaperId());
            LambdaQueryWrapper<ExaminationQuestionAssign> examinationQuestionAssignLambdaQueryWrapper = new LambdaQueryWrapper<>();
            examinationQuestionAssignLambdaQueryWrapper.eq(ExaminationQuestionAssign::getFkExaminationPaperId, data.getFkExaminationPaperId());
            List<ExaminationQuestionAssign> examinationQuestionAssigns = examinationQuestionAssignMapper.selectList(examinationQuestionAssignLambdaQueryWrapper);
            if (GeneralTool.isEmpty(examinationQuestionAssigns)) {
                //根据考场Id查询无记录
                return new ArrayList<>();
            }
            //获取该考卷对应的所有考题ids
            Set<Long> fkQuestionIds = examinationQuestionAssigns.stream().filter(examinationQuestionAssign -> examinationQuestionAssign.getTargetType() == 1).
                    map(ExaminationQuestionAssign::getTargetId).collect(Collectors.toSet());
            //获取该考卷对应的所有考题类型ids
            Set<Long> fkQuestionTypeIds = examinationQuestionAssigns.stream().filter(examinationQuestionAssign -> examinationQuestionAssign.getTargetType() == 0).
                    map(ExaminationQuestionAssign::getTargetId).collect(Collectors.toSet());
            //如果都为空，代表考卷下没有考题
            if (GeneralTool.isEmpty(fkQuestionIds) && GeneralTool.isEmpty(fkQuestionTypeIds)) {
                return new ArrayList<>();
            }
//            Example.Criteria criteria1 = example.createCriteria();
            queryWrapper.lambda().and(wrapper -> {
                if (GeneralTool.isNotEmpty(fkQuestionIds)) {
//                    criteria1.orIn("id", fkQuestionIds);
                    wrapper.in(ExaminationQuestion::getId, fkQuestionIds);
                }
                if (GeneralTool.isNotEmpty(fkQuestionTypeIds)) {
//                    criteria1.orIn("fkQuestionTypeId", fkQuestionTypeIds);
                    wrapper.or().in(ExaminationQuestion::getFkQuestionTypeId, fkQuestionTypeIds);
                }
            });
//
//            example.and(criteria1);
        }
//        example.setOrderByClause("IFNULL(view_order,0) DESC,fk_question_type_id ASC");
//        queryWrapper.lambda().apply("order by IFNULL(view_order,0) DESC,fk_question_type_id ASC");
        queryWrapper.orderByDesc("IFNULL(view_order,0)");
        queryWrapper.orderByAsc("fk_question_type_id");
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
        IPage<ExaminationQuestion> iPage = examinationQuestionMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), queryWrapper);
        List<ExaminationQuestion> examinationQuestions = iPage.getRecords();
        page.setAll((int) iPage.getTotal());
        if (GeneralTool.isEmpty(examinationQuestions)) {
            return new ArrayList<>();
        }
        //List<ExaminationQuestionDto> examinationQuestionData = examinationQuestionMapper.getExaminationQuestionData(data);
//        page.restPage(examinationQuestions);
        List<ExaminationQuestionVo> examinationQuestionVos = new ArrayList<>();
        //考题类型ids
        Set<Long> fkQuestionTypeIds = examinationQuestions.stream().map(ExaminationQuestion::getFkQuestionTypeId).collect(Collectors.toSet());
        Map<Long, String> namesByQuestionTypeIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkQuestionTypeIds)) {
            namesByQuestionTypeIds = questionTypeService.getNamesByQuestionTypeIds(fkQuestionTypeIds);
        }

        //获取所有的公司ids
        Set<Long> fkComanyIds = examinationQuestions.stream().map(ExaminationQuestion::getFkCompanyId).collect(Collectors.toSet());
        //根据公司ids获取名称
        Map<Long, String> companyNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkComanyIds)) {
            Result<Map<Long, String>> result = permissionCenterClient.getCompanyNamesByIds(fkComanyIds);
            if (result.isSuccess() && result.getData() != null) {
                companyNamesByIds = result.getData();
            }
        }

        for (ExaminationQuestion examinationQuestion : examinationQuestions) {
            ExaminationQuestionVo examinationQuestionVo = BeanCopyUtils.objClone(examinationQuestion, ExaminationQuestionVo::new);
            if (GeneralTool.isNotEmpty(examinationQuestionVo.getIsActive())) {
                examinationQuestionVo.setIsActiveName(examinationQuestionVo.getIsActive() ? "是" : "否");
            }
            if (GeneralTool.isNotEmpty(examinationQuestionVo.getIsRetest())) {
                examinationQuestionVo.setIsRetestName(examinationQuestionVo.getIsRetest() ? "是" : "否");
            }
            if (GeneralTool.isNotEmpty(examinationQuestionVo.getIsReview())) {
                examinationQuestionVo.setIsReviewName(examinationQuestionVo.getIsReview() ? "是" : "否");
            }
            if (GeneralTool.isNotEmpty(examinationQuestionVo.getQuestionType())) {
                examinationQuestionVo.setQuestionTypeListName(ProjectExtraEnum.getValueByKey(examinationQuestionVo.getQuestionType(), ProjectExtraEnum.QUESTION_TYPE_LIST_NAME));
            }
            examinationQuestionVo.setFkCompanyName(companyNamesByIds.get(examinationQuestionVo.getFkCompanyId()));
            //考题类型名称
            examinationQuestionVo.setFkQuestionTypeName(namesByQuestionTypeIds.get(examinationQuestionVo.getFkQuestionTypeId()));
            examinationQuestionVos.add(examinationQuestionVo);

        }
        return examinationQuestionVos;

    }

    @Override
    public Long saveOrUpdataExaminationQuestionData(ExaminationQuestionDto examinationQuestionDto) {
        ExaminationQuestion examinationQuestion = BeanCopyUtils.objClone(examinationQuestionDto, ExaminationQuestion::new);
        //答案
        List<ExaminationAnswer> examinationAnswers = BeanCopyUtils.copyListProperties(examinationQuestionDto.getExaminationAnswerVoList(), ExaminationAnswer::new);
        if (GeneralTool.isEmpty(examinationQuestion.getId())) {

            utilService.updateUserInfoToEntity(examinationQuestion);
            examinationQuestionMapper.insertSelective(examinationQuestion);
            //自动生成编号
            examinationQuestion.setNum(MyStringUtils.getQuestionNum(examinationQuestion.getId()));
            examinationQuestionMapper.updateByPrimaryKey(examinationQuestion);
        } else {
            utilService.updateUserInfoToEntity(examinationQuestion);
            examinationQuestionMapper.updateByPrimaryKey(examinationQuestion);
            //获取原答案
            Set<Long> fkExaminationQuestionIds = new HashSet<>();
            fkExaminationQuestionIds.add(examinationQuestion.getId());
            Map<Long, List<ExaminationAnswerVo>> beforeAnswerList = getExaminationAnswerByExaminationQuestionIdsNews(fkExaminationQuestionIds);
            //如果原答案不在新答案，则代表是删除
            List<ExaminationAnswerVo> beforeAnswers = beforeAnswerList.get(examinationQuestion.getId());
            //现答案ids
            Set<Long> answerIds = examinationAnswers.stream().filter(examinationAnswer -> GeneralTool.isNotEmpty(examinationAnswer.getId())).
                    map(ExaminationAnswer::getId).collect(Collectors.toSet());
            for (ExaminationAnswerVo beforeAnswer : beforeAnswers) {
                //如果原答案id不在现答案id里面，则删除
                if (!answerIds.contains(beforeAnswer.getId())) {
                    //删除答案
                    examinationAnswerMapper.deleteById(beforeAnswer.getId());
                }
            }
        }
        //处理答案
        for (ExaminationAnswer answer : examinationAnswers) {
            if (GeneralTool.isNotEmpty(answer.getId())) {
                utilService.updateUserInfoToEntity(answer);
                examinationAnswerMapper.updateById(answer);
                continue;
            }
            answer.setFkExaminationQuestionId(examinationQuestion.getId());
            utilService.updateUserInfoToEntity(answer);
            examinationAnswerMapper.insertSelective(answer);
        }
        return examinationQuestion.getId();
    }


    /**
     * @Description: 根据问题ids获取答案（不返回是否正确答案标识,并且对答案进行乱序排序）
     * @Author: Jerry
     * @Date:16:53 2021/8/25
     */
    @Override
    public Map<Long, List<ExaminationAnswerVo>> getExaminationAnswerByExaminationQuestionIds(Set<Long> examinationQuestionIds) {
        Map<Long, List<ExaminationAnswerVo>> map = new HashMap<>();
        if (GeneralTool.isEmpty(examinationQuestionIds)) {
            return map;
        }
//        Example example = new Example(ExaminationAnswer.class);
//        example.createCriteria().andIn("fkExaminationQuestionId", examinationQuestionIds);
//        example.setOrderByClause("IFNULL(view_order,0) DESC,RAND()");
//        List<ExaminationAnswer> examinationAnswers = examinationAnswerMapper.selectByExample(example);
        List<ExaminationAnswer> examinationAnswers = examinationAnswerMapper.selectList(Wrappers.<ExaminationAnswer>query()
                .orderByDesc("IFNULL(view_order,0)")
                .orderByAsc("RAND()").lambda()
                .in(ExaminationAnswer::getFkExaminationQuestionId, examinationQuestionIds));
        if (GeneralTool.isEmpty(examinationAnswers)) {
            return map;
        }
        for (ExaminationAnswer examinationAnswer : examinationAnswers) {
            ExaminationAnswerVo examinationAnswerVo = BeanCopyUtils.objClone(examinationAnswer, ExaminationAnswerVo::new);
            examinationAnswerVo.setIsRightAnswer(null);
            //如果集合中包含这个问题id，则往原集合上面添加记录
            if (map.containsKey(examinationAnswer.getFkExaminationQuestionId())) {
                List<ExaminationAnswerVo> beforeExaminationAnswer = map.get(examinationAnswer.getFkExaminationQuestionId());
                beforeExaminationAnswer.add(examinationAnswerVo);
                continue;
            }
            //添加新纪录
            List<ExaminationAnswerVo> newExaminationAnswer = new ArrayList<>();
            newExaminationAnswer.add(examinationAnswerVo);
            map.put(examinationAnswer.getFkExaminationQuestionId(), newExaminationAnswer);
        }
        return map;
    }


    /**
     * @Description: 根据问题ids获取答案（返回是否正确答案标识，并且不对答案进行乱序排序）
     * @Author: Jerry
     * @Date:11:38 2021/9/13
     */
    @Override
    public Map<Long, List<ExaminationAnswerVo>> getExaminationAnswerByExaminationQuestionIdsNews(Set<Long> examinationQuestionIds) {
        Map<Long, List<ExaminationAnswerVo>> map = new HashMap<>();
        if (GeneralTool.isEmpty(examinationQuestionIds)) {
            return map;
        }
//        Example example = new Example(ExaminationAnswer.class);
//        example.createCriteria().andIn("fkExaminationQuestionId", examinationQuestionIds);
//        example.setOrderByClause("IFNULL(view_order,0) DESC");
//        List<ExaminationAnswer> examinationAnswers = examinationAnswerMapper.selectByExample(example);
        List<ExaminationAnswer> examinationAnswers = examinationAnswerMapper.selectList(Wrappers.<ExaminationAnswer>query()
                .orderByDesc("IFNULL(view_order,0)").lambda()
                .in(ExaminationAnswer::getFkExaminationQuestionId, examinationQuestionIds));
        if (GeneralTool.isEmpty(examinationAnswers)) {
            return map;
        }
        for (ExaminationAnswer examinationAnswer : examinationAnswers) {
            ExaminationAnswerVo examinationAnswerVo = BeanCopyUtils.objClone(examinationAnswer, ExaminationAnswerVo::new);
            //如果集合中包含这个问题id，则往原集合上面添加记录
            if (map.containsKey(examinationAnswer.getFkExaminationQuestionId())) {
                List<ExaminationAnswerVo> beforeExaminationAnswer = map.get(examinationAnswer.getFkExaminationQuestionId());
                beforeExaminationAnswer.add(examinationAnswerVo);
                continue;
            }
            //添加新纪录
            List<ExaminationAnswerVo> newExaminationAnswer = new ArrayList<>();
            newExaminationAnswer.add(examinationAnswerVo);
            map.put(examinationAnswer.getFkExaminationQuestionId(), newExaminationAnswer);
        }
        return map;
    }

    /**
     * @Description: 根据问题id获取答案
     * @Author: Jerry
     * @Date:10:09 2021/8/27
     */
    @Override
    public List<ExaminationAnswerVo> getExaminationAnswerByExaminationQuestionId(Long examinationQuestionId) {
        List<ExaminationAnswerVo> examinationAnswerVos = new ArrayList<>();
        if (GeneralTool.isEmpty(examinationQuestionId)) {
            return examinationAnswerVos;
        }
//        Example example = new Example(ExaminationAnswer.class);
//        example.createCriteria().andEqualTo("fkExaminationQuestionId",examinationQuestionId);
//        example.setOrderByClause("IFNULL(view_order,0) DESC");
//        List<ExaminationAnswer> examinationAnswers = examinationAnswerMapper.selectByExample(example);

        List<ExaminationAnswer> examinationAnswers = examinationAnswerMapper.selectList(Wrappers.<ExaminationAnswer>query()
                .orderByDesc("IFNULL(view_order,0)")
                .lambda()
                .eq(ExaminationAnswer::getFkExaminationQuestionId, examinationQuestionId));

        if (GeneralTool.isEmpty(examinationAnswers)) {
            return examinationAnswerVos;
        }
        for (ExaminationAnswer examinationAnswer : examinationAnswers) {
            ExaminationAnswerVo examinationAnswerVo = BeanCopyUtils.objClone(examinationAnswer, ExaminationAnswerVo::new);
            if (GeneralTool.isNotEmpty(examinationAnswerVo.getIsRightAnswer())) {
                examinationAnswerVo.setIsRightAnswerName(examinationAnswerVo.getIsRightAnswer() ? "是" : "否");
            }
            examinationAnswerVos.add(examinationAnswerVo);
        }
        return examinationAnswerVos;
    }

    /**
     * @Description: 详情
     * @Author: Jerry
     * @Date:10:01 2021/8/27
     */
    @Override
    public ExaminationQuestionVo detail(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ExaminationQuestion examinationQuestion = examinationQuestionMapper.selectById(id);
        if (GeneralTool.isEmpty(examinationQuestion)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        ExaminationQuestionVo examinationQuestionVo = BeanCopyUtils.objClone(examinationQuestion, ExaminationQuestionVo::new);
        Map<Long, String> namesByQuestionTypeIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(examinationQuestionVo.getFkQuestionTypeId())) {
            //考题类型ids
            Set<Long> fkQuestionTypeIds = new HashSet<>();
            fkQuestionTypeIds.add(examinationQuestionVo.getFkQuestionTypeId());
            namesByQuestionTypeIds = questionTypeService.getNamesByQuestionTypeIds(fkQuestionTypeIds);
        }
        if (GeneralTool.isNotEmpty(examinationQuestionVo.getIsActive())) {
            examinationQuestionVo.setIsActiveName(examinationQuestionVo.getIsActive() ? "是" : "否");
        }
        if (GeneralTool.isNotEmpty(examinationQuestionVo.getIsRetest())) {
            examinationQuestionVo.setIsRetestName(examinationQuestionVo.getIsRetest() ? "是" : "否");
        }
        if (GeneralTool.isNotEmpty(examinationQuestionVo.getIsReview())) {
            examinationQuestionVo.setIsReviewName(examinationQuestionVo.getIsReview() ? "是" : "否");
        }
        if (GeneralTool.isNotEmpty(examinationQuestionVo.getQuestionType())) {
            examinationQuestionVo.setQuestionTypeListName(ProjectExtraEnum.getValueByKey(examinationQuestionVo.getQuestionType(), ProjectExtraEnum.QUESTION_TYPE_LIST_NAME));
        }
        //答案
        examinationQuestionVo.setExaminationAnswers(getExaminationAnswerByExaminationQuestionId(examinationQuestion.getId()));
        //考题类型名称
        examinationQuestionVo.setFkQuestionTypeName(namesByQuestionTypeIds.get(examinationQuestionVo.getFkQuestionTypeId()));
        //考题是否有答题记录
        boolean questionScore = getQuestionScoreByQuestionId(id);
        examinationQuestionVo.setIsHaveQuestionScore(questionScore);
        Long fkComanyId = examinationQuestionVo.getFkCompanyId();
        Result<String> result = permissionCenterClient.getCompanyNameById(fkComanyId);
        if (result.isSuccess() && result.getData() != null) {
            examinationQuestionVo.setFkCompanyName(result.getData());
        }
        return examinationQuestionVo;
    }

    /**
     * @Description: 删除
     * @Author: Jerry
     * @Date:16:48 2021/9/6
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        //校验是否有分配试题
        validateDelete(id);
        examinationQuestionMapper.deleteById(id);
        //将问题的答案也删除
//        Example example = new Example(ExaminationAnswer.class);
//        example.createCriteria().andEqualTo("fkExaminationQuestionId",id);
//        examinationAnswerMapper.deleteByExample(example);
        examinationAnswerMapper.delete(Wrappers.<ExaminationAnswer>lambdaQuery().eq(ExaminationAnswer::getFkExaminationQuestionId, id));
    }

    /**
     * @Description: 根据问题ids获取对象
     * @Author: Jerry
     * @Date:12:35 2021/8/27
     */
    @Override
    public Map<Long, ExaminationQuestion> getExaminationQuestionByQuestionIds(Set<Long> questionIds) {
        Map<Long, ExaminationQuestion> map = new HashMap<>();
        if (GeneralTool.isEmpty(questionIds)) {
            return map;
        }
//        Example example = new Example(ExaminationQuestion.class);
//        example.createCriteria().andIn("id",questionIds);
        List<ExaminationQuestion> examinationQuestions = examinationQuestionMapper.selectBatchIds(questionIds);
        if (GeneralTool.isEmpty(examinationQuestions)) {
            return map;
        }
        for (ExaminationQuestion examinationQuestion : examinationQuestions) {
            map.put(examinationQuestion.getId(), examinationQuestion);
        }
        return map;
    }

    /**
     * @Description: 根据问题ids获取名称
     * @Author: Jerry
     * @Date:10:48 2021/9/3
     */
    @Override
    public Map<Long, String> getExaminationQuestionNamesByQuestionIds(Set<Long> questionIds) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(questionIds)) {
            return map;
        }
//        Example example = new Example(ExaminationQuestion.class);
//        example.createCriteria().andIn("id",questionIds);
        List<ExaminationQuestion> examinationQuestions = examinationQuestionMapper.selectBatchIds(questionIds);
        if (GeneralTool.isEmpty(examinationQuestions)) {
            return map;
        }
        for (ExaminationQuestion examinationQuestion : examinationQuestions) {
            map.put(examinationQuestion.getId(), examinationQuestion.getQuestion());
        }
        return map;
    }

    /**
     * @Description: 根据答案ids获取名称
     * @Author: Jerry
     * @Date:12:38 2021/8/27
     */
    @Override
    public Map<Long, String> getAnswerNamesByAnswerIds(Set<Long> answerIds) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(answerIds)) {
            return map;
        }
//        Example example = new Example(ExaminationAnswer.class);
//        example.createCriteria().andIn("id",answerIds);
//        List<ExaminationAnswer> examinationAnswers = examinationAnswerMapper.selectByExample(example);
        List<ExaminationAnswer> examinationAnswers = examinationAnswerMapper.selectBatchIds(answerIds);

        if (GeneralTool.isEmpty(examinationAnswers)) {
            return map;
        }
        for (ExaminationAnswer examinationAnswer : examinationAnswers) {
            map.put(examinationAnswer.getId(), examinationAnswer.getAnswer());
        }
        return map;
    }

    /**
     * @Description: 考题下拉框
     * @Author: Jerry
     * @Date:9:57 2021/9/3
     */
    @Override
    public List<BaseSelectEntity> questionSelect() {
        List<BaseSelectEntity> baseSelectEntities = new ArrayList<>();
//        Example example = new Example(ExaminationQuestion.class);
//        example.setOrderByClause("IFNULL(view_order,0) DESC");
//        List<ExaminationQuestion> examinationQuestions = examinationQuestionMapper.selectByExample(example);
        QueryWrapper<ExaminationQuestion> questionQueryWrapper = new QueryWrapper<>();
        questionQueryWrapper.orderByDesc("IFNULL(view_order,0)");
        List<ExaminationQuestion> examinationQuestions = examinationQuestionMapper.selectList(questionQueryWrapper);

        if (GeneralTool.isEmpty(examinationQuestions)) {
            return baseSelectEntities;
        }
        for (ExaminationQuestion examinationQuestion : examinationQuestions) {
            BaseSelectEntity baseSelectEntity = BeanCopyUtils.objClone(examinationQuestion, BaseSelectEntity::new);
            baseSelectEntity.setName(examinationQuestion.getQuestion());
            baseSelectEntities.add(baseSelectEntity);
        }
        return baseSelectEntities;
    }

    @Override
    public Long updateAnswer(ExaminationAnswerDto examinationAnswerDto) {
        if (GeneralTool.isEmpty(examinationAnswerDto.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ExaminationAnswer answer = BeanCopyUtils.objClone(examinationAnswerDto, ExaminationAnswer::new);
        utilService.updateUserInfoToEntity(answer);
        examinationAnswerMapper.updateById(answer);
        return answer.getId();
    }

    @Override
    public void DeleteAnswer(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (userExaminationQuestionScoreMapper.isExist(String.valueOf(id))) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("answer_exist"));
        }
        examinationAnswerMapper.deleteById(id);
    }


    /**
     * @Description: 校验该题目有没有被分配，有的话不可以删除
     * @Author: Jerry
     * @Date:17:06 2021/9/6
     */
    private void validateDelete(Long id) {
//        Example example = new Example(ExaminationQuestionAssign.class);
//        example.createCriteria().andEqualTo("targetType",1).andEqualTo("targetId",id);
//        List<ExaminationQuestionAssign> examinationQuestionAssigns = examinationQuestionAssignMapper.selectByExample(example);
        List<ExaminationQuestionAssign> examinationQuestionAssigns = examinationQuestionAssignMapper.selectList(Wrappers.<ExaminationQuestionAssign>lambdaQuery()
                .eq(ExaminationQuestionAssign::getTargetId, 1)
                .eq(ExaminationQuestionAssign::getTargetId, id));
        if (GeneralTool.isNotEmpty(examinationQuestionAssigns)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("question_has_been_assigned"));
        }
    }


    /**
     * @Description: 考题是否有答题记录
     * @Author: Jerry
     * @Date:12:34 2021/9/18
     */
    private boolean getQuestionScoreByQuestionId(Long id) {
//        Example example = new Example(UserExaminationQuestionScore.class);
//        example.createCriteria().andEqualTo("fkExaminationQuestionId",id);
//        List<UserExaminationQuestionScore> userExaminationQuestionScores = userExaminationQuestionScoreMapper.selectByExample(example);
        List<UserExaminationQuestionScore> userExaminationQuestionScores = userExaminationQuestionScoreMapper.selectList(Wrappers.<UserExaminationQuestionScore>lambdaQuery()
                .eq(UserExaminationQuestionScore::getFkExaminationQuestionId, id));
        if (GeneralTool.isNotEmpty(userExaminationQuestionScores)) {
            return true;
        }
        return false;
    }

    @Override
    public void moveQuestiOrder(List<ExaminationQuestionAssignOrderDto> listE) {
        if (GeneralTool.isEmpty(listE)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        ExaminationQuestionAssign ro = examinationQuestionAssignMapper.selectById(listE.get(0));
        Integer oneorder = ro.getViewOrder();
        ExaminationQuestionAssign rt = examinationQuestionAssignMapper.selectById(listE.get(1));
        Integer twoorder = rt.getViewOrder();
        ro.setViewOrder(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setViewOrder(oneorder);
        utilService.updateUserInfoToEntity(rt);
        examinationQuestionAssignMapper.updateById(ro);
        examinationQuestionAssignMapper.updateById(rt);
    }
}

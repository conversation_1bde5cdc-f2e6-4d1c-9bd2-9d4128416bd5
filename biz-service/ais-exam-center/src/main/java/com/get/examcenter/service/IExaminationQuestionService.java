package com.get.examcenter.service;

import com.get.common.result.SearchBean;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.examcenter.vo.ExaminationAnswerVo;
import com.get.examcenter.vo.ExaminationQuestionVo;
import com.get.examcenter.entity.ExaminationQuestion;
import com.get.examcenter.dto.ExaminationAnswerDto;
import com.get.examcenter.dto.ExaminationQuestionAssignOrderDto;
import com.get.examcenter.dto.ExaminationQuestionDto;
import com.get.examcenter.dto.query.ExaminationQuestionQueryDto;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2021/8/23 16:10
 */
public interface IExaminationQuestionService {

    List<ExaminationQuestionVo> getExaminationQuestionData(ExaminationQuestionQueryDto data, SearchBean<ExaminationQuestionQueryDto> page);

    Long saveOrUpdataExaminationQuestionData(ExaminationQuestionDto examinationQuestionDto);

    /**
     * @Description: 根据问题ids获取答案（不返回是否正确答案标识,并且对答案进行乱序排序）
     * @Author: Jerry
     * @Date:16:53 2021/8/25
     */
    Map<Long, List<ExaminationAnswerVo>> getExaminationAnswerByExaminationQuestionIds(Set<Long> examinationQuestionIds);

    /**
     * @Description: 根据问题ids获取答案（返回是否正确答案标识，并且不对答案进行乱序排序）
     * @Author: Jerry
     * @Date:11:37 2021/9/13
     */
    Map<Long, List<ExaminationAnswerVo>> getExaminationAnswerByExaminationQuestionIdsNews(Set<Long> examinationQuestionIds);

    Long updateAnswer(ExaminationAnswerDto examinationAnswerDto);

    void DeleteAnswer(Long id);

    /**
     * @Description: 根据问题id获取答案
     * @Author: Jerry
     * @Date:10:09 2021/8/27
     */
    List<ExaminationAnswerVo> getExaminationAnswerByExaminationQuestionId(Long examinationQuestionId);

    /**
     * @Description: 详情
     * @Author: Jerry
     * @Date:10:02 2021/8/27
     */
    ExaminationQuestionVo detail(Long id);

    /**
     * @Description: 删除
     * @Author: Jerry
     * @Date:16:47 2021/9/6
     */
    void delete(Long id);

    /**
     * @Description: 根据问题ids获取对象
     * @Author: Jerry
     * @Date:12:35 2021/8/27
     */
    Map<Long, ExaminationQuestion> getExaminationQuestionByQuestionIds(Set<Long> questionIds);

    /**
     * @Description: 根据问题ids获取名称
     * @Author: Jerry
     * @Date:10:48 2021/9/3
     */
    Map<Long, String> getExaminationQuestionNamesByQuestionIds(Set<Long> questionIds);

    /**
     * @Description: 根据答案ids获取名称
     * @Author: Jerry
     * @Date:12:38 2021/8/27
     */
    Map<Long, String> getAnswerNamesByAnswerIds(Set<Long> answerIds);


    /**
     * @Description: 考题下拉框
     * @Author: Jerry
     * @Date:9:56 2021/9/3
     */
    List<BaseSelectEntity> questionSelect();

    void moveQuestiOrder(List<ExaminationQuestionAssignOrderDto> listE);
}

package com.get.examcenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.examcenter.vo.UserExaminationQuestionScoreVo;
import com.get.examcenter.vo.UserexaminationPaperScoreVo;
import com.get.examcenter.service.UserService;
import com.get.examcenter.dto.UserDto;
import com.get.examcenter.dto.UserexaminationPaperScoreDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * Created by Jerry.
 * Time: 10:48
 * Date: 2021/8/25
 * Description:考生管理控制器
 */
@Api(tags = "考生管理")
@RestController
@RequestMapping("exam/user")
public class UserController {

    @Resource
    private UserService userService;

    /**
     * @Description: 生成考试记录
     * @Author: Jerry
     * @Date:10:53 2021/8/25
     */
    /*@ApiOperation(value = "生成考试记录", notes = "optGuid为操作id")
    @OperationLogger(module = LoggerModulesConsts.EXAMCENTER, type = LoggerOptTypeConst.ADD, description = "考试中心/考生管理/生成考试记录")
    @PostMapping("/generateExaminationRecords")
    public ResponseBo<UserexaminationPaperScoreDto> generateExaminationRecords(@RequestParam("optGuid") String optGuid)  {
        return new ResponseBo<>(userService.generateExaminationRecords(optGuid));
    }*/


    /**
     * @Description: 生成答题记录
     * @Author: Jerry
     * @Date:10:53 2021/8/25
     */
    /*@ApiOperation(value = "生成答题记录", notes = "返回结果为正确答案")
    @OperationLogger(module = LoggerModulesConsts.EXAMCENTER, type = LoggerOptTypeConst.ADD, description = "考试中心/考生管理/生成答题记录")
    @PostMapping("/generateAnswerRecords")
    public ResponseBo<String> generateAnswerRecords(@RequestBody @Validated(UserExaminationQuestionScoreDto.Add.class) UserExaminationQuestionScoreVo userExaminationQuestionScoreVo)  {
        return new ResponseBo<>(userService.generateAnswerRecords(userExaminationQuestionScoreVo));
    }*/

    /**
     * @ Description :列表
     * @ Param [page]
     * @ return com.get.common.result.ResponseBo
     * @ author LEO
     */
    @ApiOperation("列表")
    @PostMapping("/getUserExaminationPaperScore")
    public ResponseBo getUserExaminationPaperScore(@RequestBody SearchBean<UserDto> page) {
        List<UserexaminationPaperScoreVo> userExaminationPaperScore = userService.getUserExaminationPaperScore(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(userExaminationPaperScore, p);
    }


    /**
     * @Description: 答题记录列表
     * @Author: Jerry
     * @Date:12:13 2021/8/27
     */
    @ApiOperation(value = "答题记录列表", notes = "optGuid为操作id")
    @OperationLogger(module = LoggerModulesConsts.EXAMCENTER, type = LoggerOptTypeConst.LIST, description = "考试中心/考生管理/答题记录列表")
    @PostMapping("/answerRecordsList")
    public ResponseBo<UserExaminationQuestionScoreVo> answerRecordsList(@RequestBody SearchBean<UserexaminationPaperScoreDto> page) {
        List<UserExaminationQuestionScoreVo> userExaminationQuestionScoreVos = userService.answerRecordsList(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(userExaminationQuestionScoreVos, p);
    }

    /**
     * @ Description :考试记录
     * @ Param [page]
     * @ return com.get.common.result.ResponseBo
     * @ author LEO
     */
    @ApiOperation("考试记录列表")
    @PostMapping("/getUserPaperData")
    public ResponseBo getUserPaperData(@RequestBody SearchBean<UserexaminationPaperScoreDto> page) {
        List<UserexaminationPaperScoreVo> userExaminationQuestionScoreDtos = userService.getUserPaperData(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(userExaminationQuestionScoreDtos, p);
    }


    /**
     * @Description: feign调用 根据员工ids（BD）获取用户ids
     * @Author: Jerry
     * @Date:14:42 2021/8/30
     */
    @ApiIgnore
    @PostMapping("/getUserIdsByStaffIds")
    public Set<Long> getUserIdsByStaffIds(@RequestBody Set<Long> fkStaffIds) {
        return userService.getUserIdsByStaffIds(fkStaffIds);
    }
}

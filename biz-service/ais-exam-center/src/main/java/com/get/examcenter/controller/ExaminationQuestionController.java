package com.get.examcenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.examcenter.vo.ExaminationQuestionVo;
import com.get.examcenter.service.IExaminationQuestionService;
import com.get.examcenter.dto.ExaminationQuestionAssignOrderDto;
import com.get.examcenter.dto.ExaminationQuestionDto;
import com.get.examcenter.dto.query.ExaminationQuestionQueryDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2021/8/23 15:47
 */
@Api(tags = "考题管理")
@RestController
@RequestMapping("exam/ExaminationQuestion")
public class ExaminationQuestionController {

    @Autowired
    private IExaminationQuestionService iExaminationQuestionService;

    @ApiOperation("列表")
    @PostMapping("getExaminationQuestionData")
    public ResponseBo getExaminationQuestionData(@RequestBody SearchBean<ExaminationQuestionQueryDto> page) {
        List<ExaminationQuestionVo> examinationQuestionData = iExaminationQuestionService.getExaminationQuestionData(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(examinationQuestionData, p);
    }


    @ApiOperation("保存与更新课题")
    @PostMapping("saveOrUpdataExaminationQuestionData")
    public ResponseBo saveOrUpdataExaminationQuestionData(@RequestBody ExaminationQuestionDto examinationQuestionDto) {

        return new ResponseBo(iExaminationQuestionService.saveOrUpdataExaminationQuestionData(examinationQuestionDto));

    }


    /**
     * @Description: 详情
     * @Author: Jerry
     * @Date:9:57 2021/8/27
     */
    @ApiOperation(value = "详情", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.EXAMCENTER, type = LoggerOptTypeConst.DETAIL, description = "考试中心/考题管理/详情")
    @GetMapping("/{id}")
    public ResponseBo<ExaminationQuestionVo> detail(@PathVariable("id") Long id) {
        return new ResponseBo(iExaminationQuestionService.detail(id));
    }


    /**
     * @Description: 删除
     * @Author: Jerry
     * @Date:16:46 2021/9/6
     */
    @ApiOperation(value = "删除", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.EXAMCENTER, type = LoggerOptTypeConst.DELETE, description = "考试中心/考题管理/删除")
    @GetMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        iExaminationQuestionService.delete(id);
        return DeleteResponseBo.ok();
    }
    /**
     * @ Description :答案修改
     * @ Param [examinationAnswerVo]
     * @ return com.get.common.result.ResponseBo
     * @ author LEO
     */
    /*@ApiOperation("答案修改")
    @PostMapping("updateAnswer")
    public ResponseBo updateAnswer(@RequestBody ExaminationAnswerVo examinationAnswerVo)  {
        Long aLong = iExaminationQuestionService.updateAnswer(examinationAnswerVo);
        return new ResponseBo(aLong);
    }

    @ApiOperation("答案删除")
    @PostMapping("DeleteAnswer")
    public ResponseBo DeleteAnswer(@RequestParam("id") Long id) {
        iExaminationQuestionService.DeleteAnswer(id);
        return ResponseBo.ok();
    }*/


    /**
     * @Description: 考题下拉框
     * @Author: Jerry
     * @Date:9:55 2021/9/3
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "考题下拉框", notes = "")
    @OperationLogger(module = LoggerModulesConsts.EXAMCENTER, type = LoggerOptTypeConst.LIST, description = "考试中心/考题管理/考题下拉框")
    @PostMapping("/questionSelect")
    public ResponseBo<BaseSelectEntity> questionSelect() {
        return new ListResponseBo<>(iExaminationQuestionService.questionSelect());
    }

    /**
     * @Description: 删除答案
     * @Param:
     * @return:
     * @Author: Walker
     * @Date: 2022/7/28
     */
    @ApiOperation(value = "删除答案")
    @OperationLogger(module = LoggerModulesConsts.EXAMCENTER, type = LoggerOptTypeConst.DELETE, description = "考试中心/考题管理/删除")
    @GetMapping("/deleteAnswerByAnswerId")
    public ResponseBo deleteAnswerByAnsweId(@PathVariable("answerId") Long answerId) {
        iExaminationQuestionService.DeleteAnswer(answerId);
        return ResponseBo.ok();
    }

    /** 
    * @Description: 考题分配的排序增加上移下移
    * @Param: 
    * @return: 
    * @Author: Walker
    * @Date: 2022/7/28
    */
    @ApiOperation(value = "考题分配的排序增加上移下移")
    @OperationLogger(module = LoggerModulesConsts.EXAMCENTER, type = LoggerOptTypeConst.EDIT, description = "考试中心/考题管理/考题分配的排序增加上移下移")
    @PostMapping("/moveQuestiOrder")
    public ResponseBo moveQuestiOrder(@RequestBody List<ExaminationQuestionAssignOrderDto> listE) {
        iExaminationQuestionService.moveQuestiOrder(listE);
        return ResponseBo.ok();
    }
}

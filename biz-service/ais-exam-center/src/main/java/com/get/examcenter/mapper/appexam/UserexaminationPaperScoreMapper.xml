<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.examcenter.mapper.appexam.UserexaminationPaperScoreMapper">
    <insert id="insert" parameterType="com.get.examcenter.entity.UserexaminationPaperScore">
    insert into r_user_examination_paper_score (id, opt_guid, fk_user_id, fk_examination_id,
      fk_examination_paper_id, score, use_time, 
      gmt_create, gmt_create_user, gmt_modified, 
      gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{optGuid,jdbcType=VARCHAR}, #{fkUserId,jdbcType=BIGINT}, #{fkExaminationId,jdbcType=BIGINT},
      #{fkExaminationPaperId,jdbcType=BIGINT}, #{score,jdbcType=INTEGER}, #{useTime,jdbcType=INTEGER}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, 
      #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
    <insert id="insertSelective" parameterType="com.get.examcenter.entity.UserexaminationPaperScore">
        insert into r_user_examination_paper_score
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="optGuid != null">
                opt_guid,
            </if>
            <if test="fkUserId != null">
                fk_user_id,
            </if>
            <if test="fkExaminationId != null">
                fk_examination_id,
            </if>
            <if test="fkExaminationPaperId != null">
                fk_examination_paper_id,
            </if>
            <if test="score != null">
                score,
            </if>
            <if test="useTime != null">
                use_time,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="optGuid != null">
                #{optGuid,jdbcType=VARCHAR},
            </if>
            <if test="fkUserId != null">
                #{fkUserId,jdbcType=BIGINT},
            </if>
            <if test="fkExaminationId != null">
                #{fkExaminationId,jdbcType=BIGINT},
            </if>
            <if test="fkExaminationPaperId != null">
                #{fkExaminationPaperId,jdbcType=BIGINT},
            </if>
            <if test="score != null">
                #{score,jdbcType=INTEGER},
            </if>
            <if test="useTime != null">
                #{useTime,jdbcType=INTEGER},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.get.examcenter.entity.UserexaminationPaperScore">
        update r_user_examination_paper_score
        <set>
            <if test="optGuid != null">
                opt_guid = #{optGuid,jdbcType=VARCHAR},
            </if>
            <if test="fkUserId != null">
                fk_user_id = #{fkUserId,jdbcType=BIGINT},
            </if>
            <if test="fkExaminationId != null">
                fk_examination_id = #{fkExaminationId,jdbcType=BIGINT},
            </if>
            <if test="fkExaminationPaperId != null">
                fk_examination_paper_id = #{fkExaminationPaperId,jdbcType=BIGINT},
            </if>
            <if test="score != null">
                score = #{score,jdbcType=INTEGER},
            </if>
            <if test="useTime != null">
                use_time = #{useTime,jdbcType=INTEGER},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.get.examcenter.entity.UserexaminationPaperScore">
    update r_user_examination_paper_score
    set opt_guid = #{optGuid,jdbcType=VARCHAR},
      fk_user_id = #{fkUserId,jdbcType=BIGINT},
      fk_examination_id = #{fkExaminationId,jdbcType=BIGINT},
      fk_examination_paper_id = #{fkExaminationPaperId,jdbcType=BIGINT},
      score = #{score,jdbcType=INTEGER},
      use_time = #{useTime,jdbcType=INTEGER},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
    <select id="viewExaminationPaperLeaderboard" resultType="com.get.examcenter.vo.UserexaminationPaperScoreVo">
        SELECT t.*, @rownum := @rownum + 1 AS rank
        FROM (SELECT @rownum := 0) r, (select
        fk_user_id AS fkUserId,IFNULL(max(score),0) AS score,min(use_time) AS useTime
        from r_user_examination_paper_score where score is not null
        <if test="fkExaminationPaperId != null and fkExaminationPaperId != ''">
            and fk_examination_paper_id = #{fkExaminationPaperId}
        </if>
        group by fk_user_id
        order by max(score) desc,min(use_time) asc) AS t
    </select>
    <select id="viewExaminationPaperLeaderboardByUserIds" resultType="com.get.examcenter.vo.UserexaminationPaperScoreVo">
        select a.* from (SELECT t1.count,t.*, @rownum := @rownum + 1 AS rank FROM (SELECT @rownum := 0) r, (
        SELECT a.fk_user_id fkUserId, MAX(a.score) score, MIN(a.use_time) useTime FROM (
        SELECT a.* FROM ais_app_exam_center.r_user_examination_paper_score a
        INNER JOIN (
        SELECT a.fk_user_id, a.fk_examination_paper_id, MAX(a.score) max_score FROM ais_app_exam_center.r_user_examination_paper_score a GROUP BY a.fk_user_id, a.fk_examination_paper_id
        ) b ON a.fk_user_id=b.fk_user_id AND a.fk_examination_paper_id=b.fk_examination_paper_id AND a.score=b.max_score
        WHERE a.score IS NOT NULL AND a.score > 0
        <if test="fkExaminationPaperId != null and fkExaminationPaperId != ''">
            and a.fk_examination_paper_id = #{fkExaminationPaperId}
        </if>
        ) a
        GROUP BY a.fk_user_id
        ORDER BY MAX(a.score) DESC, MIN(a.use_time)
        ) AS t left join (select COUNT(id)count,fk_user_id from ais_app_exam_center.r_user_examination_paper_score a
        where 1=1
        <if test="fkExaminationPaperId != null and fkExaminationPaperId != ''">
            and fk_examination_paper_id = #{fkExaminationPaperId}
        </if>
        and a.score IS NOT NULL AND a.score > 0
        group BY fk_user_id) t1 on t.fkUserId=t1.fk_user_id) a where 1=1
<!--        select a.* from (SELECT t.*, @rownum := @rownum + 1 AS rank
        FROM (SELECT @rownum := 0) r, (select
        fk_user_id AS fkUserId,IFNULL(max(score),0) AS score,min(use_time) AS useTime
        from r_user_examination_paper_score where score is not null and score > 0
        <if test="fkExaminationPaperId != null and fkExaminationPaperId != ''">
            and fk_examination_paper_id = #{fkExaminationPaperId}
        </if>
        group by fk_user_id
        order by max(score) desc,min(use_time) asc) AS t) a where 1=1-->

        <if test="userStaffBdIds != null">
            and a.fkUserId in
            <foreach collection="userStaffBdIds" item="userStaffBdId" index="index" open="(" separator="," close=")">
                #{userStaffBdId}
            </foreach>
        </if>
        <if test="userIds != null">
            and a.fkUserId in
            <foreach collection="userIds" item="userId" index="index" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
        order by a.rank
    </select>
    <select id="viewExaminationLeaderboard" resultType="com.get.examcenter.vo.UserexaminationPaperScoreVo">
        SELECT t.*, @rownum := @rownum + 1 AS rank
        FROM (SELECT @rownum := 0) r, (select
        fk_user_id AS fkUserId,IFNULL(sum(score),0) AS score,sum(use_time) AS useTime
        from r_user_examination_paper_score where score is not null
        <if test="fkExaminationId != null and fkExaminationId != ''">
            and fk_examination_id = #{fkExaminationId}
        </if>
        group by fk_user_id
        order by sum(score) desc,sum(use_time) asc) AS t
    </select>
    <select id="viewExaminationLeaderboardByUserIds" resultType="com.get.examcenter.vo.UserexaminationPaperScoreVo">
        select b.* from (SELECT t1.count,t.*, @rownum := @rownum + 1 AS rank FROM (SELECT @rownum := 0) r, (
        SELECT a.fk_user_id fkUserId, SUM(a.paper_max_score) score, SUM(a.paper_min_time) useTime
        FROM (
        SELECT a.fk_user_id, a.fk_examination_paper_id, MAX(a.score) paper_max_score, MIN(a.use_time) paper_min_time FROM (
        SELECT a.* FROM ais_app_exam_center.r_user_examination_paper_score a
        INNER JOIN (
        SELECT a.fk_user_id, a.fk_examination_paper_id, MAX(a.score) max_score FROM ais_app_exam_center.r_user_examination_paper_score a GROUP BY a.fk_user_id, a.fk_examination_paper_id
        ) b ON a.fk_user_id=b.fk_user_id AND a.fk_examination_paper_id=b.fk_examination_paper_id AND a.score=b.max_score
        WHERE a.score IS NOT NULL AND a.score > 0
        <if test="fkExaminationId != null and fkExaminationId != ''">
            and a.fk_examination_id = #{fkExaminationId}
        </if>
        ) a
        GROUP BY a.fk_user_id, a.fk_examination_paper_id
        ) a
        GROUP BY a.fk_user_id
        ORDER BY SUM(a.paper_max_score) DESC, SUM(a.paper_min_time)
        ) AS t left join (select COUNT(id)count,fk_user_id from ais_app_exam_center.r_user_examination_paper_score a
        where 1=1
        <if test="fkExaminationId != null and fkExaminationId != ''">
            and fk_examination_id = #{fkExaminationId}
        </if>
        and a.score IS NOT NULL AND a.score > 0
        group BY fk_user_id) t1 on t.fkUserId=t1.fk_user_id) b where 1=1

 <!--       select b.* from (select a.*, @rownum := @rownum + 1 AS rank from (SELECT @rownum := 0) r,(SELECT t.fkUserId,sum(t.score) AS score,sum(t.useTime) AS useTime
        FROM (select
        fk_user_id AS fkUserId,IFNULL(max(score),0) AS score,min(use_time) AS useTime
        from r_user_examination_paper_score where score is not null and score > 5
        <if test="fkExaminationId != null and fkExaminationId != ''">
            and fk_examination_id = #{fkExaminationId}
        </if>
        group by fk_user_id,fk_examination_paper_id) AS t group by t.fkUserId order by sum(t.score) desc,sum(t.useTime) asc) a ) b
        where 1=1-->

        <if test="userStaffBdIds != null">
            and b.fkUserId in
            <foreach collection="userStaffBdIds" item="userStaffBdId" index="index" open="(" separator="," close=")">
                #{userStaffBdId}
            </foreach>
        </if>
        <if test="userIds != null">
            and b.fkUserId in
            <foreach collection="userIds" item="userId" index="index" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
    </select>
    <select id="getUserExaminationPaperScore" resultType="com.get.examcenter.vo.UserexaminationPaperScoreVo">
        SELECT fk_user_id as fkUserId, COUNT( fk_user_id ) as paperNumber,sum(score)as score FROM `r_user_examination_paper_score`
        where score is not null
        <if test="userIds != null">
            and fk_user_id in
            <foreach collection="userIds" item="userId" index="index" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
        GROUP BY fk_user_id
    </select>
    <select id="selectUserPaperScourceList" resultType="com.get.examcenter.vo.UserPaperScoreVo">
select ueps.*,mu.wechat_nickname wechatnickname,mu.name_en nameen,mu.mobile,mu.email
from r_user_examination_paper_score ueps
left join ais_app_registration_center.m_user mu on ueps.fk_user_id=mu.id
where 1=1
and ueps.fk_examination_paper_id=#{fkExaminationPaperId}
    </select>
</mapper>
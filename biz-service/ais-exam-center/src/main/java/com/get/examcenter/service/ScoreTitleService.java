package com.get.examcenter.service;

import com.get.common.result.Page;
import com.get.examcenter.vo.ScoreTitleVo;
import com.get.examcenter.dto.ScoreTitleDto;
import com.get.examcenter.dto.query.ScoreTitleQueryDto;

import java.util.List;

/**
 * Created by <PERSON>.
 * Time: 17:24
 * Date: 2021/8/27
 * Description:称号管理逻辑处理类
 */
public interface ScoreTitleService {

    /**
     * @Description: 列表数据
     * @Author: Jerry
     * @Date:10:02 2021/8/23
     */
    List<ScoreTitleVo> datas(ScoreTitleQueryDto scoreTitleVo, Page page);

    /**
     * @Description: 列表数据，不分页（给排行榜使用，需要查询附件）
     * @Author: Jerry
     * @Date:10:00 2021/9/7
     */
    List<ScoreTitleVo> datasNoPage();

    /**
     * @Description: 新增接口
     * @Author: Jerry
     * @Date:10:50 2021/8/23
     */
    void add(ScoreTitleDto scoreTitleDto);

    /**
     * @Description: 编辑接口
     * @Author: Jerry
     * @Date:10:50 2021/8/23
     */
    void update(ScoreTitleDto scoreTitleDto);


    /**
     * @Description: 删除接口
     * @Author: Jerry
     * @Date:10:51 2021/8/23
     */
    void delete(Long id);


    /**
     * @Description: 详情接口
     * @Author: Jerry
     * @Date:10:51 2021/8/23
     */
    ScoreTitleVo detail(Long id);


    /**
     * @Description: 上移下移
     * @Author: Jerry
     * @Date:18:31 2021/8/27
     */
    void movingOrder(List<ScoreTitleDto> scoreTitleDtos);
}

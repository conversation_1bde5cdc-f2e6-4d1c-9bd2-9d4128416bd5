package com.get.examcenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.examcenter.vo.ExaminationPaperVo;
import com.get.examcenter.vo.ExaminationQuestionAssignVo;
import com.get.examcenter.vo.UserexaminationPaperScoreVo;
import com.get.examcenter.service.ExaminationPaperService;
import com.get.examcenter.dto.ExaminationPaperListDto;
import com.get.examcenter.dto.ExaminationPaperUpdateDto;
import com.get.examcenter.dto.ExaminationQuestionAssignDto;
import com.get.examcenter.dto.ViewLeaderboardDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * Created by Jerry.
 * Time: 9:39
 * Date: 2021/8/23
 * Description:考场管理控制器
 */
@Api(tags = "考场管理")
@RestController
@RequestMapping("exam/ExaminationPaper")
public class ExaminationPaperController {

    @Resource
    private ExaminationPaperService examinationPaperService;

    /**
     * @Description: 列表数据
     * @Author: Jerry
     * @Date:9:41 2021/8/23
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.EXAMCENTER, type = LoggerOptTypeConst.LIST, description = "考试中心/考场管理/列表数据")
    @PostMapping("datas")
    public ResponseBo<ExaminationPaperVo> datas(@RequestBody SearchBean<ExaminationPaperListDto> page) {
        List<ExaminationPaperVo> examinationPaperVos = examinationPaperService.getExaminationPaperList(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(examinationPaperVos, p);
    }


    /**
     * @Description: 新增
     * @Author: Jerry
     * @Date:11:23 2021/8/23
     */
    @ApiOperation(value = "新增", notes = "")
    @OperationLogger(module = LoggerModulesConsts.EXAMCENTER, type = LoggerOptTypeConst.ADD, description = "考试中心/考场管理/新增")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(ExaminationPaperUpdateDto.Add.class) ExaminationPaperUpdateDto examinationPaperUpdateDto) {
        examinationPaperService.add(examinationPaperUpdateDto);
        return SaveResponseBo.ok();
    }

    /**
     * @Description: 更新
     * @Author: Jerry
     * @Date:11:25 2021/8/23
     */
    @ApiOperation(value = "更新", notes = "")
    @OperationLogger(module = LoggerModulesConsts.EXAMCENTER, type = LoggerOptTypeConst.EDIT, description = "考试中心/考场管理/更新")
    @PostMapping("update")
    public ResponseBo update(@RequestBody @Validated(ExaminationPaperUpdateDto.Update.class) ExaminationPaperUpdateDto examinationPaperUpdateDto) {
        examinationPaperService.update(examinationPaperUpdateDto);
        return UpdateResponseBo.ok();
    }

    /**
     * @Description: 详情
     * @Author: Jerry
     * @Date:11:26 2021/8/23
     */
    @ApiOperation(value = "详情", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.EXAMCENTER, type = LoggerOptTypeConst.DETAIL, description = "考试中心/考场管理/详情")
    @GetMapping("/{id}")
    public ResponseBo<ExaminationPaperVo> detail(@PathVariable("id") Long id) {
        return new ResponseBo<>(examinationPaperService.detail(id));
    }

    /**
     * 删除
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.EXAMCENTER, type = LoggerOptTypeConst.DELETE, description = "考试中心/考场管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        examinationPaperService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @Description: 激活禁用
     * @Author: Jerry
     * @Date:11:25 2021/8/23
     */
    @ApiOperation(value = "激活禁用", notes = "isActive: true：激活 false：禁用")
    @OperationLogger(module = LoggerModulesConsts.EXAMCENTER, type = LoggerOptTypeConst.EDIT, description = "考试中心/考场管理/激活禁用")
    @PostMapping("updateActive/{id}/{isActive}")
    public ResponseBo updateActive(@PathVariable("id") Long id, @PathVariable("isActive") boolean isActive) {
        examinationPaperService.updateActive(id, isActive);
        return UpdateResponseBo.ok();
    }

    /**
     * @Description: 查看排行榜
     * @Author: Jerry
     * @Date:15:43 2021/8/23
     */
    @ApiOperation(value = "查看排行榜", notes = "fkExaminationPaperId为考场id，userName为用户姓名")
    @OperationLogger(module = LoggerModulesConsts.EXAMCENTER, type = LoggerOptTypeConst.LIST, description = "考试中心/考场管理/查看排行榜")
    @PostMapping("/viewLeaderboard/{fkExaminationPaperId}")
    public ResponseBo<UserexaminationPaperScoreVo> viewLeaderboard(@PathVariable("fkExaminationPaperId") Long fkExaminationPaperId,
                                                                   @RequestBody SearchBean<ViewLeaderboardDto> page) {
        List<UserexaminationPaperScoreVo> userexaminationPaperScoreVos = examinationPaperService.viewLeaderboard(fkExaminationPaperId, page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(userexaminationPaperScoreVos, p);
    }

    /**
     * @Description: 分配试题
     * @Author: Jerry
     * @Date:16:08 2021/8/23
     */
    @ApiOperation(value = "分配试题", notes = "fkExaminationPaperId为考场id")
    @OperationLogger(module = LoggerModulesConsts.EXAMCENTER, type = LoggerOptTypeConst.ADD, description = "考试中心/考场管理/分配试题")
    @PostMapping("/assignExaminationQuestions")
    public ResponseBo assignExaminationQuestions(@RequestBody @Validated(ExaminationQuestionAssignDto.Add.class) ValidList<ExaminationQuestionAssignDto> examinationQuestionVos) {
        examinationPaperService.assignExaminationQuestions(examinationQuestionVos);
        return SaveResponseBo.ok();
    }

    /**
     * @Description: 删除已分配的试题
     * @Author: Jerry
     * @Date:10:01 2021/9/3
     */
    @ApiOperation(value = "删除已分配的试题", notes = "")
    @OperationLogger(module = LoggerModulesConsts.EXAMCENTER, type = LoggerOptTypeConst.ADD, description = "考试中心/考场管理/删除已分配的试题")
    @PostMapping("deleteAssignExaminationQuestions/{id}")
    public ResponseBo deleteAssignExaminationQuestions(@PathVariable("id") Long id) {
        examinationPaperService.deleteAssignExaminationQuestions(id);
        return SaveResponseBo.ok();
    }

    /**
     * @Description: 已分配的试题列表
     * @Author: Jerry
     * @Date:10:17 2021/9/3
     */
    @ApiOperation(value = "已分配的试题列表", notes = "fkExaminationPaperId为考场id")
    @OperationLogger(module = LoggerModulesConsts.EXAMCENTER, type = LoggerOptTypeConst.ADD, description = "考试中心/考场管理/已分配的试题列表")
    @PostMapping("/assignExaminationQuestionsDatas")
    public ResponseBo<ExaminationQuestionAssignVo> assignExaminationQuestionsDatas(@RequestParam("fkExaminationPaperId") Long fkExaminationPaperId) {
        return new ListResponseBo<>(examinationPaperService.assignExaminationQuestionsDatas(fkExaminationPaperId));
    }

    /**
     * @Description: 获取考题
     * @Author: Jerry
     * @Date:18:38 2021/8/23
     */
    /*@ApiOperation(value = "获取考题", notes = "examinationPaperNum为考场编号,fkUserId为用户id")
    @OperationLogger(module = LoggerModulesConsts.EXAMCENTER, type = LoggerOptTypeConst.LIST, description = "考试中心/考场管理/获取考题")
    @PostMapping("/getExaminationQuestions")
    public ResponseBo<ExaminationQuestionDto> getExaminationQuestions(@RequestParam("examinationPaperNum") String examinationPaperNum,
                                                                      @RequestParam("fkUserId") Long fkUserId)  {
        return new ListResponseBo<>(examinationPaperService.getExaminationQuestions(examinationPaperNum,fkUserId));
    }*/


    /**
     * @Description: 考卷下拉框
     * @Author: Jerry
     * @Date:9:44 2021/9/3
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "考卷下拉框", notes = "")
    @OperationLogger(module = LoggerModulesConsts.EXAMCENTER, type = LoggerOptTypeConst.LIST, description = "考试中心/考场管理/考卷下拉框")
    @PostMapping("/examinationPaperSelect")
    public ResponseBo<BaseSelectEntity> examinationPaperSelect(@RequestParam("fkCompanyId") String fkCompanyId) {
        return new ListResponseBo<>(examinationPaperService.examinationPaperSelect(fkCompanyId));
    }

    /**
     * @return ResponseBo
     * @Description :生成二维码
     * @Param
     * <AUTHOR>
     */
    @ApiOperation(value = "生成二维码", notes = "")
    @OperationLogger(module = LoggerModulesConsts.EXAMCENTER, type = LoggerOptTypeConst.LIST, description = "考试中心/考场管理/生成二维码")
    @GetMapping(value = "/createQrCode")
    public ResponseBo<String> createQrCode(@RequestParam("examinationPaperId") Long examinationPaperId) {
        String qrStr = examinationPaperService.createQrCode(examinationPaperId);
        return new ResponseBo<>(qrStr);
    }

    /** 
    * @Description:
    * @Param:
    * @return: void
    * @Author: Walker
    * @Date: 2022/7/11
    */
    @ApiOperation(value = "导出答题情况")
    @OperationLogger(module = LoggerModulesConsts.EXAMCENTER, type = LoggerOptTypeConst.LIST, description = "考试中心/考场管理/导出答题情况")
    @PostMapping("/exportAnswerSituation")
    @ResponseBody
    public void exportAnswerSituation(HttpServletResponse response, @RequestParam("fkExaminationPaperId") Long fkExaminationPaperId) {
        examinationPaperService.exportAnswerSituation(response, fkExaminationPaperId);
    }
    
    /** 
    * @Description: 获取考卷连接
    * @Param: paperId
    * @return: 
    * @Author: Walker
    * @Date: 2022/7/27
    */
    @ApiOperation(value = "获取考卷连接")
    @GetMapping("/getExamLinkByPaperId")
    public ResponseBo<String> getExamLinkByPaperId(@RequestParam("examinationPaperId") Long examinationPaperId) {
        String linkStr = examinationPaperService.getExamLinkByPaperId(examinationPaperId);
        return new ResponseBo<>(linkStr);
    }

    /** 
    * @Description: 复制考卷内容
    * @Param: 
    * @return: 
    * @Author: Walker
    * @Date: 2022/7/28
    */
    @ApiOperation(value = "复制考卷内容")
    @GetMapping("/copyPaperByPaperId")
    public ResponseBo copyPaperByPaperId(@RequestParam("examinationPaperId") Long examinationPaperId){
        examinationPaperService.copyPaperByPaperId(examinationPaperId);
        return ResponseBo.ok();
    }
}

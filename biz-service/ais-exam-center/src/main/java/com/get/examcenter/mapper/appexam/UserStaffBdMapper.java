package com.get.examcenter.mapper.appexam;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.examcenter.entity.UserStaffBd;

@DS("appexamdb")
public interface UserStaffBdMapper extends BaseMapper<UserStaffBd> {
//    int insert(UserStaffBd record);

    int insertSelective(UserStaffBd record);

    int updateByPrimaryKeySelective(UserStaffBd record);

    int updateByPrimaryKey(UserStaffBd record);
}
package com.get.examcenter.service;

import com.get.common.result.Page;
import com.get.common.result.SearchBean;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.examcenter.vo.ExaminationVo;
import com.get.examcenter.vo.UserexaminationPaperScoreVo;
import com.get.examcenter.entity.Examination;
import com.get.examcenter.dto.ExaminationListDto;
import com.get.examcenter.dto.ExaminationUpdateDto;
import com.get.examcenter.dto.ViewLeaderboardDto;
import com.get.institutioncenter.vo.AreaRegionVo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by <PERSON>.
 * Time: 9:48
 * Date: 2021/8/23
 * Description:考试管理逻辑处理类
 */
public interface ExaminationService {

    /**
     * @Description: 列表数据
     * @Author: Jerry
     * @Date:10:02 2021/8/23
     */
    List<ExaminationVo> getExaminationList(ExaminationListDto examinationListDto, SearchBean<ExaminationListDto> page);

    /**
     * @Description: 新增接口
     * @Author: Jerry
     * @Date:10:50 2021/8/23
     */
    void add(ExaminationUpdateDto examinationUpdateDto);

    /**
     * @Description: 编辑接口
     * @Author: Jerry
     * @Date:10:50 2021/8/23
     */
    void update(ExaminationUpdateDto examinationUpdateDto);


    /**
     * @Description: 删除接口
     * @Author: Jerry
     * @Date:10:51 2021/8/23
     */
    void delete(Long id);


    /**
     * @Description: 详情接口
     * @Author: Jerry
     * @Date:10:51 2021/8/23
     */
    ExaminationVo detail(Long id);


    /**
     * @Description: 激活禁用（true：激活 false：禁用）
     * @Author: Jerry
     * @Date:11:10 2021/8/23
     */
    void updateActive(Long id, boolean isActive);


    /**
     * @Description: 查看排行榜
     * @Author: Jerry
     * @Date:14:07 2021/8/23
     */
    List<UserexaminationPaperScoreVo> viewLeaderboard(Long fkExaminationId, ViewLeaderboardDto viewLeaderboardDto, Page page);

    /**
     * @Description: feign调用，根据考试ids获取名称
     * @Author: Jerry
     * @Date:18:02 2021/8/23
     */
    Map<Long, String> getExaminationNamesByExaminationIds(Set<Long> examinationIds);


    /**
     * @Description: 根据考试ids获取对象
     * @Author: Jerry
     * @Date:15:16 2021/8/27
     */
    Map<Long, Examination> getExaminationByExaminationIds(Set<Long> examinationIds);


    /**
     * @Description: BD下拉框
     * @Author: Jerry
     * @Date:10:43 2021/9/1
     */
    List<AreaRegionVo> getAreaRegionSelect();


    /**
     * @Description: 考试下拉框
     * @Author: Jerry
     * @Date:9:41 2021/9/3
     */
    List<BaseSelectEntity> examinationSelect(Long fkCompanyId);

    /**
     * @Description: 导出考题文件(word文件)
     * @Author: Jerry
     * @Date:10:57 2021/9/13
     */
    void exportQuestionDoc(HttpServletResponse response, Long id);
}

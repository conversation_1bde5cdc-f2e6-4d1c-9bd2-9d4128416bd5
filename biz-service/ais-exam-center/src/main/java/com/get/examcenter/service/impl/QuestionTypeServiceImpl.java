package com.get.examcenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.examcenter.vo.QuestionTypeVo;
import com.get.examcenter.entity.ExaminationQuestionAssign;
import com.get.examcenter.entity.QuestionType;
import com.get.examcenter.mapper.exam.ExaminationQuestionAssignMapper;
import com.get.examcenter.mapper.exam.QuestionTypeMapper;
import com.get.examcenter.service.IQuestionTypeService;
import com.get.examcenter.dto.QuestionTypeDto;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2021/8/24 17:07
 */
@Service
public class QuestionTypeServiceImpl implements IQuestionTypeService {

    @Resource
    private UtilService utilService;

    @Resource
    private QuestionTypeMapper questionTypeMapper;
    @Resource
    private ExaminationQuestionAssignMapper examinationQuestionAssignMapper;
    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Override
    public Long saveOrUpdataQuestionType(QuestionTypeDto questionTypeDto) {
        //校验名称是否存在，存在则不往下执行
//        Example example = new Example(QuestionType.class);
//        example.createCriteria().andEqualTo("typeName",questionTypeVo.getTypeName());
        if (GeneralTool.isEmpty(questionTypeDto.getId())) {
            QuestionType questionType = BeanCopyUtils.objClone(questionTypeDto, QuestionType::new);
            Integer maxViewOrder = questionTypeMapper.getMaxViewOrder();
            questionType.setViewOrder(maxViewOrder);
            List<QuestionType> questionTypes = questionTypeMapper.selectList(Wrappers.<QuestionType>lambdaQuery().eq(QuestionType::getTypeName, questionTypeDto.getTypeName()));
            if (GeneralTool.isNotEmpty(questionTypes)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("type_name_exists"));
            }
            utilService.updateUserInfoToEntity(questionType);
            questionTypeMapper.insertSelective(questionType);
            return questionType.getId();
        } else {
            QuestionType questionType = BeanCopyUtils.objClone(questionTypeDto, QuestionType::new);
            utilService.updateUserInfoToEntity(questionType);
            questionTypeMapper.updateById(questionType);
            return questionType.getId();
        }
    }

    @Override
    public List<QuestionTypeVo> getQuestionTypeList(QuestionTypeDto data, SearchBean<QuestionTypeDto> page) {

//        Example example = new Example(QuestionType.class);
//        Example.Criteria criteria = example.createCriteria();
        LambdaQueryWrapper<QuestionType> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(data.getTypeName())) {
//            criteria.andLike("typeName", "%"+data.getTypeName()+"%");
            lambdaQueryWrapper.like(QuestionType::getTypeName, data.getTypeName());
        }
        if (GeneralTool.isNotEmpty(data.getFkCompanyId())) {
//            criteria.andEqualTo("fkCompanyId", data.getFkCompanyId());
            lambdaQueryWrapper.like(QuestionType::getFkCompanyId, data.getFkCompanyId());
        }
//        example.orderBy("viewOrder").desc();
        lambdaQueryWrapper.orderByDesc(QuestionType::getViewOrder);

//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
        IPage<QuestionType> iPage = questionTypeMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
        List<QuestionType> questionTypes = iPage.getRecords();
        page.setAll((int) iPage.getTotal());
        if (GeneralTool.isEmpty(questionTypes)) {
            return new ArrayList<>();
        }
//        page.restPage(questionTypes);
        //获取所有的公司ids
        Set<Long> fkComanyIds = questionTypes.stream().map(QuestionType::getFkCompanyId).collect(Collectors.toSet());
        if (GeneralTool.isNotEmpty(fkComanyIds)) {
            fkComanyIds.removeIf(Objects::isNull);
        }
        //根据公司ids获取名称
        Map<Long, String> companyNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkComanyIds)) {
//            companyNamesByIds = permissionCenterClient.getCompanyNamesByIds(fkComanyIds);
            Result<Map<Long, String>> result = permissionCenterClient.getCompanyNamesByIds(fkComanyIds);
            if (result.isSuccess() && result.getData() != null) {
                companyNamesByIds = result.getData();
            }
        }
        List<QuestionTypeVo> questionTypeVos = new ArrayList<>();
        for (QuestionType questionType : questionTypes) {
            QuestionTypeVo questionTypeVo = BeanCopyUtils.objClone(questionType, QuestionTypeVo::new);
            questionTypeVo.setFkCompanyName(companyNamesByIds.get(questionType.getFkCompanyId()));
            questionTypeVos.add(questionTypeVo);
        }

        return questionTypeVos;

    }

    @Override
    public void deleteQuestionType(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }

        if (questionTypeMapper.isExistQuestion(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("type_data_exists"));
        }

        validateDelete(id);
        questionTypeMapper.deleteById(id);
    }

    /**
     * @Description: 详情
     * @Author: Jerry
     * @Date:10:28 2021/8/27
     */
    @Override
    public QuestionTypeVo detail(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        QuestionType questionType = questionTypeMapper.selectById(id);
        if (GeneralTool.isEmpty(questionType)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        QuestionTypeVo questionTypeVo = BeanCopyUtils.objClone(questionType, QuestionTypeVo::new);
        //根据公司ids获取名称
        Map<Long, String> companyNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(questionType.getFkCompanyId())) {
            Set<Long> fkComanyIds = new HashSet<>();
            fkComanyIds.add(questionType.getFkCompanyId());
//            companyNamesByIds = permissionCenterClient.getCompanyNamesByIds(fkComanyIds);
            Result<Map<Long, String>> result = permissionCenterClient.getCompanyNamesByIds(fkComanyIds);
            if (result.isSuccess() && result.getData() != null) {
                companyNamesByIds = result.getData();
            }
            questionTypeVo.setFkCompanyName(companyNamesByIds.get(questionType.getFkCompanyId()));
        }
        return questionTypeVo;
    }


    /**
     * @Description: feign调用 根据考题类型ids获取名称
     * @Author: Jerry
     * @Date:10:18 2021/8/27
     */
    @Override
    public Map<Long, String> getNamesByQuestionTypeIds(Set<Long> questionTypeIds) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(questionTypeIds)) {
            return map;
        }
//        Example example = new Example(QuestionType.class);
//        example.createCriteria().andIn("id",questionTypeIds);
//        List<QuestionType> questionTypes = questionTypeMapper.selectByExample(example);
        List<QuestionType> questionTypes = questionTypeMapper.selectBatchIds(questionTypeIds);
        if (GeneralTool.isEmpty(questionTypes)) {
            return map;
        }
        for (QuestionType questionType : questionTypes) {
            map.put(questionType.getId(), questionType.getTypeName());
        }
        return map;
    }


    /**
     * @return
     * @Description: 考题类型下拉框
     * @Author: Jerry
     * @Date:9:49 2021/9/3
     */
    @Override
    public List<BaseSelectEntity> questionTypeSelect(String fkCompanyId) {
        List<BaseSelectEntity> baseSelectEntities = new ArrayList<>();
//        Example example = new Example(QuestionType.class);
//        example.orderBy("viewOrder").desc();
        LambdaQueryWrapper<QuestionType> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if(GeneralTool.isNotEmpty(fkCompanyId)){
            lambdaQueryWrapper.eq(QuestionType::getFkCompanyId,fkCompanyId);
        }
        lambdaQueryWrapper.orderByDesc(QuestionType::getViewOrder);
        List<QuestionType> questionTypes = questionTypeMapper.selectList(lambdaQueryWrapper);
        //List<QuestionType> questionTypes = questionTypeMapper.selectList(Wrappers.<QuestionType>lambdaQuery().orderByDesc(QuestionType::getViewOrder));
        if (GeneralTool.isEmpty(questionTypes)) {
            return baseSelectEntities;
        }
        for (QuestionType questionType : questionTypes) {
            BaseSelectEntity baseSelectEntity = BeanCopyUtils.objClone(questionType, BaseSelectEntity::new);
            baseSelectEntity.setName(questionType.getTypeName());
            baseSelectEntities.add(baseSelectEntity);
        }
        return baseSelectEntities;
    }


    /**
     * @Description: 上移下移
     * @Author: Jerry
     * @Date:9:54 2021/9/13
     */
    @Override
    public void movingOrder(List<QuestionTypeDto> questionTypeDtos) {
        if (GeneralTool.isEmpty(questionTypeDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        QuestionType ro = BeanCopyUtils.objClone(questionTypeDtos.get(0), QuestionType::new);
        Integer oneorder = ro.getViewOrder();
        QuestionType rt = BeanCopyUtils.objClone(questionTypeDtos.get(1), QuestionType::new);
        Integer twoorder = rt.getViewOrder();
        ro.setViewOrder(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setViewOrder(oneorder);
        utilService.updateUserInfoToEntity(rt);
        questionTypeMapper.updateById(ro);
        questionTypeMapper.updateById(rt);
    }


    /**
     * @Description: 校验该类型有没有被分配，有的话不可以删除
     * @Author: Jerry
     * @Date:17:06 2021/9/6
     */
    private void validateDelete(Long id) {
//        Example example = new Example(ExaminationQuestionAssign.class);
//        example.createCriteria().andEqualTo("targetType",0).andEqualTo("targetId",id);
        List<ExaminationQuestionAssign> examinationQuestionAssigns = examinationQuestionAssignMapper.selectList(Wrappers.<ExaminationQuestionAssign>lambdaQuery()
                .eq(ExaminationQuestionAssign::getTargetId, 0).eq(ExaminationQuestionAssign::getTargetId, id));
        if (GeneralTool.isNotEmpty(examinationQuestionAssigns)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("questionType_has_been_assigned"));
        }
    }
}

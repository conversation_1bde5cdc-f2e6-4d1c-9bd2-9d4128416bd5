package com.get.examcenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.aisplatformcenterap.feign.IPlatformCenterClient;
import com.get.common.result.Page;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.examcenter.vo.ExaminationAnswerVo;
import com.get.examcenter.vo.ExaminationVo;
import com.get.examcenter.vo.ScoreTitleVo;
import com.get.examcenter.vo.UserexaminationPaperScoreVo;
import com.get.examcenter.entity.Examination;
import com.get.examcenter.entity.ExaminationPaper;
import com.get.examcenter.entity.ExaminationQuestion;
import com.get.examcenter.entity.ExaminationQuestionAssign;
import com.get.examcenter.entity.UserStaffBd;
import com.get.examcenter.entity.UserexaminationPaperScore;
import com.get.examcenter.mapper.appexam.UserExaminationQuestionScoreMapper;
import com.get.examcenter.mapper.appexam.UserStaffBdMapper;
import com.get.examcenter.mapper.appexam.UserexaminationPaperScoreMapper;
import com.get.examcenter.mapper.exam.ExaminationMapper;
import com.get.examcenter.mapper.exam.ExaminationPaperMapper;
import com.get.examcenter.mapper.exam.ExaminationQuestionAssignMapper;
import com.get.examcenter.mapper.exam.ExaminationQuestionMapper;
import com.get.examcenter.service.ExaminationService;
import com.get.examcenter.service.IExaminationQuestionService;
import com.get.examcenter.service.IQuestionTypeService;
import com.get.examcenter.service.ScoreTitleService;
import com.get.examcenter.utils.MyStringUtils;
import com.get.examcenter.dto.ExaminationListDto;
import com.get.examcenter.dto.ExaminationUpdateDto;
import com.get.examcenter.dto.ViewLeaderboardDto;
import com.get.examcenter.utils.DocUtils;
import com.get.institutioncenter.vo.AreaRegionVo;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.feign.ISaleCenterClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.StringJoiner;
import java.util.stream.Collectors;

/**
 * Created by Jerry.
 * Time: 9:49
 * Date: 2021/8/23
 * Description:考试管理实现类
 */
@Service
public class ExaminationImpl implements ExaminationService {

    //英文26个字母
    private static final String[] ENGLISH_ALPHABET = {"A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"};
    @Resource
    private ExaminationMapper examinationMapper;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private UtilService utilService;
    @Resource
    private UserexaminationPaperScoreMapper userexaminationPaperScoreMapper;
    @Resource
    private UserExaminationQuestionScoreMapper userExaminationQuestionScoreMapper;
//    @Resource
//    private IPlatformConfigCenterClient platformConfigCenterClient;
    @Resource
    private IPlatformCenterClient platformCenterClient;
    @Resource
    private ExaminationPaperMapper examinationPaperMapper;
    @Resource
    private ScoreTitleService scoreTitleService;
    @Resource
    private UserStaffBdMapper userStaffBdMapper;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    private ISaleCenterClient saleCenterClient;
    @Resource
    private ExaminationQuestionAssignMapper examinationQuestionAssignMapper;
    @Resource
    private IQuestionTypeService questionTypeService;
    @Resource
    private IExaminationQuestionService iExaminationQuestionService;
    @Resource
    private ExaminationQuestionMapper examinationQuestionMapper;

    /**
     * @Description: 列表
     * @Author: Jerry
     * @Date:11:06 2021/8/23
     */
    @Override
    public List<ExaminationVo> getExaminationList(ExaminationListDto examinationListDto, SearchBean<ExaminationListDto> page) {
//        Example example = new Example(Examination.class);
//        Example.Criteria criteria = example.createCriteria();
//        LambdaQueryWrapper<Examination> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        QueryWrapper<Examination> examinationQueryWrapper = new QueryWrapper<>();
        examinationQueryWrapper.eq("1", 1);
        if (GeneralTool.isNotEmpty(examinationListDto.getFkCompanyId())) {
//            criteria.andEqualTo("fkCompanyId", examinationListVo.getFkCompanyId());
            examinationQueryWrapper.lambda().eq(Examination::getFkCompanyId, examinationListDto.getFkCompanyId());
        }
        if (GeneralTool.isNotEmpty(examinationListDto.getNum())) {
//            criteria.andLike("num","%"+ examinationListVo.getNum()+"%");
            examinationQueryWrapper.lambda().like(Examination::getNum, examinationListDto.getNum());

        }
        if (GeneralTool.isNotEmpty(examinationListDto.getName())) {
//            criteria.andLike("name","%"+ examinationListVo.getName()+"%");
            examinationQueryWrapper.lambda().like(Examination::getName, examinationListDto.getName());

        }
        if (GeneralTool.isNotEmpty(examinationListDto.getStartTime())) {
//            criteria.andGreaterThanOrEqualTo("startTime", examinationListVo.getStartTime());
            examinationQueryWrapper.lambda().ge(Examination::getStartTime, examinationListDto.getStartTime());

        }
        if (GeneralTool.isNotEmpty(examinationListDto.getEndTime())) {
//            criteria.andLessThanOrEqualTo("endTime", examinationListVo.getEndTime());
            examinationQueryWrapper.lambda().le(Examination::getEndTime, examinationListDto.getEndTime());

        }
        if (GeneralTool.isNotEmpty(examinationListDto.getIsActive())) {
//            criteria.andEqualTo("isActive", examinationListVo.getIsActive());
            examinationQueryWrapper.lambda().eq(Examination::getIsActive, examinationListDto.getIsActive());
        }
//        example.setOrderByClause("is_active desc,CONVERT(name USING gbk)");
//        examinationQueryWrapper.lambda().last("order by is_active desc,CONVERT(name USING gbk)");
        examinationQueryWrapper.orderByDesc("is_active");
        examinationQueryWrapper.orderByAsc("CONVERT(name USING gbk)");
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
        IPage<Examination> iPage = examinationMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), examinationQueryWrapper);
        List<Examination> examinations = iPage.getRecords();
        page.setAll((int) iPage.getTotal());
        if (GeneralTool.isEmpty(examinations)) {
            return null;
        }
//        page.restPage(examinations);
        //获取所有的公司ids
        Set<Long> fkComanyIds = examinations.stream().map(Examination::getFkCompanyId).collect(Collectors.toSet());
        //根据公司ids获取名称
        Map<Long, String> companyNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkComanyIds)) {
//            companyNamesByIds = permissionCenterClient.getCompanyNamesByIds(fkComanyIds);
            Result<Map<Long, String>> result = permissionCenterClient.getCompanyNamesByIds(fkComanyIds);
            if (result.isSuccess() && result.getData() != null) {
                companyNamesByIds = result.getData();
            }
        }
        //获取所有的考试ids
        Set<Long> fkExaminationIds = examinations.stream().map(Examination::getId).collect(Collectors.toSet());
        //根据考试ids获取考试次数
        Map<Long, Integer> scoreCountMap = getScoreCountByFkExaminationIds(fkExaminationIds);
        List<ExaminationVo> examinationVos = examinations.stream().map(examination -> BeanCopyUtils.objClone(examination, ExaminationVo::new)).collect(Collectors.toList());
        for (ExaminationVo examinationVo : examinationVos) {
            //设置公司名称
            examinationVo.setFkCompanyName(companyNamesByIds.get(examinationVo.getFkCompanyId()));
            if (GeneralTool.isNotEmpty(examinationVo.getIsActive())) {
                examinationVo.setIsActiveName(examinationVo.getIsActive() ? "是" : "否");
            }
            examinationVo.setScoreCount(scoreCountMap.get(examinationVo.getId()));
        }
        return examinationVos;
    }


    /**
     * @Description: 新增
     * @Author: Jerry
     * @Date:11:06 2021/8/23
     */
    @Override
    public void add(ExaminationUpdateDto examinationUpdateDto) {
        if (GeneralTool.isEmpty(examinationUpdateDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        //校验日期
        if(GeneralTool.isNotEmpty(examinationUpdateDto.getStartTime()) && GeneralTool.isNotEmpty(examinationUpdateDto.getEndTime())){
            if (examinationUpdateDto.getStartTime().after(examinationUpdateDto.getEndTime())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("start_time_end_time_error"));
            }
        }
        Examination examination = BeanCopyUtils.objClone(examinationUpdateDto, Examination::new);
        utilService.updateUserInfoToEntity(examination);
        examinationMapper.insertSelective(examination);
        //自动生成编号
        examination.setNum(MyStringUtils.getExaminationNum(examination.getId()));
        examinationMapper.updateByPrimaryKey(examination);
    }

    /**
     * @Description: 编辑
     * @Author: Jerry
     * @Date:11:06 2021/8/23
     */
    @Override
    public void update(ExaminationUpdateDto examinationUpdateDto) {
        if (examinationUpdateDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        Examination examination = examinationMapper.selectById(examinationUpdateDto.getId());
        if (examination == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        if(GeneralTool.isNotEmpty(examinationUpdateDto.getStartTime()) && GeneralTool.isNotEmpty(examinationUpdateDto.getEndTime())){
            //校验日期
            if (examinationUpdateDto.getStartTime().after(examinationUpdateDto.getEndTime())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("start_time_end_time_error"));
            }
        }
        examination = BeanCopyUtils.objClone(examinationUpdateDto, Examination::new);
        utilService.updateUserInfoToEntity(examination);
        examinationMapper.updateByPrimaryKey(examination);
    }

    /**
     * @Description: 删除
     * @Author: Jerry
     * @Date:11:07 2021/8/23
     */
    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        //校验是否有关联考卷，有的话不可以删除
        validateDelete(id);
        examinationMapper.deleteById(id);
    }

    /**
     * @Description: 详情
     * @Author: Jerry
     * @Date:11:07 2021/8/23
     */
    @Override
    public ExaminationVo detail(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        Examination examination = examinationMapper.selectById(id);
        if (GeneralTool.isEmpty(examination)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        ExaminationVo examinationVo = BeanCopyUtils.objClone(examination, ExaminationVo::new);
        Result<String> result = permissionCenterClient.getCompanyNameById(examinationVo.getFkCompanyId());
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            examinationVo.setFkCompanyName(result.getData());
        }
        if (GeneralTool.isNotEmpty(examinationVo.getIsActive())) {
            examinationVo.setIsActiveName(examinationVo.getIsActive() ? "是" : "否");
        }
        return examinationVo;
    }

    /**
     * @Description: 激活禁用（true：激活 false：禁用）
     * @Author: Jerry
     * @Date:11:11 2021/8/23
     */
    @Override
    public void updateActive(Long id, boolean isActive) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        Examination examination = new Examination();
        examination.setId(id);
        examination.setIsActive(isActive);
        utilService.updateUserInfoToEntity(examination);
        examinationMapper.updateByPrimaryKeySelective(examination);
    }

    /**
     * @Description: 查看排行榜
     * @Author: Jerry
     * @Date:14:32 2021/8/23
     */
    @Override
    public List<UserexaminationPaperScoreVo> viewLeaderboard(Long fkExaminationId, ViewLeaderboardDto viewLeaderboardDto, Page page) {
        List<UserexaminationPaperScoreVo> userexaminationPaperScoreVos = new ArrayList<>();
        Set<Long> userStaffBdIds = null;
        Set<Long> userIds = null;
        if (GeneralTool.isNotEmpty(viewLeaderboardDto.getFkAreaRegionId())) {
            //根据大区查询用户
//            Example example = new Example(UserStaffBd.class);
//            example.createCriteria().andEqualTo("fkAreaRegionId",viewLeaderboardVo.getFkAreaRegionId());
//            List<UserStaffBd> userStaffBds = userStaffBdMapper.selectByExample(example);
            List<UserStaffBd> userStaffBds = userStaffBdMapper.selectList(Wrappers.<UserStaffBd>lambdaQuery().eq(UserStaffBd::getFkAreaRegionId, viewLeaderboardDto.getFkAreaRegionId()));
            //根据姓名查询不出结果,直接退出
            if (GeneralTool.isEmpty(userStaffBds)) {
                return userexaminationPaperScoreVos;
            }
            userStaffBdIds = userStaffBds.stream().map(UserStaffBd::getFkUserId).collect(Collectors.toSet());
        }
        if (GeneralTool.isNotEmpty(viewLeaderboardDto.getUserName()) || GeneralTool.isNotEmpty(viewLeaderboardDto.getFkAreaCityId())) {
            //根据名称或者城市搜索用户
//            userIds = platformConfigCenterClient.getUserIdsByParam(viewLeaderboardVo.getUserName(),
//                    viewLeaderboardVo.getFkAreaCityId(),
//                    null);//bdName暂时用不上，传空
            Result<Set<Long>> result = platformCenterClient.getUserIdsByParam(viewLeaderboardDto.getUserName(),
                    viewLeaderboardDto.getFkAreaCityId(),
                    null);//bdName暂时用不上，传空
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                userIds = result.getData();
            }
            //根据姓名查询不出结果,直接退出
            if (GeneralTool.isEmpty(userIds)) {
                return userexaminationPaperScoreVos;
            }
        }
        IPage<UserexaminationPaperScoreVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        userexaminationPaperScoreVos = userexaminationPaperScoreMapper.viewExaminationLeaderboardByUserIds(iPage, fkExaminationId, userStaffBdIds, userIds);
        page.setAll((int) iPage.getTotal());
        //无结果
        if (GeneralTool.isEmpty(userexaminationPaperScoreVos)) {
            return userexaminationPaperScoreVos;
        }
        //获取用户ids
        Set<Long> fkUserIds = userexaminationPaperScoreVos.stream().map(UserexaminationPaperScoreVo::getFkUserId).collect(Collectors.toSet());
        Map<Long, String> userNamesByUserIds = new HashMap<>();
        Map<Long, String> cityNamesByUserIds = new HashMap<>();
        Map<Long, String> bdNamesByUserIds = new HashMap<>();
        Map<Long, String> mobileByUserIds = new HashMap<>();
        Map<String, String> namesByMobiles = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkUserIds)) {
            //根据userid获取名称
//            userNamesByUserIds = platformConfigCenterClient.getUserNickNamesByUserIds(fkUserIds);
            Result<Map<Long, String>> result1 = platformCenterClient.getUserNickNamesByUserIds(fkUserIds);
            if (result1.isSuccess() && GeneralTool.isNotEmpty(result1.getData())) {
                userNamesByUserIds = result1.getData();
            }
            //获取用户的所在城市
//            cityNamesByUserIds = platformConfigCenterClient.getCityNamesByUserIds(fkUserIds);
            Result<Map<Long, String>> result2 = platformCenterClient.getCityNamesByUserIds(fkUserIds);
            if (result2.isSuccess() && GeneralTool.isNotEmpty(result2.getData())) {
                cityNamesByUserIds = result2.getData();
            }
            //根据用户ids获取BD名称
            bdNamesByUserIds = getBdNamesByUserIds(fkUserIds);
            //根据userid获取手机号
//            mobileByUserIds = platformConfigCenterClient.getMobileByUserIds(fkUserIds);
            Result<Map<Long, String>> result3 = platformCenterClient.getMobileByUserIds(fkUserIds);
            if (result3.isSuccess() && GeneralTool.isNotEmpty(result3.getData())) {
                mobileByUserIds = result3.getData();
            }
        }
        //获取用户所有的手机号
        if (GeneralTool.isNotEmpty(mobileByUserIds)) {
            Set<String> mobiles = new HashSet<>();
            for (String mobile : mobileByUserIds.values()) {
                mobiles.add(mobile);
            }
            //根据手机号获取对应的峰会人员姓名
//            namesByMobiles = saleCenterClient.getNamesByMobiles(mobiles);
            Result<Map<String, String>> result = saleCenterClient.getNamesByMobiles(mobiles);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                namesByMobiles = result.getData();
            }
        }

        //获取所有的称号
        List<ScoreTitleVo> scoreTitleVos = scoreTitleService.datasNoPage();
        //获取个人的考试次数
        Map<Long, Integer> examCountByFkExaminationId = getExamCountByFkExaminationId(fkExaminationId);
        for (UserexaminationPaperScoreVo userexaminationPaperScoreVo : userexaminationPaperScoreVos) {
            //用户名称
            userexaminationPaperScoreVo.setFkUserName(userNamesByUserIds.get(userexaminationPaperScoreVo.getFkUserId()));
            //城市名称
            userexaminationPaperScoreVo.setFkAreaCityName(cityNamesByUserIds.get(userexaminationPaperScoreVo.getFkUserId()));
            //匹配称号
            setScoreTitleName(scoreTitleVos, userexaminationPaperScoreVo);
            //BD名称
            userexaminationPaperScoreVo.setBdName(bdNamesByUserIds.get(userexaminationPaperScoreVo.getFkUserId()));
            //考试次数
            userexaminationPaperScoreVo.setExamCount(examCountByFkExaminationId.get(userexaminationPaperScoreVo.getFkUserId()));
            //排名
            userexaminationPaperScoreVo.setRank(userexaminationPaperScoreVo.getRank().replace(".0",""));
            //考试次数
            userexaminationPaperScoreVo.setCount(userexaminationPaperScoreVo.getCount());
            String mobile = mobileByUserIds.get(userexaminationPaperScoreVo.getFkUserId());
            //手机号
            userexaminationPaperScoreVo.setMobile(mobile);
            //峰会用户名称
            if (GeneralTool.isNotEmpty(mobile)) {
                userexaminationPaperScoreVo.setConventionPersonName(namesByMobiles.get(mobile));
            }
        }
//        page.restPage(userexaminationPaperScoreDtos);
        return userexaminationPaperScoreVos;
    }


    /**
     * @Description: feign调用，根据考试ids获取名称
     * @Author: Jerry
     * @Date:18:02 2021/8/23
     */
    @Override
    public Map<Long, String> getExaminationNamesByExaminationIds(Set<Long> examinationIds) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(examinationIds)) {
            return map;
        }
//        Example example = new Example(Examination.class);
//        example.createCriteria().andIn("id",examinationIds);
//        List<Examination> examinations = examinationMapper.selectByExample(example);
        List<Examination> examinations = examinationMapper.selectBatchIds(examinationIds);
        if (GeneralTool.isEmpty(examinations)) {
            return map;
        }
        for (Examination examination : examinations) {
            map.put(examination.getId(), examination.getName());
        }
        return map;
    }

    /**
     * @Description: 根据考试ids获取对象
     * @Author: Jerry
     * @Date:15:17 2021/8/27
     */
    @Override
    public Map<Long, Examination> getExaminationByExaminationIds(Set<Long> examinationIds) {
        Map<Long, Examination> map = new HashMap<>();
        if (GeneralTool.isEmpty(examinationIds)) {
            return map;
        }
//        Example example = new Example(Examination.class);
//        example.createCriteria().andIn("id",examinationIds);
//        List<Examination> examinations = examinationMapper.selectByExample(example);
        List<Examination> examinations = examinationMapper.selectBatchIds(examinationIds);
        if (GeneralTool.isEmpty(examinations)) {
            return map;
        }
        for (Examination examination : examinations) {
            map.put(examination.getId(), examination);
        }
        return map;
    }

    /**
     * @Description: BD下拉框
     * @Author: Jerry
     * @Date:10:43 2021/9/1
     */
    @Override
    public List<AreaRegionVo> getAreaRegionSelect() {
        List<AreaRegionVo> areaRegionVos = new ArrayList<>();
        //获取BD团队配置IAE下的所有大区ids
        Set<Long> areaRegionIds = null;
        Result<Set<Long>> regionResult = saleCenterClient.getAreaRegionIdsByCompanyId();
        if (regionResult.isSuccess() && GeneralTool.isNotEmpty(regionResult)) {
            areaRegionIds = regionResult.getData();
        }
        if (GeneralTool.isEmpty(areaRegionIds)) {
            return areaRegionVos;
        }
        //根据大区ids获取对象集合
        Map<Long, AreaRegionVo> areaRegionDtoByIds = new HashMap<>();
        Result<Map<Long, AreaRegionVo>> dtoResult = institutionCenterClient.getAreaRegionDtoByIds(areaRegionIds);
        if (dtoResult.isSuccess() && GeneralTool.isNotEmpty(dtoResult.getData())) {
            areaRegionDtoByIds = dtoResult.getData();
        }
        for (Map.Entry<Long, AreaRegionVo> areaRegionDtoEntry : areaRegionDtoByIds.entrySet()) {
            areaRegionVos.add(areaRegionDtoEntry.getValue());
        }
        return areaRegionVos;
    }


    /**
     * @Description: 考试下拉框 待测试
     * @Author: Jerry
     * @Date:9:41 2021/9/3
     */
    @Override
    public List<BaseSelectEntity> examinationSelect(Long fkCompanyId) {
//        Example example = new Example(Examination.class);
//        example.setOrderByClause("is_active desc,CONVERT(name USING gbk)");
//        LambdaQueryWrapper<Examination> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        QueryWrapper<Examination> queryWrapper = new QueryWrapper<>();
        if(GeneralTool.isNotEmpty(fkCompanyId)){
            queryWrapper.lambda().eq(Examination::getFkCompanyId,fkCompanyId);
        }
        queryWrapper.orderByDesc("is_active");
        queryWrapper.orderByAsc("CONVERT(name USING gbk)");
//        lambdaQueryWrapper.apply(" 1=1 order by is_active desc,CONVERT(name USING gbk)");
        List<Examination> examinations = examinationMapper.selectList(queryWrapper);
        if (GeneralTool.isEmpty(examinations)) {
            return new ArrayList<>();
        }
        return BeanCopyUtils.copyListProperties(examinations, BaseSelectEntity::new);
    }

    /**
     * @Description: 导出考题文件(word文件)
     * @Author: Jerry
     * @Date:10:57 2021/9/13
     */
    @Override
    public void exportQuestionDoc(HttpServletResponse response, Long id) {
        Map<String, Object> dataMap = new HashMap<>();
        List<Map<String, Object>> examinationPaperDocList = getExaminationPaperDocList(id);
        dataMap.put("list", examinationPaperDocList);
        try {
            DocUtils.createDoc(dataMap, response, "/examQuestion.ftl", "examQuestion");
        } catch (Exception e) {
            throw new GetServiceException(e.getMessage());
        }
    }


    /**
     * @Description: 校验是否有关联考卷，有的话不可以删除
     * @Author: Jerry
     * @Date:15:21 2021/8/23
     */
    private void validateDelete(Long fkExaminationId) {
//        Example example = new Example(ExaminationPaper.class);
//        example.createCriteria().andEqualTo("fkExaminationId",fkExaminationId);
//        List<ExaminationPaper> examinationPapers = examinationPaperMapper.selectByExample(example);
        List<ExaminationPaper> examinationPapers = examinationPaperMapper.selectList(Wrappers.<ExaminationPaper>lambdaQuery()
                .eq(ExaminationPaper::getFkExaminationId, fkExaminationId));
        if (GeneralTool.isNotEmpty(examinationPapers)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("examination_has_been_associated"));
        }
    }

    /**
     * @Description: 匹配称号
     * @Author: Jerry
     * @Date:11:04 2021/8/30
     */
    private void setScoreTitleName(List<ScoreTitleVo> scoreTitleVos, UserexaminationPaperScoreVo userexaminationPaperScoreVo) {
        if (GeneralTool.isNotEmpty(scoreTitleVos)) {
            //遍历称号列表，获取匹配的分数或排名
            for (ScoreTitleVo scoreTitleVo : scoreTitleVos) {
                Integer rank = new Double(Double.parseDouble(userexaminationPaperScoreVo.getRank())).intValue();
                //Integer rank = Integer.valueOf(userexaminationPaperScoreDto.getRank());
                Integer score = userexaminationPaperScoreVo.getScore();
                //匹配对应的分数,有可能称号列表中并没有设置分数，则匹配排名
                if (GeneralTool.isNotEmpty(scoreTitleVo.getScoreMin()) && GeneralTool.isNotEmpty(scoreTitleVo.getScoreMax())) {
                    //判断当前分数是否在这个区间
                    if (score >= scoreTitleVo.getScoreMin() && score <= scoreTitleVo.getScoreMax()) {
                        //匹配，设置称号名称
                        userexaminationPaperScoreVo.setScoreTitleName(scoreTitleVo.getTitle());
                        userexaminationPaperScoreVo.setScoreTitleFile(scoreTitleVo.getMediaAndAttachedVoList());
                        userexaminationPaperScoreVo.setColorCode(scoreTitleVo.getColorCode());
                        break;
                    }
                } else if (GeneralTool.isNotEmpty(scoreTitleVo.getScoreMin()) || GeneralTool.isNotEmpty(scoreTitleVo.getScoreMax())) {
                    //判断考生分数等于称号分数
                    if (score.equals(scoreTitleVo.getScoreMin()) || score.equals(scoreTitleVo.getScoreMax())) {
                        //匹配，设置称号名称
                        userexaminationPaperScoreVo.setScoreTitleName(scoreTitleVo.getTitle());
                        userexaminationPaperScoreVo.setScoreTitleFile(scoreTitleVo.getMediaAndAttachedVoList());
                        userexaminationPaperScoreVo.setColorCode(scoreTitleVo.getColorCode());
                        break;
                    }
                }
                //匹配排名
                if (GeneralTool.isNotEmpty(scoreTitleVo.getRankingMin()) && GeneralTool.isNotEmpty(scoreTitleVo.getRankingMax())) {
                    //判断当前排名是否在这个区间
                    if (rank >= scoreTitleVo.getRankingMin() && rank <= scoreTitleVo.getRankingMax()) {
                        //匹配，设置称号名称
                        userexaminationPaperScoreVo.setScoreTitleName(scoreTitleVo.getTitle());
                        userexaminationPaperScoreVo.setScoreTitleFile(scoreTitleVo.getMediaAndAttachedVoList());
                        userexaminationPaperScoreVo.setColorCode(scoreTitleVo.getColorCode());
                        break;
                    }
                } else if (GeneralTool.isNotEmpty(scoreTitleVo.getRankingMin()) || GeneralTool.isNotEmpty(scoreTitleVo.getRankingMax())) {
                    //判断考生排名等于称号排名
                    if (rank.equals(scoreTitleVo.getRankingMin()) || rank.equals(scoreTitleVo.getRankingMax())) {
                        //匹配，设置称号名称
                        userexaminationPaperScoreVo.setScoreTitleName(scoreTitleVo.getTitle());
                        userexaminationPaperScoreVo.setScoreTitleFile(scoreTitleVo.getMediaAndAttachedVoList());
                        userexaminationPaperScoreVo.setColorCode(scoreTitleVo.getColorCode());
                        break;
                    }
                }
            }
        }
    }


    /**
     * @Description: 根据用户ids获取BD名称(大区名称)
     * @Author: Jerry
     * @Date:14:22 2021/8/30
     */
    private Map<Long, String> getBdNamesByUserIds(Set<Long> userIds) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(userIds)) {
            return map;
        }
//        Example example = new Example(UserStaffBd.class);
//        example.createCriteria().andIn("fkUserId",userIds);
//        List<UserStaffBd> userStaffBds = userStaffBdMapper.selectByExample(example);
        List<UserStaffBd> userStaffBds = userStaffBdMapper.selectList(Wrappers.<UserStaffBd>lambdaQuery().in(UserStaffBd::getFkUserId, userIds));
        if (GeneralTool.isEmpty(userStaffBds)) {
            return map;
        }
        //获取所有的大区ids
        Set<Long> fkAreaRegionIds = userStaffBds.stream().map(UserStaffBd::getFkAreaRegionId).collect(Collectors.toSet());
        Map<Long, AreaRegionVo> getFkAreaRegionDtoByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkAreaRegionIds)) {
            //通过大区ids获取名称
//            getFkAreaRegionDtoByIds = institutionCenterClient.getAreaRegionDtoByIds(fkAreaRegionIds);
            Result<Map<Long, AreaRegionVo>> dtoResult = institutionCenterClient.getAreaRegionDtoByIds(fkAreaRegionIds);
            if (dtoResult.isSuccess() && GeneralTool.isNotEmpty(dtoResult.getData())) {
                getFkAreaRegionDtoByIds = dtoResult.getData();
            }
        }
        for (UserStaffBd userStaffBd : userStaffBds) {
            AreaRegionVo areaRegionVo = getFkAreaRegionDtoByIds.get(userStaffBd.getFkAreaRegionId());
            if (GeneralTool.isNotEmpty(areaRegionVo)) {
                map.put(userStaffBd.getFkUserId(), areaRegionVo.getNameChn());
            }
        }
        return map;
    }

    /**
     * @Description: 获取考卷的模板信息
     * @Author: Jerry
     * @Date:11:17 2021/9/13
     */
    private List<Map<String, Object>> getExaminationPaperDocList(Long id) {
        LambdaQueryWrapper<ExaminationPaper> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ExaminationPaper::getFkExaminationId, id);
        lambdaQueryWrapper.orderByAsc(ExaminationPaper::getNum);
        //不知道什么逻辑
       // lambdaQueryWrapper.apply("CAST(SUBSTRING(`name` FROM locate('（',name)+1 FOR (locate('）',name) - locate('（',name)-1)) AS SIGNED)");
        List<ExaminationPaper> examinationPapers = examinationPaperMapper.selectList(lambdaQueryWrapper);
        if (GeneralTool.isEmpty(examinationPapers)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        //考题总数
        int examinationPaperSize = examinationPapers.size();
        //考题下标
        int examinationPaperXzn = 1;
        List<Map<String, Object>> list = new ArrayList<>();//题目
        for (int i = 0; i < examinationPaperSize; i++) {
            ExaminationPaper examinationPaper = examinationPapers.get(i);
            //获取对应考卷对应的考题，如果为空则跳过
            List<Map<String, Object>> questionDocList = getQuestionDocList(examinationPaper.getId());
            if (GeneralTool.isEmpty(questionDocList)) {
                continue;
            }
            Map<String, Object> map = new HashMap<>();
            //map.put("examinationPaperXzn", examinationPaperXzn);
            map.put("examinationPaperName", examinationPaper.getName());
            map.put("questionList", questionDocList);
            list.add(map);
            examinationPaperXzn++;
        }
        return list;
    }

    /**
     * @Description: 获取考题的模板信息
     * @Author: Jerry
     * @Date:14:19 2021/9/18
     */
    private List<Map<String, Object>> getQuestionDocList(Long examinationPaperId) {
//        Example example = new Example(ExaminationQuestionAssign.class);
//        example.createCriteria().andEqualTo("fkExaminationPaperId",examinationPaperId);
//        List<ExaminationQuestionAssign> examinationQuestionAssigns = examinationQuestionAssignMapper.selectByExample(example);
        List<ExaminationQuestionAssign> examinationQuestionAssigns = examinationQuestionAssignMapper.selectList(Wrappers.<ExaminationQuestionAssign>lambdaQuery().eq(ExaminationQuestionAssign::getFkExaminationPaperId, examinationPaperId)
                .eq(ExaminationQuestionAssign::getIsActive,true));
        if (GeneralTool.isEmpty(examinationQuestionAssigns)) {
            return null;
        }
        //获取该考卷对应的所有考题ids
        Set<Long> fkQuestionIds = examinationQuestionAssigns.stream().filter(examinationQuestionAssign -> examinationQuestionAssign.getTargetType() == 1).
                map(ExaminationQuestionAssign::getTargetId).collect(Collectors.toSet());
        //获取该考卷对应的所有考题类型ids
        Set<Long> fkQuestionTypeIds = examinationQuestionAssigns.stream().filter(examinationQuestionAssign -> examinationQuestionAssign.getTargetType() == 0).
                map(ExaminationQuestionAssign::getTargetId).collect(Collectors.toSet());
        //如果都为空，代表没有分配考题
        if (GeneralTool.isEmpty(fkQuestionIds) && GeneralTool.isEmpty(fkQuestionTypeIds)) {
            //考卷没有分配考题
            return null;
        }
        //根据考题ids获取考题（复习题）
        LambdaQueryWrapper<ExaminationQuestion> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ExaminationQuestion::getIsActive, true);
        lambdaQueryWrapper.eq(ExaminationQuestion::getIsReview, true);
        lambdaQueryWrapper.and(wrapper -> {
            if (GeneralTool.isNotEmpty(fkQuestionIds)) {
                wrapper.in(ExaminationQuestion::getId, fkQuestionIds);
//                criteria1.orIn("id",fkQuestionIds);
            }
            if (GeneralTool.isNotEmpty(fkQuestionTypeIds)) {
                wrapper.or().in(ExaminationQuestion::getFkQuestionTypeId, fkQuestionTypeIds);
//                criteria1.orIn("fkQuestionTypeId",fkQuestionTypeIds);
            }
        });
        lambdaQueryWrapper.orderByAsc(ExaminationQuestion::getQuestionType);
        List<ExaminationQuestion> examinationQuestions = examinationQuestionMapper.selectList(lambdaQueryWrapper);
        if (GeneralTool.isEmpty(examinationQuestions)) {
            return null;
        }
        //考题总数
        int size = examinationQuestions.size();
        //考题下标
        int index = 1;
        List<Map<String, Object>> list = new ArrayList<>();//题目
        //考题类型ids
        Set<Long> fkQuestionTypeIdsSet = examinationQuestions.stream().map(ExaminationQuestion::getFkQuestionTypeId).collect(Collectors.toSet());
        //考题ids
        Set<Long> fkQuestionIdsSet = examinationQuestions.stream().map(ExaminationQuestion::getId).collect(Collectors.toSet());
        /*Map<Long, String> namesByQuestionTypeIds = new HashMap<>();
        if(GeneralTool.isNotEmpty(fkQuestionTypeIdsSet)){
            namesByQuestionTypeIds = questionTypeService.getNamesByQuestionTypeIds(fkQuestionTypeIdsSet);
        }*/
        Map<Long, List<ExaminationAnswerVo>> examinationAnswerByExaminationQuestionIdsNews =
                iExaminationQuestionService.getExaminationAnswerByExaminationQuestionIdsNews(fkQuestionIdsSet);
        for (int i = 0; i < size; i++) {
            ExaminationQuestion examinationQuestion = examinationQuestions.get(i);
            Map<String, Object> map = new HashMap<>();
            map.put("xzn", index + ".");
            //map.put("questionType", namesByQuestionTypeIds.get(examinationQuestion.getFkQuestionTypeId()));
            map.put("question", examinationQuestion.getQuestion());
            //获取答案
            List<ExaminationAnswerVo> examinationAnswerVos = examinationAnswerByExaminationQuestionIdsNews.get(examinationQuestion.getId());
            if (GeneralTool.isNotEmpty(examinationAnswerVos)) {
                int answerSize = examinationAnswerVos.size();
                StringJoiner answer = new StringJoiner("    ");
                StringJoiner correntAnswer = new StringJoiner("、");
                for (int j = 0; j < answerSize; j++) {
                    ExaminationAnswerVo examinationAnswerVo = examinationAnswerVos.get(j);
                    //答案选项，例如：A.上海  B.北京
                    StringBuilder answerSb = new StringBuilder();
                    answerSb.append(ENGLISH_ALPHABET[j]).append(".").append(examinationAnswerVo.getAnswer());
                    answer.add(answerSb.toString());
                    //正确答案，例如：A、B
                    if (examinationAnswerVo.getIsRightAnswer()) {
                        correntAnswer.add(ENGLISH_ALPHABET[j]);
                    }
                }
                map.put("answer", answer.toString());
                map.put("correntAnswer", correntAnswer.toString());
            } else {
                map.put("answer", " ");
                map.put("correntAnswer", " ");
            }
            list.add(map);
            index++;
        }
        return list;
    }


    /**
     * @Description: 根据考试ids获取考试次数
     * @Author: Jerry
     * @Date:18:34 2021/9/14
     */
    private Map<Long, Integer> getScoreCountByFkExaminationIds(Set<Long> fkExaminationIds) {
        Map<Long, Integer> map = new HashMap<>();
//        Example example = new Example(UserexaminationPaperScore.class);
//        example.createCriteria().andIn("fkExaminationId",fkExaminationIds).andIsNotNull("score").andGreaterThan("score",5);
//        List<UserexaminationPaperScore> userexaminationPaperScores = userexaminationPaperScoreMapper.selectByExample(example);
        List<UserexaminationPaperScore> userexaminationPaperScores = userexaminationPaperScoreMapper.selectList(Wrappers.<UserexaminationPaperScore>lambdaQuery()
                .in(UserexaminationPaperScore::getFkExaminationId, fkExaminationIds)
                .isNotNull(UserexaminationPaperScore::getScore)
                .ge(UserexaminationPaperScore::getScore, 5));
        if (GeneralTool.isEmpty(userexaminationPaperScores)) {
            return map;
        }
        for (UserexaminationPaperScore userexaminationPaperScore : userexaminationPaperScores) {
            Long fkExaminationId = userexaminationPaperScore.getFkExaminationId();
            //如果集合中包含考试id，则往原记录+1
            if (map.containsKey(fkExaminationId)) {
                Integer beforeCount = map.get(fkExaminationId);
                map.put(fkExaminationId, ++beforeCount);
                continue;
            }
            Integer count = 1;
            map.put(fkExaminationId, count);
        }
        return map;
    }


    /**
     * @Description: 获取个人的考试次数
     * @Author: Jerry
     * @Date:14:53 2021/9/26
     */
    private Map<Long, Integer> getExamCountByFkExaminationId(Long fkExaminationId) {
        Map<Long, Integer> map = new HashMap<>();
//        Example example = new Example(UserexaminationPaperScore.class);
//        example.createCriteria().andEqualTo("fkExaminationId",fkExaminationId).andIsNotNull("score").andGreaterThan("score",5);
//        List<UserexaminationPaperScore> userexaminationPaperScores = userexaminationPaperScoreMapper.selectByExample(example);

        List<UserexaminationPaperScore> userexaminationPaperScores = userexaminationPaperScoreMapper.selectList(Wrappers.<UserexaminationPaperScore>lambdaQuery()
                .eq(UserexaminationPaperScore::getFkExaminationId, fkExaminationId)
                .isNotNull(UserexaminationPaperScore::getScore)
                .ge(UserexaminationPaperScore::getScore, 5));
        if (GeneralTool.isEmpty(userexaminationPaperScores)) {
            return map;
        }
        for (UserexaminationPaperScore userexaminationPaperScore : userexaminationPaperScores) {
            Long fkUserId = userexaminationPaperScore.getFkUserId();
            //如果集合中包含人员id，则往原记录+1
            if (map.containsKey(fkUserId)) {
                Integer beforeCount = map.get(fkUserId);
                map.put(fkUserId, ++beforeCount);
                continue;
            }
            Integer count = 1;
            map.put(fkUserId, count);
        }
        return map;
    }
}

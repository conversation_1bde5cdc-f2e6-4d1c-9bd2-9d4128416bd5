#服务器端口
server:
  port: 8999

#数据源配置
#spring:
#  datasource:
#    url: ${get.datasource.dev.url}
#    username: ${get.datasource.dev.username}
#    password: ${get.datasource.dev.password}
spring:
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  datasource:
    dynamic:
      #设置默认的数据源1
      primary: examdb
      datasource:
        examdb:
          url: ${get.datasource.test.examdb.url}
          username: ${get.datasource.test.examdb.username}
          password: ${get.datasource.test.examdb.password}
        appexamdb:
          url: ${get.datasource.test.appexamdb.url}
          username: ${get.datasource.test.appexamdb.username}
          password: ${get.datasource.test.appexamdb.password}
          #examdb:
          # url: **************************************************************************************************************************
          # username: root
          # password: GETTEST_ROOT@AJL3W03D
          #appexamdb:
          # url: ******************************************************************************************************************************
          # username: root
          # password: GETTEST_ROOT@AJL3W03D

wx:
  miniapp:
    configs:
      - appid: wx1a5c1f212fb6df9e
        secret: 5ecb0b52974d908d266d35e3ea3da578
        token: #微信小程序消息服务器配置的token
        aesKey: #微信小程序消息服务器配置的EncodingAESKey
        msgDataFormat: JSON
